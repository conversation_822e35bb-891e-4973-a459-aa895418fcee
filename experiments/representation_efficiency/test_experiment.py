#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
表示效率实验测试脚本

本脚本用于测试表示效率实验框架的基本功能。
"""

import os
import sys
import logging

# 添加src目录到路径
sys.path.append(os.path.abspath("./"))

# 导入实验框架
from src.base import ExperimentRunner
from src.traditional import TraditionalRepresentation
from src.tasks import HighDimensionalRetrievalTask

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("RepresentationEfficiencyTest")

def main():
    """主函数"""
    # 创建输出目录
    os.makedirs("results", exist_ok=True)
    
    # 创建实验运行器
    runner = ExperimentRunner("results")
    
    # 添加表示方法
    runner.add_method(TraditionalRepresentation())
    
    # 添加测试任务
    runner.add_task(HighDimensionalRetrievalTask())
    
    # 运行实验
    runner.run_experiment(
        sizes=[10],
        dimensions=[5],
        repeats=1
    )
    
    logger.info("测试完成，结果保存在 results 目录中")

if __name__ == "__main__":
    main()
