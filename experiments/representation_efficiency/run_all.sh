#!/bin/bash

# 表示效率实验批处理脚本

# 创建目录
mkdir -p results
mkdir -p plots

# 运行测试实验
echo "运行测试实验..."
python test_experiment.py

# 运行小规模实验
echo "运行小规模实验..."
python run_experiment.py \
  --output-dir results/small \
  --config config.json \
  --sizes 10 100 \
  --dimensions 10 20 \
  --repeats 2 \
  --methods traditional transcendental_tensor morphism_system transcendental_state fqnfs \
  --tasks high_dimensional_retrieval pattern_completion fault_tolerance

# 可视化小规模实验结果
echo "可视化小规模实验结果..."
python visualize_results.py \
  --results-file results/small/results.json \
  --output-dir plots/small

# 运行中等规模实验
echo "运行中等规模实验..."
python run_experiment.py \
  --output-dir results/medium \
  --config config.json \
  --sizes 100 1000 \
  --dimensions 50 100 \
  --repeats 3 \
  --methods traditional transcendental_tensor morphism_system transcendental_state fqnfs \
  --tasks high_dimensional_retrieval pattern_completion fault_tolerance

# 可视化中等规模实验结果
echo "可视化中等规模实验结果..."
python visualize_results.py \
  --results-file results/medium/results.json \
  --output-dir plots/medium

echo "实验完成！"
