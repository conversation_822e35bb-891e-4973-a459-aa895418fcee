# 表示效率实验框架

本实验框架用于比较不同表示方法的效率，包括传统表示方法、超越态张量、态射系统、超融态和TFF（FQNFS）。

## 目录结构

```
representation_efficiency/
├── src/                    # 源代码目录
│   ├── base.py             # 基础组件
│   ├── traditional.py      # 传统表示方法
│   ├── transcendental_tensor.py  # 超越态张量
│   ├── morphism_system.py  # 态射系统
│   ├── transcendental_state.py   # 超融态
│   ├── fqnfs.py            # TFF (FQNFS)
│   └── tasks.py            # 测试任务
├── results/                # 结果目录
├── plots/                  # 图表目录
├── config.json             # 配置文件
├── run_experiment.py       # 实验运行脚本
└── README.md               # 说明文档
```

## 表示方法

本实验框架包含以下表示方法：

1. **传统表示方法**：使用NumPy数组表示数据
2. **超越态张量**：使用超越态张量表示数据，融合量子、全息和分形特性
3. **态射系统**：使用态射系统表示数据，基于范畴论
4. **超融态**：使用超融态表示数据，具有相干性、分布性、自相似性和涌现性
5. **TFF (FQNFS)**：使用分形量子神经网络文件系统表示数据，基于量子、全息和分形理论

## 测试任务

本实验框架包含以下测试任务：

1. **高维数据检索任务**：在大规模高维数据集中查找相似项
2. **模式完成任务**：从部分信息恢复完整模式
3. **容错性测试任务**：在信息有损的情况下恢复原始数据

## 使用方法

### 安装依赖

```bash
pip install numpy pandas matplotlib
```

### 运行实验

```bash
python run_experiment.py --output-dir results --config config.json
```

### 命令行参数

- `--output-dir`：输出目录，默认为`results`
- `--config`：配置文件路径，默认为`None`
- `--sizes`：数据大小列表，默认为`[100, 1000]`
- `--dimensions`：数据维度列表，默认为`[10, 100]`
- `--repeats`：重复次数，默认为`3`
- `--methods`：表示方法列表，默认为`["traditional", "transcendental_tensor", "morphism_system", "transcendental_state", "fqnfs"]`
- `--tasks`：测试任务列表，默认为`["high_dimensional_retrieval", "pattern_completion", "fault_tolerance"]`

### 配置文件

配置文件使用JSON格式，包含以下内容：

```json
{
  "methods": {
    "traditional": {},
    "transcendental_tensor": {
      "quantum_dims": [8],
      "holographic_dims": [8, 8],
      "fractal_dims": [4, 4]
    },
    "morphism_system": {},
    "transcendental_state": {
      "dimensions": 8,
      "coherence": 0.8,
      "distribution": 0.7,
      "self_similarity": 0.6,
      "emergence": 0.5
    },
    "fqnfs": {
      "dimension": 512,
      "use_complex": true,
      "use_phase": true,
      "normalize": true,
      "encoding_type": "spatial_frequency",
      "use_quantum": true,
      "use_semantic": true,
      "fusion_method": "weighted",
      "parallel_processing": true
    }
  },
  "tasks": {
    "high_dimensional_retrieval": {},
    "pattern_completion": {},
    "fault_tolerance": {}
  }
}
```

## 结果分析

实验结果保存在输出目录中，包括以下文件：

- `results.json`：原始结果数据
- `report.md`：实验报告

## 注意事项

1. 本实验框架需要访问TTE和TFF项目的代码库，如果无法访问，将使用模拟实现
2. 实验结果可能受到硬件性能和系统负载的影响
3. 对于大规模数据，实验可能需要较长时间运行
