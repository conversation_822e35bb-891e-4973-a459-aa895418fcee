#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
传统表示方法包装器

本模块提供了传统表示方法的包装器实现。
"""

import time
import numpy as np
from typing import Any, Dict, List, Tuple, Optional, Union

from .base import RepresentationWrapper

class TraditionalRepresentation(RepresentationWrapper):
    """传统表示方法包装器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化传统表示方法包装器
        
        Args:
            config: 配置参数
        """
        super().__init__("Traditional", config)
    
    def encode(self, data: Any) -> Any:
        """
        编码数据
        
        Args:
            data: 输入数据
            
        Returns:
            编码后的数据
        """
        start_time = time.time()
        
        # 简单地将数据转换为NumPy数组
        if not isinstance(data, np.ndarray):
            encoded = np.array(data)
        else:
            encoded = data.copy()
        
        # 记录指标
        encode_time = time.time() - start_time
        self.metrics["encode_time"].append(encode_time)
        self.metrics["memory_usage"].append(encoded.nbytes)
        
        return encoded
    
    def decode(self, encoded_data: Any) -> Any:
        """
        解码数据
        
        Args:
            encoded_data: 编码数据
            
        Returns:
            解码后的数据
        """
        start_time = time.time()
        
        # 简单地返回数据
        decoded = encoded_data.copy()
        
        # 记录指标
        decode_time = time.time() - start_time
        self.metrics["decode_time"].append(decode_time)
        
        return decoded
    
    def retrieve(self, query: Any, encoded_data: Any) -> Any:
        """
        检索数据
        
        Args:
            query: 查询数据
            encoded_data: 编码数据
            
        Returns:
            检索结果
        """
        start_time = time.time()
        
        # 简单的相似度检索
        if not isinstance(query, np.ndarray):
            query = np.array(query)
        
        # 确保query和encoded_data的维度匹配
        if len(query.shape) == 1 and len(encoded_data.shape) > 1:
            # 如果query是一维的，而encoded_data是多维的，则计算每个样本与query的距离
            distances = np.linalg.norm(encoded_data - query.reshape(1, -1), axis=1)
        else:
            # 否则，直接计算距离
            distances = np.linalg.norm(encoded_data - query, axis=1)
        
        # 获取排序后的索引
        indices = np.argsort(distances)
        
        # 记录指标
        retrieve_time = time.time() - start_time
        self.metrics["retrieve_time"].append(retrieve_time)
        
        return indices
