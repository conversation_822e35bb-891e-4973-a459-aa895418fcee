#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
表示效率实验运行脚本

本脚本用于运行表示效率实验，比较不同表示方法的效率。
"""

import os
import sys
import argparse
import logging
import json
from typing import Dict, List, Any

# 添加src目录到路径
sys.path.append(os.path.abspath("./"))

# 导入实验框架
from src.base import ExperimentRunner
from src.traditional import TraditionalRepresentation
from src.transcendental_tensor import TranscendentalTensorRepresentation
from src.morphism_system import MorphismSystemRepresentation
from src.transcendental_state import TranscendentalStateRepresentation
from src.fqnfs import FQNFSRepresentation
from src.tasks import HighDimensionalRetrievalTask, PatternCompletionTask, FaultToleranceTask

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("RepresentationEfficiencyExperiment")

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="表示效率实验")
    
    # 实验配置
    parser.add_argument("--output-dir", type=str, default="results", help="输出目录")
    parser.add_argument("--config", type=str, default=None, help="配置文件路径")
    
    # 数据配置
    parser.add_argument("--sizes", type=int, nargs="+", default=[100, 1000], help="数据大小列表")
    parser.add_argument("--dimensions", type=int, nargs="+", default=[10, 100], help="数据维度列表")
    parser.add_argument("--repeats", type=int, default=3, help="重复次数")
    
    # 方法选择
    parser.add_argument("--methods", type=str, nargs="+", default=["traditional", "transcendental_tensor", "morphism_system", "transcendental_state", "fqnfs"], help="表示方法列表")
    
    # 任务选择
    parser.add_argument("--tasks", type=str, nargs="+", default=["high_dimensional_retrieval", "pattern_completion", "fault_tolerance"], help="测试任务列表")
    
    return parser.parse_args()

def load_config(config_path: str) -> Dict[str, Any]:
    """加载配置文件"""
    if config_path is None:
        return {}
    
    with open(config_path, "r") as f:
        return json.load(f)

def create_methods(methods: List[str], config: Dict[str, Any]) -> Dict[str, Any]:
    """创建表示方法"""
    method_instances = {}
    
    # 获取方法配置
    method_configs = config.get("methods", {})
    
    # 创建方法实例
    for method in methods:
        method_config = method_configs.get(method, {})
        
        if method == "traditional":
            method_instances[method] = TraditionalRepresentation(method_config)
        elif method == "transcendental_tensor":
            method_instances[method] = TranscendentalTensorRepresentation(method_config)
        elif method == "morphism_system":
            method_instances[method] = MorphismSystemRepresentation(method_config)
        elif method == "transcendental_state":
            method_instances[method] = TranscendentalStateRepresentation(method_config)
        elif method == "fqnfs":
            method_instances[method] = FQNFSRepresentation(method_config)
        else:
            logger.warning(f"未知的表示方法: {method}")
    
    return method_instances

def create_tasks(tasks: List[str], config: Dict[str, Any]) -> Dict[str, Any]:
    """创建测试任务"""
    task_instances = {}
    
    # 获取任务配置
    task_configs = config.get("tasks", {})
    
    # 创建任务实例
    for task in tasks:
        if task == "high_dimensional_retrieval":
            task_instances[task] = HighDimensionalRetrievalTask()
        elif task == "pattern_completion":
            task_instances[task] = PatternCompletionTask()
        elif task == "fault_tolerance":
            task_instances[task] = FaultToleranceTask()
        else:
            logger.warning(f"未知的测试任务: {task}")
    
    return task_instances

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 加载配置文件
    config = load_config(args.config)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 创建实验运行器
    runner = ExperimentRunner(args.output_dir)
    
    # 创建表示方法
    methods = create_methods(args.methods, config)
    for method_name, method in methods.items():
        runner.add_method(method)
    
    # 创建测试任务
    tasks = create_tasks(args.tasks, config)
    for task_name, task in tasks.items():
        runner.add_task(task)
    
    # 运行实验
    runner.run_experiment(
        sizes=args.sizes,
        dimensions=args.dimensions,
        repeats=args.repeats
    )
    
    logger.info(f"实验完成，结果保存在 {args.output_dir} 目录中")

if __name__ == "__main__":
    main()
