#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
表示效率实验结果可视化脚本

本脚本用于可视化表示效率实验的结果。
"""

import os
import sys
import json
import argparse
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Any

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="表示效率实验结果可视化")
    
    # 输入输出配置
    parser.add_argument("--results-file", type=str, default="results/results.json", help="结果文件路径")
    parser.add_argument("--output-dir", type=str, default="plots", help="输出目录")
    
    # 可视化配置
    parser.add_argument("--metrics", type=str, nargs="+", default=["encode_time", "decode_time", "retrieve_time", "memory_usage", "accuracy"], help="指标列表")
    parser.add_argument("--tasks", type=str, nargs="+", default=None, help="任务列表")
    parser.add_argument("--methods", type=str, nargs="+", default=None, help="方法列表")
    
    return parser.parse_args()

def load_results(results_file: str) -> Dict[str, Any]:
    """加载结果文件"""
    with open(results_file, "r") as f:
        return json.load(f)

def plot_results(results: Dict[str, Any], output_dir: str, metrics: List[str], tasks: List[str] = None, methods: List[str] = None):
    """绘制结果图表"""
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取任务列表
    if tasks is None:
        tasks = list(results.keys())
    
    # 遍历任务
    for task_name in tasks:
        if task_name not in results:
            print(f"任务 {task_name} 不存在")
            continue
        
        task_results = results[task_name]
        
        # 获取方法列表
        if methods is None:
            methods = list(task_results.keys())
        
        # 获取数据大小和维度
        sizes = task_results[methods[0]]["sizes"]
        dimensions = task_results[methods[0]]["dimensions"]
        
        # 遍历指标
        for metric in metrics:
            # 创建图表
            plt.figure(figsize=(12, 8))
            
            # 遍历方法
            for method_name in methods:
                if method_name not in task_results:
                    print(f"方法 {method_name} 不存在")
                    continue
                
                method_results = task_results[method_name]
                
                # 获取指标数据
                if metric not in method_results:
                    print(f"指标 {metric} 不存在")
                    continue
                
                metric_data = np.array(method_results[metric])
                
                # 计算平均值
                metric_mean = np.mean(metric_data, axis=2)
                
                # 绘制折线图
                for d_idx, dimension in enumerate(dimensions):
                    plt.plot(sizes, metric_mean[:, d_idx], label=f"{method_name} (dim={dimension})")
            
            # 设置图表属性
            plt.title(f"{task_name} - {metric}")
            plt.xlabel("数据大小")
            plt.ylabel(metric)
            plt.xscale("log")
            if metric != "accuracy":
                plt.yscale("log")
            plt.grid(True)
            plt.legend()
            
            # 保存图表
            plt.savefig(os.path.join(output_dir, f"{task_name}_{metric}.png"))
            plt.close()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 加载结果
    results = load_results(args.results_file)
    
    # 绘制结果图表
    plot_results(
        results=results,
        output_dir=args.output_dir,
        metrics=args.metrics,
        tasks=args.tasks,
        methods=args.methods
    )
    
    print(f"可视化完成，结果保存在 {args.output_dir} 目录中")

if __name__ == "__main__":
    main()
