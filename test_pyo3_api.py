#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
PyO3 API 测试脚本

该脚本用于测试 PyO3 API 的正确性，特别是在升级到 PyO3 0.24+ 后的兼容性。
"""

import os
import sys
import unittest
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 导入需要测试的模块
try:
    from src.rust_bindings.operator_registry import registry, persistence, error_handling
    from src.utils import dependency_manager
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)

class TestErrorHandling(unittest.TestCase):
    """测试错误处理模块"""
    
    def test_error_code_creation(self):
        """测试错误代码创建"""
        try:
            # 创建一个错误代码
            error_code = error_handling.ErrorCode(
                code=1001,
                category="TEST",
                severity=error_handling.ErrorSeverity.ERROR,
                message="测试错误消息"
            )
            
            # 验证错误代码属性
            self.assertEqual(error_code.code, 1001)
            self.assertEqual(error_code.category, "TEST")
            self.assertEqual(error_code.severity, error_handling.ErrorSeverity.ERROR)
            self.assertEqual(error_code.message, "测试错误消息")
            
            # 验证字符串表示
            self.assertIn("TEST", str(error_code))
            self.assertIn("1001", str(error_code))
            self.assertIn("测试错误消息", str(error_code))
        except Exception as e:
            self.fail(f"创建错误代码失败: {e}")
    
    def test_error_handler_creation(self):
        """测试错误处理器创建"""
        try:
            # 创建一个错误处理器
            handler = error_handling.ErrorHandlingOperator("test_handler")
            
            # 验证处理器属性
            self.assertEqual(handler.name, "test_handler")
            
            # 验证元数据
            metadata = handler.get_metadata()
            self.assertEqual(metadata["name"], "test_handler")
            self.assertEqual(metadata["type"], "error_handling")
        except Exception as e:
            self.fail(f"创建错误处理器失败: {e}")
    
    def test_error_handling(self):
        """测试错误处理"""
        try:
            # 创建一个错误处理器
            handler = error_handling.ErrorHandlingOperator("test_handler")
            
            # 创建一个错误代码
            error_code = error_handling.ErrorCode(
                code=1002,
                category="TEST",
                severity=error_handling.ErrorSeverity.WARNING,
                message="测试警告消息"
            )
            
            # 创建一个处理函数
            handled_errors = []
            
            def handle_error(error_details):
                handled_errors.append(error_details)
            
            # 注册处理函数
            handler.register_handler(error_handling.ErrorSeverity.WARNING, handle_error)
            
            # 处理错误
            handler.handle_error(error_code, "自定义警告消息", {"extra": "信息"})
            
            # 验证处理结果
            self.assertEqual(len(handled_errors), 1)
            self.assertEqual(handled_errors[0]["code"], 1002)
            self.assertEqual(handled_errors[0]["category"], "TEST")
            self.assertEqual(handled_errors[0]["message"], "自定义警告消息")
            self.assertEqual(handled_errors[0]["extra"], "信息")
        except Exception as e:
            self.fail(f"错误处理失败: {e}")

class TestRegistry(unittest.TestCase):
    """测试注册表模块"""
    
    def test_operator_registration(self):
        """测试算子注册"""
        try:
            # 清空注册表
            registry.clear_registry()
            
            # 创建一个测试算子
            class TestOperator:
                def __init__(self, name="test_operator"):
                    self.name = name
                
                def apply(self, data):
                    return f"Applied {self.name} to {data}"
                
                def get_metadata(self):
                    return {
                        "name": self.name,
                        "type": "test",
                        "description": f"Test operator {self.name}",
                        "version": "1.0.0"
                    }
            
            # 注册算子
            registry.register_operator(
                registry.OperatorCategory.UTILITY,
                "test_op",
                TestOperator(),
                "1.0.0",
                "测试算子",
                ["test"],
                []
            )
            
            # 验证注册结果
            operators = registry.get_operators()
            self.assertEqual(len(operators), 1)
            self.assertIn("utility.test_op", operators)
            
            # 获取算子
            operator = registry.get_operator("utility", "test_op")
            self.assertIsNotNone(operator)
            self.assertEqual(operator.get_metadata()["name"], "test_operator")
            
            # 应用算子
            result = operator.apply("test_data")
            self.assertEqual(result, "Applied test_operator to test_data")
        except Exception as e:
            self.fail(f"算子注册失败: {e}")

class TestPersistence(unittest.TestCase):
    """测试持久化模块"""
    
    def setUp(self):
        """测试前准备"""
        # 清空注册表
        registry.clear_registry()
        
        # 创建测试目录
        self.test_dir = project_root / "test_persistence"
        self.test_dir.mkdir(exist_ok=True)
    
    def tearDown(self):
        """测试后清理"""
        # 删除测试文件
        for file in self.test_dir.glob("*"):
            file.unlink()
        
        # 删除测试目录
        self.test_dir.rmdir()
    
    def test_save_and_load_registry(self):
        """测试保存和加载注册表"""
        try:
            # 创建一个测试算子
            class TestOperator:
                def __init__(self, name="test_operator"):
                    self.name = name
                
                def apply(self, data):
                    return f"Applied {self.name} to {data}"
                
                def get_metadata(self):
                    return {
                        "name": self.name,
                        "type": "test",
                        "description": f"Test operator {self.name}",
                        "version": "1.0.0"
                    }
            
            # 注册算子
            registry.register_operator(
                registry.OperatorCategory.UTILITY,
                "test_op",
                TestOperator(),
                "1.0.0",
                "测试算子",
                ["test"],
                []
            )
            
            # 保存注册表
            file_path = self.test_dir / "registry.json"
            persistence.save_registry_to_file(str(file_path))
            
            # 清空注册表
            registry.clear_registry()
            self.assertEqual(len(registry.get_operators()), 0)
            
            # 加载注册表
            persistence.load_registry_from_file(str(file_path))
            
            # 验证加载结果
            operators = registry.get_operators()
            self.assertEqual(len(operators), 1)
            self.assertIn("utility.test_op", operators)
            
            # 获取算子
            operator = registry.get_operator("utility", "test_op")
            self.assertIsNotNone(operator)
        except Exception as e:
            self.fail(f"保存和加载注册表失败: {e}")

if __name__ == "__main__":
    unittest.main()
