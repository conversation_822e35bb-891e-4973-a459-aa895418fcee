{"operators": {}, "metadata": {"utility": {"error_handling": {"name": "error_handling", "version": "1.0.0", "description": "统一的错误处理机制，包括错误捕获、转换、处理和报告", "tags": ["error_handling", "recovery", "resilience"], "dependencies": [], "created_at": "2025-05-09T20:15:36.350188", "updated_at": "2025-05-09T20:15:36.350189"}, "test_op1": {"name": "test_op1", "version": "1.0.0", "description": "Test operator 1", "tags": ["test", "utility"], "dependencies": [], "created_at": "2025-05-09T20:15:36.351650", "updated_at": "2025-05-09T20:15:36.351652"}}, "transform": {"test_op2": {"name": "test_op2", "version": "2.0.0", "description": "Test operator 2", "tags": ["test", "transform"], "dependencies": [["utility.test_op1", ">=1.0.0"]], "created_at": "2025-05-09T20:15:36.351726", "updated_at": "2025-05-09T20:15:36.351727"}}}}