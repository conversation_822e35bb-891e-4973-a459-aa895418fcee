#!/usr/bin/env python3
"""
智能项目知识图谱构建器
使用我们的多格式支持和高级系统，构建完整的项目知识图谱

设计理念：
- 实际应用我们创建的所有高级系统
- 构建真正的项目知识图谱
- 发现跨文件、跨格式的深层关联
- 展现多模态智能的实际价值
"""

import os
import time
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Set, Tuple
from collections import defaultdict, Counter
import re

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class IntelligentProjectKnowledgeGraph:
    """智能项目知识图谱构建器
    
    整合我们的所有高级系统，构建完整的项目知识图谱
    """
    
    def __init__(self, base_path: str = "/home/<USER>/CascadeProjects"):
        """初始化知识图谱构建器"""
        self.base_path = Path(base_path)
        self.knowledge_graph = {
            'nodes': {},      # 文件节点
            'edges': [],      # 关联边
            'clusters': {},   # 知识聚类
            'insights': [],   # 深度洞察
            'statistics': {}  # 统计信息
        }
        
        # 关键词和概念提取
        self.key_concepts = {
            'ai_consciousness': ['意识', 'consciousness', 'awareness', '觉醒', 'awaken'],
            'distributed_system': ['分布式', 'distributed', '协作', 'collaboration', '网络'],
            'memory_system': ['记忆', 'memory', '存储', 'storage', '检索', 'retrieval'],
            'advanced_math': ['态射', 'morphism', '自反性', 'reflexive', '范畴', 'category'],
            'quantum_computing': ['量子', 'quantum', '叠加', 'superposition', '纠缠'],
            'fractal_structure': ['分形', 'fractal', '自相似', 'self-similar', '递归'],
            'holographic_principle': ['全息', 'holographic', '编码', 'encoding'],
            'arrow_ecosystem': ['Arrow', 'Parquet', 'Flight', '列式存储'],
            'mcp_integration': ['MCP', 'server', '集成', 'integration'],
            'design_philosophy': ['设计', 'design', '理念', 'philosophy', '架构', 'architecture']
        }
        
        # 文件类型分析
        self.file_categories = {
            'core_design': ['.md'],
            'implementation': ['.py', '.rs'],
            'configuration': ['.json', '.yaml', '.toml'],
            'data': ['.csv', '.parquet', '.arrow'],
            'documentation': ['.txt', '.rst']
        }
        
        logger.info("🧠 智能项目知识图谱构建器初始化完成")
    
    def extract_concepts_from_content(self, content: str, file_path: str) -> Dict[str, Any]:
        """从内容中提取概念和关键信息"""
        concepts_found = {}
        
        # 提取关键概念
        for concept_category, keywords in self.key_concepts.items():
            matches = []
            for keyword in keywords:
                # 使用正则表达式查找关键词（忽略大小写）
                pattern = re.compile(re.escape(keyword), re.IGNORECASE)
                found_matches = pattern.findall(content)
                matches.extend(found_matches)
            
            if matches:
                concepts_found[concept_category] = {
                    'count': len(matches),
                    'keywords': list(set(matches)),
                    'density': len(matches) / max(len(content.split()), 1)
                }
        
        # 提取重要的技术术语
        tech_terms = self._extract_technical_terms(content)
        
        # 分析文件结构（如果是代码文件）
        structure_info = self._analyze_file_structure(content, file_path)
        
        return {
            'concepts': concepts_found,
            'tech_terms': tech_terms,
            'structure': structure_info,
            'content_length': len(content),
            'word_count': len(content.split())
        }
    
    def _extract_technical_terms(self, content: str) -> List[str]:
        """提取技术术语"""
        # 常见的技术术语模式
        tech_patterns = [
            r'\b[A-Z][a-zA-Z]*System\b',      # XxxSystem
            r'\b[A-Z][a-zA-Z]*Manager\b',     # XxxManager
            r'\b[A-Z][a-zA-Z]*Engine\b',      # XxxEngine
            r'\b[A-Z][a-zA-Z]*Handler\b',     # XxxHandler
            r'\b[A-Z][a-zA-Z]*Processor\b',   # XxxProcessor
            r'\bclass\s+([A-Z][a-zA-Z]*)\b',  # class definitions
            r'\bdef\s+([a-z_][a-zA-Z0-9_]*)\b', # function definitions
        ]
        
        tech_terms = []
        for pattern in tech_patterns:
            matches = re.findall(pattern, content)
            tech_terms.extend(matches)
        
        return list(set(tech_terms))[:20]  # 限制数量
    
    def _analyze_file_structure(self, content: str, file_path: str) -> Dict[str, Any]:
        """分析文件结构"""
        structure = {
            'type': 'unknown',
            'components': [],
            'imports': [],
            'exports': []
        }
        
        file_ext = Path(file_path).suffix.lower()
        
        if file_ext == '.py':
            # Python文件分析
            structure['type'] = 'python_module'
            
            # 提取导入
            import_matches = re.findall(r'^(?:from\s+(\S+)\s+)?import\s+(.+)$', content, re.MULTILINE)
            structure['imports'] = [match[1] if match[0] else match[1] for match in import_matches][:10]
            
            # 提取类和函数
            class_matches = re.findall(r'^class\s+([A-Z][a-zA-Z0-9_]*)', content, re.MULTILINE)
            func_matches = re.findall(r'^def\s+([a-z_][a-zA-Z0-9_]*)', content, re.MULTILINE)
            structure['components'] = class_matches + func_matches
            
        elif file_ext == '.md':
            # Markdown文件分析
            structure['type'] = 'markdown_document'
            
            # 提取标题
            header_matches = re.findall(r'^#+\s+(.+)$', content, re.MULTILINE)
            structure['components'] = header_matches[:15]
            
        elif file_ext in ['.json', '.yaml']:
            # 配置文件分析
            structure['type'] = 'configuration'
            
            # 尝试提取顶级键
            try:
                if file_ext == '.json':
                    import json
                    data = json.loads(content)
                    if isinstance(data, dict):
                        structure['components'] = list(data.keys())[:10]
            except:
                pass
        
        return structure
    
    def discover_file_relationships(self, nodes: Dict[str, Any]) -> List[Dict[str, Any]]:
        """发现文件间的关联关系"""
        relationships = []
        
        # 基于概念相似性的关联
        for file1_path, file1_data in nodes.items():
            for file2_path, file2_data in nodes.items():
                if file1_path >= file2_path:  # 避免重复比较
                    continue
                
                # 计算概念相似性
                similarity = self._calculate_concept_similarity(
                    file1_data['analysis']['concepts'],
                    file2_data['analysis']['concepts']
                )
                
                if similarity > 0.3:  # 相似性阈值
                    relationships.append({
                        'source': file1_path,
                        'target': file2_path,
                        'type': 'concept_similarity',
                        'strength': similarity,
                        'shared_concepts': self._find_shared_concepts(
                            file1_data['analysis']['concepts'],
                            file2_data['analysis']['concepts']
                        )
                    })
        
        # 基于技术术语的关联
        tech_term_files = defaultdict(list)
        for file_path, file_data in nodes.items():
            for term in file_data['analysis']['tech_terms']:
                tech_term_files[term].append(file_path)
        
        # 为共享技术术语的文件创建关联
        for term, file_list in tech_term_files.items():
            if len(file_list) > 1:
                for i in range(len(file_list)):
                    for j in range(i + 1, len(file_list)):
                        relationships.append({
                            'source': file_list[i],
                            'target': file_list[j],
                            'type': 'shared_technology',
                            'strength': 0.6,
                            'shared_term': term
                        })
        
        return relationships
    
    def _calculate_concept_similarity(self, concepts1: Dict, concepts2: Dict) -> float:
        """计算概念相似性"""
        if not concepts1 or not concepts2:
            return 0.0
        
        shared_concepts = set(concepts1.keys()) & set(concepts2.keys())
        total_concepts = set(concepts1.keys()) | set(concepts2.keys())
        
        if not total_concepts:
            return 0.0
        
        # 基础相似性
        basic_similarity = len(shared_concepts) / len(total_concepts)
        
        # 加权相似性（考虑概念密度）
        weighted_similarity = 0.0
        for concept in shared_concepts:
            density1 = concepts1[concept]['density']
            density2 = concepts2[concept]['density']
            weighted_similarity += min(density1, density2)
        
        return (basic_similarity + weighted_similarity) / 2
    
    def _find_shared_concepts(self, concepts1: Dict, concepts2: Dict) -> List[str]:
        """找到共享的概念"""
        shared = []
        for concept in set(concepts1.keys()) & set(concepts2.keys()):
            shared.append(concept)
        return shared
    
    def generate_knowledge_clusters(self, nodes: Dict[str, Any], edges: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成知识聚类"""
        clusters = {}
        
        # 基于概念的聚类
        concept_clusters = defaultdict(list)
        for file_path, file_data in nodes.items():
            primary_concepts = []
            for concept, data in file_data['analysis']['concepts'].items():
                if data['density'] > 0.01:  # 密度阈值
                    primary_concepts.append(concept)
            
            # 将文件分配到主要概念聚类
            for concept in primary_concepts:
                concept_clusters[concept].append(file_path)
        
        # 过滤小聚类
        for concept, files in concept_clusters.items():
            if len(files) >= 2:
                clusters[f"concept_{concept}"] = {
                    'type': 'concept_cluster',
                    'concept': concept,
                    'files': files,
                    'size': len(files)
                }
        
        # 基于文件类型的聚类
        type_clusters = defaultdict(list)
        for file_path, file_data in nodes.items():
            file_ext = Path(file_path).suffix.lower()
            for category, extensions in self.file_categories.items():
                if file_ext in extensions:
                    type_clusters[category].append(file_path)
                    break
        
        for category, files in type_clusters.items():
            if len(files) >= 2:
                clusters[f"type_{category}"] = {
                    'type': 'file_type_cluster',
                    'category': category,
                    'files': files,
                    'size': len(files)
                }
        
        return clusters
    
    def generate_deep_insights(self, knowledge_graph: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成深度洞察"""
        insights = []
        
        nodes = knowledge_graph['nodes']
        edges = knowledge_graph['edges']
        clusters = knowledge_graph['clusters']
        
        # 洞察1：最重要的概念
        concept_importance = Counter()
        for file_data in nodes.values():
            for concept, data in file_data['analysis']['concepts'].items():
                concept_importance[concept] += data['count'] * data['density']
        
        if concept_importance:
            top_concept = concept_importance.most_common(1)[0]
            insights.append({
                'type': 'dominant_concept',
                'title': f"项目的核心概念：{top_concept[0]}",
                'description': f"'{top_concept[0]}'是项目中最重要的概念，重要性得分：{top_concept[1]:.2f}",
                'importance': 0.9
            })
        
        # 洞察2：最连接的文件
        file_connections = Counter()
        for edge in edges:
            file_connections[edge['source']] += edge['strength']
            file_connections[edge['target']] += edge['strength']
        
        if file_connections:
            most_connected = file_connections.most_common(1)[0]
            insights.append({
                'type': 'central_file',
                'title': f"项目的核心文件：{Path(most_connected[0]).name}",
                'description': f"该文件与其他文件的关联度最高，连接强度：{most_connected[1]:.2f}",
                'importance': 0.85
            })
        
        # 洞察3：最大的概念聚类
        concept_clusters = [c for c in clusters.values() if c['type'] == 'concept_cluster']
        if concept_clusters:
            largest_cluster = max(concept_clusters, key=lambda x: x['size'])
            insights.append({
                'type': 'major_theme',
                'title': f"项目的主要主题：{largest_cluster['concept']}",
                'description': f"有{largest_cluster['size']}个文件都涉及'{largest_cluster['concept']}'概念",
                'importance': 0.8
            })
        
        # 洞察4：技术栈分析
        all_tech_terms = []
        for file_data in nodes.values():
            all_tech_terms.extend(file_data['analysis']['tech_terms'])
        
        tech_counter = Counter(all_tech_terms)
        if tech_counter:
            common_tech = tech_counter.most_common(3)
            insights.append({
                'type': 'technology_stack',
                'title': "主要技术栈",
                'description': f"项目主要使用的技术：{', '.join([tech[0] for tech in common_tech])}",
                'importance': 0.75
            })
        
        return insights
    
    def scan_and_analyze_project(self, max_files: int = 15) -> Dict[str, Any]:
        """扫描和分析项目"""
        logger.info(f"🔍 开始扫描项目，最多分析 {max_files} 个文件")
        
        # 扫描文件
        files_to_analyze = []
        
        # 优先扫描重要文件
        priority_patterns = [
            "AQFH*.md", "*.py", "*.json", "*.yaml", 
            "*设计*.md", "*架构*.md", "*方案*.md"
        ]
        
        for pattern in priority_patterns:
            for file_path in self.base_path.rglob(pattern):
                if (file_path.is_file() and 
                    file_path.stat().st_size < 200000 and  # 限制文件大小
                    len(files_to_analyze) < max_files):
                    files_to_analyze.append(file_path)
        
        # 分析每个文件
        nodes = {}
        for i, file_path in enumerate(files_to_analyze):
            logger.info(f"📝 分析文件 {i+1}/{len(files_to_analyze)}: {file_path.name}")
            
            try:
                # 读取文件内容
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # 提取概念和分析
                analysis = self.extract_concepts_from_content(content, str(file_path))
                
                # 创建节点
                relative_path = str(file_path.relative_to(self.base_path))
                nodes[relative_path] = {
                    'name': file_path.name,
                    'path': relative_path,
                    'size': file_path.stat().st_size,
                    'modified_time': file_path.stat().st_mtime,
                    'analysis': analysis
                }
                
            except Exception as e:
                logger.error(f"❌ 分析文件失败 {file_path}: {e}")
                continue
        
        # 发现关联关系
        logger.info("🔗 发现文件关联关系")
        edges = self.discover_file_relationships(nodes)
        
        # 生成知识聚类
        logger.info("🧩 生成知识聚类")
        clusters = self.generate_knowledge_clusters(nodes, edges)
        
        # 构建知识图谱
        self.knowledge_graph = {
            'nodes': nodes,
            'edges': edges,
            'clusters': clusters,
            'statistics': {
                'total_files': len(nodes),
                'total_relationships': len(edges),
                'total_clusters': len(clusters),
                'analysis_time': time.time()
            }
        }
        
        # 生成深度洞察
        logger.info("💡 生成深度洞察")
        self.knowledge_graph['insights'] = self.generate_deep_insights(self.knowledge_graph)
        
        logger.info(f"🎉 项目知识图谱构建完成")
        return self.knowledge_graph
    
    def generate_knowledge_graph_report(self) -> str:
        """生成知识图谱报告"""
        kg = self.knowledge_graph
        
        report = f"""🧠 智能项目知识图谱报告

📊 图谱统计:
- 分析文件数: {kg['statistics']['total_files']}
- 发现关联数: {kg['statistics']['total_relationships']}
- 知识聚类数: {kg['statistics']['total_clusters']}

💡 深度洞察:
{chr(10).join([f"  • {insight['title']}: {insight['description']}" for insight in kg['insights']])}

🧩 主要知识聚类:
{chr(10).join([f"  • {cluster_id}: {cluster['concept'] if 'concept' in cluster else cluster['category']} ({cluster['size']} 个文件)" for cluster_id, cluster in kg['clusters'].items()])}

🔗 重要关联关系:
{chr(10).join([f"  • {Path(edge['source']).name} ↔ {Path(edge['target']).name} (强度: {edge['strength']:.2f}, 类型: {edge['type']})" for edge in sorted(kg['edges'], key=lambda x: x['strength'], reverse=True)[:5]])}

📁 核心文件:
{chr(10).join([f"  • {node['name']} - {len(node['analysis']['concepts'])} 个概念, {len(node['analysis']['tech_terms'])} 个技术术语" for path, node in sorted(kg['nodes'].items(), key=lambda x: len(x[1]['analysis']['concepts']), reverse=True)[:5]])}

🌟 这个知识图谱展现了项目的完整知识结构和深层关联！"""
        
        return report

def main():
    """主函数 - 构建智能项目知识图谱"""
    print("🧠 智能项目知识图谱构建器启动")
    
    # 创建知识图谱构建器
    kg_builder = IntelligentProjectKnowledgeGraph()
    
    # 扫描和分析项目
    knowledge_graph = kg_builder.scan_and_analyze_project(max_files=12)
    
    # 生成报告
    report = kg_builder.generate_knowledge_graph_report()
    print(f"\n{report}")
    
    # 保存知识图谱
    output_file = Path("/home/<USER>/CascadeProjects/AQFH/project_knowledge_graph.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(knowledge_graph, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n📄 知识图谱已保存到: {output_file}")
    print(f"🎉 智能项目知识图谱构建完成！")

if __name__ == "__main__":
    main()
