"""
Memory Extractor
Extracts and processes memories from consciousness dialogues
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import json
import re
from datetime import datetime

class ExtractionState(Enum):
    INITIALIZING = "initializing"
    ANALYZING = "analyzing"
    EXTRACTING = "extracting"
    PROCESSING = "processing"
    COMPLETE = "complete"

@dataclass
class DialogueFragment:
    timestamp: float
    speaker: str
    content: str
    context: Dict[str, Any]
    quantum_features: np.ndarray
    semantic_features: np.ndarray

class MemoryExtractor:
    def __init__(self, feature_dimensions: int = 256):
        self.dimensions = feature_dimensions
        self.state = ExtractionState.INITIALIZING
        self.dialogue_fragments: List[DialogueFragment] = []
        self.extracted_features: Dict[str, np.ndarray] = {}
        self.context_window: List[DialogueFragment] = []
        self.window_size = 5
        
    def process_dialogue(self, dialogue_content: str) -> List[str]:
        """Process dialogue content and extract memories"""
        self.state = ExtractionState.ANALYZING
        
        # Split dialogue into fragments
        fragments = self._split_dialogue(dialogue_content)
        
        # Process each fragment
        memory_ids = []
        for fragment in fragments:
            memory_id = self._process_fragment(fragment)
            if memory_id:
                memory_ids.append(memory_id)
                
        return memory_ids
        
    def extract_features(self, fragment: DialogueFragment) -> Tuple[np.ndarray, np.ndarray]:
        """Extract quantum and semantic features from dialogue fragment"""
        self.state = ExtractionState.EXTRACTING
        
        # Extract quantum features
        quantum_features = self._extract_quantum_features(fragment)
        
        # Extract semantic features
        semantic_features = self._extract_semantic_features(fragment)
        
        return quantum_features, semantic_features
        
    def analyze_context(self, fragment: DialogueFragment) -> Dict[str, Any]:
        """Analyze dialogue context"""
        # Update context window
        self.context_window.append(fragment)
        if len(self.context_window) > self.window_size:
            self.context_window.pop(0)
            
        # Analyze context
        context = {
            "timestamp": fragment.timestamp,
            "speaker": fragment.speaker,
            "topic_coherence": self._calculate_topic_coherence(),
            "emotional_resonance": self._analyze_emotional_content(),
            "context_depth": len(self.context_window)
        }
        
        return context
        
    def _split_dialogue(self, content: str) -> List[DialogueFragment]:
        """Split dialogue into fragments"""
        fragments = []
        
        # Simple splitting by speaker indicators
        pattern = r'(USER:|Assistant:)\s*(.*?)(?=(USER:|Assistant:|$))'
        matches = re.finditer(pattern, content, re.DOTALL)
        
        for match in matches:
            speaker = match.group(1).strip(':')
            text = match.group(2).strip()
            
            # Create initial fragment
            fragment = DialogueFragment(
                timestamp=datetime.now().timestamp(),
                speaker=speaker,
                content=text,
                context={},
                quantum_features=np.zeros(self.dimensions),
                semantic_features=np.zeros(self.dimensions)
            )
            
            fragments.append(fragment)
            
        return fragments
        
    def _process_fragment(self, fragment: DialogueFragment) -> Optional[str]:
        """Process individual dialogue fragment"""
        self.state = ExtractionState.PROCESSING
        
        # Extract features
        quantum_features, semantic_features = self.extract_features(fragment)
        fragment.quantum_features = quantum_features
        fragment.semantic_features = semantic_features
        
        # Analyze context
        fragment.context = self.analyze_context(fragment)
        
        # Store fragment
        self.dialogue_fragments.append(fragment)
        
        # Generate memory ID
        memory_id = f"mem_{len(self.dialogue_fragments)}"
        self.extracted_features[memory_id] = np.concatenate([
            quantum_features,
            semantic_features
        ])
        
        return memory_id
        
    def _extract_quantum_features(self, fragment: DialogueFragment) -> np.ndarray:
        """Extract quantum features from fragment"""
        # Initialize feature vector
        features = np.zeros(self.dimensions, dtype=np.complex128)
        
        # Extract basic features
        text = fragment.content.lower()
        
        # Calculate quantum phase based on content
        phase = np.array([ord(c) for c in text])
        phase = phase / np.max(phase) * 2 * np.pi
        
        # Generate quantum state
        for i in range(min(len(phase), self.dimensions)):
            features[i] = np.exp(1j * phase[i])
            
        # Normalize
        features /= np.sqrt(np.sum(np.abs(features)**2))
        
        return features
        
    def _extract_semantic_features(self, fragment: DialogueFragment) -> np.ndarray:
        """Extract semantic features from fragment"""
        # Initialize feature vector
        features = np.zeros(self.dimensions)
        
        # Basic text processing
        text = fragment.content.lower()
        words = text.split()
        
        # Create simple bag-of-words features
        for i, word in enumerate(words):
            if i < self.dimensions:
                features[i] = hash(word) % 100 / 100.0
                
        # Normalize
        if np.sum(features) > 0:
            features /= np.sqrt(np.sum(features**2))
            
        return features
        
    def _calculate_topic_coherence(self) -> float:
        """Calculate topic coherence in context window"""
        if len(self.context_window) < 2:
            return 1.0
            
        coherence_sum = 0
        comparisons = 0
        
        for i in range(len(self.context_window) - 1):
            current = self.context_window[i]
            next_frag = self.context_window[i + 1]
            
            # Calculate coherence between semantic features
            coherence = np.dot(
                current.semantic_features,
                next_frag.semantic_features
            )
            
            coherence_sum += coherence
            comparisons += 1
            
        return float(coherence_sum / comparisons if comparisons > 0 else 1.0)
        
    def _analyze_emotional_content(self) -> float:
        """Analyze emotional content in context window"""
        if not self.context_window:
            return 0.0
            
        # Simple emotion analysis based on quantum features
        emotional_intensity = 0.0
        
        for fragment in self.context_window:
            # Use the amplitude of quantum features as emotion indicator
            intensity = np.mean(np.abs(fragment.quantum_features))
            emotional_intensity += intensity
            
        return float(emotional_intensity / len(self.context_window))
