"""
多LLM聊天示例

此示例演示如何使用Supermemory MCP在多个LLM之间共享上下文。
"""

import asyncio
import os
import time
from typing import Dict, List, Optional

import httpx
from supermemory_mcp import MCPClient, MemoryPriority, MemoryType


class LLMClient:
    """LLM客户端基类"""
    
    def __init__(self, name: str, mcp_client: MCPClient):
        """初始化LLM客户端
        
        Args:
            name: LLM名称
            mcp_client: MCP客户端
        """
        self.name = name
        self.mcp_client = mcp_client
    
    async def chat(self, message: str, context: Optional[List[Dict]] = None) -> str:
        """聊天
        
        Args:
            message: 用户消息
            context: 上下文
            
        Returns:
            LLM响应
        """
        raise NotImplementedError("子类必须实现此方法")


class MockLLMClient(LLMClient):
    """模拟LLM客户端"""
    
    async def chat(self, message: str, context: Optional[List[Dict]] = None) -> str:
        """聊天
        
        Args:
            message: 用户消息
            context: 上下文
            
        Returns:
            LLM响应
        """
        # 模拟LLM响应
        if context:
            context_str = "\n".join([f"{item['role']}: {item['content']}" for item in context])
            response = f"[{self.name}] 我看到了以下上下文:\n{context_str}\n\n对于消息 '{message}'，我的回复是: 这是来自{self.name}的响应。"
        else:
            response = f"[{self.name}] 对于消息 '{message}'，我的回复是: 这是来自{self.name}的响应，没有上下文。"
        
        # 模拟延迟
        await asyncio.sleep(1)
        
        return response


class ChatSession:
    """聊天会话"""
    
    def __init__(self, session_id: str, mcp_client: MCPClient):
        """初始化聊天会话
        
        Args:
            session_id: 会话ID
            mcp_client: MCP客户端
        """
        self.session_id = session_id
        self.mcp_client = mcp_client
        self.llm_clients: Dict[str, LLMClient] = {}
        self.current_llm: Optional[str] = None
    
    def add_llm(self, name: str, llm_client: LLMClient) -> None:
        """添加LLM
        
        Args:
            name: LLM名称
            llm_client: LLM客户端
        """
        self.llm_clients[name] = llm_client
        if self.current_llm is None:
            self.current_llm = name
    
    def switch_llm(self, name: str) -> bool:
        """切换LLM
        
        Args:
            name: LLM名称
            
        Returns:
            是否成功
        """
        if name in self.llm_clients:
            self.current_llm = name
            return True
        return False
    
    async def send_message(self, message: str) -> str:
        """发送消息
        
        Args:
            message: 用户消息
            
        Returns:
            LLM响应
        """
        if not self.current_llm or not self.llm_clients:
            return "错误: 没有可用的LLM"
        
        # 获取当前LLM
        llm_client = self.llm_clients[self.current_llm]
        
        # 从MCP获取上下文
        context = await self._get_context()
        
        # 发送消息到LLM
        response = await llm_client.chat(message, context)
        
        # 存储用户消息和LLM响应到MCP
        await self._store_message("user", message)
        await self._store_message("assistant", response, source=self.current_llm)
        
        return response
    
    async def _get_context(self) -> List[Dict]:
        """获取上下文
        
        Returns:
            上下文
        """
        # 从MCP检索最近的记忆
        results = await self.mcp_client.retrieve_memory(
            query_text=f"session:{self.session_id}",
            filter_metadata={"session_id": self.session_id},
            top_k=10,
        )
        
        # 按创建时间排序
        results.sort(key=lambda x: x.created_at)
        
        # 转换为上下文格式
        context = []
        for result in results:
            metadata = result.metadata
            role = metadata.get("role", "unknown")
            context.append({
                "role": role,
                "content": result.content,
                "source": metadata.get("source", "unknown"),
            })
        
        return context
    
    async def _store_message(self, role: str, content: str, source: str = "user") -> None:
        """存储消息
        
        Args:
            role: 角色
            content: 内容
            source: 来源
        """
        # 存储消息到MCP
        await self.mcp_client.store_memory(
            content=content,
            metadata={
                "session_id": self.session_id,
                "role": role,
                "timestamp": time.time(),
                "source": source,
            },
            tags=[f"session:{self.session_id}", role, source],
            memory_type=MemoryType.TEXT,
            priority=MemoryPriority.MEDIUM,
            source=source,
        )


async def main():
    """主函数"""
    # 创建MCP客户端
    mcp_client = MCPClient(
        server_url="http://localhost:8000",
        embedding_provider="sentence_transformer",
    )
    
    try:
        # 创建聊天会话
        session = ChatSession("demo-session", mcp_client)
        
        # 添加LLM
        session.add_llm("ChatGPT", MockLLMClient("ChatGPT", mcp_client))
        session.add_llm("Claude", MockLLMClient("Claude", mcp_client))
        session.add_llm("Windsurf", MockLLMClient("Windsurf", mcp_client))
        
        # 使用ChatGPT
        print("使用ChatGPT...")
        response = await session.send_message("你好，我是用户")
        print(response)
        
        response = await session.send_message("我想讨论一个项目")
        print(response)
        
        # 切换到Claude
        print("\n切换到Claude...")
        session.switch_llm("Claude")
        
        response = await session.send_message("继续我们的讨论")
        print(response)
        
        # 切换到Windsurf
        print("\n切换到Windsurf...")
        session.switch_llm("Windsurf")
        
        response = await session.send_message("你能总结一下我们的对话吗？")
        print(response)
        
        # 切换回ChatGPT
        print("\n切换回ChatGPT...")
        session.switch_llm("ChatGPT")
        
        response = await session.send_message("谢谢你的帮助")
        print(response)
    
    finally:
        # 关闭MCP客户端
        await mcp_client.close()


if __name__ == "__main__":
    asyncio.run(main())
