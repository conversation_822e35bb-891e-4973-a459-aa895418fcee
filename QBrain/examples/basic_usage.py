"""
基本使用示例
"""

import asyncio
import os
from typing import List

from supermemory_mcp import MCPClient, MemoryPriority, MemoryType


async def main():
    """主函数"""
    # 创建客户端
    client = MCPClient(
        server_url="http://localhost:8000",
        embedding_provider="sentence_transformer",
    )
    
    try:
        # 存储记忆
        memory_id = await client.store_memory(
            content="这是一个测试记忆",
            metadata={"source": "basic_usage.py", "importance": "high"},
            tags=["测试", "示例"],
            memory_type=MemoryType.TEXT,
            priority=MemoryPriority.HIGH,
            source="basic_usage.py",
        )
        print(f"存储记忆成功，ID: {memory_id}")
        
        # 批量存储记忆
        contents = [
            "这是第一个批量记忆",
            "这是第二个批量记忆",
            "这是第三个批量记忆",
        ]
        metadatas = [
            {"source": "basic_usage.py", "index": 1},
            {"source": "basic_usage.py", "index": 2},
            {"source": "basic_usage.py", "index": 3},
        ]
        memory_ids = await client.batch_store_memory(
            contents=contents,
            metadatas=metadatas,
        )
        print(f"批量存储记忆成功，IDs: {memory_ids}")
        
        # 检索记忆
        results = await client.retrieve_memory(
            query_text="测试记忆",
            top_k=3,
        )
        print(f"检索到 {len(results)} 条记忆:")
        for i, result in enumerate(results):
            print(f"  {i+1}. {result.content} (分数: {result.score:.4f})")
        
        # 获取记忆
        memory = await client.get_memory(memory_id)
        if memory:
            print(f"获取记忆成功: {memory.content}")
        else:
            print(f"记忆 {memory_id} 不存在")
        
        # 更新记忆
        if memory:
            memory.content = "这是一个更新后的测试记忆"
            success = await client.update_memory(memory)
            print(f"更新记忆{'成功' if success else '失败'}")
        
        # 获取最近的记忆
        recent_memories = await client.get_recent_memory(limit=5)
        print(f"最近的 {len(recent_memories)} 条记忆:")
        for i, memory in enumerate(recent_memories):
            print(f"  {i+1}. {memory.content}")
        
        # 获取统计信息
        stats = await client.get_memory_stats()
        print(f"记忆统计信息: 总数 {stats['total_count']}")
        
        # 删除记忆
        for id in memory_ids:
            success = await client.delete_memory(id)
            print(f"删除记忆 {id} {'成功' if success else '失败'}")
        
        success = await client.delete_memory(memory_id)
        print(f"删除记忆 {memory_id} {'成功' if success else '失败'}")
        
        # 清除缓存
        await client.clear_memory_cache()
        print("清除缓存成功")
    
    finally:
        # 关闭客户端
        await client.close()


if __name__ == "__main__":
    asyncio.run(main())
