---
description: 'Enforce consistent spacing between property names and type annotations in types and interfaces.'
---

> 🛑 This file is source code, not the primary documentation location! 🛑
>
> See **https://typescript-eslint.io/rules/key-spacing** for documentation.

This rule extends the base [`eslint/key-spacing`](https://eslint.org/docs/rules/key-spacing) rule.
It adds support for type annotations on interfaces, classes and type literals properties.
