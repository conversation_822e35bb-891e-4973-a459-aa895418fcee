{"version": 3, "file": "restrict-plus-operands.js", "sourceRoot": "", "sources": ["../../src/rules/restrict-plus-operands.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,sDAAwC;AACxC,+CAAiC;AAEjC,kCAOiB;AAejB,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,wBAAwB;IAC9B,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,8FAA8F;YAChG,WAAW,EAAE,aAAa;YAC1B,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,eAAe,EACb,mGAAmG;YACrG,OAAO,EACL,wGAAwG;YAC1G,UAAU,EACR,8FAA8F;SACjG;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,QAAQ,EAAE;wBACR,WAAW,EAAE,sCAAsC;wBACnD,IAAI,EAAE,SAAS;qBAChB;oBACD,YAAY,EAAE;wBACZ,WAAW,EAAE,0CAA0C;wBACvD,IAAI,EAAE,SAAS;qBAChB;oBACD,YAAY,EAAE;wBACZ,WAAW,EACT,kEAAkE;wBACpE,IAAI,EAAE,SAAS;qBAChB;oBACD,oBAAoB,EAAE;wBACpB,WAAW,EACT,iGAAiG;wBACnG,IAAI,EAAE,SAAS;qBAChB;oBACD,WAAW,EAAE;wBACX,WAAW,EAAE,yCAAyC;wBACtD,IAAI,EAAE,SAAS;qBAChB;oBACD,uBAAuB,EAAE;wBACvB,WAAW,EAAE,oDAAoD;wBACjE,IAAI,EAAE,SAAS;qBAChB;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI;YAClB,YAAY,EAAE,IAAI;YAClB,oBAAoB,EAAE,IAAI;YAC1B,WAAW,EAAE,IAAI;YACjB,uBAAuB,EAAE,KAAK;SAC/B;KACF;IACD,MAAM,CACJ,OAAO,EACP,CACE,EACE,QAAQ,EACR,YAAY,EACZ,YAAY,EACZ,oBAAoB,EACpB,WAAW,EACX,uBAAuB,GACxB,EACF;QAED,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAEtD,MAAM,WAAW,GAAG;YAClB,QAAQ,IAAI,OAAO;YACnB,YAAY,IAAI,WAAW;YAC3B,YAAY,IAAI,QAAQ;YACxB,WAAW,IAAI,UAAU;YACzB,YAAY,IAAI,aAAa;SAC9B,CAAC,MAAM,CAAC,CAAC,KAAK,EAAmB,EAAE,CAAC,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC;QAChE,MAAM,UAAU,GAAG,WAAW,CAAC,MAAM;YACnC,CAAC,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;gBACxB,CAAC,CAAC,+BAA+B,WAAW,CAAC,CAAC,CAAC,EAAE;gBACjD,CAAC,CAAC,uCAAuC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACnE,CAAC,CAAC,QAAQ,CAAC;QAEb,SAAS,kBAAkB,CAAC,IAAmB;YAC7C,OAAO,WAAW,CAAC,wBAAwB,CACzC,IAAA,mCAA4B,EAAC,QAAQ,EAAE,IAAI,CAAC,CAC7C,CAAC;QACJ,CAAC;QAED,SAAS,iBAAiB,CACxB,IAA+D;YAE/D,MAAM,QAAQ,GAAG,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,SAAS,GAAG,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEjD,IACE,QAAQ,KAAK,SAAS;gBACtB,OAAO,CAAC,aAAa,CACnB,QAAQ,EACR,EAAE,CAAC,SAAS,CAAC,UAAU;oBACrB,EAAE,CAAC,SAAS,CAAC,UAAU;oBACvB,EAAE,CAAC,SAAS,CAAC,UAAU,CAC1B,EACD,CAAC;gBACD,OAAO;YACT,CAAC;YAED,IAAI,sBAAsB,GAAG,KAAK,CAAC;YAEnC,KAAK,MAAM,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,IAAI;gBAC5C,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,SAAS,CAAC;gBAChC,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,CAAC;aACzB,EAAE,CAAC;gBACX,IACE,oBAAoB,CAClB,QAAQ,EACR,EAAE,CAAC,SAAS,CAAC,YAAY;oBACvB,EAAE,CAAC,SAAS,CAAC,KAAK;oBAClB,EAAE,CAAC,SAAS,CAAC,OAAO,CACvB;oBACD,CAAC,CAAC,QAAQ,IAAI,oBAAoB,CAAC,QAAQ,EAAE,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;oBAC/D,CAAC,CAAC,YAAY;wBACZ,oBAAoB,CAAC,QAAQ,EAAE,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;oBAC3D,CAAC,CAAC,YAAY;wBACZ,IAAA,oBAAa,EAAC,QAAQ,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,GAAG,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,EACtE,CAAC;oBACD,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE;4BACJ,UAAU;4BACV,IAAI,EAAE,WAAW,CAAC,YAAY,CAAC,QAAQ,CAAC;yBACzC;wBACD,SAAS,EAAE,SAAS;wBACpB,IAAI,EAAE,QAAQ;qBACf,CAAC,CAAC;oBACH,sBAAsB,GAAG,IAAI,CAAC;oBAC9B,SAAS;gBACX,CAAC;gBAED,8DAA8D;gBAC9D,KAAK,MAAM,WAAW,IAAI,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC3D,MAAM,QAAQ,GAAG,IAAA,kBAAW,EAAC,WAAW,EAAE,WAAW,CAAC,CAAC;oBACvD,IACE,QAAQ,KAAK,QAAQ;wBACnB,CAAC,CAAC,CAAC,WAAW;4BACZ,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC;wBAC3D,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,IAAA,oBAAa,EAAC,WAAW,CAAC,CAAC;4BACzC,kBAAkB,CAAC,WAAW,CAAC,EACnC,CAAC;wBACD,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE;gCACJ,UAAU;gCACV,IAAI,EAAE,WAAW,CAAC,YAAY,CAAC,WAAW,CAAC;6BAC5C;4BACD,SAAS,EAAE,SAAS;4BACpB,IAAI,EAAE,QAAQ;yBACf,CAAC,CAAC;wBACH,sBAAsB,GAAG,IAAI,CAAC;wBAC9B,SAAS;oBACX,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,sBAAsB,EAAE,CAAC;gBAC3B,OAAO;YACT,CAAC;YAED,KAAK,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI;gBAClC,CAAC,QAAQ,EAAE,SAAS,CAAC;gBACrB,CAAC,SAAS,EAAE,QAAQ,CAAC;aACb,EAAE,CAAC;gBACX,IACE,CAAC,oBAAoB;oBACrB,oBAAoB,CAAC,QAAQ,EAAE,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC;oBACvD,oBAAoB,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,EACxD,CAAC;oBACD,OAAO,OAAO,CAAC,MAAM,CAAC;wBACpB,IAAI,EAAE;4BACJ,UAAU;4BACV,IAAI,EAAE,WAAW,CAAC,YAAY,CAAC,QAAQ,CAAC;4BACxC,KAAK,EAAE,WAAW,CAAC,YAAY,CAAC,SAAS,CAAC;yBAC3C;wBACD,SAAS,EAAE,YAAY;wBACvB,IAAI;qBACL,CAAC,CAAC;gBACL,CAAC;gBAED,IACE,oBAAoB,CAAC,QAAQ,EAAE,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC;oBACvD,oBAAoB,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,EACxD,CAAC;oBACD,OAAO,OAAO,CAAC,MAAM,CAAC;wBACpB,IAAI,EAAE;4BACJ,IAAI,EAAE,WAAW,CAAC,YAAY,CAAC,QAAQ,CAAC;4BACxC,KAAK,EAAE,WAAW,CAAC,YAAY,CAAC,SAAS,CAAC;yBAC3C;wBACD,SAAS,EAAE,iBAAiB;wBAC5B,IAAI;qBACL,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,gCAAgC,EAAE,iBAAiB;YACnD,GAAG,CAAC,CAAC,uBAAuB,IAAI;gBAC9B,qCAAqC,CAAC,IAAI;oBACxC,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBAC1B,CAAC;aACF,CAAC;SACH,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,kBAAkB,CAAC,IAAa;IACvC,OAAO,IAAI,CAAC,cAAc,EAAE;QAC1B,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC;QACjE,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AAC/D,CAAC;AAED,SAAS,oBAAoB,CAAC,IAAa,EAAE,IAAkB;IAC7D,OAAO,OAAO;SACX,cAAc,CAAC,IAAI,CAAC;SACpB,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;AAC3D,CAAC"}