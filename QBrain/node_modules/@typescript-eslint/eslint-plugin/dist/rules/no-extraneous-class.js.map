{"version": 3, "file": "no-extraneous-class.js", "sourceRoot": "", "sources": ["../../src/rules/no-extraneous-class.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAE1D,kCAAqC;AAYrC,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,qBAAqB;IAC3B,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,qCAAqC;YAClD,WAAW,EAAE,QAAQ;SACtB;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,oBAAoB,EAAE;wBACpB,WAAW,EACT,sEAAsE;wBACxE,IAAI,EAAE,SAAS;qBAChB;oBACD,UAAU,EAAE;wBACV,WAAW,EACT,yEAAyE;wBAC3E,IAAI,EAAE,SAAS;qBAChB;oBACD,eAAe,EAAE;wBACf,WAAW,EACT,uEAAuE;wBACzE,IAAI,EAAE,SAAS;qBAChB;oBACD,kBAAkB,EAAE;wBAClB,WAAW,EACT,+DAA+D;wBACjE,IAAI,EAAE,SAAS;qBAChB;iBACF;aACF;SACF;QACD,QAAQ,EAAE;YACR,KAAK,EAAE,yBAAyB;YAChC,UAAU,EAAE,+CAA+C;YAC3D,eAAe,EAAE,2CAA2C;SAC7D;KACF;IACD,cAAc,EAAE;QACd;YACE,oBAAoB,EAAE,KAAK;YAC3B,UAAU,EAAE,KAAK;YACjB,eAAe,EAAE,KAAK;YACtB,kBAAkB,EAAE,KAAK;SAC1B;KACF;IACD,MAAM,CACJ,OAAO,EACP,CAAC,EAAE,oBAAoB,EAAE,UAAU,EAAE,eAAe,EAAE,kBAAkB,EAAE,CAAC;QAE3E,MAAM,oBAAoB,GAAG,CAC3B,IAAsE,EAC7D,EAAE;YACX,OAAO,CAAC,CAAC,CACP,kBAAkB;gBAClB,IAAI,EAAE,UAAU;gBAChB,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,CAC7B,CAAC;QACJ,CAAC,CAAC;QAEF,OAAO;YACL,SAAS,CAAC,IAAI;gBACZ,MAAM,MAAM,GAAG,IAAI,CAAC,MAEQ,CAAC;gBAE7B,IAAI,MAAM,CAAC,UAAU,IAAI,oBAAoB,CAAC,MAAM,CAAC,EAAE,CAAC;oBACtD,OAAO;gBACT,CAAC;gBAED,MAAM,UAAU,GAAG,IAAI,IAAI,MAAM,IAAI,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;gBACpE,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC3B,IAAI,UAAU,EAAE,CAAC;wBACf,OAAO;oBACT,CAAC;oBAED,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,UAAU;wBAChB,SAAS,EAAE,OAAO;qBACnB,CAAC,CAAC;oBAEH,OAAO;gBACT,CAAC;gBAED,IAAI,UAAU,GAAG,IAAI,CAAC;gBACtB,IAAI,eAAe,GAAG,IAAI,CAAC;gBAE3B,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;oBAC7B,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;wBAClD,IACE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CACpB,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,CAC3D,EACD,CAAC;4BACD,eAAe,GAAG,KAAK,CAAC;4BACxB,UAAU,GAAG,KAAK,CAAC;wBACrB,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,eAAe,GAAG,KAAK,CAAC;wBACxB,IAAI,QAAQ,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;4BACrC,UAAU,GAAG,KAAK,CAAC;wBACrB,CAAC;oBACH,CAAC;oBACD,IAAI,CAAC,CAAC,UAAU,IAAI,eAAe,CAAC,EAAE,CAAC;wBACrC,MAAM;oBACR,CAAC;gBACH,CAAC;gBAED,IAAI,eAAe,EAAE,CAAC;oBACpB,IAAI,CAAC,oBAAoB,EAAE,CAAC;wBAC1B,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE,UAAU;4BAChB,SAAS,EAAE,iBAAiB;yBAC7B,CAAC,CAAC;oBACL,CAAC;oBACD,OAAO;gBACT,CAAC;gBACD,IAAI,UAAU,IAAI,CAAC,eAAe,EAAE,CAAC;oBACnC,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,UAAU;wBAChB,SAAS,EAAE,YAAY;qBACxB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}