{"version": 3, "file": "prefer-return-this-type.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-return-this-type.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,+CAAiC;AAEjC,kCAAgF;AAUhF,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,yBAAyB;IAC/B,cAAc,EAAE,EAAE;IAElB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,+DAA+D;YACjE,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,WAAW,EAAE,0BAA0B;SACxC;QACD,MAAM,EAAE,EAAE;QACV,OAAO,EAAE,MAAM;KAChB;IAED,MAAM,CAAC,OAAO;QACZ,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAElD,SAAS,gBAAgB,CACvB,IAAY,EACZ,QAA2B;YAE3B,IACE,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBAChD,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBACpD,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,IAAI,EAC/B,CAAC;gBACD,OAAO,QAAQ,CAAC;YAClB,CAAC;YAED,IAAI,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,EAAE,CAAC;gBACjD,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;oBAClC,MAAM,KAAK,GAAG,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;oBAC3C,IAAI,KAAK,EAAE,CAAC;wBACV,OAAO,KAAK,CAAC;oBACf,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,SAAS,2BAA2B,CAAC,YAA0B;YAC7D,MAAM,QAAQ,GAAG,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC3C,OAAO,CAAC,CAAC,CACP,QAAQ,EAAE,IAAI,KAAK,sBAAc,CAAC,UAAU,IAAI,QAAQ,CAAC,IAAI,KAAK,MAAM,CACzE,CAAC;QACJ,CAAC;QAED,SAAS,uBAAuB,CAC9B,YAA0B,EAC1B,aAAmC;YAEnC,IAAI,2BAA2B,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC9C,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,IAAI,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YAE9D,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACf,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,SAAS,GAAG,QAAQ,CAAC,iBAAiB,CAC1C,aAAa,CACM,CAAC;YAEtB,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;gBAC3C,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClD,OAAO,SAAS,CAAC,QAAQ,KAAK,IAAI,CAAC;YACrC,CAAC;YAED,IAAI,aAAa,GAAG,KAAK,CAAC;YAC1B,IAAI,kBAAkB,GAAG,KAAgB,CAAC;YAE1C,IAAA,6BAAsB,EAAC,IAAI,CAAC,IAAgB,EAAE,IAAI,CAAC,EAAE;gBACnD,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;gBAC7B,IAAI,CAAC,IAAI,EAAE,CAAC;oBACV,OAAO;gBACT,CAAC;gBAED,aAAa;gBACb,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;oBAC5C,aAAa,GAAG,IAAI,CAAC;oBACrB,OAAO;gBACT,CAAC;gBAED,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBAC7C,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;oBACvB,kBAAkB,GAAG,IAAI,CAAC;oBAC1B,OAAO,IAAI,CAAC;gBACd,CAAC;gBAED,IAAI,SAAS,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;oBAChC,aAAa,GAAG,IAAI,CAAC;oBACrB,OAAO;gBACT,CAAC;gBAED,OAAO;YACT,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,kBAAkB,IAAI,aAAa,CAAC;QAC9C,CAAC;QAED,SAAS,aAAa,CACpB,YAA0B,EAC1B,aAAmC;YAEnC,MAAM,SAAS,GAAG,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC;YACzC,IAAI,CAAC,SAAS,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;gBAC3C,OAAO;YACT,CAAC;YAED,MAAM,IAAI,GAAG,gBAAgB,CAC3B,SAAS,EACT,YAAY,CAAC,UAAU,CAAC,cAAc,CACvC,CAAC;YACF,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,OAAO;YACT,CAAC;YAED,IAAI,uBAAuB,CAAC,YAAY,EAAE,aAAa,CAAC,EAAE,CAAC;gBACzD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,aAAa;oBACxB,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC;iBAC9C,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO;YACL,8BAA8B,CAAC,IAA+B;gBAC5D,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,MAA8B,CAAC,CAAC;YACxE,CAAC;YACD,gCAAgC,CAC9B,IAAiC;gBAEjC,IACE,CAAC,CACC,IAAI,CAAC,KAAK,EAAE,IAAI,KAAK,sBAAc,CAAC,kBAAkB;oBACtD,IAAI,CAAC,KAAK,EAAE,IAAI,KAAK,sBAAc,CAAC,uBAAuB,CAC5D,EACD,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,MAA8B,CAAC,CAAC;YACxE,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}