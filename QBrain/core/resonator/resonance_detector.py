import torch
import torch.nn as nn
import numpy as np
from typing import Dict, Any

class QuantumResonanceDetector(nn.Module):
    """量子共振检测器"""
    
    def __init__(self, input_dim: int):
        super().__init__()
        self.input_dim = input_dim
        
        # 特征提取层
        self.feature_extractor = nn.Sequential(
            nn.Linear(input_dim, 512),
            nn.ReLU(),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, 128)
        )
        
        # 共振分析层
        self.resonance_analyzer = nn.Sequential(
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 32),
            nn.<PERSON>L<PERSON>(),
            nn.Linear(32, 16)
        )
        
        # 情感状态分析器
        self.emotion_analyzer = nn.Sequential(
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 3)  # [complexity, harmony, entropy]
        )
        
    def process_signal(self, x: torch.Tensor) -> Dict[str, Any]:
        """处理输入信号
        
        Args:
            x: 输入信号
            
        Returns:
            包含共振信息的字典
        """
        if len(x.shape) == 1:
            x = x.unsqueeze(0)
            
        # 提取特征
        features = self.feature_extractor(x)
        
        # 分析共振
        resonance = self.resonance_analyzer(features)
        
        # 分析情感状态
        emotional_state = self.emotion_analyzer(features)
        
        # 计算共振特征
        dominant_freq = torch.fft.fft(x)
        freq_magnitudes = torch.abs(dominant_freq)
        max_freq_idx = torch.argmax(freq_magnitudes)
        
        # 计算能量和熵
        energy = torch.sum(torch.abs(x)**2, dim=1)
        probs = torch.abs(x)**2
        probs = probs / torch.sum(probs, dim=1, keepdim=True)
        entropy = -torch.sum(probs * torch.log2(probs + 1e-10), dim=1)
        
        # 计算共振强度
        coherence = torch.sigmoid(resonance.mean())
        
        # 获取情感状态参数
        complexity = torch.sigmoid(emotional_state[:, 0])
        harmony = torch.sigmoid(emotional_state[:, 1])
        emotional_entropy = torch.sigmoid(emotional_state[:, 2])
        
        return {
            'resonance_detected': coherence > 0.5,
            'coherence': float(coherence),
            'dominant_frequency': float(max_freq_idx),
            'energy': float(energy.mean()),
            'emotional_state': {
                'complexity': float(complexity),
                'harmony': float(harmony),
                'entropy': float(emotional_entropy)
            }
        }
