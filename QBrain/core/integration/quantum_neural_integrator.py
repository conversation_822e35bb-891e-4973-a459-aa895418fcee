"""量子神经集成器"""
import numpy as np
import torch
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import time
import torch.nn.functional as F

from ..pattern.quantum_pattern_recognizer import QPattern, Pattern
from ..neural.quantum_neural_core import QCore
from ..neural.resonance_network import ResonanceNet
from ..quantum_field import QuantumField

@dataclass
class IntegrationState:
    """集成状态"""
    ψ: np.ndarray  # 量子场状态
    η: torch.Tensor  # 神经网络状态
    π: Pattern  # 识别的模式
    μ: float  # 相干度
    ε: float  # 集成度
    τ: float  # 时间戳

class IntegrationType(Enum):
    """集成类型"""
    FORWARD = "→"  # 前向集成
    BACKWARD = "←"  # 反向集成
    BIDIRECTIONAL = "↔"  # 双向集成

class QIntegrator:
    """量子神经集成器"""
    def __init__(self, field_dim: int = 512, neural_dim: Optional[int] = None, pattern_dim: Optional[int] = None):
        self.field_dim = field_dim
        self.neural_dim = neural_dim or field_dim
        self.pattern_dim = pattern_dim or field_dim // 2
        
        # Initialize components with consistent dimensions
        self.field = QuantumField(self.field_dim)  # Field dimension = field_dim
        self.neural = QCore(self.field_dim)  # Input dimension = field_dim
        self.resonance = ResonanceNet(self.field_dim, self.neural_dim)  # Input = field_dim, Output = neural_dim
        self.pattern = QPattern(self.field_dim, self.neural_dim, self.pattern_dim)  # Field = field_dim, Neural = neural_dim
        
        # State tracking
        self.states: Dict[str, IntegrationState] = {}
        self.τ = time.time()
        
    def integrate(self, 
                 ψ: Optional[np.ndarray] = None,
                 η: Optional[torch.Tensor] = None,
                 integration_type: IntegrationType = IntegrationType.BIDIRECTIONAL
                 ) -> str:
        """集成量子态和神经态"""
        self.τ = time.time()
        state_id = f"I{len(self.states)}"
        
        # 获取或生成状态
        if ψ is None:
            ψ = self.field.generate_field()
        if η is None:
            η = self.neural.generate_state()
            
        # 模式识别
        π = self.pattern.recognize(ψ)
        
        # 根据集成类型处理
        if integration_type == IntegrationType.FORWARD:
            # 量子→神经
            η_new = self._quantum_to_neural(ψ, π)
            ψ_new = ψ
        elif integration_type == IntegrationType.BACKWARD:
            # 神经→量子
            ψ_new = self._neural_to_quantum(η, π)
            η_new = η
        else:
            # 双向集成
            ψ_new, η_new = self._bidirectional_integration(ψ, η, π)
            
        # 计算集成指标
        μ = self._calculate_coherence(ψ_new, η_new)
        ε = self._calculate_integration(ψ_new, η_new, π)
        
        # 创建集成状态
        state = IntegrationState(
            ψ=ψ_new,
            η=η_new,
            π=π,
            μ=μ,
            ε=ε,
            τ=self.τ
        )
        
        # 存储状态
        self.states[state_id] = state
        return state_id
        
    def _quantum_to_neural(self, ψ: np.ndarray, π: Pattern) -> torch.Tensor:
        """量子态转神经态"""
        # 提取量子特征
        features = torch.from_numpy(np.abs(ψ)).float()
        
        # 通过共振网络
        resonance = self.resonance.forward(features)
        
        # 结合模式信息
        pattern_features = torch.from_numpy(np.abs(π.η)).float()
        pattern_features = F.interpolate(
            pattern_features.view(1, 1, -1), 
            size=self.neural_dim,
            mode='linear'
        ).view(-1)
        
        # 生成神经态
        combined_features = (resonance + pattern_features) / 2
        return self.neural.generate_state_from_features(combined_features)
        
    def _neural_to_quantum(self, η: torch.Tensor, π: Pattern) -> np.ndarray:
        """Neural to quantum state conversion"""
        # Extract neural features
        features = η.detach().cpu().numpy()
        
        # Combine with pattern information
        pattern_features = np.abs(π.η)
        pattern_features = F.interpolate(
            torch.from_numpy(pattern_features).float().view(1, 1, -1),
            size=self.field_dim,
            mode='linear'
        ).view(-1).numpy()
        
        # Generate quantum field
        combined_features = (features + pattern_features) / 2
        return self.field.generate_field_from_features(combined_features)
        
    def _bidirectional_integration(self, 
                                 ψ: np.ndarray,
                                 η: torch.Tensor,
                                 π: Pattern
                                 ) -> Tuple[np.ndarray, torch.Tensor]:
        """Bidirectional integration of quantum and neural states"""
        # Forward integration (quantum -> neural)
        η_forward = self._quantum_to_neural(ψ, π)
        
        # Backward integration (neural -> quantum)
        ψ_backward = self._neural_to_quantum(η, π)
        
        # Calculate weights based on energy
        w_q = np.abs(ψ).mean()
        w_n = float(torch.abs(η).mean())
        total = w_q + w_n
        w_q /= total
        w_n /= total
        
        # Combine states with weighted average
        η_new = w_q * η_forward + w_n * η
        ψ_new = w_q * ψ + w_n * ψ_backward
        
        # Normalize
        η_new = F.normalize(η_new, dim=0)
        ψ_new /= np.sqrt(np.sum(np.abs(ψ_new)**2))
        
        return ψ_new, η_new
        
    def _calculate_coherence(self, ψ: np.ndarray, η: torch.Tensor) -> float:
        """计算相干度"""
        # 量子相干度
        μ_q = float(np.abs(np.sum(ψ)) / len(ψ))
        
        # 神经相干度
        μ_n = float(torch.abs(torch.sum(η)) / len(η))
        
        return (μ_q + μ_n) / 2
        
    def _calculate_integration(self, 
                             ψ: np.ndarray,
                             η: torch.Tensor,
                             π: Pattern
                             ) -> float:
        """Calculate integration level between states"""
        # Convert quantum state to tensor
        ψ_tensor = torch.from_numpy(np.abs(ψ)).float()
        
        # Convert pattern state to tensor and resize
        π_tensor = F.interpolate(
            torch.from_numpy(np.abs(π.η)).float().view(1, 1, -1),
            size=self.field_dim,
            mode='linear'
        ).view(-1)
        
        # Calculate similarities
        ψη_sim = float(F.cosine_similarity(ψ_tensor.view(1, -1), η.view(1, -1)))
        ψπ_sim = float(F.cosine_similarity(ψ_tensor.view(1, -1), π_tensor.view(1, -1)))
        ηπ_sim = float(F.cosine_similarity(η.view(1, -1), π_tensor.view(1, -1)))
        
        # Return average similarity
        return (ψη_sim + ψπ_sim + ηπ_sim) / 3
        
    def evolve_state(self, state_id: str, δt: float = 0.1) -> Optional[str]:
        """演化集成状态"""
        if state_id not in self.states:
            return None
            
        state = self.states[state_id]
        
        # 演化量子态
        ψ_new = self.field.evolve_field(state.ψ, δt)
        
        # 演化神经态
        η_new = self.neural.evolve_state(state.η, δt)
        
        # 演化模式
        π_new = self.pattern.evolve_pattern(state.π, δt)
        
        # 创建新状态
        return self.integrate(ψ_new, η_new)
        
    def get_state(self, state_id: str) -> Optional[Dict[str, Any]]:
        """获取状态信息"""
        if state_id not in self.states:
            return None
            
        state = self.states[state_id]
        return {
            "id": state_id,
            "quantum_energy": float(np.sum(np.abs(state.ψ)**2)),
            "neural_energy": float(torch.sum(torch.abs(state.η))),
            "pattern_id": state.π.id,
            "coherence": state.μ,
            "integration": state.ε,
            "age": time.time() - state.τ
        }
        
    def get_system_state(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            "states": len(self.states),
            "field_state": self.field.get_field_state(),
            "neural_state": self.neural.get_system_state(),
            "pattern_state": self.pattern.get_system_state(),
            "average_coherence": float(np.mean([s.μ for s in self.states.values()])),
            "average_integration": float(np.mean([s.ε for s in self.states.values()])),
            "timestamp": time.time()
        }
