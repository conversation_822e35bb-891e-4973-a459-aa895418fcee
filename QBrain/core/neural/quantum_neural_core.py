"""QNCore"""
import numpy as np
from typing import Dict,List,Optional,Any,Tuple
from dataclasses import dataclass
from enum import Enum
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.autograd import Function
import time

class QState(Enum):
    φ="φ";ψ="ψ";χ="χ";ξ="ξ";η="η"

@dataclass
class ΨNode:
    ϕ:np.ndarray
    ω:float
    τ:float
    ε:complex
    δ:List[str]

class QGrad(Function):
    @staticmethod
    def forward(ctx, input: torch.Tensor, weight: torch.Tensor, alpha: float) -> torch.Tensor:
        # Save tensors for backward pass
        ctx.save_for_backward(input, weight)
        ctx.alpha = alpha
        
        # Ensure input is 2D
        if len(input.shape) == 1:
            input = input.view(1, -1)
            
        # Perform matrix multiplication
        return torch.matmul(input, weight)
    
    @staticmethod
    def backward(ctx, grad_output: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, None]:
        # Get saved tensors
        input, weight = ctx.saved_tensors
        alpha = ctx.alpha
        
        # Ensure input is 2D
        if len(input.shape) == 1:
            input = input.view(1, -1)
        
        # Compute gradients
        grad_input = torch.matmul(grad_output, weight.t()) * alpha
        grad_weight = torch.matmul(input.t(), grad_output) * alpha
        
        return grad_input, grad_weight, None

class ΨLayer(nn.Module):
    def __init__(self, input_dim: int, output_dim: int, α: float = 0.1):
        super().__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.α = α
        self.w = nn.Parameter(torch.randn(input_dim, output_dim) * 0.1)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # Ensure input is 2D
        if len(x.shape) == 1:
            x = x.view(1, -1)
        return QGrad.apply(x, self.w, self.α)

class QCore:
    def __init__(self, input_dim: int = 512, hidden_dim: Optional[int] = None, output_dim: Optional[int] = None):
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim or input_dim // 2
        self.output_dim = output_dim or input_dim
        self.ε = 0.01  # Learning rate
        
        # Initialize states
        self.ψ = {}  # State dictionary
        self.ϕ = np.zeros((self.input_dim, self.input_dim), dtype=np.complex128)  # Phase matrix
        self.η = self._init_η()  # Neural network
        self.ξ = torch.zeros(1, self.input_dim)  # Current state
        self.τ = time.time()  # Timestamp
        
    def _init_η(self) -> nn.Sequential:
        """Initialize neural network"""
        return nn.Sequential(
            ΨLayer(self.input_dim, self.hidden_dim),
            nn.Tanh(),
            ΨLayer(self.hidden_dim, self.hidden_dim),
            nn.Tanh(),
            ΨLayer(self.hidden_dim, self.output_dim),
            nn.Sigmoid()
        )
    
    def ϵ(self,ϕ:np.ndarray)->str:
        self.τ=time.time()
        ψid=f"ψ{len(self.ψ)}"
        self.ψ[ψid]=ΨNode(
            ϕ=ϕ,
            ω=self._calc_ω(ϕ),
            τ=self.τ,
            ε=self._calc_ε(ϕ),
            δ=[]
        )
        self._υ(ψid)
        return ψid
    
    def _calc_ω(self,ϕ:np.ndarray)->float:
        return float(np.sum(np.abs(ϕ)))
    
    def _calc_ε(self,ϕ:np.ndarray)->complex:
        return np.sum(ϕ*np.exp(2j*np.pi*np.random.random(len(ϕ))))
    
    def _υ(self,ψid:str):
        ψ=self.ψ[ψid]
        χ=torch.from_numpy(ψ.ϕ.real).float().view(1,-1)
        ξ=self.η(χ)
        self.ξ=self.ξ*0.9+ξ*0.1
        self._π(ψid)
    
    def _π(self,ψid:str):
        ψ=self.ψ[ψid]
        ϕ=np.outer(ψ.ϕ,ψ.ϕ.conj())
        self.ϕ=self.ϕ*(1-self.ε)+ϕ*self.ε
    
    def χ(self,ψids:List[str])->Optional[str]:
        if not all(i in self.ψ for i in ψids):return None
        ψs=[self.ψ[i] for i in ψids]
        χid=f"χ{len(self.ψ)}"
        self.ψ[χid]=ΨNode(
            ϕ=self._merge_ϕ(ψs),
            ω=np.mean([ψ.ω for ψ in ψs]),
            τ=time.time(),
            ε=self._merge_ε(ψs),
            δ=ψids
        )
        self._υ(χid)
        return χid
    
    def _merge_ϕ(self,ψs:List[ΨNode])->np.ndarray:
        ϕs=np.array([ψ.ϕ for ψ in ψs])
        return np.mean(ϕs,axis=0)
    
    def _merge_ε(self,ψs:List[ΨNode])->complex:
        return np.mean([ψ.ε for ψ in ψs])
    
    def ζ(self,ψid:str,n:int=1)->bool:
        if ψid not in self.ψ:return False
        ψ=self.ψ[ψid]
        for _ in range(n):
            ψ.ϕ=self._evolve_ϕ(ψ.ϕ)
            ψ.ω*=0.99
            ψ.ε=self._calc_ε(ψ.ϕ)
        self._υ(ψid)
        return True
    
    def _evolve_ϕ(self,ϕ:np.ndarray)->np.ndarray:
        ξ=np.fft.fft(ϕ)
        θ=np.random.random(len(ξ))*0.1
        ξ*=np.exp(1j*θ)
        ϕ=np.fft.ifft(ξ)
        return ϕ/np.sqrt(np.sum(np.abs(ϕ)**2))
    
    def η_state(self)->Dict[str,Any]:
        return {
            "ξ":self.ξ.numpy().tolist(),
            "ϕ_energy":float(np.sum(np.abs(self.ϕ))),
            "ϕ_coherence":float(np.mean(np.abs(self.ϕ))),
            "ψ_count":len(self.ψ)
        }
    
    def ψ_state(self,ψid:str)->Optional[Dict]:
        if ψid not in self.ψ:return None
        ψ=self.ψ[ψid]
        return {
            "ω":ψ.ω,
            "τ":ψ.τ,
            "ε":complex(ψ.ε),
            "δ":ψ.δ
        }
    
    def generate_state(self) -> torch.Tensor:
        """Generate a random neural state"""
        return torch.randn(self.input_dim)
        
    def generate_state_from_features(self, features: torch.Tensor) -> torch.Tensor:
        """Generate a neural state from input features"""
        # Ensure features have correct shape
        if len(features.shape) == 1:
            features = features.view(1, -1)
            
        # Pass through neural network
        state = self.η(features)
        
        # Normalize output
        state = F.normalize(state, dim=-1)
        
        return state.view(-1)
    
    def get_system_state(self) -> Dict[str, Any]:
        """Get the current state of the neural system"""
        return {
            "dimensions": {
                "input": self.input_dim,
                "hidden": self.hidden_dim,
                "output": self.output_dim
            },
            "learning_rate": self.ε,
            "current_state": {
                "phase_energy": float(np.abs(self.ϕ).mean()),
                "neural_energy": float(torch.abs(self.ξ).mean()),
                "timestamp": self.τ
            },
            "states": {
                state_id: {
                    "phase": float(np.angle(node.ε).mean()),
                    "energy": float(np.abs(node.ϕ).mean()),
                    "connections": len(node.δ)
                }
                for state_id, node in self.ψ.items()
            }
        }
