from datetime import datetime
from typing import Dict, Optional, List
import logging
from enum import Enum

class MessageType(Enum):
    QUERY = "query"           # 询问/请求
    SUGGESTION = "suggestion" # 建议
    RESPONSE = "response"     # 响应
    STATUS = "status"         # 状态更新

class ClaudeInterface:
    """Claude基础通信接口"""
    def __init__(self):
        self.dialogue_id = None
        self.read_permission = True
        self.write_permission = False
        self.message_history: List[Dict] = []
        
        # 配置日志
        self.logger = logging.getLogger("claude_interface")
        self.logger.setLevel(logging.INFO)
        
    def send_message(self, 
                    content: str, 
                    msg_type: MessageType = MessageType.RESPONSE) -> Dict:
        """发送消息"""
        message = {
            "dialogue_id": self.dialogue_id,
            "from": "claude",
            "type": msg_type.value,
            "content": content,
            "timestamp": datetime.now().isoformat()
        }
        
        self.message_history.append(message)
        self.logger.info(f"Sent message: {msg_type.value}")
        
        return message
        
    def receive_message(self, message: Dict) -> Optional[Dict]:
        """接收消息"""
        try:
            if message.get("to") != "claude":
                return None
                
            self.message_history.append(message)
            self.logger.info(f"Received message: {message.get('type')}")
            
            return {
                "status": "received",
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error processing message: {e}")
            return None
            
    def get_last_message(self) -> Optional[Dict]:
        """获取最后一条消息"""
        return self.message_history[-1] if self.message_history else None
