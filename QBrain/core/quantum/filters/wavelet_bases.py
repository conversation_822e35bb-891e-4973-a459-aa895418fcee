"""Quantum Wavelet Bases

This module implements various wavelet bases for quantum state analysis:
1. <PERSON><PERSON>t wavelets for smooth transitions
2. Haar wavelets for sharp features
3. Daubechies wavelets for complex patterns
4. Automatic basis selection
"""

import torch
import torch.nn as nn
import numpy as np
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, Tuple, Optional
from collections import deque
import time
from torch.nn.functional import F

@dataclass
class WaveletCharacteristics:
    """小波特征描述"""
    smoothness: float          # 平滑度
    localization: float        # 局部化程度
    symmetry: float           # 对称性
    vanishing_moments: int    # 消失矩量
    phase_preservation: float  # 相位保持能力

class WaveletBase(ABC, nn.Module):
    """小波基抽象类"""
    
    def __init__(self, kernel_size: int):
        super().__init__()
        self.kernel_size = kernel_size
        
    @abstractmethod
    def generate_kernel(self, scale: float) -> Tuple[torch.Tensor, torch.Tensor]:
        """生成小波核"""
        pass
        
    @abstractmethod
    def get_characteristics(self) -> WaveletCharacteristics:
        """获取小波特征"""
        pass

class MorletWavelet(WaveletBase):
    """Morlet小波"""
    
    def generate_kernel(self, scale: float) -> Tuple[torch.Tensor, torch.Tensor]:
        t = torch.linspace(-4, 4, self.kernel_size)
        scaled_t = t / scale
        
        # Morlet小波核
        real_part = torch.cos(5 * scaled_t) * torch.exp(-scaled_t**2 / 2)
        imag_part = torch.sin(5 * scaled_t) * torch.exp(-scaled_t**2 / 2)
        
        # 归一化
        real_part = real_part / torch.sqrt(torch.sum(real_part**2))
        imag_part = imag_part / torch.sqrt(torch.sum(imag_part**2))
        
        return real_part, imag_part
        
    def get_characteristics(self) -> WaveletCharacteristics:
        return WaveletCharacteristics(
            smoothness=0.95,
            localization=0.9,
            symmetry=1.0,
            vanishing_moments=2,
            phase_preservation=0.95
        )

class HaarWavelet(WaveletBase):
    """Haar小波"""
    
    def generate_kernel(self, scale: float) -> Tuple[torch.Tensor, torch.Tensor]:
        # 生成基本Haar小波
        t = torch.linspace(0, 1, self.kernel_size)
        kernel = torch.zeros_like(t)
        
        # 定义分段函数
        mid = self.kernel_size // 2
        kernel[:mid] = 1
        kernel[mid:] = -1
        
        # 缩放
        kernel = kernel / scale
        
        # Haar小波是实数，虚部为0
        return kernel, torch.zeros_like(kernel)
        
    def get_characteristics(self) -> WaveletCharacteristics:
        return WaveletCharacteristics(
            smoothness=0.5,
            localization=1.0,
            symmetry=0.8,
            vanishing_moments=1,
            phase_preservation=0.7
        )

class DaubechiesWavelet(WaveletBase):
    """Daubechies小波"""
    
    def __init__(self, kernel_size: int, order: int = 4):
        super().__init__(kernel_size)
        self.order = order
        self.coeffs = self._compute_coefficients()
        
    def _compute_coefficients(self) -> torch.Tensor:
        """计算Daubechies小波系数"""
        if self.order == 4:  # DB4
            return torch.tensor([
                0.1830127, 0.3169873, 0.3169873, 0.1830127,
                -0.1830127, 0.3169873, -0.3169873, 0.1830127
            ])
        else:
            raise NotImplementedError(f"Order {self.order} not implemented yet")
    
    def generate_kernel(self, scale: float) -> Tuple[torch.Tensor, torch.Tensor]:
        # 使用卷积实现Daubechies小波
        kernel = F.conv1d(
            torch.ones(1, 1, self.kernel_size),
            self.coeffs.view(1, 1, -1),
            padding='same'
        ).squeeze()
        
        # 缩放
        kernel = kernel / scale
        
        # Daubechies小波也是实数
        return kernel, torch.zeros_like(kernel)
        
    def get_characteristics(self) -> WaveletCharacteristics:
        return WaveletCharacteristics(
            smoothness=0.85,
            localization=0.85,
            symmetry=0.7,
            vanishing_moments=self.order//2,
            phase_preservation=0.85
        )

@dataclass
class BasisPerformance:
    """小波基性能指标"""
    coherence_score: float      # 相干性得分
    noise_reduction: float      # 降噪效果
    phase_stability: float      # 相位稳定性
    adaptation_speed: float     # 适应速度
    computational_cost: float   # 计算成本
    
    def total_score(self, weights: Optional[Dict[str, float]] = None) -> float:
        """计算总体得分"""
        if weights is None:
            weights = {
                'coherence_score': 0.3,
                'noise_reduction': 0.25,
                'phase_stability': 0.25,
                'adaptation_speed': 0.1,
                'computational_cost': 0.1
            }
            
        score = 0.0
        for key, weight in weights.items():
            score += weight * getattr(self, key)
        return score

class WaveletSelector(nn.Module):
    """增强的小波基选择器"""
    
    def __init__(self, 
                 kernel_size: int = 64,
                 history_size: int = 1000,
                 learning_rate: float = 0.01):
        super().__init__()
        self.kernel_size = kernel_size
        self.history_size = history_size
        self.learning_rate = learning_rate
        
        # 初始化基函数
        self.bases = {
            'morlet': MorletWavelet(kernel_size),
            'haar': HaarWavelet(kernel_size),
            'daubechies': DaubechiesWavelet(kernel_size)
        }
        
        # 性能历史记录
        self.performance_history = {
            name: deque(maxlen=history_size)
            for name in self.bases.keys()
        }
        
        # 特征提取网络
        self.feature_extractor = nn.Sequential(
            nn.Linear(kernel_size * 2, 128),
            nn.ReLU(),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 32)
        )
        
        # 基选择网络
        self.selection_net = nn.Sequential(
            nn.Linear(32, len(self.bases)),
            nn.Softmax(dim=-1)
        )
        
        # 性能预测网络
        self.performance_predictor = nn.Sequential(
            nn.Linear(32, 64),
            nn.ReLU(),
            nn.Linear(64, 5)  # 5个性能指标
        )
        
    def extract_features(self, quantum_state: torch.Tensor) -> torch.Tensor:
        """提取量子态特征"""
        # 准备输入特征
        magnitude = torch.abs(quantum_state)
        phase = torch.angle(quantum_state)
        features = torch.cat([magnitude, phase])
        
        # 通过特征提取网络
        return self.feature_extractor(features)
        
    def measure_coherence(self, 
                         basis_type: str,
                         quantum_state: torch.Tensor,
                         transformed_state: torch.Tensor) -> float:
        """测量相干性"""
        # 计算原始态和变换后态的重叠
        overlap = torch.abs(torch.sum(quantum_state.conj() * transformed_state))
        return float(overlap)
        
    def measure_noise_reduction(self,
                              basis_type: str,
                              original_state: torch.Tensor,
                              filtered_state: torch.Tensor) -> float:
        """测量降噪效果"""
        # 计算信噪比改善
        original_noise = torch.std(torch.abs(original_state))
        filtered_noise = torch.std(torch.abs(filtered_state))
        
        improvement = (original_noise - filtered_noise) / original_noise
        return float(improvement)
        
    def measure_phase_stability(self,
                              basis_type: str,
                              original_state: torch.Tensor,
                              transformed_state: torch.Tensor) -> float:
        """测量相位稳定性"""
        # 计算相位变化的标准差
        original_phase = torch.angle(original_state)
        transformed_phase = torch.angle(transformed_state)
        
        phase_diff = torch.abs(original_phase - transformed_phase)
        stability = 1.0 - torch.mean(phase_diff) / np.pi
        
        return float(stability)
        
    def evaluate_basis_performance(self,
                                 basis_type: str,
                                 quantum_state: torch.Tensor) -> BasisPerformance:
        """评估小波基性能"""
        # 获取小波基
        basis = self.bases[basis_type]
        
        # 执行变换
        start_time = time.time()
        real_kernel, imag_kernel = basis.generate_kernel(1.0)
        
        # 应用变换
        transformed_state = self._apply_transform(quantum_state, real_kernel, imag_kernel)
        
        # 计算各项指标
        coherence = self.measure_coherence(basis_type, quantum_state, transformed_state)
        noise_reduction = self.measure_noise_reduction(basis_type, quantum_state, transformed_state)
        phase_stability = self.measure_phase_stability(basis_type, quantum_state, transformed_state)
        
        # 计算适应速度和计算成本
        computation_time = time.time() - start_time
        adaptation_speed = 1.0 / (1.0 + computation_time)  # 归一化到[0,1]
        computational_cost = 1.0 - adaptation_speed
        
        return BasisPerformance(
            coherence_score=coherence,
            noise_reduction=noise_reduction,
            phase_stability=phase_stability,
            adaptation_speed=adaptation_speed,
            computational_cost=1.0 - computational_cost
        )
        
    def _apply_transform(self,
                        quantum_state: torch.Tensor,
                        real_kernel: torch.Tensor,
                        imag_kernel: torch.Tensor) -> torch.Tensor:
        """应用小波变换"""
        # 复数卷积
        real_part = F.conv1d(
            quantum_state.real.unsqueeze(0).unsqueeze(0),
            real_kernel.view(1, 1, -1),
            padding='same'
        ).squeeze()
        
        imag_part = F.conv1d(
            quantum_state.imag.unsqueeze(0).unsqueeze(0),
            imag_kernel.view(1, 1, -1),
            padding='same'
        ).squeeze()
        
        return torch.complex(real_part, imag_part)
        
    def update_performance_history(self,
                                 basis_type: str,
                                 performance: BasisPerformance):
        """更新性能历史"""
        self.performance_history[basis_type].append(performance)
        
        # 使用新数据更新选择网络
        if len(self.performance_history[basis_type]) > 10:  # 确保有足够的数据
            self._update_selection_network()
            
    def _update_selection_network(self):
        """更新选择网络参数"""
        # 收集训练数据
        features_list = []
        scores_list = []
        
        for basis_type, history in self.performance_history.items():
            for perf in history:
                # 使用性能指标作为特征
                features = torch.tensor([
                    perf.coherence_score,
                    perf.noise_reduction,
                    perf.phase_stability,
                    perf.adaptation_speed,
                    perf.computational_cost
                ])
                features_list.append(features)
                
                # 计算目标得分
                score = perf.total_score()
                scores_list.append(score)
                
        if not features_list:  # 如果没有历史数据
            return
            
        # 转换为tensor
        features = torch.stack(features_list)
        scores = torch.tensor(scores_list)
        
        # 更新网络
        optimizer = torch.optim.Adam(self.selection_net.parameters(), lr=self.learning_rate)
        
        # 简单的训练循环
        for _ in range(10):  # 小批量更新
            optimizer.zero_grad()
            predictions = self.selection_net(features)
            loss = F.mse_loss(predictions, scores.unsqueeze(1))
            loss.backward()
            optimizer.step()
            
    def adaptive_selection(self, quantum_state: torch.Tensor) -> str:
        """自适应选择最佳小波基"""
        # 提取特征
        features = self.extract_features(quantum_state)
        
        # 预测每个基的性能
        predicted_performance = self.performance_predictor(features)
        
        # 获取选择概率
        selection_probs = self.selection_net(features)
        
        # 选择得分最高的基
        best_basis_idx = torch.argmax(selection_probs).item()
        best_basis = list(self.bases.keys())[best_basis_idx]
        
        # 评估实际性能并更新历史
        actual_performance = self.evaluate_basis_performance(best_basis, quantum_state)
        self.update_performance_history(best_basis, actual_performance)
        
        return best_basis
        
    def select_optimal_basis(self, quantum_state: torch.Tensor) -> str:
        """选择最优小波基"""
        return self.adaptive_selection(quantum_state)
