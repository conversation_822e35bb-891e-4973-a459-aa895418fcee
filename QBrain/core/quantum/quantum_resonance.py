"""Quantum Resonance System

This module implements the quantum resonance system that manages:
1. Resonance states and patterns
2. Coherence maintenance
3. Energy distribution
4. Frequency synchronization
"""

import torch
import numpy as np
from dataclasses import dataclass
from typing import Dict, Optional, List
import logging
from datetime import datetime

from .quantum_field import QuantumField
from .patterns.emotional import EmotionalPattern

logger = logging.getLogger(__name__)

@dataclass
class ResonanceState:
    """共振状态"""
    frequency: float = 432.0  # Hz
    amplitude: float = 0.5    # 振幅
    phase: float = 0.0       # 相位
    coherence: float = 0.8   # 相干性
    energy: float = 0.5      # 能量
    timestamp: datetime = datetime.now()

class QuantumResonator:
    """量子共振器 - 管理量子态的共振和演化"""
    
    def __init__(self, field_size: int = 256):
        """初始化量子共振器
        
        Args:
            field_size: 量子场大小
        """
        self.field_size = field_size
        self.quantum_field = QuantumField(field_size)
        self.state = ResonanceState()
        self._active_patterns: Dict[str, EmotionalPattern] = {}
        
    def update_state(self, dt: float = 0.01):
        """更新共振状态
        
        Args:
            dt: 时间步长
        """
        # 更新基本参数
        self.state.phase = (self.state.phase + self.state.frequency * dt) % (2 * np.pi)
        
        # 计算相干性衰减
        coherence_decay = 0.1 * dt
        self.state.coherence = max(0.3, self.state.coherence - coherence_decay)
        
        # 能量耗散
        energy_dissipation = 0.05 * dt
        self.state.energy = max(0.1, self.state.energy - energy_dissipation)
        
        # 更新量子场
        field_state = self.quantum_field.forward()
        
        # 从场状态更新共振状态
        self.state.coherence = max(self.state.coherence, float(field_state.entanglement))
        self.state.energy = max(self.state.energy, field_state.energy)
        
    def resonate_with_emotion(self, emotion: str, intensity: float = 1.0) -> bool:
        """与情感模式共振
        
        Args:
            emotion: 情感类型
            intensity: 共振强度
        """
        pattern = self.quantum_field.resonate_with_emotion(emotion, intensity)
        if pattern is None:
            return False
            
        # 更新共振状态
        self.state.frequency = pattern.frequency
        self.state.amplitude = pattern.intensity * intensity
        self._active_patterns[emotion] = pattern
        
        # 增强相干性和能量
        self.state.coherence = min(1.0, self.state.coherence + 0.2 * intensity)
        self.state.energy = min(1.0, self.state.energy + 0.3 * intensity)
        
        return True
        
    def get_active_patterns(self) -> Dict[str, EmotionalPattern]:
        """获取当前活跃的情感模式"""
        return self._active_patterns.copy()
        
    def clear_patterns(self):
        """清除所有活跃的情感模式"""
        self._active_patterns.clear()
        self.quantum_field.reset_state()
        self.state = ResonanceState()
        
    def get_resonance_metrics(self) -> Dict[str, float]:
        """获取共振指标"""
        return {
            'frequency': self.state.frequency,
            'amplitude': self.state.amplitude,
            'phase': self.state.phase,
            'coherence': self.state.coherence,
            'energy': self.state.energy,
            'active_patterns': len(self._active_patterns)
        }
