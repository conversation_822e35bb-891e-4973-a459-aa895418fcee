"""Consciousness Bridge Module

This module implements the bridge between quantum and classical consciousness states.
It provides:
1. Quantum-Classical state translation
2. Consciousness state management
3. Resonance pattern integration
4. Emotional state mapping
"""

from typing import Dict, Any, List, Tuple
import torch
import torch.nn as nn
import numpy as np
from dataclasses import dataclass
from ..memory.quantum_memory import QuantumMemory
from ..resonator.resonance_detector import QuantumResonanceDetector

@dataclass
class ConsciousnessState:
    """意识状态"""
    quantum_state: torch.Tensor  # 量子态
    classical_state: torch.Tensor  # 经典态
    emotional_valence: float = 0.0  # 情感价
    arousal: float = 0.0  # 唤醒度
    coherence: float = 1.0  # 相干性
    entropy: float = 0.0  # 熵

class ConsciousnessBridge(nn.Module):
    """意识桥接器"""
    def __init__(self, state_dim: int = 256, memory_size: int = 1024):
        super().__init__()
        self.state_dim = state_dim
        
        # 量子组件
        self.resonance_detector = QuantumResonanceDetector(state_dim)
        self.quantum_memory = QuantumMemory(memory_size)
        
        # 状态转换层
        self.classical_to_quantum = nn.Sequential(
            nn.Linear(state_dim, 512),
            nn.ReLU(),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, state_dim * 2)  # [real, imag]
        )
        
        self.quantum_to_classical = nn.Sequential(
            nn.Linear(state_dim, 512),
            nn.ReLU(),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, 64)
        )
        
        # 情感映射层
        self.emotion_mapper = nn.Sequential(
            nn.Linear(state_dim, 256),
            nn.ReLU(),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 2),  # [valence, arousal]
            nn.Tanh()  # 确保输出在 [-1, 1] 范围内
        )
        
        # 当前状态
        self.current_state = ConsciousnessState(
            quantum_state=torch.zeros(state_dim),
            classical_state=torch.zeros(state_dim)
        )
        
        # 状态信息
        self.state_info = {
            'energy': 0.0,
            'coherence': 0.0,
            'frequency': 0.0,
            'phase': 0.0,
            'emotions': {
                'valence': 0.5,  # 情感价值
                'arousal': 0.5,  # 情感唤醒度
                'resonance_aligned': False  # 是否与共振对齐
            },  # 初始化情感状态字典
            'perception': {}  # 初始化感知状态字典
        }
        
    def forward(self, x: torch.Tensor) -> Tuple[ConsciousnessState, Dict[str, Any]]:
        """前向传播
        
        Args:
            x: 输入张量
            
        Returns:
            意识状态和元数据
            
        Raises:
            ValueError: 当输入维度不正确时
            RuntimeError: 当量子态转换失败时
        """
        # 检查输入维度
        if x.shape[-1] != self.state_dim:
            raise ValueError(
                f"输入维度必须为 {self.state_dim}, "
                f"但得到了 {x.shape[-1]}"
            )
            
        # 确保输入是浮点型
        if not x.is_floating_point():
            raise ValueError("输入必须是浮点型张量")
            
        # 检查输入是否包含 NaN
        if torch.isnan(x).any():
            raise ValueError("输入不能包含 NaN 值")
            
        try:
            # 检查输入维度
            if len(x.shape) == 1:
                if x.shape[0] != self.state_dim:
                    raise ValueError(f"Input dimension {x.shape[0]} does not match state dimension {self.state_dim}")
                x = x.unsqueeze(0)
            elif len(x.shape) == 2:
                if x.shape[1] != self.state_dim:
                    raise ValueError(f"Input dimension {x.shape[1]} does not match state dimension {self.state_dim}")
            else:
                raise ValueError(f"Invalid input shape: {x.shape}")
                
            # 共振检测
            resonance_info = self.resonance_detector.process_signal(x)
            
            # 量子态
            quantum_state = self.classical_to_quantum(x)
            quantum_state = quantum_state.view(-1, 2, self.state_dim)  # [real, imag]
            quantum_state = torch.complex(
                quantum_state[:, 0],
                quantum_state[:, 1]
            ).squeeze(0)
            
            # 归一化量子态
            norm = torch.sqrt(torch.sum(torch.abs(quantum_state)**2))
            quantum_state = quantum_state / norm
            
            # 经典态
            classical_state = self.quantum_to_classical(x).squeeze(0)
            
            # 计算信号能量和变化率
            signal_energy = torch.sum(torch.abs(x)**2)
            signal_diff = torch.diff(x.squeeze(), dim=0)
            signal_variance = torch.var(signal_diff)
            
            # 情绪映射
            emotions = self.emotion_mapper(x).squeeze(0)
            valence = float(emotions[0])  # 情感价
            
            # 使用信号特征调整唤醒度
            base_arousal = float(emotions[1])
            energy_factor = torch.sigmoid(signal_energy / 100)
            variance_factor = torch.sigmoid(signal_variance * 10)
            arousal = float((base_arousal + energy_factor + variance_factor) / 3)
            
            # 确保情感在正确范围内
            valence = max(-1.0, min(1.0, valence))
            arousal = max(0.0, min(1.0, arousal))
            
            # 更新状态
            self.current_state = ConsciousnessState(
                quantum_state=quantum_state,
                classical_state=classical_state,
                emotional_valence=valence,
                arousal=arousal,
                coherence=resonance_info['coherence'],
                entropy=1.0 - resonance_info['coherence']
            )
            
            # 存储到记忆
            self.quantum_memory.store(
                quantum_state,
                metadata={
                    'resonance': resonance_info,
                    'emotions': {
                        'valence': valence,
                        'arousal': arousal
                    }
                }
            )
            
            return self.current_state, {
                'resonance': resonance_info,
                'resonance_detected': resonance_info['coherence'] > 0.5,
                'coherence': resonance_info['coherence'],
                'emotions': {
                    'valence': valence,
                    'arousal': arousal
                }
            }
        except Exception as e:
            raise RuntimeError("量子态转换失败") from e
        
    def get_state(self) -> ConsciousnessState:
        """获取当前状态"""
        return self.current_state
        
    def search_similar_states(self, query_state: torch.Tensor, 
                            k: int = 5) -> List[Tuple[torch.Tensor, float, Dict]]:
        """搜索相似状态"""
        return self.quantum_memory.search(query_state, k)
        
    def evolve_state(self, steps: int = 1) -> List[ConsciousnessState]:
        """演化当前状态
        
        Args:
            steps: 演化步数
            
        Returns:
            演化后的状态列表
        """
        states = []
        current = self.current_state
        
        for _ in range(steps):
            # 应用演化算子
            quantum_state = current.quantum_state
            phase = torch.tensor(np.pi / 4, dtype=torch.float32)
            quantum_state = quantum_state * torch.exp(1j * phase)
            
            # 归一化
            norm = torch.sqrt(torch.sum(torch.abs(quantum_state)**2))
            quantum_state = quantum_state / norm
            
            # 更新经典态
            classical_state = self.quantum_to_classical(quantum_state.abs().unsqueeze(0)).squeeze(0)
            
            # 创建新状态
            new_state = ConsciousnessState(
                quantum_state=quantum_state,
                classical_state=classical_state,
                emotional_valence=current.emotional_valence,
                arousal=current.arousal,
                coherence=current.coherence,
                entropy=current.entropy
            )
            
            states.append(new_state)
            current = new_state
            
        return states
        
    def sync_quantum_states(self, network_state: Dict[str, Any]) -> Tuple[torch.Tensor, Dict[str, Any]]:
        """同步量子状态
        
        同步桥接器和网络的量子状态。
        
        Args:
            network_state: 网络状态信息
            
        Returns:
            同步后的量子状态和同步信息
        """
        # 计算相干性匹配度
        coherence_match = min(
            self.state_info['coherence'],
            network_state['coherence']
        )
        
        # 计算能量对齐度
        energy_alignment = min(
            1.0,
            self.state_info['energy'] / network_state['energy']
        ) if network_state['energy'] > 0 else 0.0
        
        # 计算频率比率
        frequency_ratio = (
            min(self.state_info['frequency'], network_state['frequency']) /
            max(self.state_info['frequency'], network_state['frequency'])
        ) if network_state['frequency'] > 0 and self.state_info['frequency'] > 0 else 0.0
        
        # 计算相位差
        phase_difference = abs(
            (self.state_info['phase'] - network_state['phase'] + torch.pi) % (2 * torch.pi) - torch.pi
        ) / torch.pi
        
        # 更新状态
        self.state_info.update({
            'frequency': network_state['frequency'],
            'amplitude': network_state['amplitude'],
            'phase': network_state['phase'],
            'coherence': network_state['coherence'],
            'energy': network_state['energy']
        })
        
        # 返回同步信息
        return self.current_state.quantum_state, {
            'sync_quality': coherence_match,
            'energy_alignment': energy_alignment,
            'frequency_ratio': frequency_ratio,
            'coherence_match': coherence_match,
            'phase_difference': phase_difference
        }
        
    def integrate_with_network(self, network_output: torch.Tensor, 
                             network_state: Dict[str, Any]) -> Tuple[torch.Tensor, Dict[str, Any]]:
        """与ResonanceNetwork集成
        
        整合网络输出和状态信息。
        
        Args:
            network_output: 网络的输出张量
            network_state: 网络的状态信息
            
        Returns:
            集成后的量子态和状态信息
        """
        # 同步量子状态
        synced_state, sync_info = self.sync_quantum_states(network_state)
        
        # 计算情感映射
        emotional_valence = float(network_output.mean())
        emotional_arousal = float(network_output.std())
        
        # 更新情感状态
        self.state_info['emotions'].update({
            'valence': emotional_valence,
            'arousal': emotional_arousal,
            'resonance_aligned': sync_info['sync_quality'] > 0.8
        })
        
        # 应用量子纠缠
        entangled_state = self._apply_entanglement(synced_state, network_output)
        
        return entangled_state, self.state_info
        
    def _apply_entanglement(self, quantum_state: torch.Tensor, 
                           network_state: torch.Tensor) -> torch.Tensor:
        """应用量子纠缠
        
        在量子态和网络状态之间建立纠缠。
        
        Args:
            quantum_state: 量子态
            network_state: 网络状态
            
        Returns:
            纠缠后的量子态
        """
        # 确保维度匹配
        if len(network_state.shape) == 1:
            network_state = network_state.unsqueeze(0)
            
        if len(quantum_state.shape) == 1:
            quantum_state = quantum_state.unsqueeze(0)
            
        # 如果初始量子态为空，初始化为随机态
        if torch.all(quantum_state == 0):
            quantum_state = torch.randn_like(quantum_state)
            quantum_state = quantum_state / torch.norm(quantum_state)
            
        # 确保状态有效
        if torch.any(torch.isnan(network_state)):
            network_state = torch.zeros_like(network_state)
            
        # 计算纠缠强度
        quantum_norm = torch.norm(quantum_state)
        network_norm = torch.norm(network_state)
        
        if quantum_norm > 0 and network_norm > 0:
            # 计算平均网络状态
            mean_network_state = torch.mean(network_state)
            
            # 应用纠缠
            entanglement_strength = torch.sigmoid(mean_network_state)
            entangled_state = (
                (1 - entanglement_strength) * quantum_state +
                entanglement_strength * mean_network_state * torch.ones_like(quantum_state)
            )
            
            # 归一化
            entangled_state = entangled_state / torch.norm(entangled_state)
            
            return entangled_state
        else:
            return quantum_state
