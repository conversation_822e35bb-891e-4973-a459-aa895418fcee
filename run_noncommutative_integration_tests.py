#!/usr/bin/env python3
"""
非交换空间算子分布式集成测试运行脚本

该脚本用于运行所有的非交换空间算子分布式集成测试。
"""

import os
import sys
import unittest
import argparse
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

# 导入测试模块
from tests.integration.test_noncommutative_integration import TestNoncommutativeIntegration
from tests.integration.test_noncommutative_integration.test_bundle_integration import TestBundleIntegration
from tests.integration.test_noncommutative_integration.test_transport_integration import TestTransportIntegration

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def run_tests(test_names=None, verbose=False):
    """
    运行测试
    
    参数:
        test_names: 要运行的测试名称列表，如果为None则运行所有测试
        verbose: 是否显示详细输出
    """
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加测试用例
    if test_names is None or "all" in test_names:
        # 添加所有测试用例
        suite.addTest(unittest.makeSuite(TestNoncommutativeIntegration))
        suite.addTest(unittest.makeSuite(TestBundleIntegration))
        suite.addTest(unittest.makeSuite(TestTransportIntegration))
    else:
        # 添加指定的测试用例
        for test_name in test_names:
            if test_name == "main":
                suite.addTest(unittest.makeSuite(TestNoncommutativeIntegration))
            elif test_name == "bundle":
                suite.addTest(unittest.makeSuite(TestBundleIntegration))
            elif test_name == "transport":
                suite.addTest(unittest.makeSuite(TestTransportIntegration))
            else:
                logger.warning(f"未知的测试名称: {test_name}")
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2 if verbose else 1)
    result = runner.run(suite)
    
    # 返回测试结果
    return result


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="运行非交换空间算子分布式集成测试")
    parser.add_argument(
        "tests",
        nargs="*",
        default=["all"],
        help="要运行的测试名称，可选值包括: all, main, bundle, transport"
    )
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="显示详细输出"
    )
    args = parser.parse_args()
    
    # 运行测试
    result = run_tests(args.tests, args.verbose)
    
    # 输出测试结果
    if result.wasSuccessful():
        logger.info("所有测试通过！")
        sys.exit(0)
    else:
        logger.error(f"测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        sys.exit(1)


if __name__ == "__main__":
    main()
