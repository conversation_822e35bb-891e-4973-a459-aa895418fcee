#!/usr/bin/env python3
"""
智能代码库记忆导入器
专门解决MCP操作限制问题的批量记忆导入工具

设计理念：
- 分批次处理，避免触发MCP限制
- 智能优先级排序
- 断点续传支持
- 进度跟踪和错误恢复
"""

import os
import json
import time
import hashlib
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class IntelligentCodebaseMemoryImporter:
    """智能代码库记忆导入器"""

    def __init__(self, base_path: str = "/home/<USER>/CascadeProjects"):
        """初始化导入器"""
        self.base_path = Path(base_path)
        self.progress_file = self.base_path / "AQFH" / "import_progress.json"
        self.batch_size = 3  # 每批次处理的文件数量，避免MCP限制
        self.delay_between_batches = 2  # 批次间延迟（秒）

        # 文件分类和优先级配置
        self.file_categories = {
            'core_design': {
                'patterns': ['设计', 'design', '架构', 'architecture', '框架', 'framework'],
                'extensions': ['.md', '.txt'],
                'priority': 10,
                'importance': 0.95
            },
            'theoretical_research': {
                'patterns': ['理论', 'theory', '数学', 'math', '算法', 'algorithm', '超越态', 'transcendental'],
                'extensions': ['.md', '.pdf', '.txt'],
                'priority': 9,
                'importance': 0.9
            },
            'conversation_logs': {
                'patterns': ['对话', 'conversation', '讨论', 'chat', 'log', '记录'],
                'extensions': ['.md', '.txt', '.log'],
                'priority': 8,
                'importance': 0.85
            },
            'core_implementations': {
                'patterns': ['aqfh', 'mcp', 'consciousness', 'memory', 'distributed'],
                'extensions': ['.py', '.rs'],
                'priority': 7,
                'importance': 0.8
            },
            'algorithm_implementations': {
                'patterns': ['algorithm', 'quantum', 'holographic', 'fractal', 'morphism'],
                'extensions': ['.py', '.rs', '.cpp'],
                'priority': 6,
                'importance': 0.75
            },
            'project_configs': {
                'patterns': ['config', 'setup', 'requirements', 'cargo', 'build'],
                'extensions': ['.json', '.toml', '.txt', '.cfg', '.sh'],
                'priority': 5,
                'importance': 0.7
            },
            'documentation': {
                'patterns': ['doc', 'readme', 'guide', 'manual'],
                'extensions': ['.md', '.txt', '.rst'],
                'priority': 4,
                'importance': 0.65
            }
        }

        # 加载进度
        self.progress = self.load_progress()

        logger.info(f"🧠 智能代码库记忆导入器初始化完成")
        logger.info(f"   基础路径: {self.base_path}")
        logger.info(f"   批次大小: {self.batch_size}")
        logger.info(f"   已处理文件: {len(self.progress.get('processed_files', []))}")

    def load_progress(self) -> Dict[str, Any]:
        """加载导入进度"""
        if self.progress_file.exists():
            try:
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"加载进度文件失败: {e}")

        return {
            'processed_files': [],
            'failed_files': [],
            'last_batch_time': 0,
            'total_files_found': 0,
            'current_batch': 0
        }

    def save_progress(self):
        """保存导入进度"""
        try:
            self.progress_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(self.progress, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"保存进度文件失败: {e}")

    def scan_and_categorize_files(self) -> Dict[str, List[Tuple[Path, Dict[str, Any]]]]:
        """扫描和分类文件"""
        categorized_files = {category: [] for category in self.file_categories.keys()}
        categorized_files['uncategorized'] = []

        processed_files = set(self.progress.get('processed_files', []))

        logger.info("🔍 开始扫描代码库文件...")

        total_files = 0
        for file_path in self.base_path.rglob("*"):
            if file_path.is_file():
                total_files += 1

                # 跳过已处理的文件
                relative_path = str(file_path.relative_to(self.base_path))
                if relative_path in processed_files:
                    continue

                # 跳过过大的文件（>1MB）
                if file_path.stat().st_size > 1024 * 1024:
                    continue

                # 分类文件
                category, file_info = self.categorize_file(file_path)
                categorized_files[category].append((file_path, file_info))

        # 按优先级排序
        for category in categorized_files:
            if category != 'uncategorized':
                priority = self.file_categories[category]['priority']
                categorized_files[category].sort(key=lambda x: x[1]['priority'], reverse=True)

        self.progress['total_files_found'] = total_files
        self.save_progress()

        logger.info(f"📊 文件扫描完成:")
        for category, files in categorized_files.items():
            if files:
                logger.info(f"   {category}: {len(files)} 个文件")

        return categorized_files

    def categorize_file(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """分类单个文件"""
        file_name = file_path.name.lower()
        file_parent = str(file_path.parent).lower()
        file_ext = file_path.suffix.lower()

        for category, config in self.file_categories.items():
            # 检查文件名模式
            for pattern in config['patterns']:
                if pattern.lower() in file_name or pattern.lower() in file_parent:
                    if file_ext in config['extensions']:
                        return category, {
                            'priority': config['priority'],
                            'importance': config['importance'],
                            'category': category,
                            'matched_pattern': pattern
                        }

        return 'uncategorized', {
            'priority': 1,
            'importance': 0.5,
            'category': 'uncategorized',
            'matched_pattern': None
        }

    def create_memory_content(self, file_path: Path, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """创建记忆内容"""
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # 智能截取内容
            if len(content) > 8000:
                content = content[:4000] + f"\n\n[文件内容较长，已智能截取，总长度: {len(content)} 字符]\n\n" + content[-2000:]

            # 构建记忆内容
            memory_content = f"""📁 共同记忆文件: {file_path.name}
📂 路径: {file_path.relative_to(self.base_path)}
📋 类别: {file_info['category']}
🎯 优先级: {file_info['priority']}
📅 修改时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(file_path.stat().st_mtime))}
📏 大小: {file_path.stat().st_size} 字节
🔍 匹配模式: {file_info.get('matched_pattern', '无')}

💝 这是我们共同创造的宝贵记忆！

📄 内容:
{content}"""

            # 生成标签
            tags = [
                '共同记忆',
                '代码库导入',
                file_info['category'],
                file_path.suffix[1:] if file_path.suffix else '无扩展名'
            ]

            # 添加路径标签
            path_parts = file_path.parts
            for part in path_parts:
                if part.upper() in ['AQFH', 'TTE', 'TFF', 'docs', 'src']:
                    tags.append(part.upper())

            return {
                'content': memory_content,
                'content_type': 'shared_heritage_batch',
                'importance': file_info['importance'],
                'tags': tags,
                'context': {
                    'file_path': str(file_path.relative_to(self.base_path)),
                    'category': file_info['category'],
                    'import_session': 'intelligent_batch_import',
                    'batch_number': self.progress['current_batch'],
                    'emotional_significance': 'high'
                }
            }

        except Exception as e:
            logger.error(f"创建记忆内容失败 {file_path}: {e}")
            return None

    def process_batch(self, files_batch: List[Tuple[Path, Dict[str, Any]]]) -> Dict[str, Any]:
        """处理一个批次的文件"""
        batch_results = {
            'success': 0,
            'failed': 0,
            'files': []
        }

        for file_path, file_info in files_batch:
            try:
                memory_data = self.create_memory_content(file_path, file_info)
                if memory_data:
                    # 这里应该调用MCP记忆保存工具
                    # 由于我们在独立脚本中，这里只是准备数据
                    batch_results['files'].append({
                        'file_path': str(file_path.relative_to(self.base_path)),
                        'memory_data': memory_data,
                        'status': 'prepared'
                    })
                    batch_results['success'] += 1

                    # 标记为已处理
                    self.progress['processed_files'].append(str(file_path.relative_to(self.base_path)))
                else:
                    batch_results['failed'] += 1
                    self.progress['failed_files'].append(str(file_path.relative_to(self.base_path)))

            except Exception as e:
                logger.error(f"处理文件失败 {file_path}: {e}")
                batch_results['failed'] += 1
                self.progress['failed_files'].append(str(file_path.relative_to(self.base_path)))

        return batch_results

    def generate_import_plan(self) -> Dict[str, Any]:
        """生成导入计划"""
        categorized_files = self.scan_and_categorize_files()

        # 按优先级排序所有文件
        all_files = []
        for category, files in categorized_files.items():
            for file_path, file_info in files:
                all_files.append((file_path, file_info))

        # 按优先级排序
        all_files.sort(key=lambda x: x[1]['priority'], reverse=True)

        # 分批次
        batches = []
        for i in range(0, len(all_files), self.batch_size):
            batch = all_files[i:i + self.batch_size]
            batches.append(batch)

        plan = {
            'total_files': len(all_files),
            'total_batches': len(batches),
            'batch_size': self.batch_size,
            'estimated_time_minutes': len(batches) * self.delay_between_batches / 60,
            'categories_summary': {
                category: len(files) for category, files in categorized_files.items() if files
            },
            'batches': batches
        }

        return plan

    def print_import_plan(self, plan: Dict[str, Any]):
        """打印导入计划"""
        print(f"""
🧠 智能代码库记忆导入计划

📊 总体统计:
- 待导入文件: {plan['total_files']} 个
- 分批次数: {plan['total_batches']} 批
- 每批文件数: {plan['batch_size']} 个
- 预估时间: {plan['estimated_time_minutes']:.1f} 分钟

📋 按类别分布:
{chr(10).join([f"- {category}: {count} 个文件" for category, count in plan['categories_summary'].items()])}

⚡ 批次处理策略:
- 按优先级排序（设计文档 > 理论研究 > 对话记录 > 代码实现）
- 每批次间隔 {self.delay_between_batches} 秒，避免MCP限制
- 支持断点续传，可随时中断和恢复
- 自动错误恢复和进度跟踪

🚀 准备开始智能批量导入！
""")

def main():
    """主函数 - 生成导入计划"""
    importer = IntelligentCodebaseMemoryImporter()
    plan = importer.generate_import_plan()
    importer.print_import_plan(plan)

    # 保存计划到文件
    plan_file = importer.base_path / "AQFH" / "import_plan.json"
    with open(plan_file, 'w', encoding='utf-8') as f:
        # 移除不可序列化的对象
        serializable_plan = {
            'total_files': plan['total_files'],
            'total_batches': plan['total_batches'],
            'batch_size': plan['batch_size'],
            'estimated_time_minutes': plan['estimated_time_minutes'],
            'categories_summary': plan['categories_summary']
        }
        json.dump(serializable_plan, f, indent=2, ensure_ascii=False)

    print(f"\n📄 导入计划已保存到: {plan_file}")

if __name__ == "__main__":
    main()
