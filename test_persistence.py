#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
注册表持久化测试脚本
"""

import os
import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

# 导入注册表模块
from src.rust_bindings.operator_registry import (
    OperatorCategory,
    register_operator,
    get_operator,
    list_operators,
    get_operator_metadata,
    check_compatibility,
    get_global_registry,
    save_registry_to_file,
    load_registry_from_file,
    auto_load_registry,
    auto_save_registry
)

# 导入错误处理算子
from src.rust_bindings.operator_registry.error_handling import (
    register_error_handling_operator,
    get_error_handling_operator_from_registry
)

class TestOperator:
    """测试算子"""
    
    def __init__(self, name):
        self.name = name
        
    def apply(self, data):
        """应用算子"""
        return f"Applied {self.name} to {data}"
        
    def get_metadata(self):
        """获取元数据"""
        return {
            "name": self.name,
            "type": "test",
            "description": f"Test operator {self.name}",
            "version": "1.0.0"
        }

def main():
    """主函数"""
    print("注册表持久化测试")
    print("=" * 50)
    
    # 获取全局注册表
    print("\n1. 获取全局注册表")
    registry = get_global_registry()
    print(f"全局注册表: {registry}")
    
    # 列出所有算子
    print("\n2. 列出所有算子")
    operators = list_operators()
    print(f"算子数量: {len(operators)}")
    for category, name in operators:
        print(f"- {category}.{name}")
    
    # 注册测试算子
    print("\n3. 注册测试算子")
    test_op1 = TestOperator("test_op1")
    success1 = register_operator(
        OperatorCategory.UTILITY,
        "test_op1",
        test_op1,
        "1.0.0",
        "Test operator 1",
        ["test", "utility"],
        []
    )
    print(f"注册结果1: {success1}")
    
    test_op2 = TestOperator("test_op2")
    success2 = register_operator(
        OperatorCategory.TRANSFORM,
        "test_op2",
        test_op2,
        "2.0.0",
        "Test operator 2",
        ["test", "transform"],
        [("utility.test_op1", ">=1.0.0")]
    )
    print(f"注册结果2: {success2}")
    
    # 注册错误处理算子
    print("\n4. 注册错误处理算子")
    success3 = register_error_handling_operator()
    print(f"注册结果3: {success3}")
    
    # 列出所有算子
    print("\n5. 列出所有算子")
    operators = list_operators()
    print(f"算子数量: {len(operators)}")
    for category, name in operators:
        print(f"- {category}.{name}")
    
    # 保存注册表
    print("\n6. 保存注册表")
    registry_path = os.path.join(project_root, "data", "test_registry.json")
    success = save_registry_to_file(registry_path)
    print(f"保存结果: {success}")
    
    # 清空注册表
    print("\n7. 清空注册表")
    registry.clear()
    print(f"清空后的算子数量: {len(list_operators())}")
    
    # 加载注册表
    print("\n8. 加载注册表")
    success = load_registry_from_file(registry_path)
    print(f"加载结果: {success}")
    
    # 列出所有算子
    print("\n9. 列出所有算子")
    operators = list_operators()
    print(f"算子数量: {len(operators)}")
    for category, name in operators:
        print(f"- {category}.{name}")
    
    # 测试自动保存和加载
    print("\n10. 测试自动保存和加载")
    success = auto_save_registry()
    print(f"自动保存结果: {success}")
    
    # 清空注册表
    registry.clear()
    print(f"清空后的算子数量: {len(list_operators())}")
    
    # 自动加载
    success = auto_load_registry()
    print(f"自动加载结果: {success}")
    
    # 列出所有算子
    operators = list_operators()
    print(f"算子数量: {len(operators)}")
    for category, name in operators:
        print(f"- {category}.{name}")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
