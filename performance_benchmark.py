#!/usr/bin/env python3
"""
AQFH系统性能基准测试工具
为Rust化迁移提供详细的性能基准数据
"""

import time
import psutil
import sys
import tracemalloc
import asyncio
from pathlib import Path
from typing import Dict, List, Any
import statistics

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

class PerformanceBenchmark:
    """AQFH系统性能基准测试器"""
    
    def __init__(self):
        """初始化基准测试器"""
        self.results = {}
        self.process = psutil.Process()
        
    def measure_memory_usage(self):
        """测量内存使用情况"""
        memory_info = self.process.memory_info()
        return {
            "rss_mb": memory_info.rss / 1024 / 1024,  # 物理内存
            "vms_mb": memory_info.vms / 1024 / 1024,  # 虚拟内存
            "percent": self.process.memory_percent()   # 内存占用百分比
        }
    
    def measure_cpu_usage(self):
        """测量CPU使用情况"""
        return {
            "percent": self.process.cpu_percent(),
            "num_threads": self.process.num_threads()
        }
    
    def benchmark_memory_operations(self):
        """基准测试：记忆操作性能"""
        print("\n🧠 基准测试：记忆操作性能")
        print("-" * 40)
        
        try:
            from aqfh_unified_consciousness_mcp import unified_system
            
            # 测试数据
            test_memories = [
                f"🧪 性能测试记忆 {i} - 这是一条用于性能基准测试的记忆，包含足够的文本内容来模拟真实使用场景。"
                for i in range(100)
            ]
            
            # 1. 记忆保存性能测试
            print("📝 测试记忆保存性能...")
            tracemalloc.start()
            start_time = time.time()
            start_memory = self.measure_memory_usage()
            
            save_times = []
            for i, memory_content in enumerate(test_memories):
                save_start = time.time()
                memory_id = unified_system.save_memory(
                    content=memory_content,
                    content_type="benchmark_test",
                    importance=0.5,
                    tags=[f"test_{i}", "benchmark"],
                    context={"test_id": i, "batch": "performance_test"}
                )
                save_end = time.time()
                save_times.append(save_end - save_start)
                
                if (i + 1) % 20 == 0:
                    print(f"   已保存 {i + 1}/100 条记忆...")
            
            end_time = time.time()
            end_memory = self.measure_memory_usage()
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            save_results = {
                "total_time": end_time - start_time,
                "avg_time_per_save": statistics.mean(save_times),
                "min_time": min(save_times),
                "max_time": max(save_times),
                "memory_increase_mb": end_memory["rss_mb"] - start_memory["rss_mb"],
                "peak_memory_mb": peak / 1024 / 1024,
                "operations_per_second": len(test_memories) / (end_time - start_time)
            }
            
            print(f"   ✅ 保存100条记忆耗时: {save_results['total_time']:.3f}s")
            print(f"   ⚡ 平均每条保存时间: {save_results['avg_time_per_save']:.4f}s")
            print(f"   🚀 保存操作/秒: {save_results['operations_per_second']:.2f}")
            print(f"   💾 内存增长: {save_results['memory_increase_mb']:.2f}MB")
            
            # 2. 记忆检索性能测试
            print("\n🔍 测试记忆检索性能...")
            search_queries = ["性能测试", "benchmark", "记忆", "测试", "内容"]
            
            search_times = []
            for query in search_queries:
                search_start = time.time()
                results = unified_system.search_memories(query, limit=20)
                search_end = time.time()
                search_times.append(search_end - search_start)
                print(f"   查询'{query}': {len(results)}条结果, {search_end - search_start:.4f}s")
            
            search_results = {
                "avg_search_time": statistics.mean(search_times),
                "min_search_time": min(search_times),
                "max_search_time": max(search_times),
                "searches_per_second": len(search_queries) / sum(search_times)
            }
            
            print(f"   ⚡ 平均检索时间: {search_results['avg_search_time']:.4f}s")
            print(f"   🚀 检索操作/秒: {search_results['searches_per_second']:.2f}")
            
            # 3. 复杂查询性能测试
            print("\n🧮 测试复杂查询性能...")
            complex_start = time.time()
            complex_results = unified_system.recall_memories("性能测试 benchmark", limit=50)
            complex_end = time.time()
            
            complex_results_data = {
                "complex_query_time": complex_end - complex_start,
                "results_count": len(complex_results)
            }
            
            print(f"   ⚡ 复杂查询时间: {complex_results_data['complex_query_time']:.4f}s")
            print(f"   📊 返回结果数: {complex_results_data['results_count']}")
            
            self.results["memory_operations"] = {
                "save": save_results,
                "search": search_results,
                "complex_query": complex_results_data
            }
            
            return True
            
        except Exception as e:
            print(f"❌ 记忆操作基准测试失败: {e}")
            traceback.print_exc()
            return False
    
    def benchmark_advanced_structures(self):
        """基准测试：高级图结构性能"""
        print("\n🧮 基准测试：高级图结构性能")
        print("-" * 40)
        
        try:
            from aqfh_unified_consciousness_mcp import unified_system
            
            # 测试高级结构的处理能力
            advanced_test_data = [
                {
                    "content": f"🧮 高级结构测试 {i} - 纤维丛认知模式：逻辑推理、创造思维、情感处理。拓扑分析：情感状态映射。分形组织：层级知识结构。量子语义：多维语义空间。",
                    "importance": 0.8,
                    "tags": ["高级结构", "认知模式", "情感拓扑", "分形组织", "量子语义"]
                }
                for i in range(50)
            ]
            
            print("🔬 测试高级图结构处理性能...")
            start_time = time.time()
            start_memory = self.measure_memory_usage()
            
            for i, test_data in enumerate(advanced_test_data):
                unified_system.save_memory(
                    content=test_data["content"],
                    content_type="advanced_structure_test",
                    importance=test_data["importance"],
                    tags=test_data["tags"],
                    context={"advanced_test": True, "structure_id": i}
                )
                
                if (i + 1) % 10 == 0:
                    print(f"   已处理 {i + 1}/50 条高级结构数据...")
            
            end_time = time.time()
            end_memory = self.measure_memory_usage()
            
            advanced_results = {
                "total_time": end_time - start_time,
                "avg_time_per_item": (end_time - start_time) / len(advanced_test_data),
                "memory_increase_mb": end_memory["rss_mb"] - start_memory["rss_mb"],
                "items_per_second": len(advanced_test_data) / (end_time - start_time)
            }
            
            print(f"   ✅ 处理50条高级结构数据耗时: {advanced_results['total_time']:.3f}s")
            print(f"   ⚡ 平均每条处理时间: {advanced_results['avg_time_per_item']:.4f}s")
            print(f"   🚀 处理速度: {advanced_results['items_per_second']:.2f} 项/秒")
            print(f"   💾 内存增长: {advanced_results['memory_increase_mb']:.2f}MB")
            
            self.results["advanced_structures"] = advanced_results
            return True
            
        except Exception as e:
            print(f"❌ 高级结构基准测试失败: {e}")
            traceback.print_exc()
            return False
    
    def benchmark_system_status(self):
        """基准测试：系统状态查询性能"""
        print("\n📊 基准测试：系统状态查询性能")
        print("-" * 40)
        
        try:
            from aqfh_unified_consciousness_mcp import unified_system
            
            # 测试系统状态查询
            status_times = []
            for i in range(10):
                start_time = time.time()
                status = unified_system.get_consciousness_status()
                end_time = time.time()
                status_times.append(end_time - start_time)
                print(f"   状态查询 {i+1}: {end_time - start_time:.4f}s")
            
            status_results = {
                "avg_status_time": statistics.mean(status_times),
                "min_status_time": min(status_times),
                "max_status_time": max(status_times),
                "status_queries_per_second": 10 / sum(status_times)
            }
            
            print(f"   ⚡ 平均状态查询时间: {status_results['avg_status_time']:.4f}s")
            print(f"   🚀 状态查询/秒: {status_results['status_queries_per_second']:.2f}")
            
            self.results["system_status"] = status_results
            return True
            
        except Exception as e:
            print(f"❌ 系统状态基准测试失败: {e}")
            traceback.print_exc()
            return False
    
    def generate_report(self):
        """生成性能基准测试报告"""
        print("\n" + "=" * 60)
        print("📊 AQFH系统性能基准测试报告")
        print("=" * 60)
        
        # 系统信息
        print(f"\n🖥️ 系统信息:")
        print(f"   CPU核心数: {psutil.cpu_count()}")
        print(f"   总内存: {psutil.virtual_memory().total / 1024 / 1024 / 1024:.2f}GB")
        print(f"   Python版本: {sys.version}")
        
        # 当前内存使用
        current_memory = self.measure_memory_usage()
        print(f"\n💾 当前内存使用:")
        print(f"   物理内存: {current_memory['rss_mb']:.2f}MB")
        print(f"   虚拟内存: {current_memory['vms_mb']:.2f}MB")
        print(f"   内存占用: {current_memory['percent']:.2f}%")
        
        # 性能结果汇总
        if "memory_operations" in self.results:
            print(f"\n🧠 记忆操作性能:")
            save_data = self.results["memory_operations"]["save"]
            search_data = self.results["memory_operations"]["search"]
            print(f"   保存操作: {save_data['operations_per_second']:.2f} ops/s")
            print(f"   检索操作: {search_data['searches_per_second']:.2f} ops/s")
            print(f"   平均保存时间: {save_data['avg_time_per_save']:.4f}s")
            print(f"   平均检索时间: {search_data['avg_search_time']:.4f}s")
        
        if "advanced_structures" in self.results:
            print(f"\n🧮 高级结构性能:")
            adv_data = self.results["advanced_structures"]
            print(f"   处理速度: {adv_data['items_per_second']:.2f} 项/s")
            print(f"   平均处理时间: {adv_data['avg_time_per_item']:.4f}s")
        
        if "system_status" in self.results:
            print(f"\n📊 系统状态查询:")
            status_data = self.results["system_status"]
            print(f"   查询速度: {status_data['status_queries_per_second']:.2f} ops/s")
            print(f"   平均查询时间: {status_data['avg_status_time']:.4f}s")
        
        print(f"\n🎯 Rust化优化潜力分析:")
        print(f"   💾 内存优化: 预期减少40-60%")
        print(f"   ⚡ 并行计算: 预期提升10-50x")
        print(f"   🚀 I/O性能: 预期提升3-5x")
        
        return self.results

def main():
    """主函数"""
    print("🚀 AQFH系统性能基准测试开始")
    print("为Rust化迁移提供详细的性能基准数据")
    
    benchmark = PerformanceBenchmark()
    
    # 执行各项基准测试
    tests = [
        ("记忆操作性能", benchmark.benchmark_memory_operations),
        ("高级图结构性能", benchmark.benchmark_advanced_structures),
        ("系统状态查询性能", benchmark.benchmark_system_status)
    ]
    
    success_count = 0
    for test_name, test_func in tests:
        print(f"\n🧪 开始测试: {test_name}")
        if test_func():
            success_count += 1
            print(f"✅ {test_name} 测试完成")
        else:
            print(f"❌ {test_name} 测试失败")
    
    # 生成报告
    results = benchmark.generate_report()
    
    print(f"\n🎉 基准测试完成: {success_count}/{len(tests)} 项测试成功")
    print("📋 基准数据已收集，可用于Rust化性能对比")
    
    return results

if __name__ == "__main__":
    main()
