#!/usr/bin/env python3
"""
构建缺失的Rust模块并安装Python绑定
"""

import os
import subprocess
import sys
import shutil
from pathlib import Path

# 定义需要构建的模块
MODULES = [
    "nonlinear_interference",
    "fractal_routing",
    "game_scheduler",
    "fft_fusion",
    "transcendental_evolution",
    "differential_geometry",
    "resonance_network"
]

# 项目根目录
ROOT_DIR = Path(os.path.dirname(os.path.abspath(__file__)))
RUST_OPERATORS_DIR = ROOT_DIR / "rust_operators"

def check_dependencies():
    """检查必要的依赖是否已安装"""
    try:
        # 检查Rust工具链
        subprocess.run(["rustc", "--version"], check=True, capture_output=True)
        subprocess.run(["cargo", "--version"], check=True, capture_output=True)
        
        # 检查maturin
        subprocess.run(["maturin", "--version"], check=True, capture_output=True)
    except (subprocess.SubprocessError, FileNotFoundError):
        print("错误: 缺少必要的依赖。请确保已安装Rust工具链和maturin。")
        print("安装指南:")
        print("1. 安装Rust: https://www.rust-lang.org/tools/install")
        print("2. 安装maturin: pip install maturin")
        return False
    
    return True

def build_module(module_name):
    """构建指定的Rust模块"""
    module_dir = RUST_OPERATORS_DIR / module_name
    
    if not module_dir.exists():
        print(f"错误: 模块目录 {module_dir} 不存在")
        return False
    
    try:
        # 使用maturin构建并安装模块
        print(f"正在构建并安装 {module_name}...")
        result = subprocess.run(
            ["maturin", "develop", "--release"],
            cwd=str(module_dir),
            check=True,
            capture_output=True,
            text=True
        )
        print(result.stdout)
        print(f"{module_name} 构建并安装成功")
        return True
    except subprocess.SubprocessError as e:
        print(f"构建 {module_name} 失败: {e}")
        print(f"错误输出: {e.stderr if hasattr(e, 'stderr') else '无'}")
        return False

def main():
    """主函数"""
    if not check_dependencies():
        sys.exit(1)
    
    # 确保rust_operators目录存在
    os.makedirs(RUST_OPERATORS_DIR, exist_ok=True)
    
    # 构建所有模块
    success_count = 0
    for module in MODULES:
        if build_module(module):
            success_count += 1
    
    print(f"构建完成: {success_count}/{len(MODULES)} 个模块构建成功")
    
    if success_count == len(MODULES):
        print("所有模块构建成功，现在可以运行测试了")
        sys.exit(0)
    else:
        print("部分模块构建失败，请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
