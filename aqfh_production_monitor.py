#!/usr/bin/env python3
"""
AQFH生产级性能监控系统
实时监控、告警、分析和优化建议
"""

import sys
import time
import json
import threading
import queue
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import statistics

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

@dataclass
class PerformanceAlert:
    """性能告警"""
    alert_id: str
    timestamp: str
    severity: str  # critical, warning, info
    component: str
    metric: str
    current_value: float
    threshold: float
    message: str
    suggested_action: str

@dataclass
class SystemHealth:
    """系统健康状态"""
    overall_score: float  # 0-100
    component_scores: Dict[str, float]
    active_alerts: List[PerformanceAlert]
    performance_trends: Dict[str, str]  # improving, stable, degrading
    recommendations: List[str]

class AQFHProductionMonitor:
    """AQFH生产级监控系统"""
    
    def __init__(self):
        """初始化监控系统"""
        self.monitor_id = f"prod_monitor_{int(time.time())}"
        self.start_time = time.time()
        self.is_monitoring = False
        
        # 监控数据
        self.metrics_history = []
        self.alerts = []
        self.alert_queue = queue.Queue()
        
        # 监控配置
        self.thresholds = {
            "memory_usage_mb": {"warning": 800, "critical": 1200},
            "cpu_percent": {"warning": 80, "critical": 95},
            "response_time_ms": {"warning": 100, "critical": 500},
            "error_rate": {"warning": 0.05, "critical": 0.1},
            "cache_hit_ratio": {"warning": 0.7, "critical": 0.5},  # 低于阈值告警
            "disk_usage_percent": {"warning": 80, "critical": 90}
        }
        
        # 组件权重
        self.component_weights = {
            "memory_system": 0.3,
            "arrow_integration": 0.25,
            "distributed_system": 0.2,
            "rust_backend": 0.15,
            "storage_system": 0.1
        }
        
        print(f"📊 AQFH生产级监控系统初始化")
        print(f"监控ID: {self.monitor_id}")
    
    def collect_system_metrics(self) -> Dict[str, Any]:
        """收集系统指标"""
        try:
            # 模拟系统指标收集
            import random
            
            # 基础系统指标
            base_metrics = {
                "timestamp": datetime.now().isoformat(),
                "memory_usage_mb": 400 + random.uniform(-50, 100),
                "cpu_percent": 25 + random.uniform(-10, 30),
                "response_time_ms": 15 + random.uniform(-5, 20),
                "cache_hit_ratio": 0.85 + random.uniform(-0.1, 0.1),
                "disk_usage_percent": 45 + random.uniform(-5, 15),
                "error_rate": max(0, random.uniform(-0.01, 0.03))
            }
            
            # AQFH特定指标
            aqfh_metrics = {
                "memory_optimization_ratio": 0.6 + random.uniform(-0.05, 0.05),
                "arrow_throughput_ops": 250 + random.uniform(-30, 50),
                "distributed_nodes_active": 4,
                "rust_backend_available": random.choice([True, False]),
                "total_memories": 1000 + random.randint(-50, 100),
                "search_latency_ms": 8 + random.uniform(-2, 5)
            }
            
            # 组件健康状态
            component_health = {
                "memory_system": 85 + random.uniform(-10, 15),
                "arrow_integration": 90 + random.uniform(-5, 10),
                "distributed_system": 88 + random.uniform(-8, 12),
                "rust_backend": 95 if aqfh_metrics["rust_backend_available"] else 60,
                "storage_system": 92 + random.uniform(-7, 8)
            }
            
            return {
                **base_metrics,
                **aqfh_metrics,
                "component_health": component_health
            }
            
        except Exception as e:
            print(f"⚠️ 指标收集失败: {e}")
            return {"error": str(e), "timestamp": datetime.now().isoformat()}
    
    def analyze_metrics(self, metrics: Dict[str, Any]) -> List[PerformanceAlert]:
        """分析指标并生成告警"""
        alerts = []
        
        for metric_name, thresholds in self.thresholds.items():
            if metric_name in metrics:
                value = metrics[metric_name]
                
                # 检查告警条件
                severity = None
                if metric_name == "cache_hit_ratio":
                    # 缓存命中率：低于阈值告警
                    if value < thresholds["critical"]:
                        severity = "critical"
                    elif value < thresholds["warning"]:
                        severity = "warning"
                else:
                    # 其他指标：高于阈值告警
                    if value > thresholds["critical"]:
                        severity = "critical"
                    elif value > thresholds["warning"]:
                        severity = "warning"
                
                if severity:
                    alert = PerformanceAlert(
                        alert_id=f"alert_{int(time.time())}_{metric_name}",
                        timestamp=datetime.now().isoformat(),
                        severity=severity,
                        component=self._get_component_for_metric(metric_name),
                        metric=metric_name,
                        current_value=value,
                        threshold=thresholds[severity],
                        message=self._generate_alert_message(metric_name, value, severity),
                        suggested_action=self._get_suggested_action(metric_name, severity)
                    )
                    alerts.append(alert)
        
        return alerts
    
    def _get_component_for_metric(self, metric_name: str) -> str:
        """获取指标对应的组件"""
        component_mapping = {
            "memory_usage_mb": "memory_system",
            "cpu_percent": "system",
            "response_time_ms": "arrow_integration",
            "cache_hit_ratio": "memory_system",
            "disk_usage_percent": "storage_system",
            "error_rate": "system"
        }
        return component_mapping.get(metric_name, "unknown")
    
    def _generate_alert_message(self, metric_name: str, value: float, severity: str) -> str:
        """生成告警消息"""
        messages = {
            "memory_usage_mb": f"内存使用过高: {value:.1f}MB",
            "cpu_percent": f"CPU使用率过高: {value:.1f}%",
            "response_time_ms": f"响应时间过长: {value:.1f}ms",
            "cache_hit_ratio": f"缓存命中率过低: {value:.1%}",
            "disk_usage_percent": f"磁盘使用率过高: {value:.1f}%",
            "error_rate": f"错误率过高: {value:.1%}"
        }
        return messages.get(metric_name, f"{metric_name}异常: {value}")
    
    def _get_suggested_action(self, metric_name: str, severity: str) -> str:
        """获取建议操作"""
        actions = {
            "memory_usage_mb": {
                "warning": "检查内存泄漏，清理缓存",
                "critical": "立即重启服务，检查内存优化配置"
            },
            "cpu_percent": {
                "warning": "检查CPU密集型任务，考虑负载均衡",
                "critical": "立即扩容或重启服务"
            },
            "response_time_ms": {
                "warning": "检查Arrow集成配置，优化查询",
                "critical": "检查网络连接，重启相关服务"
            },
            "cache_hit_ratio": {
                "warning": "调整缓存策略，增加缓存大小",
                "critical": "检查缓存配置，可能需要重建缓存"
            },
            "disk_usage_percent": {
                "warning": "清理临时文件，归档旧数据",
                "critical": "立即扩容存储，删除不必要文件"
            },
            "error_rate": {
                "warning": "检查错误日志，修复已知问题",
                "critical": "立即检查系统状态，可能需要回滚"
            }
        }
        return actions.get(metric_name, {}).get(severity, "联系技术支持")
    
    def calculate_system_health(self, metrics: Dict[str, Any], alerts: List[PerformanceAlert]) -> SystemHealth:
        """计算系统健康状态"""
        # 计算组件分数
        component_scores = metrics.get("component_health", {})
        
        # 计算总体分数
        overall_score = 0
        for component, weight in self.component_weights.items():
            score = component_scores.get(component, 50)  # 默认50分
            overall_score += score * weight
        
        # 根据告警调整分数
        critical_alerts = [a for a in alerts if a.severity == "critical"]
        warning_alerts = [a for a in alerts if a.severity == "warning"]
        
        overall_score -= len(critical_alerts) * 10  # 每个严重告警扣10分
        overall_score -= len(warning_alerts) * 5    # 每个警告告警扣5分
        overall_score = max(0, min(100, overall_score))
        
        # 分析性能趋势
        trends = self._analyze_performance_trends()
        
        # 生成建议
        recommendations = self._generate_recommendations(metrics, alerts, overall_score)
        
        return SystemHealth(
            overall_score=overall_score,
            component_scores=component_scores,
            active_alerts=alerts,
            performance_trends=trends,
            recommendations=recommendations
        )
    
    def _analyze_performance_trends(self) -> Dict[str, str]:
        """分析性能趋势"""
        if len(self.metrics_history) < 5:
            return {"overall": "insufficient_data"}
        
        # 分析最近5个数据点的趋势
        recent_metrics = self.metrics_history[-5:]
        trends = {}
        
        key_metrics = ["memory_usage_mb", "response_time_ms", "cache_hit_ratio"]
        
        for metric in key_metrics:
            values = [m.get(metric, 0) for m in recent_metrics if metric in m]
            if len(values) >= 3:
                # 简单的趋势分析
                first_half = statistics.mean(values[:len(values)//2])
                second_half = statistics.mean(values[len(values)//2:])
                
                if metric == "cache_hit_ratio":
                    # 缓存命中率：增加是好的
                    if second_half > first_half * 1.05:
                        trends[metric] = "improving"
                    elif second_half < first_half * 0.95:
                        trends[metric] = "degrading"
                    else:
                        trends[metric] = "stable"
                else:
                    # 其他指标：减少是好的
                    if second_half < first_half * 0.95:
                        trends[metric] = "improving"
                    elif second_half > first_half * 1.05:
                        trends[metric] = "degrading"
                    else:
                        trends[metric] = "stable"
        
        return trends
    
    def _generate_recommendations(self, metrics: Dict[str, Any], alerts: List[PerformanceAlert], health_score: float) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 基于健康分数的建议
        if health_score < 60:
            recommendations.append("🚨 系统健康状况较差，建议立即进行全面检查")
        elif health_score < 80:
            recommendations.append("⚠️ 系统性能有待改善，建议优化配置")
        else:
            recommendations.append("✅ 系统运行良好，继续保持")
        
        # 基于具体指标的建议
        if metrics.get("memory_usage_mb", 0) > 600:
            recommendations.append("💾 考虑启用更激进的内存优化策略")
        
        if metrics.get("cache_hit_ratio", 1) < 0.8:
            recommendations.append("🎯 优化缓存策略，提高命中率")
        
        if not metrics.get("rust_backend_available", True):
            recommendations.append("🦀 Rust后端不可用，建议检查编译和部署")
        
        if metrics.get("arrow_throughput_ops", 0) < 200:
            recommendations.append("🏹 Arrow集成性能较低，检查配置和网络")
        
        # 基于告警的建议
        if any(a.severity == "critical" for a in alerts):
            recommendations.append("🔥 存在严重告警，需要立即处理")
        
        return recommendations
    
    def start_monitoring(self, interval: float = 10.0):
        """开始监控"""
        if self.is_monitoring:
            print("⚠️ 监控已在运行中")
            return
        
        print(f"🚀 开始生产级监控 (间隔: {interval}s)")
        self.is_monitoring = True
        
        # 启动监控线程
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop, 
            args=(interval,), 
            daemon=True
        )
        self.monitoring_thread.start()
        
        # 启动告警处理线程
        self.alert_thread = threading.Thread(
            target=self._alert_processing_loop, 
            daemon=True
        )
        self.alert_thread.start()
    
    def _monitoring_loop(self, interval: float):
        """监控循环"""
        while self.is_monitoring:
            try:
                # 收集指标
                metrics = self.collect_system_metrics()
                self.metrics_history.append(metrics)
                
                # 保持最近1000个数据点
                if len(self.metrics_history) > 1000:
                    self.metrics_history = self.metrics_history[-1000:]
                
                # 分析告警
                new_alerts = self.analyze_metrics(metrics)
                for alert in new_alerts:
                    self.alert_queue.put(alert)
                    self.alerts.append(alert)
                
                # 保持最近100个告警
                if len(self.alerts) > 100:
                    self.alerts = self.alerts[-100:]
                
                time.sleep(interval)
                
            except Exception as e:
                print(f"❌ 监控循环错误: {e}")
                time.sleep(interval)
    
    def _alert_processing_loop(self):
        """告警处理循环"""
        while self.is_monitoring:
            try:
                alert = self.alert_queue.get(timeout=1)
                self._process_alert(alert)
            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ 告警处理错误: {e}")
    
    def _process_alert(self, alert: PerformanceAlert):
        """处理告警"""
        severity_icons = {
            "critical": "🔥",
            "warning": "⚠️",
            "info": "ℹ️"
        }
        
        icon = severity_icons.get(alert.severity, "❓")
        print(f"{icon} [{alert.severity.upper()}] {alert.component}: {alert.message}")
        print(f"   建议操作: {alert.suggested_action}")
    
    def get_current_status(self) -> SystemHealth:
        """获取当前系统状态"""
        if not self.metrics_history:
            return SystemHealth(
                overall_score=0,
                component_scores={},
                active_alerts=[],
                performance_trends={},
                recommendations=["系统尚未开始监控"]
            )
        
        latest_metrics = self.metrics_history[-1]
        recent_alerts = [a for a in self.alerts if 
                        datetime.fromisoformat(a.timestamp) > 
                        datetime.now() - timedelta(minutes=5)]
        
        return self.calculate_system_health(latest_metrics, recent_alerts)
    
    def generate_monitoring_report(self) -> str:
        """生成监控报告"""
        status = self.get_current_status()
        
        report = []
        report.append("📊 AQFH生产级监控报告")
        report.append("=" * 50)
        report.append(f"监控时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"运行时长: {time.time() - self.start_time:.1f}秒")
        report.append(f"数据点数: {len(self.metrics_history)}")
        report.append("")
        
        # 系统健康状态
        health_icon = "🟢" if status.overall_score >= 80 else "🟡" if status.overall_score >= 60 else "🔴"
        report.append(f"🏥 系统健康: {health_icon} {status.overall_score:.1f}/100")
        report.append("")
        
        # 组件状态
        report.append("🔧 组件状态:")
        for component, score in status.component_scores.items():
            icon = "✅" if score >= 80 else "⚠️" if score >= 60 else "❌"
            report.append(f"   {icon} {component}: {score:.1f}/100")
        report.append("")
        
        # 活跃告警
        if status.active_alerts:
            report.append(f"🚨 活跃告警 ({len(status.active_alerts)}):")
            for alert in status.active_alerts[-5:]:  # 显示最近5个
                report.append(f"   [{alert.severity}] {alert.message}")
        else:
            report.append("✅ 无活跃告警")
        report.append("")
        
        # 性能趋势
        report.append("📈 性能趋势:")
        for metric, trend in status.performance_trends.items():
            trend_icon = "📈" if trend == "improving" else "📉" if trend == "degrading" else "➡️"
            report.append(f"   {trend_icon} {metric}: {trend}")
        report.append("")
        
        # 优化建议
        report.append("💡 优化建议:")
        for i, rec in enumerate(status.recommendations, 1):
            report.append(f"   {i}. {rec}")
        
        return "\n".join(report)
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            print("⚠️ 监控未在运行")
            return
        
        print("🛑 停止生产级监控")
        self.is_monitoring = False

def main():
    """主函数"""
    print("📊 AQFH生产级性能监控系统")
    print("实时监控、告警、分析和优化建议")
    print("=" * 60)
    
    # 创建监控系统
    monitor = AQFHProductionMonitor()
    
    # 开始监控
    monitor.start_monitoring(interval=5.0)
    
    try:
        # 运行监控一段时间
        print("\n🔄 监控运行中... (按Ctrl+C停止)")
        for i in range(12):  # 监控60秒
            time.sleep(5)
            if (i + 1) % 4 == 0:
                print(f"\n📊 监控报告 (第{(i + 1) * 5}秒):")
                print(monitor.generate_monitoring_report())
                print("\n" + "=" * 60)
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断监控")
    
    # 停止监控
    monitor.stop_monitoring()
    
    # 最终报告
    print("\n📋 最终监控报告:")
    print(monitor.generate_monitoring_report())
    
    return monitor

if __name__ == "__main__":
    monitor = main()
