{"input_data": {"features": {"text_length": 500, "sentiment_score": 0.75, "topic_relevance": 0.9, "user_engagement": 0.8, "readability": 0.65}, "prediction": "positive", "confidence": 0.85, "model_type": "classification", "domain": "sentiment_analysis", "context": {"user_id": "user_123", "session_id": "session_456", "timestamp": "2023-06-15T10:30:00Z", "device": "mobile", "language": "zh-CN"}}, "explanation": {"technical": {"trace": [{"step": "输入特征分析", "feature_user_engagement": "user_engagement: 0.8", "feature_sentiment_score": "sentiment_score: 0.75", "feature_topic_relevance": "topic_relevance: 0.9", "feature_readability": "readability: 0.65", "feature_text_length": "text_length: 500"}, {"step": "预测结果", "confidence": "置信度: 0.85", "prediction": "预测: \"positive\""}], "justification": "从技术角度来看，模型基于输入特征进行了预测。预测结果为 positive，这是基于特征的加权计算得出的。", "counterfactual": [{"original": "0.65", "counterfactual": "如果readability增加10%，预测结果可能会改变", "feature": "readability"}, {"counterfactual": "如果sentiment_score增加10%，预测结果可能会改变", "feature": "sentiment_score", "original": "0.75"}, {"feature": "text_length", "original": "500", "counterfactual": "如果text_length增加10%，预测结果可能会改变"}, {"feature": "topic_relevance", "original": "0.9", "counterfactual": "如果topic_relevance增加10%，预测结果可能会改变"}, {"original": "0.8", "feature": "user_engagement", "counterfactual": "如果user_engagement增加10%，预测结果可能会改变"}]}, "conceptual": {"trace": [{"step": "概念理解", "concept": "模型通过识别输入数据中的模式来做出决策"}, {"step": "预测结果解释", "explanation": "预测结果 \"positive\" 表示模型认为输入数据符合特定的模式"}], "justification": "从概念上讲，模型识别了输入数据中的模式，并将其与已知的模式进行比较。预测结果 positive 表示模型认为输入数据最符合该类别的模式。", "counterfactual": [{"explanation": "如果输入数据的模式发生变化，模型会识别不同的模式并给出不同的预测结果", "concept": "模式变化"}]}, "analogy": {"trace": [{"step": "类比理解", "analogy": "模型就像一个图书管理员，通过查看书的特征（如封面、标题、作者）来判断这本书属于哪个类别"}, {"step": "预测结果类比", "explanation": "预测结果 \"positive\" 就像图书管理员将一本书归类到特定的书架上"}], "justification": "想象模型是一个图书管理员，它通过查看书的特征来决定将书放在哪个书架上。在这个例子中，模型看到了输入数据的特征，并决定将其归类为 positive，就像将一本书放在特定的书架上。", "counterfactual": [{"analogy": "书的特征变化", "explanation": "如果这本书的封面或内容发生变化，图书管理员可能会将其归类到不同的书架上"}]}, "fused": {"trace": [{"step": "输入特征分析", "feature_user_engagement": "user_engagement: 0.8", "feature_sentiment_score": "sentiment_score: 0.75", "feature_topic_relevance": "topic_relevance: 0.9", "feature_readability": "readability: 0.65", "feature_text_length": "text_length: 500"}, {"step": "预测结果", "confidence": "置信度: 0.85", "prediction": "预测: \"positive\""}, {"step": "概念理解", "concept": "模型通过识别输入数据中的模式来做出决策"}, {"step": "预测结果解释", "explanation": "预测结果 \"positive\" 表示模型认为输入数据符合特定的模式"}], "justification": "【技术解释】从技术角度来看，模型基于输入特征进行了预测。预测结果为 positive，这是基于特征的加权计算得出的。 【概念解释】从概念上讲，模型识别了输入数据中的模式，并将其与已知的模式进行比较。预测结果 positive 表示模型认为输入数据最符合该类别的模式。 ", "counterfactual": [{"original": "0.65", "counterfactual": "如果readability增加10%，预测结果可能会改变", "feature": "readability"}, {"counterfactual": "如果sentiment_score增加10%，预测结果可能会改变", "feature": "sentiment_score", "original": "0.75"}, {"feature": "text_length", "original": "500", "counterfactual": "如果text_length增加10%，预测结果可能会改变"}, {"feature": "topic_relevance", "original": "0.9", "counterfactual": "如果topic_relevance增加10%，预测结果可能会改变"}, {"original": "0.8", "feature": "user_engagement", "counterfactual": "如果user_engagement增加10%，预测结果可能会改变"}, {"explanation": "如果输入数据的模式发生变化，模型会识别不同的模式并给出不同的预测结果", "concept": "模式变化"}]}, "weights": {"technical": 0.3731343283582089, "conceptual": 0.3731343283582089, "analogy": 0.2537313432835821}}, "verifiability": {"id": "eb6c2399-e87f-4590-9f47-d1cf7b1a4567", "name": "可验证性报告", "description": "使用hybrid方法生成的可验证性报告", "properties": [{"name": "一致性", "description": "模型在相似输入上产生相似输出", "property_type": "robustness", "expression": "forall x, y. |x - y| < epsilon -> |f(x) - f(y)| < delta", "importance": 0.8, "result": {"status": "failed", "score": 0.37697657823982933, "method": "hybrid", "details": {"property_name": "一致性", "expression": "forall x, y. |x - y| < epsilon -> |f(x) - f(y)| < delta", "verification_method": "hybrid", "adversarial_score": "0.9447797160438551", "property_type": "robustness", "empirical_score": "0.37120597537129607", "status": "Failed", "formal_score": "0.051037630912949994", "statistical_score": "0.32822716894304693", "score": "0.37697657823982933", "threshold": "0.7"}, "timestamp": "2025-05-01T15:04:25.673033933+00:00", "duration_ms": 8.427265, "counterexamples": [{"model_type": "classification", "context": {"device": "mobile", "language": "zh-CN", "session_id": "session_456", "timestamp": "2023-06-15T10:30:00Z", "user_id": "user_123"}, "features": {"readability": 0.65, "sentiment_score": 0.75, "text_length": 500, "topic_relevance": 0.9, "user_engagement": 0.8}, "domain": "sentiment_analysis", "prediction": "positive", "confidence": 0.85}, {"domain": "sentiment_analysis", "model_type": "classification", "context": {"device": "mobile", "language": "zh-CN", "session_id": "session_456", "timestamp": "2023-06-15T10:30:00Z", "user_id": "user_123"}, "features": {"readability": 0.65, "sentiment_score": 0.75, "text_length": 500, "topic_relevance": 0.9, "user_engagement": 0.8}, "confidence": 0.85, "prediction": "positive"}, {"model_type": "classification", "prediction": "positive", "domain": "sentiment_analysis", "confidence": 0.85, "context": {"device": "mobile", "language": "zh-CN", "session_id": "session_456", "timestamp": "2023-06-15T10:30:00Z", "user_id": "user_123"}, "features": {"readability": 0.65, "sentiment_score": 0.75, "text_length": 500, "topic_relevance": 0.9, "user_engagement": 0.8}}]}}, {"name": "单调性", "description": "情感分数增加时，积极预测概率不减少", "property_type": "monotonicity", "expression": "forall x, y. x.sentiment_score <= y.sentiment_score -> f(x) <= f(y)", "importance": 0.6, "result": {"status": "failed", "score": 0.3233782125974349, "method": "hybrid", "details": {"formal_score": "0.12401291950834248", "expression": "forall x, y. x.sentiment_score <= y.sentiment_score -> f(x) <= f(y)", "status": "Failed", "statistical_score": "0.4474843014390206", "threshold": "0.7", "empirical_score": "0.23243674995270813", "verification_method": "hybrid", "property_type": "monotonicity", "score": "0.3233782125974349", "adversarial_score": "0.5272084816134216", "property_name": "单调性"}, "timestamp": "2025-05-01T15:04:25.673363744+00:00", "duration_ms": 0.32401, "counterexamples": [{"context": {"device": "mobile", "language": "zh-CN", "session_id": "session_456", "timestamp": "2023-06-15T10:30:00Z", "user_id": "user_123"}, "prediction": "positive", "model_type": "classification", "confidence": 0.85, "features": {"readability": 0.65, "sentiment_score": 0.75, "text_length": 500, "topic_relevance": 0.9, "user_engagement": 0.8}, "domain": "sentiment_analysis"}, {"domain": "sentiment_analysis", "model_type": "classification", "features": {"readability": 0.65, "sentiment_score": 0.75, "text_length": 500, "topic_relevance": 0.9, "user_engagement": 0.8}, "context": {"device": "mobile", "language": "zh-CN", "session_id": "session_456", "timestamp": "2023-06-15T10:30:00Z", "user_id": "user_123"}, "prediction": "positive", "confidence": 0.85}, {"context": {"device": "mobile", "language": "zh-CN", "session_id": "session_456", "timestamp": "2023-06-15T10:30:00Z", "user_id": "user_123"}, "features": {"readability": 0.65, "sentiment_score": 0.75, "text_length": 500, "topic_relevance": 0.9, "user_engagement": 0.8}, "prediction": "positive", "confidence": 0.85, "model_type": "classification", "domain": "sentiment_analysis"}]}}], "overall_score": 0.3540058501073746, "method": "hybrid", "timestamp": "2025-05-01T15:04:25.673478591+00:00", "duration_ms": 8.894434, "metadata": {"threshold": "0.7", "operator": "VerifiabilityOperator", "timeout_ms": "30000", "version": "0.1.0"}}, "self_explainability": {"id": "ed94a776-4b87-4f4f-815e-62c42e4ff105", "timestamp": "2025-05-01T15:04:25.674025894+00:00", "explanation_type": "hybrid", "explanation_format": "mixed", "explanation_complexity": "adaptive", "summary": "最重要的特征是user_engagement（重要性：0.97）。如果改变user_engagement，预测结果将变为String(\"negative\")。找到了一个相似度为0.96的示例。最可靠的规则是：如果user_engagement < 0.86，则prediction = 'positive'", "feature_importances": [{"name": "user_engagement", "importance": 0.9707916041711712, "value": 0.8, "feature_type": "number", "description": "特征 user_engagement 的重要性", "unit": null, "range": null}, {"name": "readability", "importance": 0.6690585327046935, "value": 0.65, "feature_type": "number", "description": "特征 readability 的重要性", "unit": null, "range": null}, {"name": "sentiment_score", "importance": 0.6661636697785913, "value": 0.75, "feature_type": "number", "description": "特征 sentiment_score 的重要性", "unit": null, "range": null}, {"name": "text_length", "importance": 0.2566745867322884, "value": 500, "feature_type": "number", "description": "特征 text_length 的重要性", "unit": null, "range": null}, {"name": "topic_relevance", "importance": 0.08072695228628246, "value": 0.9, "feature_type": "number", "description": "特征 topic_relevance 的重要性", "unit": null, "range": null}], "counterfactuals": [{"original_features": {"user_engagement": 0.8, "text_length": 500, "readability": 0.65, "topic_relevance": 0.9, "sentiment_score": 0.75}, "counterfactual_features": {"user_engagement": 0.6153173892010306, "readability": 0.65, "text_length": 500, "sentiment_score": 0.75, "topic_relevance": 0.9}, "original_prediction": "positive", "counterfactual_prediction": "negative", "changed_features": ["user_engagement"], "changed_feature_importance": {"user_engagement": 0.921508701916418}, "distance": 0.7957543111901869, "feasibility": 0.19512058363352147}, {"original_features": {"readability": 0.65, "topic_relevance": 0.9, "user_engagement": 0.8, "text_length": 500, "sentiment_score": 0.75}, "counterfactual_features": {"readability": 0.7037270591535186, "topic_relevance": 0.9, "sentiment_score": 0.75, "text_length": 500, "user_engagement": 0.7354808250505473}, "original_prediction": "positive", "counterfactual_prediction": "negative", "changed_features": ["readability", "user_engagement"], "changed_feature_importance": {"user_engagement": 0.11722444883483363, "readability": 0.07471449318242007}, "distance": 0.08225126446637765, "feasibility": 0.595019914901382}, {"original_features": {"sentiment_score": 0.75, "user_engagement": 0.8, "readability": 0.65, "topic_relevance": 0.9, "text_length": 500}, "counterfactual_features": {"text_length": 500, "readability": 0.46357536513421077, "sentiment_score": 1.0644697502527878, "topic_relevance": 0.9, "user_engagement": 0.8}, "original_prediction": "positive", "counterfactual_prediction": "negative", "changed_features": ["readability", "sentiment_score"], "changed_feature_importance": {"readability": 0.7391781297204633, "sentiment_score": 0.61048975007888}, "distance": 0.4212768637808153, "feasibility": 0.6546672822402103}], "examples": [{"features": {"readability": 0.6572827184485813, "sentiment_score": 0.75, "user_engagement": 0.8, "text_length": 500, "topic_relevance": 0.9}, "prediction": "positive", "similarity": 0.9623039070045485, "weight": 0.021982042384973366, "id": "example_1", "description": "与输入数据相似的示例 1"}, {"features": {"text_length": 469.22210334950887, "readability": 0.65, "topic_relevance": 0.9, "sentiment_score": 0.75, "user_engagement": 0.8}, "prediction": "positive", "similarity": 0.8507475877567338, "weight": 0.5707752304758733, "id": "example_0", "description": "与输入数据相似的示例 0"}, {"features": {"topic_relevance": 0.9, "user_engagement": 0.8, "sentiment_score": 0.75, "readability": 0.65, "text_length": 500}, "prediction": "positive", "similarity": 0.7189132828708164, "weight": 0.15726590575983512, "id": "example_2", "description": "与输入数据相似的示例 2"}], "rules": [{"id": "rule_4", "conditions": ["user_engagement < 0.86"], "outcome": "prediction = 'positive'", "support": 0.8080219511002164, "confidence": 0.8922867443261141, "complexity": 1, "description": "规则 4"}, {"id": "rule_2", "conditions": ["topic_relevance < 0.94", "user_engagement < 0.82", "sentiment_score < 0.79"], "outcome": "prediction = 'positive'", "support": 0.9181184453738848, "confidence": 0.8910590975644772, "complexity": 3, "description": "规则 2"}, {"id": "rule_3", "conditions": ["user_engagement < 0.84", "sentiment_score > 0.70"], "outcome": "prediction = 'positive'", "support": 0.9152325432224597, "confidence": 0.8505307965221722, "complexity": 2, "description": "规则 3"}, {"id": "rule_0", "conditions": ["readability > 0.66", "sentiment_score > 0.81", "sentiment_score > 0.68"], "outcome": "prediction = 'positive'", "support": 0.662944689383864, "confidence": 0.8431225251349933, "complexity": 3, "description": "规则 0"}, {"id": "rule_1", "conditions": ["user_engagement > 0.74", "sentiment_score > 0.79"], "outcome": "prediction = 'positive'", "support": 0.8353139762986306, "confidence": 0.7382501099379983, "complexity": 2, "description": "规则 1"}], "confidence": 0.6743309441585401, "stability": 0.7938431257340289, "consistency": 0.8003717613693045, "metadata": {"operator": "SelfExplainabilityOperator", "execution_time_ms": "0.16362700000000002", "explanation_type": "Hybrid", "explanation_format": "Mixed", "version": "0.1.0", "explanation_complexity": "Adaptive"}}, "parallel_results": {"operator_0": {"technical": {"trace": [{"step": "输入特征分析", "feature_user_engagement": "user_engagement: 0.8", "feature_sentiment_score": "sentiment_score: 0.75", "feature_topic_relevance": "topic_relevance: 0.9", "feature_readability": "readability: 0.65", "feature_text_length": "text_length: 500"}, {"step": "预测结果", "confidence": "置信度: 0.85", "prediction": "预测: \"positive\""}], "justification": "从技术角度来看，模型基于输入特征进行了预测。预测结果为 positive，这是基于特征的加权计算得出的。", "counterfactual": [{"original": "0.65", "counterfactual": "如果readability增加10%，预测结果可能会改变", "feature": "readability"}, {"counterfactual": "如果sentiment_score增加10%，预测结果可能会改变", "feature": "sentiment_score", "original": "0.75"}, {"feature": "text_length", "original": "500", "counterfactual": "如果text_length增加10%，预测结果可能会改变"}, {"feature": "topic_relevance", "original": "0.9", "counterfactual": "如果topic_relevance增加10%，预测结果可能会改变"}, {"original": "0.8", "feature": "user_engagement", "counterfactual": "如果user_engagement增加10%，预测结果可能会改变"}]}, "conceptual": {"trace": [{"step": "概念理解", "concept": "模型通过识别输入数据中的模式来做出决策"}, {"step": "预测结果解释", "explanation": "预测结果 \"positive\" 表示模型认为输入数据符合特定的模式"}], "justification": "从概念上讲，模型识别了输入数据中的模式，并将其与已知的模式进行比较。预测结果 positive 表示模型认为输入数据最符合该类别的模式。", "counterfactual": [{"explanation": "如果输入数据的模式发生变化，模型会识别不同的模式并给出不同的预测结果", "concept": "模式变化"}]}, "analogy": {"trace": [{"step": "类比理解", "analogy": "模型就像一个图书管理员，通过查看书的特征（如封面、标题、作者）来判断这本书属于哪个类别"}, {"step": "预测结果类比", "explanation": "预测结果 \"positive\" 就像图书管理员将一本书归类到特定的书架上"}], "justification": "想象模型是一个图书管理员，它通过查看书的特征来决定将书放在哪个书架上。在这个例子中，模型看到了输入数据的特征，并决定将其归类为 positive，就像将一本书放在特定的书架上。", "counterfactual": [{"analogy": "书的特征变化", "explanation": "如果这本书的封面或内容发生变化，图书管理员可能会将其归类到不同的书架上"}]}, "fused": {"trace": [{"step": "输入特征分析", "feature_user_engagement": "user_engagement: 0.8", "feature_sentiment_score": "sentiment_score: 0.75", "feature_topic_relevance": "topic_relevance: 0.9", "feature_readability": "readability: 0.65", "feature_text_length": "text_length: 500"}, {"step": "预测结果", "confidence": "置信度: 0.85", "prediction": "预测: \"positive\""}, {"step": "概念理解", "concept": "模型通过识别输入数据中的模式来做出决策"}, {"step": "预测结果解释", "explanation": "预测结果 \"positive\" 表示模型认为输入数据符合特定的模式"}], "justification": "【技术解释】从技术角度来看，模型基于输入特征进行了预测。预测结果为 positive，这是基于特征的加权计算得出的。 【概念解释】从概念上讲，模型识别了输入数据中的模式，并将其与已知的模式进行比较。预测结果 positive 表示模型认为输入数据最符合该类别的模式。 ", "counterfactual": [{"original": "0.65", "counterfactual": "如果readability增加10%，预测结果可能会改变", "feature": "readability"}, {"counterfactual": "如果sentiment_score增加10%，预测结果可能会改变", "feature": "sentiment_score", "original": "0.75"}, {"feature": "text_length", "original": "500", "counterfactual": "如果text_length增加10%，预测结果可能会改变"}, {"feature": "topic_relevance", "original": "0.9", "counterfactual": "如果topic_relevance增加10%，预测结果可能会改变"}, {"original": "0.8", "feature": "user_engagement", "counterfactual": "如果user_engagement增加10%，预测结果可能会改变"}, {"explanation": "如果输入数据的模式发生变化，模型会识别不同的模式并给出不同的预测结果", "concept": "模式变化"}]}, "weights": {"technical": 0.3731343283582089, "conceptual": 0.3731343283582089, "analogy": 0.2537313432835821}}, "operator_1": {"id": "d73d51ac-44e5-4d47-a9b8-f2965fde6e53", "name": "可验证性报告", "description": "使用hybrid方法生成的可验证性报告", "properties": [{"name": "一致性", "description": "模型在相似输入上产生相似输出", "property_type": "robustness", "expression": "forall x, y. |x - y| < epsilon -> |f(x) - f(y)| < delta", "importance": 0.8, "result": {"status": "failed", "score": 0.28051923147669167, "method": "hybrid", "details": {"status": "Failed", "formal_score": "0.09559445405770695", "property_name": "一致性", "statistical_score": "0.6140379320031109", "verification_method": "hybrid", "score": "0.28051923147669167", "adversarial_score": "0.18222283103948278", "expression": "forall x, y. |x - y| < epsilon -> |f(x) - f(y)| < delta", "empirical_score": "0.15592474725274874", "property_type": "robustness", "threshold": "0.7"}, "timestamp": "2025-05-01T15:04:25.675279858+00:00", "duration_ms": 0.36884, "counterexamples": [{"features": {"readability": 0.65, "sentiment_score": 0.75, "text_length": 500, "topic_relevance": 0.9, "user_engagement": 0.8}, "model_type": "classification", "prediction": "positive", "confidence": 0.85, "domain": "sentiment_analysis", "context": {"device": "mobile", "language": "zh-CN", "session_id": "session_456", "timestamp": "2023-06-15T10:30:00Z", "user_id": "user_123"}}, {"confidence": 0.85, "features": {"readability": 0.65, "sentiment_score": 0.75, "text_length": 500, "topic_relevance": 0.9, "user_engagement": 0.8}, "model_type": "classification", "context": {"device": "mobile", "language": "zh-CN", "session_id": "session_456", "timestamp": "2023-06-15T10:30:00Z", "user_id": "user_123"}, "prediction": "positive", "domain": "sentiment_analysis"}, {"context": {"device": "mobile", "language": "zh-CN", "session_id": "session_456", "timestamp": "2023-06-15T10:30:00Z", "user_id": "user_123"}, "confidence": 0.85, "model_type": "classification", "prediction": "positive", "features": {"readability": 0.65, "sentiment_score": 0.75, "text_length": 500, "topic_relevance": 0.9, "user_engagement": 0.8}, "domain": "sentiment_analysis"}]}}, {"name": "单调性", "description": "情感分数增加时，积极预测概率不减少", "property_type": "monotonicity", "expression": "forall x, y. x.sentiment_score <= y.sentiment_score -> f(x) <= f(y)", "importance": 0.6, "result": {"status": "failed", "score": 0.4394274325855202, "method": "hybrid", "details": {"statistical_score": "0.7486661605612872", "status": "Failed", "expression": "forall x, y. x.sentiment_score <= y.sentiment_score -> f(x) <= f(y)", "threshold": "0.7", "verification_method": "hybrid", "property_name": "单调性", "score": "0.4394274325855202", "empirical_score": "0.23115206451412096", "property_type": "monotonicity", "adversarial_score": "0.6425642406691574", "formal_score": "0.13361441126826112"}, "timestamp": "2025-05-01T15:04:25.675553211+00:00", "duration_ms": 0.27068, "counterexamples": [{"features": {"readability": 0.65, "sentiment_score": 0.75, "text_length": 500, "topic_relevance": 0.9, "user_engagement": 0.8}, "context": {"device": "mobile", "language": "zh-CN", "session_id": "session_456", "timestamp": "2023-06-15T10:30:00Z", "user_id": "user_123"}, "prediction": "positive", "domain": "sentiment_analysis", "model_type": "classification", "confidence": 0.85}, {"context": {"device": "mobile", "language": "zh-CN", "session_id": "session_456", "timestamp": "2023-06-15T10:30:00Z", "user_id": "user_123"}, "model_type": "classification", "domain": "sentiment_analysis", "confidence": 0.85, "prediction": "positive", "features": {"readability": 0.65, "sentiment_score": 0.75, "text_length": 500, "topic_relevance": 0.9, "user_engagement": 0.8}}]}}], "overall_score": 0.3486227462376182, "method": "hybrid", "timestamp": "2025-05-01T15:04:25.675642611+00:00", "duration_ms": 0.769672, "metadata": {"timeout_ms": "30000", "operator": "VerifiabilityOperator", "threshold": "0.7", "version": "0.1.0"}}, "operator_2": {"id": "3843a487-421e-4d4e-a8a1-ccb9635aa009", "timestamp": "2025-05-01T15:04:25.675952948+00:00", "explanation_type": "hybrid", "explanation_format": "mixed", "explanation_complexity": "adaptive", "summary": "最重要的特征是text_length（重要性：0.91）。如果改变sentiment_score、topic_relevance，预测结果将变为String(\"negative\")。找到了一个相似度为0.78的示例。最可靠的规则是：如果sentiment_score > 0.78，则prediction = 'positive'", "feature_importances": [{"name": "text_length", "importance": 0.9093701567837108, "value": 500, "feature_type": "number", "description": "特征 text_length 的重要性", "unit": null, "range": null}, {"name": "user_engagement", "importance": 0.6034721801021112, "value": 0.8, "feature_type": "number", "description": "特征 user_engagement 的重要性", "unit": null, "range": null}, {"name": "topic_relevance", "importance": 0.5116546153754311, "value": 0.9, "feature_type": "number", "description": "特征 topic_relevance 的重要性", "unit": null, "range": null}, {"name": "sentiment_score", "importance": 0.42995726777323084, "value": 0.75, "feature_type": "number", "description": "特征 sentiment_score 的重要性", "unit": null, "range": null}, {"name": "readability", "importance": 0.22073354346735874, "value": 0.65, "feature_type": "number", "description": "特征 readability 的重要性", "unit": null, "range": null}], "counterfactuals": [{"original_features": {"user_engagement": 0.8, "sentiment_score": 0.75, "text_length": 500, "topic_relevance": 0.9, "readability": 0.65}, "counterfactual_features": {"sentiment_score": 0.7467887315942497, "text_length": 500, "topic_relevance": 1.3291615898877658, "readability": 0.65, "user_engagement": 0.8}, "original_prediction": "positive", "counterfactual_prediction": "negative", "changed_features": ["sentiment_score", "topic_relevance"], "changed_feature_importance": {"topic_relevance": 0.9349927670402594, "sentiment_score": 0.5513621752258442}, "distance": 0.33009829546539293, "feasibility": 0.5119513047668838}, {"original_features": {"text_length": 500, "readability": 0.65, "sentiment_score": 0.75, "topic_relevance": 0.9, "user_engagement": 0.8}, "counterfactual_features": {"user_engagement": 0.9185771726723957, "readability": 0.34611405176464705, "text_length": 500, "topic_relevance": 0.9, "sentiment_score": 0.75}, "original_prediction": "positive", "counterfactual_prediction": "negative", "changed_features": ["readability", "user_engagement"], "changed_feature_importance": {"user_engagement": 0.5834439691762363, "readability": 0.27822156351990524}, "distance": 0.8664266826854324, "feasibility": 0.3514747476070169}, {"original_features": {"readability": 0.65, "topic_relevance": 0.9, "user_engagement": 0.8, "text_length": 500, "sentiment_score": 0.75}, "counterfactual_features": {"sentiment_score": 0.480496586744497, "topic_relevance": 0.9, "user_engagement": 0.8, "readability": 0.65, "text_length": 500}, "original_prediction": "positive", "counterfactual_prediction": "negative", "changed_features": ["sentiment_score"], "changed_feature_importance": {"sentiment_score": 0.9916573558435511}, "distance": 0.5535943818481727, "feasibility": 0.9179818252727406}], "examples": [{"features": {"text_length": 500, "sentiment_score": 0.8442731085985036, "topic_relevance": 0.916402606865225, "readability": 0.7690716679953975, "user_engagement": 0.8}, "prediction": "positive", "similarity": 0.7811402936239169, "weight": 0.8091712663567824, "id": "example_0", "description": "与输入数据相似的示例 0"}, {"features": {"readability": 0.65, "sentiment_score": 0.75, "text_length": 500, "topic_relevance": 0.9, "user_engagement": 0.6929892785410505}, "prediction": "positive", "similarity": 0.7625852133758908, "weight": 0.2833918798330989, "id": "example_1", "description": "与输入数据相似的示例 1"}, {"features": {"readability": 0.65, "sentiment_score": 0.75, "topic_relevance": 0.9, "user_engagement": 0.8, "text_length": 500}, "prediction": "positive", "similarity": 0.7104798209200461, "weight": 0.020468250536277766, "id": "example_2", "description": "与输入数据相似的示例 2"}], "rules": [{"id": "rule_3", "conditions": ["sentiment_score > 0.78"], "outcome": "prediction = 'positive'", "support": 0.9324842885880162, "confidence": 0.9955420991719172, "complexity": 1, "description": "规则 3"}, {"id": "rule_0", "conditions": ["sentiment_score > 0.68"], "outcome": "prediction = 'positive'", "support": 0.6351559521462039, "confidence": 0.9411928681407421, "complexity": 1, "description": "规则 0"}, {"id": "rule_2", "conditions": ["topic_relevance < 0.84", "text_length < 532.46"], "outcome": "prediction = 'positive'", "support": 0.6376015057713955, "confidence": 0.8749628484971691, "complexity": 2, "description": "规则 2"}, {"id": "rule_1", "conditions": ["user_engagement < 0.88"], "outcome": "prediction = 'positive'", "support": 0.7011478690032771, "confidence": 0.8293622357914374, "complexity": 1, "description": "规则 1"}, {"id": "rule_4", "conditions": ["text_length > 534.11", "sentiment_score < 0.79", "sentiment_score < 0.79"], "outcome": "prediction = 'positive'", "support": 0.6159211561209725, "confidence": 0.8245386422560252, "complexity": 3, "description": "规则 4"}], "confidence": 0.6933404233318313, "stability": 0.8090514357516728, "consistency": 0.8664385497324759, "metadata": {"operator": "SelfExplainabilityOperator", "explanation_type": "Hybrid", "explanation_complexity": "Adaptive", "explanation_format": "Mixed", "execution_time_ms": "0.104391", "version": "0.1.0"}}}, "metadata": {"explanation_time": 0.00012373924255371094, "verifiability_time": 0.009286880493164062, "self_explainability_time": 0.000469207763671875, "parallel_time": 0.0016949176788330078, "total_time": 0.009879827499389648}}