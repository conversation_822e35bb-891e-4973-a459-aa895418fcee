"""
自解释与可验证性算子测试脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

try:
    # 尝试导入自解释算子
    from src.operators.explanation.multilevel_explanation import MultilevelExplanationOperator
    from src.operators.explanation.explanation_quality import ExplanationQualityOperator
    from src.operators.explanation.visualization import VisualizationExplanationOperator
    
    print("成功导入自解释算子！")
    
    # 尝试导入可验证性算子
    from src.operators.verification.multi_method_verification import MultiMethodVerificationOperator
    from src.operators.verification.consistency_verification import ConsistencyVerificationOperator
    from src.operators.verification.realtime_verification import RealtimeVerificationOperator
    
    print("成功导入可验证性算子！")
    
    # 创建算子实例
    multilevel_explanation = MultilevelExplanationOperator()
    explanation_quality = ExplanationQualityOperator()
    visualization = VisualizationExplanationOperator()
    multi_method_verification = MultiMethodVerificationOperator()
    consistency_verification = ConsistencyVerificationOperator()
    realtime_verification = RealtimeVerificationOperator()
    
    print("成功创建算子实例！")
    
    # 测试多层次解释生成算子
    test_data = {
        'state': {'variable1': 10, 'variable2': 20},
        'action': {'type': 'operation1', 'parameters': {'param1': 5}}
    }
    
    explanation = multilevel_explanation.apply(test_data)
    print("\n多层次解释生成算子测试结果:")
    print(f"- 技术层面解释: {explanation['technical']['justification']}")
    print(f"- 概念层面解释: {explanation['conceptual']['justification']}")
    print(f"- 类比层面解释: {explanation['analogy']['justification']}")
    print(f"- 融合解释: {explanation['fused']['justification']}")
    
    # 测试解释质量评估算子
    quality = explanation_quality.apply(explanation)
    print("\n解释质量评估算子测试结果:")
    print(f"- 完整性: {quality['completeness']:.2f}")
    print(f"- 一致性: {quality['consistency']:.2f}")
    print(f"- 可理解性: {quality['comprehensibility']:.2f}")
    print(f"- 总体质量: {quality['quality']:.2f}")
    
    print("\n所有测试通过！")
    
except ImportError as e:
    print(f"导入错误: {e}")
    print(f"Python路径: {sys.path}")
    
except Exception as e:
    print(f"测试失败: {e}")
