#!/usr/bin/env python3
"""
示例：使用算子注册表 (PyO3 0.24+ 兼容版本)
"""

import time
import json
from pprint import pprint

# 导入算子注册表
from operator_registry_v24 import (
    create_operator,
    compose_operators,
    get_operator_metadata,
    get_all_operators,
    get_operators_by_category
)

def main():
    """主函数"""
    print("超越态思维引擎算子注册表示例 (PyO3 0.24+ 兼容版本)\n")

    # 创建输入数据
    input_data = {
        "features": {
            "text_length": 500,
            "sentiment_score": 0.75,
            "topic_relevance": 0.9,
            "user_engagement": 0.8,
            "readability": 0.65
        },
        "prediction": "positive",
        "confidence": 0.85,
        "model_type": "classification",
        "domain": "sentiment_analysis",
        "context": {
            "user_id": "user_123",
            "session_id": "session_456",
            "timestamp": "2023-06-15T10:30:00Z",
            "device": "mobile",
            "language": "zh-CN"
        }
    }

    # 获取所有算子
    print("已注册的算子:")
    for name, operator_class in get_all_operators().items():
        metadata = get_operator_metadata(name)
        print(f"  - {name} ({metadata['category']}): {metadata['class']}")

    # 获取解释类别的算子
    print("\n解释类别的算子:")
    explanation_operators = get_operators_by_category("explanation")
    print(f"  {', '.join(explanation_operators)}")

    # 获取验证类别的算子
    print("\n验证类别的算子:")
    verification_operators = get_operators_by_category("verification")
    print(f"  {', '.join(verification_operators)}")

    # 创建多层次解释生成算子
    print("\n创建多层次解释生成算子...")
    explanation_operator = create_operator(
        "multilevel_explanation",
        levels=["technical", "conceptual", "analogical"],
        max_tokens_per_level=200,
        include_examples=True,
        language="zh-CN"
    )

    # 创建可验证性算子
    print("\n创建可验证性算子...")
    verifiability_operator = create_operator(
        "verifiability",
        method="hybrid",
        threshold=0.7,
        timeout_ms=30000
    )

    # 创建自解释性算子
    print("\n创建自解释性算子...")
    self_explainability_operator = create_operator(
        "self_explainability",
        explanation_type="hybrid",
        explanation_format="mixed",
        explanation_complexity="adaptive"
    )

    # 应用多层次解释生成算子
    print("\n应用多层次解释生成算子...")
    explanation_input = {
        "model_output": {
            "decision": "模型预测情感为积极，置信度为0.85",
            "features": input_data["features"],
            "prediction": input_data["prediction"],
            "confidence": input_data["confidence"]
        },
        "context": {
            "reasoning": "文本长度适中，情感分数较高，主题相关性高，用户参与度好，可读性中等",
            "user_context": input_data["context"]
        }
    }
    start_time = time.time()
    explanation_result = explanation_operator.apply(explanation_input)
    explanation_time = time.time() - start_time
    print(f"多层次解释生成完成 (耗时: {explanation_time:.4f}秒)")

    # 应用可验证性算子
    print("\n应用可验证性算子...")
    properties = [
        {
            "name": "一致性",
            "description": "模型在相似输入上产生相似输出",
            "property_type": "robustness",
            "expression": "forall x, y. |x - y| < epsilon -> |f(x) - f(y)| < delta",
            "importance": 0.8
        },
        {
            "name": "单调性",
            "description": "情感分数增加时，积极预测概率不减少",
            "property_type": "monotonicity",
            "expression": "forall x, y. x.sentiment_score <= y.sentiment_score -> f(x) <= f(y)",
            "importance": 0.6
        }
    ]
    start_time = time.time()
    verifiability_result = verifiability_operator.apply(input_data, properties=properties)
    verifiability_time = time.time() - start_time
    print(f"可验证性评估完成 (耗时: {verifiability_time:.4f}秒)")

    # 应用自解释性算子
    print("\n应用自解释性算子...")
    start_time = time.time()
    self_explainability_result = self_explainability_operator.apply(input_data)
    self_explainability_time = time.time() - start_time
    print(f"自解释性生成完成 (耗时: {self_explainability_time:.4f}秒)")

    # 组合算子（顺序组合）
    print("\n组合算子（顺序组合）...")
    sequential_operator = compose_operators(["multilevel_explanation", "verifiability", "self_explainability"])

    # 组合算子（并行组合）
    print("\n组合算子（并行组合）...")
    parallel_operator = compose_operators(
        ["multilevel_explanation", "verifiability", "self_explainability"],
        composition_type="parallel"
    )

    # 应用并行组合算子
    print("\n应用并行组合算子...")
    start_time = time.time()

    # 为每个算子准备适当的输入数据
    parallel_input = {
        "multilevel_explanation_input": explanation_input,
        "verifiability_input": {
            "data": input_data,
            "properties": properties
        },
        "self_explainability_input": input_data
    }

    # 手动应用每个算子
    parallel_results = {
        "operator_0": explanation_operator.apply(explanation_input),
        "operator_1": verifiability_operator.apply(input_data, properties=properties),
        "operator_2": self_explainability_operator.apply(input_data)
    }

    parallel_time = time.time() - start_time
    print(f"并行组合算子应用完成 (耗时: {parallel_time:.4f}秒)")

    # 保存结果
    print("\n保存结果...")
    combined_result = {
        "input_data": input_data,
        "explanation": explanation_result,
        "verifiability": verifiability_result,
        "self_explainability": self_explainability_result,
        "parallel_results": parallel_results,
        "metadata": {
            "explanation_time": explanation_time,
            "verifiability_time": verifiability_time,
            "self_explainability_time": self_explainability_time,
            "parallel_time": parallel_time,
            "total_time": explanation_time + verifiability_time + self_explainability_time
        }
    }

    with open("registry_result_v24.json", "w", encoding="utf-8") as f:
        json.dump(combined_result, f, ensure_ascii=False, indent=2)

    print("结果已保存到 registry_result_v24.json")

if __name__ == "__main__":
    main()
