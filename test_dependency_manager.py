#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
依赖管理模块测试脚本
"""

import os
import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

# 导入依赖管理模块
from src.utils.dependency_manager import (
    get_global_dependency_manager,
    import_module,
    register_fallback,
    register_mock,
    create_mock_class,
    create_mock_function,
    create_mock_module
)

def main():
    """主函数"""
    print("依赖管理模块测试")
    print("=" * 50)

    # 获取全局依赖管理器
    print("\n1. 获取全局依赖管理器")
    manager = get_global_dependency_manager()
    print(f"全局依赖管理器: {manager}")

    # 测试导入不存在的模块
    print("\n2. 测试导入不存在的模块")
    non_existent_module = import_module("non_existent_module")
    print(f"导入结果: {non_existent_module}")
    print(f"模块属性: {dir(non_existent_module)}")

    # 测试注册回退模块
    print("\n3. 测试注册回退模块")
    class FallbackModule:
        def __init__(self):
            self.name = "fallback_module"

        def fallback_function(self):
            return "This is a fallback function"

    register_fallback("fallback_test_module", FallbackModule())
    fallback_module = import_module("fallback_test_module")
    print(f"导入结果: {fallback_module}")
    print(f"模块名称: {fallback_module.name}")
    print(f"调用函数: {fallback_module.fallback_function()}")

    # 测试注册模拟工厂
    print("\n4. 测试注册模拟工厂")
    def mock_factory():
        class MockModule:
            def __init__(self):
                self.name = "mock_module"

            def mock_function(self):
                return "This is a mock function"

        return MockModule()

    register_mock("mock_test_module", mock_factory)
    mock_module = import_module("mock_test_module")
    print(f"导入结果: {mock_module}")
    print(f"模块名称: {mock_module.name}")
    print(f"调用函数: {mock_module.mock_function()}")

    # 测试创建模拟类
    print("\n5. 测试创建模拟类")
    MockClass = create_mock_class(
        "MockClass",
        methods=["method1", "method2"],
        attributes={"attribute1": "value1", "attribute2": 42}
    )
    mock_instance = MockClass()
    print(f"模拟类: {MockClass}")
    print(f"模拟实例: {mock_instance}")
    print(f"调用方法: {mock_instance.method1()}")
    print(f"属性1: {mock_instance.attribute1}")
    print(f"属性2: {mock_instance.attribute2}")

    # 测试创建模拟函数
    print("\n6. 测试创建模拟函数")
    mock_function = create_mock_function("mock_function", return_value="Mock function result")
    print(f"模拟函数: {mock_function}")
    print(f"调用函数: {mock_function()}")
    print(f"函数名称: {mock_function.__name__}")

    # 测试创建模拟模块
    print("\n7. 测试创建模拟模块")
    mock_module = create_mock_module(
        "mock_module",
        classes={
            "MockClass1": ["method1", "method2"],
            "MockClass2": ["method3", "method4"]
        },
        functions={
            "mock_function1": "Result 1",
            "mock_function2": "Result 2"
        },
        attributes={
            "VERSION": "1.0.0",
            "AUTHOR": "Test Author"
        }
    )
    print(f"模拟模块: {mock_module}")
    print(f"模块属性: {dir(mock_module)}")
    print(f"版本: {mock_module.VERSION}")
    print(f"作者: {mock_module.AUTHOR}")
    print(f"调用函数: {mock_module.mock_function1()}")

    # 测试模拟Rust模块
    print("\n8. 测试模拟Rust模块")
    rust_module = create_mock_module(
        "rust_operators",
        classes={
            "PyErrorHandlingOperator": ["handle_error", "get_metadata", "get_metrics", "get_complexity"],
            "PyTTError": ["from_exception", "get_message", "get_code"]
        },
        functions={
            "add": 0,
            "multiply": 0,
            "version_adapter": None
        },
        attributes={
            "__version__": "0.1.0",
            "RUST_AVAILABLE": True
        }
    )

    # 确保模块已经从sys.modules中移除
    if "rust_operators" in sys.modules:
        del sys.modules["rust_operators"]

    register_fallback("rust_operators", rust_module)

    # 导入模拟的Rust模块
    print("\n9. 导入模拟的Rust模块")
    rust_operators = import_module("rust_operators")
    print(f"导入结果: {rust_operators}")
    print(f"版本: {rust_operators.__version__}")
    print(f"Rust可用: {rust_operators.RUST_AVAILABLE}")
    print(f"模块属性: {dir(rust_operators)}")

    # 创建模拟的错误处理算子
    print("\n10. 创建模拟的错误处理算子")
    error_handler = rust_operators.PyErrorHandlingOperator()
    print(f"错误处理算子: {error_handler}")

    # 添加一个返回值给get_metadata方法
    if hasattr(error_handler, "get_metadata"):
        # 修改get_metadata方法
        def get_metadata():
            return {"name": "error_handler", "version": "1.0.0"}
        error_handler.get_metadata = get_metadata

    print(f"调用方法: {error_handler.get_metadata()}")

    print("\n测试完成！")

if __name__ == "__main__":
    main()
