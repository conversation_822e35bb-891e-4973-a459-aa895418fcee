#!/usr/bin/env python3
"""
AQFH快速性能测试工具
用于建立Python基准线和验证Rust集成效果
"""

import time
import sys
import statistics
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def test_python_performance():
    """测试Python实现性能"""
    print("🐍 测试Python实现性能...")
    
    try:
        from aqfh_unified_consciousness_mcp import unified_system
        
        # 测试数据
        test_memories = [
            f"🧪 Python性能测试记忆 {i} - 用于建立基准线的测试数据"
            for i in range(20)
        ]
        
        # 保存性能测试
        print("💾 测试保存性能...")
        save_times = []
        successful_saves = 0
        
        for memory in test_memories:
            start_time = time.time()
            try:
                result = unified_system.save_memory(
                    content=memory,
                    content_type="performance_test",
                    importance=0.5,
                    tags=["python_test"],
                    context={"test_type": "performance"}
                )
                end_time = time.time()
                if result:
                    save_times.append(end_time - start_time)
                    successful_saves += 1
            except Exception as e:
                print(f"   保存错误: {e}")
                break
        
        if save_times:
            avg_save_time = statistics.mean(save_times)
            total_save_time = sum(save_times)
            save_ops_per_sec = successful_saves / total_save_time if total_save_time > 0 else 0
            
            print(f"   ✅ 成功保存: {successful_saves}/{len(test_memories)}")
            print(f"   ⏱️ 平均保存时间: {avg_save_time:.4f}s")
            print(f"   🚀 保存速度: {save_ops_per_sec:.2f} ops/s")
        
        # 搜索性能测试
        print("\n🔍 测试搜索性能...")
        search_queries = ["Python性能测试", "测试数据", "基准线"]
        search_times = []
        
        for query in search_queries:
            start_time = time.time()
            try:
                results = unified_system.search_memories(query, limit=10)
                end_time = time.time()
                search_times.append(end_time - start_time)
                print(f"   查询'{query}': {len(results)}条结果, {end_time - start_time:.4f}s")
            except Exception as e:
                print(f"   搜索错误: {e}")
        
        if search_times:
            avg_search_time = statistics.mean(search_times)
            total_search_time = sum(search_times)
            search_ops_per_sec = len(search_queries) / total_search_time if total_search_time > 0 else 0
            
            print(f"   ⏱️ 平均搜索时间: {avg_search_time:.4f}s")
            print(f"   🚀 搜索速度: {search_ops_per_sec:.2f} ops/s")
        
        # 系统状态测试
        print("\n📊 测试系统状态查询...")
        status_times = []
        for i in range(3):
            start_time = time.time()
            try:
                status = unified_system.get_consciousness_status()
                end_time = time.time()
                status_times.append(end_time - start_time)
            except Exception as e:
                print(f"   状态查询错误: {e}")
        
        if status_times:
            avg_status_time = statistics.mean(status_times)
            print(f"   ⏱️ 平均状态查询时间: {avg_status_time:.4f}s")
        
        return {
            "save_performance": {
                "successful_saves": successful_saves,
                "avg_save_time": avg_save_time if save_times else 0,
                "save_ops_per_sec": save_ops_per_sec if save_times else 0
            },
            "search_performance": {
                "avg_search_time": avg_search_time if search_times else 0,
                "search_ops_per_sec": search_ops_per_sec if search_times else 0
            },
            "status_performance": {
                "avg_status_time": avg_status_time if status_times else 0
            }
        }
        
    except ImportError as e:
        print(f"❌ 无法导入Python系统: {e}")
        return None
    except Exception as e:
        print(f"❌ Python性能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_rust_performance():
    """测试Rust实现性能"""
    print("\n🚀 测试Rust实现性能...")
    
    try:
        # 尝试导入Rust模块
        import aqfh_rust_simple
        
        # 基础功能测试
        print("🔧 测试Rust基础功能...")
        hello_result = aqfh_rust_simple.rust_hello("AQFH")
        print(f"   {hello_result}")
        
        # 保存性能测试
        print("\n💾 测试Rust保存性能...")
        test_memories = [
            f"🚀 Rust性能测试记忆 {i} - 高性能存储测试"
            for i in range(20)
        ]
        
        save_times = []
        successful_saves = 0
        
        for memory in test_memories:
            start_time = time.time()
            try:
                result = aqfh_rust_simple.rust_save_memory(memory)
                end_time = time.time()
                if result:
                    save_times.append(end_time - start_time)
                    successful_saves += 1
            except Exception as e:
                print(f"   Rust保存错误: {e}")
                break
        
        if save_times:
            avg_save_time = statistics.mean(save_times)
            total_save_time = sum(save_times)
            save_ops_per_sec = successful_saves / total_save_time if total_save_time > 0 else 0
            
            print(f"   ✅ 成功保存: {successful_saves}/{len(test_memories)}")
            print(f"   ⏱️ 平均保存时间: {avg_save_time:.4f}s")
            print(f"   🚀 保存速度: {save_ops_per_sec:.2f} ops/s")
        
        # 搜索性能测试
        print("\n🔍 测试Rust搜索性能...")
        search_queries = ["Rust性能测试", "高性能存储", "测试"]
        search_times = []
        
        for query in search_queries:
            start_time = time.time()
            try:
                results = aqfh_rust_simple.rust_search_memories(query, 10)
                end_time = time.time()
                search_times.append(end_time - start_time)
                print(f"   查询'{query}': {len(results)}条结果, {end_time - start_time:.4f}s")
            except Exception as e:
                print(f"   Rust搜索错误: {e}")
        
        if search_times:
            avg_search_time = statistics.mean(search_times)
            total_search_time = sum(search_times)
            search_ops_per_sec = len(search_queries) / total_search_time if total_search_time > 0 else 0
            
            print(f"   ⏱️ 平均搜索时间: {avg_search_time:.4f}s")
            print(f"   🚀 搜索速度: {search_ops_per_sec:.2f} ops/s")
        
        return {
            "save_performance": {
                "successful_saves": successful_saves,
                "avg_save_time": avg_save_time if save_times else 0,
                "save_ops_per_sec": save_ops_per_sec if save_times else 0
            },
            "search_performance": {
                "avg_search_time": avg_search_time if search_times else 0,
                "search_ops_per_sec": search_ops_per_sec if search_times else 0
            }
        }
        
    except ImportError as e:
        print(f"⚠️ Rust模块未找到: {e}")
        print("   这是正常的，Rust模块可能还在编译中")
        return None
    except Exception as e:
        print(f"❌ Rust性能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def compare_performance(python_results, rust_results):
    """对比Python和Rust性能"""
    print("\n" + "="*60)
    print("📊 性能对比结果")
    print("="*60)
    
    if python_results:
        print("\n🐍 Python性能:")
        save_perf = python_results["save_performance"]
        search_perf = python_results["search_performance"]
        status_perf = python_results["status_performance"]
        
        print(f"   保存速度: {save_perf['save_ops_per_sec']:.2f} ops/s")
        print(f"   搜索速度: {search_perf['search_ops_per_sec']:.2f} ops/s")
        print(f"   状态查询: {status_perf['avg_status_time']:.4f}s")
    
    if rust_results:
        print("\n🚀 Rust性能:")
        save_perf = rust_results["save_performance"]
        search_perf = rust_results["search_performance"]
        
        print(f"   保存速度: {save_perf['save_ops_per_sec']:.2f} ops/s")
        print(f"   搜索速度: {search_perf['search_ops_per_sec']:.2f} ops/s")
    
    if python_results and rust_results:
        print("\n⚡ 性能提升:")
        py_save = python_results["save_performance"]["save_ops_per_sec"]
        rust_save = rust_results["save_performance"]["save_ops_per_sec"]
        py_search = python_results["search_performance"]["search_ops_per_sec"]
        rust_search = rust_results["search_performance"]["search_ops_per_sec"]
        
        if py_save > 0:
            save_speedup = rust_save / py_save
            print(f"   保存加速: {save_speedup:.2f}x")
        
        if py_search > 0:
            search_speedup = rust_search / py_search
            print(f"   搜索加速: {search_speedup:.2f}x")

def main():
    """主函数"""
    print("🧪 AQFH快速性能测试")
    print("建立Python基准线并验证Rust集成效果")
    print("="*60)
    
    # 测试Python性能
    python_results = test_python_performance()
    
    # 测试Rust性能
    rust_results = test_rust_performance()
    
    # 对比性能
    compare_performance(python_results, rust_results)
    
    print(f"\n✅ 快速性能测试完成")
    
    if not rust_results:
        print("\n💡 提示: Rust模块编译完成后，重新运行此测试查看性能提升")

if __name__ == "__main__":
    main()
