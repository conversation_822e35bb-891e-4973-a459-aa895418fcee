#!/usr/bin/env python3
"""
AQFH系统简化性能基准测试
为Rust化迁移提供基础性能数据
"""

import time
import sys
import statistics
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def simple_performance_benchmark():
    """简化的性能基准测试"""
    print("🚀 AQFH系统简化性能基准测试")
    print("为Rust化迁移提供基础性能数据")
    print("=" * 60)
    
    try:
        from aqfh_unified_consciousness_mcp import unified_system
        
        # 测试数据
        test_memories = [
            f"🧪 性能基准测试记忆 {i} - 这是用于测试AQFH记忆系统性能的测试数据，包含足够的文本内容来模拟真实使用场景。测试内容包括记忆保存、检索和高级图结构处理性能。"
            for i in range(50)
        ]
        
        print(f"\n📊 系统初始状态:")
        initial_status = unified_system.get_consciousness_status()
        initial_memories = initial_status.get('memory_palace', {}).get('total_memories', 0)
        print(f"   初始记忆数: {initial_memories}")
        print(f"   高级组件: {len([c for c in initial_status.get('components_active', {}).values() if c])}/6 活跃")
        
        # 1. 记忆保存性能测试
        print(f"\n💾 1. 记忆保存性能测试 (50条记忆):")
        save_times = []
        
        overall_start = time.time()
        for i, memory_content in enumerate(test_memories):
            save_start = time.time()
            memory_id = unified_system.save_memory(
                content=memory_content,
                content_type="performance_test",
                importance=0.6,
                tags=[f"perf_test_{i}", "benchmark"],
                context={"test_batch": "performance", "item_id": i}
            )
            save_end = time.time()
            save_times.append(save_end - save_start)
            
            if (i + 1) % 10 == 0:
                print(f"   已保存 {i + 1}/50 条记忆...")
        
        overall_end = time.time()
        
        save_stats = {
            "total_time": overall_end - overall_start,
            "avg_time": statistics.mean(save_times),
            "min_time": min(save_times),
            "max_time": max(save_times),
            "ops_per_second": len(test_memories) / (overall_end - overall_start)
        }
        
        print(f"   ✅ 总耗时: {save_stats['total_time']:.3f}s")
        print(f"   ⚡ 平均保存时间: {save_stats['avg_time']:.4f}s")
        print(f"   🚀 保存速度: {save_stats['ops_per_second']:.2f} ops/s")
        print(f"   📈 时间范围: {save_stats['min_time']:.4f}s - {save_stats['max_time']:.4f}s")
        
        # 2. 记忆检索性能测试
        print(f"\n🔍 2. 记忆检索性能测试:")
        search_queries = ["性能基准", "测试数据", "记忆系统", "AQFH", "benchmark"]
        search_times = []
        
        for query in search_queries:
            search_start = time.time()
            results = unified_system.search_memories(query, limit=10)
            search_end = time.time()
            search_times.append(search_end - search_start)
            print(f"   查询'{query}': {len(results)}条结果, {search_end - search_start:.4f}s")
        
        search_stats = {
            "avg_time": statistics.mean(search_times),
            "min_time": min(search_times),
            "max_time": max(search_times),
            "ops_per_second": len(search_queries) / sum(search_times)
        }
        
        print(f"   ⚡ 平均检索时间: {search_stats['avg_time']:.4f}s")
        print(f"   🚀 检索速度: {search_stats['ops_per_second']:.2f} ops/s")
        
        # 3. 复杂查询性能测试
        print(f"\n🧮 3. 复杂查询性能测试:")
        complex_queries = [
            "性能基准 测试数据",
            "AQFH 记忆系统 benchmark",
            "高级图结构 纤维丛 拓扑"
        ]
        
        complex_times = []
        for query in complex_queries:
            complex_start = time.time()
            results = unified_system.recall_memories(query, limit=20)
            complex_end = time.time()
            complex_times.append(complex_end - complex_start)
            print(f"   复杂查询'{query[:20]}...': {len(results)}条结果, {complex_end - complex_start:.4f}s")
        
        complex_stats = {
            "avg_time": statistics.mean(complex_times),
            "ops_per_second": len(complex_queries) / sum(complex_times)
        }
        
        print(f"   ⚡ 平均复杂查询时间: {complex_stats['avg_time']:.4f}s")
        print(f"   🚀 复杂查询速度: {complex_stats['ops_per_second']:.2f} ops/s")
        
        # 4. 系统状态查询性能
        print(f"\n📊 4. 系统状态查询性能:")
        status_times = []
        for i in range(5):
            status_start = time.time()
            status = unified_system.get_consciousness_status()
            status_end = time.time()
            status_times.append(status_end - status_start)
        
        status_stats = {
            "avg_time": statistics.mean(status_times),
            "ops_per_second": 5 / sum(status_times)
        }
        
        print(f"   ⚡ 平均状态查询时间: {status_stats['avg_time']:.4f}s")
        print(f"   🚀 状态查询速度: {status_stats['ops_per_second']:.2f} ops/s")
        
        # 5. 最终状态检查
        print(f"\n📈 5. 最终状态检查:")
        final_status = unified_system.get_consciousness_status()
        final_memories = final_status.get('memory_palace', {}).get('total_memories', 0)
        memory_increase = final_memories - initial_memories
        
        print(f"   初始记忆数: {initial_memories}")
        print(f"   最终记忆数: {final_memories}")
        print(f"   记忆增量: +{memory_increase}")
        print(f"   预期增量: +{len(test_memories)}")
        print(f"   增量匹配: {'✅' if memory_increase == len(test_memories) else '❌'}")
        
        # 生成性能报告
        print(f"\n" + "=" * 60)
        print(f"📊 AQFH系统性能基准报告 (Python原型)")
        print(f"=" * 60)
        
        print(f"\n🧠 记忆操作性能:")
        print(f"   保存速度: {save_stats['ops_per_second']:.2f} ops/s")
        print(f"   检索速度: {search_stats['ops_per_second']:.2f} ops/s")
        print(f"   复杂查询速度: {complex_stats['ops_per_second']:.2f} ops/s")
        print(f"   状态查询速度: {status_stats['ops_per_second']:.2f} ops/s")
        
        print(f"\n⏱️ 响应时间:")
        print(f"   平均保存时间: {save_stats['avg_time']:.4f}s")
        print(f"   平均检索时间: {search_stats['avg_time']:.4f}s")
        print(f"   平均复杂查询时间: {complex_stats['avg_time']:.4f}s")
        print(f"   平均状态查询时间: {status_stats['avg_time']:.4f}s")
        
        print(f"\n🎯 Rust化优化潜力:")
        print(f"   💾 内存优化: 预期减少40-60%")
        print(f"   ⚡ 并行计算: 预期提升10-50x (高级图结构)")
        print(f"   🚀 I/O性能: 预期提升3-5x (Arrow原生)")
        print(f"   🔄 响应时间: 预期减少50-80%")
        
        print(f"\n📋 基准数据已建立，可用于Rust化性能对比")
        
        return {
            "save": save_stats,
            "search": search_stats,
            "complex": complex_stats,
            "status": status_stats,
            "memory_change": memory_increase
        }
        
    except Exception as e:
        print(f"❌ 性能基准测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    results = simple_performance_benchmark()
    if results:
        print("\n✅ 性能基准测试完成")
    else:
        print("\n❌ 性能基准测试失败")
