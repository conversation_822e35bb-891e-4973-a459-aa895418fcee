#!/usr/bin/env python3
"""
构建多层次解释生成算子的Rust实现
"""

import os
import subprocess
import sys
import platform

def main():
    """主函数"""
    # 获取当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # 获取Rust源代码目录
    rust_dir = os.path.join(current_dir, "src", "operators", "explanation", "src")

    # 检查Rust源代码目录是否存在
    if not os.path.exists(rust_dir):
        print(f"错误：Rust源代码目录 {rust_dir} 不存在")
        return 1

    # 检查Cargo.toml是否存在
    cargo_toml = os.path.join(rust_dir, "Cargo.toml")
    if not os.path.exists(cargo_toml):
        print(f"错误：Cargo.toml文件 {cargo_toml} 不存在")
        return 1

    # 检查是否安装了Rust工具链
    try:
        subprocess.run(["rustc", "--version"], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    except (subprocess.SubprocessError, FileNotFoundError):
        print("错误：未安装Rust工具链，请先安装Rust工具链")
        print("可以通过以下命令安装Rust工具链：")
        print("curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh")
        return 1

    # 检查是否安装了maturin
    try:
        subprocess.run(["maturin", "--version"], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    except (subprocess.SubprocessError, FileNotFoundError):
        print("错误：未安装maturin，请先安装maturin")
        print("可以通过以下命令安装maturin：")
        print("pip install maturin")
        return 1

    # 构建Rust实现
    print("正在构建自解释算子的Rust实现...")

    # 切换到Rust源代码目录
    os.chdir(rust_dir)

    # 构建Rust实现
    try:
        subprocess.run(["maturin", "develop", "--release"], check=True)
    except subprocess.SubprocessError as e:
        print(f"错误：构建Rust实现失败：{e}")
        return 1

    print("构建成功！")

    # 测试Rust实现
    print("正在测试自解释算子的Rust实现...")

    # 切换回项目根目录
    os.chdir(current_dir)

    # 运行多层次解释生成算子测试
    print("正在测试多层次解释生成算子...")
    try:
        subprocess.run([sys.executable, "-m", "unittest", "tests.test_multilevel_explanation"], check=True)
    except subprocess.SubprocessError as e:
        print(f"错误：测试多层次解释生成算子失败：{e}")
        return 1

    # 运行解释质量评估算子测试
    print("正在测试解释质量评估算子...")
    try:
        subprocess.run([sys.executable, "-m", "unittest", "tests.test_explanation_quality"], check=True)
    except subprocess.SubprocessError as e:
        print(f"错误：测试解释质量评估算子失败：{e}")
        return 1

    # 运行可视化解释算子测试
    print("正在测试可视化解释算子...")
    try:
        subprocess.run([sys.executable, "-m", "unittest", "tests.test_visualization"], check=True)
    except subprocess.SubprocessError as e:
        print(f"错误：测试可视化解释算子失败：{e}")
        return 1

    # 运行优化的多层次解释生成算子测试
    print("正在测试优化的多层次解释生成算子...")
    try:
        subprocess.run([sys.executable, "tests/test_optimized_multilevel_explanation.py"], check=True)
    except subprocess.SubprocessError as e:
        print(f"错误：测试优化的多层次解释生成算子失败：{e}")
        return 1

    print("测试成功！")

    return 0


if __name__ == "__main__":
    sys.exit(main())
