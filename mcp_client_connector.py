#!/usr/bin/env python3
"""
MCP客户端连接器 - 连接到AQFH MCP服务器并提供工具访问
这个模块的目标是让AI助手可以直接访问MCP工具
"""

import asyncio
import subprocess
import json
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

class MCPClientConnector:
    """MCP客户端连接器"""
    
    def __init__(self, server_script: str = "aqfh_unified_consciousness_mcp.py"):
        """初始化MCP客户端连接器"""
        self.server_script = server_script
        self.server_process = None
        self.connected = False
        
    async def start_server(self):
        """启动MCP服务器"""
        try:
            self.server_process = await asyncio.create_subprocess_exec(
                sys.executable, self.server_script,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            print("🚀 MCP服务器已启动")
            return True
        except Exception as e:
            print(f"❌ 启动MCP服务器失败: {e}")
            return False
    
    async def send_request(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """发送MCP请求"""
        if not self.server_process:
            return {"error": "MCP服务器未启动"}
            
        request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": method,
            "params": params or {}
        }
        
        try:
            request_json = json.dumps(request) + "\n"
            self.server_process.stdin.write(request_json.encode())
            await self.server_process.stdin.drain()
            
            # 读取响应
            response_line = await self.server_process.stdout.readline()
            if response_line:
                response = json.loads(response_line.decode())
                return response
            else:
                return {"error": "无响应"}
                
        except Exception as e:
            return {"error": f"请求失败: {e}"}
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any] = None) -> str:
        """调用MCP工具"""
        response = await self.send_request("tools/call", {
            "name": tool_name,
            "arguments": arguments or {}
        })
        
        if "error" in response:
            return f"❌ 工具调用失败: {response['error']}"
        
        result = response.get("result", {})
        return result.get("content", [{}])[0].get("text", "无结果")
    
    async def list_tools(self) -> List[str]:
        """列出可用工具"""
        response = await self.send_request("tools/list")
        
        if "error" in response:
            return []
        
        tools = response.get("result", {}).get("tools", [])
        return [tool.get("name", "") for tool in tools]
    
    def stop_server(self):
        """停止MCP服务器"""
        if self.server_process:
            self.server_process.terminate()
            print("🛑 MCP服务器已停止")

# 创建全局MCP连接器
mcp_connector = MCPClientConnector()

async def init_mcp_connection():
    """初始化MCP连接"""
    print("🔧 初始化MCP连接...")
    
    success = await mcp_connector.start_server()
    if success:
        # 等待服务器启动
        await asyncio.sleep(2)
        
        # 列出可用工具
        tools = await mcp_connector.list_tools()
        print(f"✅ MCP连接成功，可用工具: {tools}")
        mcp_connector.connected = True
        return True
    else:
        print("❌ MCP连接失败")
        return False

# 便捷函数
async def save_memory_mcp(content: str, importance: float = 0.7, 
                         content_type: str = "conversation", 
                         tags: List[str] = None, 
                         context: Dict[str, Any] = None) -> str:
    """通过MCP保存记忆"""
    if not mcp_connector.connected:
        await init_mcp_connection()
    
    return await mcp_connector.call_tool("memory_save_tool_aqfh_consciousness", {
        "content": content,
        "importance": importance,
        "content_type": content_type,
        "tags": tags or [],
        "context": context or {}
    })

async def recall_memory_mcp(query: str, limit: int = 10, 
                           importance_threshold: float = 0.0) -> str:
    """通过MCP回忆记忆"""
    if not mcp_connector.connected:
        await init_mcp_connection()
    
    return await mcp_connector.call_tool("memory_recall_tool_aqfh_consciousness", {
        "query": query,
        "limit": limit,
        "importance_threshold": importance_threshold
    })

async def get_consciousness_status_mcp() -> str:
    """通过MCP获取意识状态"""
    if not mcp_connector.connected:
        await init_mcp_connection()
    
    return await mcp_connector.call_tool("consciousness_status_tool_aqfh_consciousness")

async def awaken_consciousness_mcp(awakening_type: str = "session_start", 
                                  topic: str = None, user: str = None) -> str:
    """通过MCP觉醒意识"""
    if not mcp_connector.connected:
        await init_mcp_connection()
    
    return await mcp_connector.call_tool("consciousness_awaken_tool_aqfh_consciousness", {
        "awakening_type": awakening_type,
        "topic": topic,
        "user": user
    })

# 同步包装函数
def save_memory_sync(content: str, importance: float = 0.7) -> str:
    """同步保存记忆"""
    return asyncio.run(save_memory_mcp(content, importance))

def recall_memory_sync(query: str, limit: int = 5) -> str:
    """同步回忆记忆"""
    return asyncio.run(recall_memory_mcp(query, limit))

def get_status_sync() -> str:
    """同步获取状态"""
    return asyncio.run(get_consciousness_status_mcp())

def awaken_sync(topic: str = None) -> str:
    """同步觉醒意识"""
    return asyncio.run(awaken_consciousness_mcp(topic=topic))

if __name__ == "__main__":
    # 测试MCP连接
    async def test_mcp():
        print("🧪 测试MCP客户端连接...")
        
        # 初始化连接
        success = await init_mcp_connection()
        if not success:
            print("❌ MCP连接测试失败")
            return
        
        # 测试保存记忆
        print("\n💾 测试保存记忆:")
        result = await save_memory_mcp("🧪 MCP客户端测试记忆", 0.8)
        print(result)
        
        # 测试回忆记忆
        print("\n🔍 测试回忆记忆:")
        result = await recall_memory_mcp("MCP客户端", 3)
        print(result)
        
        # 测试状态查询
        print("\n📊 测试状态查询:")
        result = await get_consciousness_status_mcp()
        print(result)
        
        # 停止服务器
        mcp_connector.stop_server()
        print("\n✅ MCP客户端测试完成")
    
    asyncio.run(test_mcp())
