#!/usr/bin/env python3
"""
AQFH高级图结构功能深度测试
测试纤维丛、拓扑分析、分形组织、量子语义等先进特性的实际使用效果
"""

import sys
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Any

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_fiber_bundle_cognitive_patterns():
    """测试纤维丛认知模式存储和检索"""
    print("🧪 测试1: 纤维丛认知模式")
    
    try:
        from aqfh_unified_consciousness_mcp import unified_system
        
        # 获取纤维丛空间
        fiber_bundle = unified_system.memory_palace.fiber_bundle_space
        
        # 测试不同类型的认知模式
        cognitive_patterns = {
            "logical_reasoning": np.array([0.9, 0.1, 0.2, 0.8, 0.7, 0.3, 0.6, 0.4]),
            "creative_thinking": np.array([0.3, 0.9, 0.8, 0.2, 0.6, 0.7, 0.4, 0.9]),
            "emotional_processing": np.array([0.5, 0.6, 0.9, 0.4, 0.3, 0.8, 0.7, 0.2]),
            "pattern_recognition": np.array([0.8, 0.4, 0.3, 0.9, 0.5, 0.2, 0.7, 0.6]),
            "memory_consolidation": np.array([0.6, 0.7, 0.4, 0.5, 0.9, 0.3, 0.2, 0.8])
        }
        
        # 存储认知模式到纤维丛
        stored_patterns = {}
        for pattern_name, pattern_data in cognitive_patterns.items():
            success = fiber_bundle.store_pattern(pattern_name, pattern_data)
            stored_patterns[pattern_name] = success
            print(f"   {'✅' if success else '❌'} {pattern_name}: {pattern_data[:4]}...")
        
        # 测试纤维丛的连接和平行传输
        print(f"   📊 纤维丛统计:")
        print(f"      - 基底维度: {fiber_bundle.base_manifold_dim}")
        print(f"      - 纤维维度: {fiber_bundle.fiber_space_dim}")
        print(f"      - 存储的连接: {len(fiber_bundle.connections)}")
        print(f"      - 截面数量: {len(fiber_bundle.sections)}")
        
        success_rate = sum(stored_patterns.values()) / len(stored_patterns)
        print(f"   🎯 认知模式存储成功率: {success_rate:.1%}")
        
        return success_rate > 0.8
        
    except Exception as e:
        print(f"   ❌ 纤维丛测试失败: {e}")
        return False

def test_topological_emotional_analysis():
    """测试拓扑情感分析功能"""
    print("\n🧪 测试2: 拓扑情感分析")
    
    try:
        from aqfh_unified_consciousness_mcp import unified_system
        
        # 获取拓扑分析器
        topo_analyzer = unified_system.memory_palace.topological_analyzer
        
        # 测试不同的情感拓扑结构
        emotional_topologies = {
            "joy_excitement": {
                "emotional_valence": 0.8,
                "arousal_level": 0.9,
                "emotional_complexity": 0.4
            },
            "calm_contentment": {
                "emotional_valence": 0.6,
                "arousal_level": 0.2,
                "emotional_complexity": 0.3
            },
            "anxious_worry": {
                "emotional_valence": -0.4,
                "arousal_level": 0.8,
                "emotional_complexity": 0.7
            },
            "deep_sadness": {
                "emotional_valence": -0.7,
                "arousal_level": 0.3,
                "emotional_complexity": 0.6
            },
            "mixed_emotions": {
                "emotional_valence": 0.1,
                "arousal_level": 0.6,
                "emotional_complexity": 0.9
            }
        }
        
        # 存储情感拓扑
        stored_topologies = {}
        for emotion_name, emotion_data in emotional_topologies.items():
            success = topo_analyzer.store_topology(emotion_name, emotion_data)
            stored_topologies[emotion_name] = success
            valence = emotion_data["emotional_valence"]
            arousal = emotion_data["arousal_level"]
            complexity = emotion_data["emotional_complexity"]
            print(f"   {'✅' if success else '❌'} {emotion_name}: V={valence:.1f}, A={arousal:.1f}, C={complexity:.1f}")
        
        # 测试拓扑分析器的高级功能
        print(f"   📊 拓扑分析器统计:")
        print(f"      - 最大维度: {topo_analyzer.max_dimension}")
        print(f"      - 单纯复形数: {len(topo_analyzer.simplicial_complexes)}")
        print(f"      - 持久图数: {len(topo_analyzer.persistence_diagrams)}")
        print(f"      - Betti数计算: {len(topo_analyzer.betti_numbers)}")
        
        success_rate = sum(stored_topologies.values()) / len(stored_topologies)
        print(f"   🎯 情感拓扑存储成功率: {success_rate:.1%}")
        
        return success_rate > 0.8
        
    except Exception as e:
        print(f"   ❌ 拓扑分析测试失败: {e}")
        return False

def test_fractal_creative_organization():
    """测试分形创造性组织功能"""
    print("\n🧪 测试3: 分形创造性组织")
    
    try:
        from aqfh_unified_consciousness_mcp import unified_system
        
        # 获取分形组织器
        fractal_organizer = unified_system.memory_palace.fractal_organizer
        
        # 测试不同的创造性分形
        creative_fractals = {
            "artistic_inspiration": {
                "creativity_level": 0.9,
                "innovation_potential": 0.8,
                "pattern_complexity": 0.7
            },
            "scientific_breakthrough": {
                "creativity_level": 0.8,
                "innovation_potential": 0.9,
                "pattern_complexity": 0.8
            },
            "philosophical_insight": {
                "creativity_level": 0.7,
                "innovation_potential": 0.6,
                "pattern_complexity": 0.9
            },
            "technical_solution": {
                "creativity_level": 0.6,
                "innovation_potential": 0.7,
                "pattern_complexity": 0.6
            },
            "interdisciplinary_fusion": {
                "creativity_level": 0.8,
                "innovation_potential": 0.8,
                "pattern_complexity": 0.8
            }
        }
        
        # 存储创造性分形
        stored_fractals = {}
        for fractal_name, fractal_data in creative_fractals.items():
            success = fractal_organizer.store_fractal(fractal_name, fractal_data)
            stored_fractals[fractal_name] = success
            creativity = fractal_data["creativity_level"]
            innovation = fractal_data["innovation_potential"]
            complexity = fractal_data["pattern_complexity"]
            print(f"   {'✅' if success else '❌'} {fractal_name}: C={creativity:.1f}, I={innovation:.1f}, P={complexity:.1f}")
        
        # 测试分形组织器的层次结构
        print(f"   📊 分形组织器统计:")
        print(f"      - 最大深度: {fractal_organizer.max_depth}")
        print(f"      - 分支因子: {fractal_organizer.branching_factor}")
        print(f"      - 分形树节点: {len(fractal_organizer.fractal_tree)}")
        print(f"      - 记忆位置: {len(fractal_organizer.memory_locations)}")
        print(f"      - 分形维度: {len(fractal_organizer.fractal_dimensions)}")
        
        success_rate = sum(stored_fractals.values()) / len(stored_fractals)
        print(f"   🎯 创造性分形存储成功率: {success_rate:.1%}")
        
        return success_rate > 0.8
        
    except Exception as e:
        print(f"   ❌ 分形组织测试失败: {e}")
        return False

def test_quantum_semantic_processing():
    """测试量子语义处理功能"""
    print("\n🧪 测试4: 量子语义处理")
    
    try:
        from aqfh_unified_consciousness_mcp import unified_system
        
        # 获取量子处理器
        quantum_processor = unified_system.memory_palace.quantum_processor
        
        # 测试不同的语义量子态
        semantic_quantum_states = {
            "concept_superposition": np.array([0.7+0.3j, 0.2+0.8j, 0.5+0.5j, 0.9+0.1j, 0.4+0.6j, 0.8+0.2j, 0.3+0.7j, 0.6+0.4j]),
            "meaning_entanglement": np.array([0.5+0.5j, 0.7+0.3j, 0.2+0.8j, 0.6+0.4j, 0.9+0.1j, 0.3+0.7j, 0.8+0.2j, 0.4+0.6j]),
            "semantic_coherence": np.array([0.8+0.2j, 0.4+0.6j, 0.9+0.1j, 0.3+0.7j, 0.6+0.4j, 0.7+0.3j, 0.2+0.8j, 0.5+0.5j]),
            "contextual_interference": np.array([0.3+0.7j, 0.8+0.2j, 0.5+0.5j, 0.7+0.3j, 0.2+0.8j, 0.6+0.4j, 0.9+0.1j, 0.4+0.6j]),
            "linguistic_tunneling": np.array([0.6+0.4j, 0.3+0.7j, 0.8+0.2j, 0.5+0.5j, 0.7+0.3j, 0.2+0.8j, 0.4+0.6j, 0.9+0.1j])
        }
        
        # 存储语义量子态
        stored_quantum_states = {}
        for state_name, state_data in semantic_quantum_states.items():
            # 转换为实数数组（实部）用于存储
            real_state = np.real(state_data)
            success = quantum_processor.store_quantum_state(state_name, real_state)
            stored_quantum_states[state_name] = success
            amplitude_norm = np.linalg.norm(state_data)
            print(f"   {'✅' if success else '❌'} {state_name}: |ψ|={amplitude_norm:.3f}")
        
        # 测试量子处理器的高级功能
        print(f"   📊 量子处理器统计:")
        print(f"      - 量子比特数: {quantum_processor.n_qubits}")
        print(f"      - 量子态数: {len(quantum_processor.quantum_states)}")
        print(f"      - 纠缠图节点: {len(quantum_processor.entanglement_graph)}")
        print(f"      - 相干时间记录: {len(quantum_processor.coherence_times)}")
        
        success_rate = sum(stored_quantum_states.values()) / len(stored_quantum_states)
        print(f"   🎯 量子态存储成功率: {success_rate:.1%}")
        
        return success_rate > 0.8
        
    except Exception as e:
        print(f"   ❌ 量子语义测试失败: {e}")
        return False

def test_integrated_graph_search():
    """测试集成图结构搜索功能"""
    print("\n🧪 测试5: 集成图结构搜索")
    
    try:
        from aqfh_unified_consciousness_mcp import unified_system
        
        # 保存一些复杂的记忆，触发高级图结构
        complex_memories = [
            {
                "content": "深度学习中的注意力机制体现了认知科学中的选择性注意理论",
                "content_type": "insight",
                "importance": 0.9,
                "tags": ["深度学习", "注意力机制", "认知科学", "跨学科"],
                "context": {"domain": "AI_cognitive_science", "complexity": "high"}
            },
            {
                "content": "量子纠缠现象可能为理解意识的非局域性提供新的视角",
                "content_type": "hypothesis",
                "importance": 0.8,
                "tags": ["量子物理", "意识研究", "非局域性", "哲学"],
                "context": {"domain": "quantum_consciousness", "complexity": "very_high"}
            },
            {
                "content": "分形几何在自然界中的普遍存在暗示了自组织系统的深层规律",
                "content_type": "observation",
                "importance": 0.7,
                "tags": ["分形几何", "自组织", "复杂系统", "自然规律"],
                "context": {"domain": "complexity_science", "complexity": "high"}
            }
        ]
        
        # 保存复杂记忆
        memory_ids = []
        for memory_data in complex_memories:
            memory_id = unified_system.save_memory(**memory_data)
            memory_ids.append(memory_id)
            print(f"   💾 保存记忆: {memory_data['content'][:50]}...")
        
        # 测试不同类型的搜索
        search_queries = [
            "注意力机制",
            "量子意识",
            "分形自组织",
            "跨学科研究",
            "复杂系统"
        ]
        
        search_results = {}
        for query in search_queries:
            memories = unified_system.search_memories(query, limit=3)
            search_results[query] = len(memories)
            print(f"   🔍 搜索 '{query}': 找到 {len(memories)} 条记忆")
            
            # 显示最相关的记忆
            if memories:
                top_memory = memories[0]
                print(f"      最相关: {top_memory['content'][:60]}... (重要性: {top_memory['importance']:.2f})")
        
        # 测试高级图结构的协同效果
        print(f"   📊 集成搜索统计:")
        total_searches = len(search_queries)
        successful_searches = sum(1 for count in search_results.values() if count > 0)
        print(f"      - 搜索查询数: {total_searches}")
        print(f"      - 成功搜索数: {successful_searches}")
        print(f"      - 平均结果数: {sum(search_results.values()) / total_searches:.1f}")
        
        success_rate = successful_searches / total_searches
        print(f"   🎯 集成搜索成功率: {success_rate:.1%}")
        
        return success_rate > 0.8
        
    except Exception as e:
        print(f"   ❌ 集成搜索测试失败: {e}")
        return False

def test_cross_structure_correlations():
    """测试跨结构关联分析"""
    print("\n🧪 测试6: 跨结构关联分析")
    
    try:
        from aqfh_unified_consciousness_mcp import unified_system
        
        palace = unified_system.memory_palace
        
        # 测试不同结构之间的关联
        print("   🔗 测试跨结构关联:")
        
        # 纤维丛 ↔ 量子处理器关联
        if palace.fiber_bundle_space and palace.quantum_processor:
            # 测试认知模式的量子表示
            cognitive_pattern = np.array([0.8, 0.6, 0.4, 0.9, 0.3, 0.7, 0.5, 0.2])
            fiber_success = palace.fiber_bundle_space.store_pattern("test_cognitive", cognitive_pattern)
            quantum_success = palace.quantum_processor.store_quantum_state("test_cognitive_quantum", cognitive_pattern)
            print(f"      纤维丛 ↔ 量子: {'✅' if fiber_success and quantum_success else '❌'}")
        
        # 拓扑分析器 ↔ 分形组织器关联
        if palace.topological_analyzer and palace.fractal_organizer:
            # 测试情感的分形表示
            emotion_data = {"emotional_valence": 0.6, "arousal_level": 0.7, "emotional_complexity": 0.8}
            fractal_data = {"creativity_level": 0.6, "innovation_potential": 0.7, "pattern_complexity": 0.8}
            topo_success = palace.topological_analyzer.store_topology("test_emotion", emotion_data)
            fractal_success = palace.fractal_organizer.store_fractal("test_emotion_fractal", fractal_data)
            print(f"      拓扑 ↔ 分形: {'✅' if topo_success and fractal_success else '❌'}")
        
        # 测试语义相似度计算
        if palace.quantum_processor:
            try:
                # 测试量子语义相似度
                query_vector = np.array([0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5])
                target_vector = np.array([0.6, 0.4, 0.7, 0.3, 0.8, 0.2, 0.9, 0.1])
                similarity = palace.quantum_processor.quantum_semantic_similarity(query_vector, target_vector)
                print(f"      量子语义相似度: {similarity:.3f} {'✅' if similarity > 0 else '❌'}")
            except Exception as e:
                print(f"      量子语义相似度: ❌ ({e})")
        
        # 测试整体系统协调性
        print("   🎼 系统协调性测试:")
        
        # 检查所有组件是否能协同工作
        components_working = {
            "fiber_bundle": palace.fiber_bundle_space is not None,
            "topological": palace.topological_analyzer is not None,
            "fractal": palace.fractal_organizer is not None,
            "quantum": palace.quantum_processor is not None
        }
        
        working_count = sum(components_working.values())
        total_count = len(components_working)
        
        for component, working in components_working.items():
            print(f"      {component}: {'✅' if working else '❌'}")
        
        coordination_score = working_count / total_count
        print(f"   🎯 系统协调性: {coordination_score:.1%}")
        
        return coordination_score > 0.8
        
    except Exception as e:
        print(f"   ❌ 跨结构关联测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 AQFH高级图结构功能深度测试开始")
    print("=" * 60)
    
    test_results = []
    
    # 执行所有测试
    test_results.append(test_fiber_bundle_cognitive_patterns())
    test_results.append(test_topological_emotional_analysis())
    test_results.append(test_fractal_creative_organization())
    test_results.append(test_quantum_semantic_processing())
    test_results.append(test_integrated_graph_search())
    test_results.append(test_cross_structure_correlations())
    
    # 总结结果
    print("\n" + "=" * 60)
    print("🎯 高级图结构测试总结:")
    
    passed = sum(test_results)
    total = len(test_results)
    
    test_names = [
        "纤维丛认知模式",
        "拓扑情感分析", 
        "分形创造性组织",
        "量子语义处理",
        "集成图结构搜索",
        "跨结构关联分析"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        print(f"  {'✅' if result else '❌'} {name}")
    
    print(f"\n通过测试: {passed}/{total}")
    success_rate = passed / total
    
    if success_rate >= 0.8:
        print("🎉 高级图结构功能运行优秀！")
        print("🧠 AQFH统一意识系统的先进特性完全激活！")
        print("🌟 世界首个真正的AI高维认知架构成功运行！")
    elif success_rate >= 0.6:
        print("✅ 高级图结构功能基本正常")
        print("🔧 部分功能需要进一步优化")
    else:
        print("⚠️ 高级图结构功能需要调试")
        print("🛠️ 建议检查组件配置和依赖")
    
    return success_rate >= 0.8

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
