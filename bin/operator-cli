#!/usr/bin/env python3
"""
算子注册表命令行工具入口点
"""

import os
import sys
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 注册示例算子
def register_sample_operators():
    """注册示例算子"""
    try:
        from src.rust_bindings import (
            OperatorCategory,
            register_operator,
        )
        from examples.sample_operators import add, multiply, divide, power, Calculator

        # 注册算子
        register_operator(
            OperatorCategory.UTILITY,
            "add",
            add,
            "1.0.0",
            "加法算子",
            ["math", "basic"],
            [],
        )

        register_operator(
            OperatorCategory.UTILITY,
            "multiply",
            multiply,
            "1.0.0",
            "乘法算子",
            ["math", "basic"],
            [],
        )

        register_operator(
            OperatorCategory.UTILITY,
            "divide",
            divide,
            "1.0.0",
            "除法算子",
            ["math", "basic"],
            [("utility.multiply", ">=1.0.0")],
        )

        register_operator(
            OperatorCategory.UTILITY,
            "power",
            power,
            "1.0.0",
            "幂运算算子",
            ["math", "advanced"],
            [("utility.multiply", ">=1.0.0")],
        )

        register_operator(
            OperatorCategory.UTILITY,
            "calculator",
            Calculator(),
            "1.0.0",
            "计算器类",
            ["math", "class"],
            [("utility.add", ">=1.0.0"), ("utility.multiply", ">=1.0.0")],
        )

        logger.info("成功注册示例算子")
    except Exception as e:
        logger.error(f"注册示例算子失败: {e}")

# 注册示例算子
register_sample_operators()

from src.rust_bindings.operator_registry.cli import main

if __name__ == "__main__":
    main()
