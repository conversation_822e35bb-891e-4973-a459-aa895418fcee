# PyO3 API 修复实施计划

## 第一阶段：环境准备

### 任务 1.1：更新 Cargo.toml

- 在 `/home/<USER>/CascadeProjects/TTE/Cargo.toml` 中添加 `gil-refs` 特性
- 在 `/home/<USER>/CascadeProjects/TTE/rust_operators/Cargo.toml` 中添加 `gil-refs` 特性
- 在 `/home/<USER>/CascadeProjects/TTE/src/interfaces/Cargo.toml` 中添加 `gil-refs` 特性

### 任务 1.2：创建测试脚本

- 创建 `/home/<USER>/CascadeProjects/TTE/test_pyo3_api.py` 用于测试 Python 端调用
- 创建 `/home/<USER>/CascadeProjects/TTE/tests/test_pyo3_api.rs` 用于 Rust 端单元测试

## 第二阶段：核心模块修复

### 任务 2.1：修复错误处理模块

- 文件：`/home/<USER>/CascadeProjects/TTE/src/rust_bindings/operator_registry/error_handling.py`
- 文件：`/home/<USER>/CascadeProjects/TTE/src/interfaces/src/lib.rs`
- 更新异常创建和处理方式
- 修复 `PyErr` 相关代码

### 任务 2.2：修复算子注册表模块

- 文件：`/home/<USER>/CascadeProjects/TTE/src/rust_bindings/operator_registry/registry.py`
- 文件：`/home/<USER>/CascadeProjects/TTE/rust_operators/src/operator_registry/mod.rs`
- 更新 `PyDict` 和 `PyList` 相关方法
- 修复类型转换代码

### 任务 2.3：修复持久化模块

- 文件：`/home/<USER>/CascadeProjects/TTE/src/rust_bindings/operator_registry/persistence.py`
- 更新 Python 对象创建和访问方式
- 修复序列化和反序列化代码

## 第三阶段：算子模块修复

### 任务 3.1：修复基础算子接口

- 文件：`/home/<USER>/CascadeProjects/TTE/src/interfaces/src/lib.rs`
- 更新 `PyErrorHandlingOperator` 类
- 修复方法签名和默认参数

### 任务 3.2：修复数值算子

- 文件：`/home/<USER>/CascadeProjects/TTE/rust_operators/src/numpy_ops/math_ops.rs`
- 更新默认参数语法
- 修复方法签名

### 任务 3.3：修复并行算子

- 文件：`/home/<USER>/CascadeProjects/TTE/rust_operators/src/numpy_ops/parallel_ops.rs`
- 文件：`/home/<USER>/CascadeProjects/TTE/rust_operators/src/parallel_computing/parallel_algorithms.rs`
- 更新默认参数语法
- 修复数据借用问题

## 第四阶段：工具模块修复

### 任务 4.1：修复依赖管理模块

- 文件：`/home/<USER>/CascadeProjects/TTE/src/utils/dependency_manager.py`
- 更新模块导入和模拟类创建方式
- 修复循环导入问题

### 任务 4.2：修复版本适配器

- 文件：`/home/<USER>/CascadeProjects/TTE/rust_operators/src/version_adapter/adapter.rs`
- 添加缺少的导入
- 修复 API 调用

## 第五阶段：主模块修复

### 任务 5.1：修复主库入口点

- 文件：`/home/<USER>/CascadeProjects/TTE/rust_operators/src/lib.rs`
- 更新模块注册方式
- 修复子模块添加方法

### 任务 5.2：修复动态形态模块

- 文件：`/home/<USER>/CascadeProjects/TTE/rust_operators/src/dynamic_morphism/mod.rs`
- 更新类型转换和对象创建
- 修复线程安全问题

## 第六阶段：测试和验证

### 任务 6.1：运行单元测试

- 执行 `cargo test --no-default-features` 验证 Rust 端测试
- 修复测试中发现的问题

### 任务 6.2：运行集成测试

- 执行 Python 测试脚本验证 Python 端调用
- 修复测试中发现的问题

### 任务 6.3：编译检查

- 移除 `gil-refs` 特性
- 确保代码在没有弃用特性的情况下编译通过

## 第七阶段：文档更新

### 任务 7.1：更新 API 文档

- 更新 API 文档反映新的用法
- 添加迁移指南

### 任务 7.2：更新示例代码

- 更新示例代码使用新的 API
- 添加新的示例展示最佳实践

## 具体修复示例

### 示例 1：修复 PyTuple 创建

```rust
// 旧代码
let tup = PyTuple::new_bound(py, [1, 2, 3]);

// 新代码
let tup = PyTuple::new(py, [1, 2, 3]);
```

### 示例 2：修复类型转换

```rust
// 旧代码
let b = <PyInt as PyTryFrom>::try_from(list.get_item(0).unwrap())?;

// 新代码
let b = list.get_item(0).unwrap().downcast::<PyInt>()?;
```

### 示例 3：修复默认参数

```rust
// 旧代码
fn sum<'py>(&self, py: Python<'py>, array: &PyAny, axis: Option<usize> = None) -> PyResult<&'py PyAny> {
    self.ops.sum(py, array, axis)
}

// 新代码
#[pyo3(signature = (array, axis=None))]
fn sum<'py>(&self, py: Python<'py>, array: &PyAny, axis: Option<usize>) -> PyResult<&'py PyAny> {
    self.ops.sum(py, array, axis)
}
```

### 示例 4：修复异常创建

```rust
// 旧代码
let err: PyErr = TypeError::py_err("error message");

// 新代码
let err: PyErr = PyTypeError::new_err("error message");
```

### 示例 5：修复 GIL 引用转换

```rust
// 旧代码
let gil_ref: &PyAny = ...;
// 使用 gil_ref

// 新代码
let gil_ref: &PyAny = ...;
let bound: &Bound<PyAny> = &gil_ref.as_borrowed();
// 使用 bound
```

## 时间安排

- 第一阶段：1天
- 第二阶段：2天
- 第三阶段：2天
- 第四阶段：1天
- 第五阶段：1天
- 第六阶段：2天
- 第七阶段：1天

总计：10天
