"""
用户手册生成器

这个脚本生成超越态思维引擎的用户手册。
"""

import os
import sys
import inspect
import importlib
import pkgutil
import argparse
import re
from typing import Dict, Any, List, Set, Tuple, Optional, Union, Callable

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))


def extract_examples(docstring):
    """提取示例"""
    examples = []
    
    # 检查文档字符串
    if not docstring:
        return examples
    
    # 查找示例
    in_example = False
    current_example = []
    
    for line in docstring.split("\n"):
        # 检查示例开始
        if re.match(r"^\s*Examples?:\s*$", line):
            in_example = True
            continue
        
        # 检查示例结束
        if in_example and line.strip() and not line.startswith(" "):
            in_example = False
            if current_example:
                examples.append("\n".join(current_example))
                current_example = []
        
        # 添加示例行
        if in_example and line.strip():
            current_example.append(line)
    
    # 添加最后一个示例
    if current_example:
        examples.append("\n".join(current_example))
    
    return examples


def generate_getting_started(package_name):
    """生成入门指南"""
    doc = "# Getting Started\n\n"
    
    # 添加安装说明
    doc += "## Installation\n\n"
    doc += "You can install the package using pip:\n\n"
    doc += "```bash\npip install " + package_name + "\n```\n\n"
    
    # 添加基本用法
    doc += "## Basic Usage\n\n"
    doc += "Here's a simple example of how to use the package:\n\n"
    doc += "```python\n"
    doc += f"import {package_name}\n\n"
    doc += "# Create a transcendental state\n"
    doc += f"state = {package_name}.core.TranscendentalState()\n\n"
    doc += "# Perform operations on the state\n"
    doc += "# ...\n"
    doc += "```\n\n"
    
    # 添加配置说明
    doc += "## Configuration\n\n"
    doc += "You can configure the package using the following methods:\n\n"
    doc += "```python\n"
    doc += f"import {package_name}\n\n"
    doc += "# Load configuration from file\n"
    doc += f"{package_name}.utils.config.load_config('config.json')\n\n"
    doc += "# Set configuration values\n"
    doc += f"{package_name}.utils.config.set_value('section.key', 'value')\n"
    doc += "```\n\n"
    
    return doc


def generate_tutorials(package_name):
    """生成教程"""
    doc = "# Tutorials\n\n"
    
    # 添加基础教程
    doc += "## Basic Tutorial\n\n"
    doc += "This tutorial will guide you through the basic usage of the package.\n\n"
    doc += "### Step 1: Create a Transcendental State\n\n"
    doc += "```python\n"
    doc += f"import {package_name}\n\n"
    doc += f"state = {package_name}.core.TranscendentalState()\n"
    doc += "```\n\n"
    doc += "### Step 2: Perform Operations\n\n"
    doc += "```python\n"
    doc += "# Perform operations on the state\n"
    doc += "# ...\n"
    doc += "```\n\n"
    doc += "### Step 3: Save the State\n\n"
    doc += "```python\n"
    doc += "# Save the state to a file\n"
    doc += "with open('state.json', 'w') as f:\n"
    doc += "    f.write(state.to_json())\n"
    doc += "```\n\n"
    
    # 添加高级教程
    doc += "## Advanced Tutorial\n\n"
    doc += "This tutorial will guide you through the advanced usage of the package.\n\n"
    doc += "### Step 1: Create a Distributed Node\n\n"
    doc += "```python\n"
    doc += f"import {package_name}\n\n"
    doc += f"node = {package_name}.core.DistributedNode()\n"
    doc += "```\n\n"
    doc += "### Step 2: Add Tasks\n\n"
    doc += "```python\n"
    doc += "# Create a task\n"
    doc += f"task = {package_name}.core.Task()\n\n"
    doc += "# Add the task to the node\n"
    doc += "node.add_task(task)\n"
    doc += "```\n\n"
    doc += "### Step 3: Execute Tasks\n\n"
    doc += "```python\n"
    doc += "# Execute the task\n"
    doc += "node.execute_task(task.get_id())\n"
    doc += "```\n\n"
    
    return doc


def generate_api_reference(package_name):
    """生成API参考"""
    doc = "# API Reference\n\n"
    
    # 添加API参考
    doc += "For detailed API reference, please see the [API Documentation](../api/README.md).\n\n"
    
    return doc


def generate_examples(package_name):
    """生成示例"""
    doc = "# Examples\n\n"
    
    # 导入包
    package = importlib.import_module(package_name)
    
    # 获取包模块
    modules = {}
    for _, name, is_pkg in pkgutil.iter_modules(package.__path__, package.__name__ + "."):
        # 导入模块
        try:
            module = importlib.import_module(name)
            modules[name] = module
        except ImportError:
            pass
    
    # 提取示例
    examples = []
    for name, module in modules.items():
        # 获取模块文档字符串
        docstring = module.__doc__ or ""
        
        # 提取示例
        module_examples = extract_examples(docstring)
        
        # 添加示例
        for example in module_examples:
            examples.append((name, example))
        
        # 获取模块函数
        for func_name, func in inspect.getmembers(module, inspect.isfunction):
            # 获取函数文档字符串
            docstring = func.__doc__ or ""
            
            # 提取示例
            func_examples = extract_examples(docstring)
            
            # 添加示例
            for example in func_examples:
                examples.append((f"{name}.{func_name}", example))
        
        # 获取模块类
        for cls_name, cls in inspect.getmembers(module, inspect.isclass):
            # 检查是否为模块定义的类
            if cls.__module__ == name:
                # 获取类文档字符串
                docstring = cls.__doc__ or ""
                
                # 提取示例
                cls_examples = extract_examples(docstring)
                
                # 添加示例
                for example in cls_examples:
                    examples.append((f"{name}.{cls_name}", example))
                
                # 获取类方法
                for method_name, method in inspect.getmembers(cls, inspect.isfunction):
                    # 获取方法文档字符串
                    docstring = method.__doc__ or ""
                    
                    # 提取示例
                    method_examples = extract_examples(docstring)
                    
                    # 添加示例
                    for example in method_examples:
                        examples.append((f"{name}.{cls_name}.{method_name}", example))
    
    # 添加示例
    for i, (name, example) in enumerate(examples):
        doc += f"## Example {i+1}: {name}\n\n"
        doc += "```python\n"
        doc += example + "\n"
        doc += "```\n\n"
    
    return doc


def generate_faq():
    """生成常见问题"""
    doc = "# Frequently Asked Questions\n\n"
    
    # 添加常见问题
    doc += "## What is a Transcendental State?\n\n"
    doc += "A Transcendental State is a state that transcends the limitations of traditional computational states. It represents a higher-dimensional state that can capture complex relationships and patterns.\n\n"
    
    doc += "## How do I create a Transcendental State?\n\n"
    doc += "You can create a Transcendental State using the `TranscendentalState` class:\n\n"
    doc += "```python\nfrom src.core import TranscendentalState\n\nstate = TranscendentalState()\n```\n\n"
    
    doc += "## How do I perform operations on a Transcendental State?\n\n"
    doc += "You can perform operations on a Transcendental State using the operators provided by the package:\n\n"
    doc += "```python\nfrom src.core import TranscendentalState\nfrom src.operators import SomeOperator\n\nstate = TranscendentalState()\noperator = SomeOperator()\nresult = operator.apply(state)\n```\n\n"
    
    doc += "## How do I save and load a Transcendental State?\n\n"
    doc += "You can save and load a Transcendental State using the `to_json` and `from_json` methods:\n\n"
    doc += "```python\nfrom src.core import TranscendentalState\n\n# Save state\nstate = TranscendentalState()\nwith open('state.json', 'w') as f:\n    f.write(state.to_json())\n\n# Load state\nwith open('state.json', 'r') as f:\n    state = TranscendentalState.from_json(f.read())\n```\n\n"
    
    return doc


def generate_user_manual(package_name, output_dir):
    """生成用户手册"""
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成入门指南
    getting_started = generate_getting_started(package_name)
    with open(os.path.join(output_dir, "getting_started.md"), "w") as f:
        f.write(getting_started)
    
    # 生成教程
    tutorials = generate_tutorials(package_name)
    with open(os.path.join(output_dir, "tutorials.md"), "w") as f:
        f.write(tutorials)
    
    # 生成API参考
    api_reference = generate_api_reference(package_name)
    with open(os.path.join(output_dir, "api_reference.md"), "w") as f:
        f.write(api_reference)
    
    # 生成示例
    examples = generate_examples(package_name)
    with open(os.path.join(output_dir, "examples.md"), "w") as f:
        f.write(examples)
    
    # 生成常见问题
    faq = generate_faq()
    with open(os.path.join(output_dir, "faq.md"), "w") as f:
        f.write(faq)
    
    # 生成用户手册
    doc = "# User Manual\n\n"
    
    # 添加目录
    doc += "## Table of Contents\n\n"
    doc += "- [Getting Started](getting_started.md)\n"
    doc += "- [Tutorials](tutorials.md)\n"
    doc += "- [API Reference](api_reference.md)\n"
    doc += "- [Examples](examples.md)\n"
    doc += "- [FAQ](faq.md)\n\n"
    
    # 添加简介
    doc += "## Introduction\n\n"
    doc += f"Welcome to the {package_name} user manual. This manual will guide you through the usage of the package and provide examples and tutorials to help you get started.\n\n"
    
    # 写入文档
    with open(os.path.join(output_dir, "README.md"), "w") as f:
        f.write(doc)


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="Generate user manual")
    parser.add_argument("package", help="Package name")
    parser.add_argument("--output", "-o", default="docs/manual", help="Output directory")
    args = parser.parse_args()
    
    # 生成文档
    generate_user_manual(args.package, args.output)


if __name__ == "__main__":
    main()
