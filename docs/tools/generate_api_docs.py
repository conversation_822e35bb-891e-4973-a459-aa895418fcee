"""
API文档生成器

这个脚本生成超越态思维引擎的API文档。
"""

import os
import sys
import inspect
import importlib
import pkgutil
import argparse
from typing import Dict, Any, List, Set, Tuple, Optional, Union, Callable

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))


def get_module_docstring(module):
    """获取模块文档字符串"""
    return module.__doc__ or ""


def get_class_docstring(cls):
    """获取类文档字符串"""
    return cls.__doc__ or ""


def get_function_docstring(func):
    """获取函数文档字符串"""
    return func.__doc__ or ""


def get_module_members(module):
    """获取模块成员"""
    members = {}
    
    # 获取所有成员
    for name, obj in inspect.getmembers(module):
        # 跳过私有成员
        if name.startswith("_"):
            continue
        
        # 跳过导入的模块
        if inspect.ismodule(obj):
            continue
        
        # 添加成员
        members[name] = obj
    
    return members


def get_module_classes(module):
    """获取模块类"""
    classes = {}
    
    # 获取所有成员
    for name, obj in inspect.getmembers(module):
        # 跳过私有成员
        if name.startswith("_"):
            continue
        
        # 检查是否为类
        if inspect.isclass(obj):
            # 检查是否为模块定义的类
            if obj.__module__ == module.__name__:
                classes[name] = obj
    
    return classes


def get_module_functions(module):
    """获取模块函数"""
    functions = {}
    
    # 获取所有成员
    for name, obj in inspect.getmembers(module):
        # 跳过私有成员
        if name.startswith("_"):
            continue
        
        # 检查是否为函数
        if inspect.isfunction(obj):
            # 检查是否为模块定义的函数
            if obj.__module__ == module.__name__:
                functions[name] = obj
    
    return functions


def get_class_methods(cls):
    """获取类方法"""
    methods = {}
    
    # 获取所有成员
    for name, obj in inspect.getmembers(cls):
        # 跳过私有成员
        if name.startswith("_") and name != "__init__":
            continue
        
        # 检查是否为方法
        if inspect.isfunction(obj) or inspect.ismethod(obj):
            methods[name] = obj
    
    return methods


def get_class_attributes(cls):
    """获取类属性"""
    attributes = {}
    
    # 获取所有成员
    for name, obj in inspect.getmembers(cls):
        # 跳过私有成员
        if name.startswith("_") and name != "__init__":
            continue
        
        # 检查是否为属性
        if not (inspect.isfunction(obj) or inspect.ismethod(obj) or inspect.isclass(obj)):
            attributes[name] = obj
    
    return attributes


def get_function_signature(func):
    """获取函数签名"""
    return str(inspect.signature(func))


def get_function_parameters(func):
    """获取函数参数"""
    params = {}
    
    # 获取签名
    sig = inspect.signature(func)
    
    # 获取参数
    for name, param in sig.parameters.items():
        params[name] = {
            "name": name,
            "kind": str(param.kind),
            "default": param.default if param.default is not inspect.Parameter.empty else None,
            "annotation": param.annotation if param.annotation is not inspect.Parameter.empty else None
        }
    
    return params


def get_function_return_type(func):
    """获取函数返回类型"""
    # 获取签名
    sig = inspect.signature(func)
    
    # 获取返回类型
    return sig.return_annotation if sig.return_annotation is not inspect.Signature.empty else None


def generate_module_doc(module):
    """生成模块文档"""
    doc = f"# {module.__name__}\n\n"
    
    # 添加模块文档字符串
    doc += f"{get_module_docstring(module)}\n\n"
    
    # 添加类文档
    classes = get_module_classes(module)
    if classes:
        doc += "## Classes\n\n"
        
        for name, cls in sorted(classes.items()):
            doc += f"### {name}\n\n"
            doc += f"{get_class_docstring(cls)}\n\n"
            
            # 添加方法文档
            methods = get_class_methods(cls)
            if methods:
                doc += "#### Methods\n\n"
                
                for method_name, method in sorted(methods.items()):
                    doc += f"##### {method_name}{get_function_signature(method)}\n\n"
                    doc += f"{get_function_docstring(method)}\n\n"
            
            # 添加属性文档
            attributes = get_class_attributes(cls)
            if attributes:
                doc += "#### Attributes\n\n"
                
                for attr_name, attr in sorted(attributes.items()):
                    doc += f"##### {attr_name}\n\n"
                    doc += f"Type: {type(attr).__name__}\n\n"
    
    # 添加函数文档
    functions = get_module_functions(module)
    if functions:
        doc += "## Functions\n\n"
        
        for name, func in sorted(functions.items()):
            doc += f"### {name}{get_function_signature(func)}\n\n"
            doc += f"{get_function_docstring(func)}\n\n"
    
    return doc


def generate_package_doc(package):
    """生成包文档"""
    doc = f"# {package.__name__}\n\n"
    
    # 添加包文档字符串
    doc += f"{get_module_docstring(package)}\n\n"
    
    # 添加子模块文档
    doc += "## Modules\n\n"
    
    # 获取子模块
    for _, name, is_pkg in pkgutil.iter_modules(package.__path__, package.__name__ + "."):
        if is_pkg:
            doc += f"- [{name}]({name.split('.')[-1]}/README.md)\n"
        else:
            doc += f"- [{name}]({name.split('.')[-1]}.md)\n"
    
    return doc


def generate_docs(package_name, output_dir):
    """生成文档"""
    # 导入包
    package = importlib.import_module(package_name)
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成包文档
    doc = generate_package_doc(package)
    
    # 写入文档
    with open(os.path.join(output_dir, "README.md"), "w") as f:
        f.write(doc)
    
    # 生成子模块文档
    for _, name, is_pkg in pkgutil.iter_modules(package.__path__, package.__name__ + "."):
        # 导入模块
        module = importlib.import_module(name)
        
        # 生成模块文档
        doc = generate_module_doc(module)
        
        # 创建子目录
        if is_pkg:
            subdir = os.path.join(output_dir, name.split(".")[-1])
            os.makedirs(subdir, exist_ok=True)
            
            # 写入文档
            with open(os.path.join(subdir, "README.md"), "w") as f:
                f.write(doc)
        else:
            # 写入文档
            with open(os.path.join(output_dir, name.split(".")[-1] + ".md"), "w") as f:
                f.write(doc)


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="Generate API documentation")
    parser.add_argument("package", help="Package name")
    parser.add_argument("--output", "-o", default="docs/api", help="Output directory")
    args = parser.parse_args()
    
    # 生成文档
    generate_docs(args.package, args.output)


if __name__ == "__main__":
    main()
