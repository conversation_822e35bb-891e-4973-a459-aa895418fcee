"""
架构文档生成器

这个脚本生成超越态思维引擎的架构文档。
"""

import os
import sys
import inspect
import importlib
import pkgutil
import argparse
import networkx as nx
import matplotlib.pyplot as plt
from typing import Dict, Any, List, Set, Tuple, Optional, Union, Callable

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))


def get_module_dependencies(module):
    """获取模块依赖"""
    dependencies = set()
    
    # 获取模块源码
    try:
        source = inspect.getsource(module)
    except (TypeError, OSError):
        return dependencies
    
    # 解析导入语句
    for line in source.split("\n"):
        line = line.strip()
        
        # 检查导入语句
        if line.startswith("import "):
            # 解析导入
            parts = line[7:].split(",")
            for part in parts:
                part = part.strip()
                if part:
                    dependencies.add(part)
        elif line.startswith("from "):
            # 解析导入
            parts = line.split(" import ")
            if len(parts) == 2:
                module_name = parts[0][5:].strip()
                if module_name:
                    dependencies.add(module_name)
    
    return dependencies


def get_package_modules(package):
    """获取包模块"""
    modules = {}
    
    # 获取子模块
    for _, name, is_pkg in pkgutil.iter_modules(package.__path__, package.__name__ + "."):
        # 导入模块
        try:
            module = importlib.import_module(name)
            modules[name] = module
            
            # 递归获取子包模块
            if is_pkg:
                modules.update(get_package_modules(module))
        except ImportError:
            pass
    
    return modules


def generate_dependency_graph(package_name, output_file):
    """生成依赖图"""
    # 导入包
    package = importlib.import_module(package_name)
    
    # 获取包模块
    modules = get_package_modules(package)
    
    # 创建图
    G = nx.DiGraph()
    
    # 添加节点
    for name in modules:
        G.add_node(name)
    
    # 添加边
    for name, module in modules.items():
        # 获取依赖
        dependencies = get_module_dependencies(module)
        
        # 添加边
        for dep in dependencies:
            # 检查依赖是否为包模块
            for module_name in modules:
                if dep == module_name or module_name.startswith(dep + "."):
                    G.add_edge(name, module_name)
    
    # 绘制图
    plt.figure(figsize=(12, 8))
    pos = nx.spring_layout(G)
    nx.draw(G, pos, with_labels=True, node_color="lightblue", node_size=1500, font_size=10, font_weight="bold", arrows=True)
    plt.title(f"Dependency Graph for {package_name}")
    
    # 保存图
    plt.savefig(output_file)
    plt.close()


def generate_module_structure(package_name, output_file):
    """生成模块结构"""
    # 导入包
    package = importlib.import_module(package_name)
    
    # 获取包模块
    modules = get_package_modules(package)
    
    # 创建文档
    doc = f"# Module Structure for {package_name}\n\n"
    
    # 添加模块结构
    doc += "```\n"
    
    # 构建模块树
    tree = {}
    for name in sorted(modules.keys()):
        parts = name.split(".")
        current = tree
        for part in parts:
            if part not in current:
                current[part] = {}
            current = current[part]
    
    # 打印模块树
    def print_tree(node, prefix="", is_last=True, result=[]):
        # 获取子节点
        children = sorted(node.keys())
        
        # 打印当前节点
        if prefix:
            result.append(prefix + ("└── " if is_last else "├── ") + children[-1])
        
        # 打印子节点
        prefix += "    " if is_last else "│   "
        for i, child in enumerate(children[:-1]):
            print_tree(node[child], prefix, False, result)
        if children:
            print_tree(node[children[-1]], prefix, True, result)
        
        return result
    
    # 打印树
    lines = print_tree(tree)
    doc += "\n".join(lines) + "\n"
    
    doc += "```\n"
    
    # 写入文档
    with open(output_file, "w") as f:
        f.write(doc)


def generate_class_diagram(package_name, output_file):
    """生成类图"""
    # 导入包
    package = importlib.import_module(package_name)
    
    # 获取包模块
    modules = get_package_modules(package)
    
    # 创建文档
    doc = f"# Class Diagram for {package_name}\n\n"
    
    # 添加类图
    doc += "```mermaid\nclassDiagram\n"
    
    # 获取所有类
    classes = {}
    for name, module in modules.items():
        # 获取模块类
        for cls_name, cls in inspect.getmembers(module, inspect.isclass):
            # 检查是否为模块定义的类
            if cls.__module__ == name:
                classes[cls.__name__] = cls
    
    # 添加类
    for name, cls in classes.items():
        # 添加类
        doc += f"    class {name}\n"
        
        # 添加方法
        for method_name, method in inspect.getmembers(cls, inspect.isfunction):
            if not method_name.startswith("_") or method_name == "__init__":
                doc += f"    {name} : +{method_name}()\n"
        
        # 添加继承关系
        for base in cls.__bases__:
            if base.__name__ in classes:
                doc += f"    {base.__name__} <|-- {name}\n"
    
    doc += "```\n"
    
    # 写入文档
    with open(output_file, "w") as f:
        f.write(doc)


def generate_architecture_doc(package_name, output_dir):
    """生成架构文档"""
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成依赖图
    generate_dependency_graph(package_name, os.path.join(output_dir, "dependency_graph.png"))
    
    # 生成模块结构
    generate_module_structure(package_name, os.path.join(output_dir, "module_structure.md"))
    
    # 生成类图
    generate_class_diagram(package_name, os.path.join(output_dir, "class_diagram.md"))
    
    # 生成架构文档
    doc = f"# Architecture Documentation for {package_name}\n\n"
    
    # 添加依赖图
    doc += "## Dependency Graph\n\n"
    doc += f"![Dependency Graph](dependency_graph.png)\n\n"
    
    # 添加模块结构
    doc += "## Module Structure\n\n"
    doc += "See [Module Structure](module_structure.md)\n\n"
    
    # 添加类图
    doc += "## Class Diagram\n\n"
    doc += "See [Class Diagram](class_diagram.md)\n\n"
    
    # 写入文档
    with open(os.path.join(output_dir, "README.md"), "w") as f:
        f.write(doc)


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="Generate architecture documentation")
    parser.add_argument("package", help="Package name")
    parser.add_argument("--output", "-o", default="docs/architecture", help="Output directory")
    args = parser.parse_args()
    
    # 生成文档
    generate_architecture_doc(args.package, args.output)


if __name__ == "__main__":
    main()
