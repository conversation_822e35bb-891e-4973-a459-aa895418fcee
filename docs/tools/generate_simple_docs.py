"""
简单文档生成器

这个脚本生成超越态思维引擎的简单文档。
"""

import os
import sys
import argparse

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))


def generate_utils_docs(output_dir):
    """生成工具模块文档"""
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成错误处理模块文档
    errors_doc = """# errors

错误处理模块

这个模块提供了超越态思维引擎的错误处理功能，包括错误类型定义和错误处理机制。

## Classes

### TTEError

超越态思维引擎基础错误类

#### Methods

##### __init__(message: str, error_code: Optional[int] = None, details: Optional[Dict[str, Any]] = None)

初始化错误

参数:
    message: 错误消息
    error_code: 错误代码
    details: 错误详情

##### to_dict() -> Dict[str, Any]

将错误转换为字典

返回:
    错误字典

##### __str__() -> str

返回错误的字符串表示

### TTEValueError

值错误

#### Methods

##### __init__(message: str, value: Any = None, expected_type: Optional[Type] = None, error_code: Optional[int] = None, details: Optional[Dict[str, Any]] = None)

初始化值错误

参数:
    message: 错误消息
    value: 错误值
    expected_type: 期望类型
    error_code: 错误代码
    details: 错误详情

### TTETypeError

类型错误

#### Methods

##### __init__(message: str, value: Any = None, expected_type: Optional[Type] = None, error_code: Optional[int] = None, details: Optional[Dict[str, Any]] = None)

初始化类型错误

参数:
    message: 错误消息
    value: 错误值
    expected_type: 期望类型
    error_code: 错误代码
    details: 错误详情

### ErrorHandler

错误处理器

#### Methods

##### handle_error(error: Exception, log_error: bool = True, raise_error: bool = False) -> Dict[str, Any]

处理错误

参数:
    error: 错误
    log_error: 是否记录错误
    raise_error: 是否抛出错误
    
返回:
    错误信息字典

##### wrap_exceptions(func: callable, error_message: str = "An error occurred", error_code: Optional[int] = None) -> callable

包装异常

参数:
    func: 函数
    error_message: 错误消息
    error_code: 错误代码
    
返回:
    包装后的函数
"""
    
    with open(os.path.join(output_dir, "errors.md"), "w") as f:
        f.write(errors_doc)
    
    # 生成日志记录模块文档
    logging_doc = """# logging

日志记录模块

这个模块提供了超越态思维引擎的日志记录功能，包括日志配置、日志格式化和日志过滤等。

## Classes

### LogLevel

日志级别

#### Attributes

##### DEBUG

Type: int

##### INFO

Type: int

##### WARNING

Type: int

##### ERROR

Type: int

##### CRITICAL

Type: int

### JSONFormatter

JSON格式化器

#### Methods

##### __init__(include_timestamp: bool = True, include_level: bool = True, include_logger_name: bool = True, include_thread: bool = False, include_process: bool = False, include_file_info: bool = False)

初始化JSON格式化器

参数:
    include_timestamp: 是否包含时间戳
    include_level: 是否包含日志级别
    include_logger_name: 是否包含日志记录器名称
    include_thread: 是否包含线程信息
    include_process: 是否包含进程信息
    include_file_info: 是否包含文件信息

##### format(record: logging.LogRecord) -> str

格式化日志记录

参数:
    record: 日志记录
    
返回:
    格式化后的日志

### ContextFilter

上下文过滤器

#### Methods

##### __init__(context: Dict[str, Any] = None)

初始化上下文过滤器

参数:
    context: 上下文信息

##### filter(record: logging.LogRecord) -> bool

过滤日志记录

参数:
    record: 日志记录
    
返回:
    是否保留日志记录

### LogContext

日志上下文

#### Methods

##### get_context() -> Dict[str, Any]

获取上下文

返回:
    上下文信息

##### set_context(key: str, value: Any) -> None

设置上下文

参数:
    key: 键
    value: 值

##### clear_context() -> None

清除上下文

##### with_context(**kwargs) -> Callable[[F], F]

使用上下文装饰器

参数:
    **kwargs: 上下文信息
    
返回:
    装饰器

## Functions

### configure_logging(level: int = LogLevel.INFO, log_file: Optional[str] = None, json_format: bool = False, include_timestamp: bool = True, include_level: bool = True, include_logger_name: bool = True, include_thread: bool = False, include_process: bool = False, include_file_info: bool = False) -> None

配置日志

参数:
    level: 日志级别
    log_file: 日志文件路径
    json_format: 是否使用JSON格式
    include_timestamp: 是否包含时间戳
    include_level: 是否包含日志级别
    include_logger_name: 是否包含日志记录器名称
    include_thread: 是否包含线程信息
    include_process: 是否包含进程信息
    include_file_info: 是否包含文件信息

### get_logger(name: str, level: Optional[int] = None) -> logging.Logger

获取日志记录器

参数:
    name: 日志记录器名称
    level: 日志级别
    
返回:
    日志记录器

### log_function(level: int = LogLevel.INFO, logger_name: Optional[str] = None, log_args: bool = True, log_result: bool = True, log_execution_time: bool = True) -> Callable[[F], F]

日志装饰器

参数:
    level: 日志级别
    logger_name: 日志记录器名称
    log_args: 是否记录参数
    log_result: 是否记录结果
    log_execution_time: 是否记录执行时间
    
返回:
    装饰器
"""
    
    with open(os.path.join(output_dir, "logging.md"), "w") as f:
        f.write(logging_doc)
    
    # 生成配置管理模块文档
    config_doc = """# config

配置管理模块

这个模块提供了超越态思维引擎的配置管理功能，包括配置加载、验证和合并等。

## Classes

### ConfigManager

配置管理器

#### Methods

##### __new__(cls)

单例模式

##### __init__()

初始化配置管理器

##### load_config(config_path: str, format: Optional[str] = None) -> ConfigType

加载配置

参数:
    config_path: 配置文件路径
    format: 配置文件格式，如果为None则根据文件扩展名自动判断
    
返回:
    配置字典

##### load_config_from_string(config_string: str, format: str) -> ConfigType

从字符串加载配置

参数:
    config_string: 配置字符串
    format: 配置字符串格式
    
返回:
    配置字典

##### load_config_from_env(prefix: str = "TTE_", separator: str = "__") -> ConfigType

从环境变量加载配置

参数:
    prefix: 环境变量前缀
    separator: 环境变量分隔符
    
返回:
    配置字典

##### set_config(config: ConfigType) -> None

设置配置

参数:
    config: 配置字典

##### get_config() -> ConfigType

获取配置

返回:
    配置字典

##### get_value(key: str, default: Any = None) -> Any

获取配置值

参数:
    key: 配置键，使用点号分隔嵌套键
    default: 默认值
    
返回:
    配置值

##### set_value(key: str, value: Any) -> None

设置配置值

参数:
    key: 配置键，使用点号分隔嵌套键
    value: 配置值

##### set_schema(schema: Dict[str, Any]) -> None

设置配置模式

参数:
    schema: 配置模式

##### get_schema() -> Dict[str, Any]

获取配置模式

返回:
    配置模式

##### watch(key: str, callback: Callable[[Any, Any], None]) -> str

监听配置变化

参数:
    key: 配置键，使用点号分隔嵌套键
    callback: 回调函数，接收旧值和新值
    
返回:
    监听器ID

##### unwatch(watcher_id: str) -> bool

取消监听配置变化

参数:
    watcher_id: 监听器ID
    
返回:
    是否成功取消

##### save_config(config_path: str, format: Optional[str] = None) -> None

保存配置

参数:
    config_path: 配置文件路径
    format: 配置文件格式，如果为None则根据文件扩展名自动判断

## Functions

### load_config(config_path: str, format: Optional[str] = None) -> ConfigType

加载配置

参数:
    config_path: 配置文件路径
    format: 配置文件格式，如果为None则根据文件扩展名自动判断
    
返回:
    配置字典

### load_config_from_string(config_string: str, format: str) -> ConfigType

从字符串加载配置

参数:
    config_string: 配置字符串
    format: 配置字符串格式
    
返回:
    配置字典

### load_config_from_env(prefix: str = "TTE_", separator: str = "__") -> ConfigType

从环境变量加载配置

参数:
    prefix: 环境变量前缀
    separator: 环境变量分隔符
    
返回:
    配置字典

### set_config(config: ConfigType) -> None

设置配置

参数:
    config: 配置字典

### get_config() -> ConfigType

获取配置

返回:
    配置字典

### get_value(key: str, default: Any = None) -> Any

获取配置值

参数:
    key: 配置键，使用点号分隔嵌套键
    default: 默认值
    
返回:
    配置值

### set_value(key: str, value: Any) -> None

设置配置值

参数:
    key: 配置键，使用点号分隔嵌套键
    value: 配置值

### set_schema(schema: Dict[str, Any]) -> None

设置配置模式

参数:
    schema: 配置模式

### get_schema() -> Dict[str, Any]

获取配置模式

返回:
    配置模式

### watch(key: str, callback: Callable[[Any, Any], None]) -> str

监听配置变化

参数:
    key: 配置键，使用点号分隔嵌套键
    callback: 回调函数，接收旧值和新值
    
返回:
    监听器ID

### unwatch(watcher_id: str) -> bool

取消监听配置变化

参数:
    watcher_id: 监听器ID
    
返回:
    是否成功取消

### save_config(config_path: str, format: Optional[str] = None) -> None

保存配置

参数:
    config_path: 配置文件路径
    format: 配置文件格式，如果为None则根据文件扩展名自动判断
"""
    
    with open(os.path.join(output_dir, "config.md"), "w") as f:
        f.write(config_doc)
    
    # 生成性能监控模块文档
    performance_doc = """# performance

性能监控模块

这个模块提供了超越态思维引擎的性能监控功能，包括CPU、内存、磁盘和网络使用情况等。

## Classes

### Metric

性能指标

#### Methods

##### __init__(name: str, value: float, unit: str = "", tags: Optional[Dict[str, str]] = None)

初始化性能指标

参数:
    name: 指标名称
    value: 指标值
    unit: 指标单位
    tags: 指标标签

##### __str__() -> str

返回性能指标的字符串表示

### PerformanceMonitor

性能监控器

#### Methods

##### __init__(name: str, tags: Optional[Dict[str, str]] = None)

初始化性能监控器

参数:
    name: 监控器名称
    tags: 监控器标签

##### __enter__() -> 'PerformanceMonitor'

进入上下文

##### __exit__(exc_type, exc_val, exc_tb) -> None

退出上下文

##### start() -> None

开始监控

##### stop() -> None

停止监控

##### add_metric(name: str, value: float, unit: str = "", tags: Optional[Dict[str, str]] = None) -> None

添加指标

参数:
    name: 指标名称
    value: 指标值
    unit: 指标单位
    tags: 指标标签

##### get_metrics() -> List[Metric]

获取指标

返回:
    指标列表

##### get_duration() -> Optional[float]

获取持续时间

返回:
    持续时间（秒）

### SystemPerformanceMonitor

系统性能监控器

#### Methods

##### __new__(cls)

单例模式

##### __init__()

初始化系统性能监控器

##### start(interval: float = 1.0) -> None

开始监控

参数:
    interval: 监控间隔（秒）

##### stop() -> None

停止监控

##### add_callback(callback: Callable[[List[Metric]], None]) -> None

添加回调函数

参数:
    callback: 回调函数，接收指标列表

##### remove_callback(callback: Callable[[List[Metric]], None]) -> bool

移除回调函数

参数:
    callback: 回调函数
    
返回:
    是否成功移除

##### get_metrics() -> List[Metric]

获取指标

返回:
    指标列表

## Functions

### monitor_performance(name: Optional[str] = None, tags: Optional[Dict[str, str]] = None) -> Callable[[F], F]

性能监控装饰器

参数:
    name: 监控器名称，如果为None则使用函数名
    tags: 监控器标签
    
返回:
    装饰器

### start_system_monitoring(interval: float = 1.0) -> None

开始系统性能监控

参数:
    interval: 监控间隔（秒）

### stop_system_monitoring() -> None

停止系统性能监控

### add_system_monitoring_callback(callback: Callable[[List[Metric]], None]) -> None

添加系统性能监控回调函数

参数:
    callback: 回调函数，接收指标列表

### remove_system_monitoring_callback(callback: Callable[[List[Metric]], None]) -> bool

移除系统性能监控回调函数

参数:
    callback: 回调函数
    
返回:
    是否成功移除

### get_system_metrics() -> List[Metric]

获取系统性能指标

返回:
    指标列表

### benchmark(func: Callable, *args, **kwargs) -> Dict[str, Any]

性能基准测试

参数:
    func: 测试函数
    *args: 函数参数
    **kwargs: 函数关键字参数
    
返回:
    基准测试结果
"""
    
    with open(os.path.join(output_dir, "performance.md"), "w") as f:
        f.write(performance_doc)
    
    # 生成工具模块文档
    doc = """# Utils

工具模块提供了超越态思维引擎的辅助功能，包括错误处理、日志记录、配置管理和性能监控等。

## Modules

- [errors](errors.md)
- [logging](logging.md)
- [config](config.md)
- [performance](performance.md)
"""
    
    with open(os.path.join(output_dir, "README.md"), "w") as f:
        f.write(doc)


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="Generate simple documentation")
    parser.add_argument("--output", "-o", default="docs/api/utils", help="Output directory")
    args = parser.parse_args()
    
    # 生成文档
    generate_utils_docs(args.output)


if __name__ == "__main__":
    main()
