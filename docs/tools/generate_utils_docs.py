"""
工具模块文档生成器

这个脚本生成超越态思维引擎的工具模块文档。
"""

import os
import sys
import inspect
import importlib
import argparse
from typing import Dict, Any, List, Set, Tuple, Optional, Union, Callable

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

# 直接导入工具模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../src/utils")))
import errors
import logging
import config
import performance


def generate_module_doc(module, module_name):
    """生成模块文档"""
    doc = f"# {module_name}\n\n"
    
    # 添加模块文档字符串
    doc += f"{module.__doc__ or ''}\n\n"
    
    # 添加类文档
    classes = {}
    for name, obj in inspect.getmembers(module, inspect.isclass):
        # 跳过私有类
        if name.startswith("_"):
            continue
        
        # 添加类
        classes[name] = obj
    
    if classes:
        doc += "## Classes\n\n"
        
        for name, cls in sorted(classes.items()):
            doc += f"### {name}\n\n"
            doc += f"{cls.__doc__ or ''}\n\n"
            
            # 添加方法文档
            methods = {}
            for method_name, method in inspect.getmembers(cls, inspect.isfunction):
                # 跳过私有方法
                if method_name.startswith("_") and method_name != "__init__":
                    continue
                
                # 添加方法
                methods[method_name] = method
            
            if methods:
                doc += "#### Methods\n\n"
                
                for method_name, method in sorted(methods.items()):
                    doc += f"##### {method_name}{str(inspect.signature(method))}\n\n"
                    doc += f"{method.__doc__ or ''}\n\n"
    
    # 添加函数文档
    functions = {}
    for name, obj in inspect.getmembers(module, inspect.isfunction):
        # 跳过私有函数
        if name.startswith("_"):
            continue
        
        # 添加函数
        functions[name] = obj
    
    if functions:
        doc += "## Functions\n\n"
        
        for name, func in sorted(functions.items()):
            doc += f"### {name}{str(inspect.signature(func))}\n\n"
            doc += f"{func.__doc__ or ''}\n\n"
    
    return doc


def generate_utils_docs(output_dir):
    """生成工具模块文档"""
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成错误处理模块文档
    errors_doc = generate_module_doc(errors, "errors")
    with open(os.path.join(output_dir, "errors.md"), "w") as f:
        f.write(errors_doc)
    
    # 生成日志记录模块文档
    logging_doc = generate_module_doc(logging, "logging")
    with open(os.path.join(output_dir, "logging.md"), "w") as f:
        f.write(logging_doc)
    
    # 生成配置管理模块文档
    config_doc = generate_module_doc(config, "config")
    with open(os.path.join(output_dir, "config.md"), "w") as f:
        f.write(config_doc)
    
    # 生成性能监控模块文档
    performance_doc = generate_module_doc(performance, "performance")
    with open(os.path.join(output_dir, "performance.md"), "w") as f:
        f.write(performance_doc)
    
    # 生成工具模块文档
    doc = "# Utils\n\n"
    
    # 添加模块文档
    doc += "工具模块提供了超越态思维引擎的辅助功能，包括错误处理、日志记录、配置管理和性能监控等。\n\n"
    
    # 添加子模块文档
    doc += "## Modules\n\n"
    doc += "- [errors](errors.md)\n"
    doc += "- [logging](logging.md)\n"
    doc += "- [config](config.md)\n"
    doc += "- [performance](performance.md)\n"
    
    # 写入文档
    with open(os.path.join(output_dir, "README.md"), "w") as f:
        f.write(doc)


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="Generate utils documentation")
    parser.add_argument("--output", "-o", default="docs/api/utils", help="Output directory")
    args = parser.parse_args()
    
    # 生成文档
    generate_utils_docs(args.output)


if __name__ == "__main__":
    main()
