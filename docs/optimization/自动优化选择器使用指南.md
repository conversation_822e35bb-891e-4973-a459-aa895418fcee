# 超越态张量框架 - 自动优化选择器使用指南

## 概述

自动优化选择器是超越态张量框架的核心性能优化组件，能够根据张量大小、硬件条件和计算需求，自动选择最佳的优化策略组合，包括内存池优化、SIMD加速和并行计算等。本指南将帮助您了解如何在不同场景下使用自动优化选择器，以获得最佳性能。

## 优化策略类型

自动优化选择器支持以下优化策略：

| 策略 | 描述 | 适用场景 |
|------|------|----------|
| `Standard` | 标准策略，不使用任何优化 | 小型张量、调试阶段 |
| `MemoryPool` | 内存池优化 | 频繁创建/销毁张量的场景 |
| `Simd` | SIMD指令集优化 | 大规模数值计算、向量化友好的操作 |
| `Parallel` | 并行计算优化 | 大型张量、多核CPU环境 |
| `MemoryPoolSimd` | 内存池 + SIMD优化 | 中等规模张量、计算密集型任务 |
| `MemoryPoolParallel` | 内存池 + 并行计算优化 | 大型张量、内存分配开销大的场景 |
| `SimdParallel` | SIMD + 并行计算优化 | 大型张量、计算密集型任务 |
| `Comprehensive` | 综合优化（内存池 + SIMD + 并行计算） | 超大型张量、性能关键型应用 |
| `Auto` | 自动选择最佳策略 | 一般场景的默认选择 |

## 使用方法

### 基本用法

```rust
use tte_operators::tensor::{
    TranscendentalTensor,
    auto_optimizer::{OptimizationStrategy, OptimizationConfig}
};

// 创建张量
let tensor = /* ... */;
let hamiltonian = /* ... */;

// 创建默认配置（使用自动策略选择）
let config = OptimizationConfig::default();

// 执行优化演化
let (evolved_tensor, profiling_result) = tensor.evolve_optimized(
    &hamiltonian, 
    0.01, // 时间步长
    config
)?;

// 如果启用了性能分析，可以查看结果
if let Some(profile) = profiling_result {
    println!("执行时间: {}ms", profile.execution_time_ms);
    println!("使用策略: {:?}", profile.strategy);
    if let Some(hit_rate) = profile.memory_pool_hit_rate {
        println!("内存池命中率: {:.2}%", hit_rate * 100.0);
    }
}
```

### 指定优化策略

如果您已经知道哪种优化策略最适合您的场景，可以直接指定：

```rust
let config = OptimizationConfig {
    strategy: OptimizationStrategy::MemoryPoolSimd,
    memory_pool_warm_up_size: 10,
    parallel_threads: 0, // 使用默认线程数
    enable_profiling: true,
};

let (evolved_tensor, _) = tensor.evolve_optimized(&hamiltonian, 0.01, config)?;
```

### 多次演化

对于需要多次演化的场景，可以使用`evolve_optimized_multiple`方法：

```rust
let (final_tensor, profile) = tensor.evolve_optimized_multiple(
    &hamiltonian,
    0.01,
    100, // 迭代次数
    config
)?;
```

## 优化配置参数

`OptimizationConfig`结构体包含以下配置参数：

- `strategy`: 优化策略
- `memory_pool_warm_up_size`: 内存池预热大小（如果使用内存池优化）
- `parallel_threads`: 并行计算线程数（0表示使用默认值）
- `enable_profiling`: 是否启用性能分析

## 性能分析结果

如果启用了性能分析（`enable_profiling = true`），`evolve_optimized`和`evolve_optimized_multiple`方法将返回`ProfilingResult`结构体，包含以下信息：

- `tensor_size`: 张量大小
- `execution_time_ms`: 执行时间（毫秒）
- `strategy`: 使用的优化策略
- `memory_pool_hit_rate`: 内存池命中率（如果使用了内存池优化）
- `memory_usage_bytes`: 内存使用量（字节）

## 最佳实践

1. **对于未知场景，使用自动策略**：
   ```rust
   let config = OptimizationConfig::default(); // 使用Auto策略
   ```

2. **对于小型张量（<1000个元素）**：
   ```rust
   let config = OptimizationConfig {
       strategy: OptimizationStrategy::MemoryPool,
       memory_pool_warm_up_size: 5,
       parallel_threads: 0,
       enable_profiling: false,
   };
   ```

3. **对于中型张量（1000-10000个元素）**：
   ```rust
   let config = OptimizationConfig {
       strategy: OptimizationStrategy::MemoryPoolSimd,
       memory_pool_warm_up_size: 10,
       parallel_threads: 0,
       enable_profiling: false,
   };
   ```

4. **对于大型张量（>10000个元素）**：
   ```rust
   let config = OptimizationConfig {
       strategy: OptimizationStrategy::Comprehensive,
       memory_pool_warm_up_size: 20,
       parallel_threads: num_cpus::get(),
       enable_profiling: false,
   };
   ```

5. **性能调优时启用分析**：
   ```rust
   let config = OptimizationConfig {
       strategy: OptimizationStrategy::Auto,
       memory_pool_warm_up_size: 10,
       parallel_threads: 0,
       enable_profiling: true,
   };
   ```

## 与MRCAM性能分析工具集成

自动优化选择器可以与MRCAM性能分析工具结合使用，进行更深入的性能分析：

```rust
// 在命令行中使用
$ cargo run --bin mrcam_analyzer -- --dimension computational --output report.json

// 在代码中使用
use tte_tools::mrcam_analyzer::{analyze_computational_performance, TestConfig};

let config = TestConfig::default();
let result = analyze_computational_performance(&config);
println!("平均加速比: {}", result.metrics["average_speedup"]);
```

## 故障排除

1. **性能不如预期**：
   - 检查张量大小是否适合选择的策略
   - 增加内存池预热大小
   - 检查硬件是否支持SIMD指令集

2. **内存使用过高**：
   - 减小内存池预热大小
   - 使用`MemoryPool`策略而非`Comprehensive`

3. **编译错误**：
   - 确保已启用相关特性（例如，对于SIMD优化，需要启用`simd`特性）

## 总结

自动优化选择器是超越态张量框架的强大功能，能够根据不同场景自动选择最佳优化策略，提高计算性能。通过合理配置和使用，您可以在不同规模的张量计算中获得最佳性能。
