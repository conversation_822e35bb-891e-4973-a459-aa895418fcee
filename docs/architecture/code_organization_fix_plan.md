# 代码组织修复计划

## 概述

根据代码组织检查的结果，我们发现了两个主要问题：

1. **依赖关系违规**：许多`/src/distributed`目录下的文件没有依赖于`/src/core/distributed`模块，这违反了我们的设计原则。

2. **代码重复**：有大量的代码重复，主要是在导入语句和基本结构上。

本文档提供了一个修复计划，解决这些问题。

## 1. 修复依赖关系违规

### 1.1 创建公共导入模块

创建一个公共导入模块，用于导入`/src/core/distributed`模块中的类和函数，以便`/src/distributed`目录下的文件可以使用它们。

```python
# /src/distributed/common/imports.py
"""
公共导入模块

本模块导入了/src/core/distributed模块中的类和函数，以便/src/distributed目录下的文件可以使用它们。
"""

from src.core.distributed import (
    # 节点相关
    NodeLevel, NodeRole, NodeState,
    BaseUnitNode, FractalDimensionNode, HighDimensionCoreNode,
    
    # 网络相关
    FractalNetwork, NetworkFactory,
    
    # 通信相关
    Message, Channel, CommunicationManager,
    
    # 同步相关
    DistributedLock, DistributedBarrier, SyncManager,
    
    # 容错相关
    FaultDetector, RecoveryStrategy, FaultManager,
    
    # 共识相关
    RaftNode, RaftCluster,
    
    # 事务相关
    TransactionManager, TransactionParticipant, TransactionCoordinator,
    
    # 任务相关
    DistributedTask, TaskScheduler,
    
    # 资源相关
    ResourceManager, AllocationStrategy,
    
    # 算法相关
    AlgorithmRegistry, AlgorithmExecutor, DistributedAlgorithmAdapter
)
```

### 1.2 修改`/src/distributed`目录下的文件

修改`/src/distributed`目录下的文件，使其导入公共导入模块，以便使用`/src/core/distributed`模块中的类和函数。

例如，修改`/src/distributed/performance/monitoring/performance_monitor.py`文件：

```python
"""
性能监控系统

本模块实现了性能监控系统，用于监控分布式系统的性能。
"""

import logging
import time
import threading
from typing import Dict, List, Optional, Set, Tuple, Union, Any

# 导入核心分布式系统模块
from src.distributed.common.imports import (
    NodeLevel, NodeRole, NodeState,
    BaseUnitNode, FractalDimensionNode, HighDimensionCoreNode,
    FractalNetwork, NetworkFactory,
    Message, Channel, CommunicationManager,
    DistributedLock, DistributedBarrier, SyncManager,
    FaultDetector, RecoveryStrategy, FaultManager,
    RaftNode, RaftCluster,
    TransactionManager, TransactionParticipant, TransactionCoordinator,
    DistributedTask, TaskScheduler,
    ResourceManager, AllocationStrategy,
    AlgorithmRegistry, AlgorithmExecutor, DistributedAlgorithmAdapter
)

# 其他导入...

class PerformanceMonitor:
    """性能监控系统"""
    
    def __init__(self, network: FractalNetwork):
        """
        初始化性能监控系统
        
        参数:
            network: 分形网络
        """
        self.network = network
        # 其他初始化...
```

### 1.3 创建脚本自动修复依赖关系违规

创建一个脚本，自动修复依赖关系违规：

```python
#!/usr/bin/env python3
"""
修复依赖关系违规脚本

本脚本用于修复依赖关系违规，自动添加对/src/core/distributed模块的导入。
"""

import os
import re
import sys
import ast
from typing import List, Set, Dict, Any

def fix_dependency_violations(file_path: str) -> None:
    """
    修复依赖关系违规
    
    参数:
        file_path: 文件路径
    """
    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()
    
    # 检查是否已经导入了核心分布式系统模块
    if "from src.core.distributed import" in content or "from src.distributed.common.imports import" in content:
        return
    
    # 添加导入语句
    import_statement = """
# 导入核心分布式系统模块
from src.distributed.common.imports import (
    NodeLevel, NodeRole, NodeState,
    BaseUnitNode, FractalDimensionNode, HighDimensionCoreNode,
    FractalNetwork, NetworkFactory,
    Message, Channel, CommunicationManager,
    DistributedLock, DistributedBarrier, SyncManager,
    FaultDetector, RecoveryStrategy, FaultManager,
    RaftNode, RaftCluster,
    TransactionManager, TransactionParticipant, TransactionCoordinator,
    DistributedTask, TaskScheduler,
    ResourceManager, AllocationStrategy,
    AlgorithmRegistry, AlgorithmExecutor, DistributedAlgorithmAdapter
)
"""
    
    # 查找导入语句的位置
    import_match = re.search(r"import\s+[a-zA-Z0-9_]+", content)
    if import_match:
        # 在最后一个导入语句后添加导入语句
        last_import_pos = content.rfind("import")
        last_import_end = content.find("\n", last_import_pos)
        if last_import_end != -1:
            content = content[:last_import_end+1] + import_statement + content[last_import_end+1:]
        else:
            content = content + import_statement
    else:
        # 在文件开头添加导入语句
        content = import_statement + content
    
    # 写入文件
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(content)

def main() -> None:
    """主函数"""
    # 获取依赖关系违规的文件列表
    with open("dependency_violations.txt", "r", encoding="utf-8") as f:
        violations = f.readlines()
    
    # 修复依赖关系违规
    for violation in violations:
        file_path = violation.strip().split(":")[0]
        if os.path.exists(file_path):
            print(f"修复依赖关系违规: {file_path}")
            fix_dependency_violations(file_path)

if __name__ == "__main__":
    main()
```

## 2. 修复代码重复

### 2.1 创建公共模块

创建一个公共模块，用于存放重复的代码：

```python
# /src/utils/common.py
"""
公共模块

本模块包含了一些公共的函数和类，用于减少代码重复。
"""

import logging
import time
import threading
from typing import Dict, List, Optional, Set, Tuple, Union, Any

def setup_logger(name: str, level: int = logging.INFO) -> logging.Logger:
    """
    设置日志记录器
    
    参数:
        name: 日志记录器名称
        level: 日志级别
        
    返回:
        logging.Logger: 日志记录器
    """
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    
    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(console_handler)
    
    return logger

def get_timestamp() -> float:
    """
    获取当前时间戳
    
    返回:
        float: 当前时间戳
    """
    return time.time()

def generate_id() -> str:
    """
    生成唯一ID
    
    返回:
        str: 唯一ID
    """
    import uuid
    return str(uuid.uuid4())

class ThreadSafeDict(dict):
    """线程安全的字典"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._lock = threading.RLock()
    
    def __getitem__(self, key):
        with self._lock:
            return super().__getitem__(key)
    
    def __setitem__(self, key, value):
        with self._lock:
            return super().__setitem__(key, value)
    
    def __delitem__(self, key):
        with self._lock:
            return super().__delitem__(key)
    
    def get(self, key, default=None):
        with self._lock:
            return super().get(key, default)
    
    def pop(self, key, default=None):
        with self._lock:
            return super().pop(key, default)
    
    def update(self, *args, **kwargs):
        with self._lock:
            return super().update(*args, **kwargs)
    
    def clear(self):
        with self._lock:
            return super().clear()
    
    def copy(self):
        with self._lock:
            return ThreadSafeDict(super().copy())
    
    def items(self):
        with self._lock:
            return list(super().items())
    
    def keys(self):
        with self._lock:
            return list(super().keys())
    
    def values(self):
        with self._lock:
            return list(super().values())

class ThreadSafeSet(set):
    """线程安全的集合"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._lock = threading.RLock()
    
    def add(self, item):
        with self._lock:
            return super().add(item)
    
    def remove(self, item):
        with self._lock:
            return super().remove(item)
    
    def discard(self, item):
        with self._lock:
            return super().discard(item)
    
    def pop(self):
        with self._lock:
            return super().pop()
    
    def clear(self):
        with self._lock:
            return super().clear()
    
    def update(self, *args, **kwargs):
        with self._lock:
            return super().update(*args, **kwargs)
    
    def copy(self):
        with self._lock:
            return ThreadSafeSet(super().copy())
    
    def __iter__(self):
        with self._lock:
            return iter(list(super().__iter__()))
    
    def __len__(self):
        with self._lock:
            return super().__len__()
    
    def __contains__(self, item):
        with self._lock:
            return super().__contains__(item)
```

### 2.2 修改重复代码

修改重复代码，使用公共模块中的函数和类：

```python
# 修改前
import logging
import time
import threading
from typing import Dict, List, Optional, Set, Tuple, Union, Any

logger = logging.getLogger(__name__)

class SomeClass:
    def __init__(self):
        self.id = str(uuid.uuid4())
        self.created_at = time.time()
        self.data = {}
        self._lock = threading.RLock()
    
    def add_item(self, key, value):
        with self._lock:
            self.data[key] = value
    
    def get_item(self, key):
        with self._lock:
            return self.data.get(key)
```

```python
# 修改后
from src.utils.common import setup_logger, get_timestamp, generate_id, ThreadSafeDict

logger = setup_logger(__name__)

class SomeClass:
    def __init__(self):
        self.id = generate_id()
        self.created_at = get_timestamp()
        self.data = ThreadSafeDict()
    
    def add_item(self, key, value):
        self.data[key] = value
    
    def get_item(self, key):
        return self.data.get(key)
```

### 2.3 创建脚本自动修复代码重复

创建一个脚本，自动修复代码重复：

```python
#!/usr/bin/env python3
"""
修复代码重复脚本

本脚本用于修复代码重复，自动替换重复的代码。
"""

import os
import re
import sys
import ast
from typing import List, Set, Dict, Any

def fix_code_duplication(file_path: str) -> None:
    """
    修复代码重复
    
    参数:
        file_path: 文件路径
    """
    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()
    
    # 替换导入语句
    content = re.sub(
        r"import\s+logging\s*\nimport\s+time\s*\nimport\s+threading\s*\nimport\s+uuid\s*\nfrom\s+typing\s+import.*?\n",
        "from src.utils.common import setup_logger, get_timestamp, generate_id, ThreadSafeDict, ThreadSafeSet\n",
        content
    )
    
    # 替换日志记录器
    content = re.sub(
        r"logger\s*=\s*logging\.getLogger\(__name__\)",
        "logger = setup_logger(__name__)",
        content
    )
    
    # 替换时间戳
    content = re.sub(
        r"time\.time\(\)",
        "get_timestamp()",
        content
    )
    
    # 替换UUID
    content = re.sub(
        r"str\(uuid\.uuid4\(\)\)",
        "generate_id()",
        content
    )
    
    # 替换线程安全的字典
    content = re.sub(
        r"self\._lock\s*=\s*threading\.RLock\(\)",
        "",
        content
    )
    
    content = re.sub(
        r"self\.data\s*=\s*\{\}",
        "self.data = ThreadSafeDict()",
        content
    )
    
    content = re.sub(
        r"with\s+self\._lock:\s*\n\s*self\.data\[([^\]]+)\]\s*=\s*([^\n]+)",
        r"self.data[\1] = \2",
        content
    )
    
    content = re.sub(
        r"with\s+self\._lock:\s*\n\s*return\s+self\.data\.get\(([^)]+)\)",
        r"return self.data.get(\1)",
        content
    )
    
    # 写入文件
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(content)

def main() -> None:
    """主函数"""
    # 获取代码重复的文件列表
    with open("code_duplication.txt", "r", encoding="utf-8") as f:
        duplications = f.readlines()
    
    # 修复代码重复
    for duplication in duplications:
        file_path = duplication.strip().split(":")[0]
        if os.path.exists(file_path):
            print(f"修复代码重复: {file_path}")
            fix_code_duplication(file_path)

if __name__ == "__main__":
    main()
```

## 3. 执行计划

1. 创建公共导入模块
2. 创建公共模块
3. 运行修复依赖关系违规脚本
4. 运行修复代码重复脚本
5. 手动检查修复结果
6. 运行测试，确保修复没有引入新的问题
7. 提交修复结果

## 4. 预期结果

1. 所有`/src/distributed`目录下的文件都依赖于`/src/core/distributed`模块
2. 代码重复大幅减少
3. 代码更加清晰、可维护
4. 测试通过，没有引入新的问题
