# GPU加速向量运算算子使用指南

本文档介绍了如何使用GPU加速向量运算算子。

## 简介

GPU加速向量运算算子是超越态思维引擎中的一个重要组件，它利用GPU加速技术提高向量运算的性能。该算子支持多种向量运算，包括向量点积、向量加法、向量减法、向量元素乘法、向量元素除法、向量范数、向量归一化、向量距离、向量余弦相似度、向量叉积、向量投影等。

## 安装

GPU加速向量运算算子已经集成到超越态思维引擎中，无需单独安装。但是，为了使用GPU加速功能，您需要确保系统满足以下要求：

1. 安装了支持CUDA、ROCm、OpenCL或WebGPU的GPU
2. 安装了相应的GPU驱动程序和开发工具包
3. 安装了PyO3和相关的Rust依赖项

如果您的系统没有GPU或者不满足上述要求，算子会自动回退到CPU模式，或者使用模拟GPU加速器进行测试。

## 配置

您可以通过环境变量配置GPU加速向量运算算子的行为：

```bash
# 启用/禁用GPU加速
export TTE_GPU_ACCELERATION=1  # 1表示启用，0表示禁用

# 使用模拟GPU
export TTE_USE_MOCK_GPU=1  # 1表示使用模拟GPU，0表示不使用

# 指定GPU后端
export TTE_GPU_BACKEND=cuda  # 可选值：auto, cuda, rocm, opencl, webgpu, mock, cpu

# 启用/禁用调试模式
export TTE_DEBUG=0  # 1表示启用，0表示禁用

# 启用/禁用性能分析
export TTE_PROFILE=0  # 1表示启用，0表示禁用

# 启用/禁用内存优化
export TTE_MEMORY_OPTIMIZATION=1  # 1表示启用，0表示禁用

# 启用/禁用自动调优
export TTE_AUTO_TUNING=1  # 1表示启用，0表示禁用

# 启用/禁用批处理
export TTE_BATCH_PROCESSING=1  # 1表示启用，0表示禁用

# 设置批处理大小
export TTE_BATCH_SIZE=64

# 启用/禁用多线程
export TTE_MULTITHREADING=1  # 1表示启用，0表示禁用

# 设置线程数（0表示自动）
export TTE_NUM_THREADS=0

# 启用/禁用SIMD加速
export TTE_SIMD_ACCELERATION=1  # 1表示启用，0表示禁用

# 启用/禁用混合精度
export TTE_MIXED_PRECISION=0  # 1表示启用，0表示禁用

# 启用/禁用缓存
export TTE_CACHE_ENABLED=1  # 1表示启用，0表示禁用

# 设置缓存大小（MB）
export TTE_CACHE_SIZE=1024

# 启用/禁用自动设备选择
export TTE_AUTO_DEVICE_SELECTION=1  # 1表示启用，0表示禁用

# 设置设备ID
export TTE_DEVICE_ID=0
```

## 基本用法

以下是使用GPU加速向量运算算子的基本示例：

```python
import numpy as np
from src.operators import operator_registry

# 获取向量运算算子
vector_class = operator_registry.get_operator("gpu_acceleration", "vector")

# 创建配置
config = vector_class.PyVectorConfig(
    operation_type="dot_product",  # 可选值：dot_product, add, subtract, element_wise_multiply, element_wise_divide, norm, normalize, distance, cosine_similarity, cross_product, projection, concatenate, slice, sum, mean, std, max, min, argmax, argmin, sort, linear_combination, outer_product
    use_mixed_precision=False,
    use_multithreading=True,
    compute_device="cuda",  # 可选值：cpu, cuda, rocm, opencl, webgpu
    use_simd=True,
    batch_size=64
)

# 创建向量运算算子实例
vector_operator = vector_class.PyVectorOperator(config)

# 创建向量
a = [1.0, 2.0, 3.0, 4.0, 5.0]
b = [5.0, 4.0, 3.0, 2.0, 1.0]

# 执行向量点积
result = vector_operator.execute_operation(a, b)

# 获取结果
result_value = result.get_result()
print("结果:", result_value)

# 获取性能指标
performance_metrics = result.get_performance_metrics()
print("性能指标:", performance_metrics)
```

## 高级用法

### 使用模拟GPU加速器

如果您的系统没有GPU，或者您想在没有GPU的环境中测试GPU加速功能，可以使用模拟GPU加速器：

```python
import os
import numpy as np
from src.operators import operator_registry
from src.operators.gpu_acceleration.mock_gpu_adapter import create_mock_gpu_adapter

# 启用模拟GPU
os.environ["TTE_USE_MOCK_GPU"] = "1"

# 创建模拟GPU加速器适配器
mock_gpu_adapter = create_mock_gpu_adapter()

# 获取向量运算算子
vector_class = operator_registry.get_operator("gpu_acceleration", "vector")

# 创建配置
config = vector_class.PyVectorConfig(
    operation_type="dot_product",
    use_mixed_precision=False,
    use_multithreading=True,
    compute_device="cuda",  # 使用模拟CUDA
    use_simd=True,
    batch_size=64
)

# 创建向量运算算子实例
vector_operator = vector_class.PyVectorOperator(config)

# 创建向量
a = [1.0, 2.0, 3.0, 4.0, 5.0]
b = [5.0, 4.0, 3.0, 2.0, 1.0]

# 执行向量点积
result = vector_operator.execute_operation(a, b)

# 获取结果
result_value = result.get_result()
print("结果:", result_value)

# 关闭模拟GPU加速器适配器
mock_gpu_adapter.close()
```

### 性能测试

您可以使用以下代码测试GPU加速向量运算算子的性能：

```python
import time
import numpy as np
import matplotlib.pyplot as plt
from src.operators import operator_registry

def generate_random_vector(n):
    """生成随机向量"""
    return np.random.rand(n).tolist()

def benchmark_vector_operation(a, b, operation_type, use_gpu=False):
    """测试向量运算算子性能"""
    # 获取向量运算算子
    vector_class = operator_registry.get_operator("gpu_acceleration", "vector")
    
    # 创建配置
    config = vector_class.PyVectorConfig(
        operation_type=operation_type,
        use_mixed_precision=False,
        use_multithreading=True,
        compute_device="cuda" if use_gpu else "cpu",
        use_simd=True,
        batch_size=64
    )
    
    # 创建向量运算算子实例
    vector_operator = vector_class.PyVectorOperator(config)
    
    # 执行向量运算
    start_time = time.time()
    result = vector_operator.execute_operation(a, b)
    elapsed_time = time.time() - start_time
    
    return {
        "elapsed_time": elapsed_time,
        "result": result
    }

# 运行性能测试
operation_types = ["dot_product", "add", "subtract", "element_wise_multiply", "element_wise_divide", "norm", "normalize", "distance", "cosine_similarity"]
vector_sizes = [10, 100, 1000, 10000, 100000, 1000000]

results = {
    "cpu": {op: [] for op in operation_types},
    "gpu": {op: [] for op in operation_types}
}

for n in vector_sizes:
    print(f"测试向量大小: {n}")
    
    # 生成随机向量
    a = generate_random_vector(n)
    b = generate_random_vector(n)
    
    for operation_type in operation_types:
        print(f"  测试操作类型: {operation_type}")
        
        # 测试CPU版本
        print("    测试CPU版本...")
        cpu_result = benchmark_vector_operation(a, b, operation_type, use_gpu=False)
        results["cpu"][operation_type].append({
            "n": n,
            "elapsed_time": cpu_result["elapsed_time"]
        })
        print(f"    CPU版本耗时: {cpu_result['elapsed_time']:.4f}秒")
        
        # 测试GPU版本
        print("    测试GPU版本...")
        gpu_result = benchmark_vector_operation(a, b, operation_type, use_gpu=True)
        results["gpu"][operation_type].append({
            "n": n,
            "elapsed_time": gpu_result["elapsed_time"]
        })
        print(f"    GPU版本耗时: {gpu_result['elapsed_time']:.4f}秒")
        
        # 计算加速比
        speedup = cpu_result["elapsed_time"] / gpu_result["elapsed_time"]
        print(f"    加速比: {speedup:.2f}x")
        
        print()

# 绘制结果
fig, axes = plt.subplots(3, 3, figsize=(15, 15))
axes = axes.flatten()

for i, operation_type in enumerate(operation_types):
    ax = axes[i]
    
    # 提取数据
    cpu_data = results["cpu"][operation_type]
    gpu_data = results["gpu"][operation_type]
    
    n_values = [d["n"] for d in cpu_data]
    cpu_times = [d["elapsed_time"] for d in cpu_data]
    gpu_times = [d["elapsed_time"] for d in gpu_data]
    
    # 计算加速比
    speedups = [c / g for c, g in zip(cpu_times, gpu_times)]
    
    # 绘制时间对比
    ax.plot(n_values, cpu_times, 'o-', label='CPU')
    ax.plot(n_values, gpu_times, 's-', label='GPU')
    
    # 添加标签和标题
    ax.set_xlabel('向量大小')
    ax.set_ylabel('执行时间 (秒)')
    ax.set_title(f'{operation_type}')
    ax.legend()
    ax.grid(True)
    ax.set_xscale('log')
    ax.set_yscale('log')
    
    # 添加加速比文本
    for j, (x, y, s) in enumerate(zip(n_values, gpu_times, speedups)):
        ax.annotate(f'{s:.2f}x', (x, y), textcoords="offset points", 
                    xytext=(0, 10), ha='center')

plt.tight_layout()
plt.savefig('gpu_vector_benchmark_results.png')
plt.show()
```

## 算法说明

GPU加速向量运算算子支持以下向量运算：

### 向量点积（Dot Product）

向量点积是两个向量对应元素相乘后求和的结果。如果向量a和b的维度都为n，则点积a·b = Σ(a[i] * b[i])，i从0到n-1。

### 向量加法（Vector Addition）

向量加法是将两个向量对应元素相加。如果向量a和b的维度都为n，则向量加法c = a + b的维度也为n，其中c[i] = a[i] + b[i]。

### 向量减法（Vector Subtraction）

向量减法是将两个向量对应元素相减。如果向量a和b的维度都为n，则向量减法c = a - b的维度也为n，其中c[i] = a[i] - b[i]。

### 向量元素乘法（Element-wise Multiplication）

向量元素乘法是将两个向量对应元素相乘。如果向量a和b的维度都为n，则向量元素乘法c = a ⊙ b的维度也为n，其中c[i] = a[i] * b[i]。

### 向量元素除法（Element-wise Division）

向量元素除法是将两个向量对应元素相除。如果向量a和b的维度都为n，则向量元素除法c = a ⊘ b的维度也为n，其中c[i] = a[i] / b[i]。

### 向量范数（Vector Norm）

向量范数是衡量向量大小的度量。常见的范数包括：
- L1范数（曼哈顿范数）：||a||₁ = Σ|a[i]|
- L2范数（欧几里得范数）：||a||₂ = √(Σ(a[i]²))
- L∞范数（最大范数）：||a||∞ = max|a[i]|
- Lp范数：||a||ₚ = (Σ|a[i]|ᵖ)^(1/p)

### 向量归一化（Vector Normalization）

向量归一化是将向量缩放为单位长度的过程。归一化后的向量a' = a / ||a||，其中||a||是向量a的范数。

### 向量距离（Vector Distance）

向量距离是衡量两个向量之间差异的度量。常见的距离包括：
- L1距离（曼哈顿距离）：d₁(a, b) = Σ|a[i] - b[i]|
- L2距离（欧几里得距离）：d₂(a, b) = √(Σ((a[i] - b[i])²))
- L∞距离（切比雪夫距离）：d∞(a, b) = max|a[i] - b[i]|
- Lp距离：dₚ(a, b) = (Σ|a[i] - b[i]|ᵖ)^(1/p)

### 向量余弦相似度（Cosine Similarity）

向量余弦相似度是衡量两个向量方向相似度的度量。余弦相似度cos(a, b) = (a·b) / (||a|| * ||b||)，其中a·b是向量a和b的点积，||a||和||b||分别是向量a和b的L2范数。

### 向量叉积（Cross Product）

向量叉积是一种特殊的向量乘法，仅适用于三维向量。如果向量a = [a₁, a₂, a₃]和b = [b₁, b₂, b₃]，则叉积a × b = [a₂b₃ - a₃b₂, a₃b₁ - a₁b₃, a₁b₂ - a₂b₁]。

### 向量投影（Vector Projection）

向量投影是将一个向量投影到另一个向量上的结果。向量a在向量b上的投影proj_b(a) = (a·b / b·b) * b，其中a·b是向量a和b的点积，b·b是向量b的自点积。

### 向量拼接（Vector Concatenation）

向量拼接是将两个向量合并成一个更长的向量。如果向量a的维度为m，向量b的维度为n，则拼接后的向量c = [a₁, a₂, ..., aₘ, b₁, b₂, ..., bₙ]的维度为m+n。

### 向量切片（Vector Slice）

向量切片是从向量中提取一部分元素。如果向量a = [a₁, a₂, ..., aₙ]，则切片a[i:j] = [aᵢ₊₁, aᵢ₊₂, ..., aⱼ]。

### 向量求和（Vector Sum）

向量求和是计算向量所有元素的和。如果向量a = [a₁, a₂, ..., aₙ]，则求和sum(a) = a₁ + a₂ + ... + aₙ。

### 向量均值（Vector Mean）

向量均值是计算向量所有元素的平均值。如果向量a = [a₁, a₂, ..., aₙ]，则均值mean(a) = (a₁ + a₂ + ... + aₙ) / n。

### 向量标准差（Vector Standard Deviation）

向量标准差是衡量向量元素分散程度的度量。如果向量a = [a₁, a₂, ..., aₙ]，则标准差std(a) = √((Σ(aᵢ - mean(a))²) / n)，其中mean(a)是向量a的均值。

### 向量最大值（Vector Maximum）

向量最大值是向量中的最大元素。如果向量a = [a₁, a₂, ..., aₙ]，则最大值max(a) = max(a₁, a₂, ..., aₙ)。

### 向量最小值（Vector Minimum）

向量最小值是向量中的最小元素。如果向量a = [a₁, a₂, ..., aₙ]，则最小值min(a) = min(a₁, a₂, ..., aₙ)。

### 向量最大值索引（Vector Argmax）

向量最大值索引是向量中最大元素的索引。如果向量a = [a₁, a₂, ..., aₙ]，则最大值索引argmax(a) = i，使得aᵢ = max(a₁, a₂, ..., aₙ)。

### 向量最小值索引（Vector Argmin）

向量最小值索引是向量中最小元素的索引。如果向量a = [a₁, a₂, ..., aₙ]，则最小值索引argmin(a) = i，使得aᵢ = min(a₁, a₂, ..., aₙ)。

### 向量排序（Vector Sort）

向量排序是将向量元素按照升序或降序排列。如果向量a = [a₁, a₂, ..., aₙ]，则升序排序sort(a) = [a'₁, a'₂, ..., a'ₙ]，使得a'₁ ≤ a'₂ ≤ ... ≤ a'ₙ。

### 向量线性组合（Vector Linear Combination）

向量线性组合是将两个向量按照一定权重组合的结果。如果向量a和b的维度都为n，则线性组合c = αa + βb的维度也为n，其中c[i] = αa[i] + βb[i]，α和β是标量权重。

### 向量外积（Vector Outer Product）

向量外积是两个向量的张量积。如果向量a的维度为m，向量b的维度为n，则外积a ⊗ b是一个m×n的矩阵，其中(a ⊗ b)[i,j] = a[i] * b[j]。

## 性能优化

GPU加速向量运算算子使用以下技术优化性能：

1. **GPU加速**：利用GPU的并行计算能力加速向量运算
2. **多线程**：在CPU模式下使用多线程并行计算
3. **SIMD加速**：在支持的CPU上使用SIMD指令集加速向量运算
4. **批处理**：将大型向量分解为多个批次进行处理，减少内存占用
5. **缓存**：缓存中间结果，避免重复计算
6. **自动调优**：根据向量大小和硬件环境自动调整算法参数

## 常见问题

### Q: 如何选择合适的向量运算算法？

A: 不同的向量运算算法适用于不同的场景：
- 对于小型向量，CPU版本可能比GPU版本更快
- 对于大型向量，GPU版本通常比CPU版本快得多
- 对于特定的向量运算（如点积、范数等），可以使用专门的算法提高性能

### Q: 为什么GPU版本在小型向量上可能比CPU版本慢？

A: GPU加速在处理大型向量时效果最佳。对于小型向量，GPU初始化和数据传输的开销可能超过并行计算带来的性能提升。

### Q: 如何处理超大型向量？

A: 对于超大型向量，可以：
1. 增加批处理大小（`batch_size`）
2. 使用分布式计算（设置`TTE_DISTRIBUTED_COMPUTE=1`）
3. 使用远程计算（设置`TTE_REMOTE_COMPUTE=1`）

### Q: 如何解决"内存不足"错误？

A: 如果遇到内存不足错误，可以：
1. 启用内存优化（设置`TTE_MEMORY_OPTIMIZATION=1`）
2. 减小批处理大小（设置`TTE_BATCH_SIZE`为较小的值）
3. 使用混合精度（设置`TTE_MIXED_PRECISION=1`）

## 参考资料

- [CUDA向量运算文档](https://docs.nvidia.com/cuda/cublas/index.html)
- [GPU加速线性代数](https://developer.nvidia.com/blog/accelerating-linear-algebra-with-cuda/)
- [并行向量运算算法](https://arxiv.org/abs/1305.2076)
- [SIMD指令集加速](https://software.intel.com/content/www/us/en/develop/articles/introduction-to-intel-advanced-vector-extensions.html)
