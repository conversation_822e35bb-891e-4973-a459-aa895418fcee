# GPU加速中心节点识别算子使用指南

本文档介绍了如何使用GPU加速中心节点识别算子。

## 简介

GPU加速中心节点识别算子是超越态思维引擎中的一个重要组件，它利用GPU加速技术提高中心节点识别算法的性能。该算子支持多种中心性算法，包括度中心性、接近中心性、介数中心性、特征向量中心性、PageRank中心性和Katz中心性。

## 安装

GPU加速中心节点识别算子已经集成到超越态思维引擎中，无需单独安装。但是，为了使用GPU加速功能，您需要确保系统满足以下要求：

1. 安装了支持CUDA、ROCm、OpenCL或WebGPU的GPU
2. 安装了相应的GPU驱动程序和开发工具包
3. 安装了PyO3和相关的Rust依赖项

如果您的系统没有GPU或者不满足上述要求，算子会自动回退到CPU模式，或者使用模拟GPU加速器进行测试。

## 配置

您可以通过环境变量配置GPU加速中心节点识别算子的行为：

```bash
# 启用/禁用GPU加速
export TTE_GPU_ACCELERATION=1  # 1表示启用，0表示禁用

# 使用模拟GPU
export TTE_USE_MOCK_GPU=1  # 1表示使用模拟GPU，0表示不使用

# 指定GPU后端
export TTE_GPU_BACKEND=cuda  # 可选值：auto, cuda, rocm, opencl, webgpu, mock, cpu

# 启用/禁用调试模式
export TTE_DEBUG=0  # 1表示启用，0表示禁用

# 启用/禁用性能分析
export TTE_PROFILE=0  # 1表示启用，0表示禁用

# 启用/禁用内存优化
export TTE_MEMORY_OPTIMIZATION=1  # 1表示启用，0表示禁用

# 启用/禁用自动调优
export TTE_AUTO_TUNING=1  # 1表示启用，0表示禁用

# 启用/禁用批处理
export TTE_BATCH_PROCESSING=1  # 1表示启用，0表示禁用

# 设置批处理大小
export TTE_BATCH_SIZE=64

# 启用/禁用多线程
export TTE_MULTITHREADING=1  # 1表示启用，0表示禁用

# 设置线程数（0表示自动）
export TTE_NUM_THREADS=0

# 启用/禁用SIMD加速
export TTE_SIMD_ACCELERATION=1  # 1表示启用，0表示禁用

# 启用/禁用稀疏矩阵
export TTE_SPARSE_MATRIX=1  # 1表示启用，0表示禁用

# 启用/禁用混合精度
export TTE_MIXED_PRECISION=0  # 1表示启用，0表示禁用

# 启用/禁用缓存
export TTE_CACHE_ENABLED=1  # 1表示启用，0表示禁用

# 设置缓存大小（MB）
export TTE_CACHE_SIZE=1024

# 启用/禁用自动设备选择
export TTE_AUTO_DEVICE_SELECTION=1  # 1表示启用，0表示禁用

# 设置设备ID
export TTE_DEVICE_ID=0
```

## 基本用法

以下是使用GPU加速中心节点识别算子的基本示例：

```python
import numpy as np
from src.operators import operator_registry

# 获取中心性算子
centrality_class = operator_registry.get_operator("gpu_acceleration", "centrality")

# 创建配置
config = centrality_class.PyCentralityConfig(
    centrality_type="pagerank",  # 可选值：degree, closeness, betweenness, eigenvector, pagerank, katz
    use_edge_weights=True,
    normalize=True,
    max_iterations=100,
    convergence_threshold=1e-6,
    damping_factor=0.85,
    use_multithreading=True,
    compute_device="cuda",  # 可选值：cpu, cuda, rocm, opencl, webgpu
    use_simd=True,
    use_sparse_matrix=True,
    batch_size=64
)

# 创建中心性算子实例
centrality_operator = centrality_class.PyCentralityOperator(config)

# 创建网络结构
nodes = {
    "A": {"B": 1.0, "C": 0.5, "D": 0.8},
    "B": {"A": 1.0, "C": 0.7, "E": 0.9},
    "C": {"A": 0.5, "B": 0.7, "D": 0.6, "E": 0.5},
    "D": {"A": 0.8, "C": 0.6, "E": 0.7, "F": 1.0},
    "E": {"B": 0.9, "C": 0.5, "D": 0.7, "F": 0.8},
    "F": {"D": 1.0, "E": 0.8}
}

# 计算中心性
result = centrality_operator.calculate_centrality(nodes)

# 获取中心性值
centrality_values = result.get_centrality_values()
print("中心性值:", centrality_values)

# 获取排序后的节点
sorted_nodes = result.get_sorted_nodes()
print("排序后的节点:")
for i, node_data in enumerate(sorted_nodes):
    node_id = node_data["node_id"]
    value = node_data["value"]
    print(f"{i+1}. {node_id}: {value:.6f}")

# 获取性能指标
performance_metrics = result.get_performance_metrics()
print("性能指标:", performance_metrics)
```

## 高级用法

### 使用模拟GPU加速器

如果您的系统没有GPU，或者您想在没有GPU的环境中测试GPU加速功能，可以使用模拟GPU加速器：

```python
import os
import numpy as np
from src.operators import operator_registry
from src.operators.gpu_acceleration.mock_gpu_adapter import create_mock_gpu_adapter

# 启用模拟GPU
os.environ["TTE_USE_MOCK_GPU"] = "1"

# 创建模拟GPU加速器适配器
mock_gpu_adapter = create_mock_gpu_adapter()

# 获取中心性算子
centrality_class = operator_registry.get_operator("gpu_acceleration", "centrality")

# 创建配置
config = centrality_class.PyCentralityConfig(
    centrality_type="pagerank",
    use_edge_weights=True,
    normalize=True,
    max_iterations=100,
    convergence_threshold=1e-6,
    damping_factor=0.85,
    use_multithreading=True,
    compute_device="cuda",  # 使用模拟CUDA
    use_simd=True,
    use_sparse_matrix=True,
    batch_size=64
)

# 创建中心性算子实例
centrality_operator = centrality_class.PyCentralityOperator(config)

# 创建网络结构
nodes = {
    "A": {"B": 1.0, "C": 0.5, "D": 0.8},
    "B": {"A": 1.0, "C": 0.7, "E": 0.9},
    "C": {"A": 0.5, "B": 0.7, "D": 0.6, "E": 0.5},
    "D": {"A": 0.8, "C": 0.6, "E": 0.7, "F": 1.0},
    "E": {"B": 0.9, "C": 0.5, "D": 0.7, "F": 0.8},
    "F": {"D": 1.0, "E": 0.8}
}

# 计算中心性
result = centrality_operator.calculate_centrality(nodes)

# 获取中心性值
centrality_values = result.get_centrality_values()
print("中心性值:", centrality_values)

# 关闭模拟GPU加速器适配器
mock_gpu_adapter.close()
```

### 性能测试

您可以使用以下代码测试GPU加速中心节点识别算子的性能：

```python
import time
import random
import numpy as np
import matplotlib.pyplot as plt
from src.operators import operator_registry

def generate_random_network(n_nodes, edge_probability=0.1, weighted=True):
    """生成随机网络"""
    nodes = {}
    
    # 创建节点
    for i in range(n_nodes):
        node_id = f"node_{i}"
        connections = {}
        
        # 添加连接
        for j in range(n_nodes):
            if i != j and random.random() < edge_probability:
                if weighted:
                    weight = random.uniform(0.1, 1.0)
                else:
                    weight = 1.0
                
                connections[f"node_{j}"] = weight
        
        nodes[node_id] = connections
    
    return nodes

def benchmark_centrality(nodes, centrality_type, use_gpu=False):
    """测试中心性算子性能"""
    # 获取中心性算子
    centrality_class = operator_registry.get_operator("gpu_acceleration", "centrality")
    
    # 创建配置
    config = centrality_class.PyCentralityConfig(
        centrality_type=centrality_type,
        use_edge_weights=True,
        normalize=True,
        max_iterations=100,
        convergence_threshold=1e-6,
        damping_factor=0.85,
        use_multithreading=True,
        compute_device="cuda" if use_gpu else "cpu",
        use_simd=True,
        use_sparse_matrix=True,
        batch_size=64
    )
    
    # 创建中心性算子实例
    centrality_operator = centrality_class.PyCentralityOperator(config)
    
    # 计算中心性
    start_time = time.time()
    result = centrality_operator.calculate_centrality(nodes)
    elapsed_time = time.time() - start_time
    
    return {
        "elapsed_time": elapsed_time,
        "result": result
    }

# 运行性能测试
centrality_types = ["degree", "closeness", "betweenness", "eigenvector", "pagerank", "katz"]
node_counts = [10, 50, 100, 200, 500]

results = {
    "cpu": {ct: [] for ct in centrality_types},
    "gpu": {ct: [] for ct in centrality_types}
}

for n_nodes in node_counts:
    print(f"测试节点数量: {n_nodes}")
    
    # 生成随机网络
    nodes = generate_random_network(n_nodes, edge_probability=0.1)
    
    for centrality_type in centrality_types:
        print(f"  测试中心性类型: {centrality_type}")
        
        # 测试CPU版本
        print("    测试CPU版本...")
        cpu_result = benchmark_centrality(nodes, centrality_type, use_gpu=False)
        results["cpu"][centrality_type].append({
            "n_nodes": n_nodes,
            "elapsed_time": cpu_result["elapsed_time"]
        })
        print(f"    CPU版本耗时: {cpu_result['elapsed_time']:.4f}秒")
        
        # 测试GPU版本
        print("    测试GPU版本...")
        gpu_result = benchmark_centrality(nodes, centrality_type, use_gpu=True)
        results["gpu"][centrality_type].append({
            "n_nodes": n_nodes,
            "elapsed_time": gpu_result["elapsed_time"]
        })
        print(f"    GPU版本耗时: {gpu_result['elapsed_time']:.4f}秒")
        
        # 计算加速比
        speedup = cpu_result["elapsed_time"] / gpu_result["elapsed_time"]
        print(f"    加速比: {speedup:.2f}x")
        
        print()

# 绘制结果
fig, axes = plt.subplots(2, 3, figsize=(15, 10))
axes = axes.flatten()

for i, centrality_type in enumerate(centrality_types):
    ax = axes[i]
    
    # 提取数据
    cpu_data = results["cpu"][centrality_type]
    gpu_data = results["gpu"][centrality_type]
    
    n_nodes = [d["n_nodes"] for d in cpu_data]
    cpu_times = [d["elapsed_time"] for d in cpu_data]
    gpu_times = [d["elapsed_time"] for d in gpu_data]
    
    # 计算加速比
    speedups = [c / g for c, g in zip(cpu_times, gpu_times)]
    
    # 绘制时间对比
    ax.plot(n_nodes, cpu_times, 'o-', label='CPU')
    ax.plot(n_nodes, gpu_times, 's-', label='GPU')
    
    # 添加标签和标题
    ax.set_xlabel('节点数量')
    ax.set_ylabel('执行时间 (秒)')
    ax.set_title(f'{centrality_type.capitalize()} 中心性')
    ax.legend()
    ax.grid(True)
    
    # 添加加速比文本
    for j, (x, y, s) in enumerate(zip(n_nodes, gpu_times, speedups)):
        ax.annotate(f'{s:.2f}x', (x, y), textcoords="offset points", 
                    xytext=(0, 10), ha='center')

plt.tight_layout()
plt.savefig('gpu_centrality_benchmark_results.png')
plt.show()
```

## 算法说明

GPU加速中心节点识别算子支持以下中心性算法：

### 度中心性（Degree Centrality）

度中心性是最简单的中心性度量，它计算节点的连接数量。在有向图中，可以区分入度中心性和出度中心性。在加权图中，可以使用连接权重的总和作为度中心性。

### 接近中心性（Closeness Centrality）

接近中心性衡量节点到网络中所有其他节点的平均最短路径长度的倒数。接近中心性高的节点可以更快地将信息传播到网络中的其他节点。

### 介数中心性（Betweenness Centrality）

介数中心性衡量节点位于网络中其他节点对之间最短路径上的频率。介数中心性高的节点在网络中起到桥梁作用，控制信息流动。

### 特征向量中心性（Eigenvector Centrality）

特征向量中心性考虑了节点的连接质量，而不仅仅是数量。连接到中心性高的节点会增加节点的特征向量中心性。

### PageRank中心性（PageRank Centrality）

PageRank中心性是特征向量中心性的变体，它考虑了随机游走模型。PageRank中心性高的节点更有可能被随机游走访问。

### Katz中心性（Katz Centrality）

Katz中心性是特征向量中心性的另一个变体，它考虑了所有长度的路径，而不仅仅是直接连接。较长路径的贡献会按照衰减因子进行衰减。

## 性能优化

GPU加速中心节点识别算子使用以下技术优化性能：

1. **GPU加速**：利用GPU的并行计算能力加速中心性算法
2. **多线程**：在CPU模式下使用多线程并行计算
3. **SIMD加速**：在支持的CPU上使用SIMD指令集加速向量运算
4. **稀疏矩阵**：对于稀疏网络，使用稀疏矩阵表示提高内存效率和计算效率
5. **批处理**：将大型网络分解为多个批次进行处理，减少内存占用
6. **缓存**：缓存中间结果，避免重复计算
7. **自动调优**：根据网络规模和硬件环境自动调整算法参数

## 常见问题

### Q: 如何选择合适的中心性算法？

A: 不同的中心性算法适用于不同的网络分析任务：
- 度中心性适用于简单的连接数量分析
- 接近中心性适用于分析信息传播效率
- 介数中心性适用于识别网络中的桥梁节点
- 特征向量中心性、PageRank中心性和Katz中心性适用于考虑连接质量的复杂网络分析

### Q: 为什么GPU版本在小型网络上可能比CPU版本慢？

A: GPU加速在处理大型网络时效果最佳。对于小型网络，GPU初始化和数据传输的开销可能超过并行计算带来的性能提升。

### Q: 如何处理超大型网络？

A: 对于超大型网络，可以：
1. 启用稀疏矩阵表示（`use_sparse_matrix=True`）
2. 增加批处理大小（`batch_size`）
3. 使用分布式计算（设置`TTE_DISTRIBUTED_COMPUTE=1`）
4. 使用远程计算（设置`TTE_REMOTE_COMPUTE=1`）

### Q: 如何解决"内存不足"错误？

A: 如果遇到内存不足错误，可以：
1. 启用内存优化（设置`TTE_MEMORY_OPTIMIZATION=1`）
2. 减小批处理大小（设置`TTE_BATCH_SIZE`为较小的值）
3. 启用稀疏矩阵表示（`use_sparse_matrix=True`）
4. 使用混合精度（设置`TTE_MIXED_PRECISION=1`）

## 参考资料

- [NetworkX中心性算法文档](https://networkx.org/documentation/stable/reference/algorithms/centrality.html)
- [GPU加速图算法](https://developer.nvidia.com/blog/accelerating-graph-algorithms-with-cuda/)
- [并行中心性算法](https://arxiv.org/abs/1305.2076)
- [稀疏矩阵计算](https://docs.scipy.org/doc/scipy/reference/sparse.html)
