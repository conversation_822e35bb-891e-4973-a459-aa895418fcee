# 算子注册表使用指南

本文档介绍了算子注册表的使用方法，包括注册算子、获取算子、管理依赖和检查兼容性等功能。

## 1. 简介

算子注册表是一个统一的注册系统，用于管理所有算子。通过注册表，可以按需组合和灵活搭配各种算子，实现算法的模块化和可复用性。

算子注册表提供以下功能：

- 注册和获取算子
- 管理算子元数据
- 管理算子依赖
- 检查算子兼容性
- 创建算子实例

## 2. 安装和导入

算子注册表模块已经集成到超越态思维引擎中，可以通过以下方式导入：

```python
from src.rust_bindings import (
    OperatorCategory,
    OperatorRegistry,
    OperatorMetadata,
    DependencyManager,
    CompatibilityLevel,
    CompatibilityChecker,
    OperatorFactory,
    get_global_registry,
    register_operator,
    get_operator,
    list_operators,
    get_operator_metadata,
    check_compatibility,
)
```

## 3. 基本使用

### 3.1 注册算子

可以使用全局注册表函数或创建自己的注册表实例来注册算子：

```python
# 使用全局注册表
register_operator(
    OperatorCategory.UTILITY,
    "add",
    add_function,
    "1.0.0",
    "加法算子",
    ["math", "basic"],
    [],
)

# 或者创建自己的注册表实例
registry = OperatorRegistry()
registry.register(
    OperatorCategory.UTILITY,
    "add",
    add_function,
    "1.0.0",
    "加法算子",
    ["math", "basic"],
    [],
)
```

参数说明：

- `category`：算子类别，可以是`OperatorCategory`枚举或字符串
- `name`：算子名称
- `operator`：算子函数或类
- `version`：算子版本，使用语义化版本格式（如"1.0.0"）
- `description`：算子描述（可选）
- `tags`：算子标签列表（可选）
- `dependencies`：算子依赖列表，每个依赖是一个元组`(name, version_req)`（可选）

### 3.2 获取算子

可以使用全局函数或注册表实例来获取算子：

```python
# 使用全局函数
add_op = get_operator(OperatorCategory.UTILITY, "add")

# 或者使用注册表实例
add_op = registry.get(OperatorCategory.UTILITY, "add")

# 使用算子
result = add_op(1, 2)  # 3
```

### 3.3 列出算子

可以列出所有算子或特定类别的算子：

```python
# 列出所有算子
all_operators = list_operators()

# 列出特定类别的算子
utility_operators = list_operators(OperatorCategory.UTILITY)

# 或者使用注册表实例
all_operators = registry.list_all()
utility_operators = registry.list_by_category(OperatorCategory.UTILITY)
```

### 3.4 获取算子元数据

可以获取算子的元数据：

```python
# 使用全局函数
metadata = get_operator_metadata(OperatorCategory.UTILITY, "add")

# 或者使用注册表实例
metadata = registry.get_metadata(OperatorCategory.UTILITY, "add")

# 访问元数据属性
print(metadata.name)        # "add"
print(metadata.version)     # "1.0.0"
print(metadata.description) # "加法算子"
print(metadata.tags)        # ["math", "basic"]
print(metadata.dependencies) # []
```

## 4. 依赖管理

依赖管理器用于管理算子之间的依赖关系：

```python
# 创建依赖管理器
dependency_manager = DependencyManager(registry)

# 检查依赖是否满足
satisfied, missing = dependency_manager.check_dependencies(OperatorCategory.NUMPY, "matrix_multiply")

# 获取依赖于算子的所有算子
dependents = dependency_manager.get_dependents(OperatorCategory.UTILITY, "add")

# 构建依赖图
graph = dependency_manager.build_dependency_graph()

# 检查循环依赖
cycles = dependency_manager.check_circular_dependencies()

# 解析依赖（按拓扑顺序）
dependencies = dependency_manager.resolve_dependencies(OperatorCategory.NUMPY, "matrix_multiply")
```

## 5. 兼容性检查

兼容性检查器用于检查算子版本的兼容性：

```python
# 创建兼容性检查器
checker = CompatibilityChecker(registry)

# 添加兼容性规则
checker.add_rule(OperatorCategory.UTILITY, "add", ">=0.9.0,<1.0.0", CompatibilityLevel.PARTIAL)
checker.add_rule(OperatorCategory.UTILITY, "add", "<0.9.0", CompatibilityLevel.NONE)

# 检查兼容性
level = checker.check_compatibility(OperatorCategory.UTILITY, "add", ">=1.0.0")
# level 是 CompatibilityLevel.FULL、CompatibilityLevel.PARTIAL 或 CompatibilityLevel.NONE

# 获取所有兼容性规则
rules = checker.get_rules()

# 获取算子的兼容性规则
rules = checker.get_rules_for_operator(OperatorCategory.UTILITY, "add")
```

## 6. 算子工厂

算子工厂用于创建算子实例：

```python
# 创建算子工厂
factory = OperatorFactory(registry)

# 注册构造函数
factory.register_constructor(OperatorCategory.UTILITY, "custom_add", lambda a, b: a + b + 1)

# 创建算子
add_op = factory.create_operator(OperatorCategory.UTILITY, "add", 1, 2)  # 3
custom_add_op = factory.create_operator(OperatorCategory.UTILITY, "custom_add", 1, 2)  # 4

# 创建带有依赖的算子
nn = factory.create_operator_with_dependencies(OperatorCategory.MULTILEVEL, "neural_network")

# 获取所有构造函数
constructors = factory.get_constructors()
```

## 7. 算子类别

算子注册表支持以下预定义的算子类别：

- `OperatorCategory.DIFFERENTIAL_GEOMETRY`：微分几何算子
- `OperatorCategory.INTERFERENCE`：干涉算子
- `OperatorCategory.FRACTAL`：分形算子
- `OperatorCategory.GAME_THEORY`：博弈论算子
- `OperatorCategory.TOPOLOGY`：拓扑算子
- `OperatorCategory.TRANSFORM`：变换算子
- `OperatorCategory.EVOLUTION`：演化算子
- `OperatorCategory.RESONANCE_NETWORK`：共振网络算子
- `OperatorCategory.EXPLANATION`：解释算子
- `OperatorCategory.VERIFICATION`：验证算子
- `OperatorCategory.ENGINEERING_SUPPORT`：工程支持算子
- `OperatorCategory.MULTILEVEL`：多层级算子
- `OperatorCategory.ENTROPY`：熵算子
- `OperatorCategory.UTILITY`：实用工具算子
- `OperatorCategory.NUMPY`：NumPy算子
- `OperatorCategory.PARALLEL_COMPUTING`：并行计算算子

也可以使用自定义类别：

```python
# 使用字符串作为类别
register_operator("custom_category", "custom_op", custom_function, "1.0.0")

# 或者使用自定义类别
custom_category = OperatorCategory.from_str("custom_category")
register_operator(custom_category, "custom_op", custom_function, "1.0.0")
```

## 8. 最佳实践

### 8.1 版本管理

使用语义化版本格式（如"1.0.0"）来管理算子版本，遵循以下规则：

- 主版本号（MAJOR）：当做了不兼容的API修改时增加
- 次版本号（MINOR）：当做了向下兼容的功能性新增时增加
- 修订号（PATCH）：当做了向下兼容的问题修正时增加

### 8.2 依赖管理

在注册算子时，明确指定依赖关系：

```python
register_operator(
    OperatorCategory.NUMPY,
    "matrix_multiply",
    matrix_multiply,
    "1.0.0",
    "矩阵乘法算子",
    ["math", "matrix"],
    [("utility.multiply", ">=1.0.0")],
)
```

依赖版本要求使用以下格式：

- `=1.0.0`：精确匹配版本1.0.0
- `>=1.0.0`：大于等于版本1.0.0
- `>1.0.0`：大于版本1.0.0
- `<1.0.0`：小于版本1.0.0
- `<=1.0.0`：小于等于版本1.0.0
- `>=1.0.0,<2.0.0`：大于等于版本1.0.0且小于版本2.0.0

### 8.3 标签管理

使用标签来分类和组织算子：

```python
register_operator(
    OperatorCategory.UTILITY,
    "add",
    add_function,
    "1.0.0",
    "加法算子",
    ["math", "basic", "arithmetic"],
    [],
)
```

然后可以使用标签来查找算子：

```python
math_operators = registry.get_operators_with_tag("math")
```

## 9. 示例

完整的示例代码可以在`examples/operator_registry_example.py`中找到。

## 10. 总结

算子注册表提供了一个统一的方式来管理和使用算子，支持按需组合和灵活搭配各种算子，实现算法的模块化和可复用性。通过依赖管理和兼容性检查，可以确保算子的正确使用和版本兼容性。
