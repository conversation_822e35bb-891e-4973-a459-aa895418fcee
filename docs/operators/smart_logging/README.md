# 智能日志算子

本目录包含与智能日志相关的算子，如智能日志器、因果链、查询引擎和日志分析器。这些算子是超越态思维引擎的工程实现支持组件，用于实现因果链管理和智能查询。

## 算子列表

### 已实现算子

1. **智能日志器算子 (SmartLoggerOperator)**
   - 文件：`src/operators/smart_logging/smart_logger.py`
   - 功能：记录和管理日志，支持结构化日志、上下文跟踪和因果关系
   - 特性：支持日志轮转、上下文管理、因果关系跟踪和智能查询

2. **因果链算子 (CausalChainOperator)**
   - 文件：`src/operators/smart_logging/causal_chain.py`
   - 功能：管理和分析日志事件之间的因果关系
   - 特性：支持循环检测、可视化和链深度计算

3. **查询引擎算子 (QueryEngineOperator)**
   - 文件：`src/operators/smart_logging/query_engine.py`
   - 功能：执行智能日志查询，支持复杂查询语言和高级过滤
   - 特性：支持查询缓存、查询解释和性能优化

4. **日志分析器算子 (LogAnalyzerOperator)**
   - 文件：`src/operators/smart_logging/log_analyzer.py`
   - 功能：分析日志数据并提取有用的信息，如错误模式、异常趋势和性能瓶颈
   - 特性：支持模式检测、异常检测和趋势分析

## 使用示例

### 智能日志器

```python
from src.operators.smart_logging import SmartLoggerOperator
import logging

# 创建智能日志器
logger = SmartLoggerOperator(
    log_dir='logs',
    log_level=logging.INFO,
    enable_console=True,
    enable_file=True,
    enable_context=True,
    enable_causality=True
)

# 记录日志
logger.apply({
    'operation': 'log',
    'message': 'Hello, world!',
    'level': logging.INFO,
    'extra': {'key': 'value'}
})

# 推入上下文
logger.apply({
    'operation': 'push_context',
    'context_id': 'request-123',
    'context_data': {'user_id': 'user-456', 'ip': '***********'}
})

# 记录带上下文的日志
logger.apply({
    'operation': 'log',
    'message': 'Processing request',
    'level': logging.INFO
})

# 弹出上下文
logger.apply({
    'operation': 'pop_context'
})

# 查询日志
result = logger.apply({
    'operation': 'query',
    'query': {'level': logging.ERROR},
    'limit': 10,
    'offset': 0
})

# 获取统计信息
stats = logger.apply({
    'operation': 'get_stats'
})
```

### 因果链

```python
from src.operators.smart_logging import CausalChainOperator

# 创建因果链算子
causal_chain = CausalChainOperator(
    max_chain_length=1000,
    enable_cycle_detection=True,
    enable_visualization=True
)

# 创建因果链
causal_chain.apply({
    'operation': 'create_chain',
    'chain_id': 'request-123',
    'chain_name': 'Request Processing',
    'chain_description': 'Processing of request 123',
    'chain_metadata': {'user_id': 'user-456'}
})

# 添加事件
causal_chain.apply({
    'operation': 'add_event',
    'event_id': 'event-1',
    'chain_id': 'request-123',
    'event_type': 'request_received',
    'event_data': {'method': 'GET', 'path': '/api/v1/users'}
})

causal_chain.apply({
    'operation': 'add_event',
    'event_id': 'event-2',
    'chain_id': 'request-123',
    'event_type': 'database_query',
    'event_data': {'query': 'SELECT * FROM users'}
})

# 链接事件
causal_chain.apply({
    'operation': 'link_events',
    'source_id': 'event-1',
    'target_id': 'event-2',
    'link_type': 'causal',
    'link_data': {'reason': 'request_processing'}
})

# 获取链信息
chain = causal_chain.apply({
    'operation': 'get_chain',
    'chain_id': 'request-123',
    'include_events': True
})

# 获取事件信息
event = causal_chain.apply({
    'operation': 'get_event',
    'event_id': 'event-1',
    'include_links': True
})

# 获取后续事件
successors = causal_chain.apply({
    'operation': 'get_successors',
    'event_id': 'event-1',
    'depth': 2
})

# 获取前置事件
predecessors = causal_chain.apply({
    'operation': 'get_predecessors',
    'event_id': 'event-2',
    'depth': 2
})

# 分析链
analysis = causal_chain.apply({
    'operation': 'analyze_chain',
    'chain_id': 'request-123'
})

# 可视化链
visualization = causal_chain.apply({
    'operation': 'visualize_chain',
    'chain_id': 'request-123'
})
```

### 查询引擎

```python
from src.operators.smart_logging import QueryEngineOperator

# 创建查询引擎
query_engine = QueryEngineOperator(
    max_results=100,
    enable_caching=True,
    cache_size=100,
    query_timeout=30
)

# 执行查询
result = query_engine.apply({
    'operation': 'query',
    'query': 'level = ERROR AND message ~ "database"',
    'logs': logs,
    'limit': 10,
    'offset': 0,
    'sort_field': 'timestamp',
    'sort_order': 'desc'
})

# 解析查询
parsed_query = query_engine.apply({
    'operation': 'parse_query',
    'query': 'level = ERROR AND message ~ "database"'
})

# 解释查询
explanation = query_engine.apply({
    'operation': 'explain_query',
    'query': 'level = ERROR AND message ~ "database"'
})

# 获取统计信息
stats = query_engine.apply({
    'operation': 'get_stats'
})

# 清除缓存
query_engine.apply({
    'operation': 'clear_cache'
})
```

### 日志分析器

```python
from src.operators.smart_logging import LogAnalyzerOperator

# 创建日志分析器
log_analyzer = LogAnalyzerOperator(
    time_window=3600,
    min_pattern_frequency=0.01,
    max_patterns=100,
    enable_anomaly_detection=True,
    anomaly_threshold=3.0,
    enable_trend_analysis=True,
    trend_window=86400
)

# 分析日志
analysis = log_analyzer.apply({
    'operation': 'analyze',
    'logs': logs
})

# 检测模式
patterns = log_analyzer.apply({
    'operation': 'detect_patterns',
    'logs': logs
})

# 检测异常
anomalies = log_analyzer.apply({
    'operation': 'detect_anomalies',
    'logs': logs
})

# 分析趋势
trends = log_analyzer.apply({
    'operation': 'analyze_trends',
    'logs': logs
})

# 获取统计信息
stats = log_analyzer.apply({
    'operation': 'get_stats'
})

# 清除缓存
log_analyzer.apply({
    'operation': 'clear_cache'
})
```

## 查询语言

查询引擎支持一种简单但功能强大的查询语言，用于过滤和搜索日志。查询语言支持以下操作符：

- `AND`：逻辑与
- `OR`：逻辑或
- `NOT`：逻辑非
- `=`：等于
- `!=`：不等于
- `>`：大于
- `<`：小于
- `>=`：大于等于
- `<=`：小于等于
- `~`：包含
- `!~`：不包含

示例查询：

```
level = ERROR AND message ~ "database"
timestamp > 1609459200 AND (level = ERROR OR level = WARNING)
NOT (level = INFO) AND extra.user_id = "user-123"
```

## 因果链分析

因果链算子提供了强大的因果关系分析功能，可以帮助理解系统行为和故障原因。主要功能包括：

1. **链创建和管理**：创建和管理因果链，包括添加事件和链接事件。
2. **循环检测**：检测因果关系中的循环，避免无限递归。
3. **链深度计算**：计算因果链的深度，帮助理解因果关系的复杂性。
4. **可视化**：可视化因果链，帮助理解因果关系。

## 日志分析

日志分析器提供了三种主要的分析功能：

1. **模式检测**：检测日志中的常见模式，帮助理解系统行为。
2. **异常检测**：检测日志中的异常，帮助发现系统故障。
3. **趋势分析**：分析日志中的趋势，帮助预测系统行为。

### 模式检测

模式检测使用模板匹配算法，将数字、UUID、IP地址和日期时间等可变部分替换为占位符，然后计算模板的频率。频率超过阈值的模板被视为模式。

### 异常检测

异常检测使用统计方法，计算每个时间窗口的错误率、警告率和日志数，然后使用Z分数检测异常。Z分数超过阈值的时间窗口被视为异常。

### 趋势分析

趋势分析使用简单线性回归，计算错误率、警告率和日志数的趋势。趋势值为正表示上升趋势，为负表示下降趋势，为0表示无趋势。

## Rust实现

部分算子已经有Rust实现，可以通过以下方式使用：

```python
from src.operators.smart_logging import (
    RustSmartLoggerOperator,
    RustCausalChainOperator,
    RustQueryEngineOperator,
    RustLogAnalyzerOperator,
    RUST_AVAILABLE
)

if RUST_AVAILABLE:
    # 使用Rust实现
    logger = RustSmartLoggerOperator(
        log_dir='logs',
        log_level=logging.INFO,
        enable_console=True,
        enable_file=True,
        enable_context=True,
        enable_causality=True
    )
    # ...
else:
    # 使用Python实现
    logger = SmartLoggerOperator(
        log_dir='logs',
        log_level=logging.INFO,
        enable_console=True,
        enable_file=True,
        enable_context=True,
        enable_causality=True
    )
    # ...
```

## 性能特性

- 时间复杂度：大多数操作为O(n)，其中n为日志数量；查询为O(n)，异常检测为O(n log n)
- 空间复杂度：大多数操作为O(n)
- 并行化：部分算子支持并行计算
- 内存使用：优化的内存使用，支持大规模日志分析

## 注意事项

- 对于大规模日志分析，建议使用Rust实现以获得更好的性能
- 查询引擎的查询超时参数可以根据日志数量和查询复杂度进行调整
- 日志分析器的时间窗口和趋势窗口参数可以根据日志生成速率进行调整
- 因果链算子的循环检测可能会增加计算复杂度，对于大规模因果链可以考虑禁用
