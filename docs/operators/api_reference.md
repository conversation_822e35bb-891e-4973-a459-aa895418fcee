# 超越态思维引擎算子库API参考

## 目录

### 第一部分：核心模块和变换算子
1. [简介](#1-简介)
2. [核心模块 (src.core)](#2-核心模块-srccore)
3. [变换算子模块 (src.operators.transform)](#3-变换算子模块-srcoperatorstransform)

### 第二部分：演化算子和优化算子
5. [演化算子模块 (src.operators.evolution)](#5-演化算子模块-srcoperatorsevolution)
6. [优化算子模块 (src.operators.optimization)](#6-优化算子模块-srcoperatorsoptimization)

### 第三部分：缓存机制、算子融合和分布式算子
8. [缓存机制 (src.operators.optimization.cache)](#8-缓存机制-srcoperatorsoptimizationcache)
9. [算子融合 (src.operators.optimization.fusion)](#9-算子融合-srcoperatorsoptimizationfusion)
10. [分布式算子模块 (src.operators.distributed)](#10-分布式算子模块-srcoperatorsdistributed)

### 第四部分：兼容性算子和高级优化工具
12. [兼容性算子模块 (src.operators.compatibility)](#12-兼容性算子模块-srcoperatorscompatibility)
13. [高级优化工具 (src.operators.optimization.advanced)](#13-高级优化工具-srcoperatorsoptimizationadvanced)
14. [API参考总结](#14-api参考总结)

## 详细内容

请参考以下文件获取详细内容：

- [第一部分：核心模块和变换算子](/home/<USER>/CascadeProjects/TTE/docs/operators/api_reference_part1.md)
- [第二部分：演化算子和优化算子](/home/<USER>/CascadeProjects/TTE/docs/operators/api_reference_part2.md)
- [第三部分：缓存机制、算子融合和分布式算子](/home/<USER>/CascadeProjects/TTE/docs/operators/api_reference_part3.md)
- [第四部分：兼容性算子和高级优化工具](/home/<USER>/CascadeProjects/TTE/docs/operators/api_reference_part4.md)
