# 超越态融合算子 API 参考

## 概述

本文档提供了超越态融合算子的 API 参考，包括函数签名、参数说明和返回值说明。超越态融合算子是超越态思维引擎的核心组件，用于将多个超越态进行融合，生成具有新特性的超越态。

## 模块结构

超越态融合算子由以下模块组成：

- `fusion_wrapper.py`：Python 包装器，提供 Python 友好的接口
- `fusion_registry.py`：注册模块，将融合算子注册到系统注册表中
- `rust_simple/`：Rust 实现，提供高性能的计算能力

## 函数参考

### 融合函数

#### `quantum_superposition_fusion(state_a, state_b, weight_a=0.5, weight_b=0.5)`

量子叠加融合算子，基于量子力学中的叠加原理，将多个状态进行线性叠加。

**参数**：
- `state_a` (List[complex])：第一个状态向量
- `state_b` (List[complex])：第二个状态向量
- `weight_a` (float, 可选)：第一个状态的权重，默认为 0.5
- `weight_b` (float, 可选)：第二个状态的权重，默认为 0.5

**返回值**：
- (List[complex])：融合后的状态向量

**异常**：
- `ValueError`：如果状态维度不匹配
- `TypeError`：如果输入参数类型不正确

**示例**：
```python
state_a = [1.0 + 0.0j, 0.0 + 0.0j]  # |0⟩
state_b = [0.0 + 0.0j, 1.0 + 0.0j]  # |1⟩
result = quantum_superposition_fusion(state_a, state_b)
```

#### `holographic_interference_fusion(state_a, state_b, weight_a=0.5, weight_b=0.5)`

全息干涉融合算子，基于全息原理，通过相位干涉实现状态融合。

**参数**：
- `state_a` (List[complex])：第一个状态向量
- `state_b` (List[complex])：第二个状态向量
- `weight_a` (float, 可选)：第一个状态的权重，默认为 0.5
- `weight_b` (float, 可选)：第二个状态的权重，默认为 0.5

**返回值**：
- (List[complex])：融合后的状态向量

**异常**：
- `ValueError`：如果状态维度不匹配
- `TypeError`：如果输入参数类型不正确

**示例**：
```python
state_a = [1.0 + 0.0j, 0.0 + 0.0j]  # |0⟩
state_b = [0.0 + 0.0j, 1.0 + 0.0j]  # |1⟩
result = holographic_interference_fusion(state_a, state_b)
```

#### `fractal_fusion(state_a, state_b, weight_a=0.5, weight_b=0.5)`

分形融合算子，基于分形理论，使用分形模式进行状态融合。

**参数**：
- `state_a` (List[complex])：第一个状态向量
- `state_b` (List[complex])：第二个状态向量
- `weight_a` (float, 可选)：第一个状态的权重，默认为 0.5
- `weight_b` (float, 可选)：第二个状态的权重，默认为 0.5

**返回值**：
- (List[complex])：融合后的状态向量

**异常**：
- `ValueError`：如果状态维度不匹配
- `TypeError`：如果输入参数类型不正确

**示例**：
```python
state_a = [1.0 + 0.0j, 0.0 + 0.0j]  # |0⟩
state_b = [0.0 + 0.0j, 1.0 + 0.0j]  # |1⟩
result = fractal_fusion(state_a, state_b)
```

#### `topological_fusion(state_a, state_b, weight_a=0.5, weight_b=0.5)`

拓扑融合算子，基于拓扑学原理，通过拓扑连接实现状态融合。

**参数**：
- `state_a` (List[complex])：第一个状态向量
- `state_b` (List[complex])：第二个状态向量
- `weight_a` (float, 可选)：第一个状态的权重，默认为 0.5
- `weight_b` (float, 可选)：第二个状态的权重，默认为 0.5

**返回值**：
- (List[complex])：融合后的状态向量

**异常**：
- `ValueError`：如果状态维度不匹配
- `TypeError`：如果输入参数类型不正确

**示例**：
```python
state_a = [1.0 + 0.0j, 0.0 + 0.0j]  # |0⟩
state_b = [0.0 + 0.0j, 1.0 + 0.0j]  # |1⟩
result = topological_fusion(state_a, state_b)
```

#### `fusion_with_method(state_a, state_b, method="quantum_superposition", weight_a=0.5, weight_b=0.5)`

通用融合算子，支持多种融合方法。

**参数**：
- `state_a` (List[complex])：第一个状态向量
- `state_b` (List[complex])：第二个状态向量
- `method` (str, 可选)：融合方法，可选值：quantum_superposition, holographic_interference, fractal_fusion, topological_fusion，默认为 quantum_superposition
- `weight_a` (float, 可选)：第一个状态的权重，默认为 0.5
- `weight_b` (float, 可选)：第二个状态的权重，默认为 0.5

**返回值**：
- (List[complex])：融合后的状态向量

**异常**：
- `ValueError`：如果状态维度不匹配或方法无效
- `TypeError`：如果输入参数类型不正确

**示例**：
```python
state_a = [1.0 + 0.0j, 0.0 + 0.0j]  # |0⟩
state_b = [0.0 + 0.0j, 1.0 + 0.0j]  # |1⟩
result = fusion_with_method(state_a, state_b, "quantum_superposition")
```

### 辅助函数

#### `get_fusion_operator()`

获取融合算子实例（单例模式）。

**返回值**：
- (TranscendentalFusionOperator)：融合算子实例

**异常**：
- `RuntimeError`：如果创建融合算子实例失败

**示例**：
```python
fusion_op = get_fusion_operator()
result = fusion_op.quantum_superposition(state_a, state_b)
```

#### `get_fusion_operator_by_name(name)`

获取指定名称的融合算子。

**参数**：
- `name` (str)：融合算子名称，例如 "fusion.quantum_superposition"

**返回值**：
- (callable)：融合算子函数

**异常**：
- `ValueError`：如果指定的融合算子不存在

**示例**：
```python
quantum_superposition = get_fusion_operator_by_name("fusion.quantum_superposition")
result = quantum_superposition(state_a, state_b)
```

#### `register_fusion_operators()`

注册融合算子到系统注册表。

**返回值**：
- (bool)：注册是否成功

**示例**：
```python
success = register_fusion_operators()
```

## 类参考

### `TranscendentalFusionOperator`

超越态融合算子类，提供多种融合方法。

#### 构造函数

```python
TranscendentalFusionOperator()
```

创建一个新的超越态融合算子实例。

**异常**：
- `RuntimeError`：如果创建融合算子实例失败

#### 方法

##### `fuse(state_a, state_b, weight_a=0.5, weight_b=0.5, method=FusionMethod.QUANTUM_SUPERPOSITION)`

融合两个量子态。

**参数**：
- `state_a` (numpy.ndarray)：第一个量子态，复数数组
- `state_b` (numpy.ndarray)：第二个量子态，复数数组
- `weight_a` (float, 可选)：第一个量子态的权重，默认为 0.5
- `weight_b` (float, 可选)：第二个量子态的权重，默认为 0.5
- `method` (FusionMethod, 可选)：融合方法，默认为量子叠加

**返回值**：
- (numpy.ndarray)：融合后的量子态，复数数组

**异常**：
- `ValueError`：如果状态维度不匹配
- `TypeError`：如果输入参数类型不正确

**示例**：
```python
fusion_op = TranscendentalFusionOperator()
result = fusion_op.fuse(state_a, state_b, method=FusionMethod.QUANTUM_SUPERPOSITION)
```

##### `quantum_superposition(state_a, state_b, weight_a=0.5, weight_b=0.5)`

量子叠加融合。

**参数**：
- `state_a` (numpy.ndarray)：第一个量子态，复数数组
- `state_b` (numpy.ndarray)：第二个量子态，复数数组
- `weight_a` (float, 可选)：第一个量子态的权重，默认为 0.5
- `weight_b` (float, 可选)：第二个量子态的权重，默认为 0.5

**返回值**：
- (numpy.ndarray)：融合后的量子态，复数数组

**示例**：
```python
fusion_op = TranscendentalFusionOperator()
result = fusion_op.quantum_superposition(state_a, state_b)
```

##### `holographic_interference(state_a, state_b, weight_a=0.5, weight_b=0.5)`

全息干涉融合。

**参数**：
- `state_a` (numpy.ndarray)：第一个量子态，复数数组
- `state_b` (numpy.ndarray)：第二个量子态，复数数组
- `weight_a` (float, 可选)：第一个量子态的权重，默认为 0.5
- `weight_b` (float, 可选)：第二个量子态的权重，默认为 0.5

**返回值**：
- (numpy.ndarray)：融合后的量子态，复数数组

**示例**：
```python
fusion_op = TranscendentalFusionOperator()
result = fusion_op.holographic_interference(state_a, state_b)
```

##### `fractal_fusion(state_a, state_b, weight_a=0.5, weight_b=0.5)`

分形融合。

**参数**：
- `state_a` (numpy.ndarray)：第一个量子态，复数数组
- `state_b` (numpy.ndarray)：第二个量子态，复数数组
- `weight_a` (float, 可选)：第一个量子态的权重，默认为 0.5
- `weight_b` (float, 可选)：第二个量子态的权重，默认为 0.5

**返回值**：
- (numpy.ndarray)：融合后的量子态，复数数组

**示例**：
```python
fusion_op = TranscendentalFusionOperator()
result = fusion_op.fractal_fusion(state_a, state_b)
```

##### `topological_fusion(state_a, state_b, weight_a=0.5, weight_b=0.5)`

拓扑融合。

**参数**：
- `state_a` (numpy.ndarray)：第一个量子态，复数数组
- `state_b` (numpy.ndarray)：第二个量子态，复数数组
- `weight_a` (float, 可选)：第一个量子态的权重，默认为 0.5
- `weight_b` (float, 可选)：第二个量子态的权重，默认为 0.5

**返回值**：
- (numpy.ndarray)：融合后的量子态，复数数组

**示例**：
```python
fusion_op = TranscendentalFusionOperator()
result = fusion_op.topological_fusion(state_a, state_b)
```

### `FusionMethod`

融合方法枚举。

**成员**：
- `QUANTUM_SUPERPOSITION = 0`：量子叠加
- `HOLOGRAPHIC_INTERFERENCE = 1`：全息干涉
- `FRACTAL_FUSION = 2`：分形融合
- `TOPOLOGICAL_FUSION = 3`：拓扑融合

**示例**：
```python
from src.operators.fusion_wrapper import FusionMethod

method = FusionMethod.QUANTUM_SUPERPOSITION
```

## 性能特性

超越态融合算子的性能特性如下：

- **时间复杂度**：O(n)，其中 n 是状态向量的维度
- **空间复杂度**：O(n)，其中 n 是状态向量的维度
- **并行性**：支持并行处理，可以在多线程环境中使用
- **线程安全**：所有函数都是线程安全的

## 版本历史

### 1.0.0 (2023-07-15)

- 初始版本
- 实现了四种基本融合方法：量子叠加、全息干涉、分形融合和拓扑融合
- 提供了 Python 友好的接口
- 支持注册到系统注册表
- 支持Python 3.13+的无GIL线程模式

## 已知问题

- 当输入状态维度不同时，会抛出异常，而不是自动调整维度
- 当输入状态为空时，会返回空列表，而不是抛出异常
- 当指定的融合方法无效时，会默认使用量子叠加融合，而不是抛出异常
- 在使用中文字体显示时，可能会出现字体缺失警告，这不影响功能
- 在导入时可能会出现Rust模块不可用的警告，系统会自动回退到Python实现

## 未来计划

- 支持更多融合方法，如量子纠缠融合、量子退相干融合等
- 支持更复杂的数据结构，如张量、图、流形等
- 实现 GPU 加速，进一步提高性能
- 支持分布式计算，处理更大规模的状态
- 实现自适应融合，根据状态特性自动选择最佳融合方法
