# 超越态融合算子快速入门

## 概述

超越态融合算子是超越态思维引擎的核心组件，用于将多个超越态进行融合，生成具有新特性的超越态。本文档提供了超越态融合算子的快速入门指南，帮助您快速上手使用超越态融合算子。

## 安装

超越态融合算子已经集成到超越态思维引擎中，不需要单独安装。但是，它依赖于以下组件：

- Python 3.13+
- Rust 1.75+
- NumPy 1.24+

如果您需要单独使用超越态融合算子，请确保这些依赖已经安装。

## 基本用法

### 导入融合算子

```python
from src.operators.fusion import (
    quantum_superposition_fusion,
    holographic_interference_fusion,
    fractal_fusion,
    topological_fusion,
    fusion_with_method
)
```

### 创建状态

```python
import numpy as np

# 创建基态 |0⟩
state_0 = [1.0 + 0.0j, 0.0 + 0.0j]

# 创建基态 |1⟩
state_1 = [0.0 + 0.0j, 1.0 + 0.0j]
```

### 使用融合方法

```python
# 使用量子叠加融合
result = quantum_superposition_fusion(state_0, state_1)
print("量子叠加融合结果:", result)

# 使用全息干涉融合
result = holographic_interference_fusion(state_0, state_1)
print("全息干涉融合结果:", result)

# 使用分形融合
result = fractal_fusion(state_0, state_1)
print("分形融合结果:", result)

# 使用拓扑融合
result = topological_fusion(state_0, state_1)
print("拓扑融合结果:", result)
```

### 指定权重

```python
# 指定权重
result = quantum_superposition_fusion(state_0, state_1, weight_a=0.8, weight_b=0.2)
print("带权重的量子叠加融合结果:", result)
```

### 通过注册表使用

```python
from src.operators.fusion import get_fusion_operator_by_name

# 获取融合算子
quantum_superposition = get_fusion_operator_by_name("fusion.quantum_superposition")

# 使用融合算子
result = quantum_superposition(state_0, state_1)
print("量子叠加融合结果:", result)
```

## 常见问题

### 1. 导入错误

如果您遇到导入错误，请检查您的Python路径是否正确：

```python
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))
```

### 2. 警告抑制

在导入融合算子时，可能会出现一些警告，如Rust模块不可用的警告。这些警告不影响功能，系统会自动回退到Python实现。如果您想抑制这些警告，可以使用以下方法：

```python
import warnings

# 抑制所有警告
with warnings.catch_warnings():
    warnings.simplefilter("ignore")
    
    # 导入融合算子
    from src.operators.fusion import (
        quantum_superposition_fusion,
        holographic_interference_fusion,
        fractal_fusion,
        topological_fusion,
        fusion_with_method
    )
```

### 3. 状态维度不匹配

如果您遇到状态维度不匹配的错误，请确保输入状态的维度相同：

```python
# 错误示例
state_a = [1.0 + 0.0j, 0.0 + 0.0j]
state_b = [0.0 + 0.0j, 1.0 + 0.0j, 0.0 + 0.0j]
result = quantum_superposition_fusion(state_a, state_b)  # 错误：状态维度不匹配

# 正确示例
state_a = [1.0 + 0.0j, 0.0 + 0.0j, 0.0 + 0.0j]
state_b = [0.0 + 0.0j, 1.0 + 0.0j, 0.0 + 0.0j]
result = quantum_superposition_fusion(state_a, state_b)  # 正确
```

## 更多资源

- [超越态融合算子使用指南](fusion_operators_usage.md)：提供更详细的使用指南
- [超越态融合算子API参考](fusion_operators_api.md)：提供API参考
- [超越态融合算子示例](../../examples/fusion_operators_example.py)：提供示例代码

## 反馈与支持

如果您遇到问题或有任何建议，请使用[问题报告模板](fusion_operators_issue_template.md)提交问题。
