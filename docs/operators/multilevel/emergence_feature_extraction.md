# 涌现特性提取算子

## 概述

涌现特性提取算子用于从多层级交互系统中提取涌现特性。涌现特性是指在系统层级之间的交互过程中产生的新特性，这些特性在单个层级中不存在，而是由层级间的相互作用产生的。

本算子支持多种提取方法和特性类型，可以根据不同的应用场景进行配置。

## 功能特点

- 支持多种提取方法：统计学、拓扑学、信息论和动力学
- 支持多种特性类型：结构特性、功能特性、行为特性和时间特性
- 可配置的敏感度和阈值参数
- 提供特性重要性评分
- 支持与其他算子组合

## 提取方法

### 统计学方法 (statistical)

基于统计学原理提取涌现特性，包括：
- 度分布分析
- 聚类系数计算
- 社区结构检测
- 相关性分析
- 周期性检测
- 趋势分析

### 拓扑学方法 (topological)

基于拓扑学原理提取涌现特性，包括：
- 连通性分析
- 路径长度计算
- 中心性计算
- 小世界特性检测
- 中心-边缘结构检测
- 层次结构检测

### 信息论方法 (information_theoretic)

基于信息论原理提取涌现特性，包括：
- 熵计算
- 互信息计算
- 信息流分析
- 信息传递检测
- 信息瓶颈识别

### 动力学方法 (dynamical)

基于动力学原理提取涌现特性，包括：
- 稳定性分析
- 弹性计算
- 复杂性评估
- 预测性分析
- 适应性评估

## 特性类型

### 结构特性 (structural)

描述系统的结构特性，包括：
- 高聚类区域
- 中心层
- 桥接层
- 小世界特性
- 中心-边缘结构
- 高熵区域
- 高互信息连接
- 结构不稳定区域
- 结构弹性区域

### 功能特性 (functional)

描述系统的功能特性，包括：
- 功能组
- 功能冗余
- 功能依赖
- 功能模块
- 功能层次结构
- 功能多样性热点
- 功能信息流瓶颈
- 功能不稳定区域
- 高适应性功能

### 行为特性 (behavioral)

描述系统的行为特性，包括：
- 协同变化
- 周期性行为
- 行为同步群组
- 行为中心
- 行为熵变化
- 行为信息传递
- 高复杂性行为

### 时间特性 (temporal)

描述系统的时间特性，包括：
- 趋势
- 变化点
- 时间因果链
- 时间汇聚点
- 时间熵变化点
- 时间信息流
- 时间不稳定区域
- 高预测性时间序列

## 使用示例

```python
from src.operators.multilevel.emergence_feature_extraction import EmergenceFeatureExtractionOperator

# 创建算子实例
operator = EmergenceFeatureExtractionOperator(
    extraction_method='statistical',  # 提取方法
    feature_type='structural',        # 特性类型
    sensitivity=0.5,                  # 敏感度
    threshold=0.3                     # 阈值
)

# 准备输入数据
input_data = {
    'layers': [...],           # 层级结构
    'interactions': {...},     # 交互模式
    'history': [...]           # 历史数据
}

# 应用算子
result = operator.apply(input_data)

# 获取提取的涌现特性
extracted_features = result['extracted_features']
importance_scores = result['importance_scores']

# 打印结果
print(f"提取的特性数量: {result['feature_count']}")
for feature_id, feature in extracted_features.items():
    print(f"- {feature_id}: {feature['description']}")
    print(f"  重要性得分: {importance_scores.get(feature_id, 0):.4f}")
```

## 参数说明

- `extraction_method`: 提取方法，可选值为 'statistical', 'topological', 'information_theoretic', 'dynamical'
- `feature_type`: 特性类型，可选值为 'structural', 'functional', 'behavioral', 'temporal'
- `sensitivity`: 敏感度，用于控制提取的灵敏度，取值范围为 (0, 1]
- `threshold`: 阈值，用于过滤低重要性的特性，取值范围为 [0, 1)

## 输入数据格式

算子的输入数据应包含以下字段：

- `layers`: 层级结构，格式为 `[{'id': 'layer1', 'name': '层级1', 'function_type': '功能类型', ...}, ...]`
- `interactions`: 交互模式，格式为 `{'layer1_layer2': {'type': '交互类型', 'strength': 0.8, 'bidirectional': False, ...}, ...}`
- `history`: 历史数据，格式为 `[{'time': 0, 'layers': [{'id': 'layer1', 'state': {'属性1': 值1, ...}}, ...]}, ...]`

## 输出数据格式

算子的输出数据包含以下字段：

- `layers`: 输入的层级结构
- `extracted_features`: 提取的涌现特性，格式为 `{'特性ID': {'type': '特性类型', 'description': '特性描述', ...}, ...}`
- `extraction_method`: 使用的提取方法
- `feature_type`: 使用的特性类型
- `feature_count`: 提取的特性数量
- `importance_scores`: 特性重要性得分，格式为 `{'特性ID': 得分, ...}`
- `extraction_time`: 提取时间（秒）

## 性能考虑

- 时间复杂度：O(n²*m)，其中 n 是层级数量，m 是每个层级的状态数量
- 空间复杂度：O(n*m)
- 计算复杂度：高

## 注意事项

- 对于大型系统，提取过程可能会比较耗时，建议适当调整敏感度和阈值参数
- 历史数据越丰富，提取的行为特性和时间特性越准确
- 不同的提取方法和特性类型适用于不同的应用场景，建议根据实际需求选择
- 算子支持与其他算子组合，可以构建更复杂的分析流程
