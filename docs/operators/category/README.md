# 高阶自反性范畴算子

本目录包含与范畴论相关的算子，如范畴构建算子、函子映射算子、自然变换算子等。这些算子是超越态思维引擎的核心组件，用于实现高阶自反性和元认知能力。

## 算子列表

### 已实现算子

1. **范畴构建算子 (CategoryBuilder)**
   - 文件：`src/operators/category/builder.py`
   - 功能：构建范畴结构，包括对象和态射
   - 特性：支持多种范畴类型，如离散范畴、预序范畴、群范畴等

2. **函子映射算子 (FunctorOperator)**
   - 文件：`src/operators/category/functor.py`
   - 功能：实现范畴间的映射，保持结构
   - 特性：支持多种函子类型，如忠实函子、满函子、等价函子等

3. **自然变换算子 (NaturalTransformationOperator)**
   - 文件：`src/operators/category/transformation.py`
   - 功能：实现函子间的变换
   - 特性：支持自然同构、单射和满射自然变换

4. **伴随函子算子 (AdjointFunctorOperator)**
   - 文件：`src/operators/category/adjoint.py`
   - 功能：计算范畴间函子的伴随函子
   - 特性：支持左伴随和右伴随，计算单位和余单位自然变换

5. **极限算子 (LimitOperator)**
   - 文件：`src/operators/category/limit.py`
   - 功能：计算范畴中的极限
   - 特性：支持多种极限类型，如积、等化子、拉回等

6. **余极限算子 (ColimitOperator)**
   - 文件：`src/operators/category/limit.py`
   - 功能：计算范畴中的余极限
   - 特性：支持多种余极限类型，如余积、余等化子、推出等

7. **范畴积算子 (ProductOperator)**
   - 文件：`src/operators/category/product.py`
   - 功能：计算范畴的积
   - 特性：支持多种积类型，如直积、纤维积、逗号范畴等

8. **范畴余积算子 (CoproductOperator)**
   - 文件：`src/operators/category/product.py`
   - 功能：计算范畴的余积
   - 特性：支持多种余积类型，如直和、纤维余积、逗号范畴等

## 使用示例

### 范畴构建

```python
from src.operators.category import CategoryBuilder

# 创建范畴构建器
builder = CategoryBuilder(category_type='discrete')

# 添加对象
builder.add_object('A', {'color': 'red'})
builder.add_object('B', {'color': 'blue'})

# 添加态射
builder.add_morphism('f', 'A', 'B', {'weight': 0.5})

# 构建范畴
category = builder.build()
```

### 函子映射

```python
from src.operators.category import FunctorOperator

# 创建函子算子
functor = FunctorOperator(functor_type='faithful')

# 设置源范畴和目标范畴
functor.set_source_category(category1)
functor.set_target_category(category2)

# 添加对象映射
functor.add_object_mapping('A', 'X')
functor.add_object_mapping('B', 'Y')

# 添加态射映射
functor.add_morphism_mapping('f', 'g')

# 应用函子
result = functor.apply(input_data)
```

### 自然变换

```python
from src.operators.category import NaturalTransformationOperator

# 创建自然变换算子
transformation = NaturalTransformationOperator()

# 设置源函子和目标函子
transformation.set_source_functor(functor1)
transformation.set_target_functor(functor2)

# 添加分量
transformation.add_component('A', 'h')
transformation.add_component('B', 'k')

# 应用自然变换
result = transformation.apply(input_data)
```

### 伴随函子

```python
from src.operators.category import AdjointFunctorOperator

# 创建伴随函子算子
adjoint = AdjointFunctorOperator(adjoint_type='left')

# 应用算子
result = adjoint.apply({
    'functor': functor,
    'source_category': category1,
    'target_category': category2
})

# 获取伴随函子
adjoint_functor = result['adjoint_functor']

# 获取单位和余单位自然变换
unit = result['unit']
counit = result['counit']
```

### 极限和余极限

```python
from src.operators.category import LimitOperator, ColimitOperator

# 创建极限算子
limit_op = LimitOperator(diagram_type='product')

# 应用极限算子
limit = limit_op.apply({
    'category': category,
    'diagram': diagram
})

# 创建余极限算子
colimit_op = ColimitOperator(diagram_type='coproduct')

# 应用余极限算子
colimit = colimit_op.apply({
    'category': category,
    'diagram': diagram
})
```

### 范畴积和余积

```python
from src.operators.category import ProductOperator, CoproductOperator

# 创建范畴积算子
product_op = ProductOperator(product_type='direct')

# 应用范畴积算子
product = product_op.apply({
    'categories': [category1, category2]
})

# 创建范畴余积算子
coproduct_op = CoproductOperator(coproduct_type='direct')

# 应用范畴余积算子
coproduct = coproduct_op.apply({
    'categories': [category1, category2]
})
```

## Rust实现

部分算子已经有Rust实现，可以通过以下方式使用：

```python
from src.operators.category import (
    RustCategoryBuilder,
    RustFunctorOperator,
    RustNaturalTransformationOperator,
    RUST_AVAILABLE
)

if RUST_AVAILABLE:
    # 使用Rust实现
    builder = RustCategoryBuilder(category_type='discrete')
    # ...
else:
    # 使用Python实现
    builder = CategoryBuilder(category_type='discrete')
    # ...
```

## 性能特性

- 时间复杂度：大多数操作为O(n^2)，其中n为对象或态射的数量
- 空间复杂度：大多数操作为O(n)或O(n^2)
- 并行化：部分算子支持并行计算
- 内存使用：优化的内存使用，支持大规模范畴

## 注意事项

- 对于大规模范畴，建议使用Rust实现以获得更好的性能
- 确保范畴结构的一致性，特别是在添加态射时
- 函子映射需要保持结构，否则可能导致错误
- 自然变换需要满足自然性条件，否则可能导致错误
