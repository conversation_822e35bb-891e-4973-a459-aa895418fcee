# TTE Rust算子文档

本文档详细介绍了TTE项目中的Rust算子，包括它们的功能、参数和使用方法。这些算子已经兼容Python 3.13的无GIL线程模式，可以充分利用多核处理器的性能优势。

## 目录

1. [算子注册表使用方法](#算子注册表使用方法)
2. [分形算子](#分形算子)
   - [分数阶特征算子](#分数阶特征算子)
   - [分形维数算子](#分形维数算子)
   - [分形插值算子](#分形插值算子)
3. [熵算子](#熵算子)
   - [多尺度熵算子](#多尺度熵算子)
4. [变换算子](#变换算子)
   - [小波变换算子](#小波变换算子)
5. [工具算子](#工具算子)
   - [复数包装类型](#复数包装类型)
6. [性能优化建议](#性能优化建议)
7. [常见问题解答](#常见问题解答)

## 算子注册表使用方法

TTE项目使用统一的注册表管理算子，支持按需组合和使用不同的算子，而不需要直接导入它们。以下是使用注册表的基本方法：

```python
# 导入算子注册表
from tte.src.operators.registry import operator_registry

# 获取所有算子类别
categories = operator_registry.get_categories()
print(f"算子类别: {categories}")

# 获取特定类别下的所有算子
fractal_operators = operator_registry.get_operators_in_category("fractal")
print(f"分形类别下的算子: {list(fractal_operators.keys())}")

# 获取特定算子
fractional_feature_operator = operator_registry.get_operator("fractal", "fractional_feature")

# 创建算子实例
operator = fractional_feature_operator(1000)  # 创建维度为1000的分数阶特征算子

# 使用算子
data = [1.0, 2.0, 3.0, 4.0, 5.0]
result = operator.fractional_derivative(data, 0.5)
```

## 分形算子

### 分数阶特征算子

分数阶特征算子用于计算时间序列的分数阶导数，可以提取信号中的分数阶特征。

**类名**: `FractionalFeatureOperator`  
**注册表路径**: `fractal/fractional_feature`

**初始化参数**:
- `dim` (int): 算子维度，影响内部计算精度

**主要方法**:
- `fractional_derivative(data, alpha)`: 计算时间序列的分数阶导数
  - `data` (List[float]): 输入时间序列
  - `alpha` (float): 分数阶导数的阶数，通常在0到1之间
  - 返回: List[float] - 分数阶导数结果

**使用示例**:

```python
# 通过注册表获取
operator = operator_registry.get_operator("fractal", "fractional_feature")(1000)

# 直接导入
from tte.src.operators.rust.target.debug import tte_operators
operator = tte_operators.FractionalFeatureOperator(1000)

# 计算分数阶导数
import numpy as np
x = np.linspace(0, 10, 1000)
y = np.sin(x) + 0.1 * np.random.randn(len(x))
result = operator.fractional_derivative(y.tolist(), 0.5)
```

**应用场景**:
- 分数阶信号处理
- 长记忆时间序列分析
- 分形特征提取

### 分形维数算子

分形维数算子用于计算时间序列的分形维数，可以量化时间序列的复杂度和自相似性。

**类名**: `FractalDimensionOperator`  
**注册表路径**: `fractal/fractal_dimension`

**初始化参数**:
- `dim` (int): 算子维度，影响内部计算精度

**主要方法**:
- `box_counting_dimension(data, min_scale, max_scale, num_scales)`: 计算时间序列的盒维数
  - `data` (List[float]): 输入时间序列
  - `min_scale` (float): 最小尺度
  - `max_scale` (float): 最大尺度
  - `num_scales` (int): 尺度数量
  - 返回: BoxCountingResult - 包含维数、拟合优度等信息的结果对象

**结果对象属性**:
- `dimension` (float): 计算得到的盒维数
- `fit_r2` (float): 拟合优度，越接近1表示拟合越好
- `scale_points` (List[float]): 用于计算的尺度点
- `box_counts` (List[float]): 对应尺度的盒子数量

**使用示例**:

```python
# 通过注册表获取
operator = operator_registry.get_operator("fractal", "fractal_dimension")(1000)

# 直接导入
from tte.src.operators.rust.target.debug import tte_operators
operator = tte_operators.FractalDimensionOperator(1000)

# 计算盒维数
import numpy as np
t = np.linspace(0, 10, 1000)
brownian = np.cumsum(np.random.randn(len(t)))
result = operator.box_counting_dimension(brownian.tolist(), 0.1, 1.0, 10)
print(f"盒维数: {result.dimension}")
print(f"拟合优度: {result.fit_r2}")
```

**应用场景**:
- 时间序列复杂度分析
- 分形特征提取
- 混沌系统分析

### 分形插值算子

分形插值算子用于生成保留自相似性和分形特性的插值曲线，可以在保持数据分形特性的同时进行插值。

**类名**: `FractalInterpolationOperator`  
**注册表路径**: `fractal/fractal_interpolation`

**初始化参数**:
- `dim` (int): 算子维度，影响内部计算精度

**主要方法**:
- `fractal_interpolation(series, level, vertical_scaling=None)`: 执行分形插值
  - `series` (List[float]): 原始数据点序列
  - `level` (int): 插值细化级别，决定了生成点的密度
  - `vertical_scaling` (Optional[List[float]]): 可选的垂直缩放因子列表，控制分形的"粗糙度"
  - 返回: List[float] - 插值后的数据点序列

**使用示例**:

```python
# 通过注册表获取
operator = operator_registry.get_operator("fractal", "fractal_interpolation")(1000)

# 直接导入
from tte.src.operators.rust.target.debug import tte_operators
operator = tte_operators.FractalInterpolationOperator(1000)

# 执行分形插值
import numpy as np
x = np.linspace(0, 10, 10)
y = np.sin(x)
result = operator.fractal_interpolation(y.tolist(), 3, None)
```

**应用场景**:
- 分形数据插值
- 自然景观生成
- 分形艺术创作

## 熵算子

### 多尺度熵算子

多尺度熵算子用于计算时间序列在不同尺度上的熵值，可以评估时间序列在不同时间尺度上的复杂性。

**类名**: `MultiscaleEntropyOperator`  
**注册表路径**: `entropy/multiscale_entropy`

**初始化参数**:
- 无需参数

**主要方法**:
- `compute_multiscale_entropy(series, max_scale, embedding_dim, tolerance_factor)`: 计算多尺度熵
  - `series` (List[float]): 输入时间序列
  - `max_scale` (int): 最大尺度
  - `embedding_dim` (int): 嵌入维度
  - `tolerance_factor` (float): 容差因子
  - 返回: MultiscaleEntropyResult - 包含熵值、复杂度指数等信息的结果对象

**结果对象属性**:
- `entropy_values` (List[float]): 不同尺度的熵值
- `complexity_index` (float): 复杂度指数（熵值的加权平均）
- `scales` (List[int]): 计算的尺度列表
- `embedding_dimension` (int): 使用的嵌入维度
- `tolerance` (float): 使用的容差

**使用示例**:

```python
# 通过注册表获取
operator = operator_registry.get_operator("entropy", "multiscale_entropy")()

# 直接导入
from tte.src.operators.rust.target.debug import tte_operators
operator = tte_operators.MultiscaleEntropyOperator()

# 计算多尺度熵
import numpy as np
np.random.seed(42)
random_data = np.random.rand(1000)
result = operator.compute_multiscale_entropy(random_data.tolist(), 5, 2, 0.2)
print(f"熵值: {result.entropy_values}")
print(f"复杂度指数: {result.complexity_index}")
```

**应用场景**:
- 生理信号分析
- 复杂系统评估
- 时间序列复杂度量化

## 变换算子

### 小波变换算子

小波变换算子用于执行离散小波变换和逆变换，可以进行多分辨率分析和信号处理。

**类名**: `WaveletTransformOperator`  
**注册表路径**: `transform/wavelet_transform`

**初始化参数**:
- `thread_count` (int): 并行计算的线程数

**主要方法**:
- `discrete_wavelet_transform(signal, wavelet, level)`: 执行离散小波变换
  - `signal` (List[float]): 输入信号
  - `wavelet` (str): 小波类型，如"db4"、"haar"等
  - `level` (int): 分解级别
  - 返回: Dict - 包含近似系数和细节系数的字典
- `inverse_discrete_wavelet_transform(approx, details, wavelet, original_length)`: 执行逆离散小波变换
  - `approx` (List[float]): 近似系数
  - `details` (List[List[float]]): 细节系数列表
  - `wavelet` (str): 小波类型
  - `original_length` (int): 原始信号长度
  - 返回: List[float] - 重构的信号

**使用示例**:

```python
# 通过注册表获取
operator = operator_registry.get_operator("transform", "wavelet_transform")(4)

# 直接导入
from tte.src.operators.rust.target.debug import tte_operators
operator = tte_operators.WaveletTransformOperator(4)

# 执行离散小波变换
import numpy as np
t = np.linspace(0, 1, 512)
signal = np.sin(2 * np.pi * 10 * t) + 0.5 * np.sin(2 * np.pi * 25 * t)
result = operator.discrete_wavelet_transform(signal.tolist(), "db4", 3)
print(f"近似系数长度: {len(result['approx'])}")
print(f"细节系数数量: {len(result['details'])}")

# 执行逆离散小波变换
reconstructed = operator.inverse_discrete_wavelet_transform(
    result['approx'], 
    result['details'], 
    "db4", 
    len(signal)
)
error = np.mean(np.abs(np.array(signal) - np.array(reconstructed)))
print(f"重构误差: {error}")
```

**应用场景**:
- 信号去噪
- 特征提取
- 图像压缩
- 多分辨率分析

## 工具算子

### 复数包装类型

复数包装类型用于在Python和Rust之间传递复数，提供了复数的基本操作。

**类名**: `PyComplex64`  
**注册表路径**: `utility/complex64`

**初始化参数**:
- `real` (float): 实部
- `imag` (float): 虚部

**属性**:
- `real` (float): 实部
- `imag` (float): 虚部

**使用示例**:

```python
# 通过注册表获取
complex_class = operator_registry.get_operator("utility", "complex64")

# 直接导入
from tte.src.operators.rust.target.debug import tte_operators
complex_class = tte_operators.PyComplex64

# 创建复数
c = complex_class(1.0, 2.0)
print(f"复数: {c}")
print(f"实部: {c.real}, 虚部: {c.imag}")
```

**应用场景**:
- 复数运算
- 信号处理
- 傅里叶变换

## 性能优化建议

1. **并行计算**：这些Rust算子已经支持无GIL线程模式，可以充分利用多核处理器的性能优势。对于大规模数据处理，建议使用多线程。

2. **数据类型转换**：在Python和Rust之间传递数据时，尽量减少数据类型转换的次数，以提高性能。

3. **内存管理**：对于大型数据集，注意内存使用情况，可以考虑使用流式处理或分块处理。

4. **算子组合**：通过注册表系统，可以灵活组合不同的算子，构建复杂的处理流程。

## 常见问题解答

**Q: 如何解决导入Rust模块时的错误？**

A: 确保已经创建了从`libtte_operators.so`到`tte_operators.so`的符号链接，并将包含`.so`文件的目录添加到Python路径中。

```python
import sys
from pathlib import Path
rust_dir = Path("/path/to/TTE/src/operators/rust")
target_debug = rust_dir / "target" / "debug"
sys.path.insert(0, str(target_debug))
```

**Q: 为什么某些算子需要指定维度参数？**

A: 维度参数影响算子内部的计算精度和内存分配。对于不同的应用场景，可能需要不同的维度设置。

**Q: 如何选择合适的小波类型？**

A: 不同的小波类型适用于不同的信号特性。例如，Haar小波适合处理不连续信号，而Daubechies小波适合处理平滑信号。建议根据具体应用场景选择合适的小波类型。

**Q: 多尺度熵的参数如何选择？**

A: 嵌入维度通常选择2或3，容差因子通常选择0.1到0.25之间。最大尺度取决于数据长度，通常不超过数据长度的10%。
