# 插件管理算子

本目录包含与插件管理相关的算子，如插件管理器、依赖解析器和插件加载器。这些算子是超越态思维引擎的工程实现支持组件，用于实现插件的加载、卸载和依赖解析。

## 算子列表

### 已实现算子

1. **插件管理算子 (PluginManagerOperator)**
   - 文件：`src/operators/plugin_management/plugin_manager.py`
   - 功能：管理插件的生命周期，包括加载、卸载、启用、禁用和更新插件
   - 特性：支持自动发现、自动加载和自动启用插件，支持严格依赖模式

2. **依赖解析算子 (DependencyResolverOperator)**
   - 文件：`src/operators/plugin_management/dependency_resolver.py`
   - 功能：解析插件之间的依赖关系，确定插件的加载顺序
   - 特性：支持循环依赖检测，支持拓扑排序，支持版本兼容性检查

3. **插件加载算子 (PluginLoaderOperator)**
   - 文件：`src/operators/plugin_management/plugin_loader.py`
   - 功能：加载和卸载插件
   - 特性：支持从文件或目录加载插件，支持自动重新加载，支持插件验证

4. **插件注册表 (PluginRegistry)**
   - 文件：`src/operators/plugin_management/plugin_registry.py`
   - 功能：管理已加载的插件，包括插件的元数据、依赖关系和状态
   - 特性：支持钩子机制，支持线程安全，支持插件路径管理

## 使用示例

### 插件管理

```python
from src.operators.plugin_management import PluginManagerOperator

# 创建插件管理器
manager = PluginManagerOperator(
    plugin_dirs=['/path/to/plugins'],
    auto_discover=True,
    auto_load=True,
    auto_enable=False
)

# 发现插件
result = manager.apply({
    'operation': 'discover'
})

# 加载插件
result = manager.apply({
    'operation': 'load',
    'plugin_id': 'my_plugin'
})

# 启用插件
result = manager.apply({
    'operation': 'enable',
    'plugin_id': 'my_plugin'
})

# 禁用插件
result = manager.apply({
    'operation': 'disable',
    'plugin_id': 'my_plugin'
})

# 卸载插件
result = manager.apply({
    'operation': 'unload',
    'plugin_id': 'my_plugin'
})

# 更新插件
result = manager.apply({
    'operation': 'update',
    'plugin_id': 'my_plugin'
})

# 获取插件信息
result = manager.apply({
    'operation': 'get_info',
    'plugin_id': 'my_plugin'
})

# 列出所有插件
result = manager.apply({
    'operation': 'list'
})

# 管理多个插件
result = manager.apply({
    'operation': 'manage',
    'operations': [
        {
            'operation': 'load',
            'data': {
                'plugin_id': 'plugin1'
            }
        },
        {
            'operation': 'enable',
            'data': {
                'plugin_id': 'plugin1'
            }
        }
    ]
})
```

### 依赖解析

```python
from src.operators.plugin_management import DependencyResolverOperator

# 创建依赖解析器
resolver = DependencyResolverOperator(
    strict_mode=True,
    auto_resolve=True,
    version_check=True
)

# 解析依赖
result = resolver.apply({
    'operation': 'resolve',
    'plugin_id': 'my_plugin'
})

# 检查依赖
result = resolver.apply({
    'operation': 'check',
    'plugin_id': 'my_plugin'
})

# 确定加载顺序
result = resolver.apply({
    'operation': 'order',
    'plugin_ids': ['plugin1', 'plugin2', 'plugin3']
})
```

### 插件加载

```python
from src.operators.plugin_management import PluginLoaderOperator

# 创建插件加载器
loader = PluginLoaderOperator(
    auto_reload=False,
    validate_plugins=True
)

# 发现插件
result = loader.apply({
    'operation': 'discover',
    'search_dirs': ['/path/to/plugins']
})

# 加载插件
result = loader.apply({
    'operation': 'load',
    'plugin_path': '/path/to/plugins/my_plugin.py'
})

# 卸载插件
result = loader.apply({
    'operation': 'unload',
    'plugin_id': 'my_plugin'
})

# 重新加载插件
result = loader.apply({
    'operation': 'reload',
    'plugin_id': 'my_plugin'
})
```

### 插件注册表

```python
from src.operators.plugin_management import PluginRegistry

# 创建插件注册表
registry = PluginRegistry()

# 添加插件路径
registry.add_plugin_path('/path/to/plugins')

# 注册插件
plugin_id = 'my_plugin'
plugin_info = {
    'name': 'My Plugin',
    'version': '0.1.0',
    'description': 'A sample plugin',
    'author': 'John Doe',
    'dependencies': ['other_plugin']
}
registry.register_plugin(plugin_id, plugin_info)

# 获取插件信息
plugin_info = registry.get_plugin_info(plugin_id)

# 获取插件依赖
dependencies = registry.get_plugin_dependencies(plugin_id)

# 获取依赖插件的插件
dependents = registry.get_plugin_dependents(plugin_id)

# 设置插件状态
registry.set_plugin_state(plugin_id, 'loaded')

# 获取插件状态
state = registry.get_plugin_state(plugin_id)

# 注册钩子
def plugin_loaded_hook(plugin_id, plugin_info):
    print(f"Plugin loaded: {plugin_id}")

registry.register_hook('plugin_loaded', plugin_loaded_hook)

# 取消注册插件
registry.unregister_plugin(plugin_id)
```

## 插件开发

要开发一个与插件管理算子兼容的插件，需要遵循以下规范：

1. 插件类需要定义以下属性：
   - `plugin_id`：插件ID，唯一标识符
   - `plugin_name`：插件名称
   - `plugin_version`：插件版本
   - `plugin_description`：插件描述
   - `plugin_author`：插件作者
   - `plugin_dependencies`：插件依赖，列表形式

2. 插件类需要实现以下方法：
   - `__init__`：初始化方法
   - `enable`：启用插件
   - `disable`：禁用插件

示例：

```python
class MyPlugin:
    plugin_id = "my_plugin"
    plugin_name = "My Plugin"
    plugin_version = "0.1.0"
    plugin_description = "A sample plugin"
    plugin_author = "John Doe"
    plugin_dependencies = ["other_plugin"]
    
    def __init__(self):
        self.enabled = False
    
    def enable(self):
        self.enabled = True
        print("Plugin enabled")
    
    def disable(self):
        self.enabled = False
        print("Plugin disabled")
```

## Rust实现

部分算子已经有Rust实现，可以通过以下方式使用：

```python
from src.operators.plugin_management import (
    RustPluginManagerOperator,
    RustDependencyResolverOperator,
    RustPluginLoaderOperator,
    RUST_AVAILABLE
)

if RUST_AVAILABLE:
    # 使用Rust实现
    manager = RustPluginManagerOperator(
        plugin_dirs=['/path/to/plugins'],
        auto_discover=True,
        auto_load=True,
        auto_enable=False
    )
    # ...
else:
    # 使用Python实现
    manager = PluginManagerOperator(
        plugin_dirs=['/path/to/plugins'],
        auto_discover=True,
        auto_load=True,
        auto_enable=False
    )
    # ...
```

## 性能特性

- 时间复杂度：大多数操作为O(n)，其中n为插件数量；依赖解析为O(V+E)，其中V为插件数量，E为依赖关系数量
- 空间复杂度：大多数操作为O(n)或O(V+E)
- 并行化：部分算子支持并行计算
- 内存使用：优化的内存使用，支持大规模插件管理

## 注意事项

- 对于大规模插件管理，建议使用Rust实现以获得更好的性能
- 确保插件依赖关系的正确性，特别是在使用严格依赖模式时
- 插件加载顺序由依赖关系决定，确保没有循环依赖
- 插件状态变更会触发相应的钩子，可以通过注册钩子来监听状态变更
