## 2. 分形动力学路由算法

分形动力学路由算法是一种基于分形理论和动力学系统的高效路由算法，特别适用于大规模、动态变化的复杂网络环境。

### 2.1 基本使用教程

#### 安装与导入

```python
# 导入优化版分形动力学路由算法
from src.algorithms.routing.fractal_router_optimized import OptimizedFractalDynamicsRouter
import networkx as nx
import numpy as np
```

#### 基本用法

```python
# 创建网络图
G = nx.grid_2d_graph(10, 10)  # 10x10网格图

# 添加边权重
for u, v in G.edges():
    G[u][v]['weight'] = 1.0

# 创建路由器
router = OptimizedFractalDynamicsRouter(
    fractal_dimension=1.5,
    stability_threshold=0.01,
    max_iterations=100,
    damping_factor=0.85,
    exploration_factor=0.2
)

# 执行路由计算
result = router.compute({
    'network': G,
    'source': (0, 0),
    'target': (9, 9)
})

# 获取结果
path = result['path']
cost = result['cost']
iterations = result['iterations']
convergence_achieved = result['convergence_achieved']

print(f"最优路径: {path}")
print(f"路径成本: {cost}")
print(f"迭代次数: {iterations}")
print(f"是否收敛: {convergence_achieved}")
```

#### 带约束的路由

```python
# 创建网络图
G = nx.grid_2d_graph(10, 10)  # 10x10网格图

# 添加边权重和带宽
for u, v in G.edges():
    G[u][v]['weight'] = 1.0
    G[u][v]['bandwidth'] = 10.0

# 定义带宽约束
def bandwidth_constraint(path, G):
    min_bandwidth = float('inf')
    for i in range(len(path) - 1):
        u, v = path[i], path[i + 1]
        min_bandwidth = min(min_bandwidth, G[u][v]['bandwidth'])
    return min_bandwidth >= 5.0  # 要求带宽至少为5.0

# 创建路由器
router = OptimizedFractalDynamicsRouter(
    fractal_dimension=1.5,
    stability_threshold=0.01,
    max_iterations=100
)

# 执行路由计算
result = router.compute({
    'network': G,
    'source': (0, 0),
    'target': (9, 9),
    'constraints': [bandwidth_constraint]
})

# 获取结果
path = result['path']
cost = result['cost']

print(f"最优路径: {path}")
print(f"路径成本: {cost}")
```

### 2.2 常见应用场景

#### 计算机网络路由

分形动力学路由算法适用于各种计算机网络路由问题，如数据中心网络、广域网等。

```python
# 创建数据中心网络拓扑（Fat-Tree拓扑）
def create_fat_tree(k):
    G = nx.Graph()
    
    # 创建核心层、汇聚层和接入层交换机
    core_switches = [f"core_{i}" for i in range(k*k//4)]
    aggregation_switches = [f"agg_{pod}_{i}" for pod in range(k) for i in range(k//2)]
    edge_switches = [f"edge_{pod}_{i}" for pod in range(k) for i in range(k//2)]
    
    # 添加节点
    G.add_nodes_from(core_switches)
    G.add_nodes_from(aggregation_switches)
    G.add_nodes_from(edge_switches)
    
    # 连接核心层和汇聚层
    for i, core in enumerate(core_switches):
        for pod in range(k):
            G.add_edge(core, f"agg_{pod}_{i//(k//2)}", weight=1.0, bandwidth=10.0)
    
    # 连接汇聚层和接入层
    for pod in range(k):
        for i in range(k//2):
            for j in range(k//2):
                G.add_edge(f"agg_{pod}_{i}", f"edge_{pod}_{j}", weight=1.0, bandwidth=10.0)
    
    return G

# 创建数据中心网络
dc_network = create_fat_tree(k=4)

# 创建路由器
router = OptimizedFractalDynamicsRouter(
    fractal_dimension=1.5,
    stability_threshold=0.01,
    max_iterations=100
)

# 执行路由计算
result = router.compute({
    'network': dc_network,
    'source': "edge_0_0",
    'target': "edge_3_1"
})

# 获取结果
path = result['path']
print(f"数据中心网络路由路径: {path}")
```

#### 交通路网规划

分形动力学路由算法可用于交通路网规划，考虑道路拥堵、交通流量等因素。

```python
# 创建交通路网
traffic_network = nx.Graph()

# 添加节点（交叉口）
intersections = ["A", "B", "C", "D", "E", "F", "G", "H"]
traffic_network.add_nodes_from(intersections)

# 添加边（道路）
roads = [
    ("A", "B", {"distance": 5, "congestion": 0.2, "capacity": 1000}),
    ("A", "C", {"distance": 7, "congestion": 0.1, "capacity": 800}),
    ("B", "D", {"distance": 3, "congestion": 0.3, "capacity": 1200}),
    ("C", "D", {"distance": 4, "congestion": 0.4, "capacity": 900}),
    ("B", "E", {"distance": 6, "congestion": 0.1, "capacity": 1100}),
    ("D", "F", {"distance": 5, "congestion": 0.2, "capacity": 1000}),
    ("E", "G", {"distance": 4, "congestion": 0.3, "capacity": 950}),
    ("F", "G", {"distance": 3, "congestion": 0.1, "capacity": 1300}),
    ("F", "H", {"distance": 6, "congestion": 0.2, "capacity": 1100}),
    ("G", "H", {"distance": 5, "congestion": 0.3, "capacity": 1000})
]
traffic_network.add_edges_from(roads)

# 计算边权重（考虑距离和拥堵）
for u, v, data in traffic_network.edges(data=True):
    data["weight"] = data["distance"] * (1 + data["congestion"])

# 定义容量约束
def capacity_constraint(path, G):
    # 检查路径上的所有道路是否满足容量要求
    for i in range(len(path) - 1):
        u, v = path[i], path[i + 1]
        if G[u][v]["capacity"] < 1000:  # 要求容量至少为1000
            return False
    return True

# 创建路由器
router = OptimizedFractalDynamicsRouter(
    fractal_dimension=1.5,
    stability_threshold=0.01,
    max_iterations=100
)

# 执行路由计算
result = router.compute({
    'network': traffic_network,
    'source': "A",
    'target': "H",
    'constraints': [capacity_constraint]
})

# 获取结果
path = result['path']
cost = result['cost']

print(f"最优交通路径: {path}")
print(f"路径成本: {cost}")
```

#### 物流配送路径优化

分形动力学路由算法可用于物流配送路径优化，考虑配送时间、成本等因素。

```python
# 创建物流网络
logistics_network = nx.Graph()

# 添加节点（仓库、配送点）
locations = ["Warehouse", "Store1", "Store2", "Store3", "Store4", "Store5"]
logistics_network.add_nodes_from(locations)

# 添加边（运输路线）
routes = [
    ("Warehouse", "Store1", {"distance": 10, "time": 30, "cost": 50}),
    ("Warehouse", "Store2", {"distance": 15, "time": 45, "cost": 70}),
    ("Warehouse", "Store3", {"distance": 20, "time": 60, "cost": 90}),
    ("Store1", "Store2", {"distance": 8, "time": 25, "cost": 40}),
    ("Store1", "Store4", {"distance": 12, "time": 35, "cost": 60}),
    ("Store2", "Store3", {"distance": 9, "time": 30, "cost": 45}),
    ("Store2", "Store4", {"distance": 10, "time": 30, "cost": 50}),
    ("Store2", "Store5", {"distance": 14, "time": 40, "cost": 65}),
    ("Store3", "Store5", {"distance": 11, "time": 35, "cost": 55}),
    ("Store4", "Store5", {"distance": 7, "time": 20, "cost": 35})
]
logistics_network.add_edges_from(routes)

# 计算边权重（考虑距离、时间和成本的加权和）
for u, v, data in logistics_network.edges(data=True):
    data["weight"] = 0.3 * data["distance"] + 0.4 * data["time"] + 0.3 * data["cost"]

# 定义时间约束
def time_constraint(path, G):
    total_time = 0
    for i in range(len(path) - 1):
        u, v = path[i], path[i + 1]
        total_time += G[u][v]["time"]
    return total_time <= 120  # 总时间不超过120分钟

# 创建路由器
router = OptimizedFractalDynamicsRouter(
    fractal_dimension=1.5,
    stability_threshold=0.01,
    max_iterations=100
)

# 执行路由计算
result = router.compute({
    'network': traffic_network,
    'source': "Warehouse",
    'target': "Store5",
    'constraints': [time_constraint]
})

# 获取结果
path = result['path']
cost = result['cost']

print(f"最优物流配送路径: {path}")
print(f"路径成本: {cost}")
```
