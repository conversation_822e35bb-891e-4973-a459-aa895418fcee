## 3. 博弈优化资源调度算法

博弈优化资源调度算法是一种基于博弈论的高效资源分配方法，特别适用于多智能体环境中的资源竞争和协作场景。

### 3.1 基本使用教程

#### 安装与导入

```python
# 导入优化版博弈优化资源调度算法
from src.algorithms.game_theory.scheduler_optimized import OptimizedGameTheoreticScheduler
import numpy as np
```

#### 基本用法

```python
# 创建资源
num_agents = 10
num_resources = 5

# 资源容量
capacities = np.random.uniform(50, 100, num_resources)

# 代理需求
demands = np.random.uniform(1, 10, (num_agents, num_resources))

# 代理优先级
priorities = np.random.uniform(0.1, 1.0, (num_agents, num_resources))

# 代理效用
utilities = np.random.uniform(1, 20, (num_agents, num_resources))

# 创建调度器
scheduler = OptimizedGameTheoreticScheduler(
    game_type='non_cooperative',
    learning_rate=0.3,
    max_iterations=100,
    tolerance=1e-6,
    fairness_weight=0.5,
    efficiency_weight=0.5
)

# 执行调度计算
result = scheduler.compute({
    'capacities': capacities,
    'demands': demands,
    'priorities': priorities,
    'utilities': utilities
})

# 获取结果
allocations = result['allocations']
utilities = result['utilities']
iterations = result['iterations']
convergence_achieved = result['convergence_achieved']

print(f"资源分配:\n{allocations}")
print(f"代理效用:\n{utilities}")
print(f"迭代次数: {iterations}")
print(f"是否收敛: {convergence_achieved}")
```

#### 使用不同的博弈类型

```python
# 创建合作博弈调度器
cooperative_scheduler = OptimizedGameTheoreticScheduler(
    game_type='cooperative',
    learning_rate=0.3,
    max_iterations=100,
    tolerance=1e-6,
    fairness_weight=0.7,  # 更注重公平性
    efficiency_weight=0.3
)

# 执行调度计算
cooperative_result = cooperative_scheduler.compute({
    'capacities': capacities,
    'demands': demands,
    'priorities': priorities,
    'utilities': utilities
})

# 创建斯塔克伯格博弈调度器
stackelberg_scheduler = OptimizedGameTheoreticScheduler(
    game_type='stackelberg',
    learning_rate=0.3,
    max_iterations=100,
    tolerance=1e-6,
    fairness_weight=0.3,  # 更注重效率
    efficiency_weight=0.7
)

# 执行调度计算
stackelberg_result = stackelberg_scheduler.compute({
    'capacities': capacities,
    'demands': demands,
    'priorities': priorities,
    'utilities': utilities,
    'leader_agents': [0, 1]  # 指定领导者代理
})

# 比较不同博弈类型的结果
print("非合作博弈分配:")
print(result['allocations'])
print("\n合作博弈分配:")
print(cooperative_result['allocations'])
print("\n斯塔克伯格博弈分配:")
print(stackelberg_result['allocations'])
```

### 3.2 常见应用场景

#### 计算资源分配

博弈优化资源调度算法适用于云计算环境中的计算资源分配。

```python
# 创建计算资源
num_vms = 5  # 虚拟机数量
num_apps = 8  # 应用数量

# 资源容量（CPU核心数）
cpu_capacities = np.array([8, 16, 32, 64, 128])

# 应用需求（每个应用对每个VM的CPU需求）
cpu_demands = np.array([
    [2, 4, 8, 16, 32],  # 应用1
    [4, 8, 16, 32, 64],  # 应用2
    [1, 2, 4, 8, 16],   # 应用3
    [2, 4, 8, 16, 32],  # 应用4
    [1, 2, 4, 8, 16],   # 应用5
    [3, 6, 12, 24, 48], # 应用6
    [2, 4, 8, 16, 32],  # 应用7
    [1, 2, 4, 8, 16]    # 应用8
])

# 应用优先级
priorities = np.array([
    [0.9, 0.8, 0.7, 0.6, 0.5],  # 应用1
    [0.8, 0.7, 0.6, 0.5, 0.4],  # 应用2
    [0.7, 0.6, 0.5, 0.4, 0.3],  # 应用3
    [0.6, 0.5, 0.4, 0.3, 0.2],  # 应用4
    [0.5, 0.4, 0.3, 0.2, 0.1],  # 应用5
    [0.9, 0.8, 0.7, 0.6, 0.5],  # 应用6
    [0.8, 0.7, 0.6, 0.5, 0.4],  # 应用7
    [0.7, 0.6, 0.5, 0.4, 0.3]   # 应用8
])

# 应用效用（性能收益）
utilities = np.array([
    [10, 15, 20, 25, 30],  # 应用1
    [12, 18, 24, 30, 36],  # 应用2
    [8, 12, 16, 20, 24],   # 应用3
    [10, 15, 20, 25, 30],  # 应用4
    [6, 9, 12, 15, 18],    # 应用5
    [14, 21, 28, 35, 42],  # 应用6
    [10, 15, 20, 25, 30],  # 应用7
    [8, 12, 16, 20, 24]    # 应用8
])

# 创建调度器
scheduler = OptimizedGameTheoreticScheduler(
    game_type='non_cooperative',
    learning_rate=0.3,
    max_iterations=100,
    fairness_weight=0.6,  # 更注重公平性
    efficiency_weight=0.4
)

# 执行调度计算
result = scheduler.compute({
    'capacities': cpu_capacities,
    'demands': cpu_demands,
    'priorities': priorities,
    'utilities': utilities
})

# 获取结果
allocations = result['allocations']

# 打印每个应用的资源分配
for i, app_allocation in enumerate(allocations):
    print(f"应用 {i+1} 的CPU分配: {app_allocation}")

# 计算资源利用率
utilization = np.sum(allocations, axis=0) / cpu_capacities
print(f"\n资源利用率: {utilization}")
```

#### 网络带宽分配

博弈优化资源调度算法适用于网络带宽分配问题。

```python
# 创建网络资源
num_links = 3  # 链路数量
num_flows = 6  # 流数量

# 链路容量（Mbps）
link_capacities = np.array([100, 200, 150])

# 流量需求（每个流对每个链路的带宽需求）
bandwidth_demands = np.array([
    [50, 80, 60],   # 流1
    [30, 50, 40],   # 流2
    [20, 30, 25],   # 流3
    [40, 60, 50],   # 流4
    [25, 40, 30],   # 流5
    [35, 55, 45]    # 流6
])

# 流优先级
priorities = np.array([
    [0.9, 0.9, 0.9],  # 流1（高优先级）
    [0.7, 0.7, 0.7],  # 流2（中优先级）
    [0.5, 0.5, 0.5],  # 流3（低优先级）
    [0.8, 0.8, 0.8],  # 流4（中高优先级）
    [0.6, 0.6, 0.6],  # 流5（中低优先级）
    [0.4, 0.4, 0.4]   # 流6（最低优先级）
])

# 流效用（QoS收益）
utilities = np.array([
    [10, 12, 11],  # 流1
    [8, 10, 9],    # 流2
    [6, 8, 7],     # 流3
    [9, 11, 10],   # 流4
    [7, 9, 8],     # 流5
    [5, 7, 6]      # 流6
])

# 创建调度器
scheduler = OptimizedGameTheoreticScheduler(
    game_type='non_cooperative',
    learning_rate=0.3,
    max_iterations=100,
    equilibrium_algorithm='fictitious_play'
)

# 执行调度计算
result = scheduler.compute({
    'capacities': link_capacities,
    'demands': bandwidth_demands,
    'priorities': priorities,
    'utilities': utilities
})

# 获取结果
allocations = result['allocations']

# 打印每个流的带宽分配
for i, flow_allocation in enumerate(allocations):
    print(f"流 {i+1} 的带宽分配: {flow_allocation} Mbps")

# 计算链路利用率
utilization = np.sum(allocations, axis=0) / link_capacities
print(f"\n链路利用率: {utilization}")
```

#### 电力资源分配

博弈优化资源调度算法适用于智能电网中的电力资源分配。

```python
# 创建电力资源
num_sources = 4  # 电力来源数量（如风能、太阳能、水能、火力）
num_consumers = 7  # 消费者数量

# 电力容量（kW）
power_capacities = np.array([500, 300, 400, 600])

# 电力需求
power_demands = np.array([
    [100, 50, 80, 120],   # 消费者1
    [80, 40, 60, 100],    # 消费者2
    [120, 60, 90, 150],   # 消费者3
    [90, 45, 70, 110],    # 消费者4
    [70, 35, 55, 85],     # 消费者5
    [110, 55, 85, 130],   # 消费者6
    [60, 30, 45, 75]      # 消费者7
])

# 消费者优先级（如医院、学校、住宅、工厂等）
priorities = np.array([
    [0.9, 0.9, 0.9, 0.9],  # 消费者1（医院，最高优先级）
    [0.8, 0.8, 0.8, 0.8],  # 消费者2（学校，高优先级）
    [0.6, 0.6, 0.6, 0.6],  # 消费者3（住宅，中优先级）
    [0.7, 0.7, 0.7, 0.7],  # 消费者4（商业，中高优先级）
    [0.5, 0.5, 0.5, 0.5],  # 消费者5（工厂，中低优先级）
    [0.4, 0.4, 0.4, 0.4],  # 消费者6（娱乐，低优先级）
    [0.3, 0.3, 0.3, 0.3]   # 消费者7（其他，最低优先级）
])

# 消费者效用（基于电力来源的偏好）
utilities = np.array([
    [8, 10, 9, 7],    # 消费者1
    [7, 9, 8, 6],     # 消费者2
    [9, 7, 8, 10],    # 消费者3
    [8, 9, 7, 6],     # 消费者4
    [6, 8, 10, 9],    # 消费者5
    [7, 6, 8, 9],     # 消费者6
    [9, 8, 7, 6]      # 消费者7
])

# 创建调度器（使用合作博弈，因为电力分配通常需要更多合作）
scheduler = OptimizedGameTheoreticScheduler(
    game_type='cooperative',
    learning_rate=0.3,
    max_iterations=100,
    fairness_weight=0.7,  # 更注重公平性
    efficiency_weight=0.3
)

# 执行调度计算
result = scheduler.compute({
    'capacities': power_capacities,
    'demands': power_demands,
    'priorities': priorities,
    'utilities': utilities
})

# 获取结果
allocations = result['allocations']

# 打印每个消费者的电力分配
for i, consumer_allocation in enumerate(allocations):
    print(f"消费者 {i+1} 的电力分配: {consumer_allocation} kW")

# 计算电力来源利用率
utilization = np.sum(allocations, axis=0) / power_capacities
print(f"\n电力来源利用率: {utilization}")

# 计算每个消费者的满足率
satisfaction = np.sum(allocations, axis=1) / np.sum(power_demands, axis=1)
print(f"\n消费者需求满足率: {satisfaction}")
```

### 3.3 最佳实践

#### 参数设置建议

- **博弈类型（game_type）**：根据代理之间的关系选择合适的博弈类型。
  - `non_cooperative`：适用于代理之间竞争关系强的场景，如市场竞争。
  - `cooperative`：适用于代理之间合作关系强的场景，如团队协作。
  - `stackelberg`：适用于有领导者和跟随者的场景，如主从架构。

- **学习率（learning_rate）**：控制策略更新的步长，通常在0.1-0.5之间。
  - 较大值（0.3-0.5）：收敛更快，但可能不稳定。
  - 较小值（0.1-0.3）：收敛更慢，但更稳定。

- **最大迭代次数（max_iterations）**：根据问题规模设置，通常50-200次迭代足够。

- **收敛容差（tolerance）**：控制算法的收敛条件，通常在1e-4到1e-8之间。

- **公平性权重（fairness_weight）**和**效率权重（efficiency_weight）**：控制公平性与效率的平衡，两者之和通常为1。
  - 较大的公平性权重：更注重资源分配的公平性。
  - 较大的效率权重：更注重资源分配的效率。

- **均衡算法（equilibrium_algorithm）**：选择合适的均衡求解算法。
  - `fictitious_play`：适用于一般问题，平衡收敛速度和稳定性。
  - `best_response`：适用于简单问题，收敛更快。
  - `replicator_dynamics`：适用于复杂问题，更稳定但收敛更慢。

#### 性能优化技巧

1. **并行计算**：对于大规模问题，启用并行计算可以显著提高性能。
   ```python
   scheduler = OptimizedGameTheoreticScheduler(
       use_parallel=True,
       num_workers=8  # 根据CPU核心数设置
   )
   ```

2. **缓存机制**：对于重复计算较多的场景，启用缓存可以避免重复计算。
   ```python
   scheduler = OptimizedGameTheoreticScheduler(
       use_cache=True,
       cache_size=1000  # 根据内存大小设置
   )
   ```

3. **增量更新**：对于资源环境小幅变化的场景，使用增量更新可以提高效率。
   ```python
   # 第一次调度计算
   result1 = scheduler.compute({
       'capacities': capacities1,
       'demands': demands1,
       'priorities': priorities,
       'utilities': utilities
   })
   
   # 资源环境发生变化
   capacities2 = capacities1.copy()
   capacities2[0] += 10  # 第一个资源容量增加
   
   demands2 = demands1.copy()
   demands2[0, 0] += 2  # 第一个代理对第一个资源的需求增加
   
   # 使用增量更新进行第二次调度计算
   result2 = scheduler.compute({
       'capacities': capacities2,
       'demands': demands2,
       'priorities': priorities,
       'utilities': utilities,
       'base_allocations': result1['allocations']
   })
   ```

4. **预测机制**：对于有历史数据的场景，启用预测可以加速收敛。
   ```python
   scheduler = OptimizedGameTheoreticScheduler(
       use_prediction=True
   )
   ```
