# Arrow扩展类型与Parquet存储指南

本文档提供了使用Arrow扩展类型和Parquet存储功能的详细指南，包括复数类型、量子态类型和张量类型的存储和读取。

## 环境要求

- Python 3.13+
- NumPy 2.2.5+
- PyArrow 14.0.0+
- PyArrow Parquet 14.0.0+

## 基本概念

### Arrow扩展类型

Arrow是一种跨语言的列式内存数据格式，适用于大数据处理和分析。我们扩展了Arrow的类型系统，添加了以下类型：

- **复数类型**：支持complex64和complex128
- **量子态类型**：支持量子态的存储和验证
- **张量类型**：支持多维数组的存储

### Parquet存储

Parquet是一种列式存储格式，具有高效的压缩和查询性能。我们提供了将Arrow扩展类型存储到Parquet文件的功能，支持：

- 单个数组的存储和读取
- 多个数组的批量存储和读取
- 数据集管理和分区存储

## 使用指南

### 1. 复数数组的存储和读取

```python
import numpy as np
from src.core.data.arrow_types.complex_array import (
    numpy_to_arrow_complex128, arrow_to_numpy_complex128
)
from src.core.data.arrow_types.parquet_storage import (
    write_arrow_to_parquet, read_parquet_to_arrow
)

# 创建复数数组
complex_data = np.array([1+2j, 3+4j, 5+6j], dtype=np.complex128)

# 转换为Arrow数组
arrow_array = numpy_to_arrow_complex128(complex_data)

# 写入Parquet文件
write_arrow_to_parquet([arrow_array], ["complex_data"], "complex_data.parquet")

# 读取Parquet文件
arrays = read_parquet_to_arrow("complex_data.parquet")

# 转换回NumPy数组
numpy_array = arrow_to_numpy_complex128(arrays["complex_data"])

# 验证数据
np.testing.assert_array_almost_equal(numpy_array, complex_data)
```

### 2. 量子态的存储和读取

```python
import numpy as np
from src.core.data.arrow_types.quantum_state_array import (
    numpy_to_arrow_quantum_state, arrow_to_numpy_quantum_state
)
from src.core.data.arrow_types.parquet_storage import (
    write_arrow_to_parquet, read_parquet_to_arrow
)

# 创建量子态
# |0⟩ 状态
state0 = np.array([1.0, 0.0], dtype=np.complex128)
# |+⟩ 状态
state_plus = np.array([1/np.sqrt(2), 1/np.sqrt(2)], dtype=np.complex128)

# 转换为Arrow数组
arrow_state0 = numpy_to_arrow_quantum_state(state0, validate=True)
arrow_state_plus = numpy_to_arrow_quantum_state(state_plus, validate=True)

# 写入Parquet文件
write_arrow_to_parquet(
    [arrow_state0, arrow_state_plus],
    ["state0", "state_plus"],
    "quantum_states.parquet"
)

# 读取Parquet文件
arrays = read_parquet_to_arrow("quantum_states.parquet")

# 转换回NumPy数组
numpy_state0 = arrow_to_numpy_quantum_state(arrays["state0"])
numpy_state_plus = arrow_to_numpy_quantum_state(arrays["state_plus"])

# 验证数据
np.testing.assert_array_almost_equal(numpy_state0, state0)
np.testing.assert_array_almost_equal(numpy_state_plus, state_plus)
```

### 3. 张量数组的存储和读取

```python
import numpy as np
from src.core.data.arrow_types.tensor_array import (
    numpy_to_arrow_tensor, arrow_to_numpy_tensor
)
from src.core.data.arrow_types.parquet_storage import (
    write_arrow_to_parquet, read_parquet_to_arrow
)

# 创建张量数组
tensor_2d = np.array([[1.0, 2.0], [3.0, 4.0]], dtype=np.float64)

# 转换为Arrow数组
arrow_tensor = numpy_to_arrow_tensor(tensor_2d)

# 写入Parquet文件
write_arrow_to_parquet([arrow_tensor], ["tensor"], "tensor_data.parquet")

# 读取Parquet文件
arrays = read_parquet_to_arrow("tensor_data.parquet")

# 转换回NumPy数组
numpy_tensor = arrow_to_numpy_tensor(arrays["tensor"])

# 验证数据
np.testing.assert_array_almost_equal(numpy_tensor, tensor_2d)
```

### 4. 使用NumPy数组直接存储和读取

```python
import numpy as np
from src.core.data.arrow_types.parquet_storage import (
    write_numpy_to_parquet, read_parquet_to_numpy
)

# 创建NumPy数组
arrays = {
    "float_data": np.array([1.0, 2.0, 3.0], dtype=np.float64),
    "int_data": np.array([1, 2, 3], dtype=np.int32),
    "complex_data": np.array([1+2j, 3+4j, 5+6j], dtype=np.complex128)
}

# 写入Parquet文件
write_numpy_to_parquet(arrays, "mixed_data.parquet")

# 读取Parquet文件
result = read_parquet_to_numpy("mixed_data.parquet")

# 验证数据
np.testing.assert_array_almost_equal(result["float_data"], arrays["float_data"])
np.testing.assert_array_almost_equal(result["int_data"], arrays["int_data"])
```

### 5. 使用Parquet数据集

```python
import numpy as np
import os
from src.core.data.arrow_types.parquet_storage import ParquetDataset

# 创建数据集
dataset = ParquetDataset("data/experiment_results")

# 创建数据
data1 = np.array([1.0, 2.0, 3.0], dtype=np.float64)
data2 = np.array([4.0, 5.0, 6.0], dtype=np.float64)

# 写入分区数据
dataset.write_partition(
    {"data": data1},
    {"experiment": "test", "sample": "1"}
)

dataset.write_partition(
    {"data": data2},
    {"experiment": "test", "sample": "2"}
)

# 列出分区
partitions = dataset.list_partitions()
print(f"Available partitions: {partitions}")

# 读取分区数据
result1 = dataset.read_partition({"experiment": "test", "sample": "1"})
result2 = dataset.read_partition({"experiment": "test", "sample": "2"})

# 验证数据
np.testing.assert_array_almost_equal(result1["data"], data1)
np.testing.assert_array_almost_equal(result2["data"], data2)
```

### 6. 存储选项

```python
import numpy as np
from src.core.data.arrow_types.parquet_storage import (
    ParquetStorageOptions, write_numpy_to_parquet
)

# 创建数据
data = np.random.randn(1000000)

# 创建不同压缩选项
options_none = ParquetStorageOptions(compression=None)
options_snappy = ParquetStorageOptions(compression="snappy")
options_gzip = ParquetStorageOptions(compression="gzip")

# 写入数据
write_numpy_to_parquet({"data": data}, "data_none.parquet", options_none)
write_numpy_to_parquet({"data": data}, "data_snappy.parquet", options_snappy)
write_numpy_to_parquet({"data": data}, "data_gzip.parquet", options_gzip)

# 比较文件大小
size_none = os.path.getsize("data_none.parquet")
size_snappy = os.path.getsize("data_snappy.parquet")
size_gzip = os.path.getsize("data_gzip.parquet")

print(f"No compression: {size_none} bytes")
print(f"Snappy compression: {size_snappy} bytes")
print(f"GZip compression: {size_gzip} bytes")
```

## NumPy 2.2.5+ 兼容性说明

从NumPy 2.2.5开始，引入了`gil_state`上下文管理器，用于在计算密集型操作中释放GIL（全局解释器锁）。我们的代码已经适配了这一特性，并提供了向下兼容性支持。

如果您使用的是NumPy 2.2.5+，代码将自动使用`gil_state`特性来提高性能。如果您使用的是较早版本的NumPy，代码将使用一个空的上下文管理器作为替代，确保功能正常但不会获得性能提升。

## 性能优化建议

1. **使用适当的压缩选项**：
   - 对于需要快速读写的数据，使用`snappy`压缩
   - 对于需要节省存储空间的数据，使用`gzip`压缩

2. **批量处理**：
   - 尽量批量读写数据，而不是逐个处理

3. **使用分区**：
   - 对于大型数据集，使用分区存储，可以提高查询性能

4. **使用多线程**：
   - 设置`ParquetStorageOptions`的`use_threading=True`启用多线程读取

## 常见问题解答

### Q: 为什么我的量子态存储失败？

A: 量子态必须满足归一化条件（向量的模的平方和为1）。如果您的量子态未归一化，可以：
1. 手动归一化：`state = state / np.sqrt(np.sum(np.abs(state)**2))`
2. 禁用验证：`numpy_to_arrow_quantum_state(state, validate=False)`

### Q: 如何处理大型张量？

A: 对于大型张量，建议：
1. 使用适当的压缩选项
2. 考虑分块存储
3. 对于超大张量，考虑使用内存映射文件

### Q: 如何处理混合数据类型？

A: 对于混合数据类型，建议：
1. 使用`write_numpy_to_parquet`和`read_parquet_to_numpy`函数
2. 确保所有数组的长度相同
3. 对于复杂类型，可能需要单独处理
