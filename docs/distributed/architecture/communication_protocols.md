# 超越态思维引擎4.0 - 模块间通信协议

## 1. 概述

本文档定义了超越态思维引擎4.0中模块间的通信协议，旨在确保不同模块之间的高效、可靠和一致的数据交换。通信协议涵盖了节点间通信、任务分发、状态同步和数据传输等方面，为分布式系统提供了统一的通信标准。

## 2. 通信模型

### 2.1 基本通信模型

超越态思维引擎采用混合通信模型，结合了以下通信模式：

- **点对点通信**：节点之间的直接通信
- **发布/订阅**：基于主题的消息发布和订阅
- **请求/响应**：同步请求和响应模式
- **流式传输**：连续数据流的传输

### 2.2 通信层次结构

通信协议分为五个层次：

1. **物理连接层**：负责底层网络连接
2. **传输层**：负责可靠的数据传输
3. **会话层**：负责会话管理和安全
4. **消息层**：负责消息格式和路由
5. **应用层**：负责应用级通信

## 3. 消息格式

### 3.1 基本消息结构

所有通信消息采用统一的基本结构：

```json
{
  "header": {
    "message_id": "唯一消息ID",
    "source_node": "源节点ID",
    "target_node": "目标节点ID或主题",
    "timestamp": "消息创建时间戳",
    "message_type": "消息类型",
    "priority": "消息优先级(0-1)",
    "ttl": "生存时间(秒)"
  },
  "payload": {
    // 消息内容，根据消息类型不同而不同
  },
  "metadata": {
    // 可选的元数据
  }
}
```

### 3.2 消息类型

系统定义了以下基本消息类型：

| 消息类型 | 描述 | 优先级范围 |
|---------|------|-----------|
| TASK | 任务分发和执行 | 0.1-0.9 |
| DATA | 数据传输 | 0.3-0.7 |
| CONTROL | 控制命令 | 0.8-1.0 |
| STATUS | 状态更新 | 0.5-0.7 |
| DISCOVERY | 节点发现 | 0.6-0.8 |
| HEARTBEAT | 心跳检测 | 0.9-1.0 |
| ERROR | 错误报告 | 0.8-1.0 |

### 3.3 序列化格式

为了提高通信效率，系统支持多种序列化格式：

- **MessagePack**：默认序列化格式，提供高效的二进制序列化
- **JSON**：用于调试和与外部系统交互
- **Protocol Buffers**：用于高性能场景
- **自定义二进制格式**：用于特定数据类型的高效传输

消息头中的`serialization_format`字段指定了有效载荷的序列化格式。

## 4. 通信协议

### 4.1 节点发现协议

#### 4.1.1 节点广播

新节点加入网络时，发送广播消息：

```json
{
  "header": {
    "message_id": "uuid",
    "source_node": "new_node_id",
    "target_node": "broadcast",
    "timestamp": "timestamp",
    "message_type": "DISCOVERY",
    "priority": 0.8,
    "ttl": 30
  },
  "payload": {
    "action": "JOIN",
    "node_type": "节点类型",
    "capabilities": {
      "computation": 0.8,
      "memory": 0.7,
      "bandwidth": 0.9
    },
    "address": "节点地址",
    "port": 端口号
  }
}
```

#### 4.1.2 节点响应

收到广播的节点发送响应：

```json
{
  "header": {
    "message_id": "uuid",
    "source_node": "existing_node_id",
    "target_node": "new_node_id",
    "timestamp": "timestamp",
    "message_type": "DISCOVERY",
    "priority": 0.8,
    "ttl": 30
  },
  "payload": {
    "action": "WELCOME",
    "node_type": "节点类型",
    "capabilities": {
      "computation": 0.8,
      "memory": 0.7,
      "bandwidth": 0.9
    },
    "address": "节点地址",
    "port": 端口号,
    "known_nodes": [
      {
        "node_id": "node1_id",
        "node_type": "节点类型",
        "address": "地址",
        "port": 端口号
      },
      // 更多节点...
    ]
  }
}
```

### 4.2 任务分发协议

#### 4.2.1 任务提交

```json
{
  "header": {
    "message_id": "uuid",
    "source_node": "source_node_id",
    "target_node": "target_node_id",
    "timestamp": "timestamp",
    "message_type": "TASK",
    "priority": 0.7,
    "ttl": 3600
  },
  "payload": {
    "task_id": "任务ID",
    "task_type": "任务类型",
    "dependencies": ["依赖任务ID列表"],
    "parameters": {
      // 任务参数
    },
    "resource_requirements": {
      "cpu": 0.5,
      "memory": 0.3,
      "gpu": 0.0
    },
    "timeout": 300,
    "retry_policy": {
      "max_retries": 3,
      "retry_interval": 10
    }
  }
}
```

#### 4.2.2 任务状态更新

```json
{
  "header": {
    "message_id": "uuid",
    "source_node": "executor_node_id",
    "target_node": "source_node_id",
    "timestamp": "timestamp",
    "message_type": "TASK",
    "priority": 0.7,
    "ttl": 60
  },
  "payload": {
    "task_id": "任务ID",
    "status": "RUNNING|COMPLETED|FAILED|CANCELLED",
    "progress": 0.75,
    "result_location": "结果位置或直接结果",
    "error": "错误信息(如果失败)",
    "execution_time": 45.2,
    "resource_usage": {
      "cpu": 0.45,
      "memory": 0.28,
      "gpu": 0.0
    }
  }
}
```

### 4.3 数据传输协议

#### 4.3.1 数据传输请求

```json
{
  "header": {
    "message_id": "uuid",
    "source_node": "source_node_id",
    "target_node": "target_node_id",
    "timestamp": "timestamp",
    "message_type": "DATA",
    "priority": 0.6,
    "ttl": 300
  },
  "payload": {
    "action": "TRANSFER",
    "data_id": "数据ID",
    "data_type": "数据类型",
    "serialization_format": "msgpack",
    "compression": "none|gzip|lz4",
    "size": 数据大小(字节),
    "checksum": "数据校验和",
    "data": "序列化后的数据或数据引用"
  }
}
```

#### 4.3.2 大型数据分块传输

对于大型数据，采用分块传输：

```json
{
  "header": {
    "message_id": "uuid",
    "source_node": "source_node_id",
    "target_node": "target_node_id",
    "timestamp": "timestamp",
    "message_type": "DATA",
    "priority": 0.6,
    "ttl": 300
  },
  "payload": {
    "action": "CHUNK",
    "data_id": "数据ID",
    "chunk_id": 块ID,
    "total_chunks": 总块数,
    "chunk_size": 块大小(字节),
    "chunk_checksum": "块校验和",
    "chunk_data": "块数据"
  }
}
```

#### 4.3.3 数据确认

```json
{
  "header": {
    "message_id": "uuid",
    "source_node": "target_node_id",
    "target_node": "source_node_id",
    "timestamp": "timestamp",
    "message_type": "DATA",
    "priority": 0.6,
    "ttl": 60
  },
  "payload": {
    "action": "ACK",
    "data_id": "数据ID",
    "chunk_id": 块ID(如果是分块传输),
    "status": "SUCCESS|FAILURE",
    "error": "错误信息(如果失败)"
  }
}
```

### 4.4 状态同步协议

#### 4.4.1 状态广播

```json
{
  "header": {
    "message_id": "uuid",
    "source_node": "source_node_id",
    "target_node": "broadcast|target_node_id",
    "timestamp": "timestamp",
    "message_type": "STATUS",
    "priority": 0.6,
    "ttl": 60
  },
  "payload": {
    "status_version": 版本号,
    "status_type": "FULL|DELTA",
    "status": {
      "cpu_usage": 0.45,
      "memory_usage": 0.28,
      "network_usage": 0.15,
      "task_count": 5,
      "health": "HEALTHY|DEGRADED|UNHEALTHY",
      // 其他状态信息...
    }
  }
}
```

#### 4.4.2 状态查询

```json
{
  "header": {
    "message_id": "uuid",
    "source_node": "source_node_id",
    "target_node": "target_node_id",
    "timestamp": "timestamp",
    "message_type": "STATUS",
    "priority": 0.7,
    "ttl": 30
  },
  "payload": {
    "action": "QUERY",
    "query_fields": ["cpu_usage", "memory_usage", "task_count"]
  }
}
```

### 4.5 心跳协议

#### 4.5.1 心跳消息

```json
{
  "header": {
    "message_id": "uuid",
    "source_node": "source_node_id",
    "target_node": "target_node_id|broadcast",
    "timestamp": "timestamp",
    "message_type": "HEARTBEAT",
    "priority": 0.9,
    "ttl": 10
  },
  "payload": {
    "sequence": 序列号,
    "status": "ALIVE",
    "load": 0.35
  }
}
```

#### 4.5.2 心跳响应

```json
{
  "header": {
    "message_id": "uuid",
    "source_node": "target_node_id",
    "target_node": "source_node_id",
    "timestamp": "timestamp",
    "message_type": "HEARTBEAT",
    "priority": 0.9,
    "ttl": 10
  },
  "payload": {
    "sequence": 序列号,
    "status": "ALIVE",
    "response_time": 响应时间(毫秒)
  }
}
```

## 5. 高效数据交换格式

### 5.1 超越态数据交换格式

超越态数据使用专门的交换格式，以提高传输效率：

```json
{
  "state_id": "超越态ID",
  "state_type": "超越态类型",
  "dimension": 维度,
  "data_format": "DENSE|SPARSE|COMPRESSED",
  "serialization_format": "序列化格式",
  "data": "序列化后的数据",
  "metadata": {
    // 元数据
  }
}
```

### 5.2 稀疏数据格式

对于稀疏数据，使用以下格式：

```json
{
  "format": "COO|CSR|CSC",
  "shape": [行数, 列数],
  "indices": [[行索引], [列索引]],
  "values": [值数组]
}
```

### 5.3 压缩数据格式

对于可压缩数据，使用以下格式：

```json
{
  "compression": "压缩算法",
  "original_size": 原始大小,
  "compressed_size": 压缩后大小,
  "data": "压缩后的数据"
}
```

## 6. 通信安全

### 6.1 认证机制

节点间通信采用以下认证机制：

- **节点身份验证**：基于公钥/私钥的节点身份验证
- **消息签名**：消息完整性验证
- **会话密钥**：用于加密通信内容

### 6.2 加密通信

敏感数据传输采用以下加密方式：

- **传输层加密**：使用TLS/SSL
- **端到端加密**：使用会话密钥
- **选择性加密**：只加密敏感数据

### 6.3 访问控制

基于角色的访问控制：

- **节点角色**：定义节点的权限范围
- **操作权限**：定义允许的操作类型
- **数据权限**：定义对数据的访问权限

## 7. 错误处理

### 7.1 错误消息格式

```json
{
  "header": {
    "message_id": "uuid",
    "source_node": "source_node_id",
    "target_node": "target_node_id",
    "timestamp": "timestamp",
    "message_type": "ERROR",
    "priority": 0.9,
    "ttl": 300
  },
  "payload": {
    "error_code": "错误代码",
    "error_type": "错误类型",
    "error_message": "错误描述",
    "related_message_id": "相关消息ID",
    "severity": "INFO|WARNING|ERROR|CRITICAL",
    "retry_suggested": true|false,
    "suggested_action": "建议操作"
  }
}
```

### 7.2 错误代码

| 错误代码 | 错误类型 | 描述 |
|---------|---------|------|
| E001 | COMMUNICATION | 通信错误 |
| E002 | AUTHENTICATION | 认证错误 |
| E003 | AUTHORIZATION | 授权错误 |
| E004 | RESOURCE | 资源错误 |
| E005 | TASK | 任务执行错误 |
| E006 | DATA | 数据错误 |
| E007 | TIMEOUT | 超时错误 |
| E008 | PROTOCOL | 协议错误 |
| E009 | INTERNAL | 内部错误 |

### 7.3 错误恢复策略

| 错误类型 | 恢复策略 |
|---------|---------|
| 通信错误 | 重试通信，使用备用通道 |
| 认证错误 | 重新认证，更新凭证 |
| 授权错误 | 请求授权，使用备用路径 |
| 资源错误 | 释放资源，请求额外资源 |
| 任务执行错误 | 重试任务，使用备用算法 |
| 数据错误 | 请求重新传输，使用备用数据 |
| 超时错误 | 增加超时时间，重试操作 |
| 协议错误 | 降级协议版本，使用兼容模式 |
| 内部错误 | 重启组件，报告错误 |

## 8. 性能优化

### 8.1 批处理

为减少通信开销，支持消息批处理：

```json
{
  "header": {
    "message_id": "uuid",
    "source_node": "source_node_id",
    "target_node": "target_node_id",
    "timestamp": "timestamp",
    "message_type": "BATCH",
    "priority": 根据批次中最高优先级,
    "ttl": 根据批次中最长TTL
  },
  "payload": {
    "batch_size": 批次大小,
    "messages": [
      // 消息数组
    ]
  }
}
```

### 8.2 压缩

支持以下压缩算法：

- **gzip**：通用压缩，压缩率高但CPU开销大
- **lz4**：快速压缩，压缩率中等但CPU开销小
- **zstd**：平衡的压缩率和速度
- **特定领域压缩**：针对特定数据类型的专用压缩

### 8.3 流水线处理

对于复杂操作，采用流水线处理：

1. **请求分解**：将大请求分解为小请求
2. **并行处理**：并行处理独立请求
3. **结果聚合**：聚合处理结果
4. **异步响应**：使用回调或事件通知

## 9. 版本控制与兼容性

### 9.1 协议版本

消息头中包含协议版本信息：

```json
{
  "header": {
    // ...
    "protocol_version": "1.0",
    // ...
  },
  // ...
}
```

### 9.2 版本协商

节点间通过以下步骤协商协议版本：

1. 初始连接时交换支持的版本范围
2. 选择双方都支持的最高版本
3. 如果没有共同版本，尝试使用兼容模式

### 9.3 向后兼容性

为确保向后兼容性，遵循以下原则：

- 新字段必须是可选的
- 不得更改现有字段的语义
- 必须支持旧版本的基本功能
- 提供版本转换机制

## 10. 实现指南

### 10.1 Python实现

```python
import uuid
import time
import json
import msgpack

class CommunicationProtocol:
    def __init__(self, node_id, protocol_version="1.0"):
        self.node_id = node_id
        self.protocol_version = protocol_version
    
    def create_message(self, target_node, message_type, payload, priority=0.5, ttl=60):
        """创建消息"""
        message = {
            "header": {
                "message_id": str(uuid.uuid4()),
                "source_node": self.node_id,
                "target_node": target_node,
                "timestamp": time.time(),
                "message_type": message_type,
                "priority": priority,
                "ttl": ttl,
                "protocol_version": self.protocol_version
            },
            "payload": payload,
            "metadata": {}
        }
        return message
    
    def serialize_message(self, message, format="msgpack"):
        """序列化消息"""
        if format == "json":
            return json.dumps(message).encode('utf-8')
        elif format == "msgpack":
            return msgpack.packb(message, use_bin_type=True)
        else:
            raise ValueError(f"Unsupported format: {format}")
    
    def deserialize_message(self, data, format="msgpack"):
        """反序列化消息"""
        if format == "json":
            return json.loads(data.decode('utf-8'))
        elif format == "msgpack":
            return msgpack.unpackb(data, raw=False)
        else:
            raise ValueError(f"Unsupported format: {format}")
    
    def create_task_message(self, target_node, task_id, task_type, parameters, dependencies=None, priority=0.7):
        """创建任务消息"""
        payload = {
            "task_id": task_id,
            "task_type": task_type,
            "dependencies": dependencies or [],
            "parameters": parameters,
            "resource_requirements": {
                "cpu": 0.5,
                "memory": 0.3,
                "gpu": 0.0
            },
            "timeout": 300,
            "retry_policy": {
                "max_retries": 3,
                "retry_interval": 10
            }
        }
        return self.create_message(target_node, "TASK", payload, priority)
    
    def create_data_message(self, target_node, data_id, data, data_type, priority=0.6):
        """创建数据消息"""
        payload = {
            "action": "TRANSFER",
            "data_id": data_id,
            "data_type": data_type,
            "serialization_format": "msgpack",
            "compression": "none",
            "size": len(data) if isinstance(data, bytes) else len(str(data)),
            "checksum": self._calculate_checksum(data),
            "data": data
        }
        return self.create_message(target_node, "DATA", payload, priority)
    
    def _calculate_checksum(self, data):
        """计算校验和"""
        import hashlib
        if isinstance(data, bytes):
            return hashlib.md5(data).hexdigest()
        else:
            return hashlib.md5(str(data).encode('utf-8')).hexdigest()
```

### 10.2 Rust实现

```rust
use serde::{Serialize, Deserialize};
use uuid::Uuid;
use std::time::{SystemTime, UNIX_EPOCH};
use rmp_serde::{Serializer, Deserializer};

#[derive(Serialize, Deserialize, Debug)]
struct MessageHeader {
    message_id: String,
    source_node: String,
    target_node: String,
    timestamp: f64,
    message_type: String,
    priority: f32,
    ttl: u32,
    protocol_version: String,
}

#[derive(Serialize, Deserialize, Debug)]
struct Message<T> {
    header: MessageHeader,
    payload: T,
    metadata: std::collections::HashMap<String, String>,
}

struct CommunicationProtocol {
    node_id: String,
    protocol_version: String,
}

impl CommunicationProtocol {
    fn new(node_id: String, protocol_version: Option<String>) -> Self {
        CommunicationProtocol {
            node_id,
            protocol_version: protocol_version.unwrap_or_else(|| "1.0".to_string()),
        }
    }
    
    fn create_message<T>(&self, target_node: String, message_type: String, 
                         payload: T, priority: f32, ttl: u32) -> Message<T> {
        let now = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs_f64();
        
        Message {
            header: MessageHeader {
                message_id: Uuid::new_v4().to_string(),
                source_node: self.node_id.clone(),
                target_node,
                timestamp: now,
                message_type,
                priority,
                ttl,
                protocol_version: self.protocol_version.clone(),
            },
            payload,
            metadata: std::collections::HashMap::new(),
        }
    }
    
    fn serialize_message<T: Serialize>(&self, message: &Message<T>) -> Result<Vec<u8>, Box<dyn std::error::Error>> {
        let mut buf = Vec::new();
        message.serialize(&mut Serializer::new(&mut buf))?;
        Ok(buf)
    }
    
    fn deserialize_message<T: for<'de> Deserialize<'de>>(&self, data: &[u8]) -> Result<Message<T>, Box<dyn std::error::Error>> {
        let message = rmp_serde::from_read_ref(data)?;
        Ok(message)
    }
}
```

## 11. 测试与验证

### 11.1 协议一致性测试

测试不同实现之间的协议一致性：

1. 创建相同的消息
2. 使用不同实现序列化
3. 使用不同实现反序列化
4. 验证结果一致性

### 11.2 性能测试

测试协议的性能特性：

1. 吞吐量测试
2. 延迟测试
3. 资源使用测试
4. 扩展性测试

### 11.3 兼容性测试

测试不同版本之间的兼容性：

1. 旧版本到新版本的兼容性
2. 新版本到旧版本的兼容性
3. 混合版本环境的兼容性

## 12. 附录

### 12.1 消息类型汇总

| 消息类型 | 子类型 | 描述 |
|---------|-------|------|
| TASK | SUBMIT | 提交任务 |
| TASK | STATUS | 任务状态更新 |
| TASK | CANCEL | 取消任务 |
| DATA | TRANSFER | 数据传输 |
| DATA | CHUNK | 分块数据传输 |
| DATA | ACK | 数据确认 |
| CONTROL | START | 启动操作 |
| CONTROL | STOP | 停止操作 |
| CONTROL | RESTART | 重启操作 |
| STATUS | BROADCAST | 状态广播 |
| STATUS | QUERY | 状态查询 |
| DISCOVERY | JOIN | 节点加入 |
| DISCOVERY | LEAVE | 节点离开 |
| DISCOVERY | WELCOME | 欢迎新节点 |
| HEARTBEAT | PING | 心跳检测 |
| HEARTBEAT | PONG | 心跳响应 |
| ERROR | REPORT | 错误报告 |

### 12.2 错误代码详细说明

| 错误代码 | 错误类型 | 描述 | 建议操作 |
|---------|---------|------|---------|
| E001-001 | COMMUNICATION | 连接失败 | 检查网络连接 |
| E001-002 | COMMUNICATION | 消息发送失败 | 重试或使用备用通道 |
| E001-003 | COMMUNICATION | 消息接收失败 | 请求重新发送 |
| E002-001 | AUTHENTICATION | 认证失败 | 检查凭证 |
| E002-002 | AUTHENTICATION | 凭证过期 | 更新凭证 |
| E003-001 | AUTHORIZATION | 权限不足 | 请求授权 |
| E003-002 | AUTHORIZATION | 操作被拒绝 | 使用备用操作 |
| E004-001 | RESOURCE | CPU资源不足 | 释放资源或降低要求 |
| E004-002 | RESOURCE | 内存资源不足 | 释放资源或降低要求 |
| E004-003 | RESOURCE | 存储资源不足 | 清理存储或降低要求 |
| E005-001 | TASK | 任务执行失败 | 检查任务参数 |
| E005-002 | TASK | 任务超时 | 增加超时时间 |
| E005-003 | TASK | 任务被取消 | 重新提交任务 |
| E006-001 | DATA | 数据格式错误 | 检查数据格式 |
| E006-002 | DATA | 数据校验失败 | 重新传输数据 |
| E006-003 | DATA | 数据不完整 | 请求缺失部分 |
| E007-001 | TIMEOUT | 操作超时 | 增加超时时间 |
| E007-002 | TIMEOUT | 响应超时 | 重试操作 |
| E008-001 | PROTOCOL | 协议版本不兼容 | 使用兼容模式 |
| E008-002 | PROTOCOL | 消息格式错误 | 检查消息格式 |
| E009-001 | INTERNAL | 内部错误 | 报告错误并重试 |
| E009-002 | INTERNAL | 组件崩溃 | 重启组件 |
