# 分形动力学路由算法 API 文档

## 算法概述

分形动力学路由算法（Fractal Dynamics Router）是一种基于分形理论和动力学系统的高效路由算法。该算法利用分形的自相似性和动力学系统的演化特性，在复杂网络中寻找最优路径。它特别适用于大规模、动态变化的网络环境，能够有效避免局部拥塞和全局负载不均衡问题。

优化版的分形动力学路由算法（OptimizedFractalDynamicsRouter）在保持原始算法路由能力的同时，显著提高了计算效率、降低了内存使用，并改进了算法的扩展性。

## 接口说明

### 类定义

```python
class OptimizedFractalDynamicsRouter(AlgorithmInterface):
    def __init__(self, 
                 fractal_dimension: float = 1.5,
                 stability_threshold: float = 0.01,
                 max_iterations: int = 100,
                 damping_factor: float = 0.85,
                 exploration_factor: float = 0.2,
                 use_parallel: bool = True,
                 num_workers: int = 4,
                 use_cache: bool = True,
                 cache_size: int = 1000,
                 use_incremental: bool = True,
                 use_adaptive: bool = True,
                 use_sparse: bool = True,
                 verbose: bool = False,
                 **kwargs):
        # 初始化代码...
        
    def compute(self, input_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        # 计算代码...
        
    def get_metadata(self) -> Dict[str, Any]:
        # 获取元数据代码...
        
    def is_compatible_with(self, other_algorithm: AlgorithmInterface) -> bool:
        # 检查兼容性代码...
        
    def get_performance_metrics(self) -> Dict[str, float]:
        # 获取性能指标代码...
        
    def get_progress(self) -> Dict[str, Any]:
        # 获取进度代码...
```

### 主要方法

#### `__init__`

初始化分形动力学路由算法。

**参数**:
- `fractal_dimension` (float): 分形维度，控制路径的分形特性，默认为1.5
- `stability_threshold` (float): 稳定性阈值，控制算法的收敛条件，默认为0.01
- `max_iterations` (int): 最大迭代次数，默认为100
- `damping_factor` (float): 阻尼因子，控制动力学系统的稳定性，默认为0.85
- `exploration_factor` (float): 探索因子，控制算法的探索与利用平衡，默认为0.2
- `use_parallel` (bool): 是否使用并行计算，默认为True
- `num_workers` (int): 并行计算的工作线程数，默认为4
- `use_cache` (bool): 是否使用缓存，默认为True
- `cache_size` (int): 缓存大小，默认为1000
- `use_incremental` (bool): 是否使用增量计算，默认为True
- `use_adaptive` (bool): 是否使用自适应策略，默认为True
- `use_sparse` (bool): 是否使用稀疏矩阵表示，默认为True
- `verbose` (bool): 是否输出详细日志，默认为False
- `**kwargs`: 其他参数

#### `compute`

执行路由计算。

**参数**:
- `input_data` (Dict[str, Any]): 输入数据，包含以下键：
  - `network`: 网络图，可以是NetworkX图对象或邻接矩阵
  - `source`: 源节点
  - `target`: 目标节点
  - `weights`: 可选，边权重，可以是字典或矩阵
  - `constraints`: 可选，路由约束，如带宽、延迟等
- `**kwargs`: 其他参数
  - `max_iterations`: 可选，覆盖初始化时设置的最大迭代次数
  - `stability_threshold`: 可选，覆盖初始化时设置的稳定性阈值
  - `callback`: 可选，每次迭代后调用的回调函数，接收当前迭代次数和当前路径
  - `base_result`: 可选，基础结果，用于增量计算

**返回**:
- Dict[str, Any]: 包含计算结果的字典，包括：
  - `path`: 最优路径，节点列表
  - `cost`: 路径成本，浮点数
  - `iterations`: 实际迭代次数，整数
  - `stability`: 最终稳定性，浮点数
  - `convergence_achieved`: 是否达到收敛，布尔值
  - `performance`: 性能指标，字典

#### `get_metadata`

获取算法元数据。

**返回**:
- Dict[str, Any]: 包含算法元数据的字典，包括：
  - `name`: 算法名称，字符串
  - `version`: 算法版本，字符串
  - `description`: 算法描述，字符串
  - `parameters`: 算法参数，字典
  - `performance_metrics`: 性能指标，字典

#### `is_compatible_with`

检查与其他算法的兼容性。

**参数**:
- `other_algorithm` (AlgorithmInterface): 其他算法

**返回**:
- bool: 是否兼容

#### `get_performance_metrics`

获取性能指标。

**返回**:
- Dict[str, float]: 包含性能指标的字典，包括：
  - `total_time`: 总执行时间，浮点数
  - `initialization_time`: 初始化时间，浮点数
  - `dynamics_time`: 动力学计算时间，浮点数
  - `path_extraction_time`: 路径提取时间，浮点数
  - `memory_usage`: 内存使用，浮点数
  - `cache_hit_rate`: 缓存命中率，浮点数
  - `incremental_update_time`: 增量更新时间，浮点数

#### `get_progress`

获取当前进度。

**返回**:
- Dict[str, Any]: 包含进度信息的字典，包括：
  - `current_iteration`: 当前迭代次数，整数
  - `total_iterations`: 总迭代次数，整数
  - `current_stability`: 当前稳定性，浮点数
  - `status`: 状态，字符串

## 参数说明

### 核心参数

- **fractal_dimension**: 分形维度，控制路径的分形特性。
  - 类型: float
  - 默认值: 1.5
  - 取值范围: (1.0, 2.0)
  - 建议值: 1.3-1.7
  - 说明: 较小的值倾向于生成更直接的路径，较大的值倾向于生成更分散的路径

- **stability_threshold**: 稳定性阈值，控制算法的收敛条件。
  - 类型: float
  - 默认值: 0.01
  - 取值范围: (0, 0.1)
  - 建议值: 0.005-0.02
  - 说明: 较小的值要求更高的收敛精度，但可能需要更多迭代

- **max_iterations**: 最大迭代次数，算法的终止条件之一。
  - 类型: int
  - 默认值: 100
  - 取值范围: 正整数
  - 建议值: 根据网络规模设置，通常50-200
  - 说明: 较大的网络可能需要更多迭代才能收敛

- **damping_factor**: 阻尼因子，控制动力学系统的稳定性。
  - 类型: float
  - 默认值: 0.85
  - 取值范围: (0, 1)
  - 建议值: 0.8-0.9
  - 说明: 较大的值使系统更稳定但收敛更慢，较小的值使系统更不稳定但收敛更快

- **exploration_factor**: 探索因子，控制算法的探索与利用平衡。
  - 类型: float
  - 默认值: 0.2
  - 取值范围: (0, 0.5)
  - 建议值: 0.1-0.3
  - 说明: 较大的值增加探索，较小的值增加利用

### 性能优化参数

- **use_parallel**: 是否使用并行计算。
  - 类型: bool
  - 默认值: True
  - 建议值: 对于大型网络，建议启用

- **num_workers**: 并行计算的工作线程数。
  - 类型: int
  - 默认值: 4
  - 取值范围: 正整数
  - 建议值: 根据CPU核心数设置，通常为CPU核心数的1-2倍

- **use_cache**: 是否使用缓存。
  - 类型: bool
  - 默认值: True
  - 建议值: 对于重复计算较多的场景，建议启用

- **cache_size**: 缓存大小。
  - 类型: int
  - 默认值: 1000
  - 取值范围: 正整数
  - 建议值: 根据内存大小和网络规模设置

- **use_incremental**: 是否使用增量计算。
  - 类型: bool
  - 默认值: True
  - 建议值: 对于动态变化的网络，建议启用

- **use_adaptive**: 是否使用自适应策略。
  - 类型: bool
  - 默认值: True
  - 建议值: 对于复杂网络，建议启用

- **use_sparse**: 是否使用稀疏矩阵表示。
  - 类型: bool
  - 默认值: True
  - 建议值: 对于稀疏网络，建议启用

- **verbose**: 是否输出详细日志。
  - 类型: bool
  - 默认值: False
  - 建议值: 调试时启用，生产环境禁用

## 使用示例

### 基本用法

```python
from src.algorithms.routing.fractal_router_optimized import OptimizedFractalDynamicsRouter
import networkx as nx

# 创建网络图
G = nx.grid_2d_graph(10, 10)  # 10x10网格图

# 添加边权重
for u, v in G.edges():
    G[u][v]['weight'] = 1.0

# 创建路由器
router = OptimizedFractalDynamicsRouter(
    fractal_dimension=1.5,
    stability_threshold=0.01,
    max_iterations=100,
    damping_factor=0.85,
    exploration_factor=0.2,
    use_parallel=True,
    num_workers=4
)

# 执行路由计算
result = router.compute({
    'network': G,
    'source': (0, 0),
    'target': (9, 9)
})

# 获取结果
path = result['path']
cost = result['cost']
iterations = result['iterations']
convergence_achieved = result['convergence_achieved']

print(f"最优路径: {path}")
print(f"路径成本: {cost}")
print(f"迭代次数: {iterations}")
print(f"是否收敛: {convergence_achieved}")
```

### 带约束的路由

```python
from src.algorithms.routing.fractal_router_optimized import OptimizedFractalDynamicsRouter
import networkx as nx

# 创建网络图
G = nx.grid_2d_graph(10, 10)  # 10x10网格图

# 添加边权重和带宽
for u, v in G.edges():
    G[u][v]['weight'] = 1.0
    G[u][v]['bandwidth'] = 10.0

# 定义带宽约束
def bandwidth_constraint(path, G):
    min_bandwidth = float('inf')
    for i in range(len(path) - 1):
        u, v = path[i], path[i + 1]
        min_bandwidth = min(min_bandwidth, G[u][v]['bandwidth'])
    return min_bandwidth >= 5.0  # 要求带宽至少为5.0

# 创建路由器
router = OptimizedFractalDynamicsRouter(
    fractal_dimension=1.5,
    stability_threshold=0.01,
    max_iterations=100
)

# 执行路由计算
result = router.compute({
    'network': G,
    'source': (0, 0),
    'target': (9, 9),
    'constraints': [bandwidth_constraint]
})

# 获取结果
path = result['path']
cost = result['cost']

print(f"最优路径: {path}")
print(f"路径成本: {cost}")
```

### 增量路由

```python
from src.algorithms.routing.fractal_router_optimized import OptimizedFractalDynamicsRouter
import networkx as nx

# 创建网络图
G = nx.grid_2d_graph(10, 10)  # 10x10网格图

# 添加边权重
for u, v in G.edges():
    G[u][v]['weight'] = 1.0

# 创建路由器
router = OptimizedFractalDynamicsRouter(
    fractal_dimension=1.5,
    stability_threshold=0.01,
    max_iterations=100,
    use_incremental=True
)

# 第一次路由计算
result1 = router.compute({
    'network': G,
    'source': (0, 0),
    'target': (9, 9)
})

# 修改网络（模拟网络变化）
G[(5, 5)][(5, 6)]['weight'] = 10.0  # 增加某条边的权重

# 使用增量路由
result2 = router.compute({
    'network': G,
    'source': (0, 0),
    'target': (9, 9),
    'base_result': result1
})

# 获取结果
path1 = result1['path']
path2 = result2['path']

print(f"原始路径: {path1}")
print(f"新路径: {path2}")
```

### 使用回调函数

```python
from src.algorithms.routing.fractal_router_optimized import OptimizedFractalDynamicsRouter
import networkx as nx
import matplotlib.pyplot as plt

# 创建网络图
G = nx.grid_2d_graph(10, 10)  # 10x10网格图

# 添加边权重
for u, v in G.edges():
    G[u][v]['weight'] = 1.0

# 定义回调函数
path_history = []

def callback(iteration, current_path, stability):
    path_history.append(current_path)
    print(f"迭代 {iteration}: 稳定性 = {stability}")

# 创建路由器
router = OptimizedFractalDynamicsRouter(
    fractal_dimension=1.5,
    stability_threshold=0.01,
    max_iterations=50
)

# 执行路由计算
result = router.compute({
    'network': G,
    'source': (0, 0),
    'target': (9, 9),
    'callback': callback
})

# 绘制最终路径
plt.figure(figsize=(10, 10))
nx.draw(G, node_size=10, node_color='lightblue')
path = result['path']
path_edges = list(zip(path[:-1], path[1:]))
nx.draw_networkx_edges(G, edgelist=path_edges, width=2, edge_color='red')
plt.title('最优路径')
plt.show()
```

## 性能特性

### 时间复杂度

- **总体时间复杂度**: O(I * (V + E))
  - I: 迭代次数
  - V: 节点数
  - E: 边数

- **初始化**: O(V + E)
- **动力学计算**: O(I * E)
- **路径提取**: O(V)

### 空间复杂度

- **总体空间复杂度**: O(V + E)
  - V: 节点数
  - E: 边数

- **网络表示**: O(V + E)
- **动力学状态**: O(V)
- **缓存**: O(C)，C为缓存大小

### 并行性能

- **加速比**: 在4核CPU上，并行版本比串行版本快2-3倍
- **效率**: 并行效率约为70-80%
- **扩展性**: 随着CPU核心数的增加，加速比呈次线性增长

### 增量计算性能

- **小幅变化**: 对于网络的小幅变化，增量计算比完全重新计算快5-10倍
- **中等变化**: 对于网络的中等变化，增量计算比完全重新计算快2-5倍
- **大幅变化**: 对于网络的大幅变化，增量计算可能不如完全重新计算

### 稀疏矩阵性能

- **稀疏网络**: 对于稀疏网络（边数远小于节点数的平方），使用稀疏矩阵表示可以减少50-90%的内存使用
- **密集网络**: 对于密集网络，稀疏矩阵表示可能不如密集矩阵表示高效

## 注意事项

1. **网络规模**: 算法适用于中大型网络（节点数在10^2-10^6范围内）。对于超大型网络，可能需要使用分布式计算。

2. **参数调优**: 算法性能对参数设置敏感，特别是分形维度和阻尼因子。建议根据具体网络特性进行参数调优。

3. **收敛性**: 对于某些复杂网络，算法可能难以收敛。如果遇到收敛问题，可以尝试调整阻尼因子或增加最大迭代次数。

4. **约束处理**: 算法使用惩罚函数处理约束，对于严格约束问题，可能需要调整惩罚系数。

5. **内存使用**: 对于大型网络，算法可能消耗大量内存。如果内存不足，可以使用稀疏矩阵表示或禁用缓存。

6. **并行计算**: 并行计算可能增加内存使用。如果内存不足，可以减小工作线程数或禁用并行计算。

7. **增量计算**: 增量计算适用于网络小幅变化的情况。如果网络发生大幅变化，建议禁用增量计算。

## 算法改进方向

1. **自适应参数调整**: 进一步优化自适应参数调整策略，使算法能够更好地适应不同类型的网络。

2. **混合策略**: 结合其他路由算法的优点，如A*算法、Dijkstra算法等，形成混合策略。

3. **分布式计算**: 实现分布式计算支持，使算法能够处理超大型网络。

4. **GPU加速**: 利用GPU加速计算，特别是动力学计算部分。

5. **多目标路由**: 扩展算法以支持多目标路由问题，如同时优化延迟、带宽和可靠性。

6. **动态路由**: 增强算法处理动态变化网络的能力，如实时流量变化、链路故障等。

7. **QoS保证**: 改进约束处理机制，使算法能够提供更好的服务质量保证。
