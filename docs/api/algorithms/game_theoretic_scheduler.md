# 博弈优化资源调度算法 API 文档

## 算法概述

博弈优化资源调度算法（Game Theoretic Scheduler）是一种基于博弈论的高效资源调度算法。该算法将资源分配问题建模为多智能体博弈，通过求解纳什均衡来获得最优的资源分配方案。它特别适用于多智能体环境中的资源分配和调度问题，能够在效率和公平性之间取得良好的平衡。

优化版的博弈优化资源调度算法（OptimizedGameTheoreticScheduler）在保持原始算法调度能力的同时，显著提高了计算效率、降低了内存使用，并改进了算法的扩展性。

## 接口说明

### 类定义

```python
class OptimizedGameTheoreticScheduler(AlgorithmInterface):
    def __init__(self, 
                 game_type: str = 'non_cooperative',
                 learning_rate: float = 0.3,
                 max_iterations: int = 100,
                 tolerance: float = 1e-6,
                 fairness_weight: float = 0.5,
                 efficiency_weight: float = 0.5,
                 use_parallel: bool = True,
                 num_workers: int = 4,
                 use_cache: bool = True,
                 cache_size: int = 1000,
                 use_incremental_update: bool = True,
                 use_prediction: bool = True,
                 equilibrium_algorithm: str = 'fictitious_play',
                 verbose: bool = False,
                 **kwargs):
        # 初始化代码...
        
    def compute(self, input_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        # 计算代码...
        
    def get_metadata(self) -> Dict[str, Any]:
        # 获取元数据代码...
        
    def is_compatible_with(self, other_algorithm: AlgorithmInterface) -> bool:
        # 检查兼容性代码...
        
    def get_performance_metrics(self) -> Dict[str, float]:
        # 获取性能指标代码...
        
    def get_progress(self) -> Dict[str, Any]:
        # 获取进度代码...
```

### 主要方法

#### `__init__`

初始化博弈优化资源调度算法。

**参数**:
- `game_type` (str): 博弈类型，可选值为'non_cooperative'（非合作博弈）、'cooperative'（合作博弈）或'stackelberg'（斯塔克伯格博弈），默认为'non_cooperative'
- `learning_rate` (float): 学习率，控制策略更新的步长，默认为0.3
- `max_iterations` (int): 最大迭代次数，默认为100
- `tolerance` (float): 收敛容差，默认为1e-6
- `fairness_weight` (float): 公平性权重，控制公平性与效率的平衡，默认为0.5
- `efficiency_weight` (float): 效率权重，控制公平性与效率的平衡，默认为0.5
- `use_parallel` (bool): 是否使用并行计算，默认为True
- `num_workers` (int): 并行计算的工作线程数，默认为4
- `use_cache` (bool): 是否使用缓存，默认为True
- `cache_size` (int): 缓存大小，默认为1000
- `use_incremental_update` (bool): 是否使用增量更新，默认为True
- `use_prediction` (bool): 是否使用预测，默认为True
- `equilibrium_algorithm` (str): 均衡求解算法，可选值为'fictitious_play'（虚拟博弈）、'best_response'（最佳响应）或'replicator_dynamics'（复制动力学），默认为'fictitious_play'
- `verbose` (bool): 是否输出详细日志，默认为False
- `**kwargs`: 其他参数

#### `compute`

执行资源调度计算。

**参数**:
- `input_data` (Dict[str, Any]): 输入数据，包含以下键：
  - `capacities`: 资源容量，形状为(num_resources,)的numpy数组
  - `demands`: 代理需求，形状为(num_agents, num_resources)的numpy数组
  - `priorities`: 可选，代理优先级，形状为(num_agents, num_resources)的numpy数组
  - `utilities`: 可选，代理效用，形状为(num_agents, num_resources)的numpy数组
- `**kwargs`: 其他参数
  - `max_iterations`: 可选，覆盖初始化时设置的最大迭代次数
  - `tolerance`: 可选，覆盖初始化时设置的收敛容差
  - `callback`: 可选，每次迭代后调用的回调函数，接收当前迭代次数和当前分配
  - `base_allocations`: 可选，基础分配，用于增量更新

**返回**:
- Dict[str, Any]: 包含计算结果的字典，包括：
  - `allocations`: 资源分配，形状为(num_agents, num_resources)的numpy数组
  - `utilities`: 代理效用，形状为(num_agents, num_resources)的numpy数组
  - `equilibrium`: 均衡策略，字典，键为代理ID，值为代理的均衡策略
  - `iterations`: 实际迭代次数，整数
  - `convergence_achieved`: 是否达到收敛，布尔值
  - `performance`: 性能指标，字典

#### `get_metadata`

获取算法元数据。

**返回**:
- Dict[str, Any]: 包含算法元数据的字典，包括：
  - `name`: 算法名称，字符串
  - `version`: 算法版本，字符串
  - `description`: 算法描述，字符串
  - `parameters`: 算法参数，字典
  - `performance_metrics`: 性能指标，字典

#### `is_compatible_with`

检查与其他算法的兼容性。

**参数**:
- `other_algorithm` (AlgorithmInterface): 其他算法

**返回**:
- bool: 是否兼容

#### `get_performance_metrics`

获取性能指标。

**返回**:
- Dict[str, float]: 包含性能指标的字典，包括：
  - `total_time`: 总执行时间，浮点数
  - `initialization_time`: 初始化时间，浮点数
  - `equilibrium_time`: 均衡计算时间，浮点数
  - `allocation_time`: 分配计算时间，浮点数
  - `memory_usage`: 内存使用，浮点数
  - `cache_hit_rate`: 缓存命中率，浮点数
  - `incremental_update_time`: 增量更新时间，浮点数

#### `get_progress`

获取当前进度。

**返回**:
- Dict[str, Any]: 包含进度信息的字典，包括：
  - `current_iteration`: 当前迭代次数，整数
  - `total_iterations`: 总迭代次数，整数
  - `current_error`: 当前误差，浮点数
  - `status`: 状态，字符串

## 参数说明

### 核心参数

- **game_type**: 博弈类型，决定了代理之间的交互方式。
  - 类型: str
  - 默认值: 'non_cooperative'
  - 可选值: 'non_cooperative'（非合作博弈）、'cooperative'（合作博弈）或'stackelberg'（斯塔克伯格博弈）
  - 建议值: 根据实际问题选择，通常'non_cooperative'适用于自私代理，'cooperative'适用于合作代理，'stackelberg'适用于有领导者和跟随者的场景

- **learning_rate**: 学习率，控制策略更新的步长。
  - 类型: float
  - 默认值: 0.3
  - 取值范围: (0, 1)
  - 建议值: 0.1-0.5
  - 说明: 较大的值使收敛更快但可能不稳定，较小的值使收敛更慢但更稳定

- **max_iterations**: 最大迭代次数，算法的终止条件之一。
  - 类型: int
  - 默认值: 100
  - 取值范围: 正整数
  - 建议值: 根据问题规模设置，通常50-200
  - 说明: 较大的问题可能需要更多迭代才能收敛

- **tolerance**: 收敛容差，算法的终止条件之一。
  - 类型: float
  - 默认值: 1e-6
  - 取值范围: 正浮点数
  - 建议值: 1e-4-1e-8
  - 说明: 较小的值要求更高的收敛精度，但可能需要更多迭代

- **fairness_weight**: 公平性权重，控制公平性与效率的平衡。
  - 类型: float
  - 默认值: 0.5
  - 取值范围: [0, 1]
  - 建议值: 根据实际需求设置，0表示只关注效率，1表示只关注公平性
  - 说明: 较大的值倾向于更公平的分配，较小的值倾向于更高效的分配

- **efficiency_weight**: 效率权重，控制公平性与效率的平衡。
  - 类型: float
  - 默认值: 0.5
  - 取值范围: [0, 1]
  - 建议值: 根据实际需求设置，通常fairness_weight + efficiency_weight = 1
  - 说明: 较大的值倾向于更高效的分配，较小的值倾向于更公平的分配

- **equilibrium_algorithm**: 均衡求解算法，决定了如何计算纳什均衡。
  - 类型: str
  - 默认值: 'fictitious_play'
  - 可选值: 'fictitious_play'（虚拟博弈）、'best_response'（最佳响应）或'replicator_dynamics'（复制动力学）
  - 建议值: 根据实际问题选择，通常'fictitious_play'适用于一般问题，'best_response'适用于简单问题，'replicator_dynamics'适用于复杂问题

### 性能优化参数

- **use_parallel**: 是否使用并行计算。
  - 类型: bool
  - 默认值: True
  - 建议值: 对于大规模问题，建议启用

- **num_workers**: 并行计算的工作线程数。
  - 类型: int
  - 默认值: 4
  - 取值范围: 正整数
  - 建议值: 根据CPU核心数设置，通常为CPU核心数的1-2倍

- **use_cache**: 是否使用缓存。
  - 类型: bool
  - 默认值: True
  - 建议值: 对于重复计算较多的场景，建议启用

- **cache_size**: 缓存大小。
  - 类型: int
  - 默认值: 1000
  - 取值范围: 正整数
  - 建议值: 根据内存大小和问题规模设置

- **use_incremental_update**: 是否使用增量更新。
  - 类型: bool
  - 默认值: True
  - 建议值: 对于动态变化的资源环境，建议启用

- **use_prediction**: 是否使用预测。
  - 类型: bool
  - 默认值: True
  - 建议值: 对于有历史数据的场景，建议启用

- **verbose**: 是否输出详细日志。
  - 类型: bool
  - 默认值: False
  - 建议值: 调试时启用，生产环境禁用

## 使用示例

### 基本用法

```python
from src.algorithms.game_theory.scheduler_optimized import OptimizedGameTheoreticScheduler
import numpy as np

# 创建资源
num_agents = 10
num_resources = 5

# 资源容量
capacities = np.random.uniform(50, 100, num_resources)

# 代理需求
demands = np.random.uniform(1, 10, (num_agents, num_resources))

# 代理优先级
priorities = np.random.uniform(0.1, 1.0, (num_agents, num_resources))

# 代理效用
utilities = np.random.uniform(1, 20, (num_agents, num_resources))

# 创建调度器
scheduler = OptimizedGameTheoreticScheduler(
    game_type='non_cooperative',
    learning_rate=0.3,
    max_iterations=100,
    tolerance=1e-6,
    fairness_weight=0.5,
    efficiency_weight=0.5,
    use_parallel=True,
    num_workers=4
)

# 执行调度计算
result = scheduler.compute({
    'capacities': capacities,
    'demands': demands,
    'priorities': priorities,
    'utilities': utilities
})

# 获取结果
allocations = result['allocations']
utilities = result['utilities']
iterations = result['iterations']
convergence_achieved = result['convergence_achieved']

print(f"资源分配:\n{allocations}")
print(f"代理效用:\n{utilities}")
print(f"迭代次数: {iterations}")
print(f"是否收敛: {convergence_achieved}")
```

### 增量更新

```python
from src.algorithms.game_theory.scheduler_optimized import OptimizedGameTheoreticScheduler
import numpy as np

# 创建资源
num_agents = 10
num_resources = 5

# 资源容量
capacities = np.random.uniform(50, 100, num_resources)

# 代理需求
demands = np.random.uniform(1, 10, (num_agents, num_resources))

# 代理优先级
priorities = np.random.uniform(0.1, 1.0, (num_agents, num_resources))

# 代理效用
utilities = np.random.uniform(1, 20, (num_agents, num_resources))

# 创建调度器
scheduler = OptimizedGameTheoreticScheduler(
    game_type='non_cooperative',
    learning_rate=0.3,
    max_iterations=100,
    use_incremental_update=True
)

# 第一次调度计算
result1 = scheduler.compute({
    'capacities': capacities,
    'demands': demands,
    'priorities': priorities,
    'utilities': utilities
})

# 修改需求（模拟需求变化）
new_demands = demands.copy()
new_demands[0, 0] += 2.0  # 增加某个代理对某个资源的需求

# 使用增量更新
result2 = scheduler.compute({
    'capacities': capacities,
    'demands': new_demands,
    'priorities': priorities,
    'utilities': utilities,
    'base_allocations': result1['allocations']
})

# 获取结果
allocations1 = result1['allocations']
allocations2 = result2['allocations']

print(f"原始分配:\n{allocations1}")
print(f"新分配:\n{allocations2}")
```

### 使用不同的均衡算法

```python
from src.algorithms.game_theory.scheduler_optimized import OptimizedGameTheoreticScheduler
import numpy as np
import time

# 创建资源
num_agents = 10
num_resources = 5

# 资源容量
capacities = np.random.uniform(50, 100, num_resources)

# 代理需求
demands = np.random.uniform(1, 10, (num_agents, num_resources))

# 代理优先级
priorities = np.random.uniform(0.1, 1.0, (num_agents, num_resources))

# 代理效用
utilities = np.random.uniform(1, 20, (num_agents, num_resources))

# 测试不同的均衡算法
algorithms = ['fictitious_play', 'best_response', 'replicator_dynamics']
results = {}

for algorithm in algorithms:
    # 创建调度器
    scheduler = OptimizedGameTheoreticScheduler(
        game_type='non_cooperative',
        learning_rate=0.3,
        max_iterations=100,
        equilibrium_algorithm=algorithm
    )
    
    # 执行调度计算
    start_time = time.time()
    result = scheduler.compute({
        'capacities': capacities,
        'demands': demands,
        'priorities': priorities,
        'utilities': utilities
    })
    execution_time = time.time() - start_time
    
    # 保存结果
    results[algorithm] = {
        'allocations': result['allocations'],
        'iterations': result['iterations'],
        'time': execution_time
    }

# 比较结果
for algorithm, result in results.items():
    print(f"{algorithm}:")
    print(f"  迭代次数: {result['iterations']}")
    print(f"  执行时间: {result['time']:.4f}秒")
    print(f"  总效用: {np.sum(result['allocations'] * utilities):.2f}")
```

### 使用回调函数

```python
from src.algorithms.game_theory.scheduler_optimized import OptimizedGameTheoreticScheduler
import numpy as np
import matplotlib.pyplot as plt

# 创建资源
num_agents = 10
num_resources = 5

# 资源容量
capacities = np.random.uniform(50, 100, num_resources)

# 代理需求
demands = np.random.uniform(1, 10, (num_agents, num_resources))

# 代理优先级
priorities = np.random.uniform(0.1, 1.0, (num_agents, num_resources))

# 代理效用
utilities = np.random.uniform(1, 20, (num_agents, num_resources))

# 定义回调函数
error_history = []

def callback(iteration, allocations, error):
    error_history.append(error)
    print(f"迭代 {iteration}: 误差 = {error}")

# 创建调度器
scheduler = OptimizedGameTheoreticScheduler(
    game_type='non_cooperative',
    learning_rate=0.3,
    max_iterations=100
)

# 执行调度计算
result = scheduler.compute({
    'capacities': capacities,
    'demands': demands,
    'priorities': priorities,
    'utilities': utilities,
    'callback': callback
})

# 绘制收敛曲线
plt.figure(figsize=(10, 6))
plt.plot(error_history)
plt.xlabel('迭代次数')
plt.ylabel('误差')
plt.title('收敛曲线')
plt.yscale('log')
plt.grid(True)
plt.show()
```

## 性能特性

### 时间复杂度

- **总体时间复杂度**: O(I * N * M)
  - I: 迭代次数
  - N: 代理数量
  - M: 资源数量

- **初始化**: O(N * M)
- **均衡计算**: O(I * N * M)
- **分配计算**: O(N * M)

### 空间复杂度

- **总体空间复杂度**: O(N * M)
  - N: 代理数量
  - M: 资源数量

- **策略存储**: O(N * M)
- **分配存储**: O(N * M)
- **缓存**: O(C)，C为缓存大小

### 并行性能

- **加速比**: 在4核CPU上，并行版本比串行版本快2-3倍
- **效率**: 并行效率约为70-80%
- **扩展性**: 随着CPU核心数的增加，加速比呈次线性增长

### 增量更新性能

- **小幅变化**: 对于资源环境的小幅变化，增量更新比完全重新计算快5-10倍
- **中等变化**: 对于资源环境的中等变化，增量更新比完全重新计算快2-5倍
- **大幅变化**: 对于资源环境的大幅变化，增量更新可能不如完全重新计算

### 预测性能

- **准确率**: 对于有历史数据的场景，预测准确率可达80-90%
- **加速比**: 使用预测可以减少30-50%的迭代次数

## 注意事项

1. **规模限制**: 算法适用于中等规模的资源调度问题（代理数量和资源数量在10^2-10^4范围内）。对于超大规模问题，可能需要使用分布式计算。

2. **参数调优**: 算法性能对参数设置敏感，特别是学习率和权重。建议根据具体问题特性进行参数调优。

3. **收敛性**: 对于某些复杂问题，算法可能难以收敛。如果遇到收敛问题，可以尝试调整学习率或增加最大迭代次数。

4. **公平性与效率**: 算法通过fairness_weight和efficiency_weight参数平衡公平性与效率。根据实际需求调整这些参数。

5. **内存使用**: 对于大规模问题，算法可能消耗大量内存。如果内存不足，可以减小缓存大小或禁用缓存。

6. **并行计算**: 并行计算可能增加内存使用。如果内存不足，可以减小工作线程数或禁用并行计算。

7. **增量更新**: 增量更新适用于资源环境小幅变化的情况。如果资源环境发生大幅变化，建议禁用增量更新。

## 算法改进方向

1. **自适应参数调整**: 进一步优化自适应参数调整策略，使算法能够更好地适应不同类型的资源环境。

2. **混合策略**: 结合其他资源调度算法的优点，如贪婪算法、遗传算法等，形成混合策略。

3. **分布式计算**: 实现分布式计算支持，使算法能够处理超大规模问题。

4. **GPU加速**: 利用GPU加速计算，特别是均衡计算部分。

5. **多目标优化**: 扩展算法以支持多目标优化问题，如同时优化资源利用率、响应时间和能耗。

6. **动态调度**: 增强算法处理动态变化资源环境的能力，如实时资源变化、需求波动等。

7. **QoS保证**: 改进约束处理机制，使算法能够提供更好的服务质量保证。
