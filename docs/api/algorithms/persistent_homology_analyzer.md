# 持久同调分析算法 API 文档

## 算法概述

持久同调分析算法（Persistent Homology Analyzer）是一种基于计算拓扑学的数据分析算法。该算法通过计算数据的拓扑特征（如连通分量、环、空洞等）及其持久性，揭示数据的内在结构和特征。它特别适用于高维数据的降维、聚类、特征提取和异常检测等任务。

优化版的持久同调分析算法（OptimizedPersistentHomologyAnalyzer）在保持原始算法分析能力的同时，显著提高了计算效率、降低了内存使用，并改进了算法的扩展性。

## 接口说明

### 类定义

```python
class OptimizedPersistentHomologyAnalyzer(AlgorithmInterface):
    def __init__(self, 
                 max_dimension: int = 2,
                 max_radius: Optional[float] = None,
                 num_divisions: int = 50,
                 use_parallel: bool = True,
                 num_workers: int = 4,
                 use_cache: bool = True,
                 cache_size: int = 1000,
                 use_incremental: bool = True,
                 use_sparse: bool = True,
                 use_rust: bool = True,
                 threshold_dimension: int = 50,
                 block_size: int = 64,
                 verbose: bool = False,
                 **kwargs):
        # 初始化代码...
        
    def compute(self, input_data: Union[np.ndarray, Dict[str, Any]], 
                base_result: Optional[Dict[str, Any]] = None, 
                **kwargs) -> Dict[str, Any]:
        # 计算代码...
        
    def get_metadata(self) -> Dict[str, Any]:
        # 获取元数据代码...
        
    def is_compatible_with(self, other_algorithm: AlgorithmInterface) -> bool:
        # 检查兼容性代码...
        
    def get_performance_metrics(self) -> Dict[str, float]:
        # 获取性能指标代码...
        
    def get_progress(self) -> Dict[str, Any]:
        # 获取进度代码...
```

### 主要方法

#### `__init__`

初始化持久同调分析算法。

**参数**:
- `max_dimension` (int): 最大同调维度，默认为2
- `max_radius` (Optional[float]): 最大半径，如果为None则自动计算，默认为None
- `num_divisions` (int): 持久图划分数量，默认为50
- `use_parallel` (bool): 是否使用并行计算，默认为True
- `num_workers` (int): 并行计算的工作线程数，默认为4
- `use_cache` (bool): 是否使用缓存，默认为True
- `cache_size` (int): 缓存大小，默认为1000
- `use_incremental` (bool): 是否使用增量计算，默认为True
- `use_sparse` (bool): 是否使用稀疏矩阵，默认为True
- `use_rust` (bool): 是否使用Rust实现，默认为True
- `threshold_dimension` (int): 切换到Rust实现的阈值维度，默认为50
- `block_size` (int): 处理块大小，默认为64
- `verbose` (bool): 是否输出详细日志，默认为False
- `**kwargs`: 其他参数

#### `compute`

执行持久同调分析。

**参数**:
- `input_data` (Union[np.ndarray, Dict[str, Any]]): 输入数据，可以是点云数据（numpy数组）或包含单纯复形和过滤值的字典
- `base_result` (Optional[Dict[str, Any]]): 基础结果，用于增量计算，默认为None
- `**kwargs`: 其他参数
  - `max_dimension` (int): 可选，覆盖初始化时设置的最大同调维度
  - `max_radius` (float): 可选，覆盖初始化时设置的最大半径
  - `num_divisions` (int): 可选，覆盖初始化时设置的划分数量
  - `callback` (Callable): 可选，回调函数，接收当前进度和状态

**返回**:
- Dict[str, Any]: 包含计算结果的字典，包括：
  - `persistence_diagrams`: 持久图，键为维度，值为持久点的数组
  - `betti_curves`: 贝蒂曲线，键为维度，值为贝蒂数的数组
  - `persistence_landscapes`: 持久景观，键为维度，值为景观的数组
  - `performance`: 性能指标，字典

#### `get_metadata`

获取算法元数据。

**返回**:
- Dict[str, Any]: 包含算法元数据的字典，包括：
  - `name`: 算法名称，字符串
  - `version`: 算法版本，字符串
  - `description`: 算法描述，字符串
  - `parameters`: 算法参数，字典
  - `performance_metrics`: 性能指标，字典

#### `is_compatible_with`

检查与其他算法的兼容性。

**参数**:
- `other_algorithm` (AlgorithmInterface): 其他算法

**返回**:
- bool: 是否兼容

#### `get_performance_metrics`

获取性能指标。

**返回**:
- Dict[str, float]: 包含性能指标的字典，包括：
  - `total_time`: 总执行时间，浮点数
  - `complex_construction_time`: 单纯复形构建时间，浮点数
  - `persistence_calculation_time`: 持久性计算时间，浮点数
  - `feature_extraction_time`: 特征提取时间，浮点数
  - `memory_usage`: 内存使用，浮点数
  - `cache_hit_rate`: 缓存命中率，浮点数
  - `incremental_update_time`: 增量更新时间，浮点数

#### `get_progress`

获取当前进度。

**返回**:
- Dict[str, Any]: 包含进度信息的字典，包括：
  - `stage`: 当前阶段，字符串
  - `current_step`: 当前步骤，整数
  - `total_steps`: 总步骤数，整数
  - `status`: 状态，字符串

## 参数说明

### 核心参数

- **max_dimension**: 最大同调维度，决定了计算的拓扑特征的最高维度。
  - 类型: int
  - 默认值: 2
  - 取值范围: 非负整数
  - 建议值: 0-3
  - 说明: 0表示连通分量，1表示环，2表示空洞，更高维度表示更复杂的拓扑特征

- **max_radius**: 最大半径，决定了单纯复形构建的最大尺度。
  - 类型: Optional[float]
  - 默认值: None（自动计算）
  - 取值范围: 正浮点数
  - 建议值: 根据数据分布设置，通常为数据直径的0.2-0.5倍
  - 说明: 较大的值会捕获更大尺度的拓扑特征，但计算成本更高

- **num_divisions**: 持久图划分数量，决定了贝蒂曲线和持久景观的精度。
  - 类型: int
  - 默认值: 50
  - 取值范围: 正整数
  - 建议值: 50-200
  - 说明: 较大的值提供更精细的拓扑特征表示，但计算成本更高

### 性能优化参数

- **use_parallel**: 是否使用并行计算。
  - 类型: bool
  - 默认值: True
  - 建议值: 对于大型数据集，建议启用

- **num_workers**: 并行计算的工作线程数。
  - 类型: int
  - 默认值: 4
  - 取值范围: 正整数
  - 建议值: 根据CPU核心数设置，通常为CPU核心数的1-2倍

- **use_cache**: 是否使用缓存。
  - 类型: bool
  - 默认值: True
  - 建议值: 对于重复计算较多的场景，建议启用

- **cache_size**: 缓存大小。
  - 类型: int
  - 默认值: 1000
  - 取值范围: 正整数
  - 建议值: 根据内存大小和数据集规模设置

- **use_incremental**: 是否使用增量计算。
  - 类型: bool
  - 默认值: True
  - 建议值: 对于动态变化的数据集，建议启用

- **use_sparse**: 是否使用稀疏矩阵。
  - 类型: bool
  - 默认值: True
  - 建议值: 对于大型数据集，建议启用

- **use_rust**: 是否使用Rust实现。
  - 类型: bool
  - 默认值: True
  - 建议值: 对于计算密集型任务，建议启用

- **threshold_dimension**: 切换到Rust实现的阈值维度。
  - 类型: int
  - 默认值: 50
  - 取值范围: 正整数
  - 建议值: 根据数据集规模设置，通常为50-100

- **block_size**: 处理块大小。
  - 类型: int
  - 默认值: 64
  - 取值范围: 正整数
  - 建议值: 根据CPU缓存大小设置，通常为32-128

- **verbose**: 是否输出详细日志。
  - 类型: bool
  - 默认值: False
  - 建议值: 调试时启用，生产环境禁用

## 使用示例

### 基本用法

```python
from src.algorithms.topology.homology_optimized import OptimizedPersistentHomologyAnalyzer
import numpy as np

# 创建点云数据
num_points = 100
theta = np.linspace(0, 2 * np.pi, num_points)
radius = 1.0 + 0.1 * np.random.randn(num_points)
x = radius * np.cos(theta)
y = radius * np.sin(theta)
points = np.column_stack((x, y))

# 创建分析器
analyzer = OptimizedPersistentHomologyAnalyzer(
    max_dimension=2,
    max_radius=2.0,
    num_divisions=50,
    use_parallel=True,
    num_workers=4
)

# 执行持久同调分析
result = analyzer.compute(points)

# 获取结果
persistence_diagrams = result['persistence_diagrams']
betti_curves = result['betti_curves']
persistence_landscapes = result['persistence_landscapes']

# 打印结果
for dim, diagram in persistence_diagrams.items():
    print(f"维度 {dim} 的持久图:")
    print(diagram)
    print(f"持久点数量: {len(diagram)}")
    print()

for dim, curve in betti_curves.items():
    print(f"维度 {dim} 的贝蒂曲线长度: {len(curve)}")

for dim, landscape in persistence_landscapes.items():
    print(f"维度 {dim} 的持久景观形状: {landscape.shape}")
```

### 增量计算

```python
from src.algorithms.topology.homology_optimized import OptimizedPersistentHomologyAnalyzer
import numpy as np
import time

# 创建点云数据
num_points = 100
theta = np.linspace(0, 2 * np.pi, num_points)
radius = 1.0 + 0.1 * np.random.randn(num_points)
x = radius * np.cos(theta)
y = radius * np.sin(theta)
points1 = np.column_stack((x, y))

# 创建分析器
analyzer = OptimizedPersistentHomologyAnalyzer(
    max_dimension=2,
    max_radius=2.0,
    num_divisions=50,
    use_incremental=True
)

# 第一次计算
start_time = time.time()
result1 = analyzer.compute(points1)
first_time = time.time() - start_time
print(f"首次计算时间: {first_time:.4f}秒")

# 创建小幅变化的数据
points2 = points1.copy()
points2 += 0.05 * np.random.randn(*points2.shape)  # 添加一些噪声

# 使用增量计算
start_time = time.time()
result2 = analyzer.compute(points2, base_result=result1)
incremental_time = time.time() - start_time
print(f"增量计算时间: {incremental_time:.4f}秒")

# 不使用增量计算
analyzer.use_incremental = False
start_time = time.time()
result3 = analyzer.compute(points2)
full_time = time.time() - start_time
print(f"完全重新计算时间: {full_time:.4f}秒")

# 计算加速比
speedup = full_time / incremental_time
print(f"增量计算加速比: {speedup:.2f}x")
```

### 使用回调函数

```python
from src.algorithms.topology.homology_optimized import OptimizedPersistentHomologyAnalyzer
import numpy as np

# 创建点云数据
num_points = 100
theta = np.linspace(0, 2 * np.pi, num_points)
radius = 1.0 + 0.1 * np.random.randn(num_points)
x = radius * np.cos(theta)
y = radius * np.sin(theta)
points = np.column_stack((x, y))

# 定义回调函数
def callback(current_step, total_steps, stage, status):
    print(f"进度: {current_step}/{total_steps} ({current_step/total_steps*100:.1f}%), 阶段: {stage}, 状态: {status}")

# 创建分析器
analyzer = OptimizedPersistentHomologyAnalyzer(
    max_dimension=2,
    max_radius=2.0,
    num_divisions=50,
    verbose=True
)

# 执行持久同调分析
result = analyzer.compute(points, callback=callback)

# 获取性能指标
performance = analyzer.get_performance_metrics()
print("\n性能指标:")
for metric, value in performance.items():
    print(f"{metric}: {value}")
```

### 使用不同的矩阵表示

```python
from src.algorithms.topology.homology_optimized import OptimizedPersistentHomologyAnalyzer
import numpy as np
import time

# 创建点云数据
num_points = 200
theta = np.linspace(0, 2 * np.pi, num_points)
radius = 1.0 + 0.1 * np.random.randn(num_points)
x = radius * np.cos(theta)
y = radius * np.sin(theta)
points = np.column_stack((x, y))

# 测试不同的矩阵表示
results = {}

# 使用稀疏矩阵
analyzer_sparse = OptimizedPersistentHomologyAnalyzer(
    max_dimension=2,
    max_radius=2.0,
    num_divisions=50,
    use_sparse=True
)

start_time = time.time()
result_sparse = analyzer_sparse.compute(points)
sparse_time = time.time() - start_time
sparse_memory = analyzer_sparse.get_performance_metrics()['memory_usage']

# 使用密集矩阵
analyzer_dense = OptimizedPersistentHomologyAnalyzer(
    max_dimension=2,
    max_radius=2.0,
    num_divisions=50,
    use_sparse=False
)

start_time = time.time()
result_dense = analyzer_dense.compute(points)
dense_time = time.time() - start_time
dense_memory = analyzer_dense.get_performance_metrics()['memory_usage']

# 比较结果
print(f"稀疏矩阵:")
print(f"  执行时间: {sparse_time:.4f}秒")
print(f"  内存使用: {sparse_memory:.2f}MB")

print(f"密集矩阵:")
print(f"  执行时间: {dense_time:.4f}秒")
print(f"  内存使用: {dense_memory:.2f}MB")

print(f"时间比: {dense_time/sparse_time:.2f}x")
print(f"内存比: {dense_memory/sparse_memory:.2f}x")
```

## 性能特性

### 时间复杂度

- **总体时间复杂度**: O(n³)
  - n: 数据点数量

- **单纯复形构建**: O(n²)
- **持久性计算**: O(m³)，其中m是单纯形数量，通常m = O(n^d)，d是最大同调维度
- **特征提取**: O(p * k)，其中p是持久点数量，k是划分数量

### 空间复杂度

- **总体空间复杂度**: O(n²)
  - n: 数据点数量

- **距离矩阵**: O(n²)
- **单纯复形**: O(n^d)，其中d是最大同调维度
- **边界矩阵**: O(m²)，其中m是单纯形数量
- **缓存**: O(C)，C为缓存大小

### 并行性能

- **加速比**: 在4核CPU上，并行版本比串行版本快2-3倍
- **效率**: 并行效率约为70-80%
- **扩展性**: 随着CPU核心数的增加，加速比呈次线性增长

### 增量计算性能

- **小幅变化**: 对于数据的小幅变化，增量计算比完全重新计算快5-10倍
- **中等变化**: 对于数据的中等变化，增量计算比完全重新计算快2-5倍
- **大幅变化**: 对于数据的大幅变化，增量计算可能不如完全重新计算

### 稀疏矩阵性能

- **内存减少**: 使用稀疏矩阵表示可以减少50-90%的内存使用
- **计算加速**: 对于大型数据集，使用稀疏矩阵可以加速1.5-3倍

### Rust实现性能

- **加速比**: Rust实现比Python实现快5-20倍
- **内存效率**: Rust实现比Python实现使用更少的内存（约减少30-50%）

## 注意事项

1. **维度诅咒**: 随着同调维度的增加，计算复杂度呈指数增长。对于高维同调（维度>3），计算可能非常耗时。

2. **内存使用**: 对于大型数据集，算法可能消耗大量内存，特别是在构建单纯复形和边界矩阵时。如果内存不足，可以使用稀疏矩阵表示或减小最大半径。

3. **参数选择**: 算法性能和结果质量对参数设置敏感，特别是最大半径和最大同调维度。建议根据具体问题和数据特性进行参数调优。

4. **数据预处理**: 对于噪声数据，建议在计算持久同调之前进行预处理，如去噪、归一化等。

5. **结果解释**: 持久图和贝蒂曲线的解释需要一定的拓扑学知识。建议结合可视化工具和领域知识进行结果分析。

6. **并行计算**: 并行计算可能增加内存使用。如果内存不足，可以减小工作线程数或禁用并行计算。

7. **增量计算**: 增量计算适用于数据小幅变化的情况。如果数据发生大幅变化，建议禁用增量计算。

## 算法改进方向

1. **近似算法**: 实现近似持久同调算法，如随机投影、稀疏表示等，以处理更大规模的数据集。

2. **流式处理**: 实现流式持久同调算法，以处理无法一次性加载到内存的大型数据集。

3. **分布式计算**: 实现分布式计算支持，使算法能够在集群上运行，处理超大规模数据集。

4. **GPU加速**: 利用GPU加速计算，特别是矩阵运算部分。

5. **多尺度分析**: 实现多尺度持久同调分析，以捕获不同尺度的拓扑特征。

6. **统计推断**: 结合统计方法，提供持久同调结果的统计显著性检验。

7. **机器学习集成**: 将持久同调特征与机器学习算法集成，提高分类、聚类和异常检测的性能。
