# API参考文档 - 核心模块

## 概述

本文档提供超越态思维引擎4.0核心模块的API参考。核心模块包含系统的基础组件，如引擎、算子和算法等。

## TranscendentalEngine

`TranscendentalEngine`是系统的主引擎类，提供系统的核心功能。

### 构造函数

```python
TranscendentalEngine(config=None)
```

**参数**:
- `config` (EngineConfig, optional): 引擎配置对象。如果为None，则使用默认配置。

**返回**:
- TranscendentalEngine实例。

### 方法

#### register_operator

```python
register_operator(operator)
```

注册算子到引擎。

**参数**:
- `operator` (Operator): 要注册的算子对象。

**返回**:
- bool: 如果注册成功则返回True，否则返回False。

#### register_algorithm

```python
register_algorithm(algorithm)
```

注册算法到引擎。

**参数**:
- `algorithm` (Algorithm): 要注册的算法对象。

**返回**:
- bool: 如果注册成功则返回True，否则返回False。

#### compute

```python
compute(input_data, **kwargs)
```

执行计算。

**参数**:
- `input_data` (dict): 输入数据。
- `**kwargs`: 其他参数。

**返回**:
- dict: 计算结果。

#### apply_operator

```python
apply_operator(operator_name, input_data, **kwargs)
```

应用指定的算子。

**参数**:
- `operator_name` (str): 算子名称。
- `input_data` (dict): 输入数据。
- `**kwargs`: 其他参数。

**返回**:
- dict: 应用算子后的结果。

#### run_algorithm

```python
run_algorithm(algorithm_name, input_data, **kwargs)
```

运行指定的算法。

**参数**:
- `algorithm_name` (str): 算法名称。
- `input_data` (dict): 输入数据。
- `**kwargs`: 其他参数。

**返回**:
- dict: 运行算法后的结果。

#### save_state

```python
save_state(file_path)
```

保存引擎状态到文件。

**参数**:
- `file_path` (str): 文件路径。

**返回**:
- bool: 如果保存成功则返回True，否则返回False。

#### load_state

```python
@classmethod
load_state(cls, file_path)
```

从文件加载引擎状态。

**参数**:
- `file_path` (str): 文件路径。

**返回**:
- TranscendentalEngine: 加载状态后的引擎实例。

#### get_operators

```python
get_operators()
```

获取所有注册的算子。

**返回**:
- dict: 算子名称到算子对象的映射。

#### get_algorithms

```python
get_algorithms()
```

获取所有注册的算法。

**返回**:
- dict: 算法名称到算法对象的映射。

## EngineConfig

`EngineConfig`是引擎配置类，用于配置引擎的行为。

### 构造函数

```python
EngineConfig(max_threads=None, memory_limit=None, log_level=None, **kwargs)
```

**参数**:
- `max_threads` (int, optional): 最大线程数。
- `memory_limit` (str, optional): 内存限制，如"4GB"。
- `log_level` (str, optional): 日志级别，如"INFO"。
- `**kwargs`: 其他配置参数。

**返回**:
- EngineConfig实例。

### 方法

#### to_dict

```python
to_dict()
```

将配置转换为字典。

**返回**:
- dict: 配置字典。

#### from_dict

```python
@classmethod
from_dict(cls, config_dict)
```

从字典创建配置。

**参数**:
- `config_dict` (dict): 配置字典。

**返回**:
- EngineConfig: 配置实例。

#### from_file

```python
@classmethod
from_file(cls, file_path)
```

从文件加载配置。

**参数**:
- `file_path` (str): 文件路径。

**返回**:
- EngineConfig: 配置实例。

## Operator

`Operator`是算子基类，所有自定义算子应继承此类。

### 构造函数

```python
Operator(name=None, **kwargs)
```

**参数**:
- `name` (str, optional): 算子名称。如果为None，则使用类名。
- `**kwargs`: 其他参数。

**返回**:
- Operator实例。

### 方法

#### apply

```python
apply(input_data)
```

应用算子。

**参数**:
- `input_data` (dict): 输入数据。

**返回**:
- dict: 应用算子后的结果。

#### get_name

```python
get_name()
```

获取算子名称。

**返回**:
- str: 算子名称。

#### get_metadata

```python
get_metadata()
```

获取算子元数据。

**返回**:
- dict: 算子元数据。

## Algorithm

`Algorithm`是算法基类，所有自定义算法应继承此类。

### 构造函数

```python
Algorithm(name=None, **kwargs)
```

**参数**:
- `name` (str, optional): 算法名称。如果为None，则使用类名。
- `**kwargs`: 其他参数。

**返回**:
- Algorithm实例。

### 方法

#### run

```python
run(input_data)
```

运行算法。

**参数**:
- `input_data` (dict): 输入数据。

**返回**:
- dict: 运行算法后的结果。

#### get_name

```python
get_name()
```

获取算法名称。

**返回**:
- str: 算法名称。

#### get_metadata

```python
get_metadata()
```

获取算法元数据。

**返回**:
- dict: 算法元数据。

## Registry

`Registry`是组件注册表类，用于管理算子和算法。

### 构造函数

```python
Registry()
```

**返回**:
- Registry实例。

### 方法

#### register_operator

```python
register_operator(operator)
```

注册算子。

**参数**:
- `operator` (Operator): 要注册的算子对象。

**返回**:
- bool: 如果注册成功则返回True，否则返回False。

#### register_algorithm

```python
register_algorithm(algorithm)
```

注册算法。

**参数**:
- `algorithm` (Algorithm): 要注册的算法对象。

**返回**:
- bool: 如果注册成功则返回True，否则返回False。

#### get_operator

```python
get_operator(name)
```

获取指定名称的算子。

**参数**:
- `name` (str): 算子名称。

**返回**:
- Operator: 算子对象，如果不存在则返回None。

#### get_algorithm

```python
get_algorithm(name)
```

获取指定名称的算法。

**参数**:
- `name` (str): 算法名称。

**返回**:
- Algorithm: 算法对象，如果不存在则返回None。

#### get_operators

```python
get_operators()
```

获取所有注册的算子。

**返回**:
- dict: 算子名称到算子对象的映射。

#### get_algorithms

```python
get_algorithms()
```

获取所有注册的算法。

**返回**:
- dict: 算法名称到算法对象的映射。
