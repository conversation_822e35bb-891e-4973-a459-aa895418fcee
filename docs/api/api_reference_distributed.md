# API参考文档 - 分布式模块

## 概述

本文档提供超越态思维引擎4.0分布式模块的API参考。分布式模块包含系统的分布式计算组件，如分布式引擎、节点管理和通信等。

## DistributedEngine

`DistributedEngine`是分布式引擎类，提供分布式计算功能。

### 构造函数

```python
DistributedEngine(node_config, engine=None)
```

**参数**:
- `node_config` (NodeConfig): 节点配置对象。
- `engine` (TranscendentalEngine, optional): 基础引擎实例。如果为None，则创建新实例。

**返回**:
- DistributedEngine实例。

### 方法

#### connect_to_node

```python
connect_to_node(node_id, host, port)
```

连接到远程节点。

**参数**:
- `node_id` (str): 节点ID。
- `host` (str): 主机名或IP地址。
- `port` (int): 端口号。

**返回**:
- bool: 如果连接成功则返回True，否则返回False。

#### disconnect_from_node

```python
disconnect_from_node(node_id)
```

断开与远程节点的连接。

**参数**:
- `node_id` (str): 节点ID。

**返回**:
- bool: 如果断开连接成功则返回True，否则返回False。

#### get_connected_nodes

```python
get_connected_nodes()
```

获取所有已连接的节点。

**返回**:
- list: 节点信息列表。

#### compute_distributed

```python
compute_distributed(input_data, distribution_strategy=None, **kwargs)
```

分布式执行计算。

**参数**:
- `input_data` (dict): 输入数据。
- `distribution_strategy` (str, optional): 任务分发策略。
- `**kwargs`: 其他参数。

**返回**:
- dict: 计算结果。

#### run_algorithm_distributed

```python
run_algorithm_distributed(algorithm_name, input_data, distribution_strategy=None, **kwargs)
```

分布式运行算法。

**参数**:
- `algorithm_name` (str): 算法名称。
- `input_data` (dict): 输入数据。
- `distribution_strategy` (str, optional): 任务分发策略。
- `**kwargs`: 其他参数。

**返回**:
- dict: 运行算法后的结果。

#### submit_task

```python
submit_task(task_data, target_node=None)
```

提交任务。

**参数**:
- `task_data` (dict): 任务数据。
- `target_node` (str, optional): 目标节点ID。如果为None，则由系统选择节点。

**返回**:
- str: 任务ID。

#### get_task_result

```python
get_task_result(task_id, timeout=None)
```

获取任务结果。

**参数**:
- `task_id` (str): 任务ID。
- `timeout` (float, optional): 超时时间（秒）。

**返回**:
- dict: 任务结果。

#### get_node_status

```python
get_node_status(node_id=None)
```

获取节点状态。

**参数**:
- `node_id` (str, optional): 节点ID。如果为None，则获取所有节点状态。

**返回**:
- dict: 节点状态信息。

## NodeConfig

`NodeConfig`是节点配置类，用于配置分布式节点。

### 构造函数

```python
NodeConfig(node_id, host="localhost", port=9000, **kwargs)
```

**参数**:
- `node_id` (str): 节点ID。
- `host` (str, optional): 主机名或IP地址。默认为"localhost"。
- `port` (int, optional): 端口号。默认为9000。
- `**kwargs`: 其他配置参数。

**返回**:
- NodeConfig实例。

### 方法

#### to_dict

```python
to_dict()
```

将配置转换为字典。

**返回**:
- dict: 配置字典。

#### from_dict

```python
@classmethod
from_dict(cls, config_dict)
```

从字典创建配置。

**参数**:
- `config_dict` (dict): 配置字典。

**返回**:
- NodeConfig: 配置实例。

#### from_file

```python
@classmethod
from_file(cls, file_path)
```

从文件加载配置。

**参数**:
- `file_path` (str): 文件路径。

**返回**:
- NodeConfig: 配置实例。

## NodeManager

`NodeManager`是节点管理类，提供节点发现和管理功能。

### 构造函数

```python
NodeManager(node_config)
```

**参数**:
- `node_config` (NodeConfig): 节点配置对象。

**返回**:
- NodeManager实例。

### 方法

#### discover_nodes

```python
discover_nodes(discovery_method=None, **kwargs)
```

发现网络中的节点。

**参数**:
- `discovery_method` (str, optional): 发现方法，如"static"、"multicast"、"redis"。
- `**kwargs`: 其他参数。

**返回**:
- list: 发现的节点列表。

#### register_node

```python
register_node(node_id, host, port)
```

注册节点。

**参数**:
- `node_id` (str): 节点ID。
- `host` (str): 主机名或IP地址。
- `port` (int): 端口号。

**返回**:
- bool: 如果注册成功则返回True，否则返回False。

#### unregister_node

```python
unregister_node(node_id)
```

注销节点。

**参数**:
- `node_id` (str): 节点ID。

**返回**:
- bool: 如果注销成功则返回True，否则返回False。

#### get_node_status

```python
get_node_status(node_id)
```

获取节点状态。

**参数**:
- `node_id` (str): 节点ID。

**返回**:
- dict: 节点状态信息。

#### get_node_metrics

```python
get_node_metrics(node_id)
```

获取节点指标。

**参数**:
- `node_id` (str): 节点ID。

**返回**:
- dict: 节点指标信息。

## TaskScheduler

`TaskScheduler`是任务调度器类，负责分发和调度任务。

### 构造函数

```python
TaskScheduler(node_manager, strategy="round_robin")
```

**参数**:
- `node_manager` (NodeManager): 节点管理器实例。
- `strategy` (str, optional): 调度策略，如"round_robin"、"load_balanced"、"nearest"。默认为"round_robin"。

**返回**:
- TaskScheduler实例。

### 方法

#### schedule_task

```python
schedule_task(task_data, target_node=None)
```

调度任务。

**参数**:
- `task_data` (dict): 任务数据。
- `target_node` (str, optional): 目标节点ID。如果为None，则由调度器选择节点。

**返回**:
- str: 任务ID。

#### get_task_status

```python
get_task_status(task_id)
```

获取任务状态。

**参数**:
- `task_id` (str): 任务ID。

**返回**:
- dict: 任务状态信息。

#### get_task_result

```python
get_task_result(task_id, timeout=None)
```

获取任务结果。

**参数**:
- `task_id` (str): 任务ID。
- `timeout` (float, optional): 超时时间（秒）。

**返回**:
- dict: 任务结果。

#### cancel_task

```python
cancel_task(task_id)
```

取消任务。

**参数**:
- `task_id` (str): 任务ID。

**返回**:
- bool: 如果取消成功则返回True，否则返回False。

## CommunicationManager

`CommunicationManager`是通信管理器类，负责节点间通信。

### 构造函数

```python
CommunicationManager(node_config)
```

**参数**:
- `node_config` (NodeConfig): 节点配置对象。

**返回**:
- CommunicationManager实例。

### 方法

#### send_message

```python
send_message(target_node, message)
```

发送消息到目标节点。

**参数**:
- `target_node` (str): 目标节点ID。
- `message` (dict): 消息内容。

**返回**:
- bool: 如果发送成功则返回True，否则返回False。

#### receive_message

```python
receive_message(timeout=None)
```

接收消息。

**参数**:
- `timeout` (float, optional): 超时时间（秒）。

**返回**:
- dict: 接收到的消息。

#### register_message_handler

```python
register_message_handler(message_type, handler_func)
```

注册消息处理器。

**参数**:
- `message_type` (str): 消息类型。
- `handler_func` (callable): 处理函数。

**返回**:
- bool: 如果注册成功则返回True，否则返回False。

#### start

```python
start()
```

启动通信管理器。

**返回**:
- bool: 如果启动成功则返回True，否则返回False。

#### stop

```python
stop()
```

停止通信管理器。

**返回**:
- bool: 如果停止成功则返回True，否则返回False。
