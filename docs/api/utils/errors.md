# errors

错误处理模块

这个模块提供了超越态思维引擎的错误处理功能，包括错误类型定义和错误处理机制。

## Classes

### TTEError

超越态思维引擎基础错误类

#### Methods

##### __init__(message: str, error_code: Optional[int] = None, details: Optional[Dict[str, Any]] = None)

初始化错误

参数:
    message: 错误消息
    error_code: 错误代码
    details: 错误详情

##### to_dict() -> Dict[str, Any]

将错误转换为字典

返回:
    错误字典

##### __str__() -> str

返回错误的字符串表示

### TTEValueError

值错误

#### Methods

##### __init__(message: str, value: Any = None, expected_type: Optional[Type] = None, error_code: Optional[int] = None, details: Optional[Dict[str, Any]] = None)

初始化值错误

参数:
    message: 错误消息
    value: 错误值
    expected_type: 期望类型
    error_code: 错误代码
    details: 错误详情

### TTETypeError

类型错误

#### Methods

##### __init__(message: str, value: Any = None, expected_type: Optional[Type] = None, error_code: Optional[int] = None, details: Optional[Dict[str, Any]] = None)

初始化类型错误

参数:
    message: 错误消息
    value: 错误值
    expected_type: 期望类型
    error_code: 错误代码
    details: 错误详情

### ErrorHandler

错误处理器

#### Methods

##### handle_error(error: Exception, log_error: bool = True, raise_error: bool = False) -> Dict[str, Any]

处理错误

参数:
    error: 错误
    log_error: 是否记录错误
    raise_error: 是否抛出错误
    
返回:
    错误信息字典

##### wrap_exceptions(func: callable, error_message: str = "An error occurred", error_code: Optional[int] = None) -> callable

包装异常

参数:
    func: 函数
    error_message: 错误消息
    error_code: 错误代码
    
返回:
    包装后的函数
