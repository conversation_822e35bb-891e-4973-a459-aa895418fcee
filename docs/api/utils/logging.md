# logging

日志记录模块

这个模块提供了超越态思维引擎的日志记录功能，包括日志配置、日志格式化和日志过滤等。

## Classes

### LogLevel

日志级别

#### Attributes

##### DEBUG

Type: int

##### INFO

Type: int

##### WARNING

Type: int

##### ERROR

Type: int

##### CRITICAL

Type: int

### JSONFormatter

JSON格式化器

#### Methods

##### __init__(include_timestamp: bool = True, include_level: bool = True, include_logger_name: bool = True, include_thread: bool = False, include_process: bool = False, include_file_info: bool = False)

初始化JSON格式化器

参数:
    include_timestamp: 是否包含时间戳
    include_level: 是否包含日志级别
    include_logger_name: 是否包含日志记录器名称
    include_thread: 是否包含线程信息
    include_process: 是否包含进程信息
    include_file_info: 是否包含文件信息

##### format(record: logging.LogRecord) -> str

格式化日志记录

参数:
    record: 日志记录
    
返回:
    格式化后的日志

### ContextFilter

上下文过滤器

#### Methods

##### __init__(context: Dict[str, Any] = None)

初始化上下文过滤器

参数:
    context: 上下文信息

##### filter(record: logging.LogRecord) -> bool

过滤日志记录

参数:
    record: 日志记录
    
返回:
    是否保留日志记录

### LogContext

日志上下文

#### Methods

##### get_context() -> Dict[str, Any]

获取上下文

返回:
    上下文信息

##### set_context(key: str, value: Any) -> None

设置上下文

参数:
    key: 键
    value: 值

##### clear_context() -> None

清除上下文

##### with_context(**kwargs) -> Callable[[F], F]

使用上下文装饰器

参数:
    **kwargs: 上下文信息
    
返回:
    装饰器

## Functions

### configure_logging(level: int = LogLevel.INFO, log_file: Optional[str] = None, json_format: bool = False, include_timestamp: bool = True, include_level: bool = True, include_logger_name: bool = True, include_thread: bool = False, include_process: bool = False, include_file_info: bool = False) -> None

配置日志

参数:
    level: 日志级别
    log_file: 日志文件路径
    json_format: 是否使用JSON格式
    include_timestamp: 是否包含时间戳
    include_level: 是否包含日志级别
    include_logger_name: 是否包含日志记录器名称
    include_thread: 是否包含线程信息
    include_process: 是否包含进程信息
    include_file_info: 是否包含文件信息

### get_logger(name: str, level: Optional[int] = None) -> logging.Logger

获取日志记录器

参数:
    name: 日志记录器名称
    level: 日志级别
    
返回:
    日志记录器

### log_function(level: int = LogLevel.INFO, logger_name: Optional[str] = None, log_args: bool = True, log_result: bool = True, log_execution_time: bool = True) -> Callable[[F], F]

日志装饰器

参数:
    level: 日志级别
    logger_name: 日志记录器名称
    log_args: 是否记录参数
    log_result: 是否记录结果
    log_execution_time: 是否记录执行时间
    
返回:
    装饰器
