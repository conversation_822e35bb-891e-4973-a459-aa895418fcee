# config

配置管理模块

这个模块提供了超越态思维引擎的配置管理功能，包括配置加载、验证和合并等。

## Classes

### ConfigManager

配置管理器

#### Methods

##### __new__(cls)

单例模式

##### __init__()

初始化配置管理器

##### load_config(config_path: str, format: Optional[str] = None) -> ConfigType

加载配置

参数:
    config_path: 配置文件路径
    format: 配置文件格式，如果为None则根据文件扩展名自动判断
    
返回:
    配置字典

##### load_config_from_string(config_string: str, format: str) -> ConfigType

从字符串加载配置

参数:
    config_string: 配置字符串
    format: 配置字符串格式
    
返回:
    配置字典

##### load_config_from_env(prefix: str = "TTE_", separator: str = "__") -> ConfigType

从环境变量加载配置

参数:
    prefix: 环境变量前缀
    separator: 环境变量分隔符
    
返回:
    配置字典

##### set_config(config: ConfigType) -> None

设置配置

参数:
    config: 配置字典

##### get_config() -> ConfigType

获取配置

返回:
    配置字典

##### get_value(key: str, default: Any = None) -> Any

获取配置值

参数:
    key: 配置键，使用点号分隔嵌套键
    default: 默认值
    
返回:
    配置值

##### set_value(key: str, value: Any) -> None

设置配置值

参数:
    key: 配置键，使用点号分隔嵌套键
    value: 配置值

##### set_schema(schema: Dict[str, Any]) -> None

设置配置模式

参数:
    schema: 配置模式

##### get_schema() -> Dict[str, Any]

获取配置模式

返回:
    配置模式

##### watch(key: str, callback: Callable[[Any, Any], None]) -> str

监听配置变化

参数:
    key: 配置键，使用点号分隔嵌套键
    callback: 回调函数，接收旧值和新值
    
返回:
    监听器ID

##### unwatch(watcher_id: str) -> bool

取消监听配置变化

参数:
    watcher_id: 监听器ID
    
返回:
    是否成功取消

##### save_config(config_path: str, format: Optional[str] = None) -> None

保存配置

参数:
    config_path: 配置文件路径
    format: 配置文件格式，如果为None则根据文件扩展名自动判断

## Functions

### load_config(config_path: str, format: Optional[str] = None) -> ConfigType

加载配置

参数:
    config_path: 配置文件路径
    format: 配置文件格式，如果为None则根据文件扩展名自动判断
    
返回:
    配置字典

### load_config_from_string(config_string: str, format: str) -> ConfigType

从字符串加载配置

参数:
    config_string: 配置字符串
    format: 配置字符串格式
    
返回:
    配置字典

### load_config_from_env(prefix: str = "TTE_", separator: str = "__") -> ConfigType

从环境变量加载配置

参数:
    prefix: 环境变量前缀
    separator: 环境变量分隔符
    
返回:
    配置字典

### set_config(config: ConfigType) -> None

设置配置

参数:
    config: 配置字典

### get_config() -> ConfigType

获取配置

返回:
    配置字典

### get_value(key: str, default: Any = None) -> Any

获取配置值

参数:
    key: 配置键，使用点号分隔嵌套键
    default: 默认值
    
返回:
    配置值

### set_value(key: str, value: Any) -> None

设置配置值

参数:
    key: 配置键，使用点号分隔嵌套键
    value: 配置值

### set_schema(schema: Dict[str, Any]) -> None

设置配置模式

参数:
    schema: 配置模式

### get_schema() -> Dict[str, Any]

获取配置模式

返回:
    配置模式

### watch(key: str, callback: Callable[[Any, Any], None]) -> str

监听配置变化

参数:
    key: 配置键，使用点号分隔嵌套键
    callback: 回调函数，接收旧值和新值
    
返回:
    监听器ID

### unwatch(watcher_id: str) -> bool

取消监听配置变化

参数:
    watcher_id: 监听器ID
    
返回:
    是否成功取消

### save_config(config_path: str, format: Optional[str] = None) -> None

保存配置

参数:
    config_path: 配置文件路径
    format: 配置文件格式，如果为None则根据文件扩展名自动判断
