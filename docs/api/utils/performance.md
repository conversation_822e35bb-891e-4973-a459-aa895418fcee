# performance

性能监控模块

这个模块提供了超越态思维引擎的性能监控功能，包括CPU、内存、磁盘和网络使用情况等。

## Classes

### Metric

性能指标

#### Methods

##### __init__(name: str, value: float, unit: str = "", tags: Optional[Dict[str, str]] = None)

初始化性能指标

参数:
    name: 指标名称
    value: 指标值
    unit: 指标单位
    tags: 指标标签

##### __str__() -> str

返回性能指标的字符串表示

### PerformanceMonitor

性能监控器

#### Methods

##### __init__(name: str, tags: Optional[Dict[str, str]] = None)

初始化性能监控器

参数:
    name: 监控器名称
    tags: 监控器标签

##### __enter__() -> 'PerformanceMonitor'

进入上下文

##### __exit__(exc_type, exc_val, exc_tb) -> None

退出上下文

##### start() -> None

开始监控

##### stop() -> None

停止监控

##### add_metric(name: str, value: float, unit: str = "", tags: Optional[Dict[str, str]] = None) -> None

添加指标

参数:
    name: 指标名称
    value: 指标值
    unit: 指标单位
    tags: 指标标签

##### get_metrics() -> List[Metric]

获取指标

返回:
    指标列表

##### get_duration() -> Optional[float]

获取持续时间

返回:
    持续时间（秒）

### SystemPerformanceMonitor

系统性能监控器

#### Methods

##### __new__(cls)

单例模式

##### __init__()

初始化系统性能监控器

##### start(interval: float = 1.0) -> None

开始监控

参数:
    interval: 监控间隔（秒）

##### stop() -> None

停止监控

##### add_callback(callback: Callable[[List[Metric]], None]) -> None

添加回调函数

参数:
    callback: 回调函数，接收指标列表

##### remove_callback(callback: Callable[[List[Metric]], None]) -> bool

移除回调函数

参数:
    callback: 回调函数
    
返回:
    是否成功移除

##### get_metrics() -> List[Metric]

获取指标

返回:
    指标列表

## Functions

### monitor_performance(name: Optional[str] = None, tags: Optional[Dict[str, str]] = None) -> Callable[[F], F]

性能监控装饰器

参数:
    name: 监控器名称，如果为None则使用函数名
    tags: 监控器标签
    
返回:
    装饰器

### start_system_monitoring(interval: float = 1.0) -> None

开始系统性能监控

参数:
    interval: 监控间隔（秒）

### stop_system_monitoring() -> None

停止系统性能监控

### add_system_monitoring_callback(callback: Callable[[List[Metric]], None]) -> None

添加系统性能监控回调函数

参数:
    callback: 回调函数，接收指标列表

### remove_system_monitoring_callback(callback: Callable[[List[Metric]], None]) -> bool

移除系统性能监控回调函数

参数:
    callback: 回调函数
    
返回:
    是否成功移除

### get_system_metrics() -> List[Metric]

获取系统性能指标

返回:
    指标列表

### benchmark(func: Callable, *args, **kwargs) -> Dict[str, Any]

性能基准测试

参数:
    func: 测试函数
    *args: 函数参数
    **kwargs: 函数关键字参数
    
返回:
    基准测试结果
