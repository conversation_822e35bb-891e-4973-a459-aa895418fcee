# API参考文档 - 批处理模块

## 概述

本文档提供超越态思维引擎4.0批处理模块的API参考。批处理模块包含系统的批量任务处理组件，如任务批次、批处理调度器和批处理执行器等。

## TaskBatch

`TaskBatch`是任务批次类，用于将多个相似任务组合成批次。

### 构造函数

```python
TaskBatch(batch_id=None, strategy=BatchStrategy.SAME_TYPE, max_size=100, max_wait_time=1.0, priority=TaskPriority.NORMAL, metadata=None)
```

**参数**:
- `batch_id` (str, optional): 批次ID，如果为None则自动生成。
- `strategy` (BatchStrategy): 批处理策略。
- `max_size` (int): 最大批次大小。
- `max_wait_time` (float): 最大等待时间（秒）。
- `priority` (TaskPriority): 批次优先级。
- `metadata` (dict, optional): 元数据。

**返回**:
- TaskBatch实例。

### 方法

#### add_task

```python
add_task(task)
```

添加任务到批次。

**参数**:
- `task` (Task): 要添加的任务。

**返回**:
- bool: 如果添加成功则返回True，否则返回False。

#### remove_task

```python
remove_task(task_id)
```

从批次中移除任务。

**参数**:
- `task_id` (str): 任务ID。

**返回**:
- bool: 如果移除成功则返回True，否则返回False。

#### get_task

```python
get_task(task_id)
```

获取批次中的任务。

**参数**:
- `task_id` (str): 任务ID。

**返回**:
- Task: 如果任务存在则返回任务对象，否则返回None。

#### get_tasks

```python
get_tasks()
```

获取批次中的所有任务。

**返回**:
- list: 任务列表。

#### get_task_count

```python
get_task_count()
```

获取批次中的任务数量。

**返回**:
- int: 任务数量。

#### is_empty

```python
is_empty()
```

检查批次是否为空。

**返回**:
- bool: 如果批次为空则返回True，否则返回False。

#### is_full

```python
is_full()
```

检查批次是否已满。

**返回**:
- bool: 如果批次已满则返回True，否则返回False。

#### is_ready

```python
is_ready()
```

检查批次是否准备就绪。

**返回**:
- bool: 如果批次准备就绪则返回True，否则返回False。

#### is_timed_out

```python
is_timed_out()
```

检查批次是否超时。

**返回**:
- bool: 如果批次超时则返回True，否则返回False。

#### mark_ready

```python
mark_ready()
```

将批次标记为准备就绪。

**返回**:
- bool: 如果标记成功则返回True，否则返回False。

#### mark_scheduled

```python
mark_scheduled()
```

将批次标记为已调度。

**返回**:
- bool: 如果标记成功则返回True，否则返回False。

#### mark_running

```python
mark_running()
```

将批次标记为运行中。

**返回**:
- bool: 如果标记成功则返回True，否则返回False。

#### mark_completed

```python
mark_completed()
```

将批次标记为已完成。

**返回**:
- bool: 如果标记成功则返回True，否则返回False。

#### mark_failed

```python
mark_failed()
```

将批次标记为失败。

**返回**:
- bool: 如果标记成功则返回True，否则返回False。

#### mark_cancelled

```python
mark_cancelled()
```

将批次标记为已取消。

**返回**:
- bool: 如果标记成功则返回True，否则返回False。

#### get_stats

```python
get_stats()
```

获取批次统计信息。

**返回**:
- dict: 统计信息。

#### to_dict

```python
to_dict()
```

将批次转换为字典。

**返回**:
- dict: 批次字典。

#### from_dict

```python
@classmethod
from_dict(cls, data)
```

从字典创建批次。

**参数**:
- `data` (dict): 批次字典。

**返回**:
- TaskBatch: 批次对象。

## BatchScheduler

`BatchScheduler`是批处理调度器类，用于将任务分组成批次并进行调度。

### 构造函数

```python
BatchScheduler(scheduler_id=None, batching_policy=BatchingPolicy.HYBRID, default_batch_size=100, default_batch_timeout=1.0, max_batch_count=1000, adaptive_sizing=True, metadata=None)
```

**参数**:
- `scheduler_id` (str, optional): 调度器ID，如果为None则自动生成。
- `batching_policy` (BatchingPolicy): 批处理策略。
- `default_batch_size` (int): 默认批次大小。
- `default_batch_timeout` (float): 默认批次超时时间（秒）。
- `max_batch_count` (int): 最大批次数量。
- `adaptive_sizing` (bool): 是否启用自适应批次大小。
- `metadata` (dict, optional): 元数据。

**返回**:
- BatchScheduler实例。

### 方法

#### start

```python
start()
```

启动批处理调度器。

**返回**:
- bool: 如果启动成功则返回True，否则返回False。

#### stop

```python
stop()
```

停止批处理调度器。

**返回**:
- bool: 如果停止成功则返回True，否则返回False。

#### submit_task

```python
submit_task(task)
```

提交任务。

**参数**:
- `task` (Task): 要提交的任务。

**返回**:
- bool: 如果提交成功则返回True，否则返回False。

#### cancel_task

```python
cancel_task(task_id)
```

取消任务。

**参数**:
- `task_id` (str): 任务ID。

**返回**:
- bool: 如果取消成功则返回True，否则返回False。

#### get_batch

```python
get_batch(batch_id)
```

获取批次。

**参数**:
- `batch_id` (str): 批次ID。

**返回**:
- TaskBatch: 如果批次存在则返回批次对象，否则返回None。

#### get_task_batch

```python
get_task_batch(task_id)
```

获取任务所在的批次。

**参数**:
- `task_id` (str): 任务ID。

**返回**:
- TaskBatch: 如果任务在批次中则返回批次对象，否则返回None。

#### get_next_batch

```python
get_next_batch()
```

获取下一个要处理的批次。

**返回**:
- TaskBatch: 如果有可用批次则返回批次对象，否则返回None。

#### complete_batch

```python
complete_batch(batch_id, result=None)
```

完成批次。

**参数**:
- `batch_id` (str): 批次ID。
- `result` (any, optional): 批次结果。

**返回**:
- bool: 如果完成成功则返回True，否则返回False。

#### fail_batch

```python
fail_batch(batch_id, error=None)
```

失败批次。

**参数**:
- `batch_id` (str): 批次ID。
- `error` (any, optional): 错误信息。

**返回**:
- bool: 如果失败成功则返回True，否则返回False。

#### register_batch_callback

```python
register_batch_callback(batch_id, callback)
```

注册批次回调。

**参数**:
- `batch_id` (str): 批次ID。
- `callback` (callable): 回调函数。

**返回**:
- bool: 如果注册成功则返回True，否则返回False。

#### get_stats

```python
get_stats()
```

获取统计信息。

**返回**:
- dict: 统计信息。

## BatchExecutor

`BatchExecutor`是批处理执行器类，用于高效执行任务批次。

### 构造函数

```python
BatchExecutor(executor_id=None, batch_scheduler=None, execution_mode=ExecutionMode.ADAPTIVE, max_workers=4, max_concurrent_batches=10, task_executor=None, metadata=None)
```

**参数**:
- `executor_id` (str, optional): 执行器ID，如果为None则自动生成。
- `batch_scheduler` (BatchScheduler, optional): 批处理调度器。
- `execution_mode` (ExecutionMode): 执行模式。
- `max_workers` (int): 最大工作线程数。
- `max_concurrent_batches` (int): 最大并发批次数。
- `task_executor` (callable, optional): 任务执行函数。
- `metadata` (dict, optional): 元数据。

**返回**:
- BatchExecutor实例。

### 方法

#### start

```python
start()
```

启动批处理执行器。

**返回**:
- bool: 如果启动成功则返回True，否则返回False。

#### stop

```python
stop()
```

停止批处理执行器。

**返回**:
- bool: 如果停止成功则返回True，否则返回False。

#### set_batch_scheduler

```python
set_batch_scheduler(batch_scheduler)
```

设置批处理调度器。

**参数**:
- `batch_scheduler` (BatchScheduler): 批处理调度器。

**返回**:
- None

#### set_task_executor

```python
set_task_executor(task_executor)
```

设置任务执行函数。

**参数**:
- `task_executor` (callable): 任务执行函数。

**返回**:
- None

#### execute_batch

```python
execute_batch(batch)
```

执行批次。

**参数**:
- `batch` (TaskBatch): 要执行的批次。

**返回**:
- concurrent.futures.Future: 批次执行的Future对象。

#### get_batch_result

```python
get_batch_result(batch_id)
```

获取批次结果。

**参数**:
- `batch_id` (str): 批次ID。

**返回**:
- any: 如果批次已完成则返回结果，否则返回None。

#### get_stats

```python
get_stats()
```

获取统计信息。

**返回**:
- dict: 统计信息。

## BatchProcessor

`BatchProcessor`是批处理器类，提供高级批处理功能。

### 构造函数

```python
BatchProcessor(processor, config=None)
```

**参数**:
- `processor` (callable): 批处理函数。
- `config` (BatchConfig, optional): 批处理配置。

**返回**:
- BatchProcessor实例。

### 方法

#### process_batch

```python
process_batch(batch, cache_key=None)
```

处理批次。

**参数**:
- `batch` (list): 要处理的批次。
- `cache_key` (str, optional): 缓存键。

**返回**:
- list: 处理结果。

#### process_stream

```python
process_stream(data_iterator)
```

处理数据流。

**参数**:
- `data_iterator` (iterable): 数据迭代器。

**返回**:
- generator: 结果生成器。

#### get_performance_stats

```python
get_performance_stats()
```

获取性能统计信息。

**返回**:
- dict: 性能统计信息。

#### shutdown

```python
shutdown()
```

关闭批处理器。

**返回**:
- None

## BatchConfig

`BatchConfig`是批处理配置类，用于配置批处理行为。

### 构造函数

```python
BatchConfig(batch_size=1000, max_batches=100, prefetch_batches=2, cache_enabled=True, cache_ttl=3600, distributed_cache=False, parallel_enabled=True, parallel_strategy='process', num_workers=0, gpu_device=0)
```

**参数**:
- `batch_size` (int): 批次大小。
- `max_batches` (int): 最大批次数。
- `prefetch_batches` (int): 预加载批次数。
- `cache_enabled` (bool): 是否启用缓存。
- `cache_ttl` (int): 缓存过期时间(秒)。
- `distributed_cache` (bool): 是否使用分布式缓存。
- `parallel_enabled` (bool): 是否启用并行处理。
- `parallel_strategy` (str): 并行策略: 'process', 'thread', 'gpu', 'distributed'。
- `num_workers` (int): 工作进程/线程数，0表示自动。
- `gpu_device` (int): GPU设备ID。

**返回**:
- BatchConfig实例。
