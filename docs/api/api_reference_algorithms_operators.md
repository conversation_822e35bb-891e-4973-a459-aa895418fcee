# API参考文档 - 算法与算子模块

## 概述

本文档提供超越态思维引擎4.0算法与算子模块的API参考。算法与算子模块包含系统的核心计算组件，如分形算子、范畴算子、混沌控制算法等。

## 算子 (Operators)

### FractalOperator

`FractalOperator`是分形算子类，用于处理分形结构和自相似性。

#### 构造函数

```python
FractalOperator(dimension=2.0, iterations=100, escape_radius=2.0, name=None, **kwargs)
```

**参数**:
- `dimension` (float): 分形维度。
- `iterations` (int): 迭代次数。
- `escape_radius` (float): 逃逸半径。
- `name` (str, optional): 算子名称。
- `**kwargs`: 其他参数。

**返回**:
- FractalOperator实例。

#### 方法

##### apply

```python
apply(input_data)
```

应用分形算子。

**参数**:
- `input_data` (dict): 输入数据。

**返回**:
- dict: 应用算子后的结果。

##### generate_fractal

```python
generate_fractal(width, height, x_min, x_max, y_min, y_max)
```

生成分形图像。

**参数**:
- `width` (int): 图像宽度。
- `height` (int): 图像高度。
- `x_min` (float): X轴最小值。
- `x_max` (float): X轴最大值。
- `y_min` (float): Y轴最小值。
- `y_max` (float): Y轴最大值。

**返回**:
- numpy.ndarray: 分形图像数据。

##### compute_dimension

```python
compute_dimension(data)
```

计算数据的分形维度。

**参数**:
- `data` (numpy.ndarray): 输入数据。

**返回**:
- float: 分形维度。

### CategoryOperator

`CategoryOperator`是范畴算子类，用于处理抽象关系和映射。

#### 构造函数

```python
CategoryOperator(name=None, **kwargs)
```

**参数**:
- `name` (str, optional): 算子名称。
- `**kwargs`: 其他参数。

**返回**:
- CategoryOperator实例。

#### 方法

##### apply

```python
apply(input_data)
```

应用范畴算子。

**参数**:
- `input_data` (dict): 输入数据。

**返回**:
- dict: 应用算子后的结果。

##### compose

```python
compose(f, g)
```

组合两个映射。

**参数**:
- `f` (callable): 第一个映射。
- `g` (callable): 第二个映射。

**返回**:
- callable: 组合后的映射。

##### create_functor

```python
create_functor(source_category, target_category, object_map, morphism_map)
```

创建函子。

**参数**:
- `source_category` (dict): 源范畴。
- `target_category` (dict): 目标范畴。
- `object_map` (dict): 对象映射。
- `morphism_map` (dict): 态射映射。

**返回**:
- dict: 函子。

### MorphologyOperator

`MorphologyOperator`是形态学算子类，用于处理结构和形态变换。

#### 构造函数

```python
MorphologyOperator(kernel_size=3, name=None, **kwargs)
```

**参数**:
- `kernel_size` (int): 核大小。
- `name` (str, optional): 算子名称。
- `**kwargs`: 其他参数。

**返回**:
- MorphologyOperator实例。

#### 方法

##### apply

```python
apply(input_data)
```

应用形态学算子。

**参数**:
- `input_data` (dict): 输入数据。

**返回**:
- dict: 应用算子后的结果。

##### dilate

```python
dilate(data, kernel=None)
```

膨胀操作。

**参数**:
- `data` (numpy.ndarray): 输入数据。
- `kernel` (numpy.ndarray, optional): 核。

**返回**:
- numpy.ndarray: 膨胀后的数据。

##### erode

```python
erode(data, kernel=None)
```

腐蚀操作。

**参数**:
- `data` (numpy.ndarray): 输入数据。
- `kernel` (numpy.ndarray, optional): 核。

**返回**:
- numpy.ndarray: 腐蚀后的数据。

##### open

```python
open(data, kernel=None)
```

开操作。

**参数**:
- `data` (numpy.ndarray): 输入数据。
- `kernel` (numpy.ndarray, optional): 核。

**返回**:
- numpy.ndarray: 开操作后的数据。

##### close

```python
close(data, kernel=None)
```

闭操作。

**参数**:
- `data` (numpy.ndarray): 输入数据。
- `kernel` (numpy.ndarray, optional): 核。

**返回**:
- numpy.ndarray: 闭操作后的数据。

### QuantumOperator

`QuantumOperator`是量子算子类，用于模拟量子计算过程。

#### 构造函数

```python
QuantumOperator(num_qubits=2, name=None, **kwargs)
```

**参数**:
- `num_qubits` (int): 量子比特数。
- `name` (str, optional): 算子名称。
- `**kwargs`: 其他参数。

**返回**:
- QuantumOperator实例。

#### 方法

##### apply

```python
apply(input_data)
```

应用量子算子。

**参数**:
- `input_data` (dict): 输入数据。

**返回**:
- dict: 应用算子后的结果。

##### apply_gate

```python
apply_gate(state, gate, target_qubit, control_qubit=None)
```

应用量子门。

**参数**:
- `state` (numpy.ndarray): 量子态。
- `gate` (numpy.ndarray): 量子门。
- `target_qubit` (int): 目标量子比特。
- `control_qubit` (int, optional): 控制量子比特。

**返回**:
- numpy.ndarray: 应用量子门后的量子态。

##### measure

```python
measure(state, qubit)
```

测量量子比特。

**参数**:
- `state` (numpy.ndarray): 量子态。
- `qubit` (int): 要测量的量子比特。

**返回**:
- tuple: (测量结果, 测量后的量子态)。

## 算法 (Algorithms)

### ChaosControl

`ChaosControl`是混沌控制算法类，用于控制和利用混沌系统的行为。

#### 构造函数

```python
ChaosControl(iterations=100, control_parameter=3.8, name=None, **kwargs)
```

**参数**:
- `iterations` (int): 迭代次数。
- `control_parameter` (float): 控制参数。
- `name` (str, optional): 算法名称。
- `**kwargs`: 其他参数。

**返回**:
- ChaosControl实例。

#### 方法

##### run

```python
run(input_data)
```

运行混沌控制算法。

**参数**:
- `input_data` (dict): 输入数据。

**返回**:
- dict: 运行算法后的结果。

##### logistic_map

```python
logistic_map(x, r)
```

计算Logistic映射。

**参数**:
- `x` (float): 当前值。
- `r` (float): 控制参数。

**返回**:
- float: 下一个值。

##### control_chaos

```python
control_chaos(initial_state, target_state, tolerance=0.001)
```

控制混沌系统。

**参数**:
- `initial_state` (float): 初始状态。
- `target_state` (float): 目标状态。
- `tolerance` (float): 容差。

**返回**:
- dict: 控制结果。

### FractalRouting

`FractalRouting`是分形路由算法类，基于分形结构的信息路由。

#### 构造函数

```python
FractalRouting(depth=5, branching_factor=2, name=None, **kwargs)
```

**参数**:
- `depth` (int): 路由深度。
- `branching_factor` (int): 分支因子。
- `name` (str, optional): 算法名称。
- `**kwargs`: 其他参数。

**返回**:
- FractalRouting实例。

#### 方法

##### run

```python
run(input_data)
```

运行分形路由算法。

**参数**:
- `input_data` (dict): 输入数据。

**返回**:
- dict: 运行算法后的结果。

##### build_fractal_network

```python
build_fractal_network(depth, branching_factor)
```

构建分形网络。

**参数**:
- `depth` (int): 网络深度。
- `branching_factor` (int): 分支因子。

**返回**:
- dict: 分形网络。

##### route

```python
route(network, source, destination)
```

在分形网络中路由。

**参数**:
- `network` (dict): 分形网络。
- `source` (str): 源节点。
- `destination` (str): 目标节点。

**返回**:
- list: 路由路径。

### EmergentComputation

`EmergentComputation`是涌现计算算法类，利用涌现现象进行计算。

#### 构造函数

```python
EmergentComputation(agents=10, iterations=100, name=None, **kwargs)
```

**参数**:
- `agents` (int): 智能体数量。
- `iterations` (int): 迭代次数。
- `name` (str, optional): 算法名称。
- `**kwargs`: 其他参数。

**返回**:
- EmergentComputation实例。

#### 方法

##### run

```python
run(input_data)
```

运行涌现计算算法。

**参数**:
- `input_data` (dict): 输入数据。

**返回**:
- dict: 运行算法后的结果。

##### initialize_agents

```python
initialize_agents(num_agents, state_size)
```

初始化智能体。

**参数**:
- `num_agents` (int): 智能体数量。
- `state_size` (int): 状态大小。

**返回**:
- list: 智能体列表。

##### update_agents

```python
update_agents(agents, rules)
```

更新智能体。

**参数**:
- `agents` (list): 智能体列表。
- `rules` (dict): 更新规则。

**返回**:
- list: 更新后的智能体列表。

##### detect_patterns

```python
detect_patterns(agents)
```

检测智能体中的模式。

**参数**:
- `agents` (list): 智能体列表。

**返回**:
- list: 检测到的模式列表。

### SelfOrganizingMap

`SelfOrganizingMap`是自组织映射算法类，自适应组织和映射信息。

#### 构造函数

```python
SelfOrganizingMap(map_size=(10, 10), learning_rate=0.1, sigma=1.0, name=None, **kwargs)
```

**参数**:
- `map_size` (tuple): 映射大小。
- `learning_rate` (float): 学习率。
- `sigma` (float): 邻域半径。
- `name` (str, optional): 算法名称。
- `**kwargs`: 其他参数。

**返回**:
- SelfOrganizingMap实例。

#### 方法

##### run

```python
run(input_data)
```

运行自组织映射算法。

**参数**:
- `input_data` (dict): 输入数据。

**返回**:
- dict: 运行算法后的结果。

##### initialize_map

```python
initialize_map(map_size, input_dim)
```

初始化映射。

**参数**:
- `map_size` (tuple): 映射大小。
- `input_dim` (int): 输入维度。

**返回**:
- numpy.ndarray: 初始化的映射。

##### find_bmu

```python
find_bmu(som, x)
```

查找最佳匹配单元。

**参数**:
- `som` (numpy.ndarray): 自组织映射。
- `x` (numpy.ndarray): 输入向量。

**返回**:
- tuple: 最佳匹配单元的坐标。

##### update_weights

```python
update_weights(som, x, bmu, iteration, max_iterations)
```

更新权重。

**参数**:
- `som` (numpy.ndarray): 自组织映射。
- `x` (numpy.ndarray): 输入向量。
- `bmu` (tuple): 最佳匹配单元的坐标。
- `iteration` (int): 当前迭代次数。
- `max_iterations` (int): 最大迭代次数。

**返回**:
- numpy.ndarray: 更新后的自组织映射。
