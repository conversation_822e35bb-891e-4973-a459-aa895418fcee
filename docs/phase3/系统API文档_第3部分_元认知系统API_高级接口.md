# 超融态思维引擎系统API文档 - 第3部分：元认知系统API（高级接口）

## 4. 元认知映射接口

元认知映射接口提供了创建和管理元认知映射的功能，使用户能够将认知状态映射为元认知状态，实现对认知过程的认知和理解。

### 4.1 创建元认知映射

#### 4.1.1 `create_meta_cognitive_mapping`

创建一个新的元认知映射对象。

**函数签名**：
```python
def create_meta_cognitive_mapping(mapping_type: str, strategy: MappingStrategy = None, 
                                 parameters: Dict[str, Any] = None) -> MetaCognitiveMapping:
```

**参数**：
- `mapping_type` (str): 映射类型，如"direct"、"hierarchical"、"contextual"等。
- `strategy` (MappingStrategy, 可选): 映射策略对象，如果为None则根据映射类型自动创建。默认为None。
- `parameters` (Dict[str, Any], 可选): 映射的参数，用于配置映射的行为。默认为None。

**返回值**：
- `MetaCognitiveMapping`: 创建的元认知映射对象。

**异常**：
- `ValueError`: 如果参数无效，如无效的映射类型等。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from metacognition import create_meta_cognitive_mapping

# 创建直接映射
direct_mapping = create_meta_cognitive_mapping("direct")

# 创建层次映射
hierarchical_mapping = create_meta_cognitive_mapping("hierarchical", parameters={"levels": 3})

# 创建上下文映射
contextual_mapping = create_meta_cognitive_mapping("contextual", parameters={"context_weight": 0.5})
```

#### 4.1.2 `create_mapping_strategy`

创建一个新的映射策略对象。

**函数签名**：
```python
def create_mapping_strategy(strategy_type: str, parameters: Dict[str, Any] = None) -> MappingStrategy:
```

**参数**：
- `strategy_type` (str): 策略类型，如"direct"、"hierarchical"、"contextual"等。
- `parameters` (Dict[str, Any], 可选): 策略的参数，用于配置策略的行为。默认为None。

**返回值**：
- `MappingStrategy`: 创建的映射策略对象。

**异常**：
- `ValueError`: 如果参数无效，如无效的策略类型等。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from metacognition import create_mapping_strategy

# 创建直接映射策略
direct_strategy = create_mapping_strategy("direct")

# 创建层次映射策略
hierarchical_strategy = create_mapping_strategy("hierarchical", {"levels": 3})

# 创建上下文映射策略
contextual_strategy = create_mapping_strategy("contextual", {"context_weight": 0.5})
```

### 4.2 元认知映射操作

#### 4.2.1 `apply_meta_cognitive_mapping`

应用元认知映射，将认知状态映射为元认知状态。

**函数签名**：
```python
def apply_meta_cognitive_mapping(mapping: MetaCognitiveMapping, 
                                cognitive_state: CognitiveState, 
                                context: Dict[str, Any] = None) -> MetaCognitiveState:
```

**参数**：
- `mapping` (MetaCognitiveMapping): 要应用的元认知映射对象。
- `cognitive_state` (CognitiveState): 要映射的认知状态对象。
- `context` (Dict[str, Any], 可选): 映射的上下文信息，用于提供额外的映射信息。默认为None。

**返回值**：
- `MetaCognitiveState`: 映射后的元认知状态对象。

**异常**：
- `ValueError`: 如果元认知映射对象无效或认知状态对象无效。
- `TypeError`: 如果参数类型不正确。
- `RuntimeError`: 如果应用映射过程中发生错误。

**示例**：
```python
from metacognition import apply_meta_cognitive_mapping, create_cognitive_state

# 创建认知状态
cognitive_state = create_cognitive_state("perception", {
    "intensity": 0.8,
    "clarity": 0.7,
    "modality": "visual",
    "content": "object"
})

# 应用直接映射
meta_cognitive_state = apply_meta_cognitive_mapping(direct_mapping, cognitive_state)
print(get_meta_cognitive_state_info(meta_cognitive_state))
# 输出:
# {
#     "id": "...",
#     "state_type": "awareness",
#     "parameters": {
#         "confidence": 0.8,
#         "awareness_level": 0.7,
#         "source_state_type": "perception",
#         "source_timestamp": "..."
#     },
#     "created_at": "..."
# }

# 应用上下文映射
context = {"environment": "laboratory", "task": "object_recognition"}
meta_cognitive_state = apply_meta_cognitive_mapping(contextual_mapping, cognitive_state, context)
print(get_meta_cognitive_state_info(meta_cognitive_state))
# 输出:
# {
#     "id": "...",
#     "state_type": "awareness",
#     "parameters": {
#         "confidence": 0.85,  # 考虑上下文后的调整值
#         "awareness_level": 0.75,  # 考虑上下文后的调整值
#         "source_state_type": "perception",
#         "source_timestamp": "...",
#         "context": {"environment": "laboratory", "task": "object_recognition"}
#     },
#     "created_at": "..."
# }
```

#### 4.2.2 `get_mapping_info`

获取元认知映射的信息，包括映射类型、映射策略、参数等。

**函数签名**：
```python
def get_mapping_info(mapping: MetaCognitiveMapping) -> Dict[str, Any]:
```

**参数**：
- `mapping` (MetaCognitiveMapping): 要获取信息的元认知映射对象。

**返回值**：
- `Dict[str, Any]`: 元认知映射的信息，包括映射类型、映射策略、参数等。

**异常**：
- `ValueError`: 如果元认知映射对象无效。

**示例**：
```python
from metacognition import get_mapping_info

# 获取映射信息
info = get_mapping_info(direct_mapping)
print(info)
# 输出:
# {
#     "id": "...",
#     "mapping_type": "direct",
#     "strategy": {
#         "type": "direct",
#         "parameters": {}
#     },
#     "parameters": {},
#     "created_at": "..."
# }
```

#### 4.2.3 `update_mapping`

更新元认知映射的参数或策略。

**函数签名**：
```python
def update_mapping(mapping: MetaCognitiveMapping, strategy: MappingStrategy = None, 
                  parameters: Dict[str, Any] = None) -> MetaCognitiveMapping:
```

**参数**：
- `mapping` (MetaCognitiveMapping): 要更新的元认知映射对象。
- `strategy` (MappingStrategy, 可选): 新的映射策略，如果为None则保持原有策略。默认为None。
- `parameters` (Dict[str, Any], 可选): 新的参数，如果为None则保持原有参数。默认为None。

**返回值**：
- `MetaCognitiveMapping`: 更新后的元认知映射对象。

**异常**：
- `ValueError`: 如果元认知映射对象无效或参数无效。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from metacognition import update_mapping

# 更新映射参数
updated_mapping = update_mapping(contextual_mapping, parameters={"context_weight": 0.7})

# 获取更新后的映射信息
info = get_mapping_info(updated_mapping)
print(info["parameters"])  # 输出: {"context_weight": 0.7}
```

## 5. 元认知学习接口

元认知学习接口提供了创建和管理元认知学习的功能，使用户能够通过学习改进元认知能力，提高元认知的效果和效率。

### 5.1 创建元认知学习

#### 5.1.1 `create_meta_cognitive_learning`

创建一个新的元认知学习对象。

**函数签名**：
```python
def create_meta_cognitive_learning(learning_type: str, model: LearningModel = None, 
                                  strategy: LearningStrategy = None, 
                                  parameters: Dict[str, Any] = None) -> MetaCognitiveLearning:
```

**参数**：
- `learning_type` (str): 学习类型，如"supervised"、"reinforcement"、"transfer"等。
- `model` (LearningModel, 可选): 学习模型对象，如果为None则根据学习类型自动创建。默认为None。
- `strategy` (LearningStrategy, 可选): 学习策略对象，如果为None则根据学习类型自动创建。默认为None。
- `parameters` (Dict[str, Any], 可选): 学习的参数，用于配置学习的行为。默认为None。

**返回值**：
- `MetaCognitiveLearning`: 创建的元认知学习对象。

**异常**：
- `ValueError`: 如果参数无效，如无效的学习类型等。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from metacognition import create_meta_cognitive_learning

# 创建监督学习
supervised_learning = create_meta_cognitive_learning("supervised")

# 创建强化学习
reinforcement_learning = create_meta_cognitive_learning("reinforcement", parameters={"learning_rate": 0.01})

# 创建迁移学习
transfer_learning = create_meta_cognitive_learning("transfer", parameters={"source_domain": "vision", "target_domain": "language"})
```

#### 5.1.2 `create_learning_model`

创建一个新的学习模型对象。

**函数签名**：
```python
def create_learning_model(model_type: str, parameters: Dict[str, Any] = None) -> LearningModel:
```

**参数**：
- `model_type` (str): 模型类型，如"linear"、"decision_tree"、"neural_network"等。
- `parameters` (Dict[str, Any], 可选): 模型的参数，用于配置模型的行为。默认为None。

**返回值**：
- `LearningModel`: 创建的学习模型对象。

**异常**：
- `ValueError`: 如果参数无效，如无效的模型类型等。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from metacognition import create_learning_model

# 创建线性模型
linear_model = create_learning_model("linear")

# 创建决策树模型
decision_tree_model = create_learning_model("decision_tree", {"max_depth": 5})

# 创建神经网络模型
neural_network_model = create_learning_model("neural_network", {"hidden_layers": [10, 5]})
```

#### 5.1.3 `create_learning_strategy`

创建一个新的学习策略对象。

**函数签名**：
```python
def create_learning_strategy(strategy_type: str, parameters: Dict[str, Any] = None) -> LearningStrategy:
```

**参数**：
- `strategy_type` (str): 策略类型，如"supervised"、"reinforcement"、"transfer"等。
- `parameters` (Dict[str, Any], 可选): 策略的参数，用于配置策略的行为。默认为None。

**返回值**：
- `LearningStrategy`: 创建的学习策略对象。

**异常**：
- `ValueError`: 如果参数无效，如无效的策略类型等。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from metacognition import create_learning_strategy

# 创建监督学习策略
supervised_strategy = create_learning_strategy("supervised")

# 创建强化学习策略
reinforcement_strategy = create_learning_strategy("reinforcement", {"learning_rate": 0.01})

# 创建迁移学习策略
transfer_strategy = create_learning_strategy("transfer", {"source_domain": "vision", "target_domain": "language"})
```

### 5.2 元认知学习操作

#### 5.2.1 `train_meta_cognitive_learning`

训练元认知学习模型。

**函数签名**：
```python
def train_meta_cognitive_learning(learning: MetaCognitiveLearning, 
                                 training_data: List[Dict[str, Any]], 
                                 validation_data: List[Dict[str, Any]] = None, 
                                 parameters: Dict[str, Any] = None) -> Dict[str, Any]:
```

**参数**：
- `learning` (MetaCognitiveLearning): 要训练的元认知学习对象。
- `training_data` (List[Dict[str, Any]]): 训练数据，每个元素包含输入数据和目标数据。
- `validation_data` (List[Dict[str, Any]], 可选): 验证数据，用于评估训练效果。默认为None。
- `parameters` (Dict[str, Any], 可选): 训练的参数，用于配置训练过程。默认为None。

**返回值**：
- `Dict[str, Any]`: 训练结果，包括训练损失、验证损失、训练时间等。

**异常**：
- `ValueError`: 如果元认知学习对象无效或数据无效。
- `TypeError`: 如果参数类型不正确。
- `RuntimeError`: 如果训练过程中发生错误。

**示例**：
```python
from metacognition import train_meta_cognitive_learning

# 准备训练数据
training_data = [
    {"input": {"intensity": 0.8, "clarity": 0.7}, "target": {"confidence": 0.8, "awareness_level": 0.7}},
    {"input": {"intensity": 0.9, "clarity": 0.8}, "target": {"confidence": 0.9, "awareness_level": 0.8}},
    {"input": {"intensity": 0.7, "clarity": 0.6}, "target": {"confidence": 0.7, "awareness_level": 0.6}}
]

# 准备验证数据
validation_data = [
    {"input": {"intensity": 0.85, "clarity": 0.75}, "target": {"confidence": 0.85, "awareness_level": 0.75}}
]

# 训练监督学习模型
result = train_meta_cognitive_learning(supervised_learning, training_data, validation_data, {"epochs": 100})
print(result)
# 输出:
# {
#     "training_loss": 0.01,
#     "validation_loss": 0.02,
#     "training_time": 1.5,
#     "epochs": 100
# }
```

#### 5.2.2 `apply_meta_cognitive_learning`

应用元认知学习，使用训练好的模型进行预测。

**函数签名**：
```python
def apply_meta_cognitive_learning(learning: MetaCognitiveLearning, 
                                 input_data: Dict[str, Any], 
                                 parameters: Dict[str, Any] = None) -> Dict[str, Any]:
```

**参数**：
- `learning` (MetaCognitiveLearning): 要应用的元认知学习对象。
- `input_data` (Dict[str, Any]): 输入数据，用于预测。
- `parameters` (Dict[str, Any], 可选): 应用的参数，用于配置应用过程。默认为None。

**返回值**：
- `Dict[str, Any]`: 预测结果，包括预测值和置信度等。

**异常**：
- `ValueError`: 如果元认知学习对象无效或输入数据无效。
- `TypeError`: 如果参数类型不正确。
- `RuntimeError`: 如果应用过程中发生错误。

**示例**：
```python
from metacognition import apply_meta_cognitive_learning

# 应用监督学习模型
input_data = {"intensity": 0.85, "clarity": 0.75}
result = apply_meta_cognitive_learning(supervised_learning, input_data)
print(result)
# 输出:
# {
#     "prediction": {"confidence": 0.85, "awareness_level": 0.75},
#     "confidence": 0.9
# }
```

#### 5.2.3 `get_learning_info`

获取元认知学习的信息，包括学习类型、学习模型、学习策略、参数等。

**函数签名**：
```python
def get_learning_info(learning: MetaCognitiveLearning) -> Dict[str, Any]:
```

**参数**：
- `learning` (MetaCognitiveLearning): 要获取信息的元认知学习对象。

**返回值**：
- `Dict[str, Any]`: 元认知学习的信息，包括学习类型、学习模型、学习策略、参数等。

**异常**：
- `ValueError`: 如果元认知学习对象无效。

**示例**：
```python
from metacognition import get_learning_info

# 获取学习信息
info = get_learning_info(supervised_learning)
print(info)
# 输出:
# {
#     "id": "...",
#     "learning_type": "supervised",
#     "model": {
#         "type": "linear",
#         "parameters": {}
#     },
#     "strategy": {
#         "type": "supervised",
#         "parameters": {}
#     },
#     "parameters": {},
#     "created_at": "..."
# }
```

#### 5.2.4 `update_learning`

更新元认知学习的参数、模型或策略。

**函数签名**：
```python
def update_learning(learning: MetaCognitiveLearning, model: LearningModel = None, 
                   strategy: LearningStrategy = None, 
                   parameters: Dict[str, Any] = None) -> MetaCognitiveLearning:
```

**参数**：
- `learning` (MetaCognitiveLearning): 要更新的元认知学习对象。
- `model` (LearningModel, 可选): 新的学习模型，如果为None则保持原有模型。默认为None。
- `strategy` (LearningStrategy, 可选): 新的学习策略，如果为None则保持原有策略。默认为None。
- `parameters` (Dict[str, Any], 可选): 新的参数，如果为None则保持原有参数。默认为None。

**返回值**：
- `MetaCognitiveLearning`: 更新后的元认知学习对象。

**异常**：
- `ValueError`: 如果元认知学习对象无效或参数无效。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from metacognition import update_learning

# 创建新的学习模型
new_model = create_learning_model("decision_tree", {"max_depth": 10})

# 更新学习模型
updated_learning = update_learning(supervised_learning, model=new_model)

# 获取更新后的学习信息
info = get_learning_info(updated_learning)
print(info["model"])  # 输出: {"type": "decision_tree", "parameters": {"max_depth": 10}}
```
