## 4. 非线性干涉优化算法接口

### 4.1 适配器定义

```python
class NonlinearInterferenceOptimizerAdapter(AlgorithmAdapter):
    """非线性干涉优化算法适配器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化适配器
        
        Args:
            config: 配置参数
        """
        self.optimizer = None
        self.config = config or {}
        
    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化适配器
        
        Args:
            config: 配置参数
            
        Returns:
            初始化是否成功
        """
        try:
            merged_config = {**self.config, **config}
            
            # 创建优化器实例
            from src.algorithms.optimization.interference_optimized import OptimizedNonlinearInterferenceOptimizer
            
            self.optimizer = OptimizedNonlinearInterferenceOptimizer(
                dimensions=merged_config.get('dimensions', 2),
                population_size=merged_config.get('population_size', 100),
                max_iterations=merged_config.get('max_iterations', 100),
                interference_factor=merged_config.get('interference_factor', 0.5),
                decay_rate=merged_config.get('decay_rate', 0.02),
                convergence_threshold=merged_config.get('convergence_threshold', 1e-6),
                use_parallel=merged_config.get('use_parallel', False),
                num_workers=merged_config.get('num_workers', 4),
                use_cache=merged_config.get('use_cache', True),
                cache_size=merged_config.get('cache_size', 1000),
                use_adaptive=merged_config.get('use_adaptive', False)
            )
            
            return True
        except Exception as e:
            logging.error(f"Failed to initialize NonlinearInterferenceOptimizerAdapter: {str(e)}")
            return False
    
    def compute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行优化计算
        
        Args:
            input_data: 输入数据，包含以下字段：
                - operation_type: 操作类型，如'optimize', 'multi_objective_optimize'
                - parameters: 操作参数，包含：
                    - objective_function: 目标函数或函数名
                    - bounds: 搜索空间边界
                    - constraints: 约束函数列表（可选）
                    - base_result: 基础结果，用于增量计算（可选）
                - context: 上下文信息
                - metadata: 元数据
            
        Returns:
            计算结果，包含以下字段：
                - status: 状态码
                - result: 优化结果，包含：
                    - best_solution: 最优解
                    - best_fitness: 最优适应度值
                    - convergence_curve: 收敛曲线
                    - iterations: 迭代次数
                    - convergence_achieved: 是否达到收敛
                - metrics: 性能指标
                - metadata: 元数据
        """
        if not self.optimizer:
            return {
                'status': 3,
                'result': None,
                'metrics': {},
                'metadata': {'error': 'Optimizer not initialized'}
            }
        
        try:
            operation_type = input_data.get('operation_type', 'optimize')
            parameters = input_data.get('parameters', {})
            context = input_data.get('context', {})
            
            # 获取目标函数
            objective_function = parameters.get('objective_function')
            if isinstance(objective_function, str):
                # 如果是函数名，从上下文中获取函数
                objective_function = self._get_function_from_context(objective_function, context)
            
            # 准备优化参数
            compute_params = {
                'objective_function': objective_function,
                'bounds': parameters.get('bounds'),
            }
            
            # 添加可选参数
            if 'constraints' in parameters:
                compute_params['constraints'] = parameters['constraints']
            
            if 'base_result' in parameters:
                compute_params['base_result'] = parameters['base_result']
            
            # 执行优化
            start_time = time.time()
            result = self.optimizer.compute(compute_params)
            end_time = time.time()
            
            # 准备返回结果
            return {
                'status': 0,
                'result': result,
                'metrics': {
                    'computation_time': end_time - start_time,
                    'iterations': result.get('iterations', 0),
                    'convergence_achieved': result.get('convergence_achieved', False)
                },
                'metadata': {
                    'algorithm': 'NonlinearInterferenceOptimizer',
                    'parameters': {k: v for k, v in parameters.items() if k != 'objective_function'}
                }
            }
        except Exception as e:
            logging.error(f"Error in NonlinearInterferenceOptimizerAdapter.compute: {str(e)}")
            return {
                'status': 1,
                'result': None,
                'metrics': {},
                'metadata': {'error': str(e)}
            }
    
    def get_capabilities(self) -> Dict[str, Any]:
        """获取算法能力描述
        
        Returns:
            算法能力描述
        """
        return {
            'algorithm_type': 'optimization',
            'operations': ['optimize', 'multi_objective_optimize'],
            'supports_constraints': True,
            'supports_multi_objective': True,
            'supports_incremental': True,
            'supports_parallel': True,
            'max_dimensions': 1000,
            'performance_profile': {
                'time_complexity': 'O(n * p * i)',  # n: dimensions, p: population, i: iterations
                'space_complexity': 'O(n * p)',
                'parallelization': 'population-level'
            }
        }
    
    def shutdown(self) -> bool:
        """关闭适配器
        
        Returns:
            关闭是否成功
        """
        try:
            self.optimizer = None
            return True
        except Exception as e:
            logging.error(f"Error in NonlinearInterferenceOptimizerAdapter.shutdown: {str(e)}")
            return False
    
    def _get_function_from_context(self, function_name: str, context: Dict[str, Any]) -> Callable:
        """从上下文中获取函数
        
        Args:
            function_name: 函数名
            context: 上下文信息
            
        Returns:
            函数对象
            
        Raises:
            ValueError: 如果函数不存在
        """
        if 'functions' not in context or function_name not in context['functions']:
            raise ValueError(f"Function '{function_name}' not found in context")
        
        return context['functions'][function_name]
```

### 4.2 与ThoughtEngine的集成

#### 4.2.1 思维优化场景

在ThoughtEngine中，非线性干涉优化算法主要用于以下思维场景：

1. **参数优化**：优化思维过程中的关键参数
2. **多目标决策**：在多个目标之间寻找平衡
3. **约束满足问题**：在满足约束的条件下寻找最优解
4. **资源分配优化**：优化思维资源的分配

#### 4.2.2 集成示例

```python
# 在ThoughtEngine中使用非线性干涉优化算法
from thought_engine import ThoughtEngine
from thought_engine.adapters import NonlinearInterferenceOptimizerAdapter

# 创建思维引擎
engine = ThoughtEngine()

# 注册优化算法适配器
optimizer_adapter = NonlinearInterferenceOptimizerAdapter()
engine.register_algorithm_adapter('optimization', optimizer_adapter)

# 在思维过程中使用优化算法
def thought_process():
    # 定义优化问题
    def objective(x):
        return x[0]**2 + x[1]**2
    
    bounds = [[-10, 10], [-10, 10]]
    
    # 调用优化算法
    result = engine.invoke_algorithm(
        algorithm_type='optimization',
        operation_type='optimize',
        parameters={
            'objective_function': objective,
            'bounds': bounds
        }
    )
    
    # 使用优化结果
    optimal_solution = result['result']['best_solution']
    optimal_value = result['result']['best_fitness']
    
    # 继续思维过程
    # ...

# 执行思维过程
engine.execute(thought_process)
```

### 4.3 数据转换

#### 4.3.1 ThoughtEngine到算法库

将ThoughtEngine的数据格式转换为算法库期望的格式：

```python
def convert_to_algorithm_format(thought_data: Dict[str, Any]) -> Dict[str, Any]:
    """将ThoughtEngine数据转换为算法库格式
    
    Args:
        thought_data: ThoughtEngine数据
        
    Returns:
        算法库格式数据
    """
    # 提取相关数据
    objective = thought_data.get('objective')
    constraints = thought_data.get('constraints', [])
    bounds = thought_data.get('bounds')
    
    # 转换为算法库格式
    algorithm_data = {
        'objective_function': objective,
        'bounds': bounds
    }
    
    if constraints:
        algorithm_data['constraints'] = constraints
    
    return algorithm_data
```

#### 4.3.2 算法库到ThoughtEngine

将算法库的结果转换为ThoughtEngine期望的格式：

```python
def convert_to_thought_format(algorithm_result: Dict[str, Any]) -> Dict[str, Any]:
    """将算法库结果转换为ThoughtEngine格式
    
    Args:
        algorithm_result: 算法库结果
        
    Returns:
        ThoughtEngine格式数据
    """
    # 提取相关数据
    best_solution = algorithm_result.get('best_solution')
    best_fitness = algorithm_result.get('best_fitness')
    convergence_curve = algorithm_result.get('convergence_curve')
    
    # 转换为ThoughtEngine格式
    thought_data = {
        'solution': best_solution,
        'value': best_fitness,
        'convergence': convergence_curve,
        'quality': {
            'confidence': calculate_confidence(convergence_curve),
            'stability': calculate_stability(convergence_curve)
        }
    }
    
    return thought_data
```
