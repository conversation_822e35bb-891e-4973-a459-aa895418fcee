# 超融态思维引擎系统API文档 - 第2部分：态射系统API（基础接口）

## 1. 态射系统API概述

态射系统API提供了访问和使用态射系统功能的接口，包括态射创建、态射执行、态射组合、态射反馈和态射演化等。这些API使用户能够构建和操作态射，实现复杂的变换和映射功能。

## 2. 态射基础接口

### 2.1 创建态射

#### 2.1.1 `create_morphism`

创建一个新的态射对象。

**函数签名**：
```python
def create_morphism(morphism_type: str, domain: MorphismDomain, codomain: MorphismCodomain, 
                   mapping_function: Callable, parameters: Dict[str, Any] = None) -> DynamicMorphism:
```

**参数**：
- `morphism_type` (str): 态射类型，如"identity"、"linear"、"nonlinear"等。
- `domain` (MorphismDomain): 态射的定义域，表示态射的输入空间。
- `codomain` (MorphismCodomain): 态射的值域，表示态射的输出空间。
- `mapping_function` (Callable): 态射的映射函数，定义了从定义域到值域的映射关系。
- `parameters` (Dict[str, Any], 可选): 态射的参数，用于配置态射的行为。默认为None。

**返回值**：
- `DynamicMorphism`: 创建的态射对象。

**异常**：
- `ValueError`: 如果参数无效，如无效的态射类型、无效的定义域或值域等。
- `TypeError`: 如果参数类型不正确，如映射函数不可调用等。

**示例**：
```python
from morphism import MorphismDomain, MorphismCodomain, create_morphism

# 创建定义域和值域
domain = MorphismDomain("input_domain", 3)  # 3维输入空间
codomain = MorphismCodomain("output_domain", 3)  # 3维输出空间

# 定义映射函数
def double_mapping(x):
    return x * 2

# 创建态射
morphism = create_morphism("linear", domain, codomain, double_mapping, {"name": "double_morphism"})
```

#### 2.1.2 `create_domain`

创建一个新的态射定义域对象。

**函数签名**：
```python
def create_domain(name: str, dimension: int, parameters: Dict[str, Any] = None) -> MorphismDomain:
```

**参数**：
- `name` (str): 定义域的名称。
- `dimension` (int): 定义域的维度，表示输入数据的维度。
- `parameters` (Dict[str, Any], 可选): 定义域的参数，用于配置定义域的行为。默认为None。

**返回值**：
- `MorphismDomain`: 创建的定义域对象。

**异常**：
- `ValueError`: 如果参数无效，如负的维度等。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from morphism import create_domain

# 创建定义域
domain = create_domain("input_domain", 3, {"data_type": "float"})
```

#### 2.1.3 `create_codomain`

创建一个新的态射值域对象。

**函数签名**：
```python
def create_codomain(name: str, dimension: int, parameters: Dict[str, Any] = None) -> MorphismCodomain:
```

**参数**：
- `name` (str): 值域的名称。
- `dimension` (int): 值域的维度，表示输出数据的维度。
- `parameters` (Dict[str, Any], 可选): 值域的参数，用于配置值域的行为。默认为None。

**返回值**：
- `MorphismCodomain`: 创建的值域对象。

**异常**：
- `ValueError`: 如果参数无效，如负的维度等。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from morphism import create_codomain

# 创建值域
codomain = create_codomain("output_domain", 3, {"data_type": "float"})
```

### 2.2 态射操作

#### 2.2.1 `apply_morphism`

应用态射，将输入数据映射为输出数据。

**函数签名**：
```python
def apply_morphism(morphism: DynamicMorphism, input_data: Any, parameters: Dict[str, Any] = None) -> Any:
```

**参数**：
- `morphism` (DynamicMorphism): 要应用的态射对象。
- `input_data` (Any): 输入数据，应该符合态射定义域的要求。
- `parameters` (Dict[str, Any], 可选): 应用态射的参数，用于配置应用过程。默认为None。

**返回值**：
- `Any`: 应用态射后的输出数据，符合态射值域的要求。

**异常**：
- `ValueError`: 如果输入数据不符合态射定义域的要求。
- `RuntimeError`: 如果应用态射过程中发生错误。

**示例**：
```python
from morphism import apply_morphism

# 应用态射
input_data = [1, 2, 3]
output_data = apply_morphism(morphism, input_data)
print(output_data)  # 输出: [2, 4, 6]
```

#### 2.2.2 `get_morphism_info`

获取态射的信息，包括态射类型、定义域、值域、参数等。

**函数签名**：
```python
def get_morphism_info(morphism: DynamicMorphism) -> Dict[str, Any]:
```

**参数**：
- `morphism` (DynamicMorphism): 要获取信息的态射对象。

**返回值**：
- `Dict[str, Any]`: 态射的信息，包括态射类型、定义域、值域、参数等。

**异常**：
- `ValueError`: 如果态射对象无效。

**示例**：
```python
from morphism import get_morphism_info

# 获取态射信息
info = get_morphism_info(morphism)
print(info)
# 输出:
# {
#     "morphism_type": "linear",
#     "domain": {
#         "name": "input_domain",
#         "dimension": 3,
#         "parameters": {"data_type": "float"}
#     },
#     "codomain": {
#         "name": "output_domain",
#         "dimension": 3,
#         "parameters": {"data_type": "float"}
#     },
#     "parameters": {"name": "double_morphism"}
# }
```

#### 2.2.3 `update_morphism`

更新态射的参数或映射函数。

**函数签名**：
```python
def update_morphism(morphism: DynamicMorphism, mapping_function: Callable = None, 
                   parameters: Dict[str, Any] = None) -> DynamicMorphism:
```

**参数**：
- `morphism` (DynamicMorphism): 要更新的态射对象。
- `mapping_function` (Callable, 可选): 新的映射函数，如果为None则保持原有映射函数。默认为None。
- `parameters` (Dict[str, Any], 可选): 新的参数，如果为None则保持原有参数。默认为None。

**返回值**：
- `DynamicMorphism`: 更新后的态射对象。

**异常**：
- `ValueError`: 如果态射对象无效。
- `TypeError`: 如果参数类型不正确，如映射函数不可调用等。

**示例**：
```python
from morphism import update_morphism

# 定义新的映射函数
def triple_mapping(x):
    return x * 3

# 更新态射
updated_morphism = update_morphism(morphism, triple_mapping, {"name": "triple_morphism"})

# 应用更新后的态射
input_data = [1, 2, 3]
output_data = apply_morphism(updated_morphism, input_data)
print(output_data)  # 输出: [3, 6, 9]
```

### 2.3 态射注册与查询

#### 2.3.1 `register_morphism`

将态射注册到系统中，使其可以被其他组件使用。

**函数签名**：
```python
def register_morphism(morphism: DynamicMorphism, name: str = None) -> str:
```

**参数**：
- `morphism` (DynamicMorphism): 要注册的态射对象。
- `name` (str, 可选): 态射的注册名称，如果为None则使用态射的ID。默认为None。

**返回值**：
- `str`: 态射的注册ID，可用于后续查询和使用态射。

**异常**：
- `ValueError`: 如果态射对象无效或名称已被使用。

**示例**：
```python
from morphism import register_morphism

# 注册态射
morphism_id = register_morphism(morphism, "double_morphism")
print(morphism_id)  # 输出: "double_morphism" 或自动生成的ID
```

#### 2.3.2 `get_morphism`

根据ID或名称获取已注册的态射。

**函数签名**：
```python
def get_morphism(morphism_id: str) -> DynamicMorphism:
```

**参数**：
- `morphism_id` (str): 态射的注册ID或名称。

**返回值**：
- `DynamicMorphism`: 获取的态射对象。

**异常**：
- `ValueError`: 如果找不到指定ID或名称的态射。

**示例**：
```python
from morphism import get_morphism

# 获取态射
morphism = get_morphism("double_morphism")

# 应用获取的态射
input_data = [1, 2, 3]
output_data = apply_morphism(morphism, input_data)
print(output_data)  # 输出: [2, 4, 6]
```

#### 2.3.3 `list_morphisms`

列出系统中已注册的所有态射。

**函数签名**：
```python
def list_morphisms(filter_criteria: Dict[str, Any] = None) -> List[Dict[str, Any]]:
```

**参数**：
- `filter_criteria` (Dict[str, Any], 可选): 过滤条件，用于筛选态射。默认为None，表示不过滤。

**返回值**：
- `List[Dict[str, Any]]`: 态射信息列表，每个元素包含态射的ID、名称、类型等信息。

**异常**：
- `ValueError`: 如果过滤条件无效。

**示例**：
```python
from morphism import list_morphisms

# 列出所有态射
morphisms = list_morphisms()
for morphism_info in morphisms:
    print(f"ID: {morphism_info['id']}, Name: {morphism_info['name']}, Type: {morphism_info['type']}")

# 列出特定类型的态射
linear_morphisms = list_morphisms({"type": "linear"})
for morphism_info in linear_morphisms:
    print(f"ID: {morphism_info['id']}, Name: {morphism_info['name']}")
```
