# 超融态思维引擎系统API文档 - 第4部分：分布式系统API（基础接口）

## 1. 分布式系统API概述

分布式系统API提供了访问和使用分布式系统功能的接口，包括节点管理、任务管理、消息管理和算法管理等。这些API使用户能够构建和操作分布式系统，实现高性能、高可靠性和高可扩展性的计算。

## 2. 节点管理接口

### 2.1 创建节点

#### 2.1.1 `create_node`

创建一个新的分布式节点。

**函数签名**：
```python
def create_node(node_type: str, address: str, parameters: Dict[str, Any] = None) -> DistributedNode:
```

**参数**：
- `node_type` (str): 节点类型，如"compute"、"storage"、"manager"等。
- `address` (str): 节点的网络地址，格式为"ip:port"。
- `parameters` (Dict[str, Any], 可选): 节点的参数，用于配置节点的行为。默认为None。

**返回值**：
- `DistributedNode`: 创建的分布式节点对象。

**异常**：
- `ValueError`: 如果参数无效，如无效的节点类型、无效的地址格式等。
- `TypeError`: 如果参数类型不正确。
- `RuntimeError`: 如果创建节点过程中发生错误，如网络错误、资源不足等。

**示例**：
```python
from distributed import create_node

# 创建计算节点
compute_node = create_node("compute", "*************:8000", {
    "cpu_cores": 4,
    "memory": "8GB",
    "gpu": True
})

# 创建存储节点
storage_node = create_node("storage", "*************:8000", {
    "capacity": "1TB",
    "read_speed": "500MB/s",
    "write_speed": "300MB/s"
})

# 创建管理节点
manager_node = create_node("manager", "*************:8000", {
    "max_nodes": 100,
    "heartbeat_interval": 5
})
```

#### 2.1.2 `get_node_info`

获取分布式节点的信息，包括节点类型、地址、状态、参数等。

**函数签名**：
```python
def get_node_info(node: DistributedNode) -> Dict[str, Any]:
```

**参数**：
- `node` (DistributedNode): 要获取信息的分布式节点对象。

**返回值**：
- `Dict[str, Any]`: 分布式节点的信息，包括节点类型、地址、状态、参数等。

**异常**：
- `ValueError`: 如果分布式节点对象无效。
- `RuntimeError`: 如果获取节点信息过程中发生错误，如网络错误等。

**示例**：
```python
from distributed import get_node_info

# 获取节点信息
info = get_node_info(compute_node)
print(info)
# 输出:
# {
#     "id": "...",
#     "node_type": "compute",
#     "address": "*************:8000",
#     "status": "running",
#     "parameters": {
#         "cpu_cores": 4,
#         "memory": "8GB",
#         "gpu": True
#     },
#     "created_at": "...",
#     "last_heartbeat": "..."
# }
```

### 2.2 节点操作

#### 2.2.1 `start_node`

启动分布式节点，使其开始接收和处理任务。

**函数签名**：
```python
def start_node(node: DistributedNode) -> bool:
```

**参数**：
- `node` (DistributedNode): 要启动的分布式节点对象。

**返回值**：
- `bool`: 启动是否成功，True表示成功，False表示失败。

**异常**：
- `ValueError`: 如果分布式节点对象无效。
- `RuntimeError`: 如果启动节点过程中发生错误，如网络错误、资源不足等。

**示例**：
```python
from distributed import start_node

# 启动节点
success = start_node(compute_node)
if success:
    print("Node started successfully")
else:
    print("Failed to start node")
```

#### 2.2.2 `stop_node`

停止分布式节点，使其停止接收和处理任务。

**函数签名**：
```python
def stop_node(node: DistributedNode) -> bool:
```

**参数**：
- `node` (DistributedNode): 要停止的分布式节点对象。

**返回值**：
- `bool`: 停止是否成功，True表示成功，False表示失败。

**异常**：
- `ValueError`: 如果分布式节点对象无效。
- `RuntimeError`: 如果停止节点过程中发生错误，如网络错误等。

**示例**：
```python
from distributed import stop_node

# 停止节点
success = stop_node(compute_node)
if success:
    print("Node stopped successfully")
else:
    print("Failed to stop node")
```

#### 2.2.3 `restart_node`

重启分布式节点，先停止再启动。

**函数签名**：
```python
def restart_node(node: DistributedNode) -> bool:
```

**参数**：
- `node` (DistributedNode): 要重启的分布式节点对象。

**返回值**：
- `bool`: 重启是否成功，True表示成功，False表示失败。

**异常**：
- `ValueError`: 如果分布式节点对象无效。
- `RuntimeError`: 如果重启节点过程中发生错误，如网络错误、资源不足等。

**示例**：
```python
from distributed import restart_node

# 重启节点
success = restart_node(compute_node)
if success:
    print("Node restarted successfully")
else:
    print("Failed to restart node")
```

### 2.3 节点管理

#### 2.3.1 `register_node`

将分布式节点注册到系统中，使其可以被其他组件使用。

**函数签名**：
```python
def register_node(node: DistributedNode, manager_address: str = None) -> str:
```

**参数**：
- `node` (DistributedNode): 要注册的分布式节点对象。
- `manager_address` (str, 可选): 管理节点的地址，如果为None则使用默认管理节点。默认为None。

**返回值**：
- `str`: 节点的注册ID，可用于后续查询和使用节点。

**异常**：
- `ValueError`: 如果分布式节点对象无效或管理节点地址无效。
- `RuntimeError`: 如果注册节点过程中发生错误，如网络错误、管理节点不可用等。

**示例**：
```python
from distributed import register_node

# 注册节点
node_id = register_node(compute_node, "*************:8000")
print(node_id)  # 输出: "node-123456"
```

#### 2.3.2 `unregister_node`

从系统中注销分布式节点，使其不再可用。

**函数签名**：
```python
def unregister_node(node_id: str, manager_address: str = None) -> bool:
```

**参数**：
- `node_id` (str): 要注销的节点ID。
- `manager_address` (str, 可选): 管理节点的地址，如果为None则使用默认管理节点。默认为None。

**返回值**：
- `bool`: 注销是否成功，True表示成功，False表示失败。

**异常**：
- `ValueError`: 如果节点ID无效或管理节点地址无效。
- `RuntimeError`: 如果注销节点过程中发生错误，如网络错误、管理节点不可用等。

**示例**：
```python
from distributed import unregister_node

# 注销节点
success = unregister_node("node-123456", "*************:8000")
if success:
    print("Node unregistered successfully")
else:
    print("Failed to unregister node")
```

#### 2.3.3 `get_node`

根据ID获取分布式节点。

**函数签名**：
```python
def get_node(node_id: str, manager_address: str = None) -> DistributedNode:
```

**参数**：
- `node_id` (str): 要获取的节点ID。
- `manager_address` (str, 可选): 管理节点的地址，如果为None则使用默认管理节点。默认为None。

**返回值**：
- `DistributedNode`: 获取的分布式节点对象。

**异常**：
- `ValueError`: 如果节点ID无效或管理节点地址无效。
- `RuntimeError`: 如果获取节点过程中发生错误，如网络错误、管理节点不可用等。
- `KeyError`: 如果找不到指定ID的节点。

**示例**：
```python
from distributed import get_node

# 获取节点
node = get_node("node-123456", "*************:8000")
print(get_node_info(node))
```

#### 2.3.4 `list_nodes`

列出系统中的所有分布式节点。

**函数签名**：
```python
def list_nodes(filter_criteria: Dict[str, Any] = None, 
              manager_address: str = None) -> List[Dict[str, Any]]:
```

**参数**：
- `filter_criteria` (Dict[str, Any], 可选): 过滤条件，用于筛选节点。默认为None，表示不过滤。
- `manager_address` (str, 可选): 管理节点的地址，如果为None则使用默认管理节点。默认为None。

**返回值**：
- `List[Dict[str, Any]]`: 节点信息列表，每个元素包含节点的ID、类型、地址、状态等信息。

**异常**：
- `ValueError`: 如果过滤条件无效或管理节点地址无效。
- `RuntimeError`: 如果列出节点过程中发生错误，如网络错误、管理节点不可用等。

**示例**：
```python
from distributed import list_nodes

# 列出所有节点
nodes = list_nodes(manager_address="*************:8000")
for node_info in nodes:
    print(f"ID: {node_info['id']}, Type: {node_info['node_type']}, Address: {node_info['address']}, Status: {node_info['status']}")

# 列出特定类型的节点
compute_nodes = list_nodes({"node_type": "compute"}, "*************:8000")
for node_info in compute_nodes:
    print(f"ID: {node_info['id']}, Address: {node_info['address']}, Status: {node_info['status']}")
```

## 3. 任务管理接口

### 3.1 创建任务

#### 3.1.1 `create_task`

创建一个新的分布式任务。

**函数签名**：
```python
def create_task(task_type: str, function: Callable, input_data: Any, 
               parameters: Dict[str, Any] = None) -> DistributedTask:
```

**参数**：
- `task_type` (str): 任务类型，如"compute"、"io"、"control"等。
- `function` (Callable): 任务执行的函数，接受输入数据并返回结果。
- `input_data` (Any): 任务的输入数据。
- `parameters` (Dict[str, Any], 可选): 任务的参数，用于配置任务的行为。默认为None。

**返回值**：
- `DistributedTask`: 创建的分布式任务对象。

**异常**：
- `ValueError`: 如果参数无效，如无效的任务类型等。
- `TypeError`: 如果参数类型不正确，如函数不可调用等。

**示例**：
```python
from distributed import create_task

# 定义任务函数
def square(x):
    return x * x

# 创建计算任务
compute_task = create_task("compute", square, 5, {
    "priority": "high",
    "timeout": 10
})

# 定义IO任务函数
def read_file(path):
    with open(path, 'r') as f:
        return f.read()

# 创建IO任务
io_task = create_task("io", read_file, "data.txt", {
    "priority": "medium",
    "timeout": 30
})
```

#### 3.1.2 `get_task_info`

获取分布式任务的信息，包括任务类型、状态、参数等。

**函数签名**：
```python
def get_task_info(task: DistributedTask) -> Dict[str, Any]:
```

**参数**：
- `task` (DistributedTask): 要获取信息的分布式任务对象。

**返回值**：
- `Dict[str, Any]`: 分布式任务的信息，包括任务类型、状态、参数等。

**异常**：
- `ValueError`: 如果分布式任务对象无效。

**示例**：
```python
from distributed import get_task_info

# 获取任务信息
info = get_task_info(compute_task)
print(info)
# 输出:
# {
#     "id": "...",
#     "task_type": "compute",
#     "status": "created",
#     "parameters": {
#         "priority": "high",
#         "timeout": 10
#     },
#     "created_at": "...",
#     "started_at": null,
#     "completed_at": null,
#     "result": null,
#     "error": null
# }
```

### 3.2 任务操作

#### 3.2.1 `submit_task`

提交分布式任务到系统中，使其开始执行。

**函数签名**：
```python
def submit_task(task: DistributedTask, node_id: str = None, 
               manager_address: str = None) -> str:
```

**参数**：
- `task` (DistributedTask): 要提交的分布式任务对象。
- `node_id` (str, 可选): 执行任务的节点ID，如果为None则由系统自动选择。默认为None。
- `manager_address` (str, 可选): 管理节点的地址，如果为None则使用默认管理节点。默认为None。

**返回值**：
- `str`: 任务的提交ID，可用于后续查询和管理任务。

**异常**：
- `ValueError`: 如果分布式任务对象无效、节点ID无效或管理节点地址无效。
- `RuntimeError`: 如果提交任务过程中发生错误，如网络错误、节点不可用等。

**示例**：
```python
from distributed import submit_task

# 提交任务到自动选择的节点
task_id = submit_task(compute_task, manager_address="*************:8000")
print(task_id)  # 输出: "task-123456"

# 提交任务到指定节点
task_id = submit_task(io_task, "node-123456", "*************:8000")
print(task_id)  # 输出: "task-234567"
```

#### 3.2.2 `cancel_task`

取消正在执行的分布式任务。

**函数签名**：
```python
def cancel_task(task_id: str, manager_address: str = None) -> bool:
```

**参数**：
- `task_id` (str): 要取消的任务ID。
- `manager_address` (str, 可选): 管理节点的地址，如果为None则使用默认管理节点。默认为None。

**返回值**：
- `bool`: 取消是否成功，True表示成功，False表示失败。

**异常**：
- `ValueError`: 如果任务ID无效或管理节点地址无效。
- `RuntimeError`: 如果取消任务过程中发生错误，如网络错误、管理节点不可用等。
- `KeyError`: 如果找不到指定ID的任务。

**示例**：
```python
from distributed import cancel_task

# 取消任务
success = cancel_task("task-123456", "*************:8000")
if success:
    print("Task cancelled successfully")
else:
    print("Failed to cancel task")
```

#### 3.2.3 `get_task_result`

获取分布式任务的执行结果。

**函数签名**：
```python
def get_task_result(task_id: str, wait: bool = True, timeout: float = None, 
                   manager_address: str = None) -> Any:
```

**参数**：
- `task_id` (str): 要获取结果的任务ID。
- `wait` (bool, 可选): 是否等待任务完成，True表示等待，False表示不等待。默认为True。
- `timeout` (float, 可选): 等待超时时间（秒），如果为None则无限等待。默认为None。
- `manager_address` (str, 可选): 管理节点的地址，如果为None则使用默认管理节点。默认为None。

**返回值**：
- `Any`: 任务的执行结果，如果任务未完成且wait为False，则返回None。

**异常**：
- `ValueError`: 如果任务ID无效、超时时间无效或管理节点地址无效。
- `RuntimeError`: 如果获取结果过程中发生错误，如网络错误、管理节点不可用等。
- `KeyError`: 如果找不到指定ID的任务。
- `TimeoutError`: 如果等待超时。
- `Exception`: 如果任务执行过程中发生错误，则抛出任务的错误。

**示例**：
```python
from distributed import get_task_result

# 等待任务完成并获取结果
result = get_task_result("task-123456", True, 60, "*************:8000")
print(result)  # 输出: 25 (5的平方)

# 不等待任务完成，立即返回结果（如果任务未完成则返回None）
result = get_task_result("task-234567", False, None, "*************:8000")
if result is None:
    print("Task not completed yet")
else:
    print(result)
```
