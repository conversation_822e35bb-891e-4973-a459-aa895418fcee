## 5. 分形动力学路由算法接口

### 5.1 适配器定义

```python
class FractalDynamicsRouterAdapter(AlgorithmAdapter):
    """分形动力学路由算法适配器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化适配器
        
        Args:
            config: 配置参数
        """
        self.router = None
        self.config = config or {}
        
    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化适配器
        
        Args:
            config: 配置参数
            
        Returns:
            初始化是否成功
        """
        try:
            merged_config = {**self.config, **config}
            
            # 创建路由器实例
            from src.algorithms.routing.fractal_router_optimized import OptimizedFractalDynamicsRouter
            
            self.router = OptimizedFractalDynamicsRouter(
                fractal_dimension=merged_config.get('fractal_dimension', 1.5),
                stability_threshold=merged_config.get('stability_threshold', 0.01),
                max_iterations=merged_config.get('max_iterations', 100),
                damping_factor=merged_config.get('damping_factor', 0.85),
                exploration_factor=merged_config.get('exploration_factor', 0.2),
                use_parallel=merged_config.get('use_parallel', False),
                num_workers=merged_config.get('num_workers', 4),
                use_sparse=merged_config.get('use_sparse', False),
                use_cache=merged_config.get('use_cache', True),
                cache_size=merged_config.get('cache_size', 1000)
            )
            
            return True
        except Exception as e:
            logging.error(f"Failed to initialize FractalDynamicsRouterAdapter: {str(e)}")
            return False
    
    def compute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行路由计算
        
        Args:
            input_data: 输入数据，包含以下字段：
                - operation_type: 操作类型，如'route', 'multi_path_route'
                - parameters: 操作参数，包含：
                    - network: 网络图
                    - source: 源节点
                    - target: 目标节点
                    - constraints: 约束函数列表（可选）
                    - base_result: 基础结果，用于增量计算（可选）
                - context: 上下文信息
                - metadata: 元数据
            
        Returns:
            计算结果，包含以下字段：
                - status: 状态码
                - result: 路由结果，包含：
                    - path: 路径
                    - cost: 路径成本
                    - iterations: 迭代次数
                    - convergence_achieved: 是否达到收敛
                - metrics: 性能指标
                - metadata: 元数据
        """
        if not self.router:
            return {
                'status': 3,
                'result': None,
                'metrics': {},
                'metadata': {'error': 'Router not initialized'}
            }
        
        try:
            operation_type = input_data.get('operation_type', 'route')
            parameters = input_data.get('parameters', {})
            
            # 准备路由参数
            compute_params = {
                'network': parameters.get('network'),
                'source': parameters.get('source'),
                'target': parameters.get('target')
            }
            
            # 添加可选参数
            if 'constraints' in parameters:
                compute_params['constraints'] = parameters['constraints']
            
            if 'base_result' in parameters:
                compute_params['base_result'] = parameters['base_result']
            
            # 执行路由
            start_time = time.time()
            result = self.router.compute(compute_params)
            end_time = time.time()
            
            # 准备返回结果
            return {
                'status': 0,
                'result': result,
                'metrics': {
                    'computation_time': end_time - start_time,
                    'iterations': result.get('iterations', 0),
                    'convergence_achieved': result.get('convergence_achieved', False)
                },
                'metadata': {
                    'algorithm': 'FractalDynamicsRouter',
                    'parameters': parameters
                }
            }
        except Exception as e:
            logging.error(f"Error in FractalDynamicsRouterAdapter.compute: {str(e)}")
            return {
                'status': 1,
                'result': None,
                'metrics': {},
                'metadata': {'error': str(e)}
            }
    
    def get_capabilities(self) -> Dict[str, Any]:
        """获取算法能力描述
        
        Returns:
            算法能力描述
        """
        return {
            'algorithm_type': 'routing',
            'operations': ['route', 'multi_path_route'],
            'supports_constraints': True,
            'supports_multi_path': True,
            'supports_incremental': True,
            'supports_dynamic_network': True,
            'performance_profile': {
                'time_complexity': 'O(n³)',  # n: number of nodes
                'space_complexity': 'O(n²)',
                'parallelization': 'node-level'
            }
        }
    
    def shutdown(self) -> bool:
        """关闭适配器
        
        Returns:
            关闭是否成功
        """
        try:
            self.router = None
            return True
        except Exception as e:
            logging.error(f"Error in FractalDynamicsRouterAdapter.shutdown: {str(e)}")
            return False
```

### 5.2 与ThoughtEngine的集成

#### 5.2.1 思维路由场景

在ThoughtEngine中，分形动力学路由算法主要用于以下思维场景：

1. **思维路径规划**：在思维网络中找到最优路径
2. **概念关联导航**：在概念空间中导航和关联
3. **推理链构建**：构建从前提到结论的推理链
4. **记忆检索路径**：优化记忆检索的路径

#### 5.2.2 集成示例

```python
# 在ThoughtEngine中使用分形动力学路由算法
from thought_engine import ThoughtEngine
from thought_engine.adapters import FractalDynamicsRouterAdapter
import networkx as nx

# 创建思维引擎
engine = ThoughtEngine()

# 注册路由算法适配器
router_adapter = FractalDynamicsRouterAdapter()
engine.register_algorithm_adapter('routing', router_adapter)

# 在思维过程中使用路由算法
def thought_process():
    # 获取思维网络
    thought_network = engine.get_thought_network()
    
    # 定义源节点和目标节点
    source_concept = 'artificial_intelligence'
    target_concept = 'consciousness'
    
    # 调用路由算法
    result = engine.invoke_algorithm(
        algorithm_type='routing',
        operation_type='route',
        parameters={
            'network': thought_network,
            'source': source_concept,
            'target': target_concept
        }
    )
    
    # 使用路由结果
    path = result['result']['path']
    cost = result['result']['cost']
    
    # 继续思维过程
    # ...

# 执行思维过程
engine.execute(thought_process)
```

### 5.3 数据转换

#### 5.3.1 思维网络到路由网络转换

将ThoughtEngine的思维网络转换为路由算法期望的网络格式：

```python
def convert_thought_network_to_routing_network(thought_network: Dict[str, Any]) -> nx.Graph:
    """将思维网络转换为路由网络
    
    Args:
        thought_network: 思维网络
        
    Returns:
        路由网络
    """
    # 创建网络图
    G = nx.Graph()
    
    # 添加节点
    for node_id, node_data in thought_network['nodes'].items():
        G.add_node(node_id, **node_data)
    
    # 添加边
    for edge in thought_network['edges']:
        source = edge['source']
        target = edge['target']
        weight = edge.get('weight', 1.0)
        
        # 计算边权重（考虑关联强度、概念相似性等）
        adjusted_weight = calculate_edge_weight(edge)
        
        G.add_edge(source, target, weight=adjusted_weight, **edge)
    
    return G

def calculate_edge_weight(edge: Dict[str, Any]) -> float:
    """计算边权重
    
    Args:
        edge: 边数据
        
    Returns:
        边权重
    """
    # 基础权重
    weight = edge.get('weight', 1.0)
    
    # 考虑关联强度
    association_strength = edge.get('association_strength', 1.0)
    
    # 考虑概念相似性
    concept_similarity = edge.get('concept_similarity', 0.5)
    
    # 考虑使用频率
    usage_frequency = edge.get('usage_frequency', 1.0)
    
    # 计算综合权重
    adjusted_weight = weight * (1.0 / association_strength) * (1.0 / concept_similarity) * (1.0 / usage_frequency)
    
    return adjusted_weight
```

#### 5.3.2 路由结果到思维路径转换

将路由算法的结果转换为ThoughtEngine期望的思维路径格式：

```python
def convert_routing_result_to_thought_path(routing_result: Dict[str, Any]) -> Dict[str, Any]:
    """将路由结果转换为思维路径
    
    Args:
        routing_result: 路由结果
        
    Returns:
        思维路径
    """
    # 提取路径
    path = routing_result.get('path', [])
    cost = routing_result.get('cost', 0.0)
    
    # 构建思维路径
    thought_path = {
        'nodes': path,
        'cost': cost,
        'path_type': 'association',
        'confidence': calculate_path_confidence(routing_result),
        'reasoning_steps': generate_reasoning_steps(path)
    }
    
    return thought_path

def calculate_path_confidence(routing_result: Dict[str, Any]) -> float:
    """计算路径置信度
    
    Args:
        routing_result: 路由结果
        
    Returns:
        路径置信度
    """
    # 基于收敛性和迭代次数计算置信度
    convergence_achieved = routing_result.get('convergence_achieved', False)
    iterations = routing_result.get('iterations', 0)
    max_iterations = routing_result.get('max_iterations', 100)
    
    if not convergence_achieved:
        return 0.5  # 未收敛，中等置信度
    
    # 收敛越快，置信度越高
    iteration_factor = 1.0 - (iterations / max_iterations)
    
    return 0.7 + 0.3 * iteration_factor

def generate_reasoning_steps(path: List[str]) -> List[Dict[str, Any]]:
    """生成推理步骤
    
    Args:
        path: 路径
        
    Returns:
        推理步骤
    """
    reasoning_steps = []
    
    for i in range(len(path) - 1):
        source = path[i]
        target = path[i + 1]
        
        step = {
            'from': source,
            'to': target,
            'relation_type': 'association',
            'description': f"从{source}联想到{target}"
        }
        
        reasoning_steps.append(step)
    
    return reasoning_steps
```
