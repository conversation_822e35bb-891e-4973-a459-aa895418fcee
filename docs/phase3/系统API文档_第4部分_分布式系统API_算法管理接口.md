# 超融态思维引擎系统API文档 - 第4部分：分布式系统API（算法管理接口）

## 5. 算法管理接口

算法管理接口提供了创建、注册和执行分布式算法的功能，使用户能够在分布式环境中运行复杂的计算任务。

### 5.1 创建分布式算法

#### 5.1.1 `create_distributed_algorithm`

创建一个新的分布式算法。

**函数签名**：
```python
def create_distributed_algorithm(algorithm_type: str, 
                                function: Callable, 
                                parameters: Dict[str, Any] = None) -> DistributedAlgorithm:
```

**参数**：
- `algorithm_type` (str): 算法类型，如"map_reduce"、"parameter_server"、"allreduce"等。
- `function` (Callable): 算法执行的函数，接受输入数据并返回结果。
- `parameters` (Dict[str, Any], 可选): 算法的参数，用于配置算法的行为。默认为None。

**返回值**：
- `DistributedAlgorithm`: 创建的分布式算法对象。

**异常**：
- `ValueError`: 如果参数无效，如无效的算法类型等。
- `TypeError`: 如果参数类型不正确，如函数不可调用等。

**示例**：
```python
from distributed import create_distributed_algorithm

# 定义Map函数
def map_function(data):
    return [(word, 1) for word in data.split()]

# 定义Reduce函数
def reduce_function(key, values):
    return (key, sum(values))

# 创建MapReduce算法
map_reduce_algorithm = create_distributed_algorithm("map_reduce", {
    "map": map_function,
    "reduce": reduce_function
}, {
    "num_mappers": 10,
    "num_reducers": 5,
    "chunk_size": 1024
})

# 定义参数服务器算法函数
def parameter_server_function(parameters, gradients):
    return [p - 0.01 * g for p, g in zip(parameters, gradients)]

# 创建参数服务器算法
parameter_server_algorithm = create_distributed_algorithm("parameter_server", parameter_server_function, {
    "num_workers": 8,
    "synchronous": True,
    "staleness": 0
})
```

#### 5.1.2 `get_distributed_algorithm_info`

获取分布式算法的信息，包括算法类型、参数等。

**函数签名**：
```python
def get_distributed_algorithm_info(algorithm: DistributedAlgorithm) -> Dict[str, Any]:
```

**参数**：
- `algorithm` (DistributedAlgorithm): 要获取信息的分布式算法对象。

**返回值**：
- `Dict[str, Any]`: 分布式算法的信息，包括算法类型、参数等。

**异常**：
- `ValueError`: 如果分布式算法对象无效。

**示例**：
```python
from distributed import get_distributed_algorithm_info

# 获取算法信息
info = get_distributed_algorithm_info(map_reduce_algorithm)
print(info)
# 输出:
# {
#     "id": "...",
#     "algorithm_type": "map_reduce",
#     "parameters": {
#         "num_mappers": 10,
#         "num_reducers": 5,
#         "chunk_size": 1024
#     },
#     "created_at": "..."
# }
```

### 5.2 算法适配器

#### 5.2.1 `create_algorithm_adapter`

创建一个新的算法适配器，将普通算法适配为分布式算法。

**函数签名**：
```python
def create_algorithm_adapter(adapter_type: str, 
                            algorithm: Any, 
                            parameters: Dict[str, Any] = None) -> DistributedAlgorithmAdapter:
```

**参数**：
- `adapter_type` (str): 适配器类型，如"morphism"、"composition"、"meta_cognitive"等。
- `algorithm` (Any): 要适配的算法对象，可以是态射、组合、元认知系统等。
- `parameters` (Dict[str, Any], 可选): 适配器的参数，用于配置适配器的行为。默认为None。

**返回值**：
- `DistributedAlgorithmAdapter`: 创建的分布式算法适配器对象。

**异常**：
- `ValueError`: 如果参数无效，如无效的适配器类型等。
- `TypeError`: 如果参数类型不正确，如算法对象类型不匹配等。

**示例**：
```python
from distributed import create_algorithm_adapter
from morphism import create_morphism, MorphismDomain, MorphismCodomain

# 创建态射
domain = MorphismDomain("input_domain", 3)
codomain = MorphismCodomain("output_domain", 3)
morphism = create_morphism("linear", domain, codomain, lambda x: x * 2)

# 创建态射适配器
morphism_adapter = create_algorithm_adapter("morphism", morphism, {
    "partition_strategy": "data_parallel",
    "communication_strategy": "all_reduce"
})

# 创建元认知系统适配器
from metacognition import create_meta_cognitive_mapping
mapping = create_meta_cognitive_mapping("direct")
meta_cognitive_adapter = create_algorithm_adapter("meta_cognitive", mapping, {
    "partition_strategy": "model_parallel",
    "communication_strategy": "parameter_server"
})
```

#### 5.2.2 `get_adapter_info`

获取算法适配器的信息，包括适配器类型、算法、参数等。

**函数签名**：
```python
def get_adapter_info(adapter: DistributedAlgorithmAdapter) -> Dict[str, Any]:
```

**参数**：
- `adapter` (DistributedAlgorithmAdapter): 要获取信息的算法适配器对象。

**返回值**：
- `Dict[str, Any]`: 算法适配器的信息，包括适配器类型、算法、参数等。

**异常**：
- `ValueError`: 如果算法适配器对象无效。

**示例**：
```python
from distributed import get_adapter_info

# 获取适配器信息
info = get_adapter_info(morphism_adapter)
print(info)
# 输出:
# {
#     "id": "...",
#     "adapter_type": "morphism",
#     "algorithm": {
#         "id": "...",
#         "morphism_type": "linear",
#         ...
#     },
#     "parameters": {
#         "partition_strategy": "data_parallel",
#         "communication_strategy": "all_reduce"
#     },
#     "created_at": "..."
# }
```

### 5.3 算法执行

#### 5.3.1 `execute_distributed_algorithm`

执行分布式算法，处理输入数据并返回结果。

**函数签名**：
```python
def execute_distributed_algorithm(algorithm: Union[DistributedAlgorithm, DistributedAlgorithmAdapter], 
                                 input_data: Any, 
                                 nodes: List[str] = None, 
                                 parameters: Dict[str, Any] = None, 
                                 manager_address: str = None) -> Any:
```

**参数**：
- `algorithm` (Union[DistributedAlgorithm, DistributedAlgorithmAdapter]): 要执行的分布式算法或算法适配器对象。
- `input_data` (Any): 算法的输入数据。
- `nodes` (List[str], 可选): 执行算法的节点ID列表，如果为None则由系统自动选择。默认为None。
- `parameters` (Dict[str, Any], 可选): 执行的参数，用于配置执行过程。默认为None。
- `manager_address` (str, 可选): 管理节点的地址，如果为None则使用默认管理节点。默认为None。

**返回值**：
- `Any`: 算法的执行结果。

**异常**：
- `ValueError`: 如果分布式算法对象无效、输入数据无效、节点ID无效或管理节点地址无效。
- `TypeError`: 如果参数类型不正确。
- `RuntimeError`: 如果执行算法过程中发生错误，如网络错误、节点不可用等。

**示例**：
```python
from distributed import execute_distributed_algorithm

# 执行MapReduce算法
input_data = "hello world hello distributed world"
result = execute_distributed_algorithm(map_reduce_algorithm, input_data, None, {
    "timeout": 60
}, "*************:8000")
print(result)
# 输出: [("hello", 2), ("world", 2), ("distributed", 1)]

# 执行态射适配器
input_data = [1, 2, 3]
result = execute_distributed_algorithm(morphism_adapter, input_data, ["node-123456", "node-234567"], {
    "timeout": 30
}, "*************:8000")
print(result)
# 输出: [2, 4, 6]
```

#### 5.3.2 `execute_distributed_algorithm_async`

异步执行分布式算法，立即返回任务ID，可以稍后获取结果。

**函数签名**：
```python
def execute_distributed_algorithm_async(algorithm: Union[DistributedAlgorithm, DistributedAlgorithmAdapter], 
                                       input_data: Any, 
                                       nodes: List[str] = None, 
                                       parameters: Dict[str, Any] = None, 
                                       manager_address: str = None) -> str:
```

**参数**：
- `algorithm` (Union[DistributedAlgorithm, DistributedAlgorithmAdapter]): 要执行的分布式算法或算法适配器对象。
- `input_data` (Any): 算法的输入数据。
- `nodes` (List[str], 可选): 执行算法的节点ID列表，如果为None则由系统自动选择。默认为None。
- `parameters` (Dict[str, Any], 可选): 执行的参数，用于配置执行过程。默认为None。
- `manager_address` (str, 可选): 管理节点的地址，如果为None则使用默认管理节点。默认为None。

**返回值**：
- `str`: 任务的ID，可用于后续获取结果。

**异常**：
- `ValueError`: 如果分布式算法对象无效、输入数据无效、节点ID无效或管理节点地址无效。
- `TypeError`: 如果参数类型不正确。
- `RuntimeError`: 如果执行算法过程中发生错误，如网络错误、节点不可用等。

**示例**：
```python
from distributed import execute_distributed_algorithm_async, get_task_result

# 异步执行MapReduce算法
input_data = "hello world hello distributed world"
task_id = execute_distributed_algorithm_async(map_reduce_algorithm, input_data, None, {
    "timeout": 60
}, "*************:8000")
print(task_id)  # 输出: "task-123456"

# 稍后获取结果
result = get_task_result(task_id, True, 60, "*************:8000")
print(result)
# 输出: [("hello", 2), ("world", 2), ("distributed", 1)]
```

### 5.4 算法注册与查询

#### 5.4.1 `register_distributed_algorithm`

将分布式算法注册到系统中，使其可以被其他组件使用。

**函数签名**：
```python
def register_distributed_algorithm(algorithm: Union[DistributedAlgorithm, DistributedAlgorithmAdapter], 
                                  name: str = None, 
                                  manager_address: str = None) -> str:
```

**参数**：
- `algorithm` (Union[DistributedAlgorithm, DistributedAlgorithmAdapter]): 要注册的分布式算法或算法适配器对象。
- `name` (str, 可选): 算法的注册名称，如果为None则使用算法的ID。默认为None。
- `manager_address` (str, 可选): 管理节点的地址，如果为None则使用默认管理节点。默认为None。

**返回值**：
- `str`: 算法的注册ID，可用于后续查询和使用算法。

**异常**：
- `ValueError`: 如果分布式算法对象无效、名称已被使用或管理节点地址无效。
- `RuntimeError`: 如果注册算法过程中发生错误，如网络错误、管理节点不可用等。

**示例**：
```python
from distributed import register_distributed_algorithm

# 注册MapReduce算法
algorithm_id = register_distributed_algorithm(map_reduce_algorithm, "word_count", "*************:8000")
print(algorithm_id)  # 输出: "algorithm-123456"

# 注册态射适配器
adapter_id = register_distributed_algorithm(morphism_adapter, "double_morphism", "*************:8000")
print(adapter_id)  # 输出: "algorithm-234567"
```

#### 5.4.2 `get_distributed_algorithm`

根据ID或名称获取已注册的分布式算法。

**函数签名**：
```python
def get_distributed_algorithm(algorithm_id: str, 
                             manager_address: str = None) -> Union[DistributedAlgorithm, DistributedAlgorithmAdapter]:
```

**参数**：
- `algorithm_id` (str): 算法的注册ID或名称。
- `manager_address` (str, 可选): 管理节点的地址，如果为None则使用默认管理节点。默认为None。

**返回值**：
- `Union[DistributedAlgorithm, DistributedAlgorithmAdapter]`: 获取的分布式算法或算法适配器对象。

**异常**：
- `ValueError`: 如果算法ID无效或管理节点地址无效。
- `RuntimeError`: 如果获取算法过程中发生错误，如网络错误、管理节点不可用等。
- `KeyError`: 如果找不到指定ID或名称的算法。

**示例**：
```python
from distributed import get_distributed_algorithm

# 根据ID获取算法
algorithm = get_distributed_algorithm("algorithm-123456", "*************:8000")
print(get_distributed_algorithm_info(algorithm))

# 根据名称获取算法
algorithm = get_distributed_algorithm("word_count", "*************:8000")
print(get_distributed_algorithm_info(algorithm))
```

#### 5.4.3 `list_distributed_algorithms`

列出系统中已注册的所有分布式算法。

**函数签名**：
```python
def list_distributed_algorithms(filter_criteria: Dict[str, Any] = None, 
                               manager_address: str = None) -> List[Dict[str, Any]]:
```

**参数**：
- `filter_criteria` (Dict[str, Any], 可选): 过滤条件，用于筛选算法。默认为None，表示不过滤。
- `manager_address` (str, 可选): 管理节点的地址，如果为None则使用默认管理节点。默认为None。

**返回值**：
- `List[Dict[str, Any]]`: 算法信息列表，每个元素包含算法的ID、名称、类型等信息。

**异常**：
- `ValueError`: 如果过滤条件无效或管理节点地址无效。
- `RuntimeError`: 如果列出算法过程中发生错误，如网络错误、管理节点不可用等。

**示例**：
```python
from distributed import list_distributed_algorithms

# 列出所有算法
algorithms = list_distributed_algorithms(None, "*************:8000")
for algorithm_info in algorithms:
    print(f"ID: {algorithm_info['id']}, Name: {algorithm_info['name']}, Type: {algorithm_info['algorithm_type']}")

# 列出特定类型的算法
map_reduce_algorithms = list_distributed_algorithms({"algorithm_type": "map_reduce"}, "*************:8000")
for algorithm_info in map_reduce_algorithms:
    print(f"ID: {algorithm_info['id']}, Name: {algorithm_info['name']}")
```
