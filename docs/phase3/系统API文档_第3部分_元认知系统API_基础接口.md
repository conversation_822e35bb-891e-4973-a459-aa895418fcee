# 超融态思维引擎系统API文档 - 第3部分：元认知系统API（基础接口）

## 1. 元认知系统API概述

元认知系统API提供了访问和使用元认知系统功能的接口，包括元认知映射、元认知学习、元认知优化和元认知控制等。这些API使用户能够构建和操作元认知系统，实现自我认知、自我监控、自我评估和自我调节的功能。

## 2. 认知状态接口

### 2.1 创建认知状态

#### 2.1.1 `create_cognitive_state`

创建一个新的认知状态对象。

**函数签名**：
```python
def create_cognitive_state(state_type: str, parameters: Dict[str, Any] = None) -> CognitiveState:
```

**参数**：
- `state_type` (str): 状态类型，如"perception"、"attention"、"memory"等。
- `parameters` (Dict[str, Any], 可选): 状态的参数，用于配置状态的内容和行为。默认为None。

**返回值**：
- `CognitiveState`: 创建的认知状态对象。

**异常**：
- `ValueError`: 如果参数无效，如无效的状态类型等。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from metacognition import create_cognitive_state

# 创建感知状态
perception_state = create_cognitive_state("perception", {
    "intensity": 0.8,
    "clarity": 0.7,
    "modality": "visual",
    "content": "object"
})

# 创建注意力状态
attention_state = create_cognitive_state("attention", {
    "focus": 0.9,
    "duration": 5.0,
    "target": "object"
})

# 创建记忆状态
memory_state = create_cognitive_state("memory", {
    "strength": 0.6,
    "duration": 10.0,
    "content": "past_event"
})
```

#### 2.1.2 `get_cognitive_state_info`

获取认知状态的信息，包括状态类型、参数、创建时间等。

**函数签名**：
```python
def get_cognitive_state_info(state: CognitiveState) -> Dict[str, Any]:
```

**参数**：
- `state` (CognitiveState): 要获取信息的认知状态对象。

**返回值**：
- `Dict[str, Any]`: 认知状态的信息，包括状态类型、参数、创建时间等。

**异常**：
- `ValueError`: 如果认知状态对象无效。

**示例**：
```python
from metacognition import get_cognitive_state_info

# 获取认知状态信息
info = get_cognitive_state_info(perception_state)
print(info)
# 输出:
# {
#     "id": "...",
#     "state_type": "perception",
#     "parameters": {
#         "intensity": 0.8,
#         "clarity": 0.7,
#         "modality": "visual",
#         "content": "object"
#     },
#     "created_at": "2023-05-01T12:34:56.789Z"
# }
```

#### 2.1.3 `update_cognitive_state`

更新认知状态的参数。

**函数签名**：
```python
def update_cognitive_state(state: CognitiveState, parameters: Dict[str, Any]) -> CognitiveState:
```

**参数**：
- `state` (CognitiveState): 要更新的认知状态对象。
- `parameters` (Dict[str, Any]): 新的参数，用于更新状态的内容和行为。

**返回值**：
- `CognitiveState`: 更新后的认知状态对象。

**异常**：
- `ValueError`: 如果认知状态对象无效或参数无效。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from metacognition import update_cognitive_state

# 更新认知状态
updated_state = update_cognitive_state(perception_state, {
    "intensity": 0.9,
    "clarity": 0.8
})

# 获取更新后的状态信息
info = get_cognitive_state_info(updated_state)
print(info["parameters"])  # 输出: {"intensity": 0.9, "clarity": 0.8, "modality": "visual", "content": "object"}
```

### 2.2 认知状态操作

#### 2.2.1 `compare_cognitive_states`

比较两个认知状态，计算它们之间的相似度或差异度。

**函数签名**：
```python
def compare_cognitive_states(state1: CognitiveState, state2: CognitiveState, 
                            method: str = "similarity") -> float:
```

**参数**：
- `state1` (CognitiveState): 第一个认知状态对象。
- `state2` (CognitiveState): 第二个认知状态对象。
- `method` (str, 可选): 比较方法，可以是"similarity"（相似度）或"difference"（差异度）。默认为"similarity"。

**返回值**：
- `float`: 两个状态之间的相似度或差异度，范围为[0, 1]。

**异常**：
- `ValueError`: 如果认知状态对象无效或比较方法无效。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from metacognition import compare_cognitive_states

# 创建两个认知状态
state1 = create_cognitive_state("perception", {"intensity": 0.8, "clarity": 0.7, "modality": "visual", "content": "object"})
state2 = create_cognitive_state("perception", {"intensity": 0.9, "clarity": 0.8, "modality": "visual", "content": "object"})

# 计算相似度
similarity = compare_cognitive_states(state1, state2, "similarity")
print(similarity)  # 输出: 0.95 (示例值，实际值取决于实现)

# 计算差异度
difference = compare_cognitive_states(state1, state2, "difference")
print(difference)  # 输出: 0.05 (示例值，实际值取决于实现)
```

#### 2.2.2 `merge_cognitive_states`

合并多个认知状态，生成一个新的认知状态。

**函数签名**：
```python
def merge_cognitive_states(states: List[CognitiveState], method: str = "average") -> CognitiveState:
```

**参数**：
- `states` (List[CognitiveState]): 要合并的认知状态对象列表。
- `method` (str, 可选): 合并方法，可以是"average"（平均）、"weighted"（加权）或"max"（最大值）等。默认为"average"。

**返回值**：
- `CognitiveState`: 合并后的认知状态对象。

**异常**：
- `ValueError`: 如果认知状态对象无效、状态列表为空或合并方法无效。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from metacognition import merge_cognitive_states

# 创建多个认知状态
state1 = create_cognitive_state("perception", {"intensity": 0.8, "clarity": 0.7, "modality": "visual", "content": "object"})
state2 = create_cognitive_state("perception", {"intensity": 0.9, "clarity": 0.8, "modality": "visual", "content": "object"})
state3 = create_cognitive_state("perception", {"intensity": 0.7, "clarity": 0.6, "modality": "visual", "content": "object"})

# 合并状态（平均）
merged_state = merge_cognitive_states([state1, state2, state3], "average")
info = get_cognitive_state_info(merged_state)
print(info["parameters"])  # 输出: {"intensity": 0.8, "clarity": 0.7, "modality": "visual", "content": "object"}

# 合并状态（最大值）
merged_state = merge_cognitive_states([state1, state2, state3], "max")
info = get_cognitive_state_info(merged_state)
print(info["parameters"])  # 输出: {"intensity": 0.9, "clarity": 0.8, "modality": "visual", "content": "object"}
```

#### 2.2.3 `filter_cognitive_states`

根据条件筛选认知状态。

**函数签名**：
```python
def filter_cognitive_states(states: List[CognitiveState], 
                           filter_criteria: Dict[str, Any]) -> List[CognitiveState]:
```

**参数**：
- `states` (List[CognitiveState]): 要筛选的认知状态对象列表。
- `filter_criteria` (Dict[str, Any]): 筛选条件，用于匹配状态的类型和参数。

**返回值**：
- `List[CognitiveState]`: 符合筛选条件的认知状态对象列表。

**异常**：
- `ValueError`: 如果认知状态对象无效或筛选条件无效。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from metacognition import filter_cognitive_states

# 创建多个认知状态
state1 = create_cognitive_state("perception", {"intensity": 0.8, "clarity": 0.7, "modality": "visual", "content": "object"})
state2 = create_cognitive_state("attention", {"focus": 0.9, "duration": 5.0, "target": "object"})
state3 = create_cognitive_state("memory", {"strength": 0.6, "duration": 10.0, "content": "past_event"})

# 筛选特定类型的状态
perception_states = filter_cognitive_states([state1, state2, state3], {"state_type": "perception"})
print(len(perception_states))  # 输出: 1

# 筛选特定参数的状态
object_states = filter_cognitive_states([state1, state2, state3], {"parameters.content": "object"})
print(len(object_states))  # 输出: 1

# 筛选多个条件的状态
filtered_states = filter_cognitive_states([state1, state2, state3], {
    "parameters.intensity": {"$gt": 0.7},  # 强度大于0.7
    "parameters.clarity": {"$gt": 0.6}     # 清晰度大于0.6
})
print(len(filtered_states))  # 输出: 1
```

## 3. 元认知状态接口

### 3.1 创建元认知状态

#### 3.1.1 `create_meta_cognitive_state`

创建一个新的元认知状态对象。

**函数签名**：
```python
def create_meta_cognitive_state(state_type: str, parameters: Dict[str, Any] = None) -> MetaCognitiveState:
```

**参数**：
- `state_type` (str): 状态类型，如"awareness"、"monitoring"、"evaluation"等。
- `parameters` (Dict[str, Any], 可选): 状态的参数，用于配置状态的内容和行为。默认为None。

**返回值**：
- `MetaCognitiveState`: 创建的元认知状态对象。

**异常**：
- `ValueError`: 如果参数无效，如无效的状态类型等。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from metacognition import create_meta_cognitive_state

# 创建意识状态
awareness_state = create_meta_cognitive_state("awareness", {
    "confidence": 0.8,
    "awareness_level": 0.7,
    "source_state_type": "perception",
    "source_timestamp": "2023-05-01T12:34:56.789Z"
})

# 创建监控状态
monitoring_state = create_meta_cognitive_state("monitoring", {
    "accuracy": 0.9,
    "completeness": 0.8,
    "target_state_type": "perception",
    "target_timestamp": "2023-05-01T12:34:56.789Z"
})

# 创建评估状态
evaluation_state = create_meta_cognitive_state("evaluation", {
    "effectiveness": 0.7,
    "efficiency": 0.6,
    "target_process": "learning",
    "target_timestamp": "2023-05-01T12:34:56.789Z"
})
```

#### 3.1.2 `get_meta_cognitive_state_info`

获取元认知状态的信息，包括状态类型、参数、创建时间等。

**函数签名**：
```python
def get_meta_cognitive_state_info(state: MetaCognitiveState) -> Dict[str, Any]:
```

**参数**：
- `state` (MetaCognitiveState): 要获取信息的元认知状态对象。

**返回值**：
- `Dict[str, Any]`: 元认知状态的信息，包括状态类型、参数、创建时间等。

**异常**：
- `ValueError`: 如果元认知状态对象无效。

**示例**：
```python
from metacognition import get_meta_cognitive_state_info

# 获取元认知状态信息
info = get_meta_cognitive_state_info(awareness_state)
print(info)
# 输出:
# {
#     "id": "...",
#     "state_type": "awareness",
#     "parameters": {
#         "confidence": 0.8,
#         "awareness_level": 0.7,
#         "source_state_type": "perception",
#         "source_timestamp": "2023-05-01T12:34:56.789Z"
#     },
#     "created_at": "2023-05-01T12:35:00.000Z"
# }
```

#### 3.1.3 `update_meta_cognitive_state`

更新元认知状态的参数。

**函数签名**：
```python
def update_meta_cognitive_state(state: MetaCognitiveState, 
                               parameters: Dict[str, Any]) -> MetaCognitiveState:
```

**参数**：
- `state` (MetaCognitiveState): 要更新的元认知状态对象。
- `parameters` (Dict[str, Any]): 新的参数，用于更新状态的内容和行为。

**返回值**：
- `MetaCognitiveState`: 更新后的元认知状态对象。

**异常**：
- `ValueError`: 如果元认知状态对象无效或参数无效。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from metacognition import update_meta_cognitive_state

# 更新元认知状态
updated_state = update_meta_cognitive_state(awareness_state, {
    "confidence": 0.9,
    "awareness_level": 0.8
})

# 获取更新后的状态信息
info = get_meta_cognitive_state_info(updated_state)
print(info["parameters"])  # 输出: {"confidence": 0.9, "awareness_level": 0.8, "source_state_type": "perception", "source_timestamp": "2023-05-01T12:34:56.789Z"}
```

### 3.2 元认知状态操作

#### 3.2.1 `compare_meta_cognitive_states`

比较两个元认知状态，计算它们之间的相似度或差异度。

**函数签名**：
```python
def compare_meta_cognitive_states(state1: MetaCognitiveState, state2: MetaCognitiveState, 
                                 method: str = "similarity") -> float:
```

**参数**：
- `state1` (MetaCognitiveState): 第一个元认知状态对象。
- `state2` (MetaCognitiveState): 第二个元认知状态对象。
- `method` (str, 可选): 比较方法，可以是"similarity"（相似度）或"difference"（差异度）。默认为"similarity"。

**返回值**：
- `float`: 两个状态之间的相似度或差异度，范围为[0, 1]。

**异常**：
- `ValueError`: 如果元认知状态对象无效或比较方法无效。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from metacognition import compare_meta_cognitive_states

# 创建两个元认知状态
state1 = create_meta_cognitive_state("awareness", {"confidence": 0.8, "awareness_level": 0.7})
state2 = create_meta_cognitive_state("awareness", {"confidence": 0.9, "awareness_level": 0.8})

# 计算相似度
similarity = compare_meta_cognitive_states(state1, state2, "similarity")
print(similarity)  # 输出: 0.95 (示例值，实际值取决于实现)

# 计算差异度
difference = compare_meta_cognitive_states(state1, state2, "difference")
print(difference)  # 输出: 0.05 (示例值，实际值取决于实现)
```

#### 3.2.2 `merge_meta_cognitive_states`

合并多个元认知状态，生成一个新的元认知状态。

**函数签名**：
```python
def merge_meta_cognitive_states(states: List[MetaCognitiveState], 
                               method: str = "average") -> MetaCognitiveState:
```

**参数**：
- `states` (List[MetaCognitiveState]): 要合并的元认知状态对象列表。
- `method` (str, 可选): 合并方法，可以是"average"（平均）、"weighted"（加权）或"max"（最大值）等。默认为"average"。

**返回值**：
- `MetaCognitiveState`: 合并后的元认知状态对象。

**异常**：
- `ValueError`: 如果元认知状态对象无效、状态列表为空或合并方法无效。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from metacognition import merge_meta_cognitive_states

# 创建多个元认知状态
state1 = create_meta_cognitive_state("awareness", {"confidence": 0.8, "awareness_level": 0.7})
state2 = create_meta_cognitive_state("awareness", {"confidence": 0.9, "awareness_level": 0.8})
state3 = create_meta_cognitive_state("awareness", {"confidence": 0.7, "awareness_level": 0.6})

# 合并状态（平均）
merged_state = merge_meta_cognitive_states([state1, state2, state3], "average")
info = get_meta_cognitive_state_info(merged_state)
print(info["parameters"])  # 输出: {"confidence": 0.8, "awareness_level": 0.7}

# 合并状态（最大值）
merged_state = merge_meta_cognitive_states([state1, state2, state3], "max")
info = get_meta_cognitive_state_info(merged_state)
print(info["parameters"])  # 输出: {"confidence": 0.9, "awareness_level": 0.8}
```

#### 3.2.3 `filter_meta_cognitive_states`

根据条件筛选元认知状态。

**函数签名**：
```python
def filter_meta_cognitive_states(states: List[MetaCognitiveState], 
                                filter_criteria: Dict[str, Any]) -> List[MetaCognitiveState]:
```

**参数**：
- `states` (List[MetaCognitiveState]): 要筛选的元认知状态对象列表。
- `filter_criteria` (Dict[str, Any]): 筛选条件，用于匹配状态的类型和参数。

**返回值**：
- `List[MetaCognitiveState]`: 符合筛选条件的元认知状态对象列表。

**异常**：
- `ValueError`: 如果元认知状态对象无效或筛选条件无效。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from metacognition import filter_meta_cognitive_states

# 创建多个元认知状态
state1 = create_meta_cognitive_state("awareness", {"confidence": 0.8, "awareness_level": 0.7})
state2 = create_meta_cognitive_state("monitoring", {"accuracy": 0.9, "completeness": 0.8})
state3 = create_meta_cognitive_state("evaluation", {"effectiveness": 0.7, "efficiency": 0.6})

# 筛选特定类型的状态
awareness_states = filter_meta_cognitive_states([state1, state2, state3], {"state_type": "awareness"})
print(len(awareness_states))  # 输出: 1

# 筛选特定参数的状态
high_confidence_states = filter_meta_cognitive_states([state1, state2, state3], {"parameters.confidence": {"$gt": 0.7}})
print(len(high_confidence_states))  # 输出: 1

# 筛选多个条件的状态
filtered_states = filter_meta_cognitive_states([state1, state2, state3], {
    "parameters.confidence": {"$gt": 0.7},  # 置信度大于0.7
    "parameters.awareness_level": {"$gt": 0.6}  # 意识水平大于0.6
})
print(len(filtered_states))  # 输出: 1
```
