# 超融态思维引擎系统API文档 - 第4部分：分布式系统API（网络管理接口）

## 6. 网络管理接口

网络管理接口提供了创建、配置和管理分布式网络的功能，使用户能够构建和维护分布式系统的通信网络。

### 6.1 创建网络

#### 6.1.1 `create_network`

创建一个新的分布式网络。

**函数签名**：
```python
def create_network(network_type: str, parameters: Dict[str, Any] = None) -> NetworkManager:
```

**参数**：
- `network_type` (str): 网络类型，如"mesh"、"star"、"tree"、"ring"等。
- `parameters` (Dict[str, Any], 可选): 网络的参数，用于配置网络的行为。默认为None。

**返回值**：
- `NetworkManager`: 创建的网络管理器对象。

**异常**：
- `ValueError`: 如果参数无效，如无效的网络类型等。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from distributed import create_network

# 创建网格网络
mesh_network = create_network("mesh", {
    "connection_timeout": 30,
    "heartbeat_interval": 5,
    "reconnect_attempts": 3
})

# 创建星型网络
star_network = create_network("star", {
    "central_node": "node-123456",
    "connection_timeout": 30,
    "heartbeat_interval": 5
})

# 创建树形网络
tree_network = create_network("tree", {
    "root_node": "node-123456",
    "branching_factor": 3,
    "connection_timeout": 30
})
```

#### 6.1.2 `get_network_info`

获取分布式网络的信息，包括网络类型、节点数量、连接状态、参数等。

**函数签名**：
```python
def get_network_info(network: NetworkManager) -> Dict[str, Any]:
```

**参数**：
- `network` (NetworkManager): 要获取信息的网络管理器对象。

**返回值**：
- `Dict[str, Any]`: 分布式网络的信息，包括网络类型、节点数量、连接状态、参数等。

**异常**：
- `ValueError`: 如果网络管理器对象无效。

**示例**：
```python
from distributed import get_network_info

# 获取网络信息
info = get_network_info(mesh_network)
print(info)
# 输出:
# {
#     "id": "...",
#     "network_type": "mesh",
#     "node_count": 5,
#     "connection_count": 10,
#     "parameters": {
#         "connection_timeout": 30,
#         "heartbeat_interval": 5,
#         "reconnect_attempts": 3
#     },
#     "created_at": "...",
#     "status": "running"
# }
```

### 6.2 网络节点管理

#### 6.2.1 `add_network_node`

向分布式网络中添加节点。

**函数签名**：
```python
def add_network_node(network: NetworkManager, node: DistributedNode) -> bool:
```

**参数**：
- `network` (NetworkManager): 要添加节点的网络管理器对象。
- `node` (DistributedNode): 要添加的分布式节点对象。

**返回值**：
- `bool`: 添加是否成功，True表示成功，False表示失败。

**异常**：
- `ValueError`: 如果网络管理器对象无效或分布式节点对象无效。
- `RuntimeError`: 如果添加节点过程中发生错误，如网络错误、节点已存在等。

**示例**：
```python
from distributed import add_network_node, create_node

# 创建节点
node = create_node("compute", "*************:8000", {
    "cpu_cores": 4,
    "memory": "8GB",
    "gpu": True
})

# 添加节点到网络
success = add_network_node(mesh_network, node)
if success:
    print("Node added to network successfully")
else:
    print("Failed to add node to network")
```

#### 6.2.2 `remove_network_node`

从分布式网络中移除节点。

**函数签名**：
```python
def remove_network_node(network: NetworkManager, node_id: str) -> bool:
```

**参数**：
- `network` (NetworkManager): 要移除节点的网络管理器对象。
- `node_id` (str): 要移除的节点ID。

**返回值**：
- `bool`: 移除是否成功，True表示成功，False表示失败。

**异常**：
- `ValueError`: 如果网络管理器对象无效或节点ID无效。
- `RuntimeError`: 如果移除节点过程中发生错误，如网络错误、节点不存在等。
- `KeyError`: 如果找不到指定ID的节点。

**示例**：
```python
from distributed import remove_network_node

# 移除节点
success = remove_network_node(mesh_network, "node-123456")
if success:
    print("Node removed from network successfully")
else:
    print("Failed to remove node from network")
```

#### 6.2.3 `get_network_node`

获取分布式网络中的节点。

**函数签名**：
```python
def get_network_node(network: NetworkManager, node_id: str) -> NetworkNode:
```

**参数**：
- `network` (NetworkManager): 要获取节点的网络管理器对象。
- `node_id` (str): 要获取的节点ID。

**返回值**：
- `NetworkNode`: 获取的网络节点对象。

**异常**：
- `ValueError`: 如果网络管理器对象无效或节点ID无效。
- `RuntimeError`: 如果获取节点过程中发生错误，如网络错误等。
- `KeyError`: 如果找不到指定ID的节点。

**示例**：
```python
from distributed import get_network_node

# 获取节点
node = get_network_node(mesh_network, "node-123456")
print(node.id)  # 输出: "node-123456"
print(node.address)  # 输出: "*************:8000"
print(node.status)  # 输出: "connected"
```

#### 6.2.4 `list_network_nodes`

列出分布式网络中的所有节点。

**函数签名**：
```python
def list_network_nodes(network: NetworkManager, 
                      filter_criteria: Dict[str, Any] = None) -> List[NetworkNode]:
```

**参数**：
- `network` (NetworkManager): 要列出节点的网络管理器对象。
- `filter_criteria` (Dict[str, Any], 可选): 过滤条件，用于筛选节点。默认为None，表示不过滤。

**返回值**：
- `List[NetworkNode]`: 网络节点对象列表。

**异常**：
- `ValueError`: 如果网络管理器对象无效或过滤条件无效。
- `RuntimeError`: 如果列出节点过程中发生错误，如网络错误等。

**示例**：
```python
from distributed import list_network_nodes

# 列出所有节点
nodes = list_network_nodes(mesh_network)
for node in nodes:
    print(f"ID: {node.id}, Address: {node.address}, Status: {node.status}")

# 列出特定状态的节点
connected_nodes = list_network_nodes(mesh_network, {"status": "connected"})
for node in connected_nodes:
    print(f"ID: {node.id}, Address: {node.address}")
```

### 6.3 网络连接管理

#### 6.3.1 `create_network_connection`

在分布式网络中创建节点之间的连接。

**函数签名**：
```python
def create_network_connection(network: NetworkManager, 
                             source_node_id: str, 
                             target_node_id: str, 
                             parameters: Dict[str, Any] = None) -> str:
```

**参数**：
- `network` (NetworkManager): 要创建连接的网络管理器对象。
- `source_node_id` (str): 连接的源节点ID。
- `target_node_id` (str): 连接的目标节点ID。
- `parameters` (Dict[str, Any], 可选): 连接的参数，用于配置连接的行为。默认为None。

**返回值**：
- `str`: 创建的连接ID。

**异常**：
- `ValueError`: 如果网络管理器对象无效、节点ID无效或参数无效。
- `RuntimeError`: 如果创建连接过程中发生错误，如网络错误、节点不存在等。
- `KeyError`: 如果找不到指定ID的节点。

**示例**：
```python
from distributed import create_network_connection

# 创建连接
connection_id = create_network_connection(mesh_network, "node-123456", "node-234567", {
    "bandwidth": "10Gbps",
    "latency": "1ms",
    "reliability": 0.99
})
print(connection_id)  # 输出: "connection-123456"
```

#### 6.3.2 `remove_network_connection`

从分布式网络中移除节点之间的连接。

**函数签名**：
```python
def remove_network_connection(network: NetworkManager, connection_id: str) -> bool:
```

**参数**：
- `network` (NetworkManager): 要移除连接的网络管理器对象。
- `connection_id` (str): 要移除的连接ID。

**返回值**：
- `bool`: 移除是否成功，True表示成功，False表示失败。

**异常**：
- `ValueError`: 如果网络管理器对象无效或连接ID无效。
- `RuntimeError`: 如果移除连接过程中发生错误，如网络错误、连接不存在等。
- `KeyError`: 如果找不到指定ID的连接。

**示例**：
```python
from distributed import remove_network_connection

# 移除连接
success = remove_network_connection(mesh_network, "connection-123456")
if success:
    print("Connection removed successfully")
else:
    print("Failed to remove connection")
```

#### 6.3.3 `get_network_connection`

获取分布式网络中的连接信息。

**函数签名**：
```python
def get_network_connection(network: NetworkManager, connection_id: str) -> Dict[str, Any]:
```

**参数**：
- `network` (NetworkManager): 要获取连接的网络管理器对象。
- `connection_id` (str): 要获取的连接ID。

**返回值**：
- `Dict[str, Any]`: 连接的信息，包括源节点、目标节点、状态、参数等。

**异常**：
- `ValueError`: 如果网络管理器对象无效或连接ID无效。
- `RuntimeError`: 如果获取连接过程中发生错误，如网络错误等。
- `KeyError`: 如果找不到指定ID的连接。

**示例**：
```python
from distributed import get_network_connection

# 获取连接信息
info = get_network_connection(mesh_network, "connection-123456")
print(info)
# 输出:
# {
#     "id": "connection-123456",
#     "source_node_id": "node-123456",
#     "target_node_id": "node-234567",
#     "status": "active",
#     "parameters": {
#         "bandwidth": "10Gbps",
#         "latency": "1ms",
#         "reliability": 0.99
#     },
#     "created_at": "..."
# }
```

#### 6.3.4 `list_network_connections`

列出分布式网络中的所有连接。

**函数签名**：
```python
def list_network_connections(network: NetworkManager, 
                            filter_criteria: Dict[str, Any] = None) -> List[Dict[str, Any]]:
```

**参数**：
- `network` (NetworkManager): 要列出连接的网络管理器对象。
- `filter_criteria` (Dict[str, Any], 可选): 过滤条件，用于筛选连接。默认为None，表示不过滤。

**返回值**：
- `List[Dict[str, Any]]`: 连接信息列表，每个元素包含连接的ID、源节点、目标节点、状态等信息。

**异常**：
- `ValueError`: 如果网络管理器对象无效或过滤条件无效。
- `RuntimeError`: 如果列出连接过程中发生错误，如网络错误等。

**示例**：
```python
from distributed import list_network_connections

# 列出所有连接
connections = list_network_connections(mesh_network)
for connection in connections:
    print(f"ID: {connection['id']}, Source: {connection['source_node_id']}, Target: {connection['target_node_id']}, Status: {connection['status']}")

# 列出特定状态的连接
active_connections = list_network_connections(mesh_network, {"status": "active"})
for connection in active_connections:
    print(f"ID: {connection['id']}, Source: {connection['source_node_id']}, Target: {connection['target_node_id']}")
```

### 6.4 网络操作

#### 6.4.1 `start_network`

启动分布式网络，使其开始运行。

**函数签名**：
```python
def start_network(network: NetworkManager) -> bool:
```

**参数**：
- `network` (NetworkManager): 要启动的网络管理器对象。

**返回值**：
- `bool`: 启动是否成功，True表示成功，False表示失败。

**异常**：
- `ValueError`: 如果网络管理器对象无效。
- `RuntimeError`: 如果启动网络过程中发生错误，如网络错误、资源不足等。

**示例**：
```python
from distributed import start_network

# 启动网络
success = start_network(mesh_network)
if success:
    print("Network started successfully")
else:
    print("Failed to start network")
```

#### 6.4.2 `stop_network`

停止分布式网络，使其停止运行。

**函数签名**：
```python
def stop_network(network: NetworkManager) -> bool:
```

**参数**：
- `network` (NetworkManager): 要停止的网络管理器对象。

**返回值**：
- `bool`: 停止是否成功，True表示成功，False表示失败。

**异常**：
- `ValueError`: 如果网络管理器对象无效。
- `RuntimeError`: 如果停止网络过程中发生错误，如网络错误等。

**示例**：
```python
from distributed import stop_network

# 停止网络
success = stop_network(mesh_network)
if success:
    print("Network stopped successfully")
else:
    print("Failed to stop network")
```

#### 6.4.3 `restart_network`

重启分布式网络，先停止再启动。

**函数签名**：
```python
def restart_network(network: NetworkManager) -> bool:
```

**参数**：
- `network` (NetworkManager): 要重启的网络管理器对象。

**返回值**：
- `bool`: 重启是否成功，True表示成功，False表示失败。

**异常**：
- `ValueError`: 如果网络管理器对象无效。
- `RuntimeError`: 如果重启网络过程中发生错误，如网络错误、资源不足等。

**示例**：
```python
from distributed import restart_network

# 重启网络
success = restart_network(mesh_network)
if success:
    print("Network restarted successfully")
else:
    print("Failed to restart network")
```

#### 6.4.4 `monitor_network`

监控分布式网络的状态和性能。

**函数签名**：
```python
def monitor_network(network: NetworkManager, 
                   metrics: List[str] = None, 
                   interval: float = 1.0, 
                   duration: float = None) -> Dict[str, List[Dict[str, Any]]]:
```

**参数**：
- `network` (NetworkManager): 要监控的网络管理器对象。
- `metrics` (List[str], 可选): 要监控的指标列表，如果为None则监控所有指标。默认为None。
- `interval` (float, 可选): 监控的时间间隔（秒）。默认为1.0。
- `duration` (float, 可选): 监控的持续时间（秒），如果为None则持续监控直到手动停止。默认为None。

**返回值**：
- `Dict[str, List[Dict[str, Any]]]`: 监控结果，键为指标名称，值为时间序列数据。

**异常**：
- `ValueError`: 如果网络管理器对象无效、指标无效、间隔无效或持续时间无效。
- `RuntimeError`: 如果监控网络过程中发生错误，如网络错误等。

**示例**：
```python
from distributed import monitor_network

# 监控网络
metrics = ["node_count", "connection_count", "message_rate", "latency"]
results = monitor_network(mesh_network, metrics, 1.0, 60.0)
for metric, data in results.items():
    print(f"Metric: {metric}")
    for point in data:
        print(f"  Time: {point['timestamp']}, Value: {point['value']}")
```
