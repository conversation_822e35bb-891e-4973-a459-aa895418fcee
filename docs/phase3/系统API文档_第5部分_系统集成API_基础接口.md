# 超融态思维引擎系统API文档 - 第5部分：系统集成API（基础接口）

## 1. 系统集成API概述

系统集成API提供了访问和使用系统集成功能的接口，包括组件集成、接口管理、数据转换和服务协调等。这些API使用户能够将态射系统、元认知系统和分布式系统等核心组件集成在一起，形成一个统一的、协同工作的整体。

## 2. 组件集成接口

### 2.1 元认知系统集成

#### 2.1.1 `create_meta_cognitive_system`

创建一个集成的元认知系统，包含元认知映射、学习、优化和控制功能。

**函数签名**：
```python
def create_meta_cognitive_system(parameters: Dict[str, Any] = None) -> MetaCognitiveSystem:
```

**参数**：
- `parameters` (Dict[str, Any], 可选): 系统的参数，用于配置系统的行为。默认为None。

**返回值**：
- `MetaCognitiveSystem`: 创建的元认知系统对象。

**异常**：
- `ValueError`: 如果参数无效。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from integration import create_meta_cognitive_system

# 创建元认知系统
meta_cognitive_system = create_meta_cognitive_system({
    "mapping_type": "direct",
    "learning_type": "supervised",
    "optimization_type": "gradient",
    "control_type": "feedback"
})
```

#### 2.1.2 `register_cognitive_state`

向元认知系统注册认知状态。

**函数签名**：
```python
def register_cognitive_state(system: MetaCognitiveSystem, 
                            state: CognitiveState, 
                            name: str = None) -> str:
```

**参数**：
- `system` (MetaCognitiveSystem): 要注册状态的元认知系统对象。
- `state` (CognitiveState): 要注册的认知状态对象。
- `name` (str, 可选): 状态的注册名称，如果为None则使用状态的ID。默认为None。

**返回值**：
- `str`: 状态的注册ID，可用于后续查询和使用状态。

**异常**：
- `ValueError`: 如果元认知系统对象无效、认知状态对象无效或名称已被使用。

**示例**：
```python
from integration import register_cognitive_state
from metacognition import create_cognitive_state

# 创建认知状态
perception_state = create_cognitive_state("perception", {
    "intensity": 0.8,
    "clarity": 0.7,
    "modality": "visual",
    "content": "object"
})

# 注册认知状态
state_id = register_cognitive_state(meta_cognitive_system, perception_state, "visual_perception")
print(state_id)  # 输出: "state-123456"
```

#### 2.1.3 `register_meta_cognitive_state`

向元认知系统注册元认知状态。

**函数签名**：
```python
def register_meta_cognitive_state(system: MetaCognitiveSystem, 
                                 state: MetaCognitiveState, 
                                 name: str = None) -> str:
```

**参数**：
- `system` (MetaCognitiveSystem): 要注册状态的元认知系统对象。
- `state` (MetaCognitiveState): 要注册的元认知状态对象。
- `name` (str, 可选): 状态的注册名称，如果为None则使用状态的ID。默认为None。

**返回值**：
- `str`: 状态的注册ID，可用于后续查询和使用状态。

**异常**：
- `ValueError`: 如果元认知系统对象无效、元认知状态对象无效或名称已被使用。

**示例**：
```python
from integration import register_meta_cognitive_state
from metacognition import create_meta_cognitive_state

# 创建元认知状态
awareness_state = create_meta_cognitive_state("awareness", {
    "confidence": 0.8,
    "awareness_level": 0.7,
    "source_state_type": "perception",
    "source_timestamp": "2023-05-01T12:34:56.789Z"
})

# 注册元认知状态
state_id = register_meta_cognitive_state(meta_cognitive_system, awareness_state, "visual_awareness")
print(state_id)  # 输出: "state-234567"
```

#### 2.1.4 `get_cognitive_state`

从元认知系统获取认知状态。

**函数签名**：
```python
def get_cognitive_state(system: MetaCognitiveSystem, state_id: str) -> CognitiveState:
```

**参数**：
- `system` (MetaCognitiveSystem): 要获取状态的元认知系统对象。
- `state_id` (str): 要获取的状态ID或名称。

**返回值**：
- `CognitiveState`: 获取的认知状态对象。

**异常**：
- `ValueError`: 如果元认知系统对象无效或状态ID无效。
- `KeyError`: 如果找不到指定ID或名称的状态。

**示例**：
```python
from integration import get_cognitive_state

# 获取认知状态
state = get_cognitive_state(meta_cognitive_system, "visual_perception")
print(get_cognitive_state_info(state))
```

#### 2.1.5 `get_meta_cognitive_state`

从元认知系统获取元认知状态。

**函数签名**：
```python
def get_meta_cognitive_state(system: MetaCognitiveSystem, state_id: str) -> MetaCognitiveState:
```

**参数**：
- `system` (MetaCognitiveSystem): 要获取状态的元认知系统对象。
- `state_id` (str): 要获取的状态ID或名称。

**返回值**：
- `MetaCognitiveState`: 获取的元认知状态对象。

**异常**：
- `ValueError`: 如果元认知系统对象无效或状态ID无效。
- `KeyError`: 如果找不到指定ID或名称的状态。

**示例**：
```python
from integration import get_meta_cognitive_state

# 获取元认知状态
state = get_meta_cognitive_state(meta_cognitive_system, "visual_awareness")
print(get_meta_cognitive_state_info(state))
```

### 2.2 态射系统与元认知系统集成

#### 2.2.1 `create_morphism_meta_cognition_integration`

创建一个集成态射系统和元认知系统的对象。

**函数签名**：
```python
def create_morphism_meta_cognition_integration(morphism_system: Any, 
                                              meta_cognitive_system: MetaCognitiveSystem, 
                                              parameters: Dict[str, Any] = None) -> MorphismMetaCognitionIntegration:
```

**参数**：
- `morphism_system` (Any): 态射系统对象。
- `meta_cognitive_system` (MetaCognitiveSystem): 元认知系统对象。
- `parameters` (Dict[str, Any], 可选): 集成的参数，用于配置集成的行为。默认为None。

**返回值**：
- `MorphismMetaCognitionIntegration`: 创建的态射系统与元认知系统集成对象。

**异常**：
- `ValueError`: 如果态射系统对象无效、元认知系统对象无效或参数无效。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from integration import create_morphism_meta_cognition_integration
from morphism import create_morphism_system

# 创建态射系统
morphism_system = create_morphism_system()

# 创建集成对象
integration = create_morphism_meta_cognition_integration(morphism_system, meta_cognitive_system, {
    "mapping_strategy": "direct",
    "feedback_strategy": "gradient",
    "adaptation_rate": 0.1
})
```

#### 2.2.2 `register_morphism`

向集成对象注册态射。

**函数签名**：
```python
def register_morphism(integration: MorphismMetaCognitionIntegration, 
                     morphism: DynamicMorphism, 
                     name: str = None) -> str:
```

**参数**：
- `integration` (MorphismMetaCognitionIntegration): 要注册态射的集成对象。
- `morphism` (DynamicMorphism): 要注册的态射对象。
- `name` (str, 可选): 态射的注册名称，如果为None则使用态射的ID。默认为None。

**返回值**：
- `str`: 态射的注册ID，可用于后续查询和使用态射。

**异常**：
- `ValueError`: 如果集成对象无效、态射对象无效或名称已被使用。

**示例**：
```python
from integration import register_morphism
from morphism import create_morphism, MorphismDomain, MorphismCodomain

# 创建态射
domain = MorphismDomain("input_domain", 3)
codomain = MorphismCodomain("output_domain", 3)
morphism = create_morphism("linear", domain, codomain, lambda x: x * 2)

# 注册态射
morphism_id = register_morphism(integration, morphism, "double_morphism")
print(morphism_id)  # 输出: "morphism-123456"
```

#### 2.2.3 `create_cognitive_state_from_morphism`

从态射创建认知状态。

**函数签名**：
```python
def create_cognitive_state_from_morphism(integration: MorphismMetaCognitionIntegration, 
                                        morphism_id: str) -> CognitiveState:
```

**参数**：
- `integration` (MorphismMetaCognitionIntegration): 要创建状态的集成对象。
- `morphism_id` (str): 要转换的态射ID或名称。

**返回值**：
- `CognitiveState`: 创建的认知状态对象。

**异常**：
- `ValueError`: 如果集成对象无效或态射ID无效。
- `KeyError`: 如果找不到指定ID或名称的态射。

**示例**：
```python
from integration import create_cognitive_state_from_morphism

# 从态射创建认知状态
state = create_cognitive_state_from_morphism(integration, "double_morphism")
print(get_cognitive_state_info(state))
```

#### 2.2.4 `apply_meta_cognitive_state_to_morphism`

将元认知状态应用到态射。

**函数签名**：
```python
def apply_meta_cognitive_state_to_morphism(integration: MorphismMetaCognitionIntegration, 
                                          meta_cognitive_state_id: str, 
                                          morphism_id: str) -> DynamicMorphism:
```

**参数**：
- `integration` (MorphismMetaCognitionIntegration): 要应用状态的集成对象。
- `meta_cognitive_state_id` (str): 要应用的元认知状态ID或名称。
- `morphism_id` (str): 要应用到的态射ID或名称。

**返回值**：
- `DynamicMorphism`: 应用元认知状态后的态射对象。

**异常**：
- `ValueError`: 如果集成对象无效、元认知状态ID无效或态射ID无效。
- `KeyError`: 如果找不到指定ID或名称的元认知状态或态射。

**示例**：
```python
from integration import apply_meta_cognitive_state_to_morphism

# 将元认知状态应用到态射
enhanced_morphism = apply_meta_cognitive_state_to_morphism(integration, "visual_awareness", "double_morphism")
print(get_morphism_info(enhanced_morphism))
```

### 2.3 分布式算法集成

#### 2.3.1 `create_distributed_integration`

创建一个分布式集成对象，用于将普通算法适配为分布式算法。

**函数签名**：
```python
def create_distributed_integration(parameters: Dict[str, Any] = None) -> DistributedIntegration:
```

**参数**：
- `parameters` (Dict[str, Any], 可选): 集成的参数，用于配置集成的行为。默认为None。

**返回值**：
- `DistributedIntegration`: 创建的分布式集成对象。

**异常**：
- `ValueError`: 如果参数无效。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from integration import create_distributed_integration

# 创建分布式集成对象
distributed_integration = create_distributed_integration({
    "partition_strategy": "data_parallel",
    "communication_strategy": "all_reduce",
    "synchronization_mode": "synchronous"
})
```

#### 2.3.2 `create_morphism_distributed_adapter`

创建一个态射分布式适配器，将态射适配为分布式算法。

**函数签名**：
```python
def create_morphism_distributed_adapter(integration: DistributedIntegration, 
                                       morphism: DynamicMorphism, 
                                       parameters: Dict[str, Any] = None) -> MorphismDistributedAdapter:
```

**参数**：
- `integration` (DistributedIntegration): 分布式集成对象。
- `morphism` (DynamicMorphism): 要适配的态射对象。
- `parameters` (Dict[str, Any], 可选): 适配器的参数，用于配置适配器的行为。默认为None。

**返回值**：
- `MorphismDistributedAdapter`: 创建的态射分布式适配器对象。

**异常**：
- `ValueError`: 如果分布式集成对象无效、态射对象无效或参数无效。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from integration import create_morphism_distributed_adapter
from morphism import create_morphism, MorphismDomain, MorphismCodomain

# 创建态射
domain = MorphismDomain("input_domain", 3)
codomain = MorphismCodomain("output_domain", 3)
morphism = create_morphism("linear", domain, codomain, lambda x: x * 2)

# 创建态射分布式适配器
adapter = create_morphism_distributed_adapter(distributed_integration, morphism, {
    "partition_strategy": "data_parallel",
    "chunk_size": 1000
})
```

#### 2.3.3 `create_meta_cognitive_distributed_adapter`

创建一个元认知分布式适配器，将元认知系统适配为分布式算法。

**函数签名**：
```python
def create_meta_cognitive_distributed_adapter(integration: DistributedIntegration, 
                                             meta_cognitive_system: MetaCognitiveSystem, 
                                             parameters: Dict[str, Any] = None) -> MetaCognitiveDistributedAdapter:
```

**参数**：
- `integration` (DistributedIntegration): 分布式集成对象。
- `meta_cognitive_system` (MetaCognitiveSystem): 要适配的元认知系统对象。
- `parameters` (Dict[str, Any], 可选): 适配器的参数，用于配置适配器的行为。默认为None。

**返回值**：
- `MetaCognitiveDistributedAdapter`: 创建的元认知分布式适配器对象。

**异常**：
- `ValueError`: 如果分布式集成对象无效、元认知系统对象无效或参数无效。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from integration import create_meta_cognitive_distributed_adapter

# 创建元认知分布式适配器
adapter = create_meta_cognitive_distributed_adapter(distributed_integration, meta_cognitive_system, {
    "partition_strategy": "model_parallel",
    "state_synchronization": "periodic"
})
```

## 3. 集成注册表接口

### 3.1 创建集成注册表

#### 3.1.1 `create_integration_registry`

创建一个新的集成注册表，用于管理集成组件和接口。

**函数签名**：
```python
def create_integration_registry(parameters: Dict[str, Any] = None) -> IntegrationRegistry:
```

**参数**：
- `parameters` (Dict[str, Any], 可选): 注册表的参数，用于配置注册表的行为。默认为None。

**返回值**：
- `IntegrationRegistry`: 创建的集成注册表对象。

**异常**：
- `ValueError`: 如果参数无效。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from integration import create_integration_registry

# 创建集成注册表
registry = create_integration_registry({
    "storage_type": "memory",
    "max_entries": 1000,
    "ttl": 3600
})
```

#### 3.1.2 `get_registry_info`

获取集成注册表的信息，包括注册表类型、大小、参数等。

**函数签名**：
```python
def get_registry_info(registry: IntegrationRegistry) -> Dict[str, Any]:
```

**参数**：
- `registry` (IntegrationRegistry): 要获取信息的集成注册表对象。

**返回值**：
- `Dict[str, Any]`: 集成注册表的信息，包括注册表类型、大小、参数等。

**异常**：
- `ValueError`: 如果集成注册表对象无效。

**示例**：
```python
from integration import get_registry_info

# 获取注册表信息
info = get_registry_info(registry)
print(info)
# 输出:
# {
#     "id": "...",
#     "storage_type": "memory",
#     "size": 0,
#     "parameters": {
#         "max_entries": 1000,
#         "ttl": 3600
#     },
#     "created_at": "..."
# }
```

### 3.2 注册表操作

#### 3.2.1 `register_component`

向集成注册表注册组件。

**函数签名**：
```python
def register_component(registry: IntegrationRegistry, 
                      component_type: str, 
                      component: Any, 
                      name: str = None, 
                      metadata: Dict[str, Any] = None) -> str:
```

**参数**：
- `registry` (IntegrationRegistry): 要注册组件的集成注册表对象。
- `component_type` (str): 组件类型，如"morphism"、"meta_cognitive"、"distributed"等。
- `component` (Any): 要注册的组件对象。
- `name` (str, 可选): 组件的注册名称，如果为None则使用组件的ID。默认为None。
- `metadata` (Dict[str, Any], 可选): 组件的元数据，用于描述组件的属性和行为。默认为None。

**返回值**：
- `str`: 组件的注册ID，可用于后续查询和使用组件。

**异常**：
- `ValueError`: 如果集成注册表对象无效、组件类型无效、组件对象无效或名称已被使用。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from integration import register_component

# 注册态射系统
component_id = register_component(registry, "morphism", morphism_system, "morphism_system", {
    "description": "Main morphism system",
    "version": "1.0.0"
})
print(component_id)  # 输出: "component-123456"

# 注册元认知系统
component_id = register_component(registry, "meta_cognitive", meta_cognitive_system, "meta_cognitive_system", {
    "description": "Main meta-cognitive system",
    "version": "1.0.0"
})
print(component_id)  # 输出: "component-234567"
```

#### 3.2.2 `register_interface`

向集成注册表注册接口。

**函数签名**：
```python
def register_interface(registry: IntegrationRegistry, 
                      interface_type: str, 
                      interface: Any, 
                      name: str = None, 
                      metadata: Dict[str, Any] = None) -> str:
```

**参数**：
- `registry` (IntegrationRegistry): 要注册接口的集成注册表对象。
- `interface_type` (str): 接口类型，如"morphism_api"、"meta_cognitive_api"、"distributed_api"等。
- `interface` (Any): 要注册的接口对象。
- `name` (str, 可选): 接口的注册名称，如果为None则使用接口的ID。默认为None。
- `metadata` (Dict[str, Any], 可选): 接口的元数据，用于描述接口的属性和行为。默认为None。

**返回值**：
- `str`: 接口的注册ID，可用于后续查询和使用接口。

**异常**：
- `ValueError`: 如果集成注册表对象无效、接口类型无效、接口对象无效或名称已被使用。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from integration import register_interface

# 注册态射API
interface_id = register_interface(registry, "morphism_api", morphism_api, "morphism_api", {
    "description": "Morphism system API",
    "version": "1.0.0"
})
print(interface_id)  # 输出: "interface-123456"

# 注册元认知API
interface_id = register_interface(registry, "meta_cognitive_api", meta_cognitive_api, "meta_cognitive_api", {
    "description": "Meta-cognitive system API",
    "version": "1.0.0"
})
print(interface_id)  # 输出: "interface-234567"
```

#### 3.2.3 `get_component`

从集成注册表获取组件。

**函数签名**：
```python
def get_component(registry: IntegrationRegistry, component_id: str) -> Any:
```

**参数**：
- `registry` (IntegrationRegistry): 要获取组件的集成注册表对象。
- `component_id` (str): 要获取的组件ID或名称。

**返回值**：
- `Any`: 获取的组件对象。

**异常**：
- `ValueError`: 如果集成注册表对象无效或组件ID无效。
- `KeyError`: 如果找不到指定ID或名称的组件。

**示例**：
```python
from integration import get_component

# 获取态射系统
morphism_system = get_component(registry, "morphism_system")

# 获取元认知系统
meta_cognitive_system = get_component(registry, "meta_cognitive_system")
```

#### 3.2.4 `get_interface`

从集成注册表获取接口。

**函数签名**：
```python
def get_interface(registry: IntegrationRegistry, interface_id: str) -> Any:
```

**参数**：
- `registry` (IntegrationRegistry): 要获取接口的集成注册表对象。
- `interface_id` (str): 要获取的接口ID或名称。

**返回值**：
- `Any`: 获取的接口对象。

**异常**：
- `ValueError`: 如果集成注册表对象无效或接口ID无效。
- `KeyError`: 如果找不到指定ID或名称的接口。

**示例**：
```python
from integration import get_interface

# 获取态射API
morphism_api = get_interface(registry, "morphism_api")

# 获取元认知API
meta_cognitive_api = get_interface(registry, "meta_cognitive_api")
```
