# 超融态思维引擎系统API文档 - 第4部分：分布式系统API（消息管理接口）

## 4. 消息管理接口

消息管理接口提供了创建、发送和接收分布式消息的功能，使节点之间能够进行通信和协作。

### 4.1 创建消息

#### 4.1.1 `create_message`

创建一个新的分布式消息。

**函数签名**：
```python
def create_message(message_type: str, content: Any, 
                  source_node_id: str = None, 
                  target_node_id: str = None, 
                  parameters: Dict[str, Any] = None) -> DistributedMessage:
```

**参数**：
- `message_type` (str): 消息类型，如"data"、"control"、"status"等。
- `content` (Any): 消息内容，可以是任何可序列化的数据。
- `source_node_id` (str, 可选): 消息的源节点ID，如果为None则使用当前节点。默认为None。
- `target_node_id` (str, 可选): 消息的目标节点ID，如果为None则表示广播消息。默认为None。
- `parameters` (Dict[str, Any], 可选): 消息的参数，用于配置消息的行为。默认为None。

**返回值**：
- `DistributedMessage`: 创建的分布式消息对象。

**异常**：
- `ValueError`: 如果参数无效，如无效的消息类型等。
- `TypeError`: 如果参数类型不正确，如内容不可序列化等。

**示例**：
```python
from distributed import create_message

# 创建点对点数据消息
data_message = create_message("data", {"value": 42}, "node-123456", "node-234567", {
    "priority": "high",
    "ttl": 60
})

# 创建广播控制消息
control_message = create_message("control", {"command": "restart"}, "node-123456", None, {
    "priority": "highest",
    "ttl": 30
})

# 创建状态消息
status_message = create_message("status", {"status": "running", "load": 0.5}, "node-123456", "node-234567", {
    "priority": "normal",
    "ttl": 10
})
```

#### 4.1.2 `get_message_info`

获取分布式消息的信息，包括消息类型、内容、源节点、目标节点、参数等。

**函数签名**：
```python
def get_message_info(message: DistributedMessage) -> Dict[str, Any]:
```

**参数**：
- `message` (DistributedMessage): 要获取信息的分布式消息对象。

**返回值**：
- `Dict[str, Any]`: 分布式消息的信息，包括消息类型、内容、源节点、目标节点、参数等。

**异常**：
- `ValueError`: 如果分布式消息对象无效。

**示例**：
```python
from distributed import get_message_info

# 获取消息信息
info = get_message_info(data_message)
print(info)
# 输出:
# {
#     "id": "...",
#     "message_type": "data",
#     "content": {"value": 42},
#     "source_node_id": "node-123456",
#     "target_node_id": "node-234567",
#     "parameters": {
#         "priority": "high",
#         "ttl": 60
#     },
#     "created_at": "...",
#     "delivered": False,
#     "delivered_at": null
# }
```

### 4.2 消息操作

#### 4.2.1 `send_message`

发送分布式消息到目标节点。

**函数签名**：
```python
def send_message(message: DistributedMessage, 
                manager_address: str = None) -> bool:
```

**参数**：
- `message` (DistributedMessage): 要发送的分布式消息对象。
- `manager_address` (str, 可选): 管理节点的地址，如果为None则使用默认管理节点。默认为None。

**返回值**：
- `bool`: 发送是否成功，True表示成功，False表示失败。

**异常**：
- `ValueError`: 如果分布式消息对象无效或管理节点地址无效。
- `RuntimeError`: 如果发送消息过程中发生错误，如网络错误、管理节点不可用等。

**示例**：
```python
from distributed import send_message

# 发送消息
success = send_message(data_message, "*************:8000")
if success:
    print("Message sent successfully")
else:
    print("Failed to send message")
```

#### 4.2.2 `broadcast_message`

广播分布式消息到所有节点或特定类型的节点。

**函数签名**：
```python
def broadcast_message(message: DistributedMessage, 
                     node_type: str = None, 
                     manager_address: str = None) -> bool:
```

**参数**：
- `message` (DistributedMessage): 要广播的分布式消息对象。
- `node_type` (str, 可选): 目标节点类型，如果为None则广播到所有节点。默认为None。
- `manager_address` (str, 可选): 管理节点的地址，如果为None则使用默认管理节点。默认为None。

**返回值**：
- `bool`: 广播是否成功，True表示成功，False表示失败。

**异常**：
- `ValueError`: 如果分布式消息对象无效、节点类型无效或管理节点地址无效。
- `RuntimeError`: 如果广播消息过程中发生错误，如网络错误、管理节点不可用等。

**示例**：
```python
from distributed import broadcast_message

# 广播消息到所有节点
success = broadcast_message(control_message, None, "*************:8000")
if success:
    print("Message broadcasted to all nodes successfully")
else:
    print("Failed to broadcast message to all nodes")

# 广播消息到特定类型的节点
success = broadcast_message(control_message, "compute", "*************:8000")
if success:
    print("Message broadcasted to compute nodes successfully")
else:
    print("Failed to broadcast message to compute nodes")
```

#### 4.2.3 `receive_message`

接收分布式消息，可以指定消息类型和源节点。

**函数签名**：
```python
def receive_message(message_type: str = None, 
                   source_node_id: str = None, 
                   wait: bool = True, 
                   timeout: float = None, 
                   manager_address: str = None) -> DistributedMessage:
```

**参数**：
- `message_type` (str, 可选): 要接收的消息类型，如果为None则接收任何类型。默认为None。
- `source_node_id` (str, 可选): 要接收的消息的源节点ID，如果为None则接收任何源节点的消息。默认为None。
- `wait` (bool, 可选): 是否等待消息到达，True表示等待，False表示不等待。默认为True。
- `timeout` (float, 可选): 等待超时时间（秒），如果为None则无限等待。默认为None。
- `manager_address` (str, 可选): 管理节点的地址，如果为None则使用默认管理节点。默认为None。

**返回值**：
- `DistributedMessage`: 接收到的分布式消息对象，如果没有消息且wait为False，则返回None。

**异常**：
- `ValueError`: 如果消息类型无效、源节点ID无效、超时时间无效或管理节点地址无效。
- `RuntimeError`: 如果接收消息过程中发生错误，如网络错误、管理节点不可用等。
- `TimeoutError`: 如果等待超时。

**示例**：
```python
from distributed import receive_message

# 等待接收任何消息
message = receive_message(None, None, True, 60, "*************:8000")
if message:
    info = get_message_info(message)
    print(f"Received message: Type={info['message_type']}, Content={info['content']}")

# 等待接收特定类型的消息
data_message = receive_message("data", None, True, 30, "*************:8000")
if data_message:
    info = get_message_info(data_message)
    print(f"Received data message: Content={info['content']}")

# 不等待，立即检查是否有消息
message = receive_message(None, None, False, None, "*************:8000")
if message:
    info = get_message_info(message)
    print(f"Received message: Type={info['message_type']}, Content={info['content']}")
else:
    print("No message available")
```

### 4.3 消息队列

#### 4.3.1 `create_message_queue`

创建一个新的消息队列，用于存储和管理消息。

**函数签名**：
```python
def create_message_queue(queue_name: str, 
                        message_type: str = None, 
                        parameters: Dict[str, Any] = None, 
                        manager_address: str = None) -> str:
```

**参数**：
- `queue_name` (str): 队列名称，用于标识队列。
- `message_type` (str, 可选): 队列接受的消息类型，如果为None则接受任何类型。默认为None。
- `parameters` (Dict[str, Any], 可选): 队列的参数，用于配置队列的行为。默认为None。
- `manager_address` (str, 可选): 管理节点的地址，如果为None则使用默认管理节点。默认为None。

**返回值**：
- `str`: 队列的ID，可用于后续操作队列。

**异常**：
- `ValueError`: 如果队列名称无效、消息类型无效或管理节点地址无效。
- `RuntimeError`: 如果创建队列过程中发生错误，如网络错误、管理节点不可用等。

**示例**：
```python
from distributed import create_message_queue

# 创建接受任何类型消息的队列
queue_id = create_message_queue("general_queue", None, {
    "max_size": 1000,
    "ttl": 3600
}, "*************:8000")
print(queue_id)  # 输出: "queue-123456"

# 创建只接受数据消息的队列
data_queue_id = create_message_queue("data_queue", "data", {
    "max_size": 500,
    "ttl": 1800
}, "*************:8000")
print(data_queue_id)  # 输出: "queue-234567"
```

#### 4.3.2 `push_message_to_queue`

将消息推送到指定的队列。

**函数签名**：
```python
def push_message_to_queue(queue_id: str, 
                         message: DistributedMessage, 
                         manager_address: str = None) -> bool:
```

**参数**：
- `queue_id` (str): 队列的ID。
- `message` (DistributedMessage): 要推送的分布式消息对象。
- `manager_address` (str, 可选): 管理节点的地址，如果为None则使用默认管理节点。默认为None。

**返回值**：
- `bool`: 推送是否成功，True表示成功，False表示失败。

**异常**：
- `ValueError`: 如果队列ID无效、分布式消息对象无效或管理节点地址无效。
- `RuntimeError`: 如果推送消息过程中发生错误，如网络错误、管理节点不可用等。
- `KeyError`: 如果找不到指定ID的队列。

**示例**：
```python
from distributed import push_message_to_queue

# 推送消息到队列
success = push_message_to_queue("queue-123456", data_message, "*************:8000")
if success:
    print("Message pushed to queue successfully")
else:
    print("Failed to push message to queue")
```

#### 4.3.3 `pop_message_from_queue`

从指定的队列中弹出消息。

**函数签名**：
```python
def pop_message_from_queue(queue_id: str, 
                          wait: bool = True, 
                          timeout: float = None, 
                          manager_address: str = None) -> DistributedMessage:
```

**参数**：
- `queue_id` (str): 队列的ID。
- `wait` (bool, 可选): 是否等待消息到达，True表示等待，False表示不等待。默认为True。
- `timeout` (float, 可选): 等待超时时间（秒），如果为None则无限等待。默认为None。
- `manager_address` (str, 可选): 管理节点的地址，如果为None则使用默认管理节点。默认为None。

**返回值**：
- `DistributedMessage`: 弹出的分布式消息对象，如果队列为空且wait为False，则返回None。

**异常**：
- `ValueError`: 如果队列ID无效、超时时间无效或管理节点地址无效。
- `RuntimeError`: 如果弹出消息过程中发生错误，如网络错误、管理节点不可用等。
- `KeyError`: 如果找不到指定ID的队列。
- `TimeoutError`: 如果等待超时。

**示例**：
```python
from distributed import pop_message_from_queue

# 等待从队列中弹出消息
message = pop_message_from_queue("queue-123456", True, 60, "*************:8000")
if message:
    info = get_message_info(message)
    print(f"Popped message: Type={info['message_type']}, Content={info['content']}")

# 不等待，立即尝试从队列中弹出消息
message = pop_message_from_queue("queue-123456", False, None, "*************:8000")
if message:
    info = get_message_info(message)
    print(f"Popped message: Type={info['message_type']}, Content={info['content']}")
else:
    print("Queue is empty")
```

#### 4.3.4 `get_queue_info`

获取消息队列的信息，包括队列名称、消息类型、大小、参数等。

**函数签名**：
```python
def get_queue_info(queue_id: str, 
                  manager_address: str = None) -> Dict[str, Any]:
```

**参数**：
- `queue_id` (str): 队列的ID。
- `manager_address` (str, 可选): 管理节点的地址，如果为None则使用默认管理节点。默认为None。

**返回值**：
- `Dict[str, Any]`: 消息队列的信息，包括队列名称、消息类型、大小、参数等。

**异常**：
- `ValueError`: 如果队列ID无效或管理节点地址无效。
- `RuntimeError`: 如果获取队列信息过程中发生错误，如网络错误、管理节点不可用等。
- `KeyError`: 如果找不到指定ID的队列。

**示例**：
```python
from distributed import get_queue_info

# 获取队列信息
info = get_queue_info("queue-123456", "*************:8000")
print(info)
# 输出:
# {
#     "id": "queue-123456",
#     "name": "general_queue",
#     "message_type": null,
#     "size": 10,
#     "parameters": {
#         "max_size": 1000,
#         "ttl": 3600
#     },
#     "created_at": "..."
# }
```
