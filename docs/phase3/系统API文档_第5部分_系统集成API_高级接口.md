# 超融态思维引擎系统API文档 - 第5部分：系统集成API（高级接口）

## 4. 集成管理接口

集成管理接口提供了管理系统集成过程和集成状态的功能，使用户能够协调不同组件的集成和交互。

### 4.1 创建集成管理器

#### 4.1.1 `create_integration_manager`

创建一个新的集成管理器，用于管理系统集成过程和集成状态。

**函数签名**：
```python
def create_integration_manager(registry: IntegrationRegistry = None, 
                              parameters: Dict[str, Any] = None) -> IntegrationManager:
```

**参数**：
- `registry` (IntegrationRegistry, 可选): 集成注册表对象，如果为None则创建一个新的注册表。默认为None。
- `parameters` (Dict[str, Any], 可选): 管理器的参数，用于配置管理器的行为。默认为None。

**返回值**：
- `IntegrationManager`: 创建的集成管理器对象。

**异常**：
- `ValueError`: 如果集成注册表对象无效或参数无效。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from integration import create_integration_manager

# 创建集成管理器（使用现有注册表）
manager = create_integration_manager(registry, {
    "auto_connect": True,
    "error_handling": "retry",
    "max_retries": 3
})

# 创建集成管理器（创建新注册表）
manager = create_integration_manager(None, {
    "auto_connect": True,
    "error_handling": "retry",
    "max_retries": 3
})
```

#### 4.1.2 `get_manager_info`

获取集成管理器的信息，包括管理器状态、注册表、参数等。

**函数签名**：
```python
def get_manager_info(manager: IntegrationManager) -> Dict[str, Any]:
```

**参数**：
- `manager` (IntegrationManager): 要获取信息的集成管理器对象。

**返回值**：
- `Dict[str, Any]`: 集成管理器的信息，包括管理器状态、注册表、参数等。

**异常**：
- `ValueError`: 如果集成管理器对象无效。

**示例**：
```python
from integration import get_manager_info

# 获取管理器信息
info = get_manager_info(manager)
print(info)
# 输出:
# {
#     "id": "...",
#     "status": "running",
#     "registry": {
#         "id": "...",
#         "size": 4,
#         ...
#     },
#     "parameters": {
#         "auto_connect": True,
#         "error_handling": "retry",
#         "max_retries": 3
#     },
#     "created_at": "..."
# }
```

### 4.2 集成连接管理

#### 4.2.1 `create_integration_connection`

创建一个新的集成连接，连接两个组件。

**函数签名**：
```python
def create_integration_connection(manager: IntegrationManager, 
                                 source_component_id: str, 
                                 target_component_id: str, 
                                 connection_type: str = "default", 
                                 parameters: Dict[str, Any] = None) -> str:
```

**参数**：
- `manager` (IntegrationManager): 要创建连接的集成管理器对象。
- `source_component_id` (str): 源组件的ID或名称。
- `target_component_id` (str): 目标组件的ID或名称。
- `connection_type` (str, 可选): 连接类型，如"default"、"data_flow"、"control_flow"等。默认为"default"。
- `parameters` (Dict[str, Any], 可选): 连接的参数，用于配置连接的行为。默认为None。

**返回值**：
- `str`: 创建的连接ID。

**异常**：
- `ValueError`: 如果集成管理器对象无效、组件ID无效、连接类型无效或参数无效。
- `KeyError`: 如果找不到指定ID或名称的组件。
- `RuntimeError`: 如果创建连接过程中发生错误，如组件不兼容等。

**示例**：
```python
from integration import create_integration_connection

# 创建数据流连接
connection_id = create_integration_connection(manager, "morphism_system", "meta_cognitive_system", "data_flow", {
    "data_format": "tensor",
    "buffer_size": 1000,
    "timeout": 30
})
print(connection_id)  # 输出: "connection-123456"

# 创建控制流连接
connection_id = create_integration_connection(manager, "meta_cognitive_system", "morphism_system", "control_flow", {
    "control_type": "feedback",
    "priority": "high",
    "timeout": 10
})
print(connection_id)  # 输出: "connection-234567"
```

#### 4.2.2 `remove_integration_connection`

移除集成连接。

**函数签名**：
```python
def remove_integration_connection(manager: IntegrationManager, connection_id: str) -> bool:
```

**参数**：
- `manager` (IntegrationManager): 要移除连接的集成管理器对象。
- `connection_id` (str): 要移除的连接ID。

**返回值**：
- `bool`: 移除是否成功，True表示成功，False表示失败。

**异常**：
- `ValueError`: 如果集成管理器对象无效或连接ID无效。
- `KeyError`: 如果找不到指定ID的连接。
- `RuntimeError`: 如果移除连接过程中发生错误。

**示例**：
```python
from integration import remove_integration_connection

# 移除连接
success = remove_integration_connection(manager, "connection-123456")
if success:
    print("Connection removed successfully")
else:
    print("Failed to remove connection")
```

#### 4.2.3 `get_integration_connection`

获取集成连接的信息。

**函数签名**：
```python
def get_integration_connection(manager: IntegrationManager, connection_id: str) -> Dict[str, Any]:
```

**参数**：
- `manager` (IntegrationManager): 要获取连接的集成管理器对象。
- `connection_id` (str): 要获取的连接ID。

**返回值**：
- `Dict[str, Any]`: 集成连接的信息，包括连接类型、源组件、目标组件、状态、参数等。

**异常**：
- `ValueError`: 如果集成管理器对象无效或连接ID无效。
- `KeyError`: 如果找不到指定ID的连接。

**示例**：
```python
from integration import get_integration_connection

# 获取连接信息
info = get_integration_connection(manager, "connection-123456")
print(info)
# 输出:
# {
#     "id": "connection-123456",
#     "connection_type": "data_flow",
#     "source_component_id": "morphism_system",
#     "target_component_id": "meta_cognitive_system",
#     "status": "active",
#     "parameters": {
#         "data_format": "tensor",
#         "buffer_size": 1000,
#         "timeout": 30
#     },
#     "created_at": "..."
# }
```

#### 4.2.4 `list_integration_connections`

列出集成管理器中的所有连接。

**函数签名**：
```python
def list_integration_connections(manager: IntegrationManager, 
                                filter_criteria: Dict[str, Any] = None) -> List[Dict[str, Any]]:
```

**参数**：
- `manager` (IntegrationManager): 要列出连接的集成管理器对象。
- `filter_criteria` (Dict[str, Any], 可选): 过滤条件，用于筛选连接。默认为None，表示不过滤。

**返回值**：
- `List[Dict[str, Any]]`: 连接信息列表，每个元素包含连接的ID、类型、源组件、目标组件、状态等信息。

**异常**：
- `ValueError`: 如果集成管理器对象无效或过滤条件无效。

**示例**：
```python
from integration import list_integration_connections

# 列出所有连接
connections = list_integration_connections(manager)
for connection in connections:
    print(f"ID: {connection['id']}, Type: {connection['connection_type']}, Source: {connection['source_component_id']}, Target: {connection['target_component_id']}, Status: {connection['status']}")

# 列出特定类型的连接
data_flow_connections = list_integration_connections(manager, {"connection_type": "data_flow"})
for connection in data_flow_connections:
    print(f"ID: {connection['id']}, Source: {connection['source_component_id']}, Target: {connection['target_component_id']}, Status: {connection['status']}")
```

### 4.3 集成流程管理

#### 4.3.1 `start_integration`

启动集成流程，使组件开始协同工作。

**函数签名**：
```python
def start_integration(manager: IntegrationManager) -> bool:
```

**参数**：
- `manager` (IntegrationManager): 要启动集成的集成管理器对象。

**返回值**：
- `bool`: 启动是否成功，True表示成功，False表示失败。

**异常**：
- `ValueError`: 如果集成管理器对象无效。
- `RuntimeError`: 如果启动集成过程中发生错误，如组件不可用、连接失败等。

**示例**：
```python
from integration import start_integration

# 启动集成
success = start_integration(manager)
if success:
    print("Integration started successfully")
else:
    print("Failed to start integration")
```

#### 4.3.2 `stop_integration`

停止集成流程，使组件停止协同工作。

**函数签名**：
```python
def stop_integration(manager: IntegrationManager) -> bool:
```

**参数**：
- `manager` (IntegrationManager): 要停止集成的集成管理器对象。

**返回值**：
- `bool`: 停止是否成功，True表示成功，False表示失败。

**异常**：
- `ValueError`: 如果集成管理器对象无效。
- `RuntimeError`: 如果停止集成过程中发生错误。

**示例**：
```python
from integration import stop_integration

# 停止集成
success = stop_integration(manager)
if success:
    print("Integration stopped successfully")
else:
    print("Failed to stop integration")
```

#### 4.3.3 `restart_integration`

重启集成流程，先停止再启动。

**函数签名**：
```python
def restart_integration(manager: IntegrationManager) -> bool:
```

**参数**：
- `manager` (IntegrationManager): 要重启集成的集成管理器对象。

**返回值**：
- `bool`: 重启是否成功，True表示成功，False表示失败。

**异常**：
- `ValueError`: 如果集成管理器对象无效。
- `RuntimeError`: 如果重启集成过程中发生错误，如组件不可用、连接失败等。

**示例**：
```python
from integration import restart_integration

# 重启集成
success = restart_integration(manager)
if success:
    print("Integration restarted successfully")
else:
    print("Failed to restart integration")
```

#### 4.3.4 `get_integration_status`

获取集成流程的状态。

**函数签名**：
```python
def get_integration_status(manager: IntegrationManager) -> Dict[str, Any]:
```

**参数**：
- `manager` (IntegrationManager): 要获取状态的集成管理器对象。

**返回值**：
- `Dict[str, Any]`: 集成流程的状态，包括整体状态、组件状态、连接状态等。

**异常**：
- `ValueError`: 如果集成管理器对象无效。

**示例**：
```python
from integration import get_integration_status

# 获取集成状态
status = get_integration_status(manager)
print(status)
# 输出:
# {
#     "overall_status": "running",
#     "components": {
#         "morphism_system": "running",
#         "meta_cognitive_system": "running",
#         ...
#     },
#     "connections": {
#         "connection-123456": "active",
#         "connection-234567": "active",
#         ...
#     },
#     "errors": [],
#     "warnings": [],
#     "last_updated": "..."
# }
```

## 5. 集成服务接口

集成服务接口提供了访问和使用集成服务的功能，这些服务提供了特定的集成功能，如数据转换、协议转换等。

### 5.1 数据转换服务

#### 5.1.1 `create_data_transformer`

创建一个新的数据转换器，用于在不同数据格式之间进行转换。

**函数签名**：
```python
def create_data_transformer(source_format: str, 
                           target_format: str, 
                           parameters: Dict[str, Any] = None) -> DataTransformer:
```

**参数**：
- `source_format` (str): 源数据格式，如"json"、"xml"、"tensor"等。
- `target_format` (str): 目标数据格式，如"json"、"xml"、"tensor"等。
- `parameters` (Dict[str, Any], 可选): 转换器的参数，用于配置转换器的行为。默认为None。

**返回值**：
- `DataTransformer`: 创建的数据转换器对象。

**异常**：
- `ValueError`: 如果源格式无效、目标格式无效或参数无效。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from integration import create_data_transformer

# 创建JSON到XML的转换器
json_to_xml = create_data_transformer("json", "xml", {
    "root_element": "data",
    "array_item_name": "item",
    "pretty_print": True
})

# 创建张量到JSON的转换器
tensor_to_json = create_data_transformer("tensor", "json", {
    "include_shape": True,
    "include_dtype": True,
    "compact": False
})
```

#### 5.1.2 `transform_data`

使用数据转换器转换数据。

**函数签名**：
```python
def transform_data(transformer: DataTransformer, data: Any) -> Any:
```

**参数**：
- `transformer` (DataTransformer): 要使用的数据转换器对象。
- `data` (Any): 要转换的数据。

**返回值**：
- `Any`: 转换后的数据。

**异常**：
- `ValueError`: 如果数据转换器对象无效或数据无效。
- `TypeError`: 如果数据类型不正确。
- `RuntimeError`: 如果转换过程中发生错误，如格式不兼容等。

**示例**：
```python
from integration import transform_data

# 转换JSON到XML
json_data = {"name": "John", "age": 30, "skills": ["Python", "JavaScript"]}
xml_data = transform_data(json_to_xml, json_data)
print(xml_data)
# 输出:
# <?xml version="1.0" encoding="UTF-8"?>
# <data>
#   <name>John</name>
#   <age>30</age>
#   <skills>
#     <item>Python</item>
#     <item>JavaScript</item>
#   </skills>
# </data>

# 转换张量到JSON
import numpy as np
tensor_data = np.array([[1, 2, 3], [4, 5, 6]])
json_data = transform_data(tensor_to_json, tensor_data)
print(json_data)
# 输出:
# {
#   "data": [[1, 2, 3], [4, 5, 6]],
#   "shape": [2, 3],
#   "dtype": "int64"
# }
```

### 5.2 协议转换服务

#### 5.2.1 `create_protocol_converter`

创建一个新的协议转换器，用于在不同通信协议之间进行转换。

**函数签名**：
```python
def create_protocol_converter(source_protocol: str, 
                             target_protocol: str, 
                             parameters: Dict[str, Any] = None) -> ProtocolConverter:
```

**参数**：
- `source_protocol` (str): 源协议，如"http"、"mqtt"、"grpc"等。
- `target_protocol` (str): 目标协议，如"http"、"mqtt"、"grpc"等。
- `parameters` (Dict[str, Any], 可选): 转换器的参数，用于配置转换器的行为。默认为None。

**返回值**：
- `ProtocolConverter`: 创建的协议转换器对象。

**异常**：
- `ValueError`: 如果源协议无效、目标协议无效或参数无效。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from integration import create_protocol_converter

# 创建HTTP到MQTT的转换器
http_to_mqtt = create_protocol_converter("http", "mqtt", {
    "mqtt_broker": "mqtt://broker.example.com",
    "mqtt_topic": "data/from_http",
    "qos": 1
})

# 创建MQTT到gRPC的转换器
mqtt_to_grpc = create_protocol_converter("mqtt", "grpc", {
    "mqtt_broker": "mqtt://broker.example.com",
    "mqtt_topic": "data/from_mqtt",
    "grpc_server": "grpc://server.example.com",
    "grpc_service": "DataService",
    "grpc_method": "SendData"
})
```

#### 5.2.2 `convert_protocol`

使用协议转换器转换协议消息。

**函数签名**：
```python
def convert_protocol(converter: ProtocolConverter, 
                    message: Any, 
                    headers: Dict[str, Any] = None) -> Tuple[Any, Dict[str, Any]]:
```

**参数**：
- `converter` (ProtocolConverter): 要使用的协议转换器对象。
- `message` (Any): 要转换的消息。
- `headers` (Dict[str, Any], 可选): 消息的头信息，用于提供额外的协议信息。默认为None。

**返回值**：
- `Tuple[Any, Dict[str, Any]]`: 转换后的消息和头信息。

**异常**：
- `ValueError`: 如果协议转换器对象无效、消息无效或头信息无效。
- `TypeError`: 如果消息类型不正确。
- `RuntimeError`: 如果转换过程中发生错误，如协议不兼容等。

**示例**：
```python
from integration import convert_protocol

# 转换HTTP到MQTT
http_message = "Hello, MQTT!"
http_headers = {"Content-Type": "text/plain", "Authorization": "Bearer token123"}
mqtt_message, mqtt_headers = convert_protocol(http_to_mqtt, http_message, http_headers)
print(mqtt_message)  # 输出: "Hello, MQTT!"
print(mqtt_headers)  # 输出: {"topic": "data/from_http", "qos": 1, "retain": False}

# 转换MQTT到gRPC
mqtt_message = "Hello, gRPC!"
mqtt_headers = {"topic": "data/from_mqtt", "qos": 1, "retain": False}
grpc_message, grpc_headers = convert_protocol(mqtt_to_grpc, mqtt_message, mqtt_headers)
print(grpc_message)  # 输出: {"data": "Hello, gRPC!"}
print(grpc_headers)  # 输出: {"service": "DataService", "method": "SendData"}
```

### 5.3 服务协调接口

#### 5.3.1 `create_service_coordinator`

创建一个新的服务协调器，用于协调多个服务的交互。

**函数签名**：
```python
def create_service_coordinator(parameters: Dict[str, Any] = None) -> ServiceCoordinator:
```

**参数**：
- `parameters` (Dict[str, Any], 可选): 协调器的参数，用于配置协调器的行为。默认为None。

**返回值**：
- `ServiceCoordinator`: 创建的服务协调器对象。

**异常**：
- `ValueError`: 如果参数无效。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from integration import create_service_coordinator

# 创建服务协调器
coordinator = create_service_coordinator({
    "timeout": 30,
    "retry_count": 3,
    "retry_delay": 5,
    "parallel_execution": True
})
```

#### 5.3.2 `register_service`

向服务协调器注册服务。

**函数签名**：
```python
def register_service(coordinator: ServiceCoordinator, 
                    service_type: str, 
                    service: Any, 
                    name: str = None, 
                    parameters: Dict[str, Any] = None) -> str:
```

**参数**：
- `coordinator` (ServiceCoordinator): 要注册服务的服务协调器对象。
- `service_type` (str): 服务类型，如"data_transformer"、"protocol_converter"、"api_gateway"等。
- `service` (Any): 要注册的服务对象。
- `name` (str, 可选): 服务的注册名称，如果为None则使用服务的ID。默认为None。
- `parameters` (Dict[str, Any], 可选): 服务的参数，用于配置服务的行为。默认为None。

**返回值**：
- `str`: 服务的注册ID，可用于后续查询和使用服务。

**异常**：
- `ValueError`: 如果服务协调器对象无效、服务类型无效、服务对象无效或名称已被使用。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from integration import register_service

# 注册数据转换器
service_id = register_service(coordinator, "data_transformer", json_to_xml, "json_to_xml", {
    "priority": "high",
    "cache_enabled": True,
    "cache_size": 100
})
print(service_id)  # 输出: "service-123456"

# 注册协议转换器
service_id = register_service(coordinator, "protocol_converter", http_to_mqtt, "http_to_mqtt", {
    "priority": "medium",
    "async_mode": True,
    "buffer_size": 1000
})
print(service_id)  # 输出: "service-234567"
```

#### 5.3.3 `create_service_pipeline`

创建一个服务管道，将多个服务连接起来形成处理流程。

**函数签名**：
```python
def create_service_pipeline(coordinator: ServiceCoordinator, 
                           service_ids: List[str], 
                           name: str = None, 
                           parameters: Dict[str, Any] = None) -> str:
```

**参数**：
- `coordinator` (ServiceCoordinator): 要创建管道的服务协调器对象。
- `service_ids` (List[str]): 要连接的服务ID或名称列表，按照处理顺序排列。
- `name` (str, 可选): 管道的名称，如果为None则自动生成。默认为None。
- `parameters` (Dict[str, Any], 可选): 管道的参数，用于配置管道的行为。默认为None。

**返回值**：
- `str`: 创建的管道ID。

**异常**：
- `ValueError`: 如果服务协调器对象无效、服务ID无效、名称已被使用或参数无效。
- `KeyError`: 如果找不到指定ID或名称的服务。
- `RuntimeError`: 如果创建管道过程中发生错误，如服务不兼容等。

**示例**：
```python
from integration import create_service_pipeline

# 创建服务管道
pipeline_id = create_service_pipeline(coordinator, ["json_to_xml", "http_to_mqtt"], "json_to_mqtt_pipeline", {
    "parallel_execution": False,
    "error_handling": "continue",
    "timeout": 60
})
print(pipeline_id)  # 输出: "pipeline-123456"
```

#### 5.3.4 `execute_service_pipeline`

执行服务管道，处理输入数据并返回结果。

**函数签名**：
```python
def execute_service_pipeline(coordinator: ServiceCoordinator, 
                            pipeline_id: str, 
                            input_data: Any, 
                            parameters: Dict[str, Any] = None) -> Any:
```

**参数**：
- `coordinator` (ServiceCoordinator): 要执行管道的服务协调器对象。
- `pipeline_id` (str): 要执行的管道ID或名称。
- `input_data` (Any): 管道的输入数据。
- `parameters` (Dict[str, Any], 可选): 执行的参数，用于配置执行过程。默认为None。

**返回值**：
- `Any`: 管道的执行结果。

**异常**：
- `ValueError`: 如果服务协调器对象无效、管道ID无效、输入数据无效或参数无效。
- `KeyError`: 如果找不到指定ID或名称的管道。
- `RuntimeError`: 如果执行管道过程中发生错误，如服务失败等。

**示例**：
```python
from integration import execute_service_pipeline

# 执行服务管道
input_data = {"name": "John", "age": 30, "skills": ["Python", "JavaScript"]}
result = execute_service_pipeline(coordinator, "json_to_mqtt_pipeline", input_data, {
    "timeout": 30,
    "debug": True
})
print(result)  # 输出: 管道处理后的结果
```
