# 超融态思维引擎系统API文档 - 第2部分：态射系统API（高级接口）

## 3. 态射组合接口

态射组合接口提供了组合多个态射的功能，使用户能够构建复杂的态射网络，实现更强大的变换和映射功能。

### 3.1 创建态射组合

#### 3.1.1 `create_composition`

创建一个新的态射组合对象。

**函数签名**：
```python
def create_composition(composition_type: str, morphisms: List[DynamicMorphism], 
                      strategy: CompositionStrategy = None, 
                      parameters: Dict[str, Any] = None) -> MorphismComposition:
```

**参数**：
- `composition_type` (str): 组合类型，如"sequential"、"parallel"、"hierarchical"等。
- `morphisms` (List[DynamicMorphism]): 要组合的态射列表。
- `strategy` (CompositionStrategy, 可选): 组合策略对象，如果为None则根据组合类型自动创建。默认为None。
- `parameters` (Dict[str, Any], 可选): 组合的参数，用于配置组合的行为。默认为None。

**返回值**：
- `MorphismComposition`: 创建的态射组合对象。

**异常**：
- `ValueError`: 如果参数无效，如无效的组合类型、空的态射列表等。
- `TypeError`: 如果参数类型不正确。
- `RuntimeError`: 如果态射不兼容，无法组合。

**示例**：
```python
from morphism import create_morphism, create_composition, MorphismDomain, MorphismCodomain

# 创建定义域和值域
domain1 = MorphismDomain("domain1", 3)
codomain1 = MorphismCodomain("codomain1", 3)
domain2 = MorphismDomain("domain2", 3)
codomain2 = MorphismCodomain("codomain2", 3)

# 创建态射
morphism1 = create_morphism("linear", domain1, codomain1, lambda x: x * 2)
morphism2 = create_morphism("linear", domain2, codomain2, lambda x: x + 1)

# 创建顺序组合
sequential_composition = create_composition("sequential", [morphism1, morphism2])

# 创建并行组合
parallel_composition = create_composition("parallel", [morphism1, morphism2])
```

#### 3.1.2 `create_composition_strategy`

创建一个新的态射组合策略对象。

**函数签名**：
```python
def create_composition_strategy(strategy_type: str, parameters: Dict[str, Any] = None) -> CompositionStrategy:
```

**参数**：
- `strategy_type` (str): 策略类型，如"sequential"、"parallel"、"hierarchical"等。
- `parameters` (Dict[str, Any], 可选): 策略的参数，用于配置策略的行为。默认为None。

**返回值**：
- `CompositionStrategy`: 创建的组合策略对象。

**异常**：
- `ValueError`: 如果参数无效，如无效的策略类型等。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from morphism import create_composition_strategy

# 创建顺序组合策略
sequential_strategy = create_composition_strategy("sequential")

# 创建并行组合策略
parallel_strategy = create_composition_strategy("parallel", {"combine_method": "sum"})

# 创建层次组合策略
hierarchical_strategy = create_composition_strategy("hierarchical", {"levels": 3})
```

### 3.2 态射组合操作

#### 3.2.1 `apply_composition`

应用态射组合，将输入数据映射为输出数据。

**函数签名**：
```python
def apply_composition(composition: MorphismComposition, input_data: Any, 
                     parameters: Dict[str, Any] = None) -> Any:
```

**参数**：
- `composition` (MorphismComposition): 要应用的态射组合对象。
- `input_data` (Any): 输入数据，应该符合组合的输入要求。
- `parameters` (Dict[str, Any], 可选): 应用组合的参数，用于配置应用过程。默认为None。

**返回值**：
- `Any`: 应用组合后的输出数据，符合组合的输出要求。

**异常**：
- `ValueError`: 如果输入数据不符合组合的输入要求。
- `RuntimeError`: 如果应用组合过程中发生错误。

**示例**：
```python
from morphism import apply_composition

# 应用顺序组合
input_data = [1, 2, 3]
output_data = apply_composition(sequential_composition, input_data)
print(output_data)  # 输出: [3, 5, 7] (先乘以2，再加1)

# 应用并行组合
input_data = [1, 2, 3]
output_data = apply_composition(parallel_composition, input_data)
print(output_data)  # 输出: [[2, 4, 6], [2, 3, 4]] (分别应用两个态射)
```

#### 3.2.2 `get_composition_info`

获取态射组合的信息，包括组合类型、组合策略、组成态射、参数等。

**函数签名**：
```python
def get_composition_info(composition: MorphismComposition) -> Dict[str, Any]:
```

**参数**：
- `composition` (MorphismComposition): 要获取信息的态射组合对象。

**返回值**：
- `Dict[str, Any]`: 态射组合的信息，包括组合类型、组合策略、组成态射、参数等。

**异常**：
- `ValueError`: 如果态射组合对象无效。

**示例**：
```python
from morphism import get_composition_info

# 获取组合信息
info = get_composition_info(sequential_composition)
print(info)
# 输出:
# {
#     "composition_type": "sequential",
#     "strategy": {
#         "type": "sequential",
#         "parameters": {}
#     },
#     "morphisms": [
#         {"id": "...", "type": "linear", ...},
#         {"id": "...", "type": "linear", ...}
#     ],
#     "parameters": {}
# }
```

#### 3.2.3 `update_composition`

更新态射组合的参数、策略或组成态射。

**函数签名**：
```python
def update_composition(composition: MorphismComposition, morphisms: List[DynamicMorphism] = None, 
                      strategy: CompositionStrategy = None, 
                      parameters: Dict[str, Any] = None) -> MorphismComposition:
```

**参数**：
- `composition` (MorphismComposition): 要更新的态射组合对象。
- `morphisms` (List[DynamicMorphism], 可选): 新的组成态射列表，如果为None则保持原有态射。默认为None。
- `strategy` (CompositionStrategy, 可选): 新的组合策略，如果为None则保持原有策略。默认为None。
- `parameters` (Dict[str, Any], 可选): 新的参数，如果为None则保持原有参数。默认为None。

**返回值**：
- `MorphismComposition`: 更新后的态射组合对象。

**异常**：
- `ValueError`: 如果态射组合对象无效或参数无效。
- `TypeError`: 如果参数类型不正确。
- `RuntimeError`: 如果态射不兼容，无法组合。

**示例**：
```python
from morphism import update_composition

# 创建新的态射
morphism3 = create_morphism("linear", domain2, codomain2, lambda x: x * 3)

# 更新组合的态射
updated_composition = update_composition(sequential_composition, [morphism1, morphism3])

# 应用更新后的组合
input_data = [1, 2, 3]
output_data = apply_composition(updated_composition, input_data)
print(output_data)  # 输出: [6, 12, 18] (先乘以2，再乘以3)
```

## 4. 态射反馈接口

态射反馈接口提供了创建和管理态射反馈的功能，使用户能够构建带有反馈机制的态射系统，实现自适应和自我调节的功能。

### 4.1 创建态射反馈

#### 4.1.1 `create_feedback`

创建一个新的态射反馈对象。

**函数签名**：
```python
def create_feedback(feedback_type: str, morphism: DynamicMorphism, 
                   strategy: FeedbackStrategy = None, 
                   parameters: Dict[str, Any] = None) -> MorphismFeedback:
```

**参数**：
- `feedback_type` (str): 反馈类型，如"direct"、"gradient"、"adaptive"等。
- `morphism` (DynamicMorphism): 要添加反馈的态射对象。
- `strategy` (FeedbackStrategy, 可选): 反馈策略对象，如果为None则根据反馈类型自动创建。默认为None。
- `parameters` (Dict[str, Any], 可选): 反馈的参数，用于配置反馈的行为。默认为None。

**返回值**：
- `MorphismFeedback`: 创建的态射反馈对象。

**异常**：
- `ValueError`: 如果参数无效，如无效的反馈类型等。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from morphism import create_feedback

# 创建直接反馈
direct_feedback = create_feedback("direct", morphism1)

# 创建梯度反馈
gradient_feedback = create_feedback("gradient", morphism1, parameters={"learning_rate": 0.01})

# 创建自适应反馈
adaptive_feedback = create_feedback("adaptive", morphism1, parameters={"adaptation_rate": 0.1})
```

#### 4.1.2 `create_feedback_strategy`

创建一个新的态射反馈策略对象。

**函数签名**：
```python
def create_feedback_strategy(strategy_type: str, parameters: Dict[str, Any] = None) -> FeedbackStrategy:
```

**参数**：
- `strategy_type` (str): 策略类型，如"direct"、"gradient"、"adaptive"等。
- `parameters` (Dict[str, Any], 可选): 策略的参数，用于配置策略的行为。默认为None。

**返回值**：
- `FeedbackStrategy`: 创建的反馈策略对象。

**异常**：
- `ValueError`: 如果参数无效，如无效的策略类型等。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from morphism import create_feedback_strategy

# 创建直接反馈策略
direct_strategy = create_feedback_strategy("direct")

# 创建梯度反馈策略
gradient_strategy = create_feedback_strategy("gradient", {"learning_rate": 0.01})

# 创建自适应反馈策略
adaptive_strategy = create_feedback_strategy("adaptive", {"adaptation_rate": 0.1})
```

### 4.2 态射反馈操作

#### 4.2.1 `apply_feedback`

应用态射反馈，将输入数据映射为输出数据，并根据反馈调整态射。

**函数签名**：
```python
def apply_feedback(feedback: MorphismFeedback, input_data: Any, 
                  target_data: Any = None, 
                  parameters: Dict[str, Any] = None) -> Tuple[Any, Dict[str, Any]]:
```

**参数**：
- `feedback` (MorphismFeedback): 要应用的态射反馈对象。
- `input_data` (Any): 输入数据，应该符合态射的输入要求。
- `target_data` (Any, 可选): 目标数据，用于计算反馈信号。如果为None，则使用输入数据作为目标。默认为None。
- `parameters` (Dict[str, Any], 可选): 应用反馈的参数，用于配置应用过程。默认为None。

**返回值**：
- `Tuple[Any, Dict[str, Any]]`: 应用反馈后的输出数据和反馈信息，包括反馈信号、调整量等。

**异常**：
- `ValueError`: 如果输入数据或目标数据不符合要求。
- `RuntimeError`: 如果应用反馈过程中发生错误。

**示例**：
```python
from morphism import apply_feedback

# 应用直接反馈
input_data = [1, 2, 3]
target_data = [2, 4, 6]
output_data, feedback_info = apply_feedback(direct_feedback, input_data, target_data)
print(output_data)  # 输出: [2, 4, 6] (应用态射后的结果)
print(feedback_info)  # 输出: {"feedback_signal": [0, 0, 0], "adjustment": {...}}

# 应用梯度反馈
input_data = [1, 2, 3]
target_data = [2, 4, 6]
output_data, feedback_info = apply_feedback(gradient_feedback, input_data, target_data)
print(output_data)  # 输出: [2, 4, 6] (应用态射后的结果)
print(feedback_info)  # 输出: {"feedback_signal": [0, 0, 0], "gradient": [...], "adjustment": {...}}
```

#### 4.2.2 `get_feedback_info`

获取态射反馈的信息，包括反馈类型、反馈策略、态射、参数等。

**函数签名**：
```python
def get_feedback_info(feedback: MorphismFeedback) -> Dict[str, Any]:
```

**参数**：
- `feedback` (MorphismFeedback): 要获取信息的态射反馈对象。

**返回值**：
- `Dict[str, Any]`: 态射反馈的信息，包括反馈类型、反馈策略、态射、参数等。

**异常**：
- `ValueError`: 如果态射反馈对象无效。

**示例**：
```python
from morphism import get_feedback_info

# 获取反馈信息
info = get_feedback_info(direct_feedback)
print(info)
# 输出:
# {
#     "feedback_type": "direct",
#     "strategy": {
#         "type": "direct",
#         "parameters": {}
#     },
#     "morphism": {"id": "...", "type": "linear", ...},
#     "parameters": {}
# }
```

#### 4.2.3 `update_feedback`

更新态射反馈的参数、策略或态射。

**函数签名**：
```python
def update_feedback(feedback: MorphismFeedback, morphism: DynamicMorphism = None, 
                   strategy: FeedbackStrategy = None, 
                   parameters: Dict[str, Any] = None) -> MorphismFeedback:
```

**参数**：
- `feedback` (MorphismFeedback): 要更新的态射反馈对象。
- `morphism` (DynamicMorphism, 可选): 新的态射，如果为None则保持原有态射。默认为None。
- `strategy` (FeedbackStrategy, 可选): 新的反馈策略，如果为None则保持原有策略。默认为None。
- `parameters` (Dict[str, Any], 可选): 新的参数，如果为None则保持原有参数。默认为None。

**返回值**：
- `MorphismFeedback`: 更新后的态射反馈对象。

**异常**：
- `ValueError`: 如果态射反馈对象无效或参数无效。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from morphism import update_feedback

# 更新反馈的参数
updated_feedback = update_feedback(gradient_feedback, parameters={"learning_rate": 0.02})

# 应用更新后的反馈
input_data = [1, 2, 3]
target_data = [2, 4, 6]
output_data, feedback_info = apply_feedback(updated_feedback, input_data, target_data)
print(output_data)  # 输出: [2, 4, 6] (应用态射后的结果)
print(feedback_info)  # 输出: {"feedback_signal": [0, 0, 0], "gradient": [...], "adjustment": {...}}
```

## 5. 态射演化接口

态射演化接口提供了创建和管理态射演化的功能，使用户能够构建能够自我演化和适应的态射系统，实现更高层次的智能和适应性。

### 5.1 创建态射演化

#### 5.1.1 `create_evolution`

创建一个新的态射演化对象。

**函数签名**：
```python
def create_evolution(evolution_type: str, morphism: DynamicMorphism, 
                    strategy: EvolutionStrategy = None, 
                    parameters: Dict[str, Any] = None) -> MorphismEvolution:
```

**参数**：
- `evolution_type` (str): 演化类型，如"gradual"、"disruptive"、"adaptive"等。
- `morphism` (DynamicMorphism): 要演化的态射对象。
- `strategy` (EvolutionStrategy, 可选): 演化策略对象，如果为None则根据演化类型自动创建。默认为None。
- `parameters` (Dict[str, Any], 可选): 演化的参数，用于配置演化的行为。默认为None。

**返回值**：
- `MorphismEvolution`: 创建的态射演化对象。

**异常**：
- `ValueError`: 如果参数无效，如无效的演化类型等。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from morphism import create_evolution

# 创建渐进演化
gradual_evolution = create_evolution("gradual", morphism1)

# 创建突变演化
disruptive_evolution = create_evolution("disruptive", morphism1, parameters={"mutation_rate": 0.1})

# 创建自适应演化
adaptive_evolution = create_evolution("adaptive", morphism1, parameters={"adaptation_rate": 0.1})
```

#### 5.1.2 `create_evolution_strategy`

创建一个新的态射演化策略对象。

**函数签名**：
```python
def create_evolution_strategy(strategy_type: str, parameters: Dict[str, Any] = None) -> EvolutionStrategy:
```

**参数**：
- `strategy_type` (str): 策略类型，如"gradual"、"disruptive"、"adaptive"等。
- `parameters` (Dict[str, Any], 可选): 策略的参数，用于配置策略的行为。默认为None。

**返回值**：
- `EvolutionStrategy`: 创建的演化策略对象。

**异常**：
- `ValueError`: 如果参数无效，如无效的策略类型等。
- `TypeError`: 如果参数类型不正确。

**示例**：
```python
from morphism import create_evolution_strategy

# 创建渐进演化策略
gradual_strategy = create_evolution_strategy("gradual")

# 创建突变演化策略
disruptive_strategy = create_evolution_strategy("disruptive", {"mutation_rate": 0.1})

# 创建自适应演化策略
adaptive_strategy = create_evolution_strategy("adaptive", {"adaptation_rate": 0.1})
```
