#!/bin/bash

# 编译Rust库脚本
# 该脚本用于编译超越态思维引擎的Rust库

set -e  # 遇到错误立即退出

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 设置日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Rust工具链
check_rust() {
    log_info "检查Rust工具链..."
    if ! command -v rustc &> /dev/null; then
        log_error "未找到Rust编译器，请安装Rust工具链"
        exit 1
    fi
    
    if ! command -v cargo &> /dev/null; then
        log_error "未找到Cargo，请安装Rust工具链"
        exit 1
    fi
    
    rustc_version=$(rustc --version)
    cargo_version=$(cargo --version)
    
    log_success "Rust工具链检查通过"
    log_info "Rust版本: $rustc_version"
    log_info "Cargo版本: $cargo_version"
}

# 创建目录
create_directories() {
    log_info "创建目录..."
    mkdir -p lib
    log_success "目录创建完成"
}

# 编译主Rust库
build_main_lib() {
    log_info "编译主Rust库..."
    cd "$PROJECT_ROOT"
    cargo build --release
    
    if [ $? -eq 0 ]; then
        log_success "主Rust库编译成功"
    else
        log_error "主Rust库编译失败"
        exit 1
    fi
}

# 编译rust_operators库
build_rust_operators() {
    log_info "编译rust_operators库..."
    cd "$PROJECT_ROOT/rust_operators"
    cargo build --release
    
    if [ $? -eq 0 ]; then
        log_success "rust_operators库编译成功"
    else
        log_error "rust_operators库编译失败"
        exit 1
    fi
}

# 编译错误处理算子
build_error_handler() {
    log_info "编译错误处理算子..."
    cd "$PROJECT_ROOT/src/interfaces"
    
    # 检查Cargo.toml是否存在
    if [ ! -f "Cargo.toml" ]; then
        log_warning "错误处理算子的Cargo.toml不存在，跳过编译"
        return
    fi
    
    cargo build --release
    
    if [ $? -eq 0 ]; then
        log_success "错误处理算子编译成功"
    else
        log_error "错误处理算子编译失败"
        exit 1
    fi
}

# 编译变换算子
build_transform_operator() {
    log_info "编译变换算子..."
    cd "$PROJECT_ROOT/src/operators/transform"
    
    # 检查Cargo.toml是否存在
    if [ ! -f "Cargo.toml" ]; then
        log_warning "变换算子的Cargo.toml不存在，跳过编译"
        return
    fi
    
    cargo build --release
    
    if [ $? -eq 0 ]; then
        log_success "变换算子编译成功"
    else
        log_error "变换算子编译失败"
        exit 1
    fi
}

# 编译演化算子
build_evolution_operator() {
    log_info "编译演化算子..."
    cd "$PROJECT_ROOT/src/operators/evolution"
    
    # 检查Cargo.toml是否存在
    if [ ! -f "Cargo.toml" ]; then
        log_warning "演化算子的Cargo.toml不存在，跳过编译"
        return
    fi
    
    cargo build --release
    
    if [ $? -eq 0 ]; then
        log_success "演化算子编译成功"
    else
        log_error "演化算子编译失败"
        exit 1
    fi
}

# 复制库文件
copy_libraries() {
    log_info "复制库文件..."
    
    # 创建lib目录
    mkdir -p "$PROJECT_ROOT/lib"
    
    # 复制主库
    if [ -f "$PROJECT_ROOT/target/release/libtte.so" ]; then
        cp "$PROJECT_ROOT/target/release/libtte.so" "$PROJECT_ROOT/lib/"
        log_success "复制主库成功"
    else
        log_warning "主库文件不存在，跳过复制"
    fi
    
    # 复制rust_operators库
    if [ -f "$PROJECT_ROOT/rust_operators/target/release/librust_operators.so" ]; then
        cp "$PROJECT_ROOT/rust_operators/target/release/librust_operators.so" "$PROJECT_ROOT/lib/"
        log_success "复制rust_operators库成功"
    else
        log_warning "rust_operators库文件不存在，跳过复制"
    fi
    
    # 复制错误处理算子
    if [ -f "$PROJECT_ROOT/src/interfaces/target/release/liboperator_interface_core.so" ]; then
        cp "$PROJECT_ROOT/src/interfaces/target/release/liboperator_interface_core.so" "$PROJECT_ROOT/lib/"
        log_success "复制错误处理算子成功"
    else
        log_warning "错误处理算子文件不存在，跳过复制"
    fi
    
    # 复制变换算子
    if [ -f "$PROJECT_ROOT/src/operators/transform/target/release/libtransform_operator_core.so" ]; then
        cp "$PROJECT_ROOT/src/operators/transform/target/release/libtransform_operator_core.so" "$PROJECT_ROOT/lib/"
        log_success "复制变换算子成功"
    else
        log_warning "变换算子文件不存在，跳过复制"
    fi
    
    # 复制演化算子
    if [ -f "$PROJECT_ROOT/src/operators/evolution/target/release/libevolution_operator_core.so" ]; then
        cp "$PROJECT_ROOT/src/operators/evolution/target/release/libevolution_operator_core.so" "$PROJECT_ROOT/lib/"
        log_success "复制演化算子成功"
    else
        log_warning "演化算子文件不存在，跳过复制"
    fi
}

# 设置环境变量
setup_environment() {
    log_info "设置环境变量..."
    
    # 创建环境变量设置脚本
    cat > "$PROJECT_ROOT/setup_env.sh" << EOF
#!/bin/bash

# 设置环境变量
export RUST_LIBRARY_PATH="$PROJECT_ROOT/lib"
export LD_LIBRARY_PATH="\$LD_LIBRARY_PATH:$PROJECT_ROOT/lib"
export PYTHONPATH="\$PYTHONPATH:$PROJECT_ROOT"

echo "环境变量设置完成"
echo "RUST_LIBRARY_PATH: \$RUST_LIBRARY_PATH"
echo "LD_LIBRARY_PATH: \$LD_LIBRARY_PATH"
echo "PYTHONPATH: \$PYTHONPATH"
EOF
    
    chmod +x "$PROJECT_ROOT/setup_env.sh"
    log_success "环境变量设置脚本创建成功"
}

# 主函数
main() {
    # 设置项目根目录
    PROJECT_ROOT=$(pwd)
    
    log_info "开始编译Rust库..."
    log_info "项目根目录: $PROJECT_ROOT"
    
    # 检查Rust工具链
    check_rust
    
    # 创建目录
    create_directories
    
    # 编译主Rust库
    build_main_lib
    
    # 编译rust_operators库
    build_rust_operators
    
    # 编译错误处理算子
    build_error_handler
    
    # 编译变换算子
    build_transform_operator
    
    # 编译演化算子
    build_evolution_operator
    
    # 复制库文件
    copy_libraries
    
    # 设置环境变量
    setup_environment
    
    log_success "Rust库编译完成"
}

# 执行主函数
main
