"""
干涉算子测试脚本

这个脚本测试干涉算子的基本功能。
"""

import numpy as np
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath('.'))

# 定义一个简单的接口类
class OperatorInterface:
    def __init__(self, **kwargs):
        pass

    def apply(self, input_data, **kwargs):
        pass

    def get_metadata(self):
        pass

    def is_compatible_with(self, other_operator):
        pass

    def get_performance_metrics(self):
        pass

    def compose(self, other_operator):
        pass

    def get_parameters(self):
        pass

    def set_parameters(self, parameters):
        pass

    def to_rust(self):
        pass

    def get_complexity(self):
        pass

# 导入要测试的模块
from test_interference import InterferenceOperator, InterferencePatternGenerator


def test_interference_operator():
    """测试InterferenceOperator算子"""
    print("\n测试InterferenceOperator算子...")

    # 创建算子
    operator = InterferenceOperator(dimension=4, pattern_type="quantum")
    print(f"创建算子: {operator}")

    # 测试元数据
    metadata = operator.get_metadata()
    print(f"元数据: {metadata}")

    # 测试应用到向量
    vector = np.array([1.0, 2.0, 3.0, 4.0])
    result_vector = operator.apply(vector)
    print(f"应用到向量，输入形状: {vector.shape}, 输出形状: {result_vector.shape}")
    print(f"输入向量: {vector}")
    print(f"输出向量: {result_vector}")

    # 测试应用到矩阵
    matrix = np.array([[1.0, 2.0, 3.0, 4.0], [5.0, 6.0, 7.0, 8.0]])
    result_matrix = operator.apply(matrix)
    print(f"应用到矩阵，输入形状: {matrix.shape}, 输出形状: {result_matrix.shape}")

    # 测试融合
    data1 = np.array([1.0, 2.0, 3.0, 4.0])
    data2 = np.array([4.0, 3.0, 2.0, 1.0])
    fused_data = operator.fuse(data1, data2)
    print(f"融合数据，输入形状: {data1.shape} 和 {data2.shape}, 输出形状: {fused_data.shape}")
    print(f"输入数据1: {data1}")
    print(f"输入数据2: {data2}")
    print(f"融合结果: {fused_data}")

    print("InterferenceOperator测试完成")


def test_pattern_generator():
    """测试InterferencePatternGenerator"""
    print("\n测试InterferencePatternGenerator...")

    # 创建生成器
    generator = InterferencePatternGenerator(dimension=8)
    print(f"创建生成器: {generator}")

    # 生成量子干涉模式
    quantum_pattern = generator.generate_quantum_pattern()
    print(f"量子干涉模式形状: {quantum_pattern.shape}")

    # 生成全息干涉模式
    holographic_pattern = generator.generate_holographic_pattern()
    print(f"全息干涉模式形状: {holographic_pattern.shape}")

    # 生成分形干涉模式
    fractal_pattern = generator.generate_fractal_pattern()
    print(f"分形干涉模式形状: {fractal_pattern.shape}")

    # 生成波动干涉模式
    wave_pattern = generator.generate_wave_pattern()
    print(f"波动干涉模式形状: {wave_pattern.shape}")

    # 组合模式
    patterns = [quantum_pattern, holographic_pattern, fractal_pattern]
    combined_pattern = generator.combine_patterns(patterns)
    print(f"组合模式形状: {combined_pattern.shape}")

    # 分析模式
    analysis = generator.analyze_pattern(quantum_pattern)
    print(f"模式分析结果: {list(analysis.keys())}")

    print("InterferencePatternGenerator测试完成")


# 简化版的InterferenceAnalyzer
class InterferenceAnalyzer:
    """干涉分析工具类"""

    def __init__(self, visualization_enabled=True):
        """初始化InterferenceAnalyzer"""
        self.visualization_enabled = visualization_enabled

    def analyze_interference_pattern(self, pattern):
        """分析干涉模式"""
        # 计算幅度和相位
        amplitude = np.abs(pattern)
        phase = np.angle(pattern)

        # 返回分析结果
        return {
            "amplitude_mean": np.mean(amplitude),
            "amplitude_std": np.std(amplitude),
            "phase_mean": np.mean(phase),
            "phase_std": np.std(phase),
            "energy": np.sum(amplitude**2)
        }

    def analyze_interference_result(self, original, result):
        """分析干涉结果"""
        # 计算差异
        diff = result - original

        # 返回分析结果
        return {
            "original_mean": np.mean(np.abs(original)),
            "result_mean": np.mean(np.abs(result)),
            "diff_mean": np.mean(np.abs(diff)),
            "correlation": np.corrcoef(original, result)[0, 1] if original.size > 1 else 1.0
        }

    def analyze_multiple_interference_results(self, results):
        """分析多个干涉结果"""
        # 计算均值和标准差
        mean_result = np.mean(results, axis=0)
        std_result = np.std(results, axis=0)

        # 返回分析结果
        return {
            "num_results": len(results),
            "mean_result_mean": np.mean(mean_result),
            "std_result_mean": np.mean(std_result)
        }

def test_interference_analyzer():
    """测试InterferenceAnalyzer"""
    print("\n测试InterferenceAnalyzer...")

    # 创建分析器
    analyzer = InterferenceAnalyzer(visualization_enabled=False)
    print(f"创建分析器: {analyzer}")

    # 创建测试数据
    dimension = 8
    pattern = np.random.randn(dimension, dimension) + 1j * np.random.randn(dimension, dimension)
    original = np.random.randn(dimension)
    result = np.random.randn(dimension)

    # 分析干涉模式
    pattern_analysis = analyzer.analyze_interference_pattern(pattern)
    print(f"干涉模式分析结果: {list(pattern_analysis.keys())}")

    # 分析干涉结果
    result_analysis = analyzer.analyze_interference_result(original, result)
    print(f"干涉结果分析结果: {list(result_analysis.keys())}")

    # 分析多个干涉结果
    results = [np.random.randn(dimension) for _ in range(5)]
    multiple_analysis = analyzer.analyze_multiple_interference_results(results)
    print(f"多个干涉结果分析结果: {list(multiple_analysis.keys())}")

    print("InterferenceAnalyzer测试完成")


def main():
    """主函数"""
    print("开始测试干涉算子...")

    test_interference_operator()
    test_pattern_generator()
    test_interference_analyzer()

    print("\n所有测试完成")


if __name__ == "__main__":
    main()
