"""
设置Python路径

确保项目的根目录在Python的导入路径中。
"""

import os
import sys

# 获取项目根目录的绝对路径
project_root = os.path.abspath(os.path.dirname(__file__))

# 将项目根目录添加到Python的导入路径中
if project_root not in sys.path:
    sys.path.insert(0, project_root)
    print(f"已将项目根目录 {project_root} 添加到Python路径中")
else:
    print(f"项目根目录 {project_root} 已经在Python路径中")

# 打印Python路径
print("\nPython路径:")
for path in sys.path:
    print(f"  {path}")
