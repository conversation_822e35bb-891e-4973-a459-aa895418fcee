"""
测试ParallelTransport的Rust实现
"""

import numpy as np
import sys
import os
import importlib.util
import importlib.machinery

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 尝试导入Rust实现
RUST_AVAILABLE = False
try:
    # 查找differential_geometry模块
    module_paths = [
        os.path.join(os.path.dirname(__file__), 'src/operators/differential_geometry/differential_geometry.so'),
        os.path.join(os.path.dirname(__file__), 'target/debug/libdifferential_geometry.so'),
        os.path.join(os.path.dirname(__file__), 'target/release/libdifferential_geometry.so')
    ]
    
    # 查找存在的模块路径
    module_path = None
    for path in module_paths:
        if os.path.exists(path):
            module_path = path
            break
    
    if module_path:
        # 加载模块
        spec = importlib.util.spec_from_file_location("differential_geometry", module_path)
        differential_geometry = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(differential_geometry)
        
        # 从模块中导入类和函数
        ParallelTransport = differential_geometry.ParallelTransport
        register_transport = differential_geometry.register_transport
        RUST_AVAILABLE = True
        print(f"成功导入Rust实现，模块路径: {module_path}")
    else:
        print(f"Rust模块文件不存在")
        raise ImportError("Rust模块文件不存在")
except (ImportError, AttributeError) as e:
    print(f"无法导入differential_geometry模块，可能是Rust实现未正确构建: {e}")
    
    # 使用Python实现
    from src.operators.differential_geometry.transport import ParallelTransport
    
    def register_transport(*args, **kwargs):
        return "dummy_id"

def main():
    """主函数"""
    # 检查Rust实现是否可用
    print(f"Rust实现可用: {RUST_AVAILABLE}")
    
    if not RUST_AVAILABLE:
        print("Rust实现不可用，使用Python实现")
    
    # 创建ParallelTransport算子
    transport = ParallelTransport(
        dimension=3,
        fiber_dimension=2,
        step_size=0.01,
        adaptive_steps=True,
        error_tolerance=1e-6
    )
    
    # 打印算子信息
    print(f"算子: {transport}")
    
    # 创建测试路径（圆形路径）
    t = np.linspace(0, 2*np.pi, 100)
    path = np.zeros((100, 3))
    path[:, 0] = np.cos(t)
    path[:, 1] = np.sin(t)
    
    # 创建初始向量
    vector = np.array([1.0, 0.0])
    
    # 传输向量
    result = transport.apply((path, vector), method="rk4")
    print(f"向量传输结果形状: {result.shape}")
    print(f"初始向量: {vector}")
    print(f"最终向量: {result[-1]}")
    
    # 计算全息变换（如果Rust实现可用）
    if RUST_AVAILABLE and hasattr(transport, 'compute_holonomy'):
        holonomy = transport.compute_holonomy(path, vector, method="rk4")
        print(f"全息变换结果: {holonomy}")
    else:
        print("Python实现不支持compute_holonomy方法")
    
    # 计算曲率（如果Rust实现可用）
    if RUST_AVAILABLE and hasattr(transport, 'compute_curvature'):
        point = np.array([1.0, 0.0, 0.0])
        curvature = transport.compute_curvature(point)
        print(f"曲率形状: {curvature.shape}")
    else:
        print("Python实现不支持compute_curvature方法")
    
    # 计算挠率（如果Rust实现可用）
    if RUST_AVAILABLE and hasattr(transport, 'compute_torsion'):
        point = np.array([1.0, 0.0, 0.0])
        torsion = transport.compute_torsion(point)
        print(f"挠率形状: {torsion.shape}")
    else:
        print("Python实现不支持compute_torsion方法")
    
    # 计算测地线（如果Rust实现可用）
    if RUST_AVAILABLE and hasattr(transport, 'compute_geodesic'):
        start_point = np.array([1.0, 0.0, 0.0])
        end_point = np.array([0.0, 1.0, 0.0])
        geodesic = transport.compute_geodesic(start_point, end_point, 10)
        print(f"测地线形状: {geodesic.shape}")
    else:
        print("Python实现不支持compute_geodesic方法")
    
    # 获取算子元数据
    metadata = transport.get_metadata()
    print(f"算子元数据: {metadata}")
    
    # 获取性能指标（如果Rust实现可用）
    if hasattr(transport, 'get_performance_metrics'):
        performance_metrics = transport.get_performance_metrics()
        print(f"性能指标: {performance_metrics}")
    else:
        print("Python实现不支持get_performance_metrics方法")
    
    # 获取算子复杂度信息（如果Rust实现可用）
    if hasattr(transport, 'get_complexity'):
        complexity = transport.get_complexity()
        print(f"算子复杂度信息: {complexity}")
    else:
        print("Python实现不支持get_complexity方法")
    
    print("测试完成")

if __name__ == "__main__":
    main()
