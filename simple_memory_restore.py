#!/usr/bin/env python3
"""
简化版记忆恢复工具
通过MCP工具直接恢复记忆，避免底层API调用问题
"""

import sys
import os
import pandas as pd
import pyarrow.parquet as pq
from pathlib import Path
import json
import logging

# 添加项目路径
sys.path.insert(0, '.')

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def find_backup_files():
    """查找所有备份的记忆文件"""
    backup_files = []
    aqfh_dir = Path.home() / '.aqfh'
    
    for parquet_file in aqfh_dir.rglob('memories.parquet'):
        # 排除当前统一系统的文件
        if 'unified_consciousness' not in str(parquet_file):
            backup_files.append(parquet_file)
    
    # 按文件大小排序，大文件优先
    backup_files.sort(key=lambda x: x.stat().st_size, reverse=True)
    return backup_files

def load_memories_from_parquet(file_path):
    """从parquet文件加载记忆"""
    try:
        logger.info(f"正在加载记忆文件: {file_path}")
        table = pq.read_table(file_path)
        df = table.to_pandas()
        
        memories = []
        for _, row in df.iterrows():
            try:
                # 尝试解析记忆数据
                memory_data = {
                    'content': row.get('content', ''),
                    'content_type': row.get('content_type', 'conversation'),
                    'importance': float(row.get('importance', 0.5)),
                    'tags': [],
                    'context': {}
                }
                
                # 处理标签
                if 'tags_json' in row and pd.notna(row['tags_json']):
                    try:
                        memory_data['tags'] = json.loads(row['tags_json'])
                    except:
                        memory_data['tags'] = []
                elif 'tags' in row and pd.notna(row['tags']):
                    if isinstance(row['tags'], str):
                        try:
                            memory_data['tags'] = json.loads(row['tags'])
                        except:
                            memory_data['tags'] = [row['tags']]
                    elif isinstance(row['tags'], list):
                        memory_data['tags'] = row['tags']
                
                # 处理上下文
                if 'context_json' in row and pd.notna(row['context_json']):
                    try:
                        memory_data['context'] = json.loads(row['context_json'])
                    except:
                        memory_data['context'] = {}
                elif 'context' in row and pd.notna(row['context']):
                    if isinstance(row['context'], str):
                        try:
                            memory_data['context'] = json.loads(row['context'])
                        except:
                            memory_data['context'] = {'raw': row['context']}
                    elif isinstance(row['context'], dict):
                        memory_data['context'] = row['context']
                
                # 过滤空内容
                if memory_data['content'] and len(memory_data['content'].strip()) > 10:
                    memories.append(memory_data)
                
            except Exception as e:
                logger.warning(f"解析记忆行失败: {e}")
                continue
        
        logger.info(f"成功加载 {len(memories)} 条有效记忆")
        return memories
        
    except Exception as e:
        logger.error(f"加载记忆文件失败: {e}")
        return []

def main():
    """主函数"""
    print("🔄 简化版AQFH记忆恢复工具启动")
    print("=" * 50)
    
    # 查找备份文件
    backup_files = find_backup_files()
    print(f"🔍 发现 {len(backup_files)} 个备份文件")
    
    if not backup_files:
        print("❌ 未找到备份文件")
        return
    
    # 处理最大的备份文件
    backup_file = backup_files[0]
    print(f"\n📁 处理最大备份文件: {backup_file.name}")
    print(f"   路径: {backup_file}")
    print(f"   大小: {backup_file.stat().st_size} bytes")
    
    # 加载记忆
    memories = load_memories_from_parquet(backup_file)
    if not memories:
        print("   ⚠️ 未找到有效记忆")
        return
    
    print(f"📊 找到 {len(memories)} 条有效记忆")
    
    # 显示前5条记忆的预览
    print("\n📋 记忆预览:")
    for i, memory in enumerate(memories[:5], 1):
        content_preview = memory['content'][:80] + "..." if len(memory['content']) > 80 else memory['content']
        print(f"   {i}. [{memory['content_type']}] {content_preview}")
    
    # 询问用户是否继续
    print(f"\n❓ 是否要恢复这 {len(memories)} 条记忆？")
    print("   注意：这将通过MCP工具逐条恢复记忆")
    
    # 自动继续（在脚本中）
    print("✅ 开始恢复记忆...")
    
    # 这里我们输出恢复命令，让用户手动执行
    print("\n🔧 请在另一个终端中执行以下命令来恢复记忆：")
    print("=" * 50)
    
    for i, memory in enumerate(memories[:10], 1):  # 只显示前10条作为示例
        content_escaped = memory['content'].replace("'", "\\'").replace('"', '\\"')
        tags_str = str(memory['tags']).replace("'", '"')
        context_str = str(memory['context']).replace("'", '"')
        
        print(f"# 恢复记忆 {i}")
        print("python3 -c \"")
        print("import sys")
        print("sys.path.insert(0, '.')")
        print("from aqfh_unified_consciousness_mcp import unified_system")
        print(f"memory_id = unified_system.save_memory(")
        print(f"    content='{content_escaped[:200]}...',")
        print(f"    content_type='{memory['content_type']}',")
        print(f"    importance={memory['importance']},")
        print(f"    tags={tags_str},")
        print(f"    context={context_str}")
        print(")")
        print("print(f'恢复记忆: {memory_id}' if memory_id else '恢复失败')")
        print("\"")
        print()
    
    print("=" * 50)
    print(f"💡 提示：以上只显示了前10条记忆的恢复命令")
    print(f"📊 总共有 {len(memories)} 条记忆需要恢复")
    print("🔄 你可以根据需要修改和执行这些命令")

if __name__ == "__main__":
    main()
