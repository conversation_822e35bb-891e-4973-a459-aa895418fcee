"""
SIMD测试

测试SIMD优化的性能。
"""

import os
import sys
import time
import numpy as np

def test_numpy_performance():
    """测试NumPy性能"""
    print("测试NumPy性能...")
    
    # 向量点积
    size = 10000
    iterations = 100
    
    a = np.random.rand(size)
    b = np.random.rand(size)
    
    start_time = time.time()
    for _ in range(iterations):
        np.dot(a, b)
    numpy_time = time.time() - start_time
    
    print(f"向量点积 (大小: {size}, {iterations}次迭代): {numpy_time:.6f}秒")
    print(f"每次迭代时间: {numpy_time / iterations:.6f}秒")
    
    # 矩阵乘法
    size = 500
    iterations = 10
    
    A = np.random.rand(size, size)
    B = np.random.rand(size, size)
    
    start_time = time.time()
    for _ in range(iterations):
        np.matmul(A, B)
    numpy_time = time.time() - start_time
    
    print(f"矩阵乘法 (大小: {size}x{size}, {iterations}次迭代): {numpy_time:.6f}秒")
    print(f"每次迭代时间: {numpy_time / iterations:.6f}秒")

if __name__ == '__main__':
    print("开始SIMD测试...")
    
    # 测试NumPy性能
    test_numpy_performance()
    
    print("SIMD测试完成！")
