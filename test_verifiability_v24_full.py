#!/usr/bin/env python3
"""
测试PyO3 0.24+兼容版本的可验证性算子
"""

import time
import json
from pprint import pprint

# 导入算子
import verifiability_v24

def test_basic_functionality():
    """测试基本功能"""
    print("测试基本功能...")
    
    # 创建可验证性算子
    operator = verifiability_v24.VerifiabilityOperator(
        method="hybrid",
        threshold=0.7,
        timeout_ms=30000,
        parallel_threshold=3
    )
    
    # 创建验证属性
    properties = [
        {
            "name": "一致性",
            "description": "模型在相似输入上产生相似输出",
            "property_type": "robustness",
            "expression": "forall x, y. |x - y| < epsilon -> |f(x) - f(y)| < delta",
            "importance": 0.8
        },
        {
            "name": "单调性",
            "description": "输入增加时输出不减少",
            "property_type": "monotonicity",
            "expression": "forall x, y. x <= y -> f(x) <= f(y)",
            "importance": 0.6
        }
    ]
    
    # 测试输入数据
    input_data = {
        "model_output": {
            "prediction": "positive",
            "confidence": 0.85,
            "features": {
                "text_length": 500,
                "sentiment_score": 0.75,
                "topic_relevance": 0.9
            }
        },
        "model_type": "classification",
        "domain": "sentiment_analysis"
    }
    
    # 应用算子
    result = operator.apply(input_data, properties=properties)
    
    # 检查结果
    assert "id" in result
    assert "properties" in result
    assert "overall_score" in result
    assert len(result["properties"]) == 2
    
    # 打印结果
    print("验证报告ID:", result["id"])
    print("总体验证分数:", result["overall_score"])
    print("验证方法:", result["method"])
    
    print("\n验证属性:")
    for prop in result["properties"]:
        print(f"  - {prop['name']} ({prop['property_type']})")
        print(f"    描述: {prop['description']}")
        print(f"    表达式: {prop['expression']}")
        print(f"    重要性: {prop['importance']}")
        print(f"    验证状态: {prop['result']['status']}")
        print(f"    验证分数: {prop['result']['score']}")
        print()
    
    print("基本功能测试通过！")

def test_verification_methods():
    """测试不同的验证方法"""
    print("\n测试不同的验证方法...")
    
    # 验证方法
    methods = ["formal", "statistical", "empirical", "adversarial", "hybrid"]
    
    # 创建验证属性
    properties = [
        {
            "name": "一致性",
            "description": "模型在相似输入上产生相似输出",
            "property_type": "robustness",
            "expression": "forall x, y. |x - y| < epsilon -> |f(x) - f(y)| < delta",
            "importance": 0.8
        }
    ]
    
    # 测试输入数据
    input_data = {
        "model_output": {
            "prediction": "positive",
            "confidence": 0.85,
            "features": {
                "text_length": 500,
                "sentiment_score": 0.75,
                "topic_relevance": 0.9
            }
        }
    }
    
    for method in methods:
        print(f"\n验证方法: {method}")
        
        # 创建可验证性算子
        operator = verifiability_v24.VerifiabilityOperator(
            method=method,
            threshold=0.7,
            timeout_ms=30000
        )
        
        # 应用算子
        result = operator.apply(input_data, properties=properties)
        
        # 打印结果
        print(f"总体验证分数: {result['overall_score']}")
        print(f"验证状态: {result['properties'][0]['result']['status']}")
        
        # 打印验证详情
        details = result["properties"][0]["result"]["details"]
        for key, value in details.items():
            print(f"  {key}: {value}")
    
    print("\n不同验证方法测试通过！")

def test_performance():
    """测试性能"""
    print("\n测试性能...")
    
    # 创建可验证性算子
    operator = verifiability_v24.VerifiabilityOperator()
    
    # 创建验证属性
    properties = [
        {
            "name": "一致性",
            "description": "模型在相似输入上产生相似输出",
            "property_type": "robustness",
            "expression": "forall x, y. |x - y| < epsilon -> |f(x) - f(y)| < delta",
            "importance": 0.8
        },
        {
            "name": "单调性",
            "description": "输入增加时输出不减少",
            "property_type": "monotonicity",
            "expression": "forall x, y. x <= y -> f(x) <= f(y)",
            "importance": 0.6
        }
    ]
    
    # 测试输入数据
    input_data = {
        "model_output": {
            "prediction": "positive",
            "confidence": 0.85,
            "features": {
                "text_length": 500,
                "sentiment_score": 0.75,
                "topic_relevance": 0.9
            }
        }
    }
    
    # 测试缓存
    print("测试缓存...")
    
    # 第一次调用
    start_time = time.time()
    operator.apply(input_data, properties=properties)
    first_call_time = time.time() - start_time
    print(f"第一次调用时间: {first_call_time:.6f}秒")
    
    # 第二次调用（应该使用缓存）
    start_time = time.time()
    operator.apply(input_data, properties=properties)
    second_call_time = time.time() - start_time
    print(f"第二次调用时间: {second_call_time:.6f}秒")
    
    # 获取缓存统计信息
    cache_stats = operator.get_cache_stats()
    print("缓存统计信息:")
    pprint(cache_stats)
    
    # 清理缓存
    operator.clear_cache()
    
    # 获取性能指标
    metrics = operator.get_performance_metrics()
    print("性能指标:")
    pprint(metrics)
    
    print("性能测试通过！")

def test_batch_processing():
    """测试批量处理"""
    print("\n测试批量处理...")
    
    # 创建可验证性算子
    operator = verifiability_v24.VerifiabilityOperator(
        parallel_threshold=2
    )
    
    # 创建验证属性
    properties = [
        {
            "name": "一致性",
            "description": "模型在相似输入上产生相似输出",
            "property_type": "robustness",
            "expression": "forall x, y. |x - y| < epsilon -> |f(x) - f(y)| < delta",
            "importance": 0.8
        }
    ]
    
    # 测试输入数据
    input_batch = [
        {
            "model_output": {
                "prediction": "positive",
                "confidence": 0.85,
                "features": {
                    "text_length": 500,
                    "sentiment_score": 0.75,
                    "topic_relevance": 0.9
                }
            }
        },
        {
            "model_output": {
                "prediction": "negative",
                "confidence": 0.65,
                "features": {
                    "text_length": 300,
                    "sentiment_score": 0.25,
                    "topic_relevance": 0.7
                }
            }
        },
        {
            "model_output": {
                "prediction": "neutral",
                "confidence": 0.55,
                "features": {
                    "text_length": 400,
                    "sentiment_score": 0.5,
                    "topic_relevance": 0.8
                }
            }
        }
    ]
    
    # 应用算子
    try:
        results = operator.apply_batch(input_batch, properties=properties)
        
        # 检查结果
        assert len(results) == len(input_batch)
        
        # 打印结果
        for i, result in enumerate(results):
            print(f"\n批量处理结果 {i+1}:")
            print(f"预测: {input_batch[i]['model_output']['prediction']}")
            print(f"总体验证分数: {result['overall_score']}")
            print(f"验证状态: {result['properties'][0]['result']['status']}")
        
        print("批量处理测试通过！")
    except Exception as e:
        print(f"批量处理测试失败: {e}")

def test_metadata():
    """测试元数据"""
    print("\n测试元数据...")
    
    # 创建可验证性算子
    operator = verifiability_v24.VerifiabilityOperator()
    
    # 获取算子元数据
    metadata = operator.get_metadata()
    print("算子元数据:")
    pprint(metadata)
    
    # 获取算子参数
    parameters = operator.get_parameters()
    print("\n算子参数:")
    pprint(parameters)
    
    # 设置算子参数
    new_parameters = {
        "method": "formal",
        "threshold": 0.8,
        "timeout_ms": 60000,
        "parallel_threshold": 4
    }
    
    operator.set_parameters(new_parameters)
    
    # 获取更新后的算子参数
    updated_parameters = operator.get_parameters()
    print("\n更新后的算子参数:")
    pprint(updated_parameters)
    
    print("元数据测试通过！")

def main():
    """主函数"""
    print("开始测试PyO3 0.24+兼容版本的可验证性算子...\n")
    
    # 测试基本功能
    test_basic_functionality()
    
    # 测试不同的验证方法
    test_verification_methods()
    
    # 测试性能
    test_performance()
    
    # 测试批量处理
    test_batch_processing()
    
    # 测试元数据
    test_metadata()
    
    print("\n所有测试通过！")

if __name__ == "__main__":
    main()
