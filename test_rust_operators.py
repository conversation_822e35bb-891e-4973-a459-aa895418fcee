#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Rust算子加载测试脚本
"""

import logging
import sys
import os
import importlib
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("rust_test")

def check_rust_module(module_name):
    """检查Rust模块是否可用"""
    try:
        module = importlib.import_module(module_name)
        return True, module
    except ImportError as e:
        return False, str(e)

def check_library_file(library_path):
    """检查库文件是否存在"""
    exists = os.path.exists(library_path)
    if exists:
        size = os.path.getsize(library_path)
        return exists, size
    return exists, 0

def main():
    """主函数"""
    print("Rust算子加载测试")
    print("=" * 50)

    # 检查环境变量
    print("\n1. 检查环境变量")
    rust_lib_path = os.environ.get("RUST_LIBRARY_PATH")
    print(f"RUST_LIBRARY_PATH: {rust_lib_path}")

    python_path = os.environ.get("PYTHONPATH")
    print(f"PYTHONPATH: {python_path}")

    ld_library_path = os.environ.get("LD_LIBRARY_PATH")
    print(f"LD_LIBRARY_PATH: {ld_library_path}")

    # 检查Rust模块
    print("\n2. 检查Rust模块")
    modules_to_check = [
        "rust_operators",
        "tte_operators",
        "nonlinear_interference",
        "fractal_routing",
        "game_scheduler",
        "transcendental_evolution"
    ]

    for module_name in modules_to_check:
        success, result = check_rust_module(module_name)
        if success:
            print(f"模块 {module_name} 可用")
            print(f"  - 版本: {getattr(result, '__version__', '未知')}")
            print(f"  - 路径: {getattr(result, '__file__', '未知')}")

            # 列出模块中的函数和类
            functions = []
            classes = []
            for name in dir(result):
                if name.startswith("_"):
                    continue

                attr = getattr(result, name)
                if callable(attr):
                    if isinstance(attr, type):
                        classes.append(name)
                    else:
                        functions.append(name)

            print(f"  - 函数: {', '.join(functions) if functions else '无'}")
            print(f"  - 类: {', '.join(classes) if classes else '无'}")
        else:
            print(f"模块 {module_name} 不可用: {result}")

    # 检查库文件
    print("\n3. 检查库文件")
    libraries_to_check = [
        "/home/<USER>/CascadeProjects/TTE/src/interfaces/target/release/liboperator_interface_core.so",
        "/home/<USER>/CascadeProjects/TTE/src/operators/transform/target/release/libtransform_operator_core.so",
        "/home/<USER>/CascadeProjects/TTE/src/operators/transform/target/release/libtransform_operator_optimized.so",
        "/home/<USER>/CascadeProjects/TTE/src/operators/transform/target/release/libtransform_operator_simd.so",
        "/home/<USER>/CascadeProjects/TTE/src/operators/transform/target/release/libtransform_operator_gpu.so",
        "/home/<USER>/CascadeProjects/TTE/src/operators/transform/target/release/libtransform_operator_distributed.so",
        "/home/<USER>/CascadeProjects/TTE/src/operators/evolution/target/release/libevolution_operator_core.so"
    ]

    for library_path in libraries_to_check:
        exists, size = check_library_file(library_path)
        if exists:
            print(f"库文件 {library_path} 存在，大小: {size} 字节")
        else:
            print(f"库文件 {library_path} 不存在")

    # 检查Rust绑定
    print("\n4. 检查Rust绑定")
    try:
        from src.rust_bindings import RUST_AVAILABLE
        print(f"Rust绑定可用: {RUST_AVAILABLE}")

        if RUST_AVAILABLE:
            from src.rust_bindings import add, multiply, Point
            print(f"基本函数和类: add, multiply, Point")
            print(f"add(1, 2) = {add(1, 2)}")
            print(f"multiply(3, 4) = {multiply(3, 4)}")

            p = Point(5, 6)
            print(f"Point(5, 6) = {p}")
    except Exception as e:
        print(f"检查Rust绑定时出错: {e}")
        traceback.print_exc()

    # 检查错误处理算子
    print("\n5. 检查错误处理算子")
    try:
        from src.rust_bindings.error_handler.operator import (
            get_error_handling_operator,
            RUST_AVAILABLE as ERROR_HANDLER_RUST_AVAILABLE
        )

        print(f"错误处理算子Rust实现可用: {ERROR_HANDLER_RUST_AVAILABLE}")

        operator = get_error_handling_operator()
        print(f"错误处理算子: {operator}")
    except Exception as e:
        print(f"检查错误处理算子时出错: {e}")
        traceback.print_exc()

    print("\n测试完成！")

if __name__ == '__main__':
    main()
