#!/usr/bin/env python3
"""
构建PyO3 0.24+兼容版本的解释算子
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def build_rust_extension(path: str, features: list = None) -> bool:
    """
    构建Rust扩展

    参数:
        path: Rust扩展的路径
        features: 要启用的特性列表

    返回:
        是否成功构建
    """
    logger.info(f"构建Rust扩展: {path}")

    try:
        # 切换到Rust扩展目录
        os.chdir(path)

        # 构建命令
        cmd = ["maturin", "develop", "--release"]

        # 添加特性
        if features:
            cmd.extend(["--features", ",".join(features)])

        # 构建Rust扩展
        result = subprocess.run(
            cmd,
            check=True,
            capture_output=True,
            text=True
        )

        logger.info(f"构建成功: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"构建失败: {e.stderr}")
        return False
    except Exception as e:
        logger.error(f"构建失败: {e}")
        return False

def main():
    """主函数"""
    # 获取项目根目录
    root_dir = Path(__file__).parent.absolute()

    # 构建解释算子的Rust实现
    explanation_path = root_dir / "rust_operators" / "explanation_v24"

    if not explanation_path.exists():
        logger.error(f"解释算子的Rust实现路径不存在: {explanation_path}")
        return

    # 保存当前目录
    current_dir = os.getcwd()

    try:
        # 构建PyO3 0.24+兼容版本的解释算子
        success = build_rust_extension(str(explanation_path))

        if success:
            logger.info("PyO3 0.24+兼容版本的解释算子构建成功")

            # 测试Rust扩展
            logger.info("测试Rust扩展...")
            try:
                # 创建测试脚本
                test_script = """
import explanation_v24

# 创建优化的多层次解释生成算子
operator = explanation_v24.OptimizedMultilevelExplanationOperator(
    tech_level=0.7,
    concept_level=0.5,
    analogy_level=0.3,
    parallel_threshold=3
)

# 测试输入数据
input_data = {
    "model_output": {
        "prediction": "positive",
        "confidence": 0.85,
        "features": {
            "text_length": 500,
            "sentiment_score": 0.75,
            "topic_relevance": 0.9
        }
    },
    "explanation_level": "all",
    "user_expertise": "intermediate"
}

# 应用算子
result = operator.apply(input_data)

# 检查结果
assert "technical" in result
assert "conceptual" in result
assert "analogy" in result
assert "fused" in result
assert "weights" in result

print("测试成功！")
"""

                # 保存测试脚本
                with open("test_explanation_v24.py", "w") as f:
                    f.write(test_script)

                # 运行测试脚本
                # 使用虚拟环境中的Python解释器
                venv_python = os.path.join(os.path.expanduser("~"), ".venv", "bin", "python")
                result = subprocess.run(
                    [venv_python, "-c", test_script],
                    check=True,
                    capture_output=True,
                    text=True
                )

                logger.info(f"测试成功: {result.stdout}")
            except subprocess.CalledProcessError as e:
                logger.error(f"测试失败: {e.stderr}")
            except Exception as e:
                logger.error(f"测试失败: {e}")
            finally:
                # 删除测试脚本
                if os.path.exists("test_explanation_v24.py"):
                    os.remove("test_explanation_v24.py")
        else:
            logger.error("PyO3 0.24+兼容版本的解释算子构建失败")
    finally:
        # 恢复当前目录
        os.chdir(current_dir)

if __name__ == "__main__":
    main()
