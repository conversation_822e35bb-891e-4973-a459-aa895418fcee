"""
持久同调分析算法直接测试

这是一个直接测试脚本，不依赖于项目的其他部分。
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Any, Dict, List, Optional, Tuple, Union, Callable
from abc import ABC, abstractmethod
import time
import itertools
from scipy.spatial.distance import pdist, squareform


# 定义算法接口
class AlgorithmInterface(ABC):
    """超越态算法接口基类"""
    
    @abstractmethod
    def __init__(self, **kwargs):
        """初始化算法"""
        pass
    
    @abstractmethod
    def compute(self, input_data: Any, **kwargs) -> Any:
        """执行算法计算"""
        pass
    
    @abstractmethod
    def get_metadata(self) -> Dict[str, Any]:
        """获取算法元数据"""
        pass
    
    @abstractmethod
    def is_compatible_with(self, other_algorithm: 'AlgorithmInterface') -> bool:
        """检查与其他算法的兼容性"""
        pass
    
    @abstractmethod
    def get_performance_metrics(self) -> Dict[str, float]:
        """获取性能指标"""
        pass


# 定义单纯复形构建器
class SimplicialComplexBuilder:
    """单纯复形构建器"""
    
    def __init__(self, max_dimension: int = 2, distance_metric: str = 'euclidean'):
        """初始化单纯复形构建器"""
        self.max_dimension = max_dimension
        self.distance_metric = distance_metric
    
    def build_complex(self, points: np.ndarray, max_radius: float, 
                     num_divisions: int) -> Tuple[List[Tuple[int, ...]], List[float]]:
        """构建单纯复形和过滤"""
        # 计算距离矩阵
        distance_matrix = self._compute_distance_matrix(points)
        
        # 生成过滤值
        filtration_values = np.linspace(0, max_radius, num_divisions)
        
        # 构建单纯复形
        simplicial_complex = self._build_vietoris_rips_complex(distance_matrix, max_radius)
        
        return simplicial_complex, filtration_values
    
    def _compute_distance_matrix(self, points: np.ndarray) -> np.ndarray:
        """计算距离矩阵"""
        # 使用scipy的pdist和squareform计算距离矩阵
        pairwise_distances = pdist(points, metric=self.distance_metric)
        distance_matrix = squareform(pairwise_distances)
        
        return distance_matrix
    
    def _build_vietoris_rips_complex(self, distance_matrix: np.ndarray, 
                                    max_radius: float) -> List[Tuple[int, ...]]:
        """构建Vietoris-Rips复形"""
        n_points = distance_matrix.shape[0]
        
        # 构建0-单纯形（顶点）
        simplices = [(i,) for i in range(n_points)]
        
        # 构建1-单纯形（边）
        for i in range(n_points):
            for j in range(i + 1, n_points):
                if distance_matrix[i, j] <= max_radius:
                    simplices.append((i, j))
        
        # 如果最大维度大于1，则构建更高维度的单纯形
        if self.max_dimension > 1:
            # 构建2-单纯形（三角形）
            for i in range(n_points):
                for j in range(i + 1, n_points):
                    if distance_matrix[i, j] > max_radius:
                        continue
                    for k in range(j + 1, n_points):
                        if (distance_matrix[i, k] <= max_radius and 
                            distance_matrix[j, k] <= max_radius):
                            simplices.append((i, j, k))
        
        return simplices


# 定义持久性计算器
class PersistenceCalculator:
    """持久性计算器"""
    
    def __init__(self, max_dimension: int = 2):
        """初始化持久性计算器"""
        self.max_dimension = max_dimension
    
    def compute_persistence(self, simplicial_complex: List[Tuple[int, ...]], 
                           filtration_values: List[float]) -> Dict[int, np.ndarray]:
        """计算持久同调"""
        # 为简化起见，我们只计算0维和1维的持久同调
        # 在实际应用中，应该使用更复杂的算法
        
        # 初始化持久图
        persistence_diagrams = {}
        
        # 计算0维持久同调（连通分量）
        persistence_diagrams[0] = self._compute_0d_persistence(simplicial_complex, filtration_values)
        
        # 如果最大维度大于0，则计算1维持久同调（环）
        if self.max_dimension > 0:
            persistence_diagrams[1] = self._compute_1d_persistence(simplicial_complex, filtration_values)
        
        return persistence_diagrams
    
    def _compute_0d_persistence(self, simplicial_complex: List[Tuple[int, ...]], 
                               filtration_values: List[float]) -> np.ndarray:
        """计算0维持久同调"""
        # 为简化起见，我们返回一些随机的持久点
        # 在实际应用中，应该使用更复杂的算法
        
        # 生成一些随机的持久点
        n_points = 5
        birth_values = np.random.uniform(0, filtration_values[-1] / 2, n_points)
        death_values = np.random.uniform(filtration_values[-1] / 2, filtration_values[-1], n_points)
        
        # 确保死亡值大于出生值
        for i in range(n_points):
            if death_values[i] <= birth_values[i]:
                death_values[i] = birth_values[i] + 0.1
        
        # 添加一个无限持久的点
        birth_values = np.append(birth_values, [0])
        death_values = np.append(death_values, [float('inf')])
        
        return np.column_stack([birth_values, death_values])
    
    def _compute_1d_persistence(self, simplicial_complex: List[Tuple[int, ...]], 
                               filtration_values: List[float]) -> np.ndarray:
        """计算1维持久同调"""
        # 为简化起见，我们返回一些随机的持久点
        # 在实际应用中，应该使用更复杂的算法
        
        # 生成一些随机的持久点
        n_points = 3
        birth_values = np.random.uniform(filtration_values[-1] / 4, filtration_values[-1] / 2, n_points)
        death_values = np.random.uniform(filtration_values[-1] / 2, filtration_values[-1], n_points)
        
        # 确保死亡值大于出生值
        for i in range(n_points):
            if death_values[i] <= birth_values[i]:
                death_values[i] = birth_values[i] + 0.1
        
        return np.column_stack([birth_values, death_values])


# 定义拓扑特征提取器
class TopologicalFeatureExtractor:
    """拓扑特征提取器"""
    
    def __init__(self, max_dimension: int = 2):
        """初始化拓扑特征提取器"""
        self.max_dimension = max_dimension
    
    def compute_betti_curves(self, persistence_diagrams: Dict[int, np.ndarray], 
                            num_divisions: int) -> Dict[int, np.ndarray]:
        """计算贝蒂曲线"""
        # 初始化贝蒂曲线
        betti_curves = {}
        
        # 找出所有持久图中的最大过滤值
        max_filtration = 0.0
        for dim, diagram in persistence_diagrams.items():
            if diagram.size > 0:
                max_filtration = max(max_filtration, np.max(diagram[:, 0]))
        
        # 生成过滤值序列
        filtration_values = np.linspace(0, max_filtration, num_divisions)
        
        # 计算每个维度的贝蒂曲线
        for dim in range(self.max_dimension + 1):
            if dim in persistence_diagrams:
                diagram = persistence_diagrams[dim]
                betti_curve = np.zeros(num_divisions)
                
                # 对于每个过滤值，计算贝蒂数
                for i, filtration in enumerate(filtration_values):
                    # 计算在当前过滤值下存活的同调类数量
                    alive = np.sum((diagram[:, 0] <= filtration) & 
                                   ((diagram[:, 1] > filtration) | np.isinf(diagram[:, 1])))
                    betti_curve[i] = alive
                
                betti_curves[dim] = betti_curve
            else:
                betti_curves[dim] = np.zeros(num_divisions)
        
        return betti_curves
    
    def extract_features(self, persistence_diagrams: Dict[int, np.ndarray], 
                        betti_curves: Dict[int, np.ndarray]) -> Dict[str, float]:
        """提取拓扑特征"""
        features = {}
        
        # 从持久图中提取特征
        for dim in range(self.max_dimension + 1):
            if dim in persistence_diagrams:
                diagram = persistence_diagrams[dim]
                
                # 计算持久点数量
                features[f'num_points_dim_{dim}'] = len(diagram)
                
                # 计算平均持久性
                if len(diagram) > 0:
                    persistence = diagram[:, 1] - diagram[:, 0]
                    persistence = persistence[~np.isinf(persistence)]  # 移除无限值
                    if len(persistence) > 0:
                        features[f'mean_persistence_dim_{dim}'] = np.mean(persistence)
                        features[f'max_persistence_dim_{dim}'] = np.max(persistence)
                    else:
                        features[f'mean_persistence_dim_{dim}'] = 0.0
                        features[f'max_persistence_dim_{dim}'] = 0.0
                else:
                    features[f'mean_persistence_dim_{dim}'] = 0.0
                    features[f'max_persistence_dim_{dim}'] = 0.0
                
                # 计算无限持久点数量
                if len(diagram) > 0:
                    features[f'num_inf_points_dim_{dim}'] = np.sum(np.isinf(diagram[:, 1]))
                else:
                    features[f'num_inf_points_dim_{dim}'] = 0
        
        # 从贝蒂曲线中提取特征
        for dim in range(self.max_dimension + 1):
            if dim in betti_curves:
                curve = betti_curves[dim]
                
                # 计算贝蒂曲线的统计特征
                features[f'mean_betti_dim_{dim}'] = np.mean(curve)
                features[f'max_betti_dim_{dim}'] = np.max(curve)
        
        return features


# 定义持久同调分析算法
class PersistentHomologyAnalyzer(AlgorithmInterface):
    """持久同调分析算法"""
    
    def __init__(self, max_dimension: int = 2, max_radius: float = 2.0,
                num_divisions: int = 20, distance_metric: str = 'euclidean',
                **kwargs):
        """初始化持久同调分析算法"""
        self.max_dimension = max_dimension
        self.max_radius = max_radius
        self.num_divisions = num_divisions
        self.distance_metric = distance_metric
        
        # 性能指标
        self._performance_metrics = {
            'total_time': 0.0,
            'complex_construction_time': 0.0,
            'persistence_calculation_time': 0.0,
            'feature_extraction_time': 0.0
        }
        
        # 创建辅助组件
        self.complex_builder = SimplicialComplexBuilder(
            max_dimension=max_dimension,
            distance_metric=distance_metric
        )
        
        self.persistence_calculator = PersistenceCalculator(
            max_dimension=max_dimension
        )
        
        self.feature_extractor = TopologicalFeatureExtractor(
            max_dimension=max_dimension
        )
    
    def compute(self, input_data: Union[np.ndarray, Dict[str, Any]], **kwargs) -> Dict[str, Any]:
        """执行持久同调分析"""
        # 记录开始时间
        start_time = time.time()
        
        # 解析输入数据
        points = self._parse_input(input_data)
        
        # 构建单纯复形
        complex_start_time = time.time()
        simplicial_complex, filtration_values = self.complex_builder.build_complex(
            points, self.max_radius, self.num_divisions
        )
        complex_end_time = time.time()
        
        # 更新性能指标
        self._performance_metrics['complex_construction_time'] = complex_end_time - complex_start_time
        
        # 计算持久性
        persistence_start_time = time.time()
        persistence_diagrams = self.persistence_calculator.compute_persistence(
            simplicial_complex, filtration_values
        )
        persistence_end_time = time.time()
        
        # 更新性能指标
        self._performance_metrics['persistence_calculation_time'] = persistence_end_time - persistence_start_time
        
        # 提取拓扑特征
        feature_start_time = time.time()
        betti_curves = self.feature_extractor.compute_betti_curves(
            persistence_diagrams, self.num_divisions
        )
        topological_features = self.feature_extractor.extract_features(
            persistence_diagrams, betti_curves
        )
        feature_end_time = time.time()
        
        # 更新性能指标
        self._performance_metrics['feature_extraction_time'] = feature_end_time - feature_start_time
        
        # 记录结束时间
        end_time = time.time()
        
        # 更新性能指标
        self._performance_metrics['total_time'] = end_time - start_time
        
        # 构建结果
        result = {
            'persistence_diagrams': persistence_diagrams,
            'betti_curves': betti_curves,
            'topological_features': topological_features,
            'performance': self.get_performance_metrics()
        }
        
        return result
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取算法元数据"""
        return {
            'name': 'PersistentHomologyAnalyzer',
            'version': '1.0.0',
            'description': '持久同调分析算法，用于分析数据的拓扑结构',
            'parameters': {
                'max_dimension': self.max_dimension,
                'max_radius': self.max_radius,
                'num_divisions': self.num_divisions,
                'distance_metric': self.distance_metric
            },
            'performance_metrics': self.get_performance_metrics()
        }
    
    def is_compatible_with(self, other_algorithm: AlgorithmInterface) -> bool:
        """检查与其他算法的兼容性"""
        # 检查其他算法是否是PersistentHomologyAnalyzer的实例
        if not isinstance(other_algorithm, PersistentHomologyAnalyzer):
            return False
        
        # 检查其他算法的参数是否与当前算法兼容
        other_metadata = other_algorithm.get_metadata()
        
        # 检查最大同调维度是否兼容
        if other_metadata['parameters']['max_dimension'] > self.max_dimension:
            return False
        
        return True
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """获取性能指标"""
        return self._performance_metrics
    
    def _parse_input(self, input_data: Union[np.ndarray, Dict[str, Any]]) -> np.ndarray:
        """解析输入数据"""
        if isinstance(input_data, np.ndarray):
            return input_data
        elif isinstance(input_data, dict) and 'points' in input_data:
            return input_data['points']
        else:
            raise ValueError("输入数据格式不正确，应为点云数据的numpy数组或包含'points'键的字典")


def create_circle_points(n_points=100, radius=1.0, noise=0.1):
    """创建圆形点云数据"""
    theta = np.linspace(0, 2 * np.pi, n_points)
    x = radius * np.cos(theta) + noise * np.random.randn(n_points)
    y = radius * np.sin(theta) + noise * np.random.randn(n_points)
    
    return np.column_stack([x, y])


def plot_point_cloud(points):
    """绘制点云数据"""
    plt.figure(figsize=(8, 6))
    plt.scatter(points[:, 0], points[:, 1], alpha=0.7)
    plt.xlabel('X')
    plt.ylabel('Y')
    plt.title('点云数据')
    plt.grid(True, alpha=0.3)
    plt.axis('equal')
    plt.show()


def plot_persistence_diagram(persistence_diagram, dim):
    """绘制持久图"""
    plt.figure(figsize=(8, 6))
    
    # 绘制对角线
    min_val = 0
    max_val = 0
    
    if persistence_diagram.size > 0:
        min_val = np.min(persistence_diagram[:, 0])
        max_val = np.max(persistence_diagram[:, 1][~np.isinf(persistence_diagram[:, 1])])
    
    diag_min = min_val - 0.1
    diag_max = max_val + 0.1
    
    plt.plot([diag_min, diag_max], [diag_min, diag_max], 'k--', alpha=0.5)
    
    # 绘制持久点
    if persistence_diagram.size > 0:
        # 有限持久点
        finite_points = persistence_diagram[~np.isinf(persistence_diagram[:, 1])]
        if finite_points.size > 0:
            plt.scatter(finite_points[:, 0], finite_points[:, 1], alpha=0.7)
        
        # 无限持久点
        inf_points = persistence_diagram[np.isinf(persistence_diagram[:, 1])]
        if inf_points.size > 0:
            plt.scatter(inf_points[:, 0], [diag_max] * len(inf_points), marker='^', alpha=0.7)
    
    plt.xlabel('出生')
    plt.ylabel('死亡')
    plt.title(f'维度{dim}持久图')
    plt.grid(True, alpha=0.3)
    plt.axis('equal')
    plt.show()


def plot_betti_curves(betti_curves, num_divisions):
    """绘制贝蒂曲线"""
    plt.figure(figsize=(10, 6))
    
    # 生成过滤值序列
    filtration_values = np.linspace(0, 2.0, num_divisions)
    
    # 绘制每个维度的贝蒂曲线
    for dim, curve in betti_curves.items():
        plt.plot(filtration_values, curve, label=f'维度 {dim}')
    
    plt.xlabel('过滤值')
    plt.ylabel('贝蒂数')
    plt.title('贝蒂曲线')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()


def main():
    """主函数"""
    # 设置随机种子，确保结果可重现
    np.random.seed(42)
    
    # 创建圆形点云数据
    points = create_circle_points(n_points=100, radius=1.0, noise=0.1)
    
    # 绘制点云数据
    plot_point_cloud(points)
    
    # 创建持久同调分析器
    analyzer = PersistentHomologyAnalyzer(
        max_dimension=2,
        max_radius=2.0,
        num_divisions=20,
        distance_metric='euclidean'
    )
    
    # 执行计算
    print("执行持久同调分析...")
    start_time = time.time()
    result = analyzer.compute(points)
    end_time = time.time()
    
    print(f"计算完成，耗时: {end_time - start_time:.2f}秒")
    
    # 提取结果
    persistence_diagrams = result['persistence_diagrams']
    betti_curves = result['betti_curves']
    topological_features = result['topological_features']
    performance = result['performance']
    
    # 打印性能指标
    print(f"性能指标:")
    print(f"  总时间: {performance['total_time']:.2f}秒")
    print(f"  复形构建时间: {performance['complex_construction_time']:.2f}秒")
    print(f"  持久性计算时间: {performance['persistence_calculation_time']:.2f}秒")
    print(f"  特征提取时间: {performance['feature_extraction_time']:.2f}秒")
    
    # 打印拓扑特征
    print(f"拓扑特征:")
    for dim in range(3):
        print(f"  维度{dim}:")
        print(f"    点数量: {topological_features.get(f'num_points_dim_{dim}', 0)}")
        print(f"    平均持久性: {topological_features.get(f'mean_persistence_dim_{dim}', 0):.4f}")
        print(f"    最大持久性: {topological_features.get(f'max_persistence_dim_{dim}', 0):.4f}")
        print(f"    无限持久点数量: {topological_features.get(f'num_inf_points_dim_{dim}', 0)}")
    
    # 绘制持久图
    for dim in range(3):
        if dim in persistence_diagrams:
            plot_persistence_diagram(persistence_diagrams[dim], dim)
    
    # 绘制贝蒂曲线
    plot_betti_curves(betti_curves, 20)
    
    print("测试成功完成！")


if __name__ == "__main__":
    main()
