[build-system]
requires = ["maturin>=1.4,<2.0"]
build-backend = "maturin"

[project]
name = "tte"
version = "0.1.0"
description = "Transcendental Thinking Engine"
requires-python = ">=3.7"
authors = [
    {name = "TTE Team"}
]
readme = "README.md"
license = {text = "MIT"}
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Programming Language :: Rust",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Development Status :: 3 - Alpha",
]
dependencies = [
    "numpy>=1.24.0",
    "scipy>=1.10.0",
    "networkx>=3.0",
    "pydantic>=2.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
]
distributed = [
    "ray>=2.6.0",
]
visualization = [
    "matplotlib>=3.7.0",
    "plotly>=5.14.0",
]

[tool.maturin]
python-source = "python"
features = ["pyo3/extension-module"]
module-name = "tte.core.rust_core"
binding = "pyo3"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"

[tool.black]
line-length = 88
target-version = ["py310", "py311", "py312", "py313"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 88
