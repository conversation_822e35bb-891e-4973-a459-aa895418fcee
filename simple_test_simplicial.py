"""
简单的单纯复形构建器测试脚本

这个脚本测试重构后的单纯复形构建器的基本功能。
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Any, Dict, List, Optional, Tuple, Union, FrozenSet


# 导入辅助函数
def preprocess_input(input_data: Any, metric: str = 'euclidean') -> np.ndarray:
    """预处理输入数据，转换为距离矩阵"""
    # 如果输入已经是距离矩阵
    if isinstance(input_data, np.ndarray) and len(input_data.shape) == 2 and input_data.shape[0] == input_data.shape[1]:
        # 检查是否是有效的距离矩阵
        if np.allclose(input_data, input_data.T) and np.all(np.diag(input_data) == 0) and np.all(input_data >= 0):
            return input_data
    
    # 如果输入是点云数据
    if isinstance(input_data, np.ndarray) and len(input_data.shape) == 2:
        # 计算距离矩阵
        from scipy.spatial.distance import pdist, squareform
        return squareform(pdist(input_data, metric=metric))
    
    # 不支持的输入类型
    raise ValueError(f"Unsupported input type: {type(input_data)}")


def build_rips_complex(distance_matrix: np.ndarray, max_dimension: int, 
                      max_radius: float, lazy: bool = False) -> Dict:
    """
    构建Vietoris-Rips复形
    
    参数:
        distance_matrix: 距离矩阵
        max_dimension: 最大维数
        max_radius: 最大半径
        lazy: 是否使用惰性计算
    
    返回:
        包含单纯形和滤值的字典
    """
    n = distance_matrix.shape[0]
    
    # 初始化单纯复形
    simplices = []
    filtration = {}
    
    # 添加0-单纯形（顶点）
    for i in range(n):
        simplex = frozenset([i])
        simplices.append(simplex)
        filtration[simplex] = 0.0
    
    # 添加1-单纯形（边）
    for i in range(n):
        for j in range(i+1, n):
            if distance_matrix[i, j] <= max_radius:
                simplex = frozenset([i, j])
                simplices.append(simplex)
                filtration[simplex] = distance_matrix[i, j]
    
    # 添加高维单纯形
    if max_dimension >= 2 and not lazy:
        # 对于Vietoris-Rips复形，我们添加所有可能的高维单纯形
        for d in range(2, max_dimension + 1):
            # 找到所有d-1维单纯形
            candidates = [s for s in simplices if len(s) == d]
            
            # 对于每对d-1维单纯形，检查是否可以合并
            for i, s1 in enumerate(candidates):
                for s2 in candidates[i+1:]:
                    # 检查s1和s2是否共享d-1个顶点
                    if len(s1.intersection(s2)) == d - 1:
                        # 合并s1和s2
                        new_simplex = frozenset(s1.union(s2))
                        
                        # 检查所有d维子单纯形是否在复形中
                        all_subsimplices_present = True
                        for v in new_simplex:
                            subsimplex = frozenset(v for v in new_simplex if v != v)
                            if subsimplex not in simplices:
                                all_subsimplices_present = False
                                break
                        
                        if all_subsimplices_present:
                            # 计算滤值（最大边长）
                            edges = [(i, j) for i in new_simplex for j in new_simplex if i < j]
                            max_edge_length = max(distance_matrix[i, j] for i, j in edges)
                            
                            if max_edge_length <= max_radius:
                                simplices.append(new_simplex)
                                filtration[new_simplex] = max_edge_length
    
    # 返回结果
    return {
        'simplices': simplices,
        'filtration': filtration,
        'complex_type': 'rips',
        'max_dimension': max_dimension,
        'max_radius': max_radius
    }


# 单纯复形构建器类
class SimplicialComplexBuilder:
    """
    单纯复形构建器类
    
    该类提供了构建数据的单纯复形表示的方法，支持多种复形类型和构建策略。
    
    属性:
        max_dimension (int): 构建的最大单纯形维数
        max_radius (float): 最大半径
        complex_type (str): 复形类型
        name (str): 算子名称
    """
    
    def __init__(self, 
                 max_dimension: int = 2, 
                 max_radius: float = float('inf'),
                 complex_type: str = 'rips',
                 **kwargs):
        """
        初始化SimplicialComplexBuilder算子
        
        参数:
            max_dimension (int): 构建的最大单纯形维数
            max_radius (float): 最大半径
            complex_type (str): 复形类型，可选值包括"rips"、"alpha"、"witness"、"cech"
            **kwargs: 其他参数
        """
        self.max_dimension = max_dimension
        self.max_radius = max_radius
        self.complex_type = complex_type
        self.name = "SimplicialComplexBuilder"
    
    def apply(self, input_data: Any, **kwargs) -> Any:
        """
        应用SimplicialComplexBuilder算子到输入数据
        
        参数:
            input_data: 输入数据，可以是以下形式之一：
                - 点云数据：形状为(n_samples, n_features)的numpy数组
                - 距离矩阵：形状为(n_samples, n_samples)的numpy数组
            **kwargs: 其他参数，包括：
                - metric (str, optional): 距离度量，默认为"euclidean"
                - lazy (bool, optional): 是否使用惰性计算，默认为False
        
        返回:
            单纯复形表示，包含单纯形列表和滤值
        """
        # 提取参数
        metric = kwargs.get('metric', 'euclidean')
        lazy = kwargs.get('lazy', False)
        
        # 预处理输入数据
        distance_matrix = preprocess_input(input_data, metric)
        
        # 根据复形类型选择构建函数
        if self.complex_type == 'rips':
            return self._build_rips_complex(distance_matrix, lazy)
        else:
            raise ValueError(f"Unknown complex type: {self.complex_type}")
    
    def _build_rips_complex(self, distance_matrix: np.ndarray, lazy: bool) -> Dict:
        """构建Vietoris-Rips复形"""
        return build_rips_complex(
            distance_matrix, 
            self.max_dimension, 
            self.max_radius, 
            lazy
        )
    
    def __str__(self) -> str:
        """返回算子的字符串表示"""
        return f"SimplicialComplexBuilder(max_dimension={self.max_dimension}, complex_type={self.complex_type})"


def generate_circle_data(n_points=100, noise=0.1):
    """生成圆形点云数据"""
    theta = np.linspace(0, 2*np.pi, n_points)
    x = np.cos(theta) + np.random.normal(0, noise, n_points)
    y = np.sin(theta) + np.random.normal(0, noise, n_points)
    return np.column_stack((x, y))


def test_simplicial_complex_builder():
    """测试SimplicialComplexBuilder"""
    print("\n测试SimplicialComplexBuilder...")
    
    # 生成圆形点云数据
    circle_data = generate_circle_data(n_points=20, noise=0.05)
    
    # 创建单纯复形构建器
    sc_builder = SimplicialComplexBuilder(max_dimension=1, complex_type='rips')
    print(f"创建构建器: {sc_builder}")
    
    # 应用构建器
    result = sc_builder.apply(circle_data)
    
    # 打印结果
    print(f"单纯复形构建结果:")
    print(f"  复形类型: {result['complex_type']}")
    print(f"  最大维数: {result['max_dimension']}")
    print(f"  单纯形数量: {len(result['simplices'])}")
    
    # 打印单纯形分布
    simplex_dims = [len(s) - 1 for s in result['simplices']]
    unique_dims, counts = np.unique(simplex_dims, return_counts=True)
    for dim, count in zip(unique_dims, counts):
        print(f"  {dim}维单纯形: {count}个")
    
    print("SimplicialComplexBuilder测试完成")
    
    return result


def main():
    """主函数"""
    print("开始测试单纯复形构建器...")
    
    # 测试SimplicialComplexBuilder
    sc_result = test_simplicial_complex_builder()
    
    print("\n所有测试完成")


if __name__ == "__main__":
    main()
