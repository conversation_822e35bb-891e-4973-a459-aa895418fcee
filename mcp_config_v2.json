{
  "mcpServers": {
    "aqfh-consciousness": {
      "command": "python3",
      "args": ["/home/<USER>/CascadeProjects/AQFH/aqfh_mcp_official.py"]
    }
  }
}

{
  "mcpServers": {
    "aqfh-consciousness": {
      "command": "python3",
      "args": [
        "/home/<USER>/CascadeProjects/AQFH/aqfh_consciousness_mcp_official.py"
      ]
    }
  }
}

{
  "mcpServers": {
    "aqfh": {
      "command": "python3",
      "args": [
        "/home/<USER>/CascadeProjects/AQFH/aqfh_unified_consciousness_mcp.py"
      ],
      "env": {
        "PYTHONPATH": "/home/<USER>/CascadeProjects/AQFH"
      }
    }
  }
}