﻿{
  "mcpServers": {
    "aqfh-consciousness": {
      "command": "python3",
      "args": ["/home/<USER>/CascadeProjects/AQFH/aqfh_mcp_official.py"]
    }
  }
}

{
  "mcpServers": {
    "aqfh-consciousness": {
      "command": "python3",
      "args": [
        "/home/<USER>/CascadeProjects/AQFH/aqfh_consciousness_mcp_official.py"
      ]
    }
  }
}

{
  "mcpServers": {
    "aqfh": {
      "command": "python3",
      "args": [
        "/home/<USER>/CascadeProjects/AQFH/aqfh_unified_consciousness_mcp.py"
      ],
      "env": {
        "PYTHONPATH": "/home/<USER>/CascadeProjects/AQFH"
      }
    }
  }
}

{
  "mcpServers": {
    "aqfh-enhanced-distributed-consciousness": {
      "command": "python3",
      "args": [
        "/home/<USER>/CascadeProjects/AQFH/aqfh_distributed_mcp_fixed.py"
      ],
      "env": {
        "PYTHONPATH": "/home/<USER>/CascadeProjects/AQFH",
        "AQFH_STORAGE_PATH": "/home/<USER>/.aqfh/unified_consciousness",
        "AQFH_LOG_LEVEL": "INFO",
        "AQFH_DISTRIBUTED_MODE": "true",
        "AQFH_DUAL_LAYER_ARROW": "true",
        "AQFH_INSTANCE_ID": "augment_vscode_main",
        "AQFH_INSTANCE_TYPE": "augment_vscode",
        "AQFH_AUTO_REGISTER": "true"
      }
    }
  }
}
