"""
算子版本管理示例

本示例展示了如何使用版本管理器管理算子的版本升级路径、版本迁移和版本回滚。
"""

import os
import sys
import logging
from typing import Dict, List, Any, Optional, Tuple, Union

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入算子注册表模块
try:
    from src.rust_bindings import (
        OperatorCategory,
        OperatorRegistry,
        OperatorMetadata,
        get_global_registry,
        register_operator,
        get_operator,
        list_operators,
        get_operator_metadata,
    )
    from src.rust_bindings.operator_registry.version_manager import (
        VersionManager,
        VersionPath,
        get_global_version_manager,
    )
    logger.info(f"成功导入算子注册表模块")
except ImportError as e:
    logger.error(f"导入算子注册表模块失败: {e}")
    sys.exit(1)

# 定义一些示例算子
class Calculator:
    """计算器类"""
    
    def __init__(self, version):
        """初始化计算器"""
        self.version = version
        
    def add(self, a, b):
        """加法"""
        return a + b
        
    def subtract(self, a, b):
        """减法"""
        return a - b
        
    def __str__(self):
        """字符串表示"""
        return f"Calculator(version={self.version})"

class CalculatorV2(Calculator):
    """计算器类V2"""
    
    def __init__(self):
        """初始化计算器"""
        super().__init__("2.0.0")
        
    def multiply(self, a, b):
        """乘法"""
        return a * b

class CalculatorV3(CalculatorV2):
    """计算器类V3"""
    
    def __init__(self):
        """初始化计算器"""
        super().__init__()
        self.version = "3.0.0"
        
    def divide(self, a, b):
        """除法"""
        if b == 0:
            raise ValueError("除数不能为零")
        return a / b

# 定义迁移函数
def migrate_v1_to_v2(calculator, *args, **kwargs):
    """从V1迁移到V2"""
    logger.info(f"迁移计算器从V1到V2: {calculator}")
    return CalculatorV2()

def migrate_v2_to_v3(calculator, *args, **kwargs):
    """从V2迁移到V3"""
    logger.info(f"迁移计算器从V2到V3: {calculator}")
    return CalculatorV3()

def test_version_manager():
    """测试版本管理器"""
    logger.info("开始测试版本管理器...")
    
    # 创建版本管理器
    version_manager = VersionManager()
    
    # 注册版本升级路径
    version_manager.register_version_path(
        OperatorCategory.UTILITY,
        "calculator",
        "1.0.0",
        "2.0.0",
        migrate_v1_to_v2,
    )
    
    version_manager.register_version_path(
        OperatorCategory.UTILITY,
        "calculator",
        "2.0.0",
        "3.0.0",
        migrate_v2_to_v3,
    )
    
    # 获取版本升级路径
    path = version_manager.get_version_path(
        OperatorCategory.UTILITY,
        "calculator",
        "1.0.0",
        "2.0.0",
    )
    logger.info(f"版本升级路径: {path}")
    
    # 查找版本升级路径
    paths = version_manager.find_upgrade_path(
        OperatorCategory.UTILITY,
        "calculator",
        "1.0.0",
        "3.0.0",
    )
    logger.info(f"版本升级路径列表: {paths}")
    
    # 迁移算子
    calculator_v1 = Calculator("1.0.0")
    calculator_v3 = version_manager.migrate_operator(
        OperatorCategory.UTILITY,
        "calculator",
        "1.0.0",
        "3.0.0",
        calculator_v1,
    )
    logger.info(f"迁移后的计算器: {calculator_v3}")
    
    # 测试迁移后的计算器
    logger.info(f"1 + 2 = {calculator_v3.add(1, 2)}")
    logger.info(f"3 - 1 = {calculator_v3.subtract(3, 1)}")
    logger.info(f"2 * 3 = {calculator_v3.multiply(2, 3)}")
    logger.info(f"6 / 2 = {calculator_v3.divide(6, 2)}")
    
    # 获取版本历史
    history = version_manager.get_version_history(
        OperatorCategory.UTILITY,
        "calculator",
    )
    logger.info(f"版本历史: {history}")
    
    # 获取最新版本
    latest = version_manager.get_latest_version(
        OperatorCategory.UTILITY,
        "calculator",
    )
    logger.info(f"最新版本: {latest}")
    
    # 获取所有版本升级路径
    all_paths = version_manager.get_all_version_paths(
        OperatorCategory.UTILITY,
        "calculator",
    )
    logger.info(f"所有版本升级路径: {all_paths}")
    
    logger.info("版本管理器测试完成")

def test_global_version_manager():
    """测试全局版本管理器"""
    logger.info("开始测试全局版本管理器...")
    
    # 获取全局版本管理器
    version_manager = get_global_version_manager()
    
    # 注册版本升级路径
    version_manager.register_version_path(
        OperatorCategory.UTILITY,
        "global_calculator",
        "1.0.0",
        "2.0.0",
        migrate_v1_to_v2,
    )
    
    version_manager.register_version_path(
        OperatorCategory.UTILITY,
        "global_calculator",
        "2.0.0",
        "3.0.0",
        migrate_v2_to_v3,
    )
    
    # 迁移算子
    calculator_v1 = Calculator("1.0.0")
    calculator_v3 = version_manager.migrate_operator(
        OperatorCategory.UTILITY,
        "global_calculator",
        "1.0.0",
        "3.0.0",
        calculator_v1,
    )
    logger.info(f"迁移后的计算器: {calculator_v3}")
    
    # 测试迁移后的计算器
    logger.info(f"1 + 2 = {calculator_v3.add(1, 2)}")
    logger.info(f"3 - 1 = {calculator_v3.subtract(3, 1)}")
    logger.info(f"2 * 3 = {calculator_v3.multiply(2, 3)}")
    logger.info(f"6 / 2 = {calculator_v3.divide(6, 2)}")
    
    logger.info("全局版本管理器测试完成")

def test_integration_with_registry():
    """测试与注册表的集成"""
    logger.info("开始测试与注册表的集成...")
    
    # 获取全局注册表
    registry = get_global_registry()
    
    # 获取全局版本管理器
    version_manager = get_global_version_manager()
    
    # 注册算子
    register_operator(
        OperatorCategory.UTILITY,
        "calculator",
        Calculator("1.0.0"),
        "1.0.0",
        "计算器算子",
        ["math", "utility"],
        [],
    )
    
    # 注册版本升级路径
    version_manager.register_version_path(
        OperatorCategory.UTILITY,
        "calculator",
        "1.0.0",
        "2.0.0",
        migrate_v1_to_v2,
    )
    
    version_manager.register_version_path(
        OperatorCategory.UTILITY,
        "calculator",
        "2.0.0",
        "3.0.0",
        migrate_v2_to_v3,
    )
    
    # 获取算子
    calculator_v1 = get_operator(OperatorCategory.UTILITY, "calculator")
    logger.info(f"获取到的计算器: {calculator_v1}")
    
    # 迁移算子
    calculator_v3 = version_manager.migrate_operator(
        OperatorCategory.UTILITY,
        "calculator",
        "1.0.0",
        "3.0.0",
        calculator_v1,
    )
    logger.info(f"迁移后的计算器: {calculator_v3}")
    
    # 更新注册表中的算子
    register_operator(
        OperatorCategory.UTILITY,
        "calculator",
        calculator_v3,
        "3.0.0",
        "计算器算子",
        ["math", "utility"],
        [],
    )
    
    # 获取更新后的算子
    calculator_v3_from_registry = get_operator(OperatorCategory.UTILITY, "calculator")
    logger.info(f"从注册表获取的计算器: {calculator_v3_from_registry}")
    
    # 测试从注册表获取的计算器
    logger.info(f"1 + 2 = {calculator_v3_from_registry.add(1, 2)}")
    logger.info(f"3 - 1 = {calculator_v3_from_registry.subtract(3, 1)}")
    logger.info(f"2 * 3 = {calculator_v3_from_registry.multiply(2, 3)}")
    logger.info(f"6 / 2 = {calculator_v3_from_registry.divide(6, 2)}")
    
    logger.info("与注册表的集成测试完成")

def main():
    """主函数"""
    logger.info("开始算子版本管理示例")
    
    # 测试版本管理器
    test_version_manager()
    
    # 测试全局版本管理器
    test_global_version_manager()
    
    # 测试与注册表的集成
    test_integration_with_registry()
    
    logger.info("算子版本管理示例结束")

if __name__ == "__main__":
    main()
