"""
错误处理器示例

本示例展示了如何使用错误处理器来处理系统中的各种错误。
"""

import os
import sys
import logging
import time
import random

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入错误处理器相关模块
try:
    from src.interfaces.error_codes import (
        ErrorSeverity, ErrorCategory, ErrorCode, ErrorCodeRegistry,
        get_registry, SYSTEM_INTERNAL_ERROR, VALIDATION_INVALID_INPUT
    )
    from src.interfaces.error_handler import (
        T<PERSON>rror, ErrorHandler, get_error_handler, handle_errors
    )
    from src.interfaces.recovery_strategies import (
        retry_strategy, fallback_strategy, cache_strategy, degradation_strategy,
        timeout_strategy, CircuitBreaker, CircuitBreakerRegistry, circuit_breaker_strategy,
        get_circuit_breaker_registry, register_default_recovery_strategies
    )
    from src.interfaces.error_reporter import (
        ErrorReporter, get_error_reporter, error_report_handler
    )
    logger.info("成功导入错误处理器相关模块")
except ImportError as e:
    logger.error(f"导入错误处理器相关模块失败: {e}")
    sys.exit(1)

# 创建自定义错误码
CUSTOM_ERROR = ErrorCode(
    1001,
    ErrorCategory.VALIDATION,
    ErrorSeverity.ERROR,
    "自定义错误: {param}",
    "这是一个自定义错误，用于测试错误处理器",
    "请检查参数是否正确"
)

# 注册自定义错误码
get_registry().register(CUSTOM_ERROR)

# 创建一些测试函数
@handle_errors(default_return="默认返回值", reraise=False, recovery=True)
def test_function_with_error(raise_error=True, error_type="custom"):
    """测试函数，可以抛出不同类型的错误"""
    if not raise_error:
        return "成功执行"
    
    if error_type == "custom":
        raise TTError(CUSTOM_ERROR, param="测试参数")
    elif error_type == "validation":
        raise TTError(VALIDATION_INVALID_INPUT, input_name="username")
    elif error_type == "system":
        raise TTError(SYSTEM_INTERNAL_ERROR)
    else:
        raise ValueError("未知的错误类型")

@handle_errors(default_return="重试失败", reraise=False, recovery=True)
def test_retry_function(max_attempts=3):
    """测试重试策略的函数"""
    # 获取函数的调用次数
    if not hasattr(test_retry_function, "attempts"):
        test_retry_function.attempts = 0
    
    test_retry_function.attempts += 1
    logger.info(f"尝试执行函数，第 {test_retry_function.attempts} 次")
    
    # 如果调用次数小于最大尝试次数，抛出错误
    if test_retry_function.attempts < max_attempts:
        # 添加原始函数和参数到错误详情中，以便重试策略可以使用
        details = {
            "original_func": test_retry_function,
            "args": (max_attempts,),
            "kwargs": {}
        }
        raise TTError(SYSTEM_INTERNAL_ERROR, details=details)
    
    return "重试成功"

@handle_errors(default_return="断路器失败", reraise=False, recovery=True)
def test_circuit_breaker_function(fail_probability=0.7):
    """测试断路器的函数"""
    # 随机决定是否失败
    if random.random() < fail_probability:
        # 添加原始函数和参数到错误详情中，以便断路器策略可以使用
        details = {
            "original_func": test_circuit_breaker_function,
            "args": (fail_probability,),
            "kwargs": {}
        }
        raise TTError(SYSTEM_INTERNAL_ERROR, details=details)
    
    return "断路器测试成功"

def main():
    """主函数"""
    logger.info("开始错误处理器示例")
    
    # 获取错误处理器
    error_handler = get_error_handler()
    
    # 注册错误处理函数
    def custom_error_handler(error):
        logger.info(f"自定义错误处理函数处理错误: {error}")
    
    error_handler.register_handler(ErrorSeverity.ERROR, custom_error_handler)
    
    # 注册错误恢复策略
    error_handler.register_recovery_strategy(
        CUSTOM_ERROR.code,
        fallback_strategy("自定义错误的回退值")
    )
    
    error_handler.register_recovery_strategy(
        SYSTEM_INTERNAL_ERROR.code,
        retry_strategy(max_retries=3, delay=0.5)
    )
    
    # 获取错误报告器
    error_reporter = get_error_reporter()
    
    # 配置错误报告器
    error_reporter.configure(
        report_dir="logs/error_reports",
        min_severity=ErrorSeverity.ERROR,
        report_format="json",
        max_reports=10
    )
    
    # 测试错误处理
    logger.info("测试错误处理...")
    
    # 测试自定义错误
    result = test_function_with_error(raise_error=True, error_type="custom")
    logger.info(f"自定义错误处理结果: {result}")
    
    # 测试验证错误
    result = test_function_with_error(raise_error=True, error_type="validation")
    logger.info(f"验证错误处理结果: {result}")
    
    # 测试系统错误
    result = test_function_with_error(raise_error=True, error_type="system")
    logger.info(f"系统错误处理结果: {result}")
    
    # 测试正常执行
    result = test_function_with_error(raise_error=False)
    logger.info(f"正常执行结果: {result}")
    
    # 测试重试策略
    logger.info("测试重试策略...")
    test_retry_function.attempts = 0  # 重置尝试次数
    result = test_retry_function(max_attempts=3)
    logger.info(f"重试策略结果: {result}")
    
    # 测试断路器
    logger.info("测试断路器...")
    
    # 注册断路器策略
    error_handler.register_recovery_strategy(
        SYSTEM_INTERNAL_ERROR.code,
        circuit_breaker_strategy("test_circuit", failure_threshold=3, reset_timeout=2.0)
    )
    
    # 执行多次，触发断路器
    for i in range(10):
        result = test_circuit_breaker_function(fail_probability=0.7)
        logger.info(f"断路器测试 {i+1} 结果: {result}")
        time.sleep(0.2)
    
    # 等待断路器重置
    logger.info("等待断路器重置...")
    time.sleep(2.5)
    
    # 再次执行，断路器应该已经重置
    for i in range(3):
        result = test_circuit_breaker_function(fail_probability=0.3)
        logger.info(f"断路器重置后测试 {i+1} 结果: {result}")
        time.sleep(0.2)
    
    # 刷新错误报告
    logger.info("刷新错误报告...")
    report_paths = error_reporter.flush()
    
    if report_paths:
        logger.info(f"生成的错误报告: {report_paths}")
    else:
        logger.info("没有生成错误报告")
    
    logger.info("错误处理器示例结束")

if __name__ == "__main__":
    main()
