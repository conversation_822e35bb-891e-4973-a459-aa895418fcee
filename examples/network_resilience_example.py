"""
网络弹性计算算子示例

本示例展示了如何使用网络弹性计算算子进行网络弹性分析和优化。
"""

import sys
import os
import time
import logging
import random
import numpy as np
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入网络弹性计算算子
from src.transcendental_tensor.network_resilience import (
    NodeType, NodeStatus, LinkStatus, FailureType,
    ResilienceNode, ResilienceLink, ResilienceNetwork,
    FailureEvent, ResilienceResult,
    ResilienceAnalyzer, ResilienceSimulator, NetworkResilienceOperator
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def create_test_network(node_count: int = 10) -> ResilienceNetwork:
    """
    创建测试网络
    
    参数:
        node_count (int): 节点数量
        
    返回:
        ResilienceNetwork: 测试网络
    """
    # 创建网络
    network = ResilienceNetwork(name="TestNetwork")
    
    # 创建节点
    for i in range(node_count):
        # 随机选择节点类型
        node_type = random.choice(list(NodeType))
        
        # 创建节点
        node = ResilienceNode(
            node_id=f"node-{i}",
            name=f"Node {i}",
            node_type=node_type,
            status=NodeStatus.ONLINE,
            resources={
                "cpu": random.uniform(1, 8),
                "memory": random.uniform(4, 32),
                "storage": random.uniform(100, 1000)
            }
        )
        
        # 添加节点
        network.add_node(node)
    
    # 创建链接
    for i in range(node_count):
        for j in range(i + 1, node_count):
            # 随机决定是否创建链接
            if random.random() < 0.3:
                # 创建链接
                link = ResilienceLink(
                    source_id=f"node-{i}",
                    target_id=f"node-{j}",
                    status=LinkStatus.ACTIVE,
                    latency=random.uniform(1, 100),
                    bandwidth=random.uniform(10, 1000),
                    packet_loss=random.uniform(0, 0.1)
                )
                
                # 添加链接
                network.add_link(link)
    
    return network


def create_failure_events(network: ResilienceNetwork, count: int = 5) -> List[FailureEvent]:
    """
    创建故障事件
    
    参数:
        network (ResilienceNetwork): 网络
        count (int): 事件数量
        
    返回:
        List[FailureEvent]: 故障事件列表
    """
    # 获取节点和链接
    nodes = list(network.nodes.values())
    links = list(network.links.values())
    
    # 创建故障事件
    events = []
    
    for i in range(count):
        # 随机选择故障类型
        failure_type = random.choice(list(FailureType))
        
        # 随机选择目标
        if failure_type in (FailureType.NODE_FAILURE, FailureType.LATENCY_SPIKE) and nodes:
            target = random.choice(nodes)
            target_id = target.node_id
            target_type = "node"
        elif links:
            target = random.choice(links)
            target_id = target.link_id
            target_type = "link"
        else:
            continue
        
        # 随机选择故障严重程度
        severity = random.uniform(0.1, 1.0)
        
        # 随机选择故障开始时间
        start_time = random.uniform(0, 3600)
        
        # 随机选择故障持续时间
        duration = random.uniform(10, 300)
        
        # 创建故障事件
        event = FailureEvent(
            failure_type=failure_type,
            target_id=target_id,
            target_type=target_type,
            severity=severity,
            start_time=start_time,
            duration=duration
        )
        
        events.append(event)
    
    return events


def main():
    """主函数"""
    logger.info("Starting network resilience example")
    
    # 创建测试网络
    network = create_test_network(10)
    
    logger.info(f"Created test network with {len(network.nodes)} nodes and {len(network.links)} links")
    
    # 创建故障事件
    failure_events = create_failure_events(network, 5)
    
    logger.info(f"Created {len(failure_events)} failure events")
    
    # 创建网络弹性计算算子
    operator = NetworkResilienceOperator()
    
    # 分析网络弹性
    analysis = operator.analyze_network(network)
    
    logger.info("Network resilience analysis:")
    logger.info(f"  Resilience score: {analysis['resilience_score']:.4f}")
    
    for metric_name, metric_value in analysis["metrics"].items():
        logger.info(f"  {metric_name}: {metric_value:.4f}")
    
    # 模拟网络弹性
    result = operator.simulate_network(network, failure_events)
    
    logger.info("Network resilience simulation:")
    logger.info(f"  Resilience score: {result.resilience_score:.4f}")
    
    for metric_name, metric_value in result.metrics.items():
        logger.info(f"  {metric_name}: {metric_value:.4f}")
    
    # 优化网络弹性
    optimized_network, optimization_result = operator.optimize_network(network, "redundancy")
    
    logger.info("Network resilience optimization:")
    logger.info(f"  Original score: {optimization_result['original_score']:.4f}")
    logger.info(f"  Optimized score: {optimization_result['optimized_score']:.4f}")
    logger.info(f"  Improvement: {optimization_result['improvement']:.4f}")
    
    # 比较不同优化类型
    optimization_types = ["redundancy", "diversity", "robustness"]
    
    for opt_type in optimization_types:
        optimized_network, optimization_result = operator.optimize_network(network, opt_type)
        
        logger.info(f"{opt_type.capitalize()} optimization:")
        logger.info(f"  Original score: {optimization_result['original_score']:.4f}")
        logger.info(f"  Optimized score: {optimization_result['optimized_score']:.4f}")
        logger.info(f"  Improvement: {optimization_result['improvement']:.4f}")
    
    # 创建多个网络进行比较
    networks = []
    
    for i in range(3):
        network = create_test_network(10)
        networks.append(network)
    
    # 比较网络
    comparison = operator.compare_networks(networks)
    
    logger.info("Network comparison:")
    logger.info(f"  Average score: {comparison['average_score']:.4f}")
    
    if comparison["best_network"]:
        logger.info(f"  Best network: {comparison['best_network']['network_name']} (score: {comparison['best_network']['resilience_score']:.4f})")
    
    if comparison["worst_network"]:
        logger.info(f"  Worst network: {comparison['worst_network']['network_name']} (score: {comparison['worst_network']['resilience_score']:.4f})")
    
    logger.info("Network resilience example completed")


if __name__ == "__main__":
    main()
