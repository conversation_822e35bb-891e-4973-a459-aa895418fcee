"""
高阶自反性操作示例

本示例展示了如何使用高阶自反性操作模块。
"""

import sys
import os
import time
import logging
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入高阶自反性操作模块
from src.transcendental_tensor.self_reflective_category.dynamic_morphism import (
    DynamicMorphism, DynamicMorphismConfig, OptimizationStrategy,
    ComposableDynamicMorphism, Environment, EnvironmentSensitiveMorphism,
    SystemState, FeedbackMorphism,
    OperationLevel, OperationType, OperationStrategy,
    OperationContext, OperationResult, OperationConfig,
    Operation, MorphismOperation, RecursiveOperation, HigherOrderOperation,
    ReflectionOperation, TransformationOperation, CompositionOperation, AbstractionOperation,
    RecursiveReflectionOperation, HigherOrderReflectionOperation,
    OperationSpace
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


# 示例态射
class ExampleMorphism(ComposableDynamicMorphism):
    """示例态射"""
    
    def __init__(self, name: str = "ExampleMorphism"):
        """初始化示例态射"""
        config = DynamicMorphismConfig(
            optimization_strategy=OptimizationStrategy.GRADIENT_DESCENT
        )
        super().__init__(config)
        self.name = name
        self.call_count = 0
    
    def _apply(self, x: Any) -> Any:
        """应用态射"""
        self.call_count += 1
        
        if isinstance(x, (int, float)):
            return x * 2
        elif isinstance(x, list):
            return [item * 2 for item in x]
        elif isinstance(x, dict):
            return {k: v * 2 if isinstance(v, (int, float)) else v for k, v in x.items()}
        else:
            return x
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.name} (calls: {self.call_count})"


# 示例1：基本反射操作
def example_reflection_operation():
    """基本反射操作示例"""
    logger.info("示例1：基本反射操作")
    
    # 创建态射
    morphism = ExampleMorphism("OriginalMorphism")
    logger.info(f"原始态射: {morphism}")
    
    # 测试态射
    result = morphism.apply(5)
    logger.info(f"原始态射结果: {result}")
    
    # 创建反射操作
    reflection_op = ReflectionOperation(
        name="BasicReflection",
        description="Basic reflection operation"
    )
    
    # 应用反射操作
    reflected_morphism = reflection_op.apply(morphism).result
    logger.info(f"反射后的态射: {reflected_morphism}")
    
    # 测试反射后的态射
    result = reflected_morphism.apply(5)
    logger.info(f"反射后的态射结果: {result}")
    
    # 检查元数据
    logger.info(f"反射元数据: {reflected_morphism.metadata}")


# 示例2：递归反射操作
def example_recursive_reflection():
    """递归反射操作示例"""
    logger.info("示例2：递归反射操作")
    
    # 创建态射
    morphism = ExampleMorphism("OriginalMorphism")
    
    # 创建递归反射操作
    recursive_op = RecursiveReflectionOperation(
        max_depth=3,
        name="RecursiveReflection",
        description="Recursive reflection operation with depth 3"
    )
    
    # 应用递归反射操作
    recursive_morphism = recursive_op.apply(morphism).result
    logger.info(f"递归反射后的态射: {recursive_morphism}")
    
    # 测试递归反射后的态射
    result = recursive_morphism.apply(5)
    logger.info(f"递归反射后的态射结果: {result}")
    
    # 检查元数据
    logger.info(f"递归反射元数据: {recursive_morphism.metadata}")


# 示例3：高阶反射操作
def example_higher_order_reflection():
    """高阶反射操作示例"""
    logger.info("示例3：高阶反射操作")
    
    # 创建基本反射操作
    reflection_op = ReflectionOperation(
        name="BasicReflection",
        description="Basic reflection operation"
    )
    
    # 创建高阶反射操作
    higher_order_op = HigherOrderReflectionOperation(
        name="HigherOrderReflection",
        description="Higher-order reflection operation"
    )
    
    # 应用高阶反射操作到基本反射操作
    enhanced_reflection_op = higher_order_op.apply_to_operation(reflection_op)
    logger.info(f"增强的反射操作: {enhanced_reflection_op}")
    
    # 创建态射
    morphism = ExampleMorphism("OriginalMorphism")
    
    # 应用增强的反射操作
    enhanced_morphism = enhanced_reflection_op.apply(morphism).result
    logger.info(f"增强反射后的态射: {enhanced_morphism}")
    
    # 测试增强反射后的态射
    result = enhanced_morphism.apply(5)
    logger.info(f"增强反射后的态射结果: {result}")
    
    # 检查元数据
    if hasattr(enhanced_morphism, "metadata") and "higher_order_reflection" in enhanced_morphism.metadata:
        logger.info(f"高阶反射信息: {enhanced_morphism.metadata['higher_order_reflection']}")


# 示例4：操作空间
def example_operation_space():
    """操作空间示例"""
    logger.info("示例4：操作空间")
    
    # 创建操作空间
    space = OperationSpace("ExampleSpace")
    
    # 创建基本操作
    reflection_op = ReflectionOperation(
        name="BasicReflection",
        description="Basic reflection operation"
    )
    
    transformation_op = TransformationOperation(
        transformation_function=lambda m: m,  # 恒等变换
        name="IdentityTransformation",
        description="Identity transformation operation"
    )
    
    # 添加操作到空间
    space.add_operation(reflection_op)
    space.add_operation(transformation_op)
    
    # 添加关系
    space.add_relation(
        reflection_op.id,
        transformation_op.id,
        "follows",
        {"priority": 1}
    )
    
    # 组合操作
    composed_op = space.compose_operations(
        reflection_op.id,
        transformation_op.id
    )
    
    logger.info(f"组合操作: {composed_op}")
    
    # 创建高阶操作
    higher_order_op = space.create_higher_order_operation(reflection_op.id)
    logger.info(f"高阶操作: {higher_order_op}")
    
    # 获取相关操作
    related_ops = space.get_related_operations(reflection_op.id)
    logger.info(f"与反射操作相关的操作: {[op.name for op in related_ops]}")


def main():
    """主函数"""
    logger.info("高阶自反性操作示例")
    
    # 运行示例
    example_reflection_operation()
    print("\n" + "-" * 80 + "\n")
    
    example_recursive_reflection()
    print("\n" + "-" * 80 + "\n")
    
    example_higher_order_reflection()
    print("\n" + "-" * 80 + "\n")
    
    example_operation_space()
    
    logger.info("示例完成")


if __name__ == "__main__":
    main()
