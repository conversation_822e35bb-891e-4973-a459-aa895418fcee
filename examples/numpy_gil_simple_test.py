#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
NumPy GIL管理简单性能测试

这个脚本提供了一个简单的测试，用于比较不同GIL管理策略对NumPy操作的性能影响。
它不依赖于项目中的其他模块，可以独立运行。
"""

import os
import sys
import time
import logging
import contextlib
import numpy as np
from typing import Dict, List, Any, Optional, Callable, Tuple

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 检查Python和NumPy版本
logger.info(f"Python版本: {sys.version}")
logger.info(f"NumPy版本: {np.__version__}")

# 检查nogil支持
has_nogil = hasattr(sys, 'set_nogil')
logger.info(f"Python nogil支持: {has_nogil}")

# 检查NumPy gil_state支持
has_np_gil_state = hasattr(np, 'gil_state')
logger.info(f"NumPy gil_state支持: {has_np_gil_state}")

# 测试参数
MATRIX_SIZES = [1000, 2000, 3000]
VECTOR_SIZES = [1_000_000, 10_000_000, 50_000_000]
REPEAT_COUNT = 3


def benchmark_function(func: Callable, *args, repeat: int = 3) -> Dict[str, float]:
    """
    对函数进行基准测试
    
    Args:
        func: 要测试的函数
        *args: 函数参数
        repeat: 重复次数
        
    Returns:
        包含性能指标的字典
    """
    times = []
    
    # 预热
    func(*args)
    
    # 测试
    for _ in range(repeat):
        start_time = time.time()
        result = func(*args)
        end_time = time.time()
        times.append(end_time - start_time)
    
    # 计算统计数据
    avg_time = sum(times) / len(times)
    min_time = min(times)
    max_time = max(times)
    
    return {
        'avg_time': avg_time,
        'min_time': min_time,
        'max_time': max_time,
        'times': times
    }


# 测试函数 - 矩阵乘法

def matrix_multiply_normal(a: np.ndarray, b: np.ndarray) -> np.ndarray:
    """普通矩阵乘法（不释放GIL）"""
    return np.dot(a, b)


def matrix_multiply_numpy_gil(a: np.ndarray, b: np.ndarray) -> np.ndarray:
    """使用NumPy gil_state的矩阵乘法"""
    if has_np_gil_state:
        with np.gil_state(False):
            return np.dot(a, b)
    else:
        return np.dot(a, b)


def matrix_multiply_python_nogil(a: np.ndarray, b: np.ndarray) -> np.ndarray:
    """使用Python nogil模式的矩阵乘法"""
    if has_nogil:
        old_state = sys.get_nogil()
        sys.set_nogil(True)
        try:
            return np.dot(a, b)
        finally:
            sys.set_nogil(old_state)
    else:
        return np.dot(a, b)


def matrix_multiply_combined(a: np.ndarray, b: np.ndarray) -> np.ndarray:
    """结合NumPy gil_state和Python nogil模式的矩阵乘法"""
    if has_nogil and has_np_gil_state:
        old_state = sys.get_nogil()
        sys.set_nogil(True)
        try:
            with np.gil_state(False):
                return np.dot(a, b)
        finally:
            sys.set_nogil(old_state)
    elif has_np_gil_state:
        with np.gil_state(False):
            return np.dot(a, b)
    elif has_nogil:
        old_state = sys.get_nogil()
        sys.set_nogil(True)
        try:
            return np.dot(a, b)
        finally:
            sys.set_nogil(old_state)
    else:
        return np.dot(a, b)


# 测试函数 - 复数数组操作

def complex_array_normal(a: np.ndarray, b: np.ndarray) -> np.ndarray:
    """普通复数数组操作（不释放GIL）"""
    c = a * b
    d = np.abs(c) ** 2
    e = np.sqrt(d)
    return np.fft.fft(e)


def complex_array_numpy_gil(a: np.ndarray, b: np.ndarray) -> np.ndarray:
    """使用NumPy gil_state的复数数组操作"""
    if has_np_gil_state:
        with np.gil_state(False):
            c = a * b
            d = np.abs(c) ** 2
            e = np.sqrt(d)
            return np.fft.fft(e)
    else:
        return complex_array_normal(a, b)


def complex_array_python_nogil(a: np.ndarray, b: np.ndarray) -> np.ndarray:
    """使用Python nogil模式的复数数组操作"""
    if has_nogil:
        old_state = sys.get_nogil()
        sys.set_nogil(True)
        try:
            c = a * b
            d = np.abs(c) ** 2
            e = np.sqrt(d)
            return np.fft.fft(e)
        finally:
            sys.set_nogil(old_state)
    else:
        return complex_array_normal(a, b)


def complex_array_combined(a: np.ndarray, b: np.ndarray) -> np.ndarray:
    """结合NumPy gil_state和Python nogil模式的复数数组操作"""
    if has_nogil and has_np_gil_state:
        old_state = sys.get_nogil()
        sys.set_nogil(True)
        try:
            with np.gil_state(False):
                c = a * b
                d = np.abs(c) ** 2
                e = np.sqrt(d)
                return np.fft.fft(e)
        finally:
            sys.set_nogil(old_state)
    elif has_np_gil_state:
        with np.gil_state(False):
            c = a * b
            d = np.abs(c) ** 2
            e = np.sqrt(d)
            return np.fft.fft(e)
    elif has_nogil:
        old_state = sys.get_nogil()
        sys.set_nogil(True)
        try:
            c = a * b
            d = np.abs(c) ** 2
            e = np.sqrt(d)
            return np.fft.fft(e)
        finally:
            sys.set_nogil(old_state)
    else:
        return complex_array_normal(a, b)


def create_test_matrices(size: int) -> Tuple[np.ndarray, np.ndarray]:
    """创建测试矩阵"""
    a = np.random.rand(size, size).astype(np.float64)
    b = np.random.rand(size, size).astype(np.float64)
    return a, b


def create_test_vectors(size: int) -> Tuple[np.ndarray, np.ndarray]:
    """创建测试向量"""
    a = np.random.rand(size).astype(np.complex128)
    b = np.random.rand(size).astype(np.complex128)
    return a, b


def test_matrix_multiplication():
    """测试矩阵乘法性能"""
    logger.info("=" * 80)
    logger.info("矩阵乘法性能测试")
    logger.info("=" * 80)
    
    results = {}
    
    for size in MATRIX_SIZES:
        logger.info(f"\n测试 {size}x{size} 矩阵乘法:")
        a, b = create_test_matrices(size)
        
        # 普通模式
        normal_results = benchmark_function(matrix_multiply_normal, a, b, repeat=REPEAT_COUNT)
        logger.info(f"普通模式: {normal_results['avg_time']:.6f}秒")
        results[f"normal_{size}"] = normal_results
        
        # NumPy gil_state模式
        if has_np_gil_state:
            numpy_gil_results = benchmark_function(matrix_multiply_numpy_gil, a, b, repeat=REPEAT_COUNT)
            logger.info(f"NumPy gil_state模式: {numpy_gil_results['avg_time']:.6f}秒")
            results[f"numpy_gil_{size}"] = numpy_gil_results
            
            # 计算加速比
            speedup = normal_results['avg_time'] / numpy_gil_results['avg_time']
            logger.info(f"NumPy gil_state加速比: {speedup:.2f}x")
        
        # Python nogil模式
        if has_nogil:
            python_nogil_results = benchmark_function(matrix_multiply_python_nogil, a, b, repeat=REPEAT_COUNT)
            logger.info(f"Python nogil模式: {python_nogil_results['avg_time']:.6f}秒")
            results[f"python_nogil_{size}"] = python_nogil_results
            
            # 计算加速比
            speedup = normal_results['avg_time'] / python_nogil_results['avg_time']
            logger.info(f"Python nogil加速比: {speedup:.2f}x")
        
        # 组合模式
        if has_np_gil_state and has_nogil:
            combined_results = benchmark_function(matrix_multiply_combined, a, b, repeat=REPEAT_COUNT)
            logger.info(f"组合模式: {combined_results['avg_time']:.6f}秒")
            results[f"combined_{size}"] = combined_results
            
            # 计算加速比
            speedup = normal_results['avg_time'] / combined_results['avg_time']
            logger.info(f"组合模式加速比: {speedup:.2f}x")
    
    return results


def test_complex_array_operations():
    """测试复数数组操作性能"""
    logger.info("=" * 80)
    logger.info("复数数组操作性能测试")
    logger.info("=" * 80)
    
    results = {}
    
    for size in VECTOR_SIZES:
        logger.info(f"\n测试 {size} 元素复数数组操作:")
        a, b = create_test_vectors(size)
        
        # 普通模式
        normal_results = benchmark_function(complex_array_normal, a, b, repeat=REPEAT_COUNT)
        logger.info(f"普通模式: {normal_results['avg_time']:.6f}秒")
        results[f"normal_{size}"] = normal_results
        
        # NumPy gil_state模式
        if has_np_gil_state:
            numpy_gil_results = benchmark_function(complex_array_numpy_gil, a, b, repeat=REPEAT_COUNT)
            logger.info(f"NumPy gil_state模式: {numpy_gil_results['avg_time']:.6f}秒")
            results[f"numpy_gil_{size}"] = numpy_gil_results
            
            # 计算加速比
            speedup = normal_results['avg_time'] / numpy_gil_results['avg_time']
            logger.info(f"NumPy gil_state加速比: {speedup:.2f}x")
        
        # Python nogil模式
        if has_nogil:
            python_nogil_results = benchmark_function(complex_array_python_nogil, a, b, repeat=REPEAT_COUNT)
            logger.info(f"Python nogil模式: {python_nogil_results['avg_time']:.6f}秒")
            results[f"python_nogil_{size}"] = python_nogil_results
            
            # 计算加速比
            speedup = normal_results['avg_time'] / python_nogil_results['avg_time']
            logger.info(f"Python nogil加速比: {speedup:.2f}x")
        
        # 组合模式
        if has_np_gil_state and has_nogil:
            combined_results = benchmark_function(complex_array_combined, a, b, repeat=REPEAT_COUNT)
            logger.info(f"组合模式: {combined_results['avg_time']:.6f}秒")
            results[f"combined_{size}"] = combined_results
            
            # 计算加速比
            speedup = normal_results['avg_time'] / combined_results['avg_time']
            logger.info(f"组合模式加速比: {speedup:.2f}x")
    
    return results


def generate_summary_report(matrix_results, complex_results):
    """生成性能测试摘要报告"""
    logger.info("=" * 80)
    logger.info("性能测试摘要报告")
    logger.info("=" * 80)
    
    # 系统信息
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"NumPy版本: {np.__version__}")
    logger.info(f"nogil支持: {has_nogil}")
    logger.info(f"NumPy gil_state支持: {has_np_gil_state}")
    
    # 矩阵乘法摘要
    logger.info("\n矩阵乘法性能摘要:")
    for size in MATRIX_SIZES:
        logger.info(f"  {size}x{size} 矩阵:")
        
        normal_time = matrix_results.get(f"normal_{size}", {}).get('avg_time', 0)
        logger.info(f"    普通模式: {normal_time:.6f}秒")
        
        if has_np_gil_state:
            numpy_gil_time = matrix_results.get(f"numpy_gil_{size}", {}).get('avg_time', 0)
            speedup = normal_time / numpy_gil_time if numpy_gil_time > 0 else 0
            logger.info(f"    NumPy gil_state模式: {numpy_gil_time:.6f}秒 (加速比: {speedup:.2f}x)")
        
        if has_nogil:
            python_nogil_time = matrix_results.get(f"python_nogil_{size}", {}).get('avg_time', 0)
            speedup = normal_time / python_nogil_time if python_nogil_time > 0 else 0
            logger.info(f"    Python nogil模式: {python_nogil_time:.6f}秒 (加速比: {speedup:.2f}x)")
        
        if has_np_gil_state and has_nogil:
            combined_time = matrix_results.get(f"combined_{size}", {}).get('avg_time', 0)
            speedup = normal_time / combined_time if combined_time > 0 else 0
            logger.info(f"    组合模式: {combined_time:.6f}秒 (加速比: {speedup:.2f}x)")
    
    # 复数数组操作摘要
    logger.info("\n复数数组操作性能摘要:")
    for size in VECTOR_SIZES:
        logger.info(f"  {size} 元素复数数组:")
        
        normal_time = complex_results.get(f"normal_{size}", {}).get('avg_time', 0)
        logger.info(f"    普通模式: {normal_time:.6f}秒")
        
        if has_np_gil_state:
            numpy_gil_time = complex_results.get(f"numpy_gil_{size}", {}).get('avg_time', 0)
            speedup = normal_time / numpy_gil_time if numpy_gil_time > 0 else 0
            logger.info(f"    NumPy gil_state模式: {numpy_gil_time:.6f}秒 (加速比: {speedup:.2f}x)")
        
        if has_nogil:
            python_nogil_time = complex_results.get(f"python_nogil_{size}", {}).get('avg_time', 0)
            speedup = normal_time / python_nogil_time if python_nogil_time > 0 else 0
            logger.info(f"    Python nogil模式: {python_nogil_time:.6f}秒 (加速比: {speedup:.2f}x)")
        
        if has_np_gil_state and has_nogil:
            combined_time = complex_results.get(f"combined_{size}", {}).get('avg_time', 0)
            speedup = normal_time / combined_time if combined_time > 0 else 0
            logger.info(f"    组合模式: {combined_time:.6f}秒 (加速比: {speedup:.2f}x)")
    
    # 最佳实践建议
    logger.info("\n最佳实践建议:")
    
    if has_np_gil_state and has_nogil:
        logger.info("  1. 对于计算密集型任务，组合模式（Python nogil + NumPy gil_state）通常提供最佳性能")
        logger.info("  2. 对于并行计算，释放GIL至关重要，可以获得接近线性的加速比")
    elif has_np_gil_state:
        logger.info("  1. NumPy gil_state模式可以显著提高计算密集型任务的性能")
        logger.info("  2. 对于并行计算，使用NumPy gil_state可以获得良好的加速比")
    elif has_nogil:
        logger.info("  1. Python nogil模式可以显著提高并行计算性能")
        logger.info("  2. 对于计算密集型任务，nogil模式也能提供性能提升")
    else:
        logger.info("  1. 当前环境不支持高级GIL管理，建议升级到Python 3.13+和NumPy 2.x以获得更好的性能")
    
    logger.info("=" * 80)


def main():
    """主函数"""
    logger.info("开始NumPy GIL管理简单性能测试")
    
    # 测试矩阵乘法
    matrix_results = test_matrix_multiplication()
    
    # 测试复数数组操作
    complex_results = test_complex_array_operations()
    
    # 生成摘要报告
    generate_summary_report(matrix_results, complex_results)
    
    logger.info("NumPy GIL管理简单性能测试完成")


if __name__ == "__main__":
    main()
