#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
演化追踪算子示例

演示演化追踪算子的使用方法，包括实体追踪、演化记录、演化分析和演化可视化。
"""

import os
import sys
import time
import random
import datetime
import json
from typing import Dict, List, Any

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.operators.engineering_support.evolution_tracker import (
    EntityTrackerOperator,
    EvolutionRecorderOperator,
    EvolutionAnalyzerOperator,
    EvolutionVisualizerOperator
)

def generate_sample_entities(count: int = 10) -> List[Dict[str, Any]]:
    """
    生成示例实体
    
    参数:
        count: 实体数量
        
    返回:
        实体列表
    """
    entities = []
    
    # 实体类型
    entity_types = ['user', 'product', 'order', 'payment', 'shipment']
    
    # 标签
    tags = ['active', 'inactive', 'new', 'vip', 'featured', 'sale', 'urgent']
    
    for i in range(count):
        # 实体ID
        entity_id = f'entity-{i}'
        
        # 实体类型
        entity_type = random.choice(entity_types)
        
        # 实体标签
        entity_tags = random.sample(tags, random.randint(1, 3))
        
        # 实体数据
        entity_data = {
            'name': f'{entity_type.capitalize()} {i}',
            'created_at': time.time() - random.randint(0, 3600 * 24 * 30),  # 最多30天前创建
            'status': random.choice(['active', 'inactive', 'pending']),
            'score': random.randint(1, 100),
            'metadata': {
                'source': random.choice(['web', 'mobile', 'api']),
                'region': random.choice(['north', 'south', 'east', 'west']),
                'priority': random.randint(1, 5)
            }
        }
        
        entities.append({
            'entity_id': entity_id,
            'entity_type': entity_type,
            'entity_data': entity_data,
            'tags': entity_tags
        })
    
    return entities

def generate_entity_updates(entities: List[Dict[str, Any]], update_count: int = 50) -> List[Dict[str, Any]]:
    """
    生成实体更新
    
    参数:
        entities: 实体列表
        update_count: 更新数量
        
    返回:
        更新列表
    """
    updates = []
    
    for _ in range(update_count):
        # 随机选择一个实体
        entity = random.choice(entities)
        entity_id = entity['entity_id']
        entity_data = entity['entity_data'].copy()
        
        # 随机更新字段
        update_field = random.choice(['status', 'score', 'metadata'])
        
        if update_field == 'status':
            entity_data['status'] = random.choice(['active', 'inactive', 'pending'])
        elif update_field == 'score':
            # 增加或减少分数
            entity_data['score'] = max(1, min(100, entity_data['score'] + random.randint(-10, 10)))
        elif update_field == 'metadata':
            # 更新元数据
            metadata_field = random.choice(['source', 'region', 'priority'])
            if metadata_field == 'source':
                entity_data['metadata']['source'] = random.choice(['web', 'mobile', 'api'])
            elif metadata_field == 'region':
                entity_data['metadata']['region'] = random.choice(['north', 'south', 'east', 'west'])
            elif metadata_field == 'priority':
                entity_data['metadata']['priority'] = random.randint(1, 5)
        
        # 添加更新时间
        entity_data['updated_at'] = time.time()
        
        updates.append({
            'entity_id': entity_id,
            'entity_data': entity_data,
            'timestamp': time.time()
        })
        
        # 更新原始实体数据
        entity['entity_data'] = entity_data
        
        # 添加一些延迟，使时间戳不同
        time.sleep(0.01)
    
    return updates

def demonstrate_entity_tracker():
    """
    演示实体追踪算子
    """
    print("=== 实体追踪算子示例 ===")
    
    # 创建实体追踪算子
    entity_tracker = EntityTrackerOperator(
        max_history_length=10,
        track_relationships=True,
        auto_timestamp=True
    )
    
    # 生成示例实体
    entities = generate_sample_entities(5)
    
    # 创建实体
    print("\n1. 创建实体:")
    for entity in entities:
        result = entity_tracker.apply({
            'action': 'create',
            'entity_id': entity['entity_id'],
            'entity_type': entity['entity_type'],
            'entity_data': entity['entity_data'],
            'tags': entity['tags']
        })
        print(f"创建实体 {entity['entity_id']} 结果: {result['success']}")
    
    # 获取实体
    print("\n2. 获取实体:")
    entity_id = entities[0]['entity_id']
    result = entity_tracker.apply({
        'action': 'get',
        'entity_id': entity_id
    })
    print(f"获取实体 {entity_id} 结果: {result['success']}")
    print(f"实体数据: {json.dumps(result['entity_data'], indent=2)}")
    
    # 更新实体
    print("\n3. 更新实体:")
    entity_id = entities[0]['entity_id']
    update_data = {
        'status': 'active',
        'score': 95,
        'metadata': {
            'priority': 5
        }
    }
    result = entity_tracker.apply({
        'action': 'update',
        'entity_id': entity_id,
        'entity_data': update_data
    })
    print(f"更新实体 {entity_id} 结果: {result['success']}")
    
    # 列出实体
    print("\n4. 列出实体:")
    result = entity_tracker.apply({
        'action': 'list',
        'entity_type': 'user'
    })
    print(f"列出实体结果: {result['success']}")
    print(f"实体数量: {result['count']}")
    
    # 添加关系
    print("\n5. 添加关系:")
    source_id = entities[0]['entity_id']
    target_id = entities[1]['entity_id']
    result = entity_tracker.apply({
        'action': 'add_relationship',
        'source_id': source_id,
        'target_id': target_id,
        'relationship_type': 'follows',
        'relationship_data': {
            'created_at': time.time(),
            'strength': 0.8
        }
    })
    print(f"添加关系 {source_id} -> {target_id} 结果: {result['success']}")
    
    # 获取关系
    print("\n6. 获取关系:")
    result = entity_tracker.apply({
        'action': 'get_relationships',
        'entity_id': source_id
    })
    print(f"获取关系结果: {result['success']}")
    print(f"出向关系: {json.dumps(result['outgoing'], indent=2)}")
    print(f"入向关系: {json.dumps(result['incoming'], indent=2)}")
    
    # 获取历史记录
    print("\n7. 获取历史记录:")
    result = entity_tracker.apply({
        'action': 'get_history',
        'entity_id': entity_id
    })
    print(f"获取历史记录结果: {result['success']}")
    print(f"历史记录数量: {result['count']}")
    
    print("\n实体追踪算子元数据:")
    print(json.dumps(entity_tracker.get_metadata(), indent=2))

def demonstrate_evolution_recorder():
    """
    演示演化记录算子
    """
    print("\n=== 演化记录算子示例 ===")
    
    # 创建演化记录算子
    evolution_recorder = EvolutionRecorderOperator(
        max_snapshots=10,
        snapshot_interval=60,
        incremental_recording=True
    )
    
    # 生成示例实体
    entities = generate_sample_entities(5)
    
    # 初始状态
    initial_state = {}
    for entity in entities:
        initial_state[entity['entity_id']] = entity['entity_data']
    
    # 记录初始状态
    print("\n1. 记录初始状态:")
    for entity_id, entity_data in initial_state.items():
        result = evolution_recorder.apply({
            'action': 'record',
            'entity_id': entity_id,
            'entity_data': entity_data
        })
        print(f"记录实体 {entity_id} 结果: {result['success']}")
    
    # 创建快照
    print("\n2. 创建快照:")
    result = evolution_recorder.apply({
        'action': 'snapshot',
        'snapshot_label': 'initial',
        'metadata': {
            'description': '初始状态',
            'created_by': 'example'
        }
    })
    print(f"创建快照结果: {result['success']}")
    print(f"快照ID: {result['snapshot_id']}")
    initial_snapshot_id = result['snapshot_id']
    
    # 生成实体更新
    updates = generate_entity_updates(entities, 10)
    
    # 记录更新
    print("\n3. 记录更新:")
    for update in updates:
        result = evolution_recorder.apply({
            'action': 'record',
            'entity_id': update['entity_id'],
            'entity_data': update['entity_data']
        })
        print(f"记录更新 {update['entity_id']} 结果: {result['success']}")
    
    # 创建第二个快照
    print("\n4. 创建第二个快照:")
    result = evolution_recorder.apply({
        'action': 'snapshot',
        'snapshot_label': 'updated',
        'metadata': {
            'description': '更新后状态',
            'created_by': 'example'
        }
    })
    print(f"创建快照结果: {result['success']}")
    print(f"快照ID: {result['snapshot_id']}")
    updated_snapshot_id = result['snapshot_id']
    
    # 列出快照
    print("\n5. 列出快照:")
    result = evolution_recorder.apply({
        'action': 'list_snapshots'
    })
    print(f"列出快照结果: {result['success']}")
    print(f"快照数量: {result['count']}")
    
    # 比较快照
    print("\n6. 比较快照:")
    result = evolution_recorder.apply({
        'action': 'compare',
        'start_snapshot_id': initial_snapshot_id,
        'end_snapshot_id': updated_snapshot_id
    })
    print(f"比较快照结果: {result['success']}")
    print(f"变更实体数量: {result['changed_entity_count']}")
    
    # 获取实体历史
    print("\n7. 获取实体历史:")
    entity_id = entities[0]['entity_id']
    result = evolution_recorder.apply({
        'action': 'get_history',
        'entity_id': entity_id
    })
    print(f"获取实体历史结果: {result['success']}")
    print(f"历史记录数量: {result['count']}")
    
    print("\n演化记录算子元数据:")
    print(json.dumps(evolution_recorder.get_metadata(), indent=2))

def demonstrate_evolution_analyzer():
    """
    演示演化分析算子
    """
    print("\n=== 演化分析算子示例 ===")
    
    # 创建演化分析算子
    evolution_analyzer = EvolutionAnalyzerOperator(
        window_size=5,
        anomaly_threshold=2.0,
        prediction_horizon=3
    )
    
    # 生成示例实体
    entities = generate_sample_entities(5)
    
    # 生成演化数据
    evolution_data = []
    
    # 为每个实体生成一系列状态变化
    for entity in entities:
        entity_id = entity['entity_id']
        base_score = entity['entity_data']['score']
        
        # 生成一些随机变化
        for i in range(20):
            # 添加一些趋势
            if i < 10:
                # 前10个数据点呈上升趋势
                score = base_score + i * 2 + random.randint(-5, 5)
            else:
                # 后10个数据点呈下降趋势
                score = base_score + 20 - (i - 10) * 3 + random.randint(-5, 5)
            
            # 确保分数在合理范围内
            score = max(1, min(100, score))
            
            # 添加数据点
            evolution_data.append({
                'entity_id': entity_id,
                'timestamp': time.time() - (20 - i) * 3600,  # 每小时一个数据点
                'value': score,
                'status': 'active' if score > 50 else 'inactive'
            })
    
    # 分析演化数据
    print("\n1. 分析演化数据:")
    result = evolution_analyzer.apply({
        'action': 'analyze',
        'evolution_data': evolution_data,
        'time_field': 'timestamp',
        'value_field': 'value',
        'group_by': 'entity_id'
    })
    print(f"分析结果: {result['success']}")
    print(f"实体数量: {result['entity_count']}")
    
    # 检测异常
    print("\n2. 检测异常:")
    result = evolution_analyzer.apply({
        'action': 'detect_anomalies',
        'evolution_data': evolution_data,
        'time_field': 'timestamp',
        'value_field': 'value'
    })
    print(f"检测到 {result['anomaly_count']} 个异常")
    if result['anomaly_count'] > 0:
        print(f"第一个异常: {json.dumps(result['anomalies'][0], indent=2)}")
    
    # 查找模式
    print("\n3. 查找模式:")
    result = evolution_analyzer.apply({
        'action': 'find_patterns',
        'evolution_data': evolution_data,
        'time_field': 'timestamp',
        'value_field': 'value'
    })
    print(f"发现 {result['pattern_count']} 个模式")
    if result['pattern_count'] > 0:
        print(f"第一个模式: {json.dumps(result['patterns'][0], indent=2)}")
    
    # 预测未来
    print("\n4. 预测未来:")
    entity_id = entities[0]['entity_id']
    result = evolution_analyzer.apply({
        'action': 'predict',
        'entity_id': entity_id,
        'horizon': 5
    })
    print(f"预测结果: {result['success']}")
    print(f"预测数据: {json.dumps(result['predictions'], indent=2)}")
    
    # 获取统计数据
    print("\n5. 获取统计数据:")
    result = evolution_analyzer.apply({
        'action': 'get_stats',
        'entity_id': entity_id
    })
    print(f"统计数据结果: {result['success']}")
    print(f"统计数据: {json.dumps(result['stats'], indent=2)}")
    
    print("\n演化分析算子元数据:")
    print(json.dumps(evolution_analyzer.get_metadata(), indent=2))

def demonstrate_evolution_visualizer():
    """
    演示演化可视化算子
    """
    print("\n=== 演化可视化算子示例 ===")
    
    # 创建演化可视化算子
    evolution_visualizer = EvolutionVisualizerOperator(
        default_format='json',
        include_metadata=True
    )
    
    # 生成示例实体
    entities = generate_sample_entities(3)
    
    # 生成演化数据
    evolution_data = []
    
    # 为每个实体生成一系列状态变化
    for entity in entities:
        entity_id = entity['entity_id']
        
        # 生成一些随机变化
        for i in range(5):
            # 添加数据点
            evolution_data.append({
                'entity_id': entity_id,
                'timestamp': time.time() - (5 - i) * 3600,  # 每小时一个数据点
                'state': {
                    'status': random.choice(['active', 'inactive', 'pending']),
                    'score': random.randint(1, 100),
                    'level': random.randint(1, 5)
                },
                'metadata': {
                    'source': random.choice(['web', 'mobile', 'api']),
                    'user_agent': f'Example/1.0 ({random.choice(["iOS", "Android", "Web"])})'
                }
            })
    
    # 可视化演化历史
    print("\n1. 可视化演化历史:")
    result = evolution_visualizer.apply({
        'action': 'visualize_history',
        'evolution_data': evolution_data,
        'format': 'json'
    })
    print(f"可视化结果: {result['success']}")
    print(f"项目数量: {result['item_count']}")
    
    # 可视化演化路径
    print("\n2. 可视化演化路径:")
    entity_id = entities[0]['entity_id']
    entity_data = [item for item in evolution_data if item['entity_id'] == entity_id]
    result = evolution_visualizer.apply({
        'action': 'visualize_path',
        'evolution_data': entity_data,
        'entity_id': entity_id,
        'format': 'json'
    })
    print(f"可视化结果: {result['success']}")
    print(f"状态数量: {result['state_count']}")
    
    # 生成报告
    print("\n3. 生成报告:")
    result = evolution_visualizer.apply({
        'action': 'generate_report',
        'evolution_data': evolution_data,
        'format': 'json'
    })
    print(f"报告结果: {result['success']}")
    print(f"实体数量: {result['entity_count']}")
    
    # 清除缓存
    print("\n4. 清除缓存:")
    result = evolution_visualizer.apply({
        'action': 'clear_cache'
    })
    print(f"清除缓存结果: {result['success']}")
    print(f"清除条目数: {result['cleared_entries']}")
    
    print("\n演化可视化算子元数据:")
    print(json.dumps(evolution_visualizer.get_metadata(), indent=2))

def main():
    """
    主函数
    """
    print("演化追踪算子示例程序")
    print("=" * 50)
    
    # 演示实体追踪算子
    demonstrate_entity_tracker()
    
    # 演示演化记录算子
    demonstrate_evolution_recorder()
    
    # 演示演化分析算子
    demonstrate_evolution_analyzer()
    
    # 演示演化可视化算子
    demonstrate_evolution_visualizer()
    
    print("\n示例程序结束")

if __name__ == "__main__":
    main()
