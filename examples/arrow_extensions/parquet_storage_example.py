#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Arrow扩展类型与Parquet存储示例

本示例展示了如何使用Arrow扩展类型和Parquet存储功能，包括：
1. 复数数组的存储和读取
2. 量子态的存储和读取
3. 张量数组的存储和读取
4. 使用Parquet数据集
5. 性能测试
"""

import os
import time
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Any

# 导入Arrow扩展类型
from src.core.data.arrow_types.complex_array import (
    numpy_to_arrow_complex64, arrow_to_numpy_complex64,
    numpy_to_arrow_complex128, arrow_to_numpy_complex128
)
from src.core.data.arrow_types.quantum_state_array import (
    numpy_to_arrow_quantum_state, arrow_to_numpy_quantum_state
)
from src.core.data.arrow_types.tensor_array import (
    numpy_to_arrow_tensor, arrow_to_numpy_tensor
)

# 导入Parquet存储功能
from src.core.data.arrow_types.parquet_storage import (
    ParquetStorageOptions,
    write_arrow_to_parquet, read_parquet_to_arrow,
    write_numpy_to_parquet, read_parquet_to_numpy,
    ParquetDataset
)


def example_complex_array():
    """复数数组示例"""
    print("\n=== 复数数组示例 ===")
    
    # 创建复数数组
    complex64_data = np.array([1+2j, 3+4j, 5+6j], dtype=np.complex64)
    complex128_data = np.array([1+2j, 3+4j, 5+6j], dtype=np.complex128)
    
    print(f"原始complex64数组: {complex64_data}")
    print(f"原始complex128数组: {complex128_data}")
    
    # 转换为Arrow数组
    arrow_complex64 = numpy_to_arrow_complex64(complex64_data)
    arrow_complex128 = numpy_to_arrow_complex128(complex128_data)
    
    # 创建输出目录
    os.makedirs("output", exist_ok=True)
    
    # 写入Parquet文件
    file_path = "output/complex_data.parquet"
    write_arrow_to_parquet(
        [arrow_complex64, arrow_complex128],
        ["complex64_data", "complex128_data"],
        file_path
    )
    
    print(f"Parquet文件已保存: {file_path}")
    print(f"文件大小: {os.path.getsize(file_path)} 字节")
    
    # 读取Parquet文件
    arrays = read_parquet_to_arrow(file_path)
    
    # 转换回NumPy数组
    numpy_complex64 = arrow_to_numpy_complex64(arrays["complex64_data"])
    numpy_complex128 = arrow_to_numpy_complex128(arrays["complex128_data"])
    
    print(f"读取的complex64数组: {numpy_complex64}")
    print(f"读取的complex128数组: {numpy_complex128}")
    
    # 验证数据
    assert np.allclose(numpy_complex64, complex64_data)
    assert np.allclose(numpy_complex128, complex128_data)
    print("数据验证通过！")


def example_quantum_state():
    """量子态示例"""
    print("\n=== 量子态示例 ===")
    
    # 创建量子态
    # |0⟩ 状态
    state0 = np.array([1.0, 0.0], dtype=np.complex128)
    # |1⟩ 状态
    state1 = np.array([0.0, 1.0], dtype=np.complex128)
    # |+⟩ 状态
    state_plus = np.array([1/np.sqrt(2), 1/np.sqrt(2)], dtype=np.complex128)
    # |-⟩ 状态
    state_minus = np.array([1/np.sqrt(2), -1/np.sqrt(2)], dtype=np.complex128)
    
    print(f"|0⟩ 状态: {state0}")
    print(f"|1⟩ 状态: {state1}")
    print(f"|+⟩ 状态: {state_plus}")
    print(f"|-⟩ 状态: {state_minus}")
    
    # 转换为Arrow数组
    arrow_state0 = numpy_to_arrow_quantum_state(state0, validate=True)
    arrow_state1 = numpy_to_arrow_quantum_state(state1, validate=True)
    arrow_state_plus = numpy_to_arrow_quantum_state(state_plus, validate=True)
    arrow_state_minus = numpy_to_arrow_quantum_state(state_minus, validate=True)
    
    # 创建输出目录
    os.makedirs("output", exist_ok=True)
    
    # 写入Parquet文件
    file_path = "output/quantum_states.parquet"
    write_arrow_to_parquet(
        [arrow_state0, arrow_state1, arrow_state_plus, arrow_state_minus],
        ["state0", "state1", "state_plus", "state_minus"],
        file_path
    )
    
    print(f"Parquet文件已保存: {file_path}")
    print(f"文件大小: {os.path.getsize(file_path)} 字节")
    
    # 读取Parquet文件
    arrays = read_parquet_to_arrow(file_path)
    
    # 转换回NumPy数组
    numpy_state0 = arrow_to_numpy_quantum_state(arrays["state0"])
    numpy_state1 = arrow_to_numpy_quantum_state(arrays["state1"])
    numpy_state_plus = arrow_to_numpy_quantum_state(arrays["state_plus"])
    numpy_state_minus = arrow_to_numpy_quantum_state(arrays["state_minus"])
    
    print(f"读取的|0⟩ 状态: {numpy_state0}")
    print(f"读取的|1⟩ 状态: {numpy_state1}")
    print(f"读取的|+⟩ 状态: {numpy_state_plus}")
    print(f"读取的|-⟩ 状态: {numpy_state_minus}")
    
    # 验证数据
    assert np.allclose(numpy_state0, state0)
    assert np.allclose(numpy_state1, state1)
    assert np.allclose(numpy_state_plus, state_plus)
    assert np.allclose(numpy_state_minus, state_minus)
    print("数据验证通过！")


def example_tensor_array():
    """张量数组示例"""
    print("\n=== 张量数组示例 ===")
    
    # 创建张量数组
    tensor_2d = np.array([[1.0, 2.0], [3.0, 4.0]], dtype=np.float64)
    
    print(f"原始张量:\n{tensor_2d}")
    
    # 转换为Arrow数组
    arrow_tensor = numpy_to_arrow_tensor(tensor_2d)
    
    # 创建输出目录
    os.makedirs("output", exist_ok=True)
    
    # 写入Parquet文件
    file_path = "output/tensor_data.parquet"
    write_arrow_to_parquet([arrow_tensor], ["tensor"], file_path)
    
    print(f"Parquet文件已保存: {file_path}")
    print(f"文件大小: {os.path.getsize(file_path)} 字节")
    
    # 读取Parquet文件
    arrays = read_parquet_to_arrow(file_path)
    
    # 转换回NumPy数组
    numpy_tensor = arrow_to_numpy_tensor(arrays["tensor"])
    
    print(f"读取的张量:\n{numpy_tensor}")
    
    # 验证数据
    assert np.allclose(numpy_tensor, tensor_2d)
    print("数据验证通过！")


def example_parquet_dataset():
    """Parquet数据集示例"""
    print("\n=== Parquet数据集示例 ===")
    
    # 创建输出目录
    os.makedirs("output", exist_ok=True)
    
    # 创建数据集
    dataset = ParquetDataset("output/experiment_results")
    
    # 创建数据
    data1 = np.array([1.0, 2.0, 3.0], dtype=np.float64)
    data2 = np.array([4.0, 5.0, 6.0], dtype=np.float64)
    
    print(f"数据1: {data1}")
    print(f"数据2: {data2}")
    
    # 写入分区数据
    dataset.write_partition(
        {"data": data1},
        {"experiment": "test", "sample": "1"}
    )
    
    dataset.write_partition(
        {"data": data2},
        {"experiment": "test", "sample": "2"}
    )
    
    # 列出分区
    partitions = dataset.list_partitions()
    print(f"可用分区: {partitions}")
    
    # 读取分区数据
    result1 = dataset.read_partition({"experiment": "test", "sample": "1"})
    result2 = dataset.read_partition({"experiment": "test", "sample": "2"})
    
    print(f"读取的数据1: {result1['data']}")
    print(f"读取的数据2: {result2['data']}")
    
    # 验证数据
    assert np.allclose(result1["data"], data1)
    assert np.allclose(result2["data"], data2)
    print("数据验证通过！")


def example_compression_options():
    """压缩选项示例"""
    print("\n=== 压缩选项示例 ===")
    
    # 创建数据
    size = 1000000
    data = np.random.randn(size)
    
    print(f"创建随机数组，大小: {size}")
    
    # 创建不同压缩选项
    options_none = ParquetStorageOptions(compression=None)
    options_snappy = ParquetStorageOptions(compression="snappy")
    options_gzip = ParquetStorageOptions(compression="gzip")
    
    # 创建输出目录
    os.makedirs("output", exist_ok=True)
    
    # 写入数据
    file_none = "output/data_none.parquet"
    file_snappy = "output/data_snappy.parquet"
    file_gzip = "output/data_gzip.parquet"
    
    write_numpy_to_parquet({"data": data}, file_none, options_none)
    write_numpy_to_parquet({"data": data}, file_snappy, options_snappy)
    write_numpy_to_parquet({"data": data}, file_gzip, options_gzip)
    
    # 比较文件大小
    size_none = os.path.getsize(file_none)
    size_snappy = os.path.getsize(file_snappy)
    size_gzip = os.path.getsize(file_gzip)
    
    print(f"无压缩: {size_none} 字节")
    print(f"Snappy压缩: {size_snappy} 字节")
    print(f"GZip压缩: {size_gzip} 字节")
    
    # 计算压缩率
    ratio_snappy = size_snappy / size_none
    ratio_gzip = size_gzip / size_none
    
    print(f"Snappy压缩率: {ratio_snappy:.2%}")
    print(f"GZip压缩率: {ratio_gzip:.2%}")
    
    # 绘制压缩率比较图
    labels = ['无压缩', 'Snappy', 'GZip']
    sizes = [size_none, size_snappy, size_gzip]
    
    plt.figure(figsize=(10, 6))
    plt.bar(labels, sizes)
    plt.title('不同压缩选项的文件大小比较')
    plt.ylabel('文件大小 (字节)')
    plt.savefig('output/compression_comparison.png')
    print("压缩比较图已保存到 output/compression_comparison.png")


def example_performance_test():
    """性能测试示例"""
    print("\n=== 性能测试示例 ===")
    
    # 创建不同大小的数据
    sizes = [10000, 100000, 1000000]
    float_write_times = []
    float_read_times = []
    complex_write_times = []
    complex_read_times = []
    
    # 创建输出目录
    os.makedirs("output", exist_ok=True)
    
    for size in sizes:
        print(f"\n测试大小: {size}")
        
        # 创建数据
        float_data = np.random.randn(size)
        complex_data = np.random.randn(size) + 1j * np.random.randn(size)
        
        # 测试浮点数据写入性能
        file_path = f"output/perf_float_{size}.parquet"
        start_time = time.time()
        write_numpy_to_parquet({"data": float_data}, file_path)
        float_write_time = time.time() - start_time
        float_write_times.append(float_write_time)
        
        # 测试浮点数据读取性能
        start_time = time.time()
        result = read_parquet_to_numpy(file_path)
        float_read_time = time.time() - start_time
        float_read_times.append(float_read_time)
        
        # 测试复数数据写入性能
        file_path = f"output/perf_complex_{size}.parquet"
        start_time = time.time()
        write_numpy_to_parquet({"data": complex_data}, file_path)
        complex_write_time = time.time() - start_time
        complex_write_times.append(complex_write_time)
        
        # 测试复数数据读取性能
        start_time = time.time()
        result = read_parquet_to_numpy(file_path)
        complex_read_time = time.time() - start_time
        complex_read_times.append(complex_read_time)
        
        print(f"浮点数据写入时间: {float_write_time:.4f}s")
        print(f"浮点数据读取时间: {float_read_time:.4f}s")
        print(f"复数数据写入时间: {complex_write_time:.4f}s")
        print(f"复数数据读取时间: {complex_read_time:.4f}s")
    
    # 绘制性能比较图
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 1, 1)
    plt.plot(sizes, float_write_times, 'b-o', label='浮点数据写入')
    plt.plot(sizes, complex_write_times, 'r-o', label='复数数据写入')
    plt.title('写入性能比较')
    plt.xlabel('数组大小')
    plt.ylabel('时间 (秒)')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(2, 1, 2)
    plt.plot(sizes, float_read_times, 'b-o', label='浮点数据读取')
    plt.plot(sizes, complex_read_times, 'r-o', label='复数数据读取')
    plt.title('读取性能比较')
    plt.xlabel('数组大小')
    plt.ylabel('时间 (秒)')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('output/performance_comparison.png')
    print("性能比较图已保存到 output/performance_comparison.png")


if __name__ == "__main__":
    # 运行所有示例
    example_complex_array()
    example_quantum_state()
    example_tensor_array()
    example_parquet_dataset()
    example_compression_options()
    example_performance_test()
    
    print("\n所有示例运行完成！")
