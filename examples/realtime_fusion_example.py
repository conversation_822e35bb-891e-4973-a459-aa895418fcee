"""
实时多模态融合示例

本示例展示了如何使用多模态融合系统处理实时数据流，包括：
1. 量子态数据
2. 全息场数据
3. 分形模式数据
"""

import numpy as np
import time
import logging
from typing import Dict, Any
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
import threading
import queue

from src.transcendental_tensor.multimodal_fusion.multimodal_fusion_operator import (
    MultimodalFusionOperator,
    ModalityType,
    ModalityConfig
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealtimeFusionVisualizer:
    """实时融合可视化器"""
    
    def __init__(self):
        # 初始化融合算子
        self.fusion_op = MultimodalFusionOperator()
        
        # 注册模态
        self._setup_modalities()
        
        # 设置数据流处理
        self.fusion_op.setup_streaming(
            buffer_size=100,
            max_time_diff=0.1
        )
        
        # 初始化数据缓冲区
        self.data_buffers = {
            "quantum": queue.Queue(maxsize=100),
            "holographic": queue.Queue(maxsize=100),
            "fractal": queue.Queue(maxsize=100),
            "fused": queue.Queue(maxsize=100)
        }
        
        # 初始化可视化
        self._setup_visualization()
        
        # 控制标志
        self.running = False
        
    def _setup_modalities(self):
        """设置模态配置"""
        # 量子态模态
        self.fusion_op.register_modality(
            "quantum",
            ModalityConfig(
                type=ModalityType.QUANTUM,
                dimension=8,
                encoding_params={
                    "encoding_type": "amplitude",
                    "normalize": True
                }
            )
        )
        
        # 全息场模态
        self.fusion_op.register_modality(
            "holographic",
            ModalityConfig(
                type=ModalityType.HOLOGRAPHIC,
                dimension=16,
                encoding_params={
                    "field_type": "scalar",
                    "use_fft": True
                }
            )
        )
        
        # 分形模态
        self.fusion_op.register_modality(
            "fractal",
            ModalityConfig(
                type=ModalityType.FRACTAL,
                dimension=12,
                encoding_params={
                    "max_scales": 3,
                    "use_wavelets": True
                }
            )
        )
        
    def _setup_visualization(self):
        """设置可视化"""
        self.fig = plt.figure(figsize=(15, 10))
        
        # 原始数据子图
        self.ax_quantum = self.fig.add_subplot(321)
        self.ax_holographic = self.fig.add_subplot(322)
        self.ax_fractal = self.fig.add_subplot(323)
        
        # 融合结果子图
        self.ax_fused = self.fig.add_subplot(324)
        
        # 性能指标子图
        self.ax_performance = self.fig.add_subplot(325)
        self.ax_quality = self.fig.add_subplot(326)
        
        # 初始化图表
        self.quantum_plot = self.ax_quantum.plot([])[0]
        self.holographic_im = self.ax_holographic.imshow(
            np.zeros((4, 4)),
            cmap='viridis'
        )
        self.fractal_im = self.ax_fractal.imshow(
            np.zeros((8, 8)),
            cmap='viridis'
        )
        self.fused_plot = self.ax_fused.plot([])[0]
        
        # 性能指标
        self.performance_lines = {
            "encoding": self.ax_performance.plot([], label='Encoding')[0],
            "fusion": self.ax_performance.plot([], label='Fusion')[0]
        }
        self.quality_line = self.ax_quality.plot([])[0]
        
        # 设置标签
        self.ax_quantum.set_title("量子态数据")
        self.ax_holographic.set_title("全息场数据")
        self.ax_fractal.set_title("分形数据")
        self.ax_fused.set_title("融合结果")
        self.ax_performance.set_title("处理时间")
        self.ax_quality.set_title("融合质量")
        
        self.ax_performance.legend()
        
        # 性能指标数据
        self.performance_history = {
            "encoding": [],
            "fusion": [],
            "quality": []
        }
        
    def _generate_quantum_data(self) -> np.ndarray:
        """生成模拟量子态数据"""
        # 生成随机量子态
        state = np.random.rand(4) + 1j * np.random.rand(4)
        state = state / np.linalg.norm(state)
        return state
        
    def _generate_holographic_data(self) -> np.ndarray:
        """生成模拟全息场数据"""
        # 生成2D高斯场
        x = np.linspace(-2, 2, 4)
        y = np.linspace(-2, 2, 4)
        X, Y = list(np.meshgrid(x, y))
        sigma = 0.5 + 0.2 * np.sin(time.time())  # 动态变化的宽度
        Z = np.exp(-(X**2 + Y**2)/(2*sigma**2))
        return Z
        
    def _generate_fractal_data(self) -> np.ndarray:
        """生成模拟分形数据"""
        # 生成简单的分形图案
        size = 8
        data = np.zeros((size, size))
        scale = 0.5 + 0.3 * np.sin(time.time() * 0.5)  # 动态缩放
        
        for i in range(size):
            for j in range(size):
                x = i / size
                y = j / size
                # Mandelbrot-like pattern
                data[i,j] = scale * (x**2 + y**2 + np.sin(x*y*10))
                
        return data
        
    def _data_generation_loop(self):
        """数据生成循环"""
        while self.running:
            try:
                # 生成数据
                quantum_data = self._generate_quantum_data()
                holographic_data = self._generate_holographic_data()
                fractal_data = self._generate_fractal_data()
                
                # 添加到缓冲区
                self.data_buffers["quantum"].put(quantum_data)
                self.data_buffers["holographic"].put(holographic_data)
                self.data_buffers["fractal"].put(fractal_data)
                
                # 控制生成速率
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"数据生成错误: {str(e)}")
                
    def _process_data(self):
        """数据处理循环"""
        while self.running:
            try:
                # 获取数据
                quantum_data = self.data_buffers["quantum"].get()
                holographic_data = self.data_buffers["holographic"].get()
                fractal_data = self.data_buffers["fractal"].get()
                
                # 记录开始时间
                start_time = time.time()
                
                # 编码数据
                encoded_quantum = self.fusion_op.encode_modality(
                    quantum_data,
                    "quantum"
                )
                encoded_holographic = self.fusion_op.encode_modality(
                    holographic_data,
                    "holographic"
                )
                encoded_fractal = self.fusion_op.encode_modality(
                    fractal_data,
                    "fractal"
                )
                
                encoding_time = time.time() - start_time
                
                # 融合数据
                fusion_start = time.time()
                fusion_result = self.fusion_op.fuse_modalities([
                    encoded_quantum,
                    encoded_holographic,
                    encoded_fractal
                ])
                fusion_time = time.time() - fusion_start
                
                # 更新性能指标
                self.performance_history["encoding"].append(encoding_time)
                self.performance_history["fusion"].append(fusion_time)
                self.performance_history["quality"].append(
                    fusion_result["fusion_quality"]
                )
                
                # 保持历史记录在合理范围内
                max_history = 100
                for key in self.performance_history:
                    if len(self.performance_history[key]) > max_history:
                        self.performance_history[key] = \
                            self.performance_history[key][-max_history:]
                            
                # 添加融合结果到缓冲区
                self.data_buffers["fused"].put(fusion_result["fused_data"])
                
            except Exception as e:
                logger.error(f"数据处理错误: {str(e)}")
                
    def _update_plot(self, frame):
        """更新可视化"""
        try:
            # 更新量子态图
            quantum_data = self.data_buffers["quantum"].get_nowait()
            self.quantum_plot.set_data(
                range(len(quantum_data)),
                np.abs(quantum_data)
            )
            self.ax_quantum.relim()
            self.ax_quantum.autoscale_view()
            
            # 更新全息场图
            holographic_data = self.data_buffers["holographic"].get_nowait()
            self.holographic_im.set_array(holographic_data)
            
            # 更新分形图
            fractal_data = self.data_buffers["fractal"].get_nowait()
            self.fractal_im.set_array(fractal_data)
            
            # 更新融合结果
            fused_data = self.data_buffers["fused"].get_nowait()
            self.fused_plot.set_data(
                range(len(fused_data)),
                fused_data
            )
            self.ax_fused.relim()
            self.ax_fused.autoscale_view()
            
            # 更新性能指标
            x = range(len(self.performance_history["encoding"]))
            self.performance_lines["encoding"].set_data(
                x,
                self.performance_history["encoding"]
            )
            self.performance_lines["fusion"].set_data(
                x,
                self.performance_history["fusion"]
            )
            self.ax_performance.relim()
            self.ax_performance.autoscale_view()
            
            # 更新质量指标
            self.quality_line.set_data(
                range(len(self.performance_history["quality"])),
                self.performance_history["quality"]
            )
            self.ax_quality.relim()
            self.ax_quality.autoscale_view()
            
        except queue.Empty:
            pass
        
        return (self.quantum_plot, self.holographic_im, self.fractal_im,
                self.fused_plot, self.performance_lines["encoding"],
                self.performance_lines["fusion"], self.quality_line)
                
    def start(self):
        """启动可视化"""
        self.running = True
        
        # 启动数据生成线程
        self.data_thread = threading.Thread(target=self._data_generation_loop)
        self.data_thread.daemon = True
        self.data_thread.start()
        
        # 启动处理线程
        self.process_thread = threading.Thread(target=self._process_data)
        self.process_thread.daemon = True
        self.process_thread.start()
        
        # 启动动画
        self.animation = FuncAnimation(
            self.fig,
            self._update_plot,
            interval=100,  # 刷新间隔(ms)
            blit=True
        )
        
        plt.tight_layout()
        plt.show()
        
    def stop(self):
        """停止可视化"""
        self.running = False
        if hasattr(self, 'data_thread'):
            self.data_thread.join()
        if hasattr(self, 'process_thread'):
            self.process_thread.join()

def main():
    """主函数"""
    visualizer = RealtimeFusionVisualizer()
    try:
        visualizer.start()
    except KeyboardInterrupt:
        visualizer.stop()
        logger.info("可视化已停止")

if __name__ == "__main__":
    main()