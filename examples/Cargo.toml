[package]
name = "tte-examples"
version = "0.1.0"
edition = "2021"
authors = ["超越态思维引擎团队"]
description = "超越态思维引擎示例程序"

[dependencies]
tte-core = { path = "../src/core" }
tte-operators = { path = "../src/operators" }
tte-algorithms = { path = "../src/algorithms" }
ndarray = { version = "0.15", features = ["serde"] }
num = "0.4"
num-complex = "0.4"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
rayon = "1.7"
lazy_static = "1.4"
num_cpus = "1.16"
clap = "3.0"
chrono = "0.4"

[[bin]]
name = "performance_test"
path = "performance_test.rs"

[[bin]]
name = "auto_optimization_demo"
path = "auto_optimization_demo.rs"

[[bin]]
name = "prototype_validation_main"
path = "core/prototype_validation_main.rs"
