#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超越态思维引擎 - Python-Rust互操作优化工具示例
"""

import os
import time
import numpy as np
import matplotlib.pyplot as plt
from interop import numpy_utils, zero_copy, shared_memory, parallel_processing

def test_numpy_utils():
    """测试NumPy工具"""
    print("测试NumPy工具...")
    
    # 创建NumPy转换器
    converter = numpy_utils.NumpyConverter(use_parallel=True, use_zero_copy=True, batch_size=1000)
    
    # 创建测试数组
    array_1d = np.random.rand(10000)
    array_2d = np.random.rand(100, 100)
    
    # 测试处理1D数组
    def process_func(data):
        return [x * 2 for x in data]
    
    start_time = time.time()
    result_1d = converter.process_array1d(array_1d, process_func)
    elapsed_time = time.time() - start_time
    print(f"处理1D数组耗时: {elapsed_time:.6f}秒")
    
    # 测试处理2D数组
    def process_func_2d(data):
        return [x * 2 for x in data]
    
    start_time = time.time()
    result_2d = converter.process_array2d(array_2d, process_func_2d)
    elapsed_time = time.time() - start_time
    print(f"处理2D数组耗时: {elapsed_time:.6f}秒")
    
    # 测试数组操作
    start_time = time.time()
    sum_result = converter.sum_array(array_1d)
    elapsed_time = time.time() - start_time
    print(f"求和耗时: {elapsed_time:.6f}秒")
    print(f"求和结果: {sum_result:.6f}")
    
    start_time = time.time()
    mean_result = converter.mean_array(array_1d)
    elapsed_time = time.time() - start_time
    print(f"求平均值耗时: {elapsed_time:.6f}秒")
    print(f"平均值结果: {mean_result:.6f}")
    
    start_time = time.time()
    std_result = converter.std_array(array_1d)
    elapsed_time = time.time() - start_time
    print(f"求标准差耗时: {elapsed_time:.6f}秒")
    print(f"标准差结果: {std_result:.6f}")
    
    # 测试矩阵乘法
    start_time = time.time()
    matmul_result = converter.matrix_multiply(array_2d, array_2d)
    elapsed_time = time.time() - start_time
    print(f"矩阵乘法耗时: {elapsed_time:.6f}秒")
    
    # 性能测试
    print("\n性能测试:")
    benchmark_results = converter.benchmark(array_size=10000, iterations=10)
    for key, value in benchmark_results.items():
        print(f"  {key}: {value:.2f}微秒")
    
    print()

def test_zero_copy():
    """测试零拷贝工具"""
    print("测试零拷贝工具...")
    
    # 创建临时文件路径
    temp_file = os.path.join(os.getcwd(), "temp_buffer.bin")
    
    # 创建零拷贝缓冲区
    buffer = zero_copy.ZeroCopyBuffer(temp_file, 1024 * 1024)
    buffer.initialize()
    
    # 创建测试数组
    array_float = np.random.rand(1000)
    
    # 写入数据
    start_time = time.time()
    buffer.write_data(array_float, 0)
    elapsed_time = time.time() - start_time
    print(f"写入数据耗时: {elapsed_time:.6f}秒")
    
    # 读取数据
    start_time = time.time()
    result_array = buffer.read_data(0, 1000 * 8, "float64")
    elapsed_time = time.time() - start_time
    print(f"读取数据耗时: {elapsed_time:.6f}秒")
    
    # 验证数据
    print(f"数据一致性: {np.allclose(array_float, result_array)}")
    
    # 创建零拷贝数组
    array = zero_copy.ZeroCopyArray(buffer, "float64", 0, 1000)
    
    # 获取数组数据
    start_time = time.time()
    array_data = array.get_data()
    elapsed_time = time.time() - start_time
    print(f"获取数组数据耗时: {elapsed_time:.6f}秒")
    
    # 设置数组数据
    new_array = np.random.rand(1000)
    start_time = time.time()
    array.set_data(new_array)
    elapsed_time = time.time() - start_time
    print(f"设置数组数据耗时: {elapsed_time:.6f}秒")
    
    # 验证数据
    array_data = array.get_data()
    print(f"数据一致性: {np.allclose(new_array, array_data)}")
    
    # 关闭缓冲区
    buffer.close()
    
    print()

def test_shared_memory():
    """测试共享内存工具"""
    print("测试共享内存工具...")
    
    # 创建共享内存管理器
    manager = shared_memory.SharedMemoryManager()
    
    # 分配共享内存
    manager.allocate("test_memory", 1024 * 1024)
    
    # 创建测试数组
    array_float = np.random.rand(1000)
    
    # 写入数据
    start_time = time.time()
    manager.write_data("test_memory", array_float, 0)
    elapsed_time = time.time() - start_time
    print(f"写入数据耗时: {elapsed_time:.6f}秒")
    
    # 读取数据
    start_time = time.time()
    result_array = manager.read_data("test_memory", 0, 1000 * 8, "float64")
    elapsed_time = time.time() - start_time
    print(f"读取数据耗时: {elapsed_time:.6f}秒")
    
    # 验证数据
    print(f"数据一致性: {np.allclose(array_float, result_array)}")
    
    # 创建共享内存数组
    array = shared_memory.SharedMemoryArray(manager, "test_memory", "float64", 0, 1000)
    
    # 获取数组数据
    start_time = time.time()
    array_data = array.get_data()
    elapsed_time = time.time() - start_time
    print(f"获取数组数据耗时: {elapsed_time:.6f}秒")
    
    # 设置数组数据
    new_array = np.random.rand(1000)
    start_time = time.time()
    array.set_data(new_array)
    elapsed_time = time.time() - start_time
    print(f"设置数组数据耗时: {elapsed_time:.6f}秒")
    
    # 验证数据
    array_data = array.get_data()
    print(f"数据一致性: {np.allclose(new_array, array_data)}")
    
    # 释放共享内存
    manager.free("test_memory")
    
    print()

def test_parallel_processing():
    """测试并行处理工具"""
    print("测试并行处理工具...")
    
    # 创建并行执行器
    executor = parallel_processing.ParallelExecutor(num_threads=4, batch_size=1000)
    
    # 创建测试数据
    items = list(range(10000))
    
    # 测试map
    def map_func(data):
        return [x * 2 for x in data]
    
    start_time = time.time()
    map_result = executor.map(map_func, items)
    elapsed_time = time.time() - start_time
    print(f"Map耗时: {elapsed_time:.6f}秒")
    print(f"Map结果前5个: {map_result[:5]}")
    
    # 测试filter
    def filter_func(data):
        return [x % 2 == 0 for x in data]
    
    start_time = time.time()
    filter_result = executor.filter(filter_func, items)
    elapsed_time = time.time() - start_time
    print(f"Filter耗时: {elapsed_time:.6f}秒")
    print(f"Filter结果前5个: {filter_result[:5]}")
    
    # 测试reduce
    def reduce_func(a, b):
        return a + b
    
    start_time = time.time()
    reduce_result = executor.reduce(reduce_func, items)
    elapsed_time = time.time() - start_time
    print(f"Reduce耗时: {elapsed_time:.6f}秒")
    print(f"Reduce结果: {reduce_result}")
    
    # 测试任务池
    pool = parallel_processing.TaskPool(num_threads=4, queue_size=100)
    pool.start()
    
    # 提交任务
    def task_func(data):
        return sum(data)
    
    futures = []
    for i in range(10):
        chunk = items[i*1000:(i+1)*1000]
        future = pool.submit(task_func, chunk)
        futures.append(future)
    
    # 获取结果
    results = [future.get() for future in futures]
    print(f"任务池结果: {results}")
    
    # 停止任务池
    pool.stop()
    
    print()

def compare_performance():
    """比较性能"""
    print("比较性能...")
    
    # 创建测试数据
    array_sizes = [1000, 10000, 100000, 1000000]
    
    # 测试NumPy求和
    numpy_times = []
    rust_times = []
    
    converter = numpy_utils.NumpyConverter(use_parallel=True, use_zero_copy=True, batch_size=1000)
    
    for size in array_sizes:
        array = np.random.rand(size)
        
        # NumPy求和
        start_time = time.time()
        numpy_sum = np.sum(array)
        numpy_time = time.time() - start_time
        numpy_times.append(numpy_time)
        
        # Rust求和
        start_time = time.time()
        rust_sum = converter.sum_array(array)
        rust_time = time.time() - start_time
        rust_times.append(rust_time)
        
        print(f"数组大小: {size}")
        print(f"  NumPy求和耗时: {numpy_time:.6f}秒")
        print(f"  Rust求和耗时: {rust_time:.6f}秒")
        print(f"  加速比: {numpy_time / rust_time:.2f}x")
    
    # 绘制性能对比图
    plt.figure(figsize=(10, 6))
    plt.plot(array_sizes, numpy_times, 'o-', label='NumPy')
    plt.plot(array_sizes, rust_times, 's-', label='Rust')
    plt.xscale('log')
    plt.yscale('log')
    plt.xlabel('数组大小')
    plt.ylabel('执行时间 (秒)')
    plt.title('NumPy vs Rust性能对比')
    plt.legend()
    plt.grid(True)
    plt.savefig('interop_performance_comparison.png')
    
    print(f"性能对比图已保存为 'interop_performance_comparison.png'")
    print()

def main():
    """主函数"""
    print("超越态思维引擎 - Python-Rust互操作优化工具示例")
    print("=" * 50)
    
    # 测试NumPy工具
    test_numpy_utils()
    
    # 测试零拷贝工具
    test_zero_copy()
    
    # 测试共享内存工具
    test_shared_memory()
    
    # 测试并行处理工具
    test_parallel_processing()
    
    # 比较性能
    compare_performance()
    
    print("示例完成!")

if __name__ == "__main__":
    main()
