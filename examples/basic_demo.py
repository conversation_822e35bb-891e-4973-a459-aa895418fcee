"""
基本演示脚本

展示分形态量子神经网络的基本用法。
"""

import numpy as np
import time
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fractal_quantum_network.core.quantum_state import QuantumState
from fractal_quantum_network.core.holographic_node import HolographicNode
from fractal_quantum_network.core.fractal_network import FractalQuantumNetwork
from fractal_quantum_network.interface.transcendental import TranscendentalInterface
from fractal_quantum_network.registry.operator_registry import OperatorRegistry

def create_demo_network():
    """创建演示网络"""
    print("创建分形态量子神经网络...")
    
    # 创建网络
    network = FractalQuantumNetwork()
    
    # 创建中心节点
    print("创建中心节点...")
    center_state = QuantumState(4)  # 16维量子态
    center_id = network.create_node(
        center_state, 
        node_id="center",
        metadata={"description": "中心节点", "importance": "high"}
    )
    
    # 构建蒲公英结构
    print("构建蒲公英分形结构...")
    result = network.build_dandelion_structure(center_id, layers=2, nodes_per_layer=3)
    print(f"创建了 {len(result['new_nodes'])} 个新节点")
    
    # 添加一些特殊节点
    print("添加特殊节点...")
    
    # 创建表示"量子计算"概念的节点
    quantum_computing_state = QuantumState(0, np.array([0.8, 0.2, 0.4, 0.1, 0.3, 0.1, 0, 0], dtype=complex))
    quantum_computing_state.normalize()
    quantum_computing_id = network.create_node(
        quantum_computing_state,
        node_id="quantum_computing",
        metadata={"description": "量子计算", "category": "concept"}
    )
    
    # 创建表示"神经网络"概念的节点
    neural_network_state = QuantumState(0, np.array([0.3, 0.7, 0.2, 0.5, 0.1, 0.2, 0, 0], dtype=complex))
    neural_network_state.normalize()
    neural_network_id = network.create_node(
        neural_network_state,
        node_id="neural_network",
        metadata={"description": "神经网络", "category": "concept"}
    )
    
    # 创建表示"分形结构"概念的节点
    fractal_state = QuantumState(0, np.array([0.4, 0.3, 0.8, 0.2, 0.1, 0, 0, 0], dtype=complex))
    fractal_state.normalize()
    fractal_id = network.create_node(
        fractal_state,
        node_id="fractal",
        metadata={"description": "分形结构", "category": "concept"}
    )
    
    # 连接概念节点
    print("连接概念节点...")
    network.connect_nodes("quantum_computing", "neural_network", 0.6)
    network.connect_nodes("neural_network", "fractal", 0.7)
    network.connect_nodes("fractal", "quantum_computing", 0.5)
    network.connect_nodes("center", "quantum_computing", 0.9)
    network.connect_nodes("center", "neural_network", 0.8)
    network.connect_nodes("center", "fractal", 0.9)
    
    print(f"网络创建完成，共有 {len(network.nodes)} 个节点")
    
    return network

def demo_query(network):
    """演示查询"""
    print("\n执行查询演示...")
    
    # 创建查询状态
    print("创建查询状态...")
    query_state = QuantumState(0, np.array([0.7, 0.3, 0.5, 0.2, 0.3, 0.1, 0, 0], dtype=complex))
    query_state.normalize()
    
    # 处理查询
    print("处理查询...")
    start_time = time.time()
    results = network.process_query(query_state)
    query_time = time.time() - start_time
    
    print(f"查询完成，耗时 {query_time:.4f} 秒，找到 {len(results)} 个匹配节点")
    
    # 显示前3个结果
    print("\n查询结果:")
    for i, result in enumerate(results[:3]):
        node_id = result["node_id"]
        similarity = result["similarity"]
        description = network.nodes[node_id].metadata.get("description", "无描述")
        print(f"结果 {i+1}: 节点 {node_id}, 相似度: {similarity:.4f}, 描述: {description}")
    
    return results

def demo_in_memory_transform(network, node_id):
    """演示内存中转换"""
    print("\n执行内存中转换演示...")
    
    if node_id not in network.nodes:
        print(f"节点 {node_id} 不存在")
        return
    
    node = network.nodes[node_id]
    
    # 记录原始状态
    original_state = node.quantum_state.state.copy()
    
    print(f"对节点 {node_id} 应用相位偏移...")
    result = node.in_memory_transform("phase_shift", {"phase": 0.5})
    print(f"转换结果: {result}")
    
    # 计算状态变化
    new_state = node.quantum_state.state
    state_diff = np.linalg.norm(new_state - original_state)
    print(f"状态变化量: {state_diff:.4f}")
    
    # 再次查询
    print("\n转换后再次查询...")
    query_state = QuantumState(0, np.array([0.7, 0.3, 0.5, 0.2, 0.3, 0.1, 0, 0], dtype=complex))
    query_state.normalize()
    results = network.process_query(query_state)
    
    # 显示前3个结果
    print("\n新查询结果:")
    for i, result in enumerate(results[:3]):
        node_id = result["node_id"]
        similarity = result["similarity"]
        description = network.nodes[node_id].metadata.get("description", "无描述")
        print(f"结果 {i+1}: 节点 {node_id}, 相似度: {similarity:.4f}, 描述: {description}")

def demo_transcendental_interface(network):
    """演示超越态接口"""
    print("\n执行超越态接口演示...")
    
    # 创建超越态接口
    interface = TranscendentalInterface(network)
    
    # 获取一个节点的量子态
    node_id = "quantum_computing"
    if node_id not in network.nodes:
        print(f"节点 {node_id} 不存在")
        return
    
    quantum_state = network.nodes[node_id].quantum_state
    
    # 转换为超越态
    print(f"将节点 {node_id} 的量子态转换为超越态...")
    start_time = time.time()
    transcendental_state = interface.quantum_to_transcendental(
        quantum_state, 
        dimensions_needed=["holographic", "fractal", "emergent"],
        complexity_level="full"
    )
    conversion_time = time.time() - start_time
    
    print(f"转换完成，耗时 {conversion_time:.4f} 秒")
    print(f"超越态维度: {list(transcendental_state.keys())}")
    
    # 分解存储
    print("\n将超越态分解存储到网络...")
    start_time = time.time()
    storage_result = interface.store_decomposed_transcendental(transcendental_state)
    storage_time = time.time() - start_time
    
    print(f"存储完成，耗时 {storage_time:.4f} 秒")
    print(f"存储结果: 状态ID={storage_result['state_id']}, 维度数量={len(storage_result['dimension_ids'])}")
    
    # 检索重组
    print("\n从网络检索并重组超越态...")
    start_time = time.time()
    retrieved_state = interface.retrieve_recomposed_transcendental(storage_result['state_id'])
    retrieval_time = time.time() - start_time
    
    print(f"检索完成，耗时 {retrieval_time:.4f} 秒")
    print(f"检索到的超越态维度: {list(retrieved_state.keys())}")
    
    # 转换回量子态
    print("\n将超越态转换回量子态...")
    start_time = time.time()
    restored_quantum_state = interface.transcendental_to_quantum(
        retrieved_state, 
        preservation_strategy="balanced"
    )
    reconversion_time = time.time() - start_time
    
    print(f"转换完成，耗时 {reconversion_time:.4f} 秒")
    
    # 计算与原始量子态的相似度
    similarity = np.abs(quantum_state.inner_product(restored_quantum_state))**2
    print(f"恢复的量子态与原始量子态的相似度: {similarity:.4f}")

def demo_operator_registry():
    """演示算子注册表"""
    print("\n执行算子注册表演示...")
    
    # 创建算子注册表
    registry = OperatorRegistry()
    
    # 注册常用量子算子
    count = registry.register_common_quantum_operators()
    print(f"注册了 {count} 个常用量子算子")
    
    # 列出算子
    operators = registry.list_operators("quantum")
    print(f"量子算子列表: {operators}")
    
    # 使用算子
    print("\n使用Hadamard门算子...")
    h_operator = registry.get_operator_instance("hadamard_gate")
    if h_operator:
        h_matrix = h_operator.apply(n_qubits=1)
        print(f"Hadamard门矩阵:\n{h_matrix}")
        
        # 应用到量子态
        state = QuantumState(1)  # |+⟩ = (|0⟩ + |1⟩)/√2
        print(f"初始状态: {state.state}")
        
        new_state = state.apply_operator(h_matrix)
        print(f"应用Hadamard门后: {new_state.state}")

def main():
    """主函数"""
    print("分形态量子神经网络演示")
    print("=" * 50)
    
    # 创建演示网络
    network = create_demo_network()
    
    # 演示查询
    results = demo_query(network)
    
    # 演示内存中转换
    if results:
        demo_in_memory_transform(network, results[0]["node_id"])
    
    # 演示超越态接口
    demo_transcendental_interface(network)
    
    # 演示算子注册表
    demo_operator_registry()
    
    print("\n演示完成。")

if __name__ == "__main__":
    main()
