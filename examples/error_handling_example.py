"""
错误处理算子示例

本示例展示了如何使用错误处理算子进行错误处理。
"""

import sys
import os
import time
import logging
import random
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入错误处理算子
from src.transcendental_tensor.error_handling import (
    ErrorSeverity, ErrorCategory, ErrorHandlingStrategy,
    ErrorContext, ErrorInfo, ErrorHandlingResult, ErrorHandlingConfig,
    ErrorHandler, ErrorHandlingOperator,
    handle_errors, retry, fallback, recover, suppress_errors, log_errors
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


# 自定义错误类型
class CustomError(Exception):
    """自定义错误"""
    def __init__(self, message: str, error_code: int = 1000):
        super().__init__(message)
        self.error_code = error_code
        self.details = {"timestamp": time.time()}


class ResourceNotFoundError(CustomError):
    """资源未找到错误"""
    def __init__(self, resource_id: str):
        super().__init__(f"Resource not found: {resource_id}", 1001)
        self.details["resource_id"] = resource_id


class ValidationError(CustomError):
    """验证错误"""
    def __init__(self, field: str, message: str):
        super().__init__(f"Validation error: {field} - {message}", 1002)
        self.details["field"] = field


# 示例函数
def divide(a: float, b: float) -> float:
    """除法函数"""
    if b == 0:
        raise ValueError("Division by zero")
    return a / b


def get_resource(resource_id: str) -> Dict[str, Any]:
    """获取资源函数"""
    if resource_id == "not_found":
        raise ResourceNotFoundError(resource_id)
    
    if resource_id == "invalid":
        raise ValidationError("resource_id", "Invalid resource ID format")
    
    return {"id": resource_id, "name": f"Resource {resource_id}", "value": random.randint(1, 100)}


def process_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """处理数据函数"""
    if "value" not in data:
        raise KeyError("Missing 'value' in data")
    
    if not isinstance(data["value"], (int, float)):
        raise TypeError("'value' must be a number")
    
    result = {"processed": True, "original": data, "result": data["value"] * 2}
    
    if random.random() < 0.3:
        # 随机失败
        raise RuntimeError("Random processing error")
    
    return result


def unreliable_network_call(endpoint: str) -> Dict[str, Any]:
    """不可靠的网络调用函数"""
    if random.random() < 0.5:
        # 随机失败
        if random.random() < 0.5:
            raise ConnectionError(f"Failed to connect to {endpoint}")
        else:
            raise TimeoutError(f"Timeout connecting to {endpoint}")
    
    return {"endpoint": endpoint, "status": "success", "data": {"value": random.randint(1, 100)}}


# 示例上下文提供者
def create_operation_context(func_name: str, *args, **kwargs) -> ErrorContext:
    """创建操作上下文"""
    return ErrorContext(
        component="example",
        operation=func_name,
        metadata={"args": str(args), "kwargs": str(kwargs)}
    )


# 示例恢复函数
def recover_process_data(func, args, kwargs, error):
    """恢复处理数据函数"""
    logger.warning(f"Recovering from error in {func.__name__}: {error}")
    
    # 获取原始数据
    data = args[0] if args else kwargs.get("data")
    
    if data is None:
        return {"processed": False, "error": str(error)}
    
    # 创建默认结果
    return {"processed": True, "original": data, "result": 0, "recovered": True}


# 示例备用函数
def fallback_network_call(endpoint: str) -> Dict[str, Any]:
    """备用网络调用函数"""
    logger.warning(f"Using fallback for network call to {endpoint}")
    return {"endpoint": endpoint, "status": "fallback", "data": {"value": 0}}


def main():
    """主函数"""
    logger.info("Starting error handling example")
    
    # 创建错误处理算子
    operator = ErrorHandlingOperator()
    
    # 注册自定义错误类型
    operator.register_error_type(CustomError, ErrorCategory.INTERNAL, ErrorSeverity.ERROR)
    operator.register_error_type(ResourceNotFoundError, ErrorCategory.RESOURCE, ErrorSeverity.WARNING)
    operator.register_error_type(ValidationError, ErrorCategory.VALIDATION, ErrorSeverity.ERROR)
    
    # 设置错误类型策略
    operator.set_strategy_for_error_type(ValueError, ErrorHandlingStrategy.PROPAGATE)
    operator.set_strategy_for_error_type(ResourceNotFoundError, ErrorHandlingStrategy.SUPPRESS)
    operator.set_strategy_for_error_type(ValidationError, ErrorHandlingStrategy.LOG_ONLY)
    
    # 设置错误类别策略
    operator.set_strategy_for_error_category(ErrorCategory.NETWORK, ErrorHandlingStrategy.RETRY)
    operator.set_strategy_for_error_category(ErrorCategory.TIMEOUT, ErrorHandlingStrategy.RETRY)
    
    # 示例1：基本错误处理
    logger.info("Example 1: Basic error handling")
    try:
        result = divide(10, 0)
    except Exception as e:
        context = operator.create_context("example", "divide", metadata={"a": 10, "b": 0})
        result = operator.handle_error(e, context)
        logger.info(f"Error handled: {result.error_info.error_type} - {result.error_info.error_message}")
    
    # 示例2：使用装饰器
    logger.info("Example 2: Using decorators")
    
    # 重试装饰器
    @operator.get_retry_decorator(max_retries=3, retry_delay=0.5)
    def retry_example():
        logger.info("Executing retry_example")
        if random.random() < 0.8:
            raise ConnectionError("Random connection error")
        return "Success"
    
    try:
        result = retry_example()
        logger.info(f"Retry result: {result}")
    except Exception as e:
        logger.error(f"Retry failed: {e}")
    
    # 备用装饰器
    @operator.get_fallback_decorator(fallback_func=fallback_network_call)
    def fallback_example(endpoint):
        return unreliable_network_call(endpoint)
    
    result = fallback_example("api.example.com")
    logger.info(f"Fallback result: {result}")
    
    # 恢复装饰器
    @operator.get_recover_decorator(recovery_func=recover_process_data)
    def recover_example(data):
        return process_data(data)
    
    result = recover_example({"value": 42})
    logger.info(f"Recover result: {result}")
    
    # 抑制错误装饰器
    @operator.get_suppress_decorator(error_types=[KeyError, TypeError], return_value={"error": True})
    def suppress_example(data):
        return process_data(data)
    
    result = suppress_example({})  # 缺少 'value' 键
    logger.info(f"Suppress result: {result}")
    
    # 记录错误装饰器
    @operator.get_log_decorator()
    def log_example():
        logger.info("Executing log_example")
        raise ValueError("Test error for logging")
    
    try:
        log_example()
    except Exception as e:
        logger.info(f"Caught logged error: {e}")
    
    # 示例3：自定义错误处理
    logger.info("Example 3: Custom error handling")
    
    # 注册自定义处理器
    def custom_resource_handler(error, error_info):
        logger.info(f"Custom handler for {error.__class__.__name__}: {error}")
        return {"error": True, "resource_id": error.details.get("resource_id"), "message": str(error)}
    
    operator.register_custom_handler(ResourceNotFoundError, custom_resource_handler)
    
    # 使用自定义处理器
    try:
        result = get_resource("not_found")
    except Exception as e:
        context = operator.create_context("example", "get_resource", metadata={"resource_id": "not_found"})
        result = operator.handle_error(e, context, ErrorHandlingStrategy.CUSTOM)
        logger.info(f"Custom handler result: {result.recovery_data}")
    
    logger.info("Error handling example completed")


if __name__ == "__main__":
    main()
