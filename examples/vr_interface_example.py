"""
VR接口算子示例

本示例展示了如何使用VR接口算子。
"""

import os
import sys
import logging
import json
from typing import Dict, List, Any, Optional

# 自定义JSON编码器
class CustomEncoder(json.JSONEncoder):
    def default(self, obj):
        if hasattr(obj, 'to_list'):
            return obj.to_list()
        elif hasattr(obj, 'to_dict'):
            return obj.to_dict()
        return super().default(obj)

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入VR接口算子
try:
    from src.rust_bindings import (
        RUST_AVAILABLE,
        Vector3,
        Transform,
        SceneObject,
        VRScene,
        InteractionEventType,
        InteractionEvent,
        InteractionHandler,
        RenderConfig,
        Renderer,
        TransformManager,
    )
    logger.info(f"成功导入VR接口算子，Rust可用: {RUST_AVAILABLE}")
except ImportError as e:
    logger.error(f"导入VR接口算子失败: {e}")
    sys.exit(1)

def test_vector3():
    """测试Vector3类"""
    logger.info("开始测试Vector3类...")

    # 创建向量
    v1 = Vector3(1.0, 2.0, 3.0)
    v2 = Vector3(4.0, 5.0, 6.0)

    # 测试向量操作
    v3 = v1 + v2
    v4 = v1 - v2
    v5 = v1 * 2.0
    v6 = v1 / 2.0

    logger.info(f"v1 = {v1}")
    logger.info(f"v2 = {v2}")
    logger.info(f"v1 + v2 = {v3}")
    logger.info(f"v1 - v2 = {v4}")
    logger.info(f"v1 * 2.0 = {v5}")
    logger.info(f"v1 / 2.0 = {v6}")

    # 测试向量方法
    dot = v1.dot(v2)
    cross = v1.cross(v2)
    mag = v1.magnitude()
    norm = v1.normalize()

    logger.info(f"v1 · v2 = {dot}")
    logger.info(f"v1 × v2 = {cross}")
    logger.info(f"||v1|| = {mag}")
    logger.info(f"v1/||v1|| = {norm}")

    # 测试静态方法
    zero = Vector3.zero()
    one = Vector3.one()

    logger.info(f"零向量 = {zero}")
    logger.info(f"单位向量 = {one}")

    logger.info("Vector3类测试完成")

def test_transform():
    """测试Transform类"""
    logger.info("开始测试Transform类...")

    # 创建变换
    t1 = Transform(
        Vector3(1.0, 2.0, 3.0),
        Vector3(0.0, 0.0, 0.0),
        Vector3(1.0, 1.0, 1.0)
    )

    t2 = Transform(
        Vector3(4.0, 5.0, 6.0),
        Vector3(0.0, 0.0, 0.0),
        Vector3(2.0, 2.0, 2.0)
    )

    # 测试变换方法
    matrix = t1.to_matrix()
    point = Vector3(1.0, 1.0, 1.0)
    transformed_point = t1.apply_to_point(point)
    combined = t1.combine(t2)

    logger.info(f"t1 = {t1}")
    logger.info(f"t2 = {t2}")
    logger.info(f"t1矩阵 = {matrix}")
    logger.info(f"点 = {point}")
    logger.info(f"变换后的点 = {transformed_point}")
    logger.info(f"t1 + t2 = {combined}")

    # 测试静态方法
    identity = Transform.identity()

    logger.info(f"单位变换 = {identity}")

    logger.info("Transform类测试完成")

def test_scene_object():
    """测试SceneObject类"""
    logger.info("开始测试SceneObject类...")

    # 创建场景对象
    root = SceneObject(
        "root",
        "根对象",
        "group",
        Transform(
            Vector3(0.0, 0.0, 0.0),
            Vector3(0.0, 0.0, 0.0),
            Vector3(1.0, 1.0, 1.0)
        )
    )

    child1 = SceneObject(
        "child1",
        "子对象1",
        "mesh",
        Transform(
            Vector3(1.0, 0.0, 0.0),
            Vector3(0.0, 0.0, 0.0),
            Vector3(0.5, 0.5, 0.5)
        )
    )

    child2 = SceneObject(
        "child2",
        "子对象2",
        "light",
        Transform(
            Vector3(0.0, 1.0, 0.0),
            Vector3(0.0, 0.0, 0.0),
            Vector3(1.0, 1.0, 1.0)
        )
    )

    # 添加子对象
    root.add_child(child1)
    root.add_child(child2)

    # 测试对象方法
    logger.info(f"根对象 = {root.name}")
    logger.info(f"子对象数量 = {len(root.get_children())}")

    # 设置属性
    root.set_property("visible", True)
    child1.set_property("material", "metal")
    child2.set_property("intensity", 0.8)

    # 获取属性
    logger.info(f"根对象可见性 = {root.get_property('visible')}")
    logger.info(f"子对象1材质 = {child1.get_property('material')}")
    logger.info(f"子对象2强度 = {child2.get_property('intensity')}")

    # 转换为字典
    root_dict = root.to_dict()
    logger.info(f"根对象字典 = {json.dumps(root_dict, indent=2, cls=CustomEncoder)}")

    logger.info("SceneObject类测试完成")

def test_vr_scene():
    """测试VRScene类"""
    logger.info("开始测试VRScene类...")

    # 创建VR场景
    scene = VRScene("scene1", "测试场景")

    # 创建场景对象
    obj1 = SceneObject(
        "obj1",
        "对象1",
        "mesh",
        Transform(
            Vector3(1.0, 0.0, 0.0),
            Vector3(0.0, 0.0, 0.0),
            Vector3(1.0, 1.0, 1.0)
        )
    )

    obj2 = SceneObject(
        "obj2",
        "对象2",
        "light",
        Transform(
            Vector3(0.0, 1.0, 0.0),
            Vector3(0.0, 0.0, 0.0),
            Vector3(1.0, 1.0, 1.0)
        )
    )

    # 添加根对象
    scene.add_root_object(obj1)
    scene.add_root_object(obj2)

    # 测试场景方法
    logger.info(f"场景 = {scene.name}")
    logger.info(f"根对象数量 = {len(scene.get_root_objects())}")

    # 设置属性
    scene.set_property("skybox", "sky.jpg")
    scene.set_property("gravity", Vector3(0.0, -9.8, 0.0))

    # 获取属性
    logger.info(f"场景天空盒 = {scene.get_property('skybox')}")
    logger.info(f"场景重力 = {scene.get_property('gravity')}")

    # 转换为字典
    scene_dict = scene.to_dict()
    logger.info(f"场景字典 = {json.dumps(scene_dict, indent=2, cls=CustomEncoder)}")

    logger.info("VRScene类测试完成")

def test_interaction():
    """测试交互类"""
    logger.info("开始测试交互类...")

    # 创建交互事件类型
    event_type = InteractionEventType.button_click()

    # 创建场景对象
    source = SceneObject(
        "controller",
        "控制器",
        "controller",
        Transform(
            Vector3(0.0, 1.0, 0.0),
            Vector3(0.0, 0.0, 0.0),
            Vector3(1.0, 1.0, 1.0)
        )
    )

    target = SceneObject(
        "button",
        "按钮",
        "button",
        Transform(
            Vector3(0.0, 1.0, 0.0),
            Vector3(0.0, 0.0, 0.0),
            Vector3(1.0, 1.0, 1.0)
        )
    )

    # 创建交互事件
    event = InteractionEvent(
        "event1",
        event_type,
        source,
        target,
        Vector3(0.0, 1.0, 0.0),
        {"button": "trigger", "pressure": 0.8}
    )

    # 测试事件方法
    logger.info(f"事件 = {event.id}")
    logger.info(f"事件类型 = {event.event_type}")
    logger.info(f"事件源 = {event.source.name}")
    logger.info(f"事件目标 = {event.target.name}")
    logger.info(f"事件位置 = {event.position}")
    logger.info(f"事件数据 = {event.data}")

    # 创建交互处理器
    handler = InteractionHandler("test_handler", 10)

    # 注册事件处理器
    def on_button_click(e):
        logger.info(f"按钮点击: {e.source.name} -> {e.target.name}")

    handler.register_handler(InteractionEventType.button_click(), on_button_click)

    # 处理事件
    handler.handle_event(event)

    # 测试历史
    logger.info(f"历史大小 = {len(handler.get_history())}")

    # 清除历史
    handler.clear_history()
    logger.info(f"清除历史后的大小 = {len(handler.get_history())}")

    logger.info("交互类测试完成")

def test_renderer():
    """测试渲染器类"""
    logger.info("开始测试渲染器类...")

    # 创建渲染配置
    config = RenderConfig(
        1920,
        1080,
        60.0,
        0.1,
        1000.0,
        Vector3(0.0, 0.0, 0.0),
        Vector3(0.2, 0.2, 0.2)
    )

    # 测试配置方法
    logger.info(f"配置 = {config.width}x{config.height}, FOV={config.fov}")
    logger.info(f"背景颜色 = {config.background_color}")
    logger.info(f"环境光 = {config.ambient_light}")

    # 创建渲染器
    renderer = Renderer("test_renderer", config)

    # 创建VR场景
    scene = VRScene("scene1", "测试场景")

    # 创建场景对象
    obj = SceneObject(
        "obj1",
        "对象1",
        "mesh",
        Transform(
            Vector3(1.0, 0.0, 0.0),
            Vector3(0.0, 0.0, 0.0),
            Vector3(1.0, 1.0, 1.0)
        )
    )

    # 添加根对象
    scene.add_root_object(obj)

    # 渲染场景
    result = renderer.render(scene)

    logger.info(f"渲染结果 = {json.dumps(result, indent=2, cls=CustomEncoder)}")

    logger.info("渲染器类测试完成")

def test_transform_manager():
    """测试变换管理器类"""
    logger.info("开始测试变换管理器类...")

    # 创建变换管理器
    manager = TransformManager("test_manager")

    # 创建变换
    t1 = Transform(
        Vector3(1.0, 2.0, 3.0),
        Vector3(0.0, 0.0, 0.0),
        Vector3(1.0, 1.0, 1.0)
    )

    t2 = Transform(
        Vector3(4.0, 5.0, 6.0),
        Vector3(0.0, 0.0, 0.0),
        Vector3(2.0, 2.0, 2.0)
    )

    # 设置变换
    manager.set_transform("obj1", t1)
    manager.set_transform("obj2", t2)

    # 获取变换
    logger.info(f"obj1变换 = {manager.get_transform('obj1')}")
    logger.info(f"obj2变换 = {manager.get_transform('obj2')}")

    # 获取所有变换
    all_transforms = manager.get_all_transforms()
    logger.info(f"所有变换 = {all_transforms}")

    # 移除变换
    removed = manager.remove_transform("obj1")
    logger.info(f"移除的变换 = {removed}")
    logger.info(f"移除后的变换数量 = {len(manager.get_all_transforms())}")

    logger.info("变换管理器类测试完成")

def main():
    """主函数"""
    logger.info(f"开始VR接口算子示例，Rust可用: {RUST_AVAILABLE}")

    # 测试Vector3类
    test_vector3()

    # 测试Transform类
    test_transform()

    # 测试SceneObject类
    test_scene_object()

    # 测试VRScene类
    test_vr_scene()

    # 测试交互类
    test_interaction()

    # 测试渲染器类
    test_renderer()

    # 测试变换管理器类
    test_transform_manager()

    logger.info("VR接口算子示例结束")

if __name__ == "__main__":
    main()
