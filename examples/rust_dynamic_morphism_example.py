"""
Rust动态形态算子示例

本示例展示了如何使用Rust实现的动态形态算子。
"""

import os
import sys
import logging
import json
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入动态形态算子
try:
    from src.rust_bindings import (
        RUST_AVAILABLE,
        MorphismType,
        DynamicMorphismBase,
        MorphismComposition,
        Environment,
        EnvironmentSensitive,
        FeedbackMechanism,
    )
    logger.info(f"成功导入动态形态算子，Rust可用: {RUST_AVAILABLE}")
except ImportError as e:
    logger.error(f"导入动态形态算子失败: {e}")
    sys.exit(1)

def test_dynamic_morphism_base():
    """测试动态形态基础类"""
    logger.info("开始测试动态形态基础类...")
    
    # 创建一个将字典转换为JSON字符串的形态
    def dict_to_json(obj: Dict) -> str:
        return json.dumps(obj, ensure_ascii=False)
    
    # 创建一个将JSON字符串转换为字典的形态
    def json_to_dict(obj: str) -> Dict:
        return json.loads(obj)
    
    # 创建动态形态
    morphism = DynamicMorphismBase(
        name="dict_to_json",
        morphism_type=MorphismType.isomorphism(),
        domain="dict",
        codomain="json",
        morphism_fn=dict_to_json,
        inverse_fn=json_to_dict,
    )
    
    # 测试形态
    test_dict = {"name": "张三", "age": 30, "city": "北京"}
    json_str = morphism.apply(test_dict)
    logger.info(f"字典: {test_dict}")
    logger.info(f"JSON: {json_str}")
    
    # 测试逆形态
    recovered_dict = morphism.apply_inverse(json_str)
    logger.info(f"恢复的字典: {recovered_dict}")
    
    # 测试属性
    morphism.set_property("author", "张三")
    morphism.set_property("version", "1.0")
    
    logger.info(f"形态属性: {morphism.get_all_properties()}")
    logger.info(f"形态名称: {morphism.name}")
    logger.info(f"形态类型: {morphism.morphism_type}")
    logger.info(f"形态域: {morphism.domain}")
    logger.info(f"形态余域: {morphism.codomain}")
    logger.info(f"形态是否可逆: {morphism.is_invertible()}")
    
    logger.info("动态形态基础类测试完成")

def test_morphism_composition():
    """测试形态组合类"""
    logger.info("开始测试形态组合类...")
    
    # 创建一个将字典转换为JSON字符串的形态
    dict_to_json = DynamicMorphismBase(
        name="dict_to_json",
        morphism_type=MorphismType.isomorphism(),
        domain="dict",
        codomain="json",
        morphism_fn=lambda obj: json.dumps(obj, ensure_ascii=False),
        inverse_fn=lambda obj: json.loads(obj),
    )
    
    # 创建一个将JSON字符串转换为压缩字符串的形态
    def json_to_compressed(obj: str) -> str:
        # 这里只是一个简单的模拟，实际应用中可能会使用真正的压缩算法
        return obj.replace(" ", "")
    
    def compressed_to_json(obj: str) -> str:
        # 这里只是一个简单的模拟，实际应用中可能会使用真正的解压缩算法
        return obj
    
    json_to_compressed_morphism = DynamicMorphismBase(
        name="json_to_compressed",
        morphism_type=MorphismType.isomorphism(),
        domain="json",
        codomain="compressed",
        morphism_fn=json_to_compressed,
        inverse_fn=compressed_to_json,
    )
    
    # 创建形态组合
    composition = MorphismComposition("dict_to_compressed")
    
    # 添加形态到组合
    composition.add_morphism(dict_to_json)
    composition.add_morphism(json_to_compressed_morphism)
    
    # 测试组合
    test_dict = {"name": "张三", "age": 30, "city": "北京"}
    compressed_str = composition.apply(test_dict)
    logger.info(f"字典: {test_dict}")
    logger.info(f"压缩字符串: {compressed_str}")
    
    # 测试逆组合
    recovered_dict = composition.apply_inverse(compressed_str)
    logger.info(f"恢复的字典: {recovered_dict}")
    
    # 测试属性
    composition.set_property("author", "张三")
    composition.set_property("version", "1.0")
    
    logger.info(f"组合属性: {composition.get_all_properties()}")
    logger.info(f"组合名称: {composition.name}")
    logger.info(f"组合域: {composition.domain()}")
    logger.info(f"组合余域: {composition.codomain()}")
    logger.info(f"组合是否可逆: {composition.is_invertible()}")
    logger.info(f"组合中形态的数量: {len(composition)}")
    
    logger.info("形态组合类测试完成")

def test_environment_sensitive():
    """测试环境敏感类"""
    logger.info("开始测试环境敏感类...")
    
    # 创建一个环境
    env = Environment("test_env")
    env.set_variable("language", "zh-CN")
    env.set_variable("format", "pretty")
    
    # 创建一个将字典转换为JSON字符串的形态，考虑环境变量
    def dict_to_json_with_env(obj: Dict) -> str:
        format_type = env.get_variable("format")
        if format_type == "pretty":
            return json.dumps(obj, ensure_ascii=False, indent=2)
        else:
            return json.dumps(obj, ensure_ascii=False)
    
    # 创建一个将JSON字符串转换为字典的形态
    def json_to_dict(obj: str) -> Dict:
        return json.loads(obj)
    
    # 创建基础形态
    base_morphism = DynamicMorphismBase(
        name="dict_to_json",
        morphism_type=MorphismType.isomorphism(),
        domain="dict",
        codomain="json",
        morphism_fn=dict_to_json_with_env,
        inverse_fn=json_to_dict,
    )
    
    # 创建环境敏感形态
    env_sensitive = EnvironmentSensitive(
        name="dict_to_json_env_sensitive",
        base_morphism=base_morphism,
        environment=env,
    )
    
    # 测试形态
    test_dict = {"name": "张三", "age": 30, "city": "北京"}
    json_str = env_sensitive.apply(test_dict)
    logger.info(f"字典: {test_dict}")
    logger.info(f"JSON (pretty): {json_str}")
    
    # 修改环境变量
    env.set_variable("format", "compact")
    
    # 再次测试形态
    json_str = env_sensitive.apply(test_dict)
    logger.info(f"JSON (compact): {json_str}")
    
    # 测试逆形态
    recovered_dict = env_sensitive.apply_inverse(json_str)
    logger.info(f"恢复的字典: {recovered_dict}")
    
    # 测试属性
    env_sensitive.set_property("author", "张三")
    env_sensitive.set_property("version", "1.0")
    
    logger.info(f"形态属性: {env_sensitive.get_all_properties()}")
    logger.info(f"形态名称: {env_sensitive.name}")
    logger.info(f"形态域: {env_sensitive.domain}")
    logger.info(f"形态余域: {env_sensitive.codomain}")
    logger.info(f"形态是否可逆: {env_sensitive.is_invertible()}")
    
    logger.info("环境敏感类测试完成")

def test_feedback_mechanism():
    """测试反馈机制类"""
    logger.info("开始测试反馈机制类...")
    
    # 创建一个将字典转换为JSON字符串的形态
    dict_to_json = DynamicMorphismBase(
        name="dict_to_json",
        morphism_type=MorphismType.isomorphism(),
        domain="dict",
        codomain="json",
        morphism_fn=lambda obj: json.dumps(obj, ensure_ascii=False),
        inverse_fn=lambda obj: json.loads(obj),
    )
    
    # 创建一个反馈函数
    def feedback_fn(input_obj: Dict, output_obj: str, error: Optional[str]) -> None:
        if error:
            logger.warning(f"转换失败: {error}")
        else:
            logger.info(f"转换成功: {input_obj} -> {output_obj}")
    
    # 创建反馈机制
    feedback = FeedbackMechanism(
        name="dict_to_json_feedback",
        base_morphism=dict_to_json,
        max_history_size=10,
        feedback_fn=feedback_fn,
    )
    
    # 测试形态
    test_dict = {"name": "张三", "age": 30, "city": "北京"}
    json_str = feedback.apply(test_dict)
    logger.info(f"字典: {test_dict}")
    logger.info(f"JSON: {json_str}")
    
    # 测试逆形态
    recovered_dict = feedback.apply_inverse(json_str)
    logger.info(f"恢复的字典: {recovered_dict}")
    
    # 测试历史
    logger.info(f"历史大小: {feedback.history_size()}")
    logger.info(f"历史: {feedback.get_history()}")
    
    # 清除历史
    feedback.clear_history()
    logger.info(f"清除历史后的大小: {feedback.history_size()}")
    
    # 测试属性
    feedback.set_property("author", "张三")
    feedback.set_property("version", "1.0")
    
    logger.info(f"形态属性: {feedback.get_all_properties()}")
    logger.info(f"形态名称: {feedback.name}")
    logger.info(f"形态域: {feedback.domain}")
    logger.info(f"形态余域: {feedback.codomain}")
    logger.info(f"形态是否可逆: {feedback.is_invertible()}")
    
    logger.info("反馈机制类测试完成")

def main():
    """主函数"""
    logger.info(f"开始Rust动态形态算子示例，Rust可用: {RUST_AVAILABLE}")
    
    # 测试动态形态基础类
    test_dynamic_morphism_base()
    
    # 测试形态组合类
    test_morphism_composition()
    
    # 测试环境敏感类
    test_environment_sensitive()
    
    # 测试反馈机制类
    test_feedback_mechanism()
    
    logger.info("Rust动态形态算子示例结束")

if __name__ == "__main__":
    main()
