#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
高级GIL策略基准测试脚本

使用改进的GIL检测逻辑，测试不同GIL管理策略在超越态计算框架中的性能表现
"""

import os
import sys
import time
import numpy as np
import threading
import multiprocessing
import logging
import argparse
from typing import Dict, Any, Callable, List, Tuple, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入GIL管理模块
try:
    from src.core.parallel.gil_manager import (
        nogil, release_gil, nogil_mode, gil_state,
        get_gil_status, optimize_gil_for_task, apply_gil_strategy,
        detect_nogil_support
    )
except ImportError as e:
    logger.warning(f"无法导入GIL管理模块: {e}")
    
    # 创建兼容性函数
    def detect_nogil_support():
        return {
            'has_nogil_support': False,
            'is_nogil_build': False,
            'is_nogil_enabled': False,
            'has_np_gil_state': False,
            'nogil_functionality_works': False,
            'numpy_gil_state_works': False
        }
        
    def nogil(func):
        return func
        
    def release_gil(func):
        return func
        
    def nogil_mode(enabled):
        class DummyContext:
            def __enter__(self): pass
            def __exit__(self, *args): pass
        return DummyContext()
        
    def gil_state(enabled):
        class DummyContext:
            def __enter__(self): pass
            def __exit__(self, *args): pass
        return DummyContext()
        
    def get_gil_status():
        return detect_nogil_support()
        
    def optimize_gil_for_task(task_type, task_size='medium'):
        return 'normal'
        
    def apply_gil_strategy(strategy, func):
        return func


# 超越态计算任务基类
class TTEComputationTask:
    """超越态计算任务基类"""
    
    def __init__(self, name: str, task_type: str, task_size: str = 'medium'):
        """
        初始化计算任务
        
        Args:
            name: 任务名称
            task_type: 任务类型
            task_size: 任务大小
        """
        self.name = name
        self.task_type = task_type
        self.task_size = task_size
    
    def run(self, *args, **kwargs) -> float:
        """
        运行任务并返回执行时间
        
        Returns:
            执行时间（秒）
        """
        raise NotImplementedError("子类必须实现run方法")
    
    def run_with_strategy(self, strategy: str, *args, **kwargs) -> float:
        """
        使用指定的GIL策略运行任务
        
        Args:
            strategy: GIL策略名称
            
        Returns:
            执行时间（秒）
        """
        if strategy == 'normal':
            return self.run(*args, **kwargs)
        elif strategy == 'nogil':
            with nogil_mode(True):
                return self.run(*args, **kwargs)
        elif strategy == 'numpy_gil':
            with gil_state(False):
                return self.run(*args, **kwargs)
        elif strategy == 'combined':
            with nogil_mode(True):
                with gil_state(False):
                    return self.run(*args, **kwargs)
        else:
            logger.warning(f"未知的GIL策略: {strategy}，使用正常模式")
            return self.run(*args, **kwargs)
    
    def run_with_optimized(self, *args, **kwargs) -> Tuple[float, str]:
        """
        使用自动优化的GIL策略运行任务
        
        Returns:
            执行时间（秒）和使用的策略名称
        """
        strategy = optimize_gil_for_task(self.task_type, self.task_size)
        logger.info(f"任务 '{self.name}' 自动选择的GIL策略: {strategy}")
        time_taken = self.run_with_strategy(strategy, *args, **kwargs)
        return time_taken, strategy


# 量子态演化任务
class QuantumEvolutionTask(TTEComputationTask):
    """量子态演化计算任务"""
    
    def __init__(self, state_size: int = 1024, iterations: int = 10, task_size: str = 'medium'):
        """
        初始化量子态演化任务
        
        Args:
            state_size: 量子态大小
            iterations: 迭代次数
            task_size: 任务大小
        """
        super().__init__("量子态演化", "quantum", task_size)
        self.state_size = state_size
        self.iterations = iterations
    
    def run(self) -> float:
        """
        运行量子态演化计算任务
        
        Returns:
            执行时间（秒）
        """
        total_time = 0
        for _ in range(self.iterations):
            # 创建随机量子态
            state = np.random.random(self.state_size) + 1j * np.random.random(self.state_size)
            state = state / np.linalg.norm(state)  # 归一化
            
            # 创建随机酉矩阵作为演化算子
            h = np.random.random((self.state_size, self.state_size)) + 1j * np.random.random((self.state_size, self.state_size))
            u = np.eye(self.state_size) + 1j * h
            u = u / np.linalg.norm(u, ord=2)  # 近似归一化
            
            # 执行量子演化
            start_time = time.time()
            evolved_state = np.dot(u, state)
            total_time += time.time() - start_time
        
        return total_time / self.iterations


# 全息重建任务
class HolographicReconstructionTask(TTEComputationTask):
    """全息重建计算任务"""
    
    def __init__(self, grid_size: int = 100, iterations: int = 5, task_size: str = 'medium'):
        """
        初始化全息重建任务
        
        Args:
            grid_size: 网格大小
            iterations: 迭代次数
            task_size: 任务大小
        """
        super().__init__("全息重建", "holographic", task_size)
        self.grid_size = grid_size
        self.iterations = iterations
    
    def run(self) -> float:
        """
        运行全息重建计算任务
        
        Returns:
            执行时间（秒）
        """
        total_time = 0
        for _ in range(self.iterations):
            # 创建随机全息数据
            hologram = np.random.random((self.grid_size, self.grid_size)) * np.exp(1j * np.random.random((self.grid_size, self.grid_size)) * 2 * np.pi)
            
            # 执行全息重建（使用FFT模拟）
            start_time = time.time()
            reconstruction = np.fft.fft2(hologram)
            reconstruction = np.fft.fftshift(reconstruction)
            total_time += time.time() - start_time
        
        return total_time / self.iterations


# 分形生长任务
class FractalGrowthTask(TTEComputationTask):
    """分形生长计算任务"""
    
    def __init__(self, iterations: int = 20, size: int = 500, task_size: str = 'medium'):
        """
        初始化分形生长任务
        
        Args:
            iterations: 迭代次数
            size: 网格大小
            task_size: 任务大小
        """
        super().__init__("分形生长", "fractal", task_size)
        self.iterations = iterations
        self.size = size
    
    def run(self) -> float:
        """
        运行分形生长计算任务
        
        Returns:
            执行时间（秒）
        """
        # 创建初始网格
        grid = np.zeros((self.size, self.size), dtype=np.int32)
        center = self.size // 2
        grid[center, center] = 1
        
        # 分形生长模拟
        start_time = time.time()
        for _ in range(self.iterations):
            # 创建新网格以存储更新
            new_grid = grid.copy()
            
            # 遍历网格
            for i in range(1, self.size-1):
                for j in range(1, self.size-1):
                    # 计算邻居数量
                    neighbors = np.sum(grid[i-1:i+2, j-1:j+2]) - grid[i, j]
                    
                    # 应用生长规则
                    if grid[i, j] == 0 and neighbors == 1:
                        new_grid[i, j] = 1
            
            # 更新网格
            grid = new_grid
        
        total_time = time.time() - start_time
        return total_time


# 并行场态演化任务
class ParallelFieldEvolutionTask(TTEComputationTask):
    """并行场态演化计算任务"""
    
    def __init__(self, field_size: int = 50, num_threads: int = 4, iterations: int = 5, task_size: str = 'medium'):
        """
        初始化并行场态演化任务
        
        Args:
            field_size: 场态大小
            num_threads: 线程数
            iterations: 迭代次数
            task_size: 任务大小
        """
        super().__init__("并行场态演化", "parallel", task_size)
        self.field_size = field_size
        self.num_threads = num_threads
        self.iterations = iterations
    
    def run(self) -> float:
        """
        运行并行场态演化计算任务
        
        Returns:
            执行时间（秒）
        """
        # 创建随机场态
        field = np.random.random((self.field_size, self.field_size, self.field_size))
        
        # 将场分割为多个区域
        chunks = []
        chunk_size = self.field_size // self.num_threads
        for i in range(self.num_threads):
            start = i * chunk_size
            end = (i + 1) * chunk_size if i < self.num_threads - 1 else self.field_size
            chunks.append((start, end))
        
        results = [None] * self.num_threads
        
        # 定义演化函数
        def evolve_chunk(field, start, end, chunk_id):
            chunk = field[start:end].copy()
            # 应用拉普拉斯算子（简化版）
            for _ in range(self.iterations):
                for i in range(1, chunk.shape[0]-1):
                    for j in range(1, chunk.shape[1]-1):
                        for k in range(1, chunk.shape[2]-1):
                            chunk[i, j, k] = 0.1 * (
                                chunk[i+1, j, k] + chunk[i-1, j, k] +
                                chunk[i, j+1, k] + chunk[i, j-1, k] +
                                chunk[i, j, k+1] + chunk[i, j, k-1] -
                                6 * chunk[i, j, k]
                            )
            results[chunk_id] = chunk
        
        # 执行并行演化
        start_time = time.time()
        threads = []
        for i, (start, end) in enumerate(chunks):
            t = threading.Thread(target=evolve_chunk, args=(field, start, end, i))
            threads.append(t)
            t.start()
        
        for t in threads:
            t.join()
        
        # 合并结果
        for i, (start, end) in enumerate(chunks):
            if results[i] is not None:
                field[start:end] = results[i]
        
        total_time = time.time() - start_time
        return total_time


# 基准测试管理器
class BenchmarkManager:
    """基准测试管理器"""
    
    def __init__(self):
        """初始化基准测试管理器"""
        self.tasks = []
        self.results = {}
    
    def add_task(self, task: TTEComputationTask) -> None:
        """
        添加任务
        
        Args:
            task: 计算任务
        """
        self.tasks.append(task)
    
    def run_benchmarks(self, strategies: Optional[List[str]] = None) -> None:
        """
        运行基准测试
        
        Args:
            strategies: 要测试的GIL策略列表，如果为None则使用默认策略
        """
        if strategies is None:
            strategies = ['normal', 'optimized']
            
            # 根据系统支持情况添加其他策略
            gil_status = get_gil_status()
            if gil_status.get('has_nogil_support', False):
                strategies.append('nogil')
            if gil_status.get('numpy_gil_state_works', False):
                strategies.append('numpy_gil')
            if (gil_status.get('has_nogil_support', False) and 
                gil_status.get('numpy_gil_state_works', False)):
                strategies.append('combined')
        
        for task in self.tasks:
            task_results = {}
            
            for strategy in strategies:
                if strategy == 'optimized':
                    logger.info(f"运行任务 '{task.name}' (自动优化模式，任务类型: {task.task_type}, 任务大小: {task.task_size})...")
                    time_taken, used_strategy = task.run_with_optimized()
                    task_results[strategy] = (time_taken, used_strategy)
                else:
                    logger.info(f"运行任务 '{task.name}' ({strategy}模式)...")
                    time_taken = task.run_with_strategy(strategy)
                    task_results[strategy] = (time_taken, strategy)
            
            self.results[task.name] = task_results
    
    def print_results(self) -> None:
        """打印基准测试结果"""
        for task_name, task_results in self.results.items():
            print(f"\n=== {task_name} 基准测试结果 ===")
            
            # 找出基准时间（正常模式）
            baseline, _ = task_results.get('normal', (0, ''))
            if baseline <= 0:
                print("无法获取基准时间")
                continue
            
            # 打印每种模式的结果和相对性能
            for mode, (time_taken, used_strategy) in sorted(task_results.items()):
                speedup = baseline / time_taken if time_taken > 0 else 0
                strategy_info = f"(使用策略: {used_strategy})" if mode == 'optimized' else ""
                print(f"{mode.ljust(10)}: {time_taken:.6f} 秒 (相对性能: {speedup:.2f}x) {strategy_info}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='超越态计算框架GIL策略基准测试')
    parser.add_argument('--quantum-size', type=int, default=512, help='量子态大小')
    parser.add_argument('--holo-size', type=int, default=100, help='全息网格大小')
    parser.add_argument('--fractal-size', type=int, default=200, help='分形网格大小')
    parser.add_argument('--field-size', type=int, default=50, help='场态大小')
    parser.add_argument('--iterations', type=int, default=3, help='迭代次数')
    parser.add_argument('--threads', type=int, default=4, help='线程数')
    parser.add_argument('--task-size', type=str, default='medium', choices=['small', 'medium', 'large'], help='任务大小')
    parser.add_argument('--strategies', type=str, nargs='+', help='要测试的GIL策略列表')
    args = parser.parse_args()
    
    # 打印系统信息
    print(f"Python版本: {sys.version}")
    print(f"NumPy版本: {np.__version__}")
    print(f"CPU核心数: {os.cpu_count()}")
    print(f"GIL状态: {get_gil_status()}")
    
    # 创建基准测试管理器
    benchmark_manager = BenchmarkManager()
    
    # 添加任务
    benchmark_manager.add_task(QuantumEvolutionTask(
        state_size=args.quantum_size,
        iterations=args.iterations,
        task_size=args.task_size
    ))
    
    benchmark_manager.add_task(HolographicReconstructionTask(
        grid_size=args.holo_size,
        iterations=args.iterations,
        task_size=args.task_size
    ))
    
    benchmark_manager.add_task(FractalGrowthTask(
        iterations=args.iterations,
        size=args.fractal_size,
        task_size=args.task_size
    ))
    
    benchmark_manager.add_task(ParallelFieldEvolutionTask(
        field_size=args.field_size,
        num_threads=args.threads,
        iterations=args.iterations,
        task_size=args.task_size
    ))
    
    # 运行基准测试
    benchmark_manager.run_benchmarks(args.strategies)
    
    # 打印结果
    benchmark_manager.print_results()


if __name__ == "__main__":
    main()
