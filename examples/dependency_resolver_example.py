"""
算子依赖解析示例

本示例展示了如何使用依赖解析器解析算子的依赖关系。
"""

import os
import sys
import logging
from typing import Dict, List, Any, Optional, Tuple, Union

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入算子注册表模块
try:
    from src.rust_bindings import (
        OperatorCategory,
        OperatorRegistry,
        OperatorMetadata,
        DependencyManager,
        get_global_registry,
        register_operator,
        get_operator,
        list_operators,
        get_operator_metadata,
    )
    from src.rust_bindings.operator_registry.dependency_resolver import (
        DependencyResolver,
        DependencyGraph,
        DependencyNode,
        get_global_dependency_resolver,
    )
    logger.info(f"成功导入算子注册表模块")
except ImportError as e:
    logger.error(f"导入算子注册表模块失败: {e}")
    sys.exit(1)

# 定义一些示例算子
def add(a, b):
    """加法算子"""
    return a + b

def subtract(a, b):
    """减法算子"""
    return a - b

def multiply(a, b):
    """乘法算子"""
    return a * b

def divide(a, b):
    """除法算子"""
    if b == 0:
        raise ValueError("除数不能为零")
    return a / b

def power(a, b):
    """幂运算算子"""
    return a ** b

def square_root(a):
    """平方根算子"""
    if a < 0:
        raise ValueError("不能对负数求平方根")
    return a ** 0.5

def matrix_multiply(a, b):
    """矩阵乘法算子"""
    import numpy as np
    return np.matmul(a, b)

def convolution(a, b):
    """卷积算子"""
    import numpy as np
    return np.convolve(a, b)

def test_dependency_graph():
    """测试依赖图"""
    logger.info("开始测试依赖图...")

    # 创建依赖图
    graph = DependencyGraph()

    # 添加节点
    graph.add_node("utility", "add", "1.0.0")
    graph.add_node("utility", "subtract", "1.0.0")
    graph.add_node("utility", "multiply", "1.0.0")
    graph.add_node("utility", "divide", "1.0.0")
    graph.add_node("utility", "power", "1.0.0")
    graph.add_node("utility", "square_root", "1.0.0")
    graph.add_node("numpy", "matrix_multiply", "1.0.0")
    graph.add_node("numpy", "convolution", "1.0.0")

    # 添加依赖关系
    graph.add_dependency("utility", "multiply", "1.0.0", "utility", "add", "1.0.0", ">=1.0.0")
    graph.add_dependency("utility", "divide", "1.0.0", "utility", "multiply", "1.0.0", ">=1.0.0")
    graph.add_dependency("utility", "power", "1.0.0", "utility", "multiply", "1.0.0", ">=1.0.0")
    graph.add_dependency("utility", "square_root", "1.0.0", "utility", "power", "1.0.0", ">=1.0.0")
    graph.add_dependency("numpy", "matrix_multiply", "1.0.0", "utility", "multiply", "1.0.0", ">=1.0.0")
    graph.add_dependency("numpy", "convolution", "1.0.0", "numpy", "matrix_multiply", "1.0.0", ">=1.0.0")

    # 获取所有节点
    nodes = graph.get_all_nodes()
    logger.info(f"所有节点: {nodes}")

    # 获取根节点
    root_nodes = graph.get_root_nodes()
    logger.info(f"根节点: {root_nodes}")

    # 获取叶节点
    leaf_nodes = graph.get_leaf_nodes()
    logger.info(f"叶节点: {leaf_nodes}")

    # 获取依赖链
    chain = graph.get_dependency_chain("numpy", "convolution", "1.0.0")
    logger.info(f"依赖链: {chain}")

    # 检查循环依赖
    cycles = graph.check_circular_dependencies()
    logger.info(f"循环依赖: {cycles}")

    # 解析依赖
    dependencies = graph.resolve_dependencies("numpy", "convolution", "1.0.0")
    logger.info(f"依赖列表: {dependencies}")

    # 转换为DOT格式
    dot = graph.to_dot()
    logger.info(f"DOT格式:\n{dot}")

    # 保存DOT文件
    dot_file = os.path.join(project_root, "examples", "dependency_graph.dot")
    with open(dot_file, "w") as f:
        f.write(dot)
    logger.info(f"DOT文件已保存到: {dot_file}")

    logger.info("依赖图测试完成")

def test_dependency_resolver():
    """测试依赖解析器"""
    logger.info("开始测试依赖解析器...")

    # 获取全局注册表
    registry = get_global_registry()

    # 注册算子
    register_operator(
        OperatorCategory.UTILITY,
        "add",
        add,
        "1.0.0",
        "加法算子",
        ["math", "basic"],
        [],
    )

    register_operator(
        OperatorCategory.UTILITY,
        "subtract",
        subtract,
        "1.0.0",
        "减法算子",
        ["math", "basic"],
        [],
    )

    register_operator(
        OperatorCategory.UTILITY,
        "multiply",
        multiply,
        "1.0.0",
        "乘法算子",
        ["math", "basic"],
        [("utility.add", ">=1.0.0")],
    )

    register_operator(
        OperatorCategory.UTILITY,
        "divide",
        divide,
        "1.0.0",
        "除法算子",
        ["math", "basic"],
        [("utility.multiply", ">=1.0.0")],
    )

    register_operator(
        OperatorCategory.UTILITY,
        "power",
        power,
        "1.0.0",
        "幂运算算子",
        ["math", "advanced"],
        [("utility.multiply", ">=1.0.0")],
    )

    register_operator(
        OperatorCategory.UTILITY,
        "square_root",
        square_root,
        "1.0.0",
        "平方根算子",
        ["math", "advanced"],
        [("utility.power", ">=1.0.0")],
    )

    register_operator(
        OperatorCategory.NUMPY,
        "matrix_multiply",
        matrix_multiply,
        "1.0.0",
        "矩阵乘法算子",
        ["math", "matrix"],
        [("utility.multiply", ">=1.0.0")],
    )

    register_operator(
        OperatorCategory.NUMPY,
        "convolution",
        convolution,
        "1.0.0",
        "卷积算子",
        ["math", "signal"],
        [("numpy.matrix_multiply", ">=1.0.0")],
    )

    # 创建依赖解析器
    resolver = DependencyResolver(registry)

    # 构建依赖图
    graph = resolver.build_dependency_graph()
    logger.info(f"依赖图节点数: {len(graph.get_all_nodes())}")

    # 解析依赖
    dependencies = resolver.resolve_dependencies(OperatorCategory.NUMPY, "convolution")
    logger.info(f"卷积算子的依赖: {dependencies}")

    # 检查循环依赖
    cycles = resolver.check_circular_dependencies()
    logger.info(f"循环依赖: {cycles}")

    # 获取依赖链
    chain = resolver.get_dependency_chain(OperatorCategory.NUMPY, "convolution")
    logger.info(f"依赖链: {chain}")

    # 获取依赖图的DOT格式
    dot = resolver.get_dependency_dot()

    # 保存DOT文件
    dot_file = os.path.join(project_root, "examples", "dependency_resolver.dot")
    with open(dot_file, "w") as f:
        f.write(dot)
    logger.info(f"DOT文件已保存到: {dot_file}")

    logger.info("依赖解析器测试完成")

def test_global_dependency_resolver():
    """测试全局依赖解析器"""
    logger.info("开始测试全局依赖解析器...")

    # 获取全局依赖解析器
    resolver = get_global_dependency_resolver()

    # 解析依赖
    dependencies = resolver.resolve_dependencies(OperatorCategory.NUMPY, "convolution")
    logger.info(f"卷积算子的依赖: {dependencies}")

    # 检查循环依赖
    cycles = resolver.check_circular_dependencies()
    logger.info(f"循环依赖: {cycles}")

    # 获取依赖链
    chain = resolver.get_dependency_chain(OperatorCategory.NUMPY, "convolution")
    logger.info(f"依赖链: {chain}")

    logger.info("全局依赖解析器测试完成")

def test_circular_dependency():
    """测试循环依赖"""
    logger.info("开始测试循环依赖...")

    # 获取全局注册表
    registry = get_global_registry()

    # 注册循环依赖的算子
    register_operator(
        OperatorCategory.UTILITY,
        "a",
        lambda: None,
        "1.0.0",
        "测试算子A",
        ["test"],
        [("utility.b", ">=1.0.0")],
    )

    register_operator(
        OperatorCategory.UTILITY,
        "b",
        lambda: None,
        "1.0.0",
        "测试算子B",
        ["test"],
        [("utility.c", ">=1.0.0")],
    )

    register_operator(
        OperatorCategory.UTILITY,
        "c",
        lambda: None,
        "1.0.0",
        "测试算子C",
        ["test"],
        [("utility.a", ">=1.0.0")],
    )

    # 创建依赖解析器
    resolver = DependencyResolver(registry)

    # 检查循环依赖
    cycles = resolver.check_circular_dependencies()
    logger.info(f"循环依赖: {cycles}")

    # 尝试解析依赖
    dependencies = resolver.resolve_dependencies(OperatorCategory.UTILITY, "a")
    logger.info(f"测试算子A的依赖: {dependencies}")

    logger.info("循环依赖测试完成")

def main():
    """主函数"""
    logger.info("开始算子依赖解析示例")

    # 测试依赖图
    test_dependency_graph()

    # 测试依赖解析器
    test_dependency_resolver()

    # 测试全局依赖解析器
    test_global_dependency_resolver()

    # 测试循环依赖
    test_circular_dependency()

    logger.info("算子依赖解析示例结束")

if __name__ == "__main__":
    main()
