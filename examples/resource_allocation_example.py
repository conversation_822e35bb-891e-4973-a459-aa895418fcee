"""
动态资源分配算子示例

本示例展示了如何使用动态资源分配算子进行资源管理。
"""

import sys
import os
import time
import logging
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入动态资源分配算子
from src.transcendental_tensor.performance_optimization.resource_allocation import (
    DynamicResourceAllocator,
    AdaptiveStrategy,
    MLPredictor,
    MRCAMAnalyzer,
    ElasticScaler,
    ResourceDependencyManager,
    DistributedCoordinator
)

from src.transcendental_tensor.performance_optimization.resource_allocation.models import (
    ResourceType,
    ResourceStatus,
    ResourcePriority,
    ResourceRequest,
    ResourcePool
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def allocation_callback(result: Dict[str, Any]) -> None:
    """
    分配回调函数
    
    参数:
        result (Dict[str, Any]): 分配结果
    """
    logger.info(f"Allocation callback: {result['status']} - {result['message']}")


def main():
    """主函数"""
    logger.info("Starting resource allocation example")
    
    # 创建组件
    adaptive_strategy = AdaptiveStrategy(name="ExampleStrategy")
    ml_predictor = MLPredictor(name="ExamplePredictor")
    mrcam_analyzer = MRCAMAnalyzer(name="ExampleAnalyzer")
    elastic_scaler = ElasticScaler(name="ExampleScaler")
    dependency_manager = ResourceDependencyManager(name="ExampleDependencyManager")
    distributed_coordinator = DistributedCoordinator(name="ExampleCoordinator")
    
    # 创建动态资源分配器
    allocator = DynamicResourceAllocator(
        name="ExampleAllocator",
        adaptive_strategy=adaptive_strategy,
        ml_predictor=ml_predictor,
        mrcam_analyzer=mrcam_analyzer,
        elastic_scaler=elastic_scaler,
        dependency_manager=dependency_manager,
        distributed_coordinator=distributed_coordinator
    )
    
    # 添加分配回调函数
    allocator.add_allocation_callback(allocation_callback)
    
    # 创建资源池
    cpu_pool = ResourcePool(
        pool_id="cpu_pool_1",
        resource_type=ResourceType.CPU,
        capacity=100.0,
        available=100.0
    )
    
    memory_pool = ResourcePool(
        pool_id="memory_pool_1",
        resource_type=ResourceType.MEMORY,
        capacity=1024.0,
        available=1024.0
    )
    
    # 添加资源池
    allocator.add_resource_pool(cpu_pool)
    allocator.add_resource_pool(memory_pool)
    
    logger.info(f"Resource pools: {len(allocator.get_resource_pools())}")
    
    # 创建资源请求
    cpu_request = ResourceRequest(
        request_id="cpu_request_1",
        resource_type=ResourceType.CPU,
        amount=10.0,
        priority=ResourcePriority.MEDIUM
    )
    
    memory_request = ResourceRequest(
        request_id="memory_request_1",
        resource_type=ResourceType.MEMORY,
        amount=256.0,
        priority=ResourcePriority.HIGH
    )
    
    # 分配资源
    cpu_result = allocator.allocate_resource(cpu_request)
    logger.info(f"CPU allocation result: {cpu_result['status']} - {cpu_result['message']}")
    
    memory_result = allocator.allocate_resource(memory_request)
    logger.info(f"Memory allocation result: {memory_result['status']} - {memory_result['message']}")
    
    # 获取分配
    cpu_allocation = allocator.get_allocation(cpu_result["allocation_id"])
    memory_allocation = allocator.get_allocation(memory_result["allocation_id"])
    
    logger.info(f"CPU allocation: {cpu_allocation.amount} from pool {cpu_allocation.pool_id}")
    logger.info(f"Memory allocation: {memory_allocation.amount} from pool {memory_allocation.pool_id}")
    
    # 获取资源池状态
    cpu_pool = allocator.get_resource_pool("cpu_pool_1")
    memory_pool = allocator.get_resource_pool("memory_pool_1")
    
    logger.info(f"CPU pool: capacity={cpu_pool.capacity}, available={cpu_pool.available}, allocated={cpu_pool.allocated}")
    logger.info(f"Memory pool: capacity={memory_pool.capacity}, available={memory_pool.available}, allocated={memory_pool.allocated}")
    
    # 分析资源使用情况
    cpu_pools = allocator.get_resource_pools(ResourceType.CPU)
    cpu_allocations = allocator.get_allocations(ResourceType.CPU)
    
    analysis = allocator.mrcam_analyzer.analyze_resource_usage(ResourceType.CPU, cpu_pools, cpu_allocations)
    logger.info(f"CPU analysis: {len(analysis['metrics'])} metrics, {len(analysis['bottlenecks'])} bottlenecks, {len(analysis['suggestions'])} suggestions")
    
    # 预测资源需求
    allocator.ml_predictor.record_data(ResourceType.CPU, "allocation", 10.0)
    allocator.ml_predictor.record_data(ResourceType.CPU, "allocation", 15.0)
    allocator.ml_predictor.record_data(ResourceType.CPU, "allocation", 20.0)
    
    prediction = allocator.ml_predictor.predict(ResourceType.CPU, "allocation")
    logger.info(f"CPU prediction: {prediction['prediction'] if prediction['success'] else 'Failed'}")
    
    # 评估伸缩需求
    evaluation = allocator.elastic_scaler.evaluate_scaling(ResourceType.CPU, cpu_pools, 0.5)
    logger.info(f"CPU scaling evaluation: {evaluation['action']}")
    
    # 释放资源
    cpu_release = allocator.release_resource(cpu_result["allocation_id"])
    logger.info(f"CPU release result: {cpu_release['success']} - {cpu_release['message']}")
    
    memory_release = allocator.release_resource(memory_result["allocation_id"])
    logger.info(f"Memory release result: {memory_release['success']} - {memory_release['message']}")
    
    # 获取资源池状态
    cpu_pool = allocator.get_resource_pool("cpu_pool_1")
    memory_pool = allocator.get_resource_pool("memory_pool_1")
    
    logger.info(f"CPU pool: capacity={cpu_pool.capacity}, available={cpu_pool.available}, allocated={cpu_pool.allocated}")
    logger.info(f"Memory pool: capacity={memory_pool.capacity}, available={memory_pool.available}, allocated={memory_pool.allocated}")
    
    # 清理
    cleanup_result = allocator.cleanup()
    logger.info(f"Cleanup result: {cleanup_result['expired_allocations']} expired allocations, {cleanup_result['expired_requests']} expired requests")
    
    logger.info("Resource allocation example completed")


if __name__ == "__main__":
    main()
