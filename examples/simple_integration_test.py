#!/usr/bin/env python3
"""
简单的集成测试脚本

该脚本测试自解释与可验证性算子的基本功能。
"""

import os
import sys
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('..'))

# 创建必要的目录
os.makedirs("src/operators/explanation", exist_ok=True)
os.makedirs("src/operators/verification", exist_ok=True)
os.makedirs("src/integration", exist_ok=True)

# 创建简单的算子类
class MultilevelExplanationOperator:
    def apply(self, input_data):
        return {
            "technical_explanation": "这是技术解释",
            "conceptual_explanation": "这是概念解释",
            "analogical_explanation": "这是类比解释",
            "combined_explanation": "这是综合解释"
        }

class ExplanationQualityOperator:
    def apply(self, input_data):
        return {
            "accuracy_score": 0.85,
            "completeness_score": 0.78,
            "consistency_score": 0.92,
            "understandability_score": 0.88,
            "overall_quality_score": 0.86
        }

class VisualizationExplanationOperator:
    def apply(self, input_data):
        return {
            "decision_tree": "决策树可视化",
            "feature_importance": "特征重要性可视化",
            "attention_heatmap": "注意力热图可视化"
        }

class MultiMethodVerificationOperator:
    def apply(self, input_data):
        return {
            "verification_results": {
                "property1": {
                    "result": "true",
                    "confidence": 0.9,
                    "method": "model_checking"
                }
            },
            "verification_properties": input_data.get("properties", []),
            "verification_methods_used": {
                "model_checking": 1,
                "theorem_proving": 0,
                "runtime_monitoring": 0
            }
        }

class ConsistencyVerificationOperator:
    def apply(self, input_data):
        return {
            "pairwise_consistency": True,
            "global_consistency": True,
            "inconsistencies": [],
            "is_consistent": True,
            "consistency_score": 0.95
        }

class RealtimeVerificationOperator:
    def apply(self, input_data):
        return {
            "verification_results": {},
            "violations": [],
            "monitoring_active": True,
            "history_size": 0
        }

# 创建简单的决策类
class Decision:
    def __init__(self, entity_id, options, context=None, metadata=None):
        self.decision_id = f"decision_{entity_id}_{id(self)}"
        self.entity_id = entity_id
        self.options = options
        self.context = context or {}
        self.metadata = metadata or {}
        self.weights = {}
        self.selected_option = None
        self.confidence = 0.0
        self.status = "created"
        self.execution_result = None

# 创建简单的决策协调系统
class DecisionCoordinator:
    def __init__(self):
        self.decisions = {}
        self.stats = {"decisions_created": 0, "decisions_executed": 0}
    
    def create_decision(self, entity_id, options, context=None, metadata=None):
        decision = Decision(entity_id, options, context, metadata)
        self.decisions[decision.decision_id] = decision
        self.stats["decisions_created"] += 1
        return decision
    
    def evaluate_decision(self, decision):
        if decision.decision_id not in self.decisions:
            self.decisions[decision.decision_id] = decision
        
        # 计算权重
        weights = {}
        for option in decision.options:
            weights[option] = 1.0
        
        # 应用自定义权重
        if decision.weights:
            for option, weight in decision.weights.items():
                if option in weights:
                    weights[option] = weight
        
        # 选择最佳选项
        if weights:
            selected_option = max(weights.items(), key=lambda x: x[1])[0]
            confidence = weights[selected_option] / sum(weights.values())
        else:
            selected_option = None
            confidence = 0.0
        
        # 更新决策
        decision.selected_option = selected_option
        decision.confidence = confidence
        decision.status = "evaluated"
        
        return decision
    
    def execute_decision(self, decision_id, option=None):
        decision = self.decisions.get(decision_id)
        if not decision:
            return None
        
        # 使用指定选项或已选择的选项
        option = option or decision.selected_option
        
        # 更新决策
        decision.status = "executed"
        decision.execution_result = {"option": option, "success": True}
        
        self.stats["decisions_executed"] += 1
        
        return decision.execution_result
    
    def coordinate_decisions(self, decisions, strategy=None):
        if not decisions:
            return None
        
        # 选择置信度最高的决策
        best_decision = max(decisions, key=lambda d: getattr(d, "confidence", 0.0))
        
        # 创建协调决策
        coordinated_decision = Decision(
            entity_id="coordinator",
            options={"coordinated": True},
            context={"source_decisions": [d.decision_id for d in decisions]},
            metadata={"strategy": str(strategy)}
        )
        
        # 设置选项和置信度
        coordinated_decision.selected_option = "coordinated"
        coordinated_decision.confidence = getattr(best_decision, "confidence", 0.5)
        coordinated_decision.status = "coordinated"
        
        return coordinated_decision

# 创建简单的推理引擎
class ReasoningEngine:
    def __init__(self):
        self.verification_properties = [
            {
                "id": "logical_consistency",
                "type": "safety",
                "expression": "G(result is consistent with input and context)"
            },
            {
                "id": "completeness",
                "type": "liveness",
                "expression": "F(result addresses all aspects of input)"
            }
        ]
        
        self.consistency_properties = [
            {
                "id": "prop_consistency",
                "type": "consistency",
                "expression": "result is consistent"
            }
        ]
        
        self.monitoring_properties = [
            {
                "id": "resource_usage",
                "type": "safety",
                "expression": "G(memory_usage < 90%)"
            }
        ]
    
    def reason(self, input_data, context=None, **kwargs):
        # 简单的推理逻辑
        if isinstance(input_data, dict):
            result = {k: v for k, v in input_data.items()}
            result["reasoning_applied"] = True
        elif isinstance(input_data, list):
            result = [item for item in input_data]
            result.append("reasoning_applied")
        else:
            result = f"{input_data} (reasoning_applied)"
        
        return result
    
    def evaluate_result(self, result, context=None):
        # 简单的评估逻辑
        if isinstance(result, dict):
            result["evaluation_applied"] = True
        elif isinstance(result, list):
            result.append("evaluation_applied")
        else:
            result = f"{result} (evaluation_applied)"
        
        return result
    
    def handle_violations(self, violations):
        # 简单的违规处理逻辑
        logger.warning(f"处理违规: {violations}")

# 创建集成类
class ExplanationVerificationIntegration:
    def __init__(self, use_rust=False):
        self.use_rust = use_rust
        
        # 创建算子实例
        self.multilevel_explanation_operator = MultilevelExplanationOperator()
        self.explanation_quality_operator = ExplanationQualityOperator()
        self.visualization_operator = VisualizationExplanationOperator()
        self.multi_method_verification_operator = MultiMethodVerificationOperator()
        self.consistency_verification_operator = ConsistencyVerificationOperator()
        self.realtime_verification_operator = RealtimeVerificationOperator()
        
        # 集成状态
        self.integration_status = {
            "decision_coordinator_integrated": False,
            "reasoning_engine_integrated": False,
            "registry_integrated": False
        }
        
        logger.info("自解释与可验证性算子集成类已初始化")
    
    def integrate_with_decision_coordinator(self, decision_coordinator):
        """将算子集成到决策协调系统中"""
        # 保存原始方法
        original_evaluate_decision = decision_coordinator.evaluate_decision
        
        # 定义包装方法
        def evaluate_decision_with_explanation(decision):
            # 调用原始方法
            result = original_evaluate_decision(decision)
            
            # 生成决策解释
            explanation_input = {
                "model_output": {
                    "decision": decision.__dict__,
                    "result": result.__dict__ if result else {},
                    "confidence": getattr(decision, "confidence", 0.5),
                    "features": {
                        "options": getattr(decision, "options", {}),
                        "weights": getattr(decision, "weights", {}),
                        "context": getattr(decision, "context", {})
                    }
                },
                "explanation_level": "all",
                "user_expertise": "intermediate"
            }
            
            explanation_result = self.multilevel_explanation_operator.apply(explanation_input)
            
            # 将解释添加到决策中
            result.explanation = explanation_result
            
            return result
        
        # 替换方法
        decision_coordinator.evaluate_decision = evaluate_decision_with_explanation
        
        # 更新集成状态
        self.integration_status["decision_coordinator_integrated"] = True
        
        logger.info("自解释与可验证性算子已成功集成到决策协调系统")
        return True
    
    def integrate_with_reasoning_engine(self, reasoning_engine):
        """将算子集成到推理引擎中"""
        # 保存原始方法
        original_reason = reasoning_engine.reason
        
        # 定义包装方法
        def reason_with_verification(input_data, context=None, **kwargs):
            # 调用原始方法
            result = original_reason(input_data, context, **kwargs)
            
            # 验证推理结果
            system_state = {
                "variables": {
                    "input": input_data,
                    "context": context,
                    "result": result
                }
            }
            
            verification_input = {
                "state": system_state,
                "properties": reasoning_engine.verification_properties
            }
            
            verification_result = self.multi_method_verification_operator.apply(verification_input)
            
            # 将验证结果添加到推理结果中
            if isinstance(result, dict):
                result["verification"] = verification_result
            
            return result
        
        # 替换方法
        reasoning_engine.reason = reason_with_verification
        
        # 更新集成状态
        self.integration_status["reasoning_engine_integrated"] = True
        
        logger.info("自解释与可验证性算子已成功集成到推理引擎")
        return True
    
    def get_integration_status(self):
        """获取集成状态"""
        return self.integration_status.copy()

# 测试集成
def test_integration():
    logger.info("=" * 80)
    logger.info("测试自解释与可验证性算子集成")
    logger.info("=" * 80)
    
    # 创建集成实例
    integration = ExplanationVerificationIntegration()
    
    # 创建决策协调系统
    decision_coordinator = DecisionCoordinator()
    
    # 创建推理引擎
    reasoning_engine = ReasoningEngine()
    
    # 集成到决策协调系统
    integration.integrate_with_decision_coordinator(decision_coordinator)
    
    # 集成到推理引擎
    integration.integrate_with_reasoning_engine(reasoning_engine)
    
    # 获取集成状态
    status = integration.get_integration_status()
    logger.info(f"集成状态: {status}")
    
    # 测试决策解释
    logger.info("\n" + "=" * 80)
    logger.info("测试决策解释")
    logger.info("=" * 80)
    
    # 创建决策
    decision = decision_coordinator.create_decision(
        entity_id="test_entity",
        options={"option1": "value1", "option2": "value2"},
        context={"context_key": "context_value"},
        metadata={"metadata_key": "metadata_value"}
    )
    
    # 设置权重
    decision.weights = {
        "option1": 0.7,
        "option2": 0.3
    }
    
    # 评估决策
    evaluated_decision = decision_coordinator.evaluate_decision(decision)
    
    # 打印解释
    logger.info(f"选择选项: {evaluated_decision.selected_option}, 置信度: {evaluated_decision.confidence:.2f}")
    logger.info("\n解释:")
    logger.info(f"技术解释: {evaluated_decision.explanation.get('technical_explanation', '')}")
    logger.info(f"概念解释: {evaluated_decision.explanation.get('conceptual_explanation', '')}")
    logger.info(f"类比解释: {evaluated_decision.explanation.get('analogical_explanation', '')}")
    
    # 测试推理验证
    logger.info("\n" + "=" * 80)
    logger.info("测试推理验证")
    logger.info("=" * 80)
    
    # 准备输入数据
    input_data = {
        "facts": [
            "所有人都是凡人",
            "苏格拉底是人"
        ],
        "query": "苏格拉底是凡人吗？"
    }
    
    context = {
        "reasoning_type": "deductive",
        "confidence_threshold": 0.8
    }
    
    # 执行推理
    result = reasoning_engine.reason(input_data, context)
    
    # 打印推理结果
    logger.info(f"推理结果: {result}")
    
    # 打印验证结果
    if isinstance(result, dict) and "verification" in result:
        logger.info("\n验证结果:")
        verification = result["verification"]
        
        if "verification_results" in verification:
            for prop_id, prop_result in verification["verification_results"].items():
                logger.info(f"属性 {prop_id}: {prop_result.get('result', 'unknown')}, 置信度: {prop_result.get('confidence', 0):.2f}")
        
        if "verification_methods_used" in verification:
            logger.info(f"使用的验证方法: {verification['verification_methods_used']}")
    
    logger.info("\n" + "=" * 80)
    logger.info("测试完成")
    logger.info("=" * 80)

if __name__ == "__main__":
    test_integration()
