"""
注册算子示例

本脚本用于注册一些示例算子，以便测试算子注册表。
"""

import os
import sys
import logging

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入算子注册表模块
try:
    from src.rust_bindings import (
        OperatorCategory,
        OperatorRegistry,
        get_global_registry,
        register_operator,
        get_operator,
        list_operators,
        get_operator_metadata,
    )
    logger.info(f"成功导入算子注册表模块")
except ImportError as e:
    logger.error(f"导入算子注册表模块失败: {e}")
    sys.exit(1)

# 导入示例算子
from examples.sample_operators import add, multiply, divide, power, Calculator

def main():
    """主函数"""
    logger.info("开始注册算子")
    
    # 注册算子
    register_operator(
        OperatorCategory.UTILITY,
        "add",
        add,
        "1.0.0",
        "加法算子",
        ["math", "basic"],
        [],
    )
    
    register_operator(
        OperatorCategory.UTILITY,
        "multiply",
        multiply,
        "1.0.0",
        "乘法算子",
        ["math", "basic"],
        [],
    )
    
    register_operator(
        OperatorCategory.UTILITY,
        "divide",
        divide,
        "1.0.0",
        "除法算子",
        ["math", "basic"],
        [("utility.multiply", ">=1.0.0")],
    )
    
    register_operator(
        OperatorCategory.UTILITY,
        "power",
        power,
        "1.0.0",
        "幂运算算子",
        ["math", "advanced"],
        [("utility.multiply", ">=1.0.0")],
    )
    
    register_operator(
        OperatorCategory.UTILITY,
        "calculator",
        Calculator(),
        "1.0.0",
        "计算器类",
        ["math", "class"],
        [("utility.add", ">=1.0.0"), ("utility.multiply", ">=1.0.0")],
    )
    
    # 列出所有算子
    operators = list_operators()
    logger.info(f"已注册的算子: {operators}")
    
    # 获取算子
    add_op = get_operator(OperatorCategory.UTILITY, "add")
    logger.info(f"获取到的加法算子: {add_op}")
    logger.info(f"1 + 2 = {add_op(1, 2)}")
    
    # 获取算子元数据
    add_metadata = get_operator_metadata(OperatorCategory.UTILITY, "add")
    logger.info(f"加法算子元数据: {add_metadata}")
    
    logger.info("算子注册完成")

if __name__ == "__main__":
    main()
