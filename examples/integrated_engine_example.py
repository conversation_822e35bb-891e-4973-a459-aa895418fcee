#!/usr/bin/env python3
"""
集成后的思维引擎示例脚本

该脚本展示了如何使用集成了自解释与可验证性算子的思维引擎。
"""

import os
import sys
import logging
import argparse
from typing import Dict, Any, List, Optional, Tuple, Union

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('..'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 尝试导入集成模块
try:
    from src.integration.explanation_verification_integration import (
        create_integration,
        integrate_with_engine
    )
except ImportError as e:
    logger.error(f"导入集成模块失败: {e}")
    sys.exit(1)

# 尝试导入思维引擎组件
try:
    # 决策协调系统
    from src.transcendental_tensor.multi_scale_coordination.decision_coordination import (
        DecisionCoordinator,
        Decision,
        CoordinationStrategy
    )
    
    # 推理引擎
    from src.core.reasoning.reasoning_engine import ReasoningEngine
except ImportError as e:
    logger.error(f"导入思维引擎组件失败: {e}")
    sys.exit(1)


def create_integrated_engine(use_rust: bool = True) -> Tuple[DecisionCoordinator, ReasoningEngine]:
    """
    创建集成了自解释与可验证性算子的思维引擎
    
    参数:
        use_rust: 是否使用Rust实现
        
    返回:
        (决策协调系统, 推理引擎)
    """
    logger.info("创建集成了自解释与可验证性算子的思维引擎...")
    
    # 创建集成实例
    integration = create_integration(use_rust=use_rust)
    
    # 创建决策协调系统
    decision_coordinator = DecisionCoordinator()
    
    # 创建推理引擎
    reasoning_engine = ReasoningEngine()
    
    # 集成到思维引擎
    status = integrate_with_engine(
        integration,
        decision_coordinator,
        reasoning_engine
    )
    
    logger.info(f"集成状态: {status}")
    
    return decision_coordinator, reasoning_engine


def demonstrate_decision_making(decision_coordinator: DecisionCoordinator):
    """
    演示决策制定过程
    
    参数:
        decision_coordinator: 决策协调系统
    """
    logger.info("=" * 80)
    logger.info("演示决策制定过程")
    logger.info("=" * 80)
    
    # 创建实体
    entity_id = "demo_entity"
    
    # 创建决策
    options = {
        "option1": "增加资源分配",
        "option2": "减少资源分配",
        "option3": "保持当前资源分配"
    }
    
    context = {
        "current_resource_usage": 0.75,
        "system_load": 0.65,
        "priority": "high"
    }
    
    metadata = {
        "decision_type": "resource_allocation",
        "importance": "high"
    }
    
    logger.info(f"创建决策: 实体={entity_id}, 选项={options}, 上下文={context}")
    decision = decision_coordinator.create_decision(
        entity_id=entity_id,
        options=options,
        context=context,
        metadata=metadata
    )
    
    # 设置权重
    decision.weights = {
        "option1": 0.7,
        "option2": 0.2,
        "option3": 0.1
    }
    
    # 评估决策
    logger.info("评估决策...")
    evaluated_decision = decision_coordinator.evaluate_decision(decision)
    
    logger.info(f"评估结果: 选择选项={evaluated_decision.selected_option}, 置信度={evaluated_decision.confidence:.2f}")
    
    # 打印解释
    if hasattr(evaluated_decision, "explanation"):
        logger.info("\n解释:")
        logger.info(f"技术解释: {evaluated_decision.explanation.get('technical_explanation', '')}")
        logger.info(f"概念解释: {evaluated_decision.explanation.get('conceptual_explanation', '')}")
        logger.info(f"类比解释: {evaluated_decision.explanation.get('analogical_explanation', '')}")
    
    # 执行决策
    logger.info("\n执行决策...")
    result = decision_coordinator.execute_decision(decision.decision_id)
    
    logger.info(f"执行结果: {result}")
    
    # 打印解释质量
    if hasattr(evaluated_decision, "explanation_quality"):
        logger.info("\n解释质量:")
        logger.info(f"准确性: {evaluated_decision.explanation_quality.get('accuracy_score', 0):.2f}")
        logger.info(f"完整性: {evaluated_decision.explanation_quality.get('completeness_score', 0):.2f}")
        logger.info(f"一致性: {evaluated_decision.explanation_quality.get('consistency_score', 0):.2f}")
        logger.info(f"可理解性: {evaluated_decision.explanation_quality.get('understandability_score', 0):.2f}")
        logger.info(f"总体质量: {evaluated_decision.explanation_quality.get('overall_quality_score', 0):.2f}")
    
    # 创建多个决策
    logger.info("\n创建多个决策...")
    decisions = [evaluated_decision]
    
    for i in range(2):
        new_decision = decision_coordinator.create_decision(
            entity_id=f"{entity_id}_{i}",
            options=options,
            context=context,
            metadata=metadata
        )
        
        # 设置不同的权重
        new_decision.weights = {
            "option1": 0.3 + i * 0.1,
            "option2": 0.5 - i * 0.1,
            "option3": 0.2
        }
        
        # 评估决策
        new_evaluated_decision = decision_coordinator.evaluate_decision(new_decision)
        
        logger.info(f"决策 {i+1}: 选择选项={new_evaluated_decision.selected_option}, 置信度={new_evaluated_decision.confidence:.2f}")
        
        decisions.append(new_evaluated_decision)
    
    # 协调决策
    logger.info("\n协调决策...")
    coordinated_decision = decision_coordinator.coordinate_decisions(decisions)
    
    logger.info(f"协调结果: 选择选项={coordinated_decision.selected_option}, 置信度={coordinated_decision.confidence:.2f}")
    
    # 打印可视化
    if hasattr(coordinated_decision, "visualization"):
        logger.info("\n可视化:")
        for key, value in coordinated_decision.visualization.items():
            if isinstance(value, str) and len(value) < 100:
                logger.info(f"{key}: {value}")
            else:
                logger.info(f"{key}: [复杂可视化数据]")


def demonstrate_reasoning(reasoning_engine: ReasoningEngine):
    """
    演示推理过程
    
    参数:
        reasoning_engine: 推理引擎
    """
    logger.info("\n" + "=" * 80)
    logger.info("演示推理过程")
    logger.info("=" * 80)
    
    # 准备输入数据
    input_data = {
        "facts": [
            "所有人都是凡人",
            "苏格拉底是人"
        ],
        "query": "苏格拉底是凡人吗？"
    }
    
    context = {
        "reasoning_type": "deductive",
        "confidence_threshold": 0.8
    }
    
    logger.info(f"输入数据: {input_data}")
    logger.info(f"上下文: {context}")
    
    # 执行推理
    logger.info("\n执行推理...")
    result = reasoning_engine.reason(input_data, context)
    
    # 打印推理结果
    logger.info(f"推理结果: {result}")
    
    # 打印验证结果
    if isinstance(result, dict) and "verification" in result:
        logger.info("\n验证结果:")
        verification = result["verification"]
        
        if "verification_results" in verification:
            for prop_id, prop_result in verification["verification_results"].items():
                logger.info(f"属性 {prop_id}: {prop_result.get('result', 'unknown')}, 置信度: {prop_result.get('confidence', 0):.2f}")
        
        if "verification_methods_used" in verification:
            logger.info(f"使用的验证方法: {verification['verification_methods_used']}")
    
    # 评估结果
    logger.info("\n评估结果...")
    evaluated_result = reasoning_engine.evaluate_result(result)
    
    logger.info(f"评估后的结果: {evaluated_result}")
    
    # 打印一致性结果
    if isinstance(evaluated_result, dict) and "consistency" in evaluated_result:
        logger.info("\n一致性结果:")
        consistency = evaluated_result["consistency"]
        
        logger.info(f"全局一致性: {consistency.get('global_consistency', False)}")
        logger.info(f"一致性分数: {consistency.get('consistency_score', 0):.2f}")
        
        if "inconsistencies" in consistency and consistency["inconsistencies"]:
            logger.info("检测到的不一致:")
            for inconsistency in consistency["inconsistencies"]:
                logger.info(f"  - {inconsistency}")
        else:
            logger.info("未检测到不一致")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='集成后的思维引擎示例')
    parser.add_argument('--use-rust', action='store_true', help='使用Rust实现')
    args = parser.parse_args()
    
    # 创建集成后的思维引擎
    decision_coordinator, reasoning_engine = create_integrated_engine(use_rust=args.use_rust)
    
    # 演示决策制定过程
    demonstrate_decision_making(decision_coordinator)
    
    # 演示推理过程
    demonstrate_reasoning(reasoning_engine)


if __name__ == "__main__":
    main()
