"""
混沌系统稳定控制器示例

本脚本展示了如何使用混沌系统稳定控制器。
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import sys
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入我们的实现
from src.algorithms.chaos_control import (
    ChaosSystemController,
    ModelFactory,
    ControlAnalyzer,
    create_test_system,
    calculate_lyapunov_exponents,
    is_chaotic
)


def run_control(system_type='lorenz', control_method='ogy', 
               max_iterations=200, time_step=0.01, stability_threshold=1e-3):
    """
    运行混沌系统控制
    
    参数:
        system_type: 系统类型，可选值为'lorenz', 'rossler', 'chua'
        control_method: 控制方法，可选值为'ogy', 'delayed_feedback', 'adaptive', 'predictive'
        max_iterations: 最大迭代次数
        time_step: 时间步长
        stability_threshold: 稳定性阈值
    
    返回:
        控制结果
    """
    # 创建测试系统
    system, parameters, initial_state, target_state = create_test_system(
        system_type=system_type,
        seed=42
    )
    
    # 创建控制器
    controller = ChaosSystemController(
        control_method=control_method,
        stability_threshold=stability_threshold,
        max_iterations=max_iterations,
        time_step=time_step,
        use_parallel=True,
        num_workers=4,
        use_cache=True,
        cache_size=10
    )
    
    # 创建输入数据
    input_data = {
        'system': system,
        'parameters': parameters,
        'initial_state': initial_state,
        'target_state': target_state
    }
    
    # 执行控制
    print(f"执行混沌系统控制...")
    print(f"  系统类型: {system_type}")
    print(f"  控制方法: {control_method}")
    print(f"  最大迭代次数: {max_iterations}")
    print(f"  时间步长: {time_step}")
    print(f"  稳定性阈值: {stability_threshold}")
    
    start_time = time.time()
    result = controller.compute(input_data)
    end_time = time.time()
    
    print(f"控制完成，耗时: {end_time - start_time:.2f}秒")
    
    # 提取结果
    controlled_states = result['controlled_states']
    control_signals = result['control_signals']
    final_state = result['final_state']
    is_stabilized = result['is_stabilized']
    iterations = result['iterations']
    stability_measures = result['stability_measures']
    analysis = result['analysis']
    performance = result['performance']
    
    # 打印结果
    print(f"控制结果:")
    print(f"  是否稳定: {is_stabilized}")
    print(f"  迭代次数: {iterations}")
    print(f"  最终控制误差: {analysis['control_error']:.6f}")
    print(f"  平均控制误差: {analysis['mean_error']:.6f}")
    print(f"  控制信号能量: {analysis['control_signal_energy']:.6f}")
    print(f"  最终稳定性度量: {analysis['stability_measure']:.6f}")
    print(f"  是否是混沌的: {analysis['is_chaotic']}")
    print(f"  最大李雅普诺夫指数: {analysis['max_lyapunov_exponent']:.6f}")
    print(f"  控制效率: {analysis['control_efficiency']:.6f}")
    
    # 打印性能指标
    print(f"性能指标:")
    print(f"  总时间: {performance['total_time']:.2f}秒")
    print(f"  模型构建时间: {performance['model_construction_time']:.2f}秒")
    print(f"  控制时间: {performance['control_time']:.2f}秒")
    print(f"  分析时间: {performance['analysis_time']:.2f}秒")
    print(f"  内存使用: {performance['memory_usage']:.2f}MB")
    
    # 创建模型
    model_factory = ModelFactory()
    model = model_factory.create_model(system, parameters)
    
    # 创建分析器
    analyzer = ControlAnalyzer()
    
    # 绘制控制结果
    fig_control = analyzer.plot_control_results(
        controlled_states, control_signals, target_state, 
        stability_measures, time_step
    )
    
    # 绘制相位图
    fig_phase = analyzer.plot_phase_portrait(
        controlled_states, target_state
    )
    
    # 创建输出目录
    os.makedirs('output', exist_ok=True)
    
    # 保存图形
    fig_control.savefig(f'output/control_results_{system_type}_{control_method}.png')
    fig_phase.savefig(f'output/phase_portrait_{system_type}_{control_method}.png')
    
    return result


def compare_control_methods(system_type='lorenz'):
    """
    比较不同的控制方法
    
    参数:
        system_type: 系统类型，可选值为'lorenz', 'rossler', 'chua'
    """
    # 控制方法列表
    control_methods = ['ogy', 'delayed_feedback', 'adaptive', 'predictive']
    
    # 存储结果
    results = {}
    
    # 对每种控制方法执行控制
    for method in control_methods:
        print(f"\n===== 使用{method}控制方法 =====")
        result = run_control(
            system_type=system_type,
            control_method=method,
            max_iterations=100,
            time_step=0.01,
            stability_threshold=1e-3
        )
        results[method] = result
    
    # 创建分析器
    analyzer = ControlAnalyzer()
    
    # 绘制控制方法比较
    fig_comparison = analyzer.plot_control_comparison(results)
    
    # 保存图形
    fig_comparison.savefig(f'output/control_methods_comparison_{system_type}.png')
    
    # 显示图形
    plt.show()


def compare_systems():
    """比较不同的混沌系统"""
    # 系统类型列表
    system_types = ['lorenz', 'rossler', 'chua']
    
    # 存储结果
    results = {}
    
    # 对每种系统类型执行控制
    for system_type in system_types:
        print(f"\n===== 使用{system_type}系统 =====")
        result = run_control(
            system_type=system_type,
            control_method='ogy',
            max_iterations=100,
            time_step=0.01,
            stability_threshold=1e-3
        )
        results[system_type] = result
    
    # 比较不同系统的结果
    plt.figure(figsize=(12, 8))
    
    # 绘制控制误差历史
    plt.subplot(2, 2, 1)
    for system_type in system_types:
        plt.plot(
            results[system_type]['analysis']['error_history'],
            label=f"{system_type}"
        )
    plt.xlabel('迭代次数')
    plt.ylabel('控制误差')
    plt.title('控制误差历史')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 绘制稳定性度量历史
    plt.subplot(2, 2, 2)
    for system_type in system_types:
        plt.plot(
            results[system_type]['stability_measures'],
            label=f"{system_type}"
        )
    plt.xlabel('迭代次数')
    plt.ylabel('稳定性度量')
    plt.title('稳定性度量历史')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 绘制控制信号能量
    plt.subplot(2, 2, 3)
    energies = [results[system_type]['analysis']['control_signal_energy'] for system_type in system_types]
    plt.bar(system_types, energies)
    plt.xlabel('系统类型')
    plt.ylabel('控制信号能量')
    plt.title('控制信号能量')
    plt.grid(True, alpha=0.3)
    
    # 绘制最大李雅普诺夫指数
    plt.subplot(2, 2, 4)
    lyapunovs = [results[system_type]['analysis']['max_lyapunov_exponent'] for system_type in system_types]
    plt.bar(system_types, lyapunovs)
    plt.xlabel('系统类型')
    plt.ylabel('最大李雅普诺夫指数')
    plt.title('最大李雅普诺夫指数')
    plt.grid(True, alpha=0.3)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图形
    plt.savefig('output/systems_comparison.png')
    
    # 显示图形
    plt.show()


def explore_parameter_space(system_type='lorenz', control_method='ogy'):
    """
    探索参数空间
    
    参数:
        system_type: 系统类型，可选值为'lorenz', 'rossler', 'chua'
        control_method: 控制方法，可选值为'ogy', 'delayed_feedback', 'adaptive', 'predictive'
    """
    # 创建测试系统
    system, parameters, initial_state, target_state = create_test_system(
        system_type=system_type,
        seed=42
    )
    
    # 创建控制器
    controller = ChaosSystemController(
        control_method=control_method,
        stability_threshold=1e-3,
        max_iterations=100,
        time_step=0.01
    )
    
    # 创建输入数据
    input_data = {
        'system': system,
        'parameters': parameters,
        'initial_state': initial_state,
        'target_state': target_state
    }
    
    # 控制参数列表
    if control_method == 'ogy':
        param_name = 'control_gain'
        param_values = [0.05, 0.1, 0.2, 0.5]
    elif control_method == 'delayed_feedback':
        param_name = 'delay_steps'
        param_values = [1, 2, 3, 4]
    elif control_method == 'adaptive':
        param_name = 'adaptation_rate'
        param_values = [0.005, 0.01, 0.02, 0.05]
    elif control_method == 'predictive':
        param_name = 'prediction_horizon'
        param_values = [1, 3, 5, 10]
    else:
        raise ValueError(f"不支持的控制方法: {control_method}")
    
    # 存储结果
    results = {}
    
    # 对每个参数值执行控制
    for param_value in param_values:
        print(f"\n===== {param_name} = {param_value} =====")
        
        # 设置控制参数
        control_parameters = {param_name: param_value}
        
        # 执行控制
        result = controller.compute(
            input_data,
            control_parameters=control_parameters
        )
        
        results[param_value] = result
    
    # 比较不同参数值的结果
    plt.figure(figsize=(12, 8))
    
    # 绘制控制误差历史
    plt.subplot(2, 2, 1)
    for param_value in param_values:
        plt.plot(
            results[param_value]['analysis']['error_history'],
            label=f"{param_name} = {param_value}"
        )
    plt.xlabel('迭代次数')
    plt.ylabel('控制误差')
    plt.title('控制误差历史')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 绘制稳定性度量历史
    plt.subplot(2, 2, 2)
    for param_value in param_values:
        plt.plot(
            results[param_value]['stability_measures'],
            label=f"{param_name} = {param_value}"
        )
    plt.xlabel('迭代次数')
    plt.ylabel('稳定性度量')
    plt.title('稳定性度量历史')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 绘制控制信号能量
    plt.subplot(2, 2, 3)
    energies = [results[param_value]['analysis']['control_signal_energy'] for param_value in param_values]
    plt.bar([str(val) for val in param_values], energies)
    plt.xlabel(param_name)
    plt.ylabel('控制信号能量')
    plt.title('控制信号能量')
    plt.grid(True, alpha=0.3)
    
    # 绘制控制效率
    plt.subplot(2, 2, 4)
    efficiencies = [results[param_value]['analysis']['control_efficiency'] for param_value in param_values]
    plt.bar([str(val) for val in param_values], efficiencies)
    plt.xlabel(param_name)
    plt.ylabel('控制效率')
    plt.title('控制效率')
    plt.grid(True, alpha=0.3)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图形
    plt.savefig(f'output/parameter_space_{system_type}_{control_method}_{param_name}.png')
    
    # 显示图形
    plt.show()


def analyze_chaotic_behavior():
    """分析混沌行为"""
    # 系统类型列表
    system_types = ['lorenz', 'rossler', 'chua']
    
    # 存储结果
    lyapunov_exponents = {}
    is_chaotic_result = {}
    
    # 对每种系统类型分析混沌行为
    for system_type in system_types:
        # 创建测试系统
        system, parameters, initial_state, _ = create_test_system(
            system_type=system_type,
            seed=42
        )
        
        # 计算李雅普诺夫指数
        lyapunov = calculate_lyapunov_exponents(
            system, parameters, initial_state, num_iterations=1000, time_step=0.01
        )
        lyapunov_exponents[system_type] = lyapunov
        
        # 判断是否是混沌的
        chaotic = is_chaotic(
            system, parameters, initial_state, num_iterations=1000, time_step=0.01
        )
        is_chaotic_result[system_type] = chaotic
        
        print(f"\n===== {system_type}系统 =====")
        print(f"  李雅普诺夫指数: {lyapunov}")
        print(f"  最大李雅普诺夫指数: {np.max(lyapunov):.6f}")
        print(f"  是否是混沌的: {chaotic}")
    
    # 绘制李雅普诺夫指数
    plt.figure(figsize=(12, 6))
    
    # 绘制李雅普诺夫指数
    plt.subplot(1, 2, 1)
    for system_type in system_types:
        plt.bar(
            [f"{system_type} λ{i+1}" for i in range(len(lyapunov_exponents[system_type]))],
            lyapunov_exponents[system_type]
        )
    plt.axhline(y=0, color='r', linestyle='--')
    plt.xlabel('系统和指数')
    plt.ylabel('李雅普诺夫指数')
    plt.title('李雅普诺夫指数')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    
    # 绘制最大李雅普诺夫指数
    plt.subplot(1, 2, 2)
    max_lyapunovs = [np.max(lyapunov_exponents[system_type]) for system_type in system_types]
    bars = plt.bar(system_types, max_lyapunovs)
    
    # 为柱状图添加颜色
    for i, bar in enumerate(bars):
        if is_chaotic_result[system_types[i]]:
            bar.set_color('red')
        else:
            bar.set_color('blue')
    
    plt.axhline(y=0, color='r', linestyle='--')
    plt.xlabel('系统类型')
    plt.ylabel('最大李雅普诺夫指数')
    plt.title('最大李雅普诺夫指数')
    plt.grid(True, alpha=0.3)
    
    # 添加图例
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor='red', label='混沌的'),
        Patch(facecolor='blue', label='非混沌的')
    ]
    plt.legend(handles=legend_elements)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图形
    plt.savefig('output/chaotic_behavior_analysis.png')
    
    # 显示图形
    plt.show()


def main():
    """主函数"""
    # 设置随机数种子，确保结果可重现
    np.random.seed(42)
    
    # 创建输出目录
    os.makedirs('output', exist_ok=True)
    
    # 运行单个控制
    print("\n===== 运行单个控制 =====")
    run_control(
        system_type='lorenz',
        control_method='ogy',
        max_iterations=200,
        time_step=0.01,
        stability_threshold=1e-3
    )
    
    # 比较不同的控制方法
    print("\n===== 比较不同的控制方法 =====")
    compare_control_methods(
        system_type='lorenz'
    )
    
    # 比较不同的混沌系统
    print("\n===== 比较不同的混沌系统 =====")
    compare_systems()
    
    # 探索参数空间
    print("\n===== 探索参数空间 =====")
    explore_parameter_space(
        system_type='lorenz',
        control_method='ogy'
    )
    
    # 分析混沌行为
    print("\n===== 分析混沌行为 =====")
    analyze_chaotic_behavior()
    
    print("\n示例完成！")


if __name__ == "__main__":
    main()
