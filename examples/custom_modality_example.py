"""
自定义模态示例

本示例展示了如何扩展多模态融合系统以支持新的数据类型。
这里我们实现了一个图数据模态，用于处理网络结构数据。
"""

import numpy as np
import networkx as nx
from typing import Dict, Any
import logging

from src.transcendental_tensor.multimodal_fusion.multimodal_fusion_operator import (
    MultimodalFusionOperator,
    ModalityType,
    ModalityConfig
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GraphModalityEncoder:
    """图数据编码器"""
    
    def __init__(self,
                 dimension: int = 16,
                 use_spectral: bool = True):
        """
        初始化图编码器
        
        参数:
            dimension: 编码维度
            use_spectral: 是否使用谱方法
        """
        self.dimension = dimension
        self.use_spectral = use_spectral
        
    def encode(self, graph: nx.Graph) -> np.ndarray:
        """
        编码图数据
        
        参数:
            graph: NetworkX图对象
            
        返回:
            编码向量
        """
        features = []
        
        # 1. 基本特征
        features.extend([
            graph.number_of_nodes(),  # 节点数
            graph.number_of_edges(),  # 边数
            nx.density(graph),        # 密度
            nx.average_clustering(graph)  # 聚类系数
        ])
        
        # 2. 谱特征
        if self.use_spectral:
            # 计算拉普拉斯矩阵特征值
            laplacian = nx.normalized_laplacian_matrix(graph)
            eigvals = np.linalg.eigvals(laplacian.toarray())
            
            # 取前k个特征值
            k = min(8, len(eigvals))
            features.extend(sorted(eigvals)[:k])
            
        # 3. 中心性特征
        # 度中心性
        degree_centrality = nx.degree_centrality(graph)
        features.append(np.mean(list(degree_centrality.values())))
        
        # 介数中心性
        betweenness_centrality = nx.betweenness_centrality(graph)
        features.append(np.mean(list(betweenness_centrality.values())))
        
        # 接近中心性
        closeness_centrality = nx.closeness_centrality(graph)
        features.append(np.mean(list(closeness_centrality.values())))
        
        # 确保维度匹配
        features = np.array(features)
        if len(features) < self.dimension:
            # 填充
            features = np.pad(
                features,
                (0, self.dimension - len(features)),
                mode='constant'
            )
        else:
            # 截断
            features = features[:self.dimension]
            
        return features
        
class CustomGraphFusion:
    """图数据融合示例"""
    
    def __init__(self):
        self.fusion_op = MultimodalFusionOperator()
        
        # 注册标准模态
        self.fusion_op.register_modality(
            "quantum",
            ModalityConfig(
                type=ModalityType.QUANTUM,
                dimension=8
            )
        )
        
        # 注册图模态
        self.fusion_op.register_modality(
            "graph",
            ModalityConfig(
                type=ModalityType.CUSTOM,
                dimension=16,
                encoding_params={
                    "use_spectral": True
                }
            )
        )
        
        # 创建图编码器
        self.graph_encoder = GraphModalityEncoder(dimension=16)
        
    def process_data(self,
                    quantum_data: np.ndarray,
                    graph_data: nx.Graph) -> Dict[str, Any]:
        """
        处理和融合数据
        
        参数:
            quantum_data: 量子态数据
            graph_data: 图数据
            
        返回:
            融合结果
        """
        try:
            # 编码量子数据
            quantum_result = self.fusion_op.encode_modality(
                quantum_data,
                "quantum"
            )
            
            # 编码图数据
            graph_features = self.graph_encoder.encode(graph_data)
            graph_result = {
                "success": True,
                "modality": "graph",
                "encoded_data": graph_features,
                "metadata": {
                    "type": "CUSTOM",
                    "dimension": 16
                }
            }
            
            # 融合数据
            fusion_result = self.fusion_op.fuse_modalities([
                quantum_result,
                graph_result
            ])
            
            return fusion_result
            
        except Exception as e:
            logger.error(f"处理失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
            
def main():
    """主函数"""
    # 创建融合器
    fusion = CustomGraphFusion()
    
    # 生成示例数据
    # 1. 量子数据
    quantum_data = np.random.rand(4) + 1j * np.random.rand(4)
    quantum_data = quantum_data / np.linalg.norm(quantum_data)
    
    # 2. 图数据
    graph = nx.random_geometric_graph(20, 0.2)
    
    # 进行融合
    result = fusion.process_data(quantum_data, graph)
    
    # 输出结果
    if result["success"]:
        logger.info("融合成功!")
        logger.info(f"融合质量: {result['fusion_quality']:.4f}")
        logger.info(f"融合维度: {len(result['fused_data'])}")
    else:
        logger.error(f"融合失败: {result.get('error', '未知错误')}")
        
if __name__ == "__main__":
    main()