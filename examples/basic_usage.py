#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超越态思维引擎算子库基本使用示例

本示例展示了超越态思维引擎算子库的基本用法，包括：
1. 创建和使用变换算子
2. 创建和使用演化算子
3. 组合多个算子
4. 使用优化工具
"""

import numpy as np
import matplotlib.pyplot as plt
import time

# 导入算子库模块
# 注意：在实际使用中，导入路径可能不同
from src.operators.transform import TransformOperator
from src.operators.evolution import EvolutionOperator
from src.operators.optimization.fusion import fuse_operators
from src.operators.optimization.parallel import parallelize
from src.operators.optimization.cache import cache_result


def main():
    """主函数"""
    print("超越态思维引擎算子库基本使用示例")
    print("=" * 50)

    # 创建示例数据
    print("\n1. 创建示例数据")
    data = create_sample_data()
    print(f"数据形状: {data.shape}")
    print(f"数据范围: [{np.min(data)}, {np.max(data)}]")

    # 使用变换算子
    print("\n2. 使用变换算子")
    transform = create_transform_operator()
    transformed_data = apply_transform(transform, data)
    print(f"变换后数据形状: {transformed_data.shape}")
    print(f"变换后数据范围: [{np.min(transformed_data)}, {np.max(transformed_data)}]")

    # 使用演化算子
    print("\n3. 使用演化算子")
    evolution = create_evolution_operator()
    evolved_data = apply_evolution(evolution, transformed_data)
    print(f"演化后数据形状: {evolved_data.shape}")
    print(f"演化后数据范围: [{np.min(evolved_data)}, {np.max(evolved_data)}]")

    # 组合多个算子
    print("\n4. 组合多个算子")
    # 4.1 顺序应用
    start_time = time.time()
    result1 = apply_sequential(transform, evolution, data)
    sequential_time = time.time() - start_time
    print(f"顺序应用耗时: {sequential_time:.6f}秒")

    # 4.2 使用算子融合
    start_time = time.time()
    result2 = apply_fused(transform, evolution, data)
    fused_time = time.time() - start_time
    print(f"融合应用耗时: {fused_time:.6f}秒")
    print(f"加速比: {sequential_time / fused_time:.2f}x")

    # 验证结果一致性
    print(f"结果一致性: {np.allclose(result1, result2, rtol=1e-5)}")

    # 使用优化工具
    print("\n5. 使用优化工具")
    # 5.1 使用并行化
    start_time = time.time()
    result3 = apply_parallel(transform, evolution, data)
    parallel_time = time.time() - start_time
    print(f"并行应用耗时: {parallel_time:.6f}秒")
    print(f"加速比: {sequential_time / parallel_time:.2f}x")

    # 5.2 使用缓存
    print("\n5.2 使用缓存")
    # 第一次调用（计算）
    start_time = time.time()
    result4 = apply_cached(transform, evolution, data)
    first_call_time = time.time() - start_time
    print(f"第一次调用耗时: {first_call_time:.6f}秒")

    # 第二次调用（从缓存获取）
    start_time = time.time()
    result5 = apply_cached(transform, evolution, data)
    second_call_time = time.time() - start_time
    print(f"第二次调用耗时: {second_call_time:.6f}秒")
    print(f"加速比: {first_call_time / second_call_time:.2f}x")

    # 可视化结果
    print("\n6. 可视化结果")
    visualize_results(data, transformed_data, evolved_data)

    print("\n示例完成！")


def create_sample_data(num_samples=1000, dimension=3):
    """创建示例数据"""
    # 创建随机数据
    data = np.random.random((num_samples, dimension))
    
    # 添加一些结构
    t = np.linspace(0, 10, num_samples)
    data[:, 0] = np.sin(t) + 0.1 * np.random.randn(num_samples)
    data[:, 1] = np.cos(t) + 0.1 * np.random.randn(num_samples)
    data[:, 2] = t / 10 + 0.1 * np.random.randn(num_samples)
    
    return data


def create_transform_operator():
    """创建变换算子"""
    # 创建线性变换算子
    transform = TransformOperator(
        transform_type='linear',
        dimension=3,
        parameters={
            'matrix': np.array([
                [0.8, -0.6, 0.0],
                [0.6, 0.8, 0.0],
                [0.0, 0.0, 1.0]
            ]),
            'offset': np.array([0.5, 0.5, 0.0])
        }
    )
    
    return transform


def create_evolution_operator():
    """创建演化算子"""
    # 创建微分方程演化算子
    evolution = EvolutionOperator(
        evolution_type='differential_equation',
        dimension=3,
        parameters={
            'equation': lambda t, x: np.array([-0.1 * x[0], -0.1 * x[1], 0.1 * (1 - x[2])]),
            'time_step': 0.1,
            'num_steps': 10,
            'method': 'rk4'
        }
    )
    
    return evolution


def apply_transform(transform, data):
    """应用变换算子"""
    print(f"应用变换: {transform.get_parameters()}")
    return transform.apply(data)


def apply_evolution(evolution, data):
    """应用演化算子"""
    print(f"应用演化: {evolution.get_parameters()}")
    return evolution.apply(data)


def apply_sequential(transform, evolution, data):
    """顺序应用多个算子"""
    print("顺序应用变换和演化算子")
    transformed = transform.apply(data)
    evolved = evolution.apply(transformed)
    return evolved


def apply_fused(transform, evolution, data):
    """使用算子融合"""
    print("使用算子融合")
    fused_operator = fuse_operators([transform, evolution])
    return fused_operator.apply(data)


@parallelize(mode='thread', max_workers=4)
def apply_parallel(transform, evolution, data):
    """使用并行化"""
    print("使用并行化")
    transformed = transform.apply(data)
    evolved = evolution.apply(transformed)
    return evolved


@cache_result
def apply_cached(transform, evolution, data):
    """使用缓存"""
    print("使用缓存")
    transformed = transform.apply(data)
    evolved = evolution.apply(transformed)
    return evolved


def visualize_results(original_data, transformed_data, evolved_data):
    """可视化结果"""
    # 创建3D图
    fig = plt.figure(figsize=(15, 5))
    
    # 原始数据
    ax1 = fig.add_subplot(131, projection='3d')
    ax1.scatter(original_data[:, 0], original_data[:, 1], original_data[:, 2], c='b', marker='o', alpha=0.6)
    ax1.set_title('原始数据')
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.set_zlabel('Z')
    
    # 变换后数据
    ax2 = fig.add_subplot(132, projection='3d')
    ax2.scatter(transformed_data[:, 0], transformed_data[:, 1], transformed_data[:, 2], c='g', marker='o', alpha=0.6)
    ax2.set_title('变换后数据')
    ax2.set_xlabel('X')
    ax2.set_ylabel('Y')
    ax2.set_zlabel('Z')
    
    # 演化后数据
    ax3 = fig.add_subplot(133, projection='3d')
    ax3.scatter(evolved_data[:, 0], evolved_data[:, 1], evolved_data[:, 2], c='r', marker='o', alpha=0.6)
    ax3.set_title('演化后数据')
    ax3.set_xlabel('X')
    ax3.set_ylabel('Y')
    ax3.set_zlabel('Z')
    
    plt.tight_layout()
    plt.savefig('results.png')
    print("结果已保存到 'results.png'")
    plt.close()


if __name__ == "__main__":
    main()
