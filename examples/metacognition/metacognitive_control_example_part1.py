"""
元认知控制示例 - 第一部分

本示例展示了如何使用元认知控制机制，包括监控控制、评估控制和自适应控制。
这是第一部分，主要展示基础设置和监控控制。
"""

import numpy as np
import logging
import time
from typing import Any, Dict, List, Optional, Callable, Tuple

from src.core.metacognition import (
    CognitiveLevel, MetacognitiveProcess,
    NewCognitiveState as CognitiveState, 
    NewMetacognitiveState as MetacognitiveState,
    CognitiveProcess, NewMetacognitiveProcess as MetacognitiveProcess,
    MetacognitionFactory, MetacognitionManager
)
from src.core.metacognition.control import (
    MetacognitiveController, ControlStrategy, ControlAction
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    logger.info("元认知控制示例 - 第一部分")
    
    # 创建元认知系统工厂
    factory = MetacognitionFactory()
    
    # 创建元认知系统管理器
    manager = MetacognitionManager(manager_id="metacognition_manager")
    
    # 第一部分：创建控制器
    logger.info("\n第一部分：创建控制器")
    
    # 创建元认知控制器
    controller = MetacognitiveController(controller_id="metacognitive_controller")
    logger.info(f"创建元认知控制器: {controller}")
    
    # 第二部分：创建认知状态和元认知状态
    logger.info("\n第二部分：创建认知状态和元认知状态")
    
    # 创建感知层级的认知状态
    perception_data = {
        "image": np.random.rand(28, 28),  # 模拟图像数据
        "noise_level": 0.2,
        "timestamp": time.time()
    }
    
    perception_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.PERCEPTION,
        data=perception_data,
        metadata={"description": "视觉感知状态", "source": "camera"}
    )
    logger.info(f"创建感知状态: {perception_state}")
    
    # 创建注意力层级的认知状态
    attention_data = {
        "focus_region": [10, 10, 5, 5],  # 模拟注意力焦点区域 [x, y, width, height]
        "focus_intensity": 0.6,
        "distraction_level": 0.3,
        "timestamp": time.time()
    }
    
    attention_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.ATTENTION,
        data=attention_data,
        metadata={"description": "视觉注意力状态", "source": "attention_mechanism"}
    )
    logger.info(f"创建注意力状态: {attention_state}")
    
    # 创建记忆层级的认知状态
    memory_data = {
        "memories": ["apple", "banana", "orange"],
        "activation_levels": [0.7, 0.5, 0.3],
        "retrieval_speed": 0.6,
        "timestamp": time.time()
    }
    
    memory_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.MEMORY,
        data=memory_data,
        metadata={"description": "工作记忆状态", "source": "memory_system"}
    )
    logger.info(f"创建记忆状态: {memory_state}")
    
    # 创建学习层级的认知状态
    learning_data = {
        "learning_rate": 0.01,
        "batch_size": 32,
        "epochs": 10,
        "loss_history": [0.5, 0.4, 0.3, 0.2, 0.1],
        "current_loss": 0.1,
        "timestamp": time.time()
    }
    
    learning_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.LEARNING,
        data=learning_data,
        metadata={"description": "学习状态", "source": "learning_system"}
    )
    logger.info(f"创建学习状态: {learning_state}")
    
    # 收集所有认知状态
    cognitive_states = {
        perception_state.state_id: perception_state,
        attention_state.state_id: attention_state,
        memory_state.state_id: memory_state,
        learning_state.state_id: learning_state
    }
    
    # 创建监控元认知状态
    monitoring_data = {
        "timestamp": time.time(),
        "cognitive_state_count": 4,
        "cognitive_levels_present": [
            CognitiveLevel.PERCEPTION.value,
            CognitiveLevel.ATTENTION.value,
            CognitiveLevel.MEMORY.value,
            CognitiveLevel.LEARNING.value
        ],
        "system_load": 0.7,
        "attention_focus": "learning",
        "performance_metrics": {
            "perception_quality": 0.8,
            "attention_focus_quality": 0.6,
            "memory_recall_accuracy": 0.7,
            "learning_efficiency": 0.8
        }
    }
    
    monitoring_state = manager.create_metacognitive_state(
        metacognitive_process=MetacognitiveProcess.MONITORING,
        cognitive_states=cognitive_states,
        data=monitoring_data,
        metadata={"description": "系统监控状态", "source": "metacognition_monitor"}
    )
    logger.info(f"创建监控元认知状态: {monitoring_state}")
    
    # 创建评估元认知状态
    evaluation_data = {
        "timestamp": time.time(),
        "evaluation_results": {
            perception_state.state_id: {
                "quality": 0.8,
                "reliability": 0.7,
                "issues": ["moderate noise level"]
            },
            attention_state.state_id: {
                "focus_quality": 0.6,
                "distraction_level": 0.3,
                "issues": ["focus intensity too low", "high distraction"]
            },
            memory_state.state_id: {
                "recall_accuracy": 0.7,
                "retrieval_speed": 0.6,
                "issues": ["low activation for some items"]
            },
            learning_state.state_id: {
                "learning_efficiency": 0.8,
                "convergence_rate": 0.9,
                "issues": []
            }
        },
        "overall_system_performance": 0.75,
        "critical_issues": ["attention focus quality below threshold"]
    }
    
    evaluation_state = manager.create_metacognitive_state(
        metacognitive_process=MetacognitiveProcess.EVALUATION,
        cognitive_states=cognitive_states,
        data=evaluation_data,
        metadata={"description": "系统评估状态", "source": "metacognition_evaluator"}
    )
    logger.info(f"创建评估元认知状态: {evaluation_state}")
    
    # 第三部分：监控控制
    logger.info("\n第三部分：监控控制")
    
    # 定义监控控制策略
    def monitoring_control_strategy(monitoring_state: MetacognitiveState) -> Dict[str, Any]:
        """监控控制策略"""
        # 获取监控数据
        monitoring_data = monitoring_state.data
        cognitive_states = monitoring_state.cognitive_states
        performance_metrics = monitoring_data.get("performance_metrics", {})
        
        # 创建控制动作
        control_actions = {}
        
        # 检查系统负载
        system_load = monitoring_data.get("system_load", 0.0)
        if system_load > 0.8:
            # 系统负载过高，减少处理任务
            control_actions["system"] = {
                "action": "reduce_load",
                "parameters": {"target_load": 0.7},
                "priority": "high",
                "reason": "System load too high"
            }
        
        # 检查感知质量
        perception_quality = performance_metrics.get("perception_quality", 0.0)
        if perception_quality < 0.7:
            # 感知质量低，增加感知灵敏度
            for state_id, state in cognitive_states.items():
                if state.cognitive_level == CognitiveLevel.PERCEPTION:
                    control_actions[state_id] = {
                        "action": "increase_sensitivity",
                        "parameters": {"factor": 1.2},
                        "priority": "medium",
                        "reason": "Low perception quality"
                    }
        
        # 检查注意力焦点质量
        attention_focus_quality = performance_metrics.get("attention_focus_quality", 0.0)
        if attention_focus_quality < 0.7:
            # 注意力焦点质量低，调整注意力焦点
            for state_id, state in cognitive_states.items():
                if state.cognitive_level == CognitiveLevel.ATTENTION:
                    control_actions[state_id] = {
                        "action": "adjust_focus",
                        "parameters": {"intensity_increase": 0.2},
                        "priority": "high",
                        "reason": "Low attention focus quality"
                    }
        
        # 检查记忆召回准确率
        memory_recall_accuracy = performance_metrics.get("memory_recall_accuracy", 0.0)
        if memory_recall_accuracy < 0.7:
            # 记忆召回准确率低，增强记忆激活
            for state_id, state in cognitive_states.items():
                if state.cognitive_level == CognitiveLevel.MEMORY:
                    control_actions[state_id] = {
                        "action": "boost_activation",
                        "parameters": {"factor": 1.3},
                        "priority": "medium",
                        "reason": "Low memory recall accuracy"
                    }
        
        # 检查学习效率
        learning_efficiency = performance_metrics.get("learning_efficiency", 0.0)
        if learning_efficiency < 0.7:
            # 学习效率低，调整学习参数
            for state_id, state in cognitive_states.items():
                if state.cognitive_level == CognitiveLevel.LEARNING:
                    control_actions[state_id] = {
                        "action": "adjust_learning_rate",
                        "parameters": {"new_rate": 0.02},
                        "priority": "low",
                        "reason": "Low learning efficiency"
                    }
        
        # 返回控制动作
        return {
            "control_actions": control_actions,
            "control_strategy": "monitoring_based",
            "timestamp": time.time()
        }
    
    # 注册监控控制策略
    controller.register_control_strategy(
        strategy_id="monitoring_control",
        strategy_type=ControlStrategy.MONITORING,
        strategy_function=monitoring_control_strategy
    )
    logger.info("注册监控控制策略")
    
    # 执行监控控制
    logger.info("\n执行监控控制:")
    monitoring_control_result = controller.control(
        strategy_id="monitoring_control",
        metacognitive_state=monitoring_state
    )
    
    # 分析监控控制结果
    logger.info("\n监控控制结果:")
    control_actions = monitoring_control_result.get("control_actions", {})
    
    for target_id, action in control_actions.items():
        logger.info(f"  目标: {target_id}")
        logger.info(f"    动作: {action.get('action')}")
        logger.info(f"    参数: {action.get('parameters')}")
        logger.info(f"    优先级: {action.get('priority')}")
        logger.info(f"    原因: {action.get('reason')}")
    
    # 应用控制动作
    logger.info("\n应用控制动作:")
    
    # 更新认知状态
    for target_id, action in control_actions.items():
        if target_id == "system":
            # 系统级控制动作
            logger.info(f"  应用系统级控制动作: {action.get('action')}")
            # 在实际系统中，这里会调整系统参数
        elif target_id in cognitive_states:
            # 认知状态控制动作
            state = cognitive_states[target_id]
            logger.info(f"  应用认知状态控制动作: {state.cognitive_level.value}, ID={target_id}, 动作={action.get('action')}")
            
            # 更新认知状态
            updated_data = state.data.copy()
            
            if state.cognitive_level == CognitiveLevel.PERCEPTION and action.get("action") == "increase_sensitivity":
                # 增加感知灵敏度
                factor = action.get("parameters", {}).get("factor", 1.0)
                updated_data["noise_level"] = max(0.05, updated_data.get("noise_level", 0.0) / factor)
                logger.info(f"    降低噪声级别: {updated_data['noise_level']:.2f}")
            
            elif state.cognitive_level == CognitiveLevel.ATTENTION and action.get("action") == "adjust_focus":
                # 调整注意力焦点
                intensity_increase = action.get("parameters", {}).get("intensity_increase", 0.0)
                updated_data["focus_intensity"] = min(1.0, updated_data.get("focus_intensity", 0.0) + intensity_increase)
                updated_data["distraction_level"] = max(0.0, updated_data.get("distraction_level", 0.0) - intensity_increase)
                logger.info(f"    增加焦点强度: {updated_data['focus_intensity']:.2f}")
                logger.info(f"    降低分心级别: {updated_data['distraction_level']:.2f}")
            
            elif state.cognitive_level == CognitiveLevel.MEMORY and action.get("action") == "boost_activation":
                # 增强记忆激活
                factor = action.get("parameters", {}).get("factor", 1.0)
                activation_levels = updated_data.get("activation_levels", [])
                updated_data["activation_levels"] = [min(1.0, level * factor) for level in activation_levels]
                updated_data["retrieval_speed"] = min(1.0, updated_data.get("retrieval_speed", 0.0) * factor)
                logger.info(f"    增强激活级别: {updated_data['activation_levels']}")
                logger.info(f"    提高检索速度: {updated_data['retrieval_speed']:.2f}")
            
            elif state.cognitive_level == CognitiveLevel.LEARNING and action.get("action") == "adjust_learning_rate":
                # 调整学习率
                new_rate = action.get("parameters", {}).get("new_rate", 0.01)
                updated_data["learning_rate"] = new_rate
                logger.info(f"    调整学习率: {updated_data['learning_rate']:.4f}")
            
            # 更新认知状态
            updated_data["timestamp"] = time.time()
            updated_state = manager.update_cognitive_state(
                state_id=target_id,
                data=updated_data
            )
            logger.info(f"    更新认知状态: {updated_state}")
    
    # 创建控制元认知状态
    control_data = {
        "timestamp": time.time(),
        "control_actions": control_actions,
        "control_strategy": monitoring_control_result.get("control_strategy"),
        "control_result": "applied"
    }
    
    control_state = manager.create_metacognitive_state(
        metacognitive_process=MetacognitiveProcess.CONTROL,
        cognitive_states=cognitive_states,
        data=control_data,
        metadata={"description": "系统控制状态", "source": "metacognition_controller"}
    )
    logger.info(f"创建控制元认知状态: {control_state}")
    
    logger.info("元认知控制示例 - 第一部分完成")

if __name__ == "__main__":
    main()
