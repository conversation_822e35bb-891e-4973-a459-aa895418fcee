"""
认知状态和元认知状态示例

本示例展示了如何创建和使用认知状态和元认知状态，包括不同认知层级的状态创建、状态转换和状态管理。
"""

import numpy as np
import logging
import time
from typing import Any, Dict, List, Optional, Callable

from src.core.metacognition import (
    CognitiveLevel, MetacognitiveProcess,
    NewCognitiveState as CognitiveState, 
    NewMetacognitiveState as MetacognitiveState,
    CognitiveProcess, NewMetacognitiveProcess as MetacognitiveProcess,
    MetacognitionFactory, MetacognitionManager
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    logger.info("认知状态和元认知状态示例")
    
    # 创建元认知系统工厂
    factory = MetacognitionFactory()
    
    # 创建元认知系统管理器
    manager = MetacognitionManager(manager_id="metacognition_manager")
    
    # 第一部分：创建不同认知层级的认知状态
    logger.info("\n第一部分：创建不同认知层级的认知状态")
    
    # 创建感知层级的认知状态
    perception_data = {
        "image": np.random.rand(28, 28),  # 模拟图像数据
        "timestamp": time.time()
    }
    
    perception_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.PERCEPTION,
        data=perception_data,
        metadata={"description": "视觉感知状态", "source": "camera"}
    )
    logger.info(f"创建感知状态: {perception_state}")
    
    # 创建注意力层级的认知状态
    attention_data = {
        "focus_region": [10, 10, 5, 5],  # 模拟注意力焦点区域 [x, y, width, height]
        "focus_intensity": 0.8,
        "timestamp": time.time()
    }
    
    attention_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.ATTENTION,
        data=attention_data,
        metadata={"description": "视觉注意力状态", "source": "attention_mechanism"}
    )
    logger.info(f"创建注意力状态: {attention_state}")
    
    # 创建记忆层级的认知状态
    memory_data = {
        "memories": ["apple", "banana", "orange"],
        "activation_levels": [0.9, 0.7, 0.5],
        "timestamp": time.time()
    }
    
    memory_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.MEMORY,
        data=memory_data,
        metadata={"description": "工作记忆状态", "source": "memory_system"}
    )
    logger.info(f"创建记忆状态: {memory_state}")
    
    # 创建学习层级的认知状态
    learning_data = {
        "learning_rate": 0.01,
        "batch_size": 32,
        "epochs": 10,
        "loss_history": [0.5, 0.4, 0.3, 0.2, 0.1],
        "timestamp": time.time()
    }
    
    learning_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.LEARNING,
        data=learning_data,
        metadata={"description": "学习状态", "source": "learning_system"}
    )
    logger.info(f"创建学习状态: {learning_state}")
    
    # 创建推理层级的认知状态
    reasoning_data = {
        "premises": ["All men are mortal", "Socrates is a man"],
        "conclusion": "Socrates is mortal",
        "confidence": 0.95,
        "timestamp": time.time()
    }
    
    reasoning_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.REASONING,
        data=reasoning_data,
        metadata={"description": "逻辑推理状态", "source": "reasoning_system"}
    )
    logger.info(f"创建推理状态: {reasoning_state}")
    
    # 创建规划层级的认知状态
    planning_data = {
        "goal": "Make a cup of coffee",
        "steps": [
            "Boil water",
            "Add coffee grounds",
            "Pour hot water",
            "Stir",
            "Add sugar and milk"
        ],
        "current_step": 0,
        "timestamp": time.time()
    }
    
    planning_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.PLANNING,
        data=planning_data,
        metadata={"description": "任务规划状态", "source": "planning_system"}
    )
    logger.info(f"创建规划状态: {planning_state}")
    
    # 创建执行层级的认知状态
    execution_data = {
        "action": "Pour hot water",
        "parameters": {"amount": "200ml", "temperature": "90C"},
        "status": "in_progress",
        "progress": 0.5,
        "timestamp": time.time()
    }
    
    execution_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.EXECUTION,
        data=execution_data,
        metadata={"description": "动作执行状态", "source": "execution_system"}
    )
    logger.info(f"创建执行状态: {execution_state}")
    
    # 创建反思层级的认知状态
    reflection_data = {
        "evaluation": "Task completed successfully",
        "performance_metrics": {
            "time_taken": 120,
            "accuracy": 0.95,
            "efficiency": 0.85
        },
        "lessons_learned": [
            "Boiling water first saves time",
            "Adding milk last preserves flavor"
        ],
        "timestamp": time.time()
    }
    
    reflection_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.REFLECTION,
        data=reflection_data,
        metadata={"description": "任务反思状态", "source": "reflection_system"}
    )
    logger.info(f"创建反思状态: {reflection_state}")
    
    # 第二部分：创建元认知状态
    logger.info("\n第二部分：创建元认知状态")
    
    # 创建监控元认知状态
    monitoring_data = {
        "timestamp": time.time(),
        "cognitive_state_count": 8,
        "cognitive_levels_present": [level.value for level in CognitiveLevel],
        "system_load": 0.6,
        "attention_focus": "planning"
    }
    
    # 收集所有认知状态
    cognitive_states = {
        perception_state.state_id: perception_state,
        attention_state.state_id: attention_state,
        memory_state.state_id: memory_state,
        learning_state.state_id: learning_state,
        reasoning_state.state_id: reasoning_state,
        planning_state.state_id: planning_state,
        execution_state.state_id: execution_state,
        reflection_state.state_id: reflection_state
    }
    
    monitoring_state = manager.create_metacognitive_state(
        metacognitive_process=MetacognitiveProcess.MONITORING,
        cognitive_states=cognitive_states,
        data=monitoring_data,
        metadata={"description": "系统监控状态", "source": "metacognition_monitor"}
    )
    logger.info(f"创建监控元认知状态: {monitoring_state}")
    
    # 创建评估元认知状态
    evaluation_data = {
        "timestamp": time.time(),
        "evaluation_results": {
            "perception": {"quality": 0.8, "reliability": 0.9},
            "attention": {"focus_quality": 0.7, "distraction_level": 0.2},
            "memory": {"recall_accuracy": 0.85, "retrieval_speed": 0.75},
            "learning": {"learning_efficiency": 0.6, "knowledge_gain": 0.5},
            "reasoning": {"logical_consistency": 0.9, "inference_validity": 0.95},
            "planning": {"plan_quality": 0.8, "plan_efficiency": 0.7},
            "execution": {"action_precision": 0.75, "timing_accuracy": 0.8},
            "reflection": {"insight_depth": 0.6, "improvement_potential": 0.7}
        },
        "overall_system_performance": 0.78
    }
    
    evaluation_state = manager.create_metacognitive_state(
        metacognitive_process=MetacognitiveProcess.EVALUATION,
        cognitive_states=cognitive_states,
        data=evaluation_data,
        metadata={"description": "系统评估状态", "source": "metacognition_evaluator"}
    )
    logger.info(f"创建评估元认知状态: {evaluation_state}")
    
    # 创建控制元认知状态
    control_data = {
        "timestamp": time.time(),
        "control_actions": {
            "perception": {"adjust_sensitivity": 1.2},
            "attention": {"shift_focus": [15, 15, 8, 8]},
            "memory": {"prioritize_items": ["banana", "apple", "orange"]},
            "learning": {"increase_learning_rate": 0.02},
            "reasoning": {"apply_alternative_logic": "inductive"},
            "planning": {"reorder_steps": [0, 2, 1, 3, 4]},
            "execution": {"slow_down_execution": 0.8},
            "reflection": {"deepen_analysis": True}
        },
        "control_strategy": "adaptive",
        "priority_level": "high"
    }
    
    control_state = manager.create_metacognitive_state(
        metacognitive_process=MetacognitiveProcess.CONTROL,
        cognitive_states=cognitive_states,
        data=control_data,
        metadata={"description": "系统控制状态", "source": "metacognition_controller"}
    )
    logger.info(f"创建控制元认知状态: {control_state}")
    
    # 创建调节元认知状态
    regulation_data = {
        "timestamp": time.time(),
        "regulation_actions": {
            "perception": {"filter_noise": 0.3},
            "attention": {"broaden_focus": 1.5},
            "memory": {"consolidate_memories": ["apple", "banana"]},
            "learning": {"adjust_batch_size": 64},
            "reasoning": {"increase_inference_depth": 2},
            "planning": {"add_contingency_steps": ["Check water temperature"]},
            "execution": {"enable_parallel_execution": True},
            "reflection": {"schedule_review": "+1h"}
        },
        "regulation_strategy": "proactive",
        "expected_improvements": 0.15
    }
    
    regulation_state = manager.create_metacognitive_state(
        metacognitive_process=MetacognitiveProcess.REGULATION,
        cognitive_states=cognitive_states,
        data=regulation_data,
        metadata={"description": "系统调节状态", "source": "metacognition_regulator"}
    )
    logger.info(f"创建调节元认知状态: {regulation_state}")
    
    # 第三部分：状态查询和管理
    logger.info("\n第三部分：状态查询和管理")
    
    # 获取所有认知状态
    all_cognitive_states = manager.get_cognitive_states()
    logger.info(f"获取所有认知状态: {len(all_cognitive_states)} 个状态")
    
    # 获取所有元认知状态
    all_metacognitive_states = manager.get_metacognitive_states()
    logger.info(f"获取所有元认知状态: {len(all_metacognitive_states)} 个状态")
    
    # 按认知层级查询认知状态
    perception_states = manager.get_cognitive_states_by_level(CognitiveLevel.PERCEPTION)
    logger.info(f"感知层级的认知状态: {len(perception_states)} 个状态")
    
    # 按元认知过程查询元认知状态
    monitoring_states = manager.get_metacognitive_states_by_process(MetacognitiveProcess.MONITORING)
    logger.info(f"监控过程的元认知状态: {len(monitoring_states)} 个状态")
    
    # 按时间范围查询认知状态
    current_time = time.time()
    recent_states = manager.get_cognitive_states_by_time_range(current_time - 60, current_time)
    logger.info(f"最近60秒内的认知状态: {len(recent_states)} 个状态")
    
    # 按元数据查询认知状态
    visual_states = manager.get_cognitive_states_by_metadata({"source": "camera"})
    logger.info(f"来自相机的认知状态: {len(visual_states)} 个状态")
    
    # 第四部分：状态更新和转换
    logger.info("\n第四部分：状态更新和转换")
    
    # 更新认知状态
    updated_attention_data = attention_data.copy()
    updated_attention_data["focus_region"] = [15, 15, 8, 8]
    updated_attention_data["focus_intensity"] = 0.9
    updated_attention_data["timestamp"] = time.time()
    
    updated_attention_state = manager.update_cognitive_state(
        state_id=attention_state.state_id,
        data=updated_attention_data
    )
    logger.info(f"更新注意力状态: {updated_attention_state}")
    
    # 更新元认知状态
    updated_monitoring_data = monitoring_data.copy()
    updated_monitoring_data["system_load"] = 0.7
    updated_monitoring_data["attention_focus"] = "execution"
    updated_monitoring_data["timestamp"] = time.time()
    
    updated_monitoring_state = manager.update_metacognitive_state(
        state_id=monitoring_state.state_id,
        data=updated_monitoring_data
    )
    logger.info(f"更新监控元认知状态: {updated_monitoring_state}")
    
    # 状态转换：从感知状态到注意力状态
    def perception_to_attention_transform(perception_state: CognitiveState) -> CognitiveState:
        """从感知状态转换到注意力状态"""
        # 获取感知数据
        perception_data = perception_state.data
        image = perception_data.get("image", np.zeros((28, 28)))
        
        # 找到图像中的最大值位置作为注意力焦点
        max_pos = np.unravel_index(np.argmax(image), image.shape)
        
        # 创建注意力数据
        attention_data = {
            "focus_region": [max_pos[0], max_pos[1], 5, 5],
            "focus_intensity": np.max(image),
            "timestamp": time.time()
        }
        
        # 创建注意力状态
        return manager.create_cognitive_state(
            cognitive_level=CognitiveLevel.ATTENTION,
            data=attention_data,
            metadata={
                "description": "从感知转换的注意力状态",
                "source": "perception_transform",
                "parent_state_id": perception_state.state_id
            }
        )
    
    # 执行状态转换
    transformed_attention_state = perception_to_attention_transform(perception_state)
    logger.info(f"从感知状态转换到注意力状态: {transformed_attention_state}")
    
    # 状态转换：从注意力状态到记忆状态
    def attention_to_memory_transform(attention_state: CognitiveState) -> CognitiveState:
        """从注意力状态转换到记忆状态"""
        # 获取注意力数据
        attention_data = attention_state.data
        focus_region = attention_data.get("focus_region", [0, 0, 0, 0])
        focus_intensity = attention_data.get("focus_intensity", 0.0)
        
        # 创建记忆数据（模拟从注意力焦点区域提取特征）
        memory_data = {
            "memories": [f"Feature at ({focus_region[0]}, {focus_region[1]})"],
            "activation_levels": [focus_intensity],
            "timestamp": time.time()
        }
        
        # 创建记忆状态
        return manager.create_cognitive_state(
            cognitive_level=CognitiveLevel.MEMORY,
            data=memory_data,
            metadata={
                "description": "从注意力转换的记忆状态",
                "source": "attention_transform",
                "parent_state_id": attention_state.state_id
            }
        )
    
    # 执行状态转换
    transformed_memory_state = attention_to_memory_transform(transformed_attention_state)
    logger.info(f"从注意力状态转换到记忆状态: {transformed_memory_state}")
    
    # 第五部分：状态序列和状态链
    logger.info("\n第五部分：状态序列和状态链")
    
    # 创建认知状态序列
    cognitive_state_sequence = [
        perception_state,
        transformed_attention_state,
        transformed_memory_state
    ]
    logger.info(f"认知状态序列: {len(cognitive_state_sequence)} 个状态")
    
    # 创建元认知状态序列
    metacognitive_state_sequence = [
        monitoring_state,
        evaluation_state,
        control_state,
        regulation_state
    ]
    logger.info(f"元认知状态序列: {len(metacognitive_state_sequence)} 个状态")
    
    # 分析认知状态链
    logger.info("\n分析认知状态链:")
    for i, state in enumerate(cognitive_state_sequence):
        logger.info(f"  状态 {i+1}: 层级={state.cognitive_level.value}, ID={state.state_id}")
        if "parent_state_id" in state.metadata:
            parent_id = state.metadata["parent_state_id"]
            logger.info(f"    父状态: {parent_id}")
    
    # 分析元认知状态链
    logger.info("\n分析元认知状态链:")
    for i, state in enumerate(metacognitive_state_sequence):
        logger.info(f"  状态 {i+1}: 过程={state.metacognitive_process.value}, ID={state.state_id}")
        logger.info(f"    认知状态数量: {len(state.cognitive_states)}")
    
    # 获取管理器统计信息
    stats = manager.get_stats()
    logger.info(f"\n管理器统计信息: {stats}")
    
    logger.info("示例完成")

if __name__ == "__main__":
    main()
