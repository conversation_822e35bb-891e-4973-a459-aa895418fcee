"""
元认知映射示例

本示例展示了如何创建和使用元认知映射，包括状态映射、过程映射和层级映射。
"""

import numpy as np
import logging
import time
from typing import Any, Dict, List, Optional, Callable

from src.core.metacognition import (
    CognitiveLevel, MetacognitiveProcess,
    NewCognitiveState as CognitiveState, 
    NewMetacognitiveState as MetacognitiveState,
    CognitiveProcess, NewMetacognitiveProcess as MetacognitiveProcess,
    MetacognitionFactory, MetacognitionManager
)
from src.core.metacognition.mapping.metacognitive_mapping import MetaCognitiveMapping

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    logger.info("元认知映射示例")
    
    # 创建元认知系统工厂
    factory = MetacognitionFactory()
    
    # 创建元认知系统管理器
    manager = MetacognitionManager(manager_id="metacognition_manager")
    
    # 第一部分：创建认知状态和元认知状态
    logger.info("\n第一部分：创建认知状态和元认知状态")
    
    # 创建感知层级的认知状态
    perception_data = {
        "image": np.random.rand(28, 28),  # 模拟图像数据
        "timestamp": time.time()
    }
    
    perception_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.PERCEPTION,
        data=perception_data,
        metadata={"description": "视觉感知状态", "source": "camera"}
    )
    logger.info(f"创建感知状态: {perception_state}")
    
    # 创建注意力层级的认知状态
    attention_data = {
        "focus_region": [10, 10, 5, 5],  # 模拟注意力焦点区域 [x, y, width, height]
        "focus_intensity": 0.8,
        "timestamp": time.time()
    }
    
    attention_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.ATTENTION,
        data=attention_data,
        metadata={"description": "视觉注意力状态", "source": "attention_mechanism"}
    )
    logger.info(f"创建注意力状态: {attention_state}")
    
    # 创建记忆层级的认知状态
    memory_data = {
        "memories": ["apple", "banana", "orange"],
        "activation_levels": [0.9, 0.7, 0.5],
        "timestamp": time.time()
    }
    
    memory_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.MEMORY,
        data=memory_data,
        metadata={"description": "工作记忆状态", "source": "memory_system"}
    )
    logger.info(f"创建记忆状态: {memory_state}")
    
    # 收集所有认知状态
    cognitive_states = {
        perception_state.state_id: perception_state,
        attention_state.state_id: attention_state,
        memory_state.state_id: memory_state
    }
    
    # 创建监控元认知状态
    monitoring_data = {
        "timestamp": time.time(),
        "cognitive_state_count": 3,
        "cognitive_levels_present": [
            CognitiveLevel.PERCEPTION.value,
            CognitiveLevel.ATTENTION.value,
            CognitiveLevel.MEMORY.value
        ],
        "system_load": 0.6,
        "attention_focus": "memory"
    }
    
    monitoring_state = manager.create_metacognitive_state(
        metacognitive_process=MetacognitiveProcess.MONITORING,
        cognitive_states=cognitive_states,
        data=monitoring_data,
        metadata={"description": "系统监控状态", "source": "metacognition_monitor"}
    )
    logger.info(f"创建监控元认知状态: {monitoring_state}")
    
    # 第二部分：创建元认知映射
    logger.info("\n第二部分：创建元认知映射")
    
    # 创建元认知映射
    metacognitive_mapping = MetaCognitiveMapping(mapping_id="test_mapping")
    logger.info(f"创建元认知映射: {metacognitive_mapping}")
    
    # 第三部分：状态映射
    logger.info("\n第三部分：状态映射")
    
    # 定义状态映射函数
    def perception_to_attention_mapping(perception_state: CognitiveState) -> CognitiveState:
        """从感知状态映射到注意力状态"""
        # 获取感知数据
        perception_data = perception_state.data
        image = perception_data.get("image", np.zeros((28, 28)))
        
        # 找到图像中的最大值位置作为注意力焦点
        max_pos = np.unravel_index(np.argmax(image), image.shape)
        
        # 创建注意力数据
        attention_data = {
            "focus_region": [max_pos[0], max_pos[1], 5, 5],
            "focus_intensity": np.max(image),
            "timestamp": time.time()
        }
        
        # 创建注意力状态
        return manager.create_cognitive_state(
            cognitive_level=CognitiveLevel.ATTENTION,
            data=attention_data,
            metadata={
                "description": "从感知映射的注意力状态",
                "source": "perception_mapping",
                "parent_state_id": perception_state.state_id
            }
        )
    
    # 注册状态映射
    metacognitive_mapping.register_state_mapping(
        source_level=CognitiveLevel.PERCEPTION,
        target_level=CognitiveLevel.ATTENTION,
        mapping_function=perception_to_attention_mapping
    )
    logger.info("注册感知到注意力的状态映射")
    
    # 定义状态映射函数
    def attention_to_memory_mapping(attention_state: CognitiveState) -> CognitiveState:
        """从注意力状态映射到记忆状态"""
        # 获取注意力数据
        attention_data = attention_state.data
        focus_region = attention_data.get("focus_region", [0, 0, 0, 0])
        focus_intensity = attention_data.get("focus_intensity", 0.0)
        
        # 创建记忆数据
        memory_data = {
            "memories": [f"Feature at ({focus_region[0]}, {focus_region[1]})"],
            "activation_levels": [focus_intensity],
            "timestamp": time.time()
        }
        
        # 创建记忆状态
        return manager.create_cognitive_state(
            cognitive_level=CognitiveLevel.MEMORY,
            data=memory_data,
            metadata={
                "description": "从注意力映射的记忆状态",
                "source": "attention_mapping",
                "parent_state_id": attention_state.state_id
            }
        )
    
    # 注册状态映射
    metacognitive_mapping.register_state_mapping(
        source_level=CognitiveLevel.ATTENTION,
        target_level=CognitiveLevel.MEMORY,
        mapping_function=attention_to_memory_mapping
    )
    logger.info("注册注意力到记忆的状态映射")
    
    # 执行状态映射
    mapped_attention_state = metacognitive_mapping.map_state(perception_state)
    logger.info(f"从感知状态映射到注意力状态: {mapped_attention_state}")
    
    mapped_memory_state = metacognitive_mapping.map_state(mapped_attention_state)
    logger.info(f"从注意力状态映射到记忆状态: {mapped_memory_state}")
    
    # 第四部分：过程映射
    logger.info("\n第四部分：过程映射")
    
    # 定义过程映射函数
    def monitoring_to_evaluation_mapping(monitoring_state: MetacognitiveState) -> MetacognitiveState:
        """从监控状态映射到评估状态"""
        # 获取监控数据
        monitoring_data = monitoring_state.data
        cognitive_states = monitoring_state.cognitive_states
        
        # 创建评估数据
        evaluation_data = {
            "timestamp": time.time(),
            "evaluation_results": {},
            "overall_system_performance": 0.0
        }
        
        # 为每个认知状态创建评估结果
        total_score = 0.0
        count = 0
        
        for state_id, state in cognitive_states.items():
            # 根据认知层级创建不同的评估指标
            if state.cognitive_level == CognitiveLevel.PERCEPTION:
                # 评估感知质量（使用图像方差作为指标）
                image = state.data.get("image", np.zeros((1, 1)))
                quality = np.var(image)  # 方差越大，图像信息量越大
                reliability = min(1.0, quality * 10)  # 将方差映射到0-1范围
                
                evaluation_data["evaluation_results"][state_id] = {
                    "quality": quality,
                    "reliability": reliability,
                    "score": reliability
                }
                
                total_score += reliability
                count += 1
                
            elif state.cognitive_level == CognitiveLevel.ATTENTION:
                # 评估注意力质量
                focus_intensity = state.data.get("focus_intensity", 0.0)
                focus_quality = focus_intensity
                distraction_level = 1.0 - focus_intensity
                
                evaluation_data["evaluation_results"][state_id] = {
                    "focus_quality": focus_quality,
                    "distraction_level": distraction_level,
                    "score": focus_quality
                }
                
                total_score += focus_quality
                count += 1
                
            elif state.cognitive_level == CognitiveLevel.MEMORY:
                # 评估记忆质量
                activation_levels = state.data.get("activation_levels", [])
                if activation_levels:
                    recall_accuracy = sum(activation_levels) / len(activation_levels)
                    retrieval_speed = max(0.5, min(1.0, recall_accuracy * 1.2))
                else:
                    recall_accuracy = 0.0
                    retrieval_speed = 0.0
                
                evaluation_data["evaluation_results"][state_id] = {
                    "recall_accuracy": recall_accuracy,
                    "retrieval_speed": retrieval_speed,
                    "score": recall_accuracy
                }
                
                total_score += recall_accuracy
                count += 1
        
        # 计算整体系统性能
        if count > 0:
            evaluation_data["overall_system_performance"] = total_score / count
        
        # 创建评估状态
        return manager.create_metacognitive_state(
            metacognitive_process=MetacognitiveProcess.EVALUATION,
            cognitive_states=cognitive_states,
            data=evaluation_data,
            metadata={
                "description": "从监控映射的评估状态",
                "source": "monitoring_mapping",
                "parent_state_id": monitoring_state.state_id
            }
        )
    
    # 注册过程映射
    metacognitive_mapping.register_process_mapping(
        source_process=MetacognitiveProcess.MONITORING,
        target_process=MetacognitiveProcess.EVALUATION,
        mapping_function=monitoring_to_evaluation_mapping
    )
    logger.info("注册监控到评估的过程映射")
    
    # 定义过程映射函数
    def evaluation_to_control_mapping(evaluation_state: MetacognitiveState) -> MetacognitiveState:
        """从评估状态映射到控制状态"""
        # 获取评估数据
        evaluation_data = evaluation_state.data
        cognitive_states = evaluation_state.cognitive_states
        evaluation_results = evaluation_data.get("evaluation_results", {})
        
        # 创建控制数据
        control_data = {
            "timestamp": time.time(),
            "control_actions": {},
            "control_strategy": "adaptive",
            "priority_level": "medium"
        }
        
        # 为每个认知状态创建控制动作
        for state_id, result in evaluation_results.items():
            if state_id not in cognitive_states:
                continue
                
            state = cognitive_states[state_id]
            score = result.get("score", 0.0)
            
            # 根据评估分数和认知层级创建不同的控制动作
            if state.cognitive_level == CognitiveLevel.PERCEPTION:
                # 如果感知质量低，增加灵敏度
                if score < 0.7:
                    control_data["control_actions"][state_id] = {
                        "adjust_sensitivity": 1.5,
                        "reason": "Low perception quality"
                    }
                    control_data["priority_level"] = "high"
                else:
                    control_data["control_actions"][state_id] = {
                        "maintain_settings": True,
                        "reason": "Good perception quality"
                    }
                
            elif state.cognitive_level == CognitiveLevel.ATTENTION:
                # 如果注意力质量低，调整焦点
                if score < 0.7:
                    # 获取当前焦点区域
                    focus_region = state.data.get("focus_region", [0, 0, 0, 0])
                    # 扩大焦点区域
                    new_focus = [
                        focus_region[0],
                        focus_region[1],
                        focus_region[2] * 1.5,
                        focus_region[3] * 1.5
                    ]
                    
                    control_data["control_actions"][state_id] = {
                        "adjust_focus": new_focus,
                        "reason": "Low attention quality"
                    }
                    control_data["priority_level"] = "high"
                else:
                    control_data["control_actions"][state_id] = {
                        "maintain_focus": True,
                        "reason": "Good attention quality"
                    }
                
            elif state.cognitive_level == CognitiveLevel.MEMORY:
                # 如果记忆质量低，增强激活
                if score < 0.7:
                    control_data["control_actions"][state_id] = {
                        "boost_activation": 1.3,
                        "reason": "Low memory recall"
                    }
                    control_data["priority_level"] = "medium"
                else:
                    control_data["control_actions"][state_id] = {
                        "maintain_activation": True,
                        "reason": "Good memory recall"
                    }
        
        # 创建控制状态
        return manager.create_metacognitive_state(
            metacognitive_process=MetacognitiveProcess.CONTROL,
            cognitive_states=cognitive_states,
            data=control_data,
            metadata={
                "description": "从评估映射的控制状态",
                "source": "evaluation_mapping",
                "parent_state_id": evaluation_state.state_id
            }
        )
    
    # 注册过程映射
    metacognitive_mapping.register_process_mapping(
        source_process=MetacognitiveProcess.EVALUATION,
        target_process=MetacognitiveProcess.CONTROL,
        mapping_function=evaluation_to_control_mapping
    )
    logger.info("注册评估到控制的过程映射")
    
    # 执行过程映射
    mapped_evaluation_state = metacognitive_mapping.map_process(monitoring_state)
    logger.info(f"从监控状态映射到评估状态: {mapped_evaluation_state}")
    
    mapped_control_state = metacognitive_mapping.map_process(mapped_evaluation_state)
    logger.info(f"从评估状态映射到控制状态: {mapped_control_state}")
    
    # 第五部分：层级映射
    logger.info("\n第五部分：层级映射")
    
    # 定义层级映射函数
    def perception_to_metacognition_mapping(perception_state: CognitiveState) -> MetacognitiveState:
        """从感知层级映射到元认知层级"""
        # 创建认知状态字典
        cognitive_states = {perception_state.state_id: perception_state}
        
        # 创建监控数据
        monitoring_data = {
            "timestamp": time.time(),
            "perception_quality": np.var(perception_state.data.get("image", np.zeros((1, 1)))),
            "system_status": "normal"
        }
        
        # 创建监控状态
        return manager.create_metacognitive_state(
            metacognitive_process=MetacognitiveProcess.MONITORING,
            cognitive_states=cognitive_states,
            data=monitoring_data,
            metadata={
                "description": "从感知映射的监控状态",
                "source": "perception_level_mapping",
                "parent_state_id": perception_state.state_id
            }
        )
    
    # 注册层级映射
    metacognitive_mapping.register_level_mapping(
        source_level=CognitiveLevel.PERCEPTION,
        target_process=MetacognitiveProcess.MONITORING,
        mapping_function=perception_to_metacognition_mapping
    )
    logger.info("注册感知层级到监控过程的层级映射")
    
    # 执行层级映射
    mapped_monitoring_state = metacognitive_mapping.map_level(perception_state)
    logger.info(f"从感知层级映射到监控过程: {mapped_monitoring_state}")
    
    # 第六部分：复合映射
    logger.info("\n第六部分：复合映射")
    
    # 执行复合映射：感知 -> 注意力 -> 记忆 -> 监控 -> 评估 -> 控制
    logger.info("执行复合映射：感知 -> 注意力 -> 记忆 -> 监控 -> 评估 -> 控制")
    
    # 从感知状态开始
    current_state = perception_state
    logger.info(f"初始状态: {current_state.cognitive_level.value}, ID={current_state.state_id}")
    
    # 映射到注意力状态
    current_state = metacognitive_mapping.map_state(current_state)
    logger.info(f"映射到注意力状态: {current_state.cognitive_level.value}, ID={current_state.state_id}")
    
    # 映射到记忆状态
    current_state = metacognitive_mapping.map_state(current_state)
    logger.info(f"映射到记忆状态: {current_state.cognitive_level.value}, ID={current_state.state_id}")
    
    # 创建包含所有认知状态的监控状态
    all_states = {
        perception_state.state_id: perception_state,
        attention_state.state_id: attention_state,
        memory_state.state_id: memory_state,
        current_state.state_id: current_state
    }
    
    monitoring_data = {
        "timestamp": time.time(),
        "cognitive_state_count": len(all_states),
        "system_load": 0.7
    }
    
    current_meta_state = manager.create_metacognitive_state(
        metacognitive_process=MetacognitiveProcess.MONITORING,
        cognitive_states=all_states,
        data=monitoring_data,
        metadata={"description": "复合映射监控状态"}
    )
    logger.info(f"创建监控状态: {current_meta_state.metacognitive_process.value}, ID={current_meta_state.state_id}")
    
    # 映射到评估状态
    current_meta_state = metacognitive_mapping.map_process(current_meta_state)
    logger.info(f"映射到评估状态: {current_meta_state.metacognitive_process.value}, ID={current_meta_state.state_id}")
    
    # 映射到控制状态
    current_meta_state = metacognitive_mapping.map_process(current_meta_state)
    logger.info(f"映射到控制状态: {current_meta_state.metacognitive_process.value}, ID={current_meta_state.state_id}")
    
    # 分析最终控制状态
    logger.info("\n分析最终控制状态:")
    control_data = current_meta_state.data
    control_actions = control_data.get("control_actions", {})
    
    for state_id, action in control_actions.items():
        if state_id in current_meta_state.cognitive_states:
            state = current_meta_state.cognitive_states[state_id]
            logger.info(f"  状态 {state_id} ({state.cognitive_level.value}) 的控制动作:")
            for key, value in action.items():
                logger.info(f"    {key}: {value}")
    
    logger.info(f"  控制策略: {control_data.get('control_strategy')}")
    logger.info(f"  优先级: {control_data.get('priority_level')}")
    
    # 获取映射统计信息
    stats = metacognitive_mapping.get_stats()
    logger.info(f"\n映射统计信息: {stats}")
    
    logger.info("示例完成")

if __name__ == "__main__":
    main()
