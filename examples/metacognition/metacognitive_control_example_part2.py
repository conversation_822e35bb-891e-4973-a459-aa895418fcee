"""
元认知控制示例 - 第二部分

本示例展示了如何使用元认知控制机制，包括监控控制、评估控制和自适应控制。
这是第二部分，主要展示评估控制和自适应控制。
"""

import numpy as np
import logging
import time
from typing import Any, Dict, List, Optional, Callable, Tuple

from src.core.metacognition import (
    CognitiveLevel, MetacognitiveProcess,
    NewCognitiveState as CognitiveState, 
    NewMetacognitiveState as MetacognitiveState,
    CognitiveProcess, NewMetacognitiveProcess as MetacognitiveProcess,
    MetacognitionFactory, MetacognitionManager
)
from src.core.metacognition.control import (
    MetacognitiveController, ControlStrategy, ControlAction
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    logger.info("元认知控制示例 - 第二部分")
    
    # 创建元认知系统工厂
    factory = MetacognitionFactory()
    
    # 创建元认知系统管理器
    manager = MetacognitionManager(manager_id="metacognition_manager")
    
    # 创建元认知控制器
    controller = MetacognitiveController(controller_id="metacognitive_controller")
    
    # 创建认知状态
    # 创建感知层级的认知状态
    perception_data = {
        "image": np.random.rand(28, 28),  # 模拟图像数据
        "noise_level": 0.2,
        "timestamp": time.time()
    }
    
    perception_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.PERCEPTION,
        data=perception_data,
        metadata={"description": "视觉感知状态", "source": "camera"}
    )
    
    # 创建注意力层级的认知状态
    attention_data = {
        "focus_region": [10, 10, 5, 5],  # 模拟注意力焦点区域 [x, y, width, height]
        "focus_intensity": 0.6,
        "distraction_level": 0.3,
        "timestamp": time.time()
    }
    
    attention_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.ATTENTION,
        data=attention_data,
        metadata={"description": "视觉注意力状态", "source": "attention_mechanism"}
    )
    
    # 创建记忆层级的认知状态
    memory_data = {
        "memories": ["apple", "banana", "orange"],
        "activation_levels": [0.7, 0.5, 0.3],
        "retrieval_speed": 0.6,
        "timestamp": time.time()
    }
    
    memory_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.MEMORY,
        data=memory_data,
        metadata={"description": "工作记忆状态", "source": "memory_system"}
    )
    
    # 创建学习层级的认知状态
    learning_data = {
        "learning_rate": 0.01,
        "batch_size": 32,
        "epochs": 10,
        "loss_history": [0.5, 0.4, 0.3, 0.2, 0.1],
        "current_loss": 0.1,
        "timestamp": time.time()
    }
    
    learning_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.LEARNING,
        data=learning_data,
        metadata={"description": "学习状态", "source": "learning_system"}
    )
    
    # 收集所有认知状态
    cognitive_states = {
        perception_state.state_id: perception_state,
        attention_state.state_id: attention_state,
        memory_state.state_id: memory_state,
        learning_state.state_id: learning_state
    }
    
    # 创建评估元认知状态
    evaluation_data = {
        "timestamp": time.time(),
        "evaluation_results": {
            perception_state.state_id: {
                "quality": 0.8,
                "reliability": 0.7,
                "issues": ["moderate noise level"]
            },
            attention_state.state_id: {
                "focus_quality": 0.6,
                "distraction_level": 0.3,
                "issues": ["focus intensity too low", "high distraction"]
            },
            memory_state.state_id: {
                "recall_accuracy": 0.7,
                "retrieval_speed": 0.6,
                "issues": ["low activation for some items"]
            },
            learning_state.state_id: {
                "learning_efficiency": 0.8,
                "convergence_rate": 0.9,
                "issues": []
            }
        },
        "overall_system_performance": 0.75,
        "critical_issues": ["attention focus quality below threshold"]
    }
    
    evaluation_state = manager.create_metacognitive_state(
        metacognitive_process=MetacognitiveProcess.EVALUATION,
        cognitive_states=cognitive_states,
        data=evaluation_data,
        metadata={"description": "系统评估状态", "source": "metacognition_evaluator"}
    )
    logger.info(f"创建评估元认知状态: {evaluation_state}")
    
    # 第四部分：评估控制
    logger.info("\n第四部分：评估控制")
    
    # 定义评估控制策略
    def evaluation_control_strategy(evaluation_state: MetacognitiveState) -> Dict[str, Any]:
        """评估控制策略"""
        # 获取评估数据
        evaluation_data = evaluation_state.data
        cognitive_states = evaluation_state.cognitive_states
        evaluation_results = evaluation_data.get("evaluation_results", {})
        critical_issues = evaluation_data.get("critical_issues", [])
        
        # 创建控制动作
        control_actions = {}
        
        # 处理关键问题
        if critical_issues:
            logger.info(f"  发现关键问题: {critical_issues}")
            
            # 检查是否有注意力问题
            if any("attention" in issue.lower() for issue in critical_issues):
                # 注意力问题，高优先级处理
                for state_id, result in evaluation_results.items():
                    if state_id in cognitive_states and cognitive_states[state_id].cognitive_level == CognitiveLevel.ATTENTION:
                        # 获取问题详情
                        issues = result.get("issues", [])
                        
                        if "focus intensity too low" in issues:
                            # 焦点强度过低，大幅增加焦点强度
                            control_actions[state_id] = {
                                "action": "boost_focus",
                                "parameters": {"intensity_increase": 0.3, "distraction_decrease": 0.2},
                                "priority": "critical",
                                "reason": "Critical attention focus issue"
                            }
                        elif "high distraction" in issues:
                            # 分心程度高，减少分心
                            control_actions[state_id] = {
                                "action": "reduce_distraction",
                                "parameters": {"distraction_decrease": 0.2},
                                "priority": "high",
                                "reason": "High distraction level"
                            }
        
        # 处理各个认知状态的评估结果
        for state_id, result in evaluation_results.items():
            if state_id not in cognitive_states:
                continue
                
            state = cognitive_states[state_id]
            issues = result.get("issues", [])
            
            # 如果已经为该状态创建了控制动作，则跳过
            if state_id in control_actions:
                continue
            
            # 根据认知层级和评估结果创建控制动作
            if state.cognitive_level == CognitiveLevel.PERCEPTION:
                quality = result.get("quality", 0.0)
                reliability = result.get("reliability", 0.0)
                
                if "moderate noise level" in issues:
                    # 中等噪声级别，减少噪声
                    control_actions[state_id] = {
                        "action": "reduce_noise",
                        "parameters": {"noise_reduction": 0.1},
                        "priority": "medium",
                        "reason": "Moderate noise level"
                    }
                elif quality < 0.7:
                    # 感知质量低，增强感知
                    control_actions[state_id] = {
                        "action": "enhance_perception",
                        "parameters": {"enhancement_factor": 1.2},
                        "priority": "medium",
                        "reason": "Low perception quality"
                    }
            
            elif state.cognitive_level == CognitiveLevel.MEMORY:
                recall_accuracy = result.get("recall_accuracy", 0.0)
                retrieval_speed = result.get("retrieval_speed", 0.0)
                
                if "low activation for some items" in issues:
                    # 部分项目激活程度低，选择性增强激活
                    control_actions[state_id] = {
                        "action": "selective_activation",
                        "parameters": {"items": ["banana", "orange"], "activation_increase": 0.2},
                        "priority": "medium",
                        "reason": "Low activation for some memory items"
                    }
                elif recall_accuracy < 0.7:
                    # 记忆召回准确率低，全面增强激活
                    control_actions[state_id] = {
                        "action": "boost_all_activations",
                        "parameters": {"activation_increase": 0.2},
                        "priority": "medium",
                        "reason": "Low memory recall accuracy"
                    }
            
            elif state.cognitive_level == CognitiveLevel.LEARNING:
                learning_efficiency = result.get("learning_efficiency", 0.0)
                convergence_rate = result.get("convergence_rate", 0.0)
                
                if learning_efficiency < 0.7:
                    # 学习效率低，调整学习参数
                    control_actions[state_id] = {
                        "action": "optimize_learning",
                        "parameters": {"learning_rate": 0.02, "batch_size": 64},
                        "priority": "low",
                        "reason": "Low learning efficiency"
                    }
        
        # 返回控制动作
        return {
            "control_actions": control_actions,
            "control_strategy": "evaluation_based",
            "timestamp": time.time()
        }
    
    # 注册评估控制策略
    controller.register_control_strategy(
        strategy_id="evaluation_control",
        strategy_type=ControlStrategy.EVALUATION,
        strategy_function=evaluation_control_strategy
    )
    logger.info("注册评估控制策略")
    
    # 执行评估控制
    logger.info("\n执行评估控制:")
    evaluation_control_result = controller.control(
        strategy_id="evaluation_control",
        metacognitive_state=evaluation_state
    )
    
    # 分析评估控制结果
    logger.info("\n评估控制结果:")
    control_actions = evaluation_control_result.get("control_actions", {})
    
    for target_id, action in control_actions.items():
        logger.info(f"  目标: {target_id}")
        logger.info(f"    动作: {action.get('action')}")
        logger.info(f"    参数: {action.get('parameters')}")
        logger.info(f"    优先级: {action.get('priority')}")
        logger.info(f"    原因: {action.get('reason')}")
    
    # 应用控制动作
    logger.info("\n应用控制动作:")
    
    # 更新认知状态
    for target_id, action in control_actions.items():
        if target_id in cognitive_states:
            # 认知状态控制动作
            state = cognitive_states[target_id]
            logger.info(f"  应用认知状态控制动作: {state.cognitive_level.value}, ID={target_id}, 动作={action.get('action')}")
            
            # 更新认知状态
            updated_data = state.data.copy()
            
            if state.cognitive_level == CognitiveLevel.PERCEPTION:
                if action.get("action") == "reduce_noise":
                    # 减少噪声
                    noise_reduction = action.get("parameters", {}).get("noise_reduction", 0.0)
                    updated_data["noise_level"] = max(0.05, updated_data.get("noise_level", 0.0) - noise_reduction)
                    logger.info(f"    降低噪声级别: {updated_data['noise_level']:.2f}")
                elif action.get("action") == "enhance_perception":
                    # 增强感知
                    enhancement_factor = action.get("parameters", {}).get("enhancement_factor", 1.0)
                    # 模拟增强图像（在实际应用中，这里会应用图像增强算法）
                    updated_data["noise_level"] = max(0.05, updated_data.get("noise_level", 0.0) / enhancement_factor)
                    logger.info(f"    增强感知，降低噪声级别: {updated_data['noise_level']:.2f}")
            
            elif state.cognitive_level == CognitiveLevel.ATTENTION:
                if action.get("action") == "boost_focus":
                    # 增强焦点
                    intensity_increase = action.get("parameters", {}).get("intensity_increase", 0.0)
                    distraction_decrease = action.get("parameters", {}).get("distraction_decrease", 0.0)
                    updated_data["focus_intensity"] = min(1.0, updated_data.get("focus_intensity", 0.0) + intensity_increase)
                    updated_data["distraction_level"] = max(0.0, updated_data.get("distraction_level", 0.0) - distraction_decrease)
                    logger.info(f"    增加焦点强度: {updated_data['focus_intensity']:.2f}")
                    logger.info(f"    降低分心级别: {updated_data['distraction_level']:.2f}")
                elif action.get("action") == "reduce_distraction":
                    # 减少分心
                    distraction_decrease = action.get("parameters", {}).get("distraction_decrease", 0.0)
                    updated_data["distraction_level"] = max(0.0, updated_data.get("distraction_level", 0.0) - distraction_decrease)
                    logger.info(f"    降低分心级别: {updated_data['distraction_level']:.2f}")
            
            elif state.cognitive_level == CognitiveLevel.MEMORY:
                if action.get("action") == "selective_activation":
                    # 选择性增强激活
                    items = action.get("parameters", {}).get("items", [])
                    activation_increase = action.get("parameters", {}).get("activation_increase", 0.0)
                    
                    memories = updated_data.get("memories", [])
                    activation_levels = updated_data.get("activation_levels", [])
                    
                    # 更新激活级别
                    for i, memory in enumerate(memories):
                        if memory in items and i < len(activation_levels):
                            activation_levels[i] = min(1.0, activation_levels[i] + activation_increase)
                    
                    updated_data["activation_levels"] = activation_levels
                    logger.info(f"    选择性增强激活级别: {updated_data['activation_levels']}")
                elif action.get("action") == "boost_all_activations":
                    # 全面增强激活
                    activation_increase = action.get("parameters", {}).get("activation_increase", 0.0)
                    activation_levels = updated_data.get("activation_levels", [])
                    
                    # 更新激活级别
                    updated_data["activation_levels"] = [min(1.0, level + activation_increase) for level in activation_levels]
                    updated_data["retrieval_speed"] = min(1.0, updated_data.get("retrieval_speed", 0.0) + activation_increase)
                    
                    logger.info(f"    全面增强激活级别: {updated_data['activation_levels']}")
                    logger.info(f"    提高检索速度: {updated_data['retrieval_speed']:.2f}")
            
            elif state.cognitive_level == CognitiveLevel.LEARNING:
                if action.get("action") == "optimize_learning":
                    # 优化学习参数
                    learning_rate = action.get("parameters", {}).get("learning_rate", 0.01)
                    batch_size = action.get("parameters", {}).get("batch_size", 32)
                    
                    updated_data["learning_rate"] = learning_rate
                    updated_data["batch_size"] = batch_size
                    
                    logger.info(f"    优化学习率: {updated_data['learning_rate']:.4f}")
                    logger.info(f"    优化批量大小: {updated_data['batch_size']}")
            
            # 更新认知状态
            updated_data["timestamp"] = time.time()
            updated_state = manager.update_cognitive_state(
                state_id=target_id,
                data=updated_data
            )
            logger.info(f"    更新认知状态: {updated_state}")
    
    # 第五部分：自适应控制
    logger.info("\n第五部分：自适应控制")
    
    # 创建控制历史
    control_history = [
        {
            "timestamp": time.time() - 60,
            "control_actions": {
                attention_state.state_id: {
                    "action": "adjust_focus",
                    "parameters": {"intensity_increase": 0.1},
                    "priority": "medium",
                    "result": "partial_improvement"
                }
            },
            "control_strategy": "monitoring_based"
        },
        {
            "timestamp": time.time() - 30,
            "control_actions": {
                attention_state.state_id: {
                    "action": "boost_focus",
                    "parameters": {"intensity_increase": 0.2, "distraction_decrease": 0.1},
                    "priority": "high",
                    "result": "significant_improvement"
                }
            },
            "control_strategy": "evaluation_based"
        }
    ]
    
    # 定义自适应控制策略
    def adaptive_control_strategy(metacognitive_state: MetacognitiveState, history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """自适应控制策略"""
        # 获取元认知状态数据
        metacognitive_data = metacognitive_state.data
        cognitive_states = metacognitive_state.cognitive_states
        
        # 创建控制动作
        control_actions = {}
        
        # 分析历史控制效果
        if history:
            logger.info(f"  分析历史控制效果: {len(history)} 条记录")
            
            # 查找最近的控制动作及其效果
            recent_actions = {}
            for record in reversed(history):
                actions = record.get("control_actions", {})
                for target_id, action in actions.items():
                    if target_id not in recent_actions:
                        recent_actions[target_id] = action
            
            # 根据历史效果调整控制策略
            for target_id, action in recent_actions.items():
                if target_id not in cognitive_states:
                    continue
                    
                state = cognitive_states[target_id]
                result = action.get("result", "unknown")
                
                if state.cognitive_level == CognitiveLevel.ATTENTION:
                    if result == "significant_improvement":
                        # 之前的控制效果显著，维持当前状态
                        logger.info(f"  注意力控制效果显著，维持当前状态: {target_id}")
                    elif result == "partial_improvement":
                        # 之前的控制效果部分改善，增强控制强度
                        previous_action = action.get("action")
                        previous_params = action.get("parameters", {})
                        
                        if previous_action == "adjust_focus":
                            # 之前是调整焦点，现在增强焦点
                            control_actions[target_id] = {
                                "action": "boost_focus",
                                "parameters": {
                                    "intensity_increase": previous_params.get("intensity_increase", 0.1) * 1.5,
                                    "distraction_decrease": 0.15
                                },
                                "priority": "high",
                                "reason": "Previous control had partial effect, increasing intensity"
                            }
                        elif previous_action == "boost_focus":
                            # 之前是增强焦点，现在进一步增强并减少分心
                            control_actions[target_id] = {
                                "action": "comprehensive_focus_enhancement",
                                "parameters": {
                                    "intensity_increase": previous_params.get("intensity_increase", 0.2) * 1.2,
                                    "distraction_decrease": previous_params.get("distraction_decrease", 0.1) * 1.5,
                                    "focus_region_expansion": 1.2
                                },
                                "priority": "high",
                                "reason": "Previous control had partial effect, applying comprehensive enhancement"
                            }
                    else:
                        # 之前的控制效果不明显，尝试不同的控制方法
                        control_actions[target_id] = {
                            "action": "reset_and_refocus",
                            "parameters": {
                                "new_focus_region": [15, 15, 8, 8],
                                "intensity_set": 0.9,
                                "distraction_set": 0.1
                            },
                            "priority": "critical",
                            "reason": "Previous control ineffective, trying new approach"
                        }
        
        # 如果没有基于历史的控制动作，则根据当前状态创建控制动作
        if not control_actions:
            # 检查元认知过程
            if metacognitive_state.metacognitive_process == MetacognitiveProcess.MONITORING:
                # 基于监控数据创建控制动作
                performance_metrics = metacognitive_data.get("performance_metrics", {})
                
                for state_id, state in cognitive_states.items():
                    if state.cognitive_level == CognitiveLevel.ATTENTION:
                        attention_focus_quality = performance_metrics.get("attention_focus_quality", 0.0)
                        
                        if attention_focus_quality < 0.7:
                            # 注意力焦点质量低，自适应调整注意力
                            current_focus = state.data.get("focus_region", [0, 0, 0, 0])
                            current_intensity = state.data.get("focus_intensity", 0.0)
                            
                            # 根据当前状态自适应调整参数
                            if current_intensity < 0.5:
                                # 当前强度很低，大幅提升
                                intensity_increase = 0.3
                            elif current_intensity < 0.7:
                                # 当前强度中等，适度提升
                                intensity_increase = 0.2
                            else:
                                # 当前强度较高，小幅提升
                                intensity_increase = 0.1
                            
                            control_actions[state_id] = {
                                "action": "adaptive_focus_adjustment",
                                "parameters": {
                                    "intensity_increase": intensity_increase,
                                    "focus_region_adjustment": [
                                        current_focus[0] + 2,
                                        current_focus[1] + 2,
                                        current_focus[2] * 1.2,
                                        current_focus[3] * 1.2
                                    ]
                                },
                                "priority": "high",
                                "reason": "Adaptive attention adjustment based on current state"
                            }
            
            elif metacognitive_state.metacognitive_process == MetacognitiveProcess.EVALUATION:
                # 基于评估数据创建控制动作
                evaluation_results = metacognitive_data.get("evaluation_results", {})
                
                for state_id, result in evaluation_results.items():
                    if state_id in cognitive_states and cognitive_states[state_id].cognitive_level == CognitiveLevel.LEARNING:
                        learning_efficiency = result.get("learning_efficiency", 0.0)
                        convergence_rate = result.get("convergence_rate", 0.0)
                        
                        # 自适应优化学习参数
                        if learning_efficiency < 0.7:
                            state = cognitive_states[state_id]
                            current_rate = state.data.get("learning_rate", 0.01)
                            current_batch = state.data.get("batch_size", 32)
                            
                            # 根据收敛率调整学习参数
                            if convergence_rate < 0.7:
                                # 收敛慢，减小学习率，增大批量
                                new_rate = current_rate * 0.8
                                new_batch = current_batch * 2
                            else:
                                # 收敛快，增大学习率
                                new_rate = current_rate * 1.2
                                new_batch = current_batch
                            
                            control_actions[state_id] = {
                                "action": "adaptive_learning_optimization",
                                "parameters": {
                                    "learning_rate": new_rate,
                                    "batch_size": new_batch,
                                    "momentum": 0.9
                                },
                                "priority": "medium",
                                "reason": "Adaptive learning parameter optimization"
                            }
        
        # 返回控制动作
        return {
            "control_actions": control_actions,
            "control_strategy": "adaptive",
            "timestamp": time.time()
        }
    
    # 注册自适应控制策略
    controller.register_control_strategy(
        strategy_id="adaptive_control",
        strategy_type=ControlStrategy.ADAPTIVE,
        strategy_function=adaptive_control_strategy
    )
    logger.info("注册自适应控制策略")
    
    # 执行自适应控制
    logger.info("\n执行自适应控制:")
    adaptive_control_result = controller.control(
        strategy_id="adaptive_control",
        metacognitive_state=evaluation_state,
        history=control_history
    )
    
    # 分析自适应控制结果
    logger.info("\n自适应控制结果:")
    control_actions = adaptive_control_result.get("control_actions", {})
    
    for target_id, action in control_actions.items():
        logger.info(f"  目标: {target_id}")
        logger.info(f"    动作: {action.get('action')}")
        logger.info(f"    参数: {action.get('parameters')}")
        logger.info(f"    优先级: {action.get('priority')}")
        logger.info(f"    原因: {action.get('reason')}")
    
    # 第六部分：比较不同控制策略
    logger.info("\n第六部分：比较不同控制策略")
    
    # 获取控制器统计信息
    stats = controller.get_stats()
    logger.info(f"控制器统计信息: {stats}")
    
    # 比较不同控制策略的特点
    logger.info("\n不同控制策略的特点比较:")
    logger.info("  监控控制:")
    logger.info("    - 基于实时监控数据")
    logger.info("    - 响应速度快")
    logger.info("    - 适合处理即时问题")
    logger.info("    - 缺乏深度分析")
    
    logger.info("  评估控制:")
    logger.info("    - 基于深度评估结果")
    logger.info("    - 能够识别和处理关键问题")
    logger.info("    - 提供更有针对性的控制动作")
    logger.info("    - 响应可能较慢")
    
    logger.info("  自适应控制:")
    logger.info("    - 基于历史控制效果和当前状态")
    logger.info("    - 能够学习和改进控制策略")
    logger.info("    - 提供最优化的控制动作")
    logger.info("    - 需要足够的历史数据")
    
    logger.info("元认知控制示例 - 第二部分完成")

if __name__ == "__main__":
    main()
