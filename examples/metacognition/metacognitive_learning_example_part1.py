"""
元认知学习示例 - 第一部分

本示例展示了如何使用元认知学习机制，包括基础学习、自适应学习和元学习。
这是第一部分，主要展示基础设置和基础学习。
"""

import numpy as np
import logging
import time
from typing import Any, Dict, List, Optional, Callable, Tuple

from src.core.metacognition import (
    CognitiveLevel, MetacognitiveProcess,
    NewCognitiveState as CognitiveState, 
    NewMetacognitiveState as MetacognitiveState,
    CognitiveProcess, NewMetacognitiveProcess as MetacognitiveProcess,
    MetacognitionFactory, MetacognitionManager
)
from src.core.metacognition.learning import (
    MetacognitiveLearner, LearningStrategy, LearningConfig
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    logger.info("元认知学习示例 - 第一部分")
    
    # 创建元认知系统工厂
    factory = MetacognitionFactory()
    
    # 创建元认知系统管理器
    manager = MetacognitionManager(manager_id="metacognition_manager")
    
    # 第一部分：创建学习配置和学习器
    logger.info("\n第一部分：创建学习配置和学习器")
    
    # 创建基础学习配置
    basic_config = LearningConfig(
        learning_rate=0.01,
        batch_size=32,
        epochs=5,
        validation_split=0.2,
        early_stopping=True,
        patience=3,
        min_delta=0.001
    )
    logger.info(f"创建基础学习配置: {basic_config}")
    
    # 创建自适应学习配置
    adaptive_config = LearningConfig(
        learning_rate=0.05,
        batch_size=64,
        epochs=10,
        validation_split=0.2,
        early_stopping=True,
        patience=5,
        min_delta=0.001,
        adaptive_learning_rate=True,
        learning_rate_decay=0.9,
        min_learning_rate=0.001
    )
    logger.info(f"创建自适应学习配置: {adaptive_config}")
    
    # 创建元学习配置
    meta_config = LearningConfig(
        learning_rate=0.1,
        batch_size=128,
        epochs=20,
        validation_split=0.3,
        early_stopping=True,
        patience=10,
        min_delta=0.0005,
        adaptive_learning_rate=True,
        learning_rate_decay=0.95,
        min_learning_rate=0.0005,
        meta_learning=True,
        meta_learning_rate=0.001
    )
    logger.info(f"创建元学习配置: {meta_config}")
    
    # 创建元认知学习器
    learner = MetacognitiveLearner(learner_id="metacognitive_learner")
    logger.info(f"创建元认知学习器: {learner}")
    
    # 第二部分：创建认知状态和元认知状态
    logger.info("\n第二部分：创建认知状态和元认知状态")
    
    # 创建感知层级的认知状态
    perception_data = {
        "image": np.random.rand(28, 28),  # 模拟图像数据
        "timestamp": time.time()
    }
    
    perception_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.PERCEPTION,
        data=perception_data,
        metadata={"description": "视觉感知状态", "source": "camera"}
    )
    logger.info(f"创建感知状态: {perception_state}")
    
    # 创建注意力层级的认知状态
    attention_data = {
        "focus_region": [10, 10, 5, 5],  # 模拟注意力焦点区域 [x, y, width, height]
        "focus_intensity": 0.8,
        "timestamp": time.time()
    }
    
    attention_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.ATTENTION,
        data=attention_data,
        metadata={"description": "视觉注意力状态", "source": "attention_mechanism"}
    )
    logger.info(f"创建注意力状态: {attention_state}")
    
    # 创建学习层级的认知状态
    learning_data = {
        "learning_rate": 0.01,
        "batch_size": 32,
        "epochs": 10,
        "loss_history": [0.5, 0.4, 0.3, 0.2, 0.1],
        "timestamp": time.time()
    }
    
    learning_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.LEARNING,
        data=learning_data,
        metadata={"description": "学习状态", "source": "learning_system"}
    )
    logger.info(f"创建学习状态: {learning_state}")
    
    # 收集所有认知状态
    cognitive_states = {
        perception_state.state_id: perception_state,
        attention_state.state_id: attention_state,
        learning_state.state_id: learning_state
    }
    
    # 创建监控元认知状态
    monitoring_data = {
        "timestamp": time.time(),
        "cognitive_state_count": 3,
        "cognitive_levels_present": [
            CognitiveLevel.PERCEPTION.value,
            CognitiveLevel.ATTENTION.value,
            CognitiveLevel.LEARNING.value
        ],
        "system_load": 0.6,
        "attention_focus": "learning"
    }
    
    monitoring_state = manager.create_metacognitive_state(
        metacognitive_process=MetacognitiveProcess.MONITORING,
        cognitive_states=cognitive_states,
        data=monitoring_data,
        metadata={"description": "系统监控状态", "source": "metacognition_monitor"}
    )
    logger.info(f"创建监控元认知状态: {monitoring_state}")
    
    # 第三部分：基础学习
    logger.info("\n第三部分：基础学习")
    
    # 创建训练数据
    num_samples = 100
    X_train = np.random.rand(num_samples, 28, 28)  # 模拟图像数据
    y_train = np.random.randint(0, 10, num_samples)  # 模拟标签数据
    
    # 创建验证数据
    num_val_samples = 20
    X_val = np.random.rand(num_val_samples, 28, 28)
    y_val = np.random.randint(0, 10, num_val_samples)
    
    # 创建测试数据
    num_test_samples = 10
    X_test = np.random.rand(num_test_samples, 28, 28)
    y_test = np.random.randint(0, 10, num_test_samples)
    
    logger.info(f"创建训练数据: {num_samples} 个样本")
    logger.info(f"创建验证数据: {num_val_samples} 个样本")
    logger.info(f"创建测试数据: {num_test_samples} 个样本")
    
    # 定义基础学习策略
    def basic_learning_strategy(X, y, config: LearningConfig) -> Dict[str, Any]:
        """基础学习策略"""
        # 模拟训练过程
        epochs = config.epochs
        learning_rate = config.learning_rate
        batch_size = config.batch_size
        
        # 初始化结果
        loss_history = []
        accuracy_history = []
        val_loss_history = []
        val_accuracy_history = []
        
        # 模拟训练循环
        for epoch in range(epochs):
            # 模拟训练损失和准确率
            train_loss = 1.0 / (epoch + 1) + 0.1 * np.random.rand()
            train_accuracy = 0.5 + 0.5 * (1.0 - train_loss)
            
            # 模拟验证损失和准确率
            val_loss = train_loss + 0.05 * np.random.rand()
            val_accuracy = train_accuracy - 0.05 * np.random.rand()
            
            # 记录历史
            loss_history.append(train_loss)
            accuracy_history.append(train_accuracy)
            val_loss_history.append(val_loss)
            val_accuracy_history.append(val_accuracy)
            
            logger.info(f"  轮次 {epoch+1}/{epochs}, 损失: {train_loss:.4f}, 准确率: {train_accuracy:.4f}, 验证损失: {val_loss:.4f}, 验证准确率: {val_accuracy:.4f}")
            
            # 早停检查
            if config.early_stopping and epoch >= config.patience:
                # 检查最近几轮的验证损失是否有显著改善
                recent_val_losses = val_loss_history[-config.patience:]
                min_recent_loss = min(recent_val_losses)
                if val_loss > min_recent_loss - config.min_delta:
                    logger.info(f"  早停在轮次 {epoch+1}")
                    break
        
        # 返回学习结果
        return {
            "loss_history": loss_history,
            "accuracy_history": accuracy_history,
            "val_loss_history": val_loss_history,
            "val_accuracy_history": val_accuracy_history,
            "final_loss": loss_history[-1],
            "final_accuracy": accuracy_history[-1],
            "final_val_loss": val_loss_history[-1],
            "final_val_accuracy": val_accuracy_history[-1],
            "epochs_completed": len(loss_history),
            "learning_rate": learning_rate,
            "batch_size": batch_size
        }
    
    # 注册基础学习策略
    learner.register_learning_strategy(
        strategy_id="basic_strategy",
        strategy_type=LearningStrategy.BASIC,
        strategy_function=basic_learning_strategy
    )
    logger.info("注册基础学习策略")
    
    # 执行基础学习
    logger.info("\n执行基础学习:")
    basic_result = learner.learn(
        strategy_id="basic_strategy",
        X=X_train,
        y=y_train,
        X_val=X_val,
        y_val=y_val,
        config=basic_config
    )
    
    # 分析基础学习结果
    logger.info("\n基础学习结果:")
    logger.info(f"  最终训练损失: {basic_result['final_loss']:.4f}")
    logger.info(f"  最终训练准确率: {basic_result['final_accuracy']:.4f}")
    logger.info(f"  最终验证损失: {basic_result['final_val_loss']:.4f}")
    logger.info(f"  最终验证准确率: {basic_result['final_val_accuracy']:.4f}")
    logger.info(f"  完成轮次: {basic_result['epochs_completed']}/{basic_config.epochs}")
    
    # 评估基础学习模型
    logger.info("\n评估基础学习模型:")
    
    # 模拟测试过程
    test_loss = basic_result['final_val_loss'] + 0.02 * np.random.rand()
    test_accuracy = basic_result['final_val_accuracy'] - 0.02 * np.random.rand()
    
    logger.info(f"  测试损失: {test_loss:.4f}")
    logger.info(f"  测试准确率: {test_accuracy:.4f}")
    
    # 更新学习状态
    updated_learning_data = learning_state.data.copy()
    updated_learning_data.update({
        "learning_rate": basic_config.learning_rate,
        "batch_size": basic_config.batch_size,
        "epochs": basic_result['epochs_completed'],
        "loss_history": basic_result['loss_history'],
        "accuracy_history": basic_result['accuracy_history'],
        "val_loss_history": basic_result['val_loss_history'],
        "val_accuracy_history": basic_result['val_accuracy_history'],
        "final_loss": basic_result['final_loss'],
        "final_accuracy": basic_result['final_accuracy'],
        "final_val_loss": basic_result['final_val_loss'],
        "final_val_accuracy": basic_result['final_val_accuracy'],
        "test_loss": test_loss,
        "test_accuracy": test_accuracy,
        "timestamp": time.time()
    })
    
    updated_learning_state = manager.update_cognitive_state(
        state_id=learning_state.state_id,
        data=updated_learning_data
    )
    logger.info(f"更新学习状态: {updated_learning_state}")
    
    logger.info("元认知学习示例 - 第一部分完成")

if __name__ == "__main__":
    main()
