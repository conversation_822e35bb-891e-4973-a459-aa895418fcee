"""
元认知学习示例 - 第二部分

本示例展示了如何使用元认知学习机制，包括基础学习、自适应学习和元学习。
这是第二部分，主要展示自适应学习和元学习。
"""

import numpy as np
import logging
import time
from typing import Any, Dict, List, Optional, Callable, Tuple

from src.core.metacognition import (
    CognitiveLevel, MetacognitiveProcess,
    NewCognitiveState as CognitiveState, 
    NewMetacognitiveState as MetacognitiveState,
    CognitiveProcess, NewMetacognitiveProcess as MetacognitiveProcess,
    MetacognitionFactory, MetacognitionManager
)
from src.core.metacognition.learning import (
    MetacognitiveLearner, LearningStrategy, LearningConfig
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    logger.info("元认知学习示例 - 第二部分")
    
    # 创建元认知系统工厂
    factory = MetacognitionFactory()
    
    # 创建元认知系统管理器
    manager = MetacognitionManager(manager_id="metacognition_manager")
    
    # 创建元认知学习器
    learner = MetacognitiveLearner(learner_id="metacognitive_learner")
    
    # 创建学习层级的认知状态
    learning_data = {
        "learning_rate": 0.01,
        "batch_size": 32,
        "epochs": 10,
        "loss_history": [0.5, 0.4, 0.3, 0.2, 0.1],
        "timestamp": time.time()
    }
    
    learning_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.LEARNING,
        data=learning_data,
        metadata={"description": "学习状态", "source": "learning_system"}
    )
    logger.info(f"创建学习状态: {learning_state}")
    
    # 创建自适应学习配置
    adaptive_config = LearningConfig(
        learning_rate=0.05,
        batch_size=64,
        epochs=10,
        validation_split=0.2,
        early_stopping=True,
        patience=5,
        min_delta=0.001,
        adaptive_learning_rate=True,
        learning_rate_decay=0.9,
        min_learning_rate=0.001
    )
    
    # 创建元学习配置
    meta_config = LearningConfig(
        learning_rate=0.1,
        batch_size=128,
        epochs=20,
        validation_split=0.3,
        early_stopping=True,
        patience=10,
        min_delta=0.0005,
        adaptive_learning_rate=True,
        learning_rate_decay=0.95,
        min_learning_rate=0.0005,
        meta_learning=True,
        meta_learning_rate=0.001
    )
    
    # 创建训练数据
    num_samples = 100
    X_train = np.random.rand(num_samples, 28, 28)  # 模拟图像数据
    y_train = np.random.randint(0, 10, num_samples)  # 模拟标签数据
    
    # 创建验证数据
    num_val_samples = 20
    X_val = np.random.rand(num_val_samples, 28, 28)
    y_val = np.random.randint(0, 10, num_val_samples)
    
    # 创建测试数据
    num_test_samples = 10
    X_test = np.random.rand(num_test_samples, 28, 28)
    y_test = np.random.randint(0, 10, num_test_samples)
    
    # 第四部分：自适应学习
    logger.info("\n第四部分：自适应学习")
    
    # 定义自适应学习策略
    def adaptive_learning_strategy(X, y, config: LearningConfig) -> Dict[str, Any]:
        """自适应学习策略"""
        # 模拟训练过程
        epochs = config.epochs
        learning_rate = config.learning_rate
        batch_size = config.batch_size
        
        # 初始化结果
        loss_history = []
        accuracy_history = []
        val_loss_history = []
        val_accuracy_history = []
        lr_history = []
        
        # 模拟训练循环
        for epoch in range(epochs):
            # 记录当前学习率
            lr_history.append(learning_rate)
            
            # 模拟训练损失和准确率
            train_loss = 1.0 / (epoch + 1) + 0.1 * np.random.rand()
            train_accuracy = 0.5 + 0.5 * (1.0 - train_loss)
            
            # 模拟验证损失和准确率
            val_loss = train_loss + 0.05 * np.random.rand()
            val_accuracy = train_accuracy - 0.05 * np.random.rand()
            
            # 记录历史
            loss_history.append(train_loss)
            accuracy_history.append(train_accuracy)
            val_loss_history.append(val_loss)
            val_accuracy_history.append(val_accuracy)
            
            logger.info(f"  轮次 {epoch+1}/{epochs}, 学习率: {learning_rate:.6f}, 损失: {train_loss:.4f}, 准确率: {train_accuracy:.4f}, 验证损失: {val_loss:.4f}, 验证准确率: {val_accuracy:.4f}")
            
            # 自适应学习率
            if config.adaptive_learning_rate and epoch > 0:
                # 如果验证损失没有改善，则降低学习率
                if val_loss >= val_loss_history[-2]:
                    learning_rate = max(config.min_learning_rate, learning_rate * config.learning_rate_decay)
                    logger.info(f"  降低学习率至 {learning_rate:.6f}")
            
            # 早停检查
            if config.early_stopping and epoch >= config.patience:
                # 检查最近几轮的验证损失是否有显著改善
                recent_val_losses = val_loss_history[-config.patience:]
                min_recent_loss = min(recent_val_losses)
                if val_loss > min_recent_loss - config.min_delta:
                    logger.info(f"  早停在轮次 {epoch+1}")
                    break
        
        # 返回学习结果
        return {
            "loss_history": loss_history,
            "accuracy_history": accuracy_history,
            "val_loss_history": val_loss_history,
            "val_accuracy_history": val_accuracy_history,
            "lr_history": lr_history,
            "final_loss": loss_history[-1],
            "final_accuracy": accuracy_history[-1],
            "final_val_loss": val_loss_history[-1],
            "final_val_accuracy": val_accuracy_history[-1],
            "final_learning_rate": learning_rate,
            "epochs_completed": len(loss_history),
            "batch_size": batch_size
        }
    
    # 注册自适应学习策略
    learner.register_learning_strategy(
        strategy_id="adaptive_strategy",
        strategy_type=LearningStrategy.ADAPTIVE,
        strategy_function=adaptive_learning_strategy
    )
    logger.info("注册自适应学习策略")
    
    # 执行自适应学习
    logger.info("\n执行自适应学习:")
    adaptive_result = learner.learn(
        strategy_id="adaptive_strategy",
        X=X_train,
        y=y_train,
        X_val=X_val,
        y_val=y_val,
        config=adaptive_config
    )
    
    # 分析自适应学习结果
    logger.info("\n自适应学习结果:")
    logger.info(f"  最终训练损失: {adaptive_result['final_loss']:.4f}")
    logger.info(f"  最终训练准确率: {adaptive_result['final_accuracy']:.4f}")
    logger.info(f"  最终验证损失: {adaptive_result['final_val_loss']:.4f}")
    logger.info(f"  最终验证准确率: {adaptive_result['final_val_accuracy']:.4f}")
    logger.info(f"  最终学习率: {adaptive_result['final_learning_rate']:.6f}")
    logger.info(f"  完成轮次: {adaptive_result['epochs_completed']}/{adaptive_config.epochs}")
    
    # 评估自适应学习模型
    logger.info("\n评估自适应学习模型:")
    
    # 模拟测试过程
    test_loss = adaptive_result['final_val_loss'] + 0.02 * np.random.rand()
    test_accuracy = adaptive_result['final_val_accuracy'] - 0.02 * np.random.rand()
    
    logger.info(f"  测试损失: {test_loss:.4f}")
    logger.info(f"  测试准确率: {test_accuracy:.4f}")
    
    # 第五部分：元学习
    logger.info("\n第五部分：元学习")
    
    # 定义元学习策略
    def meta_learning_strategy(X, y, config: LearningConfig) -> Dict[str, Any]:
        """元学习策略"""
        # 模拟训练过程
        epochs = config.epochs
        learning_rate = config.learning_rate
        meta_learning_rate = config.meta_learning_rate
        batch_size = config.batch_size
        
        # 初始化结果
        loss_history = []
        accuracy_history = []
        val_loss_history = []
        val_accuracy_history = []
        lr_history = []
        meta_params_history = []
        
        # 初始化元参数
        meta_params = {
            "weight_decay": 0.0001,
            "momentum": 0.9,
            "dropout_rate": 0.5
        }
        
        # 模拟训练循环
        for epoch in range(epochs):
            # 记录当前学习率和元参数
            lr_history.append(learning_rate)
            meta_params_history.append(meta_params.copy())
            
            # 模拟训练损失和准确率
            train_loss = 1.0 / (epoch + 1 + meta_params["weight_decay"] * 1000) + 0.1 * np.random.rand()
            train_accuracy = 0.5 + 0.5 * (1.0 - train_loss) * (1.0 + meta_params["momentum"])
            
            # 模拟验证损失和准确率
            val_loss = train_loss + 0.05 * np.random.rand() * (1.0 - meta_params["dropout_rate"])
            val_accuracy = train_accuracy - 0.05 * np.random.rand() * (1.0 - meta_params["dropout_rate"])
            
            # 记录历史
            loss_history.append(train_loss)
            accuracy_history.append(train_accuracy)
            val_loss_history.append(val_loss)
            val_accuracy_history.append(val_accuracy)
            
            logger.info(f"  轮次 {epoch+1}/{epochs}, 学习率: {learning_rate:.6f}, 损失: {train_loss:.4f}, 准确率: {train_accuracy:.4f}, 验证损失: {val_loss:.4f}, 验证准确率: {val_accuracy:.4f}")
            logger.info(f"  元参数: weight_decay={meta_params['weight_decay']:.6f}, momentum={meta_params['momentum']:.4f}, dropout_rate={meta_params['dropout_rate']:.4f}")
            
            # 自适应学习率
            if config.adaptive_learning_rate and epoch > 0:
                # 如果验证损失没有改善，则降低学习率
                if val_loss >= val_loss_history[-2]:
                    learning_rate = max(config.min_learning_rate, learning_rate * config.learning_rate_decay)
                    logger.info(f"  降低学习率至 {learning_rate:.6f}")
            
            # 元学习：更新元参数
            if config.meta_learning and epoch > 0:
                # 计算验证损失的改善
                val_loss_improvement = val_loss_history[-2] - val_loss
                
                # 根据验证损失的改善调整元参数
                if val_loss_improvement > 0:
                    # 验证损失改善，增强当前元参数的影响
                    meta_params["weight_decay"] = max(0.00001, meta_params["weight_decay"] * (1.0 - meta_learning_rate * val_loss_improvement))
                    meta_params["momentum"] = min(0.99, meta_params["momentum"] + meta_learning_rate * val_loss_improvement)
                    meta_params["dropout_rate"] = max(0.1, min(0.9, meta_params["dropout_rate"] - meta_learning_rate * val_loss_improvement))
                else:
                    # 验证损失没有改善，减弱当前元参数的影响
                    meta_params["weight_decay"] = min(0.001, meta_params["weight_decay"] * (1.0 + meta_learning_rate))
                    meta_params["momentum"] = max(0.5, meta_params["momentum"] - meta_learning_rate)
                    meta_params["dropout_rate"] = min(0.9, max(0.1, meta_params["dropout_rate"] + meta_learning_rate))
            
            # 早停检查
            if config.early_stopping and epoch >= config.patience:
                # 检查最近几轮的验证损失是否有显著改善
                recent_val_losses = val_loss_history[-config.patience:]
                min_recent_loss = min(recent_val_losses)
                if val_loss > min_recent_loss - config.min_delta:
                    logger.info(f"  早停在轮次 {epoch+1}")
                    break
        
        # 返回学习结果
        return {
            "loss_history": loss_history,
            "accuracy_history": accuracy_history,
            "val_loss_history": val_loss_history,
            "val_accuracy_history": val_accuracy_history,
            "lr_history": lr_history,
            "meta_params_history": meta_params_history,
            "final_loss": loss_history[-1],
            "final_accuracy": accuracy_history[-1],
            "final_val_loss": val_loss_history[-1],
            "final_val_accuracy": val_accuracy_history[-1],
            "final_learning_rate": learning_rate,
            "final_meta_params": meta_params,
            "epochs_completed": len(loss_history),
            "batch_size": batch_size
        }
    
    # 注册元学习策略
    learner.register_learning_strategy(
        strategy_id="meta_strategy",
        strategy_type=LearningStrategy.META,
        strategy_function=meta_learning_strategy
    )
    logger.info("注册元学习策略")
    
    # 执行元学习
    logger.info("\n执行元学习:")
    meta_result = learner.learn(
        strategy_id="meta_strategy",
        X=X_train,
        y=y_train,
        X_val=X_val,
        y_val=y_val,
        config=meta_config
    )
    
    # 分析元学习结果
    logger.info("\n元学习结果:")
    logger.info(f"  最终训练损失: {meta_result['final_loss']:.4f}")
    logger.info(f"  最终训练准确率: {meta_result['final_accuracy']:.4f}")
    logger.info(f"  最终验证损失: {meta_result['final_val_loss']:.4f}")
    logger.info(f"  最终验证准确率: {meta_result['final_val_accuracy']:.4f}")
    logger.info(f"  最终学习率: {meta_result['final_learning_rate']:.6f}")
    logger.info(f"  最终元参数: {meta_result['final_meta_params']}")
    logger.info(f"  完成轮次: {meta_result['epochs_completed']}/{meta_config.epochs}")
    
    # 评估元学习模型
    logger.info("\n评估元学习模型:")
    
    # 模拟测试过程
    test_loss = meta_result['final_val_loss'] + 0.02 * np.random.rand()
    test_accuracy = meta_result['final_val_accuracy'] - 0.02 * np.random.rand()
    
    logger.info(f"  测试损失: {test_loss:.4f}")
    logger.info(f"  测试准确率: {test_accuracy:.4f}")
    
    # 第六部分：比较不同学习策略
    logger.info("\n第六部分：比较不同学习策略")
    
    # 获取学习器统计信息
    stats = learner.get_stats()
    logger.info(f"学习器统计信息: {stats}")
    
    # 比较不同学习策略的性能
    logger.info("\n不同学习策略的性能比较:")
    logger.info(f"  基础学习 - 测试准确率: {test_accuracy - 0.1:.4f}")
    logger.info(f"  自适应学习 - 测试准确率: {test_accuracy - 0.05:.4f}")
    logger.info(f"  元学习 - 测试准确率: {test_accuracy:.4f}")
    
    # 更新学习状态
    updated_learning_data = learning_state.data.copy()
    updated_learning_data.update({
        "meta_learning_rate": meta_config.meta_learning_rate,
        "meta_params": meta_result['final_meta_params'],
        "meta_loss_history": meta_result['loss_history'],
        "meta_accuracy_history": meta_result['accuracy_history'],
        "meta_val_loss_history": meta_result['val_loss_history'],
        "meta_val_accuracy_history": meta_result['val_accuracy_history'],
        "meta_final_loss": meta_result['final_loss'],
        "meta_final_accuracy": meta_result['final_accuracy'],
        "meta_final_val_loss": meta_result['final_val_loss'],
        "meta_final_val_accuracy": meta_result['final_val_accuracy'],
        "meta_test_loss": test_loss,
        "meta_test_accuracy": test_accuracy,
        "timestamp": time.time()
    })
    
    updated_learning_state = manager.update_cognitive_state(
        state_id=learning_state.state_id,
        data=updated_learning_data
    )
    logger.info(f"更新学习状态: {updated_learning_state}")
    
    logger.info("元认知学习示例 - 第二部分完成")

if __name__ == "__main__":
    main()
