"""
多层次递归优化器示例

本脚本展示了如何使用多层次递归优化器。
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import sys
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入我们的实现
from src.algorithms.multilevel_optimizer import (
    MultiLevelRecursiveOptimizer,
    Hierarchy,
    HierarchyNode,
    HierarchyBuilder,
    OptimizationStrategy,
    StrategySelector,
    OptimizationAnalyzer,
    create_test_problem
)


def run_optimization(problem_type='quadratic', dimension=10, 
                    optimization_method='standard', recursion_strategy='top_down',
                    max_recursion_depth=3, convergence_threshold=1e-6):
    """
    运行优化
    
    参数:
        problem_type: 问题类型，可选值为'quadratic', 'rosenbrock', 'rastrigin'
        dimension: 问题维度
        optimization_method: 优化方法，可选值为'standard', 'adaptive', 'hierarchical'
        recursion_strategy: 递归策略，可选值为'top_down', 'bottom_up', 'bidirectional'
        max_recursion_depth: 最大递归深度
        convergence_threshold: 收敛阈值
    
    返回:
        优化结果
    """
    # 创建测试问题
    problem, objective_function, constraints, initial_solution = create_test_problem(
        problem_type=problem_type,
        dimension=dimension,
        seed=42
    )
    
    # 创建优化器
    optimizer = MultiLevelRecursiveOptimizer(
        optimization_method=optimization_method,
        recursion_strategy=recursion_strategy,
        max_recursion_depth=max_recursion_depth,
        convergence_threshold=convergence_threshold,
        use_parallel=True,
        num_workers=4,
        use_cache=True,
        cache_size=10
    )
    
    # 创建输入数据
    input_data = {
        'problem': problem,
        'objective_function': objective_function,
        'constraints': constraints,
        'initial_solution': initial_solution
    }
    
    # 执行优化
    print(f"执行多层次递归优化...")
    print(f"  问题类型: {problem_type}")
    print(f"  问题维度: {dimension}")
    print(f"  优化方法: {optimization_method}")
    print(f"  递归策略: {recursion_strategy}")
    print(f"  最大递归深度: {max_recursion_depth}")
    print(f"  收敛阈值: {convergence_threshold}")
    
    start_time = time.time()
    result = optimizer.compute(input_data)
    end_time = time.time()
    
    print(f"优化完成，耗时: {end_time - start_time:.2f}秒")
    
    # 提取结果
    solution = result['solution']
    objective_value = result['objective_value']
    iterations = result['iterations']
    hierarchy = result['hierarchy']
    convergence_history = result['convergence_history']
    analysis = result['analysis']
    performance = result['performance']
    
    # 打印结果
    print(f"优化结果:")
    print(f"  初始目标函数值: {objective_function(initial_solution):.6f}")
    print(f"  最终目标函数值: {objective_value:.6f}")
    print(f"  迭代次数: {iterations}")
    print(f"  解的范数: {analysis['solution_norm']:.6f}")
    print(f"  解的稀疏性: {analysis['solution_sparsity']:.6f}")
    print(f"  梯度范数: {analysis['gradient_norm']:.6f}")
    print(f"  是否收敛: {analysis['is_converged']}")
    print(f"  收敛率: {analysis['convergence_rate']:.6f}")
    
    # 打印性能指标
    print(f"性能指标:")
    print(f"  总时间: {performance['total_time']:.2f}秒")
    print(f"  层次结构构建时间: {performance['hierarchy_construction_time']:.2f}秒")
    print(f"  优化时间: {performance['optimization_time']:.2f}秒")
    print(f"  分析时间: {performance['analysis_time']:.2f}秒")
    print(f"  内存使用: {performance['memory_usage']:.2f}MB")
    
    # 创建分析器
    analyzer = OptimizationAnalyzer()
    
    # 绘制收敛历史
    fig_convergence = analyzer.plot_convergence(convergence_history)
    
    # 绘制层次结构
    fig_hierarchy = analyzer.plot_hierarchy(hierarchy)
    
    # 绘制解
    fig_solution = analyzer.plot_solution(solution)
    
    # 创建输出目录
    os.makedirs('output', exist_ok=True)
    
    # 保存图形
    fig_convergence.savefig(f'output/convergence_{problem_type}_{optimization_method}_{recursion_strategy}.png')
    fig_hierarchy.savefig(f'output/hierarchy_{problem_type}_{optimization_method}_{recursion_strategy}.png')
    fig_solution.savefig(f'output/solution_{problem_type}_{optimization_method}_{recursion_strategy}.png')
    
    return result


def compare_optimization_methods(problem_type='quadratic', dimension=10):
    """
    比较不同的优化方法
    
    参数:
        problem_type: 问题类型，可选值为'quadratic', 'rosenbrock', 'rastrigin'
        dimension: 问题维度
    """
    # 优化方法列表
    optimization_methods = ['standard', 'adaptive', 'hierarchical']
    
    # 存储结果
    results = {}
    
    # 对每种优化方法执行优化
    for method in optimization_methods:
        print(f"\n===== 使用{method}优化方法 =====")
        result = run_optimization(
            problem_type=problem_type,
            dimension=dimension,
            optimization_method=method,
            recursion_strategy='top_down'
        )
        results[method] = result
    
    # 比较不同优化方法的结果
    plt.figure(figsize=(12, 8))
    
    # 绘制收敛历史
    plt.subplot(2, 2, 1)
    for method in optimization_methods:
        plt.plot(
            range(len(results[method]['convergence_history'])),
            results[method]['convergence_history'],
            label=f"{method}"
        )
    plt.xlabel('迭代次数')
    plt.ylabel('目标函数值')
    plt.title('收敛历史')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 绘制目标函数值
    plt.subplot(2, 2, 2)
    objective_values = [results[method]['objective_value'] for method in optimization_methods]
    plt.bar(optimization_methods, objective_values)
    plt.xlabel('优化方法')
    plt.ylabel('目标函数值')
    plt.title('最终目标函数值')
    plt.grid(True, alpha=0.3)
    
    # 绘制迭代次数
    plt.subplot(2, 2, 3)
    iterations = [results[method]['iterations'] for method in optimization_methods]
    plt.bar(optimization_methods, iterations)
    plt.xlabel('优化方法')
    plt.ylabel('迭代次数')
    plt.title('迭代次数')
    plt.grid(True, alpha=0.3)
    
    # 绘制计算时间
    plt.subplot(2, 2, 4)
    times = [results[method]['performance']['total_time'] for method in optimization_methods]
    plt.bar(optimization_methods, times)
    plt.xlabel('优化方法')
    plt.ylabel('计算时间 (秒)')
    plt.title('计算时间')
    plt.grid(True, alpha=0.3)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图形
    plt.savefig(f'output/optimization_methods_comparison_{problem_type}.png')
    
    # 显示图形
    plt.show()


def compare_recursion_strategies(problem_type='quadratic', dimension=10):
    """
    比较不同的递归策略
    
    参数:
        problem_type: 问题类型，可选值为'quadratic', 'rosenbrock', 'rastrigin'
        dimension: 问题维度
    """
    # 递归策略列表
    recursion_strategies = ['top_down', 'bottom_up', 'bidirectional']
    
    # 存储结果
    results = {}
    
    # 对每种递归策略执行优化
    for strategy in recursion_strategies:
        print(f"\n===== 使用{strategy}递归策略 =====")
        result = run_optimization(
            problem_type=problem_type,
            dimension=dimension,
            optimization_method='standard',
            recursion_strategy=strategy
        )
        results[strategy] = result
    
    # 比较不同递归策略的结果
    plt.figure(figsize=(12, 8))
    
    # 绘制收敛历史
    plt.subplot(2, 2, 1)
    for strategy in recursion_strategies:
        plt.plot(
            range(len(results[strategy]['convergence_history'])),
            results[strategy]['convergence_history'],
            label=f"{strategy}"
        )
    plt.xlabel('迭代次数')
    plt.ylabel('目标函数值')
    plt.title('收敛历史')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 绘制目标函数值
    plt.subplot(2, 2, 2)
    objective_values = [results[strategy]['objective_value'] for strategy in recursion_strategies]
    plt.bar(recursion_strategies, objective_values)
    plt.xlabel('递归策略')
    plt.ylabel('目标函数值')
    plt.title('最终目标函数值')
    plt.grid(True, alpha=0.3)
    
    # 绘制迭代次数
    plt.subplot(2, 2, 3)
    iterations = [results[strategy]['iterations'] for strategy in recursion_strategies]
    plt.bar(recursion_strategies, iterations)
    plt.xlabel('递归策略')
    plt.ylabel('迭代次数')
    plt.title('迭代次数')
    plt.grid(True, alpha=0.3)
    
    # 绘制计算时间
    plt.subplot(2, 2, 4)
    times = [results[strategy]['performance']['total_time'] for strategy in recursion_strategies]
    plt.bar(recursion_strategies, times)
    plt.xlabel('递归策略')
    plt.ylabel('计算时间 (秒)')
    plt.title('计算时间')
    plt.grid(True, alpha=0.3)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图形
    plt.savefig(f'output/recursion_strategies_comparison_{problem_type}.png')
    
    # 显示图形
    plt.show()


def compare_problem_types(dimension=10):
    """
    比较不同的问题类型
    
    参数:
        dimension: 问题维度
    """
    # 问题类型列表
    problem_types = ['quadratic', 'rosenbrock', 'rastrigin']
    
    # 存储结果
    results = {}
    
    # 对每种问题类型执行优化
    for problem_type in problem_types:
        print(f"\n===== 使用{problem_type}问题类型 =====")
        result = run_optimization(
            problem_type=problem_type,
            dimension=dimension,
            optimization_method='standard',
            recursion_strategy='top_down'
        )
        results[problem_type] = result
    
    # 比较不同问题类型的结果
    plt.figure(figsize=(12, 8))
    
    # 绘制收敛历史
    plt.subplot(2, 2, 1)
    for problem_type in problem_types:
        # 归一化收敛历史
        history = results[problem_type]['convergence_history']
        normalized_history = [(h - min(history)) / (max(history) - min(history)) for h in history]
        
        plt.plot(
            range(len(normalized_history)),
            normalized_history,
            label=f"{problem_type}"
        )
    plt.xlabel('迭代次数')
    plt.ylabel('归一化目标函数值')
    plt.title('归一化收敛历史')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 绘制迭代次数
    plt.subplot(2, 2, 2)
    iterations = [results[problem_type]['iterations'] for problem_type in problem_types]
    plt.bar(problem_types, iterations)
    plt.xlabel('问题类型')
    plt.ylabel('迭代次数')
    plt.title('迭代次数')
    plt.grid(True, alpha=0.3)
    
    # 绘制计算时间
    plt.subplot(2, 2, 3)
    times = [results[problem_type]['performance']['total_time'] for problem_type in problem_types]
    plt.bar(problem_types, times)
    plt.xlabel('问题类型')
    plt.ylabel('计算时间 (秒)')
    plt.title('计算时间')
    plt.grid(True, alpha=0.3)
    
    # 绘制收敛率
    plt.subplot(2, 2, 4)
    convergence_rates = [results[problem_type]['analysis']['convergence_rate'] for problem_type in problem_types]
    plt.bar(problem_types, convergence_rates)
    plt.xlabel('问题类型')
    plt.ylabel('收敛率')
    plt.title('收敛率')
    plt.grid(True, alpha=0.3)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图形
    plt.savefig('output/problem_types_comparison.png')
    
    # 显示图形
    plt.show()


def explore_dimension_effect(problem_type='quadratic'):
    """
    探索问题维度的影响
    
    参数:
        problem_type: 问题类型，可选值为'quadratic', 'rosenbrock', 'rastrigin'
    """
    # 问题维度列表
    dimensions = [5, 10, 20, 50, 100]
    
    # 存储结果
    results = {}
    
    # 对每种问题维度执行优化
    for dimension in dimensions:
        print(f"\n===== 使用{dimension}维问题 =====")
        result = run_optimization(
            problem_type=problem_type,
            dimension=dimension,
            optimization_method='standard',
            recursion_strategy='top_down'
        )
        results[dimension] = result
    
    # 比较不同问题维度的结果
    plt.figure(figsize=(12, 8))
    
    # 绘制目标函数值
    plt.subplot(2, 2, 1)
    objective_values = [results[dimension]['objective_value'] for dimension in dimensions]
    plt.plot(dimensions, objective_values, marker='o')
    plt.xlabel('问题维度')
    plt.ylabel('目标函数值')
    plt.title('最终目标函数值')
    plt.grid(True, alpha=0.3)
    
    # 绘制迭代次数
    plt.subplot(2, 2, 2)
    iterations = [results[dimension]['iterations'] for dimension in dimensions]
    plt.plot(dimensions, iterations, marker='o')
    plt.xlabel('问题维度')
    plt.ylabel('迭代次数')
    plt.title('迭代次数')
    plt.grid(True, alpha=0.3)
    
    # 绘制计算时间
    plt.subplot(2, 2, 3)
    times = [results[dimension]['performance']['total_time'] for dimension in dimensions]
    plt.plot(dimensions, times, marker='o')
    plt.xlabel('问题维度')
    plt.ylabel('计算时间 (秒)')
    plt.title('计算时间')
    plt.grid(True, alpha=0.3)
    
    # 绘制层次结构节点数
    plt.subplot(2, 2, 4)
    num_nodes = [results[dimension]['hierarchy'].get_num_nodes() for dimension in dimensions]
    plt.plot(dimensions, num_nodes, marker='o')
    plt.xlabel('问题维度')
    plt.ylabel('层次结构节点数')
    plt.title('层次结构节点数')
    plt.grid(True, alpha=0.3)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图形
    plt.savefig(f'output/dimension_effect_{problem_type}.png')
    
    # 显示图形
    plt.show()


def main():
    """主函数"""
    # 设置随机数种子，确保结果可重现
    np.random.seed(42)
    
    # 创建输出目录
    os.makedirs('output', exist_ok=True)
    
    # 运行单个优化
    print("\n===== 运行单个优化 =====")
    run_optimization(
        problem_type='quadratic',
        dimension=10,
        optimization_method='standard',
        recursion_strategy='top_down'
    )
    
    # 比较不同的优化方法
    print("\n===== 比较不同的优化方法 =====")
    compare_optimization_methods(
        problem_type='quadratic',
        dimension=10
    )
    
    # 比较不同的递归策略
    print("\n===== 比较不同的递归策略 =====")
    compare_recursion_strategies(
        problem_type='quadratic',
        dimension=10
    )
    
    # 比较不同的问题类型
    print("\n===== 比较不同的问题类型 =====")
    compare_problem_types(
        dimension=10
    )
    
    # 探索问题维度的影响
    print("\n===== 探索问题维度的影响 =====")
    explore_dimension_effect(
        problem_type='quadratic'
    )
    
    print("\n示例完成！")


if __name__ == "__main__":
    main()
