#!/usr/bin/env python3
"""
动态形态算子示例 (PyO3 0.24+ 兼容版本)

本示例展示了如何使用动态形态算子进行数据变换。
"""

import time
import numpy as np
import json
from pprint import pprint

# 导入动态形态算子
try:
    from dynamic_morphism_v24 import DynamicMorphismOperator
except ImportError:
    print("错误: 无法导入动态形态算子。请确保已编译Rust绑定。")
    print("提示: 可以使用以下命令编译: cd rust_operators/dynamic_morphism_v24 && cargo build --release")
    exit(1)

def create_simple_graph():
    """创建简单的形态图"""
    # 定义节点
    nodes = {
        "input": {
            "type": "input",
            "dimension": 3
        },
        "hidden1": {
            "type": "hidden",
            "dimension": 4,
            "activation": "relu"
        },
        "hidden2": {
            "type": "hidden",
            "dimension": 4,
            "activation": "tanh"
        },
        "output": {
            "type": "output",
            "dimension": 2,
            "activation": "sigmoid"
        }
    }
    
    # 定义边
    edges = [
        {
            "id": "edge1",
            "source": "input",
            "target": "hidden1",
            "type": "standard",
            "weight": [
                [0.1, 0.2, 0.3],
                [0.4, 0.5, 0.6],
                [0.7, 0.8, 0.9],
                [0.1, 0.2, 0.3]
            ]
        },
        {
            "id": "edge2",
            "source": "hidden1",
            "target": "hidden2",
            "type": "standard",
            "weight": [
                [0.1, 0.2, 0.3, 0.4],
                [0.5, 0.6, 0.7, 0.8],
                [0.9, 0.1, 0.2, 0.3],
                [0.4, 0.5, 0.6, 0.7]
            ]
        },
        {
            "id": "edge3",
            "source": "hidden2",
            "target": "output",
            "type": "standard",
            "weight": [
                [0.1, 0.2, 0.3, 0.4],
                [0.5, 0.6, 0.7, 0.8]
            ]
        },
        {
            "id": "edge4",
            "source": "input",
            "target": "output",
            "type": "skip",
            "weight": [
                [0.1, 0.2, 0.3],
                [0.4, 0.5, 0.6]
            ]
        }
    ]
    
    return nodes, edges

def create_adaptive_graph():
    """创建自适应形态图"""
    # 定义节点
    nodes = {
        "input": {
            "type": "input",
            "dimension": 5
        },
        "hidden1": {
            "type": "hidden",
            "dimension": 8,
            "activation": "relu"
        },
        "hidden2": {
            "type": "hidden",
            "dimension": 8,
            "activation": "tanh"
        },
        "hidden3": {
            "type": "hidden",
            "dimension": 6,
            "activation": "sigmoid"
        },
        "output": {
            "type": "output",
            "dimension": 3,
            "activation": "softmax"
        }
    }
    
    # 定义边
    edges = [
        {
            "id": "edge1",
            "source": "input",
            "target": "hidden1",
            "type": "standard",
            "weight": [[0.1 * (i + j) for j in range(5)] for i in range(8)]
        },
        {
            "id": "edge2",
            "source": "hidden1",
            "target": "hidden2",
            "type": "standard",
            "weight": [[0.05 * (i + j) for j in range(8)] for i in range(8)]
        },
        {
            "id": "edge3",
            "source": "hidden2",
            "target": "hidden3",
            "type": "standard",
            "weight": [[0.1 * (i + j) for j in range(8)] for i in range(6)]
        },
        {
            "id": "edge4",
            "source": "hidden3",
            "target": "output",
            "type": "standard",
            "weight": [[0.15 * (i + j) for j in range(6)] for i in range(3)]
        },
        {
            "id": "edge5",
            "source": "input",
            "target": "hidden3",
            "type": "skip",
            "weight": [[0.1 * (i + j) for j in range(5)] for i in range(6)]
        },
        {
            "id": "edge6",
            "source": "hidden1",
            "target": "output",
            "type": "skip",
            "weight": [[0.05 * (i + j) for j in range(8)] for i in range(3)]
        }
    ]
    
    return nodes, edges

def example_linear_transform():
    """线性变换示例"""
    print("\n=== 线性变换示例 ===")
    
    # 创建动态形态算子
    operator = DynamicMorphismOperator(use_cache=True, parallel=True, morphism_type="linear", direction="forward")
    
    # 创建形态图
    nodes, edges = create_simple_graph()
    
    # 设置形态图
    operator.set_graph(nodes, edges)
    
    # 创建输入数据
    input_data = {
        "input": [1.0, 2.0, 3.0]
    }
    
    # 应用算子
    start_time = time.time()
    result = operator.apply(input_data)
    end_time = time.time()
    
    # 打印结果
    print(f"输入数据: {input_data}")
    print(f"输出数据: {result}")
    print(f"执行时间: {(end_time - start_time) * 1000:.2f} ms")
    
    # 获取算子元数据
    metadata = operator.get_metadata()
    print(f"算子元数据: {metadata}")
    
    # 获取性能指标
    metrics = operator.get_performance_metrics()
    print(f"性能指标: {metrics}")

def example_nonlinear_transform():
    """非线性变换示例"""
    print("\n=== 非线性变换示例 ===")
    
    # 创建动态形态算子
    operator = DynamicMorphismOperator(use_cache=True, parallel=True, morphism_type="nonlinear", direction="forward")
    
    # 创建形态图
    nodes, edges = create_simple_graph()
    
    # 设置形态图
    operator.set_graph(nodes, edges)
    
    # 创建输入数据
    input_data = {
        "input": [1.0, 2.0, 3.0]
    }
    
    # 应用算子
    result = operator.apply(input_data)
    
    # 打印结果
    print(f"输入数据: {input_data}")
    print(f"输出数据: {result}")
    
    # 再次应用算子（测试缓存）
    start_time = time.time()
    result = operator.apply(input_data)
    end_time = time.time()
    
    print(f"缓存命中执行时间: {(end_time - start_time) * 1000:.2f} ms")
    
    # 获取性能指标
    metrics = operator.get_performance_metrics()
    print(f"性能指标: {metrics}")

def example_adaptive_transform():
    """自适应变换示例"""
    print("\n=== 自适应变换示例 ===")
    
    # 创建动态形态算子
    operator = DynamicMorphismOperator(use_cache=True, parallel=True, morphism_type="adaptive", direction="forward")
    
    # 创建形态图
    nodes, edges = create_adaptive_graph()
    
    # 设置形态图
    operator.set_graph(nodes, edges)
    
    # 创建输入数据
    input_data = {
        "input": [0.1, 0.2, 0.3, 0.4, 0.5]
    }
    
    # 创建上下文
    context = {
        "learning_rate": 0.01,
        "adaptation_factor": 0.5,
        "environment": {
            "temperature": 0.8,
            "noise_level": 0.2
        }
    }
    
    # 应用算子
    result1 = operator.apply(input_data, context)
    
    # 修改上下文
    context["learning_rate"] = 0.05
    context["adaptation_factor"] = 0.8
    
    # 再次应用算子
    result2 = operator.apply(input_data, context)
    
    # 打印结果
    print(f"输入数据: {input_data}")
    print(f"上下文1: {context}")
    print(f"结果1: {result1}")
    print(f"上下文2: {context}")
    print(f"结果2: {result2}")
    
    # 获取性能指标
    metrics = operator.get_performance_metrics()
    print(f"性能指标: {metrics}")

def example_evolutionary_transform():
    """演化变换示例"""
    print("\n=== 演化变换示例 ===")
    
    # 创建动态形态算子
    operator = DynamicMorphismOperator(use_cache=True, parallel=True, morphism_type="evolutionary", direction="forward")
    
    # 创建形态图
    nodes, edges = create_adaptive_graph()
    
    # 设置形态图
    operator.set_graph(nodes, edges)
    
    # 创建输入数据
    input_data = {
        "input": [0.1, 0.2, 0.3, 0.4, 0.5]
    }
    
    # 创建上下文
    context = {
        "generation": 0,
        "mutation_rate": 0.05
    }
    
    # 应用算子
    results = []
    for generation in range(5):
        context["generation"] = generation
        result = operator.apply(input_data, context)
        results.append(result)
        print(f"第 {generation} 代结果: {result}")
    
    # 获取性能指标
    metrics = operator.get_performance_metrics()
    print(f"性能指标: {metrics}")

def example_batch_processing():
    """批处理示例"""
    print("\n=== 批处理示例 ===")
    
    # 创建动态形态算子
    operator = DynamicMorphismOperator(use_cache=True, parallel=True, morphism_type="adaptive", direction="forward")
    
    # 创建形态图
    nodes, edges = create_simple_graph()
    
    # 设置形态图
    operator.set_graph(nodes, edges)
    
    # 创建输入批次
    input_batch = [
        {"input": [1.0, 2.0, 3.0]},
        {"input": [4.0, 5.0, 6.0]},
        {"input": [7.0, 8.0, 9.0]},
        {"input": [0.1, 0.2, 0.3]},
        {"input": [0.4, 0.5, 0.6]}
    ]
    
    # 创建上下文
    context = {
        "learning_rate": 0.01,
        "adaptation_factor": 0.5
    }
    
    # 应用算子
    start_time = time.time()
    results = operator.apply_batch(input_batch, context)
    end_time = time.time()
    
    # 打印结果
    print(f"输入批次大小: {len(input_batch)}")
    print(f"输出批次大小: {len(results) - 1}")  # 减去统计信息
    print(f"执行时间: {(end_time - start_time) * 1000:.2f} ms")
    print(f"每个样本平均时间: {(end_time - start_time) * 1000 / len(input_batch):.2f} ms")
    
    # 获取性能指标
    metrics = operator.get_performance_metrics()
    print(f"性能指标: {metrics}")

def example_bidirectional_transform():
    """双向变换示例"""
    print("\n=== 双向变换示例 ===")
    
    # 创建动态形态算子
    operator = DynamicMorphismOperator(use_cache=True, parallel=True, morphism_type="linear", direction="bidirectional")
    
    # 创建形态图
    nodes, edges = create_simple_graph()
    
    # 设置形态图
    operator.set_graph(nodes, edges)
    
    # 创建输入数据
    input_data = {
        "input": [1.0, 2.0, 3.0]
    }
    
    # 应用算子
    result = operator.apply(input_data)
    
    # 打印结果
    print(f"输入数据: {input_data}")
    print(f"输出数据: {result}")
    
    # 获取性能指标
    metrics = operator.get_performance_metrics()
    print(f"性能指标: {metrics}")

def main():
    """主函数"""
    print("=== 动态形态算子示例 (PyO3 0.24+ 兼容版本) ===")
    
    # 运行示例
    example_linear_transform()
    example_nonlinear_transform()
    example_adaptive_transform()
    example_evolutionary_transform()
    example_batch_processing()
    example_bidirectional_transform()

if __name__ == "__main__":
    main()
