#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
智能日志算子示例

演示智能日志算子的使用方法，包括结构化日志、因果链管理、查询引擎和日志分析。
"""

import os
import sys
import time
import random
import datetime
import json
from typing import Dict, List, Any

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.operators.engineering_support.smart_logger import (
    SmartLoggerOperator,
    CausalChainOperator,
    QueryEngineOperator,
    LogAnalyzerOperator
)

def generate_sample_logs(count: int = 100) -> List[Dict[str, Any]]:
    """
    生成示例日志
    
    参数:
        count: 日志数量
        
    返回:
        日志列表
    """
    logs = []
    
    # 日志级别
    levels = ['debug', 'info', 'warning', 'error', 'critical']
    level_weights = [0.1, 0.6, 0.2, 0.08, 0.02]
    
    # 组件
    components = ['api', 'database', 'auth', 'cache', 'worker']
    
    # 操作
    operations = ['read', 'write', 'update', 'delete', 'query']
    
    # 状态
    statuses = ['success', 'failure', 'timeout', 'error']
    status_weights = [0.8, 0.1, 0.05, 0.05]
    
    # 用户
    users = [f'user_{i}' for i in range(1, 11)]
    
    # 生成日志
    start_time = time.time() - count * 10  # 从过去开始
    
    for i in range(count):
        # 时间戳
        timestamp = start_time + i * 10 + random.uniform(-5, 5)
        
        # 级别
        level = random.choices(levels, weights=level_weights)[0]
        
        # 组件
        component = random.choice(components)
        
        # 操作
        operation = random.choice(operations)
        
        # 状态
        status = random.choices(statuses, weights=status_weights)[0]
        
        # 用户
        user = random.choice(users)
        
        # 响应时间
        response_time = random.uniform(10, 500) if status != 'timeout' else random.uniform(1000, 5000)
        
        # 消息
        message = f"{operation.capitalize()} operation on {component}"
        
        # 创建日志
        log = {
            'timestamp': timestamp,
            'datetime': datetime.datetime.fromtimestamp(timestamp).isoformat(),
            'level': level,
            'component': component,
            'operation': operation,
            'status': status,
            'user': user,
            'response_time': response_time,
            'message': message,
            'request_id': f'req-{i}-{random.randint(1000, 9999)}'
        }
        
        # 添加错误信息
        if status in ['error', 'failure']:
            error_types = ['ValidationError', 'ConnectionError', 'TimeoutError', 'AuthenticationError']
            log['error'] = {
                'type': random.choice(error_types),
                'code': random.randint(400, 599),
                'details': f"Error occurred during {operation} on {component}"
            }
        
        logs.append(log)
    
    return logs

def demonstrate_smart_logger():
    """
    演示智能日志算子
    """
    print("=== 智能日志算子示例 ===")
    
    # 创建智能日志算子
    logger = SmartLoggerOperator(
        name="demo",
        log_level="INFO",
        log_format="console",
        capture_context=True,
        capture_callsite=True
    )
    
    # 记录日志
    print("\n1. 记录基本日志:")
    result = logger.apply({
        'action': 'log',
        'level': 'info',
        'message': 'Hello, Smart Logger!',
        'user': 'demo_user',
        'module': 'example'
    })
    print(f"日志结果: {result}")
    
    # 绑定上下文
    print("\n2. 绑定上下文:")
    result = logger.apply({
        'action': 'bind',
        'context': {
            'session_id': 'sess-123456',
            'user_id': 'user-789',
            'ip_address': '***********'
        }
    })
    print(f"绑定结果: {result}")
    
    # 使用绑定的上下文记录日志
    print("\n3. 使用绑定的上下文记录日志:")
    result = logger.apply({
        'action': 'log',
        'level': 'warning',
        'message': 'Something might be wrong',
        'component': 'auth'
    })
    print(f"日志结果: {result}")
    
    # 解绑上下文
    print("\n4. 解绑部分上下文:")
    result = logger.apply({
        'action': 'unbind',
        'keys': ['ip_address']
    })
    print(f"解绑结果: {result}")
    
    # 获取当前上下文
    print("\n5. 获取当前上下文:")
    result = logger.apply({
        'action': 'get_context'
    })
    print(f"当前上下文: {result}")
    
    # 清除上下文
    print("\n6. 清除上下文:")
    result = logger.apply({
        'action': 'clear'
    })
    print(f"清除结果: {result}")
    
    print("\n智能日志算子元数据:")
    print(json.dumps(logger.get_metadata(), indent=2))

def demonstrate_causal_chain():
    """
    演示因果链算子
    """
    print("\n=== 因果链算子示例 ===")
    
    # 创建因果链算子
    causal_chain = CausalChainOperator(
        max_chain_length=1000,
        detect_cycles=True,
        store_events=True
    )
    
    # 添加事件
    print("\n1. 添加事件:")
    events = []
    for i in range(5):
        event_id = f"event-{i}"
        event_data = {
            'timestamp': time.time(),
            'type': f'type-{i % 3}',
            'description': f'Event {i} description'
        }
        
        result = causal_chain.apply({
            'action': 'add_event',
            'event_id': event_id,
            'event_data': event_data
        })
        events.append(event_id)
        print(f"添加事件 {event_id} 结果: {result['success']}")
    
    # 添加因果链接
    print("\n2. 添加因果链接:")
    # 创建链: 0 -> 1 -> 2 -> 4 和 0 -> 3 -> 4
    links = [
        (events[0], events[1]),
        (events[1], events[2]),
        (events[2], events[4]),
        (events[0], events[3]),
        (events[3], events[4])
    ]
    
    for cause_id, effect_id in links:
        result = causal_chain.apply({
            'action': 'add_causal_link',
            'cause_id': cause_id,
            'effect_id': effect_id
        })
        print(f"添加链接 {cause_id} -> {effect_id} 结果: {result['success']}")
    
    # 获取事件的原因
    print("\n3. 获取事件的原因:")
    result = causal_chain.apply({
        'action': 'get_causes',
        'event_id': events[4],
        'max_depth': 2
    })
    print(f"事件 {events[4]} 的原因: {result['causes']}")
    
    # 获取事件的结果
    print("\n4. 获取事件的结果:")
    result = causal_chain.apply({
        'action': 'get_effects',
        'event_id': events[0],
        'max_depth': 2
    })
    print(f"事件 {events[0]} 的结果: {result['effects']}")
    
    # 获取因果链
    print("\n5. 获取因果链:")
    result = causal_chain.apply({
        'action': 'get_chain',
        'start_id': events[0],
        'end_id': events[4]
    })
    print(f"从 {events[0]} 到 {events[4]} 的因果链: {result['path']}")
    
    # 检测循环
    print("\n6. 检测循环:")
    # 尝试添加一个会导致循环的链接
    result = causal_chain.apply({
        'action': 'add_causal_link',
        'cause_id': events[4],
        'effect_id': events[0]
    })
    print(f"添加循环链接结果: {result}")
    
    # 可视化因果图
    print("\n7. 可视化因果图:")
    result = causal_chain.apply({
        'action': 'visualize',
        'format': 'json'
    })
    print(f"因果图节点: {result['nodes']}")
    print(f"因果图边: {result['edges']}")
    
    print("\n因果链算子元数据:")
    print(json.dumps(causal_chain.get_metadata(), indent=2))

def demonstrate_query_engine():
    """
    演示查询引擎算子
    """
    print("\n=== 查询引擎算子示例 ===")
    
    # 创建查询引擎算子
    query_engine = QueryEngineOperator(
        max_results=100,
        cache_queries=True
    )
    
    # 生成示例日志
    logs = generate_sample_logs(100)
    
    # 执行查询
    print("\n1. 执行简单查询:")
    result = query_engine.apply({
        'action': 'query',
        'query': 'level:error',
        'logs': logs,
        'limit': 5
    })
    print(f"查询结果数量: {result['count']}")
    if result['count'] > 0:
        print(f"第一条结果: {json.dumps(result['results'][0], indent=2)}")
    
    # 执行复杂查询
    print("\n2. 执行复杂查询:")
    result = query_engine.apply({
        'action': 'query',
        'query': 'level:error component:database response_time:>1000',
        'logs': logs,
        'limit': 5
    })
    print(f"查询结果数量: {result['count']}")
    if result['count'] > 0:
        print(f"第一条结果: {json.dumps(result['results'][0], indent=2)}")
    
    # 使用过滤器
    print("\n3. 使用过滤器:")
    result = query_engine.apply({
        'action': 'filter',
        'logs': logs,
        'filters': {
            'status': 'failure',
            'response_time': {
                'op': '>',
                'value': '1000'
            }
        },
        'limit': 5
    })
    print(f"过滤结果数量: {result['count']}")
    if result['count'] > 0:
        print(f"第一条结果: {json.dumps(result['results'][0], indent=2)}")
    
    # 分析日志
    print("\n4. 分析日志:")
    result = query_engine.apply({
        'action': 'analyze',
        'logs': logs,
        'group_by': 'component',
        'sort_by': 'count'
    })
    print(f"分析结果: {json.dumps(result['groups'], indent=2)}")
    
    # 清除缓存
    print("\n5. 清除缓存:")
    result = query_engine.apply({
        'action': 'clear_cache'
    })
    print(f"清除缓存结果: {result}")
    
    print("\n查询引擎算子元数据:")
    print(json.dumps(query_engine.get_metadata(), indent=2))

def demonstrate_log_analyzer():
    """
    演示日志分析算子
    """
    print("\n=== 日志分析算子示例 ===")
    
    # 创建日志分析算子
    log_analyzer = LogAnalyzerOperator(
        window_size=100,
        anomaly_threshold=2.0,
        min_pattern_support=0.05
    )
    
    # 生成示例日志
    logs = generate_sample_logs(100)
    
    # 添加一些异常日志
    for i in range(5):
        logs.append({
            'timestamp': time.time(),
            'datetime': datetime.datetime.now().isoformat(),
            'level': 'critical',
            'component': 'database',
            'operation': 'query',
            'status': 'failure',
            'user': 'user_1',
            'response_time': random.uniform(5000, 10000),
            'message': 'Database connection lost',
            'request_id': f'req-anomaly-{i}',
            'error': {
                'type': 'ConnectionError',
                'code': 500,
                'details': 'Database connection lost due to network issue'
            }
        })
    
    # 分析日志
    print("\n1. 分析日志:")
    result = log_analyzer.apply({
        'action': 'analyze',
        'logs': logs,
        'fields': ['level', 'component', 'operation', 'status', 'response_time'],
        'time_field': 'timestamp',
        'time_unit': 'hour'
    })
    print(f"分析结果: {json.dumps(result['stats'], indent=2)}")
    
    # 检测异常
    print("\n2. 检测异常:")
    result = log_analyzer.apply({
        'action': 'detect_anomalies',
        'logs': logs,
        'fields': ['response_time']
    })
    print(f"检测到 {result['anomaly_count']} 个异常")
    if result['anomaly_count'] > 0:
        print(f"第一个异常: {json.dumps(result['anomalies'][0], indent=2)}")
    
    # 查找模式
    print("\n3. 查找模式:")
    result = log_analyzer.apply({
        'action': 'find_patterns',
        'logs': logs,
        'fields': ['level', 'component', 'operation', 'status']
    })
    print(f"发现 {result['pattern_count']} 个模式")
    if result['pattern_count'] > 0:
        print(f"第一个模式: {json.dumps(result['patterns'][0], indent=2)}")
    
    # 获取统计数据
    print("\n4. 获取统计数据:")
    result = log_analyzer.apply({
        'action': 'get_stats'
    })
    print(f"级别统计: {json.dumps(result['stats']['level_counts'], indent=2)}")
    print(f"数值统计: {json.dumps(result['stats']['numeric_stats'], indent=2)}")
    
    # 重置统计数据
    print("\n5. 重置统计数据:")
    result = log_analyzer.apply({
        'action': 'reset_stats'
    })
    print(f"重置结果: {result}")
    
    print("\n日志分析算子元数据:")
    print(json.dumps(log_analyzer.get_metadata(), indent=2))

def main():
    """
    主函数
    """
    print("智能日志算子示例程序")
    print("=" * 50)
    
    # 演示智能日志算子
    demonstrate_smart_logger()
    
    # 演示因果链算子
    demonstrate_causal_chain()
    
    # 演示查询引擎算子
    demonstrate_query_engine()
    
    # 演示日志分析算子
    demonstrate_log_analyzer()
    
    print("\n示例程序结束")

if __name__ == "__main__":
    main()
