"""
NumPy操作示例

本示例展示了如何使用NumPy操作。
"""

import os
import sys
import logging
import numpy as np
import time
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入NumPy操作
try:
    from src.rust_bindings import (
        RUST_AVAILABLE,
        ArrayOps,
        MathOps,
        SimdOps,
        ParallelOps,
        TensorOps,
        array_add,
        array_multiply,
        array_matmul,
        array_transpose,
        array_reshape,
    )
    logger.info(f"成功导入NumPy操作，Rust可用: {RUST_AVAILABLE}")
except ImportError as e:
    logger.error(f"导入NumPy操作失败: {e}")
    sys.exit(1)

def test_array_ops():
    """测试数组操作"""
    logger.info("开始测试数组操作...")
    
    # 创建数组操作
    array_ops = ArrayOps("test_array_ops")
    
    # 创建测试数组
    a = np.array([[1, 2], [3, 4]])
    b = np.array([[5, 6], [7, 8]])
    c = np.array([[9, 10], [11, 12]])
    
    # 测试连接
    result = array_ops.concatenate([a, b, c], axis=0)
    logger.info(f"连接结果:\n{result}")
    
    # 测试分割
    result = array_ops.split(result, 3, axis=0)
    logger.info(f"分割结果:\n{result}")
    
    # 测试堆叠
    result = array_ops.stack([a, b, c], axis=0)
    logger.info(f"堆叠结果:\n{result}")
    
    logger.info("数组操作测试完成")

def test_math_ops():
    """测试数学操作"""
    logger.info("开始测试数学操作...")
    
    # 创建数学操作
    math_ops = MathOps("test_math_ops")
    
    # 创建测试数组
    a = np.array([[1, 2, 3], [4, 5, 6], [7, 8, 9]])
    
    # 测试求和
    result = math_ops.sum(a)
    logger.info(f"求和结果: {result}")
    
    result = math_ops.sum(a, axis=0)
    logger.info(f"沿轴0求和结果: {result}")
    
    result = math_ops.sum(a, axis=1)
    logger.info(f"沿轴1求和结果: {result}")
    
    # 测试平均值
    result = math_ops.mean(a)
    logger.info(f"平均值结果: {result}")
    
    result = math_ops.mean(a, axis=0)
    logger.info(f"沿轴0平均值结果: {result}")
    
    result = math_ops.mean(a, axis=1)
    logger.info(f"沿轴1平均值结果: {result}")
    
    # 测试标准差
    result = math_ops.std(a)
    logger.info(f"标准差结果: {result}")
    
    result = math_ops.std(a, axis=0)
    logger.info(f"沿轴0标准差结果: {result}")
    
    result = math_ops.std(a, axis=1)
    logger.info(f"沿轴1标准差结果: {result}")
    
    # 测试最小值
    result = math_ops.min(a)
    logger.info(f"最小值结果: {result}")
    
    result = math_ops.min(a, axis=0)
    logger.info(f"沿轴0最小值结果: {result}")
    
    result = math_ops.min(a, axis=1)
    logger.info(f"沿轴1最小值结果: {result}")
    
    # 测试最大值
    result = math_ops.max(a)
    logger.info(f"最大值结果: {result}")
    
    result = math_ops.max(a, axis=0)
    logger.info(f"沿轴0最大值结果: {result}")
    
    result = math_ops.max(a, axis=1)
    logger.info(f"沿轴1最大值结果: {result}")
    
    logger.info("数学操作测试完成")

def test_simd_ops():
    """测试SIMD操作"""
    logger.info("开始测试SIMD操作...")
    
    # 创建SIMD操作
    simd_ops = SimdOps("test_simd_ops")
    
    # 检查SIMD支持
    is_supported = simd_ops.is_simd_supported()
    logger.info(f"SIMD支持: {is_supported}")
    
    # 创建测试数组
    a = np.random.rand(1000000)
    b = np.random.rand(1000000)
    
    # 测试普通加法
    start_time = time.time()
    result1 = a + b
    end_time = time.time()
    logger.info(f"普通加法耗时: {end_time - start_time:.6f}秒")
    
    # 测试SIMD加法
    start_time = time.time()
    result2 = simd_ops.simd_add(a, b)
    end_time = time.time()
    logger.info(f"SIMD加法耗时: {end_time - start_time:.6f}秒")
    
    # 检查结果是否相同
    logger.info(f"结果是否相同: {np.allclose(result1, result2)}")
    
    # 测试普通乘法
    start_time = time.time()
    result1 = a * b
    end_time = time.time()
    logger.info(f"普通乘法耗时: {end_time - start_time:.6f}秒")
    
    # 测试SIMD乘法
    start_time = time.time()
    result2 = simd_ops.simd_multiply(a, b)
    end_time = time.time()
    logger.info(f"SIMD乘法耗时: {end_time - start_time:.6f}秒")
    
    # 检查结果是否相同
    logger.info(f"结果是否相同: {np.allclose(result1, result2)}")
    
    logger.info("SIMD操作测试完成")

def test_parallel_ops():
    """测试并行操作"""
    logger.info("开始测试并行操作...")
    
    # 创建并行操作
    parallel_ops = ParallelOps("test_parallel_ops")
    
    # 获取可用线程数
    num_threads = parallel_ops.get_num_threads()
    logger.info(f"可用线程数: {num_threads}")
    
    # 设置线程数
    parallel_ops.set_num_threads(4)
    logger.info(f"设置线程数为4")
    
    # 创建测试数组
    a = np.random.rand(1000000)
    
    # 测试普通映射
    start_time = time.time()
    result1 = np.sin(a)
    end_time = time.time()
    logger.info(f"普通映射耗时: {end_time - start_time:.6f}秒")
    
    # 测试并行映射
    start_time = time.time()
    result2 = parallel_ops.par_map(a, np.sin)
    end_time = time.time()
    logger.info(f"并行映射耗时: {end_time - start_time:.6f}秒")
    
    # 检查结果是否相同
    logger.info(f"结果是否相同: {np.allclose(result1, result2)}")
    
    # 测试带索引的并行映射
    def indexed_func(idx, val):
        return val * idx[0]
    
    start_time = time.time()
    result = parallel_ops.par_map_indexed(a.reshape(1000, 1000), indexed_func)
    end_time = time.time()
    logger.info(f"带索引的并行映射耗时: {end_time - start_time:.6f}秒")
    
    # 测试并行过滤
    start_time = time.time()
    result = parallel_ops.par_filter(a, lambda x: x > 0.5)
    end_time = time.time()
    logger.info(f"并行过滤耗时: {end_time - start_time:.6f}秒")
    logger.info(f"过滤后的元素数量: {len(result)}")
    
    # 测试并行排序
    start_time = time.time()
    result = parallel_ops.par_sort(a)
    end_time = time.time()
    logger.info(f"并行排序耗时: {end_time - start_time:.6f}秒")
    
    logger.info("并行操作测试完成")

def test_tensor_ops():
    """测试张量操作"""
    logger.info("开始测试张量操作...")
    
    # 创建张量操作
    tensor_ops = TensorOps("test_tensor_ops")
    
    # 创建测试数组
    a = np.random.rand(3, 4, 5)
    b = np.random.rand(5, 6)
    
    # 测试张量收缩
    result = tensor_ops.tensordot(a, b, axes=1)
    logger.info(f"张量收缩结果形状: {result.shape}")
    
    # 测试外积
    a = np.random.rand(3)
    b = np.random.rand(4)
    result = tensor_ops.outer(a, b)
    logger.info(f"外积结果形状: {result.shape}")
    logger.info(f"外积结果:\n{result}")
    
    # 测试内积
    a = np.random.rand(3, 5)
    b = np.random.rand(4, 5)
    result = tensor_ops.inner(a, b)
    logger.info(f"内积结果形状: {result.shape}")
    logger.info(f"内积结果:\n{result}")
    
    logger.info("张量操作测试完成")

def test_module_functions():
    """测试模块函数"""
    logger.info("开始测试模块函数...")
    
    # 创建测试数组
    a = np.array([[1, 2], [3, 4]])
    b = np.array([[5, 6], [7, 8]])
    
    # 测试数组加法
    result = array_add(a, b)
    logger.info(f"数组加法结果:\n{result}")
    
    # 测试数组乘法
    result = array_multiply(a, b)
    logger.info(f"数组乘法结果:\n{result}")
    
    # 测试矩阵乘法
    result = array_matmul(a, b)
    logger.info(f"矩阵乘法结果:\n{result}")
    
    # 测试数组转置
    result = array_transpose(a)
    logger.info(f"数组转置结果:\n{result}")
    
    # 测试数组重塑
    result = array_reshape(a, (1, 4))
    logger.info(f"数组重塑结果:\n{result}")
    
    logger.info("模块函数测试完成")

def main():
    """主函数"""
    logger.info(f"开始NumPy操作示例，Rust可用: {RUST_AVAILABLE}")
    
    # 测试数组操作
    test_array_ops()
    
    # 测试数学操作
    test_math_ops()
    
    # 测试SIMD操作
    test_simd_ops()
    
    # 测试并行操作
    test_parallel_ops()
    
    # 测试张量操作
    test_tensor_ops()
    
    # 测试模块函数
    test_module_functions()
    
    logger.info("NumPy操作示例结束")

if __name__ == "__main__":
    main()
