"""
元认知系统示例

本示例展示了如何使用元认知系统。
"""

import time
import logging
import numpy as np
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable

from src.core.metacognition import (
    CognitiveLevel, MetacognitiveProcess,
    NewCognitiveState as CognitiveState, 
    NewMetacognitiveState as MetacognitiveState,
    CognitiveProcess, NewMetacognitiveProcess as MetacognitiveProcess,
    MetacognitionFactory, MetacognitionManager,
    MetacognitionMonitor, MetacognitionEvaluator, MetacognitionRegulator
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    logger.info("元认知系统示例")
    
    # 创建元认知系统管理器
    logger.info("创建元认知系统管理器...")
    manager = MetacognitionManager(manager_id="metacognition_manager")
    
    # 创建认知状态
    logger.info("\n创建认知状态...")
    perception_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.PERCEPTION,
        data={"image": np.random.rand(28, 28)},
        metadata={"description": "感知状态"}
    )
    logger.info(f"感知状态: {perception_state}")
    
    attention_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.ATTENTION,
        data={"focus": [10, 10, 5, 5]},
        metadata={"description": "注意力状态"}
    )
    logger.info(f"注意力状态: {attention_state}")
    
    memory_state = manager.create_cognitive_state(
        cognitive_level=CognitiveLevel.MEMORY,
        data={"memories": ["apple", "banana", "orange"]},
        metadata={"description": "记忆状态"}
    )
    logger.info(f"记忆状态: {memory_state}")
    
    # 创建元认知状态
    logger.info("\n创建元认知状态...")
    monitoring_state = manager.create_metacognitive_state(
        metacognitive_process=MetacognitiveProcess.MONITORING,
        cognitive_states={
            perception_state.state_id: perception_state,
            attention_state.state_id: attention_state,
            memory_state.state_id: memory_state
        },
        data={"timestamp": time.time()},
        metadata={"description": "监控状态"}
    )
    logger.info(f"监控状态: {monitoring_state}")
    
    # 创建认知过程
    logger.info("\n创建认知过程...")
    
    # 定义感知过程函数
    def perception_process(input_data: Any) -> Any:
        """感知过程"""
        # 模拟图像处理
        if isinstance(input_data, dict) and "image" in input_data:
            image = input_data["image"]
            # 简单的图像处理：计算平均值和标准差
            mean = np.mean(image)
            std = np.std(image)
            return {"mean": mean, "std": std, "processed_image": image}
        return None
    
    # 创建感知过程
    perception_process_obj = manager.create_cognitive_process(
        cognitive_level=CognitiveLevel.PERCEPTION,
        process_function=perception_process,
        parameters={"threshold": 0.5},
        metadata={"description": "感知过程"}
    )
    logger.info(f"感知过程: {perception_process_obj}")
    
    # 定义注意力过程函数
    def attention_process(input_data: Any) -> Any:
        """注意力过程"""
        # 模拟注意力机制
        if isinstance(input_data, dict) and "processed_image" in input_data:
            image = input_data["processed_image"]
            # 简单的注意力机制：找到图像中的最大值位置
            max_pos = np.unravel_index(np.argmax(image), image.shape)
            return {"focus": [max_pos[0], max_pos[1], 3, 3]}
        return None
    
    # 创建注意力过程
    attention_process_obj = manager.create_cognitive_process(
        cognitive_level=CognitiveLevel.ATTENTION,
        process_function=attention_process,
        parameters={"focus_size": 3},
        metadata={"description": "注意力过程"}
    )
    logger.info(f"注意力过程: {attention_process_obj}")
    
    # 创建元认知过程
    logger.info("\n创建元认知过程...")
    
    # 定义监控过程函数
    def monitoring_process(input_data: Any) -> Any:
        """监控过程"""
        # 模拟监控过程
        if isinstance(input_data, dict) and "cognitive_states" in input_data:
            cognitive_states = input_data["cognitive_states"]
            # 简单的监控：统计各种认知状态的数量
            state_counts = {}
            for state_id, state in cognitive_states.items():
                level = state.cognitive_level.value
                state_counts[level] = state_counts.get(level, 0) + 1
            return {"state_counts": state_counts, "timestamp": time.time()}
        return None
    
    # 创建监控过程
    monitoring_process_obj = manager.create_metacognitive_process(
        metacognitive_process_type=MetacognitiveProcess.MONITORING,
        process_function=monitoring_process,
        parameters={"interval": 1.0},
        metadata={"description": "监控过程"}
    )
    logger.info(f"监控过程: {monitoring_process_obj}")
    
    # 执行认知过程
    logger.info("\n执行认知过程...")
    
    # 执行感知过程
    perception_result = manager.execute_cognitive_process(
        process_id=perception_process_obj.process_id,
        input_data={"image": np.random.rand(28, 28)}
    )
    logger.info(f"感知结果: {perception_result}")
    
    # 执行注意力过程
    attention_result = manager.execute_cognitive_process(
        process_id=attention_process_obj.process_id,
        input_data=perception_result
    )
    logger.info(f"注意力结果: {attention_result}")
    
    # 执行元认知过程
    logger.info("\n执行元认知过程...")
    
    # 执行监控过程
    monitoring_result = manager.execute_metacognitive_process(
        process_id=monitoring_process_obj.process_id,
        input_data={"cognitive_states": {
            perception_state.state_id: perception_state,
            attention_state.state_id: attention_state,
            memory_state.state_id: memory_state
        }}
    )
    logger.info(f"监控结果: {monitoring_result}")
    
    # 创建元认知系统监控器
    logger.info("\n创建元认知系统监控器...")
    monitor = MetacognitionMonitor(
        monitor_id="metacognition_monitor",
        manager=manager
    )
    
    # 定义监控回调函数
    def monitor_callback(monitoring_state: MetacognitiveState) -> None:
        """监控回调函数"""
        logger.info(f"监控回调: {len(monitoring_state.cognitive_states)} 个认知状态")
    
    # 添加监控回调函数
    monitor.add_monitor_callback(
        callback_id="monitor_callback",
        callback=monitor_callback
    )
    
    # 设置监控间隔
    monitor.set_monitor_interval(2.0)
    
    # 启动监控器
    logger.info("启动监控器...")
    monitor.start()
    
    # 创建元认知系统评估器
    logger.info("\n创建元认知系统评估器...")
    evaluator = MetacognitionEvaluator(
        evaluator_id="metacognition_evaluator",
        manager=manager
    )
    
    # 定义评估规则
    def evaluation_rule(cognitive_states: Dict[str, CognitiveState]) -> Dict[str, Any]:
        """评估规则"""
        # 简单的评估：检查是否存在所有必要的认知状态
        required_levels = [CognitiveLevel.PERCEPTION, CognitiveLevel.ATTENTION]
        existing_levels = set()
        
        for state in cognitive_states.values():
            existing_levels.add(state.cognitive_level)
        
        missing_levels = []
        for level in required_levels:
            if level not in existing_levels:
                missing_levels.append(level.value)
        
        return {
            "complete": len(missing_levels) == 0,
            "missing_levels": missing_levels
        }
    
    # 添加评估规则
    evaluator.add_evaluation_rule(
        rule_id="completeness_rule",
        rule_function=evaluation_rule,
        metadata={"description": "完整性规则"}
    )
    
    # 评估认知状态
    logger.info("评估认知状态...")
    evaluation_state = evaluator.evaluate({
        perception_state.state_id: perception_state,
        attention_state.state_id: attention_state,
        memory_state.state_id: memory_state
    })
    logger.info(f"评估状态: {evaluation_state}")
    logger.info(f"评估结果: {evaluation_state.data}")
    
    # 创建元认知系统调节器
    logger.info("\n创建元认知系统调节器...")
    regulator = MetacognitionRegulator(
        regulator_id="metacognition_regulator",
        manager=manager
    )
    
    # 定义调节规则
    def regulation_rule(evaluation_state: MetacognitiveState) -> Dict[str, Any]:
        """调节规则"""
        # 简单的调节：根据评估结果调整参数
        if isinstance(evaluation_state.data, dict) and "evaluation_results" in evaluation_state.data:
            results = evaluation_state.data["evaluation_results"]
            if "completeness_rule" in results:
                completeness = results["completeness_rule"]
                if not completeness.get("complete", True):
                    # 如果缺少必要的认知状态，则创建它们
                    missing_levels = completeness.get("missing_levels", [])
                    created_states = []
                    
                    for level in missing_levels:
                        try:
                            level_enum = CognitiveLevel(level)
                            state = manager.create_cognitive_state(
                                cognitive_level=level_enum,
                                data={"auto_created": True},
                                metadata={"description": f"自动创建的{level}状态"}
                            )
                            created_states.append(state.state_id)
                        except ValueError:
                            pass
                    
                    return {
                        "action": "create_states",
                        "created_states": created_states
                    }
            
            return {"action": "none"}
        
        return {"action": "none"}
    
    # 添加调节规则
    regulator.add_regulation_rule(
        rule_id="completeness_regulation",
        rule_function=regulation_rule,
        metadata={"description": "完整性调节规则"}
    )
    
    # 调节认知过程
    logger.info("调节认知过程...")
    regulation_state = regulator.regulate(evaluation_state)
    logger.info(f"调节状态: {regulation_state}")
    logger.info(f"调节结果: {regulation_state.data}")
    
    # 等待一段时间，让监控器运行
    logger.info("等待监控器运行...")
    time.sleep(5)
    
    # 停止监控器
    logger.info("停止监控器...")
    monitor.stop()
    
    # 获取统计信息
    logger.info("\n获取统计信息...")
    manager_stats = manager.get_stats()
    monitor_stats = monitor.get_stats()
    evaluator_stats = evaluator.get_stats()
    regulator_stats = regulator.get_stats()
    
    logger.info(f"管理器统计信息: {manager_stats}")
    logger.info(f"监控器统计信息: {monitor_stats}")
    logger.info(f"评估器统计信息: {evaluator_stats}")
    logger.info(f"调节器统计信息: {regulator_stats}")
    
    logger.info("示例完成")

if __name__ == "__main__":
    main()
