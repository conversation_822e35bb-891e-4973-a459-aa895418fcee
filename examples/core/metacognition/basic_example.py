"""
元认知系统基本示例

本示例展示了如何使用元认知系统核心模块创建和使用认知状态和元认知状态。
"""

import numpy as np
import matplotlib.pyplot as plt

from src.core.metacognition import (
    CognitiveState, MetaCognitiveState, MetaCognitiveMapping,
    MetaCognitiveStateManager, MetaCognitiveLearning, MetaCognitiveControl,
    SupervisedStrategy
)

def main():
    """主函数"""
    print("元认知系统基本示例")
    print("=" * 50)
    
    # 创建元认知状态管理器
    manager = MetaCognitiveStateManager()
    
    # 创建特征映射
    mapping = MetaCognitiveMapping("feature")
    
    # 更新管理器的映射
    manager.mapping = mapping
    
    # 创建一系列认知状态
    print("\n创建认知状态...")
    cognitive_states = []
    for i in range(10):
        # 创建认知状态
        state = manager.create_cognitive_state(
            state_type="perception",
            parameters={
                "input_value": i,
                "processed_value": i * 2,
                "confidence": 0.5 + i * 0.05,
                "noise": np.random.normal(0, 0.1)
            },
            metadata={
                "source": "sensor",
                "timestamp": i * 100
            }
        )
        cognitive_states.append(state)
        print(f"  创建认知状态: {state.id}, 类型: {state.state_type}, 输入值: {state.get_parameter('input_value')}")
    
    # 映射认知状态为元认知状态
    print("\n映射认知状态为元认知状态...")
    meta_states = []
    for state in cognitive_states:
        meta_state = manager.map_to_metacognitive(state)
        meta_states.append(meta_state)
        print(f"  映射认知状态 {state.id} 为元认知状态 {meta_state.id}, 置信度: {meta_state.confidence:.2f}, 意识水平: {meta_state.awareness_level:.2f}")
    
    # 创建元认知学习
    print("\n创建元认知学习...")
    learning = MetaCognitiveLearning(
        learning_type="supervised",
        strategy=SupervisedStrategy(),
        parameters={
            "model_type": "random_forest",
            "n_estimators": 100,
            "test_size": 0.3,
            "random_state": 42
        }
    )
    
    # 学习元认知状态
    print("学习元认知状态...")
    learning_result = learning.learn(cognitive_states, meta_states)
    print(f"  学习结果: {learning_result}")
    
    # 创建新的认知状态
    print("\n创建新的认知状态...")
    new_cognitive_states = []
    for i in range(10, 15):
        state = manager.create_cognitive_state(
            state_type="perception",
            parameters={
                "input_value": i,
                "processed_value": i * 2,
                "confidence": 0.5 + i * 0.05,
                "noise": np.random.normal(0, 0.1)
            },
            metadata={
                "source": "sensor",
                "timestamp": i * 100
            }
        )
        new_cognitive_states.append(state)
        print(f"  创建认知状态: {state.id}, 类型: {state.state_type}, 输入值: {state.get_parameter('input_value')}")
    
    # 预测元认知状态
    print("\n预测元认知状态...")
    predicted_meta_states = []
    for state in new_cognitive_states:
        predicted_meta_state = learning.predict(state)
        predicted_meta_states.append(predicted_meta_state)
        print(f"  预测认知状态 {state.id} 的元认知状态: 置信度: {predicted_meta_state.confidence:.2f}, 意识水平: {predicted_meta_state.awareness_level:.2f}")
    
    # 创建元认知控制
    print("\n创建元认知控制...")
    control = MetaCognitiveControl(
        control_type="adaptive",
        parameters={
            "adaptation_rate": 0.2,
            "confidence_weight": 1.5,
            "awareness_weight": 1.0
        }
    )
    
    # 应用元认知控制
    print("应用元认知控制...")
    controlled_states = []
    for i, (cognitive_state, meta_state) in enumerate(zip(new_cognitive_states, predicted_meta_states)):
        controlled_state, control_info = control.control(cognitive_state, meta_state)
        controlled_states.append(controlled_state)
        print(f"  控制认知状态 {cognitive_state.id}: 适应因子: {control_info['adaptation_factor']:.2f}, 动作数量: {control_info['action_count']}")
    
    # 绘制结果
    print("\n绘制结果...")
    
    # 提取数据
    input_values = [state.get_parameter("input_value") for state in cognitive_states + new_cognitive_states]
    confidence_values = [state.confidence for state in meta_states + predicted_meta_states]
    awareness_values = [state.awareness_level for state in meta_states + predicted_meta_states]
    
    # 绘制图表
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 2, 1)
    plt.plot(input_values[:10], confidence_values[:10], 'bo-', label="训练数据")
    plt.plot(input_values[10:], confidence_values[10:], 'ro-', label="预测数据")
    plt.xlabel("输入值")
    plt.ylabel("置信度")
    plt.title("输入值与置信度的关系")
    plt.legend()
    plt.grid(True)
    
    plt.subplot(2, 2, 2)
    plt.plot(input_values[:10], awareness_values[:10], 'bo-', label="训练数据")
    plt.plot(input_values[10:], awareness_values[10:], 'ro-', label="预测数据")
    plt.xlabel("输入值")
    plt.ylabel("意识水平")
    plt.title("输入值与意识水平的关系")
    plt.legend()
    plt.grid(True)
    
    plt.subplot(2, 2, 3)
    original_values = [state.get_parameter("processed_value") for state in new_cognitive_states]
    controlled_values = [state.get_parameter("processed_value") for state in controlled_states]
    x = np.arange(len(original_values))
    width = 0.35
    plt.bar(x - width/2, original_values, width, label="原始值")
    plt.bar(x + width/2, controlled_values, width, label="控制后的值")
    plt.xlabel("样本索引")
    plt.ylabel("处理值")
    plt.title("元认知控制前后的处理值比较")
    plt.xticks(x)
    plt.legend()
    plt.grid(True)
    
    plt.subplot(2, 2, 4)
    adaptation_factors = [control.control(state, meta_state)[1]["adaptation_factor"] for state, meta_state in zip(new_cognitive_states, predicted_meta_states)]
    plt.plot(input_values[10:], adaptation_factors, 'go-')
    plt.xlabel("输入值")
    plt.ylabel("适应因子")
    plt.title("输入值与适应因子的关系")
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig("metacognition_example.png")
    plt.show()
    
    print("示例完成，结果已保存到 metacognition_example.png")
    
    # 获取管理器统计信息
    stats = manager.get_stats()
    print("\n管理器统计信息:")
    print(f"  认知状态数量: {stats['cognitive_state_count']}")
    print(f"  元认知状态数量: {stats['metacognitive_state_count']}")
    print(f"  映射类型: {stats['mapping_type']}")


if __name__ == "__main__":
    main()
