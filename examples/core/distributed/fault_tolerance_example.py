"""
分布式容错示例

本示例展示了如何使用分布式系统核心模块进行容错。
"""

import logging
import time
import threading
import uuid
from typing import Dict, Any

from src.core.distributed import (
    NodeLevel, NodeRole, NodeState,
    BaseUnitNode, FractalDimensionNode, HighDimensionCoreNode,
    NodeFactory, FractalNetwork, NetworkFactory,
    FaultType, FaultSeverity, FaultStatus,
    Fault, FaultFactory,
    FaultDetector, FaultDetectionError,
    HeartbeatDetector, TimeoutDetector,
    FaultDetectorFactory,
    RecoveryStrategy, RecoveryError,
    NodeFailureRecoveryStrategy, NetworkFailureRecoveryStrategy, TimeoutRecoveryStrategy,
    RecoveryStrategyFactory,
    FaultManager
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NodeFaultHandler:
    """
    节点故障处理器类
    
    节点故障处理器负责处理节点故障。
    """
    
    def __init__(self, node_id: str, node_name: str):
        """
        初始化节点故障处理器
        
        参数:
            node_id (str): 节点ID
            node_name (str): 节点名称
        """
        # 设置节点ID和名称
        self.node_id = node_id
        self.node_name = node_name
        
        # 创建容错管理器
        self.fault_manager = FaultManager(
            manager_id=f"manager_{node_id}",
            node_id=node_id,
            metadata={"name": node_name}
        )
        
        # 创建心跳检测器
        self.heartbeat_detector = self.fault_manager.create_heartbeat_detector(
            detector_id=f"heartbeat_detector_{node_id}",
            heartbeat_interval=1.0,
            heartbeat_timeout=3.0,
            metadata={"name": f"{node_name} 心跳检测器"}
        )
        
        # 创建超时检测器
        self.timeout_detector = self.fault_manager.create_timeout_detector(
            detector_id=f"timeout_detector_{node_id}",
            default_timeout=5.0,
            metadata={"name": f"{node_name} 超时检测器"}
        )
        
        # 创建节点故障恢复策略
        self.node_failure_strategy = self.fault_manager.create_node_failure_recovery_strategy(
            strategy_id=f"node_failure_strategy_{node_id}",
            node_restart_callback=self.restart_node,
            metadata={"name": f"{node_name} 节点故障恢复策略"}
        )
        
        # 创建网络故障恢复策略
        self.network_failure_strategy = self.fault_manager.create_network_failure_recovery_strategy(
            strategy_id=f"network_failure_strategy_{node_id}",
            network_reconnect_callback=self.reconnect_network,
            metadata={"name": f"{node_name} 网络故障恢复策略"}
        )
        
        # 创建超时故障恢复策略
        self.timeout_strategy = self.fault_manager.create_timeout_recovery_strategy(
            strategy_id=f"timeout_strategy_{node_id}",
            retry_count=3,
            retry_interval=1.0,
            operation_retry_callback=self.retry_operation,
            metadata={"name": f"{node_name} 超时故障恢复策略"}
        )
        
        # 设置节点状态
        self.node_status = {}
        
        logger.info(f"创建节点故障处理器: {node_id}, {node_name}")
    
    def start(self) -> bool:
        """
        启动节点故障处理器
        
        返回:
            bool: 如果启动成功则返回True，否则返回False
        """
        # 启动故障检测器
        self.heartbeat_detector.start()
        self.timeout_detector.start()
        
        logger.info(f"启动节点故障处理器: {self.node_name}")
        
        return True
    
    def stop(self) -> bool:
        """
        停止节点故障处理器
        
        返回:
            bool: 如果停止成功则返回True，否则返回False
        """
        # 停止故障检测器
        self.heartbeat_detector.stop()
        self.timeout_detector.stop()
        
        logger.info(f"停止节点故障处理器: {self.node_name}")
        
        return True
    
    def update_heartbeat(self, target_id: str, status: Optional[Dict[str, Any]] = None) -> None:
        """
        更新心跳信息
        
        参数:
            target_id (str): 目标节点ID
            status (Dict[str, Any], optional): 节点状态
        """
        # 更新心跳信息
        self.heartbeat_detector.update_heartbeat(target_id, status)
        
        # 更新节点状态
        self.node_status[target_id] = {
            "timestamp": time.time(),
            "status": status or {}
        }
        
        logger.debug(f"更新心跳信息: {self.node_name} -> {target_id}")
    
    def start_operation(self, 
                       operation_id: str,
                       timeout: Optional[float] = None,
                       target_id: Optional[str] = None,
                       metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        开始操作
        
        参数:
            operation_id (str): 操作ID
            timeout (float, optional): 超时时间，单位为秒，如果为None则使用默认超时时间
            target_id (str, optional): 目标节点ID
            metadata (Dict[str, Any], optional): 元数据
        """
        # 开始操作
        self.timeout_detector.start_operation(operation_id, timeout, target_id, metadata)
        
        logger.debug(f"开始操作: {self.node_name}, 操作: {operation_id}")
    
    def end_operation(self, operation_id: str) -> bool:
        """
        结束操作
        
        参数:
            operation_id (str): 操作ID
            
        返回:
            bool: 如果结束成功则返回True，否则返回False
        """
        # 结束操作
        result = self.timeout_detector.end_operation(operation_id)
        
        if result:
            logger.debug(f"结束操作: {self.node_name}, 操作: {operation_id}")
        else:
            logger.warning(f"结束操作失败: {self.node_name}, 操作: {operation_id}")
        
        return result
    
    def restart_node(self, node_id: str) -> bool:
        """
        重启节点
        
        参数:
            node_id (str): 节点ID
            
        返回:
            bool: 如果重启成功则返回True，否则返回False
        """
        logger.info(f"重启节点: {self.node_name} -> {node_id}")
        
        # 模拟节点重启
        time.sleep(1.0)
        
        # 更新节点状态
        self.node_status[node_id] = {
            "timestamp": time.time(),
            "status": {"state": "running"}
        }
        
        # 更新心跳信息
        self.heartbeat_detector.update_heartbeat(node_id, {"state": "running"})
        
        return True
    
    def reconnect_network(self, target_id: str) -> bool:
        """
        重连网络
        
        参数:
            target_id (str): 目标节点ID
            
        返回:
            bool: 如果重连成功则返回True，否则返回False
        """
        logger.info(f"重连网络: {self.node_name} -> {target_id}")
        
        # 模拟网络重连
        time.sleep(0.5)
        
        return True
    
    def retry_operation(self, operation: Dict[str, Any]) -> bool:
        """
        重试操作
        
        参数:
            operation (Dict[str, Any]): 操作信息
            
        返回:
            bool: 如果重试成功则返回True，否则返回False
        """
        operation_id = operation.get("operation_id", "")
        target_id = operation.get("target_id")
        
        logger.info(f"重试操作: {self.node_name}, 操作: {operation_id}, 目标: {target_id}")
        
        # 模拟操作重试
        time.sleep(0.5)
        
        return True
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        返回:
            Dict[str, Any]: 统计信息
        """
        return self.fault_manager.get_stats()


def simulate_node_failure(node_handler: NodeFaultHandler, target_id: str) -> None:
    """
    模拟节点故障
    
    参数:
        node_handler (NodeFaultHandler): 节点故障处理器
        target_id (str): 目标节点ID
    """
    logger.info(f"模拟节点故障: {node_handler.node_name} -> {target_id}")
    
    # 停止更新心跳
    # 心跳检测器会自动检测到节点故障
    
    # 等待一段时间
    time.sleep(5.0)


def simulate_operation_timeout(node_handler: NodeFaultHandler, target_id: str) -> None:
    """
    模拟操作超时
    
    参数:
        node_handler (NodeFaultHandler): 节点故障处理器
        target_id (str): 目标节点ID
    """
    logger.info(f"模拟操作超时: {node_handler.node_name} -> {target_id}")
    
    # 创建操作ID
    operation_id = f"operation_{str(uuid.uuid4())}"
    
    # 开始操作
    node_handler.start_operation(
        operation_id=operation_id,
        timeout=2.0,
        target_id=target_id,
        metadata={"type": "query", "parameters": {"key": "value"}}
    )
    
    # 不结束操作，让其超时
    # 超时检测器会自动检测到操作超时
    
    # 等待一段时间
    time.sleep(5.0)


def main():
    """主函数"""
    logger.info("分布式容错示例")
    
    # 创建节点
    logger.info("创建节点...")
    
    # 创建计算节点
    compute_node = NodeFactory.create_base_unit_node(
        role=NodeRole.COMPUTE,
        metadata={"name": "计算节点"}
    )
    
    # 创建存储节点
    storage_node = NodeFactory.create_base_unit_node(
        role=NodeRole.STORAGE,
        metadata={"name": "存储节点"}
    )
    
    # 创建存算一体节点
    compute_storage_node = NodeFactory.create_base_unit_node(
        role=NodeRole.COMPUTE_STORAGE,
        metadata={"name": "存算一体节点"}
    )
    
    # 创建分形分维层节点
    dimension_node = NodeFactory.create_fractal_dimension_node(
        role=NodeRole.COORDINATOR,
        metadata={"name": "分形分维层节点"}
    )
    
    # 创建高维分形核心节点
    core_node = NodeFactory.create_high_dimension_core_node(
        role=NodeRole.INTEGRATION,
        metadata={"name": "高维分形核心节点"}
    )
    
    # 创建节点故障处理器
    logger.info("创建节点故障处理器...")
    
    # 创建计算节点故障处理器
    compute_handler = NodeFaultHandler(
        node_id=compute_node.id,
        node_name="计算节点"
    )
    
    # 创建存储节点故障处理器
    storage_handler = NodeFaultHandler(
        node_id=storage_node.id,
        node_name="存储节点"
    )
    
    # 创建存算一体节点故障处理器
    compute_storage_handler = NodeFaultHandler(
        node_id=compute_storage_node.id,
        node_name="存算一体节点"
    )
    
    # 创建分形分维层节点故障处理器
    dimension_handler = NodeFaultHandler(
        node_id=dimension_node.id,
        node_name="分形分维层节点"
    )
    
    # 创建高维分形核心节点故障处理器
    core_handler = NodeFaultHandler(
        node_id=core_node.id,
        node_name="高维分形核心节点"
    )
    
    # 启动节点故障处理器
    logger.info("启动节点故障处理器...")
    compute_handler.start()
    storage_handler.start()
    compute_storage_handler.start()
    dimension_handler.start()
    core_handler.start()
    
    # 更新心跳信息
    logger.info("更新心跳信息...")
    
    # 计算节点更新心跳
    compute_handler.update_heartbeat(
        target_id=storage_node.id,
        status={"state": "running", "cpu_usage": 0.3, "memory_usage": 0.2}
    )
    compute_handler.update_heartbeat(
        target_id=compute_storage_node.id,
        status={"state": "running", "cpu_usage": 0.4, "memory_usage": 0.3}
    )
    compute_handler.update_heartbeat(
        target_id=dimension_node.id,
        status={"state": "running", "cpu_usage": 0.5, "memory_usage": 0.4}
    )
    
    # 存储节点更新心跳
    storage_handler.update_heartbeat(
        target_id=compute_node.id,
        status={"state": "running", "cpu_usage": 0.2, "memory_usage": 0.1}
    )
    storage_handler.update_heartbeat(
        target_id=compute_storage_node.id,
        status={"state": "running", "cpu_usage": 0.3, "memory_usage": 0.2}
    )
    storage_handler.update_heartbeat(
        target_id=dimension_node.id,
        status={"state": "running", "cpu_usage": 0.4, "memory_usage": 0.3}
    )
    
    # 存算一体节点更新心跳
    compute_storage_handler.update_heartbeat(
        target_id=compute_node.id,
        status={"state": "running", "cpu_usage": 0.3, "memory_usage": 0.2}
    )
    compute_storage_handler.update_heartbeat(
        target_id=storage_node.id,
        status={"state": "running", "cpu_usage": 0.4, "memory_usage": 0.3}
    )
    compute_storage_handler.update_heartbeat(
        target_id=dimension_node.id,
        status={"state": "running", "cpu_usage": 0.5, "memory_usage": 0.4}
    )
    
    # 分形分维层节点更新心跳
    dimension_handler.update_heartbeat(
        target_id=compute_node.id,
        status={"state": "running", "cpu_usage": 0.2, "memory_usage": 0.1}
    )
    dimension_handler.update_heartbeat(
        target_id=storage_node.id,
        status={"state": "running", "cpu_usage": 0.3, "memory_usage": 0.2}
    )
    dimension_handler.update_heartbeat(
        target_id=compute_storage_node.id,
        status={"state": "running", "cpu_usage": 0.4, "memory_usage": 0.3}
    )
    dimension_handler.update_heartbeat(
        target_id=core_node.id,
        status={"state": "running", "cpu_usage": 0.5, "memory_usage": 0.4}
    )
    
    # 高维分形核心节点更新心跳
    core_handler.update_heartbeat(
        target_id=dimension_node.id,
        status={"state": "running", "cpu_usage": 0.3, "memory_usage": 0.2}
    )
    
    # 等待一段时间
    logger.info("等待心跳稳定...")
    time.sleep(2.0)
    
    # 模拟节点故障
    logger.info("模拟节点故障...")
    
    # 创建线程模拟节点故障
    node_failure_thread = threading.Thread(
        target=simulate_node_failure,
        args=(compute_handler, storage_node.id)
    )
    node_failure_thread.start()
    
    # 等待一段时间
    time.sleep(10.0)
    
    # 模拟操作超时
    logger.info("模拟操作超时...")
    
    # 创建线程模拟操作超时
    operation_timeout_thread = threading.Thread(
        target=simulate_operation_timeout,
        args=(storage_handler, compute_node.id)
    )
    operation_timeout_thread.start()
    
    # 等待一段时间
    time.sleep(10.0)
    
    # 获取统计信息
    logger.info("获取统计信息...")
    
    # 获取计算节点故障处理器统计信息
    compute_stats = compute_handler.get_stats()
    logger.info(f"计算节点故障处理器统计信息:")
    logger.info(f"  故障数: {compute_stats['fault_count']}")
    logger.info(f"  恢复数: {compute_stats['recovery_count']}")
    logger.info(f"  成功数: {compute_stats['success_count']}")
    logger.info(f"  失败数: {compute_stats['failure_count']}")
    
    # 获取存储节点故障处理器统计信息
    storage_stats = storage_handler.get_stats()
    logger.info(f"存储节点故障处理器统计信息:")
    logger.info(f"  故障数: {storage_stats['fault_count']}")
    logger.info(f"  恢复数: {storage_stats['recovery_count']}")
    logger.info(f"  成功数: {storage_stats['success_count']}")
    logger.info(f"  失败数: {storage_stats['failure_count']}")
    
    # 停止节点故障处理器
    logger.info("停止节点故障处理器...")
    compute_handler.stop()
    storage_handler.stop()
    compute_storage_handler.stop()
    dimension_handler.stop()
    core_handler.stop()
    
    logger.info("示例完成")

if __name__ == "__main__":
    main()
