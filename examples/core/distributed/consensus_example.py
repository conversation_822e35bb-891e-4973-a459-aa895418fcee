"""
分布式共识示例

本示例展示了如何使用分布式系统核心模块进行共识。
"""

import logging
import time
import threading
import uuid
from typing import Dict, Any, List

from src.core.distributed import (
    RaftNodeState, LogEntryType, LogEntry,
    RaftLog, RaftState, RaftNode,
    RaftStorageBackend, MemoryRaftStorageBackend, RaftStorageFactory,
    RaftRpcClient, RaftRpcServer, LocalRaftRpcClient, LocalRaftRpcServer, RaftRpcFactory,
    RaftCluster
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class StateMachine:
    """
    状态机类
    
    状态机负责应用Raft共识算法中的日志条目。
    """
    
    def __init__(self, node_id: str):
        """
        初始化状态机
        
        参数:
            node_id (str): 节点ID
        """
        # 设置节点ID
        self.node_id = node_id
        
        # 设置状态
        self.state = {}
        
        # 设置锁
        self._lock = threading.RLock()
        
        # 设置统计信息
        self.stats = {
            "created_at": time.time(),
            "apply_count": 0,
            "command_count": 0,
            "configuration_count": 0,
            "no_operation_count": 0
        }
        
        logger.debug(f"创建状态机: {node_id}")
    
    def apply(self, entry: LogEntry) -> None:
        """
        应用日志条目
        
        参数:
            entry (LogEntry): 日志条目
        """
        with self._lock:
            # 更新统计信息
            self.stats["apply_count"] += 1
            
            # 根据条目类型应用日志条目
            if entry.entry_type == LogEntryType.COMMAND:
                self._apply_command(entry)
            elif entry.entry_type == LogEntryType.CONFIGURATION:
                self._apply_configuration(entry)
            elif entry.entry_type == LogEntryType.NO_OPERATION:
                self._apply_no_operation(entry)
            else:
                logger.warning(f"未知条目类型: {entry.entry_type.value}, 索引: {entry.index}")
    
    def _apply_command(self, entry: LogEntry) -> None:
        """
        应用命令
        
        参数:
            entry (LogEntry): 日志条目
        """
        # 更新统计信息
        self.stats["command_count"] += 1
        
        # 获取命令
        command = entry.command
        if not command:
            logger.warning(f"命令为空: {entry.index}")
            return
        
        # 获取操作类型
        operation = command.get("operation")
        if not operation:
            logger.warning(f"操作类型为空: {entry.index}")
            return
        
        # 根据操作类型应用命令
        if operation == "set":
            key = command.get("key")
            value = command.get("value")
            if key is not None:
                self.state[key] = value
                logger.debug(f"设置键值对: {key} = {value}, 节点: {self.node_id}")
        
        elif operation == "delete":
            key = command.get("key")
            if key is not None and key in self.state:
                del self.state[key]
                logger.debug(f"删除键值对: {key}, 节点: {self.node_id}")
        
        elif operation == "clear":
            self.state.clear()
            logger.debug(f"清空状态: 节点: {self.node_id}")
        
        else:
            logger.warning(f"未知操作类型: {operation}, 索引: {entry.index}")
    
    def _apply_configuration(self, entry: LogEntry) -> None:
        """
        应用配置
        
        参数:
            entry (LogEntry): 日志条目
        """
        # 更新统计信息
        self.stats["configuration_count"] += 1
        
        # 获取配置
        configuration = entry.command
        if not configuration:
            logger.warning(f"配置为空: {entry.index}")
            return
        
        logger.debug(f"应用配置: {configuration}, 节点: {self.node_id}")
    
    def _apply_no_operation(self, entry: LogEntry) -> None:
        """
        应用空操作
        
        参数:
            entry (LogEntry): 日志条目
        """
        # 更新统计信息
        self.stats["no_operation_count"] += 1
        
        logger.debug(f"应用空操作: {entry.index}, 节点: {self.node_id}")
    
    def get(self, key: str) -> Any:
        """
        获取键值
        
        参数:
            key (str): 键
            
        返回:
            Any: 值
        """
        with self._lock:
            return self.state.get(key)
    
    def get_all(self) -> Dict[str, Any]:
        """
        获取所有键值对
        
        返回:
            Dict[str, Any]: 所有键值对
        """
        with self._lock:
            return self.state.copy()
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        返回:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            return self.stats.copy()


def main():
    """主函数"""
    logger.info("分布式共识示例")
    
    # 创建Raft集群
    logger.info("创建Raft集群...")
    
    # 创建集群
    cluster = RaftCluster(
        cluster_id="example_cluster",
        storage_backend=RaftStorageFactory.create_memory_storage(),
        metadata={"name": "示例集群"}
    )
    
    # 创建状态机
    state_machines = {}
    
    # 创建节点
    logger.info("创建Raft节点...")
    
    # 创建节点1
    node1_id = "node1"
    state_machines[node1_id] = StateMachine(node1_id)
    node1 = cluster.add_node(
        node_id=node1_id,
        apply_handler=state_machines[node1_id].apply,
        metadata={"name": "节点1"}
    )
    
    # 创建节点2
    node2_id = "node2"
    state_machines[node2_id] = StateMachine(node2_id)
    node2 = cluster.add_node(
        node_id=node2_id,
        apply_handler=state_machines[node2_id].apply,
        metadata={"name": "节点2"}
    )
    
    # 创建节点3
    node3_id = "node3"
    state_machines[node3_id] = StateMachine(node3_id)
    node3 = cluster.add_node(
        node_id=node3_id,
        apply_handler=state_machines[node3_id].apply,
        metadata={"name": "节点3"}
    )
    
    # 创建节点4
    node4_id = "node4"
    state_machines[node4_id] = StateMachine(node4_id)
    node4 = cluster.add_node(
        node_id=node4_id,
        apply_handler=state_machines[node4_id].apply,
        metadata={"name": "节点4"}
    )
    
    # 创建节点5
    node5_id = "node5"
    state_machines[node5_id] = StateMachine(node5_id)
    node5 = cluster.add_node(
        node_id=node5_id,
        apply_handler=state_machines[node5_id].apply,
        metadata={"name": "节点5"}
    )
    
    # 启动集群
    logger.info("启动Raft集群...")
    cluster.start()
    
    # 等待选举完成
    logger.info("等待选举完成...")
    time.sleep(2.0)
    
    # 获取领导者
    leader_id = cluster.get_leader()
    if leader_id:
        logger.info(f"领导者: {leader_id}")
    else:
        logger.warning("没有领导者")
    
    # 追加日志条目
    logger.info("追加日志条目...")
    
    # 设置键值对
    cluster.append_entry(
        entry_type=LogEntryType.COMMAND,
        command={"operation": "set", "key": "name", "value": "Raft共识算法"},
        metadata={"timestamp": time.time()}
    )
    
    cluster.append_entry(
        entry_type=LogEntryType.COMMAND,
        command={"operation": "set", "key": "version", "value": "1.0.0"},
        metadata={"timestamp": time.time()}
    )
    
    cluster.append_entry(
        entry_type=LogEntryType.COMMAND,
        command={"operation": "set", "key": "author", "value": "Diego Ongaro and John Ousterhout"},
        metadata={"timestamp": time.time()}
    )
    
    # 等待日志复制和应用
    logger.info("等待日志复制和应用...")
    time.sleep(2.0)
    
    # 获取状态
    logger.info("获取状态...")
    
    for node_id, state_machine in state_machines.items():
        state = state_machine.get_all()
        logger.info(f"节点 {node_id} 状态: {state}")
    
    # 模拟领导者故障
    logger.info("模拟领导者故障...")
    
    # 获取领导者
    leader_id = cluster.get_leader()
    if leader_id:
        logger.info(f"当前领导者: {leader_id}")
        
        # 停止领导者
        cluster.remove_node(leader_id)
        
        logger.info(f"停止领导者: {leader_id}")
    else:
        logger.warning("没有领导者")
    
    # 等待新的选举完成
    logger.info("等待新的选举完成...")
    time.sleep(2.0)
    
    # 获取新的领导者
    new_leader_id = cluster.get_leader()
    if new_leader_id:
        logger.info(f"新的领导者: {new_leader_id}")
    else:
        logger.warning("没有新的领导者")
    
    # 追加更多日志条目
    logger.info("追加更多日志条目...")
    
    # 设置更多键值对
    cluster.append_entry(
        entry_type=LogEntryType.COMMAND,
        command={"operation": "set", "key": "paper", "value": "In Search of an Understandable Consensus Algorithm"},
        metadata={"timestamp": time.time()}
    )
    
    cluster.append_entry(
        entry_type=LogEntryType.COMMAND,
        command={"operation": "set", "key": "year", "value": 2014},
        metadata={"timestamp": time.time()}
    )
    
    # 等待日志复制和应用
    logger.info("等待日志复制和应用...")
    time.sleep(2.0)
    
    # 获取状态
    logger.info("获取状态...")
    
    for node_id, state_machine in state_machines.items():
        if node_id != leader_id:  # 跳过已停止的领导者
            state = state_machine.get_all()
            logger.info(f"节点 {node_id} 状态: {state}")
    
    # 停止集群
    logger.info("停止Raft集群...")
    cluster.stop()
    
    logger.info("示例完成")

if __name__ == "__main__":
    main()
