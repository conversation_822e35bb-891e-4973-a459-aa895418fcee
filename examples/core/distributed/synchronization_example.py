"""
分布式同步示例

本示例展示了如何使用分布式系统核心模块进行同步。
"""

import logging
import time
import threading
from typing import Dict, Any

from src.core.distributed import (
    NodeLevel, NodeRole, NodeState,
    BaseUnitNode, FractalDimensionNode, HighDimensionCoreNode,
    NodeFactory, FractalNetwork, NetworkFactory,
    SyncPrimitive, SynchronizationError,
    LocalLock, LocalBarrier, LocalSemaphore, LocalEvent,
    SyncPrimitiveFactory,
    DistributedLock, LockStorageBackend, DistributedLockFactory,
    DistributedBarrier, BarrierStorageBackend, DistributedBarrierFactory,
    SyncManager
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NodeSynchronizer:
    """
    节点同步器类
    
    节点同步器负责节点之间的同步。
    """
    
    def __init__(self, node_id: str, node_name: str):
        """
        初始化节点同步器
        
        参数:
            node_id (str): 节点ID
            node_name (str): 节点名称
        """
        # 设置节点ID和名称
        self.node_id = node_id
        self.node_name = node_name
        
        # 创建同步管理器
        self.sync_manager = SyncManager(
            manager_id=f"manager_{node_id}",
            node_id=node_id,
            metadata={"name": node_name}
        )
        
        logger.info(f"创建节点同步器: {node_id}, {node_name}")
    
    def acquire_lock(self, 
                    lock_id: str,
                    timeout: float = None,
                    ttl: float = None) -> bool:
        """
        获取锁
        
        参数:
            lock_id (str): 锁ID
            timeout (float, optional): 超时时间，单位为秒，如果为None则阻塞等待
            ttl (float, optional): 锁的生存时间，单位为秒，如果为None则永不过期
            
        返回:
            bool: 如果获取成功则返回True，否则返回False
        """
        # 获取锁
        result = self.sync_manager.acquire_primitive(lock_id, timeout=timeout, ttl=ttl)
        
        if result:
            logger.info(f"获取锁成功: {self.node_name}, 锁: {lock_id}")
        else:
            logger.warning(f"获取锁失败: {self.node_name}, 锁: {lock_id}")
        
        return result
    
    def release_lock(self, lock_id: str) -> bool:
        """
        释放锁
        
        参数:
            lock_id (str): 锁ID
            
        返回:
            bool: 如果释放成功则返回True，否则返回False
        """
        # 释放锁
        result = self.sync_manager.release_primitive(lock_id)
        
        if result:
            logger.info(f"释放锁成功: {self.node_name}, 锁: {lock_id}")
        else:
            logger.warning(f"释放锁失败: {self.node_name}, 锁: {lock_id}")
        
        return result
    
    def wait_barrier(self, 
                    barrier_id: str,
                    timeout: float = None) -> bool:
        """
        等待屏障
        
        参数:
            barrier_id (str): 屏障ID
            timeout (float, optional): 超时时间，单位为秒，如果为None则阻塞等待
            
        返回:
            bool: 如果等待成功则返回True，否则返回False
        """
        # 等待屏障
        result = self.sync_manager.acquire_primitive(barrier_id, timeout=timeout)
        
        if result:
            logger.info(f"等待屏障成功: {self.node_name}, 屏障: {barrier_id}")
        else:
            logger.warning(f"等待屏障失败: {self.node_name}, 屏障: {barrier_id}")
        
        return result
    
    def reset_barrier(self, barrier_id: str) -> bool:
        """
        重置屏障
        
        参数:
            barrier_id (str): 屏障ID
            
        返回:
            bool: 如果重置成功则返回True，否则返回False
        """
        # 重置屏障
        result = self.sync_manager.release_primitive(barrier_id)
        
        if result:
            logger.info(f"重置屏障成功: {self.node_name}, 屏障: {barrier_id}")
        else:
            logger.warning(f"重置屏障失败: {self.node_name}, 屏障: {barrier_id}")
        
        return result


def worker_with_lock(node_synchronizer: NodeSynchronizer, lock_id: str, shared_resource: Dict[str, Any], worker_id: int):
    """
    使用锁的工作线程
    
    参数:
        node_synchronizer (NodeSynchronizer): 节点同步器
        lock_id (str): 锁ID
        shared_resource (Dict[str, Any]): 共享资源
        worker_id (int): 工作线程ID
    """
    logger.info(f"工作线程 {worker_id} 启动: {node_synchronizer.node_name}")
    
    for i in range(5):
        # 获取锁
        if node_synchronizer.acquire_lock(lock_id, timeout=5.0):
            try:
                # 访问共享资源
                current_value = shared_resource.get("value", 0)
                logger.info(f"工作线程 {worker_id} 读取值: {current_value}")
                
                # 模拟处理
                time.sleep(0.1)
                
                # 更新共享资源
                shared_resource["value"] = current_value + 1
                shared_resource["last_updated_by"] = f"{node_synchronizer.node_name}-{worker_id}"
                
                logger.info(f"工作线程 {worker_id} 更新值: {shared_resource['value']}")
            finally:
                # 释放锁
                node_synchronizer.release_lock(lock_id)
        else:
            logger.warning(f"工作线程 {worker_id} 获取锁超时")
        
        # 等待一段时间
        time.sleep(0.2)
    
    logger.info(f"工作线程 {worker_id} 完成: {node_synchronizer.node_name}")


def worker_with_barrier(node_synchronizer: NodeSynchronizer, barrier_id: str, worker_id: int):
    """
    使用屏障的工作线程
    
    参数:
        node_synchronizer (NodeSynchronizer): 节点同步器
        barrier_id (str): 屏障ID
        worker_id (int): 工作线程ID
    """
    logger.info(f"工作线程 {worker_id} 启动: {node_synchronizer.node_name}")
    
    # 第一阶段
    logger.info(f"工作线程 {worker_id} 执行第一阶段: {node_synchronizer.node_name}")
    time.sleep(0.1 * worker_id)  # 模拟不同的处理时间
    
    # 等待所有线程完成第一阶段
    logger.info(f"工作线程 {worker_id} 等待第一阶段屏障: {node_synchronizer.node_name}")
    if node_synchronizer.wait_barrier(barrier_id, timeout=5.0):
        logger.info(f"工作线程 {worker_id} 通过第一阶段屏障: {node_synchronizer.node_name}")
    else:
        logger.warning(f"工作线程 {worker_id} 等待第一阶段屏障超时: {node_synchronizer.node_name}")
        return
    
    # 第二阶段
    logger.info(f"工作线程 {worker_id} 执行第二阶段: {node_synchronizer.node_name}")
    time.sleep(0.1 * (5 - worker_id))  # 模拟不同的处理时间
    
    # 等待所有线程完成第二阶段
    logger.info(f"工作线程 {worker_id} 等待第二阶段屏障: {node_synchronizer.node_name}")
    if node_synchronizer.wait_barrier(barrier_id, timeout=5.0):
        logger.info(f"工作线程 {worker_id} 通过第二阶段屏障: {node_synchronizer.node_name}")
    else:
        logger.warning(f"工作线程 {worker_id} 等待第二阶段屏障超时: {node_synchronizer.node_name}")
        return
    
    logger.info(f"工作线程 {worker_id} 完成: {node_synchronizer.node_name}")


def main():
    """主函数"""
    logger.info("分布式同步示例")
    
    # 创建节点
    logger.info("创建节点...")
    
    # 创建计算节点
    compute_node = NodeFactory.create_base_unit_node(
        role=NodeRole.COMPUTE,
        metadata={"name": "计算节点"}
    )
    
    # 创建存储节点
    storage_node = NodeFactory.create_base_unit_node(
        role=NodeRole.STORAGE,
        metadata={"name": "存储节点"}
    )
    
    # 创建存算一体节点
    compute_storage_node = NodeFactory.create_base_unit_node(
        role=NodeRole.COMPUTE_STORAGE,
        metadata={"name": "存算一体节点"}
    )
    
    # 创建节点同步器
    logger.info("创建节点同步器...")
    
    # 创建计算节点同步器
    compute_synchronizer = NodeSynchronizer(
        node_id=compute_node.id,
        node_name="计算节点"
    )
    
    # 创建存储节点同步器
    storage_synchronizer = NodeSynchronizer(
        node_id=storage_node.id,
        node_name="存储节点"
    )
    
    # 创建存算一体节点同步器
    compute_storage_synchronizer = NodeSynchronizer(
        node_id=compute_storage_node.id,
        node_name="存算一体节点"
    )
    
    # 创建同步原语
    logger.info("创建同步原语...")
    
    # 创建分布式锁
    lock_id = "shared_resource_lock"
    compute_synchronizer.sync_manager.create_distributed_lock(
        primitive_id=lock_id,
        metadata={"name": "共享资源锁"}
    )
    
    # 创建分布式屏障
    barrier_id = "phase_barrier"
    compute_synchronizer.sync_manager.create_distributed_barrier(
        parties=6,  # 3个节点，每个节点2个工作线程
        primitive_id=barrier_id,
        metadata={"name": "阶段屏障"}
    )
    
    # 创建共享资源
    shared_resource = {"value": 0, "last_updated_by": None}
    
    # 使用锁示例
    logger.info("使用锁示例...")
    
    # 创建工作线程
    lock_threads = []
    
    # 计算节点工作线程
    for i in range(2):
        thread = threading.Thread(
            target=worker_with_lock,
            args=(compute_synchronizer, lock_id, shared_resource, i)
        )
        lock_threads.append(thread)
    
    # 存储节点工作线程
    for i in range(2):
        thread = threading.Thread(
            target=worker_with_lock,
            args=(storage_synchronizer, lock_id, shared_resource, i)
        )
        lock_threads.append(thread)
    
    # 存算一体节点工作线程
    for i in range(2):
        thread = threading.Thread(
            target=worker_with_lock,
            args=(compute_storage_synchronizer, lock_id, shared_resource, i)
        )
        lock_threads.append(thread)
    
    # 启动工作线程
    for thread in lock_threads:
        thread.start()
    
    # 等待工作线程完成
    for thread in lock_threads:
        thread.join()
    
    # 输出共享资源
    logger.info(f"共享资源: {shared_resource}")
    
    # 使用屏障示例
    logger.info("使用屏障示例...")
    
    # 创建工作线程
    barrier_threads = []
    
    # 计算节点工作线程
    for i in range(2):
        thread = threading.Thread(
            target=worker_with_barrier,
            args=(compute_synchronizer, barrier_id, i)
        )
        barrier_threads.append(thread)
    
    # 存储节点工作线程
    for i in range(2):
        thread = threading.Thread(
            target=worker_with_barrier,
            args=(storage_synchronizer, barrier_id, i)
        )
        barrier_threads.append(thread)
    
    # 存算一体节点工作线程
    for i in range(2):
        thread = threading.Thread(
            target=worker_with_barrier,
            args=(compute_storage_synchronizer, barrier_id, i)
        )
        barrier_threads.append(thread)
    
    # 启动工作线程
    for thread in barrier_threads:
        thread.start()
    
    # 等待工作线程完成
    for thread in barrier_threads:
        thread.join()
    
    # 获取统计信息
    logger.info("获取统计信息...")
    
    # 获取计算节点同步器统计信息
    compute_stats = compute_synchronizer.sync_manager.get_stats()
    logger.info(f"计算节点同步器统计信息:")
    logger.info(f"  操作次数: {compute_stats['operation_count']}")
    logger.info(f"  成功次数: {compute_stats['success_count']}")
    logger.info(f"  错误次数: {compute_stats['error_count']}")
    
    # 获取存储节点同步器统计信息
    storage_stats = storage_synchronizer.sync_manager.get_stats()
    logger.info(f"存储节点同步器统计信息:")
    logger.info(f"  操作次数: {storage_stats['operation_count']}")
    logger.info(f"  成功次数: {storage_stats['success_count']}")
    logger.info(f"  错误次数: {storage_stats['error_count']}")
    
    logger.info("示例完成")

if __name__ == "__main__":
    main()
