"""
分布式任务示例

本示例展示了如何使用分布式系统核心模块创建和调度任务。
"""

import logging
import time
import threading
from typing import Dict, Any

from src.core.distributed import (
    NodeLevel, NodeRole, NodeState,
    BaseUnitNode, FractalDimensionNode, HighDimensionCoreNode,
    NodeFactory, FractalNetwork, NetworkFactory,
    TaskType, TaskPriority, TaskState,
    TaskResult, TaskDependency, FractalTaskProperties,
    DistributedTask, TaskFactory, TaskScheduler
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TaskExecutor(threading.Thread):
    """
    任务执行器类
    
    任务执行器负责执行分配给节点的任务。
    """
    
    def __init__(self, node_id: str, scheduler: TaskScheduler):
        """
        初始化任务执行器
        
        参数:
            node_id (str): 节点ID
            scheduler (TaskScheduler): 任务调度器
        """
        super().__init__()
        self.node_id = node_id
        self.scheduler = scheduler
        self.is_running = False
        self.daemon = True
    
    def run(self):
        """
        执行任务
        """
        self.is_running = True
        
        while self.is_running:
            try:
                # 获取节点上的任务
                task_ids = self.scheduler.get_node_tasks(self.node_id)
                
                # 执行任务
                for task_id in task_ids:
                    # 获取任务
                    task = self.scheduler.get_task(task_id)
                    if task is None:
                        continue
                    
                    # 检查任务状态
                    if task.state == TaskState.SCHEDULED:
                        # 更新任务状态为RUNNING
                        self.scheduler.update_task_state(task_id, TaskState.RUNNING)
                        
                        # 执行任务
                        logger.info(f"执行任务: {task_id}, 类型: {task.task_type.value}, 名称: {task.name}")
                        
                        try:
                            # 根据任务类型执行不同的操作
                            if task.task_type == TaskType.COMPUTE:
                                result = self._execute_compute_task(task)
                            elif task.task_type == TaskType.STORAGE:
                                result = self._execute_storage_task(task)
                            elif task.task_type == TaskType.COMMUNICATION:
                                result = self._execute_communication_task(task)
                            elif task.task_type == TaskType.COORDINATION:
                                result = self._execute_coordination_task(task)
                            elif task.task_type == TaskType.REASONING:
                                result = self._execute_reasoning_task(task)
                            elif task.task_type == TaskType.INTEGRATION:
                                result = self._execute_integration_task(task)
                            elif task.task_type == TaskType.CREATIVE:
                                result = self._execute_creative_task(task)
                            else:
                                result = TaskResult(
                                    success=False,
                                    error_message=f"不支持的任务类型: {task.task_type.value}"
                                )
                            
                            # 设置任务结果
                            self.scheduler.set_task_result(task_id, result)
                            
                            logger.info(f"任务完成: {task_id}, 成功: {result.success}")
                        except Exception as e:
                            # 设置任务失败
                            error_result = TaskResult(
                                success=False,
                                error_message=str(e)
                            )
                            self.scheduler.set_task_result(task_id, error_result)
                            
                            logger.error(f"任务执行异常: {task_id}, 错误: {str(e)}")
                
                # 等待一段时间
                time.sleep(0.1)
            except Exception as e:
                logger.error(f"任务执行器异常: {str(e)}")
    
    def stop(self):
        """
        停止任务执行器
        """
        self.is_running = False
    
    def _execute_compute_task(self, task: DistributedTask) -> TaskResult:
        """
        执行计算任务
        
        参数:
            task (DistributedTask): 要执行的任务
            
        返回:
            TaskResult: 任务结果
        """
        # 获取任务参数
        operation = task.parameters.get("operation")
        data = task.parameters.get("data")
        
        # 检查参数
        if not operation:
            return TaskResult(
                success=False,
                error_message="缺少操作参数"
            )
        
        # 执行操作
        try:
            result = None
            
            if operation == "add":
                if isinstance(data, (list, tuple)) and len(data) == 2:
                    result = data[0] + data[1]
                else:
                    return TaskResult(
                        success=False,
                        error_message="无效的数据格式，需要包含两个元素的列表或元组"
                    )
            elif operation == "multiply":
                if isinstance(data, (list, tuple)) and len(data) == 2:
                    result = data[0] * data[1]
                else:
                    return TaskResult(
                        success=False,
                        error_message="无效的数据格式，需要包含两个元素的列表或元组"
                    )
            elif operation == "transform":
                result = str(data)
            else:
                return TaskResult(
                    success=False,
                    error_message=f"不支持的操作: {operation}"
                )
            
            # 模拟计算延迟
            time.sleep(0.5)
            
            return TaskResult(
                success=True,
                data=result,
                metadata={"operation": operation}
            )
        except Exception as e:
            return TaskResult(
                success=False,
                error_message=f"计算失败: {str(e)}"
            )
    
    def _execute_storage_task(self, task: DistributedTask) -> TaskResult:
        """
        执行存储任务
        
        参数:
            task (DistributedTask): 要执行的任务
            
        返回:
            TaskResult: 任务结果
        """
        # 获取任务参数
        operation = task.parameters.get("operation")
        key = task.parameters.get("key")
        value = task.parameters.get("value")
        
        # 检查参数
        if not operation:
            return TaskResult(
                success=False,
                error_message="缺少操作参数"
            )
        
        if not key:
            return TaskResult(
                success=False,
                error_message="缺少键参数"
            )
        
        # 执行操作
        try:
            # 模拟存储操作
            # 在实际应用中，这里应该与实际的存储系统交互
            storage = {}
            
            if operation.lower() == "store":
                if value is None:
                    return TaskResult(
                        success=False,
                        error_message="存储操作需要提供值"
                    )
                
                storage[key] = value
                
                # 模拟存储延迟
                time.sleep(0.3)
                
                return TaskResult(
                    success=True,
                    data={"key": key},
                    metadata={"operation": operation}
                )
            elif operation.lower() == "retrieve":
                # 模拟检索延迟
                time.sleep(0.2)
                
                if key in storage:
                    return TaskResult(
                        success=True,
                        data={"key": key, "value": storage[key]},
                        metadata={"operation": operation}
                    )
                else:
                    return TaskResult(
                        success=False,
                        error_message=f"键不存在: {key}"
                    )
            elif operation.lower() == "delete":
                # 模拟删除延迟
                time.sleep(0.1)
                
                if key in storage:
                    del storage[key]
                    return TaskResult(
                        success=True,
                        data={"key": key},
                        metadata={"operation": operation}
                    )
                else:
                    return TaskResult(
                        success=False,
                        error_message=f"键不存在: {key}"
                    )
            else:
                return TaskResult(
                    success=False,
                    error_message=f"不支持的操作: {operation}"
                )
        except Exception as e:
            return TaskResult(
                success=False,
                error_message=f"存储操作失败: {str(e)}"
            )
    
    def _execute_communication_task(self, task: DistributedTask) -> TaskResult:
        """
        执行通信任务
        
        参数:
            task (DistributedTask): 要执行的任务
            
        返回:
            TaskResult: 任务结果
        """
        # 获取任务参数
        source_node_id = task.parameters.get("source_node_id")
        target_node_id = task.parameters.get("target_node_id")
        message = task.parameters.get("message")
        
        # 检查参数
        if not source_node_id:
            return TaskResult(
                success=False,
                error_message="缺少源节点ID参数"
            )
        
        if not target_node_id:
            return TaskResult(
                success=False,
                error_message="缺少目标节点ID参数"
            )
        
        # 执行操作
        try:
            # 模拟通信延迟
            time.sleep(0.2)
            
            # 模拟通信操作
            # 在实际应用中，这里应该与实际的通信系统交互
            
            return TaskResult(
                success=True,
                data={"message_delivered": True},
                metadata={"source": source_node_id, "target": target_node_id}
            )
        except Exception as e:
            return TaskResult(
                success=False,
                error_message=f"通信失败: {str(e)}"
            )
    
    def _execute_coordination_task(self, task: DistributedTask) -> TaskResult:
        """
        执行协调任务
        
        参数:
            task (DistributedTask): 要执行的任务
            
        返回:
            TaskResult: 任务结果
        """
        # 获取任务参数
        coordination_type = task.parameters.get("coordination_type")
        nodes = task.parameters.get("nodes", [])
        coordination_data = task.parameters.get("coordination_data")
        
        # 检查参数
        if not coordination_type:
            return TaskResult(
                success=False,
                error_message="缺少协调类型参数"
            )
        
        if not nodes:
            return TaskResult(
                success=False,
                error_message="缺少节点列表参数"
            )
        
        # 执行操作
        try:
            # 模拟协调延迟
            time.sleep(0.5)
            
            # 模拟协调操作
            # 在实际应用中，这里应该与实际的协调系统交互
            
            return TaskResult(
                success=True,
                data={"coordination_result": "success"},
                metadata={"type": coordination_type, "nodes": nodes}
            )
        except Exception as e:
            return TaskResult(
                success=False,
                error_message=f"协调失败: {str(e)}"
            )
    
    def _execute_reasoning_task(self, task: DistributedTask) -> TaskResult:
        """
        执行推理任务
        
        参数:
            task (DistributedTask): 要执行的任务
            
        返回:
            TaskResult: 任务结果
        """
        # 获取任务参数
        reasoning_type = task.parameters.get("reasoning_type")
        input_data = task.parameters.get("input_data")
        
        # 检查参数
        if not reasoning_type:
            return TaskResult(
                success=False,
                error_message="缺少推理类型参数"
            )
        
        # 执行操作
        try:
            # 模拟推理延迟
            time.sleep(0.8)
            
            # 模拟推理操作
            # 在实际应用中，这里应该与实际的推理系统交互
            
            return TaskResult(
                success=True,
                data={"reasoning_result": "logical conclusion"},
                metadata={"type": reasoning_type}
            )
        except Exception as e:
            return TaskResult(
                success=False,
                error_message=f"推理失败: {str(e)}"
            )
    
    def _execute_integration_task(self, task: DistributedTask) -> TaskResult:
        """
        执行整合任务
        
        参数:
            task (DistributedTask): 要执行的任务
            
        返回:
            TaskResult: 任务结果
        """
        # 获取任务参数
        integration_type = task.parameters.get("integration_type")
        input_data = task.parameters.get("input_data", [])
        
        # 检查参数
        if not integration_type:
            return TaskResult(
                success=False,
                error_message="缺少整合类型参数"
            )
        
        if not input_data:
            return TaskResult(
                success=False,
                error_message="缺少输入数据参数"
            )
        
        # 执行操作
        try:
            # 模拟整合延迟
            time.sleep(0.7)
            
            # 模拟整合操作
            # 在实际应用中，这里应该与实际的整合系统交互
            
            return TaskResult(
                success=True,
                data={"integration_result": "synthesized data"},
                metadata={"type": integration_type}
            )
        except Exception as e:
            return TaskResult(
                success=False,
                error_message=f"整合失败: {str(e)}"
            )
    
    def _execute_creative_task(self, task: DistributedTask) -> TaskResult:
        """
        执行创造性任务
        
        参数:
            task (DistributedTask): 要执行的任务
            
        返回:
            TaskResult: 任务结果
        """
        # 获取任务参数
        creative_type = task.parameters.get("creative_type")
        input_data = task.parameters.get("input_data")
        constraints = task.parameters.get("constraints", {})
        
        # 检查参数
        if not creative_type:
            return TaskResult(
                success=False,
                error_message="缺少创造性类型参数"
            )
        
        # 执行操作
        try:
            # 模拟创造性延迟
            time.sleep(1.0)
            
            # 模拟创造性操作
            # 在实际应用中，这里应该与实际的创造性系统交互
            
            return TaskResult(
                success=True,
                data={"creative_result": "novel idea"},
                metadata={"type": creative_type}
            )
        except Exception as e:
            return TaskResult(
                success=False,
                error_message=f"创造性任务失败: {str(e)}"
            )


def main():
    """主函数"""
    logger.info("分布式任务示例")
    
    # 创建任务调度器
    logger.info("创建任务调度器...")
    scheduler = TaskScheduler(
        max_concurrent_tasks=5,
        scheduling_interval=0.5,
        load_balancing_strategy="round_robin"
    )
    
    # 创建节点
    logger.info("创建节点...")
    compute_node = NodeFactory.create_base_unit_node(
        role=NodeRole.COMPUTE,
        metadata={"name": "计算节点"}
    )
    
    storage_node = NodeFactory.create_base_unit_node(
        role=NodeRole.STORAGE,
        metadata={"name": "存储节点"}
    )
    
    compute_storage_node = NodeFactory.create_base_unit_node(
        role=NodeRole.COMPUTE_STORAGE,
        metadata={"name": "存算一体节点"}
    )
    
    # 初始化节点
    compute_node.initialize()
    storage_node.initialize()
    compute_storage_node.initialize()
    
    # 将节点添加到调度器
    scheduler.add_node(compute_node.id, {"state": "ready", "info": compute_node.get_info()})
    scheduler.add_node(storage_node.id, {"state": "ready", "info": storage_node.get_info()})
    scheduler.add_node(compute_storage_node.id, {"state": "ready", "info": compute_storage_node.get_info()})
    
    # 创建任务执行器
    logger.info("创建任务执行器...")
    compute_executor = TaskExecutor(compute_node.id, scheduler)
    storage_executor = TaskExecutor(storage_node.id, scheduler)
    compute_storage_executor = TaskExecutor(compute_storage_node.id, scheduler)
    
    # 启动任务执行器
    compute_executor.start()
    storage_executor.start()
    compute_storage_executor.start()
    
    # 启动调度器
    logger.info("启动调度器...")
    scheduler.start()
    
    # 创建任务
    logger.info("创建任务...")
    
    # 创建计算任务
    compute_task1 = TaskFactory.create_compute_task(
        name="加法计算",
        operation="add",
        data=[10, 20],
        priority=TaskPriority.HIGH
    )
    
    compute_task2 = TaskFactory.create_compute_task(
        name="乘法计算",
        operation="multiply",
        data=[5, 6],
        priority=TaskPriority.NORMAL
    )
    
    # 创建存储任务
    storage_task1 = TaskFactory.create_storage_task(
        name="存储数据",
        operation="store",
        key="key1",
        value="value1",
        priority=TaskPriority.NORMAL
    )
    
    storage_task2 = TaskFactory.create_storage_task(
        name="检索数据",
        operation="retrieve",
        key="key1",
        priority=TaskPriority.LOW
    )
    
    # 创建通信任务
    communication_task = TaskFactory.create_communication_task(
        name="发送消息",
        source_node_id=compute_node.id,
        target_node_id=storage_node.id,
        message="Hello, Storage Node!",
        priority=TaskPriority.NORMAL
    )
    
    # 创建协调任务
    coordination_task = TaskFactory.create_coordination_task(
        name="协调节点",
        coordination_type="synchronize",
        nodes=[compute_node.id, storage_node.id, compute_storage_node.id],
        coordination_data={"action": "sync"},
        priority=TaskPriority.HIGH
    )
    
    # 创建推理任务
    reasoning_task = TaskFactory.create_reasoning_task(
        name="逻辑推理",
        reasoning_type="deduction",
        input_data={"premises": ["All men are mortal", "Socrates is a man"]},
        priority=TaskPriority.NORMAL
    )
    
    # 创建整合任务
    integration_task = TaskFactory.create_integration_task(
        name="数据整合",
        integration_type="merge",
        input_data=[{"a": 1}, {"b": 2}, {"c": 3}],
        priority=TaskPriority.NORMAL
    )
    
    # 创建创造性任务
    creative_task = TaskFactory.create_creative_task(
        name="创意生成",
        creative_type="generate",
        input_data="创意主题",
        constraints={"min_length": 10, "max_length": 100},
        priority=TaskPriority.LOW
    )
    
    # 添加任务依赖
    # 存储任务2依赖存储任务1
    storage_task2.add_dependency(TaskDependency(
        task_id=storage_task1.id,
        dependency_type=TaskDependency.DependencyType.COMPLETION
    ))
    
    # 整合任务依赖计算任务1和计算任务2
    integration_task.add_dependency(TaskDependency(
        task_id=compute_task1.id,
        dependency_type=TaskDependency.DependencyType.DATA
    ))
    
    integration_task.add_dependency(TaskDependency(
        task_id=compute_task2.id,
        dependency_type=TaskDependency.DependencyType.DATA
    ))
    
    # 将任务添加到调度器
    logger.info("添加任务到调度器...")
    scheduler.add_task(compute_task1)
    scheduler.add_task(compute_task2)
    scheduler.add_task(storage_task1)
    scheduler.add_task(storage_task2)
    scheduler.add_task(communication_task)
    scheduler.add_task(coordination_task)
    scheduler.add_task(reasoning_task)
    scheduler.add_task(integration_task)
    scheduler.add_task(creative_task)
    
    # 等待任务完成
    logger.info("等待任务完成...")
    
    try:
        # 等待一段时间
        time.sleep(10)
        
        # 获取任务结果
        logger.info("获取任务结果...")
        
        tasks = [
            compute_task1, compute_task2,
            storage_task1, storage_task2,
            communication_task, coordination_task,
            reasoning_task, integration_task, creative_task
        ]
        
        for task in tasks:
            task_info = scheduler.get_task(task.id)
            if task_info:
                result = task_info.get_result()
                logger.info(f"任务 {task.name} ({task.id}) 状态: {task_info.state.value}, 结果: {result}")
        
        # 获取调度器统计信息
        logger.info("调度器统计信息:")
        stats = scheduler.get_stats()
        for key, value in stats.items():
            logger.info(f"  {key}: {value}")
    finally:
        # 停止调度器和执行器
        logger.info("停止调度器和执行器...")
        scheduler.stop()
        compute_executor.stop()
        storage_executor.stop()
        compute_storage_executor.stop()
    
    logger.info("示例完成")


if __name__ == "__main__":
    main()
