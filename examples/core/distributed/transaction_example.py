"""
分布式事务示例

本示例展示了如何使用分布式系统核心模块进行事务管理。
"""

import logging
import time
import threading
import uuid
from typing import Dict, Any, List, Optional, Tuple

from src.core.distributed import (
    TransactionState, TransactionType, IsolationLevel,
    Transaction, TransactionFactory,
    PrepareRequest, PrepareResponse,
    CommitRequest, CommitResponse,
    AbortRequest, AbortResponse,
    TransactionLogEntry, TransactionLog,
    TransactionParticipant, TransactionCoordinator, TransactionManager,
    TransactionStorageBackend, MemoryTransactionStorageBackend, FileTransactionStorageBackend,
    TransactionStorageFactory
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ResourceManager:
    """
    资源管理器类
    
    资源管理器负责管理资源，包括准备、提交和中止操作。
    """
    
    def __init__(self, node_id: str):
        """
        初始化资源管理器
        
        参数:
            node_id (str): 节点ID
        """
        # 设置节点ID
        self.node_id = node_id
        
        # 设置资源
        self.resources = {}
        
        # 设置事务资源
        self.transaction_resources = {}
        
        # 设置锁
        self._lock = threading.RLock()
        
        # 设置统计信息
        self.stats = {
            "created_at": time.time(),
            "prepare_count": 0,
            "prepare_success_count": 0,
            "prepare_failure_count": 0,
            "commit_count": 0,
            "commit_success_count": 0,
            "commit_failure_count": 0,
            "abort_count": 0,
            "abort_success_count": 0,
            "abort_failure_count": 0,
            "operation_count": 0,
            "operation_success_count": 0,
            "operation_failure_count": 0
        }
        
        logger.debug(f"创建资源管理器: {node_id}")
    
    def prepare(self, transaction_id: str) -> bool:
        """
        准备事务
        
        参数:
            transaction_id (str): 事务ID
            
        返回:
            bool: 如果准备成功则返回True，否则返回False
        """
        with self._lock:
            # 更新统计信息
            self.stats["prepare_count"] += 1
            
            # 检查事务是否存在
            if transaction_id not in self.transaction_resources:
                logger.warning(f"事务不存在: {transaction_id}, 节点: {self.node_id}")
                
                # 更新统计信息
                self.stats["prepare_failure_count"] += 1
                
                return False
            
            # 更新统计信息
            self.stats["prepare_success_count"] += 1
            
            logger.info(f"准备事务: {transaction_id}, 节点: {self.node_id}")
            
            return True
    
    def commit(self, transaction_id: str) -> bool:
        """
        提交事务
        
        参数:
            transaction_id (str): 事务ID
            
        返回:
            bool: 如果提交成功则返回True，否则返回False
        """
        with self._lock:
            # 更新统计信息
            self.stats["commit_count"] += 1
            
            # 检查事务是否存在
            if transaction_id not in self.transaction_resources:
                logger.warning(f"事务不存在: {transaction_id}, 节点: {self.node_id}")
                
                # 更新统计信息
                self.stats["commit_failure_count"] += 1
                
                return False
            
            try:
                # 获取事务资源
                resources = self.transaction_resources[transaction_id]
                
                # 提交资源
                for key, value in resources.items():
                    self.resources[key] = value
                
                # 移除事务资源
                del self.transaction_resources[transaction_id]
                
                # 更新统计信息
                self.stats["commit_success_count"] += 1
                
                logger.info(f"提交事务: {transaction_id}, 节点: {self.node_id}")
                
                return True
            
            except Exception as e:
                logger.error(f"提交事务异常: {transaction_id}, 节点: {self.node_id}, 错误: {str(e)}")
                
                # 更新统计信息
                self.stats["commit_failure_count"] += 1
                
                return False
    
    def abort(self, transaction_id: str) -> bool:
        """
        中止事务
        
        参数:
            transaction_id (str): 事务ID
            
        返回:
            bool: 如果中止成功则返回True，否则返回False
        """
        with self._lock:
            # 更新统计信息
            self.stats["abort_count"] += 1
            
            # 检查事务是否存在
            if transaction_id not in self.transaction_resources:
                logger.warning(f"事务不存在: {transaction_id}, 节点: {self.node_id}")
                
                # 更新统计信息
                self.stats["abort_failure_count"] += 1
                
                return False
            
            try:
                # 移除事务资源
                del self.transaction_resources[transaction_id]
                
                # 更新统计信息
                self.stats["abort_success_count"] += 1
                
                logger.info(f"中止事务: {transaction_id}, 节点: {self.node_id}")
                
                return True
            
            except Exception as e:
                logger.error(f"中止事务异常: {transaction_id}, 节点: {self.node_id}, 错误: {str(e)}")
                
                # 更新统计信息
                self.stats["abort_failure_count"] += 1
                
                return False
    
    def execute_operation(self, 
                         transaction_id: str,
                         operation_type: str,
                         operation_data: Dict[str, Any]) -> Any:
        """
        执行操作
        
        参数:
            transaction_id (str): 事务ID
            operation_type (str): 操作类型
            operation_data (Dict[str, Any]): 操作数据
            
        返回:
            Any: 操作结果
        """
        with self._lock:
            # 更新统计信息
            self.stats["operation_count"] += 1
            
            # 检查事务是否存在
            if transaction_id not in self.transaction_resources:
                # 创建事务资源
                self.transaction_resources[transaction_id] = {}
            
            try:
                # 获取事务资源
                resources = self.transaction_resources[transaction_id]
                
                # 根据操作类型执行操作
                if operation_type == "get":
                    # 获取键
                    key = operation_data.get("key")
                    if key is None:
                        raise ValueError("缺少键")
                    
                    # 获取值
                    value = resources.get(key)
                    if value is None:
                        value = self.resources.get(key)
                    
                    # 更新统计信息
                    self.stats["operation_success_count"] += 1
                    
                    logger.debug(f"获取值: {key} = {value}, 事务: {transaction_id}, 节点: {self.node_id}")
                    
                    return value
                
                elif operation_type == "set":
                    # 获取键和值
                    key = operation_data.get("key")
                    value = operation_data.get("value")
                    if key is None:
                        raise ValueError("缺少键")
                    
                    # 设置值
                    resources[key] = value
                    
                    # 更新统计信息
                    self.stats["operation_success_count"] += 1
                    
                    logger.debug(f"设置值: {key} = {value}, 事务: {transaction_id}, 节点: {self.node_id}")
                    
                    return value
                
                elif operation_type == "delete":
                    # 获取键
                    key = operation_data.get("key")
                    if key is None:
                        raise ValueError("缺少键")
                    
                    # 删除值
                    if key in resources:
                        del resources[key]
                    
                    # 更新统计信息
                    self.stats["operation_success_count"] += 1
                    
                    logger.debug(f"删除值: {key}, 事务: {transaction_id}, 节点: {self.node_id}")
                    
                    return None
                
                else:
                    raise ValueError(f"未知操作类型: {operation_type}")
            
            except Exception as e:
                logger.error(f"执行操作异常: {transaction_id}, 类型: {operation_type}, 节点: {self.node_id}, 错误: {str(e)}")
                
                # 更新统计信息
                self.stats["operation_failure_count"] += 1
                
                raise
    
    def get_resources(self) -> Dict[str, Any]:
        """
        获取资源
        
        返回:
            Dict[str, Any]: 资源
        """
        with self._lock:
            return self.resources.copy()
    
    def get_transaction_resources(self, transaction_id: str) -> Optional[Dict[str, Any]]:
        """
        获取事务资源
        
        参数:
            transaction_id (str): 事务ID
            
        返回:
            Optional[Dict[str, Any]]: 事务资源，如果不存在则返回None
        """
        with self._lock:
            resources = self.transaction_resources.get(transaction_id)
            if resources is None:
                return None
            return resources.copy()
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        返回:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            return self.stats.copy()


class RpcClient:
    """
    RPC客户端类
    
    RPC客户端负责发送RPC请求。
    """
    
    def __init__(self, nodes: Dict[str, TransactionManager]):
        """
        初始化RPC客户端
        
        参数:
            nodes (Dict[str, TransactionManager]): 节点字典
        """
        # 设置节点
        self.nodes = nodes
        
        # 设置锁
        self._lock = threading.RLock()
        
        # 设置统计信息
        self.stats = {
            "created_at": time.time(),
            "prepare_count": 0,
            "commit_count": 0,
            "abort_count": 0
        }
        
        logger.debug(f"创建RPC客户端")
    
    def prepare(self, node_id: str, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送准备请求
        
        参数:
            node_id (str): 节点ID
            request (Dict[str, Any]): 准备请求
            
        返回:
            Dict[str, Any]: 准备响应
        """
        with self._lock:
            # 更新统计信息
            self.stats["prepare_count"] += 1
            
            # 获取节点
            node = self.nodes.get(node_id)
            if node is None:
                logger.warning(f"节点不存在: {node_id}")
                return {
                    "transaction_id": request.get("transaction_id", ""),
                    "participant_id": request.get("participant_id", ""),
                    "prepared": False,
                    "error_message": f"节点不存在: {node_id}"
                }
            
            # 发送准备请求
            return node.prepare(request)
    
    def commit(self, node_id: str, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送提交请求
        
        参数:
            node_id (str): 节点ID
            request (Dict[str, Any]): 提交请求
            
        返回:
            Dict[str, Any]: 提交响应
        """
        with self._lock:
            # 更新统计信息
            self.stats["commit_count"] += 1
            
            # 获取节点
            node = self.nodes.get(node_id)
            if node is None:
                logger.warning(f"节点不存在: {node_id}")
                return {
                    "transaction_id": request.get("transaction_id", ""),
                    "participant_id": request.get("participant_id", ""),
                    "committed": False,
                    "error_message": f"节点不存在: {node_id}"
                }
            
            # 发送提交请求
            return node.commit(request)
    
    def abort(self, node_id: str, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送中止请求
        
        参数:
            node_id (str): 节点ID
            request (Dict[str, Any]): 中止请求
            
        返回:
            Dict[str, Any]: 中止响应
        """
        with self._lock:
            # 更新统计信息
            self.stats["abort_count"] += 1
            
            # 获取节点
            node = self.nodes.get(node_id)
            if node is None:
                logger.warning(f"节点不存在: {node_id}")
                return {
                    "transaction_id": request.get("transaction_id", ""),
                    "participant_id": request.get("participant_id", ""),
                    "aborted": False,
                    "error_message": f"节点不存在: {node_id}"
                }
            
            # 发送中止请求
            return node.abort(request)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        返回:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            return self.stats.copy()


def main():
    """主函数"""
    logger.info("分布式事务示例")
    
    # 创建节点
    logger.info("创建节点...")
    
    # 创建节点字典
    nodes = {}
    
    # 创建资源管理器字典
    resource_managers = {}
    
    # 创建事务管理器字典
    transaction_managers = {}
    
    # 创建存储后端
    storage_backend = TransactionStorageFactory.create_memory_storage()
    
    # 创建节点1
    node1_id = "node1"
    resource_managers[node1_id] = ResourceManager(node1_id)
    
    # 创建节点2
    node2_id = "node2"
    resource_managers[node2_id] = ResourceManager(node2_id)
    
    # 创建节点3
    node3_id = "node3"
    resource_managers[node3_id] = ResourceManager(node3_id)
    
    # 创建RPC客户端
    rpc_client = RpcClient(nodes)
    
    # 创建事务管理器
    for node_id, resource_manager in resource_managers.items():
        transaction_managers[node_id] = TransactionManager(
            node_id=node_id,
            resource_manager=resource_manager,
            rpc_client=rpc_client,
            storage_backend=storage_backend,
            metadata={"name": f"节点{node_id}"}
        )
        nodes[node_id] = transaction_managers[node_id]
    
    # 启动事务管理器
    logger.info("启动事务管理器...")
    for node_id, transaction_manager in transaction_managers.items():
        transaction_manager.start()
    
    # 创建事务
    logger.info("创建事务...")
    transaction = transaction_managers[node1_id].create_transaction(
        transaction_type=TransactionType.DISTRIBUTED,
        isolation_level=IsolationLevel.READ_COMMITTED,
        timeout=10.0,
        metadata={"name": "示例事务"}
    )
    
    # 添加参与者
    logger.info("添加参与者...")
    transaction_managers[node1_id].add_participant(transaction.transaction_id, node2_id)
    transaction_managers[node1_id].add_participant(transaction.transaction_id, node3_id)
    
    # 加入事务
    logger.info("加入事务...")
    transaction_managers[node2_id].join_transaction(transaction)
    transaction_managers[node3_id].join_transaction(transaction)
    
    # 执行操作
    logger.info("执行操作...")
    
    # 节点1设置值
    transaction_managers[node1_id].execute_operation(
        transaction_id=transaction.transaction_id,
        operation_type="set",
        operation_data={"key": "name", "value": "分布式事务"},
        metadata={"timestamp": time.time()}
    )
    
    # 节点2设置值
    transaction_managers[node2_id].execute_operation(
        transaction_id=transaction.transaction_id,
        operation_type="set",
        operation_data={"key": "version", "value": "1.0.0"},
        metadata={"timestamp": time.time()}
    )
    
    # 节点3设置值
    transaction_managers[node3_id].execute_operation(
        transaction_id=transaction.transaction_id,
        operation_type="set",
        operation_data={"key": "author", "value": "分布式系统"},
        metadata={"timestamp": time.time()}
    )
    
    # 获取事务资源
    logger.info("获取事务资源...")
    for node_id, resource_manager in resource_managers.items():
        resources = resource_manager.get_transaction_resources(transaction.transaction_id)
        logger.info(f"节点 {node_id} 事务资源: {resources}")
    
    # 提交事务
    logger.info("提交事务...")
    committed = transaction_managers[node1_id].commit_transaction(transaction.transaction_id)
    logger.info(f"提交事务结果: {committed}")
    
    # 获取资源
    logger.info("获取资源...")
    for node_id, resource_manager in resource_managers.items():
        resources = resource_manager.get_resources()
        logger.info(f"节点 {node_id} 资源: {resources}")
    
    # 创建另一个事务
    logger.info("创建另一个事务...")
    transaction2 = transaction_managers[node2_id].create_transaction(
        transaction_type=TransactionType.DISTRIBUTED,
        isolation_level=IsolationLevel.READ_COMMITTED,
        timeout=10.0,
        metadata={"name": "示例事务2"}
    )
    
    # 添加参与者
    logger.info("添加参与者...")
    transaction_managers[node2_id].add_participant(transaction2.transaction_id, node1_id)
    transaction_managers[node2_id].add_participant(transaction2.transaction_id, node3_id)
    
    # 加入事务
    logger.info("加入事务...")
    transaction_managers[node1_id].join_transaction(transaction2)
    transaction_managers[node3_id].join_transaction(transaction2)
    
    # 执行操作
    logger.info("执行操作...")
    
    # 节点1设置值
    transaction_managers[node1_id].execute_operation(
        transaction_id=transaction2.transaction_id,
        operation_type="set",
        operation_data={"key": "language", "value": "Python"},
        metadata={"timestamp": time.time()}
    )
    
    # 节点2设置值
    transaction_managers[node2_id].execute_operation(
        transaction_id=transaction2.transaction_id,
        operation_type="set",
        operation_data={"key": "framework", "value": "分布式系统核心模块"},
        metadata={"timestamp": time.time()}
    )
    
    # 节点3设置值
    transaction_managers[node3_id].execute_operation(
        transaction_id=transaction2.transaction_id,
        operation_type="set",
        operation_data={"key": "year", "value": 2023},
        metadata={"timestamp": time.time()}
    )
    
    # 获取事务资源
    logger.info("获取事务资源...")
    for node_id, resource_manager in resource_managers.items():
        resources = resource_manager.get_transaction_resources(transaction2.transaction_id)
        logger.info(f"节点 {node_id} 事务资源: {resources}")
    
    # 中止事务
    logger.info("中止事务...")
    aborted = transaction_managers[node2_id].abort_transaction(transaction2.transaction_id, "示例中止")
    logger.info(f"中止事务结果: {aborted}")
    
    # 获取资源
    logger.info("获取资源...")
    for node_id, resource_manager in resource_managers.items():
        resources = resource_manager.get_resources()
        logger.info(f"节点 {node_id} 资源: {resources}")
    
    # 停止事务管理器
    logger.info("停止事务管理器...")
    for node_id, transaction_manager in transaction_managers.items():
        transaction_manager.stop()
    
    logger.info("示例完成")

if __name__ == "__main__":
    main()
