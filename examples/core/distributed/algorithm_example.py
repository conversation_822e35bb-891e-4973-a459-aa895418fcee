"""
分布式算法示例

本示例展示了如何使用分布式系统核心模块进行算法适配和执行。
"""

import time
import logging
import threading
import random
from typing import Dict, Any, List, Optional, Tuple

from src.core.distributed import (
    # 算法模块
    AlgorithmType, AlgorithmState,
    ExecutionMode, DistributionStrategy,
    Algorithm, AlgorithmTask,
    AlgorithmRegistry, AlgorithmExecutor,
    DistributedAlgorithmAdapter,
    
    # 调度模块
    TaskState, TaskPriority, TaskType,
    Task, TaskFactory,
    NodeInfo, LoadBalancingStrategy,
    ResourceAwareLoadBalancingStrategy, LoadBalancingStrategyFactory,
    TaskQueue, TaskScheduler,
    ResourceType, ResourceAllocation, ResourceManager
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AlgorithmImplementation:
    """
    算法实现类
    
    算法实现类提供了各种算法的具体实现。
    """
    
    @staticmethod
    def fibonacci(n: int) -> int:
        """
        计算斐波那契数列
        
        参数:
            n (int): 项数
            
        返回:
            int: 斐波那契数列的第n项
        """
        if n <= 0:
            return 0
        elif n == 1:
            return 1
        else:
            a, b = 0, 1
            for _ in range(2, n + 1):
                a, b = b, a + b
            return b
    
    @staticmethod
    def matrix_multiply(a: List[List[float]], b: List[List[float]]) -> List[List[float]]:
        """
        矩阵乘法
        
        参数:
            a (List[List[float]]): 矩阵A
            b (List[List[float]]): 矩阵B
            
        返回:
            List[List[float]]: 矩阵C = A * B
        """
        # 检查矩阵维度
        if not a or not b or not a[0] or not b[0]:
            return []
        
        # 获取矩阵维度
        m = len(a)
        n = len(a[0])
        p = len(b[0])
        
        # 检查矩阵维度是否匹配
        if n != len(b):
            raise ValueError("矩阵维度不匹配")
        
        # 初始化结果矩阵
        c = [[0.0 for _ in range(p)] for _ in range(m)]
        
        # 计算矩阵乘法
        for i in range(m):
            for j in range(p):
                for k in range(n):
                    c[i][j] += a[i][k] * b[k][j]
        
        return c
    
    @staticmethod
    def gradient_descent(f, df, x0, learning_rate=0.01, max_iterations=1000, tolerance=1e-6):
        """
        梯度下降优化
        
        参数:
            f (Callable): 目标函数
            df (Callable): 梯度函数
            x0 (List[float]): 初始点
            learning_rate (float): 学习率
            max_iterations (int): 最大迭代次数
            tolerance (float): 收敛容差
            
        返回:
            Tuple[List[float], float, int]: 最优点、最优值和迭代次数
        """
        # 初始化
        x = x0.copy()
        iterations = 0
        
        # 迭代优化
        while iterations < max_iterations:
            # 计算梯度
            gradient = df(x)
            
            # 检查收敛
            if sum(g**2 for g in gradient) < tolerance:
                break
            
            # 更新参数
            for i in range(len(x)):
                x[i] -= learning_rate * gradient[i]
            
            # 增加迭代次数
            iterations += 1
        
        # 计算最优值
        value = f(x)
        
        return x, value, iterations
    
    @staticmethod
    def kmeans(data, k, max_iterations=100):
        """
        K均值聚类
        
        参数:
            data (List[List[float]]): 数据点
            k (int): 聚类数
            max_iterations (int): 最大迭代次数
            
        返回:
            Tuple[List[List[float]], List[int]]: 聚类中心和聚类标签
        """
        # 检查数据
        if not data or k <= 0 or k > len(data):
            return [], []
        
        # 获取数据维度
        n = len(data)
        d = len(data[0])
        
        # 随机初始化聚类中心
        centers = []
        for _ in range(k):
            center = [random.random() for _ in range(d)]
            centers.append(center)
        
        # 迭代优化
        for _ in range(max_iterations):
            # 分配数据点到最近的聚类中心
            clusters = [[] for _ in range(k)]
            labels = []
            
            for i, point in enumerate(data):
                # 计算到各聚类中心的距离
                distances = []
                for center in centers:
                    distance = sum((point[j] - center[j])**2 for j in range(d))
                    distances.append(distance)
                
                # 找到最近的聚类中心
                cluster_idx = distances.index(min(distances))
                clusters[cluster_idx].append(point)
                labels.append(cluster_idx)
            
            # 更新聚类中心
            new_centers = []
            for cluster in clusters:
                if cluster:
                    # 计算均值
                    center = [sum(point[j] for point in cluster) / len(cluster) for j in range(d)]
                    new_centers.append(center)
                else:
                    # 如果聚类为空，则随机生成新的聚类中心
                    center = [random.random() for _ in range(d)]
                    new_centers.append(center)
            
            # 检查收敛
            if all(all(abs(new_centers[i][j] - centers[i][j]) < 1e-6 for j in range(d)) for i in range(k)):
                break
            
            # 更新聚类中心
            centers = new_centers
        
        return centers, labels


def task_executor(node_id: str, task: Task) -> Any:
    """
    任务执行器函数
    
    参数:
        node_id (str): 节点ID
        task (Task): 任务
        
    返回:
        Any: 任务结果
    """
    # 获取任务负载
    payload = task.payload
    
    # 获取算法ID
    algorithm_id = payload.get("algorithm_id")
    
    # 获取算法任务ID
    algorithm_task_id = payload.get("algorithm_task_id")
    
    # 获取输入数据
    input_data = payload.get("input_data", {})
    
    # 获取算法参数
    parameters = payload.get("parameters", {})
    
    logger.info(f"执行任务: {task.task_id}, 算法: {algorithm_id}, 节点: {node_id}")
    
    # 根据算法ID执行不同的算法
    if algorithm_id == "fibonacci":
        # 获取参数
        n = input_data.get("n", 10)
        
        # 执行算法
        result = AlgorithmImplementation.fibonacci(n)
        
        # 返回结果
        return {
            "algorithm_id": algorithm_id,
            "algorithm_task_id": algorithm_task_id,
            "task_id": task.task_id,
            "node_id": node_id,
            "input": n,
            "result": result
        }
    
    elif algorithm_id == "matrix_multiply":
        # 获取参数
        a = input_data.get("a", [[1, 2], [3, 4]])
        b = input_data.get("b", [[5, 6], [7, 8]])
        
        # 执行算法
        result = AlgorithmImplementation.matrix_multiply(a, b)
        
        # 返回结果
        return {
            "algorithm_id": algorithm_id,
            "algorithm_task_id": algorithm_task_id,
            "task_id": task.task_id,
            "node_id": node_id,
            "input": {"a": a, "b": b},
            "result": result
        }
    
    elif algorithm_id == "gradient_descent":
        # 获取参数
        x0 = input_data.get("x0", [0.0, 0.0])
        learning_rate = parameters.get("learning_rate", 0.01)
        max_iterations = parameters.get("max_iterations", 1000)
        tolerance = parameters.get("tolerance", 1e-6)
        
        # 定义目标函数和梯度函数
        def f(x):
            return x[0]**2 + x[1]**2
        
        def df(x):
            return [2*x[0], 2*x[1]]
        
        # 执行算法
        result = AlgorithmImplementation.gradient_descent(f, df, x0, learning_rate, max_iterations, tolerance)
        
        # 返回结果
        return {
            "algorithm_id": algorithm_id,
            "algorithm_task_id": algorithm_task_id,
            "task_id": task.task_id,
            "node_id": node_id,
            "input": {"x0": x0, "parameters": parameters},
            "result": {
                "x": result[0],
                "value": result[1],
                "iterations": result[2]
            }
        }
    
    elif algorithm_id == "kmeans":
        # 获取参数
        data = input_data.get("data", [[1, 2], [2, 3], [8, 7], [9, 8]])
        k = parameters.get("k", 2)
        max_iterations = parameters.get("max_iterations", 100)
        
        # 执行算法
        result = AlgorithmImplementation.kmeans(data, k, max_iterations)
        
        # 返回结果
        return {
            "algorithm_id": algorithm_id,
            "algorithm_task_id": algorithm_task_id,
            "task_id": task.task_id,
            "node_id": node_id,
            "input": {"data": data, "k": k, "max_iterations": max_iterations},
            "result": {
                "centers": result[0],
                "labels": result[1]
            }
        }
    
    else:
        # 未知算法
        logger.warning(f"未知算法: {algorithm_id}, 任务: {task.task_id}, 节点: {node_id}")
        
        # 返回错误
        return {
            "algorithm_id": algorithm_id,
            "algorithm_task_id": algorithm_task_id,
            "task_id": task.task_id,
            "node_id": node_id,
            "error": f"未知算法: {algorithm_id}"
        }


def algorithm_task_callback(task: AlgorithmTask) -> None:
    """
    算法任务回调函数
    
    参数:
        task (AlgorithmTask): 算法任务
    """
    if task.is_completed():
        logger.info(f"算法任务完成: {task.task_id}, 算法: {task.algorithm.algorithm_id}, 结果: {task.result}")
    elif task.is_failed():
        logger.warning(f"算法任务失败: {task.task_id}, 算法: {task.algorithm.algorithm_id}, 错误: {task.error}")


def main():
    """主函数"""
    logger.info("分布式算法示例")
    
    # 创建任务调度器
    scheduler = TaskScheduler(
        scheduler_id="scheduler1",
        load_balancing_strategy=LoadBalancingStrategyFactory.create_resource_aware_strategy(),
        task_executor=task_executor
    )
    
    # 创建资源管理器
    resource_manager = ResourceManager("manager1")
    
    # 创建算法注册表
    algorithm_registry = AlgorithmRegistry("registry1")
    
    # 创建算法执行器
    algorithm_executor = AlgorithmExecutor("executor1")
    
    # 创建分布式算法适配器
    algorithm_adapter = DistributedAlgorithmAdapter(
        adapter_id="adapter1",
        task_scheduler=scheduler,
        resource_manager=resource_manager,
        algorithm_registry=algorithm_registry,
        algorithm_executor=algorithm_executor
    )
    
    # 注册节点
    logger.info("注册节点...")
    
    # 注册节点1
    node1_id = "node1"
    node1_resources = {
        ResourceType.CPU: 4.0,
        ResourceType.MEMORY: 8.0,
        ResourceType.DISK: 100.0
    }
    resource_manager.register_node(node1_id, node1_resources)
    scheduler.register_node(
        node_id=node1_id,
        capabilities=["computation", "optimization"],
        resources=node1_resources
    )
    
    # 注册节点2
    node2_id = "node2"
    node2_resources = {
        ResourceType.CPU: 2.0,
        ResourceType.MEMORY: 4.0,
        ResourceType.DISK: 50.0
    }
    resource_manager.register_node(node2_id, node2_resources)
    scheduler.register_node(
        node_id=node2_id,
        capabilities=["computation", "learning"],
        resources=node2_resources
    )
    
    # 注册节点3
    node3_id = "node3"
    node3_resources = {
        ResourceType.CPU: 8.0,
        ResourceType.MEMORY: 16.0,
        ResourceType.DISK: 200.0,
        ResourceType.GPU: 2.0
    }
    resource_manager.register_node(node3_id, node3_resources)
    scheduler.register_node(
        node_id=node3_id,
        capabilities=["computation", "inference"],
        resources=node3_resources
    )
    
    # 启动任务调度器
    logger.info("启动任务调度器...")
    scheduler.start()
    
    # 注册算法
    logger.info("注册算法...")
    
    # 注册斐波那契算法
    fibonacci_algorithm = Algorithm(
        algorithm_id="fibonacci",
        algorithm_type=AlgorithmType.COMPUTATION,
        version="1.0.0",
        description="计算斐波那契数列",
        parameters={},
        metadata={"author": "分布式系统"}
    )
    algorithm_registry.register_algorithm(fibonacci_algorithm)
    
    # 注册矩阵乘法算法
    matrix_multiply_algorithm = Algorithm(
        algorithm_id="matrix_multiply",
        algorithm_type=AlgorithmType.COMPUTATION,
        version="1.0.0",
        description="矩阵乘法",
        parameters={},
        metadata={"author": "分布式系统"}
    )
    algorithm_registry.register_algorithm(matrix_multiply_algorithm)
    
    # 注册梯度下降算法
    gradient_descent_algorithm = Algorithm(
        algorithm_id="gradient_descent",
        algorithm_type=AlgorithmType.OPTIMIZATION,
        version="1.0.0",
        description="梯度下降优化",
        parameters={
            "learning_rate": 0.01,
            "max_iterations": 1000,
            "tolerance": 1e-6
        },
        metadata={"author": "分布式系统"}
    )
    algorithm_registry.register_algorithm(gradient_descent_algorithm)
    
    # 注册K均值聚类算法
    kmeans_algorithm = Algorithm(
        algorithm_id="kmeans",
        algorithm_type=AlgorithmType.LEARNING,
        version="1.0.0",
        description="K均值聚类",
        parameters={
            "k": 2,
            "max_iterations": 100
        },
        metadata={"author": "分布式系统"}
    )
    algorithm_registry.register_algorithm(kmeans_algorithm)
    
    # 执行算法
    logger.info("执行算法...")
    
    # 执行斐波那契算法
    fibonacci_task_id = algorithm_adapter.execute_algorithm(
        algorithm_id="fibonacci",
        input_data={"n": 10},
        execution_mode=ExecutionMode.DISTRIBUTED,
        distribution_strategy=DistributionStrategy.LOAD_BALANCED,
        timeout=5.0,
        callback=algorithm_task_callback,
        metadata={"priority": "high"}
    )
    logger.info(f"执行斐波那契算法: {fibonacci_task_id}")
    
    # 执行矩阵乘法算法
    matrix_multiply_task_id = algorithm_adapter.execute_algorithm(
        algorithm_id="matrix_multiply",
        input_data={
            "a": [[1, 2], [3, 4]],
            "b": [[5, 6], [7, 8]]
        },
        execution_mode=ExecutionMode.DISTRIBUTED,
        distribution_strategy=DistributionStrategy.LOAD_BALANCED,
        timeout=5.0,
        callback=algorithm_task_callback,
        metadata={"priority": "normal"}
    )
    logger.info(f"执行矩阵乘法算法: {matrix_multiply_task_id}")
    
    # 执行梯度下降算法
    gradient_descent_task_id = algorithm_adapter.execute_algorithm(
        algorithm_id="gradient_descent",
        input_data={"x0": [1.0, 1.0]},
        execution_mode=ExecutionMode.DISTRIBUTED,
        distribution_strategy=DistributionStrategy.LOAD_BALANCED,
        timeout=10.0,
        callback=algorithm_task_callback,
        metadata={"priority": "high"}
    )
    logger.info(f"执行梯度下降算法: {gradient_descent_task_id}")
    
    # 执行K均值聚类算法
    kmeans_task_id = algorithm_adapter.execute_algorithm(
        algorithm_id="kmeans",
        input_data={
            "data": [[1, 2], [2, 3], [8, 7], [9, 8]]
        },
        execution_mode=ExecutionMode.DISTRIBUTED,
        distribution_strategy=DistributionStrategy.LOAD_BALANCED,
        timeout=10.0,
        callback=algorithm_task_callback,
        metadata={"priority": "normal"}
    )
    logger.info(f"执行K均值聚类算法: {kmeans_task_id}")
    
    # 等待算法执行完成
    logger.info("等待算法执行完成...")
    time.sleep(5.0)
    
    # 获取算法结果
    logger.info("获取算法结果...")
    
    # 获取斐波那契算法结果
    fibonacci_result = algorithm_adapter.get_algorithm_task_result(fibonacci_task_id)
    logger.info(f"斐波那契算法结果: {fibonacci_result}")
    
    # 获取矩阵乘法算法结果
    matrix_multiply_result = algorithm_adapter.get_algorithm_task_result(matrix_multiply_task_id)
    logger.info(f"矩阵乘法算法结果: {matrix_multiply_result}")
    
    # 获取梯度下降算法结果
    gradient_descent_result = algorithm_adapter.get_algorithm_task_result(gradient_descent_task_id)
    logger.info(f"梯度下降算法结果: {gradient_descent_result}")
    
    # 获取K均值聚类算法结果
    kmeans_result = algorithm_adapter.get_algorithm_task_result(kmeans_task_id)
    logger.info(f"K均值聚类算法结果: {kmeans_result}")
    
    # 获取统计信息
    logger.info("获取统计信息...")
    
    # 获取任务调度器统计信息
    scheduler_stats = scheduler.get_stats()
    logger.info(f"任务调度器统计信息: {scheduler_stats}")
    
    # 获取资源管理器统计信息
    resource_manager_stats = resource_manager.get_stats()
    logger.info(f"资源管理器统计信息: {resource_manager_stats}")
    
    # 获取算法注册表统计信息
    algorithm_registry_stats = algorithm_registry.get_stats()
    logger.info(f"算法注册表统计信息: {algorithm_registry_stats}")
    
    # 获取算法执行器统计信息
    algorithm_executor_stats = algorithm_executor.get_stats()
    logger.info(f"算法执行器统计信息: {algorithm_executor_stats}")
    
    # 获取分布式算法适配器统计信息
    algorithm_adapter_stats = algorithm_adapter.get_stats()
    logger.info(f"分布式算法适配器统计信息: {algorithm_adapter_stats}")
    
    # 停止任务调度器
    logger.info("停止任务调度器...")
    scheduler.stop()
    
    logger.info("示例完成")

if __name__ == "__main__":
    main()
