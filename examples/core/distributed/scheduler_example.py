"""
分布式调度示例

本示例展示了如何使用分布式系统核心模块进行任务调度和资源管理。
"""

import time
import logging
import threading
import random
from typing import Dict, Any, List, Optional, Tuple

from src.core.distributed import (
    TaskState, TaskPriority, TaskType,
    Task, TaskFactory,
    NodeInfo, LoadBalancingStrategy,
    ResourceAwareLoadBalancingStrategy, LoadBalancingStrategyFactory,
    TaskQueue, TaskScheduler,
    ResourceType, ResourceAllocation, ResourceManager
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TaskExecutor:
    """
    任务执行器类
    
    任务执行器负责执行任务。
    """
    
    def __init__(self, executor_id: str):
        """
        初始化任务执行器
        
        参数:
            executor_id (str): 执行器ID
        """
        # 设置执行器ID
        self.executor_id = executor_id
        
        # 设置锁
        self._lock = threading.RLock()
        
        # 设置统计信息
        self.stats = {
            "created_at": time.time(),
            "execute_count": 0,
            "success_count": 0,
            "failure_count": 0
        }
        
        logger.info(f"创建任务执行器: {executor_id}")
    
    def execute(self, node_id: str, task: Task) -> None:
        """
        执行任务
        
        参数:
            node_id (str): 节点ID
            task (Task): 任务
        """
        with self._lock:
            # 更新统计信息
            self.stats["execute_count"] += 1
            
            logger.info(f"执行任务: {task.task_id}, 节点: {node_id}, 执行器: {self.executor_id}")
            
            try:
                # 根据任务类型执行任务
                if task.task_type == TaskType.COMPUTATION:
                    # 执行计算任务
                    result = self._execute_computation_task(task)
                    
                    # 设置任务结果
                    task.set_result(result)
                    
                    # 更新统计信息
                    self.stats["success_count"] += 1
                
                elif task.task_type == TaskType.IO:
                    # 执行IO任务
                    result = self._execute_io_task(task)
                    
                    # 设置任务结果
                    task.set_result(result)
                    
                    # 更新统计信息
                    self.stats["success_count"] += 1
                
                elif task.task_type == TaskType.NETWORK:
                    # 执行网络任务
                    result = self._execute_network_task(task)
                    
                    # 设置任务结果
                    task.set_result(result)
                    
                    # 更新统计信息
                    self.stats["success_count"] += 1
                
                elif task.task_type == TaskType.STORAGE:
                    # 执行存储任务
                    result = self._execute_storage_task(task)
                    
                    # 设置任务结果
                    task.set_result(result)
                    
                    # 更新统计信息
                    self.stats["success_count"] += 1
                
                elif task.task_type == TaskType.CONTROL:
                    # 执行控制任务
                    result = self._execute_control_task(task)
                    
                    # 设置任务结果
                    task.set_result(result)
                    
                    # 更新统计信息
                    self.stats["success_count"] += 1
                
                elif task.task_type == TaskType.COMPOSITE:
                    # 执行复合任务
                    result = self._execute_composite_task(task)
                    
                    # 设置任务结果
                    task.set_result(result)
                    
                    # 更新统计信息
                    self.stats["success_count"] += 1
                
                else:
                    # 未知任务类型
                    task.set_error(f"未知任务类型: {task.task_type}")
                    
                    # 更新统计信息
                    self.stats["failure_count"] += 1
            
            except Exception as e:
                logger.error(f"执行任务异常: {task.task_id}, 节点: {node_id}, 执行器: {self.executor_id}, 错误: {str(e)}")
                
                # 设置任务错误
                task.set_error(str(e))
                
                # 更新统计信息
                self.stats["failure_count"] += 1
    
    def _execute_computation_task(self, task: Task) -> Any:
        """
        执行计算任务
        
        参数:
            task (Task): 任务
            
        返回:
            Any: 任务结果
        """
        # 获取任务负载
        payload = task.payload
        
        # 获取操作类型
        operation = payload.get("operation")
        
        if operation == "add":
            # 获取操作数
            a = payload.get("a", 0)
            b = payload.get("b", 0)
            
            # 执行加法操作
            result = a + b
            
            # 模拟计算延迟
            time.sleep(0.1)
            
            return result
        
        elif operation == "multiply":
            # 获取操作数
            a = payload.get("a", 0)
            b = payload.get("b", 0)
            
            # 执行乘法操作
            result = a * b
            
            # 模拟计算延迟
            time.sleep(0.2)
            
            return result
        
        elif operation == "fibonacci":
            # 获取操作数
            n = payload.get("n", 0)
            
            # 执行斐波那契数列计算
            if n <= 0:
                return 0
            elif n == 1:
                return 1
            else:
                a, b = 0, 1
                for _ in range(2, n + 1):
                    a, b = b, a + b
                
                # 模拟计算延迟
                time.sleep(0.3)
                
                return b
        
        else:
            raise ValueError(f"未知操作类型: {operation}")
    
    def _execute_io_task(self, task: Task) -> Any:
        """
        执行IO任务
        
        参数:
            task (Task): 任务
            
        返回:
            Any: 任务结果
        """
        # 获取任务负载
        payload = task.payload
        
        # 获取操作类型
        operation = payload.get("operation")
        
        if operation == "read":
            # 获取文件路径
            path = payload.get("path")
            
            # 模拟读取文件
            logger.info(f"模拟读取文件: {path}")
            
            # 模拟IO延迟
            time.sleep(0.2)
            
            return f"文件内容: {path}"
        
        elif operation == "write":
            # 获取文件路径和内容
            path = payload.get("path")
            content = payload.get("content")
            
            # 模拟写入文件
            logger.info(f"模拟写入文件: {path}, 内容: {content}")
            
            # 模拟IO延迟
            time.sleep(0.3)
            
            return True
        
        else:
            raise ValueError(f"未知操作类型: {operation}")
    
    def _execute_network_task(self, task: Task) -> Any:
        """
        执行网络任务
        
        参数:
            task (Task): 任务
            
        返回:
            Any: 任务结果
        """
        # 获取任务负载
        payload = task.payload
        
        # 获取操作类型
        operation = payload.get("operation")
        
        if operation == "request":
            # 获取URL
            url = payload.get("url")
            
            # 模拟网络请求
            logger.info(f"模拟网络请求: {url}")
            
            # 模拟网络延迟
            time.sleep(0.5)
            
            return f"响应内容: {url}"
        
        elif operation == "download":
            # 获取URL
            url = payload.get("url")
            
            # 模拟下载文件
            logger.info(f"模拟下载文件: {url}")
            
            # 模拟网络延迟
            time.sleep(1.0)
            
            return f"下载内容: {url}"
        
        else:
            raise ValueError(f"未知操作类型: {operation}")
    
    def _execute_storage_task(self, task: Task) -> Any:
        """
        执行存储任务
        
        参数:
            task (Task): 任务
            
        返回:
            Any: 任务结果
        """
        # 获取任务负载
        payload = task.payload
        
        # 获取操作类型
        operation = payload.get("operation")
        
        if operation == "store":
            # 获取键和值
            key = payload.get("key")
            value = payload.get("value")
            
            # 模拟存储数据
            logger.info(f"模拟存储数据: {key} = {value}")
            
            # 模拟存储延迟
            time.sleep(0.2)
            
            return True
        
        elif operation == "retrieve":
            # 获取键
            key = payload.get("key")
            
            # 模拟检索数据
            logger.info(f"模拟检索数据: {key}")
            
            # 模拟存储延迟
            time.sleep(0.1)
            
            return f"值: {key}"
        
        else:
            raise ValueError(f"未知操作类型: {operation}")
    
    def _execute_control_task(self, task: Task) -> Any:
        """
        执行控制任务
        
        参数:
            task (Task): 任务
            
        返回:
            Any: 任务结果
        """
        # 获取任务负载
        payload = task.payload
        
        # 获取操作类型
        operation = payload.get("operation")
        
        if operation == "start":
            # 获取服务名称
            service = payload.get("service")
            
            # 模拟启动服务
            logger.info(f"模拟启动服务: {service}")
            
            # 模拟控制延迟
            time.sleep(0.3)
            
            return True
        
        elif operation == "stop":
            # 获取服务名称
            service = payload.get("service")
            
            # 模拟停止服务
            logger.info(f"模拟停止服务: {service}")
            
            # 模拟控制延迟
            time.sleep(0.2)
            
            return True
        
        else:
            raise ValueError(f"未知操作类型: {operation}")
    
    def _execute_composite_task(self, task: Task) -> Any:
        """
        执行复合任务
        
        参数:
            task (Task): 任务
            
        返回:
            Any: 任务结果
        """
        # 获取任务负载
        payload = task.payload
        
        # 获取子任务
        subtasks = payload.get("subtasks", [])
        
        # 执行子任务
        results = []
        for subtask in subtasks:
            # 获取子任务类型
            subtask_type = subtask.get("type")
            
            # 获取子任务负载
            subtask_payload = subtask.get("payload", {})
            
            # 创建子任务
            if subtask_type == "computation":
                # 执行计算子任务
                subtask_result = self._execute_computation_task(Task(
                    task_id=f"{task.task_id}_sub_{len(results)}",
                    task_type=TaskType.COMPUTATION,
                    payload=subtask_payload
                ))
            
            elif subtask_type == "io":
                # 执行IO子任务
                subtask_result = self._execute_io_task(Task(
                    task_id=f"{task.task_id}_sub_{len(results)}",
                    task_type=TaskType.IO,
                    payload=subtask_payload
                ))
            
            elif subtask_type == "network":
                # 执行网络子任务
                subtask_result = self._execute_network_task(Task(
                    task_id=f"{task.task_id}_sub_{len(results)}",
                    task_type=TaskType.NETWORK,
                    payload=subtask_payload
                ))
            
            elif subtask_type == "storage":
                # 执行存储子任务
                subtask_result = self._execute_storage_task(Task(
                    task_id=f"{task.task_id}_sub_{len(results)}",
                    task_type=TaskType.STORAGE,
                    payload=subtask_payload
                ))
            
            elif subtask_type == "control":
                # 执行控制子任务
                subtask_result = self._execute_control_task(Task(
                    task_id=f"{task.task_id}_sub_{len(results)}",
                    task_type=TaskType.CONTROL,
                    payload=subtask_payload
                ))
            
            else:
                raise ValueError(f"未知子任务类型: {subtask_type}")
            
            # 添加子任务结果
            results.append(subtask_result)
        
        return results
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        返回:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            return self.stats.copy()


def task_callback(task: Task) -> None:
    """
    任务回调函数
    
    参数:
        task (Task): 任务
    """
    if task.is_completed():
        logger.info(f"任务完成: {task.task_id}, 结果: {task.result}")
    elif task.is_failed():
        logger.warning(f"任务失败: {task.task_id}, 错误: {task.error}")
    elif task.is_canceled():
        logger.warning(f"任务取消: {task.task_id}")
    elif task.is_timeout():
        logger.warning(f"任务超时: {task.task_id}")


def main():
    """主函数"""
    logger.info("分布式调度示例")
    
    # 创建任务执行器
    task_executor = TaskExecutor("executor1")
    
    # 创建任务调度器
    scheduler = TaskScheduler(
        scheduler_id="scheduler1",
        load_balancing_strategy=LoadBalancingStrategyFactory.create_resource_aware_strategy(),
        task_executor=task_executor.execute
    )
    
    # 创建资源管理器
    resource_manager = ResourceManager("manager1")
    
    # 注册节点
    logger.info("注册节点...")
    
    # 注册节点1
    node1_id = "node1"
    node1_resources = {
        ResourceType.CPU: 4.0,
        ResourceType.MEMORY: 8.0,
        ResourceType.DISK: 100.0
    }
    resource_manager.register_node(node1_id, node1_resources)
    scheduler.register_node(
        node_id=node1_id,
        capabilities=["computation", "io", "storage"],
        resources=node1_resources
    )
    
    # 注册节点2
    node2_id = "node2"
    node2_resources = {
        ResourceType.CPU: 2.0,
        ResourceType.MEMORY: 4.0,
        ResourceType.DISK: 50.0,
        ResourceType.NETWORK: 10.0
    }
    resource_manager.register_node(node2_id, node2_resources)
    scheduler.register_node(
        node_id=node2_id,
        capabilities=["computation", "network"],
        resources=node2_resources
    )
    
    # 注册节点3
    node3_id = "node3"
    node3_resources = {
        ResourceType.CPU: 8.0,
        ResourceType.MEMORY: 16.0,
        ResourceType.DISK: 200.0,
        ResourceType.GPU: 2.0
    }
    resource_manager.register_node(node3_id, node3_resources)
    scheduler.register_node(
        node_id=node3_id,
        capabilities=["computation", "control"],
        resources=node3_resources
    )
    
    # 启动任务调度器
    logger.info("启动任务调度器...")
    scheduler.start()
    
    # 创建任务
    logger.info("创建任务...")
    
    # 创建计算任务
    computation_task = TaskFactory.create_computation_task(
        payload={
            "operation": "fibonacci",
            "n": 10
        },
        priority=TaskPriority.HIGH,
        timeout=5.0,
        metadata={
            "required_capabilities": ["computation"],
            "required_resources": {
                ResourceType.CPU: 2.0,
                ResourceType.MEMORY: 1.0
            }
        }
    )
    
    # 创建IO任务
    io_task = TaskFactory.create_io_task(
        payload={
            "operation": "read",
            "path": "/tmp/example.txt"
        },
        priority=TaskPriority.NORMAL,
        timeout=3.0,
        metadata={
            "required_capabilities": ["io"],
            "required_resources": {
                ResourceType.CPU: 1.0,
                ResourceType.MEMORY: 0.5,
                ResourceType.DISK: 1.0
            }
        }
    )
    
    # 创建网络任务
    network_task = TaskFactory.create_network_task(
        payload={
            "operation": "request",
            "url": "https://example.com"
        },
        priority=TaskPriority.NORMAL,
        timeout=10.0,
        metadata={
            "required_capabilities": ["network"],
            "required_resources": {
                ResourceType.CPU: 0.5,
                ResourceType.MEMORY: 0.5,
                ResourceType.NETWORK: 1.0
            }
        }
    )
    
    # 创建存储任务
    storage_task = TaskFactory.create_storage_task(
        payload={
            "operation": "store",
            "key": "example",
            "value": "Hello, World!"
        },
        priority=TaskPriority.LOW,
        timeout=2.0,
        metadata={
            "required_capabilities": ["storage"],
            "required_resources": {
                ResourceType.CPU: 0.5,
                ResourceType.MEMORY: 0.5,
                ResourceType.DISK: 1.0
            }
        }
    )
    
    # 创建控制任务
    control_task = TaskFactory.create_control_task(
        payload={
            "operation": "start",
            "service": "example-service"
        },
        priority=TaskPriority.CRITICAL,
        timeout=5.0,
        metadata={
            "required_capabilities": ["control"],
            "required_resources": {
                ResourceType.CPU: 1.0,
                ResourceType.MEMORY: 1.0
            }
        }
    )
    
    # 创建复合任务
    composite_task = TaskFactory.create_composite_task(
        payload={
            "subtasks": [
                {
                    "type": "computation",
                    "payload": {
                        "operation": "add",
                        "a": 1,
                        "b": 2
                    }
                },
                {
                    "type": "io",
                    "payload": {
                        "operation": "write",
                        "path": "/tmp/example.txt",
                        "content": "Hello, World!"
                    }
                }
            ]
        },
        priority=TaskPriority.HIGH,
        timeout=10.0,
        metadata={
            "required_capabilities": ["computation", "io"],
            "required_resources": {
                ResourceType.CPU: 2.0,
                ResourceType.MEMORY: 2.0,
                ResourceType.DISK: 1.0
            }
        }
    )
    
    # 提交任务
    logger.info("提交任务...")
    
    # 提交计算任务
    computation_task_id = scheduler.submit_task(computation_task, task_callback)
    logger.info(f"提交计算任务: {computation_task_id}")
    
    # 提交IO任务
    io_task_id = scheduler.submit_task(io_task, task_callback)
    logger.info(f"提交IO任务: {io_task_id}")
    
    # 提交网络任务
    network_task_id = scheduler.submit_task(network_task, task_callback)
    logger.info(f"提交网络任务: {network_task_id}")
    
    # 提交存储任务
    storage_task_id = scheduler.submit_task(storage_task, task_callback)
    logger.info(f"提交存储任务: {storage_task_id}")
    
    # 提交控制任务
    control_task_id = scheduler.submit_task(control_task, task_callback)
    logger.info(f"提交控制任务: {control_task_id}")
    
    # 提交复合任务
    composite_task_id = scheduler.submit_task(composite_task, task_callback)
    logger.info(f"提交复合任务: {composite_task_id}")
    
    # 等待任务完成
    logger.info("等待任务完成...")
    time.sleep(5.0)
    
    # 获取任务结果
    logger.info("获取任务结果...")
    
    # 获取计算任务结果
    computation_task_result = scheduler.get_task_result(computation_task_id)
    logger.info(f"计算任务结果: {computation_task_result}")
    
    # 获取IO任务结果
    io_task_result = scheduler.get_task_result(io_task_id)
    logger.info(f"IO任务结果: {io_task_result}")
    
    # 获取网络任务结果
    network_task_result = scheduler.get_task_result(network_task_id)
    logger.info(f"网络任务结果: {network_task_result}")
    
    # 获取存储任务结果
    storage_task_result = scheduler.get_task_result(storage_task_id)
    logger.info(f"存储任务结果: {storage_task_result}")
    
    # 获取控制任务结果
    control_task_result = scheduler.get_task_result(control_task_id)
    logger.info(f"控制任务结果: {control_task_result}")
    
    # 获取复合任务结果
    composite_task_result = scheduler.get_task_result(composite_task_id)
    logger.info(f"复合任务结果: {composite_task_result}")
    
    # 获取统计信息
    logger.info("获取统计信息...")
    
    # 获取任务调度器统计信息
    scheduler_stats = scheduler.get_stats()
    logger.info(f"任务调度器统计信息: {scheduler_stats}")
    
    # 获取资源管理器统计信息
    resource_manager_stats = resource_manager.get_stats()
    logger.info(f"资源管理器统计信息: {resource_manager_stats}")
    
    # 获取任务执行器统计信息
    task_executor_stats = task_executor.get_stats()
    logger.info(f"任务执行器统计信息: {task_executor_stats}")
    
    # 停止任务调度器
    logger.info("停止任务调度器...")
    scheduler.stop()
    
    logger.info("示例完成")

if __name__ == "__main__":
    main()
