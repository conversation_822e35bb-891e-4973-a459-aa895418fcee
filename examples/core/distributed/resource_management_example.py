"""
分布式资源管理示例

本示例展示了如何使用分布式系统核心模块管理和分配资源。
"""

import logging
import time
from typing import Dict, Any

from src.core.distributed import (
    NodeLevel, NodeRole, NodeState,
    BaseUnitNode, FractalDimensionNode, HighDimensionCoreNode,
    NodeFactory, FractalNetwork, NetworkFactory,
    ResourceType, ResourceState, ResourcePriority,
    ResourceCapacity, ResourceRequirement, ResourceAllocation,
    ResourceDescription, ResourceManager, ResourceFactory
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def print_resource_info(description: ResourceDescription) -> None:
    """
    打印资源信息
    
    参数:
        description (ResourceDescription): 资源描述
    """
    logger.info(f"节点资源信息: {description.node_id}")
    
    # 打印资源容量
    for resource_type, capacity in description.resources.items():
        logger.info(f"  {resource_type}: 总量={capacity.total}{capacity.unit}, 已用={capacity.used}{capacity.unit}, 可用={capacity.available}{capacity.unit}, 利用率={capacity.utilization:.2f}")
    
    # 打印资源状态
    for resource_type, state in description.resource_states.items():
        logger.info(f"  {resource_type} 状态: {state.value}")
    
    # 打印资源分配
    active_allocations = description.get_allocations(active_only=True)
    logger.info(f"  活动分配数: {len(active_allocations)}")
    for allocation in active_allocations:
        logger.info(f"    {allocation}")

def print_manager_info(manager: ResourceManager) -> None:
    """
    打印资源管理器信息
    
    参数:
        manager (ResourceManager): 资源管理器
    """
    logger.info(f"资源管理器信息:")
    
    # 打印节点数量
    nodes = manager.get_all_nodes()
    logger.info(f"  节点数: {len(nodes)}")
    
    # 打印可用资源
    available_resources = manager.get_available_resources()
    logger.info(f"  可用资源:")
    for node_id, resources in available_resources.items():
        logger.info(f"    {node_id}: {resources}")
    
    # 打印资源利用率
    utilization = manager.get_utilization()
    logger.info(f"  资源利用率:")
    for node_id, resources in utilization.items():
        logger.info(f"    {node_id}: {resources}")
    
    # 打印统计信息
    stats = manager.get_stats()
    logger.info(f"  统计信息:")
    for key, value in stats.items():
        logger.info(f"    {key}: {value}")

def main():
    """主函数"""
    logger.info("分布式资源管理示例")
    
    # 创建资源管理器
    logger.info("创建资源管理器...")
    manager = ResourceFactory.create_resource_manager(
        strategy_type="fractal",
        metadata={"name": "分形资源管理器"}
    )
    
    # 创建节点
    logger.info("创建节点...")
    
    # 创建计算节点
    compute_node = NodeFactory.create_base_unit_node(
        role=NodeRole.COMPUTE,
        metadata={
            "name": "计算节点",
            "fractal_properties": {
                "scale_level": 0,
                "self_similarity_factor": 0.8,
                "complexity_dimension": 1.5
            }
        }
    )
    
    # 创建存储节点
    storage_node = NodeFactory.create_base_unit_node(
        role=NodeRole.STORAGE,
        metadata={
            "name": "存储节点",
            "fractal_properties": {
                "scale_level": 0,
                "self_similarity_factor": 0.7,
                "complexity_dimension": 1.2
            }
        }
    )
    
    # 创建存算一体节点
    compute_storage_node = NodeFactory.create_base_unit_node(
        role=NodeRole.COMPUTE_STORAGE,
        metadata={
            "name": "存算一体节点",
            "fractal_properties": {
                "scale_level": 0,
                "self_similarity_factor": 0.9,
                "complexity_dimension": 1.8
            }
        }
    )
    
    # 创建分形分维层节点
    dimension_node = NodeFactory.create_fractal_dimension_node(
        role=NodeRole.COORDINATOR,
        metadata={
            "name": "分形分维层节点",
            "fractal_properties": {
                "scale_level": 1,
                "self_similarity_factor": 0.95,
                "complexity_dimension": 2.2
            }
        }
    )
    
    # 创建高维分形核心节点
    core_node = NodeFactory.create_high_dimension_core_node(
        role=NodeRole.INTEGRATION,
        metadata={
            "name": "高维分形核心节点",
            "fractal_properties": {
                "scale_level": 2,
                "self_similarity_factor": 0.98,
                "complexity_dimension": 2.5
            }
        }
    )
    
    # 注册节点资源
    logger.info("注册节点资源...")
    
    # 注册计算节点资源
    manager.register_node(
        node_id=compute_node.id,
        resources={
            ResourceType.CPU.value: ResourceFactory.create_resource_capacity(
                total=8.0,
                used=0.0,
                unit="cores"
            ),
            ResourceType.MEMORY.value: ResourceFactory.create_resource_capacity(
                total=16.0,
                used=0.0,
                unit="GB"
            ),
            ResourceType.NETWORK.value: ResourceFactory.create_resource_capacity(
                total=1000.0,
                used=0.0,
                unit="Mbps"
            )
        },
        metadata=compute_node.metadata
    )
    
    # 注册存储节点资源
    manager.register_node(
        node_id=storage_node.id,
        resources={
            ResourceType.CPU.value: ResourceFactory.create_resource_capacity(
                total=4.0,
                used=0.0,
                unit="cores"
            ),
            ResourceType.MEMORY.value: ResourceFactory.create_resource_capacity(
                total=8.0,
                used=0.0,
                unit="GB"
            ),
            ResourceType.STORAGE.value: ResourceFactory.create_resource_capacity(
                total=1000.0,
                used=0.0,
                unit="GB"
            ),
            ResourceType.NETWORK.value: ResourceFactory.create_resource_capacity(
                total=500.0,
                used=0.0,
                unit="Mbps"
            )
        },
        metadata=storage_node.metadata
    )
    
    # 注册存算一体节点资源
    manager.register_node(
        node_id=compute_storage_node.id,
        resources={
            ResourceType.CPU.value: ResourceFactory.create_resource_capacity(
                total=16.0,
                used=0.0,
                unit="cores"
            ),
            ResourceType.MEMORY.value: ResourceFactory.create_resource_capacity(
                total=32.0,
                used=0.0,
                unit="GB"
            ),
            ResourceType.STORAGE.value: ResourceFactory.create_resource_capacity(
                total=500.0,
                used=0.0,
                unit="GB"
            ),
            ResourceType.NETWORK.value: ResourceFactory.create_resource_capacity(
                total=2000.0,
                used=0.0,
                unit="Mbps"
            ),
            ResourceType.GPU.value: ResourceFactory.create_resource_capacity(
                total=2.0,
                used=0.0,
                unit="cards"
            )
        },
        metadata=compute_storage_node.metadata
    )
    
    # 注册分形分维层节点资源
    manager.register_node(
        node_id=dimension_node.id,
        resources={
            ResourceType.CPU.value: ResourceFactory.create_resource_capacity(
                total=32.0,
                used=0.0,
                unit="cores"
            ),
            ResourceType.MEMORY.value: ResourceFactory.create_resource_capacity(
                total=64.0,
                used=0.0,
                unit="GB"
            ),
            ResourceType.NETWORK.value: ResourceFactory.create_resource_capacity(
                total=5000.0,
                used=0.0,
                unit="Mbps"
            )
        },
        metadata=dimension_node.metadata
    )
    
    # 注册高维分形核心节点资源
    manager.register_node(
        node_id=core_node.id,
        resources={
            ResourceType.CPU.value: ResourceFactory.create_resource_capacity(
                total=64.0,
                used=0.0,
                unit="cores"
            ),
            ResourceType.MEMORY.value: ResourceFactory.create_resource_capacity(
                total=128.0,
                used=0.0,
                unit="GB"
            ),
            ResourceType.NETWORK.value: ResourceFactory.create_resource_capacity(
                total=10000.0,
                used=0.0,
                unit="Mbps"
            ),
            ResourceType.GPU.value: ResourceFactory.create_resource_capacity(
                total=8.0,
                used=0.0,
                unit="cards"
            )
        },
        metadata=core_node.metadata
    )
    
    # 打印资源管理器信息
    print_manager_info(manager)
    
    # 创建资源需求
    logger.info("创建资源需求...")
    
    # 创建计算任务需求
    compute_requirements = [
        ResourceFactory.create_cpu_requirement(
            amount=4.0,
            priority=ResourcePriority.HIGH
        ),
        ResourceFactory.create_memory_requirement(
            amount=8.0,
            priority=ResourcePriority.HIGH
        ),
        ResourceFactory.create_network_requirement(
            amount=100.0,
            priority=ResourcePriority.NORMAL
        )
    ]
    
    # 创建存储任务需求
    storage_requirements = [
        ResourceFactory.create_storage_requirement(
            amount=200.0,
            priority=ResourcePriority.HIGH
        ),
        ResourceFactory.create_network_requirement(
            amount=50.0,
            priority=ResourcePriority.NORMAL
        )
    ]
    
    # 创建GPU计算任务需求
    gpu_compute_requirements = [
        ResourceFactory.create_cpu_requirement(
            amount=8.0,
            priority=ResourcePriority.HIGH
        ),
        ResourceFactory.create_memory_requirement(
            amount=16.0,
            priority=ResourcePriority.HIGH
        ),
        ResourceFactory.create_gpu_requirement(
            amount=1.0,
            priority=ResourcePriority.CRITICAL
        ),
        ResourceFactory.create_network_requirement(
            amount=500.0,
            priority=ResourcePriority.NORMAL
        )
    ]
    
    # 创建弹性需求
    elastic_requirements = [
        ResourceFactory.create_cpu_requirement(
            amount=16.0,
            priority=ResourcePriority.NORMAL,
            is_elastic=True,
            min_amount=8.0
        ),
        ResourceFactory.create_memory_requirement(
            amount=32.0,
            priority=ResourcePriority.NORMAL,
            is_elastic=True,
            min_amount=16.0
        )
    ]
    
    # 分配资源
    logger.info("分配资源...")
    
    # 分配计算任务资源
    compute_allocations = manager.allocate_resources(
        requirements=compute_requirements,
        task_id="compute_task_1",
        metadata={"name": "计算任务1"}
    )
    
    logger.info(f"计算任务资源分配结果: {compute_allocations}")
    
    # 分配存储任务资源
    storage_allocations = manager.allocate_resources(
        requirements=storage_requirements,
        task_id="storage_task_1",
        metadata={"name": "存储任务1"}
    )
    
    logger.info(f"存储任务资源分配结果: {storage_allocations}")
    
    # 分配GPU计算任务资源
    gpu_compute_allocations = manager.allocate_resources(
        requirements=gpu_compute_requirements,
        task_id="gpu_compute_task_1",
        metadata={"name": "GPU计算任务1"}
    )
    
    logger.info(f"GPU计算任务资源分配结果: {gpu_compute_allocations}")
    
    # 分配弹性需求资源
    elastic_allocations = manager.allocate_resources(
        requirements=elastic_requirements,
        task_id="elastic_task_1",
        metadata={"name": "弹性任务1"}
    )
    
    logger.info(f"弹性任务资源分配结果: {elastic_allocations}")
    
    # 打印资源管理器信息
    print_manager_info(manager)
    
    # 打印节点资源信息
    for node_id in manager.get_all_nodes():
        description = manager.get_node(node_id)
        print_resource_info(description)
    
    # 释放资源
    logger.info("释放资源...")
    
    # 释放计算任务资源
    manager.release_resources("compute_task_1")
    
    # 释放GPU计算任务资源
    manager.release_resources("gpu_compute_task_1")
    
    # 打印资源管理器信息
    print_manager_info(manager)
    
    # 更改分配策略
    logger.info("更改分配策略...")
    
    # 设置最佳适应策略
    manager.set_strategy(ResourceFactory.create_allocation_strategy("best_fit"))
    
    # 分配新的计算任务资源
    new_compute_allocations = manager.allocate_resources(
        requirements=compute_requirements,
        task_id="compute_task_2",
        metadata={"name": "计算任务2"}
    )
    
    logger.info(f"新计算任务资源分配结果: {new_compute_allocations}")
    
    # 打印资源管理器信息
    print_manager_info(manager)
    
    # 注销节点
    logger.info("注销节点...")
    
    # 注销存储节点
    manager.unregister_node(storage_node.id)
    
    # 打印资源管理器信息
    print_manager_info(manager)
    
    logger.info("示例完成")

if __name__ == "__main__":
    main()
