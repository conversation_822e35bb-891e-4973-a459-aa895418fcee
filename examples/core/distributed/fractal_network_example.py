"""
分形网络示例

本示例展示了如何使用分布式系统核心模块创建和使用分形网络。
"""

import logging
import matplotlib.pyplot as plt
import networkx as nx
from typing import Dict, List, Set, Tuple

from src.core.distributed import (
    NodeLevel, NodeRole, NodeState,
    BaseUnitNode, FractalDimensionNode, HighDimensionCoreNode,
    NodeFactory, FractalNetwork, NetworkFactory
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_network_visualization(network: FractalNetwork) -> None:
    """
    创建网络可视化
    
    参数:
        network (FractalNetwork): 要可视化的网络
    """
    # 创建NetworkX图
    G = nx.Graph()
    
    # 添加节点
    node_colors = []
    node_sizes = []
    node_labels = {}
    
    # 添加基础单元节点
    base_units = network.get_all_nodes(NodeLevel.BASE_UNIT)
    for node_id, node in base_units.items():
        G.add_node(node_id)
        node_colors.append('blue')
        node_sizes.append(100)
        node_labels[node_id] = f"B{node.metadata.get('index', '')}"
    
    # 添加分形分维层节点
    dimensions = network.get_all_nodes(NodeLevel.FRACTAL_DIMENSION)
    for node_id, node in dimensions.items():
        G.add_node(node_id)
        node_colors.append('green')
        node_sizes.append(200)
        node_labels[node_id] = f"D{node.metadata.get('index', '')}"
    
    # 添加高维分形核心节点
    cores = network.get_all_nodes(NodeLevel.HIGH_DIMENSION_CORE)
    for node_id, node in cores.items():
        G.add_node(node_id)
        node_colors.append('red')
        node_sizes.append(300)
        node_labels[node_id] = f"C{node.metadata.get('index', '')}"
    
    # 添加边
    connections = network.get_connections()
    for conn_type, conn_list in connections.items():
        for source_id, target_id in conn_list:
            G.add_edge(source_id, target_id)
    
    # 创建图形
    plt.figure(figsize=(12, 8))
    
    # 使用不同的布局算法
    if len(G.nodes) < 20:
        pos = nx.spring_layout(G, seed=42)
    else:
        pos = nx.kamada_kawai_layout(G)
    
    # 绘制节点
    nx.draw_networkx_nodes(G, pos, node_color=node_colors, node_size=node_sizes, alpha=0.8)
    
    # 绘制边
    nx.draw_networkx_edges(G, pos, width=1.0, alpha=0.5)
    
    # 绘制标签
    nx.draw_networkx_labels(G, pos, labels=node_labels, font_size=10)
    
    # 添加标题
    plt.title("分形网络可视化")
    
    # 保存图形
    plt.savefig("fractal_network.png")
    plt.close()
    
    logger.info("网络可视化已保存到 fractal_network.png")

def main():
    """主函数"""
    logger.info("分形网络示例")
    
    # 创建层次化网络
    logger.info("创建层次化网络...")
    hierarchical_network = NetworkFactory.create_hierarchical_network(
        base_unit_count=8,
        dimension_count=4,
        core_count=2,
        metadata={"name": "层次化网络示例"}
    )
    
    # 打印网络信息
    logger.info(f"网络信息: {hierarchical_network}")
    logger.info(f"网络统计: {hierarchical_network.get_stats()}")
    
    # 可视化网络
    logger.info("可视化层次化网络...")
    create_network_visualization(hierarchical_network)
    
    # 创建小世界网络
    logger.info("创建小世界网络...")
    small_world_network = NetworkFactory.create_small_world_network(
        node_count=20,
        k=4,
        p=0.1,
        metadata={"name": "小世界网络示例"}
    )
    
    # 打印网络信息
    logger.info(f"网络信息: {small_world_network}")
    logger.info(f"网络统计: {small_world_network.get_stats()}")
    
    # 可视化网络
    logger.info("可视化小世界网络...")
    create_network_visualization(small_world_network)
    
    # 创建无标度网络
    logger.info("创建无标度网络...")
    scale_free_network = NetworkFactory.create_scale_free_network(
        node_count=20,
        m0=5,
        m=2,
        metadata={"name": "无标度网络示例"}
    )
    
    # 打印网络信息
    logger.info(f"网络信息: {scale_free_network}")
    logger.info(f"网络统计: {scale_free_network.get_stats()}")
    
    # 可视化网络
    logger.info("可视化无标度网络...")
    create_network_visualization(scale_free_network)
    
    # 手动创建自定义网络
    logger.info("创建自定义网络...")
    custom_network = FractalNetwork(metadata={"name": "自定义网络示例"})
    
    # 创建基础单元节点
    base1 = NodeFactory.create_base_unit_node(role=NodeRole.COMPUTE, metadata={"name": "计算节点1"})
    base2 = NodeFactory.create_base_unit_node(role=NodeRole.STORAGE, metadata={"name": "存储节点1"})
    base3 = NodeFactory.create_base_unit_node(role=NodeRole.COMPUTE_STORAGE, metadata={"name": "存算一体节点1"})
    
    # 创建分形分维层节点
    dim1 = NodeFactory.create_fractal_dimension_node(role=NodeRole.COORDINATOR, metadata={"name": "协调节点1"})
    dim2 = NodeFactory.create_fractal_dimension_node(role=NodeRole.REASONING, metadata={"name": "推理节点1"})
    
    # 创建高维分形核心节点
    core1 = NodeFactory.create_high_dimension_core_node(role=NodeRole.INTEGRATION, metadata={"name": "整合节点1"})
    
    # 添加节点
    custom_network.add_node(base1)
    custom_network.add_node(base2)
    custom_network.add_node(base3)
    custom_network.add_node(dim1)
    custom_network.add_node(dim2)
    custom_network.add_node(core1)
    
    # 连接节点
    custom_network.connect_nodes(base1.id, base2.id, NodeLevel.BASE_UNIT, NodeLevel.BASE_UNIT)
    custom_network.connect_nodes(base2.id, base3.id, NodeLevel.BASE_UNIT, NodeLevel.BASE_UNIT)
    custom_network.connect_nodes(base1.id, dim1.id, NodeLevel.BASE_UNIT, NodeLevel.FRACTAL_DIMENSION)
    custom_network.connect_nodes(base2.id, dim1.id, NodeLevel.BASE_UNIT, NodeLevel.FRACTAL_DIMENSION)
    custom_network.connect_nodes(base3.id, dim2.id, NodeLevel.BASE_UNIT, NodeLevel.FRACTAL_DIMENSION)
    custom_network.connect_nodes(dim1.id, dim2.id, NodeLevel.FRACTAL_DIMENSION, NodeLevel.FRACTAL_DIMENSION)
    custom_network.connect_nodes(dim1.id, core1.id, NodeLevel.FRACTAL_DIMENSION, NodeLevel.HIGH_DIMENSION_CORE)
    custom_network.connect_nodes(dim2.id, core1.id, NodeLevel.FRACTAL_DIMENSION, NodeLevel.HIGH_DIMENSION_CORE)
    
    # 打印网络信息
    logger.info(f"网络信息: {custom_network}")
    logger.info(f"网络统计: {custom_network.get_stats()}")
    
    # 可视化网络
    logger.info("可视化自定义网络...")
    create_network_visualization(custom_network)
    
    # 模拟节点操作
    logger.info("模拟节点操作...")
    
    # 初始化节点
    base1.initialize()
    base2.initialize()
    base3.initialize()
    
    # 存储数据
    logger.info("存储数据...")
    success, _ = base2.store_data("key1", "value1")
    logger.info(f"存储数据结果: {success}")
    
    success, _ = base3.store_data("key2", {"name": "复杂数据", "value": 42})
    logger.info(f"存储数据结果: {success}")
    
    # 检索数据
    logger.info("检索数据...")
    success, value = base2.retrieve_data("key1")
    logger.info(f"检索数据结果: {success}, 值: {value}")
    
    success, value = base3.retrieve_data("key2")
    logger.info(f"检索数据结果: {success}, 值: {value}")
    
    # 执行计算
    logger.info("执行计算...")
    success, result = base1.compute("add", [10, 20])
    logger.info(f"计算结果: {success}, 值: {result}")
    
    success, result = base3.compute("multiply", [5, 6])
    logger.info(f"计算结果: {success}, 值: {result}")
    
    logger.info("示例完成")

if __name__ == "__main__":
    main()
