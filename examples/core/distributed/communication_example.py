"""
分布式通信示例

本示例展示了如何使用分布式系统核心模块进行通信。
"""

import logging
import time
import threading
from typing import Dict, Any

from src.core.distributed import (
    NodeLevel, NodeRole, NodeState,
    BaseUnitNode, FractalDimensionNode, HighDimensionCoreNode,
    NodeFactory, FractalNetwork, NetworkFactory,
    MessageType, MessagePriority, MessageState, DeliveryMode,
    Message, MessageFactory,
    Channel, ChannelException, InMemoryChannel, ChannelFactory,
    MessageHandler, DefaultMessageHandler, FunctionMessageHandler,
    MessageProcessor, CommunicationManager
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NodeCommunicator:
    """
    节点通信器类
    
    节点通信器负责节点之间的通信。
    """
    
    def __init__(self, node_id: str, node_name: str):
        """
        初始化节点通信器
        
        参数:
            node_id (str): 节点ID
            node_name (str): 节点名称
        """
        # 设置节点ID和名称
        self.node_id = node_id
        self.node_name = node_name
        
        # 创建通信管理器
        self.comm_manager = CommunicationManager(
            manager_id=f"manager_{node_id}",
            node_id=node_id,
            metadata={"name": node_name}
        )
        
        # 创建通信通道
        self.channel = self.comm_manager.create_channel(
            channel_type="memory",
            channel_id=f"channel_{node_id}",
            metadata={"name": f"{node_name} Channel"}
        )
        
        # 创建消息处理器
        self.handler = self.comm_manager.create_handler(
            handler_type="default",
            handler_id=f"handler_{node_id}",
            metadata={"name": f"{node_name} Handler"}
        )
        
        # 创建消息处理器
        self.processor = self.comm_manager.create_processor(
            channel_id=f"channel_{node_id}",
            handler_id=f"handler_{node_id}",
            processor_id=f"processor_{node_id}",
            metadata={"name": f"{node_name} Processor"}
        )
        
        # 启动消息处理器
        self.processor.start()
        
        logger.info(f"创建节点通信器: {node_id}, {node_name}")
    
    def send_message(self, 
                    target_id: str,
                    message_type: MessageType,
                    content: Any,
                    priority: MessagePriority = MessagePriority.NORMAL) -> bool:
        """
        发送消息
        
        参数:
            target_id (str): 目标节点ID
            message_type (MessageType): 消息类型
            content (Any): 消息内容
            priority (MessagePriority): 消息优先级，默认为NORMAL
            
        返回:
            bool: 如果发送成功则返回True，否则返回False
        """
        # 创建消息
        message = MessageFactory.create_message(
            message_type=message_type,
            source_id=self.node_id,
            target_id=target_id,
            content=content,
            priority=priority,
            delivery_mode=DeliveryMode.POINT_TO_POINT,
            metadata={"source_name": self.node_name}
        )
        
        # 发送消息
        result = self.comm_manager.send_message(message)
        
        if result:
            logger.info(f"发送消息: {self.node_name} -> {target_id}, 类型: {message_type.value}")
        else:
            logger.warning(f"发送消息失败: {self.node_name} -> {target_id}, 类型: {message_type.value}")
        
        return result
    
    def broadcast_message(self, 
                         message_type: MessageType,
                         content: Any,
                         priority: MessagePriority = MessagePriority.NORMAL) -> bool:
        """
        广播消息
        
        参数:
            message_type (MessageType): 消息类型
            content (Any): 消息内容
            priority (MessagePriority): 消息优先级，默认为NORMAL
            
        返回:
            bool: 如果发送成功则返回True，否则返回False
        """
        # 创建消息
        message = MessageFactory.create_message(
            message_type=message_type,
            source_id=self.node_id,
            content=content,
            priority=priority,
            delivery_mode=DeliveryMode.BROADCAST,
            metadata={"source_name": self.node_name}
        )
        
        # 发送消息
        result = self.comm_manager.send_message(message)
        
        if result:
            logger.info(f"广播消息: {self.node_name}, 类型: {message_type.value}")
        else:
            logger.warning(f"广播消息失败: {self.node_name}, 类型: {message_type.value}")
        
        return result
    
    def multicast_message(self, 
                         target_ids: list,
                         message_type: MessageType,
                         content: Any,
                         priority: MessagePriority = MessagePriority.NORMAL) -> bool:
        """
        多播消息
        
        参数:
            target_ids (list): 目标节点ID列表
            message_type (MessageType): 消息类型
            content (Any): 消息内容
            priority (MessagePriority): 消息优先级，默认为NORMAL
            
        返回:
            bool: 如果发送成功则返回True，否则返回False
        """
        # 创建消息
        message = MessageFactory.create_message(
            message_type=message_type,
            source_id=self.node_id,
            target_ids=target_ids,
            content=content,
            priority=priority,
            delivery_mode=DeliveryMode.MULTICAST,
            metadata={"source_name": self.node_name}
        )
        
        # 发送消息
        result = self.comm_manager.send_message(message)
        
        if result:
            logger.info(f"多播消息: {self.node_name} -> {target_ids}, 类型: {message_type.value}")
        else:
            logger.warning(f"多播消息失败: {self.node_name} -> {target_ids}, 类型: {message_type.value}")
        
        return result
    
    def stop(self) -> None:
        """
        停止节点通信器
        """
        # 停止消息处理器
        self.processor.stop()
        
        logger.info(f"停止节点通信器: {self.node_id}, {self.node_name}")


def handle_data_message(message: Message) -> bool:
    """
    处理数据消息
    
    参数:
        message (Message): 要处理的消息
        
    返回:
        bool: 如果处理成功则返回True，否则返回False
    """
    source_name = message.metadata.get("source_name", message.source_id)
    logger.info(f"处理数据消息: 来自 {source_name}, 内容: {message.content}")
    return True


def handle_control_message(message: Message) -> bool:
    """
    处理控制消息
    
    参数:
        message (Message): 要处理的消息
        
    返回:
        bool: 如果处理成功则返回True，否则返回False
    """
    source_name = message.metadata.get("source_name", message.source_id)
    content = message.content
    command = content.get("command")
    parameters = content.get("parameters", {})
    logger.info(f"处理控制消息: 来自 {source_name}, 命令: {command}, 参数: {parameters}")
    return True


def handle_heartbeat_message(message: Message) -> bool:
    """
    处理心跳消息
    
    参数:
        message (Message): 要处理的消息
        
    返回:
        bool: 如果处理成功则返回True，否则返回False
    """
    source_name = message.metadata.get("source_name", message.source_id)
    content = message.content
    timestamp = content.get("timestamp")
    status = content.get("status", {})
    logger.info(f"处理心跳消息: 来自 {source_name}, 时间戳: {timestamp}, 状态: {status}")
    return True


def main():
    """主函数"""
    logger.info("分布式通信示例")
    
    # 创建节点
    logger.info("创建节点...")
    
    # 创建计算节点
    compute_node = NodeFactory.create_base_unit_node(
        role=NodeRole.COMPUTE,
        metadata={"name": "计算节点"}
    )
    
    # 创建存储节点
    storage_node = NodeFactory.create_base_unit_node(
        role=NodeRole.STORAGE,
        metadata={"name": "存储节点"}
    )
    
    # 创建存算一体节点
    compute_storage_node = NodeFactory.create_base_unit_node(
        role=NodeRole.COMPUTE_STORAGE,
        metadata={"name": "存算一体节点"}
    )
    
    # 创建分形分维层节点
    dimension_node = NodeFactory.create_fractal_dimension_node(
        role=NodeRole.COORDINATOR,
        metadata={"name": "分形分维层节点"}
    )
    
    # 创建高维分形核心节点
    core_node = NodeFactory.create_high_dimension_core_node(
        role=NodeRole.INTEGRATION,
        metadata={"name": "高维分形核心节点"}
    )
    
    # 创建节点通信器
    logger.info("创建节点通信器...")
    
    # 创建计算节点通信器
    compute_communicator = NodeCommunicator(
        node_id=compute_node.id,
        node_name="计算节点"
    )
    
    # 创建存储节点通信器
    storage_communicator = NodeCommunicator(
        node_id=storage_node.id,
        node_name="存储节点"
    )
    
    # 创建存算一体节点通信器
    compute_storage_communicator = NodeCommunicator(
        node_id=compute_storage_node.id,
        node_name="存算一体节点"
    )
    
    # 创建分形分维层节点通信器
    dimension_communicator = NodeCommunicator(
        node_id=dimension_node.id,
        node_name="分形分维层节点"
    )
    
    # 创建高维分形核心节点通信器
    core_communicator = NodeCommunicator(
        node_id=core_node.id,
        node_name="高维分形核心节点"
    )
    
    # 设置自定义消息处理器
    logger.info("设置自定义消息处理器...")
    
    # 为计算节点设置自定义消息处理器
    compute_handler = compute_communicator.comm_manager.create_handler(
        handler_type="function",
        handler_id=f"custom_handler_{compute_node.id}",
        handlers={
            MessageType.DATA: handle_data_message,
            MessageType.CONTROL: handle_control_message,
            MessageType.HEARTBEAT: handle_heartbeat_message
        },
        metadata={"name": "计算节点自定义处理器"}
    )
    
    compute_processor = compute_communicator.comm_manager.create_processor(
        channel_id=f"channel_{compute_node.id}",
        handler_id=f"custom_handler_{compute_node.id}",
        processor_id=f"custom_processor_{compute_node.id}",
        metadata={"name": "计算节点自定义处理器"}
    )
    
    # 停止默认处理器
    compute_communicator.processor.stop()
    
    # 启动自定义处理器
    compute_processor.start()
    
    # 发送消息
    logger.info("发送消息...")
    
    # 发送点对点消息
    compute_communicator.send_message(
        target_id=storage_node.id,
        message_type=MessageType.DATA,
        content="这是计算节点发送给存储节点的数据",
        priority=MessagePriority.NORMAL
    )
    
    storage_communicator.send_message(
        target_id=compute_node.id,
        message_type=MessageType.DATA,
        content="这是存储节点发送给计算节点的数据",
        priority=MessagePriority.NORMAL
    )
    
    compute_storage_communicator.send_message(
        target_id=dimension_node.id,
        message_type=MessageType.DATA,
        content="这是存算一体节点发送给分形分维层节点的数据",
        priority=MessagePriority.HIGH
    )
    
    # 发送控制消息
    dimension_communicator.send_message(
        target_id=compute_node.id,
        message_type=MessageType.CONTROL,
        content={
            "command": "start_computation",
            "parameters": {
                "task_id": "task_1",
                "priority": "high"
            }
        },
        priority=MessagePriority.HIGH
    )
    
    # 发送广播消息
    core_communicator.broadcast_message(
        message_type=MessageType.HEARTBEAT,
        content={
            "timestamp": time.time(),
            "status": {
                "cpu_usage": 0.5,
                "memory_usage": 0.3,
                "disk_usage": 0.2
            }
        },
        priority=MessagePriority.LOW
    )
    
    # 发送多播消息
    dimension_communicator.multicast_message(
        target_ids=[compute_node.id, storage_node.id, compute_storage_node.id],
        message_type=MessageType.SYNCHRONIZATION,
        content={
            "sync_type": "state",
            "sync_data": {
                "version": "1.0",
                "timestamp": time.time()
            }
        },
        priority=MessagePriority.HIGH
    )
    
    # 等待消息处理
    logger.info("等待消息处理...")
    time.sleep(2)
    
    # 获取统计信息
    logger.info("获取统计信息...")
    
    # 获取计算节点通信器统计信息
    compute_stats = compute_communicator.comm_manager.get_stats()
    logger.info(f"计算节点通信器统计信息:")
    logger.info(f"  发送消息数: {compute_stats['sent_count']}")
    logger.info(f"  接收消息数: {compute_stats['processors']['custom_processor_' + compute_node.id]['received_count']}")
    logger.info(f"  处理消息数: {compute_stats['processors']['custom_processor_' + compute_node.id]['processed_count']}")
    
    # 获取存储节点通信器统计信息
    storage_stats = storage_communicator.comm_manager.get_stats()
    logger.info(f"存储节点通信器统计信息:")
    logger.info(f"  发送消息数: {storage_stats['sent_count']}")
    logger.info(f"  接收消息数: {storage_stats['processors']['processor_' + storage_node.id]['received_count']}")
    logger.info(f"  处理消息数: {storage_stats['processors']['processor_' + storage_node.id]['processed_count']}")
    
    # 停止节点通信器
    logger.info("停止节点通信器...")
    compute_communicator.stop()
    storage_communicator.stop()
    compute_storage_communicator.stop()
    dimension_communicator.stop()
    core_communicator.stop()
    
    logger.info("示例完成")

if __name__ == "__main__":
    main()
