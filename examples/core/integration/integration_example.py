"""
系统集成示例

本示例展示了如何使用系统集成模块，将态射系统、元认知系统和分布式系统集成起来。
"""

import time
import logging
import numpy as np
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable

from src.core.morphism import (
    DynamicMorphismManager, DynamicMorphism
)
from src.core.metacognition import (
    MetacognitionManager, CognitiveState, MetacognitiveState
)
from src.core.distributed import (
    NetworkManager, BaseUnitNode
)
from src.core.integration import (
    IntegrationType, IntegrationMode,
    IntegrationComponent, IntegrationConnector, IntegrationPipeline, IntegrationEvent,
    IntegrationManager, IntegrationFactory, FullIntegrationFactory,
    MorphismMetacognitionAdapter, MorphismDistributedAdapter, MetacognitionDistributedAdapter
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    logger.info("系统集成示例")
    
    # 创建态射系统管理器
    logger.info("创建态射系统管理器...")
    morphism_manager = DynamicMorphismManager(manager_id="morphism_manager")
    
    # 创建元认知系统管理器
    logger.info("创建元认知系统管理器...")
    metacognition_manager = MetacognitionManager(manager_id="metacognition_manager")
    
    # 创建分布式系统管理器
    logger.info("创建分布式系统管理器...")
    distributed_manager = NetworkManager(manager_id="distributed_manager")
    
    # 创建系统集成管理器
    logger.info("创建系统集成管理器...")
    integration_manager = IntegrationManager(manager_id="integration_manager")
    
    # 创建态射系统与元认知系统集成
    logger.info("\n创建态射系统与元认知系统集成...")
    morphism_metacognition_ids = IntegrationFactory.create_morphism_metacognition_integration(
        manager=integration_manager,
        morphism_manager=morphism_manager,
        metacognition_manager=metacognition_manager,
        integration_mode=IntegrationMode.LOOSE_COUPLING
    )
    logger.info(f"态射系统与元认知系统集成ID: {morphism_metacognition_ids}")
    
    # 创建态射系统与分布式系统集成
    logger.info("\n创建态射系统与分布式系统集成...")
    morphism_distributed_ids = IntegrationFactory.create_morphism_distributed_integration(
        manager=integration_manager,
        morphism_manager=morphism_manager,
        distributed_manager=distributed_manager,
        integration_mode=IntegrationMode.LOOSE_COUPLING
    )
    logger.info(f"态射系统与分布式系统集成ID: {morphism_distributed_ids}")
    
    # 创建元认知系统与分布式系统集成
    logger.info("\n创建元认知系统与分布式系统集成...")
    metacognition_distributed_ids = IntegrationFactory.create_metacognition_distributed_integration(
        manager=integration_manager,
        metacognition_manager=metacognition_manager,
        distributed_manager=distributed_manager,
        integration_mode=IntegrationMode.LOOSE_COUPLING
    )
    logger.info(f"元认知系统与分布式系统集成ID: {metacognition_distributed_ids}")
    
    # 创建全系统集成
    logger.info("\n创建全系统集成...")
    full_integration_ids = FullIntegrationFactory.create_full_integration(
        manager=integration_manager,
        morphism_manager=morphism_manager,
        metacognition_manager=metacognition_manager,
        distributed_manager=distributed_manager,
        integration_mode=IntegrationMode.HYBRID
    )
    logger.info(f"全系统集成ID: {full_integration_ids}")
    
    # 测试态射系统与元认知系统集成
    logger.info("\n测试态射系统与元认知系统集成...")
    
    # 创建态射系统数据
    morphism_data = {
        "morphism_id": "m1",
        "morphism_type": "linear",
        "data": np.array([1.0, 2.0, 3.0]),
        "metadata": {
            "description": "线性态射"
        }
    }
    
    # 使用适配器转换数据
    metacognition_data = MorphismMetacognitionAdapter.morphism_to_metacognition(morphism_data)
    logger.info(f"态射系统数据: {morphism_data}")
    logger.info(f"转换后的元认知系统数据: {metacognition_data}")
    
    # 执行态射到元认知的连接器
    morphism_to_metacognition_connector_id = morphism_metacognition_ids["morphism_to_metacognition_connector_id"]
    transformed_data = integration_manager.execute_connector(
        connector_id=morphism_to_metacognition_connector_id,
        input_data=morphism_data
    )
    logger.info(f"执行连接器后的数据: {transformed_data}")
    
    # 测试元认知系统与分布式系统集成
    logger.info("\n测试元认知系统与分布式系统集成...")
    
    # 创建元认知系统数据
    metacognition_data = {
        "state_id": "cs1",
        "cognitive_level": "learning",
        "data": {
            "learning_rate": 0.01,
            "epochs": 100
        },
        "metadata": {
            "description": "学习状态"
        }
    }
    
    # 使用适配器转换数据
    distributed_data = MetacognitionDistributedAdapter.metacognition_to_distributed(metacognition_data)
    logger.info(f"元认知系统数据: {metacognition_data}")
    logger.info(f"转换后的分布式系统数据: {distributed_data}")
    
    # 执行元认知到分布式的连接器
    metacognition_to_distributed_connector_id = metacognition_distributed_ids["metacognition_to_distributed_connector_id"]
    transformed_data = integration_manager.execute_connector(
        connector_id=metacognition_to_distributed_connector_id,
        input_data=metacognition_data
    )
    logger.info(f"执行连接器后的数据: {transformed_data}")
    
    # 测试全系统集成
    logger.info("\n测试全系统集成...")
    
    # 创建事件
    event_data = {
        "action": "update",
        "parameters": {
            "learning_rate": 0.001,
            "batch_size": 32
        }
    }
    
    # 发布事件
    event = integration_manager.publish_event(
        event_type="morphism_event",
        source_component_id=full_integration_ids["morphism_component_id"],
        data=event_data
    )
    logger.info(f"发布事件: {event}")
    
    # 获取统计信息
    logger.info("\n获取统计信息...")
    stats = integration_manager.get_stats()
    logger.info(f"系统集成管理器统计信息: {stats}")
    
    logger.info("示例完成")

if __name__ == "__main__":
    main()
