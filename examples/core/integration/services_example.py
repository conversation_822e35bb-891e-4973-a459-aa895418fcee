"""
集成服务示例

本示例展示了如何使用集成服务模块，包括数据转换服务、协议转换服务、服务协调器和服务管道。
"""

import time
import logging
import numpy as np
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable

from src.core.integration import (
    ServiceType, ServiceStatus, ServiceConfig, Service,
    DataTransformer, ProtocolConverter,
    ServiceCoordinator, ServicePipeline, PipelineStage,
    ServiceFactory
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    logger.info("集成服务示例")
    
    # 创建数据转换服务
    logger.info("\n创建数据转换服务...")
    
    # 定义转换函数
    def normalize_data(data: Any) -> Any:
        """归一化数据"""
        if isinstance(data, np.ndarray):
            return (data - np.mean(data)) / np.std(data)
        return data
    
    # 创建数据转换服务
    data_transformer = ServiceFactory.create_data_transformer(
        transform_function=normalize_data,
        metadata={"description": "数据归一化服务"}
    )
    logger.info(f"数据转换服务: {data_transformer}")
    
    # 添加转换规则
    data_transformer.add_transform_rule(
        rule_id="array_to_list",
        source_type="numpy.ndarray",
        target_type="list",
        rule_function=lambda x: x.tolist() if isinstance(x, np.ndarray) else x,
        metadata={"description": "数组转列表"}
    )
    
    data_transformer.add_transform_rule(
        rule_id="list_to_array",
        source_type="list",
        target_type="numpy.ndarray",
        rule_function=lambda x: np.array(x) if isinstance(x, list) else x,
        metadata={"description": "列表转数组"}
    )
    
    # 初始化和启动服务
    data_transformer.initialize()
    data_transformer.start()
    
    # 测试数据转换服务
    logger.info("\n测试数据转换服务...")
    
    # 创建测试数据
    test_data = np.array([1.0, 2.0, 3.0, 4.0, 5.0])
    logger.info(f"原始数据: {test_data}")
    
    # 使用默认转换函数
    normalized_data = data_transformer.transform(test_data)
    logger.info(f"归一化数据: {normalized_data}")
    
    # 使用转换规则
    array_to_list_data = data_transformer.transform(
        input_data=test_data,
        source_type="numpy.ndarray",
        target_type="list"
    )
    logger.info(f"数组转列表: {array_to_list_data}")
    
    # 使用转换规则
    list_to_array_data = data_transformer.transform(
        input_data=array_to_list_data,
        source_type="list",
        target_type="numpy.ndarray"
    )
    logger.info(f"列表转数组: {list_to_array_data}")
    
    # 创建协议转换服务
    logger.info("\n创建协议转换服务...")
    
    # 创建协议转换服务
    protocol_converter = ServiceFactory.create_protocol_converter(
        metadata={"description": "协议转换服务"}
    )
    logger.info(f"协议转换服务: {protocol_converter}")
    
    # 添加协议映射
    protocol_converter.add_protocol_mapping(
        mapping_id="json_to_dict",
        source_protocol="json",
        target_protocol="dict",
        mapping_function=lambda x: eval(x) if isinstance(x, str) else x,
        metadata={"description": "JSON转字典"}
    )
    
    protocol_converter.add_protocol_mapping(
        mapping_id="dict_to_json",
        source_protocol="dict",
        target_protocol="json",
        mapping_function=lambda x: str(x) if isinstance(x, dict) else x,
        metadata={"description": "字典转JSON"}
    )
    
    # 初始化和启动服务
    protocol_converter.initialize()
    protocol_converter.start()
    
    # 测试协议转换服务
    logger.info("\n测试协议转换服务...")
    
    # 创建测试数据
    json_data = "{'name': 'Alice', 'age': 30, 'city': 'New York'}"
    logger.info(f"JSON数据: {json_data}")
    
    # 转换协议
    dict_data = protocol_converter.convert(
        message=json_data,
        source_protocol="json",
        target_protocol="dict"
    )
    logger.info(f"字典数据: {dict_data}")
    
    # 转换协议
    json_data_2 = protocol_converter.convert(
        message=dict_data,
        source_protocol="dict",
        target_protocol="json"
    )
    logger.info(f"JSON数据: {json_data_2}")
    
    # 创建服务管道
    logger.info("\n创建服务管道...")
    
    # 创建服务管道
    service_pipeline = ServiceFactory.create_service_pipeline(
        metadata={"description": "数据处理管道"}
    )
    logger.info(f"服务管道: {service_pipeline}")
    
    # 添加阶段
    service_pipeline.add_stage(
        stage_id="stage_1",
        service=data_transformer,
        metadata={"description": "数据转换阶段"}
    )
    
    service_pipeline.add_stage(
        stage_id="stage_2",
        service=protocol_converter,
        transform_function=lambda x: {"message": x, "convert_params": {"source_protocol": "dict", "target_protocol": "json"}},
        metadata={"description": "协议转换阶段"}
    )
    
    # 初始化和启动服务
    service_pipeline.initialize()
    service_pipeline.start()
    
    # 测试服务管道
    logger.info("\n测试服务管道...")
    
    # 创建测试数据
    pipeline_input = np.array([1.0, 2.0, 3.0, 4.0, 5.0])
    logger.info(f"管道输入: {pipeline_input}")
    
    # 执行管道
    pipeline_output = service_pipeline.execute(pipeline_input)
    logger.info(f"管道输出: {pipeline_output}")
    
    # 创建服务协调器
    logger.info("\n创建服务协调器...")
    
    # 创建服务协调器
    service_coordinator = ServiceFactory.create_service_coordinator(
        metadata={"description": "服务协调器"}
    )
    logger.info(f"服务协调器: {service_coordinator}")
    
    # 注册服务
    service_coordinator.register_service(
        service_id="data_transformer",
        service=data_transformer,
        metadata={"description": "数据转换服务"}
    )
    
    service_coordinator.register_service(
        service_id="protocol_converter",
        service=protocol_converter,
        dependencies=["data_transformer"],
        metadata={"description": "协议转换服务"}
    )
    
    service_coordinator.register_service(
        service_id="service_pipeline",
        service=service_pipeline,
        dependencies=["data_transformer", "protocol_converter"],
        metadata={"description": "服务管道"}
    )
    
    # 初始化和启动服务
    service_coordinator.initialize()
    service_coordinator.start()
    
    # 测试服务协调器
    logger.info("\n测试服务协调器...")
    
    # 获取服务执行顺序
    execution_order = service_coordinator.get_service_execution_order()
    logger.info(f"服务执行顺序: {execution_order}")
    
    # 创建测试数据
    coordinator_input = {"data": np.array([1.0, 2.0, 3.0, 4.0, 5.0])}
    logger.info(f"协调器输入: {coordinator_input}")
    
    # 执行协调器
    coordinator_output = service_coordinator.execute(coordinator_input)
    logger.info(f"协调器输出: {coordinator_output}")
    
    # 停止所有服务
    logger.info("\n停止所有服务...")
    service_coordinator.stop()
    service_pipeline.stop()
    protocol_converter.stop()
    data_transformer.stop()
    
    # 获取统计信息
    logger.info("\n获取统计信息...")
    data_transformer_stats = data_transformer.get_stats()
    protocol_converter_stats = protocol_converter.get_stats()
    service_pipeline_stats = service_pipeline.get_stats()
    service_coordinator_stats = service_coordinator.get_stats()
    
    logger.info(f"数据转换服务统计信息: {data_transformer_stats}")
    logger.info(f"协议转换服务统计信息: {protocol_converter_stats}")
    logger.info(f"服务管道统计信息: {service_pipeline_stats}")
    logger.info(f"服务协调器统计信息: {service_coordinator_stats}")
    
    logger.info("示例完成")

if __name__ == "__main__":
    main()
