"""
态射系统基本示例

本示例展示了如何使用态射系统核心模块创建和使用态射。
"""

import numpy as np
import matplotlib.pyplot as plt

from src.core.morphism import (
    DynamicMorphism, MorphismDomain, MorphismCodomain,
    MorphismComposition, MorphismFeedback, MorphismEvolution,
    MorphismRegistry
)

def main():
    """主函数"""
    print("态射系统基本示例")
    print("=" * 50)
    
    # 创建定义域和值域
    input_domain = MorphismDomain("input_domain", 1)
    output_domain = MorphismCodomain("output_domain", 1)
    
    # 创建线性态射
    def linear_function(x):
        return [2 * x[0] + 1]
    
    linear_morphism = DynamicMorphism(
        "linear",
        input_domain,
        output_domain,
        linear_function,
        {"slope": 2, "intercept": 1}
    )
    
    # 创建二次态射
    def quadratic_function(x):
        return [x[0] ** 2]
    
    quadratic_morphism = DynamicMorphism(
        "quadratic",
        input_domain,
        output_domain,
        quadratic_function,
        {"power": 2}
    )
    
    # 创建正弦态射
    def sine_function(x):
        return [np.sin(x[0])]
    
    sine_morphism = DynamicMorphism(
        "sine",
        input_domain,
        output_domain,
        sine_function
    )
    
    # 获取态射注册表
    registry = MorphismRegistry()
    
    # 注册态射
    registry.register_morphism(linear_morphism)
    registry.register_morphism(quadratic_morphism)
    registry.register_morphism(sine_morphism)
    
    print(f"已注册态射数量: {len(registry.get_all_morphisms())}")
    
    # 创建顺序组合
    sequential_composition = MorphismComposition(
        "sequential",
        [linear_morphism, quadratic_morphism]
    )
    
    # 注册组合
    registry.register_composition(sequential_composition)
    
    print(f"已注册组合数量: {len(registry.get_all_compositions())}")
    
    # 创建反馈
    feedback = MorphismFeedback(
        "direct",
        linear_morphism
    )
    
    # 注册反馈
    registry.register_feedback(feedback)
    
    print(f"已注册反馈数量: {len(registry.get_all_feedbacks())}")
    
    # 创建演化
    evolution = MorphismEvolution(
        "gradual",
        sine_morphism
    )
    
    # 注册演化
    registry.register_evolution(evolution)
    
    print(f"已注册演化数量: {len(registry.get_all_evolutions())}")
    
    # 应用态射
    x_values = np.linspace(-5, 5, 100)
    linear_outputs = []
    quadratic_outputs = []
    sine_outputs = []
    composition_outputs = []
    
    for x in x_values:
        linear_outputs.append(linear_morphism.apply([x])[0])
        quadratic_outputs.append(quadratic_morphism.apply([x])[0])
        sine_outputs.append(sine_morphism.apply([x])[0])
        composition_outputs.append(sequential_composition.apply([x])[0])
    
    # 绘制结果
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 2, 1)
    plt.plot(x_values, linear_outputs)
    plt.title("线性态射")
    plt.grid(True)
    
    plt.subplot(2, 2, 2)
    plt.plot(x_values, quadratic_outputs)
    plt.title("二次态射")
    plt.grid(True)
    
    plt.subplot(2, 2, 3)
    plt.plot(x_values, sine_outputs)
    plt.title("正弦态射")
    plt.grid(True)
    
    plt.subplot(2, 2, 4)
    plt.plot(x_values, composition_outputs)
    plt.title("顺序组合（线性 -> 二次）")
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig("morphism_example.png")
    plt.show()
    
    print("示例完成，结果已保存到 morphism_example.png")
    
    # 演化态射
    print("\n演化态射示例")
    print("-" * 50)
    
    # 创建环境
    environment = {
        "test_data": [np.pi / 4],
        "target_data": [0.8]  # 目标值接近 sin(pi/4) = 0.7071
    }
    
    # 演化态射
    evolved_morphism, evolution_info = evolution.evolve(
        environment,
        parameters={"num_generations": 5, "population_size": 10}
    )
    
    print(f"演化类型: {evolution_info['evolution_type']}")
    print(f"演化代数: {evolution_info['num_generations']}")
    print(f"种群大小: {evolution_info['population_size']}")
    print(f"最终适应度: {evolution_info['final_fitness']}")
    
    # 应用演化后的态射
    evolved_outputs = []
    for x in x_values:
        evolved_outputs.append(evolved_morphism.apply([x])[0])
    
    # 绘制演化结果
    plt.figure(figsize=(10, 6))
    
    plt.subplot(1, 2, 1)
    plt.plot(x_values, sine_outputs, label="原始态射")
    plt.plot(x_values, evolved_outputs, label="演化态射")
    plt.scatter([np.pi / 4], [0.8], color='red', s=100, label="目标点")
    plt.title("态射演化")
    plt.legend()
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    generations = [info["generation"] for info in evolution_info["evolution_history"]]
    best_fitness = [info["best_fitness"] for info in evolution_info["evolution_history"]]
    avg_fitness = [info["average_fitness"] for info in evolution_info["evolution_history"]]
    
    plt.plot(generations, best_fitness, label="最佳适应度")
    plt.plot(generations, avg_fitness, label="平均适应度")
    plt.title("演化过程")
    plt.xlabel("代数")
    plt.ylabel("适应度")
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig("morphism_evolution_example.png")
    plt.show()
    
    print("演化示例完成，结果已保存到 morphism_evolution_example.png")


if __name__ == "__main__":
    main()
