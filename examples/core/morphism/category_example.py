"""
范畴论示例

本示例展示了如何使用范畴论模块。
"""

import logging
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable

from src.core.morphism import (
    CategoryType, MorphismType,
    Object, Morphism, Category, Functor, NaturalTransformation,
    MorphismSystemFactory, MorphismOperations
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    logger.info("范畴论示例")
    
    # 创建集合范畴
    logger.info("创建集合范畴...")
    set_category = MorphismSystemFactory.create_set_category(
        elements=[1, 2, 3, 4, 5],
        metadata={"name": "Set Category Example"}
    )
    logger.info(f"集合范畴: {set_category}")
    logger.info(f"对象数量: {len(set_category.objects)}")
    logger.info(f"态射数量: {len(set_category.morphisms)}")
    
    # 创建群范畴
    logger.info("\n创建群范畴...")
    
    # 定义群操作
    def group_operation(a: int, b: int) -> int:
        return (a + b) % 5
    
    # 定义逆元函数
    def inverse_function(a: int) -> int:
        return (5 - a) % 5
    
    group_category = MorphismSystemFactory.create_group_category(
        elements=[0, 1, 2, 3, 4],
        operation=group_operation,
        identity_element=0,
        inverse_function=inverse_function,
        metadata={"name": "Group Category Example"}
    )
    logger.info(f"群范畴: {group_category}")
    logger.info(f"对象数量: {len(group_category.objects)}")
    logger.info(f"态射数量: {len(group_category.morphisms)}")
    
    # 创建偏序集范畴
    logger.info("\n创建偏序集范畴...")
    
    # 定义偏序关系
    def poset_relation(a: int, b: int) -> bool:
        return a <= b
    
    poset_category = MorphismSystemFactory.create_poset_category(
        elements=[1, 2, 3, 4, 5],
        relation=poset_relation,
        metadata={"name": "Poset Category Example"}
    )
    logger.info(f"偏序集范畴: {poset_category}")
    logger.info(f"对象数量: {len(poset_category.objects)}")
    logger.info(f"态射数量: {len(poset_category.morphisms)}")
    
    # 创建积范畴
    logger.info("\n创建积范畴...")
    product_category = MorphismOperations.create_product_category(
        category1=set_category,
        category2=poset_category
    )
    logger.info(f"积范畴: {product_category}")
    logger.info(f"对象数量: {len(product_category.objects)}")
    logger.info(f"态射数量: {len(product_category.morphisms)}")
    
    # 创建余积范畴
    logger.info("\n创建余积范畴...")
    coproduct_category = MorphismOperations.create_coproduct_category(
        category1=set_category,
        category2=poset_category
    )
    logger.info(f"余积范畴: {coproduct_category}")
    logger.info(f"对象数量: {len(coproduct_category.objects)}")
    logger.info(f"态射数量: {len(coproduct_category.morphisms)}")
    
    # 创建函子
    logger.info("\n创建函子...")
    
    # 创建对象映射
    object_mapping = {}
    for obj_id, obj in set_category.objects.items():
        # 映射到偏序集范畴中的对象
        for poset_obj_id, poset_obj in poset_category.objects.items():
            if obj.data == poset_obj.data:
                object_mapping[obj_id] = poset_obj_id
                break
    
    # 创建态射映射
    morphism_mapping = {}
    for morphism_id, morphism in set_category.morphisms.items():
        # 映射到偏序集范畴中的态射
        source_id = morphism.source.object_id
        target_id = morphism.target.object_id
        
        # 获取映射后的源对象和目标对象
        mapped_source_id = object_mapping.get(source_id)
        mapped_target_id = object_mapping.get(target_id)
        
        if mapped_source_id is not None and mapped_target_id is not None:
            # 查找对应的态射
            for poset_morphism_id, poset_morphism in poset_category.morphisms.items():
                if (poset_morphism.source.object_id == mapped_source_id and 
                    poset_morphism.target.object_id == mapped_target_id):
                    morphism_mapping[morphism_id] = poset_morphism_id
                    break
    
    # 创建函子
    functor = MorphismSystemFactory.create_functor(
        source_category=set_category,
        target_category=poset_category,
        object_mapping=object_mapping,
        morphism_mapping=morphism_mapping,
        metadata={"name": "Set to Poset Functor"}
    )
    logger.info(f"函子: {functor}")
    logger.info(f"对象映射数量: {len(functor.object_mapping)}")
    logger.info(f"态射映射数量: {len(functor.morphism_mapping)}")
    
    # 创建自然变换
    logger.info("\n创建自然变换...")
    
    # 创建另一个函子
    functor2 = MorphismSystemFactory.create_functor(
        source_category=set_category,
        target_category=poset_category,
        object_mapping=object_mapping,
        morphism_mapping=morphism_mapping,
        metadata={"name": "Another Set to Poset Functor"}
    )
    
    # 创建组件映射
    component_mapping = {}
    for obj_id in set_category.objects:
        # 获取映射后的对象
        mapped_obj_id = object_mapping.get(obj_id)
        
        if mapped_obj_id is not None:
            # 获取恒等态射
            identity = poset_category.get_identity_morphism(mapped_obj_id)
            
            if identity is not None:
                component_mapping[obj_id] = identity.morphism_id
    
    # 创建自然变换
    transformation = MorphismSystemFactory.create_natural_transformation(
        source_functor=functor,
        target_functor=functor2,
        component_mapping=component_mapping,
        metadata={"name": "Natural Transformation Example"}
    )
    logger.info(f"自然变换: {transformation}")
    logger.info(f"组件映射数量: {len(transformation.component_mapping)}")
    
    # 查找路径
    logger.info("\n查找路径...")
    
    # 获取偏序集范畴中的对象
    obj1 = None
    obj5 = None
    
    for obj_id, obj in poset_category.objects.items():
        if obj.data == 1:
            obj1 = obj
        elif obj.data == 5:
            obj5 = obj
    
    if obj1 is not None and obj5 is not None:
        # 查找从1到5的路径
        path = MorphismOperations.find_path(obj1.object_id, obj5.object_id, poset_category)
        
        if path is not None:
            logger.info(f"找到从1到5的路径，长度为: {len(path)}")
            
            # 组合路径上的态射
            composed_morphism = MorphismOperations.compose_morphisms(path)
            
            if composed_morphism is not None:
                logger.info(f"组合后的态射: {composed_morphism}")
        else:
            logger.info("没有找到从1到5的路径")
    
    logger.info("示例完成")

if __name__ == "__main__":
    main()
