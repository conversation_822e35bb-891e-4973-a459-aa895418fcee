"""
态射反馈示例

本示例展示了如何使用态射反馈模块。
"""

import numpy as np
import logging
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable

from src.core.morphism import (
    DynamicMorphism, DynamicMorphismFactory, DynamicMorphismManager,
    MorphismDomain, MorphismCodomain,
    MorphismFeedback, FeedbackStrategy, DirectFeedbackStrategy, GradientFeedbackStrategy,
    FeedbackFactory, FeedbackManager
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    logger.info("态射反馈示例")
    
    # 创建动态态射管理器
    logger.info("创建动态态射管理器...")
    morphism_manager = DynamicMorphismManager(manager_id="morphism_manager")
    
    # 创建态射反馈管理器
    logger.info("创建态射反馈管理器...")
    feedback_manager = FeedbackManager(manager_id="feedback_manager")
    
    # 创建线性态射
    logger.info("\n创建线性态射...")
    linear_morphism = morphism_manager.create_linear_morphism(
        domain_name="linear_domain",
        codomain_name="linear_codomain",
        input_dimension=2,
        output_dimension=1,
        weight_matrix=np.array([[0.5, 0.5]]),  # 初始权重
        bias_vector=np.array([0.0]),
        domain_parameters={"description": "2D输入空间"},
        codomain_parameters={"description": "1D输出空间"},
        morphism_parameters={"description": "线性变换"}
    )
    logger.info(f"线性态射: {linear_morphism}")
    
    # 测试线性态射
    input_data = np.array([1.0, 2.0])
    output_data = morphism_manager.apply_morphism(linear_morphism.id, input_data)
    logger.info(f"输入: {input_data}")
    logger.info(f"输出: {output_data}")
    
    # 创建直接反馈
    logger.info("\n创建直接反馈...")
    direct_feedback = feedback_manager.create_direct_feedback(
        morphism=linear_morphism,
        parameters={"description": "直接反馈"}
    )
    logger.info(f"直接反馈: {direct_feedback}")
    
    # 应用直接反馈
    input_data = np.array([1.0, 2.0])
    target_data = np.array([2.0])  # 目标输出
    output_data, feedback_info = feedback_manager.apply_feedback(
        feedback_id=direct_feedback.id,
        input_data=input_data,
        target_data=target_data
    )
    logger.info(f"输入: {input_data}")
    logger.info(f"输出: {output_data}")
    logger.info(f"目标: {target_data}")
    logger.info(f"反馈信息: {feedback_info}")
    
    # 创建梯度反馈
    logger.info("\n创建梯度反馈...")
    gradient_feedback = feedback_manager.create_gradient_feedback(
        morphism=linear_morphism,
        learning_rate=0.1,
        parameters={"description": "梯度反馈"}
    )
    logger.info(f"梯度反馈: {gradient_feedback}")
    
    # 训练线性态射
    logger.info("\n训练线性态射...")
    
    # 创建训练数据
    X_train = np.array([[1.0, 2.0], [2.0, 3.0], [3.0, 4.0], [4.0, 5.0]])
    y_train = np.array([[3.0], [5.0], [7.0], [9.0]])  # 目标函数: y = x1 + x2 + 1
    
    # 训练参数
    epochs = 100
    batch_size = 1
    
    # 训练循环
    for epoch in range(epochs):
        epoch_loss = 0.0
        
        # 随机打乱数据
        indices = np.random.permutation(len(X_train))
        X_shuffled = X_train[indices]
        y_shuffled = y_train[indices]
        
        # 批量训练
        for i in range(0, len(X_train), batch_size):
            # 获取批次数据
            X_batch = X_shuffled[i:i+batch_size]
            y_batch = y_shuffled[i:i+batch_size]
            
            # 对每个样本应用反馈
            for j in range(len(X_batch)):
                # 应用反馈
                output, feedback_info = feedback_manager.apply_feedback(
                    feedback_id=gradient_feedback.id,
                    input_data=X_batch[j],
                    target_data=y_batch[j]
                )
                
                # 计算损失
                loss = np.mean((output - y_batch[j])**2)
                epoch_loss += loss
        
        # 计算平均损失
        epoch_loss /= len(X_train)
        
        # 每10个epoch打印一次损失
        if (epoch + 1) % 10 == 0:
            logger.info(f"Epoch {epoch + 1}/{epochs}, Loss: {epoch_loss:.6f}")
    
    # 测试训练后的线性态射
    logger.info("\n测试训练后的线性态射...")
    
    # 创建测试数据
    X_test = np.array([[5.0, 6.0], [6.0, 7.0], [7.0, 8.0], [8.0, 9.0]])
    y_test = np.array([[11.0], [13.0], [15.0], [17.0]])  # 目标函数: y = x1 + x2 + 1
    
    # 测试
    test_loss = 0.0
    for i in range(len(X_test)):
        # 应用态射
        output = morphism_manager.apply_morphism(linear_morphism.id, X_test[i])
        
        # 计算损失
        loss = np.mean((output - y_test[i])**2)
        test_loss += loss
        
        logger.info(f"输入: {X_test[i]}, 输出: {output}, 目标: {y_test[i]}, 损失: {loss:.6f}")
    
    # 计算平均损失
    test_loss /= len(X_test)
    logger.info(f"测试损失: {test_loss:.6f}")
    
    # 查看训练后的权重
    logger.info(f"训练后的权重: {linear_morphism.parameters['weight_matrix']}")
    logger.info(f"训练后的偏置: {linear_morphism.parameters['bias_vector']}")
    
    # 创建自适应反馈
    logger.info("\n创建自适应反馈...")
    adaptive_feedback = feedback_manager.create_adaptive_feedback(
        morphism=linear_morphism,
        initial_learning_rate=0.1,
        decay_rate=0.95,
        min_learning_rate=0.001,
        parameters={"description": "自适应反馈"}
    )
    logger.info(f"自适应反馈: {adaptive_feedback}")
    
    # 创建动量反馈
    logger.info("\n创建动量反馈...")
    momentum_feedback = feedback_manager.create_momentum_feedback(
        morphism=linear_morphism,
        learning_rate=0.05,
        momentum=0.9,
        parameters={"description": "动量反馈"}
    )
    logger.info(f"动量反馈: {momentum_feedback}")
    
    # 获取统计信息
    logger.info("\n获取统计信息...")
    morphism_stats = morphism_manager.get_stats()
    feedback_stats = feedback_manager.get_stats()
    logger.info(f"态射管理器统计信息: {morphism_stats}")
    logger.info(f"反馈管理器统计信息: {feedback_stats}")
    
    logger.info("示例完成")

if __name__ == "__main__":
    main()
