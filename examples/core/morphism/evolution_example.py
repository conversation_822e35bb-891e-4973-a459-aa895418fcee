"""
态射演化示例

本示例展示了如何使用态射演化模块。
"""

import numpy as np
import logging
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable

from src.core.morphism import (
    DynamicMorphism, DynamicMorphismFactory, DynamicMorphismManager,
    MorphismDomain, MorphismCodomain,
    MorphismEvolution, EvolutionStrategy, GradualEvolutionStrategy, DisruptiveEvolutionStrategy,
    EvolutionFactory, EvolutionManager
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    logger.info("态射演化示例")
    
    # 创建动态态射管理器
    logger.info("创建动态态射管理器...")
    morphism_manager = DynamicMorphismManager(manager_id="morphism_manager")
    
    # 创建态射演化管理器
    logger.info("创建态射演化管理器...")
    evolution_manager = EvolutionManager(manager_id="evolution_manager")
    
    # 创建线性态射
    logger.info("\n创建线性态射...")
    linear_morphism = morphism_manager.create_linear_morphism(
        domain_name="linear_domain",
        codomain_name="linear_codomain",
        input_dimension=2,
        output_dimension=1,
        weight_matrix=np.array([[0.5, 0.5]]),  # 初始权重
        bias_vector=np.array([0.0]),
        domain_parameters={"description": "2D输入空间"},
        codomain_parameters={"description": "1D输出空间"},
        morphism_parameters={"description": "线性变换"}
    )
    logger.info(f"线性态射: {linear_morphism}")
    
    # 测试线性态射
    input_data = np.array([1.0, 2.0])
    output_data = morphism_manager.apply_morphism(linear_morphism.id, input_data)
    logger.info(f"输入: {input_data}")
    logger.info(f"输出: {output_data}")
    
    # 创建渐进演化
    logger.info("\n创建渐进演化...")
    gradual_evolution = evolution_manager.create_gradual_evolution(
        morphism=linear_morphism,
        parameters={"description": "渐进演化"}
    )
    logger.info(f"渐进演化: {gradual_evolution}")
    
    # 定义适应度函数
    def fitness_function(morphism: DynamicMorphism, environment: Dict[str, Any]) -> float:
        """
        适应度函数
        
        参数:
            morphism (DynamicMorphism): 态射
            environment (Dict[str, Any]): 环境信息
            
        返回:
            float: 适应度值
        """
        # 获取训练数据
        X_train = environment.get("X_train")
        y_train = environment.get("y_train")
        
        if X_train is None or y_train is None:
            return 0.0
        
        # 计算预测值
        y_pred = []
        for x in X_train:
            try:
                y = morphism.apply(x)
                y_pred.append(y)
            except Exception as e:
                logger.error(f"预测失败: {str(e)}")
                return 0.0
        
        # 计算均方误差
        mse = np.mean([(y_pred[i] - y_train[i])**2 for i in range(len(y_train))])
        
        # 返回适应度值（负的均方误差，因为适应度越高越好）
        return -mse
    
    # 创建环境
    environment = {
        "X_train": np.array([[1.0, 2.0], [2.0, 3.0], [3.0, 4.0], [4.0, 5.0]]),
        "y_train": np.array([3.0, 5.0, 7.0, 9.0])  # 目标函数: y = x1 + x2 + 1
    }
    
    # 演化态射
    logger.info("\n演化态射（渐进演化）...")
    for i in range(10):
        # 演化态射
        evolved_morphism, evolution_info = evolution_manager.evolve(
            evolution_id=gradual_evolution.id,
            environment=environment,
            fitness_function=fitness_function
        )
        
        # 打印演化信息
        logger.info(f"演化 {i+1}: 适应度 = {evolution_info.get('final_fitness', 'N/A')}")
        logger.info(f"权重: {evolved_morphism.parameters['weight_matrix']}")
        logger.info(f"偏置: {evolved_morphism.parameters['bias_vector']}")
    
    # 测试演化后的态射
    logger.info("\n测试演化后的态射...")
    
    # 创建测试数据
    X_test = np.array([[5.0, 6.0], [6.0, 7.0], [7.0, 8.0], [8.0, 9.0]])
    y_test = np.array([11.0, 13.0, 15.0, 17.0])  # 目标函数: y = x1 + x2 + 1
    
    # 测试
    test_loss = 0.0
    for i in range(len(X_test)):
        # 应用态射
        output = morphism_manager.apply_morphism(linear_morphism.id, X_test[i])
        
        # 计算损失
        loss = np.mean((output - y_test[i])**2)
        test_loss += loss
        
        logger.info(f"输入: {X_test[i]}, 输出: {output}, 目标: {y_test[i]}, 损失: {loss:.6f}")
    
    # 计算平均损失
    test_loss /= len(X_test)
    logger.info(f"测试损失: {test_loss:.6f}")
    
    # 创建突变演化
    logger.info("\n创建突变演化...")
    disruptive_evolution = evolution_manager.create_disruptive_evolution(
        morphism=linear_morphism,
        mutation_rate=0.2,
        parameters={"description": "突变演化"}
    )
    logger.info(f"突变演化: {disruptive_evolution}")
    
    # 演化态射
    logger.info("\n演化态射（突变演化）...")
    for i in range(10):
        # 演化态射
        evolved_morphism, evolution_info = evolution_manager.evolve(
            evolution_id=disruptive_evolution.id,
            environment=environment,
            fitness_function=fitness_function
        )
        
        # 打印演化信息
        logger.info(f"演化 {i+1}: 适应度 = {evolution_info.get('final_fitness', 'N/A')}")
        logger.info(f"权重: {evolved_morphism.parameters['weight_matrix']}")
        logger.info(f"偏置: {evolved_morphism.parameters['bias_vector']}")
    
    # 测试演化后的态射
    logger.info("\n测试演化后的态射...")
    
    # 测试
    test_loss = 0.0
    for i in range(len(X_test)):
        # 应用态射
        output = morphism_manager.apply_morphism(linear_morphism.id, X_test[i])
        
        # 计算损失
        loss = np.mean((output - y_test[i])**2)
        test_loss += loss
        
        logger.info(f"输入: {X_test[i]}, 输出: {output}, 目标: {y_test[i]}, 损失: {loss:.6f}")
    
    # 计算平均损失
    test_loss /= len(X_test)
    logger.info(f"测试损失: {test_loss:.6f}")
    
    # 创建自适应演化
    logger.info("\n创建自适应演化...")
    adaptive_evolution = evolution_manager.create_adaptive_evolution(
        morphism=linear_morphism,
        initial_mutation_rate=0.2,
        decay_rate=0.9,
        min_mutation_rate=0.01,
        parameters={"description": "自适应演化"}
    )
    logger.info(f"自适应演化: {adaptive_evolution}")
    
    # 创建混合演化
    logger.info("\n创建混合演化...")
    hybrid_evolution = evolution_manager.create_hybrid_evolution(
        morphism=linear_morphism,
        parameters={"description": "混合演化", "stagnation_threshold": 3}
    )
    logger.info(f"混合演化: {hybrid_evolution}")
    
    # 演化态射
    logger.info("\n演化态射（混合演化）...")
    for i in range(20):
        # 演化态射
        evolved_morphism, evolution_info = evolution_manager.evolve(
            evolution_id=hybrid_evolution.id,
            environment=environment,
            fitness_function=fitness_function
        )
        
        # 打印演化信息
        logger.info(f"演化 {i+1}: 适应度 = {evolution_info.get('final_fitness', 'N/A')}, 策略 = {evolution_info.get('strategy', 'N/A')}")
        logger.info(f"权重: {evolved_morphism.parameters['weight_matrix']}")
        logger.info(f"偏置: {evolved_morphism.parameters['bias_vector']}")
    
    # 测试演化后的态射
    logger.info("\n测试演化后的态射...")
    
    # 测试
    test_loss = 0.0
    for i in range(len(X_test)):
        # 应用态射
        output = morphism_manager.apply_morphism(linear_morphism.id, X_test[i])
        
        # 计算损失
        loss = np.mean((output - y_test[i])**2)
        test_loss += loss
        
        logger.info(f"输入: {X_test[i]}, 输出: {output}, 目标: {y_test[i]}, 损失: {loss:.6f}")
    
    # 计算平均损失
    test_loss /= len(X_test)
    logger.info(f"测试损失: {test_loss:.6f}")
    
    # 获取统计信息
    logger.info("\n获取统计信息...")
    morphism_stats = morphism_manager.get_stats()
    evolution_stats = evolution_manager.get_stats()
    logger.info(f"态射管理器统计信息: {morphism_stats}")
    logger.info(f"演化管理器统计信息: {evolution_stats}")
    
    logger.info("示例完成")

if __name__ == "__main__":
    main()
