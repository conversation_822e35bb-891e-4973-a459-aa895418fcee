"""
动态态射示例

本示例展示了如何使用动态态射模块。
"""

import numpy as np
import logging
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable

from src.core.morphism import (
    DynamicMorphism, DynamicMorphismFactory, DynamicMorphismManager,
    MorphismDomain, MorphismCodomain
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    logger.info("动态态射示例")
    
    # 创建动态态射管理器
    logger.info("创建动态态射管理器...")
    manager = DynamicMorphismManager(manager_id="example_manager")
    
    # 创建恒等态射
    logger.info("\n创建恒等态射...")
    identity_morphism = manager.create_identity_morphism(
        domain_name="identity_domain",
        dimension=3,
        domain_parameters={"description": "3D向量空间"},
        morphism_parameters={"description": "恒等映射"}
    )
    logger.info(f"恒等态射: {identity_morphism}")
    
    # 测试恒等态射
    input_data = np.array([1.0, 2.0, 3.0])
    output_data = manager.apply_morphism(identity_morphism.id, input_data)
    logger.info(f"输入: {input_data}")
    logger.info(f"输出: {output_data}")
    
    # 创建线性态射
    logger.info("\n创建线性态射...")
    linear_morphism = manager.create_linear_morphism(
        domain_name="linear_domain",
        codomain_name="linear_codomain",
        input_dimension=3,
        output_dimension=2,
        weight_matrix=np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]]),
        bias_vector=np.array([0.1, 0.2]),
        domain_parameters={"description": "3D输入空间"},
        codomain_parameters={"description": "2D输出空间"},
        morphism_parameters={"description": "线性变换"}
    )
    logger.info(f"线性态射: {linear_morphism}")
    
    # 测试线性态射
    input_data = np.array([1.0, 2.0, 3.0])
    output_data = manager.apply_morphism(linear_morphism.id, input_data)
    logger.info(f"输入: {input_data}")
    logger.info(f"输出: {output_data}")
    
    # 创建非线性态射
    logger.info("\n创建非线性态射...")
    
    # 定义激活函数
    def relu(x):
        return np.maximum(0, x)
    
    nonlinear_morphism = manager.create_nonlinear_morphism(
        domain_name="nonlinear_domain",
        codomain_name="nonlinear_codomain",
        input_dimension=3,
        output_dimension=2,
        activation_function=relu,
        weight_matrix=np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]]),
        bias_vector=np.array([-10.0, -5.0]),  # 使用负偏置以测试ReLU
        domain_parameters={"description": "3D输入空间"},
        codomain_parameters={"description": "2D输出空间"},
        morphism_parameters={"description": "非线性变换"}
    )
    logger.info(f"非线性态射: {nonlinear_morphism}")
    
    # 测试非线性态射
    input_data = np.array([1.0, 2.0, 3.0])
    output_data = manager.apply_morphism(nonlinear_morphism.id, input_data)
    logger.info(f"输入: {input_data}")
    logger.info(f"输出: {output_data}")
    
    # 创建复合态射
    logger.info("\n创建复合态射...")
    
    # 创建另一个线性态射
    linear_morphism2 = manager.create_linear_morphism(
        domain_name="linear_domain2",
        codomain_name="linear_codomain2",
        input_dimension=2,
        output_dimension=1,
        weight_matrix=np.array([[1.0, 1.0]]),
        bias_vector=np.array([0.0]),
        domain_parameters={"description": "2D输入空间"},
        codomain_parameters={"description": "1D输出空间"},
        morphism_parameters={"description": "线性变换2"}
    )
    logger.info(f"线性态射2: {linear_morphism2}")
    
    # 创建复合态射
    composite_morphism = manager.create_composite_morphism(
        morphism_ids=[linear_morphism2.id, linear_morphism.id],
        morphism_parameters={"description": "复合变换"}
    )
    logger.info(f"复合态射: {composite_morphism}")
    
    # 测试复合态射
    input_data = np.array([1.0, 2.0, 3.0])
    output_data = manager.apply_morphism(composite_morphism.id, input_data)
    logger.info(f"输入: {input_data}")
    logger.info(f"输出: {output_data}")
    
    # 创建并行态射
    logger.info("\n创建并行态射...")
    parallel_morphism = manager.create_parallel_morphism(
        morphism_ids=[linear_morphism.id, nonlinear_morphism.id],
        morphism_parameters={"description": "并行变换"}
    )
    logger.info(f"并行态射: {parallel_morphism}")
    
    # 测试并行态射
    input_data = np.concatenate([np.array([1.0, 2.0, 3.0]), np.array([1.0, 2.0, 3.0])])
    output_data = manager.apply_morphism(parallel_morphism.id, input_data)
    logger.info(f"输入: {input_data}")
    logger.info(f"输出: {output_data}")
    
    # 创建自定义态射
    logger.info("\n创建自定义态射...")
    
    # 定义自定义映射函数
    def custom_mapping(x):
        # 计算输入的平方和
        if not isinstance(x, np.ndarray):
            x = np.array(x)
        
        # 确保输入是一维数组
        if x.ndim > 1:
            x = x.flatten()
        
        # 计算平方和
        return np.array([np.sum(x**2)])
    
    custom_morphism = manager.create_custom_morphism(
        domain_name="custom_domain",
        codomain_name="custom_codomain",
        input_dimension=3,
        output_dimension=1,
        mapping_function=custom_mapping,
        domain_parameters={"description": "3D输入空间"},
        codomain_parameters={"description": "1D输出空间"},
        morphism_parameters={"description": "自定义变换"}
    )
    logger.info(f"自定义态射: {custom_morphism}")
    
    # 测试自定义态射
    input_data = np.array([1.0, 2.0, 3.0])
    output_data = manager.apply_morphism(custom_morphism.id, input_data)
    logger.info(f"输入: {input_data}")
    logger.info(f"输出: {output_data}")
    
    # 更新态射
    logger.info("\n更新态射...")
    
    # 更新线性态射的参数
    manager.update_morphism(
        morphism_id=linear_morphism.id,
        parameters={"description": "更新后的线性变换", "learning_rate": 0.01}
    )
    logger.info(f"更新后的线性态射: {manager.get_morphism(linear_morphism.id)}")
    
    # 更新非线性态射的映射函数
    def sigmoid(x):
        return 1 / (1 + np.exp(-x))
    
    manager.update_morphism(
        morphism_id=nonlinear_morphism.id,
        mapping_function=lambda x: sigmoid(np.dot(nonlinear_morphism.parameters["weight_matrix"], x) + nonlinear_morphism.parameters["bias_vector"]),
        parameters={"description": "更新后的非线性变换", "activation": "sigmoid"}
    )
    logger.info(f"更新后的非线性态射: {manager.get_morphism(nonlinear_morphism.id)}")
    
    # 测试更新后的非线性态射
    input_data = np.array([1.0, 2.0, 3.0])
    output_data = manager.apply_morphism(nonlinear_morphism.id, input_data)
    logger.info(f"输入: {input_data}")
    logger.info(f"输出: {output_data}")
    
    # 删除态射
    logger.info("\n删除态射...")
    manager.delete_morphism(custom_morphism.id)
    logger.info(f"删除自定义态射: {custom_morphism.id}")
    
    # 获取统计信息
    logger.info("\n获取统计信息...")
    stats = manager.get_stats()
    logger.info(f"统计信息: {stats}")
    
    logger.info("示例完成")

if __name__ == "__main__":
    main()
