"""
算子性能优化示例

本示例展示了如何使用算子性能优化功能，包括算子缓存、懒加载和并行处理。
"""

import os
import sys
import time
import logging
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Union

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入算子注册表模块
try:
    from src.rust_bindings import (
        OperatorCategory,
        OperatorRegistry,
        get_global_registry,
        register_operator,
        get_operator,
    )
    from src.rust_bindings.operator_registry.performance import (
        OperatorCache,
        LazyOperator,
        ParallelExecutor,
        cached_operator,
        parallel_operator,
        get_global_operator_cache,
    )
    logger.info(f"成功导入算子注册表模块")
except ImportError as e:
    logger.error(f"导入算子注册表模块失败: {e}")
    sys.exit(1)

# 定义一些示例算子
def add(a, b):
    """加法算子"""
    time.sleep(0.01)  # 模拟耗时操作
    return a + b

def multiply(a, b):
    """乘法算子"""
    time.sleep(0.01)  # 模拟耗时操作
    return a * b

def matrix_multiply(a, b):
    """矩阵乘法算子"""
    time.sleep(0.01)  # 模拟耗时操作
    return np.matmul(a, b)

@cached_operator
def cached_add(a, b):
    """缓存加法算子"""
    time.sleep(0.01)  # 模拟耗时操作
    return a + b

@parallel_operator
def parallel_add(a, b):
    """并行加法算子"""
    time.sleep(0.01)  # 模拟耗时操作
    return a + b

def test_operator_cache():
    """测试算子缓存"""
    logger.info("开始测试算子缓存...")
    
    # 创建算子缓存
    cache = OperatorCache(max_size=10, ttl=60)
    
    # 缓存算子实例
    cache.set_instance(OperatorCategory.UTILITY.value, "add", add)
    
    # 获取算子实例
    add_op = cache.get_instance(OperatorCategory.UTILITY.value, "add")
    logger.info(f"从缓存获取算子: {add_op}")
    
    # 测试算子
    start_time = time.time()
    result = add_op(1, 2)
    end_time = time.time()
    logger.info(f"1 + 2 = {result}，耗时: {end_time - start_time:.6f}秒")
    
    # 缓存算子结果
    cache.set_result(OperatorCategory.UTILITY.value, "add", (1, 2), (), result)
    
    # 获取算子结果
    start_time = time.time()
    cached_result = cache.get_result(OperatorCategory.UTILITY.value, "add", (1, 2), ())
    end_time = time.time()
    logger.info(f"从缓存获取结果: {cached_result}，耗时: {end_time - start_time:.6f}秒")
    
    # 获取缓存统计信息
    stats = cache.get_stats()
    logger.info(f"缓存统计信息: {stats}")
    
    # 清空缓存
    cache.clear()
    logger.info(f"清空缓存后的统计信息: {cache.get_stats()}")
    
    logger.info("算子缓存测试完成")

def test_global_operator_cache():
    """测试全局算子缓存"""
    logger.info("开始测试全局算子缓存...")
    
    # 获取全局算子缓存
    cache = get_global_operator_cache()
    
    # 缓存算子实例
    cache.set_instance(OperatorCategory.UTILITY.value, "multiply", multiply)
    
    # 获取算子实例
    multiply_op = cache.get_instance(OperatorCategory.UTILITY.value, "multiply")
    logger.info(f"从全局缓存获取算子: {multiply_op}")
    
    # 测试算子
    start_time = time.time()
    result = multiply_op(2, 3)
    end_time = time.time()
    logger.info(f"2 * 3 = {result}，耗时: {end_time - start_time:.6f}秒")
    
    # 缓存算子结果
    cache.set_result(OperatorCategory.UTILITY.value, "multiply", (2, 3), (), result)
    
    # 获取算子结果
    start_time = time.time()
    cached_result = cache.get_result(OperatorCategory.UTILITY.value, "multiply", (2, 3), ())
    end_time = time.time()
    logger.info(f"从全局缓存获取结果: {cached_result}，耗时: {end_time - start_time:.6f}秒")
    
    logger.info("全局算子缓存测试完成")

def test_lazy_operator():
    """测试懒加载算子"""
    logger.info("开始测试懒加载算子...")
    
    # 获取全局注册表
    registry = get_global_registry()
    
    # 注册算子
    register_operator(
        OperatorCategory.UTILITY,
        "add",
        add,
        "1.0.0",
        "加法算子",
        ["math", "basic"],
        [],
    )
    
    # 创建懒加载算子
    lazy_add = LazyOperator(registry, OperatorCategory.UTILITY.value, "add")
    
    # 测试懒加载算子
    logger.info(f"懒加载算子: {lazy_add}")
    
    # 调用懒加载算子
    start_time = time.time()
    result = lazy_add(1, 2)
    end_time = time.time()
    logger.info(f"1 + 2 = {result}，耗时: {end_time - start_time:.6f}秒")
    
    logger.info("懒加载算子测试完成")

def test_parallel_executor():
    """测试并行执行器"""
    logger.info("开始测试并行执行器...")
    
    # 创建并行执行器
    with ParallelExecutor(max_workers=4) as executor:
        # 提交任务
        future1 = executor.submit(add, 1, 2)
        future2 = executor.submit(multiply, 2, 3)
        
        # 获取结果
        result1 = future1.result()
        result2 = future2.result()
        
        logger.info(f"1 + 2 = {result1}")
        logger.info(f"2 * 3 = {result2}")
        
        # 映射任务
        numbers = [1, 2, 3, 4, 5]
        squares = executor.map(lambda x: x * x, numbers)
        
        logger.info(f"平方: {squares}")
    
    logger.info("并行执行器测试完成")

def test_cached_operator():
    """测试缓存算子装饰器"""
    logger.info("开始测试缓存算子装饰器...")
    
    # 第一次调用
    start_time = time.time()
    result1 = cached_add(1, 2)
    end_time = time.time()
    logger.info(f"1 + 2 = {result1}，耗时: {end_time - start_time:.6f}秒")
    
    # 第二次调用（应该从缓存获取）
    start_time = time.time()
    result2 = cached_add(1, 2)
    end_time = time.time()
    logger.info(f"1 + 2 = {result2}，耗时: {end_time - start_time:.6f}秒")
    
    # 不同参数的调用
    start_time = time.time()
    result3 = cached_add(2, 3)
    end_time = time.time()
    logger.info(f"2 + 3 = {result3}，耗时: {end_time - start_time:.6f}秒")
    
    logger.info("缓存算子装饰器测试完成")

def test_parallel_operator():
    """测试并行算子装饰器"""
    logger.info("开始测试并行算子装饰器...")
    
    # 创建测试数据
    a = np.array([1, 2, 3, 4, 5])
    b = np.array([6, 7, 8, 9, 10])
    
    # 调用并行算子
    start_time = time.time()
    result = parallel_add(a, b)
    end_time = time.time()
    
    logger.info(f"结果: {result}")
    logger.info(f"耗时: {end_time - start_time:.6f}秒")
    
    # 比较与非并行版本的性能
    start_time = time.time()
    result2 = add(a, b)
    end_time = time.time()
    
    logger.info(f"非并行结果: {result2}")
    logger.info(f"非并行耗时: {end_time - start_time:.6f}秒")
    
    logger.info("并行算子装饰器测试完成")

def test_integration_with_registry():
    """测试与注册表的集成"""
    logger.info("开始测试与注册表的集成...")
    
    # 获取全局注册表
    registry = get_global_registry()
    
    # 获取全局算子缓存
    cache = get_global_operator_cache()
    
    # 注册算子
    register_operator(
        OperatorCategory.NUMPY,
        "matrix_multiply",
        matrix_multiply,
        "1.0.0",
        "矩阵乘法算子",
        ["math", "matrix"],
        [],
    )
    
    # 创建测试数据
    a = np.array([[1, 2], [3, 4]])
    b = np.array([[5, 6], [7, 8]])
    
    # 获取算子
    matrix_multiply_op = get_operator(OperatorCategory.NUMPY, "matrix_multiply")
    
    # 缓存算子实例
    cache.set_instance(OperatorCategory.NUMPY.value, "matrix_multiply", matrix_multiply_op)
    
    # 从缓存获取算子
    cached_op = cache.get_instance(OperatorCategory.NUMPY.value, "matrix_multiply")
    
    # 测试算子
    start_time = time.time()
    result = cached_op(a, b)
    end_time = time.time()
    
    logger.info(f"矩阵乘法结果:\n{result}")
    logger.info(f"耗时: {end_time - start_time:.6f}秒")
    
    # 缓存结果
    cache.set_result(OperatorCategory.NUMPY.value, "matrix_multiply", (a, b), (), result)
    
    # 从缓存获取结果
    start_time = time.time()
    cached_result = cache.get_result(OperatorCategory.NUMPY.value, "matrix_multiply", (a, b), ())
    end_time = time.time()
    
    logger.info(f"从缓存获取结果:\n{cached_result}")
    logger.info(f"耗时: {end_time - start_time:.6f}秒")
    
    logger.info("与注册表的集成测试完成")

def main():
    """主函数"""
    logger.info("开始算子性能优化示例")
    
    # 测试算子缓存
    test_operator_cache()
    
    # 测试全局算子缓存
    test_global_operator_cache()
    
    # 测试懒加载算子
    test_lazy_operator()
    
    # 测试并行执行器
    test_parallel_executor()
    
    # 测试缓存算子装饰器
    test_cached_operator()
    
    # 测试并行算子装饰器
    test_parallel_operator()
    
    # 测试与注册表的集成
    test_integration_with_registry()
    
    logger.info("算子性能优化示例结束")

if __name__ == "__main__":
    main()
