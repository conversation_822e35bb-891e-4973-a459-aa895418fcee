"""
算子安全性示例

本示例展示了如何使用算子安全性功能，包括算子签名验证、访问控制和审计日志。
"""

import os
import sys
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入算子注册表模块
try:
    from src.rust_bindings import (
        OperatorCategory,
        OperatorRegistry,
        get_global_registry,
        register_operator,
        get_operator,
    )
    from src.rust_bindings.operator_registry.security import (
        OperatorSignature,
        AccessControl,
        AuditLog,
        AccessLevel,
        get_global_operator_signature,
        get_global_access_control,
        get_global_audit_log,
    )
    logger.info(f"成功导入算子注册表模块")
except ImportError as e:
    logger.error(f"导入算子注册表模块失败: {e}")
    sys.exit(1)

# 定义一些示例算子
def add(a, b):
    """加法算子"""
    return a + b

def multiply(a, b):
    """乘法算子"""
    return a * b

def divide(a, b):
    """除法算子"""
    if b == 0:
        raise ValueError("除数不能为零")
    return a / b

def test_operator_signature():
    """测试算子签名"""
    logger.info("开始测试算子签名...")
    
    # 创建算子签名
    signature = OperatorSignature()
    
    # 签名算子
    add_signature = signature.sign_operator(OperatorCategory.UTILITY.value, "add", add)
    logger.info(f"加法算子签名: {add_signature}")
    
    # 验证算子签名
    is_valid = signature.verify_operator(OperatorCategory.UTILITY.value, "add", add, add_signature)
    logger.info(f"加法算子签名验证结果: {is_valid}")
    
    # 修改算子后验证签名
    def modified_add(a, b):
        """修改后的加法算子"""
        return a + b + 1
        
    is_valid = signature.verify_operator(OperatorCategory.UTILITY.value, "add", modified_add, add_signature)
    logger.info(f"修改后的加法算子签名验证结果: {is_valid}")
    
    logger.info("算子签名测试完成")

def test_global_operator_signature():
    """测试全局算子签名"""
    logger.info("开始测试全局算子签名...")
    
    # 获取全局算子签名
    signature = get_global_operator_signature()
    
    # 签名算子
    multiply_signature = signature.sign_operator(OperatorCategory.UTILITY.value, "multiply", multiply)
    logger.info(f"乘法算子签名: {multiply_signature}")
    
    # 验证算子签名
    is_valid = signature.verify_operator(OperatorCategory.UTILITY.value, "multiply", multiply, multiply_signature)
    logger.info(f"乘法算子签名验证结果: {is_valid}")
    
    logger.info("全局算子签名测试完成")

def test_access_control():
    """测试访问控制"""
    logger.info("开始测试访问控制...")
    
    # 创建访问控制
    access_control = AccessControl()
    
    # 设置算子访问级别
    access_control.set_operator_access_level(OperatorCategory.UTILITY.value, "add", AccessLevel.PUBLIC)
    access_control.set_operator_access_level(OperatorCategory.UTILITY.value, "multiply", AccessLevel.PROTECTED)
    access_control.set_operator_access_level(OperatorCategory.UTILITY.value, "divide", AccessLevel.PRIVATE)
    
    # 添加用户角色
    access_control.add_user_role("user1", "user")
    access_control.add_user_role("user2", "admin")
    
    # 设置角色权限
    access_control.set_role_permission("user", AccessLevel.PUBLIC)
    access_control.set_role_permission("user", AccessLevel.PROTECTED)
    access_control.set_role_permission("admin", AccessLevel.PUBLIC)
    access_control.set_role_permission("admin", AccessLevel.PROTECTED)
    access_control.set_role_permission("admin", AccessLevel.PRIVATE)
    
    # 检查访问权限
    can_access = access_control.check_access("user1", OperatorCategory.UTILITY.value, "add")
    logger.info(f"用户1访问加法算子: {can_access}")
    
    can_access = access_control.check_access("user1", OperatorCategory.UTILITY.value, "multiply")
    logger.info(f"用户1访问乘法算子: {can_access}")
    
    can_access = access_control.check_access("user1", OperatorCategory.UTILITY.value, "divide")
    logger.info(f"用户1访问除法算子: {can_access}")
    
    can_access = access_control.check_access("user2", OperatorCategory.UTILITY.value, "add")
    logger.info(f"用户2访问加法算子: {can_access}")
    
    can_access = access_control.check_access("user2", OperatorCategory.UTILITY.value, "multiply")
    logger.info(f"用户2访问乘法算子: {can_access}")
    
    can_access = access_control.check_access("user2", OperatorCategory.UTILITY.value, "divide")
    logger.info(f"用户2访问除法算子: {can_access}")
    
    # 移除用户角色
    access_control.remove_user_role("user1", "user")
    
    can_access = access_control.check_access("user1", OperatorCategory.UTILITY.value, "add")
    logger.info(f"移除角色后用户1访问加法算子: {can_access}")
    
    logger.info("访问控制测试完成")

def test_global_access_control():
    """测试全局访问控制"""
    logger.info("开始测试全局访问控制...")
    
    # 获取全局访问控制
    access_control = get_global_access_control()
    
    # 设置算子访问级别
    access_control.set_operator_access_level(OperatorCategory.UTILITY.value, "add", AccessLevel.PUBLIC)
    access_control.set_operator_access_level(OperatorCategory.UTILITY.value, "multiply", AccessLevel.PROTECTED)
    
    # 添加用户角色
    access_control.add_user_role("user3", "user")
    
    # 设置角色权限
    access_control.set_role_permission("user", AccessLevel.PUBLIC)
    
    # 检查访问权限
    can_access = access_control.check_access("user3", OperatorCategory.UTILITY.value, "add")
    logger.info(f"用户3访问加法算子: {can_access}")
    
    can_access = access_control.check_access("user3", OperatorCategory.UTILITY.value, "multiply")
    logger.info(f"用户3访问乘法算子: {can_access}")
    
    # 添加权限
    access_control.set_role_permission("user", AccessLevel.PROTECTED)
    
    can_access = access_control.check_access("user3", OperatorCategory.UTILITY.value, "multiply")
    logger.info(f"添加权限后用户3访问乘法算子: {can_access}")
    
    logger.info("全局访问控制测试完成")

def test_audit_log():
    """测试审计日志"""
    logger.info("开始测试审计日志...")
    
    # 创建审计日志
    log_file = os.path.join(project_root, "examples", "audit.log")
    audit_log = AuditLog(log_file)
    
    # 记录算子访问
    audit_log.log_operator_access("user1", OperatorCategory.UTILITY.value, "add", True)
    audit_log.log_operator_access("user1", OperatorCategory.UTILITY.value, "multiply", False)
    
    # 记录算子执行
    audit_log.log_operator_execution("user1", OperatorCategory.UTILITY.value, "add", (1, 2), {}, True)
    audit_log.log_operator_execution("user1", OperatorCategory.UTILITY.value, "divide", (1, 0), {}, False, "除数不能为零")
    
    # 获取日志
    logs = audit_log.get_logs()
    logger.info(f"所有日志条目数: {len(logs)}")
    
    # 按用户过滤日志
    user_logs = audit_log.get_logs(user_id="user1")
    logger.info(f"用户1的日志条目数: {len(user_logs)}")
    
    # 按操作类型过滤日志
    access_logs = audit_log.get_logs(action="access")
    logger.info(f"访问操作的日志条目数: {len(access_logs)}")
    
    # 按时间过滤日志
    start_time = datetime.now() - timedelta(minutes=5)
    recent_logs = audit_log.get_logs(start_time=start_time)
    logger.info(f"最近5分钟的日志条目数: {len(recent_logs)}")
    
    logger.info(f"审计日志已保存到: {log_file}")
    logger.info("审计日志测试完成")

def test_global_audit_log():
    """测试全局审计日志"""
    logger.info("开始测试全局审计日志...")
    
    # 获取全局审计日志
    audit_log = get_global_audit_log()
    
    # 记录算子访问
    audit_log.log_operator_access("user2", OperatorCategory.UTILITY.value, "add", True)
    
    # 记录算子执行
    audit_log.log_operator_execution("user2", OperatorCategory.UTILITY.value, "add", (3, 4), {}, True)
    
    # 获取日志
    logs = audit_log.get_logs(user_id="user2")
    logger.info(f"用户2的日志条目数: {len(logs)}")
    
    logger.info("全局审计日志测试完成")

def test_integration():
    """测试集成"""
    logger.info("开始测试集成...")
    
    # 获取全局对象
    registry = get_global_registry()
    signature = get_global_operator_signature()
    access_control = get_global_access_control()
    audit_log = get_global_audit_log()
    
    # 注册算子
    register_operator(
        OperatorCategory.UTILITY,
        "add",
        add,
        "1.0.0",
        "加法算子",
        ["math", "basic"],
        [],
    )
    
    # 签名算子
    add_signature = signature.sign_operator(OperatorCategory.UTILITY.value, "add", add)
    
    # 设置访问级别
    access_control.set_operator_access_level(OperatorCategory.UTILITY.value, "add", AccessLevel.PROTECTED)
    
    # 添加用户角色
    access_control.add_user_role("user4", "user")
    access_control.set_role_permission("user", AccessLevel.PROTECTED)
    
    # 模拟用户访问
    user_id = "user4"
    
    # 检查访问权限
    can_access = access_control.check_access(user_id, OperatorCategory.UTILITY.value, "add")
    
    if can_access:
        # 记录访问
        audit_log.log_operator_access(user_id, OperatorCategory.UTILITY.value, "add", True)
        
        # 获取算子
        add_op = get_operator(OperatorCategory.UTILITY, "add")
        
        # 验证签名
        is_valid = signature.verify_operator(OperatorCategory.UTILITY.value, "add", add_op, add_signature)
        
        if is_valid:
            # 执行算子
            try:
                result = add_op(5, 6)
                audit_log.log_operator_execution(user_id, OperatorCategory.UTILITY.value, "add", (5, 6), {}, True)
                logger.info(f"执行结果: 5 + 6 = {result}")
            except Exception as e:
                audit_log.log_operator_execution(user_id, OperatorCategory.UTILITY.value, "add", (5, 6), {}, False, str(e))
                logger.error(f"执行失败: {e}")
        else:
            logger.error("签名验证失败")
    else:
        # 记录访问失败
        audit_log.log_operator_access(user_id, OperatorCategory.UTILITY.value, "add", False)
        logger.error("访问被拒绝")
    
    logger.info("集成测试完成")

def main():
    """主函数"""
    logger.info("开始算子安全性示例")
    
    # 测试算子签名
    test_operator_signature()
    
    # 测试全局算子签名
    test_global_operator_signature()
    
    # 测试访问控制
    test_access_control()
    
    # 测试全局访问控制
    test_global_access_control()
    
    # 测试审计日志
    test_audit_log()
    
    # 测试全局审计日志
    test_global_audit_log()
    
    # 测试集成
    test_integration()
    
    logger.info("算子安全性示例结束")

if __name__ == "__main__":
    main()
