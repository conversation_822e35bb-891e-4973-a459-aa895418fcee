digraph DependencyGraph {
  "utility.add@1.0.0" [label="utility.add\n1.0.0"];
  "utility.subtract@1.0.0" [label="utility.subtract\n1.0.0"];
  "utility.multiply@1.0.0" [label="utility.multiply\n1.0.0"];
  "utility.divide@1.0.0" [label="utility.divide\n1.0.0"];
  "utility.power@1.0.0" [label="utility.power\n1.0.0"];
  "utility.square_root@1.0.0" [label="utility.square_root\n1.0.0"];
  "numpy.matrix_multiply@1.0.0" [label="numpy.matrix_multiply\n1.0.0"];
  "numpy.convolution@1.0.0" [label="numpy.convolution\n1.0.0"];
  "utility.multiply@1.0.0" -> "utility.add@1.0.0" [label=">=1.0.0"];
  "utility.divide@1.0.0" -> "utility.multiply@1.0.0" [label=">=1.0.0"];
  "utility.power@1.0.0" -> "utility.multiply@1.0.0" [label=">=1.0.0"];
  "utility.square_root@1.0.0" -> "utility.power@1.0.0" [label=">=1.0.0"];
  "numpy.matrix_multiply@1.0.0" -> "utility.multiply@1.0.0" [label=">=1.0.0"];
  "numpy.convolution@1.0.0" -> "numpy.matrix_multiply@1.0.0" [label=">=1.0.0"];
}
