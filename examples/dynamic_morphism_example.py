"""
动态态射示例脚本

本脚本展示了如何使用动态态射模块实现自适应计算。
"""

import sys
import os
import time
import numpy as np

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入动态态射模块
from src.transcendental_tensor.self_reflective_category.dynamic_morphism import (
    OptimizationStrategy,
    DynamicMorphismConfig,
    DynamicMorphism,
    ComposableDynamicMorphism,
    Environment,
    EnvironmentSensitiveMorphism,
    SystemState,
    FeedbackTransform,
    FeedbackEmbed,
    FeedbackController,
    FeedbackMorphism
)

# 创建示例环境
environment = Environment(name="ExampleEnvironment")

# 设置环境变量
environment.set("debug_mode", False)
environment.set("optimization_level", 1)
environment.set("learning_rate", 0.01)

# 创建示例系统状态
system_state = SystemState(name="ExampleSystemState")

# 设置系统状态变量
system_state.set("available_memory", 8 * 1024 * 1024 * 1024)  # 8GB
system_state.set("available_cores", 4)
system_state.set("gpu_available", True)

# 定义基础函数
def matrix_multiply(a: np.ndarray, b: np.ndarray) -> np.ndarray:
    """矩阵乘法"""
    return np.matmul(a, b)

def matrix_transpose(a: np.ndarray) -> np.ndarray:
    """矩阵转置"""
    return np.transpose(a)

def matrix_inverse(a: np.ndarray) -> np.ndarray:
    """矩阵求逆"""
    return np.linalg.inv(a)

# 创建基础态射
multiply_morphism = DynamicMorphism(matrix_multiply, name="MatrixMultiply")
transpose_morphism = DynamicMorphism(matrix_transpose, name="MatrixTranspose")
inverse_morphism = DynamicMorphism(matrix_inverse, name="MatrixInverse")

# 创建可组合态射
multiply_composable = ComposableDynamicMorphism(matrix_multiply, name="MatrixMultiply")
transpose_composable = ComposableDynamicMorphism(matrix_transpose, name="MatrixTranspose")
inverse_composable = ComposableDynamicMorphism(matrix_inverse, name="MatrixInverse")

# 组合态射
transpose_then_inverse = transpose_composable >> inverse_composable
inverse_then_transpose = inverse_composable >> transpose_composable

# 定义环境条件和行为
def is_high_optimization(env: Environment) -> bool:
    """检查是否高优化级别"""
    return env.get("optimization_level", 0) >= 2

def optimize_matrix_behavior(args, kwargs, env: Environment) -> tuple:
    """优化矩阵计算行为"""
    # 检查参数是否为矩阵
    if len(args) >= 2 and isinstance(args[0], np.ndarray) and isinstance(args[1], np.ndarray):
        a, b = args[0], args[1]
        
        # 如果矩阵很小，使用标准乘法
        if a.size < 1000 or b.size < 1000:
            return args, kwargs
        
        # 如果矩阵很大，尝试使用更优化的算法
        # 这里只是示例，实际中可能会使用更复杂的策略
        if a.shape[0] > 1000 and a.shape[1] > 1000 and b.shape[1] > 1000:
            print(f"Using optimized algorithm for large matrices: {a.shape} x {b.shape}")
            
            # 这里可以实现更优化的矩阵乘法算法
            # 例如，分块矩阵乘法、Strassen算法等
            
            # 为了示例，我们只是打印一条消息，并继续使用标准算法
            pass
    
    return args, kwargs

# 创建环境敏感态射
adaptive_multiply = EnvironmentSensitiveMorphism(matrix_multiply, environment, name="AdaptiveMatrixMultiply")
adaptive_multiply.add_response(is_high_optimization, optimize_matrix_behavior, name="MatrixOptimizer")

# 定义反馈变换和嵌入
class PerformanceTransform(FeedbackTransform):
    """性能反馈变换"""
    
    def transform(self, cell_state, system_state):
        """执行反馈变换"""
        super().transform(cell_state, system_state)
        
        # 提取执行时间和结果大小
        execution_time = cell_state.get("execution_time", 0.0)
        
        result = cell_state.get("result")
        result_size = result.size if isinstance(result, np.ndarray) else 0
        
        # 计算性能指标
        performance = result_size / max(execution_time, 1e-6)
        
        return {
            "performance": performance,
            "execution_time": execution_time,
            "result_size": result_size
        }

class OptimizationEmbed(FeedbackEmbed):
    """优化反馈嵌入"""
    
    def embed(self, category_state, system_state):
        """执行反馈嵌入"""
        super().embed(category_state, system_state)
        
        # 提取性能指标
        performance = category_state.get("performance", 0.0)
        
        # 根据性能调整优化级别
        if performance < 1e6:  # 低性能
            optimization_level = 1
        elif performance < 1e7:  # 中等性能
            optimization_level = 2
        else:  # 高性能
            optimization_level = 3
        
        return {
            "optimization_level": optimization_level,
            "block_size": 32 * 2 ** optimization_level  # 根据优化级别调整分块大小
        }

# 创建反馈控制器
transform = PerformanceTransform(name="PerformanceTransform")
embed = OptimizationEmbed(name="OptimizationEmbed")
controller = FeedbackController(transform, embed, system_state, name="MatrixOptimizationController")

# 创建反馈态射
feedback_multiply = FeedbackMorphism(matrix_multiply, controller, environment, name="FeedbackMatrixMultiply")

# 自定义反馈态射，实现参数调整
class AdaptiveMatrixMultiply(FeedbackMorphism):
    """自适应矩阵乘法"""
    
    def _apply_feedback_state(self, args, kwargs):
        """应用反馈状态"""
        # 获取分块大小
        block_size = self.feedback_state.get("block_size", 64)
        
        # 检查参数是否为矩阵
        if len(args) >= 2 and isinstance(args[0], np.ndarray) and isinstance(args[1], np.ndarray):
            a, b = args[0], args[1]
            
            # 如果矩阵很大，使用分块乘法
            if a.shape[0] > block_size and a.shape[1] > block_size and b.shape[1] > block_size:
                print(f"Using blocked matrix multiplication with block size {block_size}")
                
                # 这里可以实现分块矩阵乘法
                # 为了示例，我们只是打印一条消息，并继续使用标准算法
                pass
        
        return args, kwargs

# 创建自定义反馈态射
adaptive_matrix_multiply = AdaptiveMatrixMultiply(matrix_multiply, controller, environment, name="AdaptiveMatrixMultiply")

# 示例：使用基础态射
def example_basic_morphism():
    """基础态射示例"""
    print("\n=== 基础态射示例 ===")
    
    # 创建测试矩阵
    a = np.random.rand(100, 100)
    b = np.random.rand(100, 100)
    
    # 使用基础态射
    start_time = time.time()
    result = multiply_morphism(a, b)
    end_time = time.time()
    
    print(f"矩阵乘法结果形状: {result.shape}")
    print(f"执行时间: {(end_time - start_time) * 1000:.2f} ms")
    
    # 查看态射度量
    metrics = multiply_morphism.get_metrics()
    print(f"态射度量: {metrics}")

# 示例：使用组合态射
def example_composition():
    """组合态射示例"""
    print("\n=== 组合态射示例 ===")
    
    # 创建测试矩阵
    a = np.random.rand(100, 100)
    
    # 使用组合态射
    start_time = time.time()
    result1 = transpose_then_inverse(a)
    end_time = time.time()
    
    print(f"转置后求逆结果形状: {result1.shape}")
    print(f"执行时间: {(end_time - start_time) * 1000:.2f} ms")
    
    start_time = time.time()
    result2 = inverse_then_transpose(a)
    end_time = time.time()
    
    print(f"求逆后转置结果形状: {result2.shape}")
    print(f"执行时间: {(end_time - start_time) * 1000:.2f} ms")
    
    # 验证结果是否相同
    print(f"结果是否相同: {np.allclose(result1, result2)}")

# 示例：使用环境敏感态射
def example_environment_sensitivity():
    """环境敏感态射示例"""
    print("\n=== 环境敏感态射示例 ===")
    
    # 创建测试矩阵
    a = np.random.rand(500, 500)
    b = np.random.rand(500, 500)
    
    # 使用环境敏感态射（低优化级别）
    environment.set("optimization_level", 1)
    
    start_time = time.time()
    result1 = adaptive_multiply(a, b)
    end_time = time.time()
    
    print(f"低优化级别执行时间: {(end_time - start_time) * 1000:.2f} ms")
    
    # 使用环境敏感态射（高优化级别）
    environment.set("optimization_level", 3)
    
    start_time = time.time()
    result2 = adaptive_multiply(a, b)
    end_time = time.time()
    
    print(f"高优化级别执行时间: {(end_time - start_time) * 1000:.2f} ms")
    
    # 验证结果是否相同
    print(f"结果是否相同: {np.allclose(result1, result2)}")
    
    # 查看环境响应统计信息
    response_stats = adaptive_multiply.get_response_stats()
    print(f"环境响应统计信息: {response_stats}")

# 示例：使用反馈态射
def example_feedback_mechanism():
    """反馈机制示例"""
    print("\n=== 反馈机制示例 ===")
    
    # 创建测试矩阵
    a = np.random.rand(500, 500)
    b = np.random.rand(500, 500)
    
    # 多次调用反馈态射，观察性能变化
    for i in range(5):
        start_time = time.time()
        result = adaptive_matrix_multiply(a, b)
        end_time = time.time()
        
        execution_time = (end_time - start_time) * 1000
        print(f"第 {i+1} 次调用执行时间: {execution_time:.2f} ms")
        
        # 查看反馈状态
        feedback_stats = adaptive_matrix_multiply.get_feedback_stats()
        print(f"反馈状态: {feedback_stats['feedback_state']}")
        
        # 增加矩阵大小，观察自适应行为
        a = np.random.rand(500 + i * 100, 500 + i * 100)
        b = np.random.rand(500 + i * 100, 500 + i * 100)

# 运行示例
if __name__ == "__main__":
    example_basic_morphism()
    example_composition()
    example_environment_sensitivity()
    example_feedback_mechanism()
