#!/usr/bin/env python3
"""
高级算子示例脚本

该脚本展示了如何使用对比解释算子和统计验证算子。
"""

import os
import sys
import logging
import numpy as np
from typing import Dict, Any, List, Optional, Tuple, Union

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建必要的目录
os.makedirs("src/operators/explanation", exist_ok=True)
os.makedirs("src/operators/verification", exist_ok=True)

# 创建对比解释算子
class ComparativeExplanationOperator:
    def apply(self, input_data):
        # 获取模型输出
        model_output = input_data.get('model_output', {})
        
        # 获取决策
        decision = model_output.get('decision', {})
        
        # 获取选项
        options = decision.get('options', {})
        
        # 获取选择的选项
        selected_option = decision.get('selected_option', None)
        
        # 获取比较方面
        comparison_aspects = input_data.get('comparison_aspects', ['cost', 'benefit', 'risk', 'time'])
        
        # 生成随机对比
        comparisons = []
        
        for option_id, option_value in options.items():
            if option_id != selected_option:
                # 生成随机相似度
                similarity = np.random.random() * 0.5 + 0.5
                
                # 生成随机相似点
                similarities = []
                for aspect in comparison_aspects[:2]:
                    similarities.append({
                        'aspect': aspect,
                        'selected_score': np.random.random() * 0.5 + 0.5,
                        'option_score': np.random.random() * 0.5 + 0.5,
                        'difference': np.random.random() * 0.1
                    })
                
                # 生成随机差异点
                differences = []
                for aspect in comparison_aspects[2:]:
                    differences.append({
                        'aspect': aspect,
                        'selected_score': np.random.random() * 0.5 + 0.5,
                        'option_score': np.random.random() * 0.5 + 0.5,
                        'difference': np.random.random() * 0.3 + 0.2
                    })
                
                # 生成对比
                comparison = {
                    'selected_option': selected_option,
                    'compared_option': option_id,
                    'similarity': similarity,
                    'similarities': similarities,
                    'differences': differences,
                    'selected_weight': 0.7,
                    'compared_weight': 0.3
                }
                
                comparisons.append(comparison)
        
        # 生成对比文本解释
        comparative_explanations = []
        
        for comparison in comparisons:
            compared_option = comparison['compared_option']
            similarity = comparison['similarity']
            
            # 生成相似点
            similarity_points = []
            for sim in comparison['similarities']:
                aspect = sim['aspect']
                selected_score = sim['selected_score']
                option_score = sim['option_score']
                
                similarity_points.append(f"在{aspect}方面，{selected_option}的得分为{selected_score:.2f}，"
                                        f"{compared_option}的得分为{option_score:.2f}，非常接近")
            
            # 生成差异点
            difference_points = []
            for diff in comparison['differences']:
                aspect = diff['aspect']
                selected_score = diff['selected_score']
                option_score = diff['option_score']
                
                if selected_score > option_score:
                    difference_points.append(f"在{aspect}方面，{selected_option}的得分为{selected_score:.2f}，"
                                            f"高于{compared_option}的{option_score:.2f}")
                else:
                    difference_points.append(f"在{aspect}方面，{selected_option}的得分为{selected_score:.2f}，"
                                            f"低于{compared_option}的{option_score:.2f}")
            
            # 生成总体解释
            explanation = f"{selected_option}与{compared_option}的相似度为{similarity:.2f}。"
            
            if similarity_points:
                explanation += f"\n相似点：\n- " + "\n- ".join(similarity_points)
            
            if difference_points:
                explanation += f"\n差异点：\n- " + "\n- ".join(difference_points)
            
            # 添加结论
            selected_weight = comparison['selected_weight']
            compared_weight = comparison['compared_weight']
            
            explanation += f"\n总体而言，{selected_option}（权重{selected_weight:.2f}）优于{compared_option}（权重{compared_weight:.2f}）。"
            
            comparative_explanations.append(explanation)
        
        # 生成选项排名
        option_rankings = {}
        
        for aspect in comparison_aspects:
            # 按得分排序
            sorted_options = sorted(
                [(option_id, np.random.random()) for option_id in options],
                key=lambda x: x[1],
                reverse=True
            )
            
            option_rankings[aspect] = sorted_options
        
        # 计算总体排名
        overall_ranking = sorted(
            [(option_id, np.random.random()) for option_id in options],
            key=lambda x: x[1],
            reverse=True
        )
        
        option_rankings['overall'] = overall_ranking
        
        # 构建返回结果
        result = {
            'comparisons': comparisons,
            'comparative_explanations': comparative_explanations,
            'option_rankings': option_rankings,
            'selected_option': selected_option,
            'comparison_aspects': comparison_aspects
        }
        
        return result

# 创建统计验证算子
class StatisticalVerificationOperator:
    def apply(self, input_data):
        # 获取系统数据
        system_data = input_data.get('system_data', {})
        
        # 获取属性
        properties = input_data.get('properties', [])
        
        # 验证结果
        verification_results = {}
        
        # 对每个属性进行验证
        for prop in properties:
            # 获取属性ID
            prop_id = prop.get('id', 'unknown')
            
            # 获取属性类型
            prop_type = prop.get('type', 'unknown')
            
            # 获取属性表达式
            prop_expr = prop.get('expression', '')
            
            # 随机生成验证结果
            result = np.random.choice(['true', 'false', 'unknown'], p=[0.7, 0.2, 0.1])
            
            # 随机生成置信度
            confidence = np.random.random() * 0.3 + 0.7 if result == 'true' else np.random.random() * 0.5
            
            # 构建验证结果
            verification_result = {
                'method': 'hypothesis_testing',
                'property_id': prop_id,
                'property_expression': prop_expr,
                'property_type': prop_type,
                'result': result,
                'confidence': confidence,
                'p_value': 1.0 - confidence
            }
            
            # 如果结果为false，则添加违例
            if result == 'false':
                verification_result['violations'] = [
                    {
                        'id': f"violation_{i}",
                        'property_id': prop_id,
                        'property_expression': prop_expr,
                        'property_type': prop_type,
                        'description': f"违反了属性 '{prop_expr}'",
                        'severity': np.random.choice(['low', 'medium', 'high']),
                        'data': {
                            f"feature_{j}": np.random.random()
                            for j in range(np.random.randint(2, 5))
                        }
                    }
                    for i in range(np.random.randint(1, 4))
                ]
            
            verification_results[prop_id] = verification_result
        
        # 构建返回结果
        result = {
            'verification_results': verification_results,
            'verification_properties': properties,
            'verification_methods_used': {
                'hypothesis_testing': len(properties),
                'confidence_interval': 0,
                'bootstrap': 0
            }
        }
        
        return result

# 创建简单的决策类
class Decision:
    def __init__(self, decision_id, options, selected_option=None, confidence=0.0):
        self.decision_id = decision_id
        self.options = options
        self.selected_option = selected_option
        self.confidence = confidence

# 演示对比解释算子
def demonstrate_comparative_explanation():
    logger.info("=" * 80)
    logger.info("演示对比解释算子")
    logger.info("=" * 80)
    
    # 创建对比解释算子
    comparative_explanation_operator = ComparativeExplanationOperator()
    
    # 创建决策
    decisions = [
        Decision("decision1", {"option1": "增加资源分配", "option2": "减少资源分配", "option3": "保持当前资源分配"}, "option1", 0.7),
        Decision("decision2", {"option1": "增加资源分配", "option2": "减少资源分配", "option3": "保持当前资源分配"}, "option2", 0.6),
        Decision("decision3", {"option1": "增加资源分配", "option2": "减少资源分配", "option3": "保持当前资源分配"}, "option3", 0.5)
    ]
    
    # 比较决策
    model_output = {
        "decision": {
            "options": {d.decision_id: d.selected_option for d in decisions},
            "selected_option": decisions[0].decision_id,
            "weights": {d.decision_id: d.confidence for d in decisions}
        }
    }
    
    comparative_input = {
        "model_output": model_output,
        "comparison_aspects": ["cost", "benefit", "risk", "time"]
    }
    
    comparative_result = comparative_explanation_operator.apply(comparative_input)
    
    # 打印对比解释
    logger.info("\n对比解释:")
    for explanation in comparative_result['comparative_explanations']:
        logger.info(explanation)
        logger.info("-" * 40)
    
    # 打印选项排名
    logger.info("\n选项排名:")
    for aspect, rankings in comparative_result['option_rankings'].items():
        logger.info(f"{aspect}: {rankings}")

# 演示统计验证算子
def demonstrate_statistical_verification():
    logger.info("\n" + "=" * 80)
    logger.info("演示统计验证算子")
    logger.info("=" * 80)
    
    # 创建统计验证算子
    statistical_verification_operator = StatisticalVerificationOperator()
    
    # 创建系统数据
    system_data = {
        "inputs": [np.random.random() for _ in range(100)],
        "outputs": [np.random.random() for _ in range(100)],
        "groups": [np.random.choice(["A", "B", "C"]) for _ in range(100)]
    }
    
    # 创建属性
    properties = [
        {
            "id": "stability",
            "type": "stability",
            "expression": "system output is stable under small input perturbations",
            "parameters": {
                "threshold": 0.1
            }
        },
        {
            "id": "fairness",
            "type": "fairness",
            "expression": "system treats different groups fairly",
            "parameters": {
                "threshold": 0.05
            }
        },
        {
            "id": "robustness",
            "type": "robustness",
            "expression": "system is robust to adversarial inputs",
            "parameters": {
                "threshold": 0.2
            }
        }
    ]
    
    # 验证系统数据
    verification_input = {
        "system_data": system_data,
        "properties": properties
    }
    
    verification_result = statistical_verification_operator.apply(verification_input)
    
    # 打印验证结果
    logger.info("\n统计验证结果:")
    for prop_id, result in verification_result['verification_results'].items():
        logger.info(f"属性 {prop_id}: {result['result']}, 置信度: {result['confidence']:.2f}")
        
        # 打印违例
        if 'violations' in result:
            logger.info("违例:")
            for violation in result['violations']:
                logger.info(f"  - {violation['description']} (严重程度: {violation['severity']})")
        
        logger.info("-" * 40)
    
    # 打印使用的验证方法
    logger.info(f"\n使用的验证方法: {verification_result['verification_methods_used']}")

# 主函数
def main():
    # 演示对比解释算子
    demonstrate_comparative_explanation()
    
    # 演示统计验证算子
    demonstrate_statistical_verification()

if __name__ == "__main__":
    main()
