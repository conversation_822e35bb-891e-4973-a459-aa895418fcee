"""
量子-超越态集成示例

这个示例展示了如何使用量子态表示、量子门操作、量子演化模块和超越态模块的集成。
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import List, Tuple, Dict

from src.core.quantum.state import QuantumStateArrow
from src.core.quantum.operators import HadamardGate, PauliXGate, PauliZGate, PhaseGate, RotationGate
from src.core.quantum.evolution import QuantumCircuit, TimeEvolution
from src.core.quantum.measurement import ProjectiveMeasurement

from src.core.transcendental.state import TranscendentalState
from src.core.transcendental.evolution import TCFEvolution
from src.core.transcendental.emergence import EmergenceDetector, CreativeEmergence


def quantum_to_transcendental_workflow():
    """
    量子态到超越态的工作流
    
    展示如何从量子态创建超越态，并进行超越态演化和涌现检测。
    """
    print("量子态到超越态的工作流")
    print("=" * 50)
    
    # 1. 创建量子态
    print("\n1. 创建量子态")
    # 创建|+⟩ = (|0⟩ + |1⟩)/√2
    quantum_state = QuantumStateArrow.create_superposition(2, [0, 1], [1, 1])
    quantum_array = QuantumStateArrow.to_numpy(quantum_state)
    print(f"量子态: {quantum_array}")
    
    # 2. 将量子态转换为超越态
    print("\n2. 将量子态转换为超越态")
    transcendental_state = QuantumStateArrow.to_transcendental(quantum_state)
    print(f"超越态: {transcendental_state}")
    
    # 3. 创建TCF演化
    print("\n3. 创建TCF演化")
    tcf = TCFEvolution()
    evolution_op = tcf.create_evolution_operator(time_step=0.1)
    print(f"演化算子: {evolution_op}")
    
    # 4. 演化超越态
    print("\n4. 演化超越态")
    states = [transcendental_state]
    times = [0.0]
    
    # 演化10步
    current_state = transcendental_state
    for i in range(1, 11):
        current_state = evolution_op.apply(current_state)
        states.append(current_state)
        times.append(i * 0.1)
    
    print(f"初始超越态复杂度: {states[0].metadata.get('complexity', 0.0)}")
    print(f"最终超越态复杂度: {states[-1].metadata.get('complexity', 0.0)}")
    
    # 5. 检测涌现特性
    print("\n5. 检测涌现特性")
    detector = EmergenceDetector()
    
    emergence_results = []
    for i in range(1, len(states)):
        result = detector.detect(states[i], states[i-1])
        emergence_results.append(result)
        
        if result['has_emergence']:
            print(f"在时间 {times[i]} 检测到涌现特性:")
            print(f"  涌现类型: {result['emergence_types']}")
            print(f"  涌现强度: {result['emergence_strength']}")
    
    # 6. 应用创造性涌现
    print("\n6. 应用创造性涌现")
    creative = CreativeEmergence()
    emergence_op = creative.create_emergence_operator(emergence_threshold=0.5)
    
    # 对最终状态应用涌现
    emerged_state = emergence_op.apply(states[-1])
    
    print(f"涌现前复杂度: {states[-1].metadata.get('complexity', 0.0)}")
    print(f"涌现后复杂度: {emerged_state.metadata.get('complexity', 0.0)}")
    
    # 7. 绘制复杂度演化
    print("\n7. 绘制复杂度演化")
    plt.figure(figsize=(10, 6))
    
    # 提取复杂度
    complexities = [state.metadata.get('complexity', 0.0) for state in states]
    
    # 绘制复杂度
    plt.plot(times, complexities, 'b-', linewidth=2)
    
    plt.title("超越态复杂度演化")
    plt.xlabel("时间")
    plt.ylabel("复杂度")
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig("quantum_transcendental_complexity.png")
    plt.close()
    
    print("已保存复杂度演化图表到 quantum_transcendental_complexity.png")
    
    # 8. 绘制涌现强度
    print("\n8. 绘制涌现强度")
    plt.figure(figsize=(10, 6))
    
    # 提取涌现强度
    strengths = [result.get('emergence_strength', 0.0) for result in emergence_results]
    
    # 绘制涌现强度
    plt.plot(times[1:], strengths, 'r-', linewidth=2)
    
    plt.title("超越态涌现强度")
    plt.xlabel("时间")
    plt.ylabel("涌现强度")
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig("quantum_transcendental_emergence.png")
    plt.close()
    
    print("已保存涌现强度图表到 quantum_transcendental_emergence.png")
    
    return states, times, emergence_results


def quantum_circuit_to_transcendental():
    """
    量子线路到超越态的工作流
    
    展示如何使用量子线路创建量子态，然后转换为超越态。
    """
    print("\n量子线路到超越态的工作流")
    print("=" * 50)
    
    # 1. 创建量子线路
    print("\n1. 创建量子线路")
    circuit = QuantumCircuit(2)
    
    # 添加Hadamard门到第一个量子比特
    h_gate = HadamardGate()
    circuit.add_gate(h_gate, 0)
    
    # 添加CNOT门
    from src.core.quantum.operators import CNOTGate
    cnot_gate = CNOTGate()
    circuit.add_gate(cnot_gate, (0, 1))
    
    print(f"量子线路: {circuit}")
    
    # 2. 应用量子线路创建Bell态
    print("\n2. 应用量子线路创建Bell态")
    state_00 = QuantumStateArrow.create_basis_state(4, 0)  # |00⟩
    bell_state = circuit.apply(state_00)
    
    bell_array = QuantumStateArrow.to_numpy(bell_state)
    print(f"Bell态: {bell_array}")
    
    # 3. 将Bell态转换为超越态
    print("\n3. 将Bell态转换为超越态")
    transcendental_state = QuantumStateArrow.to_transcendental(bell_state)
    print(f"超越态: {transcendental_state}")
    
    # 4. 创建TCF演化
    print("\n4. 创建TCF演化")
    tcf = TCFEvolution()
    evolution_op = tcf.create_evolution_operator(time_step=0.1)
    
    # 5. 演化超越态
    print("\n5. 演化超越态")
    evolved_state = evolution_op.apply(transcendental_state)
    print(f"演化后的超越态: {evolved_state}")
    
    # 6. 检测涌现特性
    print("\n6. 检测涌现特性")
    detector = EmergenceDetector()
    result = detector.detect(evolved_state, transcendental_state)
    
    if result['has_emergence']:
        print(f"检测到涌现特性:")
        print(f"  涌现类型: {result['emergence_types']}")
        print(f"  涌现强度: {result['emergence_strength']}")
    else:
        print("未检测到涌现特性")
    
    return transcendental_state, evolved_state, result


def main():
    """主函数"""
    print("量子-超越态集成示例")
    print("=" * 50)
    
    # 运行量子态到超越态的工作流
    states, times, emergence_results = quantum_to_transcendental_workflow()
    
    # 运行量子线路到超越态的工作流
    initial_state, evolved_state, emergence_result = quantum_circuit_to_transcendental()
    
    print("\n示例完成！")


if __name__ == "__main__":
    main()
