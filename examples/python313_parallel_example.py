#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Python 3.13+ 并行计算示例

本示例展示了如何使用基于 Python 3.13+ nogil 模式的并行计算模块。
相比基于 Rust 的并行计算模块，这个纯 Python 实现的模块更易于扩展和维护，
同时通过利用 Python 3.13+ 的 nogil 模式，性能也非常接近 Rust 实现。
"""

import os
import sys
import logging
import time
import numpy as np
from typing import Dict, List, Any, Optional, Callable

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入并行计算模块
try:
    from src.core.parallel import (
        # GIL 管理
        gil_state, nogil_mode, nogil, release_gil, acquire_gil, adaptive_gil,
        get_gil_status, set_gil_strategy, set_nogil_mode,
        
        # 并行执行
        ParallelExecutor, get_executor, parallel, parallel_map,
        get_performance_data, set_parallel_strategy, set_max_workers
    )
    
    # 检查 nogil 支持
    gil_status = get_gil_status()
    has_nogil = gil_status['has_nogil_support']
    logger.info(f"成功导入并行计算模块，nogil 支持: {has_nogil}")
except ImportError as e:
    logger.error(f"导入并行计算模块失败: {e}")
    sys.exit(1)


def test_gil_management():
    """测试 GIL 管理功能"""
    logger.info("开始测试 GIL 管理功能...")
    
    # 获取 GIL 状态
    gil_status = get_gil_status()
    logger.info(f"GIL 状态: {gil_status}")
    
    # 测试 gil_state 上下文管理器
    logger.info("测试 gil_state 上下文管理器...")
    
    # 计算密集型任务
    def compute_intensive_task():
        result = 0
        for i in range(10_000_000):
            result += i
        return result
    
    # 不释放 GIL
    start_time = time.time()
    result1 = compute_intensive_task()
    normal_time = time.time() - start_time
    logger.info(f"不释放 GIL 耗时: {normal_time:.6f}秒，结果: {result1}")
    
    # 释放 GIL
    start_time = time.time()
    with gil_state(False):
        result2 = compute_intensive_task()
    gil_release_time = time.time() - start_time
    logger.info(f"释放 GIL 耗时: {gil_release_time:.6f}秒，结果: {result2}")
    
    # 测试 nogil 模式
    if has_nogil:
        logger.info("测试 nogil 模式...")
        
        # 使用 nogil 装饰器
        @nogil
        def nogil_task():
            result = 0
            for i in range(10_000_000):
                result += i
            return result
        
        start_time = time.time()
        result3 = nogil_task()
        nogil_time = time.time() - start_time
        logger.info(f"nogil 模式耗时: {nogil_time:.6f}秒，结果: {result3}")
        
        # 使用 nogil_mode 上下文管理器
        start_time = time.time()
        with nogil_mode(True):
            result4 = compute_intensive_task()
        nogil_mode_time = time.time() - start_time
        logger.info(f"nogil_mode 上下文管理器耗时: {nogil_mode_time:.6f}秒，结果: {result4}")
    
    # 测试自适应 GIL 管理
    logger.info("测试自适应 GIL 管理...")
    
    @adaptive_gil(compute_intensity=0.9)  # 高计算密集度，自动释放 GIL
    def adaptive_high_intensity():
        result = 0
        for i in range(10_000_000):
            result += i
        return result
    
    @adaptive_gil(compute_intensity=0.1)  # 低计算密集度，不释放 GIL
    def adaptive_low_intensity():
        result = 0
        for i in range(100):
            result += i
        return result
    
    start_time = time.time()
    result5 = adaptive_high_intensity()
    adaptive_high_time = time.time() - start_time
    logger.info(f"自适应高强度计算耗时: {adaptive_high_time:.6f}秒，结果: {result5}")
    
    start_time = time.time()
    result6 = adaptive_low_intensity()
    adaptive_low_time = time.time() - start_time
    logger.info(f"自适应低强度计算耗时: {adaptive_low_time:.6f}秒，结果: {result6}")
    
    logger.info("GIL 管理功能测试完成")


def test_parallel_executor():
    """测试并行执行器"""
    logger.info("开始测试并行执行器...")
    
    # 创建并行执行器
    executor = ParallelExecutor(max_workers=4, mode='thread')
    
    # 定义任务函数
    def task_func(task_id, sleep_time):
        logger.info(f"任务 {task_id} 开始执行，休眠 {sleep_time} 秒")
        time.sleep(sleep_time)
        logger.info(f"任务 {task_id} 执行完成")
        return f"任务 {task_id} 的结果"
    
    # 提交任务
    futures = []
    for i in range(10):
        future = executor.submit(task_func, i, 0.5)
        futures.append(future)
    
    # 获取任务结果
    for i, future in enumerate(futures):
        result = future.result()
        logger.info(f"任务 {i} 的结果: {result}")
    
    # 关闭执行器
    executor.shutdown()
    
    logger.info("并行执行器测试完成")


def test_work_stealing():
    """测试工作窃取调度"""
    logger.info("开始测试工作窃取调度...")
    
    # 创建不平衡的工作负载
    def unbalanced_task(task_id):
        if task_id % 3 == 0:
            # 长任务
            logger.info(f"长任务 {task_id} 开始执行")
            time.sleep(1.0)
            logger.info(f"长任务 {task_id} 执行完成")
            return f"长任务 {task_id} 的结果"
        else:
            # 短任务
            logger.info(f"短任务 {task_id} 开始执行")
            time.sleep(0.1)
            logger.info(f"短任务 {task_id} 执行完成")
            return f"短任务 {task_id} 的结果"
    
    # 使用全局执行器
    executor = get_executor(max_workers=4, mode='thread')
    
    # 提交任务
    start_time = time.time()
    futures = []
    for i in range(12):
        future = executor.submit(unbalanced_task, i)
        futures.append(future)
    
    # 获取任务结果
    for i, future in enumerate(futures):
        result = future.result()
        logger.info(f"任务 {i} 的结果: {result}")
    
    end_time = time.time()
    logger.info(f"工作窃取调度总耗时: {end_time - start_time:.6f}秒")
    
    # 关闭执行器
    executor.shutdown()
    
    logger.info("工作窃取调度测试完成")


def square(x):
    """计算平方"""
    return x * x


def add(x, y):
    """计算和"""
    return x + y


def test_parallel_map_reduce():
    """测试并行映射和归约"""
    logger.info("开始测试并行映射和归约...")
    
    # 创建测试数据
    data = list(range(1000))
    
    # 测试并行映射
    start_time = time.time()
    result = parallel_map(square, data)
    end_time = time.time()
    logger.info(f"并行映射耗时: {end_time - start_time:.6f}秒")
    logger.info(f"映射结果前10个元素: {result[:10]}")
    
    # 测试串行映射
    start_time = time.time()
    serial_result = list(map(square, data))
    end_time = time.time()
    logger.info(f"串行映射耗时: {end_time - start_time:.6f}秒")
    
    # 验证结果
    assert result == serial_result, "并行映射结果与串行映射结果不一致"
    
    # 测试并行归约
    # 由于我们的 parallel_map 函数返回列表，我们可以使用 Python 内置的 reduce 函数
    from functools import reduce
    
    start_time = time.time()
    parallel_mapped = parallel_map(square, data)
    reduced_result = reduce(add, parallel_mapped)
    end_time = time.time()
    logger.info(f"并行映射+归约耗时: {end_time - start_time:.6f}秒")
    logger.info(f"归约结果: {reduced_result}")
    
    # 测试串行归约
    start_time = time.time()
    serial_reduced = reduce(add, map(square, data))
    end_time = time.time()
    logger.info(f"串行映射+归约耗时: {end_time - start_time:.6f}秒")
    logger.info(f"串行归约结果: {serial_reduced}")
    
    # 验证结果
    assert reduced_result == serial_reduced, "并行归约结果与串行归约结果不一致"
    
    logger.info("并行映射和归约测试完成")


def matrix_multiply(a: np.ndarray, b: np.ndarray) -> np.ndarray:
    """矩阵乘法"""
    return np.dot(a, b)


@release_gil
def matrix_multiply_nogil(a: np.ndarray, b: np.ndarray) -> np.ndarray:
    """释放 GIL 的矩阵乘法"""
    return np.dot(a, b)


def test_numpy_performance():
    """测试 NumPy 性能"""
    logger.info("开始测试 NumPy 性能...")
    
    # 创建测试矩阵
    size = 1000
    a = np.random.rand(size, size)
    b = np.random.rand(size, size)
    
    # 测试普通矩阵乘法
    start_time = time.time()
    result1 = matrix_multiply(a, b)
    normal_time = time.time() - start_time
    logger.info(f"普通矩阵乘法耗时: {normal_time:.6f}秒")
    
    # 测试释放 GIL 的矩阵乘法
    start_time = time.time()
    result2 = matrix_multiply_nogil(a, b)
    nogil_time = time.time() - start_time
    logger.info(f"释放 GIL 的矩阵乘法耗时: {nogil_time:.6f}秒")
    
    # 验证结果
    assert np.allclose(result1, result2), "两种方法的结果不一致"
    
    # 测试并行矩阵乘法
    def parallel_matrix_multiply(matrices):
        a, b = matrices
        return np.dot(a, b)
    
    # 创建多个矩阵对
    matrix_pairs = [(np.random.rand(100, 100), np.random.rand(100, 100)) for _ in range(10)]
    
    # 并行执行
    start_time = time.time()
    results = parallel_map(parallel_matrix_multiply, matrix_pairs)
    parallel_time = time.time() - start_time
    logger.info(f"并行矩阵乘法耗时: {parallel_time:.6f}秒")
    
    logger.info("NumPy 性能测试完成")


def test_performance_monitoring():
    """测试性能监控"""
    logger.info("开始测试性能监控...")
    
    # 执行一些并行任务
    data = list(range(1000))
    parallel_map(square, data)
    
    # 获取性能数据
    perf_data = get_performance_data()
    logger.info(f"性能数据: {perf_data}")
    
    logger.info("性能监控测试完成")


def main():
    """主函数"""
    logger.info(f"开始 Python 3.13+ 并行计算示例，nogil 支持: {has_nogil}")
    
    # 测试 GIL 管理功能
    test_gil_management()
    
    # 测试并行执行器
    test_parallel_executor()
    
    # 测试工作窃取调度
    test_work_stealing()
    
    # 测试并行映射和归约
    test_parallel_map_reduce()
    
    # 测试 NumPy 性能
    test_numpy_performance()
    
    # 测试性能监控
    test_performance_monitoring()
    
    logger.info("Python 3.13+ 并行计算示例结束")


if __name__ == "__main__":
    main()
