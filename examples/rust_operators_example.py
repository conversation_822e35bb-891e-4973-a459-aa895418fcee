"""
Rust算子示例

本示例展示了如何使用Rust实现的算子。
"""

import os
import sys
import logging
import time
import random

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入Rust绑定模块
try:
    from src.rust_bindings import (
        RUST_AVAILABLE,
        VersionDetector, VersionAdapter, CompatibilityTester,
        ErrorSeverity, ErrorCategory, ErrorCode, ErrorCodeRegistry,
        TTError, ErrorHandler, RetryStrategy, FallbackStrategy, CircuitBreaker,
        ErrorReporter
    )
    logger.info(f"成功导入Rust绑定模块，Rust可用: {RUST_AVAILABLE}")
except ImportError as e:
    logger.error(f"导入Rust绑定模块失败: {e}")
    sys.exit(1)

def test_version_adapter():
    """测试版本适配器"""
    logger.info("开始测试版本适配器...")
    
    # 创建版本检测器
    detector = VersionDetector("example_detector")
    
    # 注册版本模式
    detector.register_version_pattern("1.0", r"1\.0(\.\d+)?")
    detector.register_version_pattern("2.0", r"2\.0(\.\d+)?")
    
    # 创建一些测试对象
    class TestAPI_V1:
        """测试API V1"""
        def __init__(self):
            self.version = "1.0"
            self.data = {
                "name": "test",
                "value": 123,
                "options": ["option1", "option2"]
            }
    
    class TestAPI_V2:
        """测试API V2"""
        def __init__(self):
            self.version = "2.0"
            self.data = {
                "name": "test",
                "value": 123,
                "options": [
                    {"name": "option1", "enabled": True},
                    {"name": "option2", "enabled": True}
                ],
                "metadata": {}
            }
    
    # 创建测试对象
    api_v1 = TestAPI_V1()
    api_v2 = TestAPI_V2()
    
    # 检测版本
    v1_version = detector.detect_version(api_v1)
    v2_version = detector.detect_version(api_v2)
    
    logger.info(f"API V1版本: {v1_version}")
    logger.info(f"API V2版本: {v2_version}")
    
    # 创建版本适配器
    adapter = VersionAdapter("1.0", "2.0", "example_adapter")
    
    # 注册版本模式
    adapter.register_version_pattern("1.0", r"1\.0(\.\d+)?")
    adapter.register_version_pattern("2.0", r"2\.0(\.\d+)?")
    
    # 添加版本信息
    adapter.add_version("1.0", {
        "release_date": "2023-01-01",
        "supported": "true",
        "deprecated": "false"
    })
    
    adapter.add_version("2.0", {
        "release_date": "2023-06-01",
        "supported": "true",
        "deprecated": "false"
    })
    
    # 定义适配函数
    def adapt_data_v1_to_v2(data):
        """适配数据从v1到v2"""
        # 创建适配后的数据
        adapted_data = data.copy()
        
        # 转换options字段
        if "options" in adapted_data and isinstance(adapted_data["options"], list):
            adapted_data["options"] = [{"name": option, "enabled": True} for option in adapted_data["options"]]
        
        # 添加metadata字段
        if "metadata" not in adapted_data:
            adapted_data["metadata"] = {}
        
        return adapted_data
    
    # 注册适配函数
    adapter.register_adapter("1.0", "2.0", adapt_data_v1_to_v2)
    
    # 适配数据
    logger.info("适配API V1数据到V2...")
    adapted_data, success, path = adapter.adapt(api_v1.data)
    
    if success:
        logger.info(f"适配成功，路径: {path}")
        logger.info(f"适配前: {api_v1.data}")
        logger.info(f"适配后: {adapted_data}")
    else:
        logger.error(f"适配失败，路径: {path}")
    
    # 创建兼容性测试器
    tester = CompatibilityTester("example_tester")
    
    # 注册适配器
    tester.register_adapter("2.0", adapter)
    
    # 注册测试用例
    tester.register_test_case(
        "test_data_structure",
        "1.0",
        "2.0",
        api_v1.data,
        api_v2.data,
        "测试数据结构适配"
    )
    
    # 运行测试
    logger.info("运行兼容性测试...")
    result = tester.run_test("test_data_structure")
    
    if result["success"]:
        logger.info(f"测试通过: {result['test_id']}")
    else:
        logger.error(f"测试失败: {result['test_id']} - {result.get('error')}")
    
    # 运行所有测试
    results = tester.run_all_tests()
    
    # 获取测试摘要
    summary = tester.get_test_summary()
    
    logger.info(f"测试摘要: {summary}")
    
    logger.info("版本适配器测试完成")

def test_error_handler():
    """测试错误处理器"""
    logger.info("开始测试错误处理器...")
    
    # 创建错误码
    validation_error = ErrorCode(
        401,
        ErrorCategory.VALIDATION,
        ErrorSeverity.ERROR,
        "无效的输入: {input_name}",
        "输入数据不符合预期格式或约束",
        "检查输入数据并确保符合要求"
    )
    
    system_error = ErrorCode(
        101,
        ErrorCategory.SYSTEM,
        ErrorSeverity.CRITICAL,
        "系统内部错误: {operation}",
        "系统内部发生了未预期的错误",
        "请联系系统管理员或开发团队"
    )
    
    # 创建错误码注册表
    registry = ErrorCodeRegistry()
    registry.register(validation_error)
    registry.register(system_error)
    
    # 创建错误处理器
    handler = ErrorHandler("example_handler")
    
    # 注册错误处理函数
    def error_handler(error):
        """错误处理函数"""
        logger.info(f"处理错误: {error}")
    
    handler.register_handler(ErrorSeverity.ERROR, error_handler)
    handler.register_handler(ErrorSeverity.CRITICAL, error_handler)
    
    # 注册错误恢复策略
    handler.register_recovery_strategy(
        401,  # validation_error
        FallbackStrategy("默认值")
    )
    
    handler.register_recovery_strategy(
        101,  # system_error
        RetryStrategy(3, 100, 2.0, 0.1)
    )
    
    # 创建错误
    validation_error_instance = TTError(
        validation_error,
        details={"input_name": "username"}
    )
    
    system_error_instance = TTError(
        system_error,
        details={
            "operation": "save_data",
            "original_func": "test_function",
            "args": "[]"
        }
    )
    
    # 处理错误
    logger.info("处理验证错误...")
    handler.handle(validation_error_instance)
    
    # 恢复错误
    logger.info("恢复验证错误...")
    success, result = handler.recover(validation_error_instance)
    
    if success:
        logger.info(f"恢复成功，结果: {result}")
    else:
        logger.error("恢复失败")
    
    # 处理系统错误
    logger.info("处理系统错误...")
    handler.handle(system_error_instance)
    
    # 恢复系统错误
    logger.info("恢复系统错误...")
    success, result = handler.recover(system_error_instance)
    
    if success:
        logger.info(f"恢复成功，结果: {result}")
    else:
        logger.error("恢复失败")
    
    # 创建断路器
    circuit = CircuitBreaker("example_circuit", 3, 1000)
    
    # 测试断路器
    logger.info("测试断路器...")
    
    for i in range(5):
        if circuit.allow_request():
            logger.info(f"请求 {i+1} 允许")
            
            # 模拟成功或失败
            if random.random() < 0.3:
                logger.info(f"请求 {i+1} 成功")
                circuit.record_success()
            else:
                logger.info(f"请求 {i+1} 失败")
                circuit.record_failure()
        else:
            logger.info(f"请求 {i+1} 被阻止")
    
    # 创建错误报告器
    reporter = ErrorReporter("example_reporter")
    
    # 配置报告器
    reporter.configure(
        report_dir="logs/error_reports",
        min_severity=ErrorSeverity.ERROR,
        report_format="json",
        max_reports=10
    )
    
    # 报告错误
    logger.info("报告错误...")
    report_path = reporter.report(system_error_instance)
    
    if report_path:
        logger.info(f"错误报告已生成: {report_path}")
    else:
        logger.info("没有生成错误报告")
    
    # 刷新报告
    logger.info("刷新错误报告...")
    report_paths = reporter.flush()
    
    if report_paths:
        logger.info(f"错误报告已刷新: {report_paths}")
    else:
        logger.info("没有刷新错误报告")
    
    logger.info("错误处理器测试完成")

def main():
    """主函数"""
    logger.info(f"开始Rust算子示例，Rust可用: {RUST_AVAILABLE}")
    
    # 测试版本适配器
    test_version_adapter()
    
    # 测试错误处理器
    test_error_handler()
    
    logger.info("Rust算子示例结束")

if __name__ == "__main__":
    main()
