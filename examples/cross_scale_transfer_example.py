"""
跨尺度传递优化算子示例

本示例展示了如何使用跨尺度传递优化算子进行跨尺度信息传递优化。
"""

import sys
import os
import time
import logging
import random
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入跨尺度传递优化算子
from src.transcendental_tensor.cross_scale_transfer import (
    ScaleLevel, TransferMode, OptimizationGoal,
    ScaleInfo, TransferChannel, ScaleNetwork, OptimizationResult,
    CrossScaleTransferOptimizer
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def create_test_network() -> ScaleNetwork:
    """
    创建测试网络
    
    返回:
        ScaleNetwork: 测试网络
    """
    # 创建尺度网络
    network = ScaleNetwork()
    
    # 创建尺度信息
    quantum_scale = ScaleInfo(
        level=ScaleLevel.QUANTUM,
        dimension=2,
        resolution=0.1,
        coherence=0.9,
        distribution=0.7,
        self_similarity=0.5
    )
    
    micro_scale = ScaleInfo(
        level=ScaleLevel.MICRO,
        dimension=3,
        resolution=0.5,
        coherence=0.8,
        distribution=0.8,
        self_similarity=0.6
    )
    
    meso_scale = ScaleInfo(
        level=ScaleLevel.MESO,
        dimension=4,
        resolution=1.0,
        coherence=0.7,
        distribution=0.9,
        self_similarity=0.7
    )
    
    macro_scale = ScaleInfo(
        level=ScaleLevel.MACRO,
        dimension=5,
        resolution=2.0,
        coherence=0.6,
        distribution=0.8,
        self_similarity=0.8
    )
    
    holographic_scale = ScaleInfo(
        level=ScaleLevel.HOLOGRAPHIC,
        dimension=6,
        resolution=5.0,
        coherence=0.5,
        distribution=0.7,
        self_similarity=0.9
    )
    
    fractal_scale = ScaleInfo(
        level=ScaleLevel.FRACTAL,
        dimension=7,
        resolution=10.0,
        coherence=0.4,
        distribution=0.6,
        self_similarity=1.0
    )
    
    # 添加尺度信息
    network.add_scale(quantum_scale)
    network.add_scale(micro_scale)
    network.add_scale(meso_scale)
    network.add_scale(macro_scale)
    network.add_scale(holographic_scale)
    network.add_scale(fractal_scale)
    
    # 创建通道
    # 量子 -> 微观
    channel1 = TransferChannel(
        source_level=ScaleLevel.QUANTUM,
        target_level=ScaleLevel.MICRO,
        bandwidth=0.9,
        latency=5.0,
        fidelity=0.95,
        energy_cost=10.0
    )
    
    # 微观 -> 中观
    channel2 = TransferChannel(
        source_level=ScaleLevel.MICRO,
        target_level=ScaleLevel.MESO,
        bandwidth=0.8,
        latency=10.0,
        fidelity=0.9,
        energy_cost=15.0
    )
    
    # 中观 -> 宏观
    channel3 = TransferChannel(
        source_level=ScaleLevel.MESO,
        target_level=ScaleLevel.MACRO,
        bandwidth=0.7,
        latency=15.0,
        fidelity=0.85,
        energy_cost=20.0
    )
    
    # 宏观 -> 全息
    channel4 = TransferChannel(
        source_level=ScaleLevel.MACRO,
        target_level=ScaleLevel.HOLOGRAPHIC,
        bandwidth=0.6,
        latency=20.0,
        fidelity=0.8,
        energy_cost=25.0
    )
    
    # 全息 -> 分形
    channel5 = TransferChannel(
        source_level=ScaleLevel.HOLOGRAPHIC,
        target_level=ScaleLevel.FRACTAL,
        bandwidth=0.5,
        latency=25.0,
        fidelity=0.75,
        energy_cost=30.0
    )
    
    # 分形 -> 全息
    channel6 = TransferChannel(
        source_level=ScaleLevel.FRACTAL,
        target_level=ScaleLevel.HOLOGRAPHIC,
        bandwidth=0.55,
        latency=23.0,
        fidelity=0.78,
        energy_cost=28.0
    )
    
    # 全息 -> 宏观
    channel7 = TransferChannel(
        source_level=ScaleLevel.HOLOGRAPHIC,
        target_level=ScaleLevel.MACRO,
        bandwidth=0.65,
        latency=18.0,
        fidelity=0.83,
        energy_cost=23.0
    )
    
    # 宏观 -> 中观
    channel8 = TransferChannel(
        source_level=ScaleLevel.MACRO,
        target_level=ScaleLevel.MESO,
        bandwidth=0.75,
        latency=13.0,
        fidelity=0.88,
        energy_cost=18.0
    )
    
    # 中观 -> 微观
    channel9 = TransferChannel(
        source_level=ScaleLevel.MESO,
        target_level=ScaleLevel.MICRO,
        bandwidth=0.85,
        latency=8.0,
        fidelity=0.93,
        energy_cost=13.0
    )
    
    # 微观 -> 量子
    channel10 = TransferChannel(
        source_level=ScaleLevel.MICRO,
        target_level=ScaleLevel.QUANTUM,
        bandwidth=0.95,
        latency=3.0,
        fidelity=0.98,
        energy_cost=8.0
    )
    
    # 量子 -> 中观
    channel11 = TransferChannel(
        source_level=ScaleLevel.QUANTUM,
        target_level=ScaleLevel.MESO,
        bandwidth=0.7,
        latency=15.0,
        fidelity=0.85,
        energy_cost=20.0
    )
    
    # 微观 -> 宏观
    channel12 = TransferChannel(
        source_level=ScaleLevel.MICRO,
        target_level=ScaleLevel.MACRO,
        bandwidth=0.6,
        latency=20.0,
        fidelity=0.8,
        energy_cost=25.0
    )
    
    # 中观 -> 全息
    channel13 = TransferChannel(
        source_level=ScaleLevel.MESO,
        target_level=ScaleLevel.HOLOGRAPHIC,
        bandwidth=0.5,
        latency=25.0,
        fidelity=0.75,
        energy_cost=30.0
    )
    
    # 宏观 -> 分形
    channel14 = TransferChannel(
        source_level=ScaleLevel.MACRO,
        target_level=ScaleLevel.FRACTAL,
        bandwidth=0.4,
        latency=30.0,
        fidelity=0.7,
        energy_cost=35.0
    )
    
    # 量子 -> 全息
    channel15 = TransferChannel(
        source_level=ScaleLevel.QUANTUM,
        target_level=ScaleLevel.HOLOGRAPHIC,
        bandwidth=0.5,
        latency=25.0,
        fidelity=0.75,
        energy_cost=30.0
    )
    
    # 微观 -> 分形
    channel16 = TransferChannel(
        source_level=ScaleLevel.MICRO,
        target_level=ScaleLevel.FRACTAL,
        bandwidth=0.4,
        latency=30.0,
        fidelity=0.7,
        energy_cost=35.0
    )
    
    # 全息 -> 量子
    channel17 = TransferChannel(
        source_level=ScaleLevel.HOLOGRAPHIC,
        target_level=ScaleLevel.QUANTUM,
        bandwidth=0.6,
        latency=20.0,
        fidelity=0.8,
        energy_cost=25.0
    )
    
    # 分形 -> 微观
    channel18 = TransferChannel(
        source_level=ScaleLevel.FRACTAL,
        target_level=ScaleLevel.MICRO,
        bandwidth=0.5,
        latency=25.0,
        fidelity=0.75,
        energy_cost=30.0
    )
    
    # 添加通道
    network.add_channel(channel1)
    network.add_channel(channel2)
    network.add_channel(channel3)
    network.add_channel(channel4)
    network.add_channel(channel5)
    network.add_channel(channel6)
    network.add_channel(channel7)
    network.add_channel(channel8)
    network.add_channel(channel9)
    network.add_channel(channel10)
    network.add_channel(channel11)
    network.add_channel(channel12)
    network.add_channel(channel13)
    network.add_channel(channel14)
    network.add_channel(channel15)
    network.add_channel(channel16)
    network.add_channel(channel17)
    network.add_channel(channel18)
    
    return network


def print_optimization_result(result: OptimizationResult, network: ScaleNetwork) -> None:
    """
    打印优化结果
    
    参数:
        result (OptimizationResult): 优化结果
        network (ScaleNetwork): 尺度网络
    """
    logger.info(f"Optimization result:")
    logger.info(f"  Goal: {result.goal.value}")
    logger.info(f"  Mode: {result.mode.value}")
    logger.info(f"  Optimized channels: {len(result.optimized_channels)}")
    
    # 打印优化的通道
    logger.info("  Optimized channels:")
    for i, channel_id in enumerate(result.optimized_channels[:5]):
        channel = network.get_channel(channel_id)
        if channel:
            logger.info(f"    {i+1}. {channel.source_level.value} -> {channel.target_level.value} "
                       f"(bandwidth={channel.bandwidth:.2f}, latency={channel.latency:.2f}, "
                       f"fidelity={channel.fidelity:.2f}, energy_cost={channel.energy_cost:.2f})")
    
    if len(result.optimized_channels) > 5:
        logger.info(f"    ... and {len(result.optimized_channels) - 5} more")
    
    # 打印指标
    logger.info("  Metrics:")
    for key, value in result.metrics.items():
        logger.info(f"    {key}: {value:.4f}")


def main():
    """主函数"""
    logger.info("Starting cross-scale transfer optimization example")
    
    # 创建测试网络
    network = create_test_network()
    
    logger.info(f"Created test network with {len(network.scales)} scales and {len(network.channels)} channels")
    
    # 创建跨尺度传递优化算子
    optimizer = CrossScaleTransferOptimizer()
    
    # 效率优化
    logger.info("Optimizing for EFFICIENCY")
    optimizer.parameters["goal"] = OptimizationGoal.EFFICIENCY.value
    optimizer.parameters["mode"] = TransferMode.ADAPTIVE.value
    
    result = optimizer.optimize(network)
    
    print_optimization_result(result, network)
    
    # 保真度优化
    logger.info("Optimizing for FIDELITY")
    optimizer.parameters["goal"] = OptimizationGoal.FIDELITY.value
    optimizer.parameters["mode"] = TransferMode.ADAPTIVE.value
    
    result = optimizer.optimize(network)
    
    print_optimization_result(result, network)
    
    # 带宽优化
    logger.info("Optimizing for BANDWIDTH")
    optimizer.parameters["goal"] = OptimizationGoal.BANDWIDTH.value
    optimizer.parameters["mode"] = TransferMode.BOTTOM_UP.value
    
    result = optimizer.optimize(network)
    
    print_optimization_result(result, network)
    
    # 延迟优化
    logger.info("Optimizing for LATENCY")
    optimizer.parameters["goal"] = OptimizationGoal.LATENCY.value
    optimizer.parameters["mode"] = TransferMode.TOP_DOWN.value
    
    result = optimizer.optimize(network)
    
    print_optimization_result(result, network)
    
    # 能耗优化
    logger.info("Optimizing for ENERGY")
    optimizer.parameters["goal"] = OptimizationGoal.ENERGY.value
    optimizer.parameters["mode"] = TransferMode.BIDIRECTIONAL.value
    
    result = optimizer.optimize(network)
    
    print_optimization_result(result, network)
    
    # 平衡优化
    logger.info("Optimizing for BALANCED")
    optimizer.parameters["goal"] = OptimizationGoal.BALANCED.value
    optimizer.parameters["mode"] = TransferMode.RESONANT.value
    
    result = optimizer.optimize(network)
    
    print_optimization_result(result, network)
    
    logger.info("Cross-scale transfer optimization example completed")


if __name__ == "__main__":
    main()
