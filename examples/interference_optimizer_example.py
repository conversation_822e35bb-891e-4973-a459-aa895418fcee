"""
非线性干涉优化算法示例

本脚本展示了如何使用非线性干涉优化算法。
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import time

from src.algorithms.interference import (
    NonlinearInterferenceOptimizer,
    InterferencePatternGenerator,
    InterferenceResultAnalyzer
)


def create_quantum_state(size=10):
    """创建量子态"""
    # 创建一个随机量子态
    state = np.random.normal(0, 1, size) + 1j * np.random.normal(0, 1, size)
    
    # 归一化
    norm = np.linalg.norm(state)
    state = state / norm
    
    return state


def create_holographic_state(size=10):
    """创建全息态"""
    # 创建一个随机全息态
    state = np.random.normal(0, 1, size) + 1j * np.random.normal(0, 1, size)
    
    # 归一化
    norm = np.linalg.norm(state)
    state = state / norm
    
    return state


def run_optimization(quantum_state, holographic_state, output_dir=None):
    """运行优化"""
    # 创建输出目录
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # 创建优化器
    optimizer = NonlinearInterferenceOptimizer(
        lambda_init=0.5,
        max_iterations=50,
        tolerance=1e-6,
        use_parallel=True,
        num_workers=4,
        adaptive_lambda=True,
        save_intermediate=bool(output_dir),
        intermediate_dir=output_dir
    )
    
    # 创建结果分析器
    analyzer = InterferenceResultAnalyzer(verbose=True)
    
    # 执行优化
    print("开始优化...")
    start_time = time.time()
    result = optimizer.compute((quantum_state, holographic_state))
    end_time = time.time()
    print(f"优化完成，耗时: {end_time - start_time:.2f}秒")
    
    # 提取结果
    fused_state = result['fused_state']
    lambda_param = result['lambda_param']
    iterations = result['iterations']
    convergence_achieved = result['convergence_achieved']
    entropy_loss = result['entropy_loss']
    performance = result['performance']
    
    # 打印结果
    print(f"迭代次数: {iterations}")
    print(f"是否收敛: {convergence_achieved}")
    print(f"最终lambda参数: {lambda_param:.6f}")
    print(f"最终熵损失: {entropy_loss:.6f}")
    
    # 分析结果
    analysis = analyzer.analyze(quantum_state, holographic_state, fused_state, lambda_param)
    
    # 绘制状态比较图
    fig_states = analyzer.plot_states(quantum_state, holographic_state, fused_state)
    if output_dir:
        fig_states.savefig(os.path.join(output_dir, 'states_comparison.png'))
    
    # 绘制熵损失历史图
    fig_entropy = analyzer.plot_entropy_history(performance['entropy_history'])
    if output_dir:
        fig_entropy.savefig(os.path.join(output_dir, 'entropy_history.png'))
    
    # 绘制Lambda参数历史图
    fig_lambda = analyzer.plot_lambda_history(performance['lambda_history'])
    if output_dir:
        fig_lambda.savefig(os.path.join(output_dir, 'lambda_history.png'))
    
    # 显示图形
    plt.show()
    
    return result, analysis


def main():
    """主函数"""
    # 设置随机种子，确保结果可重现
    np.random.seed(42)
    
    # 创建输出目录
    output_dir = 'output/interference_optimizer'
    
    # 创建量子态和全息态
    quantum_state = create_quantum_state(size=20)
    holographic_state = create_holographic_state(size=20)
    
    # 运行优化
    result, analysis = run_optimization(quantum_state, holographic_state, output_dir)
    
    # 打印分析结果
    print("\n分析结果:")
    for key, value in analysis.items():
        print(f"{key}: {value:.6f}")


if __name__ == '__main__':
    main()
