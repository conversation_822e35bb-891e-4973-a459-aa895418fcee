discovery:
  exclude_patterns:
  - test
  - tests
  - example
  - examples
  include_patterns: []
  recursive: true
logging:
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  level: INFO
performance:
  cache:
    enabled: true
    max_size: 1000
    ttl: 3600
  parallel:
    enabled: true
    max_workers: null
registries:
  algorithm:
    auto_discover: true
    discover_packages:
    - src.algorithms
    enabled: true
  model:
    auto_discover: true
    discover_packages:
    - src.models
    enabled: true
  numpy:
    auto_discover: true
    discover_packages:
    - src.numpy_extensions
    enabled: true
  operator:
    auto_discover: true
    discover_packages:
    - src.operators
    enabled: true
  utility:
    auto_discover: true
    discover_packages:
    - src.utils
    enabled: true
security:
  access_control:
    default_level: PUBLIC
    enabled: false
  audit_log:
    enabled: false
    log_file: null
  signature_verification:
    enabled: false
    secret_key: null
