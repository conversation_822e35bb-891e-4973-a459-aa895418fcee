#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
超越态计算框架GIL装饰器示例程序

演示如何使用超越态计算框架专用的GIL装饰器优化计算性能
"""

import os
import sys
import time
import numpy as np
import threading
import logging
from typing import List, Dict, Any, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入GIL装饰器
try:
    from src.core.parallel.tte_gil_decorator import (
        quantum_task, holographic_task, fractal_task, parallel_task,
        compute_intensive, get_performance_data
    )
    from src.core.parallel.gil_manager import get_gil_status
except ImportError as e:
    logger.warning(f"无法导入GIL装饰器模块: {e}")
    
    # 创建兼容性装饰器
    def quantum_task(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    
    def holographic_task(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    
    def fractal_task(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    
    def parallel_task(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    
    def compute_intensive(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    
    def get_performance_data(*args, **kwargs):
        return {}
    
    def get_gil_status():
        return {
            'has_nogil_support': False,
            'is_nogil_build': False,
            'is_nogil_enabled': False,
            'has_np_gil_state': False
        }


class SuperpositionProcessor:
    """超越态叠加处理器"""
    
    @quantum_task(task_size='medium', adaptive=True, log_performance=True)
    def process_quantum_superposition(self, size: int = 1000, iterations: int = 5) -> np.ndarray:
        """
        处理量子态叠加
        
        Args:
            size: 量子态大小
            iterations: 迭代次数
            
        Returns:
            处理后的量子态
        """
        logger.info(f"处理量子态叠加 (大小: {size}, 迭代: {iterations})")
        
        result = None
        for i in range(iterations):
            # 创建随机量子态
            state1 = np.random.random(size) + 1j * np.random.random(size)
            state1 = state1 / np.linalg.norm(state1)
            
            state2 = np.random.random(size) + 1j * np.random.random(size)
            state2 = state2 / np.linalg.norm(state2)
            
            # 计算叠加态
            alpha = np.random.random()
            beta = np.sqrt(1 - alpha**2)
            
            superposition = alpha * state1 + beta * state2
            superposition = superposition / np.linalg.norm(superposition)
            
            # 应用随机酉变换
            h = np.random.random((size, size)) + 1j * np.random.random((size, size))
            u = np.eye(size) + 1j * h
            u = u / np.linalg.norm(u, ord=2)
            
            result = np.dot(u, superposition)
        
        return result
    
    @holographic_task(task_size='medium', adaptive=True, log_performance=True)
    def process_holographic_reconstruction(self, size: int = 100, iterations: int = 3) -> np.ndarray:
        """
        处理全息重建
        
        Args:
            size: 全息图大小
            iterations: 迭代次数
            
        Returns:
            重建后的全息图
        """
        logger.info(f"处理全息重建 (大小: {size}, 迭代: {iterations})")
        
        # 创建随机全息数据
        hologram = np.random.random((size, size)) * np.exp(1j * np.random.random((size, size)) * 2 * np.pi)
        
        for _ in range(iterations):
            # 应用傅里叶变换
            transformed = np.fft.fft2(hologram)
            transformed = np.fft.fftshift(transformed)
            
            # 应用非线性变换
            phase = np.angle(transformed)
            amplitude = np.abs(transformed)
            
            # 非线性调制
            modulated_amplitude = np.tanh(amplitude / np.mean(amplitude))
            
            # 重构全息图
            hologram = modulated_amplitude * np.exp(1j * phase)
            
            # 应用逆变换
            hologram = np.fft.ifftshift(hologram)
            hologram = np.fft.ifft2(hologram)
        
        return hologram
    
    @fractal_task(task_size='medium', adaptive=True, log_performance=True)
    def generate_fractal_pattern(self, size: int = 200, iterations: int = 20) -> np.ndarray:
        """
        生成分形图案
        
        Args:
            size: 图案大小
            iterations: 迭代次数
            
        Returns:
            分形图案
        """
        logger.info(f"生成分形图案 (大小: {size}, 迭代: {iterations})")
        
        # 创建初始网格
        grid = np.zeros((size, size), dtype=np.int32)
        center = size // 2
        grid[center, center] = 1
        
        # 分形生长模拟
        for _ in range(iterations):
            # 创建新网格以存储更新
            new_grid = grid.copy()
            
            # 遍历网格
            for i in range(1, size-1):
                for j in range(1, size-1):
                    # 计算邻居数量
                    neighbors = np.sum(grid[i-1:i+2, j-1:j+2]) - grid[i, j]
                    
                    # 应用生长规则
                    if grid[i, j] == 0 and neighbors == 1:
                        new_grid[i, j] = 1
            
            # 更新网格
            grid = new_grid
        
        return grid
    
    @parallel_task(task_size='medium', adaptive=True, log_performance=True)
    def parallel_field_evolution(self, size: int = 50, num_threads: int = 4, iterations: int = 5) -> np.ndarray:
        """
        并行场态演化
        
        Args:
            size: 场态大小
            num_threads: 线程数
            iterations: 迭代次数
            
        Returns:
            演化后的场态
        """
        logger.info(f"并行场态演化 (大小: {size}, 线程数: {num_threads}, 迭代: {iterations})")
        
        # 创建随机场态
        field = np.random.random((size, size, size))
        
        # 将场分割为多个区域
        chunks = []
        chunk_size = size // num_threads
        for i in range(num_threads):
            start = i * chunk_size
            end = (i + 1) * chunk_size if i < num_threads - 1 else size
            chunks.append((start, end))
        
        results = [None] * num_threads
        
        # 定义演化函数
        def evolve_chunk(field, start, end, chunk_id):
            chunk = field[start:end].copy()
            # 应用拉普拉斯算子（简化版）
            for _ in range(iterations):
                for i in range(1, chunk.shape[0]-1):
                    for j in range(1, chunk.shape[1]-1):
                        for k in range(1, chunk.shape[2]-1):
                            chunk[i, j, k] = 0.1 * (
                                chunk[i+1, j, k] + chunk[i-1, j, k] +
                                chunk[i, j+1, k] + chunk[i, j-1, k] +
                                chunk[i, j, k+1] + chunk[i, j, k-1] -
                                6 * chunk[i, j, k]
                            )
            results[chunk_id] = chunk
        
        # 执行并行演化
        threads = []
        for i, (start, end) in enumerate(chunks):
            t = threading.Thread(target=evolve_chunk, args=(field, start, end, i))
            threads.append(t)
            t.start()
        
        for t in threads:
            t.join()
        
        # 合并结果
        for i, (start, end) in enumerate(chunks):
            if results[i] is not None:
                field[start:end] = results[i]
        
        return field


def run_benchmark(iterations: int = 3):
    """
    运行基准测试
    
    Args:
        iterations: 迭代次数
    """
    # 打印系统信息
    print(f"Python版本: {sys.version}")
    print(f"NumPy版本: {np.__version__}")
    print(f"CPU核心数: {os.cpu_count()}")
    print(f"GIL状态: {get_gil_status()}")
    
    # 创建处理器实例
    processor = SuperpositionProcessor()
    
    # 测试量子态叠加处理
    print("\n=== 测试量子态叠加处理 ===")
    sizes = [500, 1000, 2000]
    for size in sizes:
        start_time = time.time()
        processor.process_quantum_superposition(size, iterations)
        elapsed = time.time() - start_time
        print(f"大小: {size}, 总耗时: {elapsed:.6f} 秒")
        
        # 获取性能数据
        try:
            perf_data = processor.process_quantum_superposition.get_performance_data()
            print(f"性能数据: {perf_data}")
        except AttributeError:
            pass
    
    # 测试全息重建处理
    print("\n=== 测试全息重建处理 ===")
    sizes = [50, 100, 200]
    for size in sizes:
        start_time = time.time()
        processor.process_holographic_reconstruction(size, iterations)
        elapsed = time.time() - start_time
        print(f"大小: {size}, 总耗时: {elapsed:.6f} 秒")
        
        # 获取性能数据
        try:
            perf_data = processor.process_holographic_reconstruction.get_performance_data()
            print(f"性能数据: {perf_data}")
        except AttributeError:
            pass
    
    # 测试分形图案生成
    print("\n=== 测试分形图案生成 ===")
    sizes = [100, 200, 300]
    for size in sizes:
        start_time = time.time()
        processor.generate_fractal_pattern(size, iterations)
        elapsed = time.time() - start_time
        print(f"大小: {size}, 总耗时: {elapsed:.6f} 秒")
        
        # 获取性能数据
        try:
            perf_data = processor.generate_fractal_pattern.get_performance_data()
            print(f"性能数据: {perf_data}")
        except AttributeError:
            pass
    
    # 测试并行场态演化
    print("\n=== 测试并行场态演化 ===")
    thread_counts = [2, 4, 8]
    for threads in thread_counts:
        start_time = time.time()
        processor.parallel_field_evolution(50, threads, iterations)
        elapsed = time.time() - start_time
        print(f"线程数: {threads}, 总耗时: {elapsed:.6f} 秒")
        
        # 获取性能数据
        try:
            perf_data = processor.parallel_field_evolution.get_performance_data()
            print(f"性能数据: {perf_data}")
        except AttributeError:
            pass
    
    # 打印全局性能数据
    print("\n=== 全局性能数据 ===")
    all_perf_data = get_performance_data()
    for func_key, strategies_data in all_perf_data.items():
        print(f"\n函数: {func_key}")
        for strategy, data in strategies_data.items():
            print(f"  策略: {strategy}")
            print(f"    调用次数: {data['calls']}")
            print(f"    平均时间: {data['avg_time']:.6f} 秒")
            print(f"    最小时间: {data['min_time']:.6f} 秒")
            print(f"    最大时间: {data['max_time']:.6f} 秒")


if __name__ == "__main__":
    run_benchmark()
