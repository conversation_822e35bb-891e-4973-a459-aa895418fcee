"""
量子演化示例

这个示例展示了如何使用量子态表示、量子门操作和量子演化模块。
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import List, Tuple

from src.core.quantum.state import QuantumStateArrow
from src.core.quantum.operators import HadamardGate, PauliXGate, PauliZGate, PhaseGate, RotationGate
from src.core.quantum.evolution import QuantumCircuit, TimeEvolution
from src.core.quantum.measurement import ProjectiveMeasurement


def create_bell_state() -> Tuple[np.ndarray, str]:
    """
    创建Bell态 |Φ⁺⟩ = (|00⟩ + |11⟩)/√2
    
    Returns:
        Bell态的NumPy数组表示和描述
    """
    # 创建|00⟩
    state_00 = QuantumStateArrow.create_basis_state(4, 0)
    
    # 创建量子线路
    circuit = QuantumCircuit(2)
    
    # 添加Hadamard门到第一个量子比特
    h_gate = HadamardGate()
    circuit.add_gate(h_gate, 0)
    
    # 添加CNOT门
    from src.core.quantum.operators import CNOTGate
    cnot_gate = CNOTGate()
    circuit.add_gate(cnot_gate, (0, 1))
    
    # 应用量子线路
    bell_state = circuit.apply(state_00)
    
    # 转换为NumPy数组
    bell_array = QuantumStateArrow.to_numpy(bell_state)
    
    return bell_array, "Bell态 |Φ⁺⟩ = (|00⟩ + |11⟩)/√2"


def evolve_superposition(steps: int = 10) -> Tuple[List[np.ndarray], List[float], str]:
    """
    演化叠加态 |+⟩ = (|0⟩ + |1⟩)/√2
    
    Args:
        steps: 演化步数
        
    Returns:
        演化过程中的状态列表、时间列表和描述
    """
    # 创建|+⟩ = (|0⟩ + |1⟩)/√2
    state_plus = QuantumStateArrow.create_superposition(2, [0, 1], [1, 1])
    
    # 创建哈密顿量（Pauli-Z）
    hamiltonian = np.array([[1, 0], [0, -1]], dtype=np.complex128)
    
    # 创建时间演化
    evolution = TimeEvolution(hamiltonian=hamiltonian)
    
    # 演化
    states = []
    times = []
    
    # 转换为NumPy数组
    initial_array = QuantumStateArrow.to_numpy(state_plus)
    states.append(initial_array)
    times.append(0.0)
    
    # 演化步长
    time_step = np.pi / steps
    
    # 演化过程
    current_state = state_plus
    for i in range(1, steps + 1):
        # 演化一步
        current_time = i * time_step
        current_state = evolution.evolve(state_plus, current_time)
        
        # 转换为NumPy数组
        current_array = QuantumStateArrow.to_numpy(current_state)
        states.append(current_array)
        times.append(current_time)
    
    return states, times, "叠加态 |+⟩ 在Pauli-Z哈密顿量下的演化"


def measure_superposition(trials: int = 1000) -> Tuple[List[int], str]:
    """
    测量叠加态 |+⟩ = (|0⟩ + |1⟩)/√2
    
    Args:
        trials: 测量次数
        
    Returns:
        测量结果列表和描述
    """
    # 创建|+⟩ = (|0⟩ + |1⟩)/√2
    state_plus = QuantumStateArrow.create_superposition(2, [0, 1], [1, 1])
    
    # 创建计算基底测量
    measurement = ProjectiveMeasurement(basis="computational")
    
    # 测量多次
    results = []
    for _ in range(trials):
        result, _, _ = measurement.measure(state_plus)
        results.append(result)
    
    return results, "叠加态 |+⟩ 在计算基底下的测量结果"


def plot_state_evolution(states: List[np.ndarray], times: List[float], title: str):
    """
    绘制量子态演化过程
    
    Args:
        states: 量子态列表
        times: 时间列表
        title: 图表标题
    """
    plt.figure(figsize=(10, 6))
    
    # 提取实部和虚部
    real_parts = []
    imag_parts = []
    
    for state in states:
        real_parts.append([s.real for s in state])
        imag_parts.append([s.imag for s in state])
    
    # 绘制实部
    plt.subplot(2, 1, 1)
    for i in range(len(states[0])):
        plt.plot(times, [real_parts[j][i] for j in range(len(times))], label=f"|{i}⟩ 实部")
    
    plt.title(f"{title} - 实部")
    plt.xlabel("时间")
    plt.ylabel("振幅实部")
    plt.legend()
    plt.grid(True)
    
    # 绘制虚部
    plt.subplot(2, 1, 2)
    for i in range(len(states[0])):
        plt.plot(times, [imag_parts[j][i] for j in range(len(times))], label=f"|{i}⟩ 虚部")
    
    plt.title(f"{title} - 虚部")
    plt.xlabel("时间")
    plt.ylabel("振幅虚部")
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig("quantum_evolution.png")
    plt.close()


def plot_measurement_results(results: List[int], title: str):
    """
    绘制测量结果直方图
    
    Args:
        results: 测量结果列表
        title: 图表标题
    """
    plt.figure(figsize=(8, 6))
    
    # 计算结果统计
    unique, counts = np.unique(results, return_counts=True)
    
    # 绘制直方图
    plt.bar(unique, counts / len(results))
    
    plt.title(title)
    plt.xlabel("测量结果")
    plt.ylabel("概率")
    plt.xticks(unique)
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig("quantum_measurement.png")
    plt.close()


def main():
    """主函数"""
    print("量子演化示例")
    print("=" * 50)
    
    # 创建Bell态
    bell_state, bell_desc = create_bell_state()
    print(f"\n{bell_desc}")
    print(f"Bell态: {bell_state}")
    
    # 演化叠加态
    states, times, evolution_desc = evolve_superposition(steps=20)
    print(f"\n{evolution_desc}")
    print(f"初始态: {states[0]}")
    print(f"最终态: {states[-1]}")
    
    # 测量叠加态
    results, measurement_desc = measure_superposition(trials=1000)
    print(f"\n{measurement_desc}")
    count_0 = results.count(0)
    count_1 = results.count(1)
    print(f"测量结果: |0⟩: {count_0} 次 ({count_0/len(results):.2%}), |1⟩: {count_1} 次 ({count_1/len(results):.2%})")
    
    # 绘制演化过程
    plot_state_evolution(states, times, evolution_desc)
    print("\n已保存演化过程图表到 quantum_evolution.png")
    
    # 绘制测量结果
    plot_measurement_results(results, measurement_desc)
    print("已保存测量结果图表到 quantum_measurement.png")


if __name__ == "__main__":
    main()
