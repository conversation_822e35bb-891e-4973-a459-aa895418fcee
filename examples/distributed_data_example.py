#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
超越态计算框架 - 分布式数据处理示例

本示例展示了如何使用超越态计算框架的分布式数据管理功能，
包括启动服务器、传输复数数组、量子态和张量数据等。

依赖:
- Python 3.13+
- NumPy 2.2.5+
- PyArrow 14.0.0+
- Arrow Flight 14.0.0+
"""

import os
import time
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

# 导入分布式数据管理模块
from src.core.data.distributed.arrow_flight_service import (
    DistributedDataManager, start_flight_server
)


def complex_array_example(manager):
    """复数数组分布式处理示例"""
    print("\n=== 复数数组分布式处理示例 ===")
    
    # 创建复数数组
    complex64_data = np.array([1+2j, 3+4j, 5+6j], dtype=np.complex64)
    complex128_data = np.array([1+2j, 3+4j, 5+6j], dtype=np.complex128)
    
    print(f"原始 complex64 数据: {complex64_data}")
    print(f"原始 complex128 数据: {complex128_data}")
    
    # 上传数据
    path1 = manager.upload_numpy_array("complex64_data", complex64_data, 
                                      {"description": "测试复数数组 (complex64)"})
    path2 = manager.upload_numpy_array("complex128_data", complex128_data, 
                                      {"description": "测试复数数组 (complex128)"})
    
    print(f"数据已上传: {path1}, {path2}")
    
    # 列出数据集
    datasets = manager.list_datasets()
    print(f"可用数据集: {datasets}")
    
    # 获取数据集信息
    info1 = manager.get_dataset_info(path1)
    print(f"数据集信息: {info1}")
    
    # 下载数据
    downloaded_data1, metadata1 = manager.download_numpy_array(path1)
    downloaded_data2, metadata2 = manager.download_numpy_array(path2)
    
    print(f"下载的 complex64 数据: {downloaded_data1}")
    print(f"下载的 complex128 数据: {downloaded_data2}")
    
    # 验证数据一致性
    assert np.allclose(complex64_data, downloaded_data1)
    assert np.allclose(complex128_data, downloaded_data2)
    print("数据一致性验证通过")


def quantum_state_example(manager):
    """量子态分布式处理示例"""
    print("\n=== 量子态分布式处理示例 ===")
    
    # 创建量子态
    # |0⟩ 状态
    state1 = np.array([[1.0, 0.0], [0.0, 0.0]], dtype=np.float64)
    # |1⟩ 状态
    state2 = np.array([[0.0, 0.0], [1.0, 0.0]], dtype=np.float64)
    # |+⟩ 状态 (|0⟩ + |1⟩)/√2
    state3 = np.array([[1/np.sqrt(2), 0.0], [1/np.sqrt(2), 0.0]], dtype=np.float64)
    
    print(f"量子态 |0⟩: {state1}")
    print(f"量子态 |1⟩: {state2}")
    print(f"量子态 |+⟩: {state3}")
    
    # 上传量子态
    path1 = manager.upload_quantum_state("state_0", state1, 
                                        metadata={"description": "基态 |0⟩"})
    path2 = manager.upload_quantum_state("state_1", state2, 
                                        metadata={"description": "基态 |1⟩"})
    path3 = manager.upload_quantum_state("state_plus", state3, 
                                        metadata={"description": "叠加态 |+⟩"})
    
    print(f"量子态已上传: {path1}, {path2}, {path3}")
    
    # 下载量子态
    downloaded_state1, metadata1 = manager.download_numpy_array(path1)
    downloaded_state2, metadata2 = manager.download_numpy_array(path2)
    downloaded_state3, metadata3 = manager.download_numpy_array(path3)
    
    print(f"下载的量子态 |0⟩: {downloaded_state1}")
    print(f"下载的量子态 |1⟩: {downloaded_state2}")
    print(f"下载的量子态 |+⟩: {downloaded_state3}")
    
    # 验证数据一致性
    assert np.allclose(state1, downloaded_state1)
    assert np.allclose(state2, downloaded_state2)
    assert np.allclose(state3, downloaded_state3)
    print("量子态数据一致性验证通过")


def tensor_example(manager):
    """张量分布式处理示例"""
    print("\n=== 张量分布式处理示例 ===")
    
    # 创建张量数据
    tensor1 = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], dtype=np.float64)
    tensor2 = np.array([[[1.0, 2.0], [3.0, 4.0]], [[5.0, 6.0], [7.0, 8.0]]], dtype=np.float64)
    
    print(f"张量 1 形状: {tensor1.shape}, 数据: \n{tensor1}")
    print(f"张量 2 形状: {tensor2.shape}, 数据: \n{tensor2}")
    
    # 上传张量
    path1 = manager.upload_tensor("tensor_2d", tensor1, 
                                 metadata={"description": "2D张量"})
    path2 = manager.upload_tensor("tensor_3d", tensor2, 
                                 metadata={"description": "3D张量"})
    
    print(f"张量已上传: {path1}, {path2}")
    
    # 下载张量
    downloaded_tensor1, metadata1 = manager.download_numpy_array(path1)
    downloaded_tensor2, metadata2 = manager.download_numpy_array(path2)
    
    print(f"下载的张量 1 形状: {downloaded_tensor1.shape}")
    print(f"下载的张量 2 形状: {downloaded_tensor2.shape}")
    
    # 验证数据一致性
    assert np.allclose(tensor1, downloaded_tensor1)
    assert np.allclose(tensor2, downloaded_tensor2)
    print("张量数据一致性验证通过")


def large_data_example(manager):
    """大规模数据分布式处理示例"""
    print("\n=== 大规模数据分布式处理示例 ===")
    
    # 创建大规模数据
    size = 1_000_000
    large_array = np.random.random(size) + 1j * np.random.random(size)
    large_array = large_array.astype(np.complex128)
    
    print(f"大规模数据形状: {large_array.shape}, 类型: {large_array.dtype}")
    
    # 测量上传时间
    start_time = time.time()
    path = manager.upload_numpy_array("large_data", large_array, 
                                     metadata={"description": "大规模复数数据"})
    upload_time = time.time() - start_time
    
    print(f"大规模数据已上传: {path}, 耗时: {upload_time:.2f}秒")
    
    # 测量下载时间
    start_time = time.time()
    downloaded_array, metadata = manager.download_numpy_array(path)
    download_time = time.time() - start_time
    
    print(f"大规模数据已下载, 形状: {downloaded_array.shape}, 耗时: {download_time:.2f}秒")
    
    # 验证数据一致性
    assert np.allclose(large_array[:10], downloaded_array[:10])  # 只检查前10个元素，以节省时间
    print("大规模数据一致性验证通过")
    
    # 获取服务器统计信息
    stats = manager.get_server_stats()
    print(f"服务器统计信息: {stats}")


def server_operations_example(manager):
    """服务器操作示例"""
    print("\n=== 服务器操作示例 ===")
    
    # 列出所有数据集
    datasets = manager.list_datasets()
    print(f"当前数据集: {datasets}")
    
    # 移除一个数据集
    if datasets:
        result = manager.remove_dataset(datasets[0])
        print(f"移除数据集结果: {result}")
    
    # 再次列出数据集
    datasets = manager.list_datasets()
    print(f"移除后的数据集: {datasets}")
    
    # 清除所有数据集
    result = manager.clear_all_datasets()
    print(f"清除所有数据集结果: {result}")
    
    # 确认所有数据集已清除
    datasets = manager.list_datasets()
    print(f"清除后的数据集: {datasets}")
    assert len(datasets) == 0
    print("所有数据集已成功清除")


def multi_node_simulation(server_port=8815, client_port=8816):
    """多节点模拟示例
    
    模拟一个服务器节点和一个客户端节点之间的数据传输。
    
    Args:
        server_port: 服务器端口
        client_port: 客户端端口
    """
    print("\n=== 多节点模拟示例 ===")
    
    # 启动服务器
    server_location = f"grpc://0.0.0.0:{server_port}"
    server = start_flight_server(server_location)
    print(f"服务器已启动: {server_location}")
    
    # 创建客户端
    client_location = f"grpc://localhost:{server_port}"
    client_manager = DistributedDataManager(client_location)
    print(f"客户端已连接: {client_location}")
    
    try:
        # 创建测试数据
        test_data = np.array([1+2j, 3+4j, 5+6j], dtype=np.complex128)
        
        # 上传数据
        path = client_manager.upload_numpy_array("test_data", test_data, 
                                               metadata={"description": "测试数据"})
        print(f"数据已上传: {path}")
        
        # 列出数据集
        datasets = client_manager.list_datasets()
        print(f"可用数据集: {datasets}")
        
        # 下载数据
        downloaded_data, metadata = client_manager.download_numpy_array(path)
        print(f"下载的数据: {downloaded_data}")
        
        # 验证数据一致性
        assert np.allclose(test_data, downloaded_data)
        print("数据一致性验证通过")
        
        # 清除数据
        result = client_manager.clear_all_datasets()
        print(f"清除所有数据集结果: {result}")
    
    finally:
        # 关闭客户端
        client_manager.close()
        print("客户端已关闭")
        
        # 停止服务器
        server.shutdown()
        print("服务器已停止")


def main():
    """主函数"""
    print("超越态计算框架 - 分布式数据处理示例")
    
    # 创建分布式数据管理器
    manager = DistributedDataManager(start_server=True)
    print("分布式数据管理器已初始化")
    
    try:
        # 运行示例
        complex_array_example(manager)
        quantum_state_example(manager)
        tensor_example(manager)
        large_data_example(manager)
        server_operations_example(manager)
        
        # 多节点模拟
        multi_node_simulation(server_port=8817, client_port=8818)
        
    finally:
        # 关闭管理器
        manager.close()
        print("分布式数据管理器已关闭")
    
    print("\n所有示例运行完成")


if __name__ == "__main__":
    main()
