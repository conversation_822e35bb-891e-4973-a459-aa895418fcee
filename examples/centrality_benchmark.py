#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超越态思维引擎 - 中心性算子性能测试
"""

import time
import random
import numpy as np
import matplotlib.pyplot as plt
from src.operators import operator_registry

def generate_random_network(n_nodes, edge_probability=0.1, weighted=True):
    """生成随机网络"""
    nodes = {}
    
    # 创建节点
    for i in range(n_nodes):
        node_id = f"node_{i}"
        connections = {}
        
        # 添加连接
        for j in range(n_nodes):
            if i != j and random.random() < edge_probability:
                if weighted:
                    weight = random.uniform(0.1, 1.0)
                else:
                    weight = 1.0
                
                connections[f"node_{j}"] = weight
        
        nodes[node_id] = connections
    
    return nodes

def benchmark_centrality(nodes, centrality_type, use_optimized=False):
    """测试中心性算子性能"""
    # 获取中心性算子
    if use_optimized:
        centrality_class = operator_registry.get_operator("resonance_network", "centrality_optimized")
        if centrality_class is None:
            print("优化版中心性算子不可用，请确保已编译Rust绑定")
            return None
        
        # 创建配置
        config = centrality_class.PyCentralityConfig(
            centrality_type=centrality_type,
            use_edge_weights=True,
            normalize=True,
            max_iterations=100,
            convergence_threshold=1e-6,
            damping_factor=0.85,
            use_multithreading=True,
            compute_device="cpu",
            use_simd=True,
            use_sparse_matrix=True,
            batch_size=64
        )
    else:
        centrality_class = operator_registry.get_operator("resonance_network", "centrality")
        if centrality_class is None:
            print("中心性算子不可用，请确保已编译Rust绑定")
            return None
        
        # 创建配置
        config = centrality_class.PyCentralityConfig(
            centrality_type=centrality_type,
            use_edge_weights=True,
            normalize=True,
            max_iterations=100,
            convergence_threshold=1e-6,
            damping_factor=0.85,
            use_multithreading=True
        )
    
    # 创建中心性算子实例
    centrality_operator = centrality_class(config)
    
    # 计算中心性
    start_time = time.time()
    result = centrality_operator.calculate_centrality(nodes)
    elapsed_time = time.time() - start_time
    
    return {
        "elapsed_time": elapsed_time,
        "result": result
    }

def run_benchmark():
    """运行性能测试"""
    centrality_types = ["degree", "closeness", "betweenness", "eigenvector", "pagerank", "katz"]
    node_counts = [10, 50, 100, 200, 500]
    
    results = {
        "original": {ct: [] for ct in centrality_types},
        "optimized": {ct: [] for ct in centrality_types}
    }
    
    for n_nodes in node_counts:
        print(f"测试节点数量: {n_nodes}")
        
        # 生成随机网络
        nodes = generate_random_network(n_nodes, edge_probability=0.1)
        
        for centrality_type in centrality_types:
            print(f"  测试中心性类型: {centrality_type}")
            
            # 测试原始版本
            print("    测试原始版本...")
            original_result = benchmark_centrality(nodes, centrality_type, use_optimized=False)
            if original_result:
                results["original"][centrality_type].append({
                    "n_nodes": n_nodes,
                    "elapsed_time": original_result["elapsed_time"]
                })
                print(f"    原始版本耗时: {original_result['elapsed_time']:.4f}秒")
            
            # 测试优化版本
            print("    测试优化版本...")
            optimized_result = benchmark_centrality(nodes, centrality_type, use_optimized=True)
            if optimized_result:
                results["optimized"][centrality_type].append({
                    "n_nodes": n_nodes,
                    "elapsed_time": optimized_result["elapsed_time"]
                })
                print(f"    优化版本耗时: {optimized_result['elapsed_time']:.4f}秒")
            
            # 计算加速比
            if original_result and optimized_result:
                speedup = original_result["elapsed_time"] / optimized_result["elapsed_time"]
                print(f"    加速比: {speedup:.2f}x")
            
            print()
    
    return results

def plot_results(results):
    """绘制性能测试结果"""
    centrality_types = ["degree", "closeness", "betweenness", "eigenvector", "pagerank", "katz"]
    
    # 创建图表
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    axes = axes.flatten()
    
    for i, centrality_type in enumerate(centrality_types):
        ax = axes[i]
        
        # 提取数据
        original_data = results["original"][centrality_type]
        optimized_data = results["optimized"][centrality_type]
        
        if not original_data or not optimized_data:
            continue
        
        n_nodes = [d["n_nodes"] for d in original_data]
        original_times = [d["elapsed_time"] for d in original_data]
        optimized_times = [d["elapsed_time"] for d in optimized_data]
        
        # 计算加速比
        speedups = [o / n for o, n in zip(original_times, optimized_times)]
        
        # 绘制时间对比
        ax.plot(n_nodes, original_times, 'o-', label='原始版本')
        ax.plot(n_nodes, optimized_times, 's-', label='优化版本')
        
        # 添加标签和标题
        ax.set_xlabel('节点数量')
        ax.set_ylabel('执行时间 (秒)')
        ax.set_title(f'{centrality_type.capitalize()} 中心性')
        ax.legend()
        ax.grid(True)
        
        # 添加加速比文本
        for j, (x, y, s) in enumerate(zip(n_nodes, optimized_times, speedups)):
            ax.annotate(f'{s:.2f}x', (x, y), textcoords="offset points", 
                        xytext=(0, 10), ha='center')
    
    plt.tight_layout()
    plt.savefig('centrality_benchmark_results.png')
    plt.show()

def main():
    """主函数"""
    print("超越态思维引擎 - 中心性算子性能测试")
    print("=" * 50)
    
    # 运行性能测试
    results = run_benchmark()
    
    # 绘制结果
    plot_results(results)
    
    print("测试完成!")

if __name__ == "__main__":
    main()
