#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
超越态计算框架 - Arrow 扩展和态射操作示例

本示例展示了如何使用 Arrow 扩展和态射操作模块进行数据处理和存储。
包括复数数组、量子态和张量的创建、转换和存储，以及态射的创建、组合和应用。
"""

import os
import numpy as np
from pathlib import Path

# 导入 Arrow 扩展模块
from src.core.data.arrow_types.complex_array import (
    numpy_to_arrow_complex64, arrow_to_numpy_complex64,
    numpy_to_arrow_complex128, arrow_to_numpy_complex128
)
from src.core.data.arrow_types.quantum_state_array import (
    numpy_to_arrow_quantum_state, arrow_to_numpy_quantum_state
)
from src.core.data.arrow_types.tensor_array import (
    numpy_to_arrow_tensor, arrow_to_numpy_tensor
)
from src.core.data.arrow_types.parquet_storage import (
    ParquetStorageOptions,
    write_arrow_to_parquet, read_parquet_to_arrow,
    write_numpy_to_parquet, read_parquet_to_numpy,
    ParquetDataset
)

# 导入态射操作模块
try:
    from src.core.morphism import (
        Morphism, MorphismType, SequentialComposition, MorphismApplication
    )
    MORPHISM_AVAILABLE = True
except ImportError:
    print("态射操作模块不可用，仅展示 Arrow 扩展功能")
    MORPHISM_AVAILABLE = False


def complex_array_example():
    """复数数组示例"""
    print("\n=== 复数数组示例 ===")
    
    # 创建复数数组
    complex64_data = np.array([1+2j, 3+4j, 5+6j], dtype=np.complex64)
    complex128_data = np.array([1+2j, 3+4j, 5+6j], dtype=np.complex128)
    
    print(f"原始 complex64 数据: {complex64_data}")
    print(f"原始 complex128 数据: {complex128_data}")
    
    # 转换为 Arrow 数组
    arrow_complex64 = numpy_to_arrow_complex64(complex64_data)
    arrow_complex128 = numpy_to_arrow_complex128(complex128_data)
    
    print(f"Arrow complex64 数组类型: {arrow_complex64.type}")
    print(f"Arrow complex128 数组类型: {arrow_complex128.type}")
    
    # 转换回 NumPy 数组
    numpy_complex64 = arrow_to_numpy_complex64(arrow_complex64)
    numpy_complex128 = arrow_to_numpy_complex128(arrow_complex128)
    
    print(f"转换回的 complex64 数据: {numpy_complex64}")
    print(f"转换回的 complex128 数据: {numpy_complex128}")
    
    # 验证数据一致性
    assert np.allclose(complex64_data, numpy_complex64)
    assert np.allclose(complex128_data, numpy_complex128)
    print("数据一致性验证通过")


def quantum_state_example():
    """量子态示例"""
    print("\n=== 量子态示例 ===")
    
    # 创建量子态
    # |0⟩ 状态
    state1 = np.array([[1.0, 0.0], [0.0, 0.0]], dtype=np.float64)
    # |1⟩ 状态
    state2 = np.array([[0.0, 0.0], [1.0, 0.0]], dtype=np.float64)
    # |+⟩ 状态 (|0⟩ + |1⟩)/√2
    state3 = np.array([[1/np.sqrt(2), 0.0], [1/np.sqrt(2), 0.0]], dtype=np.float64)
    
    print(f"量子态 |0⟩: {state1}")
    print(f"量子态 |1⟩: {state2}")
    print(f"量子态 |+⟩: {state3}")
    
    # 转换为 Arrow 数组
    arrow_state1 = numpy_to_arrow_quantum_state(state1, validate=True)
    arrow_state2 = numpy_to_arrow_quantum_state(state2, validate=True)
    arrow_state3 = numpy_to_arrow_quantum_state(state3, validate=True)
    
    print(f"Arrow 量子态数组类型: {arrow_state1.type}")
    
    # 转换回 NumPy 数组
    numpy_state1 = arrow_to_numpy_quantum_state(arrow_state1)
    numpy_state2 = arrow_to_numpy_quantum_state(arrow_state2)
    numpy_state3 = arrow_to_numpy_quantum_state(arrow_state3)
    
    print(f"转换回的量子态 |0⟩: {numpy_state1}")
    print(f"转换回的量子态 |1⟩: {numpy_state2}")
    print(f"转换回的量子态 |+⟩: {numpy_state3}")
    
    # 验证数据一致性
    assert np.allclose(state1, numpy_state1)
    assert np.allclose(state2, numpy_state2)
    assert np.allclose(state3, numpy_state3)
    print("数据一致性验证通过")


def tensor_example():
    """张量示例"""
    print("\n=== 张量示例 ===")
    
    # 创建张量数据
    tensor1 = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]], dtype=np.float64)
    tensor2 = np.array([[[1.0, 2.0], [3.0, 4.0]], [[5.0, 6.0], [7.0, 8.0]]], dtype=np.float64)
    
    print(f"张量 1 形状: {tensor1.shape}, 数据: \n{tensor1}")
    print(f"张量 2 形状: {tensor2.shape}, 数据: \n{tensor2}")
    
    # 转换为 Arrow 数组
    arrow_tensor1 = numpy_to_arrow_tensor(tensor1)
    arrow_tensor2 = numpy_to_arrow_tensor(tensor2)
    
    print(f"Arrow 张量数组类型: {arrow_tensor1.type}")
    
    # 转换回 NumPy 数组
    numpy_tensor1 = arrow_to_numpy_tensor(arrow_tensor1)
    numpy_tensor2 = arrow_to_numpy_tensor(arrow_tensor2)
    
    print(f"转换回的张量 1 形状: {numpy_tensor1.shape}")
    print(f"转换回的张量 2 形状: {numpy_tensor2.shape}")
    
    # 验证数据一致性
    assert np.allclose(tensor1, numpy_tensor1)
    assert np.allclose(tensor2, numpy_tensor2)
    print("数据一致性验证通过")


def parquet_storage_example():
    """Parquet 存储示例"""
    print("\n=== Parquet 存储示例 ===")
    
    # 创建临时目录
    temp_dir = Path("./temp_parquet")
    temp_dir.mkdir(exist_ok=True)
    
    # 创建数据
    complex_data = np.array([1+2j, 3+4j, 5+6j], dtype=np.complex128)
    tensor_data = np.array([[1.0, 2.0], [3.0, 4.0]], dtype=np.float64)
    
    # 转换为 Arrow 数组
    arrow_complex = numpy_to_arrow_complex128(complex_data)
    arrow_tensor = numpy_to_arrow_tensor(tensor_data)
    
    # 创建文件路径
    file_path = temp_dir / "data.parquet"
    
    # 写入 Parquet 文件
    write_arrow_to_parquet(
        [arrow_complex, arrow_tensor],
        ["complex_data", "tensor_data"],
        str(file_path)
    )
    
    print(f"数据已写入 {file_path}")
    
    # 读取 Parquet 文件
    arrays = read_parquet_to_arrow(str(file_path))
    
    # 验证数据
    numpy_complex = arrow_to_numpy_complex128(arrays["complex_data"])
    numpy_tensor = arrow_to_numpy_tensor(arrays["tensor_data"])
    
    print(f"读取的复数数据: {numpy_complex}")
    print(f"读取的张量数据形状: {numpy_tensor.shape}")
    
    # 验证数据一致性
    assert np.allclose(complex_data, numpy_complex)
    assert np.allclose(tensor_data, numpy_tensor)
    print("数据一致性验证通过")
    
    # 使用 NumPy 接口
    data_dict = {
        "complex": complex_data,
        "tensor": tensor_data
    }
    
    numpy_file_path = temp_dir / "numpy_data.parquet"
    write_numpy_to_parquet(data_dict, str(numpy_file_path))
    
    print(f"NumPy 数据已写入 {numpy_file_path}")
    
    # 读取 NumPy 数据
    numpy_data = read_parquet_to_numpy(str(numpy_file_path))
    
    print(f"读取的 NumPy 复数数据: {numpy_data['complex']}")
    print(f"读取的 NumPy 张量数据形状: {numpy_data['tensor'].shape}")
    
    # 验证数据一致性
    assert np.allclose(complex_data, numpy_data["complex"])
    assert np.allclose(tensor_data, numpy_data["tensor"])
    print("NumPy 数据一致性验证通过")
    
    # 使用 ParquetDataset
    dataset_path = temp_dir / "dataset"
    dataset = ParquetDataset(str(dataset_path))
    
    # 写入分区数据
    dataset.write_partition(
        {"data": complex_data},
        {"type": "complex", "id": "1"}
    )
    
    dataset.write_partition(
        {"data": tensor_data},
        {"type": "tensor", "id": "1"}
    )
    
    print(f"分区数据已写入 {dataset_path}")
    
    # 列出分区
    partitions = dataset.list_partitions()
    print(f"分区列表: {partitions}")
    
    # 读取分区数据
    complex_result = dataset.read_partition({"type": "complex", "id": "1"})
    tensor_result = dataset.read_partition({"type": "tensor", "id": "1"})
    
    print(f"读取的分区复数数据: {complex_result['data']}")
    print(f"读取的分区张量数据形状: {tensor_result['data'].shape}")
    
    # 验证数据一致性
    assert np.allclose(complex_data, complex_result["data"])
    assert np.allclose(tensor_data, tensor_result["data"])
    print("分区数据一致性验证通过")


def morphism_example():
    """态射操作示例"""
    if not MORPHISM_AVAILABLE:
        print("\n=== 态射操作模块不可用，跳过示例 ===")
        return
    
    print("\n=== 态射操作示例 ===")
    
    # 创建态射
    morphism1 = Morphism(
        id="morphism1",
        type=MorphismType.TRANSFORM,
        metadata={"name": "transform1", "version": "1.0"},
        inputs=["input1"],
        outputs=["output1"]
    )
    
    morphism2 = Morphism(
        id="morphism2",
        type=MorphismType.TRANSFORM,
        metadata={"name": "transform2", "version": "1.0"},
        inputs=["input2"],
        outputs=["output2"]
    )
    
    print(f"态射 1: {morphism1}")
    print(f"态射 2: {morphism2}")
    
    # 创建顺序组合
    composition = SequentialComposition(
        id="composition",
        morphisms=[morphism1, morphism2],
        metadata={"name": "sequential", "version": "1.0"}
    )
    
    print(f"态射组合: {composition}")
    
    # 创建应用
    application = MorphismApplication(
        id="application",
        morphism=morphism1,
        metadata={"name": "application", "version": "1.0"}
    )
    
    print(f"态射应用: {application}")
    
    # 验证
    assert morphism1.validate()
    assert morphism2.validate()
    assert composition.validate()
    assert application.validate()
    print("态射验证通过")


def main():
    """主函数"""
    print("超越态计算框架 - Arrow 扩展和态射操作示例")
    
    # 运行示例
    complex_array_example()
    quantum_state_example()
    tensor_example()
    parquet_storage_example()
    morphism_example()
    
    print("\n所有示例运行完成")


if __name__ == "__main__":
    main()
