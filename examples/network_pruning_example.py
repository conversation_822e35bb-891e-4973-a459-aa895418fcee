"""
网络剪枝算子示例

本示例展示了如何使用网络剪枝算子进行网络剪枝。
"""

import sys
import os
import time
import logging
import random
import networkx as nx
import matplotlib.pyplot as plt
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入网络剪枝算子
from src.transcendental_tensor.network_pruning import (
    PruningStrategy, PruningGoal, PruningScope, PruningMode,
    PruningResult, NetworkPruner
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def create_test_network(node_count: int = 20, edge_probability: float = 0.2) -> nx.Graph:
    """
    创建测试网络
    
    参数:
        node_count (int): 节点数量
        edge_probability (float): 边概率
        
    返回:
        nx.Graph: 测试网络
    """
    # 创建随机图
    G = nx.erdos_renyi_graph(node_count, edge_probability)
    
    # 确保图是连通的
    if not nx.is_connected(G):
        # 获取连通分量
        components = list(nx.connected_components(G))
        
        # 连接连通分量
        for i in range(len(components) - 1):
            # 从第一个连通分量中随机选择一个节点
            node1 = random.choice(list(components[i]))
            
            # 从第二个连通分量中随机选择一个节点
            node2 = random.choice(list(components[i + 1]))
            
            # 添加边
            G.add_edge(node1, node2)
    
    # 添加边权重
    for u, v in G.edges():
        # 随机权重
        weight = random.uniform(0.1, 1.0)
        
        # 设置权重
        G[u][v]['weight'] = weight
    
    # 添加节点活动度
    for node in G.nodes():
        # 随机活动度
        activity = random.uniform(0.1, 1.0)
        
        # 设置活动度
        G.nodes[node]['activity'] = activity
    
    return G


def visualize_network(G: nx.Graph, title: str = "Network", pruned_nodes: List[Any] = None, pruned_edges: List[Any] = None):
    """
    可视化网络
    
    参数:
        G (nx.Graph): 网络图
        title (str): 标题
        pruned_nodes (List[Any]): 剪枝节点列表
        pruned_edges (List[Any]): 剪枝边列表
    """
    plt.figure(figsize=(10, 8))
    
    # 设置布局
    pos = nx.spring_layout(G, seed=42)
    
    # 绘制节点
    nx.draw_networkx_nodes(G, pos, node_size=300, node_color='lightblue')
    
    # 绘制边
    nx.draw_networkx_edges(G, pos, width=1.0, alpha=0.5)
    
    # 绘制节点标签
    nx.draw_networkx_labels(G, pos, font_size=10)
    
    # 绘制剪枝节点
    if pruned_nodes:
        nx.draw_networkx_nodes(G, pos, nodelist=pruned_nodes, node_size=300, node_color='red')
    
    # 绘制剪枝边
    if pruned_edges:
        nx.draw_networkx_edges(G, pos, edgelist=pruned_edges, width=2.0, alpha=0.7, edge_color='red')
    
    # 设置标题
    plt.title(title)
    
    # 关闭坐标轴
    plt.axis('off')
    
    # 显示图形
    plt.show()


def main():
    """主函数"""
    logger.info("Starting network pruning example")
    
    # 创建测试网络
    G = create_test_network(20, 0.2)
    
    logger.info(f"Created test network with {len(G.nodes())} nodes and {len(G.edges())} edges")
    
    # 可视化原始网络
    visualize_network(G, "Original Network")
    
    # 创建网络剪枝算子
    pruner = NetworkPruner()
    
    # 基于低中心性剪枝
    logger.info("Pruning network using LOW_CENTRALITY strategy")
    pruner.parameters["strategy"] = PruningStrategy.LOW_CENTRALITY.value
    pruner.parameters["scope"] = PruningScope.LINKS.value
    pruner.parameters["threshold"] = 0.2
    
    result = pruner.prune_network(G)
    
    logger.info(f"Pruned {len(result.pruned_links)} links using LOW_CENTRALITY strategy")
    
    # 可视化剪枝结果
    visualize_network(G, "Network after LOW_CENTRALITY Pruning", pruned_edges=result.pruned_links)
    
    # 基于冗余度剪枝
    logger.info("Pruning network using REDUNDANT strategy")
    pruner.parameters["strategy"] = PruningStrategy.REDUNDANT.value
    pruner.parameters["scope"] = PruningScope.LINKS.value
    pruner.parameters["threshold"] = 0.2
    
    result = pruner.prune_network(G)
    
    logger.info(f"Pruned {len(result.pruned_links)} links using REDUNDANT strategy")
    
    # 可视化剪枝结果
    visualize_network(G, "Network after REDUNDANT Pruning", pruned_edges=result.pruned_links)
    
    # 基于低活动度剪枝
    logger.info("Pruning network using LOW_ACTIVITY strategy")
    pruner.parameters["strategy"] = PruningStrategy.LOW_ACTIVITY.value
    pruner.parameters["scope"] = PruningScope.NODES.value
    pruner.parameters["threshold"] = 0.2
    
    result = pruner.prune_network(G)
    
    logger.info(f"Pruned {len(result.pruned_nodes)} nodes using LOW_ACTIVITY strategy")
    
    # 可视化剪枝结果
    visualize_network(G, "Network after LOW_ACTIVITY Pruning", pruned_nodes=result.pruned_nodes)
    
    # 基于混合策略剪枝
    logger.info("Pruning network using HYBRID strategy")
    pruner.parameters["strategy"] = PruningStrategy.HYBRID.value
    pruner.parameters["scope"] = PruningScope.LINKS.value
    pruner.parameters["threshold"] = 0.2
    pruner.parameters["centrality_weight"] = 0.3
    pruner.parameters["weight_weight"] = 0.3
    pruner.parameters["redundancy_weight"] = 0.4
    
    result = pruner.prune_network(G)
    
    logger.info(f"Pruned {len(result.pruned_links)} links using HYBRID strategy")
    
    # 可视化剪枝结果
    visualize_network(G, "Network after HYBRID Pruning", pruned_edges=result.pruned_links)
    
    # 输出指标
    logger.info("Pruning metrics:")
    for key, value in result.metrics.items():
        logger.info(f"  {key}: {value}")
    
    logger.info("Network pruning example completed")


if __name__ == "__main__":
    main()
