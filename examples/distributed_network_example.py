#!/usr/bin/env python3
"""
分布式网络示例脚本

该脚本展示了如何使用超越态思维引擎的分布式网络功能，包括物理层、数据层和计算层。
"""

import time
import logging
import threading
from src.distributed import (
    PhysicalLayer,
    DataLayer,
    ConsistencyLevel,
    ComputationLayer,
    TaskPriority,
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def create_node(node_id, host, port, discovery_port):
    """创建一个分布式网络节点"""
    # 创建物理层
    physical_layer = PhysicalLayer(
        node_id=node_id,
        host=host,
        port=port,
        discovery_port=discovery_port
    )
    
    # 创建数据层
    data_layer = DataLayer(
        node_id=node_id,
        physical_layer=physical_layer,
        consistency_level=ConsistencyLevel.EVENTUAL
    )
    
    # 创建计算层
    computation_layer = ComputationLayer(
        node_id=node_id,
        physical_layer=physical_layer,
        data_layer=data_layer
    )
    
    # 启动各层
    physical_layer.start()
    data_layer.start()
    computation_layer.start()
    
    return {
        "node_id": node_id,
        "physical_layer": physical_layer,
        "data_layer": data_layer,
        "computation_layer": computation_layer
    }

def node_discovery_handler(node_info):
    """节点发现处理函数"""
    print(f"发现新节点: {node_info.node_id} at {node_info.address}:{node_info.port}")

def node_failure_handler(node_id):
    """节点故障处理函数"""
    print(f"节点故障: {node_id}")

def data_change_handler(key, value, version):
    """数据变更处理函数"""
    print(f"数据变更: {key} = {value} (version: {version})")

def task_status_handler(task_id, status):
    """任务状态变更处理函数"""
    print(f"任务状态变更: {task_id} -> {status}")

def task_result_handler(task_id, result):
    """任务结果处理函数"""
    print(f"任务结果: {task_id} -> {result}")

def example_function(a, b):
    """示例函数，将在分布式环境中执行"""
    print(f"执行计算: {a} + {b}")
    return a + b

def main():
    """主函数"""
    # 创建两个节点
    node1 = create_node("node1", "127.0.0.1", 9000, 9001)
    node2 = create_node("node2", "127.0.0.1", 9002, 9003)
    
    # 注册处理函数
    node1["physical_layer"].register_node_discovery_handler(node_discovery_handler)
    node1["physical_layer"].register_node_failure_handler(node_failure_handler)
    node1["data_layer"].register_data_change_handler(data_change_handler)
    node1["computation_layer"].register_task_status_handler(task_status_handler)
    node1["computation_layer"].register_task_result_handler(task_result_handler)
    
    # 等待节点发现
    print("等待节点发现...")
    time.sleep(5)
    
    # 获取节点信息
    nodes = node1["physical_layer"].get_nodes()
    print(f"已知节点: {', '.join(nodes.keys())}")
    
    # 数据层操作
    print("\n=== 数据层操作 ===")
    # 存储数据
    node1["data_layer"].put("key1", "value1")
    node1["data_layer"].put("key2", {"name": "test", "value": 123})
    
    # 等待数据同步
    time.sleep(2)
    
    # 获取数据
    value1 = node2["data_layer"].get("key1")
    value2 = node2["data_layer"].get("key2")
    print(f"node2 获取数据: key1 = {value1}")
    print(f"node2 获取数据: key2 = {value2}")
    
    # 计算层操作
    print("\n=== 计算层操作 ===")
    # 注册函数
    node1["computation_layer"].register_function(example_function)
    
    # 提交任务
    task_id = node1["computation_layer"].submit_task(
        function_name="example_function",
        args=[10, 20],
        priority=TaskPriority.HIGH
    )
    print(f"提交任务: {task_id}")
    
    # 等待任务完成
    try:
        result = node1["computation_layer"].get_task_result(task_id, timeout=5)
        print(f"任务结果: {result}")
    except Exception as e:
        print(f"任务执行失败: {e}")
    
    # 停止节点
    print("\n停止节点...")
    node1["computation_layer"].stop()
    node1["data_layer"].stop()
    node1["physical_layer"].stop()
    
    node2["computation_layer"].stop()
    node2["data_layer"].stop()
    node2["physical_layer"].stop()
    
    print("示例完成")

if __name__ == "__main__":
    main()
