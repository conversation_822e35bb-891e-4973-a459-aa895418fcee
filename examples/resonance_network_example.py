#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超越态思维引擎 - 共振网络算子示例
"""

import sys
import time
import numpy as np
import matplotlib.pyplot as plt
from src.operators import operator_registry

def main():
    """主函数"""
    print("超越态思维引擎 - 共振网络算子示例")
    print("=" * 50)
    
    # 获取所有算子类别
    categories = operator_registry.get_categories()
    print(f"算子类别: {categories}")
    
    # 获取共振网络算子
    resonance_operators = operator_registry.get_operators_in_category("resonance_network")
    print(f"共振网络算子: {list(resonance_operators.keys())}")
    
    # 获取网络弹性计算算子
    elasticity_operator_class = operator_registry.get_operator("resonance_network", "elasticity")
    if elasticity_operator_class is None:
        print("网络弹性计算算子不可用，请确保已编译Rust绑定")
        return
    
    # 创建网络弹性计算算子实例
    elasticity_operator = elasticity_operator_class()
    
    # 创建测试网络
    nodes = {
        "A": {"status": 1.0, "attributes": {"importance": 0.8}, "connections": ["B", "C", "D"]},
        "B": {"status": 1.0, "attributes": {"importance": 0.5}, "connections": ["A", "C"]},
        "C": {"status": 1.0, "attributes": {"importance": 0.7}, "connections": ["A", "B", "D"]},
        "D": {"status": 1.0, "attributes": {"importance": 0.6}, "connections": ["A", "C", "E"]},
        "E": {"status": 1.0, "attributes": {"importance": 0.4}, "connections": ["D"]}
    }
    
    links = {
        "A-B": {"source": "A", "target": "B", "status": 1.0, "attributes": {"weight": 0.9}},
        "A-C": {"source": "A", "target": "C", "status": 1.0, "attributes": {"weight": 0.8}},
        "A-D": {"source": "A", "target": "D", "status": 1.0, "attributes": {"weight": 0.7}},
        "B-C": {"source": "B", "target": "C", "status": 1.0, "attributes": {"weight": 0.6}},
        "C-D": {"source": "C", "target": "D", "status": 1.0, "attributes": {"weight": 0.5}},
        "D-E": {"source": "D", "target": "E", "status": 1.0, "attributes": {"weight": 0.4}}
    }
    
    # 计算弹性
    result = elasticity_operator.calculate_elasticity(nodes, links)
    
    # 打印结果
    print(f"弹性值: {result.get_elasticity()}")
    print(f"失效节点数: {result.get_failed_nodes()}")
    print(f"失效链接数: {result.get_failed_links()}")
    print(f"恢复节点数: {result.get_recovered_nodes()}")
    print(f"恢复链接数: {result.get_recovered_links()}")
    print(f"计算耗时: {result.get_elapsed_ms()} ms")
    
    # 获取社区检测算子
    community_operator_class = operator_registry.get_operator("resonance_network", "community")
    if community_operator_class is None:
        print("社区检测算子不可用，请确保已编译Rust绑定")
        return
    
    # 创建社区检测算子实例
    community_operator = community_operator_class()
    
    # 创建测试网络
    nodes = {
        "A": {"weight": 1.0, "attributes": {"importance": 0.8}},
        "B": {"weight": 1.0, "attributes": {"importance": 0.5}},
        "C": {"weight": 1.0, "attributes": {"importance": 0.7}},
        "D": {"weight": 1.0, "attributes": {"importance": 0.6}},
        "E": {"weight": 1.0, "attributes": {"importance": 0.4}},
        "F": {"weight": 1.0, "attributes": {"importance": 0.3}},
        "G": {"weight": 1.0, "attributes": {"importance": 0.2}},
        "H": {"weight": 1.0, "attributes": {"importance": 0.1}}
    }
    
    edges = [
        {"source": "A", "target": "B", "weight": 0.9},
        {"source": "A", "target": "C", "weight": 0.8},
        {"source": "B", "target": "C", "weight": 0.7},
        {"source": "C", "target": "D", "weight": 0.3},
        {"source": "D", "target": "E", "weight": 0.9},
        {"source": "D", "target": "F", "weight": 0.8},
        {"source": "E", "target": "F", "weight": 0.7},
        {"source": "F", "target": "G", "weight": 0.3},
        {"source": "G", "target": "H", "weight": 0.9}
    ]
    
    # 检测社区
    result = community_operator.detect_communities(nodes, edges)
    
    # 打印结果
    print(f"社区数量: {result.get_num_communities()}")
    print(f"模块度: {result.get_modularity()}")
    print(f"社区分配: {result.get_communities()}")
    
    print("示例完成!")

if __name__ == "__main__":
    main()
