#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超越态思维引擎算子库分布式计算示例 - 算子工具模块

本模块提供了创建和管理算子的工具函数。
"""

import numpy as np
import logging

# 导入算子库模块
# 注意：在实际使用中，导入路径可能不同
from src.operators.transform import LinearTransformOperator, NonlinearTransformOperator, ProjectionTransformOperator
from src.operators.evolution import DifferentialEquationOperator, StochasticProcessOperator

logger = logging.getLogger("distributed.operator_utils")


def create_operators():
    """创建算子列表
    
    Returns:
        list: 算子列表
    """
    logger.info("创建算子列表")
    
    operators = []
    
    # 创建线性变换算子
    linear_transform = create_linear_transform()
    operators.append(linear_transform)
    logger.info(f"创建线性变换算子: {linear_transform.__class__.__name__}")
    
    # 创建非线性变换算子
    nonlinear_transform = create_nonlinear_transform()
    operators.append(nonlinear_transform)
    logger.info(f"创建非线性变换算子: {nonlinear_transform.__class__.__name__}")
    
    # 创建投影变换算子
    projection_transform = create_projection_transform()
    operators.append(projection_transform)
    logger.info(f"创建投影变换算子: {projection_transform.__class__.__name__}")
    
    # 创建微分方程演化算子
    diff_eq_evolution = create_differential_equation_operator()
    operators.append(diff_eq_evolution)
    logger.info(f"创建微分方程演化算子: {diff_eq_evolution.__class__.__name__}")
    
    # 创建随机过程演化算子
    stochastic_evolution = create_stochastic_process_operator()
    operators.append(stochastic_evolution)
    logger.info(f"创建随机过程演化算子: {stochastic_evolution.__class__.__name__}")
    
    return operators


def create_linear_transform():
    """创建线性变换算子
    
    Returns:
        LinearTransformOperator: 线性变换算子
    """
    # 创建旋转矩阵
    dimension = 10
    theta = np.pi / 4  # 45度旋转
    
    # 在前两个维度上应用旋转
    matrix = np.eye(dimension)
    matrix[0, 0] = np.cos(theta)
    matrix[0, 1] = -np.sin(theta)
    matrix[1, 0] = np.sin(theta)
    matrix[1, 1] = np.cos(theta)
    
    # 创建偏移
    offset = np.zeros(dimension)
    offset[0] = 0.5
    offset[1] = 0.5
    
    # 创建线性变换算子
    transform = LinearTransformOperator(dimension, matrix, offset)
    
    return transform


def create_nonlinear_transform():
    """创建非线性变换算子
    
    Returns:
        NonlinearTransformOperator: 非线性变换算子
    """
    # 创建非线性变换算子
    transform = NonlinearTransformOperator(
        dimension=10,
        function=lambda x: np.tanh(x),
        scale=2.0
    )
    
    return transform


def create_projection_transform():
    """创建投影变换算子
    
    Returns:
        ProjectionTransformOperator: 投影变换算子
    """
    # 创建投影变换算子
    transform = ProjectionTransformOperator(
        source_dimension=10,
        target_dimension=3,
        method='pca'
    )
    
    return transform


def create_differential_equation_operator():
    """创建微分方程演化算子
    
    Returns:
        DifferentialEquationOperator: 微分方程演化算子
    """
    # 创建微分方程演化算子
    evolution = DifferentialEquationOperator(
        dimension=3,
        equation=lambda t, x: np.array([-0.1 * x[0], -0.1 * x[1], 0.1 * (1 - x[2])]),
        time_step=0.1,
        num_steps=10,
        method='rk4'
    )
    
    return evolution


def create_stochastic_process_operator():
    """创建随机过程演化算子
    
    Returns:
        StochasticProcessOperator: 随机过程演化算子
    """
    # 创建随机过程演化算子
    evolution = StochasticProcessOperator(
        dimension=3,
        drift=lambda x: -0.1 * x,
        diffusion=lambda x: 0.05 * np.ones_like(x),
        time_step=0.1,
        num_steps=10,
        method='euler_maruyama'
    )
    
    return evolution


def apply_operator(operator, data):
    """应用算子
    
    Args:
        operator: 要应用的算子
        data: 输入数据
    
    Returns:
        numpy.ndarray: 算子应用结果
    """
    logger.info(f"应用算子: {operator.__class__.__name__}")
    result = operator.apply(data)
    logger.info(f"应用结果形状: {result.shape}")
    return result


def apply_operators_sequence(operators, data):
    """顺序应用多个算子
    
    Args:
        operators: 算子列表
        data: 输入数据
    
    Returns:
        numpy.ndarray: 算子应用结果
    """
    logger.info(f"顺序应用{len(operators)}个算子")
    
    result = data
    for i, op in enumerate(operators):
        logger.info(f"应用算子 {i+1}/{len(operators)}: {op.__class__.__name__}")
        result = op.apply(result)
    
    return result
