#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超越态思维引擎算子库分布式计算示例 - 数据工具模块

本模块提供了数据生成、处理和可视化的工具函数。
"""

import numpy as np
import matplotlib.pyplot as plt
import logging

logger = logging.getLogger("distributed.data_utils")


def create_sample_data(num_samples=1000, dimension=3, seed=42):
    """创建示例数据
    
    Args:
        num_samples: 样本数量
        dimension: 数据维度
        seed: 随机种子
    
    Returns:
        numpy.ndarray: 生成的数据
    """
    logger.info(f"创建{num_samples}个{dimension}维样本")
    
    # 设置随机种子以确保可重复性
    np.random.seed(seed)
    
    # 创建随机数据
    data = np.random.random((num_samples, dimension))
    
    # 添加一些结构
    t = np.linspace(0, 10, num_samples)
    for i in range(min(dimension, 3)):
        if i == 0:
            data[:, i] = np.sin(t) + 0.1 * np.random.randn(num_samples)
        elif i == 1:
            data[:, i] = np.cos(t) + 0.1 * np.random.randn(num_samples)
        elif i == 2:
            data[:, i] = t / 10 + 0.1 * np.random.randn(num_samples)
    
    return data


def partition_data(data, num_partitions):
    """将数据分成多个分区
    
    Args:
        data: 要分区的数据
        num_partitions: 分区数量
    
    Returns:
        list: 数据分区列表
    """
    logger.info(f"将数据分成{num_partitions}个分区")
    
    # 计算每个分区的大小
    partition_size = data.shape[0] // num_partitions
    
    # 创建分区
    partitions = []
    for i in range(num_partitions):
        start = i * partition_size
        end = (i + 1) * partition_size if i < num_partitions - 1 else data.shape[0]
        partitions.append(data[start:end])
        logger.info(f"分区 {i+1}: 形状 {partitions[-1].shape}")
    
    return partitions


def visualize_results(original_data, sequential_result, distributed_result, filename):
    """可视化结果
    
    Args:
        original_data: 原始数据
        sequential_result: 顺序处理结果
        distributed_result: 分布式处理结果
        filename: 输出文件名
    """
    logger.info(f"可视化结果并保存到 {filename}")
    
    # 只使用前1000个点进行可视化
    n = min(1000, original_data.shape[0])
    
    # 创建3D图
    fig = plt.figure(figsize=(15, 5))
    
    # 原始数据
    ax1 = fig.add_subplot(131, projection='3d')
    ax1.scatter(original_data[:n, 0], original_data[:n, 1], original_data[:n, 2], c='b', marker='o', alpha=0.6)
    ax1.set_title('原始数据')
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.set_zlabel('Z')
    
    # 顺序处理结果
    ax2 = fig.add_subplot(132, projection='3d')
    ax2.scatter(sequential_result[:n, 0], sequential_result[:n, 1], sequential_result[:n, 2], c='g', marker='o', alpha=0.6)
    ax2.set_title('顺序处理结果')
    ax2.set_xlabel('X')
    ax2.set_ylabel('Y')
    ax2.set_zlabel('Z')
    
    # 分布式处理结果
    ax3 = fig.add_subplot(133, projection='3d')
    ax3.scatter(distributed_result[:n, 0], distributed_result[:n, 1], distributed_result[:n, 2], c='r', marker='o', alpha=0.6)
    ax3.set_title('分布式处理结果')
    ax3.set_xlabel('X')
    ax3.set_ylabel('Y')
    ax3.set_zlabel('Z')
    
    plt.tight_layout()
    plt.savefig(filename)
    logger.info(f"结果已保存到 {filename}")
    plt.close()


def compute_data_hash(data):
    """计算数据哈希值
    
    Args:
        data: 输入数据
    
    Returns:
        str: 数据哈希值
    """
    import hashlib
    return hashlib.md5(data.tobytes()).hexdigest()


def compare_results(result1, result2, rtol=1e-5, atol=1e-8):
    """比较两个结果
    
    Args:
        result1: 第一个结果
        result2: 第二个结果
        rtol: 相对容差
        atol: 绝对容差
    
    Returns:
        bool: 如果结果相似则为True
    """
    if result1.shape != result2.shape:
        logger.warning(f"结果形状不同: {result1.shape} vs {result2.shape}")
        return False
    
    is_close = np.allclose(result1, result2, rtol=rtol, atol=atol)
    if not is_close:
        # 计算差异统计
        diff = np.abs(result1 - result2)
        max_diff = np.max(diff)
        mean_diff = np.mean(diff)
        logger.warning(f"结果不同: 最大差异 {max_diff}, 平均差异 {mean_diff}")
    
    return is_close
