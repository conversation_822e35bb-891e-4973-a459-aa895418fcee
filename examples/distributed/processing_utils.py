#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超越态思维引擎算子库分布式计算示例 - 处理工具模块

本模块提供了分布式数据处理和结果聚合的工具函数。
"""

import numpy as np
import logging
import time

# 导入本地模块
from data_utils import partition_data
from distributed_utils import check_node_health, handle_node_failure, simulate_network_delay, simulate_node_failure

# 导入算子库模块
# 注意：在实际使用中，导入路径可能不同
from src.operators.distributed import ResultAggregator

logger = logging.getLogger("distributed.processing_utils")


def process_distributed(dist_operators, data, nodes, env, max_retries=3):
    """分布式处理数据
    
    Args:
        dist_operators: 分布式算子列表
        data: 输入数据
        nodes: 节点列表
        env: 分布式环境
        max_retries: 最大重试次数
    
    Returns:
        list: 处理结果列表
    """
    logger.info(f"分布式处理数据，节点数: {len(nodes)}")
    
    # 分区数据
    partitions = partition_data(data, len(nodes))
    
    # 分配任务
    tasks = []
    for i, (node, partition) in enumerate(zip(nodes, partitions)):
        task = {
            'node': node,
            'partition': partition,
            'operators': dist_operators,
            'retry_count': 0
        }
        tasks.append(task)
        logger.info(f"分配任务 {i+1} 到节点 {node.node_id}")
    
    # 执行任务
    results = []
    failed_tasks = []
    
    for task in tasks:
        try:
            # 检查节点健康状态
            if not check_node_health(task['node']):
                raise RuntimeError(f"节点不健康: {task['node'].node_id}")
            
            # 模拟节点故障
            if simulate_node_failure():
                raise RuntimeError(f"模拟节点故障: {task['node'].node_id}")
            
            # 处理数据
            result = process_task(task)
            results.append(result)
            logger.info(f"任务在节点 {task['node'].node_id} 上完成")
        
        except Exception as e:
            logger.error(f"任务在节点 {task['node'].node_id} 上失败: {e}")
            task['retry_count'] += 1
            if task['retry_count'] <= max_retries:
                # 重试任务
                failed_tasks.append(task)
                logger.info(f"将重试任务，重试次数: {task['retry_count']}/{max_retries}")
            else:
                logger.error(f"任务重试次数超过上限: {max_retries}")
    
    # 处理失败的任务
    if failed_tasks:
        logger.info(f"处理 {len(failed_tasks)} 个失败的任务")
        results.extend(process_failed_tasks(failed_tasks, nodes, max_retries))
    
    return results


def process_task(task):
    """处理单个任务
    
    Args:
        task: 任务信息
    
    Returns:
        numpy.ndarray: 处理结果
    """
    logger.info(f"在节点 {task['node'].node_id} 上处理任务")
    
    # 模拟网络延迟
    simulate_network_delay()
    
    # 处理数据
    result = task['partition']
    for i, op in enumerate(task['operators']):
        logger.info(f"在节点 {task['node'].node_id} 上应用算子 {i+1}/{len(task['operators'])}")
        # 在实际环境中，这里会在远程节点上执行算子
        # 这里我们在本地执行基础算子作为模拟
        result = op.get_base_operator().apply(result)
        
        # 模拟网络延迟
        simulate_network_delay()
    
    return result


def process_failed_tasks(failed_tasks, healthy_nodes, max_retries):
    """处理失败的任务
    
    Args:
        failed_tasks: 失败的任务列表
        healthy_nodes: 健康节点列表
        max_retries: 最大重试次数
    
    Returns:
        list: 处理结果列表
    """
    logger.info(f"处理 {len(failed_tasks)} 个失败的任务")
    
    results = []
    still_failed_tasks = []
    
    # 过滤出健康节点
    available_nodes = [node for node in healthy_nodes if check_node_health(node)]
    logger.info(f"可用健康节点: {len(available_nodes)}")
    
    if not available_nodes:
        logger.error("没有可用的健康节点")
        raise RuntimeError("没有可用的健康节点")
    
    # 重新分配任务
    for task in failed_tasks:
        # 选择一个健康节点
        node_index = np.random.randint(0, len(available_nodes))
        new_node = available_nodes[node_index]
        logger.info(f"将任务从节点 {task['node'].node_id} 重新分配到节点 {new_node.node_id}")
        
        # 更新任务
        task['node'] = new_node
        
        try:
            # 处理任务
            result = process_task(task)
            results.append(result)
            logger.info(f"重试任务在节点 {task['node'].node_id} 上完成")
        
        except Exception as e:
            logger.error(f"重试任务在节点 {task['node'].node_id} 上失败: {e}")
            task['retry_count'] += 1
            if task['retry_count'] <= max_retries:
                # 继续重试
                still_failed_tasks.append(task)
                logger.info(f"将再次重试任务，重试次数: {task['retry_count']}/{max_retries}")
            else:
                logger.error(f"任务重试次数超过上限: {max_retries}")
    
    # 递归处理仍然失败的任务
    if still_failed_tasks:
        logger.info(f"处理 {len(still_failed_tasks)} 个仍然失败的任务")
        results.extend(process_failed_tasks(still_failed_tasks, available_nodes, max_retries))
    
    return results


def aggregate_results(results, method='mean'):
    """聚合结果
    
    Args:
        results: 结果列表
        method: 聚合方法
    
    Returns:
        numpy.ndarray: 聚合结果
    """
    logger.info(f"聚合 {len(results)} 个结果，方法: {method}")
    
    # 创建结果聚合器
    aggregator = ResultAggregator(aggregation_method=method)
    
    # 聚合结果
    aggregated_result = aggregator.aggregate(results)
    logger.info(f"聚合结果形状: {aggregated_result.shape}")
    
    return aggregated_result
