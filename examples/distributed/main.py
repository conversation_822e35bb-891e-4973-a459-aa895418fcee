#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超越态思维引擎算子库分布式计算示例 - 主模块

本示例展示了超越态思维引擎算子库的分布式计算功能，包括：
1. 创建分布式环境
2. 使用分布式算子
3. 数据分片和聚合
4. 分布式任务调度
5. 容错和恢复机制
"""

import numpy as np
import matplotlib.pyplot as plt
import time
import logging
import os
import sys

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入本地模块
from data_utils import create_sample_data, visualize_results
from operator_utils import create_operators
from distributed_utils import setup_distributed_environment, create_distributed_operators
from processing_utils import process_distributed, aggregate_results

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("distributed.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("distributed")


def main():
    """主函数"""
    logger.info("超越态思维引擎算子库分布式计算示例")
    logger.info("=" * 50)

    # 创建示例数据
    logger.info("\n1. 创建示例数据")
    data = create_sample_data(num_samples=50000, dimension=10)
    logger.info(f"数据形状: {data.shape}")
    logger.info(f"数据大小: {data.nbytes / (1024 * 1024):.2f} MB")

    # 创建算子
    logger.info("\n2. 创建算子")
    operators = create_operators()
    logger.info(f"创建了 {len(operators)} 个算子")

    # 设置分布式环境
    logger.info("\n3. 设置分布式环境")
    env, nodes = setup_distributed_environment(num_nodes=4)
    logger.info(f"创建了 {len(nodes)} 个节点")

    # 创建分布式算子
    logger.info("\n4. 创建分布式算子")
    dist_operators = create_distributed_operators(operators, env)
    logger.info(f"创建了 {len(dist_operators)} 个分布式算子")

    # 顺序处理（基准）
    logger.info("\n5. 顺序处理（基准）")
    start_time = time.time()
    sequential_result = process_sequential(operators, data)
    sequential_time = time.time() - start_time
    logger.info(f"顺序处理耗时: {sequential_time:.6f}秒")

    # 分布式处理
    logger.info("\n6. 分布式处理")
    start_time = time.time()
    distributed_results = process_distributed(dist_operators, data, nodes, env)
    distributed_time = time.time() - start_time
    logger.info(f"分布式处理耗时: {distributed_time:.6f}秒")
    logger.info(f"加速比: {sequential_time / distributed_time:.2f}x")

    # 聚合结果
    logger.info("\n7. 聚合结果")
    aggregated_result = aggregate_results(distributed_results)
    logger.info(f"聚合结果形状: {aggregated_result.shape}")

    # 验证结果
    logger.info("\n8. 验证结果")
    # 由于随机性和浮点误差，我们使用较大的容差
    is_close = np.allclose(sequential_result, aggregated_result, rtol=1e-4, atol=1e-4)
    logger.info(f"结果一致性: {is_close}")

    # 可视化结果
    logger.info("\n9. 可视化结果")
    visualize_results(data, sequential_result, aggregated_result, "distributed_results.png")

    logger.info("\n示例完成！")


def process_sequential(operators, data):
    """顺序处理（用作基准）"""
    logger.info(f"顺序应用 {len(operators)} 个算子")
    
    result = data
    for i, op in enumerate(operators):
        logger.info(f"应用算子 {i+1}/{len(operators)}: {op.__class__.__name__}")
        result = op.apply(result)
    
    return result


if __name__ == "__main__":
    main()
