"""
综合分布式系统示例

本示例展示了如何将分布式系统的各种功能结合起来，构建一个完整的分布式应用。
包括节点和网络管理、消息传递、同步机制、容错机制、共识机制、事务管理、调度和算法执行。
"""

import time
import logging
import random
import threading
import numpy as np
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable

from src.core.distributed import (
    Node, Network, Message, MessageType, MessagePriority,
    DistributedLock, DistributedBarrier, DistributedSemaphore,
    FaultDetector, HeartbeatMonitor, ReplicationManager,
    ConsensusProtocol, RaftConsensus, PaxosConsensus,
    TransactionManager, TwoPhaseCommitProtocol,
    TaskScheduler, LoadBalancer, ResourceManager,
    DistributedAlgorithm, MapReduceFramework
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DistributedApplication:
    """分布式应用类，整合各种分布式功能"""
    
    def __init__(self, app_name: str, node_count: int = 5):
        """
        初始化分布式应用
        
        参数:
            app_name (str): 应用名称
            node_count (int): 节点数量
        """
        self.app_name = app_name
        self.node_count = node_count
        
        # 创建网络
        self.network = Network(network_id=f"{app_name}_network")
        
        # 创建节点
        self.nodes = {}
        for i in range(node_count):
            node = Node(
                node_id=f"node_{i}",
                capabilities={"compute": 100, "memory": 1024, "storage": 10240},
                metadata={"region": f"region_{i % 3}", "role": "worker" if i > 0 else "master"}
            )
            self.nodes[node.node_id] = node
            self.network.add_node(node)
        
        # 连接节点（全连接网络）
        for i in range(node_count):
            for j in range(i+1, node_count):
                self.network.connect_nodes(
                    f"node_{i}", f"node_{j}",
                    metadata={"bandwidth": 100, "latency": 5}
                )
        
        # 创建分布式锁
        self.lock = DistributedLock(lock_id=f"{app_name}_lock", network=self.network)
        
        # 创建分布式屏障
        self.barrier = DistributedBarrier(
            barrier_id=f"{app_name}_barrier",
            network=self.network,
            party_count=node_count
        )
        
        # 创建故障检测器
        self.fault_detector = FaultDetector(
            detector_id=f"{app_name}_fault_detector",
            network=self.network
        )
        
        # 创建心跳监视器
        self.heartbeat_monitor = HeartbeatMonitor(
            monitor_id=f"{app_name}_heartbeat_monitor",
            network=self.network,
            heartbeat_interval=5
        )
        
        # 创建复制管理器
        self.replication_manager = ReplicationManager(
            manager_id=f"{app_name}_replication_manager",
            network=self.network,
            replication_factor=3
        )
        
        # 创建共识协议
        self.consensus_protocol = RaftConsensus(
            protocol_id=f"{app_name}_consensus",
            network=self.network
        )
        
        # 创建事务管理器
        self.transaction_manager = TransactionManager(
            manager_id=f"{app_name}_transaction_manager",
            network=self.network,
            protocol=TwoPhaseCommitProtocol()
        )
        
        # 创建任务调度器
        self.task_scheduler = TaskScheduler(
            scheduler_id=f"{app_name}_task_scheduler",
            network=self.network
        )
        
        # 创建负载均衡器
        self.load_balancer = LoadBalancer(
            balancer_id=f"{app_name}_load_balancer",
            network=self.network
        )
        
        # 创建资源管理器
        self.resource_manager = ResourceManager(
            manager_id=f"{app_name}_resource_manager",
            network=self.network
        )
        
        # 创建MapReduce框架
        self.mapreduce = MapReduceFramework(
            framework_id=f"{app_name}_mapreduce",
            network=self.network
        )
        
        # 应用状态
        self.is_running = False
        self.shared_data = {}
        self.task_results = {}
        
        logger.info(f"初始化分布式应用: {app_name}, 节点数量: {node_count}")
    
    def start(self) -> bool:
        """
        启动分布式应用
        
        返回:
            bool: 如果启动成功则返回True，否则返回False
        """
        try:
            # 启动网络
            self.network.start()
            logger.info(f"启动网络: {self.network.network_id}")
            
            # 启动所有节点
            for node_id, node in self.nodes.items():
                node.start()
                logger.info(f"启动节点: {node_id}")
            
            # 启动心跳监视器
            self.heartbeat_monitor.start()
            logger.info(f"启动心跳监视器: {self.heartbeat_monitor.monitor_id}")
            
            # 启动故障检测器
            self.fault_detector.start()
            logger.info(f"启动故障检测器: {self.fault_detector.detector_id}")
            
            # 启动共识协议
            self.consensus_protocol.start()
            logger.info(f"启动共识协议: {self.consensus_protocol.protocol_id}")
            
            # 启动事务管理器
            self.transaction_manager.start()
            logger.info(f"启动事务管理器: {self.transaction_manager.manager_id}")
            
            # 启动任务调度器
            self.task_scheduler.start()
            logger.info(f"启动任务调度器: {self.task_scheduler.scheduler_id}")
            
            # 启动负载均衡器
            self.load_balancer.start()
            logger.info(f"启动负载均衡器: {self.load_balancer.balancer_id}")
            
            # 启动资源管理器
            self.resource_manager.start()
            logger.info(f"启动资源管理器: {self.resource_manager.manager_id}")
            
            # 设置应用状态
            self.is_running = True
            logger.info(f"分布式应用已启动: {self.app_name}")
            
            return True
        except Exception as e:
            logger.error(f"启动分布式应用失败: {str(e)}")
            self.stop()
            return False
    
    def stop(self) -> bool:
        """
        停止分布式应用
        
        返回:
            bool: 如果停止成功则返回True，否则返回False
        """
        try:
            # 停止资源管理器
            if hasattr(self, 'resource_manager') and self.resource_manager:
                self.resource_manager.stop()
                logger.info(f"停止资源管理器: {self.resource_manager.manager_id}")
            
            # 停止负载均衡器
            if hasattr(self, 'load_balancer') and self.load_balancer:
                self.load_balancer.stop()
                logger.info(f"停止负载均衡器: {self.load_balancer.balancer_id}")
            
            # 停止任务调度器
            if hasattr(self, 'task_scheduler') and self.task_scheduler:
                self.task_scheduler.stop()
                logger.info(f"停止任务调度器: {self.task_scheduler.scheduler_id}")
            
            # 停止事务管理器
            if hasattr(self, 'transaction_manager') and self.transaction_manager:
                self.transaction_manager.stop()
                logger.info(f"停止事务管理器: {self.transaction_manager.manager_id}")
            
            # 停止共识协议
            if hasattr(self, 'consensus_protocol') and self.consensus_protocol:
                self.consensus_protocol.stop()
                logger.info(f"停止共识协议: {self.consensus_protocol.protocol_id}")
            
            # 停止故障检测器
            if hasattr(self, 'fault_detector') and self.fault_detector:
                self.fault_detector.stop()
                logger.info(f"停止故障检测器: {self.fault_detector.detector_id}")
            
            # 停止心跳监视器
            if hasattr(self, 'heartbeat_monitor') and self.heartbeat_monitor:
                self.heartbeat_monitor.stop()
                logger.info(f"停止心跳监视器: {self.heartbeat_monitor.monitor_id}")
            
            # 停止所有节点
            if hasattr(self, 'nodes') and self.nodes:
                for node_id, node in self.nodes.items():
                    node.stop()
                    logger.info(f"停止节点: {node_id}")
            
            # 停止网络
            if hasattr(self, 'network') and self.network:
                self.network.stop()
                logger.info(f"停止网络: {self.network.network_id}")
            
            # 设置应用状态
            self.is_running = False
            logger.info(f"分布式应用已停止: {self.app_name}")
            
            return True
        except Exception as e:
            logger.error(f"停止分布式应用失败: {str(e)}")
            return False
    
    def demo_message_passing(self) -> None:
        """演示消息传递功能"""
        logger.info("\n演示消息传递功能:")
        
        # 点对点消息
        source_node = self.nodes["node_0"]
        target_node = self.nodes["node_1"]
        
        message = Message(
            message_id="msg_1",
            source_id=source_node.node_id,
            target_id=target_node.node_id,
            message_type=MessageType.DATA,
            priority=MessagePriority.NORMAL,
            content={"data": "Hello from node_0!"}
        )
        
        # 发送消息
        self.network.send_message(message)
        logger.info(f"发送点对点消息: {message.message_id}, 从 {message.source_id} 到 {message.target_id}")
        
        # 等待消息传递
        time.sleep(1)
        
        # 广播消息
        broadcast_message = Message(
            message_id="broadcast_1",
            source_id=source_node.node_id,
            target_id="*",  # 广播标记
            message_type=MessageType.NOTIFICATION,
            priority=MessagePriority.HIGH,
            content={"notification": "Broadcast message to all nodes!"}
        )
        
        # 发送广播消息
        self.network.broadcast_message(broadcast_message)
        logger.info(f"发送广播消息: {broadcast_message.message_id}, 从 {broadcast_message.source_id} 到所有节点")
        
        # 等待消息传递
        time.sleep(1)
    
    def demo_synchronization(self) -> None:
        """演示同步机制功能"""
        logger.info("\n演示同步机制功能:")
        
        # 创建线程列表
        threads = []
        
        # 共享资源
        shared_counter = 0
        
        # 定义线程函数
        def worker(node_id: str, lock: DistributedLock) -> None:
            nonlocal shared_counter
            
            logger.info(f"线程 {node_id} 尝试获取锁")
            with lock:
                logger.info(f"线程 {node_id} 获取锁成功")
                # 模拟工作
                time.sleep(random.uniform(0.5, 1.5))
                # 更新共享资源
                shared_counter += 1
                logger.info(f"线程 {node_id} 更新共享计数器: {shared_counter}")
            logger.info(f"线程 {node_id} 释放锁")
        
        # 创建并启动线程
        for node_id in self.nodes.keys():
            thread = threading.Thread(target=worker, args=(node_id, self.lock))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        logger.info(f"所有线程完成，最终共享计数器值: {shared_counter}")
        
        # 演示分布式屏障
        logger.info("\n演示分布式屏障:")
        
        # 重置线程列表
        threads = []
        
        # 定义屏障线程函数
        def barrier_worker(node_id: str, barrier: DistributedBarrier) -> None:
            # 模拟第一阶段工作
            work_time = random.uniform(0.5, 2.0)
            logger.info(f"线程 {node_id} 执行第一阶段工作 ({work_time:.2f}秒)")
            time.sleep(work_time)
            
            logger.info(f"线程 {node_id} 到达屏障，等待其他线程")
            barrier.wait()
            logger.info(f"线程 {node_id} 通过屏障")
            
            # 模拟第二阶段工作
            work_time = random.uniform(0.5, 1.5)
            logger.info(f"线程 {node_id} 执行第二阶段工作 ({work_time:.2f}秒)")
            time.sleep(work_time)
            
            logger.info(f"线程 {node_id} 完成所有工作")
        
        # 创建并启动线程
        for node_id in self.nodes.keys():
            thread = threading.Thread(target=barrier_worker, args=(node_id, self.barrier))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        logger.info("所有线程通过屏障并完成工作")
    
    def demo_fault_tolerance(self) -> None:
        """演示容错机制功能"""
        logger.info("\n演示容错机制功能:")
        
        # 模拟节点故障
        failed_node_id = "node_2"
        logger.info(f"模拟节点故障: {failed_node_id}")
        
        # 停止节点
        self.nodes[failed_node_id].stop()
        
        # 等待故障检测
        logger.info("等待故障检测...")
        time.sleep(2)
        
        # 获取故障检测结果
        detection_result = self.fault_detector.get_node_status(failed_node_id)
        logger.info(f"故障检测结果: 节点 {failed_node_id} 状态为 {detection_result}")
        
        # 模拟节点恢复
        logger.info(f"恢复节点: {failed_node_id}")
        self.nodes[failed_node_id].start()
        
        # 等待节点恢复检测
        logger.info("等待节点恢复检测...")
        time.sleep(2)
        
        # 获取恢复检测结果
        recovery_result = self.fault_detector.get_node_status(failed_node_id)
        logger.info(f"恢复检测结果: 节点 {failed_node_id} 状态为 {recovery_result}")
        
        # 演示数据复制
        logger.info("\n演示数据复制:")
        
        # 创建要复制的数据
        data_key = "important_data"
        data_value = {"timestamp": time.time(), "value": random.randint(1, 100)}
        
        # 复制数据
        replication_result = self.replication_manager.replicate_data(
            data_key, data_value, source_node_id="node_0"
        )
        
        logger.info(f"数据复制结果: {replication_result}")
        
        # 获取复制的数据
        for node_id in ["node_0", "node_1", "node_3"]:  # 假设复制到这些节点
            replicated_data = self.replication_manager.get_replicated_data(data_key, node_id)
            logger.info(f"节点 {node_id} 上的复制数据: {replicated_data}")
    
    def demo_consensus(self) -> None:
        """演示共识机制功能"""
        logger.info("\n演示共识机制功能:")
        
        # 提议值
        proposal_value = {"command": "SET", "key": "consensus_key", "value": "consensus_value"}
        
        # 启动共识过程
        logger.info(f"启动共识过程，提议值: {proposal_value}")
        consensus_result = self.consensus_protocol.propose(proposal_value)
        
        # 等待共识达成
        logger.info("等待共识达成...")
        time.sleep(3)
        
        # 获取共识结果
        final_value = self.consensus_protocol.get_consensus_value()
        logger.info(f"共识结果: {final_value}")
        
        # 验证所有节点的值是否一致
        for node_id in self.nodes.keys():
            node_value = self.consensus_protocol.get_node_value(node_id)
            logger.info(f"节点 {node_id} 的值: {node_value}")
    
    def demo_transaction(self) -> None:
        """演示事务管理功能"""
        logger.info("\n演示事务管理功能:")
        
        # 创建事务
        transaction_id = f"tx_{int(time.time())}"
        
        # 启动事务
        logger.info(f"启动事务: {transaction_id}")
        self.transaction_manager.begin_transaction(transaction_id)
        
        # 添加事务操作
        self.transaction_manager.add_operation(
            transaction_id, "node_0", {"operation": "write", "key": "tx_key_1", "value": "value_1"}
        )
        self.transaction_manager.add_operation(
            transaction_id, "node_1", {"operation": "write", "key": "tx_key_2", "value": "value_2"}
        )
        
        # 提交事务
        logger.info(f"提交事务: {transaction_id}")
        commit_result = self.transaction_manager.commit_transaction(transaction_id)
        
        logger.info(f"事务提交结果: {commit_result}")
        
        # 验证事务结果
        for node_id, key in [("node_0", "tx_key_1"), ("node_1", "tx_key_2")]:
            value = self.nodes[node_id].get_data(key)
            logger.info(f"节点 {node_id} 上的键 {key} 的值: {value}")
        
        # 演示事务回滚
        rollback_tx_id = f"tx_rollback_{int(time.time())}"
        
        # 启动事务
        logger.info(f"\n启动回滚事务: {rollback_tx_id}")
        self.transaction_manager.begin_transaction(rollback_tx_id)
        
        # 添加事务操作
        self.transaction_manager.add_operation(
            rollback_tx_id, "node_0", {"operation": "write", "key": "tx_key_3", "value": "temp_value"}
        )
        
        # 回滚事务
        logger.info(f"回滚事务: {rollback_tx_id}")
        rollback_result = self.transaction_manager.rollback_transaction(rollback_tx_id)
        
        logger.info(f"事务回滚结果: {rollback_result}")
        
        # 验证回滚结果
        value = self.nodes["node_0"].get_data("tx_key_3")
        logger.info(f"节点 node_0 上的键 tx_key_3 的值: {value}")
    
    def demo_scheduling(self) -> None:
        """演示任务调度功能"""
        logger.info("\n演示任务调度功能:")
        
        # 创建任务
        tasks = []
        for i in range(10):
            task = {
                "task_id": f"task_{i}",
                "type": "compute",
                "priority": random.randint(1, 5),
                "resources": {"cpu": random.randint(10, 50), "memory": random.randint(100, 500)},
                "dependencies": [] if i < 3 else [f"task_{random.randint(0, i-1)}"],
                "data": {"input": f"input_data_{i}"}
            }
            tasks.append(task)
        
        # 提交任务
        for task in tasks:
            logger.info(f"提交任务: {task['task_id']}, 优先级: {task['priority']}, 资源: {task['resources']}")
            self.task_scheduler.submit_task(task)
        
        # 执行任务调度
        logger.info("执行任务调度...")
        schedule_result = self.task_scheduler.schedule()
        
        logger.info(f"任务调度结果: {schedule_result}")
        
        # 获取任务分配情况
        for node_id in self.nodes.keys():
            node_tasks = self.task_scheduler.get_node_tasks(node_id)
            logger.info(f"节点 {node_id} 分配的任务: {node_tasks}")
        
        # 模拟任务执行
        logger.info("\n模拟任务执行...")
        for i in range(3):  # 模拟几轮任务执行
            # 更新任务状态
            for node_id in self.nodes.keys():
                node_tasks = self.task_scheduler.get_node_tasks(node_id)
                for task_id in node_tasks:
                    # 随机完成一些任务
                    if random.random() < 0.7:
                        self.task_scheduler.complete_task(task_id, {"result": f"result_for_{task_id}"})
                        logger.info(f"完成任务: {task_id}")
            
            # 重新调度
            self.task_scheduler.schedule()
            
            # 等待一段时间
            time.sleep(1)
        
        # 获取任务结果
        completed_tasks = self.task_scheduler.get_completed_tasks()
        logger.info(f"已完成任务: {completed_tasks}")
    
    def demo_mapreduce(self) -> None:
        """演示MapReduce功能"""
        logger.info("\n演示MapReduce功能:")
        
        # 定义Map函数
        def map_function(key: str, value: Any) -> List[Tuple[str, int]]:
            """单词计数的Map函数"""
            words = str(value).split()
            return [(word.lower(), 1) for word in words]
        
        # 定义Reduce函数
        def reduce_function(key: str, values: List[int]) -> int:
            """单词计数的Reduce函数"""
            return sum(values)
        
        # 创建输入数据
        input_data = {
            "doc_1": "Hello world hello",
            "doc_2": "World is beautiful",
            "doc_3": "Hello beautiful world",
            "doc_4": "Programming is fun",
            "doc_5": "World of programming"
        }
        
        # 配置MapReduce作业
        job_config = {
            "job_id": "word_count_job",
            "map_function": map_function,
            "reduce_function": reduce_function,
            "input_data": input_data,
            "num_reducers": 3
        }
        
        # 提交MapReduce作业
        logger.info(f"提交MapReduce作业: {job_config['job_id']}")
        job_id = self.mapreduce.submit_job(job_config)
        
        # 等待作业完成
        logger.info("等待MapReduce作业完成...")
        while not self.mapreduce.is_job_completed(job_id):
            time.sleep(0.5)
        
        # 获取作业结果
        result = self.mapreduce.get_job_result(job_id)
        logger.info(f"MapReduce作业结果: {result}")
    
    def run_comprehensive_demo(self) -> None:
        """运行综合演示"""
        if not self.is_running:
            logger.error("分布式应用未启动，请先调用start()方法")
            return
        
        # 演示消息传递
        self.demo_message_passing()
        
        # 演示同步机制
        self.demo_synchronization()
        
        # 演示容错机制
        self.demo_fault_tolerance()
        
        # 演示共识机制
        self.demo_consensus()
        
        # 演示事务管理
        self.demo_transaction()
        
        # 演示任务调度
        self.demo_scheduling()
        
        # 演示MapReduce
        self.demo_mapreduce()
        
        logger.info("\n综合演示完成!")

def main():
    """主函数"""
    logger.info("综合分布式系统示例")
    
    # 创建分布式应用
    app = DistributedApplication(app_name="comprehensive_demo", node_count=5)
    
    try:
        # 启动应用
        if app.start():
            # 运行综合演示
            app.run_comprehensive_demo()
        else:
            logger.error("启动分布式应用失败")
    finally:
        # 停止应用
        app.stop()
    
    logger.info("示例完成")

if __name__ == "__main__":
    main()
