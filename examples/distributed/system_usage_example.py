"""
分布式系统使用示例

该示例展示了如何使用分布式系统的各个功能。
"""

import numpy as np
import time
from typing import Dict, Any, List
import logging
from src.core.distributed.system_integrator import (
    DistributedSystemIntegrator,
    TopologyLayer,
    OptimizationGoal,
    FusionMode,
    StorageMode
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_network() -> tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """创建测试网络"""
    # 创建节点
    nodes = []
    for i in range(10):
        nodes.append({
            "id": f"node_{i}",
            "type": "compute" if i < 5 else "storage",
            "capacity": np.random.uniform(0.5, 1.0),
            "reliability": np.random.uniform(0.8, 0.99),
            "performance": np.random.uniform(0.7, 0.95)
        })
        
    # 创建链接
    links = []
    for i in range(len(nodes)):
        for j in range(i + 1, len(nodes)):
            if np.random.random() < 0.3:  # 30%概率创建链接
                links.append({
                    "source": nodes[i]["id"],
                    "target": nodes[j]["id"],
                    "latency": np.random.uniform(1, 10),
                    "bandwidth": np.random.uniform(100, 1000),
                    "reliability": np.random.uniform(0.9, 0.99)
                })
                
    return nodes, links

def simulate_topology_fault(node_id: str) -> Dict[str, float]:
    """模拟拓扑故障"""
    return {
        "timestamp": time.time(),
        "connectivity": np.random.uniform(0.3, 0.7),
        "redundancy": np.random.uniform(0.4, 0.8),
        "stability": np.random.uniform(0.5, 0.9),
        "components": [node_id]
    }

def generate_test_states(num_states: int, dimension: int) -> List[np.ndarray]:
    """生成测试状态"""
    states = []
    for _ in range(num_states):
        state = np.random.normal(0, 1, (dimension,))
        # 归一化
        state /= np.linalg.norm(state)
        states.append(state)
    return states

def main():
    """主函数"""
    # 创建系统集成器
    system = DistributedSystemIntegrator()
    
    # 创建测试网络
    logger.info("创建测试网络...")
    nodes, links = create_test_network()
    system.update_network_state(nodes, links)
    
    # 测试拓扑故障处理
    logger.info("\n测试拓扑故障处理:")
    fault_metrics = simulate_topology_fault("node_2")
    recovery_strategies = system.handle_topology_fault(
        TopologyLayer.PHYSICAL,
        fault_metrics
    )
    logger.info(f"生成的恢复策略: {recovery_strategies}")
    
    # 测试路径优化
    logger.info("\n测试路径优化:")
    source = "node_0"
    target = "node_8"
    optimal_paths = system.optimize_path(
        source,
        target,
        OptimizationGoal.MINIMAL_LATENCY
    )
    logger.info(f"从 {source} 到 {target} 的最优路径:")
    for i, path in enumerate(optimal_paths):
        logger.info(f"路径 {i + 1}: {' -> '.join(path)}")
        
    # 测试状态融合
    logger.info("\n测试状态融合:")
    test_states = generate_test_states(3, 10)
    fused_state, fusion_metrics = system.fuse_states(
        test_states,
        mode=FusionMode.ADAPTIVE
    )
    logger.info(f"融合度量: {fusion_metrics}")
    
    # 测试数据存储和检索
    logger.info("\n测试数据存储和检索:")
    test_data = {
        "id": "test_1",
        "content": np.random.random((100, 100)),
        "metadata": {"type": "matrix", "created_at": time.time()}
    }
    
    # 存储数据
    storage_metrics = system.store_data(
        "test_1",
        test_data,
        mode=StorageMode.HYBRID
    )
    logger.info(f"存储度量: {storage_metrics}")
    
    # 检索数据
    retrieved_data, retrieval_metrics = system.retrieve_data("test_1")
    logger.info(f"检索度量: {retrieval_metrics}")
    
    # 获取系统度量
    logger.info("\n系统度量:")
    system_metrics = system.get_system_metrics()
    logger.info(f"拓扑度量: {system_metrics['topology']}")
    logger.info(f"路径度量: {system_metrics['paths']}")
    logger.info(f"存储度量: {system_metrics['storage']}")
    logger.info(f"故障统计: {system_metrics['faults']}")

if __name__ == "__main__":
    main()