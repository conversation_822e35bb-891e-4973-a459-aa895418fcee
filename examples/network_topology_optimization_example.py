"""
网络拓扑优化算子示例

本示例展示了如何使用网络拓扑优化算子进行网络拓扑优化。
"""

import sys
import os
import time
import logging
import random
import numpy as np
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入网络拓扑优化算子
from src.transcendental_tensor.performance_optimization.network_topology import (
    NetworkNode, NetworkLink, NetworkTopology,
    NodeRole, NodeStatus, LinkType,
    OptimizationGoal, OptimizationStrategy, OptimizationResult,
    TopologyAnalyzer, TopologyOptimizer
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def optimization_callback(result: OptimizationResult) -> None:
    """
    优化回调函数
    
    参数:
        result (OptimizationResult): 优化结果
    """
    logger.info(f"Optimization callback: {result.result_id}")
    
    # 打印优化指标
    for metric_name, metric_value in result.metrics.items():
        logger.info(f"  {metric_name}: {metric_value}")


def create_test_topology(node_count: int = 10) -> NetworkTopology:
    """
    创建测试拓扑
    
    参数:
        node_count (int): 节点数量
        
    返回:
        NetworkTopology: 测试拓扑
    """
    # 创建拓扑
    topology = NetworkTopology(name="TestTopology")
    
    # 创建节点
    for i in range(node_count):
        # 随机位置
        location = (
            random.uniform(0, 100),
            random.uniform(0, 100),
            random.uniform(0, 100)
        )
        
        # 创建节点
        node = NetworkNode(
            node_id=f"node-{i}",
            name=f"Node {i}",
            role=NodeRole.WORKER,
            status=NodeStatus.ONLINE,
            address=f"192.168.1.{i+10}",
            port=8000 + i,
            location=location,
            resources={
                "cpu": random.uniform(1, 8),
                "memory": random.uniform(4, 32),
                "storage": random.uniform(100, 1000)
            }
        )
        
        # 添加节点
        topology.add_node(node)
    
    # 创建链接
    for i in range(node_count):
        for j in range(i + 1, node_count):
            # 随机决定是否创建链接
            if random.random() < 0.3:
                # 计算节点之间的距离
                node_i = topology.nodes[f"node-{i}"]
                node_j = topology.nodes[f"node-{j}"]
                
                distance = np.sqrt(
                    (node_i.location[0] - node_j.location[0]) ** 2 +
                    (node_i.location[1] - node_j.location[1]) ** 2 +
                    (node_i.location[2] - node_j.location[2]) ** 2
                )
                
                # 创建链接
                link = NetworkLink(
                    source_id=f"node-{i}",
                    target_id=f"node-{j}",
                    link_type=LinkType.PHYSICAL,
                    latency=distance / 10,  # 延迟与距离成正比
                    bandwidth=100 / (distance + 1),  # 带宽与距离成反比
                    packet_loss=distance / 1000,  # 丢包率与距离成正比
                    status=True,
                    weight=1.0,
                    bidirectional=True
                )
                
                # 添加链接
                topology.add_link(link)
    
    return topology


def main():
    """主函数"""
    logger.info("Starting network topology optimization example")
    
    # 创建测试拓扑
    topology = create_test_topology(10)
    
    logger.info(f"Created test topology with {len(topology.nodes)} nodes and {len(topology.links)} links")
    
    # 创建拓扑分析器
    analyzer = TopologyAnalyzer()
    
    # 分析拓扑
    analysis_result = analyzer.analyze_topology(topology)
    
    logger.info("Topology analysis result:")
    for metric_name, metric_value in analysis_result["metrics"].items():
        logger.info(f"  {metric_name}: {metric_value}")
    
    # 创建拓扑优化器
    optimizer = TopologyOptimizer(
        strategy_type=OptimizationStrategy.MINIMUM_SPANNING_TREE,
        analyzer=analyzer
    )
    
    # 添加优化回调函数
    optimizer.add_optimization_callback(optimization_callback)
    
    # 优化拓扑（最小生成树策略）
    logger.info("Optimizing topology using Minimum Spanning Tree strategy")
    mst_result = optimizer.optimize_topology(
        topology,
        goal=OptimizationGoal.LATENCY
    )
    
    logger.info(f"MST optimization result: {len(mst_result.optimized_topology.links)} links")
    
    # 优化拓扑（最短路径树策略）
    logger.info("Optimizing topology using Shortest Path Tree strategy")
    spt_result = optimizer.optimize_topology(
        topology,
        goal=OptimizationGoal.LATENCY,
        strategy_type=OptimizationStrategy.SHORTEST_PATH_TREE
    )
    
    logger.info(f"SPT optimization result: {len(spt_result.optimized_topology.links)} links")
    
    # 优化拓扑（K近邻策略）
    logger.info("Optimizing topology using K-Nearest Neighbors strategy")
    knn_result = optimizer.optimize_topology(
        topology,
        goal=OptimizationGoal.LATENCY,
        strategy_type=OptimizationStrategy.K_NEAREST_NEIGHBORS
    )
    
    logger.info(f"KNN optimization result: {len(knn_result.optimized_topology.links)} links")
    
    # 使用多策略优化拓扑
    logger.info("Optimizing topology using multiple strategies")
    multi_result = optimizer.optimize_topology_multi_strategy(
        topology,
        goal=OptimizationGoal.LATENCY
    )
    
    logger.info(f"Multi-strategy optimization result: {len(multi_result.optimized_topology.links)} links")
    logger.info(f"Selected strategy: {multi_result.optimization_strategy}")
    
    # 比较不同策略的结果
    logger.info("Comparing optimization results:")
    logger.info(f"  Original topology: {len(topology.links)} links")
    logger.info(f"  MST optimized topology: {len(mst_result.optimized_topology.links)} links")
    logger.info(f"  SPT optimized topology: {len(spt_result.optimized_topology.links)} links")
    logger.info(f"  KNN optimized topology: {len(knn_result.optimized_topology.links)} links")
    logger.info(f"  Multi-strategy optimized topology: {len(multi_result.optimized_topology.links)} links")
    
    # 打印优化历史
    optimization_history = optimizer.get_optimization_history()
    logger.info(f"Optimization history: {len(optimization_history)} entries")
    
    logger.info("Network topology optimization example completed")


if __name__ == "__main__":
    main()
