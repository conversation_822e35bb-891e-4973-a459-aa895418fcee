#!/usr/bin/env python3
"""
简单的算子使用示例脚本
"""

import os
import sys
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('..'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 直接导入算子类
from src.operators.explanation.multilevel_explanation import MultilevelExplanationOperator
from src.operators.verification.multi_method_verification import MultiMethodVerificationOperator

def main():
    """主函数"""
    print("=" * 80)
    print(" 简单的算子使用示例 ".center(80, "="))
    print("=" * 80)
    
    # 创建多层次解释生成算子
    print("\n创建多层次解释生成算子...")
    multilevel_explanation_operator = MultilevelExplanationOperator()
    
    # 准备输入数据
    input_data = {
        "model_output": {
            "prediction": "positive",
            "confidence": 0.85,
            "features": {
                "text_length": 120,
                "sentiment_score": 0.75,
                "topic_relevance": 0.9
            }
        },
        "explanation_level": "all",
        "user_expertise": "beginner"
    }
    
    # 应用算子
    print("\n应用多层次解释生成算子...")
    result = multilevel_explanation_operator.apply(input_data)
    
    # 打印结果
    print("\n多层次解释生成算子结果:")
    for key, value in result.items():
        print(f"{key}: {value}")
    
    print("\n" + "=" * 80)
    
    # 创建多方法验证算子
    print("\n创建多方法验证算子...")
    multi_method_verification_operator = MultiMethodVerificationOperator()
    
    # 准备输入数据
    input_data = {
        "state": {
            "variables": {
                "x": 10,
                "y": 20,
                "z": 30
            }
        },
        "properties": [
            {
                "id": "safety1",
                "type": "safety",
                "expression": "G(x > 0)"
            },
            {
                "id": "liveness1",
                "type": "liveness",
                "expression": "F(y = 1)"
            },
            {
                "id": "runtime1",
                "type": "runtime",
                "expression": "x + y < 100"
            }
        ]
    }
    
    # 应用算子
    print("\n应用多方法验证算子...")
    result = multi_method_verification_operator.apply(input_data)
    
    # 打印结果
    print("\n多方法验证算子结果:")
    for key, value in result.items():
        if isinstance(value, dict):
            print(f"{key}:")
            for k, v in value.items():
                print(f"  {k}: {v}")
        else:
            print(f"{key}: {value}")

if __name__ == "__main__":
    main()
