"""
博弈优化资源调度算法示例

本脚本展示了如何使用博弈优化资源调度算法。
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import sys
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入我们的实现
from src.algorithms.game_theory.scheduler import GameTheoreticScheduler
from src.algorithms.game_theory.models import GameModelBuilder
from src.algorithms.game_theory.nash_solver import NashEquilibriumSolver
from src.algorithms.game_theory.analysis import ResourceAllocationAnalyzer


def create_test_data():
    """创建测试数据"""
    # 创建玩家
    players = [
        {'id': f'player{i}', 'name': f'Player {i}', 'capacity': 5.0} for i in range(1, 6)
    ]
    
    # 创建资源
    resources = [
        {'id': 'cpu', 'name': 'CPU', 'capacity': 100.0},
        {'id': 'memory', 'name': 'Memory', 'capacity': 256.0},
        {'id': 'storage', 'name': 'Storage', 'capacity': 1024.0},
        {'id': 'network', 'name': 'Network', 'capacity': 50.0},
        {'id': 'gpu', 'name': 'GPU', 'capacity': 8.0}
    ]
    
    # 创建任务
    tasks = [
        {
            'id': 'task1', 
            'name': 'Data Processing', 
            'requirements': {'cpu': 20.0, 'memory': 64.0, 'storage': 256.0, 'network': 10.0}
        },
        {
            'id': 'task2', 
            'name': 'Machine Learning', 
            'requirements': {'cpu': 40.0, 'memory': 128.0, 'storage': 512.0, 'gpu': 4.0}
        },
        {
            'id': 'task3', 
            'name': 'Web Serving', 
            'requirements': {'cpu': 10.0, 'memory': 32.0, 'storage': 128.0, 'network': 20.0}
        },
        {
            'id': 'task4', 
            'name': 'Database', 
            'requirements': {'cpu': 30.0, 'memory': 64.0, 'storage': 512.0, 'network': 5.0}
        },
        {
            'id': 'task5', 
            'name': 'Video Processing', 
            'requirements': {'cpu': 50.0, 'memory': 128.0, 'storage': 256.0, 'gpu': 2.0, 'network': 15.0}
        }
    ]
    
    # 创建约束条件
    constraints = {
        'resource_constraints': {
            'cpu': {'min': 0.0, 'max': 100.0},
            'memory': {'min': 0.0, 'max': 256.0},
            'storage': {'min': 0.0, 'max': 1024.0},
            'network': {'min': 0.0, 'max': 50.0},
            'gpu': {'min': 0.0, 'max': 8.0}
        },
        'task_constraints': {
            'task1': {'min_players': 1, 'max_players': 2},
            'task2': {'min_players': 1, 'max_players': 3},
            'task3': {'min_players': 1, 'max_players': 2},
            'task4': {'min_players': 1, 'max_players': 2},
            'task5': {'min_players': 2, 'max_players': 3}
        }
    }
    
    # 创建偏好设置
    preferences = {}
    for i in range(1, 6):
        player_prefs = {}
        # 资源偏好
        for resource in resources:
            resource_id = resource['id']
            player_prefs[resource_id] = np.random.uniform(0.1, 1.0)
        
        # 任务偏好
        for task in tasks:
            task_id = task['id']
            player_prefs[task_id] = np.random.uniform(0.1, 1.0)
        
        preferences[f'player{i}'] = player_prefs
    
    return players, resources, tasks, constraints, preferences


def run_non_cooperative_game(players, resources, tasks, constraints, preferences, output_dir=None):
    """运行非合作博弈"""
    # 创建输出目录
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # 创建调度器
    scheduler = GameTheoreticScheduler(
        game_type='non_cooperative',
        max_iterations=50,
        tolerance=1e-6,
        use_parallel=True,
        num_workers=4,
        adaptive_strategy=True,
        fairness_weight=0.5,
        efficiency_weight=0.5
    )
    
    # 创建输入数据
    input_data = {
        'players': players,
        'resources': resources,
        'tasks': tasks,
        'constraints': constraints,
        'preferences': preferences
    }
    
    # 执行计算
    print("执行非合作博弈优化...")
    start_time = time.time()
    result = scheduler.compute(input_data)
    end_time = time.time()
    
    print(f"计算完成，耗时: {end_time - start_time:.2f}秒")
    
    # 提取结果
    allocation = result['allocation']
    utilities = result['utilities']
    equilibrium = result['equilibrium']
    iterations = result['iterations']
    convergence_achieved = result['convergence_achieved']
    performance = result['performance']
    analysis = result['analysis']
    
    # 打印结果
    print(f"迭代次数: {iterations}")
    print(f"是否收敛: {convergence_achieved}")
    print(f"最终效用: {performance['final_utility']:.6f}")
    print(f"公平性指数: {analysis['fairness_index']:.6f}")
    print(f"资源利用率: {analysis['resource_utilization']:.6f}")
    print(f"任务完成率: {analysis['task_completion_rate']:.6f}")
    
    # 创建资源分配分析器
    analyzer = ResourceAllocationAnalyzer(verbose=True)
    
    # 绘制效用分布图
    fig_utility = analyzer.plot_utility_distribution(utilities, title="非合作博弈 - 效用分布")
    if output_dir:
        fig_utility.savefig(os.path.join(output_dir, 'non_cooperative_utility.png'))
    
    # 绘制资源分配图
    fig_resource = analyzer.plot_resource_allocation(resources, allocation, title="非合作博弈 - 资源分配")
    if output_dir:
        fig_resource.savefig(os.path.join(output_dir, 'non_cooperative_resource.png'))
    
    # 绘制任务分配图
    fig_task = analyzer.plot_task_assignment(players, tasks, allocation, title="非合作博弈 - 任务分配")
    if output_dir:
        fig_task.savefig(os.path.join(output_dir, 'non_cooperative_task.png'))
    
    # 绘制性能指标图
    fig_metrics = analyzer.plot_performance_metrics(analysis, title="非合作博弈 - 性能指标")
    if output_dir:
        fig_metrics.savefig(os.path.join(output_dir, 'non_cooperative_metrics.png'))
    
    # 显示图形
    plt.show()
    
    return result


def run_cooperative_game(players, resources, tasks, constraints, preferences, output_dir=None):
    """运行合作博弈"""
    # 创建输出目录
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # 创建调度器
    scheduler = GameTheoreticScheduler(
        game_type='cooperative',
        max_iterations=50,
        tolerance=1e-6,
        use_parallel=True,
        num_workers=4,
        adaptive_strategy=True,
        fairness_weight=0.7,  # 更注重公平性
        efficiency_weight=0.3
    )
    
    # 创建输入数据
    input_data = {
        'players': players,
        'resources': resources,
        'tasks': tasks,
        'constraints': constraints,
        'preferences': preferences
    }
    
    # 执行计算
    print("执行合作博弈优化...")
    start_time = time.time()
    result = scheduler.compute(input_data)
    end_time = time.time()
    
    print(f"计算完成，耗时: {end_time - start_time:.2f}秒")
    
    # 提取结果
    allocation = result['allocation']
    utilities = result['utilities']
    equilibrium = result['equilibrium']
    iterations = result['iterations']
    convergence_achieved = result['convergence_achieved']
    performance = result['performance']
    analysis = result['analysis']
    
    # 打印结果
    print(f"迭代次数: {iterations}")
    print(f"是否收敛: {convergence_achieved}")
    print(f"最终效用: {performance['final_utility']:.6f}")
    print(f"公平性指数: {analysis['fairness_index']:.6f}")
    print(f"资源利用率: {analysis['resource_utilization']:.6f}")
    print(f"任务完成率: {analysis['task_completion_rate']:.6f}")
    
    # 创建资源分配分析器
    analyzer = ResourceAllocationAnalyzer(verbose=True)
    
    # 绘制效用分布图
    fig_utility = analyzer.plot_utility_distribution(utilities, title="合作博弈 - 效用分布")
    if output_dir:
        fig_utility.savefig(os.path.join(output_dir, 'cooperative_utility.png'))
    
    # 绘制资源分配图
    fig_resource = analyzer.plot_resource_allocation(resources, allocation, title="合作博弈 - 资源分配")
    if output_dir:
        fig_resource.savefig(os.path.join(output_dir, 'cooperative_resource.png'))
    
    # 绘制任务分配图
    fig_task = analyzer.plot_task_assignment(players, tasks, allocation, title="合作博弈 - 任务分配")
    if output_dir:
        fig_task.savefig(os.path.join(output_dir, 'cooperative_task.png'))
    
    # 绘制性能指标图
    fig_metrics = analyzer.plot_performance_metrics(analysis, title="合作博弈 - 性能指标")
    if output_dir:
        fig_metrics.savefig(os.path.join(output_dir, 'cooperative_metrics.png'))
    
    # 显示图形
    plt.show()
    
    return result


def compare_game_types(non_cooperative_result, cooperative_result, output_dir=None):
    """比较不同博弈类型的结果"""
    # 创建输出目录
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # 提取结果
    non_coop_analysis = non_cooperative_result['analysis']
    coop_analysis = cooperative_result['analysis']
    
    # 创建比较图
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 获取指标名称和值
    metric_names = list(non_coop_analysis.keys())
    non_coop_values = [non_coop_analysis[name] for name in metric_names]
    coop_values = [coop_analysis[name] for name in metric_names]
    
    # 设置x轴位置
    x = np.arange(len(metric_names))
    width = 0.35
    
    # 绘制条形图
    ax.bar(x - width/2, non_coop_values, width, label='非合作博弈', alpha=0.7)
    ax.bar(x + width/2, coop_values, width, label='合作博弈', alpha=0.7)
    
    # 设置图表属性
    ax.set_xlabel('指标')
    ax.set_ylabel('值')
    ax.set_title('博弈类型比较')
    ax.set_xticks(x)
    ax.set_xticklabels(metric_names)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 设置y轴范围
    ax.set_ylim(0, 1.1)
    
    # 添加数值标签
    for i, v in enumerate(non_coop_values):
        ax.text(i - width/2, v + 0.05, f'{v:.2f}', ha='center', va='bottom')
    
    for i, v in enumerate(coop_values):
        ax.text(i + width/2, v + 0.05, f'{v:.2f}', ha='center', va='bottom')
    
    # 旋转x轴标签，避免重叠
    plt.xticks(rotation=45, ha='right')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图形
    if output_dir:
        plt.savefig(os.path.join(output_dir, 'game_type_comparison.png'))
    
    # 显示图形
    plt.show()
    
    # 打印比较结果
    print("=" * 50)
    print("博弈类型比较")
    print("=" * 50)
    print(f"{'指标':<20} {'非合作博弈':<15} {'合作博弈':<15} {'差异':<15}")
    print("-" * 65)
    for name in metric_names:
        non_coop_value = non_coop_analysis[name]
        coop_value = coop_analysis[name]
        diff = coop_value - non_coop_value
        print(f"{name:<20} {non_coop_value:<15.6f} {coop_value:<15.6f} {diff:<15.6f}")
    print("=" * 50)


def main():
    """主函数"""
    # 设置随机种子，确保结果可重现
    np.random.seed(42)
    
    # 创建输出目录
    output_dir = 'output/game_scheduler'
    
    # 创建测试数据
    players, resources, tasks, constraints, preferences = create_test_data()
    
    # 运行非合作博弈
    non_cooperative_result = run_non_cooperative_game(
        players, resources, tasks, constraints, preferences, output_dir
    )
    
    # 运行合作博弈
    cooperative_result = run_cooperative_game(
        players, resources, tasks, constraints, preferences, output_dir
    )
    
    # 比较不同博弈类型的结果
    compare_game_types(non_cooperative_result, cooperative_result, output_dir)
    
    print("示例完成！")


if __name__ == "__main__":
    main()
