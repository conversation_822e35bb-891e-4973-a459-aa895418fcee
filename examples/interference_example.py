"""
非线性干涉优化算法示例

本脚本展示了如何使用非线性干涉优化算法。
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import sys
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 直接导入我们的实现
from direct_test import (
    NonlinearInterferenceOptimizer,
    InterferencePatternGenerator,
    InterferenceResultAnalyzer,
    create_test_states
)


def main():
    """主函数"""
    print("创建测试状态...")
    quantum_state, holographic_state = create_test_states()
    
    print("创建优化器...")
    optimizer = NonlinearInterferenceOptimizer(
        lambda_init=0.5,
        max_iterations=50,
        tolerance=1e-6,
        use_parallel=True,
        num_workers=4,
        adaptive_lambda=True
    )
    
    print("执行计算...")
    start_time = time.time()
    result = optimizer.compute((quantum_state, holographic_state))
    end_time = time.time()
    
    print(f"计算完成！耗时: {end_time - start_time:.2f}秒")
    print(f"迭代次数: {result['iterations']}")
    print(f"最终lambda参数: {result['lambda_param']:.6f}")
    print(f"最终熵损失: {result['entropy_loss']:.6f}")
    
    print("创建结果分析器...")
    analyzer = InterferenceResultAnalyzer(verbose=True)
    
    print("分析结果...")
    analysis = analyzer.analyze(
        quantum_state, holographic_state, result['fused_state'], result['lambda_param']
    )
    
    # 绘制状态比较图
    plt.figure(figsize=(10, 6))
    x = np.arange(len(quantum_state))
    plt.bar(x - 0.2, np.abs(quantum_state) ** 2, width=0.2, label='量子态', alpha=0.7)
    plt.bar(x, np.abs(holographic_state) ** 2, width=0.2, label='全息态', alpha=0.7)
    plt.bar(x + 0.2, np.abs(result['fused_state']) ** 2, width=0.2, label='融合态', alpha=0.7)
    plt.xlabel('状态索引')
    plt.ylabel('概率')
    plt.title('状态比较')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig('states_comparison.png')
    
    # 绘制熵损失历史图
    plt.figure(figsize=(10, 6))
    x = np.arange(len(result['performance']['entropy_history']))
    plt.plot(x, result['performance']['entropy_history'], marker='o', linestyle='-', linewidth=2)
    plt.xlabel('迭代次数')
    plt.ylabel('熵损失')
    plt.title('熵损失历史')
    plt.grid(True, alpha=0.3)
    plt.savefig('entropy_history.png')
    
    # 绘制Lambda参数历史图
    plt.figure(figsize=(10, 6))
    x = np.arange(len(result['performance']['lambda_history']))
    plt.plot(x, result['performance']['lambda_history'], marker='o', linestyle='-', linewidth=2)
    plt.xlabel('迭代次数')
    plt.ylabel('Lambda参数')
    plt.title('Lambda参数历史')
    plt.grid(True, alpha=0.3)
    plt.savefig('lambda_history.png')
    
    print("示例成功完成！")
    print("生成的图表已保存为states_comparison.png、entropy_history.png和lambda_history.png")


if __name__ == "__main__":
    main()
