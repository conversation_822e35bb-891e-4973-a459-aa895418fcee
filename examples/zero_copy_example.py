#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
零拷贝和内存映射示例

展示如何使用零拷贝和内存映射优化数据处理。
"""

import os
import time
import numpy as np
import pyarrow as pa

# 导入零拷贝和内存映射优化
from core.data.cache.zero_copy import numpy_to_arrow, arrow_to_numpy, table_to_dict, dict_to_table
from core.data.cache.memory_map import memory_map_array, memory_map_table
from core.data.cache.utils import format_size

def test_zero_copy():
    """测试零拷贝优化"""
    print("零拷贝优化示例")
    print("=" * 50)
    
    # 创建测试数据
    print("创建测试数据...")
    data_small = np.random.rand(1000, 1000).astype(np.float64)  # ~8MB
    data_large = np.random.rand(5000, 5000).astype(np.float64)  # ~200MB
    data_complex = (np.random.rand(1000, 1000) + 1j * np.random.rand(1000, 1000)).astype(np.complex128)  # ~16MB
    
    print(f"小数据集大小: {format_size(data_small.nbytes)}")
    print(f"大数据集大小: {format_size(data_large.nbytes)}")
    print(f"复数数据集大小: {format_size(data_complex.nbytes)}")
    print("-" * 50)
    
    # NumPy到Arrow转换
    print("NumPy到Arrow转换:")
    
    # 小数据集
    start_time = time.time()
    arrow_small = numpy_to_arrow(data_small)
    small_time = time.time() - start_time
    print(f"小数据集: {small_time:.6f}秒")
    
    # 大数据集
    start_time = time.time()
    arrow_large = numpy_to_arrow(data_large)
    large_time = time.time() - start_time
    print(f"大数据集: {large_time:.6f}秒")
    
    # 复数数据集
    start_time = time.time()
    arrow_complex = numpy_to_arrow(data_complex)
    complex_time = time.time() - start_time
    print(f"复数数据集: {complex_time:.6f}秒")
    print("-" * 50)
    
    # Arrow到NumPy转换
    print("Arrow到NumPy转换:")
    
    # 小数据集
    start_time = time.time()
    numpy_small = arrow_to_numpy(arrow_small)
    small_time = time.time() - start_time
    print(f"小数据集: {small_time:.6f}秒")
    
    # 大数据集
    start_time = time.time()
    numpy_large = arrow_to_numpy(arrow_large)
    large_time = time.time() - start_time
    print(f"大数据集: {large_time:.6f}秒")
    
    # 复数数据集
    start_time = time.time()
    numpy_complex = arrow_to_numpy(arrow_complex)
    complex_time = time.time() - start_time
    print(f"复数数据集: {complex_time:.6f}秒")
    print("-" * 50)
    
    # 验证数据一致性
    print("验证数据一致性:")
    print(f"小数据集: {np.allclose(data_small, numpy_small)}")
    print(f"大数据集: {np.allclose(data_large, numpy_large)}")
    print(f"复数数据集: {np.allclose(data_complex, numpy_complex)}")
    print("-" * 50)
    
    # 字典和表格转换
    print("字典和表格转换:")
    
    # 创建字典
    data_dict = {
        'col1': np.random.rand(1000),
        'col2': np.random.rand(1000),
        'col3': np.random.rand(1000)
    }
    
    # 字典到表格
    start_time = time.time()
    table = dict_to_table(data_dict)
    dict_to_table_time = time.time() - start_time
    print(f"字典到表格: {dict_to_table_time:.6f}秒")
    
    # 表格到字典
    start_time = time.time()
    result_dict = table_to_dict(table)
    table_to_dict_time = time.time() - start_time
    print(f"表格到字典: {table_to_dict_time:.6f}秒")
    
    # 验证数据一致性
    print("验证数据一致性:")
    for key in data_dict:
        print(f"{key}: {np.allclose(data_dict[key], result_dict[key])}")
    print("-" * 50)

def test_memory_map():
    """测试内存映射优化"""
    print("内存映射优化示例")
    print("=" * 50)
    
    # 创建测试数据
    print("创建测试数据...")
    data_large = np.random.rand(5000, 5000).astype(np.float64)  # ~200MB
    arrow_large = numpy_to_arrow(data_large)
    
    print(f"数据大小: {format_size(data_large.nbytes)}")
    print("-" * 50)
    
    # 创建内存映射
    print("创建内存映射:")
    
    start_time = time.time()
    mmap_obj = memory_map_array(arrow_large)
    create_time = time.time() - start_time
    print(f"创建内存映射: {create_time:.6f}秒")
    
    # 获取数据
    start_time = time.time()
    result = mmap_obj.get_array()
    get_time = time.time() - start_time
    print(f"获取数据: {get_time:.6f}秒")
    
    # 验证数据一致性
    print("验证数据一致性:")
    print(f"数据一致: {np.allclose(arrow_to_numpy(result), data_large)}")
    
    # 关闭内存映射
    mmap_obj.close()
    print("内存映射已关闭")
    print("-" * 50)
    
    # 表格内存映射
    print("表格内存映射:")
    
    # 创建表格
    data_dict = {
        'col1': np.random.rand(1000),
        'col2': np.random.rand(1000),
        'col3': np.random.rand(1000)
    }
    table = dict_to_table(data_dict)
    
    # 创建内存映射
    start_time = time.time()
    mmap_obj = memory_map_table(table)
    create_time = time.time() - start_time
    print(f"创建表格内存映射: {create_time:.6f}秒")
    
    # 获取表格
    start_time = time.time()
    result_table = mmap_obj.get_table()
    get_time = time.time() - start_time
    print(f"获取表格: {get_time:.6f}秒")
    
    # 验证数据一致性
    result_dict = table_to_dict(result_table)
    print("验证数据一致性:")
    for key in data_dict:
        print(f"{key}: {np.allclose(data_dict[key], result_dict[key])}")
    
    # 关闭内存映射
    mmap_obj.close()
    print("表格内存映射已关闭")
    print("-" * 50)

def main():
    """主函数"""
    # 测试零拷贝优化
    test_zero_copy()
    
    print("\n")
    
    # 测试内存映射优化
    test_memory_map()

if __name__ == '__main__':
    main()
