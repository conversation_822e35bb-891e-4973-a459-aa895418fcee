"""
FFT融合方法示例

本脚本展示了如何使用FFT融合方法。
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import sys
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入我们的实现
from src.algorithms.fft_fusion.fusion import FFTFusion
from src.algorithms.fft_fusion.transforms import FFTTransformer
from src.algorithms.fft_fusion.filters import FrequencyDomainFilter
from src.algorithms.fft_fusion.analysis import FusionAnalyzer


def create_test_signals():
    """创建测试信号"""
    # 创建时间轴
    t = np.linspace(0, 1, 1000)
    
    # 创建信号1：正弦波
    signal1 = np.sin(2 * np.pi * 5 * t)
    
    # 创建信号2：余弦波
    signal2 = np.cos(2 * np.pi * 10 * t)
    
    # 创建信号3：方波
    signal3 = np.sign(np.sin(2 * np.pi * 2 * t))
    
    return t, [signal1, signal2, signal3]


def create_test_images():
    """创建测试图像"""
    # 创建图像1：水平条纹
    image1 = np.zeros((100, 100))
    for i in range(0, 100, 10):
        image1[i:i+5, :] = 1.0
    
    # 创建图像2：垂直条纹
    image2 = np.zeros((100, 100))
    for i in range(0, 100, 10):
        image2[:, i:i+5] = 1.0
    
    # 创建图像3：对角线条纹
    image3 = np.zeros((100, 100))
    for i in range(100):
        image3[i, i] = 1.0
        if i < 95:
            image3[i, i+5] = 1.0
        if i > 4:
            image3[i, i-5] = 1.0
    
    return [image1, image2, image3]


def plot_signals(t, signals, title="信号"):
    """绘制信号"""
    plt.figure(figsize=(10, 6))
    
    for i, signal in enumerate(signals):
        plt.plot(t, signal, label=f"信号 {i+1}")
    
    plt.xlabel('时间')
    plt.ylabel('幅度')
    plt.title(title)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()


def plot_images(images, titles=None):
    """绘制图像"""
    n = len(images)
    
    if titles is None:
        titles = [f"图像 {i+1}" for i in range(n)]
    
    fig, axes = plt.subplots(1, n, figsize=(4 * n, 4))
    
    for i, (image, title) in enumerate(zip(images, titles)):
        if n == 1:
            ax = axes
        else:
            ax = axes[i]
        
        ax.imshow(image, cmap='gray')
        ax.set_title(title)
        ax.axis('off')
    
    plt.tight_layout()
    plt.show()


def plot_frequency_domain(frequency_domain, title="频域表示"):
    """绘制频域表示"""
    plt.figure(figsize=(10, 6))
    
    # 计算幅度谱
    amplitude = np.abs(frequency_domain)
    
    # 计算相位谱
    phase = np.angle(frequency_domain)
    
    # 绘制幅度谱
    plt.subplot(2, 1, 1)
    plt.plot(amplitude)
    plt.xlabel('频率')
    plt.ylabel('幅度')
    plt.title(f"{title} - 幅度谱")
    plt.grid(True, alpha=0.3)
    
    # 绘制相位谱
    plt.subplot(2, 1, 2)
    plt.plot(phase)
    plt.xlabel('频率')
    plt.ylabel('相位')
    plt.title(f"{title} - 相位谱")
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()


def plot_frequency_domain_2d(frequency_domain, title="频域表示"):
    """绘制2D频域表示"""
    plt.figure(figsize=(10, 6))
    
    # 计算幅度谱
    amplitude = np.log(1 + np.abs(frequency_domain))
    
    # 计算相位谱
    phase = np.angle(frequency_domain)
    
    # 绘制幅度谱
    plt.subplot(1, 2, 1)
    plt.imshow(amplitude, cmap='viridis')
    plt.title(f"{title} - 幅度谱")
    plt.colorbar()
    
    # 绘制相位谱
    plt.subplot(1, 2, 2)
    plt.imshow(phase, cmap='hsv')
    plt.title(f"{title} - 相位谱")
    plt.colorbar()
    
    plt.tight_layout()
    plt.show()


def fuse_signals(signals, fusion_method='weighted', window_type='hann', output_dir=None):
    """融合信号"""
    # 创建输出目录
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # 创建融合器
    fusion = FFTFusion(
        fusion_method=fusion_method,
        window_type=window_type,
        overlap_ratio=0.5,
        use_parallel=True,
        num_workers=4,
        use_cache=True,
        cache_size=10
    )
    
    # 执行计算
    print(f"使用{fusion_method}方法融合信号...")
    start_time = time.time()
    result = fusion.compute(signals)
    end_time = time.time()
    
    print(f"计算完成，耗时: {end_time - start_time:.2f}秒")
    
    # 提取结果
    fused_signal = result['fused_signal']
    frequency_domain = result['frequency_domain']
    fusion_quality = result['fusion_quality']
    performance = result['performance']
    
    # 打印性能指标
    print(f"性能指标:")
    print(f"  总时间: {performance['total_time']:.2f}秒")
    print(f"  变换时间: {performance['transform_time']:.2f}秒")
    print(f"  融合时间: {performance['fusion_time']:.2f}秒")
    print(f"  逆变换时间: {performance['inverse_transform_time']:.2f}秒")
    print(f"  内存使用: {performance['memory_usage']:.2f}MB")
    print(f"  融合质量: {fusion_quality:.4f}")
    
    # 创建融合分析器
    analyzer = FusionAnalyzer()
    
    # 生成融合报告
    report = analyzer.generate_fusion_report(signals, fused_signal)
    
    # 打印融合报告
    print(f"融合报告:")
    print(f"  总体质量: {report['overall_quality']:.4f}")
    print(f"  指标:")
    for metric, value in report['metrics'].items():
        print(f"    {metric}: {value:.4f}")
    
    return fused_signal, frequency_domain, fusion_quality, report


def fuse_images(images, fusion_method='weighted', window_type='hann', output_dir=None):
    """融合图像"""
    # 创建输出目录
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # 创建融合器
    fusion = FFTFusion(
        fusion_method=fusion_method,
        window_type=window_type,
        overlap_ratio=0.5,
        use_parallel=True,
        num_workers=4,
        use_cache=True,
        cache_size=10
    )
    
    # 执行计算
    print(f"使用{fusion_method}方法融合图像...")
    start_time = time.time()
    result = fusion.compute(images)
    end_time = time.time()
    
    print(f"计算完成，耗时: {end_time - start_time:.2f}秒")
    
    # 提取结果
    fused_image = result['fused_signal']
    frequency_domain = result['frequency_domain']
    fusion_quality = result['fusion_quality']
    performance = result['performance']
    
    # 打印性能指标
    print(f"性能指标:")
    print(f"  总时间: {performance['total_time']:.2f}秒")
    print(f"  变换时间: {performance['transform_time']:.2f}秒")
    print(f"  融合时间: {performance['fusion_time']:.2f}秒")
    print(f"  逆变换时间: {performance['inverse_transform_time']:.2f}秒")
    print(f"  内存使用: {performance['memory_usage']:.2f}MB")
    print(f"  融合质量: {fusion_quality:.4f}")
    
    # 创建融合分析器
    analyzer = FusionAnalyzer()
    
    # 生成融合报告
    report = analyzer.generate_fusion_report(images, fused_image)
    
    # 打印融合报告
    print(f"融合报告:")
    print(f"  总体质量: {report['overall_quality']:.4f}")
    print(f"  指标:")
    for metric, value in report['metrics'].items():
        print(f"    {metric}: {value:.4f}")
    
    return fused_image, frequency_domain, fusion_quality, report


def compare_fusion_methods(signals, t, output_dir=None):
    """比较不同融合方法"""
    # 创建输出目录
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # 融合方法列表
    fusion_methods = ['average', 'weighted', 'max', 'min']
    
    # 存储结果
    fused_signals = {}
    fusion_qualities = {}
    
    # 对每种融合方法执行融合
    for method in fusion_methods:
        print(f"\n===== 使用{method}方法融合 =====")
        fused_signal, _, quality, _ = fuse_signals(signals, fusion_method=method, output_dir=output_dir)
        fused_signals[method] = fused_signal
        fusion_qualities[method] = quality
    
    # 绘制融合结果
    plt.figure(figsize=(12, 8))
    
    # 绘制原始信号
    for i, signal in enumerate(signals):
        plt.plot(t, signal, alpha=0.5, linestyle='--', label=f"原始信号 {i+1}")
    
    # 绘制融合信号
    for method, signal in fused_signals.items():
        plt.plot(t, signal, linewidth=2, label=f"{method}融合 (质量: {fusion_qualities[method]:.4f})")
    
    plt.xlabel('时间')
    plt.ylabel('幅度')
    plt.title('不同融合方法的比较')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图形
    if output_dir:
        plt.savefig(os.path.join(output_dir, 'fusion_methods_comparison.png'))
    
    plt.show()
    
    # 比较融合质量
    plt.figure(figsize=(10, 6))
    
    # 绘制条形图
    plt.bar(fusion_methods, [fusion_qualities[method] for method in fusion_methods], alpha=0.7)
    
    plt.xlabel('融合方法')
    plt.ylabel('融合质量')
    plt.title('不同融合方法的融合质量比较')
    plt.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, method in enumerate(fusion_methods):
        plt.text(i, fusion_qualities[method] + 0.01, f'{fusion_qualities[method]:.4f}', ha='center')
    
    plt.tight_layout()
    
    # 保存图形
    if output_dir:
        plt.savefig(os.path.join(output_dir, 'fusion_quality_comparison.png'))
    
    plt.show()


def compare_fusion_methods_images(images, output_dir=None):
    """比较不同融合方法（图像）"""
    # 创建输出目录
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # 融合方法列表
    fusion_methods = ['average', 'weighted', 'max', 'min']
    
    # 存储结果
    fused_images = {}
    fusion_qualities = {}
    
    # 对每种融合方法执行融合
    for method in fusion_methods:
        print(f"\n===== 使用{method}方法融合 =====")
        fused_image, _, quality, _ = fuse_images(images, fusion_method=method, output_dir=output_dir)
        fused_images[method] = fused_image
        fusion_qualities[method] = quality
    
    # 绘制融合结果
    plt.figure(figsize=(15, 8))
    
    # 绘制原始图像
    for i, image in enumerate(images):
        plt.subplot(2, len(images) + len(fusion_methods), i + 1)
        plt.imshow(image, cmap='gray')
        plt.title(f"原始图像 {i+1}")
        plt.axis('off')
    
    # 绘制融合图像
    for i, (method, image) in enumerate(fused_images.items()):
        plt.subplot(2, len(images) + len(fusion_methods), len(images) + i + 1)
        plt.imshow(image, cmap='gray')
        plt.title(f"{method}融合\n质量: {fusion_qualities[method]:.4f}")
        plt.axis('off')
    
    plt.tight_layout()
    
    # 保存图形
    if output_dir:
        plt.savefig(os.path.join(output_dir, 'fusion_methods_comparison_images.png'))
    
    plt.show()
    
    # 比较融合质量
    plt.figure(figsize=(10, 6))
    
    # 绘制条形图
    plt.bar(fusion_methods, [fusion_qualities[method] for method in fusion_methods], alpha=0.7)
    
    plt.xlabel('融合方法')
    plt.ylabel('融合质量')
    plt.title('不同融合方法的融合质量比较（图像）')
    plt.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, method in enumerate(fusion_methods):
        plt.text(i, fusion_qualities[method] + 0.01, f'{fusion_qualities[method]:.4f}', ha='center')
    
    plt.tight_layout()
    
    # 保存图形
    if output_dir:
        plt.savefig(os.path.join(output_dir, 'fusion_quality_comparison_images.png'))
    
    plt.show()


def main():
    """主函数"""
    # 设置随机种子，确保结果可重现
    np.random.seed(42)
    
    # 创建输出目录
    output_dir = 'output/fft_fusion'
    
    # 创建测试信号
    t, signals = create_test_signals()
    
    # 绘制测试信号
    plot_signals(t, signals, title="测试信号")
    
    # 融合信号
    fused_signal, frequency_domain, fusion_quality, report = fuse_signals(
        signals, fusion_method='weighted', window_type='hann', output_dir=output_dir
    )
    
    # 绘制融合信号
    plot_signals(t, [fused_signal], title=f"融合信号 (质量: {fusion_quality:.4f})")
    
    # 绘制频域表示
    plot_frequency_domain(frequency_domain, title="融合信号的频域表示")
    
    # 比较不同融合方法
    compare_fusion_methods(signals, t, output_dir)
    
    # 创建测试图像
    images = create_test_images()
    
    # 绘制测试图像
    plot_images(images, titles=["水平条纹", "垂直条纹", "对角线条纹"])
    
    # 融合图像
    fused_image, frequency_domain_2d, fusion_quality_image, report_image = fuse_images(
        images, fusion_method='weighted', window_type='hann', output_dir=output_dir
    )
    
    # 绘制融合图像
    plot_images([fused_image], titles=[f"融合图像 (质量: {fusion_quality_image:.4f})"])
    
    # 绘制频域表示
    plot_frequency_domain_2d(frequency_domain_2d, title="融合图像的频域表示")
    
    # 比较不同融合方法（图像）
    compare_fusion_methods_images(images, output_dir)
    
    print("示例完成！")


if __name__ == "__main__":
    main()
