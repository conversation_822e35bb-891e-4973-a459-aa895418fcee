"""
算子注册表示例

本示例展示了如何使用算子注册表。
"""

import os
import sys
import logging
import numpy as np
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入算子注册表模块
try:
    from src.rust_bindings import (
        RUST_AVAILABLE,
        OperatorCategory,
        OperatorRegistry,
        OperatorMetadata,
        DependencyManager,
        CompatibilityLevel,
        CompatibilityChecker,
        OperatorFactory,
        get_global_registry,
        register_operator,
        get_operator,
        list_operators,
        get_operator_metadata,
        check_compatibility,
    )
    logger.info(f"成功导入算子注册表模块，Rust可用: {RUST_AVAILABLE}")
except ImportError as e:
    logger.error(f"导入算子注册表模块失败: {e}")
    sys.exit(1)

# 定义一些示例算子
def add(a, b):
    """加法算子"""
    return a + b

def multiply(a, b):
    """乘法算子"""
    return a * b

def matrix_multiply(a, b):
    """矩阵乘法算子"""
    return np.matmul(a, b)

def convolution(array, kernel):
    """卷积算子"""
    # 检查维度
    if array.ndim != 2 or kernel.ndim != 2:
        raise ValueError("两个数组必须是2维的")

    # 计算输出形状
    output_rows = array.shape[0] - kernel.shape[0] + 1
    output_cols = array.shape[1] - kernel.shape[1] + 1

    # 检查输出形状是否有效
    if output_rows <= 0 or output_cols <= 0:
        raise ValueError("核太大")

    # 创建输出数组
    result = np.zeros((output_rows, output_cols))

    # 执行卷积
    for i in range(output_rows):
        for j in range(output_cols):
            result[i, j] = np.sum(array[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)

    return result

class NeuralNetwork:
    """神经网络"""
    def __init__(self, layers=None):
        """初始化神经网络"""
        self.layers = layers or [10, 5, 1]
        self.weights = []
        self.biases = []

        # 初始化权重和偏置
        for i in range(len(self.layers) - 1):
            self.weights.append(np.random.randn(self.layers[i], self.layers[i+1]))
            self.biases.append(np.random.randn(self.layers[i+1]))

    def forward(self, x):
        """前向传播"""
        for i in range(len(self.weights)):
            x = np.matmul(x, self.weights[i]) + self.biases[i]
            x = 1 / (1 + np.exp(-x))  # sigmoid激活函数

        return x

    def __call__(self, x):
        """调用神经网络"""
        return self.forward(x)

def test_operator_registry():
    """测试算子注册表"""
    logger.info("开始测试算子注册表...")

    # 创建算子注册表
    registry = OperatorRegistry()

    # 注册算子
    registry.register(
        OperatorCategory.UTILITY,
        "add",
        add,
        "1.0.0",
        "加法算子",
        ["math", "basic"],
        [],
    )

    registry.register(
        OperatorCategory.UTILITY,
        "multiply",
        multiply,
        "1.0.0",
        "乘法算子",
        ["math", "basic"],
        [("utility.add", ">=1.0.0")],
    )

    registry.register(
        OperatorCategory.NUMPY,
        "matrix_multiply",
        matrix_multiply,
        "1.0.0",
        "矩阵乘法算子",
        ["math", "matrix"],
        [("utility.multiply", ">=1.0.0")],
    )

    registry.register(
        OperatorCategory.NUMPY,
        "convolution",
        convolution,
        "1.0.0",
        "卷积算子",
        ["math", "matrix", "convolution"],
        [("numpy.matrix_multiply", ">=1.0.0")],
    )

    registry.register(
        OperatorCategory.MULTILEVEL,
        "neural_network",
        NeuralNetwork,
        "1.0.0",
        "神经网络",
        ["neural", "network"],
        [("numpy.matrix_multiply", ">=1.0.0")],
    )

    # 列出所有算子
    operators = registry.list_all()
    logger.info(f"所有算子: {operators}")

    # 列出特定类别的算子
    numpy_operators = registry.list_by_category(OperatorCategory.NUMPY)
    logger.info(f"NumPy算子: {numpy_operators}")

    # 获取算子
    add_op = registry.get(OperatorCategory.UTILITY, "add")
    logger.info(f"加法算子: {add_op}")
    logger.info(f"1 + 2 = {add_op(1, 2)}")

    # 获取算子元数据
    add_metadata = registry.get_metadata(OperatorCategory.UTILITY, "add")
    logger.info(f"加法算子元数据: {add_metadata}")

    # 检查算子是否存在
    exists = registry.exists(OperatorCategory.UTILITY, "add")
    logger.info(f"加法算子是否存在: {exists}")

    # 获取带有标签的算子
    math_operators = registry.get_operators_with_tag("math")
    logger.info(f"数学算子: {math_operators}")

    # 获取带有版本的算子
    v1_operators = registry.get_operators_with_version(">=1.0.0")
    logger.info(f"版本>=1.0.0的算子: {v1_operators}")

    # 获取带有依赖的算子
    dependent_operators = registry.get_operators_with_dependency("utility.add")
    logger.info(f"依赖于add的算子: {dependent_operators}")

    logger.info("算子注册表测试完成")

def test_dependency_manager():
    """测试依赖管理器"""
    logger.info("开始测试依赖管理器...")

    # 创建算子注册表
    registry = OperatorRegistry()

    # 注册算子
    registry.register(
        OperatorCategory.UTILITY,
        "add",
        add,
        "1.0.0",
        "加法算子",
        ["math", "basic"],
        [],
    )

    registry.register(
        OperatorCategory.UTILITY,
        "multiply",
        multiply,
        "1.0.0",
        "乘法算子",
        ["math", "basic"],
        [("utility.add", ">=1.0.0")],
    )

    registry.register(
        OperatorCategory.NUMPY,
        "matrix_multiply",
        matrix_multiply,
        "1.0.0",
        "矩阵乘法算子",
        ["math", "matrix"],
        [("utility.multiply", ">=1.0.0")],
    )

    registry.register(
        OperatorCategory.NUMPY,
        "convolution",
        convolution,
        "1.0.0",
        "卷积算子",
        ["math", "matrix", "convolution"],
        [("numpy.matrix_multiply", ">=1.0.0")],
    )

    # 创建依赖管理器
    dependency_manager = DependencyManager(registry)

    # 检查依赖
    satisfied, missing = dependency_manager.check_dependencies(OperatorCategory.NUMPY, "convolution")
    logger.info(f"卷积算子的依赖是否满足: {satisfied}, 缺失: {missing}")

    # 获取依赖于算子的所有算子
    dependents = dependency_manager.get_dependents(OperatorCategory.UTILITY, "add")
    logger.info(f"依赖于add的算子: {dependents}")

    # 构建依赖图
    graph = dependency_manager.build_dependency_graph()
    logger.info(f"依赖图: {graph}")

    # 检查循环依赖
    cycles = dependency_manager.check_circular_dependencies()
    logger.info(f"循环依赖: {cycles}")

    # 解析依赖
    dependencies = dependency_manager.resolve_dependencies(OperatorCategory.NUMPY, "convolution")
    logger.info(f"卷积算子的依赖顺序: {dependencies}")

    logger.info("依赖管理器测试完成")

def test_compatibility_checker():
    """测试兼容性检查器"""
    logger.info("开始测试兼容性检查器...")

    # 创建算子注册表
    registry = OperatorRegistry()

    # 注册算子
    registry.register(
        OperatorCategory.UTILITY,
        "add",
        add,
        "1.0.0",
        "加法算子",
        ["math", "basic"],
        [],
    )

    # 创建兼容性检查器
    checker = CompatibilityChecker(registry)

    # 添加兼容性规则
    checker.add_rule(OperatorCategory.UTILITY, "add", ">=0.9.0", CompatibilityLevel.PARTIAL)
    checker.add_rule(OperatorCategory.UTILITY, "add", "<0.9.0", CompatibilityLevel.NONE)

    # 检查兼容性
    level = checker.check_compatibility(OperatorCategory.UTILITY, "add", ">=1.0.0")
    logger.info(f"加法算子与>=1.0.0的兼容性: {level}")

    level = checker.check_compatibility(OperatorCategory.UTILITY, "add", ">=0.9.0")
    logger.info(f"加法算子与>=0.9.0的兼容性: {level}")

    level = checker.check_compatibility(OperatorCategory.UTILITY, "add", "<0.9.0")
    logger.info(f"加法算子与<0.9.0的兼容性: {level}")

    # 获取所有兼容性规则
    rules = checker.get_rules()
    logger.info(f"所有兼容性规则: {rules}")

    # 获取算子的兼容性规则
    rules = checker.get_rules_for_operator(OperatorCategory.UTILITY, "add")
    logger.info(f"加法算子的兼容性规则: {rules}")

    logger.info("兼容性检查器测试完成")

def test_operator_factory():
    """测试算子工厂"""
    logger.info("开始测试算子工厂...")

    # 创建算子注册表
    registry = OperatorRegistry()

    # 注册算子
    registry.register(
        OperatorCategory.UTILITY,
        "add",
        add,
        "1.0.0",
        "加法算子",
        ["math", "basic"],
        [],
    )

    registry.register(
        OperatorCategory.UTILITY,
        "multiply",
        multiply,
        "1.0.0",
        "乘法算子",
        ["math", "basic"],
        [("utility.add", ">=1.0.0")],
    )

    registry.register(
        OperatorCategory.NUMPY,
        "matrix_multiply",
        matrix_multiply,
        "1.0.0",
        "矩阵乘法算子",
        ["math", "matrix"],
        [("utility.multiply", ">=1.0.0")],
    )

    registry.register(
        OperatorCategory.MULTILEVEL,
        "neural_network",
        NeuralNetwork,
        "1.0.0",
        "神经网络",
        ["neural", "network"],
        [("numpy.matrix_multiply", ">=1.0.0")],
    )

    # 创建算子工厂
    factory = OperatorFactory(registry)

    # 注册构造函数
    factory.register_constructor(OperatorCategory.UTILITY, "custom_add", lambda a, b: a + b + 1)

    # 创建算子
    add_op = factory.create_operator(OperatorCategory.UTILITY, "add", 1, 2)
    logger.info(f"1 + 2 = {add_op}")

    custom_add_op = factory.create_operator(OperatorCategory.UTILITY, "custom_add", 1, 2)
    logger.info(f"1 + 2 + 1 = {custom_add_op}")

    # 创建带有依赖的算子
    nn = factory.create_operator_with_dependencies(OperatorCategory.MULTILEVEL, "neural_network", [10, 5, 1])
    logger.info(f"神经网络: {nn}")

    # 使用神经网络
    input_data = np.random.rand(1, 10)
    output = nn(input_data)
    logger.info(f"神经网络输出: {output}")

    # 获取所有构造函数
    constructors = factory.get_constructors()
    logger.info(f"所有构造函数: {constructors}")

    logger.info("算子工厂测试完成")

def test_global_registry():
    """测试全局注册表"""
    logger.info("开始测试全局注册表...")

    # 获取全局注册表
    registry = get_global_registry()

    # 注册算子
    register_operator(
        OperatorCategory.UTILITY,
        "add",
        add,
        "1.0.0",
        "加法算子",
        ["math", "basic"],
        [],
    )

    register_operator(
        OperatorCategory.UTILITY,
        "multiply",
        multiply,
        "1.0.0",
        "乘法算子",
        ["math", "basic"],
        [("utility.add", ">=1.0.0")],
    )

    # 列出所有算子
    operators = list_operators()
    logger.info(f"所有算子: {operators}")

    # 获取算子
    add_op = get_operator(OperatorCategory.UTILITY, "add")
    logger.info(f"加法算子: {add_op}")
    logger.info(f"1 + 2 = {add_op(1, 2)}")

    # 获取算子元数据
    add_metadata = get_operator_metadata(OperatorCategory.UTILITY, "add")
    logger.info(f"加法算子元数据: {add_metadata}")

    # 检查兼容性
    compatible = check_compatibility(OperatorCategory.UTILITY, "add", ">=1.0.0")
    logger.info(f"加法算子与>=1.0.0的兼容性: {compatible}")

    logger.info("全局注册表测试完成")

def main():
    """主函数"""
    logger.info(f"开始算子注册表示例，Rust可用: {RUST_AVAILABLE}")

    # 测试算子注册表
    test_operator_registry()

    # 测试依赖管理器
    test_dependency_manager()

    # 测试兼容性检查器
    test_compatibility_checker()

    # 测试算子工厂
    test_operator_factory()

    # 测试全局注册表
    test_global_registry()

    logger.info("算子注册表示例结束")

if __name__ == "__main__":
    main()
