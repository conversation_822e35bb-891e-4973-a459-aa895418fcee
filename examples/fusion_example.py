"""
超越态融合算子示例

本示例展示了如何使用超越态融合算子进行状态融合，并可视化融合结果。
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# 导入融合算子
try:
    from tests.test_fusion_standalone import TranscendentalFusionOperator, FusionMethod
except ImportError as e:
    print(f"导入融合算子失败: {e}")
    sys.exit(1)

def create_quantum_state(n, type="basis"):
    """
    创建量子态
    
    参数:
        n: 状态维度
        type: 状态类型，可选值：basis, uniform, random
        
    返回:
        量子态，复数数组
    """
    if type == "basis":
        # 创建基态 |0⟩, |1⟩, ...
        state = np.zeros(n, dtype=np.complex128)
        state[0] = 1.0
        return state
    elif type == "uniform":
        # 创建均匀叠加态 (|0⟩ + |1⟩ + ... + |n-1⟩) / sqrt(n)
        state = np.ones(n, dtype=np.complex128) / np.sqrt(n)
        return state
    elif type == "random":
        # 创建随机态
        state = np.random.normal(0, 1, n) + 1j * np.random.normal(0, 1, n)
        # 归一化
        state = state / np.sqrt(np.sum(np.abs(state)**2))
        return state
    else:
        raise ValueError(f"未知的状态类型: {type}")

def visualize_state(state, title="量子态"):
    """
    可视化量子态
    
    参数:
        state: 量子态，复数数组
        title: 图表标题
    """
    n = len(state)
    
    # 计算概率
    probs = np.abs(state)**2
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # 绘制概率分布
    ax1.bar(range(n), probs)
    ax1.set_xlabel("基态")
    ax1.set_ylabel("概率")
    ax1.set_title(f"{title} - 概率分布")
    
    # 绘制相位分布
    phases = np.angle(state)
    ax2.bar(range(n), phases)
    ax2.set_xlabel("基态")
    ax2.set_ylabel("相位")
    ax2.set_title(f"{title} - 相位分布")
    
    plt.tight_layout()
    return fig

def fusion_example():
    """超越态融合示例"""
    # 创建融合算子
    fusion_op = TranscendentalFusionOperator()
    
    # 创建两个测试状态
    n = 8  # 状态维度
    state_a = create_quantum_state(n, "basis")
    state_b = create_quantum_state(n, "uniform")
    
    print("状态A:", state_a)
    print("状态B:", state_b)
    
    # 可视化原始状态
    fig_a = visualize_state(state_a, "状态A")
    fig_b = visualize_state(state_b, "状态B")
    
    # 使用不同的融合方法
    methods = {
        "量子叠加": FusionMethod.QUANTUM_SUPERPOSITION,
        "全息干涉": FusionMethod.HOLOGRAPHIC_INTERFERENCE,
        "分形融合": FusionMethod.FRACTAL_FUSION,
        "拓扑融合": FusionMethod.TOPOLOGICAL_FUSION,
    }
    
    results = {}
    figures = {}
    
    for name, method in methods.items():
        # 执行融合
        result = fusion_op.fuse(state_a, state_b, method=method)
        results[name] = result
        
        print(f"\n方法: {name}")
        print("融合结果:", result)
        print("结果概率:", np.abs(result)**2)
        
        # 验证结果是否为单位向量
        norm = np.sqrt(np.sum(np.abs(result)**2))
        print("结果范数:", norm)
        
        # 可视化融合结果
        figures[name] = visualize_state(result, f"融合结果 ({name})")
    
    # 显示所有图表
    plt.show()
    
    return results, figures

if __name__ == "__main__":
    print("超越态融合示例")
    fusion_example()
