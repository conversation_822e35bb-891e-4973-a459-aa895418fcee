#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GIL装饰器示例程序

演示如何使用GIL装饰器自动优化超越态计算框架中的计算任务
"""

import os
import sys
import time
import numpy as np
import threading
import logging
from typing import List, Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入GIL装饰器
try:
    from src.core.parallel.gil_decorator import (
        auto_gil, compute_intensive, parallel_task, io_intensive, mixed_task
    )
    from src.core.parallel.gil_manager import get_gil_status
except ImportError as e:
    logger.warning(f"无法导入GIL装饰器模块: {e}")
    
    # 创建兼容性装饰器
    def auto_gil(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    
    compute_intensive = auto_gil
    parallel_task = auto_gil
    io_intensive = auto_gil
    mixed_task = auto_gil
    
    def get_gil_status():
        return {
            'has_nogil_support': False,
            'is_nogil_build': False,
            'is_nogil_enabled': False,
            'has_np_gil_state': False
        }


class SuperpositionCalculator:
    """超越态叠加计算器"""
    
    @compute_intensive(log_performance=True)
    def calculate_superposition(self, size: int = 1000, iterations: int = 5) -> np.ndarray:
        """计算量子态叠加"""
        logger.info(f"计算量子态叠加 (大小: {size}, 迭代: {iterations})")
        
        result = None
        for i in range(iterations):
            # 创建随机量子态
            state1 = np.random.random(size) + 1j * np.random.random(size)
            state1 = state1 / np.linalg.norm(state1)
            
            state2 = np.random.random(size) + 1j * np.random.random(size)
            state2 = state2 / np.linalg.norm(state2)
            
            # 计算叠加态
            alpha = np.random.random()
            beta = np.sqrt(1 - alpha**2)
            
            superposition = alpha * state1 + beta * state2
            superposition = superposition / np.linalg.norm(superposition)
            
            # 应用随机酉变换
            h = np.random.random((size, size)) + 1j * np.random.random((size, size))
            u = np.eye(size) + 1j * h
            u = u / np.linalg.norm(u, ord=2)
            
            result = np.dot(u, superposition)
        
        return result
    
    @parallel_task(log_performance=True)
    def parallel_entanglement_simulation(self, size: int = 100, num_threads: int = 4) -> List[np.ndarray]:
        """并行纠缠模拟"""
        logger.info(f"并行纠缠模拟 (大小: {size}, 线程数: {num_threads})")
        
        # 创建纠缠态
        bell_states = []
        for _ in range(size):
            # 创建Bell态
            state = np.zeros(4, dtype=complex)
            state[0] = 1/np.sqrt(2)  # |00>
            state[3] = 1/np.sqrt(2)  # |11>
            bell_states.append(state)
        
        # 分割任务
        chunks = np.array_split(bell_states, num_threads)
        results = [None] * num_threads
        
        def process_chunk(chunk, chunk_id):
            processed = []
            for state in chunk:
                # 应用随机局部操作
                u1 = np.random.random((2, 2)) + 1j * np.random.random((2, 2))
                u1 = u1 / np.linalg.norm(u1, ord=2)
                
                u2 = np.random.random((2, 2)) + 1j * np.random.random((2, 2))
                u2 = u2 / np.linalg.norm(u2, ord=2)
                
                # 计算张量积 U1 ⊗ U2
                u_combined = np.kron(u1, u2)
                
                # 应用到Bell态
                new_state = np.dot(u_combined, state)
                processed.append(new_state)
            
            results[chunk_id] = processed
        
        # 启动线程
        threads = []
        for i, chunk in enumerate(chunks):
            t = threading.Thread(target=process_chunk, args=(chunk, i))
            threads.append(t)
            t.start()
        
        # 等待所有线程完成
        for t in threads:
            t.join()
        
        # 合并结果
        all_results = []
        for r in results:
            if r is not None:
                all_results.extend(r)
        
        return all_results
    
    @auto_gil(task_type='compute_intensive', adaptive=True, log_performance=True)
    def adaptive_holographic_transform(self, size: int = 100, iterations: int = 3) -> np.ndarray:
        """自适应全息变换"""
        logger.info(f"自适应全息变换 (大小: {size}, 迭代: {iterations})")
        
        # 创建随机全息数据
        hologram = np.random.random((size, size)) * np.exp(1j * np.random.random((size, size)) * 2 * np.pi)
        
        for _ in range(iterations):
            # 应用傅里叶变换
            transformed = np.fft.fft2(hologram)
            transformed = np.fft.fftshift(transformed)
            
            # 应用非线性变换
            phase = np.angle(transformed)
            amplitude = np.abs(transformed)
            
            # 非线性调制
            modulated_amplitude = np.tanh(amplitude / np.mean(amplitude))
            
            # 重构全息图
            hologram = modulated_amplitude * np.exp(1j * phase)
            
            # 应用逆变换
            hologram = np.fft.ifftshift(hologram)
            hologram = np.fft.ifft2(hologram)
        
        return hologram


def run_benchmark(iterations: int = 3):
    """运行基准测试"""
    # 打印系统信息
    print(f"Python版本: {sys.version}")
    print(f"NumPy版本: {np.__version__}")
    print(f"CPU核心数: {os.cpu_count()}")
    print(f"GIL状态: {get_gil_status()}")
    
    # 创建计算器实例
    calculator = SuperpositionCalculator()
    
    # 测试计算量子态叠加
    print("\n=== 测试计算量子态叠加 ===")
    sizes = [500, 1000, 2000]
    for size in sizes:
        start_time = time.time()
        calculator.calculate_superposition(size, iterations)
        elapsed = time.time() - start_time
        print(f"大小: {size}, 耗时: {elapsed:.6f} 秒")
        
        # 获取性能数据
        perf_data = calculator.calculate_superposition.get_performance_data()
        print(f"性能数据: {perf_data}")
    
    # 测试并行纠缠模拟
    print("\n=== 测试并行纠缠模拟 ===")
    thread_counts = [2, 4, 8]
    for threads in thread_counts:
        start_time = time.time()
        calculator.parallel_entanglement_simulation(200, threads)
        elapsed = time.time() - start_time
        print(f"线程数: {threads}, 耗时: {elapsed:.6f} 秒")
        
        # 获取性能数据
        perf_data = calculator.parallel_entanglement_simulation.get_performance_data()
        print(f"性能数据: {perf_data}")
    
    # 测试自适应全息变换
    print("\n=== 测试自适应全息变换 ===")
    sizes = [50, 100, 200]
    for size in sizes:
        start_time = time.time()
        calculator.adaptive_holographic_transform(size, iterations)
        elapsed = time.time() - start_time
        print(f"大小: {size}, 耗时: {elapsed:.6f} 秒")
        
        # 获取性能数据
        perf_data = calculator.adaptive_holographic_transform.get_performance_data()
        print(f"性能数据: {perf_data}")


if __name__ == "__main__":
    run_benchmark()
