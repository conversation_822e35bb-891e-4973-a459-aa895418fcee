"""
元认知映射示例

本示例展示了如何使用元认知映射模块。
"""

import sys
import os
import time
import logging
import numpy as np
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入元认知映射模块
from src.transcendental_tensor.self_reflective_category.dynamic_morphism import (
    DynamicMorphism, DynamicMorphismConfig, OptimizationStrategy,
    SystemState, Environment,
    CognitiveLevel, LearningStrategy, AdaptationMode,
    CognitiveState, MappingResult, MappingConfig,
    CognitiveMapping, MetacognitiveMapping,
    FeatureBasedMetacognitiveMapping, LearningMetacognitiveMapping,
    AdaptiveMetacognitiveMapping, CompositeMetacognitiveMapping,
    MetacognitiveMappingOperator
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


# 示例系统状态
class ExampleSystemState(SystemState):
    """示例系统状态"""
    
    def __init__(self, complexity: float = 0.5, uncertainty: float = 0.5):
        """初始化示例系统状态"""
        super().__init__()
        self.complexity = complexity
        self.uncertainty = uncertainty
        self.metrics = {
            "complexity": complexity,
            "uncertainty": uncertainty,
            "coherence": 1.0 - uncertainty,
            "novelty": np.random.random() * 0.5
        }
        self.parameters = {
            "learning_rate": 0.01,
            "regularization": 0.001
        }
        self.representations = {
            "state_type": "example",
            "state_quality": "good" if complexity < 0.7 and uncertainty < 0.7 else "challenging"
        }
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"ExampleSystemState(complexity={self.complexity:.2f}, uncertainty={self.uncertainty:.2f})"


# 示例1：基本元认知映射
def example_basic_mapping():
    """基本元认知映射示例"""
    logger.info("示例1：基本元认知映射")
    
    # 创建系统状态
    system_state = ExampleSystemState(0.6, 0.4)
    logger.info(f"系统状态: {system_state}")
    
    # 创建元认知映射
    mapping = MetacognitiveMapping(
        name="BasicMapping",
        description="Basic metacognitive mapping",
        config=MappingConfig(
            source_level=CognitiveLevel.PERCEPTUAL,
            target_level=CognitiveLevel.METACOGNITIVE
        )
    )
    
    # 应用映射
    cognitive_state, result = mapping.map(system_state)
    
    logger.info(f"映射结果: 成功={result.success}")
    logger.info(f"认知状态级别: {cognitive_state.level.value}")
    logger.info(f"认知状态特征: {cognitive_state.features}")
    logger.info(f"认知状态表示: {cognitive_state.representations}")


# 示例2：特征基础元认知映射
def example_feature_based_mapping():
    """特征基础元认知映射示例"""
    logger.info("示例2：特征基础元认知映射")
    
    # 创建系统状态
    system_state = ExampleSystemState(0.8, 0.3)
    logger.info(f"系统状态: {system_state}")
    
    # 创建特征基础元认知映射
    mapping = FeatureBasedMetacognitiveMapping(
        name="FeatureMapping",
        description="Feature-based metacognitive mapping",
        config=MappingConfig(
            source_level=CognitiveLevel.PERCEPTUAL,
            target_level=CognitiveLevel.METACOGNITIVE
        )
    )
    
    # 应用映射
    cognitive_state, result = mapping.map(system_state)
    
    logger.info(f"映射结果: 成功={result.success}")
    logger.info(f"认知状态级别: {cognitive_state.level.value}")
    logger.info(f"认知状态特征: {cognitive_state.features}")
    logger.info(f"认知状态表示: {cognitive_state.representations}")
    
    # 提取特征
    logger.info(f"复杂度特征: {cognitive_state.features.get('extracted_complexity')}")
    logger.info(f"不确定性特征: {cognitive_state.features.get('extracted_uncertainty')}")
    logger.info(f"一致性特征: {cognitive_state.features.get('extracted_coherence')}")
    logger.info(f"新颖性特征: {cognitive_state.features.get('extracted_novelty')}")


# 示例3：学习型元认知映射
def example_learning_mapping():
    """学习型元认知映射示例"""
    logger.info("示例3：学习型元认知映射")
    
    # 创建学习型元认知映射
    mapping = LearningMetacognitiveMapping(
        name="LearningMapping",
        description="Learning metacognitive mapping",
        config=MappingConfig(
            source_level=CognitiveLevel.PERCEPTUAL,
            target_level=CognitiveLevel.METACOGNITIVE,
            learning_strategy=LearningStrategy.SUPERVISED,
            learning_rate=0.1
        )
    )
    
    # 训练映射
    logger.info("训练映射...")
    
    for i in range(10):
        # 创建系统状态
        complexity = np.random.random()
        uncertainty = np.random.random()
        system_state = ExampleSystemState(complexity, uncertainty)
        
        # 创建期望的元认知状态
        expected_features = {
            "metacognitive_feature_0": complexity,
            "metacognitive_feature_1": uncertainty,
            "metacognitive_feature_2": 1.0 - uncertainty,
            "metacognitive_feature_3": complexity * uncertainty,
            "mean": (complexity + uncertainty) / 2,
            "std": abs(complexity - uncertainty) / 2
        }
        
        expected_state = CognitiveState(
            level=CognitiveLevel.METACOGNITIVE,
            features=expected_features
        )
        
        # 更新映射
        success = mapping.update(system_state, expected_state)
        
        if i % 3 == 0:
            logger.info(f"训练步骤 {i+1}: 成功={success}")
    
    # 测试映射
    test_state = ExampleSystemState(0.7, 0.2)
    logger.info(f"测试状态: {test_state}")
    
    cognitive_state, result = mapping.map(test_state)
    
    logger.info(f"映射结果: 成功={result.success}")
    logger.info(f"认知状态级别: {cognitive_state.level.value}")
    logger.info(f"认知状态特征: {cognitive_state.features}")
    
    # 获取学习统计
    learning_stats = mapping.get_learning_stats()
    logger.info(f"学习统计: {learning_stats}")


# 示例4：自适应元认知映射
def example_adaptive_mapping():
    """自适应元认知映射示例"""
    logger.info("示例4：自适应元认知映射")
    
    # 创建自适应元认知映射
    mapping = AdaptiveMetacognitiveMapping(
        name="AdaptiveMapping",
        description="Adaptive metacognitive mapping",
        config=MappingConfig(
            source_level=CognitiveLevel.PERCEPTUAL,
            target_level=CognitiveLevel.METACOGNITIVE,
            adaptation_mode=AdaptationMode.DYNAMIC
        )
    )
    
    # 测试不同类型的输入
    
    # 高复杂度，低不确定性
    state1 = ExampleSystemState(0.9, 0.1)
    logger.info(f"状态1: {state1}")
    
    cognitive_state1, result1 = mapping.map(state1)
    
    logger.info(f"映射结果1: 成功={result1.success}")
    logger.info(f"使用的策略: {cognitive_state1.metadata.get('mapping_strategy')}")
    logger.info(f"策略置信度: {cognitive_state1.metadata.get('mapping_confidence')}")
    
    # 低复杂度，高不确定性
    state2 = ExampleSystemState(0.1, 0.9)
    logger.info(f"状态2: {state2}")
    
    cognitive_state2, result2 = mapping.map(state2)
    
    logger.info(f"映射结果2: 成功={result2.success}")
    logger.info(f"使用的策略: {cognitive_state2.metadata.get('mapping_strategy')}")
    logger.info(f"策略置信度: {cognitive_state2.metadata.get('mapping_confidence')}")
    
    # 高复杂度，高不确定性
    state3 = ExampleSystemState(0.9, 0.9)
    logger.info(f"状态3: {state3}")
    
    cognitive_state3, result3 = mapping.map(state3)
    
    logger.info(f"映射结果3: 成功={result3.success}")
    logger.info(f"使用的策略: {cognitive_state3.metadata.get('mapping_strategy')}")
    logger.info(f"策略置信度: {cognitive_state3.metadata.get('mapping_confidence')}")


# 示例5：元认知映射算子
def example_mapping_operator():
    """元认知映射算子示例"""
    logger.info("示例5：元认知映射算子")
    
    # 创建元认知映射算子
    operator = MetacognitiveMappingOperator(
        name="MappingOperator",
        config=MappingConfig(
            source_level=CognitiveLevel.PERCEPTUAL,
            target_level=CognitiveLevel.METACOGNITIVE,
            learning_strategy=LearningStrategy.SELF_SUPERVISED
        )
    )
    
    # 创建特征映射
    operator.create_feature_mapping("custom_feature_mapping")
    
    # 创建学习映射
    operator.create_learning_mapping("custom_learning_mapping", hidden_size=30)
    
    # 创建自适应映射
    operator.create_adaptive_mapping("custom_adaptive_mapping")
    
    # 创建组合映射
    operator.create_composite_mapping(
        "custom_composite_mapping",
        ["custom_feature_mapping", "custom_learning_mapping"]
    )
    
    # 测试不同的映射
    state = ExampleSystemState(0.6, 0.4)
    logger.info(f"测试状态: {state}")
    
    # 使用默认映射
    result1 = operator.apply(state)
    logger.info(f"默认映射结果: {result1}")
    
    # 使用特征映射
    result2 = operator.apply(state, mapping_name="custom_feature_mapping")
    logger.info(f"特征映射结果: {result2}")
    
    # 使用学习映射
    result3 = operator.apply(state, mapping_name="custom_learning_mapping")
    logger.info(f"学习映射结果: {result3}")
    
    # 使用自适应映射
    result4 = operator.apply(state, mapping_name="custom_adaptive_mapping")
    logger.info(f"自适应映射结果: {result4}")
    
    # 使用组合映射
    result5 = operator.apply(state, mapping_name="custom_composite_mapping")
    logger.info(f"组合映射结果: {result5}")
    
    # 获取元数据
    metadata = operator.get_metadata()
    logger.info(f"算子元数据: {metadata}")


def main():
    """主函数"""
    logger.info("元认知映射示例")
    
    # 运行示例
    example_basic_mapping()
    print("\n" + "-" * 80 + "\n")
    
    example_feature_based_mapping()
    print("\n" + "-" * 80 + "\n")
    
    example_learning_mapping()
    print("\n" + "-" * 80 + "\n")
    
    example_adaptive_mapping()
    print("\n" + "-" * 80 + "\n")
    
    example_mapping_operator()
    
    logger.info("示例完成")


if __name__ == "__main__":
    main()
