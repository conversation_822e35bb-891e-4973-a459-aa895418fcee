"""
Rust简单示例

本示例展示了如何使用Rust实现的简单函数和类。
"""

import os
import sys
import logging

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 尝试导入Rust模块
try:
    import rust_operators
    RUST_AVAILABLE = True
    logger.info("成功导入Rust模块")
except ImportError as e:
    RUST_AVAILABLE = False
    logger.error(f"导入Rust模块失败: {e}")

def main():
    """主函数"""
    logger.info(f"开始Rust简单示例，Rust可用: {RUST_AVAILABLE}")
    
    if not RUST_AVAILABLE:
        logger.error("Rust模块不可用，无法继续")
        return
    
    # 测试add函数
    a = 5
    b = 7
    result = rust_operators.add(a, b)
    logger.info(f"{a} + {b} = {result}")
    
    # 测试multiply函数
    a = 5
    b = 7
    result = rust_operators.multiply(a, b)
    logger.info(f"{a} * {b} = {result}")
    
    # 测试Point类
    point = rust_operators.Point(3.0, 4.0)
    logger.info(f"点: {point}")
    logger.info(f"x坐标: {point.x}")
    logger.info(f"y坐标: {point.y}")
    logger.info(f"到原点的距离: {point.distance_from_origin()}")
    
    # 显示模块信息
    logger.info(f"版本: {rust_operators.__version__}")
    logger.info(f"作者: {rust_operators.__author__}")
    
    logger.info("Rust简单示例结束")

if __name__ == "__main__":
    main()
