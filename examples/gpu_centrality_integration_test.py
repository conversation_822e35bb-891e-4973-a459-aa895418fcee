#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超越态思维引擎 - GPU加速中心节点识别算子与模拟GPU加速器集成测试
"""

import os
import sys
import time
import random
import numpy as np
import logging
import matplotlib.pyplot as plt
from typing import Dict, Any, List, Tuple, Optional, Union

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入模拟GPU加速器适配器
from src.operators.gpu_acceleration.mock_gpu_adapter import create_mock_gpu_adapter, MOCK_GPU_AVAILABLE

# 导入算子注册表
from src.operators import operator_registry

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GPUCentralityIntegrationTest:
    """GPU加速中心节点识别算子与模拟GPU加速器集成测试"""
    
    def __init__(self):
        """初始化测试环境"""
        # 检查模拟GPU加速器是否可用
        if not MOCK_GPU_AVAILABLE:
            raise ImportError("模拟GPU加速器不可用")
        
        # 创建模拟GPU加速器适配器
        self.gpu_adapter = create_mock_gpu_adapter()
        if self.gpu_adapter is None:
            raise ImportError("创建模拟GPU加速器适配器失败")
        
        logger.info("模拟GPU加速器适配器初始化完成")
        
        # 获取中心性算子
        self.centrality_class = operator_registry.get_operator("gpu_acceleration", "centrality")
        if self.centrality_class is None:
            raise ImportError("GPU加速中心性算子不可用，请确保已编译Rust绑定")
        
        logger.info("GPU加速中心性算子加载完成")
    
    def generate_scale_free_network(self, n_nodes, m=2, weighted=True):
        """
        生成无标度网络（Barabási-Albert模型）
        
        Args:
            n_nodes: 节点数量
            m: 每个新节点连接到现有节点的边数
            weighted: 是否使用权重
            
        Returns:
            Dict[str, Dict[str, float]]: 网络结构
        """
        # 初始化网络
        nodes = {}
        
        # 创建初始完全图
        initial_nodes = min(m + 1, n_nodes)
        for i in range(initial_nodes):
            node_id = f"node_{i}"
            connections = {}
            
            for j in range(initial_nodes):
                if i != j:
                    if weighted:
                        weight = random.uniform(0.1, 1.0)
                    else:
                        weight = 1.0
                    
                    connections[f"node_{j}"] = weight
            
            nodes[node_id] = connections
        
        # 添加剩余节点
        for i in range(initial_nodes, n_nodes):
            node_id = f"node_{i}"
            connections = {}
            
            # 计算现有节点的度
            degrees = {}
            for existing_id, existing_connections in nodes.items():
                degrees[existing_id] = len(existing_connections)
            
            # 计算总度数
            total_degree = sum(degrees.values())
            
            # 根据优先连接选择m个节点
            selected_nodes = []
            for _ in range(min(m, len(nodes))):
                # 计算连接概率
                probabilities = {node_id: degree / total_degree for node_id, degree in degrees.items() if node_id not in selected_nodes}
                
                # 如果没有可选节点，跳出循环
                if not probabilities:
                    break
                
                # 选择节点
                selected_id = random.choices(list(probabilities.keys()), weights=list(probabilities.values()), k=1)[0]
                selected_nodes.append(selected_id)
                
                # 添加连接
                if weighted:
                    weight = random.uniform(0.1, 1.0)
                else:
                    weight = 1.0
                
                connections[selected_id] = weight
                
                # 更新被选中节点的连接
                nodes[selected_id][node_id] = weight
            
            # 添加新节点
            nodes[node_id] = connections
        
        return nodes
    
    def test_centrality_with_mock_gpu(self, nodes, centrality_type):
        """
        使用模拟GPU加速器测试中心性算子
        
        Args:
            nodes: 网络结构
            centrality_type: 中心性类型
            
        Returns:
            Dict[str, Any]: 测试结果
        """
        # 创建配置
        config = self.centrality_class.PyCentralityConfig(
            centrality_type=centrality_type,
            use_edge_weights=True,
            normalize=True,
            max_iterations=100,
            convergence_threshold=1e-6,
            damping_factor=0.85,
            use_multithreading=True,
            compute_device="cuda",
            use_simd=True,
            use_sparse_matrix=True,
            batch_size=64
        )
        
        # 创建中心性算子实例
        centrality_operator = self.centrality_class.PyCentralityOperator(config)
        
        # 计算中心性
        start_time = time.time()
        result = centrality_operator.calculate_centrality(nodes)
        elapsed_time = time.time() - start_time
        
        # 获取中心性值
        centrality_values = result.get_centrality_values()
        
        # 获取排序后的节点
        sorted_nodes = result.get_sorted_nodes()
        
        return {
            "elapsed_time": elapsed_time,
            "centrality_values": centrality_values,
            "sorted_nodes": sorted_nodes,
            "result": result
        }
    
    def test_centrality_with_adapter(self, nodes, centrality_type):
        """
        使用模拟GPU加速器适配器测试中心性
        
        Args:
            nodes: 网络结构
            centrality_type: 中心性类型
            
        Returns:
            Dict[str, Any]: 测试结果
        """
        # 将网络结构转换为邻接矩阵
        node_ids = list(nodes.keys())
        n = len(node_ids)
        adjacency_matrix = np.zeros((n, n))
        
        for i, source_id in enumerate(node_ids):
            for j, target_id in enumerate(node_ids):
                if target_id in nodes[source_id]:
                    adjacency_matrix[i, j] = nodes[source_id][target_id]
        
        # 根据中心性类型选择操作
        if centrality_type == "degree":
            operation = "degree_centrality"
        elif centrality_type == "closeness":
            operation = "closeness_centrality"
        elif centrality_type == "betweenness":
            operation = "betweenness_centrality"
        elif centrality_type == "eigenvector":
            operation = "eigenvector_centrality"
        elif centrality_type == "pagerank":
            operation = "pagerank_centrality"
        elif centrality_type == "katz":
            # Katz中心性暂时使用PageRank实现
            operation = "pagerank_centrality"
        else:
            raise ValueError(f"不支持的中心性类型: {centrality_type}")
        
        # 使用模拟GPU加速器适配器计算中心性
        result, elapsed_time = self.gpu_adapter.graph_operation(
            adjacency_matrix,
            operation,
            normalize=True,
            max_iterations=100,
            tolerance=1e-6,
            damping_factor=0.85
        )
        
        # 构建中心性值字典
        centrality_values = {node_ids[i]: result[i] for i in range(n)}
        
        # 构建排序后的节点
        sorted_items = sorted(centrality_values.items(), key=lambda x: x[1], reverse=True)
        sorted_nodes = [{"node_id": node_id, "value": value} for node_id, value in sorted_items]
        
        return {
            "elapsed_time": elapsed_time,
            "centrality_values": centrality_values,
            "sorted_nodes": sorted_nodes
        }
    
    def run_integration_test(self):
        """运行集成测试"""
        centrality_types = ["degree", "closeness", "betweenness", "eigenvector", "pagerank", "katz"]
        node_counts = [10, 20, 50, 100]
        
        results = {
            "operator": {ct: [] for ct in centrality_types},
            "adapter": {ct: [] for ct in centrality_types}
        }
        
        for n_nodes in node_counts:
            logger.info(f"测试节点数量: {n_nodes}")
            
            # 生成无标度网络
            nodes = self.generate_scale_free_network(n_nodes, m=2)
            
            for centrality_type in centrality_types:
                logger.info(f"  测试中心性类型: {centrality_type}")
                
                # 使用GPU加速中心性算子测试
                logger.info("    使用GPU加速中心性算子测试...")
                operator_result = self.test_centrality_with_mock_gpu(nodes, centrality_type)
                results["operator"][centrality_type].append({
                    "n_nodes": n_nodes,
                    "elapsed_time": operator_result["elapsed_time"]
                })
                logger.info(f"    GPU加速中心性算子耗时: {operator_result['elapsed_time']:.4f}秒")
                
                # 使用模拟GPU加速器适配器测试
                logger.info("    使用模拟GPU加速器适配器测试...")
                adapter_result = self.test_centrality_with_adapter(nodes, centrality_type)
                results["adapter"][centrality_type].append({
                    "n_nodes": n_nodes,
                    "elapsed_time": adapter_result["elapsed_time"]
                })
                logger.info(f"    模拟GPU加速器适配器耗时: {adapter_result['elapsed_time']:.4f}秒")
                
                # 比较结果
                logger.info("    比较结果...")
                self.compare_results(operator_result, adapter_result)
                
                logger.info("")
        
        return results
    
    def compare_results(self, operator_result, adapter_result):
        """
        比较两种方法的结果
        
        Args:
            operator_result: GPU加速中心性算子结果
            adapter_result: 模拟GPU加速器适配器结果
        """
        # 获取中心性值
        operator_values = operator_result["centrality_values"]
        adapter_values = adapter_result["centrality_values"]
        
        # 计算相关系数
        operator_values_list = []
        adapter_values_list = []
        
        for node_id in operator_values.keys():
            operator_values_list.append(operator_values[node_id])
            adapter_values_list.append(adapter_values.get(node_id, 0.0))
        
        correlation = np.corrcoef(operator_values_list, adapter_values_list)[0, 1]
        logger.info(f"    相关系数: {correlation:.4f}")
        
        # 检查排序是否一致
        operator_sorted = [node["node_id"] for node in operator_result["sorted_nodes"]]
        adapter_sorted = [node["node_id"] for node in adapter_result["sorted_nodes"]]
        
        # 计算排序一致性
        top_k = min(5, len(operator_sorted))
        operator_top_k = set(operator_sorted[:top_k])
        adapter_top_k = set(adapter_sorted[:top_k])
        
        overlap = len(operator_top_k.intersection(adapter_top_k))
        consistency = overlap / top_k
        
        logger.info(f"    Top-{top_k}排序一致性: {consistency:.2f}")
    
    def plot_results(self, results):
        """
        绘制测试结果
        
        Args:
            results: 测试结果
        """
        centrality_types = ["degree", "closeness", "betweenness", "eigenvector", "pagerank", "katz"]
        
        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        axes = axes.flatten()
        
        for i, centrality_type in enumerate(centrality_types):
            ax = axes[i]
            
            # 提取数据
            operator_data = results["operator"][centrality_type]
            adapter_data = results["adapter"][centrality_type]
            
            n_nodes = [d["n_nodes"] for d in operator_data]
            operator_times = [d["elapsed_time"] for d in operator_data]
            adapter_times = [d["elapsed_time"] for d in adapter_data]
            
            # 绘制时间对比
            ax.plot(n_nodes, operator_times, 'o-', label='GPU加速中心性算子')
            ax.plot(n_nodes, adapter_times, 's-', label='模拟GPU加速器适配器')
            
            # 添加标签和标题
            ax.set_xlabel('节点数量')
            ax.set_ylabel('执行时间 (秒)')
            ax.set_title(f'{centrality_type.capitalize()} 中心性')
            ax.legend()
            ax.grid(True)
        
        plt.tight_layout()
        plt.savefig('gpu_centrality_integration_test_results.png')
        plt.close()
    
    def close(self):
        """关闭测试环境"""
        # 关闭模拟GPU加速器适配器
        self.gpu_adapter.close()
        logger.info("模拟GPU加速器适配器已关闭")

def main():
    """主函数"""
    logger.info("开始测试GPU加速中心节点识别算子与模拟GPU加速器的集成...")
    
    try:
        # 创建测试环境
        test = GPUCentralityIntegrationTest()
        
        # 运行集成测试
        results = test.run_integration_test()
        
        # 绘制测试结果
        test.plot_results(results)
        
    except ImportError as e:
        logger.error(f"导入错误: {str(e)}")
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
    finally:
        # 关闭测试环境
        if 'test' in locals():
            test.close()
    
    logger.info("测试完成")

if __name__ == "__main__":
    main()
