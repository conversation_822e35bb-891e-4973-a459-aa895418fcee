"""
故障恢复算子示例

本示例展示了如何使用故障恢复算子进行故障检测和恢复。
"""

import sys
import os
import time
import logging
import random
import threading
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入故障恢复算子
from src.transcendental_tensor.performance_optimization.fault_recovery import (
    NodeState, ServiceState, FailureType, RecoveryStrategy,
    HealthStatus, FailureEvent, RecoveryPlan,
    HeartbeatDetector, MetricThresholdDetector,
    RestartStrategy, FailoverStrategy,
    FaultRecoveryManager
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def node_failure_callback(data):
    """
    节点故障回调函数
    
    参数:
        data: 回调数据
    """
    node_id, event = data
    logger.info(f"Node failure callback: {node_id}, type: {event.failure_type.value}")


def service_failure_callback(data):
    """
    服务故障回调函数
    
    参数:
        data: 回调数据
    """
    service_id, event = data
    logger.info(f"Service failure callback: {service_id}, type: {event.failure_type.value}")


def plan_completed_callback(data):
    """
    计划完成回调函数
    
    参数:
        data: 回调数据
    """
    plan, success = data
    logger.info(f"Plan completed callback: {plan.plan_id}, success: {success}")


def simulate_heartbeats(manager, nodes, services, duration=60):
    """
    模拟心跳
    
    参数:
        manager: 故障恢复管理器
        nodes: 节点列表
        services: 服务列表
        duration: 持续时间（秒）
    """
    start_time = time.time()
    
    while time.time() - start_time < duration:
        # 更新节点心跳
        for node_id in nodes:
            # 随机生成指标
            metrics = {
                "cpu_usage": random.uniform(0, 100),
                "memory_usage": random.uniform(0, 100),
                "disk_usage": random.uniform(0, 100)
            }
            
            # 更新心跳
            manager.update_node_heartbeat(node_id, metrics)
        
        # 更新服务心跳
        for service_id in services:
            # 随机生成指标
            metrics = {
                "request_count": random.uniform(0, 1000),
                "error_rate": random.uniform(0, 10),
                "response_time": random.uniform(0, 500)
            }
            
            # 更新心跳
            manager.update_service_heartbeat(service_id, metrics)
        
        # 等待一段时间
        time.sleep(1.0)


def simulate_failures(manager, nodes, services, duration=60):
    """
    模拟故障
    
    参数:
        manager: 故障恢复管理器
        nodes: 节点列表
        services: 服务列表
        duration: 持续时间（秒）
    """
    start_time = time.time()
    
    while time.time() - start_time < duration:
        # 随机选择一个节点或服务
        if random.random() < 0.5 and nodes:
            # 模拟节点故障
            node_id = random.choice(nodes)
            
            # 停止心跳一段时间
            logger.info(f"Simulating failure of node {node_id}")
            time.sleep(10.0)
        elif services:
            # 模拟服务故障
            service_id = random.choice(services)
            
            # 停止心跳一段时间
            logger.info(f"Simulating failure of service {service_id}")
            time.sleep(10.0)
        
        # 等待一段时间
        time.sleep(random.uniform(10, 30))


def main():
    """主函数"""
    logger.info("Starting fault recovery example")
    
    # 创建故障恢复管理器
    manager = FaultRecoveryManager(
        auto_recovery=True,
        strategy_selection="priority",
        max_concurrent_recoveries=2
    )
    
    # 注册回调函数
    manager.detection_manager.register_callback("node_failure", node_failure_callback)
    manager.detection_manager.register_callback("service_failure", service_failure_callback)
    manager.recovery_executor.register_callback("plan_completed", plan_completed_callback)
    
    # 启动管理器
    manager.start()
    
    # 创建节点和服务
    nodes = ["node-1", "node-2", "node-3"]
    services = ["service-1", "service-2", "service-3", "service-4"]
    
    # 初始化节点和服务
    for node_id in nodes:
        manager.update_node_heartbeat(node_id)
    
    for service_id in services:
        manager.update_service_heartbeat(service_id)
    
    logger.info(f"Initialized {len(nodes)} nodes and {len(services)} services")
    
    # 创建心跳线程
    heartbeat_thread = threading.Thread(
        target=simulate_heartbeats,
        args=(manager, nodes, services, 120),
        daemon=True
    )
    
    # 创建故障模拟线程
    failure_thread = threading.Thread(
        target=simulate_failures,
        args=(manager, nodes, services, 60),
        daemon=True
    )
    
    # 启动线程
    heartbeat_thread.start()
    failure_thread.start()
    
    # 等待线程结束
    heartbeat_thread.join()
    
    # 获取故障事件
    events = manager.get_failure_events()
    logger.info(f"Detected {len(events)} failure events")
    
    for event in events:
        logger.info(f"Event: {event.entity_type} {event.entity_id}, type: {event.failure_type.value}, resolved: {event.resolved}")
    
    # 获取恢复计划
    plans = manager.get_recovery_plans()
    logger.info(f"Created {len(plans)} recovery plans")
    
    for plan in plans:
        logger.info(f"Plan: {plan.entity_type} {plan.entity_id}, strategy: {plan.strategy.value}, status: {plan.status.value}")
    
    # 停止管理器
    manager.stop()
    
    logger.info("Fault recovery example completed")


if __name__ == "__main__":
    main()
