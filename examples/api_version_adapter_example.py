"""
API版本适配器示例

本示例展示了如何使用API版本适配器来处理不同版本的API之间的兼容性问题。
由于我们无法直接导入分布式模块，这里我们将实现一个简化版的API版本适配器。
"""

import os
import sys
import logging
import re

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 简化版的API版本检测器
class SimpleAPIVersionDetector:
    """简化版的API版本检测器"""

    def __init__(self, name="simple_detector"):
        self.name = name
        self.version_patterns = {}

    def register_version_pattern(self, version, pattern):
        """注册版本模式"""
        self.version_patterns[version] = pattern
        logger.info(f"注册版本模式: {version} -> {pattern}")

    def detect_version(self, obj):
        """检测版本"""
        if hasattr(obj, "version"):
            version = obj.version
            return self._match_version(version)
        return None

    def _match_version(self, version_str):
        """匹配版本字符串"""
        if not version_str:
            return None

        # 使用注册的版本模式进行匹配
        for version, pattern in self.version_patterns.items():
            if re.search(pattern, version_str):
                return version

        return None

# 简化版的API版本适配器
class SimpleAPIVersionAdapter:
    """简化版的API版本适配器"""

    def __init__(self, current_version, target_version, name="simple_adapter"):
        self.name = name
        self.current_version = current_version
        self.target_version = target_version
        self.detector = SimpleAPIVersionDetector(f"{name}_detector")
        self.adapters = {}
        self.version_info = {}

    def register_version_pattern(self, version, pattern):
        """注册版本模式"""
        self.detector.register_version_pattern(version, pattern)

    def add_version(self, version, info):
        """添加版本信息"""
        self.version_info[version] = info
        logger.info(f"添加版本信息: {version}")

    def register_adapter(self, source_version, target_version, adapter_func):
        """注册适配函数"""
        self.adapters[(source_version, target_version)] = adapter_func
        logger.info(f"注册适配函数: {source_version} -> {target_version}")

    def adapt(self, api):
        """适配API"""
        # 检测API版本
        source_version = self.detector.detect_version(api) if hasattr(api, "version") else None

        # 如果无法检测版本，则假设为当前版本
        if not source_version:
            if isinstance(api, dict) and "__version__" in api:
                source_version = api["__version__"]
            else:
                logger.warning(f"无法检测API版本，假设为当前版本: {self.current_version}")
                source_version = self.current_version

        # 如果源版本与目标版本相同，则不需要适配
        if source_version == self.target_version:
            return api, True, [source_version]

        # 查找适配函数
        adapter_func = self.adapters.get((source_version, self.target_version))

        if adapter_func:
            # 直接适配
            try:
                adapted_api = adapter_func(api)
                return adapted_api, True, [source_version, self.target_version]
            except Exception as e:
                logger.error(f"适配失败: {source_version} -> {self.target_version} - {e}")
                return api, False, [source_version]

        logger.error(f"没有找到适配函数: {source_version} -> {self.target_version}")
        return api, False, [source_version]

# 数据结构适配函数
def adapt_data_structure_v1_to_v2(data):
    """
    适配数据结构从v1到v2

    在v1中，数据结构为{"name": str, "value": int, "options": List[str]}
    在v2中，数据结构为{"name": str, "value": int, "options": List[Dict[str, Any]], "metadata": Dict[str, Any]}
    """
    # 创建适配后的数据结构
    adapted_data = data.copy() if isinstance(data, dict) else {"name": "unknown", "value": 0, "options": []}

    # 转换options字段
    if "options" in adapted_data and isinstance(adapted_data["options"], list):
        adapted_data["options"] = [{"name": option, "enabled": True} for option in adapted_data["options"]]

    # 添加metadata字段
    if "metadata" not in adapted_data:
        adapted_data["metadata"] = {}

    return adapted_data

# 简化版的示例测试
def run_simple_tests():
    """运行简化版的示例测试"""
    # 创建测试数据
    test_data = {
        "name": "test",
        "value": 123,
        "options": ["option1", "option2"]
    }

    # 创建适配器
    adapter = SimpleAPIVersionAdapter("1.0", "2.0", "test_adapter")

    # 注册版本模式
    adapter.register_version_pattern("1.0", r"1\.0(\.\d+)?")
    adapter.register_version_pattern("2.0", r"2\.0(\.\d+)?")

    # 注册适配函数
    adapter.register_adapter("1.0", "2.0", adapt_data_structure_v1_to_v2)

    # 适配数据
    adapted_data, success, path = adapter.adapt(test_data)

    # 返回结果
    return {
        "results": {
            "test_data_structure": {
                "success": success,
                "error": None if success else "适配失败",
                "path": path
            }
        },
        "summary": {
            "total": 1,
            "success": 1 if success else 0,
            "failure": 0 if success else 1,
            "success_rate": 1.0 if success else 0.0
        }
    }

def main():
    """主函数"""
    logger.info("开始API版本适配器示例")

    # 创建API版本检测器
    detector = SimpleAPIVersionDetector("example_detector")

    # 注册版本模式
    detector.register_version_pattern("1.0", r"1\.0(\.\d+)?")
    detector.register_version_pattern("2.0", r"2\.0(\.\d+)?")

    # 创建一些测试对象
    class TestAPI_V1:
        """测试API V1"""
        def __init__(self):
            self.version = "1.0"
            self.data = {
                "name": "test",
                "value": 123,
                "options": ["option1", "option2"]
            }

    class TestAPI_V2:
        """测试API V2"""
        def __init__(self):
            self.version = "2.0"
            self.data = {
                "name": "test",
                "value": 123,
                "options": [
                    {"name": "option1", "enabled": True},
                    {"name": "option2", "enabled": True}
                ],
                "metadata": {}
            }

    # 创建测试对象
    api_v1 = TestAPI_V1()
    api_v2 = TestAPI_V2()

    # 检测版本
    logger.info(f"API V1版本: {detector.detect_version(api_v1)}")
    logger.info(f"API V2版本: {detector.detect_version(api_v2)}")

    # 创建API版本适配器
    adapter = SimpleAPIVersionAdapter("1.0", "2.0", "example_adapter")

    # 注册版本模式
    adapter.register_version_pattern("1.0", r"1\.0(\.\d+)?")
    adapter.register_version_pattern("2.0", r"2\.0(\.\d+)?")

    # 添加版本信息
    adapter.add_version("1.0", {
        "release_date": "2023-01-01",
        "supported": True,
        "deprecated": False
    })

    adapter.add_version("2.0", {
        "release_date": "2023-06-01",
        "supported": True,
        "deprecated": False
    })

    # 注册适配函数
    adapter.register_adapter("1.0", "2.0", adapt_data_structure_v1_to_v2)

    # 适配数据
    logger.info("适配API V1数据到V2...")
    adapted_data, success, path = adapter.adapt(api_v1.data)

    if success:
        logger.info(f"适配成功，路径: {path}")
        logger.info(f"适配前: {api_v1.data}")
        logger.info(f"适配后: {adapted_data}")
    else:
        logger.error(f"适配失败，路径: {path}")

    # 运行示例测试
    logger.info("运行示例测试...")
    test_results = run_simple_tests()

    logger.info(f"测试结果摘要: {test_results['summary']}")
    for test_id, result in test_results['results'].items():
        if result['success']:
            logger.info(f"测试 {test_id} 通过")
        else:
            logger.error(f"测试 {test_id} 失败: {result['error']}")

    logger.info("API版本适配器示例结束")

if __name__ == "__main__":
    main()
