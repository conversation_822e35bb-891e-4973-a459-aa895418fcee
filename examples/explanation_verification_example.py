"""
自解释与可验证性算子使用示例

本示例展示了如何使用自解释与可验证性算子。
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('..'))

# 导入自解释算子
from src.operators.explanation.multilevel_explanation import MultilevelExplanationOperator
from src.operators.explanation.explanation_quality import ExplanationQualityOperator
from src.operators.explanation.visualization import VisualizationExplanationOperator

# 导入可验证性算子
from src.operators.verification.multi_method_verification import MultiMethodVerificationOperator
from src.operators.verification.consistency_verification import ConsistencyVerificationOperator
from src.operators.verification.realtime_verification import RealtimeVerificationOperator

def main():
    """主函数"""
    print("超越态思维引擎自解释与可验证性算子示例")
    print("=" * 50)
    
    # 创建示例数据
    system_state = {
        'variables': {
            'x': 10,
            'y': 20,
            'z': 30
        },
        'parameters': {
            'alpha': 0.5,
            'beta': 0.3,
            'gamma': 0.2
        }
    }
    
    action = {
        'type': 'transform',
        'parameters': {
            'matrix': [[0.8, -0.6, 0.0], [0.6, 0.8, 0.0], [0.0, 0.0, 1.0]],
            'offset': [1.0, 2.0, 3.0]
        }
    }
    
    input_data = {
        'state': system_state,
        'action': action
    }
    
    # 创建属性列表
    properties = [
        {
            'id': 'prop1',
            'type': 'safety',
            'expression': 'G(x > 0)',
            'description': '变量x始终大于0'
        },
        {
            'id': 'prop2',
            'type': 'liveness',
            'expression': 'F(y > 15)',
            'description': '变量y最终大于15'
        },
        {
            'id': 'prop3',
            'type': 'runtime',
            'expression': 'x + y < 100',
            'description': '变量x和y的和小于100'
        }
    ]
    
    # 示例1：多层次解释生成
    print("\n示例1：多层次解释生成")
    print("-" * 50)
    
    # 创建多层次解释生成算子
    multilevel_explanation = MultilevelExplanationOperator(
        tech_level=0.7,
        concept_level=0.8,
        analogy_level=0.6,
        user_model={
            'technical_expertise': 0.6,
            'domain_knowledge': 0.7,
            'learning_style': 'balanced'
        }
    )
    
    # 生成多层次解释
    explanation = multilevel_explanation.apply(input_data)
    
    # 打印解释
    print("技术层面解释:", explanation['technical']['justification'])
    print("概念层面解释:", explanation['conceptual']['justification'])
    print("类比层面解释:", explanation['analogy']['justification'])
    print("融合解释:", explanation['fused']['justification'])
    print("权重:", explanation['weights'])
    
    # 示例2：解释质量评估
    print("\n示例2：解释质量评估")
    print("-" * 50)
    
    # 创建解释质量评估算子
    explanation_quality = ExplanationQualityOperator(
        completeness_weight=0.3,
        consistency_weight=0.3,
        comprehensibility_weight=0.4
    )
    
    # 评估解释质量
    quality = explanation_quality.apply(explanation)
    
    # 打印质量评估结果
    print("完整性:", quality['completeness'])
    print("一致性:", quality['consistency'])
    print("可理解性:", quality['comprehensibility'])
    print("总体质量:", quality['quality'])
    
    # 示例3：可视化解释
    print("\n示例3：可视化解释")
    print("-" * 50)
    
    # 创建可视化解释算子
    visualization = VisualizationExplanationOperator(
        visualization_type='causal_graph',
        color_scheme='default',
        interactive=False,
        max_elements=10
    )
    
    # 生成可视化解释
    vis_result = visualization.apply(explanation)
    
    # 打印可视化结果
    print("可视化类型:", vis_result['visualization_type'])
    print("节点数量:", len(vis_result['visualization']['nodes']))
    print("边数量:", len(vis_result['visualization']['edges']))
    
    # 示例4：多方法验证
    print("\n示例4：多方法验证")
    print("-" * 50)
    
    # 创建多方法验证算子
    multi_method_verification = MultiMethodVerificationOperator(
        model_checking_enabled=True,
        theorem_proving_enabled=True,
        runtime_monitoring_enabled=True,
        model_checking_weight=0.3,
        theorem_proving_weight=0.3,
        runtime_monitoring_weight=0.4
    )
    
    # 验证属性
    verification_input = {
        'state': system_state,
        'properties': properties
    }
    
    verification_results = multi_method_verification.apply(verification_input)
    
    # 打印验证结果
    print("验证结果:")
    for prop_id, result in verification_results['verification_results'].items():
        print(f"- 属性 {prop_id}: {result['result']} (置信度: {result['confidence']})")
    
    # 示例5：一致性验证
    print("\n示例5：一致性验证")
    print("-" * 50)
    
    # 创建一致性验证算子
    consistency_verification = ConsistencyVerificationOperator(
        consistency_threshold=0.8,
        check_logical_consistency=True,
        check_temporal_consistency=True,
        check_causal_consistency=True
    )
    
    # 验证一致性
    consistency_results = consistency_verification.apply(verification_input)
    
    # 打印一致性验证结果
    print("全局一致性:", consistency_results['global_consistency'])
    print("是否一致:", consistency_results['is_consistent'])
    if not consistency_results['is_consistent']:
        print("不一致性:")
        for inconsistency in consistency_results['inconsistencies']:
            print(f"- {inconsistency['description']}")
    
    # 示例6：实时验证
    print("\n示例6：实时验证")
    print("-" * 50)
    
    # 创建实时验证算子
    realtime_verification = RealtimeVerificationOperator(
        monitoring_interval=1.0,
        violation_threshold=0.8,
        max_history_size=10,
        auto_start=False,
        properties=properties
    )
    
    # 注册回调函数
    def violation_callback(violations):
        print(f"检测到 {len(violations)} 个违规:")
        for violation in violations:
            print(f"- {violation['description']}")
    
    realtime_verification.register_callback(violation_callback)
    
    # 启动监控
    realtime_verification.start_monitoring()
    print("已启动实时监控")
    
    # 验证当前状态
    realtime_results = realtime_verification.apply(verification_input)
    
    # 打印实时验证结果
    print("验证结果:")
    for prop_id, result in realtime_results['verification_results'].items():
        print(f"- 属性 {prop_id}: {result['result']} (置信度: {result['confidence']})")
    
    # 等待一段时间
    print("等待监控...")
    time.sleep(2)
    
    # 停止监控
    realtime_verification.stop_monitoring()
    print("已停止实时监控")
    
    print("\n示例完成！")

if __name__ == "__main__":
    main()
