"""
负载均衡算子示例

本示例展示了如何使用负载均衡算子进行负载均衡。
"""

import sys
import os
import time
import logging
import random
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入负载均衡算子
from src.transcendental_tensor.performance_optimization.load_balancing import (
    Node, NodeStatus, Task, TaskStatus, TaskPriority,
    ResourceType, BalancingStrategy,
    LoadMonitor, LoadAnalyzer, StrategyFactory, LoadBalancer
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def balance_callback(plan):
    """
    均衡回调函数
    
    参数:
        plan: 均衡计划
    """
    logger.info(f"Balance callback: {plan.name}, actions={len(plan.actions)}")


def create_test_nodes(count: int = 3) -> List[Node]:
    """
    创建测试节点
    
    参数:
        count (int): 节点数量
        
    返回:
        List[Node]: 节点列表
    """
    nodes = []
    
    for i in range(count):
        # 创建节点
        node = Node(
            node_id=f"node-{i}",
            name=f"Node {i}",
            status=NodeStatus.ONLINE
        )
        
        # 添加CPU资源
        cpu_capacity = random.randint(4, 16)
        node.resources[ResourceType.CPU] = node.NodeResource(
            resource_type=ResourceType.CPU,
            capacity=float(cpu_capacity),
            available=float(cpu_capacity)
        )
        
        # 添加内存资源
        memory_capacity = random.randint(8, 64)
        node.resources[ResourceType.MEMORY] = node.NodeResource(
            resource_type=ResourceType.MEMORY,
            capacity=float(memory_capacity),
            available=float(memory_capacity)
        )
        
        # 添加存储资源
        storage_capacity = random.randint(100, 1000)
        node.resources[ResourceType.STORAGE] = node.NodeResource(
            resource_type=ResourceType.STORAGE,
            capacity=float(storage_capacity),
            available=float(storage_capacity)
        )
        
        nodes.append(node)
    
    return nodes


def create_test_tasks(count: int = 10) -> List[Task]:
    """
    创建测试任务
    
    参数:
        count (int): 任务数量
        
    返回:
        List[Task]: 任务列表
    """
    tasks = []
    
    for i in range(count):
        # 创建任务
        task = Task(
            task_id=f"task-{i}",
            name=f"Task {i}",
            priority=random.choice(list(TaskPriority)),
            status=TaskStatus.PENDING,
            estimated_duration=random.uniform(10, 100)
        )
        
        # 添加CPU需求
        cpu_requirement = task.ResourceRequirement(
            resource_type=ResourceType.CPU,
            amount=random.uniform(0.5, 2.0)
        )
        task.resource_requirements.append(cpu_requirement)
        
        # 添加内存需求
        memory_requirement = task.ResourceRequirement(
            resource_type=ResourceType.MEMORY,
            amount=random.uniform(1.0, 4.0)
        )
        task.resource_requirements.append(memory_requirement)
        
        tasks.append(task)
    
    return tasks


def main():
    """主函数"""
    logger.info("Starting load balancing example")
    
    # 创建负载均衡器
    balancer = LoadBalancer(
        name="ExampleBalancer",
        strategy_type=BalancingStrategy.RESOURCE_BASED
    )
    
    # 添加均衡回调函数
    balancer.add_balance_callback(balance_callback)
    
    # 创建测试节点
    nodes = create_test_nodes(3)
    
    # 注册节点
    for node in nodes:
        balancer.register_node(node)
    
    logger.info(f"Registered {len(nodes)} nodes")
    
    # 创建测试任务
    tasks = create_test_tasks(10)
    
    # 注册任务
    for task in tasks:
        balancer.register_task(task)
    
    logger.info(f"Registered {len(tasks)} tasks")
    
    # 执行负载均衡
    plan = balancer.balance()
    
    logger.info(f"Balance plan created: {plan.name}, actions={len(plan.actions)}")
    
    # 打印均衡动作
    for i, action in enumerate(plan.actions):
        logger.info(f"Action {i}: {action.action_type}, target={action.target_id}, status={action.status}")
    
    # 检查任务分配情况
    scheduled_tasks = balancer.get_tasks(TaskStatus.SCHEDULED)
    logger.info(f"Scheduled tasks: {len(scheduled_tasks)}")
    
    for task in scheduled_tasks:
        logger.info(f"Task {task.task_id} scheduled to node {task.node_id}")
    
    # 模拟任务执行
    for task in scheduled_tasks:
        # 更新任务状态为运行中
        balancer.update_task_status(task.task_id, TaskStatus.RUNNING)
    
    logger.info(f"Running tasks: {len(balancer.get_tasks(TaskStatus.RUNNING))}")
    
    # 模拟负载变化
    logger.info("Simulating load changes...")
    
    # 使一个节点过载
    overloaded_node = nodes[0]
    overloaded_node.resources[ResourceType.CPU].available = 0.1
    
    # 再次执行负载均衡
    plan = balancer.balance()
    
    logger.info(f"Balance plan created after load changes: {plan.name}, actions={len(plan.actions)}")
    
    # 打印均衡动作
    for i, action in enumerate(plan.actions):
        logger.info(f"Action {i}: {action.action_type}, target={action.target_id}, status={action.status}")
    
    # 完成所有任务
    for task in balancer.get_tasks(TaskStatus.RUNNING):
        balancer.update_task_status(task.task_id, TaskStatus.COMPLETED)
    
    logger.info(f"Completed tasks: {len(balancer.get_tasks(TaskStatus.COMPLETED))}")
    
    # 打印均衡器状态
    balancer_dict = balancer.to_dict()
    logger.info(f"Balancer status: {balancer_dict}")
    
    logger.info("Load balancing example completed")


if __name__ == "__main__":
    main()
