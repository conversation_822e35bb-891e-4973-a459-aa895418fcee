#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Arrow 兼容性示例

本示例展示了如何使用 Arrow 兼容性模块，确保与 PyArrow 14.0.0+ 的完全兼容，
并充分利用其新特性，如零拷贝转换和高效的 GIL 管理。
"""

import os
import sys
import logging
import time
import numpy as np
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入 Arrow 兼容性模块
try:
    import pyarrow as pa
    import pyarrow.compute as pc
    import pyarrow.parquet as pq
    
    from src.core.data.arrow_compat import (
        # 版本和兼容性检查
        arrow_version, is_compatible_version, check_compatibility,
        
        # 数组转换函数
        numpy_to_arrow_complex64, numpy_to_arrow_complex128,
        arrow_to_numpy_complex64, arrow_to_numpy_complex128,
        numpy_to_arrow_quantum_state, arrow_to_numpy_quantum_state,
        numpy_to_arrow_tensor, arrow_to_numpy_tensor,
        
        # Parquet 存储函数
        write_parquet, read_parquet
    )
    
    from src.core.parallel.gil_manager import gil_state, release_gil
    
    # 检查 PyArrow 版本
    version = arrow_version()
    logger.info(f"成功导入 Arrow 兼容性模块，PyArrow 版本: {version}")
    
    # 检查兼容性
    if not is_compatible_version():
        logger.warning(f"当前 PyArrow 版本 {version} 不兼容，建议升级到 14.0.0 或更高版本")
except ImportError as e:
    logger.error(f"导入 Arrow 兼容性模块失败: {e}")
    sys.exit(1)


def test_complex_array_conversion():
    """测试复数数组转换"""
    logger.info("开始测试复数数组转换...")
    
    # 创建复数数组
    size = 1000000
    complex64_array = np.random.rand(size).astype(np.float32) + 1j * np.random.rand(size).astype(np.float32)
    complex128_array = np.random.rand(size) + 1j * np.random.rand(size)
    
    # 测试 complex64 转换
    logger.info("测试 complex64 转换...")
    
    # NumPy 到 Arrow
    start_time = time.time()
    arrow_complex64 = numpy_to_arrow_complex64(complex64_array)
    np_to_arrow_time = time.time() - start_time
    logger.info(f"NumPy 到 Arrow (complex64) 耗时: {np_to_arrow_time:.6f}秒")
    
    # Arrow 到 NumPy
    start_time = time.time()
    numpy_complex64 = arrow_to_numpy_complex64(arrow_complex64)
    arrow_to_np_time = time.time() - start_time
    logger.info(f"Arrow 到 NumPy (complex64) 耗时: {arrow_to_np_time:.6f}秒")
    
    # 验证结果
    assert np.allclose(complex64_array, numpy_complex64), "complex64 转换结果不一致"
    logger.info("complex64 转换验证通过")
    
    # 测试 complex128 转换
    logger.info("测试 complex128 转换...")
    
    # NumPy 到 Arrow
    start_time = time.time()
    arrow_complex128 = numpy_to_arrow_complex128(complex128_array)
    np_to_arrow_time = time.time() - start_time
    logger.info(f"NumPy 到 Arrow (complex128) 耗时: {np_to_arrow_time:.6f}秒")
    
    # Arrow 到 NumPy
    start_time = time.time()
    numpy_complex128 = arrow_to_numpy_complex128(arrow_complex128)
    arrow_to_np_time = time.time() - start_time
    logger.info(f"Arrow 到 NumPy (complex128) 耗时: {arrow_to_np_time:.6f}秒")
    
    # 验证结果
    assert np.allclose(complex128_array, numpy_complex128), "complex128 转换结果不一致"
    logger.info("complex128 转换验证通过")
    
    logger.info("复数数组转换测试完成")


def test_quantum_state_conversion():
    """测试量子态转换"""
    logger.info("开始测试量子态转换...")
    
    # 创建量子态数组
    def create_random_quantum_state(size):
        # 创建随机复数数组
        state = np.random.rand(size) + 1j * np.random.rand(size)
        # 归一化
        norm = np.sqrt(np.sum(np.abs(state)**2))
        return state / norm
    
    # 创建不同大小的量子态
    qubit_counts = [1, 2, 3, 4, 5]
    dimensions = [2**n for n in qubit_counts]
    
    for n, dim in zip(qubit_counts, dimensions):
        logger.info(f"测试 {n} 量子比特 (维度 {dim}) 的量子态...")
        
        # 创建随机量子态
        quantum_state = create_random_quantum_state(dim)
        
        # 验证归一化
        norm = np.sum(np.abs(quantum_state)**2)
        assert np.isclose(norm, 1.0), f"量子态未归一化，范数为 {norm}"
        
        # NumPy 到 Arrow
        start_time = time.time()
        arrow_quantum_state = numpy_to_arrow_quantum_state(
            quantum_state, 
            validate=True, 
            dimensions=[2] * n  # n 个 2 维系统
        )
        np_to_arrow_time = time.time() - start_time
        logger.info(f"NumPy 到 Arrow ({n} 量子比特) 耗时: {np_to_arrow_time:.6f}秒")
        
        # Arrow 到 NumPy
        start_time = time.time()
        numpy_quantum_state = arrow_to_numpy_quantum_state(arrow_quantum_state)
        arrow_to_np_time = time.time() - start_time
        logger.info(f"Arrow 到 NumPy ({n} 量子比特) 耗时: {arrow_to_np_time:.6f}秒")
        
        # 验证结果
        assert np.allclose(quantum_state, numpy_quantum_state), f"{n} 量子比特转换结果不一致"
        logger.info(f"{n} 量子比特转换验证通过")
    
    logger.info("量子态转换测试完成")


def test_tensor_conversion():
    """测试张量转换"""
    logger.info("开始测试张量转换...")
    
    # 创建不同形状的张量
    shapes = [
        (10, 10),           # 2D 矩阵
        (5, 5, 5),          # 3D 张量
        (2, 3, 4, 5),       # 4D 张量
        (2, 2, 2, 2, 2)     # 5D 张量
    ]
    
    for shape in shapes:
        logger.info(f"测试形状为 {shape} 的张量...")
        
        # 创建随机张量
        tensor = np.random.rand(*shape) + 1j * np.random.rand(*shape)
        
        # NumPy 到 Arrow
        start_time = time.time()
        arrow_tensor = numpy_to_arrow_tensor(tensor)
        np_to_arrow_time = time.time() - start_time
        logger.info(f"NumPy 到 Arrow ({shape}) 耗时: {np_to_arrow_time:.6f}秒")
        
        # Arrow 到 NumPy
        start_time = time.time()
        numpy_tensor = arrow_to_numpy_tensor(arrow_tensor)
        arrow_to_np_time = time.time() - start_time
        logger.info(f"Arrow 到 NumPy ({shape}) 耗时: {arrow_to_np_time:.6f}秒")
        
        # 验证结果
        assert np.allclose(tensor, numpy_tensor), f"形状为 {shape} 的张量转换结果不一致"
        logger.info(f"形状为 {shape} 的张量转换验证通过")
    
    logger.info("张量转换测试完成")


def test_parquet_storage():
    """测试 Parquet 存储"""
    logger.info("开始测试 Parquet 存储...")
    
    # 创建临时目录
    import tempfile
    temp_dir = tempfile.mkdtemp()
    logger.info(f"使用临时目录: {temp_dir}")
    
    try:
        # 创建测试数据
        size = 100000
        complex_array = np.random.rand(size) + 1j * np.random.rand(size)
        quantum_state = np.random.rand(16) + 1j * np.random.rand(16)
        quantum_state = quantum_state / np.sqrt(np.sum(np.abs(quantum_state)**2))
        tensor = np.random.rand(4, 4, 4) + 1j * np.random.rand(4, 4, 4)
        
        # 转换为 Arrow 数组
        arrow_complex = numpy_to_arrow_complex128(complex_array)
        arrow_quantum = numpy_to_arrow_quantum_state(quantum_state, validate=True, dimensions=[2, 2, 2, 2])
        arrow_tensor = numpy_to_arrow_tensor(tensor)
        
        # 创建表数据
        data = {
            'complex': arrow_complex,
            'quantum': arrow_quantum,
            'tensor': arrow_tensor
        }
        
        # 写入 Parquet 文件
        parquet_path = os.path.join(temp_dir, 'test_data.parquet')
        
        start_time = time.time()
        write_parquet(data, parquet_path, compression='zstd', use_dictionary=True)
        write_time = time.time() - start_time
        logger.info(f"写入 Parquet 文件耗时: {write_time:.6f}秒")
        
        # 获取文件大小
        file_size = os.path.getsize(parquet_path)
        logger.info(f"Parquet 文件大小: {file_size / 1024 / 1024:.2f} MB")
        
        # 读取 Parquet 文件
        start_time = time.time()
        table = read_parquet(parquet_path)
        read_time = time.time() - start_time
        logger.info(f"读取 Parquet 文件耗时: {read_time:.6f}秒")
        
        # 验证数据
        read_complex = table.column('complex')
        read_quantum = table.column('quantum')
        read_tensor = table.column('tensor')
        
        # 转换回 NumPy 数组
        numpy_complex = arrow_to_numpy_complex128(read_complex)
        numpy_quantum = arrow_to_numpy_quantum_state(read_quantum)
        numpy_tensor = arrow_to_numpy_tensor(read_tensor)
        
        # 验证结果
        assert np.allclose(complex_array, numpy_complex), "复数数组存储结果不一致"
        assert np.allclose(quantum_state, numpy_quantum), "量子态存储结果不一致"
        assert np.allclose(tensor, numpy_tensor), "张量存储结果不一致"
        
        logger.info("Parquet 存储验证通过")
    
    finally:
        # 清理临时目录
        import shutil
        shutil.rmtree(temp_dir)
    
    logger.info("Parquet 存储测试完成")


def test_gil_management():
    """测试 GIL 管理"""
    logger.info("开始测试 GIL 管理...")
    
    # 创建大型数组
    size = 10000000
    array = np.random.rand(size) + 1j * np.random.rand(size)
    
    # 不释放 GIL
    start_time = time.time()
    arrow_array = numpy_to_arrow_complex128(array)
    numpy_array = arrow_to_numpy_complex128(arrow_array)
    normal_time = time.time() - start_time
    logger.info(f"不手动管理 GIL 耗时: {normal_time:.6f}秒")
    
    # 手动释放 GIL
    start_time = time.time()
    with gil_state(False):
        arrow_array = numpy_to_arrow_complex128(array)
        numpy_array = arrow_to_numpy_complex128(arrow_array)
    gil_release_time = time.time() - start_time
    logger.info(f"手动释放 GIL 耗时: {gil_release_time:.6f}秒")
    
    # 使用装饰器释放 GIL
    @release_gil
    def convert_with_decorator():
        arrow_array = numpy_to_arrow_complex128(array)
        numpy_array = arrow_to_numpy_complex128(arrow_array)
        return numpy_array
    
    start_time = time.time()
    result = convert_with_decorator()
    decorator_time = time.time() - start_time
    logger.info(f"使用装饰器释放 GIL 耗时: {decorator_time:.6f}秒")
    
    logger.info("GIL 管理测试完成")


def test_parallel_conversion():
    """测试并行转换"""
    logger.info("开始测试并行转换...")
    
    # 导入并行模块
    from src.core.parallel import parallel_map
    
    # 创建多个数组
    count = 10
    arrays = [np.random.rand(100000) + 1j * np.random.rand(100000) for _ in range(count)]
    
    # 串行转换
    start_time = time.time()
    arrow_arrays = []
    for array in arrays:
        arrow_array = numpy_to_arrow_complex128(array)
        arrow_arrays.append(arrow_array)
    serial_time = time.time() - start_time
    logger.info(f"串行转换 {count} 个数组耗时: {serial_time:.6f}秒")
    
    # 并行转换
    start_time = time.time()
    arrow_arrays_parallel = parallel_map(numpy_to_arrow_complex128, arrays)
    parallel_time = time.time() - start_time
    logger.info(f"并行转换 {count} 个数组耗时: {parallel_time:.6f}秒")
    
    # 计算加速比
    speedup = serial_time / parallel_time
    logger.info(f"加速比: {speedup:.2f}x")
    
    logger.info("并行转换测试完成")


def main():
    """主函数"""
    logger.info(f"开始 Arrow 兼容性示例，PyArrow 版本: {arrow_version()}")
    
    try:
        # 检查兼容性
        check_compatibility()
        
        # 测试复数数组转换
        test_complex_array_conversion()
        
        # 测试量子态转换
        test_quantum_state_conversion()
        
        # 测试张量转换
        test_tensor_conversion()
        
        # 测试 Parquet 存储
        test_parquet_storage()
        
        # 测试 GIL 管理
        test_gil_management()
        
        # 测试并行转换
        test_parallel_conversion()
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}", exc_info=True)
    
    logger.info("Arrow 兼容性示例结束")


if __name__ == "__main__":
    main()
