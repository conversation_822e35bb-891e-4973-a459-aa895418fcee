#!/usr/bin/env python3
"""
增强集成示例脚本

该脚本展示了如何使用增强的自解释与可验证性算子集成模块。
"""

import os
import sys
import logging
import argparse
from typing import Dict, Any, List, Optional, Tuple, Union

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('..'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 尝试导入集成模块
try:
    from src.integration.explanation_verification_integration import (
        create_explanation_verification_integration,
        integrate_explanation_verification_with_engine
    )
except ImportError as e:
    logger.error(f"导入集成模块失败: {e}")
    sys.exit(1)

# 创建简单的决策类
class Decision:
    def __init__(self, entity_id, options, context=None, metadata=None):
        self.decision_id = f"decision_{entity_id}_{id(self)}"
        self.entity_id = entity_id
        self.options = options
        self.context = context or {}
        self.metadata = metadata or {}
        self.weights = {}
        self.selected_option = None
        self.confidence = 0.0
        self.status = "created"
        self.execution_result = None

# 创建简单的决策协调系统
class DecisionCoordinator:
    def __init__(self):
        self.decisions = {}
        self.stats = {"decisions_created": 0, "decisions_executed": 0}
    
    def create_decision(self, entity_id, options, context=None, metadata=None):
        decision = Decision(entity_id, options, context, metadata)
        self.decisions[decision.decision_id] = decision
        self.stats["decisions_created"] += 1
        return decision
    
    def evaluate_decision(self, decision):
        if decision.decision_id not in self.decisions:
            self.decisions[decision.decision_id] = decision
        
        # 计算权重
        weights = {}
        for option in decision.options:
            weights[option] = 1.0
        
        # 应用自定义权重
        if decision.weights:
            for option, weight in decision.weights.items():
                if option in weights:
                    weights[option] = weight
        
        # 选择最佳选项
        if weights:
            selected_option = max(weights.items(), key=lambda x: x[1])[0]
            confidence = weights[selected_option] / sum(weights.values())
        else:
            selected_option = None
            confidence = 0.0
        
        # 更新决策
        decision.selected_option = selected_option
        decision.confidence = confidence
        decision.status = "evaluated"
        
        return decision
    
    def execute_decision(self, decision_id, option=None):
        decision = self.decisions.get(decision_id)
        if not decision:
            return None
        
        # 使用指定选项或已选择的选项
        option = option or decision.selected_option
        
        # 更新决策
        decision.status = "executed"
        decision.execution_result = {"option": option, "success": True}
        
        self.stats["decisions_executed"] += 1
        
        return decision.execution_result
    
    def coordinate_decisions(self, decisions, strategy=None):
        if not decisions:
            return None
        
        # 选择置信度最高的决策
        best_decision = max(decisions, key=lambda d: getattr(d, "confidence", 0.0))
        
        # 创建协调决策
        coordinated_decision = Decision(
            entity_id="coordinator",
            options={"coordinated": True},
            context={"source_decisions": [d.decision_id for d in decisions]},
            metadata={"strategy": str(strategy)}
        )
        
        # 设置选项和置信度
        coordinated_decision.selected_option = "coordinated"
        coordinated_decision.confidence = getattr(best_decision, "confidence", 0.5)
        coordinated_decision.status = "coordinated"
        
        return coordinated_decision

# 创建简单的推理引擎
class ReasoningEngine:
    def __init__(self):
        self.verification_properties = [
            {
                "id": "logical_consistency",
                "type": "safety",
                "expression": "G(result is consistent with input and context)"
            },
            {
                "id": "completeness",
                "type": "liveness",
                "expression": "F(result addresses all aspects of input)"
            }
        ]
        
        self.consistency_properties = [
            {
                "id": "prop_consistency",
                "type": "consistency",
                "expression": "result is consistent"
            }
        ]
        
        self.monitoring_properties = [
            {
                "id": "resource_usage",
                "type": "safety",
                "expression": "G(memory_usage < 90%)"
            }
        ]
        
        self.formal_verification_properties = [
            {
                "id": "safety",
                "type": "safety",
                "expression": "G(system_state is safe)"
            },
            {
                "id": "liveness",
                "type": "liveness",
                "expression": "F(system_state reaches goal)"
            }
        ]
    
    def reason(self, input_data, context=None, **kwargs):
        # 简单的推理逻辑
        if isinstance(input_data, dict):
            result = {k: v for k, v in input_data.items()}
            result["reasoning_applied"] = True
        elif isinstance(input_data, list):
            result = [item for item in input_data]
            result.append("reasoning_applied")
        else:
            result = f"{input_data} (reasoning_applied)"
        
        return result
    
    def evaluate_result(self, result, context=None):
        # 简单的评估逻辑
        if isinstance(result, dict):
            result["evaluation_applied"] = True
        elif isinstance(result, list):
            result.append("evaluation_applied")
        else:
            result = f"{result} (evaluation_applied)"
        
        return result
    
    def handle_violations(self, violations):
        # 简单的违规处理逻辑
        logger.warning(f"处理违规: {violations}")


def create_integrated_engine(use_rust: bool = True):
    """
    创建集成了自解释与可验证性算子的思维引擎
    
    参数:
        use_rust: 是否使用Rust实现
        
    返回:
        (决策协调系统, 推理引擎)
    """
    logger.info("创建集成了自解释与可验证性算子的思维引擎...")
    
    # 创建决策协调系统
    decision_coordinator = DecisionCoordinator()
    
    # 创建推理引擎
    reasoning_engine = ReasoningEngine()
    
    # 集成到思维引擎
    status = integrate_explanation_verification_with_engine(
        decision_coordinator=decision_coordinator,
        reasoning_engine=reasoning_engine,
        use_rust=use_rust
    )
    
    logger.info(f"集成状态: {status}")
    
    return decision_coordinator, reasoning_engine


def demonstrate_decision_making(decision_coordinator):
    """
    演示决策制定过程
    
    参数:
        decision_coordinator: 决策协调系统
    """
    logger.info("=" * 80)
    logger.info("演示决策制定过程")
    logger.info("=" * 80)
    
    # 创建实体
    entity_id = "demo_entity"
    
    # 创建决策
    options = {
        "option1": "增加资源分配",
        "option2": "减少资源分配",
        "option3": "保持当前资源分配"
    }
    
    context = {
        "current_resource_usage": 0.75,
        "system_load": 0.65,
        "priority": "high"
    }
    
    metadata = {
        "decision_type": "resource_allocation",
        "importance": "high"
    }
    
    logger.info(f"创建决策: 实体={entity_id}, 选项={options}, 上下文={context}")
    decision = decision_coordinator.create_decision(
        entity_id=entity_id,
        options=options,
        context=context,
        metadata=metadata
    )
    
    # 设置权重
    decision.weights = {
        "option1": 0.7,
        "option2": 0.2,
        "option3": 0.1
    }
    
    # 评估决策
    logger.info("评估决策...")
    evaluated_decision = decision_coordinator.evaluate_decision(decision)
    
    logger.info(f"评估结果: 选择选项={evaluated_decision.selected_option}, 置信度={evaluated_decision.confidence:.2f}")
    
    # 打印解释
    if hasattr(evaluated_decision, "explanation"):
        logger.info("\n解释:")
        logger.info(f"技术解释: {evaluated_decision.explanation.get('technical_explanation', '')}")
        logger.info(f"概念解释: {evaluated_decision.explanation.get('conceptual_explanation', '')}")
        logger.info(f"类比解释: {evaluated_decision.explanation.get('analogical_explanation', '')}")
    
    # 打印反事实解释
    if hasattr(evaluated_decision, "counterfactual_explanation"):
        logger.info("\n反事实解释:")
        for explanation in evaluated_decision.counterfactual_explanation.get('counterfactual_explanations', []):
            logger.info(f"- {explanation}")
    
    # 执行决策
    logger.info("\n执行决策...")
    result = decision_coordinator.execute_decision(decision.decision_id)
    
    logger.info(f"执行结果: {result}")
    
    # 打印解释质量
    if hasattr(evaluated_decision, "explanation_quality"):
        logger.info("\n解释质量:")
        logger.info(f"准确性: {evaluated_decision.explanation_quality.get('accuracy_score', 0):.2f}")
        logger.info(f"完整性: {evaluated_decision.explanation_quality.get('completeness_score', 0):.2f}")
        logger.info(f"一致性: {evaluated_decision.explanation_quality.get('consistency_score', 0):.2f}")
        logger.info(f"可理解性: {evaluated_decision.explanation_quality.get('understandability_score', 0):.2f}")
        logger.info(f"总体质量: {evaluated_decision.explanation_quality.get('overall_quality_score', 0):.2f}")
    
    # 创建多个决策
    logger.info("\n创建多个决策...")
    decisions = [evaluated_decision]
    
    for i in range(2):
        new_decision = decision_coordinator.create_decision(
            entity_id=f"{entity_id}_{i}",
            options=options,
            context=context,
            metadata=metadata
        )
        
        # 设置不同的权重
        new_decision.weights = {
            "option1": 0.3 + i * 0.1,
            "option2": 0.5 - i * 0.1,
            "option3": 0.2
        }
        
        # 评估决策
        new_evaluated_decision = decision_coordinator.evaluate_decision(new_decision)
        
        logger.info(f"决策 {i+1}: 选择选项={new_evaluated_decision.selected_option}, 置信度={new_evaluated_decision.confidence:.2f}")
        
        decisions.append(new_evaluated_decision)
    
    # 协调决策
    logger.info("\n协调决策...")
    coordinated_decision = decision_coordinator.coordinate_decisions(decisions)
    
    logger.info(f"协调结果: 选择选项={coordinated_decision.selected_option}, 置信度={coordinated_decision.confidence:.2f}")
    
    # 打印可视化
    if hasattr(coordinated_decision, "visualization"):
        logger.info("\n可视化:")
        for key, value in coordinated_decision.visualization.items():
            if isinstance(value, str) and len(value) < 100:
                logger.info(f"{key}: {value}")
            else:
                logger.info(f"{key}: [复杂可视化数据]")


def demonstrate_reasoning(reasoning_engine):
    """
    演示推理过程
    
    参数:
        reasoning_engine: 推理引擎
    """
    logger.info("\n" + "=" * 80)
    logger.info("演示推理过程")
    logger.info("=" * 80)
    
    # 准备输入数据
    input_data = {
        "facts": [
            "所有人都是凡人",
            "苏格拉底是人"
        ],
        "query": "苏格拉底是凡人吗？"
    }
    
    context = {
        "reasoning_type": "deductive",
        "confidence_threshold": 0.8
    }
    
    logger.info(f"输入数据: {input_data}")
    logger.info(f"上下文: {context}")
    
    # 执行推理
    logger.info("\n执行推理...")
    result = reasoning_engine.reason(input_data, context)
    
    # 打印推理结果
    logger.info(f"推理结果: {result}")
    
    # 打印验证结果
    if isinstance(result, dict) and "verification" in result:
        logger.info("\n验证结果:")
        verification = result["verification"]
        
        if "verification_results" in verification:
            for prop_id, prop_result in verification["verification_results"].items():
                logger.info(f"属性 {prop_id}: {prop_result.get('result', 'unknown')}, 置信度: {prop_result.get('confidence', 0):.2f}")
        
        if "verification_methods_used" in verification:
            logger.info(f"使用的验证方法: {verification['verification_methods_used']}")
    
    # 评估结果
    logger.info("\n评估结果...")
    evaluated_result = reasoning_engine.evaluate_result(result)
    
    logger.info(f"评估后的结果: {evaluated_result}")
    
    # 打印一致性结果
    if isinstance(evaluated_result, dict) and "consistency" in evaluated_result:
        logger.info("\n一致性结果:")
        consistency = evaluated_result["consistency"]
        
        logger.info(f"全局一致性: {consistency.get('global_consistency', False)}")
        logger.info(f"一致性分数: {consistency.get('consistency_score', 0):.2f}")
        
        if "inconsistencies" in consistency and consistency["inconsistencies"]:
            logger.info("检测到的不一致:")
            for inconsistency in consistency["inconsistencies"]:
                logger.info(f"  - {inconsistency}")
        else:
            logger.info("未检测到不一致")
    
    # 演示形式化验证
    if hasattr(reasoning_engine, "verify_system_model"):
        logger.info("\n演示形式化验证...")
        
        # 创建系统模型
        system_model = {
            "states": [
                {"id": "s0", "initial": True, "variables": {"x": 0, "y": 0}},
                {"id": "s1", "variables": {"x": 1, "y": 0}},
                {"id": "s2", "variables": {"x": 1, "y": 1}},
                {"id": "s3", "variables": {"x": 0, "y": 1}}
            ],
            "transitions": [
                {"from": "s0", "to": "s1", "action": "increment_x"},
                {"from": "s1", "to": "s2", "action": "increment_y"},
                {"from": "s2", "to": "s3", "action": "decrement_x"},
                {"from": "s3", "to": "s0", "action": "decrement_y"}
            ]
        }
        
        # 定义属性
        properties = [
            {
                "id": "safety",
                "type": "safety",
                "expression": "G(x >= 0 && y >= 0)"
            },
            {
                "id": "liveness",
                "type": "liveness",
                "expression": "F(x = 1 && y = 1)"
            }
        ]
        
        # 验证系统模型
        verification_result = reasoning_engine.verify_system_model(system_model, properties)
        
        # 打印验证结果
        logger.info("\n形式化验证结果:")
        
        if "verification_results" in verification_result:
            for prop_id, prop_result in verification_result["verification_results"].items():
                logger.info(f"属性 {prop_id}: {prop_result.get('result', 'unknown')}, 置信度: {prop_result.get('confidence', 0):.2f}")
                
                # 打印证明或反例
                if "proof" in prop_result:
                    logger.info(f"  证明: {prop_result['proof']['conclusion']}")
                elif "counterexample" in prop_result:
                    logger.info(f"  反例: {prop_result['counterexample']['property_violation']}")
        
        if "verification_methods_used" in verification_result:
            logger.info(f"使用的验证方法: {verification_result['verification_methods_used']}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='增强集成示例')
    parser.add_argument('--use-rust', action='store_true', help='使用Rust实现')
    args = parser.parse_args()
    
    # 创建集成后的思维引擎
    decision_coordinator, reasoning_engine = create_integrated_engine(use_rust=args.use_rust)
    
    # 演示决策制定过程
    demonstrate_decision_making(decision_coordinator)
    
    # 演示推理过程
    demonstrate_reasoning(reasoning_engine)


if __name__ == "__main__":
    main()
