"""
持久同调分析算法示例

本脚本展示了如何使用持久同调分析算法。
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import sys
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入我们的实现
from src.algorithms.topology.homology import PersistentHomologyAnalyzer
from src.algorithms.topology.simplicial import SimplicialComplexBuilder
from src.algorithms.topology.persistence import PersistenceCalculator
from src.algorithms.topology.features import TopologicalFeatureExtractor


def create_circle_points(n_points=100, radius=1.0, noise=0.1):
    """创建圆形点云数据"""
    theta = np.linspace(0, 2 * np.pi, n_points)
    x = radius * np.cos(theta) + noise * np.random.randn(n_points)
    y = radius * np.sin(theta) + noise * np.random.randn(n_points)
    
    return np.column_stack([x, y])


def create_torus_points(n_points=1000, R=2.0, r=1.0, noise=0.1):
    """创建环面点云数据"""
    theta = np.random.uniform(0, 2 * np.pi, n_points)
    phi = np.random.uniform(0, 2 * np.pi, n_points)
    
    x = (R + r * np.cos(phi)) * np.cos(theta) + noise * np.random.randn(n_points)
    y = (R + r * np.cos(phi)) * np.sin(theta) + noise * np.random.randn(n_points)
    z = r * np.sin(phi) + noise * np.random.randn(n_points)
    
    return np.column_stack([x, y, z])


def create_sphere_points(n_points=1000, radius=1.0, noise=0.1):
    """创建球面点云数据"""
    theta = np.random.uniform(0, np.pi, n_points)
    phi = np.random.uniform(0, 2 * np.pi, n_points)
    
    x = radius * np.sin(theta) * np.cos(phi) + noise * np.random.randn(n_points)
    y = radius * np.sin(theta) * np.sin(phi) + noise * np.random.randn(n_points)
    z = radius * np.cos(theta) + noise * np.random.randn(n_points)
    
    return np.column_stack([x, y, z])


def create_figure_eight_points(n_points=100, radius=1.0, noise=0.1):
    """创建8字形点云数据"""
    t = np.linspace(0, 2 * np.pi, n_points)
    x = np.sin(t) + noise * np.random.randn(n_points)
    y = np.sin(2 * t) + noise * np.random.randn(n_points)
    
    return np.column_stack([x, y])


def plot_point_cloud(points, title="点云数据"):
    """绘制点云数据"""
    fig = plt.figure(figsize=(10, 8))
    
    if points.shape[1] == 2:
        # 2D点云
        plt.scatter(points[:, 0], points[:, 1], alpha=0.7)
        plt.xlabel('X')
        plt.ylabel('Y')
    else:
        # 3D点云
        ax = fig.add_subplot(111, projection='3d')
        ax.scatter(points[:, 0], points[:, 1], points[:, 2], alpha=0.7)
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_zlabel('Z')
    
    plt.title(title)
    plt.tight_layout()
    
    return fig


def plot_persistence_diagram(persistence_diagram, title="持久图"):
    """绘制持久图"""
    fig, ax = plt.subplots(figsize=(10, 8))
    
    # 绘制对角线
    min_val = 0
    max_val = 0
    
    if persistence_diagram.size > 0:
        min_val = np.min(persistence_diagram[:, 0])
        max_val = np.max(persistence_diagram[:, 1][~np.isinf(persistence_diagram[:, 1])])
    
    diag_min = min_val - 0.1
    diag_max = max_val + 0.1
    
    ax.plot([diag_min, diag_max], [diag_min, diag_max], 'k--', alpha=0.5)
    
    # 绘制持久点
    if persistence_diagram.size > 0:
        # 有限持久点
        finite_points = persistence_diagram[~np.isinf(persistence_diagram[:, 1])]
        if finite_points.size > 0:
            ax.scatter(finite_points[:, 0], finite_points[:, 1], alpha=0.7)
        
        # 无限持久点
        inf_points = persistence_diagram[np.isinf(persistence_diagram[:, 1])]
        if inf_points.size > 0:
            ax.scatter(inf_points[:, 0], [diag_max] * len(inf_points), marker='^', alpha=0.7)
    
    ax.set_xlabel('出生')
    ax.set_ylabel('死亡')
    ax.set_title(title)
    
    # 设置坐标轴范围
    ax.set_xlim([diag_min, diag_max])
    ax.set_ylim([diag_min, diag_max])
    
    plt.tight_layout()
    
    return fig


def plot_betti_curves(betti_curves, filtration_values, title="贝蒂曲线"):
    """绘制贝蒂曲线"""
    fig, ax = plt.subplots(figsize=(10, 8))
    
    # 绘制每个维度的贝蒂曲线
    for dim, curve in betti_curves.items():
        ax.plot(filtration_values, curve, label=f'维度 {dim}')
    
    ax.set_xlabel('过滤值')
    ax.set_ylabel('贝蒂数')
    ax.set_title(title)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    return fig


def plot_persistence_landscape(landscape, filtration_values, title="持久景观"):
    """绘制持久景观"""
    fig, ax = plt.subplots(figsize=(10, 8))
    
    # 绘制每层景观
    for i in range(landscape.shape[0]):
        ax.plot(filtration_values, landscape[i], label=f'层 {i+1}')
    
    ax.set_xlabel('过滤值')
    ax.set_ylabel('景观值')
    ax.set_title(title)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    return fig


def analyze_point_cloud(points, shape_name, output_dir=None):
    """分析点云数据"""
    # 创建输出目录
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # 绘制点云数据
    fig_points = plot_point_cloud(points, title=f"{shape_name}点云数据")
    if output_dir:
        fig_points.savefig(os.path.join(output_dir, f"{shape_name}_points.png"))
    
    # 创建持久同调分析器
    analyzer = PersistentHomologyAnalyzer(
        max_dimension=2,
        max_radius=2.0,
        num_divisions=50,
        distance_metric='euclidean',
        use_parallel=True,
        num_workers=4,
        use_cache=True,
        cache_size=10
    )
    
    # 执行计算
    print(f"分析{shape_name}点云数据...")
    start_time = time.time()
    result = analyzer.compute(points)
    end_time = time.time()
    
    print(f"计算完成，耗时: {end_time - start_time:.2f}秒")
    
    # 提取结果
    persistence_diagrams = result['persistence_diagrams']
    betti_curves = result['betti_curves']
    persistence_landscapes = result['persistence_landscapes']
    topological_features = result['topological_features']
    performance = result['performance']
    
    # 打印性能指标
    print(f"性能指标:")
    print(f"  总时间: {performance['total_time']:.2f}秒")
    print(f"  复形构建时间: {performance['complex_construction_time']:.2f}秒")
    print(f"  持久性计算时间: {performance['persistence_calculation_time']:.2f}秒")
    print(f"  特征提取时间: {performance['feature_extraction_time']:.2f}秒")
    print(f"  内存使用: {performance['memory_usage']:.2f}MB")
    print(f"  单纯形数量: {performance['num_simplices']}")
    
    # 打印拓扑特征
    print(f"拓扑特征:")
    for dim in range(3):
        print(f"  维度{dim}:")
        print(f"    点数量: {topological_features.get(f'num_points_dim_{dim}', 0)}")
        print(f"    平均持久性: {topological_features.get(f'mean_persistence_dim_{dim}', 0):.4f}")
        print(f"    最大持久性: {topological_features.get(f'max_persistence_dim_{dim}', 0):.4f}")
        print(f"    持久性总和: {topological_features.get(f'sum_persistence_dim_{dim}', 0):.4f}")
        print(f"    无限持久点数量: {topological_features.get(f'num_inf_points_dim_{dim}', 0)}")
    
    # 生成过滤值序列
    filtration_values = np.linspace(0, 2.0, 50)
    
    # 绘制持久图
    for dim in range(3):
        if dim in persistence_diagrams:
            diagram = persistence_diagrams[dim]
            fig_diagram = plot_persistence_diagram(diagram, title=f"{shape_name} - 维度{dim}持久图")
            if output_dir:
                fig_diagram.savefig(os.path.join(output_dir, f"{shape_name}_persistence_diagram_dim{dim}.png"))
    
    # 绘制贝蒂曲线
    fig_betti = plot_betti_curves(betti_curves, filtration_values, title=f"{shape_name} - 贝蒂曲线")
    if output_dir:
        fig_betti.savefig(os.path.join(output_dir, f"{shape_name}_betti_curves.png"))
    
    # 绘制持久景观
    for dim in range(3):
        if dim in persistence_landscapes:
            landscape = persistence_landscapes[dim]
            fig_landscape = plot_persistence_landscape(
                landscape, filtration_values, title=f"{shape_name} - 维度{dim}持久景观"
            )
            if output_dir:
                fig_landscape.savefig(os.path.join(output_dir, f"{shape_name}_persistence_landscape_dim{dim}.png"))
    
    # 显示图形
    plt.show()
    
    return result


def compare_shapes(results, shape_names, output_dir=None):
    """比较不同形状的拓扑特征"""
    # 创建输出目录
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # 提取每个形状的拓扑特征
    features = {}
    for shape_name, result in zip(shape_names, results):
        features[shape_name] = result['topological_features']
    
    # 比较不同维度的贝蒂数
    for dim in range(3):
        # 创建图形
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # 获取每个形状的贝蒂数
        betti_values = []
        for shape_name in shape_names:
            betti_values.append(features[shape_name].get(f'max_betti_dim_{dim}', 0))
        
        # 绘制条形图
        ax.bar(shape_names, betti_values, alpha=0.7)
        
        # 设置图表属性
        ax.set_xlabel('形状')
        ax.set_ylabel(f'维度{dim}最大贝蒂数')
        ax.set_title(f'不同形状的维度{dim}最大贝蒂数比较')
        ax.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, v in enumerate(betti_values):
            ax.text(i, v + 0.1, f'{v:.1f}', ha='center')
        
        # 保存图形
        if output_dir:
            plt.savefig(os.path.join(output_dir, f"comparison_betti_dim{dim}.png"))
    
    # 比较不同维度的平均持久性
    for dim in range(3):
        # 创建图形
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # 获取每个形状的平均持久性
        persistence_values = []
        for shape_name in shape_names:
            persistence_values.append(features[shape_name].get(f'mean_persistence_dim_{dim}', 0))
        
        # 绘制条形图
        ax.bar(shape_names, persistence_values, alpha=0.7)
        
        # 设置图表属性
        ax.set_xlabel('形状')
        ax.set_ylabel(f'维度{dim}平均持久性')
        ax.set_title(f'不同形状的维度{dim}平均持久性比较')
        ax.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, v in enumerate(persistence_values):
            ax.text(i, v + 0.01, f'{v:.4f}', ha='center')
        
        # 保存图形
        if output_dir:
            plt.savefig(os.path.join(output_dir, f"comparison_persistence_dim{dim}.png"))
    
    # 显示图形
    plt.show()


def main():
    """主函数"""
    # 设置随机种子，确保结果可重现
    np.random.seed(42)
    
    # 创建输出目录
    output_dir = 'output/topology'
    
    # 创建不同形状的点云数据
    circle_points = create_circle_points(n_points=100, radius=1.0, noise=0.05)
    figure_eight_points = create_figure_eight_points(n_points=200, radius=1.0, noise=0.05)
    sphere_points = create_sphere_points(n_points=500, radius=1.0, noise=0.05)
    torus_points = create_torus_points(n_points=1000, R=2.0, r=1.0, noise=0.05)
    
    # 分析每个点云数据
    circle_result = analyze_point_cloud(circle_points, "圆形", output_dir)
    figure_eight_result = analyze_point_cloud(figure_eight_points, "8字形", output_dir)
    sphere_result = analyze_point_cloud(sphere_points, "球面", output_dir)
    torus_result = analyze_point_cloud(torus_points, "环面", output_dir)
    
    # 比较不同形状的拓扑特征
    compare_shapes(
        [circle_result, figure_eight_result, sphere_result, torus_result],
        ["圆形", "8字形", "球面", "环面"],
        output_dir
    )
    
    print("示例完成！")


if __name__ == "__main__":
    main()
