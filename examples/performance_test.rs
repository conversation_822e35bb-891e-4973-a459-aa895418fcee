// 超越态张量性能测试示例
//
// 本示例用于演示超越态张量框架的性能优化效果，包括内存池、SIMD和并行计算优化。

use tte_operators::tensor::{
    TranscendentalTensor, TensorConfig, Device, Precision,
    benchmark::create_benchmark_tensor,
};
use tte_operators::memory_pool;
use ndarray::ArrayD;
use num::complex::Complex64;
use std::time::{Duration, Instant};

fn main() {
    println!("超越态张量性能测试");
    println!("==================\n");
    
    // 测试不同大小的张量
    let sizes = vec![
        vec![10, 10],   // 小型张量
        vec![50, 50],   // 中型张量
        vec![100, 100], // 大型张量
    ];
    
    println!("测试配置:");
    println!("  - CPU核心数: {}", num_cpus::get());
    println!("  - 迭代次数: 5");
    println!("  - 测试大小: {:?}", sizes);
    println!();
    
    println!("性能测试结果:");
    println!("大小\t\t标准时间\t优化时间\t加速比");
    println!("----------------------------------------");
    
    for size in &sizes {
        // 创建测试张量
        let tensor = create_benchmark_tensor(
            size,
            Device::CPU,
            Precision::Double,
        ).unwrap();
        
        // 创建哈密顿算子
        let data = tensor.data.read().unwrap();
        let hamiltonian = ArrayD::from_elem(
            data.raw_dim(),
            Complex64::new(1.0, 0.0)
        );
        
        // 预热内存池
        memory_pool::GLOBAL_TENSOR_POOL.warm_up(5, size).unwrap();
        memory_pool::GLOBAL_TENSOR_POOL.reset_stats();
        
        // 预热
        let _ = tensor.evolve(&hamiltonian, 0.01).unwrap();
        let _ = tensor.parallel_evolve_simd(&hamiltonian, 0.01).unwrap();
        
        // 测量标准方法性能
        let iterations = 5;
        let start = Instant::now();
        let mut last_tensor = tensor.clone();
        
        for _ in 0..iterations {
            let evolved = last_tensor.evolve(&hamiltonian, 0.01).unwrap();
            last_tensor = evolved;
        }
        
        let standard_time = start.elapsed();
        let standard_time_ms = standard_time.as_secs_f64() * 1000.0;
        
        // 测量优化方法性能
        let start = Instant::now();
        let mut last_tensor = tensor.clone();
        
        for _ in 0..iterations {
            let evolved = last_tensor.parallel_evolve_simd(&hamiltonian, 0.01).unwrap();
            last_tensor = evolved;
        }
        
        let optimized_time = start.elapsed();
        let optimized_time_ms = optimized_time.as_secs_f64() * 1000.0;
        
        // 计算加速比
        let speedup = standard_time_ms / optimized_time_ms;
        
        println!("{:?}\t{:.2}ms\t\t{:.2}ms\t\t{:.2}x", 
            size, standard_time_ms, optimized_time_ms, speedup);
    }
    
    // 获取内存池统计
    let stats = memory_pool::GLOBAL_TENSOR_POOL.get_stats();
    
    println!("\n内存池统计:");
    println!("  - 分配: {}", stats.allocated);
    println!("  - 回收: {}", stats.recycled);
    println!("  - 命中率: {:.2}%", stats.hit_rate * 100.0);
    println!("  - 当前池大小: {}", stats.current_pool_size);
    
    println!("\n测试完成!");
}
