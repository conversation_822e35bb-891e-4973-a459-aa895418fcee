// 超越态张量自动优化示例
//
// 本示例展示如何使用自动优化选择器和MRCAM性能分析工具，
// 根据不同场景自动选择最佳优化策略。

use tte_operators::tensor::{
    TranscendentalTensor, TensorConfig, Device, Precision,
    benchmark::create_benchmark_tensor,
    auto_optimizer::{OptimizationStrategy, OptimizationConfig, ProfilingResult},
};
use tte_operators::memory_pool;
use ndarray::ArrayD;
use num::complex::Complex64;
use std::time::{Duration, Instant};

// 使用MRCAM多维度反向追溯模型进行性能分析
fn analyze_performance(tensor_size: &[usize], iterations: usize) {
    println!("===== MRCAM性能分析 =====");
    println!("张量大小: {:?}, 迭代次数: {}", tensor_size, iterations);
    
    // 创建测试张量
    let tensor = create_benchmark_tensor(
        tensor_size,
        Device::CPU,
        Precision::Double,
    ).unwrap();
    
    // 创建哈密顿算子
    let data = tensor.data.read().unwrap();
    let hamiltonian = ArrayD::from_elem(
        data.raw_dim(),
        Complex64::new(1.0, 0.0)
    );
    
    // 测试不同优化策略
    let strategies = vec![
        OptimizationStrategy::Standard,
        OptimizationStrategy::MemoryPool,
        OptimizationStrategy::Simd,
        OptimizationStrategy::Parallel,
        OptimizationStrategy::Comprehensive,
        OptimizationStrategy::Auto,
    ];
    
    println!("\n优化策略性能比较:");
    println!("策略\t\t执行时间(ms)\t加速比\t内存池命中率");
    println!("--------------------------------------------------");
    
    // 先运行标准策略作为基准
    let standard_config = OptimizationConfig {
        strategy: OptimizationStrategy::Standard,
        memory_pool_warm_up_size: 0,
        parallel_threads: 0,
        enable_profiling: true,
    };
    
    let (_, standard_profile) = tensor.evolve_optimized_multiple(
        &hamiltonian,
        0.01,
        iterations,
        standard_config,
    ).unwrap();
    
    let standard_time = if let Some(profile) = &standard_profile {
        profile.execution_time_ms
    } else {
        0.0
    };
    
    println!("标准\t\t{:.2}\t\t1.00x\t\tN/A", standard_time);
    
    // 测试其他策略
    for strategy in &strategies[1..] {
        // 重置内存池统计
        memory_pool::GLOBAL_TENSOR_POOL.reset_stats();
        
        let config = OptimizationConfig {
            strategy: *strategy,
            memory_pool_warm_up_size: 10,
            parallel_threads: 0, // 使用默认值
            enable_profiling: true,
        };
        
        // 预热
        let _ = tensor.evolve_optimized(&hamiltonian, 0.01, config.clone()).unwrap();
        
        // 执行测试
        let (_, profile) = tensor.evolve_optimized_multiple(
            &hamiltonian,
            0.01,
            iterations,
            config,
        ).unwrap();
        
        if let Some(profile) = profile {
            let speedup = standard_time / profile.execution_time_ms;
            let hit_rate = if let Some(rate) = profile.memory_pool_hit_rate {
                format!("{:.2}%", rate * 100.0)
            } else {
                "N/A".to_string()
            };
            
            println!("{:?}\t{:.2}\t\t{:.2}x\t\t{}", 
                profile.strategy, profile.execution_time_ms, speedup, hit_rate);
        }
    }
    
    // 进行MRCAM多维度分析
    println!("\n===== MRCAM多维度分析 =====");
    
    // 1. 计算性能维度分析
    println!("\n1. 计算性能维度分析:");
    let size_product = tensor_size.iter().product::<usize>();
    
    if size_product < 1000 {
        println!("  - 小型张量 ({}): 计算负载轻，优化收益有限", size_product);
        println!("  - 建议: 对于小型张量，使用内存池优化即可");
    } else if size_product < 10000 {
        println!("  - 中型张量 ({}): 计算负载中等，SIMD优化效果显著", size_product);
        println!("  - 建议: 使用内存池+SIMD优化组合");
    } else {
        println!("  - 大型张量 ({}): 计算负载重，综合优化效果最佳", size_product);
        println!("  - 建议: 使用综合优化策略（内存池+SIMD+并行）");
    }
    
    // 2. 内存效率维度分析
    println!("\n2. 内存效率维度分析:");
    let stats = memory_pool::GLOBAL_TENSOR_POOL.get_stats();
    println!("  - 内存池命中率: {:.2}%", stats.hit_rate * 100.0);
    println!("  - 分配次数: {}", stats.allocated);
    println!("  - 回收次数: {}", stats.recycled);
    
    if stats.hit_rate < 0.5 {
        println!("  - 建议: 增加内存池预热大小，提高命中率");
    } else {
        println!("  - 建议: 当前内存池配置良好");
    }
    
    // 3. 并行扩展性维度分析
    println!("\n3. 并行扩展性维度分析:");
    let num_cores = num_cpus::get();
    println!("  - 可用CPU核心数: {}", num_cores);
    
    if num_cores > 1 && size_product > 1000 {
        println!("  - 建议: 启用并行计算，设置线程数为CPU核心数");
    } else if num_cores == 1 {
        println!("  - 建议: 单核环境下不建议使用并行计算");
    } else {
        println!("  - 建议: 小型张量不建议使用并行计算，开销大于收益");
    }
    
    // 综合建议
    println!("\n===== 综合优化建议 =====");
    let best_strategy = if size_product > 10000 && num_cores > 1 {
        "综合优化 (Comprehensive)"
    } else if size_product > 1000 {
        "内存池+SIMD优化 (MemoryPoolSimd)"
    } else {
        "内存池优化 (MemoryPool)"
    };
    
    println!("最佳优化策略: {}", best_strategy);
    println!("推荐内存池预热大小: {}", if size_product > 10000 { 20 } else if size_product > 1000 { 10 } else { 5 });
    println!("推荐并行线程数: {}", if size_product > 10000 && num_cores > 1 { num_cores } else { 1 });
}

fn main() {
    println!("超越态张量自动优化示例");
    println!("====================\n");
    
    // 测试不同大小的张量
    let sizes = vec![
        vec![10, 10],   // 小型张量
        vec![50, 50],   // 中型张量
        vec![100, 100], // 大型张量
    ];
    
    for size in &sizes {
        analyze_performance(size, 5);
        println!("\n");
    }
    
    // 演示自动优化选择器的使用
    println!("===== 自动优化选择器演示 =====");
    
    // 创建中型张量
    let tensor = create_benchmark_tensor(
        &[50, 50],
        Device::CPU,
        Precision::Double,
    ).unwrap();
    
    // 创建哈密顿算子
    let data = tensor.data.read().unwrap();
    let hamiltonian = ArrayD::from_elem(
        data.raw_dim(),
        Complex64::new(1.0, 0.0)
    );
    
    // 使用自动优化策略
    println!("\n使用自动优化策略执行演化计算:");
    let config = OptimizationConfig {
        strategy: OptimizationStrategy::Auto,
        memory_pool_warm_up_size: 10,
        parallel_threads: 0, // 使用默认值
        enable_profiling: true,
    };
    
    let start = Instant::now();
    let (evolved, profile) = tensor.evolve_optimized_multiple(
        &hamiltonian,
        0.01,
        10,
        config,
    ).unwrap();
    
    if let Some(profile) = profile {
        println!("自动选择的策略: {:?}", profile.strategy);
        println!("执行时间: {:.2}ms", profile.execution_time_ms);
        if let Some(rate) = profile.memory_pool_hit_rate {
            println!("内存池命中率: {:.2}%", rate * 100.0);
        }
    }
    
    println!("\n测试完成!");
}
