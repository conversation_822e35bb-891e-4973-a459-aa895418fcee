"""
示例算子模块

本模块提供了一些示例算子，用于测试算子注册表。
"""

def add(a, b):
    """加法算子"""
    return a + b

def multiply(a, b):
    """乘法算子"""
    return a * b

def divide(a, b):
    """除法算子"""
    if b == 0:
        raise ValueError("除数不能为零")
    return a / b

def power(a, b):
    """幂运算算子"""
    return a ** b

class Calculator:
    """计算器类"""
    
    def __init__(self):
        """初始化计算器"""
        self.history = []
        
    def add(self, a, b):
        """加法"""
        result = a + b
        self.history.append((a, b, result, "add"))
        return result
        
    def subtract(self, a, b):
        """减法"""
        result = a - b
        self.history.append((a, b, result, "subtract"))
        return result
        
    def multiply(self, a, b):
        """乘法"""
        result = a * b
        self.history.append((a, b, result, "multiply"))
        return result
        
    def divide(self, a, b):
        """除法"""
        if b == 0:
            raise ValueError("除数不能为零")
        result = a / b
        self.history.append((a, b, result, "divide"))
        return result
        
    def get_history(self):
        """获取历史记录"""
        return self.history
