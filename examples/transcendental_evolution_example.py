"""
超越态演化模拟器示例

本脚本展示了如何使用超越态演化模拟器。
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import sys
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入我们的实现
from src.algorithms.transcendental_evolution import (
    TranscendentalStateEvolver,
    TranscendentalState,
    TranscendentalOperator,
    OperatorFactory,
    TranscendentalAnalyzer
)


def create_initial_state(dimension=10, state_type='standard', seed=None):
    """创建初始超越态"""
    # 设置随机数种子
    if seed is not None:
        np.random.seed(seed)
    
    # 创建随机复数向量
    real_part = np.random.normal(0, 1, dimension)
    imag_part = np.random.normal(0, 1, dimension)
    data = real_part + 1j * imag_part
    
    # 归一化
    data = data / np.linalg.norm(data)
    
    # 创建超越态
    return TranscendentalState(
        data=data,
        state_type=state_type,
        coherence=1.0,
        distribution=0.8,
        nonlinearity=0.5,
        emergence=0.3
    )


def create_operator(dimension=10, operator_type='coherence', seed=None):
    """创建超越态算子"""
    # 创建算子工厂
    factory = OperatorFactory()
    
    # 设置随机数种子
    if seed is not None:
        np.random.seed(seed)
    
    # 创建随机厄米矩阵
    operator = factory.create_random_hermitian(
        dimension=dimension,
        seed=seed,
        metadata={'name': f'random_{operator_type}_operator'}
    )
    
    return operator


def run_evolution_simulation(initial_state, operator, evolution_method='standard', 
                           integration_method='rk4', total_time=1.0, time_step=0.01,
                           coherence_factor=1.0, distribution_factor=0.5, 
                           nonlinearity_factor=0.2, emergence_factor=0.1):
    """运行超越态演化模拟"""
    # 创建演化器
    evolver = TranscendentalStateEvolver(
        evolution_method=evolution_method,
        integration_method=integration_method,
        time_step=time_step,
        use_parallel=True,
        num_workers=4,
        use_cache=True,
        cache_size=10
    )
    
    # 创建算子工厂
    factory = OperatorFactory()
    
    # 创建可观测量
    observables = {
        'energy': operator.to_array(),
        'x': factory.create_pauli_x().to_array(),
        'y': factory.create_pauli_y().to_array(),
        'z': factory.create_pauli_z().to_array()
    }
    
    # 创建输入数据
    input_data = {
        'state': initial_state,
        'operator': operator.to_array(),
        'coherence_factor': coherence_factor,
        'distribution_factor': distribution_factor,
        'nonlinearity_factor': nonlinearity_factor,
        'emergence_factor': emergence_factor
    }
    
    # 执行演化模拟
    print(f"执行超越态演化模拟...")
    print(f"  演化方法: {evolution_method}")
    print(f"  积分方法: {integration_method}")
    print(f"  总时间: {total_time}")
    print(f"  时间步长: {time_step}")
    print(f"  相干性因子: {coherence_factor}")
    print(f"  分布性因子: {distribution_factor}")
    print(f"  非线性因子: {nonlinearity_factor}")
    print(f"  涌现性因子: {emergence_factor}")
    
    start_time = time.time()
    result = evolver.compute(
        input_data,
        total_time=total_time,
        observables=observables
    )
    end_time = time.time()
    
    print(f"模拟完成，耗时: {end_time - start_time:.2f}秒")
    
    # 提取结果
    final_state = result['final_state']
    state_history = result['state_history']
    time_points = result['time_points']
    observables_history = result['observables_history']
    analysis = result['analysis']
    performance = result['performance']
    
    # 打印性能指标
    print(f"性能指标:")
    print(f"  总时间: {performance['total_time']:.2f}秒")
    print(f"  演化时间: {performance['evolution_time']:.2f}秒")
    print(f"  分析时间: {performance['analysis_time']:.2f}秒")
    print(f"  内存使用: {performance['memory_usage']:.2f}MB")
    print(f"  数值误差: {analysis['numerical_error']:.6f}")
    print(f"  能量守恒: {analysis['energy_conservation']:.6f}")
    print(f"  范数守恒: {analysis['norm_conservation']:.6f}")
    
    # 创建分析器
    analyzer = TranscendentalAnalyzer()
    
    # 绘制演化结果
    fig = analyzer.plot_evolution(
        state_history=state_history,
        time_points=time_points,
        observables_history=observables_history,
        analysis_result=analysis
    )
    
    # 保存图形
    os.makedirs('output', exist_ok=True)
    fig.savefig(f'output/transcendental_evolution_{evolution_method}.png')
    
    # 绘制3D图
    fig_3d = analyzer.plot_state_evolution_3d(
        state_history=state_history,
        time_points=time_points
    )
    
    # 保存3D图
    fig_3d.savefig(f'output/transcendental_evolution_3d_{evolution_method}.png')
    
    return result


def compare_evolution_methods(initial_state, operator, total_time=1.0, time_step=0.01):
    """比较不同的演化方法"""
    # 演化方法列表
    evolution_methods = ['standard', 'computational', 'creative']
    
    # 存储结果
    results = {}
    
    # 对每种演化方法执行模拟
    for method in evolution_methods:
        print(f"\n===== 使用{method}演化方法 =====")
        result = run_evolution_simulation(
            initial_state=initial_state,
            operator=operator,
            evolution_method=method,
            total_time=total_time,
            time_step=time_step
        )
        results[method] = result
    
    # 比较不同演化方法的结果
    plt.figure(figsize=(12, 8))
    
    # 绘制能量期望值
    plt.subplot(2, 2, 1)
    for method in evolution_methods:
        plt.plot(
            results[method]['time_points'],
            results[method]['observables_history']['energy'],
            label=f"{method}"
        )
    plt.xlabel('时间')
    plt.ylabel('能量期望值')
    plt.title('能量期望值演化')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 绘制熵演化
    plt.subplot(2, 2, 2)
    for method in evolution_methods:
        plt.plot(
            results[method]['time_points'],
            results[method]['analysis']['entropy_evolution'],
            label=f"{method}"
        )
    plt.xlabel('时间')
    plt.ylabel('熵')
    plt.title('熵演化')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 绘制相干性演化
    plt.subplot(2, 2, 3)
    for method in evolution_methods:
        plt.plot(
            results[method]['time_points'],
            results[method]['analysis']['coherence_evolution'],
            label=f"{method}"
        )
    plt.xlabel('时间')
    plt.ylabel('相干性')
    plt.title('相干性演化')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 绘制涌现性演化
    plt.subplot(2, 2, 4)
    for method in evolution_methods:
        plt.plot(
            results[method]['time_points'],
            results[method]['analysis']['emergence_evolution'],
            label=f"{method}"
        )
    plt.xlabel('时间')
    plt.ylabel('涌现性')
    plt.title('涌现性演化')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图形
    plt.savefig('output/evolution_methods_comparison.png')
    
    # 显示图形
    plt.show()


def compare_integration_methods(initial_state, operator, total_time=1.0, time_step=0.01):
    """比较不同的积分方法"""
    # 积分方法列表
    integration_methods = ['euler', 'rk4', 'adaptive_rk45']
    
    # 存储结果
    results = {}
    
    # 对每种积分方法执行模拟
    for method in integration_methods:
        print(f"\n===== 使用{method}积分方法 =====")
        result = run_evolution_simulation(
            initial_state=initial_state,
            operator=operator,
            integration_method=method,
            total_time=total_time,
            time_step=time_step
        )
        results[method] = result
    
    # 比较不同积分方法的结果
    plt.figure(figsize=(12, 8))
    
    # 绘制能量期望值
    plt.subplot(2, 2, 1)
    for method in integration_methods:
        plt.plot(
            results[method]['time_points'],
            results[method]['observables_history']['energy'],
            label=f"{method}"
        )
    plt.xlabel('时间')
    plt.ylabel('能量期望值')
    plt.title('能量期望值演化')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 绘制数值误差
    plt.subplot(2, 2, 2)
    errors = [results[method]['analysis']['numerical_error'] for method in integration_methods]
    plt.bar(integration_methods, errors)
    plt.xlabel('积分方法')
    plt.ylabel('数值误差')
    plt.title('数值误差比较')
    plt.grid(True, alpha=0.3)
    
    # 绘制能量守恒
    plt.subplot(2, 2, 3)
    conservation = [results[method]['analysis']['energy_conservation'] for method in integration_methods]
    plt.bar(integration_methods, conservation)
    plt.xlabel('积分方法')
    plt.ylabel('能量守恒')
    plt.title('能量守恒比较')
    plt.grid(True, alpha=0.3)
    
    # 绘制计算时间
    plt.subplot(2, 2, 4)
    times = [results[method]['performance']['evolution_time'] for method in integration_methods]
    plt.bar(integration_methods, times)
    plt.xlabel('积分方法')
    plt.ylabel('计算时间 (秒)')
    plt.title('计算时间比较')
    plt.grid(True, alpha=0.3)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图形
    plt.savefig('output/integration_methods_comparison.png')
    
    # 显示图形
    plt.show()


def explore_parameter_space(initial_state, operator, total_time=1.0, time_step=0.01):
    """探索参数空间"""
    # 参数列表
    coherence_factors = [0.5, 1.0, 1.5]
    distribution_factors = [0.2, 0.5, 0.8]
    nonlinearity_factors = [0.1, 0.2, 0.3]
    emergence_factors = [0.05, 0.1, 0.2]
    
    # 存储结果
    results = {}
    
    # 对相干性因子进行探索
    for factor in coherence_factors:
        print(f"\n===== 相干性因子: {factor} =====")
        result = run_evolution_simulation(
            initial_state=initial_state,
            operator=operator,
            total_time=total_time,
            time_step=time_step,
            coherence_factor=factor
        )
        results[f'coherence_{factor}'] = result
    
    # 对分布性因子进行探索
    for factor in distribution_factors:
        print(f"\n===== 分布性因子: {factor} =====")
        result = run_evolution_simulation(
            initial_state=initial_state,
            operator=operator,
            total_time=total_time,
            time_step=time_step,
            distribution_factor=factor
        )
        results[f'distribution_{factor}'] = result
    
    # 对非线性因子进行探索
    for factor in nonlinearity_factors:
        print(f"\n===== 非线性因子: {factor} =====")
        result = run_evolution_simulation(
            initial_state=initial_state,
            operator=operator,
            total_time=total_time,
            time_step=time_step,
            nonlinearity_factor=factor
        )
        results[f'nonlinearity_{factor}'] = result
    
    # 对涌现性因子进行探索
    for factor in emergence_factors:
        print(f"\n===== 涌现性因子: {factor} =====")
        result = run_evolution_simulation(
            initial_state=initial_state,
            operator=operator,
            total_time=total_time,
            time_step=time_step,
            emergence_factor=factor
        )
        results[f'emergence_{factor}'] = result
    
    # 比较不同参数的结果
    # 相干性因子比较
    plt.figure(figsize=(12, 8))
    for factor in coherence_factors:
        plt.plot(
            results[f'coherence_{factor}']['time_points'],
            results[f'coherence_{factor}']['observables_history']['energy'],
            label=f"相干性因子 = {factor}"
        )
    plt.xlabel('时间')
    plt.ylabel('能量期望值')
    plt.title('不同相干性因子的能量期望值演化')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig('output/coherence_factor_comparison.png')
    
    # 分布性因子比较
    plt.figure(figsize=(12, 8))
    for factor in distribution_factors:
        plt.plot(
            results[f'distribution_{factor}']['time_points'],
            results[f'distribution_{factor}']['analysis']['entropy_evolution'],
            label=f"分布性因子 = {factor}"
        )
    plt.xlabel('时间')
    plt.ylabel('熵')
    plt.title('不同分布性因子的熵演化')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig('output/distribution_factor_comparison.png')
    
    # 非线性因子比较
    plt.figure(figsize=(12, 8))
    for factor in nonlinearity_factors:
        plt.plot(
            results[f'nonlinearity_{factor}']['time_points'],
            results[f'nonlinearity_{factor}']['analysis']['nonlinearity_evolution'],
            label=f"非线性因子 = {factor}"
        )
    plt.xlabel('时间')
    plt.ylabel('非线性')
    plt.title('不同非线性因子的非线性演化')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig('output/nonlinearity_factor_comparison.png')
    
    # 涌现性因子比较
    plt.figure(figsize=(12, 8))
    for factor in emergence_factors:
        plt.plot(
            results[f'emergence_{factor}']['time_points'],
            results[f'emergence_{factor}']['analysis']['emergence_evolution'],
            label=f"涌现性因子 = {factor}"
        )
    plt.xlabel('时间')
    plt.ylabel('涌现性')
    plt.title('不同涌现性因子的涌现性演化')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig('output/emergence_factor_comparison.png')
    
    # 显示图形
    plt.show()


def main():
    """主函数"""
    # 设置随机数种子，确保结果可重现
    np.random.seed(42)
    
    # 创建输出目录
    os.makedirs('output', exist_ok=True)
    
    # 创建初始超越态
    dimension = 10
    initial_state = create_initial_state(dimension=dimension, seed=42)
    
    # 创建超越态算子
    operator = create_operator(dimension=dimension, seed=42)
    
    # 运行单个演化模拟
    print("\n===== 运行单个演化模拟 =====")
    run_evolution_simulation(
        initial_state=initial_state,
        operator=operator,
        total_time=1.0,
        time_step=0.01
    )
    
    # 比较不同的演化方法
    print("\n===== 比较不同的演化方法 =====")
    compare_evolution_methods(
        initial_state=initial_state,
        operator=operator,
        total_time=1.0,
        time_step=0.01
    )
    
    # 比较不同的积分方法
    print("\n===== 比较不同的积分方法 =====")
    compare_integration_methods(
        initial_state=initial_state,
        operator=operator,
        total_time=1.0,
        time_step=0.01
    )
    
    # 探索参数空间
    print("\n===== 探索参数空间 =====")
    explore_parameter_space(
        initial_state=initial_state,
        operator=operator,
        total_time=1.0,
        time_step=0.01
    )
    
    print("\n示例完成！")


if __name__ == "__main__":
    main()
