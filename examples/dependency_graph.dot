digraph DependencyGraph {
  "utility.add@1.0.0" [label="utility.add\n1.0.0"];
  "utility.multiply@1.0.0" [label="utility.multiply\n1.0.0"];
  "utility.divide@1.0.0" [label="utility.divide\n1.0.0"];
  "utility.power@1.0.0" [label="utility.power\n1.0.0"];
  "utility.calculator@1.0.0" [label="utility.calculator\n1.0.0"];
  "utility.divide@1.0.0" -> "utility.multiply@1.0.0" [label=">=1.0.0"];
  "utility.power@1.0.0" -> "utility.multiply@1.0.0" [label=">=1.0.0"];
  "utility.calculator@1.0.0" -> "utility.add@1.0.0" [label=">=1.0.0"];
  "utility.calculator@1.0.0" -> "utility.multiply@1.0.0" [label=">=1.0.0"];
}
