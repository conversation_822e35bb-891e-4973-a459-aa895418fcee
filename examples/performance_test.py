"""
性能测试脚本

本脚本测试API版本适配器和错误处理器的性能。
"""

import os
import sys
import logging
import time
import random
import statistics
from typing import List, Dict, Any, Callable

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入API版本适配器示例中的简化版API版本适配器
from examples.api_version_adapter_example import (
    SimpleAPIVersionDetector,
    SimpleAPIVersionAdapter,
    adapt_data_structure_v1_to_v2
)

# 导入错误处理器相关模块
try:
    from src.interfaces.error_codes import (
        ErrorSeverity, ErrorCategory, ErrorCode, ErrorCodeRegistry,
        get_registry, SYSTEM_INTERNAL_ERROR, VALIDATION_INVALID_INPUT
    )
    from src.interfaces.error_handler import (
        TTError, ErrorHandler, get_error_handler, handle_errors
    )
    from src.interfaces.recovery_strategies import (
        retry_strategy, fallback_strategy, cache_strategy, degradation_strategy,
        timeout_strategy, CircuitBreaker, CircuitBreakerRegistry, circuit_breaker_strategy,
        get_circuit_breaker_registry, register_default_recovery_strategies
    )
    logger.info("成功导入错误处理器相关模块")
except ImportError as e:
    logger.error(f"导入错误处理器相关模块失败: {e}")
    sys.exit(1)

def measure_execution_time(func: Callable, *args, **kwargs) -> float:
    """
    测量函数执行时间

    参数:
        func (Callable): 要测量的函数
        *args: 函数的位置参数
        **kwargs: 函数的关键字参数

    返回:
        float: 执行时间（秒）
    """
    start_time = time.time()
    func(*args, **kwargs)
    end_time = time.time()
    return end_time - start_time

def run_benchmark(func: Callable, iterations: int = 1000, *args, **kwargs) -> Dict[str, Any]:
    """
    运行基准测试

    参数:
        func (Callable): 要测试的函数
        iterations (int, optional): 迭代次数
        *args: 函数的位置参数
        **kwargs: 函数的关键字参数

    返回:
        Dict[str, Any]: 基准测试结果
    """
    execution_times = []

    for _ in range(iterations):
        execution_time = measure_execution_time(func, *args, **kwargs)
        execution_times.append(execution_time)

    return {
        "iterations": iterations,
        "total_time": sum(execution_times),
        "min_time": min(execution_times),
        "max_time": max(execution_times),
        "avg_time": statistics.mean(execution_times),
        "median_time": statistics.median(execution_times),
        "std_dev": statistics.stdev(execution_times) if len(execution_times) > 1 else 0
    }

def test_api_version_adapter_performance():
    """测试API版本适配器性能"""
    logger.info("开始测试API版本适配器性能...")

    # 创建API版本适配器
    adapter = SimpleAPIVersionAdapter("1.0", "2.0", "test_adapter")

    # 注册版本模式
    adapter.register_version_pattern("1.0", r"1\.0(\.\d+)?")
    adapter.register_version_pattern("2.0", r"2\.0(\.\d+)?")

    # 注册适配函数
    adapter.register_adapter("1.0", "2.0", adapt_data_structure_v1_to_v2)

    # 创建测试数据
    test_data = {
        "name": "test",
        "value": 123,
        "options": ["option1", "option2"]
    }

    # 测试适配性能
    def adapt_data():
        adapter.adapt(test_data)

    # 运行基准测试
    results = run_benchmark(adapt_data, iterations=1000)

    # 输出结果
    logger.info(f"API版本适配器性能测试结果:")
    logger.info(f"  迭代次数: {results['iterations']}")
    logger.info(f"  总时间: {results['total_time']:.6f} 秒")
    logger.info(f"  最小时间: {results['min_time']:.6f} 秒")
    logger.info(f"  最大时间: {results['max_time']:.6f} 秒")
    logger.info(f"  平均时间: {results['avg_time']:.6f} 秒")
    logger.info(f"  中位数时间: {results['median_time']:.6f} 秒")
    logger.info(f"  标准差: {results['std_dev']:.6f} 秒")
    logger.info(f"  每秒处理请求数: {results['iterations'] / results['total_time']:.2f}")

    return results

def test_error_handler_performance():
    """测试错误处理器性能"""
    logger.info("开始测试错误处理器性能...")

    # 获取错误处理器
    error_handler = get_error_handler()

    # 注册错误处理函数
    def dummy_handler(error):
        pass

    error_handler.register_handler(ErrorSeverity.ERROR, dummy_handler)

    # 注册错误恢复策略
    error_handler.register_recovery_strategy(
        VALIDATION_INVALID_INPUT.code,
        fallback_strategy("fallback")
    )

    # 创建测试函数
    @handle_errors(default_return="default", reraise=False, recovery=True)
    def test_function(raise_error=True):
        if raise_error:
            raise TTError(VALIDATION_INVALID_INPUT, input_name="test")
        return "success"

    # 测试错误处理性能
    def handle_error():
        test_function(raise_error=True)

    # 运行基准测试
    results = run_benchmark(handle_error, iterations=100)

    # 输出结果
    logger.info(f"错误处理器性能测试结果:")
    logger.info(f"  迭代次数: {results['iterations']}")
    logger.info(f"  总时间: {results['total_time']:.6f} 秒")
    logger.info(f"  最小时间: {results['min_time']:.6f} 秒")
    logger.info(f"  最大时间: {results['max_time']:.6f} 秒")
    logger.info(f"  平均时间: {results['avg_time']:.6f} 秒")
    logger.info(f"  中位数时间: {results['median_time']:.6f} 秒")
    logger.info(f"  标准差: {results['std_dev']:.6f} 秒")
    logger.info(f"  每秒处理错误数: {results['iterations'] / results['total_time']:.2f}")

    return results

def main():
    """主函数"""
    logger.info("开始性能测试...")

    # 测试API版本适配器性能
    api_adapter_results = test_api_version_adapter_performance()

    # 测试错误处理器性能
    error_handler_results = test_error_handler_performance()

    # 比较结果
    logger.info("性能比较:")
    logger.info(f"  API版本适配器每秒处理请求数: {api_adapter_results['iterations'] / api_adapter_results['total_time']:.2f}")
    logger.info(f"  错误处理器每秒处理错误数: {error_handler_results['iterations'] / error_handler_results['total_time']:.2f}")

    logger.info("性能测试结束")

if __name__ == "__main__":
    main()
