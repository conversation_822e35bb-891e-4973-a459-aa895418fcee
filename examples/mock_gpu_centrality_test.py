#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超越态思维引擎 - 使用模拟GPU加速器测试中心节点识别算子
"""

import os
import sys
import time
import random
import numpy as np
import logging
from typing import Dict, Any, List, Tuple, Optional, Union

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入模拟GPU加速器
try:
    from TranscendentalFramework.mock_gpu_test import MockGPUAccelerator
    MOCK_GPU_AVAILABLE = True
except ImportError:
    MOCK_GPU_AVAILABLE = False
    print("警告: 模拟GPU加速器不可用，请确保TranscendentalFramework/mock_gpu_test.py文件存在")

# 导入算子注册表
from src.operators import operator_registry

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MockGPUCentralityTest:
    """使用模拟GPU加速器测试中心节点识别算子"""
    
    def __init__(self):
        """初始化测试环境"""
        # 检查模拟GPU加速器是否可用
        if not MOCK_GPU_AVAILABLE:
            raise ImportError("模拟GPU加速器不可用")
        
        # 创建模拟GPU加速器
        self.gpu_accelerator = MockGPUAccelerator()
        logger.info("模拟GPU加速器初始化完成")
        
        # 获取中心性算子
        self.centrality_class = operator_registry.get_operator("gpu_acceleration", "centrality")
        if self.centrality_class is None:
            raise ImportError("GPU加速中心性算子不可用，请确保已编译Rust绑定")
        
        logger.info("GPU加速中心性算子加载完成")
    
    def generate_random_network(self, n_nodes, edge_probability=0.1, weighted=True):
        """生成随机网络"""
        nodes = {}
        
        # 创建节点
        for i in range(n_nodes):
            node_id = f"node_{i}"
            connections = {}
            
            # 添加连接
            for j in range(n_nodes):
                if i != j and random.random() < edge_probability:
                    if weighted:
                        weight = random.uniform(0.1, 1.0)
                    else:
                        weight = 1.0
                    
                    connections[f"node_{j}"] = weight
            
            nodes[node_id] = connections
        
        return nodes
    
    def test_centrality(self, nodes, centrality_type, use_gpu=True):
        """测试中心性算子"""
        # 创建配置
        config = self.centrality_class.PyCentralityConfig(
            centrality_type=centrality_type,
            use_edge_weights=True,
            normalize=True,
            max_iterations=100,
            convergence_threshold=1e-6,
            damping_factor=0.85,
            use_multithreading=True,
            compute_device="cuda" if use_gpu else "cpu",
            use_simd=True,
            use_sparse_matrix=True,
            batch_size=64
        )
        
        # 创建中心性算子实例
        centrality_operator = self.centrality_class.PyCentralityOperator(config)
        
        # 计算中心性
        start_time = time.time()
        result = centrality_operator.calculate_centrality(nodes)
        elapsed_time = time.time() - start_time
        
        return {
            "elapsed_time": elapsed_time,
            "result": result
        }
    
    def run_benchmark(self):
        """运行性能测试"""
        centrality_types = ["degree", "closeness", "betweenness", "eigenvector", "pagerank", "katz"]
        node_counts = [10, 50, 100, 200]
        
        results = {
            "cpu": {ct: [] for ct in centrality_types},
            "gpu": {ct: [] for ct in centrality_types}
        }
        
        for n_nodes in node_counts:
            logger.info(f"测试节点数量: {n_nodes}")
            
            # 生成随机网络
            nodes = self.generate_random_network(n_nodes, edge_probability=0.1)
            
            for centrality_type in centrality_types:
                logger.info(f"  测试中心性类型: {centrality_type}")
                
                # 测试CPU版本
                logger.info("    测试CPU版本...")
                cpu_result = self.test_centrality(nodes, centrality_type, use_gpu=False)
                if cpu_result:
                    results["cpu"][centrality_type].append({
                        "n_nodes": n_nodes,
                        "elapsed_time": cpu_result["elapsed_time"]
                    })
                    logger.info(f"    CPU版本耗时: {cpu_result['elapsed_time']:.4f}秒")
                
                # 测试GPU版本
                logger.info("    测试GPU版本...")
                gpu_result = self.test_centrality(nodes, centrality_type, use_gpu=True)
                if gpu_result:
                    results["gpu"][centrality_type].append({
                        "n_nodes": n_nodes,
                        "elapsed_time": gpu_result["elapsed_time"]
                    })
                    logger.info(f"    GPU版本耗时: {gpu_result['elapsed_time']:.4f}秒")
                
                # 计算加速比
                if cpu_result and gpu_result:
                    speedup = cpu_result["elapsed_time"] / gpu_result["elapsed_time"]
                    logger.info(f"    加速比: {speedup:.2f}x")
                
                logger.info("")
        
        return results
    
    def print_results(self, results):
        """打印测试结果"""
        centrality_types = ["degree", "closeness", "betweenness", "eigenvector", "pagerank", "katz"]
        
        logger.info("=" * 80)
        logger.info("性能测试结果")
        logger.info("=" * 80)
        
        for centrality_type in centrality_types:
            logger.info(f"\n{centrality_type.capitalize()} 中心性:")
            logger.info("-" * 60)
            logger.info(f"{'节点数':>10} | {'CPU (秒)':>12} | {'GPU (秒)':>12} | {'加速比':>10}")
            logger.info(f"{'-'*10} | {'-'*12} | {'-'*12} | {'-'*10}")
            
            cpu_data = results["cpu"][centrality_type]
            gpu_data = results["gpu"][centrality_type]
            
            for cpu_item, gpu_item in zip(cpu_data, gpu_data):
                n_nodes = cpu_item["n_nodes"]
                cpu_time = cpu_item["elapsed_time"]
                gpu_time = gpu_item["elapsed_time"]
                speedup = cpu_time / gpu_time
                
                logger.info(f"{n_nodes:>10} | {cpu_time:>12.6f} | {gpu_time:>12.6f} | {speedup:>10.2f}x")
    
    def close(self):
        """关闭测试环境"""
        # 关闭模拟GPU加速器
        self.gpu_accelerator.close()
        logger.info("模拟GPU加速器已关闭")

def main():
    """主函数"""
    logger.info("开始测试使用模拟GPU加速器的中心节点识别算子...")
    
    try:
        # 创建测试环境
        test = MockGPUCentralityTest()
        
        # 运行性能测试
        results = test.run_benchmark()
        
        # 打印测试结果
        test.print_results(results)
        
    except ImportError as e:
        logger.error(f"导入错误: {str(e)}")
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
    finally:
        # 关闭测试环境
        if 'test' in locals():
            test.close()
    
    logger.info("测试完成")

if __name__ == "__main__":
    main()
