"""
动态资源分配算子独立测试

本脚本测试动态资源分配算子的基本功能，完全独立于项目中的其他模块。
"""

import sys
import os
import time
from enum import Enum
from typing import Dict, List, Optional, Set, Tuple, Union, Any, Callable
from dataclasses import dataclass, field
import uuid
import threading

# 定义资源类型
class ResourceType(Enum):
    """资源类型"""
    CPU = "cpu"
    GPU = "gpu"
    MEMORY = "memory"
    STORAGE = "storage"
    NETWORK = "network"
    FPGA = "fpga"
    TPU = "tpu"
    CUSTOM = "custom"


class ResourceStatus(Enum):
    """资源状态"""
    AVAILABLE = "available"
    ALLOCATED = "allocated"
    RESERVED = "reserved"
    MAINTENANCE = "maintenance"
    FAILED = "failed"


class ResourcePriority(Enum):
    """资源优先级"""
    LOW = 0
    MEDIUM = 1
    HIGH = 2
    CRITICAL = 3


@dataclass
class ResourceRequest:
    """资源请求"""
    request_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    resource_type: ResourceType = ResourceType.CPU
    amount: float = 1.0
    priority: ResourcePriority = ResourcePriority.MEDIUM
    max_wait_time: Optional[float] = None  # 最大等待时间（秒），None表示无限等待
    dependencies: List[str] = field(default_factory=list)  # 依赖的其他资源ID列表
    constraints: Dict[str, Any] = field(default_factory=dict)  # 资源约束条件
    metadata: Dict[str, Any] = field(default_factory=dict)  # 元数据
    created_at: float = field(default_factory=time.time)  # 创建时间
    
    def is_expired(self) -> bool:
        """检查请求是否过期"""
        if self.max_wait_time is None:
            return False
        return time.time() - self.created_at > self.max_wait_time


@dataclass
class ResourcePool:
    """资源池"""
    pool_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    resource_type: ResourceType = ResourceType.CPU
    capacity: float = 0.0
    available: float = 0.0
    allocated: float = 0.0
    reserved: float = 0.0
    status: ResourceStatus = ResourceStatus.AVAILABLE
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: float = field(default_factory=time.time)
    last_updated: float = field(default_factory=time.time)
    
    def allocate(self, amount: float) -> bool:
        """分配资源"""
        if self.status != ResourceStatus.AVAILABLE:
            return False
        
        if self.available < amount:
            return False
        
        self.available -= amount
        self.allocated += amount
        self.last_updated = time.time()
        return True
    
    def release(self, amount: float) -> bool:
        """释放资源"""
        if amount > self.allocated:
            return False
        
        self.allocated -= amount
        self.available += amount
        self.last_updated = time.time()
        return True


@dataclass
class ResourceAllocation:
    """资源分配"""
    allocation_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    request_id: str = ""
    pool_id: str = ""
    resource_type: ResourceType = ResourceType.CPU
    amount: float = 0.0
    allocated_at: float = field(default_factory=time.time)
    expires_at: Optional[float] = None  # 过期时间，None表示永不过期
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_expired(self) -> bool:
        """检查分配是否过期"""
        if self.expires_at is None:
            return False
        return time.time() > self.expires_at


class AllocationStatus(Enum):
    """分配状态"""
    PENDING = "pending"  # 等待中
    ALLOCATED = "allocated"  # 已分配
    FAILED = "failed"  # 分配失败
    EXPIRED = "expired"  # 已过期


class DynamicResourceAllocator:
    """
    动态资源分配器类
    
    该类实现了动态资源分配算子的核心功能。
    
    属性:
        name (str): 分配器名称
        parameters (Dict[str, Any]): 分配器参数
        metrics (Dict[str, Any]): 分配器指标
    """
    
    def __init__(
        self,
        name: str = "dynamic_resource_allocator",
        parameters: Optional[Dict[str, Any]] = None
    ):
        """
        初始化动态资源分配器
        
        参数:
            name (str, optional): 分配器名称
            parameters (Dict[str, Any], optional): 分配器参数
        """
        self.name = name
        self.parameters = parameters or {}
        
        # 设置默认参数
        self._set_default_parameters()
        
        # 初始化指标
        self.metrics = {
            "start_time": time.time(),
            "allocation_count": 0,
            "allocation_success_rate": 1.0,
            "last_allocation_time": None,
            "resource_utilization": 0.0,
        }
        
        # 初始化资源池和分配
        self._resource_pools: Dict[str, ResourcePool] = {}
        self._resource_allocations: Dict[str, ResourceAllocation] = {}
        self._pending_requests: Dict[str, ResourceRequest] = {}
        
        # 初始化锁
        self._lock = threading.RLock()
        
        # 初始化回调函数
        self._allocation_callbacks = []
    
    def _set_default_parameters(self) -> None:
        """设置默认参数"""
        defaults = {
            "allocation_timeout": 60,  # 分配超时时间（秒）
            "cleanup_interval": 300,  # 清理间隔（秒）
            "max_pending_requests": 1000,  # 最大等待请求数
            "max_allocations": 10000,  # 最大分配数
        }
        
        # 更新缺失的参数
        for key, value in defaults.items():
            if key not in self.parameters:
                self.parameters[key] = value
    
    def add_resource_pool(self, pool: ResourcePool) -> bool:
        """
        添加资源池
        
        参数:
            pool (ResourcePool): 资源池
            
        返回:
            bool: 是否成功添加资源池
        """
        with self._lock:
            if pool.pool_id in self._resource_pools:
                print(f"Resource pool already exists: {pool.pool_id}")
                return False
            
            self._resource_pools[pool.pool_id] = pool
            
            print(f"Resource pool added: {pool.pool_id}, type={pool.resource_type.value}, capacity={pool.capacity}")
            
            return True
    
    def remove_resource_pool(self, pool_id: str) -> bool:
        """
        移除资源池
        
        参数:
            pool_id (str): 资源池ID
            
        返回:
            bool: 是否成功移除资源池
        """
        with self._lock:
            if pool_id not in self._resource_pools:
                print(f"Resource pool not found: {pool_id}")
                return False
            
            # 检查是否有分配使用该资源池
            for allocation in self._resource_allocations.values():
                if allocation.pool_id == pool_id:
                    print(f"Resource pool is in use: {pool_id}")
                    return False
            
            # 移除资源池
            pool = self._resource_pools.pop(pool_id)
            
            print(f"Resource pool removed: {pool_id}, type={pool.resource_type.value}")
            
            return True
    
    def get_resource_pool(self, pool_id: str) -> Optional[ResourcePool]:
        """
        获取资源池
        
        参数:
            pool_id (str): 资源池ID
            
        返回:
            ResourcePool: 资源池，如果不存在则返回None
        """
        with self._lock:
            return self._resource_pools.get(pool_id)
    
    def get_resource_pools(self, resource_type: Optional[ResourceType] = None) -> List[ResourcePool]:
        """
        获取资源池列表
        
        参数:
            resource_type (ResourceType, optional): 资源类型，如果为None则返回所有资源池
            
        返回:
            List[ResourcePool]: 资源池列表
        """
        with self._lock:
            if resource_type is None:
                return list(self._resource_pools.values())
            
            return [
                pool for pool in self._resource_pools.values()
                if pool.resource_type == resource_type
            ]
    
    def allocate_resource(self, request: ResourceRequest) -> Dict[str, Any]:
        """
        分配资源
        
        参数:
            request (ResourceRequest): 资源请求
            
        返回:
            Dict[str, Any]: 分配结果
        """
        with self._lock:
            current_time = time.time()
            
            # 创建分配结果
            result = {
                "timestamp": current_time,
                "request_id": request.request_id,
                "resource_type": request.resource_type.value,
                "amount": request.amount,
                "priority": request.priority.value,
                "status": AllocationStatus.PENDING.value,
                "allocation_id": None,
                "pool_id": None,
                "message": "Resource allocation pending",
            }
            
            # 检查请求是否已存在
            if request.request_id in self._pending_requests:
                result["status"] = AllocationStatus.FAILED.value
                result["message"] = "Request already exists"
                return result
            
            # 检查是否超过最大等待请求数
            if len(self._pending_requests) >= self.parameters["max_pending_requests"]:
                result["status"] = AllocationStatus.FAILED.value
                result["message"] = "Too many pending requests"
                return result
            
            # 添加到等待请求
            self._pending_requests[request.request_id] = request
            
            # 获取匹配的资源池
            pools = self.get_resource_pools(request.resource_type)
            
            if not pools:
                result["status"] = AllocationStatus.FAILED.value
                result["message"] = f"No resource pools available for type: {request.resource_type.value}"
                return result
            
            # 选择资源池
            selected_pool = None
            for pool in pools:
                if pool.status == ResourceStatus.AVAILABLE and pool.available >= request.amount:
                    selected_pool = pool
                    break
            
            if selected_pool is None:
                result["status"] = AllocationStatus.FAILED.value
                result["message"] = "No suitable resource pool found"
                return result
            
            # 分配资源
            if not selected_pool.allocate(request.amount):
                result["status"] = AllocationStatus.FAILED.value
                result["message"] = f"Failed to allocate resources from pool: {selected_pool.pool_id}"
                return result
            
            # 创建分配
            allocation_id = str(uuid.uuid4())
            allocation = ResourceAllocation(
                allocation_id=allocation_id,
                request_id=request.request_id,
                pool_id=selected_pool.pool_id,
                resource_type=request.resource_type,
                amount=request.amount,
                allocated_at=current_time,
                expires_at=None,  # 永不过期
                metadata=request.metadata.copy()
            )
            
            # 添加分配
            self._resource_allocations[allocation_id] = allocation
            
            # 移除等待请求
            del self._pending_requests[request.request_id]
            
            # 更新指标
            self.metrics["allocation_count"] += 1
            self.metrics["last_allocation_time"] = current_time
            self._update_utilization_metrics()
            
            # 更新结果
            result["status"] = AllocationStatus.ALLOCATED.value
            result["allocation_id"] = allocation_id
            result["pool_id"] = selected_pool.pool_id
            result["message"] = "Resource allocated successfully"
            
            # 通知分配回调函数
            self._notify_allocation(result)
            
            return result
    
    def release_resource(self, allocation_id: str) -> Dict[str, Any]:
        """
        释放资源
        
        参数:
            allocation_id (str): 分配ID
            
        返回:
            Dict[str, Any]: 释放结果
        """
        with self._lock:
            current_time = time.time()
            
            # 创建释放结果
            result = {
                "timestamp": current_time,
                "allocation_id": allocation_id,
                "success": False,
                "message": "Resource allocation not found",
            }
            
            # 检查分配是否存在
            if allocation_id not in self._resource_allocations:
                return result
            
            # 获取分配
            allocation = self._resource_allocations[allocation_id]
            
            # 获取资源池
            pool = self.get_resource_pool(allocation.pool_id)
            
            if pool is None:
                result["message"] = f"Resource pool not found: {allocation.pool_id}"
                return result
            
            # 释放资源
            if not pool.release(allocation.amount):
                result["message"] = f"Failed to release resources from pool: {pool.pool_id}"
                return result
            
            # 移除分配
            del self._resource_allocations[allocation_id]
            
            # 更新指标
            self._update_utilization_metrics()
            
            # 更新结果
            result["success"] = True
            result["message"] = "Resource released successfully"
            result["resource_type"] = allocation.resource_type.value
            result["amount"] = allocation.amount
            result["pool_id"] = allocation.pool_id
            
            return result
    
    def get_allocation(self, allocation_id: str) -> Optional[ResourceAllocation]:
        """
        获取分配
        
        参数:
            allocation_id (str): 分配ID
            
        返回:
            ResourceAllocation: 分配，如果不存在则返回None
        """
        with self._lock:
            return self._resource_allocations.get(allocation_id)
    
    def get_allocations(
        self,
        resource_type: Optional[ResourceType] = None,
        request_id: Optional[str] = None
    ) -> List[ResourceAllocation]:
        """
        获取分配列表
        
        参数:
            resource_type (ResourceType, optional): 资源类型，如果为None则不过滤
            request_id (str, optional): 请求ID，如果为None则不过滤
            
        返回:
            List[ResourceAllocation]: 分配列表
        """
        with self._lock:
            allocations = list(self._resource_allocations.values())
            
            if resource_type is not None:
                allocations = [a for a in allocations if a.resource_type == resource_type]
            
            if request_id is not None:
                allocations = [a for a in allocations if a.request_id == request_id]
            
            return allocations
    
    def get_pending_requests(
        self,
        resource_type: Optional[ResourceType] = None
    ) -> List[ResourceRequest]:
        """
        获取等待请求列表
        
        参数:
            resource_type (ResourceType, optional): 资源类型，如果为None则不过滤
            
        返回:
            List[ResourceRequest]: 等待请求列表
        """
        with self._lock:
            requests = list(self._pending_requests.values())
            
            if resource_type is not None:
                requests = [r for r in requests if r.resource_type == resource_type]
            
            return requests
    
    def _update_utilization_metrics(self) -> None:
        """更新利用率指标"""
        total_capacity = 0.0
        total_allocated = 0.0
        
        for pool in self._resource_pools.values():
            total_capacity += pool.capacity
            total_allocated += pool.allocated
        
        if total_capacity > 0:
            self.metrics["resource_utilization"] = total_allocated / total_capacity
        else:
            self.metrics["resource_utilization"] = 0.0
    
    def add_allocation_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """
        添加分配回调函数
        
        参数:
            callback (Callable[[Dict[str, Any]], None]): 回调函数，接收分配结果
        """
        with self._lock:
            self._allocation_callbacks.append(callback)
    
    def remove_allocation_callback(self, callback: Callable[[Dict[str, Any]], None]) -> bool:
        """
        移除分配回调函数
        
        参数:
            callback (Callable[[Dict[str, Any]], None]): 回调函数
            
        返回:
            bool: 是否成功移除
        """
        with self._lock:
            if callback in self._allocation_callbacks:
                self._allocation_callbacks.remove(callback)
                return True
            return False
    
    def _notify_allocation(self, result: Dict[str, Any]) -> None:
        """
        通知分配回调函数
        
        参数:
            result (Dict[str, Any]): 分配结果
        """
        for callback in self._allocation_callbacks:
            try:
                callback(result)
            except Exception as e:
                print(f"Error in allocation callback: {e}")
    
    def cleanup(self) -> Dict[str, Any]:
        """
        清理过期的分配和请求
        
        返回:
            Dict[str, Any]: 清理结果
        """
        with self._lock:
            current_time = time.time()
            
            # 创建清理结果
            result = {
                "timestamp": current_time,
                "expired_allocations": 0,
                "expired_requests": 0,
            }
            
            # 清理过期的分配
            expired_allocations = []
            
            for allocation_id, allocation in list(self._resource_allocations.items()):
                if allocation.is_expired():
                    expired_allocations.append(allocation_id)
            
            for allocation_id in expired_allocations:
                self.release_resource(allocation_id)
            
            result["expired_allocations"] = len(expired_allocations)
            
            # 清理过期的请求
            expired_requests = []
            
            for request_id, request in list(self._pending_requests.items()):
                if request.is_expired():
                    expired_requests.append(request_id)
            
            for request_id in expired_requests:
                del self._pending_requests[request_id]
            
            result["expired_requests"] = len(expired_requests)
            
            return result


def allocation_callback(result: Dict[str, Any]) -> None:
    """
    分配回调函数
    
    参数:
        result (Dict[str, Any]): 分配结果
    """
    print(f"Allocation callback: {result['status']} - {result['message']}")


def run_tests():
    """运行测试"""
    print("开始测试动态资源分配算子...")
    
    # 创建动态资源分配器
    allocator = DynamicResourceAllocator(name="TestAllocator")
    
    # 添加分配回调函数
    allocator.add_allocation_callback(allocation_callback)
    
    # 创建资源池
    cpu_pool = ResourcePool(
        pool_id="cpu_pool_1",
        resource_type=ResourceType.CPU,
        capacity=100.0,
        available=100.0
    )
    
    memory_pool = ResourcePool(
        pool_id="memory_pool_1",
        resource_type=ResourceType.MEMORY,
        capacity=1024.0,
        available=1024.0
    )
    
    # 添加资源池
    allocator.add_resource_pool(cpu_pool)
    allocator.add_resource_pool(memory_pool)
    
    # 测试1：添加资源池
    print("\n测试1：添加资源池")
    gpu_pool = ResourcePool(
        pool_id="gpu_pool_1",
        resource_type=ResourceType.GPU,
        capacity=10.0,
        available=10.0
    )
    
    result = allocator.add_resource_pool(gpu_pool)
    print(f"添加资源池结果: {result}")
    
    pool = allocator.get_resource_pool("gpu_pool_1")
    print(f"获取资源池: {pool.pool_id}, 类型={pool.resource_type.value}, 容量={pool.capacity}")
    
    # 测试2：分配资源
    print("\n测试2：分配资源")
    cpu_request = ResourceRequest(
        request_id="cpu_request_1",
        resource_type=ResourceType.CPU,
        amount=10.0,
        priority=ResourcePriority.MEDIUM
    )
    
    result = allocator.allocate_resource(cpu_request)
    print(f"分配资源结果: {result['status']} - {result['message']}")
    
    allocation = allocator.get_allocation(result["allocation_id"])
    print(f"获取分配: 请求ID={allocation.request_id}, 类型={allocation.resource_type.value}, 数量={allocation.amount}")
    
    pool = allocator.get_resource_pool("cpu_pool_1")
    print(f"资源池状态: 容量={pool.capacity}, 可用={pool.available}, 已分配={pool.allocated}")
    
    # 测试3：释放资源
    print("\n测试3：释放资源")
    release_result = allocator.release_resource(result["allocation_id"])
    print(f"释放资源结果: {release_result['success']} - {release_result['message']}")
    
    pool = allocator.get_resource_pool("cpu_pool_1")
    print(f"资源池状态: 容量={pool.capacity}, 可用={pool.available}, 已分配={pool.allocated}")
    
    # 测试4：多个分配
    print("\n测试4：多个分配")
    cpu_request_1 = ResourceRequest(
        request_id="cpu_request_1",
        resource_type=ResourceType.CPU,
        amount=10.0,
        priority=ResourcePriority.MEDIUM
    )
    
    cpu_request_2 = ResourceRequest(
        request_id="cpu_request_2",
        resource_type=ResourceType.CPU,
        amount=20.0,
        priority=ResourcePriority.HIGH
    )
    
    memory_request = ResourceRequest(
        request_id="memory_request_1",
        resource_type=ResourceType.MEMORY,
        amount=256.0,
        priority=ResourcePriority.MEDIUM
    )
    
    allocator.allocate_resource(cpu_request_1)
    allocator.allocate_resource(cpu_request_2)
    allocator.allocate_resource(memory_request)
    
    all_allocations = allocator.get_allocations()
    print(f"所有分配数量: {len(all_allocations)}")
    
    cpu_allocations = allocator.get_allocations(ResourceType.CPU)
    print(f"CPU分配数量: {len(cpu_allocations)}, 总量: {sum(a.amount for a in cpu_allocations)}")
    
    memory_allocations = allocator.get_allocations(ResourceType.MEMORY)
    print(f"内存分配数量: {len(memory_allocations)}, 总量: {sum(a.amount for a in memory_allocations)}")
    
    # 测试5：清理
    print("\n测试5：清理")
    cleanup_result = allocator.cleanup()
    print(f"清理结果: 过期分配={cleanup_result['expired_allocations']}, 过期请求={cleanup_result['expired_requests']}")
    
    print("\n测试完成！")


if __name__ == "__main__":
    run_tests()
