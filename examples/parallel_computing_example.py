"""
并行计算示例

本示例展示了如何使用并行计算模块。
"""

import os
import sys
import logging
import time
import numpy as np
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入并行计算模块
try:
    from src.rust_bindings import (
        RUST_AVAILABLE,
        TaskPriority,
        TaskStatus,
        Task,
        TaskScheduler,
        WorkerStats,
        LoadBalancingStrategy,
        LoadBalancer,
        ParallelAlgorithms,
        ThreadPool,
        WorkStealer,
        get_num_cpus,
        get_current_thread_id,
        parallel_map,
        parallel_reduce,
        parallel_for_each,
    )
    logger.info(f"成功导入并行计算模块，Rust可用: {RUST_AVAILABLE}")
except ImportError as e:
    logger.error(f"导入并行计算模块失败: {e}")
    sys.exit(1)

def test_task_scheduler():
    """测试任务调度器"""
    logger.info("开始测试任务调度器...")

    # 创建任务调度器
    scheduler = TaskScheduler("test_scheduler", 4)

    # 启动调度器
    scheduler.start()

    # 定义任务函数
    def task_func(task_id, sleep_time):
        logger.info(f"任务 {task_id} 开始执行，休眠 {sleep_time} 秒")
        time.sleep(sleep_time)
        logger.info(f"任务 {task_id} 执行完成")
        return f"任务 {task_id} 的结果"

    # 提交任务
    tasks = []
    for i in range(10):
        task = scheduler.submit_task(
            f"task_{i}",
            f"任务 {i}",
            task_func,
            args=(i, 0.5),
            priority=TaskPriority.NORMAL,
        )
        tasks.append(task)

    # 等待所有任务完成
    scheduler.wait_for_all_tasks()

    # 获取任务结果
    for task in tasks:
        logger.info(f"任务 {task.id} 的状态: {task.status}, 结果: {task.result}")

    # 停止调度器
    scheduler.stop()

    logger.info("任务调度器测试完成")

def test_load_balancer():
    """测试负载均衡器"""
    logger.info("开始测试负载均衡器...")

    # 创建负载均衡器
    balancer = LoadBalancer("test_balancer", LoadBalancingStrategy.ROUND_ROBIN)

    # 注册工作者
    for i in range(4):
        balancer.register_worker(i, f"worker_{i}")

    # 创建任务
    tasks = []
    for i in range(10):
        task = Task(
            f"task_{i}",
            f"任务 {i}",
            lambda: None,
        )
        tasks.append(task)

    # 选择工作者
    for task in tasks:
        worker_id = balancer.select_worker(task)
        logger.info(f"任务 {task.id} 分配给工作者 {worker_id}")

    # 更新工作者负载
    for i in range(4):
        balancer.update_worker_load(i, i * 0.25)

    # 更改负载均衡策略
    balancer.set_strategy(LoadBalancingStrategy.LEAST_CONNECTIONS)

    # 再次选择工作者
    for task in tasks:
        worker_id = balancer.select_worker(task)
        logger.info(f"任务 {task.id} 分配给工作者 {worker_id} (最少连接策略)")

    logger.info("负载均衡器测试完成")

def test_parallel_algorithms():
    """测试并行算法"""
    logger.info("开始测试并行算法...")

    # 创建并行算法
    algorithms = ParallelAlgorithms("test_algorithms")

    # 创建测试数组
    array = np.random.rand(1000)

    # 测试归并排序
    start_time = time.time()
    sorted_array = algorithms.merge_sort(array)
    end_time = time.time()
    logger.info(f"归并排序耗时: {end_time - start_time:.6f}秒")
    logger.info(f"排序结果是否正确: {np.all(np.diff(sorted_array) >= 0)}")

    # 测试快速排序
    start_time = time.time()
    sorted_array = algorithms.quick_sort(array)
    end_time = time.time()
    logger.info(f"快速排序耗时: {end_time - start_time:.6f}秒")
    logger.info(f"排序结果是否正确: {np.all(np.diff(sorted_array) >= 0)}")

    # 测试矩阵乘法
    a = np.random.rand(100, 100)
    b = np.random.rand(100, 100)

    start_time = time.time()
    result1 = np.matmul(a, b)
    end_time = time.time()
    logger.info(f"NumPy矩阵乘法耗时: {end_time - start_time:.6f}秒")

    start_time = time.time()
    result2 = algorithms.matrix_multiply(a, b)
    end_time = time.time()
    logger.info(f"并行矩阵乘法耗时: {end_time - start_time:.6f}秒")

    logger.info(f"结果是否相似: {np.allclose(result1, result2)}")

    # 测试卷积
    array = np.random.rand(100, 100)
    kernel = np.random.rand(3, 3)

    start_time = time.time()
    result = algorithms.convolution(array, kernel)
    end_time = time.time()
    logger.info(f"并行卷积耗时: {end_time - start_time:.6f}秒")

    # 测试并行映射
    start_time = time.time()
    result = algorithms.parallel_map(array, np.sin)
    end_time = time.time()
    logger.info(f"并行映射耗时: {end_time - start_time:.6f}秒")

    # 测试并行归约
    start_time = time.time()
    result = algorithms.parallel_reduce(array, lambda x, y: x + y)
    end_time = time.time()
    logger.info(f"并行归约耗时: {end_time - start_time:.6f}秒")
    logger.info(f"归约结果: {result}")

    logger.info("并行算法测试完成")

def test_thread_pool():
    """测试线程池"""
    logger.info("开始测试线程池...")

    # 创建线程池
    pool = ThreadPool("test_pool", 4)

    # 启动线程池
    pool.start()

    # 定义任务函数
    def task_func(task_id, sleep_time):
        logger.info(f"任务 {task_id} 开始执行，休眠 {sleep_time} 秒")
        time.sleep(sleep_time)
        logger.info(f"任务 {task_id} 执行完成")
        return f"任务 {task_id} 的结果"

    # 提交任务
    for i in range(10):
        pool.execute(task_func, i, 0.5)

    # 等待所有任务完成
    pool.wait()

    # 停止线程池
    pool.stop()

    logger.info("线程池测试完成")

def test_work_stealer():
    """测试工作窃取调度器"""
    logger.info("开始测试工作窃取调度器...")

    # 创建工作窃取调度器
    stealer = WorkStealer("test_stealer", 4)

    # 启动调度器
    stealer.start()

    # 定义任务函数
    def task_func(task_id, sleep_time):
        logger.info(f"任务 {task_id} 开始执行，休眠 {sleep_time} 秒")
        time.sleep(sleep_time)
        logger.info(f"任务 {task_id} 执行完成")
        return f"任务 {task_id} 的结果"

    # 提交任务
    for i in range(10):
        stealer.submit(f"task_{i}", task_func, args=(i, 0.5))

    # 等待一段时间，让任务执行完成
    time.sleep(6)

    # 停止调度器
    stealer.stop()

    logger.info("工作窃取调度器测试完成")

def square(x):
    """计算平方"""
    return x * x

def add(x, y):
    """计算和"""
    return x + y

def print_item(x):
    """打印项目"""
    logger.info(f"项目: {x}")

def test_module_functions():
    """测试模块函数"""
    logger.info("开始测试模块函数...")

    # 获取CPU数量
    num_cpus = get_num_cpus()
    logger.info(f"CPU数量: {num_cpus}")

    # 获取当前线程ID
    thread_id = get_current_thread_id()
    logger.info(f"当前线程ID: {thread_id}")

    # 测试并行映射
    data = list(range(10))

    start_time = time.time()
    result = parallel_map(data, square)
    end_time = time.time()

    logger.info(f"并行映射耗时: {end_time - start_time:.6f}秒")
    logger.info(f"映射结果: {result}")

    # 测试并行归约
    start_time = time.time()
    result = parallel_reduce(data, add)
    end_time = time.time()

    logger.info(f"并行归约耗时: {end_time - start_time:.6f}秒")
    logger.info(f"归约结果: {result}")

    # 测试并行遍历
    parallel_for_each(data[:5], print_item)

    logger.info("模块函数测试完成")

def main():
    """主函数"""
    logger.info(f"开始并行计算示例，Rust可用: {RUST_AVAILABLE}")

    # 测试任务调度器
    test_task_scheduler()

    # 测试负载均衡器
    test_load_balancer()

    # 测试并行算法
    test_parallel_algorithms()

    # 测试线程池
    test_thread_pool()

    # 测试工作窃取调度器
    test_work_stealer()

    # 测试模块函数
    test_module_functions()

    logger.info("并行计算示例结束")

if __name__ == "__main__":
    main()
