#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
NumPy 2.x GIL管理性能基准测试

本脚本专门用于测试NumPy 2.x的GIL=0模式与Python 3.13+ nogil模式的性能差异，
以及它们在超越态计算框架中的组合效果。
"""

import os
import sys
import time
import logging
import contextlib
import numpy as np
import multiprocessing
from typing import Dict, List, Any, Optional, Callable, Tuple

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 定义兼容性标志
has_nogil = False
has_np_gil_state = False

# 尝试导入GIL管理模块
try:
    # 首先检查是否支持nogil模式（Python 3.13+特性）
    has_nogil = hasattr(sys, 'set_nogil')
    
    # 检查NumPy是否支持gil_state
    has_np_gil_state = hasattr(np, 'gil_state')
    
    # 尝试导入我们的GIL管理模块
    try:
        from src.core.parallel.gil_manager import (
            gil_state, nogil_mode, nogil, release_gil, acquire_gil,
            get_gil_status
        )
        # 如果导入成功，使用模块提供的状态信息
        gil_status = get_gil_status()
        has_nogil = gil_status['has_nogil_support']
        has_np_gil_state = gil_status['has_np_gil_state']
        logger.info(f"成功导入GIL管理模块")
    except ImportError as e:
        logger.warning(f"导入GIL管理模块失败: {e}，将使用基本兼容模式")
        # 定义兼容性函数
        @contextlib.contextmanager
        def gil_state(acquire=True):
            yield
        
        @contextlib.contextmanager
        def nogil_mode(enable=True):
            yield
        
        def nogil(func):
            return func
        
        def release_gil(func):
            return func
        
        def acquire_gil(func):
            return func
    
    logger.info(f"GIL状态: nogil支持={has_nogil}, NumPy gil_state支持={has_np_gil_state}")

except Exception as e:
    logger.error(f"初始化GIL管理功能失败: {e}")
    # 设置兼容性标志
    has_nogil = False
    has_np_gil_state = False
    
    # 定义兼容性函数
    @contextlib.contextmanager
    def gil_state(acquire=True):
        yield
    
    @contextlib.contextmanager
    def nogil_mode(enable=True):
        yield
    
    def nogil(func):
        return func
    
    def release_gil(func):
        return func
    
    def acquire_gil(func):
        return func

# 测试参数
MATRIX_SIZES = [1000, 2000, 3000]
VECTOR_SIZES = [1_000_000, 10_000_000, 50_000_000]
REPEAT_COUNT = 5
CPU_COUNT = multiprocessing.cpu_count()


def create_test_matrices(size: int) -> Tuple[np.ndarray, np.ndarray]:
    """创建测试矩阵"""
    a = np.random.rand(size, size).astype(np.float64)
    b = np.random.rand(size, size).astype(np.float64)
    return a, b


def create_test_vectors(size: int) -> Tuple[np.ndarray, np.ndarray]:
    """创建测试向量"""
    a = np.random.rand(size).astype(np.complex128)
    b = np.random.rand(size).astype(np.complex128)
    return a, b


def benchmark_function(func: Callable, *args, repeat: int = 3) -> Dict[str, float]:
    """
    对函数进行基准测试
    
    Args:
        func: 要测试的函数
        *args: 函数参数
        repeat: 重复次数
        
    Returns:
        包含性能指标的字典
    """
    times = []
    
    # 预热
    func(*args)
    
    # 测试
    for _ in range(repeat):
        start_time = time.time()
        result = func(*args)
        end_time = time.time()
        times.append(end_time - start_time)
    
    # 计算统计数据
    avg_time = sum(times) / len(times)
    min_time = min(times)
    max_time = max(times)
    
    return {
        'avg_time': avg_time,
        'min_time': min_time,
        'max_time': max_time,
        'times': times
    }


# 测试函数 - 矩阵乘法

def matrix_multiply_normal(a: np.ndarray, b: np.ndarray) -> np.ndarray:
    """普通矩阵乘法（不释放GIL）"""
    return np.dot(a, b)


def matrix_multiply_numpy_gil(a: np.ndarray, b: np.ndarray) -> np.ndarray:
    """使用NumPy gil_state的矩阵乘法"""
    with gil_state(False):
        return np.dot(a, b)


def matrix_multiply_python_nogil(a: np.ndarray, b: np.ndarray) -> np.ndarray:
    """使用Python nogil模式的矩阵乘法"""
    with nogil_mode(True):
        return np.dot(a, b)


def matrix_multiply_combined(a: np.ndarray, b: np.ndarray) -> np.ndarray:
    """结合NumPy gil_state和Python nogil模式的矩阵乘法"""
    with nogil_mode(True):
        with gil_state(False):
            return np.dot(a, b)


# 测试函数 - 复数数组操作

def complex_array_normal(a: np.ndarray, b: np.ndarray) -> np.ndarray:
    """普通复数数组操作（不释放GIL）"""
    c = a * b
    d = np.abs(c) ** 2
    e = np.sqrt(d)
    return np.fft.fft(e)


def complex_array_numpy_gil(a: np.ndarray, b: np.ndarray) -> np.ndarray:
    """使用NumPy gil_state的复数数组操作"""
    with gil_state(False):
        c = a * b
        d = np.abs(c) ** 2
        e = np.sqrt(d)
        return np.fft.fft(e)


def complex_array_python_nogil(a: np.ndarray, b: np.ndarray) -> np.ndarray:
    """使用Python nogil模式的复数数组操作"""
    with nogil_mode(True):
        c = a * b
        d = np.abs(c) ** 2
        e = np.sqrt(d)
        return np.fft.fft(e)


def complex_array_combined(a: np.ndarray, b: np.ndarray) -> np.ndarray:
    """结合NumPy gil_state和Python nogil模式的复数数组操作"""
    with nogil_mode(True):
        with gil_state(False):
            c = a * b
            d = np.abs(c) ** 2
            e = np.sqrt(d)
            return np.fft.fft(e)


# 测试函数 - 多线程矩阵乘法

def parallel_matrix_multiply(func: Callable, matrices: List[Tuple[np.ndarray, np.ndarray]]) -> List[np.ndarray]:
    """
    并行矩阵乘法
    
    Args:
        func: 矩阵乘法函数
        matrices: 矩阵对列表
        
    Returns:
        结果列表
    """
    from src.core.parallel import parallel_map
    
    return parallel_map(lambda pair: func(*pair), matrices)


def test_matrix_multiplication():
    """测试矩阵乘法性能"""
    logger.info("=" * 80)
    logger.info("矩阵乘法性能测试")
    logger.info("=" * 80)
    
    results = {}
    
    for size in MATRIX_SIZES:
        logger.info(f"\n测试 {size}x{size} 矩阵乘法:")
        a, b = create_test_matrices(size)
        
        # 普通模式
        normal_results = benchmark_function(matrix_multiply_normal, a, b, repeat=REPEAT_COUNT)
        logger.info(f"普通模式: {normal_results['avg_time']:.6f}秒")
        results[f"normal_{size}"] = normal_results
        
        # NumPy gil_state模式
        if has_np_gil_state:
            numpy_gil_results = benchmark_function(matrix_multiply_numpy_gil, a, b, repeat=REPEAT_COUNT)
            logger.info(f"NumPy gil_state模式: {numpy_gil_results['avg_time']:.6f}秒")
            results[f"numpy_gil_{size}"] = numpy_gil_results
            
            # 计算加速比
            speedup = normal_results['avg_time'] / numpy_gil_results['avg_time']
            logger.info(f"NumPy gil_state加速比: {speedup:.2f}x")
        
        # Python nogil模式
        if has_nogil:
            python_nogil_results = benchmark_function(matrix_multiply_python_nogil, a, b, repeat=REPEAT_COUNT)
            logger.info(f"Python nogil模式: {python_nogil_results['avg_time']:.6f}秒")
            results[f"python_nogil_{size}"] = python_nogil_results
            
            # 计算加速比
            speedup = normal_results['avg_time'] / python_nogil_results['avg_time']
            logger.info(f"Python nogil加速比: {speedup:.2f}x")
        
        # 组合模式
        if has_np_gil_state and has_nogil:
            combined_results = benchmark_function(matrix_multiply_combined, a, b, repeat=REPEAT_COUNT)
            logger.info(f"组合模式: {combined_results['avg_time']:.6f}秒")
            results[f"combined_{size}"] = combined_results
            
            # 计算加速比
            speedup = normal_results['avg_time'] / combined_results['avg_time']
            logger.info(f"组合模式加速比: {speedup:.2f}x")
    
    return results


def test_complex_array_operations():
    """测试复数数组操作性能"""
    logger.info("=" * 80)
    logger.info("复数数组操作性能测试")
    logger.info("=" * 80)
    
    results = {}
    
    for size in VECTOR_SIZES:
        logger.info(f"\n测试 {size} 元素复数数组操作:")
        a, b = create_test_vectors(size)
        
        # 普通模式
        normal_results = benchmark_function(complex_array_normal, a, b, repeat=REPEAT_COUNT)
        logger.info(f"普通模式: {normal_results['avg_time']:.6f}秒")
        results[f"normal_{size}"] = normal_results
        
        # NumPy gil_state模式
        if has_np_gil_state:
            numpy_gil_results = benchmark_function(complex_array_numpy_gil, a, b, repeat=REPEAT_COUNT)
            logger.info(f"NumPy gil_state模式: {numpy_gil_results['avg_time']:.6f}秒")
            results[f"numpy_gil_{size}"] = numpy_gil_results
            
            # 计算加速比
            speedup = normal_results['avg_time'] / numpy_gil_results['avg_time']
            logger.info(f"NumPy gil_state加速比: {speedup:.2f}x")
        
        # Python nogil模式
        if has_nogil:
            python_nogil_results = benchmark_function(complex_array_python_nogil, a, b, repeat=REPEAT_COUNT)
            logger.info(f"Python nogil模式: {python_nogil_results['avg_time']:.6f}秒")
            results[f"python_nogil_{size}"] = python_nogil_results
            
            # 计算加速比
            speedup = normal_results['avg_time'] / python_nogil_results['avg_time']
            logger.info(f"Python nogil加速比: {speedup:.2f}x")
        
        # 组合模式
        if has_np_gil_state and has_nogil:
            combined_results = benchmark_function(complex_array_combined, a, b, repeat=REPEAT_COUNT)
            logger.info(f"组合模式: {combined_results['avg_time']:.6f}秒")
            results[f"combined_{size}"] = combined_results
            
            # 计算加速比
            speedup = normal_results['avg_time'] / combined_results['avg_time']
            logger.info(f"组合模式加速比: {speedup:.2f}x")
    
    return results


def test_parallel_matrix_multiplication():
    """测试并行矩阵乘法性能"""
    logger.info("=" * 80)
    logger.info("并行矩阵乘法性能测试")
    logger.info("=" * 80)
    
    results = {}
    size = 1000  # 使用中等大小的矩阵
    
    # 创建多个矩阵对
    matrices = [create_test_matrices(size) for _ in range(CPU_COUNT * 2)]
    
    logger.info(f"\n测试 {len(matrices)} 个 {size}x{size} 矩阵的并行乘法:")
    
    # 串行执行 - 普通模式
    start_time = time.time()
    serial_results = [matrix_multiply_normal(*pair) for pair in matrices]
    serial_time = time.time() - start_time
    logger.info(f"串行执行 (普通模式): {serial_time:.6f}秒")
    results["serial_normal"] = serial_time
    
    # 并行执行 - 普通模式
    start_time = time.time()
    parallel_normal_results = parallel_matrix_multiply(matrix_multiply_normal, matrices)
    parallel_normal_time = time.time() - start_time
    logger.info(f"并行执行 (普通模式): {parallel_normal_time:.6f}秒")
    results["parallel_normal"] = parallel_normal_time
    
    # 计算加速比
    speedup = serial_time / parallel_normal_time
    logger.info(f"普通模式加速比: {speedup:.2f}x")
    
    # NumPy gil_state模式
    if has_np_gil_state:
        # 并行执行 - NumPy gil_state模式
        start_time = time.time()
        parallel_numpy_gil_results = parallel_matrix_multiply(matrix_multiply_numpy_gil, matrices)
        parallel_numpy_gil_time = time.time() - start_time
        logger.info(f"并行执行 (NumPy gil_state模式): {parallel_numpy_gil_time:.6f}秒")
        results["parallel_numpy_gil"] = parallel_numpy_gil_time
        
        # 计算加速比
        speedup = serial_time / parallel_numpy_gil_time
        logger.info(f"NumPy gil_state模式加速比: {speedup:.2f}x")
    
    # Python nogil模式
    if has_nogil:
        # 并行执行 - Python nogil模式
        start_time = time.time()
        parallel_python_nogil_results = parallel_matrix_multiply(matrix_multiply_python_nogil, matrices)
        parallel_python_nogil_time = time.time() - start_time
        logger.info(f"并行执行 (Python nogil模式): {parallel_python_nogil_time:.6f}秒")
        results["parallel_python_nogil"] = parallel_python_nogil_time
        
        # 计算加速比
        speedup = serial_time / parallel_python_nogil_time
        logger.info(f"Python nogil模式加速比: {speedup:.2f}x")
    
    # 组合模式
    if has_np_gil_state and has_nogil:
        # 并行执行 - 组合模式
        start_time = time.time()
        parallel_combined_results = parallel_matrix_multiply(matrix_multiply_combined, matrices)
        parallel_combined_time = time.time() - start_time
        logger.info(f"并行执行 (组合模式): {parallel_combined_time:.6f}秒")
        results["parallel_combined"] = parallel_combined_time
        
        # 计算加速比
        speedup = serial_time / parallel_combined_time
        logger.info(f"组合模式加速比: {speedup:.2f}x")
    
    return results


def generate_summary_report(matrix_results, complex_results, parallel_results):
    """生成性能测试摘要报告"""
    logger.info("=" * 80)
    logger.info("性能测试摘要报告")
    logger.info("=" * 80)
    
    # 系统信息
    logger.info(f"CPU核心数: {CPU_COUNT}")
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"NumPy版本: {np.__version__}")
    logger.info(f"nogil支持: {has_nogil}")
    logger.info(f"NumPy gil_state支持: {has_np_gil_state}")
    
    # 矩阵乘法摘要
    logger.info("\n矩阵乘法性能摘要:")
    for size in MATRIX_SIZES:
        logger.info(f"  {size}x{size} 矩阵:")
        
        normal_time = matrix_results.get(f"normal_{size}", {}).get('avg_time', 0)
        logger.info(f"    普通模式: {normal_time:.6f}秒")
        
        if has_np_gil_state:
            numpy_gil_time = matrix_results.get(f"numpy_gil_{size}", {}).get('avg_time', 0)
            speedup = normal_time / numpy_gil_time if numpy_gil_time > 0 else 0
            logger.info(f"    NumPy gil_state模式: {numpy_gil_time:.6f}秒 (加速比: {speedup:.2f}x)")
        
        if has_nogil:
            python_nogil_time = matrix_results.get(f"python_nogil_{size}", {}).get('avg_time', 0)
            speedup = normal_time / python_nogil_time if python_nogil_time > 0 else 0
            logger.info(f"    Python nogil模式: {python_nogil_time:.6f}秒 (加速比: {speedup:.2f}x)")
        
        if has_np_gil_state and has_nogil:
            combined_time = matrix_results.get(f"combined_{size}", {}).get('avg_time', 0)
            speedup = normal_time / combined_time if combined_time > 0 else 0
            logger.info(f"    组合模式: {combined_time:.6f}秒 (加速比: {speedup:.2f}x)")
    
    # 复数数组操作摘要
    logger.info("\n复数数组操作性能摘要:")
    for size in VECTOR_SIZES:
        logger.info(f"  {size} 元素复数数组:")
        
        normal_time = complex_results.get(f"normal_{size}", {}).get('avg_time', 0)
        logger.info(f"    普通模式: {normal_time:.6f}秒")
        
        if has_np_gil_state:
            numpy_gil_time = complex_results.get(f"numpy_gil_{size}", {}).get('avg_time', 0)
            speedup = normal_time / numpy_gil_time if numpy_gil_time > 0 else 0
            logger.info(f"    NumPy gil_state模式: {numpy_gil_time:.6f}秒 (加速比: {speedup:.2f}x)")
        
        if has_nogil:
            python_nogil_time = complex_results.get(f"python_nogil_{size}", {}).get('avg_time', 0)
            speedup = normal_time / python_nogil_time if python_nogil_time > 0 else 0
            logger.info(f"    Python nogil模式: {python_nogil_time:.6f}秒 (加速比: {speedup:.2f}x)")
        
        if has_np_gil_state and has_nogil:
            combined_time = complex_results.get(f"combined_{size}", {}).get('avg_time', 0)
            speedup = normal_time / combined_time if combined_time > 0 else 0
            logger.info(f"    组合模式: {combined_time:.6f}秒 (加速比: {speedup:.2f}x)")
    
    # 并行矩阵乘法摘要
    logger.info("\n并行矩阵乘法性能摘要:")
    
    serial_time = parallel_results.get("serial_normal", 0)
    logger.info(f"  串行执行: {serial_time:.6f}秒")
    
    parallel_normal_time = parallel_results.get("parallel_normal", 0)
    speedup = serial_time / parallel_normal_time if parallel_normal_time > 0 else 0
    logger.info(f"  并行执行 (普通模式): {parallel_normal_time:.6f}秒 (加速比: {speedup:.2f}x)")
    
    if has_np_gil_state:
        parallel_numpy_gil_time = parallel_results.get("parallel_numpy_gil", 0)
        speedup = serial_time / parallel_numpy_gil_time if parallel_numpy_gil_time > 0 else 0
        logger.info(f"  并行执行 (NumPy gil_state模式): {parallel_numpy_gil_time:.6f}秒 (加速比: {speedup:.2f}x)")
    
    if has_nogil:
        parallel_python_nogil_time = parallel_results.get("parallel_python_nogil", 0)
        speedup = serial_time / parallel_python_nogil_time if parallel_python_nogil_time > 0 else 0
        logger.info(f"  并行执行 (Python nogil模式): {parallel_python_nogil_time:.6f}秒 (加速比: {speedup:.2f}x)")
    
    if has_np_gil_state and has_nogil:
        parallel_combined_time = parallel_results.get("parallel_combined", 0)
        speedup = serial_time / parallel_combined_time if parallel_combined_time > 0 else 0
        logger.info(f"  并行执行 (组合模式): {parallel_combined_time:.6f}秒 (加速比: {speedup:.2f}x)")
    
    # 最佳实践建议
    logger.info("\n最佳实践建议:")
    
    if has_np_gil_state and has_nogil:
        logger.info("  1. 对于计算密集型任务，组合模式（Python nogil + NumPy gil_state）通常提供最佳性能")
        logger.info("  2. 对于并行计算，释放GIL至关重要，可以获得接近线性的加速比")
    elif has_np_gil_state:
        logger.info("  1. NumPy gil_state模式可以显著提高计算密集型任务的性能")
        logger.info("  2. 对于并行计算，使用NumPy gil_state可以获得良好的加速比")
    elif has_nogil:
        logger.info("  1. Python nogil模式可以显著提高并行计算性能")
        logger.info("  2. 对于计算密集型任务，nogil模式也能提供性能提升")
    else:
        logger.info("  1. 当前环境不支持高级GIL管理，建议升级到Python 3.13+和NumPy 2.x以获得更好的性能")
    
    logger.info("=" * 80)


def main():
    """主函数"""
    logger.info("开始NumPy 2.x GIL管理性能基准测试")
    
    # 测试矩阵乘法
    matrix_results = test_matrix_multiplication()
    
    # 测试复数数组操作
    complex_results = test_complex_array_operations()
    
    # 测试并行矩阵乘法
    parallel_results = test_parallel_matrix_multiplication()
    
    # 生成摘要报告
    generate_summary_report(matrix_results, complex_results, parallel_results)
    
    logger.info("NumPy 2.x GIL管理性能基准测试完成")


if __name__ == "__main__":
    main()
