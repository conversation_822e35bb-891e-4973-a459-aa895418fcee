"""
API版本适配算子示例

本示例展示了如何使用API版本适配算子进行API版本适配。
"""

import sys
import os
import json
import logging
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入API版本适配算子
from src.transcendental_tensor.api_compatibility import (
    Version, VersionRange, APIVersion, APIMethod, APIInterface,
    VersionMapping, AdapterConfig, AdaptationResult,
    VersionFormat, CompatibilityLevel, AdaptationStrategy,
    VersionManager, APIVersionAdapterOperator
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


# 示例1：基本版本适配
def example_basic_adaptation():
    """基本版本适配示例"""
    logger.info("示例1：基本版本适配")
    
    # 创建API版本适配算子
    adapter_operator = APIVersionAdapterOperator(name="example_adapter")
    
    # 注册版本
    adapter_operator.register_version(APIVersion(
        name="API v1.0",
        version=Version("1.0.0"),
        description="API版本1.0"
    ))
    
    adapter_operator.register_version(APIVersion(
        name="API v2.0",
        version=Version("2.0.0"),
        description="API版本2.0"
    ))
    
    # 设置版本兼容性
    adapter_operator.set_compatibility("1.0.0", "2.0.0", bidirectional=True)
    
    # 创建版本映射
    mapping = VersionMapping(
        source_version=Version("1.0.0"),
        target_version=Version("2.0.0"),
        method_mappings={
            "get_data": "getData",
            "set_config": "setConfig"
        },
        parameter_mappings={
            "get_data": {
                "id": "identifier",
                "type": "dataType"
            },
            "set_config": {
                "config": "configuration",
                "options": "settings"
            }
        },
        return_mappings={
            "get_data": "DataResponse",
            "set_config": "ConfigResponse"
        },
        bidirectional=True
    )
    
    adapter_operator.register_mapping(mapping)
    
    # 创建测试数据
    data_v1 = {
        "get_data": {
            "id": 123,
            "type": "user"
        },
        "set_config": {
            "config": {
                "theme": "dark",
                "language": "en"
            },
            "options": {
                "cache": True,
                "timeout": 30
            }
        }
    }
    
    # 适配到v2.0
    data_v2 = adapter_operator.apply(
        data_v1,
        source_version="1.0.0",
        target_version="2.0.0"
    )
    
    logger.info(f"V1数据: {json.dumps(data_v1, indent=2)}")
    logger.info(f"V2数据: {json.dumps(data_v2, indent=2)}")
    
    # 适配回v1.0
    data_v1_restored = adapter_operator.apply(
        data_v2,
        source_version="2.0.0",
        target_version="1.0.0"
    )
    
    logger.info(f"恢复的V1数据: {json.dumps(data_v1_restored, indent=2)}")


# 示例2：使用不同的适配策略
def example_adaptation_strategies():
    """适配策略示例"""
    logger.info("示例2：使用不同的适配策略")
    
    # 创建API版本适配算子
    adapter_operator = APIVersionAdapterOperator(name="strategy_adapter")
    
    # 注册版本
    adapter_operator.register_version(APIVersion(
        name="API v1.0",
        version=Version("1.0.0"),
        description="API版本1.0"
    ))
    
    adapter_operator.register_version(APIVersion(
        name="API v2.0",
        version=Version("2.0.0"),
        description="API版本2.0"
    ))
    
    # 创建测试数据
    class UserV1:
        def __init__(self, id, name, email):
            self.id = id
            self.name = name
            self.email = email
        
        def get_info(self):
            return {
                "id": self.id,
                "name": self.name,
                "email": self.email
            }
        
        def update(self, data):
            if "name" in data:
                self.name = data["name"]
            if "email" in data:
                self.email = data["email"]
            return True
    
    user_v1 = UserV1(123, "John Doe", "<EMAIL>")
    
    # 直接适配
    logger.info("直接适配策略")
    direct_result = adapter_operator.apply(
        user_v1,
        source_version="1.0.0",
        target_version="2.0.0",
        strategy=AdaptationStrategy.DIRECT
    )
    logger.info(f"直接适配结果: {direct_result}")
    
    # 包装适配
    logger.info("包装适配策略")
    
    # 创建版本映射
    mapping = VersionMapping(
        source_version=Version("1.0.0"),
        target_version=Version("2.0.0"),
        method_mappings={
            "get_info": "getInfo",
            "update": "updateUser"
        }
    )
    
    adapter_operator.register_mapping(mapping)
    
    wrapper_result = adapter_operator.apply(
        user_v1,
        source_version="1.0.0",
        target_version="2.0.0",
        strategy=AdaptationStrategy.WRAPPER
    )
    
    logger.info(f"包装适配结果类型: {type(wrapper_result)}")
    logger.info(f"调用getInfo方法: {wrapper_result.getInfo()}")
    logger.info(f"调用updateUser方法: {wrapper_result.updateUser({'name': 'Jane Doe'})}")


# 示例3：使用装饰器
def example_decorators():
    """装饰器示例"""
    logger.info("示例3：使用装饰器")
    
    # 创建API版本适配算子
    adapter_operator = APIVersionAdapterOperator(name="decorator_adapter")
    
    # 注册版本
    adapter_operator.register_version(APIVersion(
        name="API v1.0",
        version=Version("1.0.0"),
        description="API版本1.0"
    ))
    
    adapter_operator.register_version(APIVersion(
        name="API v2.0",
        version=Version("2.0.0"),
        description="API版本2.0"
    ))
    
    # 创建版本映射
    mapping = VersionMapping(
        source_version=Version("1.0.0"),
        target_version=Version("2.0.0"),
        parameter_mappings={
            "get_user": {
                "user_id": "id",
                "include_details": "details"
            }
        }
    )
    
    adapter_operator.register_mapping(mapping)
    
    # 使用适配器装饰器
    @adapter_operator.get_adapter_decorator("1.0.0", "2.0.0")
    def get_user_data_v1(user_id, include_details=False):
        """获取用户数据（v1.0）"""
        return {
            "user_id": user_id,
            "name": "John Doe",
            "email": "<EMAIL>",
            "details": {
                "age": 30,
                "address": "123 Main St"
            } if include_details else None
        }
    
    # 使用参数适配器装饰器
    @adapter_operator.get_parameter_adapter_decorator(
        "2.0.0", "1.0.0",
        parameter_mappings={
            "id": "user_id",
            "details": "include_details"
        }
    )
    def get_user_data_v2(id, details=False):
        """获取用户数据（v2.0）"""
        return {
            "id": id,
            "name": "John Doe",
            "email": "<EMAIL>",
            "details": {
                "age": 30,
                "address": "123 Main St"
            } if details else None
        }
    
    # 调用v1函数，结果会被适配为v2格式
    result_v2 = get_user_data_v1(123, include_details=True)
    logger.info(f"V1函数适配为V2结果: {result_v2}")
    
    # 调用v2函数，但使用v1参数
    result_v1 = get_user_data_v2(user_id=456, include_details=True)
    logger.info(f"使用V1参数调用V2函数: {result_v1}")


# 示例4：版本管理
def example_version_management():
    """版本管理示例"""
    logger.info("示例4：版本管理")
    
    # 创建版本管理器
    version_manager = VersionManager(name="example_manager", current_version="2.0.0")
    
    # 添加版本
    version_manager.add_version(APIVersion(
        name="API v1.0",
        version=Version("1.0.0"),
        description="初始版本",
        release_date="2022-01-01"
    ))
    
    version_manager.add_version(APIVersion(
        name="API v1.1",
        version=Version("1.1.0"),
        description="功能更新",
        release_date="2022-03-15",
        features=["添加新的查询参数", "优化性能"]
    ))
    
    version_manager.add_version(APIVersion(
        name="API v2.0",
        version=Version("2.0.0"),
        description="重大更新",
        release_date="2022-06-30",
        features=["重构API结构", "添加新的认证机制"],
        breaking_changes=["更改了用户API的参数名", "移除了废弃的方法"]
    ))
    
    # 设置兼容性
    version_manager.set_compatibility("1.0.0", "1.1.0", bidirectional=True)
    version_manager.set_compatibility("1.1.0", "2.0.0")
    
    # 检查兼容性
    logger.info(f"1.0.0 兼容 1.1.0: {version_manager.is_compatible('1.0.0', '1.1.0')}")
    logger.info(f"1.1.0 兼容 1.0.0: {version_manager.is_compatible('1.1.0', '1.0.0')}")
    logger.info(f"1.1.0 兼容 2.0.0: {version_manager.is_compatible('1.1.0', '2.0.0')}")
    logger.info(f"2.0.0 兼容 1.1.0: {version_manager.is_compatible('2.0.0', '1.1.0')}")
    
    # 获取兼容版本
    logger.info(f"1.0.0 的兼容版本: {version_manager.get_compatible_versions('1.0.0')}")
    logger.info(f"1.1.0 的兼容版本: {version_manager.get_compatible_versions('1.1.0')}")
    logger.info(f"2.0.0 的兼容版本: {version_manager.get_compatible_versions('2.0.0')}")
    
    # 查找迁移路径
    logger.info(f"1.0.0 到 2.0.0 的迁移路径: {version_manager.find_migration_path('1.0.0', '2.0.0')}")
    
    # 保存配置
    config_file = "version_config.json"
    version_manager.save_config(config_file)
    logger.info(f"配置已保存到: {config_file}")
    
    # 加载配置
    new_manager = VersionManager()
    new_manager.load_config(config_file)
    logger.info(f"从配置加载的版本: {list(new_manager.versions.keys())}")
    
    # 清理
    if os.path.exists(config_file):
        os.remove(config_file)


# 示例5：复杂对象适配
def example_complex_adaptation():
    """复杂对象适配示例"""
    logger.info("示例5：复杂对象适配")
    
    # 创建API版本适配算子
    adapter_operator = APIVersionAdapterOperator(name="complex_adapter")
    
    # 定义v1类
    class UserV1:
        def __init__(self, id, name, email, roles=None):
            self.id = id
            self.name = name
            self.email = email
            self.roles = roles or []
        
        def to_dict(self):
            return {
                "id": self.id,
                "name": self.name,
                "email": self.email,
                "roles": self.roles
            }
    
    class OrderV1:
        def __init__(self, id, user, items, total):
            self.id = id
            self.user = user
            self.items = items
            self.total = total
        
        def to_dict(self):
            return {
                "id": self.id,
                "user": self.user.to_dict() if isinstance(self.user, UserV1) else self.user,
                "items": self.items,
                "total": self.total
            }
    
    # 定义v2类
    class UserV2:
        def __init__(self, identifier, full_name, contact, permissions=None):
            self.identifier = identifier
            self.full_name = full_name
            self.contact = contact
            self.permissions = permissions or []
        
        def to_dict(self):
            return {
                "identifier": self.identifier,
                "full_name": self.full_name,
                "contact": self.contact,
                "permissions": self.permissions
            }
    
    class OrderV2:
        def __init__(self, order_id, customer, products, amount):
            self.order_id = order_id
            self.customer = customer
            self.products = products
            self.amount = amount
        
        def to_dict(self):
            return {
                "order_id": self.order_id,
                "customer": self.customer.to_dict() if hasattr(self.customer, "to_dict") else self.customer,
                "products": self.products,
                "amount": self.amount
            }
    
    # 创建自定义适配器
    def user_v1_to_v2(user_v1):
        if isinstance(user_v1, dict):
            return {
                "identifier": user_v1.get("id"),
                "full_name": user_v1.get("name"),
                "contact": user_v1.get("email"),
                "permissions": user_v1.get("roles", [])
            }
        else:
            return UserV2(
                identifier=user_v1.id,
                full_name=user_v1.name,
                contact=user_v1.email,
                permissions=user_v1.roles
            )
    
    def order_v1_to_v2(order_v1):
        if isinstance(order_v1, dict):
            return {
                "order_id": order_v1.get("id"),
                "customer": user_v1_to_v2(order_v1.get("user", {})),
                "products": order_v1.get("items", []),
                "amount": order_v1.get("total", 0)
            }
        else:
            return OrderV2(
                order_id=order_v1.id,
                customer=user_v1_to_v2(order_v1.user),
                products=order_v1.items,
                amount=order_v1.total
            )
    
    # 创建适配器配置
    adapter_config = AdapterConfig(
        name="complex_adapter",
        source_version=Version("1.0.0"),
        target_version=Version("2.0.0"),
        strategy=AdaptationStrategy.CUSTOM,
        custom_adapters={
            "UserV1": user_v1_to_v2,
            "OrderV1": order_v1_to_v2,
            "dict": lambda obj: order_v1_to_v2(obj) if "items" in obj else user_v1_to_v2(obj)
        }
    )
    
    adapter_operator.register_adapter_config(adapter_config)
    
    # 创建测试数据
    user_v1 = UserV1(123, "John Doe", "<EMAIL>", ["admin", "user"])
    order_v1 = OrderV1(
        456,
        user_v1,
        [{"id": 1, "name": "Product 1", "price": 10.99}, {"id": 2, "name": "Product 2", "price": 20.99}],
        31.98
    )
    
    # 适配用户
    user_v2 = adapter_operator.apply(
        user_v1,
        source_version="1.0.0",
        target_version="2.0.0",
        strategy=AdaptationStrategy.CUSTOM
    )
    
    logger.info(f"V1用户: {user_v1.to_dict()}")
    logger.info(f"V2用户: {user_v2.to_dict() if hasattr(user_v2, 'to_dict') else user_v2}")
    
    # 适配订单
    order_v2 = adapter_operator.apply(
        order_v1,
        source_version="1.0.0",
        target_version="2.0.0",
        strategy=AdaptationStrategy.CUSTOM
    )
    
    logger.info(f"V1订单: {order_v1.to_dict()}")
    logger.info(f"V2订单: {order_v2.to_dict() if hasattr(order_v2, 'to_dict') else order_v2}")


def main():
    """主函数"""
    logger.info("API版本适配算子示例")
    
    # 运行示例
    example_basic_adaptation()
    print("\n" + "-" * 80 + "\n")
    
    example_adaptation_strategies()
    print("\n" + "-" * 80 + "\n")
    
    example_decorators()
    print("\n" + "-" * 80 + "\n")
    
    example_version_management()
    print("\n" + "-" * 80 + "\n")
    
    example_complex_adaptation()
    
    logger.info("示例完成")


if __name__ == "__main__":
    main()
