"""
多模态融合编码系统使用示例

本示例展示了如何使用多模态融合编码系统处理不同类型的数据。
"""

import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import librosa
import logging

from src.transcendental_tensor.multimodal_fusion.multimodal_fusion_operator import (
    MultimodalFusionOperator,
    ModalityType,
    ModalityConfig
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def preprocess_image(image_path: str) -> np.ndarray:
    """预处理图像数据"""
    # 加载并调整图像大小
    image = Image.open(image_path).convert('L')  # 转换为灰度图
    image = image.resize((64, 64))  # 调整大小
    return np.array(image)

def preprocess_audio(audio_path: str) -> np.ndarray:
    """预处理音频数据"""
    # 加载音频
    y, sr = librosa.load(audio_path, duration=5)  # 加载前5秒
    # 提取MFCC特征
    mfcc = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=20)
    return mfcc

def create_quantum_data() -> np.ndarray:
    """创建模拟量子数据"""
    # 创建一个4量子比特系统的状态
    n_qubits = 4
    dim = 2 ** n_qubits
    state = np.random.rand(dim) + 1j * np.random.rand(dim)
    # 归一化
    state = state / np.linalg.norm(state)
    return state

def plot_fusion_results(original_data: list, fusion_result: dict):
    """可视化融合结果"""
    plt.figure(figsize=(15, 5))
    
    # 绘制原始数据
    for i, data in enumerate(original_data):
        plt.subplot(1, len(original_data) + 1, i + 1)
        if isinstance(data, np.ndarray):
            if data.ndim == 2:
                plt.imshow(np.abs(data), cmap='viridis')
            else:
                plt.plot(np.abs(data))
        plt.title(f'Original Data {i+1}')
        
    # 绘制融合结果
    plt.subplot(1, len(original_data) + 1, len(original_data) + 1)
    fused_data = fusion_result["fused_data"]
    if isinstance(fused_data, np.ndarray):
        if fused_data.ndim == 2:
            plt.imshow(np.abs(fused_data), cmap='viridis')
        else:
            plt.plot(np.abs(fused_data))
    plt.title(f'Fused Data\nQuality: {fusion_result["fusion_quality"]:.4f}')
    
    plt.tight_layout()
    plt.show()

def main():
    """主函数"""
    # 创建融合算子
    fusion_op = MultimodalFusionOperator()
    
    # 注册模态
    modality_configs = {
        "quantum": ModalityConfig(
            type=ModalityType.QUANTUM,
            dimension=16,
            encoding_params={"encoding_type": "amplitude"}
        ),
        "visual": ModalityConfig(
            type=ModalityType.VISUAL,
            dimension=32,
            encoding_params={"use_dct": True}
        ),
        "audio": ModalityConfig(
            type=ModalityType.AUDIO,
            dimension=24,
            encoding_params={"use_mfcc": True}
        )
    }
    
    for name, config in modality_configs.items():
        fusion_op.register_modality(name, config)
    
    # 准备数据
    try:
        # 量子数据
        quantum_data = create_quantum_data()
        logger.info("量子数据已生成，维度: %s", quantum_data.shape)
        
        # 视觉数据
        visual_data = preprocess_image("examples/data/sample_image.png")
        logger.info("图像数据已加载，维度: %s", visual_data.shape)
        
        # 音频数据
        audio_data = preprocess_audio("examples/data/sample_audio.wav")
        logger.info("音频数据已加载，维度: %s", audio_data.shape)
        
        # 编码数据
        encoded_data = []
        
        # 编码量子数据
        quantum_result = fusion_op.encode_modality(quantum_data, "quantum")
        if quantum_result["success"]:
            encoded_data.append(quantum_result)
            logger.info("量子数据编码成功")
        
        # 编码视觉数据
        visual_result = fusion_op.encode_modality(visual_data, "visual")
        if visual_result["success"]:
            encoded_data.append(visual_result)
            logger.info("视觉数据编码成功")
        
        # 编码音频数据
        audio_result = fusion_op.encode_modality(audio_data, "audio")
        if audio_result["success"]:
            encoded_data.append(audio_result)
            logger.info("音频数据编码成功")
        
        # 执行融合
        fusion_result = fusion_op.fuse_modalities(encoded_data)
        
        if fusion_result["success"]:
            logger.info("融合成功，质量评分: %.4f", fusion_result["fusion_quality"])
            
            # 可视化结果
            plot_fusion_results(
                [quantum_data, visual_data, audio_data],
                fusion_result
            )
            
            # 打印性能统计
            stats = fusion_op.stats
            logger.info("性能统计:")
            logger.info("  - 总融合次数: %d", stats["fusions_performed"])
            logger.info("  - 成功融合次数: %d", stats["fusions_succeeded"])
            logger.info("  - 失败融合次数: %d", stats["fusions_failed"])
            logger.info("  - 平均融合质量: %.4f", stats["average_fusion_quality"])
            
        else:
            logger.error("融合失败: %s", fusion_result.get("error", "未知错误"))
            
    except Exception as e:
        logger.error("处理过程中出错: %s", str(e))
        raise

if __name__ == "__main__":
    main()