#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超越态思维引擎算子库高级使用示例

本示例展示了超越态思维引擎算子库的高级用法，包括：
1. 使用多种类型的算子
2. 构建复杂的算子组合
3. 使用高级优化技术
4. 处理大规模数据
5. 使用分布式计算
"""

import numpy as np
import matplotlib.pyplot as plt
import time
import os
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("advanced_usage.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("advanced_usage")

# 导入算子库模块
# 注意：在实际使用中，导入路径可能不同
from src.operators.transform import TransformOperator, LinearTransformOperator, NonlinearTransformOperator, ProjectionTransformOperator
from src.operators.evolution import EvolutionOperator, DifferentialEquationOperator, StochasticProcessOperator
from src.operators.optimization.fusion import fuse_operators, create_fusion_pipeline, optimize_pipeline
from src.operators.optimization.parallel import parallelize, parallel_map, ParallelExecutor
from src.operators.optimization.memory import memory_efficient, MemoryPool
from src.operators.optimization.cache import cache_result, CacheManager
from src.operators.distributed import DistributedOperator, DistributedEnvironment, DataPartitioner, ResultAggregator
from src.operators.compatibility import DataConverter


def main():
    """主函数"""
    logger.info("超越态思维引擎算子库高级使用示例")
    logger.info("=" * 50)

    # 创建示例数据
    logger.info("\n1. 创建示例数据")
    data = create_sample_data(num_samples=10000, dimension=10)
    logger.info(f"数据形状: {data.shape}")
    logger.info(f"数据范围: [{np.min(data)}, {np.max(data)}]")
    logger.info(f"数据大小: {data.nbytes / (1024 * 1024):.2f} MB")

    # 使用多种类型的算子
    logger.info("\n2. 使用多种类型的算子")
    
    # 2.1 使用线性变换算子
    linear_transform = create_linear_transform()
    linear_result = apply_transform(linear_transform, data)
    
    # 2.2 使用非线性变换算子
    nonlinear_transform = create_nonlinear_transform()
    nonlinear_result = apply_transform(nonlinear_transform, linear_result)
    
    # 2.3 使用投影变换算子
    projection_transform = create_projection_transform()
    projected_result = apply_transform(projection_transform, nonlinear_result)
    logger.info(f"投影后数据形状: {projected_result.shape}")
    
    # 2.4 使用微分方程演化算子
    diff_eq_evolution = create_differential_equation_operator()
    diff_eq_result = apply_evolution(diff_eq_evolution, projected_result)
    
    # 2.5 使用随机过程演化算子
    stochastic_evolution = create_stochastic_process_operator()
    stochastic_result = apply_evolution(stochastic_evolution, diff_eq_result)
    
    # 构建复杂的算子组合
    logger.info("\n3. 构建复杂的算子组合")
    
    # 3.1 创建算子列表
    operators = [
        linear_transform,
        nonlinear_transform,
        projection_transform,
        diff_eq_evolution,
        stochastic_evolution
    ]
    
    # 3.2 顺序应用
    start_time = time.time()
    sequential_result = apply_sequential(operators, data)
    sequential_time = time.time() - start_time
    logger.info(f"顺序应用耗时: {sequential_time:.6f}秒")
    
    # 3.3 使用算子融合
    start_time = time.time()
    fused_result = apply_fused(operators, data)
    fused_time = time.time() - start_time
    logger.info(f"融合应用耗时: {fused_time:.6f}秒")
    logger.info(f"加速比: {sequential_time / fused_time:.2f}x")
    
    # 3.4 使用流水线
    start_time = time.time()
    pipeline_result = apply_pipeline(operators, data)
    pipeline_time = time.time() - start_time
    logger.info(f"流水线应用耗时: {pipeline_time:.6f}秒")
    logger.info(f"加速比: {sequential_time / pipeline_time:.2f}x")
    
    # 验证结果一致性
    logger.info(f"融合结果一致性: {np.allclose(sequential_result, fused_result, rtol=1e-5)}")
    logger.info(f"流水线结果一致性: {np.allclose(sequential_result, pipeline_result, rtol=1e-5)}")
    
    # 使用高级优化技术
    logger.info("\n4. 使用高级优化技术")
    
    # 4.1 使用并行执行器
    start_time = time.time()
    parallel_result = apply_parallel_executor(operators, data)
    parallel_time = time.time() - start_time
    logger.info(f"并行执行器耗时: {parallel_time:.6f}秒")
    logger.info(f"加速比: {sequential_time / parallel_time:.2f}x")
    
    # 4.2 使用内存优化
    start_time = time.time()
    memory_result = apply_memory_efficient(operators, data)
    memory_time = time.time() - start_time
    logger.info(f"内存优化耗时: {memory_time:.6f}秒")
    
    # 4.3 使用缓存管理器
    cache_manager = CacheManager()
    cache_manager.register_cache('evolution_cache', cache_type='lru', maxsize=128)
    
    # 第一次调用（计算）
    start_time = time.time()
    cache_result1 = apply_with_cache_manager(operators, data, cache_manager)
    first_call_time = time.time() - start_time
    logger.info(f"第一次调用耗时: {first_call_time:.6f}秒")
    
    # 第二次调用（从缓存获取）
    start_time = time.time()
    cache_result2 = apply_with_cache_manager(operators, data, cache_manager)
    second_call_time = time.time() - start_time
    logger.info(f"第二次调用耗时: {second_call_time:.6f}秒")
    logger.info(f"加速比: {first_call_time / second_call_time:.2f}x")
    
    # 处理大规模数据
    logger.info("\n5. 处理大规模数据")
    
    # 5.1 创建大规模数据
    large_data = create_sample_data(num_samples=100000, dimension=10)
    logger.info(f"大规模数据形状: {large_data.shape}")
    logger.info(f"大规模数据大小: {large_data.nbytes / (1024 * 1024):.2f} MB")
    
    # 5.2 使用分块处理
    start_time = time.time()
    chunked_result = process_in_chunks(operators[0], large_data, chunk_size=10000)
    chunked_time = time.time() - start_time
    logger.info(f"分块处理耗时: {chunked_time:.6f}秒")
    
    # 5.3 使用内存池
    memory_pool = MemoryPool(pool_size=1024*1024*100)  # 100MB
    start_time = time.time()
    pool_result = process_with_memory_pool(operators[0], large_data, memory_pool)
    pool_time = time.time() - start_time
    logger.info(f"内存池处理耗时: {pool_time:.6f}秒")
    
    # 使用分布式计算
    logger.info("\n6. 使用分布式计算")
    
    # 6.1 创建分布式环境（模拟）
    env = create_distributed_environment()
    
    # 6.2 使用分布式算子
    distributed_transform = create_distributed_operator(operators[0])
    
    # 6.3 分片数据
    partitioner = DataPartitioner(partition_strategy='equal')
    data_partitions = partitioner.partition(data, num_partitions=4)
    logger.info(f"数据分片数: {len(data_partitions)}")
    
    # 6.4 分布式处理
    start_time = time.time()
    distributed_results = process_distributed(distributed_transform, data_partitions, env)
    distributed_time = time.time() - start_time
    logger.info(f"分布式处理耗时: {distributed_time:.6f}秒")
    
    # 6.5 聚合结果
    aggregator = ResultAggregator(aggregation_method='mean')
    aggregated_result = aggregator.aggregate(distributed_results)
    logger.info(f"聚合结果形状: {aggregated_result.shape}")
    
    # 数据转换
    logger.info("\n7. 数据转换")
    
    # 7.1 使用数据转换器
    converter = DataConverter(
        source_format='numpy',
        target_format='list'
    )
    
    converted_data = converter.apply(data[:10])
    logger.info(f"转换后数据类型: {type(converted_data)}")
    logger.info(f"转换后数据长度: {len(converted_data)}")
    
    # 可视化结果
    logger.info("\n8. 可视化结果")
    visualize_results(data, sequential_result, fused_result, pipeline_result)
    
    logger.info("\n示例完成！")


def create_sample_data(num_samples=1000, dimension=3):
    """创建示例数据"""
    logger.info(f"创建{num_samples}个{dimension}维样本")
    
    # 创建随机数据
    data = np.random.random((num_samples, dimension))
    
    # 添加一些结构
    t = np.linspace(0, 10, num_samples)
    for i in range(min(dimension, 3)):
        if i == 0:
            data[:, i] = np.sin(t) + 0.1 * np.random.randn(num_samples)
        elif i == 1:
            data[:, i] = np.cos(t) + 0.1 * np.random.randn(num_samples)
        elif i == 2:
            data[:, i] = t / 10 + 0.1 * np.random.randn(num_samples)
    
    return data


def create_linear_transform():
    """创建线性变换算子"""
    logger.info("创建线性变换算子")
    
    # 创建旋转矩阵
    dimension = 10
    theta = np.pi / 4  # 45度旋转
    
    # 在前两个维度上应用旋转
    matrix = np.eye(dimension)
    matrix[0, 0] = np.cos(theta)
    matrix[0, 1] = -np.sin(theta)
    matrix[1, 0] = np.sin(theta)
    matrix[1, 1] = np.cos(theta)
    
    # 创建偏移
    offset = np.zeros(dimension)
    offset[0] = 0.5
    offset[1] = 0.5
    
    # 创建线性变换算子
    transform = LinearTransformOperator(dimension, matrix, offset)
    
    return transform


def create_nonlinear_transform():
    """创建非线性变换算子"""
    logger.info("创建非线性变换算子")
    
    # 创建非线性变换算子
    transform = NonlinearTransformOperator(
        dimension=10,
        function=lambda x: np.tanh(x),
        scale=2.0
    )
    
    return transform


def create_projection_transform():
    """创建投影变换算子"""
    logger.info("创建投影变换算子")
    
    # 创建投影变换算子
    transform = ProjectionTransformOperator(
        source_dimension=10,
        target_dimension=3,
        method='pca'
    )
    
    return transform


def create_differential_equation_operator():
    """创建微分方程演化算子"""
    logger.info("创建微分方程演化算子")
    
    # 创建微分方程演化算子
    evolution = DifferentialEquationOperator(
        dimension=3,
        equation=lambda t, x: np.array([-0.1 * x[0], -0.1 * x[1], 0.1 * (1 - x[2])]),
        time_step=0.1,
        num_steps=10,
        method='rk4'
    )
    
    return evolution


def create_stochastic_process_operator():
    """创建随机过程演化算子"""
    logger.info("创建随机过程演化算子")
    
    # 创建随机过程演化算子
    evolution = StochasticProcessOperator(
        dimension=3,
        drift=lambda x: -0.1 * x,
        diffusion=lambda x: 0.05 * np.ones_like(x),
        time_step=0.1,
        num_steps=10,
        method='euler_maruyama'
    )
    
    return evolution


def apply_transform(transform, data):
    """应用变换算子"""
    logger.info(f"应用变换: {transform.__class__.__name__}")
    result = transform.apply(data)
    logger.info(f"变换后数据形状: {result.shape}")
    return result


def apply_evolution(evolution, data):
    """应用演化算子"""
    logger.info(f"应用演化: {evolution.__class__.__name__}")
    result = evolution.apply(data)
    logger.info(f"演化后数据形状: {result.shape}")
    return result


def apply_sequential(operators, data):
    """顺序应用多个算子"""
    logger.info(f"顺序应用{len(operators)}个算子")
    
    result = data
    for i, op in enumerate(operators):
        logger.info(f"应用算子 {i+1}/{len(operators)}: {op.__class__.__name__}")
        result = op.apply(result)
    
    return result


def apply_fused(operators, data):
    """使用算子融合"""
    logger.info(f"使用算子融合应用{len(operators)}个算子")
    
    fused_operator = fuse_operators(operators, mode='vertical')
    result = fused_operator.apply(data)
    
    return result


def apply_pipeline(operators, data):
    """使用流水线"""
    logger.info(f"使用流水线应用{len(operators)}个算子")
    
    pipeline = create_fusion_pipeline(operators)
    optimized_pipeline = optimize_pipeline(pipeline, optimization_level=2)
    result = optimized_pipeline.apply(data)
    
    return result


def apply_parallel_executor(operators, data):
    """使用并行执行器"""
    logger.info(f"使用并行执行器应用{len(operators)}个算子")
    
    executor = ParallelExecutor(mode='thread', max_workers=4)
    
    # 将数据分成多个部分
    num_parts = 4
    part_size = data.shape[0] // num_parts
    data_parts = [data[i*part_size:(i+1)*part_size] for i in range(num_parts)]
    
    # 定义处理函数
    def process_part(part_data):
        result = part_data
        for op in operators:
            result = op.apply(result)
        return result
    
    # 并行处理
    futures = [executor.submit(process_part, part) for part in data_parts]
    results = [future.result() for future in futures]
    
    # 合并结果
    result = np.vstack(results)
    
    return result


@memory_efficient(chunk_size=1000)
def apply_memory_efficient(operators, data):
    """使用内存高效处理"""
    logger.info(f"使用内存高效处理应用{len(operators)}个算子")
    
    result = data
    for op in operators:
        result = op.apply(result)
    
    return result


def apply_with_cache_manager(operators, data, cache_manager):
    """使用缓存管理器"""
    logger.info(f"使用缓存管理器应用{len(operators)}个算子")
    
    # 获取缓存
    cache = cache_manager.get_cache('evolution_cache')
    
    # 计算数据哈希作为缓存键
    import hashlib
    data_hash = hashlib.md5(data.tobytes()).hexdigest()
    
    # 尝试从缓存获取结果
    result = cache.get(data_hash)
    if result is not None:
        logger.info("从缓存获取结果")
        return result
    
    # 计算结果
    logger.info("计算结果")
    result = data
    for op in operators:
        result = op.apply(result)
    
    # 存入缓存
    cache.put(data_hash, result)
    
    return result


def process_in_chunks(operator, data, chunk_size=1000):
    """分块处理大规模数据"""
    logger.info(f"使用分块处理，块大小: {chunk_size}")
    
    num_chunks = (data.shape[0] + chunk_size - 1) // chunk_size
    results = []
    
    for i in range(num_chunks):
        start = i * chunk_size
        end = min((i + 1) * chunk_size, data.shape[0])
        chunk = data[start:end]
        
        logger.info(f"处理块 {i+1}/{num_chunks}, 形状: {chunk.shape}")
        result_chunk = operator.apply(chunk)
        results.append(result_chunk)
    
    result = np.vstack(results)
    return result


def process_with_memory_pool(operator, data, memory_pool):
    """使用内存池处理数据"""
    logger.info(f"使用内存池处理，池大小: {memory_pool.get_free_size() / (1024*1024):.2f} MB")
    
    # 分配内存
    result_size = data.shape[0] * operator.get_dimension() * 8  # 假设结果是float64
    result_memory = memory_pool.allocate(result_size)
    
    # 处理数据
    result = operator.apply(data)
    
    # 释放内存
    memory_pool.deallocate(result_memory)
    
    return result


def create_distributed_environment():
    """创建分布式环境（模拟）"""
    logger.info("创建分布式环境")
    
    # 创建分布式环境
    env = DistributedEnvironment(
        backend='tcp',
        host='localhost',
        port=5555
    )
    
    return env


def create_distributed_operator(base_operator):
    """创建分布式算子"""
    logger.info(f"创建分布式算子，基础算子: {base_operator.__class__.__name__}")
    
    # 创建分布式算子
    distributed_operator = DistributedOperator(
        base_operator=base_operator,
        distribution_strategy='data_parallel'
    )
    
    return distributed_operator


def process_distributed(distributed_operator, data_partitions, env):
    """分布式处理数据"""
    logger.info(f"分布式处理{len(data_partitions)}个数据分片")
    
    # 模拟分布式处理
    results = []
    for i, partition in enumerate(data_partitions):
        logger.info(f"处理分片 {i+1}/{len(data_partitions)}, 形状: {partition.shape}")
        # 在实际分布式环境中，这里会将任务分发到不同节点
        result = distributed_operator.get_base_operator().apply(partition)
        results.append(result)
    
    return results


def visualize_results(original_data, sequential_result, fused_result, pipeline_result):
    """可视化结果"""
    logger.info("可视化结果")
    
    # 只使用前1000个点进行可视化
    n = min(1000, original_data.shape[0])
    
    # 创建3D图
    fig = plt.figure(figsize=(15, 10))
    
    # 原始数据
    ax1 = fig.add_subplot(221, projection='3d')
    ax1.scatter(original_data[:n, 0], original_data[:n, 1], original_data[:n, 2], c='b', marker='o', alpha=0.6)
    ax1.set_title('原始数据')
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.set_zlabel('Z')
    
    # 顺序处理结果
    ax2 = fig.add_subplot(222, projection='3d')
    ax2.scatter(sequential_result[:n, 0], sequential_result[:n, 1], sequential_result[:n, 2], c='g', marker='o', alpha=0.6)
    ax2.set_title('顺序处理结果')
    ax2.set_xlabel('X')
    ax2.set_ylabel('Y')
    ax2.set_zlabel('Z')
    
    # 融合处理结果
    ax3 = fig.add_subplot(223, projection='3d')
    ax3.scatter(fused_result[:n, 0], fused_result[:n, 1], fused_result[:n, 2], c='r', marker='o', alpha=0.6)
    ax3.set_title('融合处理结果')
    ax3.set_xlabel('X')
    ax3.set_ylabel('Y')
    ax3.set_zlabel('Z')
    
    # 流水线处理结果
    ax4 = fig.add_subplot(224, projection='3d')
    ax4.scatter(pipeline_result[:n, 0], pipeline_result[:n, 1], pipeline_result[:n, 2], c='m', marker='o', alpha=0.6)
    ax4.set_title('流水线处理结果')
    ax4.set_xlabel('X')
    ax4.set_ylabel('Y')
    ax4.set_zlabel('Z')
    
    plt.tight_layout()
    plt.savefig('advanced_results.png')
    logger.info("结果已保存到 'advanced_results.png'")
    plt.close()


if __name__ == "__main__":
    main()
