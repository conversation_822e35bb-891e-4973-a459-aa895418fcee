#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
超越态张量库基本用法示例

这个示例展示了超越态张量库的基本用法，包括创建张量、编码数据、融合张量、演化张量和测量张量。
"""

import numpy as np
import transcendental_tensor as tt

def main():
    """主函数"""
    print(f"超越态张量库 {tt.__name__} {tt.__version__}")
    print(f"描述: {tt.__description__}")
    print()
    
    # 创建张量
    print("创建张量...")
    tensor1 = tt.TranscendentalTensor(
        quantum_dims=[2],
        holographic_dims=[2],
        fractal_dims=[2],
        coherence=0.8,
        distribution=0.7,
        self_similarity=0.6
    )
    print(tensor1)
    
    # 编码数据
    print("\n编码数据...")
    data = [0.5, 0.5, 0.5, 0.5]
    tensor1.encode(data, encoding_type="quantum")
    print(tensor1)
    
    # 创建另一个张量
    print("\n创建另一个张量...")
    tensor2 = tt.TranscendentalTensor(
        quantum_dims=[2],
        holographic_dims=[2],
        fractal_dims=[2],
        coherence=0.6,
        distribution=0.8,
        self_similarity=0.7
    )
    tensor2.encode([0.7, 0.3, 0.2, 0.8], encoding_type="holographic")
    print(tensor2)
    
    # 融合张量
    print("\n融合张量...")
    tensor1.fuse(tensor2, fusion_mode="quantum_holographic")
    print(tensor1)
    
    # 演化张量
    print("\n演化张量...")
    # 创建哈密顿量
    hamiltonian = np.array([
        [1.0+0.0j, 0.0+0.0j, 0.0+0.0j, 0.0+0.0j],
        [0.0+0.0j, -1.0+0.0j, 0.0+0.0j, 0.0+0.0j],
        [0.0+0.0j, 0.0+0.0j, -1.0+0.0j, 0.0+0.0j],
        [0.0+0.0j, 0.0+0.0j, 0.0+0.0j, 1.0+0.0j]
    ], dtype=np.complex128)
    
    tensor1.evolve(0.1, hamiltonian, evolution_mode="quantum")
    print(tensor1)
    
    # 测量张量
    print("\n测量张量...")
    result = tensor1.measure(measurement_type="adaptive")
    print(f"测量结果: {result}")
    
    # 创建状态
    print("\n创建状态...")
    state = tt.TranscendentalState(
        quantum_dims=[2],
        holographic_dims=[2],
        fractal_dims=[2],
        coherence=0.8,
        distribution=0.7,
        self_similarity=0.6
    )
    print(state)
    
    # 编码状态
    print("\n编码状态...")
    state.encode(data, encoding_type="quantum")
    print(state)
    
    # 演化状态
    print("\n演化状态...")
    state.evolve(0.1, hamiltonian, evolution_mode="quantum")
    print(state)
    
    # 测量状态
    print("\n测量状态...")
    result = state.measure(measurement_type="adaptive")
    print(f"测量结果: {result}")
    
    # 转换为张量
    print("\n转换为张量...")
    tensor3 = state.to_tensor()
    print(tensor3)
    
    # 从张量创建状态
    print("\n从张量创建状态...")
    state2 = tt.TranscendentalState.from_tensor(tensor3)
    print(state2)
    
    # 获取张量数据
    print("\n获取张量数据...")
    array = tensor1.to_array()
    print(f"张量数据形状: {array.shape}")
    
    # 从NumPy数组创建张量
    print("\n从NumPy数组创建张量...")
    array = np.ones((2, 2, 2), dtype=np.complex128)
    tensor4 = tt.TranscendentalTensor.from_array(
        array,
        quantum_dims=[2],
        holographic_dims=[2],
        fractal_dims=[2]
    )
    print(tensor4)
    
    # 重塑张量
    print("\n重塑张量...")
    tensor5 = tensor4.reshape(
        quantum_dims=[4],
        holographic_dims=[1],
        fractal_dims=[1]
    )
    print(tensor5)
    
    # 转置张量
    print("\n转置张量...")
    tensor6 = tensor4.transpose([2, 1, 0])
    print(tensor6)
    
    # 切片张量
    print("\n切片张量...")
    tensor7 = tensor4.slice([(0, 1), (0, 2), (0, 2)])
    print(tensor7)
    
    print("\n示例完成!")

if __name__ == "__main__":
    main()
