"""
算子注册表集成示例

本示例展示了如何使用适配器和代理将新的算子注册表与现有系统集成。
"""

import os
import sys
import logging
from typing import Dict, List, Any, Optional, Tuple, Union

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入算子注册表模块
try:
    from src.rust_bindings import (
        OperatorCategory,
        OperatorRegistry,
        OperatorMetadata,
        DependencyManager,
        CompatibilityLevel,
        CompatibilityChecker,
        OperatorFactory,
        get_global_registry,
        register_operator,
        get_operator,
        list_operators,
        get_operator_metadata,
        check_compatibility,
    )
    from src.rust_bindings.operator_registry.adapters import (
        RegistryAdapter,
        RegistryProxy,
        get_global_proxy,
    )
    logger.info(f"成功导入算子注册表模块")
except ImportError as e:
    logger.error(f"导入算子注册表模块失败: {e}")
    sys.exit(1)

# 模拟现有的注册表实现
class OldRegistry:
    """
    模拟现有的注册表实现
    """
    
    def __init__(self):
        """
        初始化注册表
        """
        self.operators = {}
        
    def register_operator(self, name, operator):
        """
        注册算子
        
        参数:
            name: 算子名称
            operator: 算子函数或类
            
        返回:
            是否成功注册
        """
        self.operators[name] = operator
        logger.info(f"向旧注册表注册算子: {name}")
        return True
        
    def get_operator(self, name):
        """
        获取算子
        
        参数:
            name: 算子名称
            
        返回:
            算子函数或类
        """
        return self.operators.get(name)
        
    def list_operators(self):
        """
        列出算子
        
        返回:
            算子列表
        """
        return list(self.operators.keys())

class OldCategoryRegistry:
    """
    模拟现有的带有类别的注册表实现
    """
    
    def __init__(self):
        """
        初始化注册表
        """
        self.operators = {}
        
    def register_operator(self, category, name, operator):
        """
        注册算子
        
        参数:
            category: 算子类别
            name: 算子名称
            operator: 算子函数或类
            
        返回:
            是否成功注册
        """
        if category not in self.operators:
            self.operators[category] = {}
            
        self.operators[category][name] = operator
        logger.info(f"向旧类别注册表注册算子: {category}.{name}")
        return True
        
    def get_operator(self, category, name):
        """
        获取算子
        
        参数:
            category: 算子类别
            name: 算子名称
            
        返回:
            算子函数或类
        """
        if category not in self.operators:
            return None
            
        return self.operators[category].get(name)
        
    def list_operators(self, category=None):
        """
        列出算子
        
        参数:
            category: 算子类别
            
        返回:
            算子列表
        """
        if category:
            if category not in self.operators:
                return []
                
            return list(self.operators[category].keys())
        else:
            result = []
            for category, operators in self.operators.items():
                result.extend(operators.keys())
            return result
            
    def get_categories(self):
        """
        获取所有类别
        
        返回:
            类别列表
        """
        return list(self.operators.keys())

# 定义一些示例算子
def add(a, b):
    """加法算子"""
    return a + b

def multiply(a, b):
    """乘法算子"""
    return a * b

def test_adapter():
    """测试适配器"""
    logger.info("开始测试适配器...")
    
    # 创建旧注册表
    old_registry = OldRegistry()
    
    # 创建适配器
    adapter = RegistryAdapter(old_registry)
    
    # 注册算子
    adapter.register_operator(
        OperatorCategory.UTILITY,
        "add",
        add,
        "1.0.0",
        "加法算子",
        ["math", "basic"],
        [],
    )
    
    # 获取算子
    add_op = adapter.get_operator(OperatorCategory.UTILITY, "add")
    logger.info(f"通过适配器获取算子: {add_op}")
    logger.info(f"1 + 2 = {add_op(1, 2)}")
    
    # 列出算子
    operators = adapter.list_operators()
    logger.info(f"通过适配器列出算子: {operators}")
    
    logger.info("适配器测试完成")

def test_category_adapter():
    """测试带有类别的适配器"""
    logger.info("开始测试带有类别的适配器...")
    
    # 创建旧类别注册表
    old_registry = OldCategoryRegistry()
    
    # 创建适配器
    adapter = RegistryAdapter(old_registry)
    
    # 注册算子
    adapter.register_operator(
        OperatorCategory.UTILITY,
        "add",
        add,
        "1.0.0",
        "加法算子",
        ["math", "basic"],
        [],
    )
    
    # 获取算子
    add_op = adapter.get_operator(OperatorCategory.UTILITY, "add")
    logger.info(f"通过适配器获取算子: {add_op}")
    logger.info(f"1 + 2 = {add_op(1, 2)}")
    
    # 列出算子
    operators = adapter.list_operators(OperatorCategory.UTILITY)
    logger.info(f"通过适配器列出算子: {operators}")
    
    logger.info("带有类别的适配器测试完成")

def test_proxy():
    """测试代理"""
    logger.info("开始测试代理...")
    
    # 创建代理
    proxy = RegistryProxy()
    
    # 注册注册表
    proxy.register_registry("old", OldRegistry())
    proxy.register_registry("old_category", OldCategoryRegistry())
    proxy.register_registry("new", get_global_registry())
    
    # 注册算子
    proxy.register_operator(
        "old",
        OperatorCategory.UTILITY,
        "add",
        add,
        "1.0.0",
        "加法算子",
        ["math", "basic"],
        [],
    )
    
    proxy.register_operator(
        "old_category",
        OperatorCategory.UTILITY,
        "multiply",
        multiply,
        "1.0.0",
        "乘法算子",
        ["math", "basic"],
        [],
    )
    
    proxy.register_operator(
        "new",
        OperatorCategory.UTILITY,
        "add",
        add,
        "1.0.0",
        "加法算子",
        ["math", "basic"],
        [],
    )
    
    # 获取算子
    add_op = proxy.get_operator("old", OperatorCategory.UTILITY, "add")
    logger.info(f"通过代理从旧注册表获取算子: {add_op}")
    logger.info(f"1 + 2 = {add_op(1, 2)}")
    
    multiply_op = proxy.get_operator("old_category", OperatorCategory.UTILITY, "multiply")
    logger.info(f"通过代理从旧类别注册表获取算子: {multiply_op}")
    logger.info(f"2 * 3 = {multiply_op(2, 3)}")
    
    add_op = proxy.get_operator("new", OperatorCategory.UTILITY, "add")
    logger.info(f"通过代理从新注册表获取算子: {add_op}")
    logger.info(f"1 + 2 = {add_op(1, 2)}")
    
    # 向所有注册表注册算子
    results = proxy.register_operator_to_all(
        OperatorCategory.UTILITY,
        "divide",
        lambda a, b: a / b,
        "1.0.0",
        "除法算子",
        ["math", "basic"],
        [],
    )
    logger.info(f"向所有注册表注册算子的结果: {results}")
    
    logger.info("代理测试完成")

def test_global_proxy():
    """测试全局代理"""
    logger.info("开始测试全局代理...")
    
    # 获取全局代理
    proxy = get_global_proxy()
    
    # 注册注册表
    proxy.register_registry("old", OldRegistry())
    proxy.register_registry("old_category", OldCategoryRegistry())
    proxy.register_registry("new", get_global_registry())
    
    # 注册算子
    proxy.register_operator_to_all(
        OperatorCategory.UTILITY,
        "power",
        lambda a, b: a ** b,
        "1.0.0",
        "幂运算算子",
        ["math", "basic"],
        [],
    )
    
    # 获取算子
    power_op = proxy.get_operator("old", OperatorCategory.UTILITY, "power")
    logger.info(f"通过全局代理从旧注册表获取算子: {power_op}")
    logger.info(f"2 ^ 3 = {power_op(2, 3)}")
    
    power_op = proxy.get_operator("old_category", OperatorCategory.UTILITY, "power")
    logger.info(f"通过全局代理从旧类别注册表获取算子: {power_op}")
    logger.info(f"2 ^ 3 = {power_op(2, 3)}")
    
    power_op = proxy.get_operator("new", OperatorCategory.UTILITY, "power")
    logger.info(f"通过全局代理从新注册表获取算子: {power_op}")
    logger.info(f"2 ^ 3 = {power_op(2, 3)}")
    
    logger.info("全局代理测试完成")

def main():
    """主函数"""
    logger.info("开始算子注册表集成示例")
    
    # 测试适配器
    test_adapter()
    
    # 测试带有类别的适配器
    test_category_adapter()
    
    # 测试代理
    test_proxy()
    
    # 测试全局代理
    test_global_proxy()
    
    logger.info("算子注册表集成示例结束")

if __name__ == "__main__":
    main()
