"""
中心节点识别算子示例

本示例展示了如何使用中心节点识别算子进行网络中心节点识别。
"""

import sys
import os
import time
import logging
import random
import numpy as np
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入中心节点识别算子
from src.transcendental_tensor.network_analysis import (
    NodeType, NodeStatus, CentralityMethod,
    NetworkNode, NetworkLink, Network,
    CentralityResult, CentralityCalculator, CentralNodeIdentifier
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def create_test_network(node_count: int = 20) -> Network:
    """
    创建测试网络
    
    参数:
        node_count (int): 节点数量
        
    返回:
        Network: 测试网络
    """
    # 创建网络
    network = Network(name="TestNetwork")
    
    # 创建节点
    for i in range(node_count):
        # 随机选择节点类型
        node_type = random.choice(list(NodeType))
        
        # 创建节点
        node = NetworkNode(
            node_id=f"node-{i}",
            name=f"Node {i}",
            node_type=node_type,
            status=NodeStatus.ONLINE,
            resources={
                "cpu": random.uniform(1, 8),
                "memory": random.uniform(4, 32),
                "storage": random.uniform(100, 1000)
            }
        )
        
        # 添加节点
        network.add_node(node)
    
    # 创建链接
    for i in range(node_count):
        for j in range(i + 1, node_count):
            # 随机决定是否创建链接
            if random.random() < 0.2:
                # 创建链接
                link = NetworkLink(
                    source_id=f"node-{i}",
                    target_id=f"node-{j}",
                    bidirectional=True,
                    weight=random.uniform(0.1, 1.0),
                    latency=random.uniform(1, 100),
                    bandwidth=random.uniform(10, 1000),
                    packet_loss=random.uniform(0, 0.1)
                )
                
                # 添加链接
                network.add_link(link)
    
    return network


def create_scale_free_network(node_count: int = 20) -> Network:
    """
    创建无标度网络
    
    参数:
        node_count (int): 节点数量
        
    返回:
        Network: 无标度网络
    """
    # 创建网络
    network = Network(name="ScaleFreeNetwork")
    
    # 创建初始完全图
    initial_nodes = 3
    
    # 创建初始节点
    for i in range(initial_nodes):
        # 创建节点
        node = NetworkNode(
            node_id=f"node-{i}",
            name=f"Node {i}",
            node_type=NodeType.COMPUTE,
            status=NodeStatus.ONLINE
        )
        
        # 添加节点
        network.add_node(node)
    
    # 创建初始链接
    for i in range(initial_nodes):
        for j in range(i + 1, initial_nodes):
            # 创建链接
            link = NetworkLink(
                source_id=f"node-{i}",
                target_id=f"node-{j}",
                bidirectional=True,
                weight=1.0
            )
            
            # 添加链接
            network.add_link(link)
    
    # 添加剩余节点
    for i in range(initial_nodes, node_count):
        # 创建节点
        node = NetworkNode(
            node_id=f"node-{i}",
            name=f"Node {i}",
            node_type=NodeType.COMPUTE,
            status=NodeStatus.ONLINE
        )
        
        # 添加节点
        network.add_node(node)
        
        # 计算连接概率
        degrees = {}
        total_degree = 0
        
        for j in range(i):
            node_j = network.get_node(f"node-{j}")
            degree = len(node_j.neighbors)
            degrees[j] = degree
            total_degree += degree
        
        # 添加链接
        for j in range(i):
            # 计算连接概率
            if total_degree > 0:
                probability = degrees[j] / total_degree
            else:
                probability = 1.0 / i
            
            # 随机决定是否创建链接
            if random.random() < probability:
                # 创建链接
                link = NetworkLink(
                    source_id=f"node-{i}",
                    target_id=f"node-{j}",
                    bidirectional=True,
                    weight=1.0
                )
                
                # 添加链接
                network.add_link(link)
    
    return network


def main():
    """主函数"""
    logger.info("Starting central nodes example")
    
    # 创建测试网络
    network = create_test_network(20)
    
    logger.info(f"Created test network with {len(network.nodes)} nodes and {len(network.links)} links")
    
    # 创建中心节点识别算子
    identifier = CentralNodeIdentifier()
    
    # 识别中心节点
    result = identifier.identify_central_nodes(network)
    
    logger.info("Central nodes (PageRank):")
    for node_id, centrality in result["central_nodes"]:
        logger.info(f"  {node_id}: {centrality:.4f}")
    
    # 使用不同的中心性计算方法
    methods = [
        CentralityMethod.DEGREE,
        CentralityMethod.BETWEENNESS,
        CentralityMethod.CLOSENESS,
        CentralityMethod.EIGENVECTOR,
        CentralityMethod.PAGERANK
    ]
    
    # 比较不同方法
    comparison = identifier.compare_methods(network, methods)
    
    logger.info("Method comparison:")
    for result in comparison["results"]:
        logger.info(f"  {result['method']}:")
        for node_id, centrality in result["central_nodes"][:5]:
            logger.info(f"    {node_id}: {centrality:.4f}")
        logger.info(f"    Computation time: {result['computation_time']:.4f}s")
    
    logger.info("Method similarities:")
    for similarity in comparison["similarities"]:
        logger.info(f"  {similarity['method1']} vs {similarity['method2']}: {similarity['similarity']:.4f}")
    
    # 获取节点中心性分布
    distribution = identifier.get_node_centrality_distribution(network)
    
    logger.info("Node centrality distribution:")
    logger.info(f"  Method: {distribution['method']}")
    logger.info(f"  Min: {distribution['quantiles']['min']:.4f}")
    logger.info(f"  Q1: {distribution['quantiles']['q1']:.4f}")
    logger.info(f"  Median: {distribution['quantiles']['median']:.4f}")
    logger.info(f"  Q3: {distribution['quantiles']['q3']:.4f}")
    logger.info(f"  Max: {distribution['quantiles']['max']:.4f}")
    logger.info(f"  Mean: {distribution['quantiles']['mean']:.4f}")
    logger.info(f"  Std: {distribution['quantiles']['std']:.4f}")
    
    # 创建无标度网络
    scale_free_network = create_scale_free_network(50)
    
    logger.info(f"Created scale-free network with {len(scale_free_network.nodes)} nodes and {len(scale_free_network.links)} links")
    
    # 识别中心节点
    result = identifier.identify_central_nodes(scale_free_network)
    
    logger.info("Central nodes in scale-free network (PageRank):")
    for node_id, centrality in result["central_nodes"]:
        logger.info(f"  {node_id}: {centrality:.4f}")
    
    # 模拟社区
    communities = {}
    for node_id in scale_free_network.nodes:
        # 随机分配社区
        community_id = f"community-{random.randint(1, 5)}"
        communities[node_id] = community_id
    
    # 获取社区中心节点
    community_result = identifier.get_community_central_nodes(scale_free_network, communities)
    
    logger.info("Community central nodes:")
    for community_id, central_nodes in community_result["community_central_nodes"].items():
        logger.info(f"  {community_id}:")
        for node_id, centrality in central_nodes[:3]:
            logger.info(f"    {node_id}: {centrality:.4f}")
    
    logger.info("Central nodes example completed")


if __name__ == "__main__":
    main()
