"""
涌现特性提取算子示例

本示例演示如何使用涌现特性提取算子从多层级系统中提取涌现特性。
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import numpy as np
import matplotlib.pyplot as plt
from src.operators.multilevel.emergence_feature_extraction import EmergenceFeatureExtractionOperator


def create_test_data():
    """创建测试数据"""
    # 创建层级
    layers = [
        {'id': 'layer1', 'name': '输入层', 'function_type': 'input'},
        {'id': 'layer2', 'name': '处理层1', 'function_type': 'processing'},
        {'id': 'layer3', 'name': '处理层2', 'function_type': 'processing'},
        {'id': 'layer4', 'name': '存储层', 'function_type': 'storage'},
        {'id': 'layer5', 'name': '控制层', 'function_type': 'control'},
        {'id': 'layer6', 'name': '输出层', 'function_type': 'output'}
    ]

    # 创建交互
    interactions = {
        'layer1_layer2': {'type': 'data_flow', 'strength': 0.9, 'bidirectional': False},
        'layer2_layer3': {'type': 'data_flow', 'strength': 0.8, 'bidirectional': True},
        'layer2_layer4': {'type': 'data_flow', 'strength': 0.7, 'bidirectional': True},
        'layer3_layer5': {'type': 'control_flow', 'strength': 0.9, 'bidirectional': False},
        'layer3_layer6': {'type': 'data_flow', 'strength': 0.8, 'bidirectional': False},
        'layer4_layer6': {'type': 'data_flow', 'strength': 0.6, 'bidirectional': False},
        'layer5_layer2': {'type': 'control_flow', 'strength': 0.7, 'bidirectional': False},
        'layer5_layer3': {'type': 'control_flow', 'strength': 0.8, 'bidirectional': False},
        'layer5_layer6': {'type': 'control_flow', 'strength': 0.9, 'bidirectional': False}
    }

    # 创建历史数据
    history = []
    for t in range(20):
        # 生成周期性模式
        sin_value = np.sin(t * 0.5)
        cos_value = np.cos(t * 0.3)

        # 层级状态
        layer_states = [
            {'id': 'layer1', 'state': {'activity': 0.5 + 0.3 * sin_value, 'load': 0.3 + 0.2 * cos_value}},
            {'id': 'layer2', 'state': {'activity': 0.6 + 0.2 * sin_value, 'load': 0.4 + 0.1 * cos_value}},
            {'id': 'layer3', 'state': {'activity': 0.7 + 0.1 * sin_value, 'load': 0.5 + 0.2 * cos_value}},
            {'id': 'layer4', 'state': {'storage': 0.3 + 0.1 * t, 'access_rate': 0.2 + 0.05 * sin_value}},
            {'id': 'layer5', 'state': {'control_level': 0.8 + 0.1 * cos_value}},
            {'id': 'layer6', 'state': {'activity': 0.4 + 0.3 * sin_value + 0.1 * cos_value, 'load': 0.3 + 0.2 * sin_value}}
        ]

        # 添加到历史记录
        history.append({
            'time': t,
            'layers': layer_states
        })

    return {
        'layers': layers,
        'interactions': interactions,
        'history': history
    }


def extract_features(data, extraction_method, feature_type):
    """提取涌现特性"""
    # 创建算子
    operator = EmergenceFeatureExtractionOperator(
        extraction_method=extraction_method,
        feature_type=feature_type,
        sensitivity=0.5,
        threshold=0.3
    )

    # 应用算子
    result = operator.apply(data)

    return result


def visualize_features(result):
    """可视化提取的特性"""
    features = result['extracted_features']
    importance_scores = result['importance_scores']

    print(f"提取方法: {result['extraction_method']}")
    print(f"特性类型: {result['feature_type']}")
    print(f"提取的特性数量: {result['feature_count']}")
    print(f"提取时间: {result['extraction_time']:.4f} 秒")
    print("\n提取的涌现特性:")

    for feature_id, feature in features.items():
        print(f"- {feature_id}: {feature['description']}")
        print(f"  重要性得分: {importance_scores.get(feature_id, 0):.4f}")

        # 根据特性类型打印详细信息
        if feature['type'] == 'structural':
            if 'layers' in feature:
                print(f"  涉及的层级: {', '.join(feature['layers'])}")
            if 'regions' in feature:
                print(f"  区域: {feature['regions']}")
            if 'components' in feature:
                print(f"  组件: {len(feature['components'])} 个")

        elif feature['type'] == 'functional':
            if 'groups' in feature:
                print(f"  功能组: {len(feature['groups'])} 个")
            if 'functions' in feature:
                if isinstance(feature['functions'], dict):
                    print(f"  功能: {list(feature['functions'].keys())}")
                else:
                    print(f"  功能: {feature['functions']}")
            if 'dependencies' in feature:
                print(f"  依赖关系: {len(feature['dependencies'])} 个")

        elif feature['type'] == 'behavioral':
            if 'changes' in feature:
                print(f"  变化: {len(feature['changes'])} 个")
            if 'behaviors' in feature:
                print(f"  行为: {len(feature['behaviors'])} 个")

        elif feature['type'] == 'temporal':
            if 'trends' in feature:
                print(f"  趋势: {len(feature['trends'])} 个")
            if 'change_points' in feature:
                print(f"  变化点: {len(feature['change_points'])} 个")

        print()

    # 绘制重要性得分
    if importance_scores:
        plt.figure(figsize=(10, 6))
        plt.bar(importance_scores.keys(), importance_scores.values())
        plt.xlabel('特性')
        plt.ylabel('重要性得分')
        plt.title(f"{result['extraction_method']} {result['feature_type']} 特性重要性得分")
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()
        plt.savefig(f"emergence_features_{result['extraction_method']}_{result['feature_type']}.png")
        plt.close()


def main():
    """主函数"""
    # 创建测试数据
    data = create_test_data()

    # 提取不同类型的涌现特性
    extraction_methods = ['statistical', 'topological', 'information_theoretic', 'dynamical']
    feature_types = ['structural', 'functional', 'behavioral', 'temporal']

    for method in extraction_methods:
        for feature_type in feature_types:
            print(f"\n{'='*80}")
            print(f"提取方法: {method}, 特性类型: {feature_type}")
            print(f"{'='*80}")

            # 提取特性
            result = extract_features(data, method, feature_type)

            # 可视化特性
            visualize_features(result)


if __name__ == '__main__':
    main()
