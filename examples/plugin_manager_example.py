"""
插件管理算子示例

演示如何使用插件管理算子加载、注册和管理插件。
"""

import os
import sys
import logging
from typing import Dict, Any, List

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入插件管理算子
from src.operators.engineering_support.plugin_manager import PluginManagerOperator

# 定义一个简单的插件类
class SimplePlugin:
    """简单的示例插件"""
    
    def __init__(self, name="SimplePlugin"):
        self.name = name
    
    def get_name(self):
        return self.name
    
    def get_description(self):
        return "A simple example plugin"
    
    def process(self, data):
        return f"Processed by {self.name}: {data}"

# 定义一个依赖其他插件的插件类
class DependentPlugin:
    """依赖其他插件的示例插件"""
    
    def __init__(self, name="DependentPlugin"):
        self.name = name
        self.__dependencies__ = ["SimplePlugin"]
    
    def get_name(self):
        return self.name
    
    def get_description(self):
        return "A plugin that depends on SimplePlugin"
    
    def process(self, data, simple_plugin):
        processed_data = simple_plugin.process(data)
        return f"Further processed by {self.name}: {processed_data}"

# 定义钩子规范
class MyHookSpecs:
    """钩子规范"""
    
    @staticmethod
    def pre_process(data):
        """在处理前调用"""
        pass
    
    @staticmethod
    def post_process(result):
        """在处理后调用"""
        pass

# 定义实现钩子的插件
class HookPlugin:
    """实现钩子的插件"""
    
    def __init__(self, name="HookPlugin"):
        self.name = name
    
    def get_name(self):
        return self.name
    
    def get_description(self):
        return "A plugin that implements hooks"
    
    def pre_process(self, data):
        print(f"Pre-processing data: {data}")
        return data
    
    def post_process(self, result):
        print(f"Post-processing result: {result}")
        return result

def main():
    """主函数"""
    # 创建插件管理算子
    plugin_manager = PluginManagerOperator(
        project_name="example",
        plugin_dirs=[os.path.dirname(__file__)],
        auto_discover=False,
        load_setuptools_entrypoints=False
    )
    
    # 创建钩子标记
    hookspec = plugin_manager.hookspec
    hookimpl = plugin_manager.hookimpl
    
    # 标记钩子规范
    MyHookSpecs.pre_process = hookspec(MyHookSpecs.pre_process)
    MyHookSpecs.post_process = hookspec(MyHookSpecs.post_process)
    
    # 标记钩子实现
    HookPlugin.pre_process = hookimpl(HookPlugin.pre_process)
    HookPlugin.post_process = hookimpl(HookPlugin.post_process)
    
    # 添加钩子规范
    plugin_manager.apply({
        'action': 'add_hook_specs',
        'hook_specs': MyHookSpecs
    })
    
    # 注册简单插件
    simple_plugin = SimplePlugin()
    result = plugin_manager.apply({
        'action': 'register',
        'plugin': simple_plugin,
        'metadata': {
            'description': simple_plugin.get_description(),
            'version': '1.0.0'
        }
    })
    print(f"注册简单插件结果: {result}")
    
    # 注册依赖插件
    dependent_plugin = DependentPlugin()
    result = plugin_manager.apply({
        'action': 'register',
        'plugin': dependent_plugin,
        'metadata': {
            'description': dependent_plugin.get_description(),
            'version': '1.0.0'
        }
    })
    print(f"注册依赖插件结果: {result}")
    
    # 注册钩子插件
    hook_plugin = HookPlugin()
    result = plugin_manager.apply({
        'action': 'register',
        'plugin': hook_plugin,
        'metadata': {
            'description': hook_plugin.get_description(),
            'version': '1.0.0'
        }
    })
    print(f"注册钩子插件结果: {result}")
    
    # 获取插件信息
    result = plugin_manager.apply({
        'action': 'get_info'
    })
    print("\n插件信息:")
    for plugin in result.get('plugins', []):
        print(f"- {plugin['name']}: {plugin['info'].get('description', 'No description')}")
    
    # 调用钩子
    data = "Hello, World!"
    print("\n调用钩子:")
    result = plugin_manager.call_hook('pre_process', data=data)
    print(f"pre_process 结果: {result}")
    
    # 使用插件处理数据
    print("\n使用插件处理数据:")
    processed_data = simple_plugin.process(data)
    print(f"简单插件处理结果: {processed_data}")
    
    further_processed_data = dependent_plugin.process(data, simple_plugin)
    print(f"依赖插件处理结果: {further_processed_data}")
    
    # 调用钩子
    result = plugin_manager.call_hook('post_process', result=further_processed_data)
    print(f"post_process 结果: {result}")
    
    # 卸载插件
    print("\n卸载插件:")
    result = plugin_manager.apply({
        'action': 'unregister',
        'plugin_name': simple_plugin.get_name()
    })
    print(f"卸载简单插件结果: {result}")
    
    # 尝试卸载被依赖的插件
    result = plugin_manager.apply({
        'action': 'unregister',
        'plugin_name': simple_plugin.get_name()
    })
    print(f"再次卸载简单插件结果: {result}")

if __name__ == "__main__":
    main()
