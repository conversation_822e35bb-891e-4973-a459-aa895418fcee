#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强的Parquet存储示例

展示如何使用增强的Parquet存储功能，包括分区存储、元数据优化和增量更新。
"""

import os
import time
import numpy as np
import pyarrow as pa
import pyarrow.compute as pc

from core.data.cache.parquet_store import ParquetStore, CompressionStrategy, PartitionStrategy
from core.data.cache.utils import format_size

def test_basic_operations():
    """测试基本操作"""
    print("基本操作示例")
    print("=" * 50)
    
    # 创建临时目录
    base_dir = os.path.join(os.path.dirname(__file__), 'parquet_store_test')
    os.makedirs(base_dir, exist_ok=True)
    
    # 创建Parquet存储
    store = ParquetStore(base_dir, {
        'compression_strategy': 'balanced',
        'partition_strategy': 'none',
        'partition_size': 1000000
    })
    
    print(f"Parquet存储已创建，基础目录: {base_dir}")
    print("-" * 50)
    
    # 创建测试数据
    print("创建测试数据...")
    
    # 表格数据
    table_data = pa.Table.from_arrays(
        [
            pa.array(range(1000)),
            pa.array(['value_' + str(i) for i in range(1000)]),
            pa.array(np.random.rand(1000))
        ],
        ['id', 'name', 'value']
    )
    
    # 数组数据
    array_data = pa.array(np.random.rand(1000))
    
    print(f"表格数据大小: {format_size(table_data.nbytes)}")
    print(f"数组数据大小: {format_size(array_data.nbytes)}")
    print("-" * 50)
    
    # 存储数据
    print("存储数据...")
    
    # 存储表格
    start_time = time.time()
    store.put('table_data', table_data, {'description': '测试表格数据'})
    table_put_time = time.time() - start_time
    print(f"存储表格耗时: {table_put_time:.6f}秒")
    
    # 存储数组
    start_time = time.time()
    store.put('array_data', array_data, {'description': '测试数组数据'})
    array_put_time = time.time() - start_time
    print(f"存储数组耗时: {array_put_time:.6f}秒")
    print("-" * 50)
    
    # 获取数据
    print("获取数据...")
    
    # 获取表格
    start_time = time.time()
    result_table = store.get('table_data')
    table_get_time = time.time() - start_time
    print(f"获取表格耗时: {table_get_time:.6f}秒")
    print(f"表格行数: {result_table.num_rows}")
    print(f"表格列名: {result_table.column_names}")
    
    # 获取数组
    start_time = time.time()
    result_array = store.get('array_data')
    array_get_time = time.time() - start_time
    print(f"获取数组耗时: {array_get_time:.6f}秒")
    print(f"数组长度: {len(result_array)}")
    print("-" * 50)
    
    # 获取元数据
    print("获取元数据...")
    
    table_metadata = store.get_metadata('table_data')
    print("表格元数据:")
    print(f"  键: {table_metadata.get('key')}")
    print(f"  行数: {table_metadata.get('num_rows')}")
    print(f"  列数: {table_metadata.get('num_columns')}")
    print(f"  列名: {table_metadata.get('column_names')}")
    print(f"  大小: {format_size(table_metadata.get('size_bytes', 0))}")
    print(f"  压缩: {table_metadata.get('compression')}")
    print(f"  压缩级别: {table_metadata.get('compression_level')}")
    print(f"  自定义: {table_metadata.get('custom')}")
    print("-" * 50)
    
    # 列出所有键
    print("列出所有键...")
    
    keys = store.list_keys()
    print(f"键列表: {keys}")
    print("-" * 50)
    
    # 关闭存储
    store.close()
    print("Parquet存储已关闭")
    print("=" * 50)

def test_partition_strategies():
    """测试分区策略"""
    print("分区策略示例")
    print("=" * 50)
    
    # 创建临时目录
    base_dir = os.path.join(os.path.dirname(__file__), 'parquet_store_partition_test')
    os.makedirs(base_dir, exist_ok=True)
    
    # 测试不同的分区策略
    strategies = [
        ('none', PartitionStrategy.NONE),
        ('hash', PartitionStrategy.HASH),
        ('directory', PartitionStrategy.DIRECTORY),
        ('hive', PartitionStrategy.HIVE)
    ]
    
    for name, strategy in strategies:
        print(f"测试分区策略: {name}")
        
        # 创建Parquet存储
        store = ParquetStore(os.path.join(base_dir, name), {
            'compression_strategy': 'balanced',
            'partition_strategy': name,
            'partition_columns': ['category'] if name == 'hive' else [],
            'hash_partitions': 4 if name == 'hash' else 16
        })
        
        # 创建测试数据
        table_data = pa.Table.from_arrays(
            [
                pa.array(range(1000)),
                pa.array(['category_' + str(i % 5) for i in range(1000)]),
                pa.array(np.random.rand(1000))
            ],
            ['id', 'category', 'value']
        )
        
        # 存储数据
        store.put('test_data', table_data)
        
        # 获取数据
        result = store.get('test_data')
        
        print(f"  行数: {result.num_rows}")
        print(f"  列名: {result.column_names}")
        
        # 关闭存储
        store.close()
        print("-" * 50)
    
    print("分区策略测试完成")
    print("=" * 50)

def test_compression_strategies():
    """测试压缩策略"""
    print("压缩策略示例")
    print("=" * 50)
    
    # 创建临时目录
    base_dir = os.path.join(os.path.dirname(__file__), 'parquet_store_compression_test')
    os.makedirs(base_dir, exist_ok=True)
    
    # 测试不同的压缩策略
    strategies = [
        ('none', CompressionStrategy.NONE),
        ('light', CompressionStrategy.LIGHT),
        ('balanced', CompressionStrategy.BALANCED),
        ('heavy', CompressionStrategy.HEAVY)
    ]
    
    # 创建测试数据
    data = np.random.rand(10000, 100)  # 约8MB
    table_data = pa.Table.from_arrays(
        [pa.array(data[:, i]) for i in range(100)],
        [f'col_{i}' for i in range(100)]
    )
    
    print(f"测试数据大小: {format_size(table_data.nbytes)}")
    
    for name, strategy in strategies:
        print(f"测试压缩策略: {name}")
        
        # 创建Parquet存储
        store = ParquetStore(os.path.join(base_dir, name), {
            'compression_strategy': name,
            'partition_strategy': 'none'
        })
        
        # 存储数据
        start_time = time.time()
        store.put('test_data', table_data)
        put_time = time.time() - start_time
        
        # 获取元数据
        metadata = store.get_metadata('test_data')
        file_size = metadata.get('size_bytes', 0)
        
        print(f"  存储耗时: {put_time:.6f}秒")
        print(f"  文件大小: {format_size(file_size)}")
        print(f"  压缩比: {table_data.nbytes / file_size:.2f}x")
        
        # 获取数据
        start_time = time.time()
        store.get('test_data')
        get_time = time.time() - start_time
        
        print(f"  读取耗时: {get_time:.6f}秒")
        
        # 关闭存储
        store.close()
        print("-" * 50)
    
    print("压缩策略测试完成")
    print("=" * 50)

def test_incremental_update():
    """测试增量更新"""
    print("增量更新示例")
    print("=" * 50)
    
    # 创建临时目录
    base_dir = os.path.join(os.path.dirname(__file__), 'parquet_store_update_test')
    os.makedirs(base_dir, exist_ok=True)
    
    # 创建Parquet存储
    store = ParquetStore(base_dir, {
        'compression_strategy': 'balanced',
        'partition_strategy': 'none'
    })
    
    # 创建初始数据
    initial_data = pa.Table.from_arrays(
        [
            pa.array(range(1000)),
            pa.array(['name_' + str(i) for i in range(1000)]),
            pa.array(np.random.rand(1000))
        ],
        ['id', 'name', 'value']
    )
    
    print("存储初始数据...")
    store.put('test_data', initial_data)
    
    # 获取初始数据
    result = store.get('test_data')
    print("初始数据:")
    print(f"  行数: {result.num_rows}")
    print(f"  列名: {result.column_names}")
    print(f"  value列前5个值: {result.column('value').slice(0, 5).to_numpy()}")
    print("-" * 50)
    
    # 增量更新
    print("执行增量更新...")
    
    # 只更新value列
    new_values = np.random.rand(1000) * 10  # 新的值范围更大
    
    store.incremental_update('test_data', {
        'value': new_values
    })
    
    # 获取更新后的数据
    result = store.get('test_data')
    print("更新后的数据:")
    print(f"  行数: {result.num_rows}")
    print(f"  列名: {result.column_names}")
    print(f"  value列前5个值: {result.column('value').slice(0, 5).to_numpy()}")
    print("-" * 50)
    
    # 关闭存储
    store.close()
    print("增量更新测试完成")
    print("=" * 50)

def test_query_and_search():
    """测试查询和搜索"""
    print("查询和搜索示例")
    print("=" * 50)
    
    # 创建临时目录
    base_dir = os.path.join(os.path.dirname(__file__), 'parquet_store_query_test')
    os.makedirs(base_dir, exist_ok=True)
    
    # 创建Parquet存储
    store = ParquetStore(base_dir, {
        'compression_strategy': 'balanced',
        'partition_strategy': 'none'
    })
    
    # 创建测试数据
    data1 = pa.Table.from_arrays(
        [
            pa.array(range(1000)),
            pa.array(['category_A' if i % 2 == 0 else 'category_B' for i in range(1000)]),
            pa.array(np.random.rand(1000) * 100)
        ],
        ['id', 'category', 'value']
    )
    
    data2 = pa.Table.from_arrays(
        [
            pa.array(range(1000, 2000)),
            pa.array(['category_C' if i % 2 == 0 else 'category_D' for i in range(1000)]),
            pa.array(np.random.rand(1000) * 200)
        ],
        ['id', 'category', 'value']
    )
    
    # 存储数据
    print("存储测试数据...")
    store.put('data1', data1)
    store.put('data2', data2)
    
    # 查询数据
    print("执行查询...")
    
    # 查询category为'category_A'的数据
    result = store.query('data1', "category = 'category_A'")
    print("查询结果 (category = 'category_A'):")
    print(f"  行数: {result.num_rows}")
    print(f"  category列唯一值: {pc.unique(result.column('category')).to_numpy()}")
    
    # 查询value大于50的数据
    result = store.query('data1', "value > 50")
    print("查询结果 (value > 50):")
    print(f"  行数: {result.num_rows}")
    print(f"  value列最小值: {pc.min(result.column('value')).as_py()}")
    
    # 只选择id和value列
    result = store.query('data1', columns=['id', 'value'])
    print("查询结果 (只选择id和value列):")
    print(f"  行数: {result.num_rows}")
    print(f"  列名: {result.column_names}")
    print("-" * 50)
    
    # 搜索数据
    print("执行搜索...")
    
    # 搜索value大于150的数据
    results = store.search("value > 150")
    print("搜索结果 (value > 150):")
    for key, result in results.items():
        print(f"  键: {key}")
        print(f"  行数: {result.num_rows}")
        print(f"  value列最小值: {pc.min(result.column('value')).as_py()}")
    
    # 关闭存储
    store.close()
    print("查询和搜索测试完成")
    print("=" * 50)

def main():
    """主函数"""
    # 测试基本操作
    test_basic_operations()
    
    print("\n")
    
    # 测试分区策略
    test_partition_strategies()
    
    print("\n")
    
    # 测试压缩策略
    test_compression_strategies()
    
    print("\n")
    
    # 测试增量更新
    test_incremental_update()
    
    print("\n")
    
    # 测试查询和搜索
    test_query_and_search()

if __name__ == '__main__':
    main()
