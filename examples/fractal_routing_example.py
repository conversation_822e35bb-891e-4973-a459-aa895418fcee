"""
分形动力学路由算法示例

本脚本展示了如何使用分形动力学路由算法。
"""

import numpy as np
import networkx as nx
import matplotlib.pyplot as plt
import os
import sys
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入我们的实现
from src.algorithms.fractal_routing.router import FractalDynamicsRouter
from src.algorithms.fractal_routing.patterns import FractalPatternGenerator
from src.algorithms.fractal_routing.analysis import RoutingPerformanceAnalyzer


def create_test_network(num_nodes=30, p=0.15):
    """创建测试网络"""
    # 创建随机图
    network = nx.erdos_renyi_graph(num_nodes, p, seed=42)
    
    # 确保图是连通的
    if not nx.is_connected(network):
        # 找到最大连通分量
        largest_cc = max(nx.connected_components(network), key=len)
        network = network.subgraph(largest_cc).copy()
    
    # 添加节点位置
    pos = nx.spring_layout(network, seed=42)
    for node, position in pos.items():
        network.nodes[node]['pos'] = position
    
    # 添加边权重
    for u, v in network.edges:
        pos_u = network.nodes[u]['pos']
        pos_v = network.nodes[v]['pos']
        distance = np.sqrt(sum((p1 - p2) ** 2 for p1, p2 in zip(pos_u, pos_v)))
        network[u][v]['weight'] = distance
    
    return network


def run_routing(network, source, destination, output_dir=None):
    """运行路由算法"""
    # 创建输出目录
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # 创建路由器
    router = FractalDynamicsRouter(
        fractal_dimension=1.5,
        max_iterations=20,
        tolerance=1e-6,
        use_parallel=True,
        num_workers=4,
        adaptive_routing=True,
        route_cache_size=1000,
        route_cache_ttl=300
    )
    
    # 创建分形模式生成器
    pattern_generator = FractalPatternGenerator(pattern_type='centrality', fractal_dimension=1.5)
    
    # 创建路由性能分析器
    analyzer = RoutingPerformanceAnalyzer(verbose=True)
    
    # 生成分形模式
    print("生成分形模式...")
    fractal_pattern = pattern_generator.generate(network)
    
    # 执行路由计算
    print(f"计算从节点 {source} 到节点 {destination} 的路由...")
    start_time = time.time()
    result = router.compute({
        'network': network,
        'source': source,
        'destination': destination
    })
    end_time = time.time()
    
    print(f"路由计算完成，耗时: {end_time - start_time:.2f}秒")
    
    # 提取路由
    route = result['routes'].get((source, destination))
    if route:
        print(f"找到路由: {route}")
        print(f"路由长度: {len(route) - 1}")
    else:
        print("未找到路由")
        return None
    
    # 分析路由性能
    print("分析路由性能...")
    analysis = analyzer.analyze(network, {(source, destination): route}, fractal_pattern)
    
    # 绘制路由可视化图
    print("绘制路由可视化图...")
    fig_route = analyzer.plot_route(network, route, title=f"从节点 {source} 到节点 {destination} 的路由")
    if output_dir:
        fig_route.savefig(os.path.join(output_dir, 'route_visualization.png'))
    
    # 绘制分形模式可视化图
    print("绘制分形模式可视化图...")
    fig_pattern = analyzer.plot_fractal_pattern(network, fractal_pattern, title="分形模式可视化")
    if output_dir:
        fig_pattern.savefig(os.path.join(output_dir, 'fractal_pattern.png'))
    
    # 计算所有节点对之间的路由
    print("计算所有节点对之间的路由...")
    start_time = time.time()
    all_pairs_result = router.compute({
        'network': network,
        'all_pairs': True
    })
    end_time = time.time()
    
    print(f"所有节点对路由计算完成，耗时: {end_time - start_time:.2f}秒")
    
    # 分析所有路由的性能
    print("分析所有路由的性能...")
    all_pairs_analysis = analyzer.analyze(network, all_pairs_result['routes'], fractal_pattern)
    
    # 绘制负载分布图
    print("绘制负载分布图...")
    fig_load = analyzer.plot_load_distribution(network, all_pairs_result['routes'], title="负载分布")
    if output_dir:
        fig_load.savefig(os.path.join(output_dir, 'load_distribution.png'))
    
    # 显示图形
    plt.show()
    
    return route, analysis, all_pairs_analysis


def run_dynamic_routing(network, output_dir=None):
    """运行动态路由算法"""
    # 创建输出目录
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # 创建路由器
    router = FractalDynamicsRouter(
        fractal_dimension=1.5,
        max_iterations=20,
        tolerance=1e-6,
        use_parallel=True,
        num_workers=4,
        adaptive_routing=True,
        route_cache_size=1000,
        route_cache_ttl=300
    )
    
    # 创建分形模式生成器
    pattern_generator = FractalPatternGenerator(pattern_type='centrality', fractal_dimension=1.5)
    
    # 创建路由性能分析器
    analyzer = RoutingPerformanceAnalyzer(verbose=True)
    
    # 生成动态分形模式
    print("生成动态分形模式...")
    fractal_patterns = pattern_generator.generate_dynamic_pattern(network, time_steps=10)
    
    # 初始化路由历史
    routes_history = []
    
    # 选择一些节点对
    nodes = list(network.nodes)
    node_pairs = [(nodes[0], nodes[-1]), (nodes[1], nodes[-2]), (nodes[2], nodes[-3])]
    
    # 执行动态路由计算
    print("执行动态路由计算...")
    for t, fractal_pattern in enumerate(fractal_patterns):
        print(f"时间步 {t}...")
        
        # 计算路由
        routes = {}
        for source, destination in node_pairs:
            result = router.compute({
                'network': network,
                'source': source,
                'destination': destination
            })
            routes[(source, destination)] = result['routes'].get((source, destination))
        
        # 记录路由
        routes_history.append(routes)
    
    # 分析动态路由性能
    print("分析动态路由性能...")
    dynamic_analysis = analyzer.analyze_dynamic_performance(network, routes_history, fractal_patterns)
    
    # 绘制性能指标图
    print("绘制性能指标图...")
    fig_metrics = analyzer.plot_performance_metrics(dynamic_analysis, title="动态路由性能指标")
    if output_dir:
        fig_metrics.savefig(os.path.join(output_dir, 'dynamic_performance_metrics.png'))
    
    # 显示图形
    plt.show()
    
    return dynamic_analysis


def main():
    """主函数"""
    # 创建输出目录
    output_dir = 'output/fractal_routing'
    
    # 创建测试网络
    print("创建测试网络...")
    network = create_test_network(num_nodes=30, p=0.15)
    
    # 选择源节点和目标节点
    source = 0
    destination = len(network.nodes) - 1
    
    # 运行路由算法
    print("运行路由算法...")
    route, analysis, all_pairs_analysis = run_routing(network, source, destination, output_dir)
    
    # 运行动态路由算法
    print("运行动态路由算法...")
    dynamic_analysis = run_dynamic_routing(network, output_dir)
    
    print("示例完成！")


if __name__ == "__main__":
    main()
