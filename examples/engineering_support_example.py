#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超越态思维引擎 - 工程支持算子示例
"""

import sys
import time
import os
from src.operators import operator_registry

def test_plugin_manager():
    """测试插件管理算子"""
    print("测试插件管理算子...")
    
    # 获取插件管理算子
    plugin_manager_class = operator_registry.get_operator("engineering_support", "plugin_manager")
    if plugin_manager_class is None:
        print("插件管理算子不可用，请确保已编译Rust绑定")
        return
    
    # 创建插件管理算子实例
    plugin_manager = plugin_manager_class()
    
    # 创建插件目录
    plugins_dir = os.path.join(os.getcwd(), "plugins")
    os.makedirs(plugins_dir, exist_ok=True)
    
    # 扫描并加载插件
    result = plugin_manager.scan_and_load_plugins()
    
    # 打印结果
    print(f"已加载插件数量: {len(result.get_loaded_plugins())}")
    print(f"已激活插件数量: {len(result.get_activated_plugins())}")
    print(f"加载失败插件数量: {len(result.get_failed_plugins())}")
    print()

def test_smart_logger():
    """测试智能日志算子"""
    print("测试智能日志算子...")
    
    # 获取智能日志算子
    smart_logger_class = operator_registry.get_operator("engineering_support", "smart_logger")
    if smart_logger_class is None:
        print("智能日志算子不可用，请确保已编译Rust绑定")
        return
    
    # 创建智能日志算子实例
    smart_logger = smart_logger_class()
    
    # 记录日志
    smart_logger.info("test_module", "这是一条信息日志", ["test"], {"context_key": "context_value"})
    smart_logger.warn("test_module", "这是一条警告日志", ["test"], {"context_key": "context_value"})
    smart_logger.error("test_module", "这是一条错误日志", "测试错误", None, ["test"], {"context_key": "context_value"})
    
    # 添加上下文
    smart_logger.add_context("global_key", "global_value")
    
    # 记录带上下文的日志
    smart_logger.info("test_module", "这是一条带全局上下文的日志", ["test"], None)
    
    # 性能计时
    smart_logger.start_timer("test_timer")
    time.sleep(0.1)
    elapsed = smart_logger.stop_timer("test_timer")
    
    # 打印结果
    print(f"日志条目数量: {len(smart_logger.get_entries())}")
    print(f"性能计时结果: {elapsed} ms")
    
    # 获取日志结果
    result = smart_logger.get_result()
    
    # 打印统计信息
    print(f"日志统计: {result.get_stats()}")
    print()

def test_evolution_tracker():
    """测试演化追踪算子"""
    print("测试演化追踪算子...")
    
    # 获取演化追踪算子
    evolution_tracker_class = operator_registry.get_operator("engineering_support", "evolution_tracker")
    if evolution_tracker_class is None:
        print("演化追踪算子不可用，请确保已编译Rust绑定")
        return
    
    # 创建演化追踪算子实例
    evolution_tracker = evolution_tracker_class()
    
    # 记录事件
    event_id = evolution_tracker.track_event(
        "系统启动",
        "system.startup",
        "系统已成功启动",
        "system",
        "info",
        ["startup", "system"],
        {"version": "1.0.0"},
        None,
        None
    )
    
    # 记录指标
    evolution_tracker.track_metric(
        "memory_usage",
        128.5,
        "MB",
        "系统内存使用量",
        ["system", "memory"],
        {"process": "main"}
    )
    
    evolution_tracker.track_metric(
        "cpu_usage",
        25.3,
        "%",
        "CPU使用率",
        ["system", "cpu"],
        {"process": "main"}
    )
    
    # 设置阶段
    evolution_tracker.set_stage("exploration")
    
    # 记录阶段变化事件
    evolution_tracker.track_event(
        "阶段变化",
        "system.stage_change",
        "系统已进入探索阶段",
        "system",
        "info",
        ["stage_change"],
        {"previous_stage": "initialization", "new_stage": "exploration"},
        None,
        "exploration"
    )
    
    # 打印结果
    print(f"当前阶段: {evolution_tracker.get_stage()}")
    print(f"事件数量: {len(evolution_tracker.get_events())}")
    print(f"指标数量: {len(evolution_tracker.get_metrics())}")
    
    # 获取结果
    result = evolution_tracker.get_result()
    
    # 打印统计信息
    print(f"统计信息: {result.get_stats()}")
    print()

def main():
    """主函数"""
    print("超越态思维引擎 - 工程支持算子示例")
    print("=" * 50)
    
    # 获取所有算子类别
    categories = operator_registry.get_categories()
    print(f"算子类别: {categories}")
    
    # 获取工程支持算子
    engineering_operators = operator_registry.get_operators_in_category("engineering_support")
    print(f"工程支持算子: {list(engineering_operators.keys())}")
    print()
    
    # 测试插件管理算子
    test_plugin_manager()
    
    # 测试智能日志算子
    test_smart_logger()
    
    # 测试演化追踪算子
    test_evolution_tracker()
    
    print("示例完成!")

if __name__ == "__main__":
    main()
