"""
统一注册表示例

本示例展示了如何使用统一注册表管理器，整合所有功能，提供简单的接口。
"""

import os
import sys
import time
import logging
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Union

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入算子注册表模块
try:
    from src.rust_bindings import (
        OperatorCategory,
    )
    from src.rust_bindings.operator_registry import (
        RegistryManager,
        get_global_registry_manager,
        operator,
        RegistryConfig,
        ConfigFormat,
        load_config,
        save_config,
    )
    logger.info(f"成功导入算子注册表模块")
except ImportError as e:
    logger.error(f"导入算子注册表模块失败: {e}")
    sys.exit(1)

# 定义一些示例算子
@operator(OperatorCategory.UTILITY, "add", "1.0.0", "加法算子", ["math", "basic"], [])
def add(a, b):
    """加法算子"""
    return a + b

@operator(OperatorCategory.UTILITY, "multiply", "1.0.0", "乘法算子", ["math", "basic"], [("utility.add", ">=1.0.0")])
def multiply(a, b):
    """乘法算子"""
    return a * b

@operator(OperatorCategory.UTILITY, "divide", "1.0.0", "除法算子", ["math", "basic"], [("utility.multiply", ">=1.0.0")])
def divide(a, b):
    """除法算子"""
    if b == 0:
        raise ValueError("除数不能为零")
    return a / b

@operator(OperatorCategory.NUMPY, "matrix_multiply", "1.0.0", "矩阵乘法算子", ["math", "matrix"], [("utility.multiply", ">=1.0.0")])
def matrix_multiply(a, b):
    """矩阵乘法算子"""
    return np.matmul(a, b)

class Calculator:
    """计算器类"""

    def __init__(self):
        """初始化计算器"""
        self.history = []

    def add(self, a, b):
        """加法"""
        result = a + b
        self.history.append((a, b, result, "add"))
        return result

    def subtract(self, a, b):
        """减法"""
        result = a - b
        self.history.append((a, b, result, "subtract"))
        return result

    def multiply(self, a, b):
        """乘法"""
        result = a * b
        self.history.append((a, b, result, "multiply"))
        return result

    def divide(self, a, b):
        """除法"""
        if b == 0:
            raise ValueError("除数不能为零")
        result = a / b
        self.history.append((a, b, result, "divide"))
        return result

    def get_history(self):
        """获取历史记录"""
        return self.history

# 注册计算器类
@operator(OperatorCategory.UTILITY, "calculator", "1.0.0", "计算器类", ["math", "class"], [("utility.add", ">=1.0.0"), ("utility.multiply", ">=1.0.0")])
def get_calculator():
    """获取计算器实例"""
    return Calculator()

def test_registry_manager():
    """测试注册表管理器"""
    logger.info("开始测试注册表管理器...")

    # 获取全局注册表管理器
    manager = get_global_registry_manager()

    # 打印注册表状态
    manager.print_registry_status()

    # 获取算子信息
    add_info = manager.get_operator_info(OperatorCategory.UTILITY, "add")
    logger.info(f"加法算子信息: {add_info}")

    # 获取算子
    add_op = manager.get_operator(OperatorCategory.UTILITY, "add", user_id="user1")
    logger.info(f"获取到的加法算子: {add_op}")

    # 执行算子
    result = manager.execute_operator(OperatorCategory.UTILITY, "add", (1, 2), user_id="user1")
    logger.info(f"1 + 2 = {result}")

    # 获取计算器
    calculator_factory = manager.get_operator(OperatorCategory.UTILITY, "calculator", user_id="user1")
    calculator = calculator_factory()
    logger.info(f"获取到的计算器: {calculator}")

    # 使用计算器
    result = calculator.add(3, 4)
    logger.info(f"3 + 4 = {result}")

    result = calculator.multiply(5, 6)
    logger.info(f"5 * 6 = {result}")

    # 获取计算器历史记录
    history = calculator.get_history()
    logger.info(f"计算器历史记录: {history}")

    logger.info("注册表管理器测试完成")

def test_config():
    """测试配置"""
    logger.info("开始测试配置...")

    # 创建配置文件
    config_file = os.path.join(project_root, "examples", "registry_config.yaml")

    # 加载默认配置
    config_dict = {
        "registries": {
            "utility": {
                "enabled": True,
                "auto_discover": False
            },
            "numpy": {
                "enabled": True,
                "auto_discover": False
            }
        },
        "performance": {
            "cache": {
                "enabled": True,
                "max_size": 100,
                "ttl": 60
            }
        },
        "logging": {
            "level": "INFO"
        }
    }

    # 保存配置
    save_config(config_file, ConfigFormat.YAML)
    logger.info(f"已保存配置文件: {config_file}")

    # 加载配置
    load_config(config_file, ConfigFormat.YAML)
    logger.info(f"已加载配置文件: {config_file}")

    # 获取全局注册表管理器
    manager = get_global_registry_manager()

    # 打印注册表状态
    manager.print_registry_status()

    logger.info("配置测试完成")

def test_performance():
    """测试性能"""
    logger.info("开始测试性能...")

    # 获取全局注册表管理器
    manager = get_global_registry_manager()

    # 执行算子多次，测试缓存
    start_time = time.time()
    for _ in range(10):
        add_result = manager.execute_operator(OperatorCategory.UTILITY, "add", (1, 2), user_id="user1")
    end_time = time.time()

    logger.info(f"执行10次加法算子，耗时: {end_time - start_time:.6f}秒")
    logger.info(f"加法结果: {add_result}")

    # 清空缓存
    manager.clear_cache()

    # 再次执行算子多次
    start_time = time.time()
    for _ in range(10):
        add_result = manager.execute_operator(OperatorCategory.UTILITY, "add", (1, 2), user_id="user1")
    end_time = time.time()

    logger.info(f"清空缓存后执行10次加法算子，耗时: {end_time - start_time:.6f}秒")
    logger.info(f"加法结果: {add_result}")

    logger.info("性能测试完成")

def test_security():
    """测试安全性"""
    logger.info("开始测试安全性...")

    # 获取全局注册表管理器
    manager = get_global_registry_manager()

    # 测试访问权限
    add_op = manager.get_operator(OperatorCategory.UTILITY, "add", user_id="user1")
    logger.info(f"用户1访问加法算子: {add_op is not None}")

    multiply_op = manager.get_operator(OperatorCategory.UTILITY, "multiply", user_id="user1")
    logger.info(f"用户1访问乘法算子: {multiply_op is not None}")

    divide_op = manager.get_operator(OperatorCategory.UTILITY, "divide", user_id="user1")
    logger.info(f"用户1访问除法算子: {divide_op is not None}")

    logger.info("安全性测试完成")

def main():
    """主函数"""
    logger.info("开始统一注册表示例")

    # 测试注册表管理器
    test_registry_manager()

    # 测试配置
    test_config()

    # 测试性能
    test_performance()

    # 测试安全性
    test_security()

    logger.info("统一注册表示例结束")

if __name__ == "__main__":
    main()
