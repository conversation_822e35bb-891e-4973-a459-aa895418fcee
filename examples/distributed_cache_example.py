#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分布式缓存系统示例

展示如何使用基于Arrow Flight的分布式缓存系统。
"""

import os
import time
import argparse
import numpy as np
import pyarrow as pa

# 导入分布式缓存系统
from core.data.cache import create_distributed_cache, format_size

def run_server(port):
    """运行服务器
    
    Args:
        port: 服务器端口
    """
    print(f"启动缓存服务器 (端口: {port})...")
    
    # 创建缓存目录
    cache_dir = os.path.join(os.path.dirname(__file__), f'cache_server_{port}')
    os.makedirs(cache_dir, exist_ok=True)
    
    # 创建分布式缓存管理器
    cache_manager = create_distributed_cache({
        'local': {
            'base_dir': cache_dir,
            'L0': {'capacity': 10 * 1024 * 1024},  # 10MB
            'L1': {'capacity': 20 * 1024 * 1024},  # 20MB
            'L2': {'capacity': 30 * 1024 * 1024},  # 30MB
            'L3': {'capacity': 40 * 1024 * 1024},  # 40MB
        },
        'remote': {
            'server': {
                'enabled': True,
                'host': 'localhost',
                'port': port
            },
            'clients': []
        }
    })
    
    print(f"缓存服务器已启动 (端口: {port})")
    print("按Ctrl+C停止服务器...")
    
    try:
        # 保持服务器运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("正在停止服务器...")
        cache_manager.close()
        print("服务器已停止")

def run_client(server_port, client_port=None):
    """运行客户端
    
    Args:
        server_port: 服务器端口
        client_port: 客户端端口
    """
    print(f"启动缓存客户端 (连接到端口: {server_port})...")
    
    # 创建缓存目录
    cache_dir = os.path.join(os.path.dirname(__file__), f'cache_client_{client_port or "default"}')
    os.makedirs(cache_dir, exist_ok=True)
    
    # 创建分布式缓存管理器
    cache_manager = create_distributed_cache({
        'local': {
            'base_dir': cache_dir,
            'L0': {'capacity': 10 * 1024 * 1024},  # 10MB
            'L1': {'capacity': 20 * 1024 * 1024},  # 20MB
            'L2': {'capacity': 30 * 1024 * 1024},  # 30MB
            'L3': {'capacity': 40 * 1024 * 1024},  # 40MB
        },
        'remote': {
            'server': {
                'enabled': client_port is not None,
                'host': 'localhost',
                'port': client_port or 0
            },
            'clients': [
                {
                    'enabled': True,
                    'host': 'localhost',
                    'port': server_port
                }
            ]
        }
    })
    
    print(f"缓存客户端已启动")
    print("-" * 50)
    
    try:
        # 示例1：基本操作
        print("示例1：基本操作")
        
        # 创建测试数据
        data = pa.Table.from_arrays(
            [pa.array(range(1000)), pa.array(['value_' + str(i) for i in range(1000)])],
            ['id', 'value']
        )
        
        print(f"测试数据大小: {format_size(data.nbytes)}")
        
        # 放入缓存
        start_time = time.time()
        cache_manager.put('test_key', data)
        put_time = time.time() - start_time
        print(f"放入缓存耗时: {put_time:.6f}秒")
        
        # 获取缓存
        start_time = time.time()
        result = cache_manager.get('test_key')
        get_time = time.time() - start_time
        print(f"获取缓存耗时: {get_time:.6f}秒")
        
        # 验证结果
        print(f"结果行数: {result.num_rows}")
        print(f"结果列名: {result.column_names}")
        print("-" * 50)
        
        # 示例2：远程缓存
        print("示例2：远程缓存")
        
        # 创建测试数据
        remote_data = pa.Table.from_arrays(
            [pa.array(range(2000)), pa.array(['remote_' + str(i) for i in range(2000)])],
            ['id', 'value']
        )
        
        print(f"远程测试数据大小: {format_size(remote_data.nbytes)}")
        
        # 放入远程缓存
        start_time = time.time()
        cache_manager.put('remote_key', remote_data)
        put_time = time.time() - start_time
        print(f"放入远程缓存耗时: {put_time:.6f}秒")
        
        # 清除本地缓存
        cache_manager.local_cache.remove('remote_key')
        
        # 从远程缓存获取
        start_time = time.time()
        remote_result = cache_manager.get('remote_key')
        get_time = time.time() - start_time
        print(f"从远程缓存获取耗时: {get_time:.6f}秒")
        
        # 验证结果
        print(f"远程结果行数: {remote_result.num_rows}")
        print(f"远程结果列名: {remote_result.column_names}")
        print("-" * 50)
        
        # 示例3：缓存统计
        print("示例3：缓存统计")
        
        # 获取统计信息
        stats = cache_manager.get_stats()
        
        # 打印分布式统计信息
        dist_stats = stats.get('distributed', {})
        print("分布式缓存统计:")
        print(f"  本地命中次数: {dist_stats.get('local_hits', 0)}")
        print(f"  远程命中次数: {dist_stats.get('remote_hits', 0)}")
        print(f"  未命中次数: {dist_stats.get('misses', 0)}")
        print(f"  放入次数: {dist_stats.get('puts', 0)}")
        print(f"  命中率: {dist_stats.get('hit_rate', 0) * 100:.2f}%")
        print(f"  本地命中率: {dist_stats.get('local_hit_rate', 0) * 100:.2f}%")
        print(f"  远程命中率: {dist_stats.get('remote_hit_rate', 0) * 100:.2f}%")
        
        # 打印本地缓存统计信息
        local_stats = stats.get('local', {})
        print("本地缓存统计:")
        print(f"  总命中次数: {local_stats.get('total', {}).get('hits', 0)}")
        print(f"  总未命中次数: {local_stats.get('total', {}).get('misses', 0)}")
        print(f"  总放入次数: {local_stats.get('total', {}).get('puts', 0)}")
        print(f"  命中率: {local_stats.get('hit_rate', 0) * 100:.2f}%")
        
        print("-" * 50)
        
        print("示例完成")
        
        # 关闭缓存管理器
        cache_manager.close()
    
    except Exception as e:
        print(f"发生错误: {e}")
        cache_manager.close()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='分布式缓存系统示例')
    parser.add_argument('--mode', choices=['server', 'client'], required=True, help='运行模式')
    parser.add_argument('--port', type=int, default=8815, help='服务器端口')
    parser.add_argument('--client-port', type=int, help='客户端端口（可选）')
    
    args = parser.parse_args()
    
    if args.mode == 'server':
        run_server(args.port)
    else:
        run_client(args.port, args.client_port)

if __name__ == '__main__':
    main()
