#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超越态思维引擎 - GPU加速中心节点识别算子基本功能测试
"""

import os
import sys
import time
import random
import numpy as np
import logging
from typing import Dict, Any, List, Tuple, Optional, Union

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入算子注册表
from src.operators import operator_registry

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_test_network():
    """生成测试网络"""
    # 创建一个简单的测试网络
    nodes = {
        "A": {"B": 1.0, "C": 0.5, "D": 0.8},
        "B": {"A": 1.0, "C": 0.7, "E": 0.9},
        "C": {"A": 0.5, "B": 0.7, "D": 0.6, "E": 0.5},
        "D": {"A": 0.8, "C": 0.6, "E": 0.7, "F": 1.0},
        "E": {"B": 0.9, "C": 0.5, "D": 0.7, "F": 0.8},
        "F": {"D": 1.0, "E": 0.8}
    }
    
    return nodes

def test_centrality_operator():
    """测试中心性算子"""
    # 获取中心性算子
    centrality_class = operator_registry.get_operator("gpu_acceleration", "centrality")
    if centrality_class is None:
        logger.error("GPU加速中心性算子不可用，请确保已编译Rust绑定")
        return
    
    logger.info("GPU加速中心性算子加载完成")
    
    # 生成测试网络
    nodes = generate_test_network()
    logger.info(f"测试网络节点数量: {len(nodes)}")
    
    # 测试不同类型的中心性
    centrality_types = ["degree", "closeness", "betweenness", "eigenvector", "pagerank", "katz"]
    
    for centrality_type in centrality_types:
        logger.info(f"\n测试 {centrality_type} 中心性:")
        
        # 创建CPU配置
        cpu_config = centrality_class.PyCentralityConfig(
            centrality_type=centrality_type,
            use_edge_weights=True,
            normalize=True,
            max_iterations=100,
            convergence_threshold=1e-6,
            damping_factor=0.85,
            use_multithreading=True,
            compute_device="cpu",
            use_simd=True,
            use_sparse_matrix=True,
            batch_size=64
        )
        
        # 创建GPU配置
        gpu_config = centrality_class.PyCentralityConfig(
            centrality_type=centrality_type,
            use_edge_weights=True,
            normalize=True,
            max_iterations=100,
            convergence_threshold=1e-6,
            damping_factor=0.85,
            use_multithreading=True,
            compute_device="cuda",
            use_simd=True,
            use_sparse_matrix=True,
            batch_size=64
        )
        
        # 创建CPU中心性算子实例
        cpu_operator = centrality_class.PyCentralityOperator(cpu_config)
        
        # 创建GPU中心性算子实例
        gpu_operator = centrality_class.PyCentralityOperator(gpu_config)
        
        # 计算CPU中心性
        start_time = time.time()
        cpu_result = cpu_operator.calculate_centrality(nodes)
        cpu_time = time.time() - start_time
        
        # 计算GPU中心性
        start_time = time.time()
        gpu_result = gpu_operator.calculate_centrality(nodes)
        gpu_time = time.time() - start_time
        
        # 打印结果
        logger.info(f"CPU计算时间: {cpu_time:.6f}秒")
        logger.info(f"GPU计算时间: {gpu_time:.6f}秒")
        
        if cpu_time > 0 and gpu_time > 0:
            speedup = cpu_time / gpu_time
            logger.info(f"加速比: {speedup:.2f}x")
        
        # 获取CPU中心性值
        cpu_centrality_values = cpu_result.get_centrality_values()
        
        # 获取GPU中心性值
        gpu_centrality_values = gpu_result.get_centrality_values()
        
        # 检查结果是否一致
        is_consistent = True
        for node_id in nodes.keys():
            cpu_value = cpu_centrality_values.get(node_id, 0.0)
            gpu_value = gpu_centrality_values.get(node_id, 0.0)
            
            # 检查值是否接近
            if abs(cpu_value - gpu_value) > 1e-5:
                is_consistent = False
                logger.warning(f"节点 {node_id} 的中心性值不一致: CPU={cpu_value:.6f}, GPU={gpu_value:.6f}")
        
        if is_consistent:
            logger.info("CPU和GPU计算结果一致")
        
        # 打印排序后的节点
        logger.info("\n排序后的节点:")
        sorted_nodes = cpu_result.get_sorted_nodes()
        for i, node_data in enumerate(sorted_nodes):
            node_id = node_data["node_id"]
            value = node_data["value"]
            logger.info(f"{i+1}. {node_id}: {value:.6f}")

def main():
    """主函数"""
    logger.info("开始测试GPU加速中心节点识别算子的基本功能...")
    
    try:
        # 测试中心性算子
        test_centrality_operator()
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
    
    logger.info("测试完成")

if __name__ == "__main__":
    main()
