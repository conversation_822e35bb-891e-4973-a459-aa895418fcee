#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
多级缓存系统示例

展示如何使用基于Arrow和Parquet的多级缓存系统。
"""

import os
import time
import numpy as np
import pyarrow as pa

# 导入多级缓存系统
from core.data.cache import create_cache_manager, CacheLevel, CachePolicy, format_size

def main():
    """主函数"""
    print("多级缓存系统示例")
    print("=" * 50)
    
    # 创建缓存目录
    cache_dir = os.path.join(os.path.dirname(__file__), 'cache')
    os.makedirs(cache_dir, exist_ok=True)
    
    # 创建缓存管理器
    cache_manager = create_cache_manager({
        'base_dir': cache_dir,
        'L0': {'capacity': 10 * 1024 * 1024},  # 10MB
        'L1': {'capacity': 20 * 1024 * 1024},  # 20MB
        'L2': {'capacity': 30 * 1024 * 1024},  # 30MB
        'L3': {'capacity': 40 * 1024 * 1024},  # 40MB
        'auto_migrate': True,
        'migrate_threshold': {
            'L0_to_L1': 60,    # 60秒未访问
            'L1_to_L2': 3600,  # 1小时未访问
            'L2_to_L3': 86400  # 1天未访问
        }
    })
    
    print(f"缓存目录: {cache_dir}")
    print("-" * 50)
    
    # 示例1：基本操作
    print("示例1：基本操作")
    
    # 创建测试数据
    data = pa.Table.from_arrays(
        [pa.array(range(1000)), pa.array(['value_' + str(i) for i in range(1000)])],
        ['id', 'value']
    )
    
    print(f"测试数据大小: {format_size(data.nbytes)}")
    
    # 放入缓存
    start_time = time.time()
    cache_manager.put('test_key', data)
    put_time = time.time() - start_time
    print(f"放入缓存耗时: {put_time:.6f}秒")
    
    # 获取缓存
    start_time = time.time()
    result = cache_manager.get('test_key')
    get_time = time.time() - start_time
    print(f"获取缓存耗时: {get_time:.6f}秒")
    
    # 验证结果
    print(f"结果行数: {result.num_rows}")
    print(f"结果列名: {result.column_names}")
    print("-" * 50)
    
    # 示例2：不同级别的缓存
    print("示例2：不同级别的缓存")
    
    # 创建不同大小的测试数据
    small_data = pa.Table.from_arrays(
        [pa.array(range(100)), pa.array(['small_' + str(i) for i in range(100)])],
        ['id', 'value']
    )
    
    medium_data = pa.Table.from_arrays(
        [pa.array(range(10000)), pa.array(['medium_' + str(i) for i in range(10000)])],
        ['id', 'value']
    )
    
    large_data = pa.Table.from_arrays(
        [pa.array(range(100000)), pa.array(['large_' + str(i) for i in range(100000)])],
        ['id', 'value']
    )
    
    # 放入不同级别的缓存
    cache_manager.put('small_key', small_data, level=CacheLevel.L0)
    cache_manager.put('medium_key', medium_data, level=CacheLevel.L1)
    cache_manager.put('large_key', large_data, level=CacheLevel.L2)
    
    # 获取缓存
    small_result = cache_manager.get('small_key')
    medium_result = cache_manager.get('medium_key')
    large_result = cache_manager.get('large_key')
    
    # 打印统计信息
    stats = cache_manager.get_stats()
    print("缓存统计信息:")
    print(f"  总命中次数: {stats['total']['hits']}")
    print(f"  总未命中次数: {stats['total']['misses']}")
    print(f"  总放入次数: {stats['total']['puts']}")
    print(f"  命中率: {stats['hit_rate'] * 100:.2f}%")
    
    print("各级缓存使用情况:")
    for level in ['L0', 'L1', 'L2', 'L3']:
        current_size = stats['current_size'][level]
        capacity = stats['capacity'][level]
        usage_percent = (current_size / capacity) * 100 if capacity > 0 else 0
        print(f"  {level}: {format_size(current_size)} / {format_size(capacity)} ({usage_percent:.2f}%)")
    
    print("-" * 50)
    
    # 示例3：生存时间
    print("示例3：生存时间")
    
    # 创建测试数据
    ttl_data = pa.Table.from_arrays(
        [pa.array(range(100)), pa.array(['ttl_' + str(i) for i in range(100)])],
        ['id', 'value']
    )
    
    # 放入缓存，设置5秒的生存时间
    cache_manager.put('ttl_key', ttl_data, ttl=5)
    
    # 立即获取
    print("立即获取:", "成功" if cache_manager.get('ttl_key') is not None else "失败")
    
    # 等待3秒
    print("等待3秒...")
    time.sleep(3)
    
    # 再次获取
    print("3秒后获取:", "成功" if cache_manager.get('ttl_key') is not None else "失败")
    
    # 等待3秒
    print("再等待3秒...")
    time.sleep(3)
    
    # 再次获取
    print("6秒后获取:", "成功" if cache_manager.get('ttl_key') is not None else "失败")
    
    print("-" * 50)
    
    print("示例完成")

if __name__ == '__main__':
    main()
