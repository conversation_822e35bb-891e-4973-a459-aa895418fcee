#!/usr/bin/env python3
"""
简单集成演示脚本

该脚本演示了自解释与可验证性算子的基本功能。
"""

import os
import sys
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建必要的目录
os.makedirs("src/operators/explanation", exist_ok=True)
os.makedirs("src/operators/verification", exist_ok=True)
os.makedirs("src/integration", exist_ok=True)

# 创建简单的算子类
class MultilevelExplanationOperator:
    def apply(self, input_data):
        return {
            "technical_explanation": "这是技术解释",
            "conceptual_explanation": "这是概念解释",
            "analogical_explanation": "这是类比解释",
            "combined_explanation": "这是综合解释"
        }

class CounterfactualExplanationOperator:
    def apply(self, input_data):
        return {
            "counterfactuals": [
                {
                    "feature_name": "system_load",
                    "original_value": 0.65,
                    "counterfactual_value": 0.85,
                    "original_option": "option1",
                    "counterfactual_option": "option3"
                }
            ],
            "counterfactual_explanations": [
                "如果系统负载增加了30.8%（从0.65变为0.85），那么决策将从增加资源分配变为保持当前资源分配。"
            ]
        }

class FormalVerificationOperator:
    def apply(self, input_data):
        return {
            "verification_results": {
                "safety": {
                    "result": "true",
                    "confidence": 0.95,
                    "method": "model_checking",
                    "proof": {
                        "conclusion": "属性'G(x >= 0 && y >= 0)'在所有可达状态中都成立"
                    }
                },
                "liveness": {
                    "result": "true",
                    "confidence": 0.9,
                    "method": "theorem_proving"
                }
            },
            "verification_methods_used": {
                "model_checking": 1,
                "theorem_proving": 1
            }
        }

# 创建简单的决策类
class Decision:
    def __init__(self, entity_id, options, context=None, metadata=None):
        self.decision_id = f"decision_{entity_id}_{id(self)}"
        self.entity_id = entity_id
        self.options = options
        self.context = context or {}
        self.metadata = metadata or {}
        self.weights = {}
        self.selected_option = None
        self.confidence = 0.0
        self.status = "created"
        self.execution_result = None

# 创建简单的决策协调系统
class DecisionCoordinator:
    def __init__(self):
        self.decisions = {}
    
    def create_decision(self, entity_id, options, context=None, metadata=None):
        decision = Decision(entity_id, options, context, metadata)
        self.decisions[decision.decision_id] = decision
        return decision
    
    def evaluate_decision(self, decision):
        # 简单的评估逻辑
        if decision.weights:
            selected_option = max(decision.weights.items(), key=lambda x: x[1])[0]
            confidence = decision.weights[selected_option] / sum(decision.weights.values())
        else:
            selected_option = list(decision.options.keys())[0] if decision.options else None
            confidence = 1.0
        
        decision.selected_option = selected_option
        decision.confidence = confidence
        decision.status = "evaluated"
        
        return decision
    
    def execute_decision(self, decision_id, option=None):
        decision = self.decisions.get(decision_id)
        if not decision:
            return None
        
        option = option or decision.selected_option
        decision.status = "executed"
        decision.execution_result = {"option": option, "success": True}
        
        return decision.execution_result

# 创建简单的推理引擎
class ReasoningEngine:
    def __init__(self):
        pass
    
    def reason(self, input_data, context=None):
        # 简单的推理逻辑
        if isinstance(input_data, dict):
            result = input_data.copy()
            result["reasoning_applied"] = True
        else:
            result = f"{input_data} (reasoning_applied)"
        
        return result
    
    def evaluate_result(self, result, context=None):
        # 简单的评估逻辑
        if isinstance(result, dict):
            result["evaluation_applied"] = True
        else:
            result = f"{result} (evaluation_applied)"
        
        return result

# 集成解释算子到决策协调系统
def integrate_explanation_to_decision_coordinator(decision_coordinator):
    # 创建算子
    multilevel_explanation_operator = MultilevelExplanationOperator()
    counterfactual_explanation_operator = CounterfactualExplanationOperator()
    
    # 保存原始方法
    original_evaluate_decision = decision_coordinator.evaluate_decision
    
    # 定义包装方法
    def evaluate_decision_with_explanation(decision):
        # 调用原始方法
        result = original_evaluate_decision(decision)
        
        # 生成解释
        explanation_input = {
            "model_output": {
                "decision": decision.__dict__,
                "confidence": decision.confidence,
                "features": {
                    "options": decision.options,
                    "weights": decision.weights,
                    "context": decision.context
                }
            }
        }
        
        # 生成多层次解释
        explanation_result = multilevel_explanation_operator.apply(explanation_input)
        result.explanation = explanation_result
        
        # 生成反事实解释
        counterfactual_result = counterfactual_explanation_operator.apply(explanation_input)
        result.counterfactual_explanation = counterfactual_result
        
        return result
    
    # 替换方法
    decision_coordinator.evaluate_decision = evaluate_decision_with_explanation
    
    logger.info("解释算子已集成到决策协调系统")

# 集成验证算子到推理引擎
def integrate_verification_to_reasoning_engine(reasoning_engine):
    # 创建算子
    formal_verification_operator = FormalVerificationOperator()
    
    # 添加验证方法
    def verify_system_model(system_model, properties=None):
        # 默认属性
        if properties is None:
            properties = [
                {
                    "id": "safety",
                    "type": "safety",
                    "expression": "G(system_state is safe)"
                },
                {
                    "id": "liveness",
                    "type": "liveness",
                    "expression": "F(system_state reaches goal)"
                }
            ]
        
        # 验证系统模型
        verification_input = {
            "system_model": system_model,
            "properties": properties
        }
        
        verification_result = formal_verification_operator.apply(verification_input)
        return verification_result
    
    # 添加方法
    reasoning_engine.verify_system_model = verify_system_model
    
    logger.info("验证算子已集成到推理引擎")

# 演示集成功能
def demonstrate_integration():
    logger.info("=" * 80)
    logger.info("演示自解释与可验证性算子集成")
    logger.info("=" * 80)
    
    # 创建决策协调系统
    decision_coordinator = DecisionCoordinator()
    
    # 创建推理引擎
    reasoning_engine = ReasoningEngine()
    
    # 集成解释算子到决策协调系统
    integrate_explanation_to_decision_coordinator(decision_coordinator)
    
    # 集成验证算子到推理引擎
    integrate_verification_to_reasoning_engine(reasoning_engine)
    
    # 演示决策解释
    logger.info("\n" + "=" * 80)
    logger.info("演示决策解释")
    logger.info("=" * 80)
    
    # 创建决策
    decision = decision_coordinator.create_decision(
        entity_id="test_entity",
        options={
            "option1": "增加资源分配",
            "option2": "减少资源分配",
            "option3": "保持当前资源分配"
        },
        context={
            "current_resource_usage": 0.75,
            "system_load": 0.65,
            "priority": "high"
        },
        metadata={
            "decision_type": "resource_allocation",
            "importance": "high"
        }
    )
    
    # 设置权重
    decision.weights = {
        "option1": 0.7,
        "option2": 0.2,
        "option3": 0.1
    }
    
    # 评估决策
    evaluated_decision = decision_coordinator.evaluate_decision(decision)
    
    # 打印结果
    logger.info(f"选择选项: {evaluated_decision.selected_option}, 置信度: {evaluated_decision.confidence:.2f}")
    
    # 打印解释
    logger.info("\n解释:")
    logger.info(f"技术解释: {evaluated_decision.explanation['technical_explanation']}")
    logger.info(f"概念解释: {evaluated_decision.explanation['conceptual_explanation']}")
    logger.info(f"类比解释: {evaluated_decision.explanation['analogical_explanation']}")
    
    # 打印反事实解释
    logger.info("\n反事实解释:")
    for explanation in evaluated_decision.counterfactual_explanation['counterfactual_explanations']:
        logger.info(f"- {explanation}")
    
    # 演示形式化验证
    logger.info("\n" + "=" * 80)
    logger.info("演示形式化验证")
    logger.info("=" * 80)
    
    # 创建系统模型
    system_model = {
        "states": [
            {"id": "s0", "initial": True, "variables": {"x": 0, "y": 0}},
            {"id": "s1", "variables": {"x": 1, "y": 0}},
            {"id": "s2", "variables": {"x": 1, "y": 1}},
            {"id": "s3", "variables": {"x": 0, "y": 1}}
        ],
        "transitions": [
            {"from": "s0", "to": "s1", "action": "increment_x"},
            {"from": "s1", "to": "s2", "action": "increment_y"},
            {"from": "s2", "to": "s3", "action": "decrement_x"},
            {"from": "s3", "to": "s0", "action": "decrement_y"}
        ]
    }
    
    # 定义属性
    properties = [
        {
            "id": "safety",
            "type": "safety",
            "expression": "G(x >= 0 && y >= 0)"
        },
        {
            "id": "liveness",
            "type": "liveness",
            "expression": "F(x = 1 && y = 1)"
        }
    ]
    
    # 验证系统模型
    verification_result = reasoning_engine.verify_system_model(system_model, properties)
    
    # 打印验证结果
    logger.info("\n形式化验证结果:")
    
    for prop_id, prop_result in verification_result["verification_results"].items():
        logger.info(f"属性 {prop_id}: {prop_result['result']}, 置信度: {prop_result['confidence']:.2f}")
        
        # 打印证明或反例
        if "proof" in prop_result:
            logger.info(f"  证明: {prop_result['proof']['conclusion']}")
    
    logger.info(f"使用的验证方法: {verification_result['verification_methods_used']}")
    
    logger.info("\n" + "=" * 80)
    logger.info("演示完成")
    logger.info("=" * 80)

if __name__ == "__main__":
    demonstrate_integration()
