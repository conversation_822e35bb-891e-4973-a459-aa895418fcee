#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GIL策略基准测试脚本

测试不同GIL管理策略在各种计算任务上的性能表现
"""

import os
import sys
import time
import numpy as np
import threading
import multiprocessing
from typing import List, Tuple, Dict, Any, Callable
import logging
import argparse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入GIL管理模块
try:
    from src.core.parallel.gil_manager import (
        nogil, release_gil, nogil_mode, gil_state,
        get_gil_status, optimize_gil_for_task, apply_gil_strategy
    )
except ImportError as e:
    logger.warning(f"无法导入GIL管理模块: {e}")
    
    # 创建兼容性函数
    def nogil(func):
        return func
        
    def release_gil(func):
        return func
        
    def nogil_mode(enabled):
        class DummyContext:
            def __enter__(self): pass
            def __exit__(self, *args): pass
        return DummyContext()
        
    def gil_state(enabled):
        class DummyContext:
            def __enter__(self): pass
            def __exit__(self, *args): pass
        return DummyContext()
        
    def get_gil_status():
        return {
            'has_nogil_support': False,
            'is_nogil_build': False,
            'is_nogil_enabled': False,
            'has_np_gil_state': False
        }
        
    def optimize_gil_for_task(task_type):
        return 'normal'
        
    def apply_gil_strategy(strategy, func):
        return func


# 定义测试任务
def matrix_multiply_task(size: int = 1000, iterations: int = 10) -> float:
    """矩阵乘法任务"""
    total_time = 0
    for _ in range(iterations):
        a = np.random.random((size, size))
        b = np.random.random((size, size))
        
        start_time = time.time()
        c = np.dot(a, b)
        total_time += time.time() - start_time
    
    return total_time / iterations


def fft_task(size: int = 1000, iterations: int = 10) -> float:
    """FFT计算任务"""
    total_time = 0
    for _ in range(iterations):
        a = np.random.random(size * size) + 1j * np.random.random(size * size)
        
        start_time = time.time()
        b = np.fft.fft(a)
        total_time += time.time() - start_time
    
    return total_time / iterations


def parallel_sum_task(size: int = 10000000, num_threads: int = 4) -> float:
    """并行求和任务"""
    data = np.random.random(size)
    chunks = np.array_split(data, num_threads)
    results = [0] * num_threads
    
    def worker(chunk, index):
        results[index] = np.sum(chunk)
    
    start_time = time.time()
    threads = []
    for i in range(num_threads):
        t = threading.Thread(target=worker, args=(chunks[i], i))
        threads.append(t)
        t.start()
    
    for t in threads:
        t.join()
    
    total_sum = sum(results)
    elapsed_time = time.time() - start_time
    
    return elapsed_time


# 定义不同GIL策略的任务包装器
def run_with_normal(task_func: Callable, *args, **kwargs) -> float:
    """使用正常模式运行任务"""
    return task_func(*args, **kwargs)


def run_with_nogil(task_func: Callable, *args, **kwargs) -> float:
    """使用nogil模式运行任务"""
    with nogil_mode(True):
        return task_func(*args, **kwargs)


def run_with_numpy_gil(task_func: Callable, *args, **kwargs) -> float:
    """使用NumPy的gil_state运行任务"""
    with gil_state(False):
        return task_func(*args, **kwargs)


def run_with_combined(task_func: Callable, *args, **kwargs) -> float:
    """使用组合模式运行任务"""
    with nogil_mode(True):
        with gil_state(False):
            return task_func(*args, **kwargs)


def run_with_optimized(task_func: Callable, task_type: str, *args, **kwargs) -> float:
    """使用自动优化的GIL策略运行任务"""
    strategy = optimize_gil_for_task(task_type)
    optimized_func = apply_gil_strategy(strategy, task_func)
    return optimized_func(*args, **kwargs)


def benchmark_task(task_name: str, task_func: Callable, task_type: str, *args, **kwargs) -> Dict[str, float]:
    """对任务进行基准测试，比较不同GIL策略的性能"""
    results = {}
    
    # 获取GIL状态信息
    gil_status = get_gil_status()
    logger.info(f"GIL状态: {gil_status}")
    
    # 正常模式
    logger.info(f"运行任务 '{task_name}' (正常模式)...")
    results['normal'] = run_with_normal(task_func, *args, **kwargs)
    
    # nogil模式
    if gil_status.get('has_nogil_support', False):
        logger.info(f"运行任务 '{task_name}' (nogil模式)...")
        results['nogil'] = run_with_nogil(task_func, *args, **kwargs)
    
    # NumPy gil_state模式
    if gil_status.get('has_np_gil_state', False):
        logger.info(f"运行任务 '{task_name}' (NumPy gil_state模式)...")
        results['numpy_gil'] = run_with_numpy_gil(task_func, *args, **kwargs)
    
    # 组合模式
    if gil_status.get('has_nogil_support', False) and gil_status.get('has_np_gil_state', False):
        logger.info(f"运行任务 '{task_name}' (组合模式)...")
        results['combined'] = run_with_combined(task_func, *args, **kwargs)
    
    # 自动优化模式
    logger.info(f"运行任务 '{task_name}' (自动优化模式，任务类型: {task_type})...")
    results['optimized'] = run_with_optimized(task_func, task_type, *args, **kwargs)
    
    return results


def print_results(task_name: str, results: Dict[str, float]) -> None:
    """打印基准测试结果"""
    print(f"\n=== {task_name} 基准测试结果 ===")
    
    # 找出基准时间（正常模式）
    baseline = results.get('normal', 0)
    if baseline <= 0:
        print("无法获取基准时间")
        return
    
    # 打印每种模式的结果和相对性能
    for mode, time_taken in sorted(results.items()):
        speedup = baseline / time_taken if time_taken > 0 else 0
        print(f"{mode.ljust(10)}: {time_taken:.6f} 秒 (相对性能: {speedup:.2f}x)")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='GIL策略基准测试')
    parser.add_argument('--matrix-size', type=int, default=1000, help='矩阵大小')
    parser.add_argument('--fft-size', type=int, default=1000, help='FFT大小')
    parser.add_argument('--sum-size', type=int, default=10000000, help='求和数组大小')
    parser.add_argument('--iterations', type=int, default=5, help='迭代次数')
    parser.add_argument('--threads', type=int, default=4, help='线程数')
    args = parser.parse_args()
    
    # 打印系统信息
    print(f"Python版本: {sys.version}")
    print(f"NumPy版本: {np.__version__}")
    print(f"CPU核心数: {os.cpu_count()}")
    print(f"GIL状态: {get_gil_status()}")
    
    # 运行矩阵乘法基准测试
    matrix_results = benchmark_task(
        "矩阵乘法", 
        matrix_multiply_task, 
        "compute_intensive",
        args.matrix_size, 
        args.iterations
    )
    print_results("矩阵乘法", matrix_results)
    
    # 运行FFT基准测试
    fft_results = benchmark_task(
        "FFT计算", 
        fft_task, 
        "compute_intensive",
        args.fft_size, 
        args.iterations
    )
    print_results("FFT计算", fft_results)
    
    # 运行并行求和基准测试
    sum_results = benchmark_task(
        "并行求和", 
        parallel_sum_task, 
        "parallel",
        args.sum_size, 
        args.threads
    )
    print_results("并行求和", sum_results)


if __name__ == "__main__":
    main()
