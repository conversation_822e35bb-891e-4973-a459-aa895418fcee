#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
超越态计算框架GIL装饰器高级性能分析示例

演示如何使用增强的GIL装饰器系统进行性能分析和优化
"""

import os
import sys
import time
import numpy as np
import logging
import argparse
import threading
import matplotlib.pyplot as plt
from typing import Dict, Any, List, Tuple, Optional, Union
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入GIL装饰器
try:
    from src.core.parallel.tte_gil_decorator import (
        TTEGilOptimizer, quantum_task, holographic_task, fractal_task,
        parallel_task, compute_intensive, get_performance_data,
        get_performance_stats, get_strategy_comparison
    )
    from src.core.parallel.gil_manager import (
        get_gil_status, detect_nogil_support, optimize_gil_for_task
    )
    MODULES_IMPORTED = True
except ImportError as e:
    logger.error(f"无法导入GIL装饰器模块: {e}")
    MODULES_IMPORTED = False
    
    # 创建兼容性装饰器和函数
    def quantum_task(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    
    def holographic_task(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    
    def fractal_task(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    
    def parallel_task(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    
    def compute_intensive(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    
    def get_performance_data(*args, **kwargs):
        return {}
    
    def get_performance_stats(*args, **kwargs):
        return {}
    
    def get_strategy_comparison(*args, **kwargs):
        return {}
    
    def get_gil_status():
        return {
            'has_nogil_support': False,
            'is_nogil_build': False,
            'is_nogil_enabled': False,
            'has_np_gil_state': False
        }
    
    def detect_nogil_support():
        return {
            'has_nogil': False,
            'is_nogil_build': False
        }
    
    def optimize_gil_for_task(*args, **kwargs):
        return 'normal'


def analyze_gil_strategies():
    """分析GIL策略的性能数据"""
    if MODULES_IMPORTED and gil_optimizer:
        try:
            # 获取所有策略的性能比较数据
            strategy_comparison = get_strategy_comparison()
            
            # 打印策略比较数据
            print("\n=== GIL策略性能比较 ===")
            
            # 打印每种策略的总体性能
            if 'strategies' in strategy_comparison:
                print("\n策略总体性能:")
                for strategy, data in strategy_comparison['strategies'].items():
                    if data['calls'] > 0:
                        print(f"  {strategy}: {data['calls']} 次调用, 平均时间: {data['avg_time']:.6f} 秒")
            
            # 打印每种任务类型的最佳策略
            if 'summary' in strategy_comparison and 'best_strategies_by_task_type' in strategy_comparison['summary']:
                print("\n任务类型最佳策略:")
                for task_type, data in strategy_comparison['summary']['best_strategies_by_task_type'].items():
                    print(f"  {task_type}: {data['strategy']} (平均时间: {data['avg_time']:.6f} 秒)")
            
            return strategy_comparison
        except Exception as e:
            logger.error(f"分析GIL策略性能数据失败: {e}")
    
    return None


def main():
    """主函数"""
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description="超越态计算框架GIL装饰器高级性能分析")
    parser.add_argument('--quantum', action='store_true', help="运行量子计算基准测试")
    parser.add_argument('--holographic', action='store_true', help="运行全息计算基准测试")
    parser.add_argument('--fractal', action='store_true', help="运行分形计算基准测试")
    parser.add_argument('--parallel', action='store_true', help="运行并行计算基准测试")
    parser.add_argument('--all', action='store_true', help="运行所有基准测试")
    parser.add_argument('--analyze', action='store_true', help="分析现有性能数据")
    parser.add_argument('--visualize', action='store_true', help="可视化性能数据")
    parser.add_argument('--repetitions', type=int, default=3, help="每个测试的重复次数")
    parser.add_argument('--output-dir', type=str, default=None, help="输出目录")
    
    args = parser.parse_args()
    
    # 如果没有选择任何选项，显示帮助信息
    if not (args.quantum or args.holographic or args.fractal or args.parallel or args.all or args.analyze or args.visualize):
        parser.print_help()
        return
    
    # 创建性能数据目录
    performance_data_dir = args.output_dir or os.path.join(project_root, 'data', 'performance')
    os.makedirs(performance_data_dir, exist_ok=True)
    
    # 创建GIL优化器实例，设置性能数据文件
    performance_data_file = os.path.join(performance_data_dir, 'gil_performance_data.dat')
    gil_optimizer = None
    
    if MODULES_IMPORTED:
        try:
            gil_optimizer = TTEGilOptimizer(performance_data_file=performance_data_file)
            logger.info(f"已创建GIL优化器，性能数据将保存到: {performance_data_file}")
        except Exception as e:
            logger.error(f"创建GIL优化器失败: {e}")
    
    # 打印系统信息
    print(f"Python版本: {sys.version}")
    print(f"NumPy版本: {np.__version__}")
    print(f"CPU核心数: {os.cpu_count()}")
    print(f"GIL状态: {get_gil_status()}")
    print(f"nogil支持: {detect_nogil_support()}")
    
    # 创建性能分析器
    analyzer = PerformanceAnalyzer(output_dir=performance_data_dir)
    
    # 运行基准测试
    if args.all or args.quantum:
        analyzer.run_quantum_benchmarks(repetitions=args.repetitions)
    
    if args.all or args.holographic:
        analyzer.run_holographic_benchmarks(repetitions=args.repetitions)
    
    if args.all or args.fractal:
        analyzer.run_fractal_benchmarks(repetitions=args.repetitions)
    
    if args.all or args.parallel:
        analyzer.run_parallel_benchmarks(repetitions=args.repetitions)
    
    # 分析性能数据
    if args.analyze or args.all:
        analyze_gil_strategies()
    
    # 可视化性能数据
    if args.visualize or args.all:
        visualizer = PerformanceVisualizer(output_dir=os.path.join(performance_data_dir, 'plots'))
        visualizer.plot_all_benchmarks(analyzer.results)
        visualizer.plot_strategy_distribution(analyzer.results)


# 创建性能数据目录
PERFORMANCE_DATA_DIR = os.path.join(project_root, 'data', 'performance')
os.makedirs(PERFORMANCE_DATA_DIR, exist_ok=True)

# 创建GIL优化器实例，设置性能数据文件
performance_data_file = os.path.join(PERFORMANCE_DATA_DIR, 'gil_performance_data.dat')
gil_optimizer = None

if MODULES_IMPORTED:
    try:
        gil_optimizer = TTEGilOptimizer(performance_data_file=performance_data_file)
        logger.info(f"已创建GIL优化器，性能数据将保存到: {performance_data_file}")
    except Exception as e:
        logger.error(f"创建GIL优化器失败: {e}")


# 这里不是程序入口点，只是初始化全局变量


class QuantumSimulation:
    """量子模拟计算类"""
    
    @quantum_task(task_size='medium', adaptive=True, log_performance=True, profile=True)
    def simulate_quantum_evolution(self, size: int = 1000, steps: int = 10) -> np.ndarray:
        """
        模拟量子态演化
        
        Args:
            size: 量子态大小
            steps: 演化步数
            
        Returns:
            演化后的量子态
        """
        logger.info(f"模拟量子态演化 (大小: {size}, 步数: {steps})")
        
        # 初始化量子态
        state = np.random.random(size) + 1j * np.random.random(size)
        state = state / np.linalg.norm(state)
        
        # 模拟演化
        for _ in range(steps):
            # 创建随机酉矩阵
            h = np.random.random((size, size)) + 1j * np.random.random((size, size))
            u = np.eye(size) + 1j * h
            u = u / np.linalg.norm(u, ord=2)
            
            # 应用酉变换
            state = np.dot(u, state)
            state = state / np.linalg.norm(state)
        
        return state
    
    @quantum_task(task_size='large', adaptive=True, log_performance=True, profile=True)
    def compute_quantum_expectation(self, size: int = 1000, samples: int = 5) -> float:
        """
        计算量子期望值
        
        Args:
            size: 量子态大小
            samples: 样本数
            
        Returns:
            期望值
        """
        logger.info(f"计算量子期望值 (大小: {size}, 样本数: {samples})")
        
        expectations = []
        
        for _ in range(samples):
            # 创建随机量子态
            state = np.random.random(size) + 1j * np.random.random(size)
            state = state / np.linalg.norm(state)
            
            # 创建随机可观测量（厄米矩阵）
            h = np.random.random((size, size)) + 1j * np.random.random((size, size))
            observable = h + h.conj().T  # 确保厄米性
            
            # 计算期望值 <ψ|O|ψ>
            expectation = np.vdot(state, np.dot(observable, state)).real
            expectations.append(expectation)
        
        return np.mean(expectations)


class HolographicProcessor:
    """全息处理器类"""
    
    @holographic_task(task_size='medium', adaptive=True, log_performance=True, profile=True)
    def reconstruct_hologram(self, size: int = 100, iterations: int = 5) -> np.ndarray:
        """
        重建全息图
        
        Args:
            size: 全息图大小
            iterations: 迭代次数
            
        Returns:
            重建后的全息图
        """
        logger.info(f"重建全息图 (大小: {size}, 迭代次数: {iterations})")
        
        # 创建随机全息数据
        hologram = np.random.random((size, size)) * np.exp(1j * np.random.random((size, size)) * 2 * np.pi)
        
        for _ in range(iterations):
            # 应用傅里叶变换
            transformed = np.fft.fft2(hologram)
            transformed = np.fft.fftshift(transformed)
            
            # 应用非线性变换
            phase = np.angle(transformed)
            amplitude = np.abs(transformed)
            
            # 非线性调制
            modulated_amplitude = np.tanh(amplitude / np.mean(amplitude))
            
            # 重构全息图
            hologram = modulated_amplitude * np.exp(1j * phase)
            
            # 应用逆变换
            hologram = np.fft.ifftshift(hologram)
            hologram = np.fft.ifft2(hologram)
        
        return hologram
    
    @holographic_task(task_size='large', adaptive=True, log_performance=True, profile=True)
    def process_holographic_interference(self, size: int = 200, waves: int = 10) -> np.ndarray:
        """
        处理全息干涉
        
        Args:
            size: 全息图大小
            waves: 波数
            
        Returns:
            干涉图案
        """
        logger.info(f"处理全息干涉 (大小: {size}, 波数: {waves})")
        
        # 创建坐标网格
        x = np.linspace(-5, 5, size)
        y = np.linspace(-5, 5, size)
        X, Y = np.meshgrid(x, y)
        
        # 初始化干涉图案
        interference = np.zeros((size, size), dtype=complex)
        
        # 添加多个波
        for _ in range(waves):
            # 随机波矢
            kx = np.random.uniform(-3, 3)
            ky = np.random.uniform(-3, 3)
            
            # 随机相位
            phase = np.random.uniform(0, 2 * np.pi)
            
            # 随机振幅
            amplitude = np.random.uniform(0.5, 1.5)
            
            # 添加平面波
            wave = amplitude * np.exp(1j * (kx * X + ky * Y + phase))
            interference += wave
        
        # 计算干涉强度
        intensity = np.abs(interference) ** 2
        
        return intensity


class FractalGenerator:
    """分形生成器类"""
    
    @fractal_task(task_size='medium', adaptive=True, log_performance=True, profile=True)
    def generate_mandelbrot(self, width: int = 1000, height: int = 1000, max_iter: int = 100) -> np.ndarray:
        """
        生成曼德博集分形
        
        Args:
            width: 图像宽度
            height: 图像高度
            max_iter: 最大迭代次数
            
        Returns:
            分形图像
        """
        logger.info(f"生成曼德博集分形 (大小: {width}x{height}, 最大迭代: {max_iter})")
        
        # 创建坐标网格
        x_min, x_max = -2.0, 1.0
        y_min, y_max = -1.5, 1.5
        x = np.linspace(x_min, x_max, width)
        y = np.linspace(y_min, y_max, height)
        X, Y = np.meshgrid(x, y)
        c = X + 1j * Y
        
        # 初始化结果数组
        result = np.zeros((height, width), dtype=np.int32)
        z = np.zeros_like(c, dtype=complex)
        
        # 计算曼德博集
        for i in range(max_iter):
            # 曼德博集迭代: z = z^2 + c
            z = z**2 + c
            
            # 标记这一迭代中发散的点
            mask = (np.abs(z) > 2.0) & (result == 0)
            result[mask] = i
        
        # 标记没有发散的点
        result[result == 0] = max_iter
        
        return result
    
    @fractal_task(task_size='large', adaptive=True, log_performance=True, profile=True)
    def generate_julia_set(self, width: int = 1000, height: int = 1000, max_iter: int = 100, c: complex = -0.7 + 0.27j) -> np.ndarray:
        """
        生成朱利亚集分形
        
        Args:
            width: 图像宽度
            height: 图像高度
            max_iter: 最大迭代次数
            c: 复数参数
            
        Returns:
            分形图像
        """
        logger.info(f"生成朱利亚集分形 (大小: {width}x{height}, 最大迭代: {max_iter}, c: {c})")
        
        # 创建坐标网格
        x_min, x_max = -2.0, 2.0
        y_min, y_max = -2.0, 2.0
        x = np.linspace(x_min, x_max, width)
        y = np.linspace(y_min, y_max, height)
        X, Y = np.meshgrid(x, y)
        z = X + 1j * Y
        
        # 初始化结果数组
        result = np.zeros((height, width), dtype=np.int32)
        
        # 计算朱利亚集
        for i in range(max_iter):
            # 朱利亚集迭代: z = z^2 + c，其中c是固定的
            z = z**2 + c
            
            # 标记这一迭代中发散的点
            mask = (np.abs(z) > 2.0) & (result == 0)
            result[mask] = i
        
        # 标记没有发散的点
        result[result == 0] = max_iter
        
        return result


class PerformanceVisualizer:
    """性能可视化器类"""
    
    def __init__(self, output_dir: str = None):
        """
        初始化性能可视化器
        
        Args:
            output_dir: 输出目录，如果为None则使用默认目录
        """
        self.output_dir = output_dir or os.path.join(project_root, 'data', 'performance', 'plots')
        os.makedirs(self.output_dir, exist_ok=True)
    
    def plot_strategy_comparison(self, results: Dict[str, Any], title: str = "GIL策略性能比较") -> str:
        """
        绘制不同策略的性能比较图
        
        Args:
            results: 性能结果字典
            title: 图表标题
            
        Returns:
            保存的图表文件路径
        """
        if not results or not isinstance(results, dict):
            logger.warning("无效的性能结果数据")
            return None
        
        # 创建图表
        plt.figure(figsize=(12, 8))
        
        # 绘制每种策略的性能数据
        strategies = results.get('strategies', [])
        times = results.get('times', [])
        
        if 'sizes' in results:
            x_values = results['sizes']
            x_label = "数据大小"
        elif 'thread_counts' in results:
            x_values = results['thread_counts']
            x_label = "线程数"
        else:
            x_values = list(range(len(times)))
            x_label = "测试编号"
        
        # 计算平均时间
        avg_times = [sum(t)/len(t) if t else 0 for t in times]
        
        # 绘制条形图
        plt.bar(x_values, avg_times, alpha=0.7)
        
        # 添加策略标签
        for i, (x, y, strategy) in enumerate(zip(x_values, avg_times, strategies)):
            plt.text(x, y + 0.05, strategy, ha='center', va='bottom', rotation=0, fontsize=10)
        
        # 设置图表标题和标签
        plt.title(title)
        plt.xlabel(x_label)
        plt.ylabel("执行时间 (秒)")
        plt.grid(True, linestyle='--', alpha=0.7)
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{title.replace(' ', '_')}_{timestamp}.png"
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"图表已保存到: {filepath}")
        return filepath
    
    def plot_all_benchmarks(self, all_results: Dict[str, Dict[str, Any]]) -> List[str]:
        """
        绘制所有基准测试的性能比较图
        
        Args:
            all_results: 所有测试结果字典
            
        Returns:
            保存的图表文件路径列表
        """
        filepaths = []
        
        # 量子计算性能图
        if 'quantum' in all_results:
            filepath = self.plot_strategy_comparison(
                all_results['quantum'], 
                "量子计算GIL策略性能比较"
            )
            if filepath:
                filepaths.append(filepath)
        
        # 全息计算性能图
        if 'holographic' in all_results:
            filepath = self.plot_strategy_comparison(
                all_results['holographic'], 
                "全息计算GIL策略性能比较"
            )
            if filepath:
                filepaths.append(filepath)
        
        # 分形计算性能图
        if 'fractal' in all_results:
            filepath = self.plot_strategy_comparison(
                all_results['fractal'], 
                "分形计算GIL策略性能比较"
            )
            if filepath:
                filepaths.append(filepath)
        
        # 并行计算性能图
        if 'parallel' in all_results:
            filepath = self.plot_strategy_comparison(
                all_results['parallel'], 
                "并行计算GIL策略性能比较"
            )
            if filepath:
                filepaths.append(filepath)
        
        return filepaths
    
    def plot_strategy_distribution(self, all_results: Dict[str, Dict[str, Any]]) -> str:
        """
        绘制不同任务类型的最佳策略分布图
        
        Args:
            all_results: 所有测试结果字典
            
        Returns:
            保存的图表文件路径
        """
        # 收集每种任务类型的最佳策略
        task_types = ['quantum', 'holographic', 'fractal', 'parallel']
        best_strategies = {}
        
        for task_type in task_types:
            if task_type in all_results and 'strategies' in all_results[task_type]:
                strategies = all_results[task_type]['strategies']
                if strategies:
                    # 统计每种策略的出现次数
                    strategy_counts = {}
                    for strategy in strategies:
                        if strategy not in strategy_counts:
                            strategy_counts[strategy] = 0
                        strategy_counts[strategy] += 1
                    
                    # 找出出现次数最多的策略
                    best_strategy = max(strategy_counts.items(), key=lambda x: x[1])[0]
                    best_strategies[task_type] = best_strategy
        
        if not best_strategies:
            logger.warning("无法获取最佳策略数据")
            return None
        
        # 创建图表
        plt.figure(figsize=(10, 6))
        
        # 绘制饼图
        task_labels = []
        strategy_counts = {}
        
        for task_type, strategy in best_strategies.items():
            task_labels.append(task_type)
            if strategy not in strategy_counts:
                strategy_counts[strategy] = 0
            strategy_counts[strategy] += 1
        
        strategies = list(strategy_counts.keys())
        counts = list(strategy_counts.values())
        
        plt.pie(counts, labels=strategies, autopct='%1.1f%%', startangle=90, shadow=True)
        plt.axis('equal')  # 确保饼图是圆的
        
        # 设置图表标题
        plt.title("不同任务类型的最佳GIL策略分布")
        
        # 添加图例
        plt.legend(title="策略", loc="best")
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"Best_GIL_Strategies_{timestamp}.png"
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"图表已保存到: {filepath}")
        return filepath


class PerformanceAnalyzer:
    """性能分析器类"""
    
    def __init__(self, output_dir: str = None):
        """
        初始化性能分析器
        
        Args:
            output_dir: 输出目录，如果为None则使用默认目录
        """
        self.output_dir = output_dir or os.path.join(project_root, 'data', 'performance')
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 创建测试实例
        self.quantum_sim = QuantumSimulation()
        self.holographic_proc = HolographicProcessor()
        self.fractal_gen = FractalGenerator()
        self.parallel_proc = ParallelFieldProcessor()
        
        # 创建结果存储
        self.results = {}
    
    def run_quantum_benchmarks(self, sizes: List[int] = None, repetitions: int = 3) -> Dict[str, Any]:
        """
        运行量子计算基准测试
        
        Args:
            sizes: 量子态大小列表
            repetitions: 重复次数
            
        Returns:
            测试结果
        """
        sizes = sizes or [500, 1000, 2000]
        results = {'sizes': sizes, 'times': [], 'strategies': []}
        
        logger.info("=== 运行量子计算基准测试 ===")
        
        for size in sizes:
            times = []
            strategies = []
            
            for _ in range(repetitions):
                # 模拟量子态演化
                start_time = time.time()
                self.quantum_sim.simulate_quantum_evolution(size, steps=5)
                elapsed = time.time() - start_time
                times.append(elapsed)
                
                # 获取使用的策略
                try:
                    strategy = self.quantum_sim.simulate_quantum_evolution.get_best_strategy()
                    strategies.append(strategy)
                except AttributeError:
                    strategies.append('unknown')
                
                # 计算量子期望值
                start_time = time.time()
                self.quantum_sim.compute_quantum_expectation(size, samples=3)
                elapsed = time.time() - start_time
                times.append(elapsed)
            
            avg_time = sum(times) / len(times)
            logger.info(f"  量子态大小: {size}, 平均时间: {avg_time:.6f} 秒, 策略: {strategies[-1]}")
            
            results['times'].append(times)
            results['strategies'].append(strategies[-1])
        
        self.results['quantum'] = results
        return results
    
    def run_holographic_benchmarks(self, sizes: List[int] = None, repetitions: int = 3) -> Dict[str, Any]:
        """
        运行全息计算基准测试
        
        Args:
            sizes: 全息图大小列表
            repetitions: 重复次数
            
        Returns:
            测试结果
        """
        sizes = sizes or [50, 100, 200]
        results = {'sizes': sizes, 'times': [], 'strategies': []}
        
        logger.info("=== 运行全息计算基准测试 ===")
        
        for size in sizes:
            times = []
            strategies = []
            
            for _ in range(repetitions):
                # 重建全息图
                start_time = time.time()
                self.holographic_proc.reconstruct_hologram(size, iterations=3)
                elapsed = time.time() - start_time
                times.append(elapsed)
                
                # 获取使用的策略
                try:
                    strategy = self.holographic_proc.reconstruct_hologram.get_best_strategy()
                    strategies.append(strategy)
                except AttributeError:
                    strategies.append('unknown')
                
                # 处理全息干涉
                start_time = time.time()
                self.holographic_proc.process_holographic_interference(size, waves=5)
                elapsed = time.time() - start_time
                times.append(elapsed)
            
            avg_time = sum(times) / len(times)
            logger.info(f"  全息图大小: {size}, 平均时间: {avg_time:.6f} 秒, 策略: {strategies[-1]}")
            
            results['times'].append(times)
            results['strategies'].append(strategies[-1])
        
        self.results['holographic'] = results
        return results
    
    def run_fractal_benchmarks(self, sizes: List[int] = None, repetitions: int = 3) -> Dict[str, Any]:
        """
        运行分形计算基准测试
        
        Args:
            sizes: 分形图大小列表
            repetitions: 重复次数
            
        Returns:
            测试结果
        """
        sizes = sizes or [500, 750, 1000]
        results = {'sizes': sizes, 'times': [], 'strategies': []}
        
        logger.info("=== 运行分形计算基准测试 ===")
        
        for size in sizes:
            times = []
            strategies = []
            
            for _ in range(repetitions):
                # 生成曼德博集
                start_time = time.time()
                self.fractal_gen.generate_mandelbrot(size, size, max_iter=50)
                elapsed = time.time() - start_time
                times.append(elapsed)
                
                # 获取使用的策略
                try:
                    strategy = self.fractal_gen.generate_mandelbrot.get_best_strategy()
                    strategies.append(strategy)
                except AttributeError:
                    strategies.append('unknown')
            
            avg_time = sum(times) / len(times)
            logger.info(f"  分形大小: {size}x{size}, 平均时间: {avg_time:.6f} 秒, 策略: {strategies[-1]}")
            
            results['times'].append(times)
            results['strategies'].append(strategies[-1])
        
        self.results['fractal'] = results
        return results
    
    def run_parallel_benchmarks(self, thread_counts: List[int] = None, repetitions: int = 3) -> Dict[str, Any]:
        """
        运行并行计算基准测试
        
        Args:
            thread_counts: 线程数列表
            repetitions: 重复次数
            
        Returns:
            测试结果
        """
        thread_counts = thread_counts or [2, 4, 8, 16]
        results = {'thread_counts': thread_counts, 'times': [], 'strategies': []}
        
        logger.info("=== 运行并行计算基准测试 ===")
        
        for threads in thread_counts:
            times = []
            strategies = []
            
            for _ in range(repetitions):
                # 模拟场扬散
                start_time = time.time()
                self.parallel_proc.simulate_field_diffusion(100, iterations=20, num_threads=threads)
                elapsed = time.time() - start_time
                times.append(elapsed)
                
                # 获取使用的策略
                try:
                    strategy = self.parallel_proc.simulate_field_diffusion.get_best_strategy()
                    strategies.append(strategy)
                except AttributeError:
                    strategies.append('unknown')
            
            avg_time = sum(times) / len(times)
            logger.info(f"  线程数: {threads}, 平均时间: {avg_time:.6f} 秒, 策略: {strategies[-1]}")
            
            results['times'].append(times)
            results['strategies'].append(strategies[-1])
        
        self.results['parallel'] = results
        return results


class ParallelFieldProcessor:
    """并行场处理器类"""
    
    @parallel_task(task_size='medium', adaptive=True, log_performance=True, profile=True)
    def simulate_field_diffusion(self, size: int = 100, iterations: int = 50, num_threads: int = 4) -> np.ndarray:
        """
        模拟场扬散
        
        Args:
            size: 场大小
            iterations: 迭代次数
            num_threads: 线程数
            
        Returns:
            扬散后的场
        """
        logger.info(f"模拟场扬散 (大小: {size}, 迭代次数: {iterations}, 线程数: {num_threads})")
        
        # 创建随机场
        field = np.random.random((size, size))
        
        # 初始化中心源
        center = size // 2
        field[center-5:center+5, center-5:center+5] = 5.0
        
        # 将场分割为多个区域
        chunks = []
        chunk_size = size // num_threads
        for i in range(num_threads):
            start = i * chunk_size
            end = (i + 1) * chunk_size if i < num_threads - 1 else size
            chunks.append((start, end))
        
        results = [None] * num_threads
        
        # 定义扬散函数
        def diffuse_chunk(field, start, end, chunk_id, iterations):
            # 创建工作副本
            chunk = field.copy()
            
            # 扬散系数
            D = 0.1
            
            # 迭代扬散
            for _ in range(iterations):
                # 使用有限差分法计算拉普拉斯算子
                for i in range(max(1, start), min(end-1, size-1)):
                    for j in range(1, size-1):
                        chunk[i, j] += D * (
                            field[i+1, j] + field[i-1, j] +
                            field[i, j+1] + field[i, j-1] -
                            4 * field[i, j]
                        )
            
            results[chunk_id] = chunk[start:end, :]
        
        # 执行并行扬散
        threads = []
        for i, (start, end) in enumerate(chunks):
            t = threading.Thread(target=diffuse_chunk, args=(field, start, end, i, iterations))
            threads.append(t)
            t.start()
        
        for t in threads:
            t.join()
        
        # 合并结果
        result = np.zeros_like(field)
        for i, (start, end) in enumerate(chunks):
            if results[i] is not None:
                result[start:end, :] = results[i]
        
        return result
    
    @parallel_task(task_size='large', adaptive=True, log_performance=True, profile=True)
    def solve_wave_equation(self, size: int = 200, time_steps: int = 100, num_threads: int = 4) -> np.ndarray:
        """
        求解波动方程
        
        Args:
            size: 网格大小
            time_steps: 时间步数
            num_threads: 线程数
            
        Returns:
            波动场
        """
        logger.info(f"求解波动方程 (大小: {size}, 时间步数: {time_steps}, 线程数: {num_threads})")
        
        # 初始化波动场
        u = np.zeros((size, size))  # 当前时间步的场
        u_prev = np.zeros((size, size))  # 前一时间步的场
        u_next = np.zeros((size, size))  # 下一时间步的场
        
        # 初始条件：中心高斯脉冲
        center = size // 2
        sigma = size // 20
        x = np.arange(size)
        y = np.arange(size)
        X, Y = np.meshgrid(x, y)
        u = np.exp(-((X - center)**2 + (Y - center)**2) / (2 * sigma**2))
        
        # 波速和时间步长
        c = 1.0  # 波速
        dt = 0.1  # 时间步长
        dx = 1.0  # 空间步长
        
        # 稳定性参数
        alpha = (c * dt / dx)**2
        
        # 将网格分割为多个区域
        chunks = []
        chunk_size = size // num_threads
        for i in range(num_threads):
            start = i * chunk_size
            end = (i + 1) * chunk_size if i < num_threads - 1 else size
            chunks.append((start, end))
        
        results = [None] * num_threads
        
        # 定义波动方程求解函数
        def solve_wave_chunk(u, u_prev, start, end, chunk_id):
            # 创建工作副本
            u_next_chunk = np.zeros((end - start, size))
            
            # 求解波动方程
            for i in range(start, end):
                for j in range(1, size-1):
                    if i > 0 and i < size-1:
                        # 二维波动方程的有限差分形式
                        u_next_chunk[i-start, j] = 2*u[i, j] - u_prev[i, j] + alpha * (
                            u[i+1, j] + u[i-1, j] + u[i, j+1] + u[i, j-1] - 4*u[i, j]
                        )
            
            results[chunk_id] = u_next_chunk
        
        # 时间步迭代
        for t in range(time_steps):
            # 执行并行求解
            threads = []
            for i, (start, end) in enumerate(chunks):
                t = threading.Thread(target=solve_wave_chunk, args=(u, u_prev, start, end, i))
                threads.append(t)
                t.start()
            
            for t in threads:
                t.join()
            
            # 合并结果
            for i, (start, end) in enumerate(chunks):
                if results[i] is not None:
                    u_next[start:end, :] = results[i]
            
            # 边界条件：固定边界
            u_next[0, :] = 0
            u_next[-1, :] = 0
            u_next[:, 0] = 0
            u_next[:, -1] = 0
            
            # 更新场
            u_prev = u.copy()
            u = u_next.copy()
        
        return u


# 程序入口点
if __name__ == "__main__":
    # 解析命令行参数并执行相应的操作
    main()
