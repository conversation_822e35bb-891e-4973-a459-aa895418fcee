#!/usr/bin/env python3
"""
算子使用示例脚本

展示如何使用算子注册表和各种算子。
"""

import os
import sys
import logging
import json
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('..'))

from src.operators.registry import operator_registry

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def print_separator(title: str) -> None:
    """打印分隔符"""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80 + "\n")


def print_dict(data: Dict[str, Any], indent: int = 0) -> None:
    """打印字典"""
    for key, value in data.items():
        if isinstance(value, dict):
            print(" " * indent + f"{key}:")
            print_dict(value, indent + 2)
        elif isinstance(value, list):
            print(" " * indent + f"{key}:")
            for item in value:
                if isinstance(item, dict):
                    print_dict(item, indent + 2)
                else:
                    print(" " * (indent + 2) + f"{item}")
        else:
            print(" " * indent + f"{key}: {value}")


def main() -> None:
    """主函数"""
    print_separator("算子注册表示例")
    
    # 获取所有类别
    categories = operator_registry.get_categories()
    print(f"算子类别: {categories}")
    
    # 获取注册表统计信息
    stats = operator_registry.get_stats()
    print("\n注册表统计信息:")
    print_dict(stats)
    
    # 展示解释算子
    print_separator("解释算子示例")
    
    # 获取解释算子
    explanation_operators = operator_registry.get_operators_in_category("explanation")
    print(f"解释算子: {list(explanation_operators.keys())}")
    
    # 创建多层次解释生成算子
    multilevel_explanation_operator_class = operator_registry.get_operator("explanation", "multilevel_explanation")
    
    if multilevel_explanation_operator_class is not None:
        print("\n创建多层次解释生成算子...")
        multilevel_explanation_operator = multilevel_explanation_operator_class()
        
        # 获取算子元数据
        metadata = multilevel_explanation_operator.get_metadata()
        print("\n多层次解释生成算子元数据:")
        print_dict(metadata)
        
        # 准备输入数据
        input_data = {
            "model_output": {
                "prediction": "positive",
                "confidence": 0.85,
                "features": {
                    "text_length": 120,
                    "sentiment_score": 0.75,
                    "topic_relevance": 0.9
                }
            },
            "explanation_level": "all",
            "user_expertise": "beginner"
        }
        
        # 应用算子
        print("\n应用多层次解释生成算子...")
        result = multilevel_explanation_operator.apply(input_data)
        
        # 打印结果
        print("\n多层次解释生成算子结果:")
        print_dict(result)
    else:
        print("多层次解释生成算子不可用")
    
    # 展示验证算子
    print_separator("验证算子示例")
    
    # 获取验证算子
    verification_operators = operator_registry.get_operators_in_category("verification")
    print(f"验证算子: {list(verification_operators.keys())}")
    
    # 创建多方法验证算子
    multi_method_verification_operator_class = operator_registry.get_operator("verification", "multi_method_verification")
    
    if multi_method_verification_operator_class is not None:
        print("\n创建多方法验证算子...")
        multi_method_verification_operator = multi_method_verification_operator_class()
        
        # 获取算子元数据
        metadata = multi_method_verification_operator.get_metadata()
        print("\n多方法验证算子元数据:")
        print_dict(metadata)
        
        # 准备输入数据
        input_data = {
            "state": {
                "variables": {
                    "x": 10,
                    "y": 20,
                    "z": 30
                }
            },
            "properties": [
                {
                    "id": "safety1",
                    "type": "safety",
                    "expression": "G(x > 0)"
                },
                {
                    "id": "liveness1",
                    "type": "liveness",
                    "expression": "F(y = 1)"
                },
                {
                    "id": "runtime1",
                    "type": "runtime",
                    "expression": "x + y < 100"
                }
            ]
        }
        
        # 应用算子
        print("\n应用多方法验证算子...")
        result = multi_method_verification_operator.apply(input_data)
        
        # 打印结果
        print("\n多方法验证算子结果:")
        print_dict(result)
    else:
        print("多方法验证算子不可用")
    
    # 尝试使用Rust实现
    print_separator("Rust实现示例")
    
    # 获取Rust实现的多层次解释生成算子
    rust_multilevel_explanation_operator_class = operator_registry.get_operator("explanation", "rust_multilevel_explanation")
    
    if rust_multilevel_explanation_operator_class is not None:
        print("\n创建Rust实现的多层次解释生成算子...")
        rust_multilevel_explanation_operator = rust_multilevel_explanation_operator_class()
        
        # 验证是否是Rust实现
        is_rust = rust_multilevel_explanation_operator.to_rust()
        print(f"是否是Rust实现: {is_rust}")
        
        if is_rust:
            # 准备输入数据
            input_data = {
                "model_output": {
                    "prediction": "positive",
                    "confidence": 0.85,
                    "features": {
                        "text_length": 120,
                        "sentiment_score": 0.75,
                        "topic_relevance": 0.9
                    }
                },
                "explanation_level": "all",
                "user_expertise": "beginner"
            }
            
            # 应用算子
            print("\n应用Rust实现的多层次解释生成算子...")
            result = rust_multilevel_explanation_operator.apply(input_data)
            
            # 打印结果
            print("\nRust实现的多层次解释生成算子结果:")
            print_dict(result)
    else:
        print("Rust实现的多层次解释生成算子不可用")
    
    # 获取Rust实现的多方法验证算子
    rust_multi_method_verification_operator_class = operator_registry.get_operator("verification", "rust_multi_method_verification")
    
    if rust_multi_method_verification_operator_class is not None:
        print("\n创建Rust实现的多方法验证算子...")
        rust_multi_method_verification_operator = rust_multi_method_verification_operator_class()
        
        # 验证是否是Rust实现
        is_rust = rust_multi_method_verification_operator.to_rust()
        print(f"是否是Rust实现: {is_rust}")
        
        if is_rust:
            # 准备输入数据
            input_data = {
                "state": {
                    "variables": {
                        "x": 10,
                        "y": 20,
                        "z": 30
                    }
                },
                "properties": [
                    {
                        "id": "safety1",
                        "type": "safety",
                        "expression": "G(x > 0)"
                    },
                    {
                        "id": "liveness1",
                        "type": "liveness",
                        "expression": "F(y = 1)"
                    },
                    {
                        "id": "runtime1",
                        "type": "runtime",
                        "expression": "x + y < 100"
                    }
                ]
            }
            
            # 应用算子
            print("\n应用Rust实现的多方法验证算子...")
            result = rust_multi_method_verification_operator.apply(input_data)
            
            # 打印结果
            print("\nRust实现的多方法验证算子结果:")
            print_dict(result)
    else:
        print("Rust实现的多方法验证算子不可用")


if __name__ == "__main__":
    main()
