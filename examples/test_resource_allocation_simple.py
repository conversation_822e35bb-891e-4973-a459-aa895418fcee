"""
动态资源分配算子简单测试

本脚本测试动态资源分配算子的基本功能，不依赖于项目中的其他模块。
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入动态资源分配算子模块
from src.transcendental_tensor.performance_optimization.resource_allocation.models import (
    ResourceType,
    ResourcePool,
    ResourceRequest,
    ResourcePriority
)
from src.transcendental_tensor.performance_optimization.resource_allocation.adaptive_strategy import AdaptiveStrategy
from src.transcendental_tensor.performance_optimization.resource_allocation.ml_predictor import MLPredictor
from src.transcendental_tensor.performance_optimization.resource_allocation.mrcam_analyzer import MRCAMAnalyzer
from src.transcendental_tensor.performance_optimization.resource_allocation.elastic_scaler import ElasticScaler
from src.transcendental_tensor.performance_optimization.resource_allocation.dependency_manager import ResourceDependencyManager
from src.transcendental_tensor.performance_optimization.resource_allocation.distributed_coordinator import DistributedCoordinator
from src.transcendental_tensor.performance_optimization.resource_allocation.dynamic_allocator import DynamicResourceAllocator


def run_tests():
    """运行测试"""
    print("开始测试动态资源分配算子...")
    
    # 创建组件
    adaptive_strategy = AdaptiveStrategy(name="TestStrategy")
    ml_predictor = MLPredictor(name="TestPredictor")
    mrcam_analyzer = MRCAMAnalyzer(name="TestAnalyzer")
    elastic_scaler = ElasticScaler(name="TestScaler")
    dependency_manager = ResourceDependencyManager(name="TestDependencyManager")
    distributed_coordinator = DistributedCoordinator(name="TestCoordinator")
    
    # 创建动态资源分配器
    allocator = DynamicResourceAllocator(
        name="TestAllocator",
        adaptive_strategy=adaptive_strategy,
        ml_predictor=ml_predictor,
        mrcam_analyzer=mrcam_analyzer,
        elastic_scaler=elastic_scaler,
        dependency_manager=dependency_manager,
        distributed_coordinator=distributed_coordinator
    )
    
    # 创建资源池
    cpu_pool = ResourcePool(
        pool_id="cpu_pool_1",
        resource_type=ResourceType.CPU,
        capacity=100.0,
        available=100.0
    )
    
    memory_pool = ResourcePool(
        pool_id="memory_pool_1",
        resource_type=ResourceType.MEMORY,
        capacity=1024.0,
        available=1024.0
    )
    
    # 添加资源池
    allocator.add_resource_pool(cpu_pool)
    allocator.add_resource_pool(memory_pool)
    
    # 测试1：添加资源池
    print("\n测试1：添加资源池")
    gpu_pool = ResourcePool(
        pool_id="gpu_pool_1",
        resource_type=ResourceType.GPU,
        capacity=10.0,
        available=10.0
    )
    
    result = allocator.add_resource_pool(gpu_pool)
    print(f"添加资源池结果: {result}")
    
    pool = allocator.get_resource_pool("gpu_pool_1")
    print(f"获取资源池: {pool.pool_id}, 类型={pool.resource_type.value}, 容量={pool.capacity}")
    
    # 测试2：分配资源
    print("\n测试2：分配资源")
    cpu_request = ResourceRequest(
        request_id="cpu_request_1",
        resource_type=ResourceType.CPU,
        amount=10.0,
        priority=ResourcePriority.MEDIUM
    )
    
    result = allocator.allocate_resource(cpu_request)
    print(f"分配资源结果: {result['status']} - {result['message']}")
    
    allocation = allocator.get_allocation(result["allocation_id"])
    print(f"获取分配: 请求ID={allocation.request_id}, 类型={allocation.resource_type.value}, 数量={allocation.amount}")
    
    pool = allocator.get_resource_pool("cpu_pool_1")
    print(f"资源池状态: 容量={pool.capacity}, 可用={pool.available}, 已分配={pool.allocated}")
    
    # 测试3：释放资源
    print("\n测试3：释放资源")
    release_result = allocator.release_resource(result["allocation_id"])
    print(f"释放资源结果: {release_result['success']} - {release_result['message']}")
    
    pool = allocator.get_resource_pool("cpu_pool_1")
    print(f"资源池状态: 容量={pool.capacity}, 可用={pool.available}, 已分配={pool.allocated}")
    
    # 测试4：多个分配
    print("\n测试4：多个分配")
    cpu_request_1 = ResourceRequest(
        request_id="cpu_request_1",
        resource_type=ResourceType.CPU,
        amount=10.0,
        priority=ResourcePriority.MEDIUM
    )
    
    cpu_request_2 = ResourceRequest(
        request_id="cpu_request_2",
        resource_type=ResourceType.CPU,
        amount=20.0,
        priority=ResourcePriority.HIGH
    )
    
    memory_request = ResourceRequest(
        request_id="memory_request_1",
        resource_type=ResourceType.MEMORY,
        amount=256.0,
        priority=ResourcePriority.MEDIUM
    )
    
    allocator.allocate_resource(cpu_request_1)
    allocator.allocate_resource(cpu_request_2)
    allocator.allocate_resource(memory_request)
    
    all_allocations = allocator.get_allocations()
    print(f"所有分配数量: {len(all_allocations)}")
    
    cpu_allocations = allocator.get_allocations(ResourceType.CPU)
    print(f"CPU分配数量: {len(cpu_allocations)}, 总量: {sum(a.amount for a in cpu_allocations)}")
    
    memory_allocations = allocator.get_allocations(ResourceType.MEMORY)
    print(f"内存分配数量: {len(memory_allocations)}, 总量: {sum(a.amount for a in memory_allocations)}")
    
    # 测试5：清理
    print("\n测试5：清理")
    cleanup_result = allocator.cleanup()
    print(f"清理结果: 过期分配={cleanup_result['expired_allocations']}, 过期请求={cleanup_result['expired_requests']}")
    
    print("\n测试完成！")


if __name__ == "__main__":
    run_tests()
