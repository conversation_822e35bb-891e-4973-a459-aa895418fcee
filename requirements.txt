# 核心依赖
numpy>=2.2.5  # 支持无GIL模式和新的API
pyarrow>=14.0.0  # 最新版Arrow Python绑定
pyarrow-flight>=14.0.0  # Arrow Flight传输层

# 科学计算
scipy>=1.12.0  # 最新科学计算库
networkx>=3.2.0  # 图论库
pyfftw>=0.13.1  # 基于FFTW的快速傅里叶变换

# 可视化和测试
matplotlib>=3.8.0  # 数据可视化
pytest>=8.0.0  # 单元测试

# 类型提示和工具
typing-extensions>=4.9.0  # 增强的类型提示支持
mypyc>=1.8.0  # 用于性能优化的静态编译器

# 并行计算
dask>=2024.4.1  # 并行计算框架
joblib>=1.3.2  # 并行计算工具

# 仅用于开发环境
ruff>=0.2.0; extra == 'dev'  # Python linter和formatter
black>=24.1.0; extra == 'dev'  # 代码格式化工具
mypy>=1.8.0; extra == 'dev'  # 静态类型检查