#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
注册表系统测试脚本
"""

import logging
import sys
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

# 导入注册表模块
from src.rust_bindings.operator_registry import (
    OperatorCategory,
    register_operator,
    get_operator,
    list_operators,
    get_operator_metadata,
    check_compatibility,
    get_global_registry
)

# 导入错误处理算子
from src.rust_bindings.operator_registry.error_handling import (
    register_error_handling_operator,
    get_error_handling_operator_from_registry
)

# 导入自动发现模块
from src.rust_bindings.operator_registry.discovery import (
    DiscoveryConfig,
    OperatorDiscovery,
    discover_operators
)

class TestOperator:
    """测试算子"""
    
    def __init__(self, name):
        self.name = name
        
    def apply(self, data):
        """应用算子"""
        return f"Applied {self.name} to {data}"
        
    def get_metadata(self):
        """获取元数据"""
        return {
            "name": self.name,
            "type": "test",
            "description": f"Test operator {self.name}",
            "version": "1.0.0"
        }

def main():
    """主函数"""
    print("注册表系统测试")
    print("=" * 50)
    
    # 获取全局注册表
    print("\n1. 获取全局注册表")
    registry = get_global_registry()
    print(f"全局注册表: {registry}")
    
    # 注册测试算子
    print("\n2. 注册测试算子")
    test_op1 = TestOperator("test_op1")
    success1 = register_operator(
        OperatorCategory.UTILITY,
        "test_op1",
        test_op1,
        "1.0.0",
        "Test operator 1",
        ["test", "utility"],
        []
    )
    print(f"注册结果1: {success1}")
    
    test_op2 = TestOperator("test_op2")
    success2 = register_operator(
        OperatorCategory.TRANSFORM,
        "test_op2",
        test_op2,
        "2.0.0",
        "Test operator 2",
        ["test", "transform"],
        [("utility.test_op1", ">=1.0.0")]
    )
    print(f"注册结果2: {success2}")
    
    # 注册错误处理算子
    print("\n3. 注册错误处理算子")
    success3 = register_error_handling_operator()
    print(f"注册结果3: {success3}")
    
    # 列出所有算子
    print("\n4. 列出所有算子")
    operators = list_operators()
    print(f"算子数量: {len(operators)}")
    for category, name in operators:
        print(f"- {category}.{name}")
    
    # 获取算子
    print("\n5. 获取算子")
    op1 = get_operator(OperatorCategory.UTILITY, "test_op1")
    print(f"获取到算子1: {op1}")
    print(f"应用算子1: {op1.apply('test data')}")
    
    op2 = get_operator(OperatorCategory.TRANSFORM, "test_op2")
    print(f"获取到算子2: {op2}")
    print(f"应用算子2: {op2.apply('test data')}")
    
    error_op = get_error_handling_operator_from_registry()
    print(f"获取到错误处理算子: {error_op}")
    
    # 获取算子元数据
    print("\n6. 获取算子元数据")
    metadata1 = get_operator_metadata(OperatorCategory.UTILITY, "test_op1")
    print(f"算子1元数据: {metadata1}")
    
    metadata2 = get_operator_metadata(OperatorCategory.TRANSFORM, "test_op2")
    print(f"算子2元数据: {metadata2}")
    
    # 检查兼容性
    print("\n7. 检查兼容性")
    compat1 = check_compatibility(OperatorCategory.UTILITY, "test_op1", ">=1.0.0")
    print(f"算子1兼容性(>=1.0.0): {compat1}")
    
    compat2 = check_compatibility(OperatorCategory.UTILITY, "test_op1", ">=2.0.0")
    print(f"算子1兼容性(>=2.0.0): {compat2}")
    
    # 测试自动发现
    print("\n8. 测试自动发现")
    discovery = OperatorDiscovery(
        config=DiscoveryConfig(
            packages=["src.transcendental_tensor.error_handling"],
            recursive=True
        )
    )
    
    count = discovery.discover()
    print(f"发现的算子数量: {count}")
    
    discovered_ops = discovery.get_discovered_operators()
    print(f"发现的算子: {discovered_ops}")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
