use pyo3::prelude::*;

/// 简单的Rust函数测试
#[pyfunction]
fn rust_hello(name: String) -> PyResult<String> {
    Ok(format!("🚀 Hello from Rust, {}!", name))
}

/// 简单的记忆保存测试
#[pyfunction]
fn rust_save_memory(content: String) -> PyResult<String> {
    let memory_id = uuid::Uuid::new_v4().to_string();
    println!("🚀 Rust保存记忆: {} ({}字符)", memory_id, content.len());
    Ok(memory_id)
}

/// 简单的记忆搜索测试
#[pyfunction]
fn rust_search_memories(query: String, limit: Option<usize>) -> PyResult<Vec<String>> {
    let limit = limit.unwrap_or(5);
    let mut results = Vec::new();
    
    for i in 0..limit {
        results.push(format!("🚀 Rust搜索结果 {} for \"{}\"", i, query));
    }
    
    println!("🚀 Rust搜索: \"{}\" -> {}条结果", query, results.len());
    Ok(results)
}

/// Python模块定义
#[pymodule]
fn aqfh_rust_simple(_py: Python, m: &PyModule) -> PyResult<()> {
    m.add_function(wrap_pyfunction!(rust_hello, m)?)?;
    m.add_function(wrap_pyfunction!(rust_save_memory, m)?)?;
    m.add_function(wrap_pyfunction!(rust_search_memories, m)?)?;
    Ok(())
}
