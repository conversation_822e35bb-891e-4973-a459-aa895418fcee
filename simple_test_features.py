"""
简单的拓扑特征提取器测试脚本

这个脚本测试重构后的拓扑特征提取器的基本功能。
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Any, Dict, List, Tuple


# 导入辅助函数
def preprocess_input(input_data: Any) -> Dict[int, np.ndarray]:
    """预处理输入数据，转换为持续图字典"""
    # 如果输入是持续同调计算结果
    if isinstance(input_data, dict) and 'persistence_diagrams' in input_data:
        return input_data['persistence_diagrams']
    
    # 如果输入是持续图
    if isinstance(input_data, np.ndarray) and len(input_data.shape) == 2 and input_data.shape[1] == 2:
        # 假设是0维持续图
        return {0: input_data}
    
    # 如果输入是持续条形码
    if isinstance(input_data, list) and all(isinstance(item, tuple) and len(item) == 2 for item in input_data):
        # 转换为numpy数组
        diagram = np.array(input_data)
        # 假设是0维持续图
        return {0: diagram}
    
    # 如果输入已经是持续图字典
    if isinstance(input_data, dict) and all(isinstance(key, int) for key in input_data.keys()):
        return input_data
    
    # 不支持的输入类型
    raise ValueError(f"Unsupported input type: {type(input_data)}")


def compute_persistence_landscape(diagram: np.ndarray, n_landscapes: int, n_bins: int) -> np.ndarray:
    """计算持续景观"""
    # 如果持续图为空，返回零景观
    if len(diagram) == 0:
        return np.zeros((n_landscapes, n_bins))
    
    # 提取有限点
    finite_mask = np.isfinite(diagram[:, 1])
    finite_points = diagram[finite_mask]
    
    # 如果没有有限点，返回零景观
    if len(finite_points) == 0:
        return np.zeros((n_landscapes, n_bins))
    
    # 计算持续度
    persistence = finite_points[:, 1] - finite_points[:, 0]
    
    # 确定景观的范围
    min_birth = np.min(finite_points[:, 0])
    max_death = np.max(finite_points[:, 1])
    
    # 创建网格
    grid = np.linspace(min_birth, max_death, n_bins)
    
    # 初始化景观
    landscape = np.zeros((n_landscapes, n_bins))
    
    # 对于每个网格点，计算景观值
    for i, x in enumerate(grid):
        # 计算每个持续对在x处的景观值
        values = np.zeros(len(finite_points))
        
        for j, (birth, death) in enumerate(finite_points):
            if birth <= x <= death:
                values[j] = min(x - birth, death - x)
            else:
                values[j] = 0
        
        # 排序值
        values = np.sort(values)[::-1]
        
        # 填充景观
        for k in range(min(n_landscapes, len(values))):
            landscape[k, i] = values[k]
    
    return landscape


def compute_betti_curve(diagram: np.ndarray, n_bins: int) -> np.ndarray:
    """计算贝蒂曲线"""
    # 如果持续图为空，返回零曲线
    if len(diagram) == 0:
        return np.zeros(n_bins)
    
    # 确定滤值范围
    min_filtration = np.min(diagram[:, 0])
    max_filtration = np.max(diagram[:, 1][np.isfinite(diagram[:, 1])])
    
    # 创建滤值网格
    filtration_values = np.linspace(min_filtration, max_filtration, n_bins)
    
    # 计算每个滤值处的贝蒂数
    betti_curve = np.zeros(n_bins)
    
    for i, filtration_value in enumerate(filtration_values):
        # 计算在当前滤值处存活的同调类数量
        betti_curve[i] = sum(1 for birth, death in diagram 
                           if birth <= filtration_value and 
                           (death >= filtration_value or np.isinf(death)))
    
    return betti_curve


def compute_persistence_statistics(diagram: np.ndarray) -> Dict[str, float]:
    """计算持续统计特征"""
    # 如果持续图为空，返回空统计信息
    if len(diagram) == 0:
        return {
            'count': 0,
            'avg_persistence': 0.0,
            'max_persistence': 0.0,
            'total_persistence': 0.0,
            'persistence_entropy': 0.0,
            'avg_midlife': 0.0,
            'avg_birth': 0.0,
            'avg_death': 0.0
        }
    
    # 提取有限点
    finite_mask = np.isfinite(diagram[:, 1])
    finite_points = diagram[finite_mask]
    
    # 计算统计信息
    count = len(diagram)
    
    if len(finite_points) > 0:
        # 计算持续度
        persistence = finite_points[:, 1] - finite_points[:, 0]
        
        # 计算基本统计量
        avg_persistence = np.mean(persistence)
        max_persistence = np.max(persistence)
        total_persistence = np.sum(persistence)
        
        # 计算持续熵
        normalized_persistence = persistence / np.sum(persistence)
        entropy = -np.sum(normalized_persistence * np.log(normalized_persistence + 1e-10))
        
        # 计算中生命期
        midlife = (finite_points[:, 0] + finite_points[:, 1]) / 2
        avg_midlife = np.mean(midlife)
        
        # 计算平均出生和死亡时间
        avg_birth = np.mean(finite_points[:, 0])
        avg_death = np.mean(finite_points[:, 1])
    else:
        avg_persistence = 0.0
        max_persistence = 0.0
        total_persistence = 0.0
        entropy = 0.0
        avg_midlife = 0.0
        avg_birth = 0.0
        avg_death = 0.0
    
    # 存储统计信息
    return {
        'count': count,
        'avg_persistence': avg_persistence,
        'max_persistence': max_persistence,
        'total_persistence': total_persistence,
        'persistence_entropy': entropy,
        'avg_midlife': avg_midlife,
        'avg_birth': avg_birth,
        'avg_death': avg_death
    }


# 拓扑特征提取器类
class TopologicalFeatureExtractor:
    """
    拓扑特征提取器类
    
    该类提供了从持续同调结果中提取拓扑特征的方法，支持多种特征类型和提取策略。
    
    属性:
        feature_type (str): 特征类型
        max_dimension (int): 最大维数
        n_bins (int): 直方图分箱数
        name (str): 算子名称
    """
    
    def __init__(self, 
                 feature_type: str = 'persistence_landscape',
                 max_dimension: int = 2,
                 n_bins: int = 10):
        """
        初始化TopologicalFeatureExtractor算子
        
        参数:
            feature_type (str): 特征类型，可选值包括"persistence_landscape"、"betti_curve"、"persistence_statistics"
            max_dimension (int): 最大维数
            n_bins (int): 直方图分箱数
        """
        self.feature_type = feature_type
        self.max_dimension = max_dimension
        self.n_bins = n_bins
        self.name = "TopologicalFeatureExtractor"
    
    def apply(self, input_data: Any, **kwargs) -> Any:
        """
        应用TopologicalFeatureExtractor算子到输入数据
        
        参数:
            input_data: 输入数据，可以是以下形式之一：
                - 持续同调计算结果：包含持续图、持续条形码和持续贝蒂数的字典
                - 持续图：形状为(n_pairs, 2)的numpy数组，表示(birth, death)对
                - 持续条形码：(birth, death)对的列表
            **kwargs: 其他参数，包括：
                - dimensions (list, optional): 要处理的维数列表，默认为[0, 1, ..., max_dimension]
                - n_landscapes (int, optional): 持续景观的数量，默认为5
                - normalize (bool, optional): 是否归一化特征，默认为True
        
        返回:
            拓扑特征，格式取决于feature_type
        """
        # 提取参数
        dimensions = kwargs.get('dimensions', list(range(self.max_dimension + 1)))
        n_landscapes = kwargs.get('n_landscapes', 5)
        normalize = kwargs.get('normalize', True)
        
        # 预处理输入数据
        persistence_data = preprocess_input(input_data)
        
        # 根据特征类型选择提取函数
        if self.feature_type == 'persistence_landscape':
            return self._extract_persistence_landscape(persistence_data, dimensions, n_landscapes, normalize)
        elif self.feature_type == 'betti_curve':
            return self._extract_betti_curve(persistence_data, dimensions, self.n_bins, normalize)
        elif self.feature_type == 'persistence_statistics':
            return self._extract_persistence_statistics(persistence_data, dimensions, normalize)
        else:
            raise ValueError(f"Unknown feature type: {self.feature_type}")
    
    def _extract_persistence_landscape(self, persistence_data: Dict[int, np.ndarray], 
                                      dimensions: List[int], n_landscapes: int, 
                                      normalize: bool) -> Dict[int, np.ndarray]:
        """提取持续景观特征"""
        # 初始化结果
        landscapes = {}
        
        # 对于每个维度，计算持续景观
        for dim in dimensions:
            if dim in persistence_data:
                diagram = persistence_data[dim]
                
                # 如果持续图为空，跳过
                if len(diagram) == 0:
                    landscapes[dim] = np.zeros((n_landscapes, self.n_bins))
                    continue
                
                # 计算持续景观
                landscape = compute_persistence_landscape(diagram, n_landscapes, self.n_bins)
                
                # 归一化
                if normalize and np.max(np.abs(landscape)) > 0:
                    landscape = landscape / np.max(np.abs(landscape))
                
                landscapes[dim] = landscape
        
        return landscapes
    
    def _extract_betti_curve(self, persistence_data: Dict[int, np.ndarray], 
                            dimensions: List[int], n_bins: int, 
                            normalize: bool) -> Dict[int, np.ndarray]:
        """提取贝蒂曲线特征"""
        # 初始化结果
        betti_curves = {}
        
        # 对于每个维度，计算贝蒂曲线
        for dim in dimensions:
            if dim in persistence_data:
                diagram = persistence_data[dim]
                
                # 如果持续图为空，跳过
                if len(diagram) == 0:
                    betti_curves[dim] = np.zeros(n_bins)
                    continue
                
                # 计算贝蒂曲线
                betti_curve = compute_betti_curve(diagram, n_bins)
                
                # 归一化
                if normalize and np.max(betti_curve) > 0:
                    betti_curve = betti_curve / np.max(betti_curve)
                
                betti_curves[dim] = betti_curve
        
        return betti_curves
    
    def _extract_persistence_statistics(self, persistence_data: Dict[int, np.ndarray], 
                                       dimensions: List[int], 
                                       normalize: bool) -> Dict[int, Dict[str, float]]:
        """提取持续统计特征"""
        # 初始化结果
        statistics = {}
        
        # 对于每个维度，计算统计信息
        for dim in dimensions:
            if dim in persistence_data:
                diagram = persistence_data[dim]
                
                # 计算统计信息
                stats = compute_persistence_statistics(diagram)
                
                # 归一化
                if normalize and stats['total_persistence'] > 0:
                    for key in ['avg_persistence', 'max_persistence', 'total_persistence', 
                               'avg_midlife', 'avg_birth', 'avg_death']:
                        stats[key] /= stats['total_persistence']
                
                # 存储统计信息
                statistics[dim] = stats
        
        return statistics
    
    def __str__(self) -> str:
        """返回算子的字符串表示"""
        return f"TopologicalFeatureExtractor(feature_type={self.feature_type}, max_dimension={self.max_dimension})"


def generate_circle_data(n_points=100, noise=0.1):
    """生成圆形点云数据"""
    theta = np.linspace(0, 2*np.pi, n_points)
    x = np.cos(theta) + np.random.normal(0, noise, n_points)
    y = np.sin(theta) + np.random.normal(0, noise, n_points)
    return np.column_stack((x, y))


def generate_figure_eight_data(n_points=100, noise=0.1):
    """生成8字形点云数据"""
    t = np.linspace(0, 2*np.pi, n_points)
    x = np.sin(t)
    y = np.sin(t) * np.cos(t)
    
    # 添加噪声
    x += np.random.normal(0, noise, n_points)
    y += np.random.normal(0, noise, n_points)
    
    return np.column_stack((x, y))


def compute_persistence_diagrams(points):
    """计算持续图（简化版）"""
    # 这里我们使用简化的实现，实际应用中应该使用专门的库如Ripser或GUDHI
    
    # 0维持续图（连通分量）
    diagram_0 = np.array([
        [0.0, 0.1],
        [0.0, 0.2],
        [0.0, 0.3],
        [0.0, 0.4],
        [0.0, float('inf')]  # 一个无限持续的连通分量
    ])
    
    # 1维持续图（环）
    diagram_1 = np.array([
        [0.2, 0.8]  # 一个环
    ])
    
    return {
        'persistence_diagrams': {
            0: diagram_0,
            1: diagram_1
        }
    }


def test_topological_feature_extractor():
    """测试TopologicalFeatureExtractor"""
    print("\n测试TopologicalFeatureExtractor...")
    
    # 生成两种不同的数据
    circle_data = generate_circle_data(n_points=50, noise=0.05)
    eight_data = generate_figure_eight_data(n_points=50, noise=0.05)
    
    # 计算持续图
    circle_ph = compute_persistence_diagrams(circle_data)
    eight_ph = compute_persistence_diagrams(eight_data)
    
    # 创建特征提取器
    feature_extractor = TopologicalFeatureExtractor(feature_type='persistence_statistics')
    print(f"创建特征提取器: {feature_extractor}")
    
    # 应用特征提取器
    circle_features = feature_extractor.apply(circle_ph)
    eight_features = feature_extractor.apply(eight_ph)
    
    # 打印结果
    print(f"拓扑特征提取结果:")
    
    # 比较两种数据的特征
    print(f"  圆形数据 vs 8字形数据:")
    for dim in set(circle_features.keys()) & set(eight_features.keys()):
        print(f"  {dim}维特征比较:")
        for key in set(circle_features[dim].keys()) & set(eight_features[dim].keys()):
            circle_value = circle_features[dim][key]
            eight_value = eight_features[dim][key]
            print(f"    {key}: {circle_value:.4f} vs {eight_value:.4f}")
    
    # 测试其他特征类型
    feature_types = ['betti_curve', 'persistence_landscape']
    for feature_type in feature_types:
        feature_extractor = TopologicalFeatureExtractor(feature_type=feature_type)
        print(f"\n  提取{feature_type}特征:")
        
        circle_features = feature_extractor.apply(circle_ph)
        eight_features = feature_extractor.apply(eight_ph)
        
        for dim in set(circle_features.keys()) & set(eight_features.keys()):
            print(f"    {dim}维{feature_type}特征形状:")
            print(f"      圆形数据: {np.array(circle_features[dim]).shape}")
            print(f"      8字形数据: {np.array(eight_features[dim]).shape}")
    
    print("TopologicalFeatureExtractor测试完成")


def main():
    """主函数"""
    print("开始测试拓扑特征提取器...")
    
    # 测试TopologicalFeatureExtractor
    test_topological_feature_extractor()
    
    print("\n所有测试完成")


if __name__ == "__main__":
    main()
