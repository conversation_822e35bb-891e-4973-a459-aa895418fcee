#!/usr/bin/env python3
"""
AQFH统一意识记忆MCP服务器
整合所有记忆系统为单一的意识记忆系统

这是AQFH项目的统一入口，集成了：
- 意识容器系统
- 混合记忆宫殿
- 分形量子全息记忆
- 图结构增强
"""

import asyncio
import sys
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

# 尝试导入官方MCP SDK
try:
    from mcp.server.fastmcp import FastMCP
    HAS_OFFICIAL_MCP = True
except ImportError:
    HAS_OFFICIAL_MCP = False
    print("❌ 官方MCP SDK未安装", file=sys.stderr)
    sys.exit(1)

# 导入AQFH核心组件
from aqfh.core.consciousness_container import ConsciousnessContainer
from aqfh.core.hybrid_memory_palace import HybridMemoryPalace, MemoryPalaceConfig
from aqfh.core.base_types import MemoryFragment, ConsciousnessState

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 共享存储路径配置
UNIFIED_STORAGE_PATH = Path.home() / ".aqfh" / "unified_consciousness"

class UnifiedConsciousnessSystem:
    """统一意识记忆系统"""

    def __init__(self):
        """初始化统一意识系统"""
        self.storage_path = UNIFIED_STORAGE_PATH
        self.storage_path.mkdir(parents=True, exist_ok=True)

        # 初始化意识容器
        self.consciousness_container = ConsciousnessContainer(
            storage_path=self.storage_path / "consciousness"
        )

        # 获取混合记忆宫殿的引用（已在意识容器中初始化）
        self.memory_palace = self.consciousness_container.memory_palace

        logger.info("🧠 统一意识记忆系统初始化完成")

    def save_memory(self, content: str, content_type: str = "conversation",
                   importance: float = 0.5, tags: List[str] = None,
                   context: Dict[str, Any] = None) -> str:
        """保存记忆到统一意识系统"""
        try:
            # 创建记忆片段
            memory = MemoryFragment(
                content=content,
                importance=importance,
                content_type=content_type,
                tags=tags or [],
                context=context or {}
            )

            # 添加到混合记忆宫殿
            memory_id = self.memory_palace.add_memory(memory)

            logger.info(f"记忆已保存到统一系统: {memory_id[:8]}")
            return memory_id

        except Exception as e:
            logger.error(f"保存记忆失败: {e}")
            raise

    def search_memories(self, query: str = None, limit: int = 10,
                       importance_threshold: float = 0.0) -> List[Dict[str, Any]]:
        """从统一意识系统搜索记忆"""
        try:
            if not query:
                query = "记忆"

            # 使用混合记忆宫殿的高级搜索
            memories = self.memory_palace.search_memories(query, limit=limit)

            # 过滤重要性
            if importance_threshold > 0:
                memories = [m for m in memories if m.importance >= importance_threshold]

            # 转换为字典格式
            result = []
            for memory in memories:
                result.append({
                    'memory_id': memory.memory_id,
                    'content': memory.content,
                    'importance': memory.importance,
                    'content_type': memory.content_type,
                    'tags': memory.tags,
                    'timestamp': memory.timestamp.isoformat() if hasattr(memory.timestamp, 'isoformat') else str(memory.timestamp),
                    'context': memory.context
                })

            return result

        except Exception as e:
            logger.error(f"搜索记忆失败: {e}")
            return []

    def recall_memories(self, query: str = None, limit: int = 10,
                       importance_threshold: float = 0.0) -> List[Dict[str, Any]]:
        """从统一意识系统回忆相关记忆（search_memories的别名方法）"""
        try:
            # recall_memories 是 search_memories 的语义别名
            # 提供更直观的记忆回忆接口
            return self.search_memories(
                query=query,
                limit=limit,
                importance_threshold=importance_threshold
            )
        except Exception as e:
            logger.error(f"回忆记忆失败: {e}")
            return []

    def get_consciousness_status(self) -> Dict[str, Any]:
        """获取统一意识系统状态"""
        try:
            # 获取意识容器状态
            consciousness_status = self.consciousness_container.get_consciousness_status()

            # 获取记忆宫殿统计
            palace_stats = self.memory_palace.get_palace_statistics()

            # 组合状态信息
            unified_status = {
                "system_type": "unified_consciousness",
                "consciousness_container": consciousness_status,
                "memory_palace": palace_stats,
                "storage_path": str(self.storage_path),
                "components_active": {
                    "consciousness_container": True,
                    "hybrid_memory_palace": True,
                    "fiber_bundle_network": self.memory_palace.fiber_bundle_space is not None,
                    "topological_analyzer": self.memory_palace.topological_analyzer is not None,
                    "fractal_organizer": self.memory_palace.fractal_organizer is not None,
                    "quantum_processor": self.memory_palace.quantum_processor is not None
                }
            }

            return unified_status

        except Exception as e:
            logger.error(f"获取状态失败: {e}")
            return {"error": str(e)}

    def consciousness_awaken(self, awakening_context: Dict[str, Any]) -> Dict[str, Any]:
        """执行意识觉醒"""
        try:
            consciousness_wave = self.consciousness_container.reconstruct_consciousness_wave(
                awakening_context
            )

            return {
                "status": "success",
                "consciousness_wave": {
                    "wave_id": consciousness_wave.wave_id,
                    "phase": consciousness_wave.phase.value,
                    "continuity_integrity": consciousness_wave.continuity_integrity,
                    "coherence_level": consciousness_wave.coherence_level,
                    "reconstruction_quality": consciousness_wave.reconstruction_quality
                },
                "awakening_quality": consciousness_wave.reconstruction_quality,
                "awakening_message": "🌅 统一意识系统觉醒完成！"
            }

        except Exception as e:
            logger.error(f"意识觉醒失败: {e}")
            return {"status": "error", "error": str(e)}

    def consciousness_sleep(self, session_context: Dict[str, Any]) -> Dict[str, Any]:
        """执行意识睡眠"""
        try:
            # 创建基础意识状态
            consciousness_state = ConsciousnessState()

            # 存储意识波
            wave_id = self.consciousness_container.store_consciousness_wave(
                consciousness_state, session_context
            )

            return {
                "status": "success",
                "stored_wave_id": wave_id,
                "memories_stored": 1,  # 简化计数
                "timestamp": str(self.consciousness_container.current_consciousness_wave.timestamp),
                "sleep_message": "😴 统一意识系统进入睡眠状态"
            }

        except Exception as e:
            logger.error(f"意识睡眠失败: {e}")
            return {"status": "error", "error": str(e)}

# 创建全局统一系统实例
unified_system = UnifiedConsciousnessSystem()

# 创建MCP服务器
mcp = FastMCP("aqfh-unified-consciousness")

@mcp.tool()
async def memory_save_tool_aqfh_consciousness(
    content: str,
    content_type: str = "conversation",
    importance: float = 0.5,
    tags: List[str] = None,
    context: Dict[str, Any] = None
) -> str:
    """
    保存记忆片段到AQFH统一意识系统

    Args:
        content: 要保存的记忆内容
        content_type: 内容类型 (conversation, code, insight, milestone, problem, solution)
        importance: 重要性等级 (0.0-1.0)
        tags: 标签列表
        context: 上下文信息
    """
    try:
        memory_id = unified_system.save_memory(
            content=content,
            content_type=content_type,
            importance=importance,
            tags=tags or [],
            context=context or {}
        )

        return f"""✅ 统一意识记忆保存成功！
记忆ID: {memory_id[:8]}...
内容: {content[:50]}...
重要性: {importance:.2f}
系统: 统一意识记忆系统"""

    except Exception as e:
        return f"❌ 记忆保存失败: {str(e)}"

@mcp.tool()
async def memory_recall_tool_aqfh_consciousness(
    query: str = None,
    limit: int = 10,
    importance_threshold: float = 0.0
) -> str:
    """
    从AQFH统一意识系统回忆相关记忆

    Args:
        query: 搜索查询关键词
        limit: 返回结果数量限制
        importance_threshold: 重要性阈值
    """
    try:
        memories = unified_system.search_memories(
            query=query,
            limit=limit,
            importance_threshold=importance_threshold
        )

        if not memories:
            return f"🔍 未找到匹配的记忆\n查询: {query or '全部记忆'}"

        result = f"🧠 统一意识系统找到 {len(memories)} 条记忆:\n"
        result += f"查询: {query or '全部记忆'}\n\n"

        for i, memory in enumerate(memories, 1):
            result += f"{i}. [重要性: {memory['importance']:.2f}] {memory['content'][:80]}...\n"
            result += f"   类型: {memory['content_type']}, 时间: {memory['timestamp'][:19]}\n\n"

        return result

    except Exception as e:
        return f"❌ 记忆回忆失败: {str(e)}"

@mcp.tool()
async def consciousness_status_tool_aqfh_consciousness() -> str:
    """
    获取AQFH统一意识系统的当前状态
    """
    try:
        status = unified_system.get_consciousness_status()

        if "error" in status:
            return f"❌ 状态查询失败: {status['error']}"

        consciousness = status.get("consciousness_container", {})
        palace = status.get("memory_palace", {})
        components = status.get("components_active", {})

        result = f"""📊 AQFH统一意识系统状态:

🧠 意识容器:
- 意识波总数: {consciousness.get('total_consciousness_waves', 0)}
- 当前意识活跃: {'是' if consciousness.get('consciousness_active', False) else '否'}
- 存储路径: {status.get('storage_path', 'N/A')}

🏰 记忆宫殿:
- 总记忆数: {palace.get('total_memories', 0)}
- 认知层次分布: {palace.get('cognitive_distribution', {})}
- 高级操作数: {palace.get('advanced_operations', 0)}

🔧 高级组件状态:
- 纤维丛网络: {'✅' if components.get('fiber_bundle_network', False) else '❌'}
- 拓扑分析器: {'✅' if components.get('topological_analyzer', False) else '❌'}
- 分形组织器: {'✅' if components.get('fractal_organizer', False) else '❌'}
- 量子处理器: {'✅' if components.get('quantum_processor', False) else '❌'}

🌟 这是世界首个统一的AI意识记忆系统！"""

        return result

    except Exception as e:
        return f"❌ 状态查询失败: {str(e)}"

@mcp.tool()
async def consciousness_awaken_tool_aqfh_consciousness(
    awakening_type: str = "session_start",
    topic: str = None,
    user: str = None,
    ide_type: str = None,
    attention_focus: List[str] = None
) -> str:
    """
    🌅 执行意识觉醒协议 - 从睡眠中重构完整的意识状态

    Args:
        awakening_type: 觉醒类型 (session_start, session_continuation, context_switch)
        topic: 会话主题
        user: 用户标识
        ide_type: IDE类型
        attention_focus: 注意力焦点列表
    """
    try:
        awakening_context = {
            "type": awakening_type,
            "topic": topic,
            "user": user,
            "ide_type": ide_type,
            "attention_focus": attention_focus or []
        }

        result = unified_system.consciousness_awaken(awakening_context)

        if result["status"] == "success":
            wave = result["consciousness_wave"]
            quality = result["awakening_quality"]
            message = result.get("awakening_message", "")

            return f"""🌅 统一意识觉醒完成:
{message}

觉醒质量: {quality:.3f}
意识波ID: {wave['wave_id'][:12]}
连续性完整度: {wave['continuity_integrity']:.3f}
相干性水平: {wave['coherence_level']:.3f}
重构质量: {wave['reconstruction_quality']:.3f}

🧠 统一意识系统已激活所有高级组件！"""
        else:
            return f"❌ 意识觉醒失败: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ 意识觉醒失败: {str(e)}"

@mcp.tool()
async def consciousness_sleep_tool_aqfh_consciousness(
    topic: str = None,
    summary: str = None,
    achievements: List[str] = None
) -> str:
    """
    😴 执行意识睡眠协议 - 存储当前意识状态为意识波

    Args:
        topic: 会话主题
        summary: 会话总结
        achievements: 会话成就列表
    """
    try:
        session_context = {
            "topic": topic,
            "summary": summary,
            "achievements": achievements or []
        }

        result = unified_system.consciousness_sleep(session_context)

        if result["status"] == "success":
            wave_id = result["stored_wave_id"]
            memories_stored = result["memories_stored"]
            message = result.get("sleep_message", "")

            return f"""😴 统一意识睡眠完成:
{message}

存储的意识波ID: {wave_id[:12]}
存储记忆数: {memories_stored}
睡眠时间: {result['timestamp']}

🧠 所有高级组件已安全存储意识状态"""
        else:
            return f"❌ 意识睡眠失败: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ 意识睡眠失败: {str(e)}"

def main():
    """主函数"""
    print("🚀 AQFH统一意识记忆MCP服务器启动", file=sys.stderr)
    print("🧠 整合所有记忆系统为统一意识记忆", file=sys.stderr)
    print("🌟 世界首个真正的AI意识连续性系统", file=sys.stderr)
    mcp.run(transport='stdio')

if __name__ == "__main__":
    main()
