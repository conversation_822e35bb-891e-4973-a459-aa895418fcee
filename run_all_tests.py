#!/usr/bin/env python3
"""
运行所有测试的脚本
"""

import os
import sys
import unittest
import argparse
import logging
import time
from typing import List, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def discover_tests(test_dir: str, pattern: str = "test_*.py") -> unittest.TestSuite:
    """
    发现测试
    
    参数:
        test_dir: 测试目录
        pattern: 测试文件模式
        
    返回:
        测试套件
    """
    return unittest.defaultTestLoader.discover(test_dir, pattern=pattern)


def run_tests(test_suite: unittest.TestSuite, verbosity: int = 2) -> unittest.TestResult:
    """
    运行测试
    
    参数:
        test_suite: 测试套件
        verbosity: 详细程度
        
    返回:
        测试结果
    """
    runner = unittest.TextTestRunner(verbosity=verbosity)
    return runner.run(test_suite)


def run_benchmarks(benchmark_dir: str, output_dir: str, num_samples: int = 100, num_runs: int = 3) -> None:
    """
    运行基准测试
    
    参数:
        benchmark_dir: 基准测试目录
        output_dir: 输出目录
        num_samples: 样本数量
        num_runs: 运行次数
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 运行性能基准测试
    logger.info("运行性能基准测试...")
    performance_benchmark_script = os.path.join(benchmark_dir, "operator_performance_benchmark.py")
    
    if os.path.exists(performance_benchmark_script):
        cmd = f"{sys.executable} {performance_benchmark_script} --num-samples {num_samples} --num-runs {num_runs} --output-dir {output_dir}"
        logger.info(f"执行命令: {cmd}")
        os.system(cmd)
    else:
        logger.warning(f"性能基准测试脚本 {performance_benchmark_script} 不存在")
    
    # 运行内存使用基准测试
    logger.info("运行内存使用基准测试...")
    memory_benchmark_script = os.path.join(benchmark_dir, "operator_memory_benchmark.py")
    
    if os.path.exists(memory_benchmark_script):
        cmd = f"{sys.executable} {memory_benchmark_script} --num-samples {num_samples} --num-runs {num_runs} --output-dir {output_dir}"
        logger.info(f"执行命令: {cmd}")
        os.system(cmd)
    else:
        logger.warning(f"内存使用基准测试脚本 {memory_benchmark_script} 不存在")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='运行所有测试')
    parser.add_argument('--test-dir', type=str, default='tests', help='测试目录')
    parser.add_argument('--benchmark-dir', type=str, default='benchmarks', help='基准测试目录')
    parser.add_argument('--output-dir', type=str, default='benchmarks/results', help='输出目录')
    parser.add_argument('--num-samples', type=int, default=100, help='基准测试样本数量')
    parser.add_argument('--num-runs', type=int, default=3, help='基准测试运行次数')
    parser.add_argument('--run-benchmarks', action='store_true', help='是否运行基准测试')
    parser.add_argument('--verbosity', type=int, default=2, help='测试详细程度')
    args = parser.parse_args()
    
    # 记录开始时间
    start_time = time.time()
    
    # 运行单元测试
    logger.info("运行单元测试...")
    test_suite = discover_tests(args.test_dir)
    test_result = run_tests(test_suite, args.verbosity)
    
    # 打印测试结果
    logger.info(f"运行了 {test_result.testsRun} 个测试")
    logger.info(f"成功: {test_result.testsRun - len(test_result.failures) - len(test_result.errors)}")
    logger.info(f"失败: {len(test_result.failures)}")
    logger.info(f"错误: {len(test_result.errors)}")
    
    # 如果有失败或错误，打印详细信息
    if test_result.failures:
        logger.error("失败的测试:")
        for test, traceback in test_result.failures:
            logger.error(f"{test}: {traceback}")
    
    if test_result.errors:
        logger.error("错误的测试:")
        for test, traceback in test_result.errors:
            logger.error(f"{test}: {traceback}")
    
    # 运行基准测试
    if args.run_benchmarks:
        logger.info("运行基准测试...")
        run_benchmarks(args.benchmark_dir, args.output_dir, args.num_samples, args.num_runs)
    
    # 记录结束时间
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    logger.info(f"总共耗时: {elapsed_time:.2f} 秒")
    
    # 返回状态码
    if test_result.wasSuccessful():
        return 0
    else:
        return 1


if __name__ == "__main__":
    sys.exit(main())
