#!/usr/bin/env python3
"""
超越态思维引擎算子注册表 (PyO3 0.24+ 兼容版本)
"""

import importlib
import inspect
from typing import Dict, List, Any, Optional, Callable, Type, Union


class OperatorRegistry:
    """算子注册表"""
    
    def __init__(self):
        """初始化算子注册表"""
        self._operators = {}
        self._operator_metadata = {}
        self._operator_categories = {}
    
    def register_operator(self, name: str, operator_class: Type, category: str = "general") -> None:
        """
        注册算子
        
        参数:
            name: 算子名称
            operator_class: 算子类
            category: 算子类别
        """
        if name in self._operators:
            raise ValueError(f"算子 {name} 已经注册")
        
        self._operators[name] = operator_class
        
        # 获取算子元数据
        metadata = {
            "name": name,
            "category": category,
            "class": operator_class.__name__,
            "module": operator_class.__module__,
            "doc": operator_class.__doc__,
            "methods": [method for method in dir(operator_class) if not method.startswith("_") and callable(getattr(operator_class, method))],
            "parameters": inspect.signature(operator_class.__init__).parameters
        }
        
        self._operator_metadata[name] = metadata
        
        # 更新算子类别
        if category not in self._operator_categories:
            self._operator_categories[category] = []
        
        self._operator_categories[category].append(name)
    
    def get_operator_class(self, name: str) -> Type:
        """
        获取算子类
        
        参数:
            name: 算子名称
            
        返回:
            算子类
        """
        if name not in self._operators:
            raise ValueError(f"算子 {name} 未注册")
        
        return self._operators[name]
    
    def create_operator(self, name: str, **kwargs) -> Any:
        """
        创建算子实例
        
        参数:
            name: 算子名称
            **kwargs: 算子参数
            
        返回:
            算子实例
        """
        operator_class = self.get_operator_class(name)
        return operator_class(**kwargs)
    
    def get_operator_metadata(self, name: str) -> Dict[str, Any]:
        """
        获取算子元数据
        
        参数:
            name: 算子名称
            
        返回:
            算子元数据
        """
        if name not in self._operator_metadata:
            raise ValueError(f"算子 {name} 未注册")
        
        return self._operator_metadata[name]
    
    def get_operator_categories(self) -> Dict[str, List[str]]:
        """
        获取算子类别
        
        返回:
            算子类别
        """
        return self._operator_categories
    
    def get_operators_by_category(self, category: str) -> List[str]:
        """
        获取指定类别的算子
        
        参数:
            category: 算子类别
            
        返回:
            算子列表
        """
        if category not in self._operator_categories:
            raise ValueError(f"类别 {category} 不存在")
        
        return self._operator_categories[category]
    
    def get_all_operators(self) -> Dict[str, Type]:
        """
        获取所有算子
        
        返回:
            算子字典
        """
        return self._operators
    
    def get_all_operator_metadata(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有算子元数据
        
        返回:
            算子元数据字典
        """
        return self._operator_metadata
    
    def compose_operators(self, operators: List[str], composition_type: str = "sequential") -> Callable:
        """
        组合算子
        
        参数:
            operators: 算子名称列表
            composition_type: 组合类型，可选值：sequential, parallel
            
        返回:
            组合算子
        """
        if composition_type not in ["sequential", "parallel"]:
            raise ValueError(f"不支持的组合类型: {composition_type}")
        
        operator_instances = [self.create_operator(name) for name in operators]
        
        if composition_type == "sequential":
            def sequential_apply(input_data: Dict[str, Any]) -> Dict[str, Any]:
                result = input_data
                for operator in operator_instances:
                    result = operator.apply(result)
                return result
            
            return sequential_apply
        else:  # parallel
            def parallel_apply(input_data: Dict[str, Any]) -> Dict[str, Any]:
                results = {}
                for i, operator in enumerate(operator_instances):
                    results[f"operator_{i}"] = operator.apply(input_data)
                return results
            
            return parallel_apply
    
    def load_operators_from_module(self, module_name: str) -> None:
        """
        从模块加载算子
        
        参数:
            module_name: 模块名称
        """
        try:
            module = importlib.import_module(module_name)
            
            # 查找模块中的所有类
            for name, obj in inspect.getmembers(module):
                if inspect.isclass(obj) and obj.__module__ == module_name:
                    # 检查类是否有apply方法
                    if hasattr(obj, "apply") and callable(getattr(obj, "apply")):
                        # 注册算子
                        self.register_operator(name, obj)
        except ImportError:
            raise ImportError(f"无法导入模块: {module_name}")


# 创建全局算子注册表
registry = OperatorRegistry()

# 注册PyO3 0.24+兼容版本的算子
try:
    import explanation_v24
    registry.register_operator(
        "multilevel_explanation",
        explanation_v24.OptimizedMultilevelExplanationOperator,
        category="explanation"
    )
except ImportError:
    pass

try:
    import verifiability_v24
    registry.register_operator(
        "verifiability",
        verifiability_v24.VerifiabilityOperator,
        category="verification"
    )
except ImportError:
    pass

try:
    import self_explainability_v24
    registry.register_operator(
        "self_explainability",
        self_explainability_v24.SelfExplainabilityOperator,
        category="explanation"
    )
except ImportError:
    pass


def get_registry() -> OperatorRegistry:
    """
    获取全局算子注册表
    
    返回:
        算子注册表
    """
    return registry


def create_operator(name: str, **kwargs) -> Any:
    """
    创建算子实例
    
    参数:
        name: 算子名称
        **kwargs: 算子参数
        
    返回:
        算子实例
    """
    return registry.create_operator(name, **kwargs)


def compose_operators(operators: List[str], composition_type: str = "sequential") -> Callable:
    """
    组合算子
    
    参数:
        operators: 算子名称列表
        composition_type: 组合类型，可选值：sequential, parallel
        
    返回:
        组合算子
    """
    return registry.compose_operators(operators, composition_type)


def get_operator_metadata(name: str) -> Dict[str, Any]:
    """
    获取算子元数据
    
    参数:
        name: 算子名称
        
    返回:
        算子元数据
    """
    return registry.get_operator_metadata(name)


def get_all_operators() -> Dict[str, Type]:
    """
    获取所有算子
    
    返回:
        算子字典
    """
    return registry.get_all_operators()


def get_operators_by_category(category: str) -> List[str]:
    """
    获取指定类别的算子
    
    参数:
        category: 算子类别
        
    返回:
        算子列表
    """
    return registry.get_operators_by_category(category)


if __name__ == "__main__":
    # 打印所有注册的算子
    print("已注册的算子:")
    for name, operator_class in registry.get_all_operators().items():
        metadata = registry.get_operator_metadata(name)
        print(f"  - {name} ({metadata['category']}): {metadata['class']}")
    
    # 打印算子类别
    print("\n算子类别:")
    for category, operators in registry.get_operator_categories().items():
        print(f"  - {category}: {', '.join(operators)}")
    
    # 创建算子实例
    print("\n创建算子实例:")
    try:
        explanation_operator = create_operator("multilevel_explanation", levels=["technical", "conceptual", "analogical"])
        print(f"  - multilevel_explanation: {explanation_operator}")
        
        verifiability_operator = create_operator("verifiability", method="hybrid")
        print(f"  - verifiability: {verifiability_operator}")
        
        self_explainability_operator = create_operator("self_explainability", explanation_type="hybrid")
        print(f"  - self_explainability: {self_explainability_operator}")
    except Exception as e:
        print(f"  错误: {e}")
    
    # 组合算子
    print("\n组合算子:")
    try:
        sequential_operator = compose_operators(["multilevel_explanation", "verifiability", "self_explainability"])
        print(f"  - sequential_operator: {sequential_operator}")
        
        parallel_operator = compose_operators(["multilevel_explanation", "verifiability", "self_explainability"], composition_type="parallel")
        print(f"  - parallel_operator: {parallel_operator}")
    except Exception as e:
        print(f"  错误: {e}")
