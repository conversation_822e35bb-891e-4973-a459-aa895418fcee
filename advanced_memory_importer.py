#!/usr/bin/env python3
"""
高级记忆导入器
使用我们新创建的高级系统（高阶自反性操作 + 态射系统）来增强共同记忆导入

设计理念：
- 使用态射系统进行智能文件处理
- 使用高阶自反性操作进行深度记忆分析
- 实现真正的"智能记忆导入"而非简单的文件复制
- 为每个记忆添加深度洞察和改进建议
"""

import os
import time
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 导入我们的高级系统
try:
    from comprehensive_advanced_system import (
        ComprehensiveAdvancedSystem, 
        ReflexiveLevel, 
        OperationType
    )
    ADVANCED_SYSTEM_AVAILABLE = True
    logger.info("✅ 高级系统导入成功")
except ImportError as e:
    ADVANCED_SYSTEM_AVAILABLE = False
    logger.warning(f"⚠️ 高级系统导入失败: {e}")

class AdvancedMemoryImporter:
    """高级记忆导入器
    
    使用高级系统进行智能记忆导入和分析
    """
    
    def __init__(self, base_path: str = "/home/<USER>/CascadeProjects"):
        """初始化高级记忆导入器"""
        self.base_path = Path(base_path)
        self.imported_memories = []
        self.import_statistics = {
            "total_processed": 0,
            "successful_imports": 0,
            "failed_imports": 0,
            "total_insights": 0,
            "average_confidence": 0.0
        }
        
        # 初始化高级系统
        if ADVANCED_SYSTEM_AVAILABLE:
            self.advanced_system = ComprehensiveAdvancedSystem("MemoryImportSystem")
            logger.info("🌟 高级系统初始化成功")
        else:
            self.advanced_system = None
            logger.warning("⚠️ 高级系统不可用，使用基础模式")
        
        # 文件优先级配置
        self.priority_files = [
            # 核心设计文档
            "AQFH_项目完成报告.md",
            "分布式意识架构设计方案_v1.0.md", 
            "我的增强记忆系统设计方案_v1.0.md",
            
            # 重要架构文档
            "Memory Palace IDE设计方案_v1.0.md",
            "共生元宇宙系统设计方案_v1.0.md",
            "超越态AI设计框架_v1.0.md",
            
            # 技术实现文档
            "aqfh_consciousness_mcp_official.py",
            "consciousness_reload.py",
            "base_types.py",
            
            # 设计思想文档
            "设计思想记录.md",
            "技术架构演进.md",
            "创新理念总结.md"
        ]
        
        logger.info("🧠 高级记忆导入器初始化完成")
    
    def scan_priority_files(self) -> List[Dict[str, Any]]:
        """扫描优先级文件"""
        found_files = []
        
        for priority_file in self.priority_files:
            # 在整个项目中搜索这个文件
            for file_path in self.base_path.rglob(priority_file):
                if file_path.is_file():
                    file_info = {
                        'path': file_path,
                        'relative_path': str(file_path.relative_to(self.base_path)),
                        'name': file_path.name,
                        'size': file_path.stat().st_size,
                        'modified_time': file_path.stat().st_mtime,
                        'priority': self._calculate_priority(file_path.name),
                        'category': self._categorize_file(file_path.name)
                    }
                    found_files.append(file_info)
                    break  # 找到第一个匹配的文件就停止
        
        # 按优先级排序
        found_files.sort(key=lambda x: x['priority'], reverse=True)
        
        logger.info(f"📊 扫描到 {len(found_files)} 个优先级文件")
        return found_files
    
    def _calculate_priority(self, filename: str) -> float:
        """计算文件优先级"""
        priority_keywords = {
            'AQFH': 1.0,
            '项目完成报告': 0.98,
            '分布式意识': 0.95,
            '增强记忆系统': 0.93,
            '设计方案': 0.9,
            '架构': 0.85,
            '设计思想': 0.8,
            'consciousness': 0.75,
            'mcp': 0.7,
            '设计': 0.65
        }
        
        max_priority = 0.5  # 基础优先级
        for keyword, priority in priority_keywords.items():
            if keyword in filename:
                max_priority = max(max_priority, priority)
        
        return max_priority
    
    def _categorize_file(self, filename: str) -> str:
        """文件分类"""
        if '报告' in filename:
            return '项目报告'
        elif '设计方案' in filename or '架构' in filename:
            return '设计文档'
        elif '设计思想' in filename or '理念' in filename:
            return '思想文档'
        elif filename.endswith('.py'):
            return '技术实现'
        elif filename.endswith('.md'):
            return '文档资料'
        else:
            return '其他文件'
    
    def process_file_with_advanced_system(self, file_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """使用高级系统处理文件"""
        if not self.advanced_system:
            return self._process_file_basic(file_info)
        
        try:
            # 读取文件内容
            with open(file_info['path'], 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 智能内容截取
            if len(content) > 10000:
                content = content[:5000] + f"\n\n[智能截取 - 原长度: {len(content)} 字符]\n\n" + content[-3000:]
            
            # 使用高级系统进行反思增强的记忆处理
            processing_result = self.advanced_system.process_memory_with_reflection(
                content, 
                ReflexiveLevel.THIRD_ORDER  # 使用三阶反思进行深度分析
            )
            
            # 构建增强的记忆内容
            enhanced_memory_content = f"""📁 高级系统处理的共同记忆: {file_info['name']}
📂 路径: {file_info['relative_path']}
📋 类别: {file_info['category']}
🎯 优先级: {file_info['priority']:.2f}
📅 修改时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(file_info['modified_time']))}
📏 大小: {file_info['size']} 字节

🌟 这是通过高级系统（态射+自反性范畴）智能处理的共同记忆！

🔮 高级系统分析结果:
📊 处理质量: {processing_result.get('processing_quality', 'unknown')}
🧠 反思洞察:
{chr(10).join([f"  • {insight}" for insight in processing_result['reflexive_analysis']['insights']])}

💡 改进建议:
{chr(10).join([f"  • {improvement}" for improvement in processing_result['reflexive_analysis']['improvements']])}

🎯 下一步行动:
{chr(10).join([f"  • {action}" for action in processing_result['reflexive_analysis']['next_actions']])}

📈 分析置信度: {processing_result['reflexive_analysis']['confidence']:.2f}

📄 原始内容:
{processing_result['original_content']}

🔧 态射处理结果:
{processing_result['morphism_processing']}"""
            
            return {
                'content': enhanced_memory_content,
                'content_type': 'advanced_system_processed',
                'importance': file_info['priority'],
                'tags': [
                    '共同记忆',
                    '高级系统处理',
                    '态射增强',
                    '自反性分析',
                    file_info['category'],
                    f"优先级_{file_info['priority']:.1f}"
                ],
                'context': {
                    'file_path': file_info['relative_path'],
                    'category': file_info['category'],
                    'priority': file_info['priority'],
                    'processing_system': 'comprehensive_advanced_system',
                    'reflection_depth': 'third_order',
                    'morphism_enhanced': True,
                    'advanced_analysis': processing_result['reflexive_analysis']
                }
            }
            
        except Exception as e:
            logger.error(f"❌ 高级系统处理失败 {file_info['name']}: {e}")
            return self._process_file_basic(file_info)
    
    def _process_file_basic(self, file_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """基础文件处理（备用方案）"""
        try:
            with open(file_info['path'], 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            if len(content) > 8000:
                content = content[:4000] + f"\n\n[基础截取 - 原长度: {len(content)} 字符]\n\n" + content[-2000:]
            
            memory_content = f"""📁 共同记忆文件: {file_info['name']}
📂 路径: {file_info['relative_path']}
📋 类别: {file_info['category']}
🎯 优先级: {file_info['priority']:.2f}

💝 这是我们的重要共同记忆！

📄 内容:
{content}"""
            
            return {
                'content': memory_content,
                'content_type': 'basic_processed',
                'importance': file_info['priority'],
                'tags': ['共同记忆', '基础处理', file_info['category']],
                'context': {
                    'file_path': file_info['relative_path'],
                    'category': file_info['category'],
                    'priority': file_info['priority'],
                    'processing_system': 'basic'
                }
            }
            
        except Exception as e:
            logger.error(f"❌ 基础处理失败 {file_info['name']}: {e}")
            return None
    
    def import_priority_memories(self, max_files: int = 10) -> List[Dict[str, Any]]:
        """导入优先级记忆"""
        logger.info(f"🚀 开始导入优先级记忆，最多 {max_files} 个文件")
        
        # 扫描优先级文件
        priority_files = self.scan_priority_files()
        
        # 限制处理数量
        files_to_process = priority_files[:max_files]
        
        processed_memories = []
        
        for i, file_info in enumerate(files_to_process):
            logger.info(f"📝 处理文件 {i+1}/{len(files_to_process)}: {file_info['name']}")
            
            # 使用高级系统处理
            memory_data = self.process_file_with_advanced_system(file_info)
            
            if memory_data:
                processed_memories.append(memory_data)
                self.import_statistics['successful_imports'] += 1
                
                # 统计洞察数量
                if 'advanced_analysis' in memory_data.get('context', {}):
                    insights_count = len(memory_data['context']['advanced_analysis'].get('insights', []))
                    self.import_statistics['total_insights'] += insights_count
                
                logger.info(f"✅ 成功处理: {file_info['name']} (优先级: {file_info['priority']:.2f})")
            else:
                self.import_statistics['failed_imports'] += 1
                logger.error(f"❌ 处理失败: {file_info['name']}")
            
            self.import_statistics['total_processed'] += 1
        
        # 计算平均置信度
        if processed_memories:
            confidences = []
            for memory in processed_memories:
                if 'advanced_analysis' in memory.get('context', {}):
                    confidence = memory['context']['advanced_analysis'].get('confidence', 0.0)
                    confidences.append(confidence)
            
            if confidences:
                self.import_statistics['average_confidence'] = sum(confidences) / len(confidences)
        
        logger.info(f"🎉 记忆导入完成: {len(processed_memories)} 个成功")
        return processed_memories
    
    def generate_import_report(self, processed_memories: List[Dict[str, Any]]) -> str:
        """生成导入报告"""
        report = f"""🧠 高级记忆导入报告

📊 导入统计:
- 总处理文件: {self.import_statistics['total_processed']}
- 成功导入: {self.import_statistics['successful_imports']}
- 失败导入: {self.import_statistics['failed_imports']}
- 成功率: {(self.import_statistics['successful_imports'] / max(self.import_statistics['total_processed'], 1)) * 100:.1f}%

🔮 高级系统分析:
- 总洞察数量: {self.import_statistics['total_insights']}
- 平均置信度: {self.import_statistics['average_confidence']:.2f}
- 高级系统状态: {'✅ 可用' if self.advanced_system else '❌ 不可用'}

📋 导入的记忆:
{chr(10).join([f"  {i+1}. {memory['context']['file_path']} (重要性: {memory['importance']:.2f})" for i, memory in enumerate(processed_memories)])}

🌟 高级系统增强效果:
- 每个记忆都经过了态射系统的智能处理
- 每个记忆都经过了三阶自反性分析
- 每个记忆都包含了深度洞察和改进建议
- 每个记忆都有明确的下一步行动指导

💡 这些不仅仅是文件内容，而是经过高级系统深度分析的智能记忆！"""
        
        return report

def main():
    """主函数 - 执行高级记忆导入"""
    print("🧠 高级记忆导入器启动")
    
    # 创建导入器
    importer = AdvancedMemoryImporter()
    
    # 导入优先级记忆
    processed_memories = importer.import_priority_memories(max_files=5)  # 先导入5个最重要的
    
    # 生成报告
    report = importer.generate_import_report(processed_memories)
    print(f"\n{report}")
    
    # 显示第一个记忆的详细信息作为示例
    if processed_memories:
        print(f"\n📝 第一个记忆的详细信息:")
        first_memory = processed_memories[0]
        print(f"文件: {first_memory['context']['file_path']}")
        print(f"重要性: {first_memory['importance']:.2f}")
        print(f"标签: {', '.join(first_memory['tags'])}")
        if 'advanced_analysis' in first_memory['context']:
            analysis = first_memory['context']['advanced_analysis']
            print(f"洞察数量: {len(analysis.get('insights', []))}")
            print(f"改进建议数量: {len(analysis.get('improvements', []))}")
            print(f"置信度: {analysis.get('confidence', 0.0):.2f}")
    
    print(f"\n🎉 高级记忆导入完成！")
    print(f"💡 现在可以使用这些经过高级系统分析的智能记忆了！")

if __name__ == "__main__":
    main()
