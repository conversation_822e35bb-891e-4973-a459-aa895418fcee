//! 简化的存储模块
//!
//! 实现分形思维引擎基础单元原型验证的存储功能。

use std::collections::HashMap;
use std::sync::{Arc, RwLock};

/// 数据ID类型
pub type DataId = String;

/// 数据类型枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum DataType {
    /// 文本数据
    Text,
    /// 量子编码数据
    Quantum,
    /// 全息编码数据
    Holographic,
    /// 分形编码数据
    Fractal,
    /// 复合编码数据
    Composite,
}

/// 错误类型
#[derive(Debug)]
pub enum Error {
    /// 未找到
    NotFound(String),
    /// 已存在
    AlreadyExists(String),
    /// 无效参数
    InvalidArgument(String),
    /// 编码错误
    Encoding(String),
    /// 解码错误
    Decoding(String),
}

impl std::fmt::Display for Error {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Error::NotFound(msg) => write!(f, "未找到: {}", msg),
            Error::AlreadyExists(msg) => write!(f, "已存在: {}", msg),
            Error::InvalidArgument(msg) => write!(f, "无效参数: {}", msg),
            Error::Encoding(msg) => write!(f, "编码错误: {}", msg),
            Error::Decoding(msg) => write!(f, "解码错误: {}", msg),
        }
    }
}

impl std::error::Error for Error {}

/// 结果类型
pub type Result<T> = std::result::Result<T, Error>;

/// 简化的存储单元
pub struct SimplifiedStorage {
    /// 数据存储
    data_store: RwLock<HashMap<DataId, Vec<u8>>>,
    /// 数据类型
    data_types: RwLock<HashMap<DataId, DataType>>,
}

impl SimplifiedStorage {
    /// 创建新的简化存储单元
    pub fn new() -> Self {
        Self {
            data_store: RwLock::new(HashMap::new()),
            data_types: RwLock::new(HashMap::new()),
        }
    }
    
    /// 存储数据
    pub fn store(&self, id: DataId, data: Vec<u8>, data_type: DataType) -> Result<()> {
        let mut data_store = self.data_store.write().unwrap();
        let mut data_types = self.data_types.write().unwrap();
        
        data_store.insert(id.clone(), data);
        data_types.insert(id, data_type);
        
        Ok(())
    }
    
    /// 加载数据
    pub fn load(&self, id: &DataId) -> Result<(Vec<u8>, DataType)> {
        let data_store = self.data_store.read().unwrap();
        let data_types = self.data_types.read().unwrap();
        
        let data = data_store.get(id)
            .ok_or_else(|| Error::NotFound(format!("数据不存在: {}", id)))?
            .clone();
        
        let data_type = data_types.get(id)
            .ok_or_else(|| Error::NotFound(format!("数据类型不存在: {}", id)))?
            .clone();
        
        Ok((data, data_type))
    }
    
    /// 删除数据
    pub fn delete(&self, id: &DataId) -> Result<()> {
        let mut data_store = self.data_store.write().unwrap();
        let mut data_types = self.data_types.write().unwrap();
        
        data_store.remove(id);
        data_types.remove(id);
        
        Ok(())
    }
    
    /// 获取所有数据ID
    pub fn get_all_ids(&self) -> Vec<DataId> {
        let data_store = self.data_store.read().unwrap();
        data_store.keys().cloned().collect()
    }
}

/// 生成随机ID
pub fn rand_id() -> String {
    use std::time::{SystemTime, UNIX_EPOCH};
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_nanos();
    format!("{:x}", now)
}
