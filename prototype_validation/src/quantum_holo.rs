//! 简化的量子全息层模块
//!
//! 实现分形思维引擎基础单元原型验证的量子全息层功能。

use std::sync::Arc;
use crate::storage::{SimplifiedStorage, DataId, DataType, Result, Error, rand_id};
use crate::computation::{SimplifiedComputation, OperatorType};

/// 简化的量子全息层
pub struct SimplifiedQuantumHolo {
    /// 存储单元
    pub storage: Arc<SimplifiedStorage>,
    /// 计算单元
    pub computation: Arc<SimplifiedComputation>,
}

impl SimplifiedQuantumHolo {
    /// 创建新的简化量子全息层
    pub fn new(storage: Arc<SimplifiedStorage>, computation: Arc<SimplifiedComputation>) -> Self {
        Self {
            storage,
            computation,
        }
    }
    
    /// 量子编码
    pub fn quantum_encode(&self, data: &[u8]) -> Result<DataId> {
        // 使用量子编码算子
        let encoded_data = self.computation.execute_operator(
            OperatorType::QuantumEncoder,
            data,
        )?;
        
        // 存储编码后的数据
        let id = format!("quantum_{}", rand_id());
        self.storage.store(id.clone(), encoded_data, DataType::Quantum)?;
        
        Ok(id)
    }
    
    /// 量子解码
    pub fn quantum_decode(&self, id: &DataId) -> Result<Vec<u8>> {
        // 加载编码数据
        let (encoded_data, data_type) = self.storage.load(id)?;
        
        // 检查数据类型
        if data_type != DataType::Quantum {
            return Err(Error::InvalidArgument(format!(
                "数据类型不是量子类型: {:?}",
                data_type
            )));
        }
        
        // 使用量子解码算子
        let decoded_data = self.computation.execute_operator(
            OperatorType::QuantumDecoder,
            &encoded_data,
        )?;
        
        Ok(decoded_data)
    }
    
    /// 全息编码
    pub fn holographic_encode(&self, data: &[u8]) -> Result<DataId> {
        // 使用全息编码算子
        let encoded_data = self.computation.execute_operator(
            OperatorType::HolographicEncoder,
            data,
        )?;
        
        // 存储编码后的数据
        let id = format!("holographic_{}", rand_id());
        self.storage.store(id.clone(), encoded_data, DataType::Holographic)?;
        
        Ok(id)
    }
    
    /// 全息解码
    pub fn holographic_decode(&self, id: &DataId) -> Result<Vec<u8>> {
        // 加载编码数据
        let (encoded_data, data_type) = self.storage.load(id)?;
        
        // 检查数据类型
        if data_type != DataType::Holographic {
            return Err(Error::InvalidArgument(format!(
                "数据类型不是全息类型: {:?}",
                data_type
            )));
        }
        
        // 使用全息解码算子
        let decoded_data = self.computation.execute_operator(
            OperatorType::HolographicDecoder,
            &encoded_data,
        )?;
        
        Ok(decoded_data)
    }
    
    /// 分形编码
    pub fn fractal_encode(&self, data: &[u8]) -> Result<DataId> {
        // 使用分形编码算子
        let encoded_data = self.computation.execute_operator(
            OperatorType::FractalEncoder,
            data,
        )?;
        
        // 存储编码后的数据
        let id = format!("fractal_{}", rand_id());
        self.storage.store(id.clone(), encoded_data, DataType::Fractal)?;
        
        Ok(id)
    }
    
    /// 分形解码
    pub fn fractal_decode(&self, id: &DataId) -> Result<Vec<u8>> {
        // 加载编码数据
        let (encoded_data, data_type) = self.storage.load(id)?;
        
        // 检查数据类型
        if data_type != DataType::Fractal {
            return Err(Error::InvalidArgument(format!(
                "数据类型不是分形类型: {:?}",
                data_type
            )));
        }
        
        // 使用分形解码算子
        let decoded_data = self.computation.execute_operator(
            OperatorType::FractalDecoder,
            &encoded_data,
        )?;
        
        Ok(decoded_data)
    }
    
    /// 复合编码
    pub fn composite_encode(&self, data: &[u8]) -> Result<DataId> {
        // 使用复合编码算子
        let encoded_data = self.computation.execute_operator(
            OperatorType::CompositeEncoder,
            data,
        )?;
        
        // 存储编码后的数据
        let id = format!("composite_{}", rand_id());
        self.storage.store(id.clone(), encoded_data, DataType::Composite)?;
        
        Ok(id)
    }
    
    /// 复合解码
    pub fn composite_decode(&self, id: &DataId) -> Result<Vec<u8>> {
        // 加载编码数据
        let (encoded_data, data_type) = self.storage.load(id)?;
        
        // 检查数据类型
        if data_type != DataType::Composite {
            return Err(Error::InvalidArgument(format!(
                "数据类型不是复合类型: {:?}",
                data_type
            )));
        }
        
        // 使用复合解码算子
        let decoded_data = self.computation.execute_operator(
            OperatorType::CompositeDecoder,
            &encoded_data,
        )?;
        
        Ok(decoded_data)
    }
}
