//! 分形思维引擎原型验证主程序
//!
//! 验证分形思维引擎基础单元的核心概念。

mod storage;
mod computation;
mod quantum_holo;
mod fractal_interface;
mod validation;

use std::sync::Arc;
use std::error::Error;

use storage::SimplifiedStorage;
use computation::SimplifiedComputation;
use quantum_holo::SimplifiedQuantumHolo;
use fractal_interface::SimplifiedFractalInterface;
use validation::{
    run_storage_computation_test,
    run_quantum_holo_test,
    run_fractal_interface_test,
    run_integration_test,
};

fn main() -> Result<(), Box<dyn Error>> {
    println!("分形思维引擎基础单元原型验证");
    println!("======================================");
    
    // 创建简化的存储单元
    let storage = Arc::new(SimplifiedStorage::new());
    
    // 创建简化的计算单元
    let computation = Arc::new(SimplifiedComputation::new());
    
    // 创建简化的量子全息层
    let quantum_holo = Arc::new(SimplifiedQuantumHolo::new(
        Arc::clone(&storage),
        Arc::clone(&computation),
    ));
    
    // 创建简化的分形接口层
    let fractal_interface = Arc::new(SimplifiedFractalInterface::new(
        Arc::clone(&quantum_holo),
        Arc::clone(&computation),
    ));
    
    // 运行存储和计算测试
    run_storage_computation_test(&storage, &computation)?;
    
    // 运行量子全息测试
    run_quantum_holo_test(&quantum_holo)?;
    
    // 运行分形接口测试
    run_fractal_interface_test(&quantum_holo, &fractal_interface)?;
    
    // 运行集成测试
    run_integration_test(&quantum_holo, &fractal_interface)?;
    
    println!("\n所有原型验证测试通过！");
    
    Ok(())
}
