2025-05-07 09:03:47,031 - __main__ - INFO - 测试分形模式生成器
2025-05-07 09:03:47,031 - __main__ - INFO - 为URL生成分形模式: https://example.com/path/to/page?query=value
2025-05-07 09:03:47,037 - __main__ - INFO - URL分形模式: {
  "type": "url",
  "domain": "example.com",
  "path_depth": 3,
  "query_param_count": 1,
  "url_entropy": 0.5,
  "url_length": 44,
  "url_hash": "ff9f7a9eb7db11f8f36c8f2593ce6c33",
  "fractal_dimension": 0.654,
  "signature": "ff9f7a9eb7db11f8:1df49b82534a9700"
}
2025-05-07 09:03:47,037 - __main__ - INFO - 为内容生成分形模式
2025-05-07 09:03:47,160 - __main__ - INFO - 内容分形模式: {
  "type": "content",
  "content_length": 190,
  "word_count": 26,
  "sentence_count": 4,
  "paragraph_count": 1,
  "content_entropy": 0.11578947368421053,
  "word_freq_entropy": 4.161978179679553,
  "fractal_dimension": 0.5900086200636143,
  "signature": "a11b8750776240cb:04d697bcd86a07af"
}
2025-05-07 09:03:47,160 - __main__ - INFO - 分形模式生成器测试通过
2025-05-07 09:03:47,160 - __main__ - INFO - 测试全息索引器
2025-05-07 09:03:47,160 - __main__ - INFO - 创建索引
2025-05-07 09:03:47,161 - __main__ - INFO - 索引: H:b0faac834eb507d2:test:test_key
2025-05-07 09:03:47,161 - __main__ - INFO - 全息索引器测试通过
2025-05-07 09:03:47,161 - __main__ - INFO - 测试量子编码器
2025-05-07 09:03:47,161 - __main__ - INFO - 编码URL: https://example.com/path/to/page?query=value
2025-05-07 09:03:47,161 - __main__ - INFO - 编码结果: Q:url:5ababd60:c02ca8e1:1df49b82534a9700
2025-05-07 09:03:47,161 - __main__ - INFO - 编码内容
2025-05-07 09:03:47,161 - __main__ - INFO - 编码结果: Q:content:25:a8a2e25e:4098887f916a7e09
2025-05-07 09:03:47,161 - __main__ - INFO - 量子编码器测试通过
2025-05-07 09:03:47,161 - __main__ - INFO - 所有测试通过
