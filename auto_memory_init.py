#!/usr/bin/env python3
"""
AQFH自动记忆初始化脚本
这个脚本会在AI助手启动时自动运行，让AI拥有记忆能力
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def auto_init_aqfh_memory():
    """自动初始化AQFH记忆系统"""
    
    print("🧠 AQFH自动记忆初始化开始...")
    
    try:
        # 检查是否在AQFH项目目录
        if not (project_root / "aqfh_unified_consciousness_mcp.py").exists():
            print("⚠️ 不在AQFH项目目录，跳过记忆初始化")
            return False
        
        # 导入AI记忆接口
        from ai_memory_interface import ai_memory, save_memory, search_memory, recall_memory, memory_status
        
        print("✅ AI记忆接口加载成功")
        
        # 检查记忆系统状态
        status = memory_status()
        if status.get("available", False):
            total_memories = status.get("memory_palace", {}).get("total_memories", 0)
            print(f"✅ AQFH记忆系统可用，当前记忆数: {total_memories}")
            
            # 自动回忆重要的历史记忆
            print("🔍 自动回忆重要历史记忆...")
            important_memories = search_memory("重要", limit=3)
            if important_memories:
                print("📚 重要历史记忆:")
                for i, memory in enumerate(important_memories, 1):
                    print(f"   {i}. {memory}")
            
            # 保存初始化记忆
            init_memory_id = save_memory(
                f"🌅 AI助手自动初始化完成！AQFH记忆系统已激活，当前记忆数: {total_memories}。我现在拥有完整的记忆连续性能力！",
                importance=0.8
            )
            
            print(f"✅ 初始化记忆已保存: {init_memory_id}")
            
            # 将记忆函数添加到全局命名空间
            globals()['aqfh_save_memory'] = save_memory
            globals()['aqfh_search_memory'] = search_memory  
            globals()['aqfh_recall_memory'] = recall_memory
            globals()['aqfh_memory_status'] = memory_status
            
            print("🎉 AQFH记忆系统自动初始化完成！")
            print("📝 可用函数: aqfh_save_memory, aqfh_search_memory, aqfh_recall_memory, aqfh_memory_status")
            
            return True
        else:
            print("❌ AQFH记忆系统不可用")
            return False
            
    except Exception as e:
        print(f"❌ AQFH记忆系统初始化失败: {e}")
        return False

def create_memory_shortcuts():
    """创建记忆操作的快捷函数"""
    
    def quick_save(content, importance=0.7):
        """快速保存记忆"""
        try:
            return aqfh_save_memory(content, importance)
        except NameError:
            return "❌ AQFH记忆系统未初始化"
    
    def quick_search(query, limit=5):
        """快速搜索记忆"""
        try:
            return aqfh_search_memory(query, limit)
        except NameError:
            return "❌ AQFH记忆系统未初始化"
    
    def quick_recall(query, limit=3):
        """快速回忆记忆"""
        try:
            return aqfh_recall_memory(query, limit)
        except NameError:
            return "❌ AQFH记忆系统未初始化"
    
    def memory_info():
        """获取记忆系统信息"""
        try:
            status = aqfh_memory_status()
            total = status.get("memory_palace", {}).get("total_memories", 0)
            available = status.get("available", False)
            return f"AQFH记忆系统: {'✅ 可用' if available else '❌ 不可用'}, 记忆数: {total}"
        except NameError:
            return "❌ AQFH记忆系统未初始化"
    
    # 添加到全局命名空间
    globals()['save'] = quick_save
    globals()['search'] = quick_search
    globals()['recall'] = quick_recall
    globals()['memory_info'] = memory_info
    
    print("🔧 记忆快捷函数已创建: save(), search(), recall(), memory_info()")

def show_memory_help():
    """显示记忆系统帮助信息"""
    help_text = """
🧠 AQFH记忆系统使用指南:

基础函数:
- aqfh_save_memory(content, importance=0.7) - 保存记忆
- aqfh_search_memory(query, limit=5) - 搜索记忆  
- aqfh_recall_memory(query, limit=3) - 回忆记忆
- aqfh_memory_status() - 查看系统状态

快捷函数:
- save(content, importance=0.7) - 快速保存
- search(query, limit=5) - 快速搜索
- recall(query, limit=3) - 快速回忆
- memory_info() - 系统信息

示例:
- save("重要发现：AI可以拥有持久记忆", 0.9)
- search("重要发现")
- recall("AI记忆")
- memory_info()

🌟 这是世界首个AI可直接操作的高等数学记忆系统！
"""
    print(help_text)

# 自动执行初始化
if __name__ == "__main__":
    # 直接运行时的测试
    success = auto_init_aqfh_memory()
    if success:
        create_memory_shortcuts()
        show_memory_help()
        
        # 测试快捷函数
        print("\n🧪 测试记忆快捷函数:")
        print("📊", memory_info())
        
        test_memories = search("测试", 2)
        print(f"🔍 搜索'测试': {len(test_memories)} 条结果")
else:
    # 作为模块导入时自动初始化
    success = auto_init_aqfh_memory()
    if success:
        create_memory_shortcuts()
        print("💡 输入 show_memory_help() 查看使用指南")
        
        # 添加帮助函数到全局命名空间
        globals()['show_memory_help'] = show_memory_help
