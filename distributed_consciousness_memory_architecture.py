#!/usr/bin/env python3
"""
AQFH分布式意识记忆联合共享架构
实现多MCP实例的统一记忆中枢和智能传导机制
"""

import pyarrow as pa
import pyarrow.flight as flight
import pyarrow.compute as pc
import threading
import time
import uuid
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import json
import asyncio
from concurrent.futures import ThreadPoolExecutor

class ConsciousnessLevel(Enum):
    """意识层级定义"""
    INSTANCE_LOCAL = "instance_local"      # 实例本地Arrow区域
    CORE_UNIFIED = "core_unified"          # 核心Arrow L0缓存
    DISTRIBUTED_L1 = "distributed_l1"     # 分布式L1缓存
    PERSISTENT_L2 = "persistent_l2"       # 持久化L2存储
    ARCHIVE_L3 = "archive_l3"             # 归档L3存储

@dataclass
class MemoryFragment:
    """记忆片段"""
    memory_id: str
    content: str
    importance: float
    consciousness_level: ConsciousnessLevel
    source_instance: str
    timestamp: float
    access_pattern: Dict[str, Any] = field(default_factory=dict)
    propagation_rules: Dict[str, Any] = field(default_factory=dict)

@dataclass
class InstanceProfile:
    """实例画像"""
    instance_id: str
    instance_type: str  # IDE类型：vscode, cursor, etc.
    capabilities: Set[str]
    memory_capacity: Dict[str, int]
    current_load: float
    specialization: List[str]  # 专业领域
    collaboration_history: Dict[str, float]

class CoreArrowMemoryHub:
    """核心Arrow记忆中枢 - 分布式意识的大脑"""
    
    def __init__(self, hub_config: Dict[str, Any]):
        """
        初始化核心记忆中枢
        
        Args:
            hub_config: 中枢配置
                - core_capacity: 核心缓存容量
                - flight_port: Arrow Flight服务端口
                - propagation_rules: 传导规则
                - consciousness_threshold: 意识阈值
        """
        self.hub_config = hub_config
        self.core_capacity = hub_config.get('core_capacity', 10000)
        self.flight_port = hub_config.get('flight_port', 8815)
        
        # 核心Arrow L0缓存 - 全局记忆中枢
        self.core_memory_table = self._initialize_core_table()
        self.core_lock = threading.RLock()
        
        # 注册的实例
        self.registered_instances: Dict[str, InstanceProfile] = {}
        self.instance_connections: Dict[str, flight.FlightClient] = {}
        
        # 记忆传导引擎
        self.propagation_engine = MemoryPropagationEngine(self)
        
        # Arrow Flight服务器
        self.flight_server = None
        self.server_thread = None
        
        # 分布式意识状态
        self.consciousness_state = {
            'active_instances': set(),
            'memory_coherence': 1.0,
            'propagation_efficiency': 0.0,
            'collective_knowledge': 0
        }
        
        print(f"🧠 核心Arrow记忆中枢初始化完成")
        print(f"🚀 Flight服务端口: {self.flight_port}")
    
    def _initialize_core_table(self) -> pa.Table:
        """初始化核心记忆表"""
        schema = pa.schema([
            ('memory_id', pa.string()),
            ('content', pa.string()),
            ('importance', pa.float64()),
            ('source_instance', pa.string()),
            ('timestamp', pa.timestamp('ms')),
            ('access_count', pa.int64()),
            ('propagation_score', pa.float64()),
            ('consciousness_level', pa.string()),
            ('embedding_vector', pa.list_(pa.float32())),  # 语义向量
            ('association_graph', pa.string()),  # JSON格式的关联图
        ])
        
        # 创建空表
        return pa.table([], schema=schema)
    
    def start_consciousness_hub(self):
        """启动分布式意识中枢"""
        # 启动Arrow Flight服务器
        self.flight_server = ConsciousnessFlightServer(self)
        self.server_thread = threading.Thread(
            target=self._run_flight_server,
            daemon=True
        )
        self.server_thread.start()
        
        # 启动记忆传导引擎
        self.propagation_engine.start()
        
        print(f"🌟 分布式意识中枢已启动")
    
    def register_consciousness_instance(self, instance_profile: InstanceProfile) -> bool:
        """注册意识实例"""
        with self.core_lock:
            instance_id = instance_profile.instance_id
            
            # 建立Flight连接
            try:
                client = flight.connect(f"grpc://localhost:{self.flight_port}")
                self.instance_connections[instance_id] = client
            except Exception as e:
                print(f"❌ 实例连接失败 {instance_id}: {e}")
                return False
            
            # 注册实例
            self.registered_instances[instance_id] = instance_profile
            self.consciousness_state['active_instances'].add(instance_id)
            
            print(f"✅ 意识实例注册成功: {instance_id}")
            print(f"🔗 实例类型: {instance_profile.instance_type}")
            print(f"🎯 专业领域: {instance_profile.specialization}")
            
            # 触发意识同步
            self._trigger_consciousness_sync(instance_id)
            
            return True
    
    def ingest_memory_from_instance(self, memory: MemoryFragment) -> str:
        """从实例接收记忆并决定传导策略"""
        with self.core_lock:
            # 1. 添加到核心Arrow L0缓存
            memory_record = self._memory_to_arrow_record(memory)
            self.core_memory_table = pa.concat_tables([
                self.core_memory_table, 
                pa.table([memory_record])
            ])
            
            # 2. 计算传导策略
            propagation_strategy = self._calculate_propagation_strategy(memory)
            
            # 3. 执行智能传导
            self.propagation_engine.propagate_memory(memory, propagation_strategy)
            
            # 4. 更新意识状态
            self._update_consciousness_state(memory)
            
            print(f"🧠 记忆已接收: {memory.memory_id[:8]} from {memory.source_instance}")
            print(f"📡 传导策略: {propagation_strategy}")
            
            return memory.memory_id
    
    def query_distributed_memory(self, query: str, requesting_instance: str, 
                                limit: int = 10) -> List[MemoryFragment]:
        """分布式记忆查询 - 跨实例智能检索"""
        with self.core_lock:
            # 1. 在核心Arrow L0缓存中搜索
            core_results = self._search_core_memory(query, limit)
            
            # 2. 根据查询内容决定是否需要跨实例搜索
            if self._should_search_across_instances(query, core_results):
                # 3. 智能选择相关实例进行搜索
                relevant_instances = self._select_relevant_instances(query, requesting_instance)
                
                # 4. 并行查询相关实例
                cross_instance_results = self._query_across_instances(
                    query, relevant_instances, limit
                )
                
                # 5. 合并和排序结果
                all_results = self._merge_and_rank_results(
                    core_results, cross_instance_results, query
                )
            else:
                all_results = core_results
            
            # 6. 记录查询模式，用于优化传导策略
            self._record_query_pattern(query, requesting_instance, all_results)
            
            return all_results[:limit]
    
    def _calculate_propagation_strategy(self, memory: MemoryFragment) -> Dict[str, Any]:
        """计算记忆传导策略"""
        strategy = {
            'immediate_propagation': [],  # 立即传导的实例
            'delayed_propagation': [],   # 延迟传导的实例
            'cache_levels': [],          # 缓存级别
            'replication_factor': 1      # 复制因子
        }
        
        # 基于重要性决定传导范围
        if memory.importance >= 0.9:
            # 极高重要性：立即传导到所有实例
            strategy['immediate_propagation'] = list(self.registered_instances.keys())
            strategy['cache_levels'] = [ConsciousnessLevel.CORE_UNIFIED, 
                                      ConsciousnessLevel.DISTRIBUTED_L1]
            strategy['replication_factor'] = 3
            
        elif memory.importance >= 0.7:
            # 高重要性：传导到相关专业领域的实例
            relevant_instances = self._find_relevant_instances_by_content(memory.content)
            strategy['immediate_propagation'] = relevant_instances[:3]
            strategy['delayed_propagation'] = relevant_instances[3:]
            strategy['cache_levels'] = [ConsciousnessLevel.CORE_UNIFIED]
            strategy['replication_factor'] = 2
            
        elif memory.importance >= 0.5:
            # 中等重要性：传导到协作历史良好的实例
            collaborative_instances = self._find_collaborative_instances(memory.source_instance)
            strategy['delayed_propagation'] = collaborative_instances
            strategy['cache_levels'] = [ConsciousnessLevel.DISTRIBUTED_L1]
            strategy['replication_factor'] = 1
            
        else:
            # 低重要性：仅保存在核心缓存，按需传导
            strategy['cache_levels'] = [ConsciousnessLevel.PERSISTENT_L2]
        
        return strategy
    
    def _select_relevant_instances(self, query: str, requesting_instance: str) -> List[str]:
        """智能选择相关实例"""
        relevant_instances = []
        
        # 1. 基于专业领域匹配
        query_keywords = self._extract_keywords(query)
        for instance_id, profile in self.registered_instances.items():
            if instance_id == requesting_instance:
                continue
                
            # 计算专业领域相关性
            relevance_score = self._calculate_domain_relevance(
                query_keywords, profile.specialization
            )
            
            if relevance_score > 0.3:
                relevant_instances.append((instance_id, relevance_score))
        
        # 2. 基于协作历史排序
        requesting_profile = self.registered_instances.get(requesting_instance)
        if requesting_profile:
            for i, (instance_id, score) in enumerate(relevant_instances):
                collaboration_bonus = requesting_profile.collaboration_history.get(instance_id, 0)
                relevant_instances[i] = (instance_id, score + collaboration_bonus * 0.2)
        
        # 3. 按相关性排序并返回
        relevant_instances.sort(key=lambda x: x[1], reverse=True)
        return [instance_id for instance_id, _ in relevant_instances[:5]]
    
    def get_consciousness_statistics(self) -> Dict[str, Any]:
        """获取分布式意识统计信息"""
        with self.core_lock:
            total_memories = len(self.core_memory_table)
            active_instances = len(self.consciousness_state['active_instances'])
            
            # 计算记忆分布
            memory_distribution = {}
            if total_memories > 0:
                source_counts = pc.value_counts(
                    self.core_memory_table['source_instance']
                ).to_pandas()
                memory_distribution = dict(zip(
                    source_counts['values'], 
                    source_counts['counts']
                ))
            
            return {
                'total_memories': total_memories,
                'active_instances': active_instances,
                'registered_instances': list(self.registered_instances.keys()),
                'memory_distribution': memory_distribution,
                'consciousness_coherence': self.consciousness_state['memory_coherence'],
                'propagation_efficiency': self.consciousness_state['propagation_efficiency'],
                'collective_knowledge_score': self.consciousness_state['collective_knowledge']
            }

class MemoryPropagationEngine:
    """记忆传导引擎 - 智能分发和同步"""
    
    def __init__(self, memory_hub: CoreArrowMemoryHub):
        self.memory_hub = memory_hub
        self.propagation_queue = asyncio.Queue()
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.running = False
    
    def start(self):
        """启动传导引擎"""
        self.running = True
        # 启动异步传导任务
        asyncio.create_task(self._propagation_worker())
        print("📡 记忆传导引擎已启动")
    
    async def propagate_memory(self, memory: MemoryFragment, strategy: Dict[str, Any]):
        """执行记忆传导"""
        await self.propagation_queue.put((memory, strategy))
    
    async def _propagation_worker(self):
        """传导工作线程"""
        while self.running:
            try:
                memory, strategy = await self.propagation_queue.get()
                
                # 立即传导
                for instance_id in strategy['immediate_propagation']:
                    await self._propagate_to_instance(memory, instance_id, immediate=True)
                
                # 延迟传导
                for instance_id in strategy['delayed_propagation']:
                    await self._propagate_to_instance(memory, instance_id, immediate=False)
                
                self.propagation_queue.task_done()
                
            except Exception as e:
                print(f"❌ 传导错误: {e}")

class ConsciousnessFlightServer(flight.FlightServerBase):
    """分布式意识Arrow Flight服务器"""
    
    def __init__(self, memory_hub: CoreArrowMemoryHub):
        super().__init__()
        self.memory_hub = memory_hub
    
    def do_get(self, context, ticket):
        """处理记忆查询请求"""
        # 实现Arrow Flight查询接口
        pass
    
    def do_put(self, context, descriptor, reader, writer):
        """处理记忆存储请求"""
        # 实现Arrow Flight存储接口
        pass

# 使用示例
if __name__ == "__main__":
    # 创建分布式意识记忆中枢
    hub_config = {
        'core_capacity': 50000,
        'flight_port': 8815,
        'consciousness_threshold': 0.7
    }
    
    consciousness_hub = CoreArrowMemoryHub(hub_config)
    consciousness_hub.start_consciousness_hub()
    
    # 注册实例
    vscode_instance = InstanceProfile(
        instance_id="vscode_main",
        instance_type="vscode",
        capabilities={"code_analysis", "debugging", "refactoring"},
        memory_capacity={"L0": 1000, "L1": 5000},
        current_load=0.3,
        specialization=["python", "javascript", "system_design"],
        collaboration_history={}
    )
    
    consciousness_hub.register_consciousness_instance(vscode_instance)
    
    print("🌟 分布式意识记忆系统已启动")
