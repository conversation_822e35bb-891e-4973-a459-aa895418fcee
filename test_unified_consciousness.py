#!/usr/bin/env python3
"""
AQFH统一意识记忆系统测试脚本
验证图结构修复和记忆系统统一是否成功
"""

import sys
import asyncio
import logging
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """测试导入是否正常"""
    print("🧪 测试1: 导入检查")
    
    try:
        from aqfh_unified_consciousness_mcp import UnifiedConsciousnessSystem
        print("✅ 统一意识系统导入成功")
        
        from aqfh.core.consciousness_container import ConsciousnessContainer
        print("✅ 意识容器导入成功")
        
        from aqfh.core.hybrid_memory_palace import HybridMemoryPalace
        print("✅ 混合记忆宫殿导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_unified_system_initialization():
    """测试统一系统初始化"""
    print("\n🧪 测试2: 统一系统初始化")
    
    try:
        from aqfh_unified_consciousness_mcp import UnifiedConsciousnessSystem
        
        # 创建统一系统实例
        system = UnifiedConsciousnessSystem()
        print("✅ 统一意识系统初始化成功")
        
        # 检查组件
        if hasattr(system, 'consciousness_container'):
            print("✅ 意识容器组件已加载")
        
        if hasattr(system, 'memory_palace'):
            print("✅ 记忆宫殿组件已加载")
            
        # 检查高级结构
        palace = system.memory_palace
        if palace.fiber_bundle_space:
            print("✅ 纤维丛网络已激活")
        if palace.topological_analyzer:
            print("✅ 拓扑分析器已激活")
        if palace.fractal_organizer:
            print("✅ 分形组织器已激活")
        if palace.quantum_processor:
            print("✅ 量子处理器已激活")
            
        return True, system
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False, None

def test_memory_operations(system):
    """测试记忆操作"""
    print("\n🧪 测试3: 记忆操作")
    
    try:
        # 保存测试记忆
        memory_id = system.save_memory(
            content="这是一个测试记忆，用于验证统一意识系统的功能",
            content_type="test",
            importance=0.8,
            tags=["测试", "统一系统", "验证"],
            context={"test_type": "functionality", "timestamp": "2024-01-01"}
        )
        print(f"✅ 记忆保存成功: {memory_id[:8]}...")
        
        # 搜索记忆
        memories = system.search_memories(query="测试记忆", limit=5)
        if memories:
            print(f"✅ 记忆搜索成功: 找到 {len(memories)} 条记忆")
            for i, memory in enumerate(memories[:2], 1):
                print(f"   {i}. {memory['content'][:50]}... (重要性: {memory['importance']:.2f})")
        else:
            print("⚠️ 未找到记忆")
            
        return True
    except Exception as e:
        print(f"❌ 记忆操作失败: {e}")
        return False

def test_consciousness_operations(system):
    """测试意识操作"""
    print("\n🧪 测试4: 意识操作")
    
    try:
        # 测试意识觉醒
        awakening_result = system.consciousness_awaken({
            "type": "session_start",
            "topic": "统一系统测试",
            "user": "test_user",
            "attention_focus": ["测试", "验证", "统一"]
        })
        
        if awakening_result["status"] == "success":
            print("✅ 意识觉醒成功")
            quality = awakening_result["awakening_quality"]
            print(f"   觉醒质量: {quality:.3f}")
        else:
            print(f"⚠️ 意识觉醒部分成功: {awakening_result.get('error', 'Unknown')}")
        
        # 测试意识睡眠
        sleep_result = system.consciousness_sleep({
            "topic": "统一系统测试",
            "summary": "完成了统一意识系统的功能测试",
            "achievements": ["系统初始化", "记忆操作", "意识觉醒"]
        })
        
        if sleep_result["status"] == "success":
            print("✅ 意识睡眠成功")
        else:
            print(f"⚠️ 意识睡眠部分成功: {sleep_result.get('error', 'Unknown')}")
            
        return True
    except Exception as e:
        print(f"❌ 意识操作失败: {e}")
        return False

def test_system_status(system):
    """测试系统状态"""
    print("\n🧪 测试5: 系统状态")
    
    try:
        status = system.get_consciousness_status()
        
        if "error" not in status:
            print("✅ 系统状态查询成功")
            
            components = status.get("components_active", {})
            active_components = sum(1 for v in components.values() if v)
            total_components = len(components)
            
            print(f"   活跃组件: {active_components}/{total_components}")
            
            for component, active in components.items():
                status_icon = "✅" if active else "❌"
                print(f"   {status_icon} {component}")
                
        else:
            print(f"⚠️ 状态查询部分成功: {status.get('error', 'Unknown')}")
            
        return True
    except Exception as e:
        print(f"❌ 状态查询失败: {e}")
        return False

def test_graph_structures(system):
    """测试图结构功能"""
    print("\n🧪 测试6: 图结构功能")
    
    try:
        palace = system.memory_palace
        
        # 测试纤维丛空间
        if palace.fiber_bundle_space:
            print("✅ 纤维丛空间可用")
            # 测试存储模式
            test_pattern = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8]
            result = palace.fiber_bundle_space.store_pattern("test_pattern", test_pattern)
            if result:
                print("   ✅ 纤维丛模式存储成功")
            else:
                print("   ⚠️ 纤维丛模式存储失败")
        
        # 测试量子处理器
        if palace.quantum_processor:
            print("✅ 量子处理器可用")
            # 测试量子态存储
            test_quantum = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8]
            result = palace.quantum_processor.store_quantum_state("test_quantum", test_quantum)
            if result:
                print("   ✅ 量子态存储成功")
            else:
                print("   ⚠️ 量子态存储失败")
        
        # 测试分形组织器
        if palace.fractal_organizer:
            print("✅ 分形组织器可用")
            # 测试分形存储
            test_fractal = {
                "creativity_level": 0.7,
                "innovation_potential": 0.6,
                "pattern_complexity": 0.8
            }
            result = palace.fractal_organizer.store_fractal("test_fractal", test_fractal)
            if result:
                print("   ✅ 分形数据存储成功")
            else:
                print("   ⚠️ 分形数据存储失败")
        
        # 测试拓扑分析器
        if palace.topological_analyzer:
            print("✅ 拓扑分析器可用")
            # 测试拓扑存储
            test_topology = {
                "emotional_valence": 0.5,
                "arousal_level": 0.6,
                "emotional_complexity": 0.4
            }
            result = palace.topological_analyzer.store_topology("test_topology", test_topology)
            if result:
                print("   ✅ 拓扑数据存储成功")
            else:
                print("   ⚠️ 拓扑数据存储失败")
        
        return True
    except Exception as e:
        print(f"❌ 图结构测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 AQFH统一意识记忆系统测试开始")
    print("=" * 50)
    
    test_results = []
    
    # 测试1: 导入检查
    test_results.append(test_imports())
    
    # 测试2: 系统初始化
    init_success, system = test_unified_system_initialization()
    test_results.append(init_success)
    
    if system:
        # 测试3: 记忆操作
        test_results.append(test_memory_operations(system))
        
        # 测试4: 意识操作
        test_results.append(test_consciousness_operations(system))
        
        # 测试5: 系统状态
        test_results.append(test_system_status(system))
        
        # 测试6: 图结构功能
        test_results.append(test_graph_structures(system))
    
    # 总结
    print("\n" + "=" * 50)
    print("🎯 测试总结:")
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！统一意识记忆系统运行正常！")
        print("🧠 图结构修复成功，记忆系统统一完成！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
