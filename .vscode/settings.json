{"aqfh.autoSave": true, "aqfh.autoRecall": true, "aqfh.sessionManagement": "automatic", "aqfh.importanceThreshold": 0.5, "aqfh.mcpServer": {"enabled": true, "serverPath": "aqfh_unified_consciousness_mcp.py", "hybridPalacePath": "aqfh_hybrid_memory_palace_mcp.py", "autoStart": true}, "aqfh.consciousness": {"enableAdvancedMath": true, "fiberBundleSpace": true, "topologicalAnalysis": true, "fractalOrganization": true, "quantumSemantics": true}, "aqfh.memory": {"storagePath": "~/.aqfh/unified_consciousness", "cacheEnabled": true, "compressionLevel": "zstd", "maxMemories": 10000, "importanceThreshold": 0.3}, "aqfh.performance": {"enableParallelProcessing": true, "disableGIL": true, "arrowOptimizations": true, "cacheStrategy": "multi_level"}, "python.defaultInterpreterPath": "./venv/bin/python", "python.terminal.activateEnvironment": true, "files.associations": {"*.aqfh": "json", "*.parquet": "binary"}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "git.ignoreLimitWarning": true}