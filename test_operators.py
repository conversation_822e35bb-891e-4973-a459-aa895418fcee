"""
算子测试脚本

这个脚本测试微分几何算子的基本功能。
"""

import numpy as np
import os
import sys

# 添加src目录到Python路径
sys.path.insert(0, os.path.abspath('src'))

# 导入算子
from operators.differential_geometry.bundle import NoncommutativeBundle
from operators.differential_geometry.transport import ParallelTransport
from operators.differential_geometry.connection import ConnectionCalculator
from operators.differential_geometry.curvature import CurvatureAnalyzer

def test_noncommutative_bundle():
    """测试NoncommutativeBundle算子"""
    print("\n测试NoncommutativeBundle算子...")
    
    # 创建算子
    bundle = NoncommutativeBundle(dimension=3, fiber_dimension=2)
    print(f"创建算子: {bundle}")
    
    # 测试元数据
    metadata = bundle.get_metadata()
    print(f"元数据: {metadata}")
    
    # 测试应用到点集
    points = np.array([[1.0, 0.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 1.0]])
    result = bundle.apply(points)
    print(f"应用到点集，输入形状: {points.shape}, 输出形状: {result.shape}")
    
    # 测试计算曲率
    point = np.array([1.0, 0.0, 0.0])
    curvature = bundle.compute_curvature(point)
    print(f"计算曲率，输出形状: {curvature.shape}")
    
    print("NoncommutativeBundle测试完成")

def test_parallel_transport():
    """测试ParallelTransport算子"""
    print("\n测试ParallelTransport算子...")
    
    # 创建算子
    transport = ParallelTransport(dimension=3, fiber_dimension=2)
    print(f"创建算子: {transport}")
    
    # 测试元数据
    metadata = transport.get_metadata()
    print(f"元数据: {metadata}")
    
    # 创建一个圆形路径
    t = np.linspace(0, 2*np.pi, 100)
    path = np.zeros((100, 3))
    path[:, 0] = np.cos(t)
    path[:, 1] = np.sin(t)
    
    # 初始向量
    vector = np.array([1.0, 0.0])
    
    # 传输向量
    result = transport.apply((path, vector))
    print(f"传输向量，输入路径形状: {path.shape}, 输入向量形状: {vector.shape}, 输出形状: {result.shape}")
    
    # 计算全息变换
    final_vector = transport.compute_holonomy(path, vector)
    print(f"计算全息变换，输出形状: {final_vector.shape}")
    
    print("ParallelTransport测试完成")

def test_connection_calculator():
    """测试ConnectionCalculator"""
    print("\n测试ConnectionCalculator...")
    
    # 创建计算器
    calculator = ConnectionCalculator(dimension=3)
    print(f"创建计算器: {calculator}")
    
    # 获取连接形式
    connection = calculator.get_connection()
    print(f"连接形式形状: {connection.shape}")
    
    # 计算曲率
    curvature = calculator.compute_curvature()
    print(f"曲率形状: {curvature.shape}")
    
    # 计算Ricci曲率
    ricci = calculator.compute_ricci_curvature()
    print(f"Ricci曲率形状: {ricci.shape}")
    
    # 计算标量曲率
    scalar = calculator.compute_scalar_curvature()
    print(f"标量曲率: {scalar}")
    
    print("ConnectionCalculator测试完成")

def test_curvature_analyzer():
    """测试CurvatureAnalyzer"""
    print("\n测试CurvatureAnalyzer...")
    
    # 创建分析器
    analyzer = CurvatureAnalyzer(dimension=3, visualization_enabled=False)
    print(f"创建分析器: {analyzer}")
    
    # 创建一个简单的曲率张量
    curvature = np.zeros((3, 3, 3, 3))
    for i in range(3):
        for j in range(3):
            for k in range(3):
                for l in range(3):
                    if i == j and k == l:
                        curvature[i, j, k, l] = 1.0
    
    # 分析曲率
    result = analyzer.analyze_curvature(curvature)
    print(f"曲率分析结果: {result.keys()}")
    
    # 创建一个简单的Ricci曲率
    ricci = np.eye(3)
    
    # 分析Ricci曲率
    result = analyzer.analyze_ricci_curvature(ricci)
    print(f"Ricci曲率分析结果: {result.keys()}")
    
    print("CurvatureAnalyzer测试完成")

def main():
    """主函数"""
    print("开始测试微分几何算子...")
    
    test_noncommutative_bundle()
    test_parallel_transport()
    test_connection_calculator()
    test_curvature_analyzer()
    
    print("\n所有测试完成")

if __name__ == "__main__":
    main()
