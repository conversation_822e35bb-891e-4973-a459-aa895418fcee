"""
持续同调算子测试脚本

这个脚本测试持续同调算子的基本功能。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

import numpy as np
import matplotlib.pyplot as plt

# 导入我们刚刚创建的模块
from src.interfaces.operator import OperatorInterface
from src.operators.topology.homology import PersistentHomologyOperator
from src.operators.topology.simplicial import SimplicialComplexBuilder
from src.operators.topology.persistence import PersistenceDiagramGenerator
from src.operators.topology.features import TopologicalFeatureExtractor


def generate_circle_data(n_points=100, noise=0.1):
    """生成圆形点云数据"""
    theta = np.linspace(0, 2*np.pi, n_points)
    x = np.cos(theta) + np.random.normal(0, noise, n_points)
    y = np.sin(theta) + np.random.normal(0, noise, n_points)
    return np.column_stack((x, y))


def generate_figure_eight_data(n_points=100, noise=0.1):
    """生成8字形点云数据"""
    t = np.linspace(0, 2*np.pi, n_points)
    x = np.sin(t)
    y = np.sin(t) * np.cos(t)

    # 添加噪声
    x += np.random.normal(0, noise, n_points)
    y += np.random.normal(0, noise, n_points)

    return np.column_stack((x, y))


def test_persistent_homology_operator():
    """测试PersistentHomologyOperator"""
    print("\n测试PersistentHomologyOperator...")

    # 生成圆形点云数据
    circle_data = generate_circle_data(n_points=50, noise=0.05)

    # 创建持续同调算子
    ph_operator = PersistentHomologyOperator(max_dimension=1, method='standard')
    print(f"创建算子: {ph_operator}")

    # 应用算子
    result = ph_operator.apply(circle_data, dimensions=[0, 1])

    # 打印结果
    print(f"持续同调计算结果:")
    print(f"  方法: {result['method']}")
    print(f"  滤值类型: {result['filtration_type']}")

    # 打印0维持续图
    if 0 in result['persistence_diagrams']:
        diagram_0 = result['persistence_diagrams'][0]
        print(f"  0维持续图: {len(diagram_0)}个点")
        if len(diagram_0) > 0:
            print(f"    前5个点: {diagram_0[:5]}")

    # 打印1维持续图
    if 1 in result['persistence_diagrams']:
        diagram_1 = result['persistence_diagrams'][1]
        print(f"  1维持续图: {len(diagram_1)}个点")
        if len(diagram_1) > 0:
            print(f"    前5个点: {diagram_1[:5]}")

    print("PersistentHomologyOperator测试完成")

    return result


def test_simplicial_complex_builder():
    """测试SimplicialComplexBuilder"""
    print("\n测试SimplicialComplexBuilder...")

    # 生成圆形点云数据
    circle_data = generate_circle_data(n_points=20, noise=0.05)

    # 创建单纯复形构建器
    sc_builder = SimplicialComplexBuilder(max_dimension=1, complex_type='rips')
    print(f"创建构建器: {sc_builder}")

    # 应用构建器
    result = sc_builder.apply(circle_data)

    # 打印结果
    print(f"单纯复形构建结果:")
    print(f"  复形类型: {result['complex_type']}")
    print(f"  最大维数: {result['max_dimension']}")
    print(f"  单纯形数量: {len(result['simplices'])}")

    # 打印单纯形分布
    simplex_dims = [len(s) - 1 for s in result['simplices']]
    unique_dims, counts = np.unique(simplex_dims, return_counts=True)
    for dim, count in zip(unique_dims, counts):
        print(f"  {dim}维单纯形: {count}个")

    print("SimplicialComplexBuilder测试完成")

    return result


def test_persistence_diagram_generator():
    """测试PersistenceDiagramGenerator"""
    print("\n测试PersistenceDiagramGenerator...")

    # 首先计算持续同调
    circle_data = generate_circle_data(n_points=50, noise=0.05)
    ph_operator = PersistentHomologyOperator(max_dimension=1, method='standard')
    ph_result = ph_operator.apply(circle_data, dimensions=[0, 1])

    # 创建持续图生成器
    pd_generator = PersistenceDiagramGenerator(visualization_enabled=True)
    print(f"创建生成器: {pd_generator}")

    # 应用生成器
    result = pd_generator.apply(ph_result, compute_statistics=True)

    # 打印结果
    print(f"持续图生成结果:")

    # 打印统计信息
    if 'statistics' in result:
        for dim, stats in result['statistics'].items():
            print(f"  {dim}维统计信息:")
            for key, value in stats.items():
                print(f"    {key}: {value}")

    # 显示图表
    if 'plots' in result:
        for dim, fig in result['plots'].items():
            if fig is not None:
                plt.figure(fig.number)
                plt.title(f"Dimension {dim} Persistence Diagram")
                plt.tight_layout()
                plt.show()

    print("PersistenceDiagramGenerator测试完成")

    return result


def test_topological_feature_extractor():
    """测试TopologicalFeatureExtractor"""
    print("\n测试TopologicalFeatureExtractor...")

    # 生成两种不同的数据
    circle_data = generate_circle_data(n_points=50, noise=0.05)
    eight_data = generate_figure_eight_data(n_points=50, noise=0.05)

    # 计算持续同调
    ph_operator = PersistentHomologyOperator(max_dimension=1, method='standard')
    circle_ph = ph_operator.apply(circle_data, dimensions=[0, 1])
    eight_ph = ph_operator.apply(eight_data, dimensions=[0, 1])

    # 创建特征提取器
    feature_extractor = TopologicalFeatureExtractor(feature_type='persistence_statistics')
    print(f"创建特征提取器: {feature_extractor}")

    # 应用特征提取器
    circle_features = feature_extractor.apply(circle_ph)
    eight_features = feature_extractor.apply(eight_ph)

    # 打印结果
    print(f"拓扑特征提取结果:")

    # 比较两种数据的特征
    print(f"  圆形数据 vs 8字形数据:")
    for dim in set(circle_features.keys()) & set(eight_features.keys()):
        print(f"  {dim}维特征比较:")
        for key in set(circle_features[dim].keys()) & set(eight_features[dim].keys()):
            circle_value = circle_features[dim][key]
            eight_value = eight_features[dim][key]
            print(f"    {key}: {circle_value:.4f} vs {eight_value:.4f}")

    # 测试其他特征类型
    feature_types = ['betti_curve', 'persistence_landscape']
    for feature_type in feature_types:
        feature_extractor.set_parameters({"feature_type": feature_type})
        print(f"\n  提取{feature_type}特征:")

        circle_features = feature_extractor.apply(circle_ph)
        eight_features = feature_extractor.apply(eight_ph)

        for dim in set(circle_features.keys()) & set(eight_features.keys()):
            print(f"    {dim}维{feature_type}特征形状:")
            print(f"      圆形数据: {np.array(circle_features[dim]).shape}")
            print(f"      8字形数据: {np.array(eight_features[dim]).shape}")

    print("TopologicalFeatureExtractor测试完成")


def main():
    """主函数"""
    print("开始测试持续同调算子...")

    # 测试PersistentHomologyOperator
    ph_result = test_persistent_homology_operator()

    # 测试SimplicialComplexBuilder
    sc_result = test_simplicial_complex_builder()

    # 测试PersistenceDiagramGenerator
    pd_result = test_persistence_diagram_generator()

    # 测试TopologicalFeatureExtractor
    test_topological_feature_extractor()

    print("\n所有测试完成")


if __name__ == "__main__":
    main()
