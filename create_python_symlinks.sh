#!/bin/bash

# Create ~/bin directory if it doesn't exist
mkdir -p ~/bin

# Create symbolic links
ln -sf /home/<USER>/python_gil_test/Python-3.13.3/python ~/bin/python
ln -sf /home/<USER>/python_gil_test/Python-3.13.3/python ~/bin/python3
ln -sf /home/<USER>/python_gil_test/Python-3.13.3/python ~/bin/python3.13

# Make sure ~/bin is in PATH
if [[ ":$PATH:" != *":$HOME/bin:"* ]]; then
    echo '
# Add ~/bin to PATH
export PATH="$HOME/bin:$PATH"
' >> ~/.bashrc
    echo "Added ~/bin to your PATH in ~/.bashrc"
fi

echo "Symbolic links to Python 3.13.3 have been created in ~/bin"
echo "If this is your first time adding ~/bin to PATH, you'll need to restart your terminal or run: source ~/.bashrc"
