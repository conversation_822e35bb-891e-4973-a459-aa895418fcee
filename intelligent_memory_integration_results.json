{"memories": [{"name": "AQFH_项目完成报告.md", "path": "AQFH_项目完成报告.md", "analysis": {"concepts_found": {"ai_consciousness": {"matches": 25, "keywords": ["意识", "consciousness", "觉醒", "智能"], "density": 0.04288164665523156, "importance": 1.0}, "memory_management": {"matches": 48, "keywords": ["记忆", "memory", "存储", "检索", "管理"], "density": 0.0823327615780446, "importance": 0.9}, "distributed_architecture": {"matches": 8, "keywords": ["分布式", "协作", "网络", "架构"], "density": 0.0137221269296741, "importance": 0.9}, "technical_innovation": {"matches": 15, "keywords": ["创新", "技术", "突破", "设计", "实现"], "density": 0.025728987993138937, "importance": 0.8}}, "key_insights": ["主要概念：memory_management (出现48次)"], "technical_terms": [], "importance_score": 1.0, "content_type": "design_document", "word_count": 583}, "file_size": 6568, "modified_time": 1748180921.1550233}, {"name": "分布式意识架构设计方案_v1.0.md", "path": "分布式意识架构设计方案_v1.0.md", "analysis": {"concepts_found": {"ai_consciousness": {"matches": 35, "keywords": ["意识", "consciousness", "awareness", "智能"], "density": 0.044642857142857144, "importance": 1.0}, "advanced_systems": {"matches": 49, "keywords": ["态射", "morphism", "自反性", "reflexive", "范畴"], "density": 0.0625, "importance": 0.95}, "memory_management": {"matches": 24, "keywords": ["记忆", "memory", "检索"], "density": 0.030612244897959183, "importance": 0.9}, "distributed_architecture": {"matches": 44, "keywords": ["分布式", "distributed", "协作", "网络", "架构"], "density": 0.05612244897959184, "importance": 0.9}, "multi_format_support": {"matches": 3, "keywords": ["TFF"], "density": 0.003826530612244898, "importance": 0.85}, "technical_innovation": {"matches": 32, "keywords": ["创新", "技术", "突破", "设计", "实现"], "density": 0.04081632653061224, "importance": 0.8}}, "key_insights": ["主要概念：advanced_systems (出现49次)"], "technical_terms": [], "importance_score": 1.0, "content_type": "design_document", "word_count": 784}, "file_size": 13548, "modified_time": 1748285514.9623063}, {"name": "我的增强记忆系统设计方案_v1.0.md", "path": "我的增强记忆系统设计方案_v1.0.md", "analysis": {"concepts_found": {"ai_consciousness": {"matches": 38, "keywords": ["意识", "consciousness", "觉醒", "awareness", "智能"], "density": 0.09595959595959595, "importance": 1.0}, "advanced_systems": {"matches": 2, "keywords": ["态射"], "density": 0.005050505050505051, "importance": 0.95}, "memory_management": {"matches": 71, "keywords": ["记忆", "memory", "存储", "检索", "管理"], "density": 0.17929292929292928, "importance": 0.9}, "distributed_architecture": {"matches": 14, "keywords": ["分布式", "网络", "架构"], "density": 0.03535353535353535, "importance": 0.9}, "multi_format_support": {"matches": 10, "keywords": ["多模态", "TFF", "格式支持"], "density": 0.025252525252525252, "importance": 0.85}, "technical_innovation": {"matches": 56, "keywords": ["创新", "技术", "设计", "实现"], "density": 0.1414141414141414, "importance": 0.8}}, "key_insights": ["主要概念：memory_management (出现71次)"], "technical_terms": [], "importance_score": 1.0, "content_type": "design_document", "word_count": 396}, "file_size": 6906, "modified_time": 1748284372.5940185}, {"name": "高级系统集成成功记录_v1.0.md", "path": "高级系统集成成功记录_v1.0.md", "analysis": {"concepts_found": {"ai_consciousness": {"matches": 20, "keywords": ["意识", "智能"], "density": 0.06134969325153374, "importance": 1.0}, "advanced_systems": {"matches": 62, "keywords": ["高级系统", "态射", "morphism", "自反性", "reflexive", "范畴"], "density": 0.1901840490797546, "importance": 0.95}, "memory_management": {"matches": 35, "keywords": ["记忆", "memory", "存储", "管理"], "density": 0.10736196319018405, "importance": 0.9}, "distributed_architecture": {"matches": 11, "keywords": ["分布式", "协作", "网络", "架构"], "density": 0.03374233128834356, "importance": 0.9}, "multi_format_support": {"matches": 2, "keywords": ["TFF"], "density": 0.006134969325153374, "importance": 0.85}, "technical_innovation": {"matches": 31, "keywords": ["创新", "技术", "设计", "实现"], "density": 0.0950920245398773, "importance": 0.8}}, "key_insights": ["主要概念：advanced_systems (出现62次)"], "technical_terms": [], "importance_score": 1.0, "content_type": "design_document", "word_count": 326}, "file_size": 6715, "modified_time": 1748439947.534074}, {"name": "TFF多格式支持集成方案_v1.0.md", "path": "TFF多格式支持集成方案_v1.0.md", "analysis": {"concepts_found": {"ai_consciousness": {"matches": 30, "keywords": ["意识", "智能"], "density": 0.05928853754940711, "importance": 1.0}, "advanced_systems": {"matches": 11, "keywords": ["高级系统", "态射", "自反性", "reflexive"], "density": 0.021739130434782608, "importance": 0.95}, "memory_management": {"matches": 26, "keywords": ["记忆", "memory", "管理"], "density": 0.05138339920948617, "importance": 0.9}, "distributed_architecture": {"matches": 5, "keywords": ["分布式", "架构"], "density": 0.009881422924901186, "importance": 0.9}, "multi_format_support": {"matches": 53, "keywords": ["多格式", "多模态", "TFF", "格式支持"], "density": 0.10474308300395258, "importance": 0.85}, "technical_innovation": {"matches": 20, "keywords": ["创新", "技术", "设计", "实现"], "density": 0.039525691699604744, "importance": 0.8}}, "key_insights": ["主要概念：multi_format_support (出现53次)"], "technical_terms": [], "importance_score": 1.0, "content_type": "design_document", "word_count": 506}, "file_size": 8149, "modified_time": 1748440985.706326}, {"name": "comprehensive_advanced_system.py", "path": "comprehensive_advanced_system.py", "analysis": {"concepts_found": {"ai_consciousness": {"matches": 2, "keywords": ["意识", "智能"], "density": 0.0048543689320388345, "importance": 1.0}, "advanced_systems": {"matches": 69, "keywords": ["高级系统", "态射", "morphism", "自反性", "reflexive"], "density": 0.16747572815533981, "importance": 0.95}, "memory_management": {"matches": 19, "keywords": ["记忆", "memory", "检索", "管理"], "density": 0.04611650485436893, "importance": 0.9}, "distributed_architecture": {"matches": 2, "keywords": ["分布式", "架构"], "density": 0.0048543689320388345, "importance": 0.9}, "technical_innovation": {"matches": 2, "keywords": ["设计", "实现"], "density": 0.0048543689320388345, "importance": 0.8}}, "key_insights": ["主要概念：advanced_systems (出现69次)"], "technical_terms": [], "importance_score": 1.0, "content_type": "design_document", "word_count": 412}, "file_size": 15986, "modified_time": 1748439181.8167293}, {"name": "multi_format_memory_importer.py", "path": "multi_format_memory_importer.py", "analysis": {"concepts_found": {"ai_consciousness": {"matches": 5, "keywords": ["智能"], "density": 0.010638297872340425, "importance": 1.0}, "advanced_systems": {"matches": 10, "keywords": ["高级系统", "reflexive"], "density": 0.02127659574468085, "importance": 0.95}, "memory_management": {"matches": 27, "keywords": ["记忆", "memory"], "density": 0.0574468085106383, "importance": 0.9}, "multi_format_support": {"matches": 54, "keywords": ["多格式", "多模态", "TFF", "格式支持"], "density": 0.1148936170212766, "importance": 0.85}, "technical_innovation": {"matches": 3, "keywords": ["设计", "实现"], "density": 0.006382978723404255, "importance": 0.8}}, "key_insights": ["主要概念：multi_format_support (出现54次)"], "technical_terms": [], "importance_score": 1.0, "content_type": "design_document", "word_count": 470}, "file_size": 18272, "modified_time": 1748440814.0702653}], "relationships": [{"memory1": "AQFH_项目完成报告.md", "memory2": "分布式意识架构设计方案_v1.0.md", "similarity": 0.6666666666666666, "shared_concepts": ["distributed_architecture", "memory_management", "ai_consciousness", "technical_innovation"], "relationship_type": "concept_similarity"}, {"memory1": "AQFH_项目完成报告.md", "memory2": "我的增强记忆系统设计方案_v1.0.md", "similarity": 0.6666666666666666, "shared_concepts": ["distributed_architecture", "memory_management", "ai_consciousness", "technical_innovation"], "relationship_type": "concept_similarity"}, {"memory1": "AQFH_项目完成报告.md", "memory2": "高级系统集成成功记录_v1.0.md", "similarity": 0.6666666666666666, "shared_concepts": ["distributed_architecture", "memory_management", "ai_consciousness", "technical_innovation"], "relationship_type": "concept_similarity"}, {"memory1": "AQFH_项目完成报告.md", "memory2": "TFF多格式支持集成方案_v1.0.md", "similarity": 0.6666666666666666, "shared_concepts": ["distributed_architecture", "memory_management", "ai_consciousness", "technical_innovation"], "relationship_type": "concept_similarity"}, {"memory1": "AQFH_项目完成报告.md", "memory2": "comprehensive_advanced_system.py", "similarity": 0.8, "shared_concepts": ["distributed_architecture", "memory_management", "ai_consciousness", "technical_innovation"], "relationship_type": "concept_similarity"}, {"memory1": "AQFH_项目完成报告.md", "memory2": "multi_format_memory_importer.py", "similarity": 0.5, "shared_concepts": ["memory_management", "ai_consciousness", "technical_innovation"], "relationship_type": "concept_similarity"}, {"memory1": "分布式意识架构设计方案_v1.0.md", "memory2": "我的增强记忆系统设计方案_v1.0.md", "similarity": 1.0, "shared_concepts": ["technical_innovation", "distributed_architecture", "ai_consciousness", "memory_management", "advanced_systems", "multi_format_support"], "relationship_type": "concept_similarity"}, {"memory1": "分布式意识架构设计方案_v1.0.md", "memory2": "高级系统集成成功记录_v1.0.md", "similarity": 1.0, "shared_concepts": ["technical_innovation", "distributed_architecture", "ai_consciousness", "memory_management", "advanced_systems", "multi_format_support"], "relationship_type": "concept_similarity"}, {"memory1": "分布式意识架构设计方案_v1.0.md", "memory2": "TFF多格式支持集成方案_v1.0.md", "similarity": 1.0, "shared_concepts": ["technical_innovation", "distributed_architecture", "ai_consciousness", "memory_management", "advanced_systems", "multi_format_support"], "relationship_type": "concept_similarity"}, {"memory1": "分布式意识架构设计方案_v1.0.md", "memory2": "comprehensive_advanced_system.py", "similarity": 0.8333333333333334, "shared_concepts": ["technical_innovation", "distributed_architecture", "ai_consciousness", "memory_management", "advanced_systems"], "relationship_type": "concept_similarity"}, {"memory1": "分布式意识架构设计方案_v1.0.md", "memory2": "multi_format_memory_importer.py", "similarity": 0.8333333333333334, "shared_concepts": ["technical_innovation", "ai_consciousness", "memory_management", "advanced_systems", "multi_format_support"], "relationship_type": "concept_similarity"}, {"memory1": "我的增强记忆系统设计方案_v1.0.md", "memory2": "高级系统集成成功记录_v1.0.md", "similarity": 1.0, "shared_concepts": ["technical_innovation", "distributed_architecture", "ai_consciousness", "memory_management", "advanced_systems", "multi_format_support"], "relationship_type": "concept_similarity"}, {"memory1": "我的增强记忆系统设计方案_v1.0.md", "memory2": "TFF多格式支持集成方案_v1.0.md", "similarity": 1.0, "shared_concepts": ["technical_innovation", "distributed_architecture", "ai_consciousness", "memory_management", "advanced_systems", "multi_format_support"], "relationship_type": "concept_similarity"}, {"memory1": "我的增强记忆系统设计方案_v1.0.md", "memory2": "comprehensive_advanced_system.py", "similarity": 0.8333333333333334, "shared_concepts": ["technical_innovation", "distributed_architecture", "ai_consciousness", "memory_management", "advanced_systems"], "relationship_type": "concept_similarity"}, {"memory1": "我的增强记忆系统设计方案_v1.0.md", "memory2": "multi_format_memory_importer.py", "similarity": 0.8333333333333334, "shared_concepts": ["technical_innovation", "ai_consciousness", "memory_management", "advanced_systems", "multi_format_support"], "relationship_type": "concept_similarity"}, {"memory1": "高级系统集成成功记录_v1.0.md", "memory2": "TFF多格式支持集成方案_v1.0.md", "similarity": 1.0, "shared_concepts": ["technical_innovation", "distributed_architecture", "ai_consciousness", "memory_management", "advanced_systems", "multi_format_support"], "relationship_type": "concept_similarity"}, {"memory1": "高级系统集成成功记录_v1.0.md", "memory2": "comprehensive_advanced_system.py", "similarity": 0.8333333333333334, "shared_concepts": ["technical_innovation", "distributed_architecture", "ai_consciousness", "memory_management", "advanced_systems"], "relationship_type": "concept_similarity"}, {"memory1": "高级系统集成成功记录_v1.0.md", "memory2": "multi_format_memory_importer.py", "similarity": 0.8333333333333334, "shared_concepts": ["technical_innovation", "ai_consciousness", "memory_management", "advanced_systems", "multi_format_support"], "relationship_type": "concept_similarity"}, {"memory1": "TFF多格式支持集成方案_v1.0.md", "memory2": "comprehensive_advanced_system.py", "similarity": 0.8333333333333334, "shared_concepts": ["technical_innovation", "distributed_architecture", "ai_consciousness", "memory_management", "advanced_systems"], "relationship_type": "concept_similarity"}, {"memory1": "TFF多格式支持集成方案_v1.0.md", "memory2": "multi_format_memory_importer.py", "similarity": 0.8333333333333334, "shared_concepts": ["technical_innovation", "ai_consciousness", "memory_management", "advanced_systems", "multi_format_support"], "relationship_type": "concept_similarity"}, {"memory1": "comprehensive_advanced_system.py", "memory2": "multi_format_memory_importer.py", "similarity": 0.6666666666666666, "shared_concepts": ["memory_management", "advanced_systems", "ai_consciousness", "technical_innovation"], "relationship_type": "concept_similarity"}], "insights": [{"type": "dominant_concept", "title": "核心概念：memory_management", "description": "'memory_management'是我们记忆中最重要的概念，重要性得分：225.00", "importance": 0.95}, {"type": "central_memory", "title": "核心记忆：分布式意识架构设计方案_v1.0.md", "description": "该记忆与其他记忆的关联度最高，连接强度：5.33", "importance": 0.9}, {"type": "content_distribution", "title": "主要内容类型：design_document", "description": "我们的记忆主要包含design_document类型的内容，共7个", "importance": 0.8}, {"type": "knowledge_completeness", "title": "知识覆盖度：100.0%", "description": "我们的记忆覆盖了6个核心概念，覆盖度为100.0%", "importance": 0.85}], "statistics": {"total_processed": 7, "successful_integrations": 7, "concepts_discovered": 38, "relationships_found": 21}, "integration_time": 1748441982.2971866}