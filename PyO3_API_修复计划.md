# PyO3 0.24+ API 修复计划

## 问题概述

在升级到 PyO3 0.24+ 版本后，我们的代码库中出现了多个与 API 变化相关的问题。主要变化包括：

1. GIL Refs API 的移除：PyO3 0.23 开始移除了旧的 GIL Refs API，改用新的 `Bound<T>` 智能指针
2. 默认参数语法变化：从 `= None` 改为 `#[pyo3(signature = (...))]` 属性
3. 方法名称变化：移除了 `_bound` 后缀，如 `PyTuple::new_bound` 变为 `PyTuple::new`
4. 类型转换变化：`PyTryFrom` 被 `downcast` 替代
5. 异常处理变化：异常创建和处理方式的更新
6. 线程安全要求：对 `#[pyclass]` 的线程安全要求更严格

## 修复策略

我们将采用以下策略来修复这些问题：

1. 首先启用 `gil-refs` 特性，暂时抑制弃用警告
2. 逐步修复各个模块中的 API 使用问题
3. 最后移除 `gil-refs` 特性，确保代码完全兼容新 API

## 详细修复任务

### 1. 更新 Cargo.toml

```toml
[dependencies]
pyo3 = { version = "0.24", features = ["gil-refs"] }
```

### 2. 修复 GIL Refs API 使用问题

#### 2.1 替换 `PyTuple::new_bound` 等方法

- 将 `PyTuple::new_bound` 改为 `PyTuple::new`
- 将 `PyList::new_bound` 改为 `PyList::new`
- 将 `PyDict::new_bound` 改为 `PyDict::new`
- 将 `PySet::new_bound` 改为 `PySet::new`
- 将其他类似的 `*_bound` 方法替换为无后缀版本

#### 2.2 更新 `&PyAny` 到 `Bound<PyAny>` 的转换

- 使用 `as_borrowed()` 方法将 `&PyAny` 转换为 `&Bound<PyAny>`
- 使用 `bind()` 或 `into_bound()` 方法将 `Py<T>` 转换为 `Bound<T>`
- 使用 `as_unbound()` 或 `unbind()` 方法将 `Bound<T>` 转换回 `Py<T>`

#### 2.3 修复 `Option<&PyAny>` 到指针的转换

- 避免使用 `map_or` 和 `as_ptr()` 的组合，可能导致悬垂指针
- 使用 `as_ref().map_or(std::ptr::null_mut(), Bound::as_ptr)` 模式

### 3. 修复类型转换问题

#### 3.1 替换 `PyTryFrom` 使用

- 将 `<T as PyTryFrom>::try_from(obj)` 替换为 `obj.downcast::<T>()?`
- 对于需要所有权转移的情况，使用 `obj.downcast_into::<T>()?`

#### 3.2 更新容器类型注册

- 为自定义映射类使用 `PyMapping::register::<MyClass>(py)`
- 为自定义序列类使用 `PySequence::register::<MyClass>(py)`

### 4. 修复默认参数语法

#### 4.1 更新函数签名

- 将 `fn func(x: i32 = 0)` 替换为 `#[pyo3(signature = (x=0))] fn func(x: i32)`
- 将 `fn func(x: Option<i32> = None)` 替换为 `#[pyo3(signature = (x=None))] fn func(x: Option<i32>)`

#### 4.2 更新位置参数和关键字参数

- 使用 `/` 标记位置参数：`#[pyo3(signature = (a, b, /))]`
- 使用 `*` 标记关键字参数：`#[pyo3(signature = (*, a, b))]`

### 5. 修复异常处理

#### 5.1 更新异常创建

- 将 `TypeError::py_err("message")` 替换为 `PyTypeError::new_err("message")`
- 将 `PyErr::new::<TypeError, _>("message").into()` 替换为 `Err(PyTypeError::new_err("message"))`

#### 5.2 更新异常实例访问

- 使用 `err.instance(py)` 获取异常实例
- 使用 `PyErr::take(py)` 检查和获取当前异常

### 6. 修复线程安全问题

#### 6.1 为非线程安全类添加 `unsendable` 标记

- 对包含 `Rc`、`RefCell` 等非线程安全类型的 `#[pyclass]` 添加 `#[pyclass(unsendable)]`
- 对包含原始指针的 `#[pyclass]` 添加 `#[pyclass(unsendable)]`

#### 6.2 修复 `__traverse__` 方法

- 确保 `__traverse__` 方法中不使用 `Python::with_gil` 或其他 Python API
- 为继承类正确实现 `__traverse__` 方法

### 7. 其他修复

#### 7.1 更新 `IntoPyDict` 实现

- 将 `into_py_dict_bound` 方法重命名为 `into_py_dict`
- 更新返回类型为 `PyResult<Bound<'py, PyDict>>`
- 更新特征边界从 `ToPyObject` 到 `IntoPyObject<'py>`

#### 7.2 修复字符串转换

- 为 `&str.into_py(py)` 添加显式类型注解，如 `let _test: Py<PyAny> = "test".into_py(py);`
- 将 `PyUnicode` 替换为 `PyString`

#### 7.3 修复字节集合转换

- 注意 `Vec<u8>` 现在默认转换为 `PyBytes` 而不是 `PyList`

## 测试计划

1. 为每个修复的模块编写单元测试
2. 创建集成测试验证跨模块功能
3. 使用 Python 脚本测试从 Python 端调用 Rust 函数
4. 验证错误处理和异常传播

## 实施顺序

1. 首先修复核心模块和基础设施
2. 然后修复依赖于核心模块的组件
3. 最后修复应用层代码

## 完成标准

1. 所有代码编译通过，无警告
2. 所有测试通过
3. 移除 `gil-refs` 特性后代码仍能正常工作
4. 文档更新反映新的 API 使用方式
