#!/usr/bin/env python3
"""
构建PyO3 0.24+兼容版本的可验证性算子
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def build_rust_extension(path: str) -> bool:
    """
    构建Rust扩展
    
    参数:
        path: Rust扩展的路径
        
    返回:
        是否成功构建
    """
    logger.info(f"构建Rust扩展: {path}")
    
    try:
        # 切换到Rust扩展目录
        os.chdir(path)
        
        # 构建Rust扩展
        result = subprocess.run(
            ["maturin", "develop", "--release"],
            check=True,
            capture_output=True,
            text=True
        )
        
        logger.info(f"构建成功: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"构建失败: {e.stderr}")
        return False
    except Exception as e:
        logger.error(f"构建失败: {e}")
        return False

def test_rust_extension() -> bool:
    """
    测试Rust扩展
    
    返回:
        是否测试成功
    """
    logger.info("测试Rust扩展...")
    
    try:
        # 创建测试脚本
        test_script = """
import verifiability_v24

# 创建可验证性算子
operator = verifiability_v24.VerifiabilityOperator(
    method="hybrid",
    threshold=0.7,
    timeout_ms=30000,
    parallel_threshold=3
)

# 创建验证属性
properties = [
    {
        "name": "一致性",
        "description": "模型在相似输入上产生相似输出",
        "property_type": "robustness",
        "expression": "forall x, y. |x - y| < epsilon -> |f(x) - f(y)| < delta",
        "importance": 0.8
    },
    {
        "name": "单调性",
        "description": "输入增加时输出不减少",
        "property_type": "monotonicity",
        "expression": "forall x, y. x <= y -> f(x) <= f(y)",
        "importance": 0.6
    }
]

# 测试输入数据
input_data = {
    "model_output": {
        "prediction": "positive",
        "confidence": 0.85,
        "features": {
            "text_length": 500,
            "sentiment_score": 0.75,
            "topic_relevance": 0.9
        }
    },
    "model_type": "classification",
    "domain": "sentiment_analysis"
}

# 应用算子
result = operator.apply(input_data, properties=properties)

# 检查结果
assert "id" in result
assert "properties" in result
assert "overall_score" in result
assert len(result["properties"]) == 2

print("测试成功！")
"""
        
        # 使用虚拟环境中的Python解释器
        venv_python = os.path.join(os.path.expanduser("~"), ".venv", "bin", "python")
        
        # 运行测试脚本
        result = subprocess.run(
            [venv_python, "-c", test_script],
            check=True,
            capture_output=True,
            text=True
        )
        
        logger.info(f"测试成功: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"测试失败: {e.stderr}")
        return False
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False

def main():
    """主函数"""
    # 获取项目根目录
    root_dir = Path(__file__).parent.absolute()
    
    # 构建可验证性算子的Rust实现
    verifiability_path = root_dir / "rust_operators" / "verifiability_v24"
    
    if not verifiability_path.exists():
        logger.error(f"可验证性算子的Rust实现路径不存在: {verifiability_path}")
        return
    
    # 保存当前目录
    current_dir = os.getcwd()
    
    try:
        # 构建PyO3 0.24+兼容版本的可验证性算子
        success = build_rust_extension(str(verifiability_path))
        
        if success:
            logger.info("PyO3 0.24+兼容版本的可验证性算子构建成功")
            
            # 测试Rust扩展
            if test_rust_extension():
                logger.info("PyO3 0.24+兼容版本的可验证性算子测试成功")
            else:
                logger.error("PyO3 0.24+兼容版本的可验证性算子测试失败")
        else:
            logger.error("PyO3 0.24+兼容版本的可验证性算子构建失败")
    finally:
        # 恢复当前目录
        os.chdir(current_dir)

if __name__ == "__main__":
    main()
