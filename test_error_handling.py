#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
错误处理算子测试脚本
"""

import logging
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

# 导入错误处理算子
from src.rust_bindings.operator_registry.error_handling import (
    register_error_handling_operator,
    get_error_handling_operator_from_registry
)

# 导入错误处理模型
from src.transcendental_tensor.error_handling.models import (
    ErrorSeverity,
    ErrorCategory,
    ErrorHandlingStrategy,
    ErrorContext
)

def main():
    """主函数"""
    print("错误处理算子测试")
    print("=" * 50)
    
    # 注册错误处理算子
    print("\n1. 注册错误处理算子")
    success = register_error_handling_operator()
    print(f"注册结果: {success}")
    
    # 获取错误处理算子
    print("\n2. 获取错误处理算子")
    operator = get_error_handling_operator_from_registry()
    print(f"错误处理算子: {operator}")
    
    # 测试算术错误
    print("\n3. 测试算术错误")
    try:
        result = 1 / 0
    except Exception as e:
        print(f"捕获到异常: {e}")
        
        # 创建错误上下文
        context = ErrorContext(
            component="test_script",
            operation="division",
            metadata={"value1": 1, "value2": 0}
        )
        
        # 处理错误
        result = operator.apply(
            e,
            context=context,
            strategy=ErrorHandlingStrategy.SUPPRESS
        )
        
        print(f"处理结果: {result}")
        print(f"错误类型: {result.error_info.error_type}")
        print(f"错误消息: {result.error_info.error_message}")
        print(f"错误严重性: {result.error_info.severity}")
        print(f"处理策略: {result.strategy}")
        print(f"处理成功: {result.success}")
    
    # 测试类型错误
    print("\n4. 测试类型错误")
    try:
        result = "string" + 123
    except Exception as e:
        print(f"捕获到异常: {e}")
        
        # 创建错误上下文
        context = ErrorContext(
            component="test_script",
            operation="string_concat",
            metadata={"value1": "string", "value2": 123}
        )
        
        # 处理错误
        result = operator.apply(
            e,
            context=context,
            strategy=ErrorHandlingStrategy.LOG_ONLY
        )
        
        print(f"处理结果: {result}")
        print(f"错误类型: {result.error_info.error_type}")
        print(f"错误消息: {result.error_info.error_message}")
        print(f"错误严重性: {result.error_info.severity}")
        print(f"处理策略: {result.strategy}")
        print(f"处理成功: {result.success}")
    
    # 测试索引错误
    print("\n5. 测试索引错误")
    try:
        data = [1, 2, 3]
        result = data[10]
    except Exception as e:
        print(f"捕获到异常: {e}")
        
        # 创建错误上下文
        context = ErrorContext(
            component="test_script",
            operation="list_access",
            metadata={"data": data, "index": 10}
        )
        
        # 处理错误
        result = operator.apply(
            e,
            context=context,
            strategy=ErrorHandlingStrategy.PROPAGATE
        )
        
        print(f"处理结果: {result}")
        print(f"错误类型: {result.error_info.error_type}")
        print(f"错误消息: {result.error_info.error_message}")
        print(f"错误严重性: {result.error_info.severity}")
        print(f"处理策略: {result.strategy}")
        print(f"处理成功: {result.success}")
    
    # 获取性能指标
    print("\n6. 获取性能指标")
    metrics = operator.get_performance_metrics()
    print(f"处理的错误数量: {metrics.get('handled_errors', 0)}")
    print(f"成功恢复的错误数量: {metrics.get('successful_recoveries', 0)}")
    print(f"失败恢复的错误数量: {metrics.get('failed_recoveries', 0)}")
    print(f"平均处理时间: {metrics.get('avg_processing_time', 0):.6f}秒")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
