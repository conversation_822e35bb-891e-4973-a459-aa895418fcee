"""
独立的TransformOperator测试脚本
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Any, Dict, List, Optional, Tuple, Union, Callable
from abc import ABC, abstractmethod


# 定义OperatorInterface接口
class OperatorInterface(ABC):
    """超越态算子接口基类"""
    
    @abstractmethod
    def __init__(self, **kwargs):
        """初始化算子"""
        pass
    
    @abstractmethod
    def apply(self, input_data: Any, **kwargs) -> Any:
        """应用算子到输入数据"""
        pass
    
    @abstractmethod
    def get_metadata(self) -> Dict[str, Any]:
        """获取算子元数据"""
        pass
    
    @abstractmethod
    def is_compatible_with(self, other_operator: 'OperatorInterface') -> bool:
        """检查与其他算子的兼容性"""
        pass
    
    @abstractmethod
    def get_performance_metrics(self) -> Dict[str, float]:
        """获取性能指标"""
        pass
    
    @abstractmethod
    def compose(self, other_operator: 'OperatorInterface') -> 'OperatorInterface':
        """与其他算子组合"""
        pass
    
    @abstractmethod
    def get_parameters(self) -> Dict[str, Any]:
        """获取算子参数"""
        pass
    
    @abstractmethod
    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """设置算子参数"""
        pass
    
    @abstractmethod
    def to_rust(self) -> bool:
        """检查是否有Rust实现"""
        pass
    
    @abstractmethod
    def get_complexity(self) -> Dict[str, Any]:
        """获取算子复杂度信息"""
        pass


# 实现TransformOperator
class TransformOperator(OperatorInterface):
    """
    变换算子类
    
    该类提供了对数据进行各种变换的方法，支持线性变换、非线性变换、群变换等。
    
    属性:
        transform_type (str): 变换类型
        dimension (int): 变换空间维度
        parameters (Dict[str, Any]): 变换参数
        name (str): 算子名称
    """
    
    def __init__(self, 
                 transform_type: str = 'linear',
                 dimension: int = 3,
                 parameters: Optional[Dict[str, Any]] = None,
                 **kwargs):
        """
        初始化TransformOperator算子
        
        参数:
            transform_type (str): 变换类型，可选值包括"linear"、"nonlinear"、"group"、"spectral"
            dimension (int): 变换空间维度
            parameters (Dict[str, Any], optional): 变换参数
            **kwargs: 其他参数
        """
        self.transform_type = transform_type
        self.dimension = dimension
        self.parameters = parameters or {}
        self.name = "TransformOperator"
        
        # 初始化变换函数映射
        self._transform_functions = {
            'linear': self._apply_linear_transform,
            'nonlinear': self._apply_nonlinear_transform,
            'group': self._apply_group_transform,
            'spectral': self._apply_spectral_transform
        }
        
        # 初始化性能指标
        self._performance_metrics = {
            "time_complexity": "O(n^2)" if transform_type == 'linear' else "O(n^3)",
            "space_complexity": "O(n)",
            "numerical_stability": 0.95 if transform_type == 'linear' else 0.85,
            "parallelizability": 0.9 if transform_type in ['linear', 'spectral'] else 0.7
        }
        
        # 初始化变换矩阵（如果是线性变换）
        if transform_type == 'linear' and 'matrix' not in self.parameters:
            self.parameters['matrix'] = np.eye(dimension)
    
    def apply(self, input_data: Any, **kwargs) -> Any:
        """
        应用变换到输入数据
        
        参数:
            input_data: 输入数据，可以是以下形式之一：
                - numpy数组：形状为(n_samples, dimension)的数据点
                - 字典：包含'data'键的字典，值为numpy数组
                - 列表：可转换为numpy数组的数据点列表
            **kwargs: 其他参数，包括：
                - inplace (bool, optional): 是否原地修改数据，默认为False
                - inverse (bool, optional): 是否应用逆变换，默认为False
                - additional_params (Dict[str, Any], optional): 额外的变换参数
        
        返回:
            变换后的数据，格式与输入数据相同
        """
        # 提取参数
        inplace = kwargs.get('inplace', False)
        inverse = kwargs.get('inverse', False)
        additional_params = kwargs.get('additional_params', {})
        
        # 合并参数
        params = {**self.parameters, **additional_params}
        
        # 预处理输入数据
        data, data_type = self._preprocess_input(input_data)
        
        # 如果不是原地修改，创建数据副本
        if not inplace:
            data = data.copy()
        
        # 应用变换
        if self.transform_type in self._transform_functions:
            if inverse:
                result = self._apply_inverse_transform(data, params)
            else:
                result = self._transform_functions[self.transform_type](data, params)
        else:
            raise ValueError(f"Unknown transform type: {self.transform_type}")
        
        # 后处理结果
        return self._postprocess_output(result, data_type, input_data)
    
    def _preprocess_input(self, input_data: Any) -> Tuple[np.ndarray, str]:
        """预处理输入数据，转换为numpy数组"""
        if isinstance(input_data, np.ndarray):
            return input_data, 'ndarray'
        
        elif isinstance(input_data, dict) and 'data' in input_data:
            return input_data['data'], 'dict'
        
        elif isinstance(input_data, list):
            return np.array(input_data), 'list'
        
        else:
            raise ValueError(f"Unsupported input type: {type(input_data)}")
    
    def _postprocess_output(self, result: np.ndarray, data_type: str, original_data: Any) -> Any:
        """后处理结果，转换回原始数据格式"""
        if data_type == 'ndarray':
            return result
        
        elif data_type == 'dict':
            output = original_data.copy()
            output['data'] = result
            return output
        
        elif data_type == 'list':
            return result.tolist()
        
        else:
            return result
    
    def _apply_linear_transform(self, data: np.ndarray, params: Dict[str, Any]) -> np.ndarray:
        """应用线性变换"""
        matrix = params.get('matrix', np.eye(self.dimension))
        offset = params.get('offset', np.zeros(self.dimension))
        
        # 检查矩阵维度
        if matrix.shape[1] != data.shape[1]:
            raise ValueError(f"Matrix shape {matrix.shape} incompatible with data shape {data.shape}")
        
        # 应用线性变换: y = Ax + b
        return np.dot(data, matrix.T) + offset
    
    def _apply_nonlinear_transform(self, data: np.ndarray, params: Dict[str, Any]) -> np.ndarray:
        """应用非线性变换"""
        transform_function = params.get('function', lambda x: x)
        
        if callable(transform_function):
            return transform_function(data)
        else:
            # 预定义的非线性变换
            if transform_function == 'sigmoid':
                return 1 / (1 + np.exp(-data))
            elif transform_function == 'tanh':
                return np.tanh(data)
            elif transform_function == 'relu':
                return np.maximum(0, data)
            elif transform_function == 'quadratic':
                return data ** 2
            else:
                raise ValueError(f"Unknown nonlinear transform function: {transform_function}")
    
    def _apply_group_transform(self, data: np.ndarray, params: Dict[str, Any]) -> np.ndarray:
        """应用群变换"""
        group_type = params.get('group_type', 'rotation')
        
        if group_type == 'rotation':
            # 旋转变换
            angle = params.get('angle', 0.0)
            axis = params.get('axis', 2)  # 默认绕z轴旋转
            
            # 创建旋转矩阵
            rotation_matrix = np.eye(self.dimension)
            
            if self.dimension >= 2:
                if axis == 0:  # 绕x轴旋转
                    rotation_matrix[1, 1] = np.cos(angle)
                    rotation_matrix[1, 2] = -np.sin(angle)
                    rotation_matrix[2, 1] = np.sin(angle)
                    rotation_matrix[2, 2] = np.cos(angle)
                elif axis == 1:  # 绕y轴旋转
                    rotation_matrix[0, 0] = np.cos(angle)
                    rotation_matrix[0, 2] = np.sin(angle)
                    rotation_matrix[2, 0] = -np.sin(angle)
                    rotation_matrix[2, 2] = np.cos(angle)
                else:  # 绕z轴旋转
                    rotation_matrix[0, 0] = np.cos(angle)
                    rotation_matrix[0, 1] = -np.sin(angle)
                    rotation_matrix[1, 0] = np.sin(angle)
                    rotation_matrix[1, 1] = np.cos(angle)
            
            return np.dot(data, rotation_matrix.T)
        
        elif group_type == 'scaling':
            # 缩放变换
            scale = params.get('scale', 1.0)
            
            if isinstance(scale, (int, float)):
                return data * scale
            else:
                return data * np.array(scale)
        
        elif group_type == 'reflection':
            # 反射变换
            axis = params.get('axis', 0)
            
            reflection_matrix = np.eye(self.dimension)
            reflection_matrix[axis, axis] = -1
            
            return np.dot(data, reflection_matrix.T)
        
        elif group_type == 'shear':
            # 剪切变换
            shear_factor = params.get('shear_factor', 0.0)
            shear_axis = params.get('shear_axis', (0, 1))  # 默认在x-y平面剪切
            
            shear_matrix = np.eye(self.dimension)
            shear_matrix[shear_axis[0], shear_axis[1]] = shear_factor
            
            return np.dot(data, shear_matrix.T)
        
        else:
            raise ValueError(f"Unknown group transform type: {group_type}")
    
    def _apply_spectral_transform(self, data: np.ndarray, params: Dict[str, Any]) -> np.ndarray:
        """应用谱变换"""
        transform_type = params.get('spectral_type', 'fft')
        
        if transform_type == 'fft':
            # 快速傅里叶变换
            result = np.fft.fft(data, axis=0)
            
            # 如果需要实数结果
            if params.get('real_output', True):
                return np.abs(result)
            return result
        
        else:
            raise ValueError(f"Unknown spectral transform type: {transform_type}")
    
    def _apply_inverse_transform(self, data: np.ndarray, params: Dict[str, Any]) -> np.ndarray:
        """应用逆变换"""
        if self.transform_type == 'linear':
            # 线性变换的逆变换
            matrix = params.get('matrix', np.eye(self.dimension))
            offset = params.get('offset', np.zeros(self.dimension))
            
            # 计算矩阵的逆
            try:
                inv_matrix = np.linalg.inv(matrix)
            except np.linalg.LinAlgError:
                raise ValueError("Matrix is not invertible")
            
            # 应用逆变换: x = A^(-1)(y - b)
            return np.dot(data - offset, inv_matrix.T)
        
        elif self.transform_type == 'nonlinear':
            # 非线性变换的逆变换
            transform_function = params.get('function', lambda x: x)
            inverse_function = params.get('inverse_function', None)
            
            if inverse_function is not None and callable(inverse_function):
                return inverse_function(data)
            else:
                # 预定义的非线性变换的逆变换
                if transform_function == 'sigmoid':
                    return -np.log(1 / data - 1)
                elif transform_function == 'tanh':
                    return 0.5 * np.log((1 + data) / (1 - data))
                elif transform_function == 'relu':
                    # ReLU的逆变换不是唯一的，我们只能恢复正值
                    return data * (data > 0)
                elif transform_function == 'quadratic':
                    return np.sqrt(np.abs(data)) * np.sign(data)
                else:
                    raise ValueError(f"No inverse function defined for {transform_function}")
        
        elif self.transform_type == 'group':
            # 群变换的逆变换
            group_type = params.get('group_type', 'rotation')
            
            if group_type == 'rotation':
                # 旋转变换的逆变换（转置旋转矩阵）
                angle = params.get('angle', 0.0)
                axis = params.get('axis', 2)
                
                # 创建逆旋转矩阵（角度取反）
                inv_params = params.copy()
                inv_params['angle'] = -angle
                
                return self._apply_group_transform(data, inv_params)
            
            elif group_type == 'scaling':
                # 缩放变换的逆变换
                scale = params.get('scale', 1.0)
                
                if isinstance(scale, (int, float)):
                    if scale == 0:
                        raise ValueError("Cannot invert scaling with zero factor")
                    return data / scale
                else:
                    scale_array = np.array(scale)
                    if np.any(scale_array == 0):
                        raise ValueError("Cannot invert scaling with zero factor")
                    return data / scale_array
            
            elif group_type == 'reflection':
                # 反射变换的逆变换（等于自身）
                return self._apply_group_transform(data, params)
            
            elif group_type == 'shear':
                # 剪切变换的逆变换
                shear_factor = params.get('shear_factor', 0.0)
                shear_axis = params.get('shear_axis', (0, 1))
                
                inv_params = params.copy()
                inv_params['shear_factor'] = -shear_factor
                
                return self._apply_group_transform(data, inv_params)
            
            else:
                raise ValueError(f"Unknown group transform type: {group_type}")
        
        elif self.transform_type == 'spectral':
            # 谱变换的逆变换
            transform_type = params.get('spectral_type', 'fft')
            
            if transform_type == 'fft':
                # 逆快速傅里叶变换
                return np.fft.ifft(data, axis=0).real
            
            else:
                raise ValueError(f"Unknown spectral transform type: {transform_type}")
        
        else:
            raise ValueError(f"Unknown transform type: {self.transform_type}")
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取算子元数据"""
        return {
            "name": self.name,
            "type": "transform",
            "transform_type": self.transform_type,
            "dimension": self.dimension,
            "description": "Transform operator for applying various transformations to data"
        }
    
    def is_compatible_with(self, other_operator: 'OperatorInterface') -> bool:
        """检查与其他算子的兼容性"""
        # 检查是否为同类型算子，如果是则检查参数兼容性
        if isinstance(other_operator, TransformOperator):
            # 检查维度是否兼容
            return self.dimension == other_operator.dimension
        
        # 检查是否为EvolutionOperator算子，如果是则兼容
        if other_operator.__class__.__name__ == "EvolutionOperator":
            return True
        
        # 默认不兼容
        return False
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return self._performance_metrics
    
    def compose(self, other_operator: 'OperatorInterface') -> 'OperatorInterface':
        """与其他算子组合"""
        if not self.is_compatible_with(other_operator):
            raise ValueError(f"Cannot compose with incompatible operator: {other_operator.get_metadata()['name']}")
        
        # 如果是同类型算子，创建一个新的TransformOperator算子
        if isinstance(other_operator, TransformOperator):
            # 如果两个都是线性变换，可以直接组合矩阵
            if self.transform_type == 'linear' and other_operator.transform_type == 'linear':
                matrix1 = self.parameters.get('matrix', np.eye(self.dimension))
                matrix2 = other_operator.parameters.get('matrix', np.eye(other_operator.dimension))
                offset1 = self.parameters.get('offset', np.zeros(self.dimension))
                offset2 = other_operator.parameters.get('offset', np.zeros(other_operator.dimension))
                
                # 组合矩阵和偏移: (A2 * A1)x + (A2 * b1 + b2)
                combined_matrix = np.dot(matrix2, matrix1)
                combined_offset = np.dot(offset1, matrix2.T) + offset2
                
                return TransformOperator(
                    transform_type='linear',
                    dimension=self.dimension,
                    parameters={'matrix': combined_matrix, 'offset': combined_offset}
                )
            else:
                # 对于其他类型的变换，返回一个新的算子，但不组合参数
                return TransformOperator(
                    transform_type=self.transform_type,
                    dimension=self.dimension,
                    parameters=self.parameters.copy()
                )
        
        # 如果是EvolutionOperator算子，返回一个复合算子
        if other_operator.__class__.__name__ == "EvolutionOperator":
            # 这里应该返回一个新的复合算子，但目前我们简单地返回self
            # 实际应用中应该创建一个新的复合算子类
            return self
        
        # 默认返回self
        return self
    
    def get_parameters(self) -> Dict[str, Any]:
        """获取算子参数"""
        return {
            "transform_type": self.transform_type,
            "dimension": self.dimension,
            "parameters": self.parameters
        }
    
    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """设置算子参数"""
        if "transform_type" in parameters:
            self.transform_type = parameters["transform_type"]
        
        if "dimension" in parameters:
            self.dimension = parameters["dimension"]
        
        if "parameters" in parameters:
            self.parameters = parameters["parameters"]
        
        # 如果是线性变换，确保矩阵存在
        if self.transform_type == 'linear' and 'matrix' not in self.parameters:
            self.parameters['matrix'] = np.eye(self.dimension)
        
        # 更新性能指标
        self._performance_metrics = {
            "time_complexity": "O(n^2)" if self.transform_type == 'linear' else "O(n^3)",
            "space_complexity": "O(n)",
            "numerical_stability": 0.95 if self.transform_type == 'linear' else 0.85,
            "parallelizability": 0.9 if self.transform_type in ['linear', 'spectral'] else 0.7
        }
    
    def to_rust(self) -> bool:
        """检查是否有Rust实现"""
        # 目前没有Rust实现
        return False
    
    def get_complexity(self) -> Dict[str, Any]:
        """获取算子复杂度信息"""
        complexity_info = {
            "time_complexity": "O(n^2)" if self.transform_type == 'linear' else "O(n^3)",
            "space_complexity": "O(n)",
            "computational_complexity": "Low" if self.transform_type == 'linear' else "Medium",
            "numerical_stability": "High" if self.transform_type == 'linear' else "Medium",
            "parallelizable": True
        }
        
        # 对于特定变换类型的额外信息
        if self.transform_type == 'spectral':
            complexity_info["time_complexity"] = "O(n log n)"
            complexity_info["computational_complexity"] = "Medium to High"
        
        return complexity_info
    
    def __str__(self) -> str:
        """返回算子的字符串表示"""
        return f"TransformOperator(type={self.transform_type}, dimension={self.dimension})"
    
    def __repr__(self) -> str:
        """返回算子的详细字符串表示"""
        return self.__str__()


def test_linear_transform():
    """测试线性变换"""
    print("\n测试线性变换...")
    
    # 创建随机数据
    np.random.seed(42)
    data = np.random.randn(100, 2)
    
    # 创建线性变换算子
    transform_op = TransformOperator(
        transform_type='linear',
        dimension=2,
        parameters={
            'matrix': np.array([[np.cos(np.pi/4), -np.sin(np.pi/4)], 
                               [np.sin(np.pi/4), np.cos(np.pi/4)]]),
            'offset': np.array([0, 0])
        }
    )
    
    # 应用变换
    transformed_data = transform_op.apply(data)
    
    # 打印结果
    print(f"变换类型: {transform_op.transform_type}")
    print(f"变换参数: {transform_op.parameters}")
    print(f"原始数据形状: {data.shape}")
    print(f"变换后数据形状: {transformed_data.shape}")
    
    # 可视化原始数据和变换后的数据
    plt.figure(figsize=(10, 5))
    
    plt.subplot(1, 2, 1)
    plt.scatter(data[:, 0], data[:, 1], alpha=0.7)
    plt.title('Original Data')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.scatter(transformed_data[:, 0], transformed_data[:, 1], alpha=0.7)
    plt.title('Transformed Data')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('linear_transform_test.png')
    plt.close()
    
    print("线性变换测试完成")


def test_nonlinear_transform():
    """测试非线性变换"""
    print("\n测试非线性变换...")
    
    # 创建随机数据
    np.random.seed(42)
    data = np.random.randn(100, 2)
    
    # 创建非线性变换算子
    transform_op = TransformOperator(
        transform_type='nonlinear',
        dimension=2,
        parameters={
            'function': 'sigmoid'
        }
    )
    
    # 应用变换
    transformed_data = transform_op.apply(data)
    
    # 打印结果
    print(f"变换类型: {transform_op.transform_type}")
    print(f"变换参数: {transform_op.parameters}")
    print(f"原始数据形状: {data.shape}")
    print(f"变换后数据形状: {transformed_data.shape}")
    
    # 可视化原始数据和变换后的数据
    plt.figure(figsize=(10, 5))
    
    plt.subplot(1, 2, 1)
    plt.scatter(data[:, 0], data[:, 1], alpha=0.7)
    plt.title('Original Data')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.scatter(transformed_data[:, 0], transformed_data[:, 1], alpha=0.7)
    plt.title('Transformed Data')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('nonlinear_transform_test.png')
    plt.close()
    
    print("非线性变换测试完成")


def test_inverse_transform():
    """测试逆变换"""
    print("\n测试逆变换...")
    
    # 创建随机数据
    np.random.seed(42)
    data = np.random.randn(100, 2)
    
    # 创建线性变换算子
    transform_op = TransformOperator(
        transform_type='linear',
        dimension=2,
        parameters={
            'matrix': np.array([[np.cos(np.pi/4), -np.sin(np.pi/4)], 
                               [np.sin(np.pi/4), np.cos(np.pi/4)]]),
            'offset': np.array([0, 0])
        }
    )
    
    # 应用变换
    transformed_data = transform_op.apply(data)
    
    # 应用逆变换
    reconstructed_data = transform_op.apply(transformed_data, inverse=True)
    
    # 计算重构误差
    reconstruction_error = np.mean(np.abs(data - reconstructed_data))
    
    # 打印结果
    print(f"变换类型: {transform_op.transform_type}")
    print(f"重构误差: {reconstruction_error}")
    
    # 可视化原始数据、变换后的数据和重构数据
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.scatter(data[:, 0], data[:, 1], alpha=0.7)
    plt.title('Original Data')
    plt.grid(True)
    
    plt.subplot(1, 3, 2)
    plt.scatter(transformed_data[:, 0], transformed_data[:, 1], alpha=0.7)
    plt.title('Transformed Data')
    plt.grid(True)
    
    plt.subplot(1, 3, 3)
    plt.scatter(reconstructed_data[:, 0], reconstructed_data[:, 1], alpha=0.7)
    plt.title('Reconstructed Data')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('inverse_transform_test.png')
    plt.close()
    
    print("逆变换测试完成")


def main():
    """主函数"""
    print("开始测试TransformOperator...")
    
    # 测试各种变换
    test_linear_transform()
    test_nonlinear_transform()
    test_inverse_transform()
    
    print("\n所有测试完成")


if __name__ == "__main__":
    main()
