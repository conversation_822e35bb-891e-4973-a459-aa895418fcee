[package]
name = "aqfh-rust-core"
version = "0.1.0"
edition = "2021"
authors = ["AQFH Team"]
description = "AQFH Rust Core - High-performance memory and consciousness system"
license = "MIT"

[lib]
name = "aqfh_rust_core"
crate-type = ["cdylib", "rlib"]

# 作为父级workspace的成员

[dependencies]
# PyO3 for Python bindings
pyo3 = { version = "0.24.0", features = ["extension-module", "abi3-py310"] }

# Arrow数据处理
arrow = { version = "55.1.0", features = ["prettyprint"] }
arrow-array = "55.1.0"
arrow-buffer = "55.1.0"
arrow-cast = "55.1.0"
arrow-data = "55.1.0"
arrow-schema = "55.1.0"
arrow-ipc = "55.1.0"
parquet = { version = "55.1.0", features = ["arrow", "async"] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 异步运行时
tokio = { version = "1.0", features = ["full"] }
tokio-util = { version = "0.7", features = ["codec"] }

# 日期时间
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 日志
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# 网络和IPC
unix-socket = "0.5"

# 数据结构
dashmap = "5.5"
parking_lot = "0.12"

# 配置
config = "0.14"
dirs = "5.0"

# 数学和科学计算
nalgebra = "0.32"
ndarray = "0.15"

# MCP协议 (我们先自己实现，后续可能有官方crate)
# mcp-sdk = "0.1"  # 待官方发布

[dev-dependencies]
tempfile = "3.0"
criterion = "0.5"

[[bin]]
name = "aqfh-daemon"
path = "src/bin/daemon.rs"

[[bin]]
name = "aqfh-mcp-server"
path = "src/bin/mcp_server.rs"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[dependencies.async-trait]
version = "0.1"
