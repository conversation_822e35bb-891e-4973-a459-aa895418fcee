[workspace]
resolver = "2"
members = [
    "rust_operators/fft_fusion",
    "rust_operators/transcendental_evolution",
    "rust_operators/game_scheduler",
    "rust_operators/nonlinear_interference",
    "rust_operators/fractal_routing",
    "src/interfaces",
    "src/transcendental_tensor",
]

[workspace.package]
version = "0.1.0"
edition = "2021"
authors = ["TTE Team"]
license = "MIT"

[workspace.dependencies]
pyo3 = { version = "0.24", features = ["extension-module"] }
numpy = "0.24"
ndarray = { version = "0.15.6", features = ["serde"] }
ndarray-rand = "0.14"
num-complex = "0.4"
rayon = "1.8"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.36", features = ["full"] }
futures = "0.3"
rand = "0.8"
rand_distr = "0.4"
thiserror = "1.0"
anyhow = "1.0"
log = "0.4"
env_logger = "0.11"
parking_lot = "0.12"
uuid = { version = "1.7", features = ["v4", "serde"] }
num_cpus = "1.16"
num-traits = "0.2"

[package]
name = "tte"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true

[dependencies]
pyo3.workspace = true
numpy.workspace = true
ndarray.workspace = true
num-complex.workspace = true
rayon.workspace = true
