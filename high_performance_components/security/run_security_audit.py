#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
零拷贝机制安全审计运行脚本

这个脚本运行所有安全审计工具，包括：
1. 代码安全审计
2. 配置安全检查
3. 安全漏洞扫描
4. 生成综合安全审计报告
"""

import os
import sys
import json
import argparse
import datetime
import subprocess
from pathlib import Path

def run_security_audit(source_dir, output_dir):
    """运行安全审计"""
    print("正在运行安全审计...")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 运行代码安全审计
    print("\n=== 运行代码安全审计 ===")
    code_audit_output = os.path.join(output_dir, "code_audit")
    os.makedirs(code_audit_output, exist_ok=True)
    
    try:
        subprocess.run(
            ["python", os.path.join(os.path.dirname(os.path.abspath(__file__)), "security_audit.py"), "--source", source_dir, "--output", code_audit_output],
            check=True
        )
        print("代码安全审计完成")
    except subprocess.CalledProcessError as e:
        print(f"代码安全审计失败: {e}")
    
    # 运行配置安全检查
    print("\n=== 运行配置安全检查 ===")
    config_check_output = os.path.join(output_dir, "config_check")
    os.makedirs(config_check_output, exist_ok=True)
    
    try:
        subprocess.run(
            ["python", os.path.join(os.path.dirname(os.path.abspath(__file__)), "config_checker.py"), "--config", source_dir, "--output", config_check_output],
            check=True
        )
        print("配置安全检查完成")
    except subprocess.CalledProcessError as e:
        print(f"配置安全检查失败: {e}")
    
    # 运行安全漏洞扫描
    print("\n=== 运行安全漏洞扫描 ===")
    vulnerability_scan_output = os.path.join(output_dir, "vulnerability_scan")
    os.makedirs(vulnerability_scan_output, exist_ok=True)
    
    try:
        subprocess.run(
            ["python", os.path.join(os.path.dirname(os.path.abspath(__file__)), "vulnerability_scanner.py"), "--source", source_dir, "--output", vulnerability_scan_output],
            check=True
        )
        print("安全漏洞扫描完成")
    except subprocess.CalledProcessError as e:
        print(f"安全漏洞扫描失败: {e}")
    
    # 生成综合安全审计报告
    print("\n=== 生成综合安全审计报告 ===")
    generate_comprehensive_report(source_dir, output_dir, code_audit_output, config_check_output, vulnerability_scan_output)
    print("综合安全审计报告生成完成")
    
    print("\n安全审计完成")

def generate_comprehensive_report(source_dir, output_dir, code_audit_output, config_check_output, vulnerability_scan_output):
    """生成综合安全审计报告"""
    # 加载各个报告
    code_audit_report = {}
    config_check_report = {}
    vulnerability_scan_report = {}
    
    try:
        with open(os.path.join(code_audit_output, "security_audit_report.json"), "r") as f:
            code_audit_report = json.load(f)
    except Exception as e:
        print(f"加载代码安全审计报告失败: {e}")
    
    try:
        with open(os.path.join(config_check_output, "config_security_report.json"), "r") as f:
            config_check_report = json.load(f)
    except Exception as e:
        print(f"加载配置安全检查报告失败: {e}")
    
    try:
        with open(os.path.join(vulnerability_scan_output, "vulnerability_report.json"), "r") as f:
            vulnerability_scan_report = json.load(f)
    except Exception as e:
        print(f"加载安全漏洞扫描报告失败: {e}")
    
    # 统计问题数量
    code_audit_issues = code_audit_report.get("total_issues", 0)
    config_check_issues = config_check_report.get("total_issues", 0)
    vulnerability_scan_issues = vulnerability_scan_report.get("total_vulnerabilities", 0)
    total_issues = code_audit_issues + config_check_issues + vulnerability_scan_issues
    
    # 统计严重程度
    severity_count = {
        "high": 0,
        "medium": 0,
        "low": 0
    }
    
    if "severity_count" in code_audit_report:
        for severity, count in code_audit_report["severity_count"].items():
            severity_count[severity] += count
    
    if "severity_count" in config_check_report:
        for severity, count in config_check_report["severity_count"].items():
            severity_count[severity] += count
    
    if "severity_count" in vulnerability_scan_report:
        for severity, count in vulnerability_scan_report["severity_count"].items():
            severity_count[severity] += count
    
    # 生成综合报告
    comprehensive_report = {
        "timestamp": datetime.datetime.now().isoformat(),
        "source_dir": source_dir,
        "total_issues": total_issues,
        "severity_count": severity_count,
        "code_audit_issues": code_audit_issues,
        "config_check_issues": config_check_issues,
        "vulnerability_scan_issues": vulnerability_scan_issues,
        "code_audit_report": code_audit_report,
        "config_check_report": config_check_report,
        "vulnerability_scan_report": vulnerability_scan_report
    }
    
    # 保存综合报告
    report_file = os.path.join(output_dir, "comprehensive_security_report.json")
    with open(report_file, "w") as f:
        json.dump(comprehensive_report, f, indent=2)
    
    # 生成HTML报告
    generate_html_report(comprehensive_report, output_dir)

def generate_html_report(report, output_dir):
    """生成HTML综合安全审计报告"""
    html_report_file = os.path.join(output_dir, "comprehensive_security_report.html")
    
    html = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>零拷贝机制综合安全审计报告</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .summary {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .summary-item {
            text-align: center;
            padding: 10px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            flex: 1;
            margin: 0 10px;
        }
        .summary-item.high {
            border-top: 3px solid #e74c3c;
        }
        .summary-item.medium {
            border-top: 3px solid #f39c12;
        }
        .summary-item.low {
            border-top: 3px solid #3498db;
        }
        .summary-item h3 {
            margin: 0;
            font-size: 1.2em;
        }
        .summary-item p {
            font-size: 2em;
            margin: 10px 0;
        }
        .report-links {
            margin-top: 20px;
        }
        .report-link {
            display: inline-block;
            margin-right: 10px;
            padding: 10px 15px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .report-link:hover {
            background-color: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>零拷贝机制综合安全审计报告</h1>
        <p>生成时间: """ + report["timestamp"] + """</p>
        <p>源代码目录: """ + report["source_dir"] + """</p>
        
        <div class="section">
            <h2>问题摘要</h2>
            <div class="summary">
                <div class="summary-item high">
                    <h3>高危问题</h3>
                    <p>""" + str(report["severity_count"]["high"]) + """</p>
                </div>
                <div class="summary-item medium">
                    <h3>中危问题</h3>
                    <p>""" + str(report["severity_count"]["medium"]) + """</p>
                </div>
                <div class="summary-item low">
                    <h3>低危问题</h3>
                    <p>""" + str(report["severity_count"]["low"]) + """</p>
                </div>
                <div class="summary-item">
                    <h3>总问题数</h3>
                    <p>""" + str(report["total_issues"]) + """</p>
                </div>
            </div>
            
            <h3>按审计类型统计</h3>
            <ul>
                <li>代码安全审计: """ + str(report["code_audit_issues"]) + """</li>
                <li>配置安全检查: """ + str(report["config_check_issues"]) + """</li>
                <li>安全漏洞扫描: """ + str(report["vulnerability_scan_issues"]) + """</li>
            </ul>
            
            <div class="report-links">
                <a href="code_audit/security_audit_report.html" class="report-link">查看代码安全审计报告</a>
                <a href="config_check/config_security_report.html" class="report-link">查看配置安全检查报告</a>
                <a href="vulnerability_scan/vulnerability_report.html" class="report-link">查看安全漏洞扫描报告</a>
            </div>
        </div>
        
        <div class="section">
            <h2>安全建议</h2>
            <p>根据安全审计结果，建议采取以下措施提高代码安全性：</p>
            <ol>
                <li>修复所有高危问题，特别是代码注入、内存安全和加密安全问题</li>
                <li>修复配置文件中的敏感信息泄露问题，使用环境变量或安全的密钥管理系统存储敏感信息</li>
                <li>修复代码中的安全漏洞，特别是SQL注入、命令注入和不安全的反序列化问题</li>
                <li>定期更新依赖项，修复已知的安全漏洞</li>
                <li>实施安全编码规范，避免常见的安全问题</li>
                <li>进行定期的安全审计，确保代码的安全性</li>
            </ol>
        </div>
    </div>
</body>
</html>
    """
    
    with open(html_report_file, "w") as f:
        f.write(html)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="零拷贝机制安全审计运行脚本")
    parser.add_argument("--source", "-s", default=".", help="源代码目录")
    parser.add_argument("--output", "-o", default="security_audit_results", help="输出目录")
    
    args = parser.parse_args()
    
    # 运行安全审计
    run_security_audit(args.source, args.output)

if __name__ == "__main__":
    main()
