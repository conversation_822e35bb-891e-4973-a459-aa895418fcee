#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
零拷贝机制安全漏洞扫描工具

这个脚本扫描代码中的安全漏洞，包括：
1. 代码注入漏洞
2. 内存安全漏洞
3. 并发安全漏洞
4. 加密安全漏洞
5. 错误处理漏洞
"""

import os
import sys
import re
import json
import argparse
import datetime
from pathlib import Path

class VulnerabilityScanner:
    """安全漏洞扫描类"""

    def __init__(self, source_dir, output_dir):
        """初始化漏洞扫描器"""
        self.source_dir = source_dir
        self.output_dir = output_dir
        self.vulnerabilities = []

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

    def scan_code(self):
        """扫描代码中的安全漏洞"""
        print("正在扫描代码中的安全漏洞...")

        # 收集源代码文件
        python_files = []
        rust_files = []
        cpp_files = []

        for root, dirs, files in os.walk(self.source_dir):
            for file in files:
                file_path = os.path.join(root, file)

                if file.endswith(".py"):
                    python_files.append(file_path)
                elif file.endswith(".rs"):
                    rust_files.append(file_path)
                elif file.endswith((".cpp", ".cc", ".h", ".hpp")):
                    cpp_files.append(file_path)

        # 扫描Python代码
        for file_path in python_files:
            self._scan_python_file(file_path)

        # 扫描Rust代码
        for file_path in rust_files:
            self._scan_rust_file(file_path)

        # 扫描C++代码
        for file_path in cpp_files:
            self._scan_cpp_file(file_path)

        print(f"代码扫描完成，发现{len(self.vulnerabilities)}个安全漏洞")

    def _scan_python_file(self, file_path):
        """扫描Python文件中的安全漏洞"""
        print(f"扫描Python文件: {file_path}")

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
                lines = content.split("\n")

            # 扫描代码注入漏洞
            self._scan_python_code_injection(file_path, content, lines)

            # 扫描加密安全漏洞
            self._scan_python_crypto_vulnerabilities(file_path, content, lines)

            # 扫描错误处理漏洞
            self._scan_python_error_handling(file_path, content, lines)

            # 扫描并发安全漏洞
            self._scan_python_concurrency(file_path, content, lines)
        except Exception as e:
            print(f"扫描Python文件时出错: {e}")

    def _scan_python_code_injection(self, file_path, content, lines):
        """扫描Python代码中的代码注入漏洞"""
        # 定义代码注入漏洞模式
        patterns = {
            "SQL注入": r"execute\(['\"].*?\%s.*?['\"].*?,\s*\(?([^)]*)\)?",
            "命令注入": r"(os\.system|subprocess\.call|subprocess\.Popen)\(['\"].*?\%s.*?['\"].*?,\s*\(?([^)]*)\)?",
            "不安全的反序列化": r"(pickle\.loads|yaml\.load|eval)\(",
            "不安全的模板字符串": r"(Template|render|format_string)\(['\"].*?\{.*?\}.*?['\"].*?,\s*\(?([^)]*)\)?",
        }

        for vuln_type, pattern in patterns.items():
            for match in re.finditer(pattern, content):
                line_number = content[:match.start()].count("\n") + 1
                line = lines[line_number - 1]

                self.vulnerabilities.append({
                    "file": file_path,
                    "line": line_number,
                    "code": line.strip(),
                    "type": vuln_type,
                    "severity": "high",
                    "description": f"发现{vuln_type}漏洞",
                    "recommendation": self._get_recommendation(vuln_type)
                })

    def _scan_python_crypto_vulnerabilities(self, file_path, content, lines):
        """扫描Python代码中的加密安全漏洞"""
        # 定义加密安全漏洞模式
        patterns = {
            "不安全的哈希算法": r"hashlib\.(md5|sha1)\(",
            "不安全的随机数": r"random\.(random|randint|choice|shuffle)",
            "硬编码密钥": r"(key|iv|salt)\s*=\s*['\"][0-9a-fA-F]{8,}['\"]",
            "不安全的密码学模式": r"(ECB|CBC)_MODE",
            "不安全的SSL验证": r"(verify|check_hostname)\s*=\s*False",
        }

        for vuln_type, pattern in patterns.items():
            for match in re.finditer(pattern, content):
                line_number = content[:match.start()].count("\n") + 1
                line = lines[line_number - 1]

                self.vulnerabilities.append({
                    "file": file_path,
                    "line": line_number,
                    "code": line.strip(),
                    "type": vuln_type,
                    "severity": "high" if vuln_type in ["不安全的哈希算法", "硬编码密钥", "不安全的SSL验证"] else "medium",
                    "description": f"发现{vuln_type}漏洞",
                    "recommendation": self._get_recommendation(vuln_type)
                })

    def _scan_python_error_handling(self, file_path, content, lines):
        """扫描Python代码中的错误处理漏洞"""
        # 定义错误处理漏洞模式
        patterns = {
            "空异常处理": r"except\s*:",
            "异常吞没": r"except\s+\w+(\s+as\s+\w+)?:\s*pass",
            "敏感信息泄露": r"(print|log|logger)\s*\(\s*.*?(exception|error|traceback)",
        }

        for vuln_type, pattern in patterns.items():
            for match in re.finditer(pattern, content):
                line_number = content[:match.start()].count("\n") + 1
                line = lines[line_number - 1]

                self.vulnerabilities.append({
                    "file": file_path,
                    "line": line_number,
                    "code": line.strip(),
                    "type": vuln_type,
                    "severity": "medium",
                    "description": f"发现{vuln_type}漏洞",
                    "recommendation": self._get_recommendation(vuln_type)
                })

    def _scan_python_concurrency(self, file_path, content, lines):
        """扫描Python代码中的并发安全漏洞"""
        # 定义并发安全漏洞模式
        patterns = {
            "不安全的线程同步": r"(threading|multiprocessing|concurrent\.futures)",
            "竞态条件": r"(global|nonlocal)\s+\w+",
        }

        # 检查是否使用了线程或进程
        uses_threading = re.search(r"(threading|multiprocessing|concurrent\.futures)", content)

        if uses_threading:
            # 检查是否使用了锁或其他同步机制
            uses_locks = re.search(r"(Lock|RLock|Semaphore|Condition|Event|Barrier|Queue)", content)

            if not uses_locks:
                self.vulnerabilities.append({
                    "file": file_path,
                    "line": None,
                    "code": None,
                    "type": "缺少线程同步",
                    "severity": "medium",
                    "description": "使用了线程或进程，但没有使用锁或其他同步机制",
                    "recommendation": "在多线程或多进程环境中使用锁或其他同步机制保护共享资源"
                })

        for vuln_type, pattern in patterns.items():
            for match in re.finditer(pattern, content):
                line_number = content[:match.start()].count("\n") + 1
                line = lines[line_number - 1]

                # 如果是全局变量，检查是否在线程中使用
                if vuln_type == "竞态条件" and uses_threading:
                    var_name = re.search(r"(global|nonlocal)\s+(\w+)", line)
                    if var_name:
                        var_name = var_name.group(2)
                        # 检查该变量是否在线程函数中使用
                        thread_funcs = re.finditer(r"def\s+(\w+)\s*\(", content)
                        for func_match in thread_funcs:
                            func_name = func_match.group(1)
                            # 检查该函数是否作为线程目标
                            if re.search(r"Thread\(.*?target\s*=\s*" + func_name, content):
                                # 检查该函数是否使用了该变量
                                func_start = func_match.start()
                                func_end = content.find("def ", func_start + 1)
                                if func_end == -1:
                                    func_end = len(content)
                                func_content = content[func_start:func_end]
                                if re.search(r"\b" + var_name + r"\b", func_content):
                                    self.vulnerabilities.append({
                                        "file": file_path,
                                        "line": line_number,
                                        "code": line.strip(),
                                        "type": vuln_type,
                                        "severity": "high",
                                        "description": f"发现{vuln_type}漏洞，全局变量'{var_name}'在线程中使用",
                                        "recommendation": self._get_recommendation(vuln_type)
                                    })

    def _scan_rust_file(self, file_path):
        """扫描Rust文件中的安全漏洞"""
        print(f"扫描Rust文件: {file_path}")

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
                lines = content.split("\n")

            # 扫描内存安全漏洞
            self._scan_rust_memory_safety(file_path, content, lines)

            # 扫描并发安全漏洞
            self._scan_rust_concurrency(file_path, content, lines)

            # 扫描加密安全漏洞
            self._scan_rust_crypto_vulnerabilities(file_path, content, lines)
        except Exception as e:
            print(f"扫描Rust文件时出错: {e}")

    def _scan_rust_memory_safety(self, file_path, content, lines):
        """扫描Rust代码中的内存安全漏洞"""
        # 定义内存安全漏洞模式
        patterns = {
            "不安全代码": r"unsafe\s*\{",
            "裸指针": r"\*(?:const|mut)\s+\w+",
            "内存泄漏": r"Box::into_raw|Vec::into_raw_parts",
            "未初始化内存": r"MaybeUninit|mem::uninitialized",
        }

        for vuln_type, pattern in patterns.items():
            for match in re.finditer(pattern, content):
                line_number = content[:match.start()].count("\n") + 1
                line = lines[line_number - 1]

                self.vulnerabilities.append({
                    "file": file_path,
                    "line": line_number,
                    "code": line.strip(),
                    "type": vuln_type,
                    "severity": "high" if vuln_type in ["不安全代码", "裸指针"] else "medium",
                    "description": f"发现{vuln_type}漏洞",
                    "recommendation": self._get_recommendation(vuln_type)
                })

    def _scan_rust_concurrency(self, file_path, content, lines):
        """扫描Rust代码中的并发安全漏洞"""
        # 定义并发安全漏洞模式
        patterns = {
            "数据竞争": r"static\s+mut\s+\w+",
            "死锁风险": r"(Mutex|RwLock).*?\.(lock|write)\(\).*?\.(lock|write)\(\)",
        }

        for vuln_type, pattern in patterns.items():
            for match in re.finditer(pattern, content):
                line_number = content[:match.start()].count("\n") + 1
                line = lines[line_number - 1]

                self.vulnerabilities.append({
                    "file": file_path,
                    "line": line_number,
                    "code": line.strip(),
                    "type": vuln_type,
                    "severity": "high",
                    "description": f"发现{vuln_type}漏洞",
                    "recommendation": self._get_recommendation(vuln_type)
                })

    def _scan_rust_crypto_vulnerabilities(self, file_path, content, lines):
        """扫描Rust代码中的加密安全漏洞"""
        # 定义加密安全漏洞模式
        patterns = {
            "不安全的哈希算法": r"(md5|sha1)::",
            "不安全的随机数": r"rand::(random|thread_rng)",
            "硬编码密钥": r"(key|iv|salt)\s*=\s*['\"][0-9a-fA-F]{8,}['\"]",
        }

        for vuln_type, pattern in patterns.items():
            for match in re.finditer(pattern, content):
                line_number = content[:match.start()].count("\n") + 1
                line = lines[line_number - 1]

                self.vulnerabilities.append({
                    "file": file_path,
                    "line": line_number,
                    "code": line.strip(),
                    "type": vuln_type,
                    "severity": "high" if vuln_type in ["不安全的哈希算法", "硬编码密钥"] else "medium",
                    "description": f"发现{vuln_type}漏洞",
                    "recommendation": self._get_recommendation(vuln_type)
                })

    def _scan_cpp_file(self, file_path):
        """扫描C++文件中的安全漏洞"""
        print(f"扫描C++文件: {file_path}")

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
                lines = content.split("\n")

            # 扫描内存安全漏洞
            self._scan_cpp_memory_safety(file_path, content, lines)

            # 扫描并发安全漏洞
            self._scan_cpp_concurrency(file_path, content, lines)

            # 扫描加密安全漏洞
            self._scan_cpp_crypto_vulnerabilities(file_path, content, lines)
        except Exception as e:
            print(f"扫描C++文件时出错: {e}")

    def _scan_cpp_memory_safety(self, file_path, content, lines):
        """扫描C++代码中的内存安全漏洞"""
        # 定义内存安全漏洞模式
        patterns = {
            "缓冲区溢出": r"(strcpy|strcat|sprintf|gets)\s*\(",
            "内存泄漏": r"new\s+\w+(?!\s*[\]\s]*\s*delete|\s*\w+\s*\()",
            "空指针解引用": r"(\w+\s*->\s*\w+|\*\s*\w+)(?!\s*\?\s*:|\s*if\s*\(\s*\w+\s*\)|\s*if\s*\(\s*\w+\s*!=\s*nullptr\s*\))",
            "释放后使用": r"(delete|free)\s+\w+.*?\w+",
            "整数溢出": r"(\w+\s*\+\+|\w+\s*\+=|\w+\s*=\s*\w+\s*\+)(?!\s*\/\/\s*overflow check)",
        }

        for vuln_type, pattern in patterns.items():
            for match in re.finditer(pattern, content):
                line_number = content[:match.start()].count("\n") + 1
                line = lines[line_number - 1]

                self.vulnerabilities.append({
                    "file": file_path,
                    "line": line_number,
                    "code": line.strip(),
                    "type": vuln_type,
                    "severity": "high",
                    "description": f"发现{vuln_type}漏洞",
                    "recommendation": self._get_recommendation(vuln_type)
                })

    def _scan_cpp_concurrency(self, file_path, content, lines):
        """扫描C++代码中的并发安全漏洞"""
        # 定义并发安全漏洞模式
        patterns = {
            "数据竞争": r"(static|global)\s+\w+(?!\s*\w+<\w+>\s*\w+)",
            "死锁风险": r"(mutex|lock).*?\.(lock|try_lock)\(\).*?\.(lock|try_lock)\(\)",
        }

        for vuln_type, pattern in patterns.items():
            for match in re.finditer(pattern, content):
                line_number = content[:match.start()].count("\n") + 1
                line = lines[line_number - 1]

                self.vulnerabilities.append({
                    "file": file_path,
                    "line": line_number,
                    "code": line.strip(),
                    "type": vuln_type,
                    "severity": "high",
                    "description": f"发现{vuln_type}漏洞",
                    "recommendation": self._get_recommendation(vuln_type)
                })

    def _scan_cpp_crypto_vulnerabilities(self, file_path, content, lines):
        """扫描C++代码中的加密安全漏洞"""
        # 定义加密安全漏洞模式
        patterns = {
            "不安全的哈希算法": r"(MD5|SHA1)(?!_HMAC)",
            "不安全的随机数": r"(rand|random)(?!_device)",
            "硬编码密钥": r"(key|iv|salt)\s*=\s*['\"][0-9a-fA-F]{8,}['\"]",
            "不安全的密码学模式": r"(ECB|CBC)_Mode",
        }

        for vuln_type, pattern in patterns.items():
            for match in re.finditer(pattern, content):
                line_number = content[:match.start()].count("\n") + 1
                line = lines[line_number - 1]

                self.vulnerabilities.append({
                    "file": file_path,
                    "line": line_number,
                    "code": line.strip(),
                    "type": vuln_type,
                    "severity": "high" if vuln_type in ["不安全的哈希算法", "硬编码密钥"] else "medium",
                    "description": f"发现{vuln_type}漏洞",
                    "recommendation": self._get_recommendation(vuln_type)
                })

    def _get_recommendation(self, vuln_type):
        """获取安全漏洞的建议"""
        recommendations = {
            # Python代码注入漏洞
            "SQL注入": "使用参数化查询或ORM框架，避免直接拼接SQL语句",
            "命令注入": "使用参数化命令或安全的API，避免直接拼接命令",
            "不安全的反序列化": "使用安全的反序列化方法，如json.loads()替换为json.loads()", # 安全修复：使用安全的反序列化方法
            "不安全的模板字符串": "使用安全的模板引擎，避免直接拼接模板字符串",

            # Python加密安全漏洞
            "不安全的哈希算法": "使用SHA-256或更安全的哈希算法",
            "不安全的随机数": "使用secrets模块生成安全的随机数",
            "硬编码密钥": "使用环境变量或安全的密钥管理系统存储密钥",
            "不安全的密码学模式": "使用GCM或其他AEAD模式",
            "不安全的SSL验证": "启用SSL验证，避免中间人攻击",

            # Python错误处理漏洞
            "空异常处理": "指定具体的异常类型，避免捕获所有异常",
            "异常吞没": "记录异常信息，避免异常吞没",
            "敏感信息泄露": "避免在错误消息中包含敏感信息",

            # Python并发安全漏洞
            "不安全的线程同步": "使用锁或其他同步机制保护共享资源",
            "竞态条件": "使用锁或其他同步机制保护共享资源",
            "缺少线程同步": "在多线程或多进程环境中使用锁或其他同步机制保护共享资源",

            # Rust内存安全漏洞
            "不安全代码": "避免使用unsafe代码，使用安全的Rust API",
            "裸指针": "避免使用裸指针，使用引用或智能指针",
            "内存泄漏": "使用RAII模式，避免手动管理内存",
            "未初始化内存": "使用安全的初始化方法，避免使用未初始化内存",

            # Rust并发安全漏洞
            "数据竞争": "使用Mutex或RwLock保护共享数据",
            "死锁风险": "避免嵌套锁，使用一致的锁顺序",

            # Rust加密安全漏洞
            # 与Python相同

            # C++内存安全漏洞
            "缓冲区溢出": "使用安全的字符串函数，如strncpy代替strcpy",
            "内存泄漏": "使用智能指针，如std::unique_ptr或std::shared_ptr",
            "空指针解引用": "在解引用前检查指针是否为空",
            "释放后使用": "避免使用已释放的内存",
            "整数溢出": "使用安全的整数类型，如std::int32_t",

            # C++并发安全漏洞
            "数据竞争": "使用std::mutex保护共享数据",
            "死锁风险": "避免嵌套锁，使用一致的锁顺序",

            # C++加密安全漏洞
            # 与Python相同
        }

        return recommendations.get(vuln_type, "修复安全漏洞")

    def generate_report(self):
        """生成安全漏洞报告"""
        print("正在生成安全漏洞报告...")

        # 按严重程度统计漏洞
        severity_count = {"high": 0, "medium": 0, "low": 0}
        for vuln in self.vulnerabilities:
            severity_count[vuln["severity"]] += 1

        # 按类型统计漏洞
        type_count = {}
        for vuln in self.vulnerabilities:
            vuln_type = vuln["type"]
            if vuln_type not in type_count:
                type_count[vuln_type] = 0
            type_count[vuln_type] += 1

        # 按文件统计漏洞
        file_count = {}
        for vuln in self.vulnerabilities:
            file_path = vuln["file"]
            if file_path not in file_count:
                file_count[file_path] = 0
            file_count[file_path] += 1

        # 生成报告
        report = {
            "timestamp": datetime.datetime.now().isoformat(),
            "source_dir": self.source_dir,
            "total_vulnerabilities": len(self.vulnerabilities),
            "severity_count": severity_count,
            "type_count": type_count,
            "file_count": file_count,
            "vulnerabilities": self.vulnerabilities
        }

        # 保存报告
        report_file = os.path.join(self.output_dir, "vulnerability_report.json")
        with open(report_file, "w") as f:
            json.dump(report, f, indent=2)

        # 生成HTML报告
        self._generate_html_report(report)

        print(f"安全漏洞报告已生成: {report_file}")
        print(f"总漏洞数: {len(self.vulnerabilities)}")
        print(f"高危漏洞: {severity_count['high']}")
        print(f"中危漏洞: {severity_count['medium']}")
        print(f"低危漏洞: {severity_count['low']}")

    def _generate_html_report(self, report):
        """生成HTML安全漏洞报告"""
        html_report_file = os.path.join(self.output_dir, "vulnerability_report.html")

        html = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>零拷贝机制安全漏洞报告</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .summary {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .summary-item {
            text-align: center;
            padding: 10px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            flex: 1;
            margin: 0 10px;
        }
        .summary-item.high {
            border-top: 3px solid #e74c3c;
        }
        .summary-item.medium {
            border-top: 3px solid #f39c12;
        }
        .summary-item.low {
            border-top: 3px solid #3498db;
        }
        .summary-item h3 {
            margin: 0;
            font-size: 1.2em;
        }
        .summary-item p {
            font-size: 2em;
            margin: 10px 0;
        }
        .vulnerability {
            margin-bottom: 10px;
            padding: 15px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .vulnerability.high {
            border-left: 5px solid #e74c3c;
        }
        .vulnerability.medium {
            border-left: 5px solid #f39c12;
        }
        .vulnerability.low {
            border-left: 5px solid #3498db;
        }
        .vulnerability-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .vulnerability-title {
            font-weight: bold;
        }
        .vulnerability-severity {
            padding: 2px 5px;
            border-radius: 3px;
            color: white;
            font-size: 0.8em;
        }
        .vulnerability-severity.high {
            background-color: #e74c3c;
        }
        .vulnerability-severity.medium {
            background-color: #f39c12;
        }
        .vulnerability-severity.low {
            background-color: #3498db;
        }
        .vulnerability-details {
            margin-bottom: 10px;
        }
        .vulnerability-file {
            font-family: monospace;
            margin-bottom: 5px;
        }
        .vulnerability-code {
            font-family: monospace;
            background-color: #f5f5f5;
            padding: 5px;
            border-radius: 3px;
            margin-bottom: 10px;
            white-space: pre;
            overflow-x: auto;
        }
        .vulnerability-description {
            margin-bottom: 10px;
        }
        .vulnerability-recommendation {
            background-color: #eafaf1;
            padding: 10px;
            border-radius: 3px;
            border-left: 3px solid #27ae60;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>零拷贝机制安全漏洞报告</h1>
        <p>生成时间: """ + report["timestamp"] + """</p>
        <p>源代码目录: """ + report["source_dir"] + """</p>

        <div class="section">
            <h2>漏洞摘要</h2>
            <div class="summary">
                <div class="summary-item high">
                    <h3>高危漏洞</h3>
                    <p>""" + str(report["severity_count"]["high"]) + """</p>
                </div>
                <div class="summary-item medium">
                    <h3>中危漏洞</h3>
                    <p>""" + str(report["severity_count"]["medium"]) + """</p>
                </div>
                <div class="summary-item low">
                    <h3>低危漏洞</h3>
                    <p>""" + str(report["severity_count"]["low"]) + """</p>
                </div>
                <div class="summary-item">
                    <h3>总漏洞数</h3>
                    <p>""" + str(report["total_vulnerabilities"]) + """</p>
                </div>
            </div>

            <h3>按类型统计</h3>
            <ul>
        """

        for vuln_type, count in report["type_count"].items():
            html += f"<li>{vuln_type}: {count}</li>"

        html += """
            </ul>

            <h3>按文件统计</h3>
            <ul>
        """

        for file_path, count in report["file_count"].items():
            html += f"<li>{file_path}: {count}</li>"

        html += """
            </ul>
        </div>

        <div class="section">
            <h2>漏洞详情</h2>
        """

        # 按严重程度排序漏洞
        sorted_vulnerabilities = sorted(report["vulnerabilities"], key=lambda x: {"high": 0, "medium": 1, "low": 2}[x["severity"]])

        for vuln in sorted_vulnerabilities:
            html += f"""
            <div class="vulnerability {vuln['severity']}">
                <div class="vulnerability-header">
                    <div class="vulnerability-title">{vuln['type']}</div>
                    <div class="vulnerability-severity {vuln['severity']}">{vuln['severity']}</div>
                </div>
                <div class="vulnerability-details">
                    <div class="vulnerability-file">{vuln['file']}{':' + str(vuln['line']) if vuln['line'] else ''}</div>
                    {f'<div class="vulnerability-code">{vuln["code"]}</div>' if vuln['code'] else ''}
                </div>
                <div class="vulnerability-description">{vuln['description']}</div>
                <div class="vulnerability-recommendation">{vuln['recommendation']}</div>
            </div>
            """

        html += """
        </div>
    </div>
</body>
</html>
        """

        with open(html_report_file, "w") as f:
            f.write(html)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="零拷贝机制安全漏洞扫描工具")
    parser.add_argument("--source", "-s", default=".", help="源代码目录")
    parser.add_argument("--output", "-o", default="security_vulnerabilities", help="输出目录")
    parser.add_argument("--skip-python", action="store_true", help="跳过Python代码扫描")
    parser.add_argument("--skip-rust", action="store_true", help="跳过Rust代码扫描")
    parser.add_argument("--skip-cpp", action="store_true", help="跳过C++代码扫描")

    args = parser.parse_args()

    # 创建漏洞扫描器
    scanner = VulnerabilityScanner(args.source, args.output)

    # 扫描代码
    scanner.scan_code()

    # 生成报告
    scanner.generate_report()

if __name__ == "__main__":
    main()