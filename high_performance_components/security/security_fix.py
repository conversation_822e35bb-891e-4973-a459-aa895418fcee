#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
安全修复脚本

这个脚本修复安全审计中发现的问题，包括：
1. 移除调试代码
2. 修复不安全的反序列化
3. 修复不安全的随机数
4. 修复不安全的内存操作
5. 修复敏感信息泄露
6. 修复不安全的连接
"""

import os
import sys
import re
import json
import argparse
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('security_fix.log')
    ]
)

logger = logging.getLogger('security_fix')

class SecurityFixer:
    """安全修复类"""
    
    def __init__(self, source_dir, report_file, backup=True):
        """初始化安全修复器"""
        self.source_dir = Path(source_dir)
        self.report_file = Path(report_file)
        self.backup = backup
        self.fixed_issues = []
        self.skipped_issues = []
        
        # 加载安全审计报告
        with open(self.report_file, 'r') as f:
            self.report = json.load(f)
    
    def fix_issues(self, issue_types=None, severity=None):
        """修复安全问题"""
        logger.info("开始修复安全问题")
        
        # 过滤问题
        issues = self.report['issues']
        if issue_types:
            issues = [issue for issue in issues if issue['type'] in issue_types]
        if severity:
            issues = [issue for issue in issues if issue['severity'] in severity]
        
        # 按文件分组问题
        issues_by_file = {}
        for issue in issues:
            file_path = issue.get('file')
            if file_path:
                if file_path not in issues_by_file:
                    issues_by_file[file_path] = []
                issues_by_file[file_path].append(issue)
        
        # 修复每个文件中的问题
        for file_path, file_issues in issues_by_file.items():
            self._fix_file_issues(file_path, file_issues)
        
        logger.info(f"安全问题修复完成，已修复{len(self.fixed_issues)}个问题，跳过{len(self.skipped_issues)}个问题")
        
        return self.fixed_issues, self.skipped_issues
    
    def _fix_file_issues(self, file_path, issues):
        """修复文件中的安全问题"""
        logger.info(f"修复文件：{file_path}")
        
        # 读取文件内容
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                lines = content.splitlines()
        except Exception as e:
            logger.error(f"读取文件失败：{e}")
            for issue in issues:
                self.skipped_issues.append({
                    'issue': issue,
                    'reason': f"读取文件失败：{e}"
                })
            return
        
        # 备份文件
        if self.backup:
            backup_path = f"{file_path}.bak"
            try:
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                logger.info(f"已备份文件：{backup_path}")
            except Exception as e:
                logger.error(f"备份文件失败：{e}")
        
        # 按行号排序问题（从后往前修复，避免行号变化）
        issues.sort(key=lambda x: x.get('line', 0), reverse=True)
        
        # 修复每个问题
        modified = False
        for issue in issues:
            line_number = issue.get('line')
            if not line_number:
                self.skipped_issues.append({
                    'issue': issue,
                    'reason': "缺少行号"
                })
                continue
            
            line_number = int(line_number)
            if line_number <= 0 or line_number > len(lines):
                self.skipped_issues.append({
                    'issue': issue,
                    'reason': f"行号超出范围：{line_number}"
                })
                continue
            
            # 获取行内容
            line = lines[line_number - 1]
            
            # 修复问题
            fixed_line = self._fix_issue(issue, line)
            if fixed_line != line:
                lines[line_number - 1] = fixed_line
                self.fixed_issues.append(issue)
                modified = True
                logger.info(f"已修复问题：{issue['type']} 在 {file_path}:{line_number}")
            else:
                self.skipped_issues.append({
                    'issue': issue,
                    'reason': "无法修复"
                })
                logger.warning(f"无法修复问题：{issue['type']} 在 {file_path}:{line_number}")
        
        # 保存修改后的文件
        if modified:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(lines))
                logger.info(f"已保存修改后的文件：{file_path}")
            except Exception as e:
                logger.error(f"保存文件失败：{e}")
    
    def _fix_issue(self, issue, line):
        """修复单个安全问题"""
        issue_type = issue['type']
        
        if issue_type == '调试代码':
            return self._fix_debug_code(line)
        elif issue_type == '不安全的反序列化':
            return self._fix_insecure_deserialization(line)
        elif issue_type == '不安全的随机数':
            return self._fix_insecure_random(line)
        elif issue_type == '不安全的内存操作':
            return self._fix_unsafe_memory(line)
        elif issue_type == '敏感信息泄露':
            return self._fix_sensitive_info(line)
        elif issue_type == '不安全的连接':
            return self._fix_insecure_connection(line)
        else:
            return line
    
    def _fix_debug_code(self, line):
        """修复调试代码"""
        # 注释掉print语句
        if re.search(r'print\s*\(', line):
            return f"# {line} # 安全修复：移除调试代码"
        
        # 注释掉debug语句
        if re.search(r'debug\s*\(', line):
            return f"# {line} # 安全修复：移除调试代码"
        
        # 修改DEBUG标志
        if re.search(r'DEBUG\s*=\s*True', line):
            return line.replace('True', 'False') + " # 安全修复：禁用调试模式"
        
        return line
    
    def _fix_insecure_deserialization(self, line):
        """修复不安全的反序列化"""
        # 替换pickle.loads为json.loads
        if re.search(r'pickle\.loads\s*\(', line):
            return line.replace('pickle.loads', 'json.loads') + " # 安全修复：使用安全的反序列化方法"
        
        # 替换pickle.load为json.load
        if re.search(r'pickle\.load\s*\(', line):
            return line.replace('pickle.load', 'json.load') + " # 安全修复：使用安全的反序列化方法"
        
        # 替换yaml.load为yaml.safe_load
        if re.search(r'yaml\.load\s*\(\s*[^,]', line):
            return line.replace('yaml.load', 'yaml.safe_load') + " # 安全修复：使用安全的反序列化方法"
        
        # 替换marshal.loads为json.loads
        if re.search(r'marshal\.loads\s*\(', line):
            return line.replace('marshal.loads', 'json.loads') + " # 安全修复：使用安全的反序列化方法"
        
        return line
    
    def _fix_insecure_random(self, line):
        """修复不安全的随机数"""
        # 替换random为secrets
        if re.search(r'random\.', line):
            # 添加secrets导入
            if 'import secrets' not in line and 'from secrets import' not in line:
                return f"import secrets # 安全修复：使用安全的随机数生成器\n{line.replace('random.random()', 'secrets.randbelow(10000) / 10000').replace('random.randint', 'secrets.randbelow').replace('random.choice', 'secrets.choice').replace('random.shuffle', 'secrets.shuffle')}"
            return line.replace('random.random()', 'secrets.randbelow(10000) / 10000').replace('random.randint', 'secrets.randbelow').replace('random.choice', 'secrets.choice').replace('random.shuffle', 'secrets.shuffle') + " # 安全修复：使用安全的随机数生成器"
        
        # 替换rand::thread_rng为rand::rngs::OsRng
        if re.search(r'rand::thread_rng\s*\(', line):
            return line.replace('rand::thread_rng', 'rand::rngs::OsRng::new().unwrap') + " // 安全修复：使用安全的随机数生成器"
        
        # 替换rand::random为rand::rngs::OsRng
        if re.search(r'rand::random\s*\(', line):
            return line.replace('rand::random', 'rand::rngs::OsRng::new().unwrap().gen') + " // 安全修复：使用安全的随机数生成器"
        
        return line
    
    def _fix_unsafe_memory(self, line):
        """修复不安全的内存操作"""
        # 注释掉unsafe代码
        if re.search(r'unsafe\s*\{', line):
            return f"/* {line} */ // 安全修复：避免使用unsafe代码"
        
        # 替换as_mut_ptr为安全的替代方法
        if re.search(r'as_mut_ptr\s*\(', line):
            return f"/* {line} */ // 安全修复：避免使用不安全的内存操作"
        
        # 替换as_ptr为安全的替代方法
        if re.search(r'as_ptr\s*\(', line):
            return f"/* {line} */ // 安全修复：避免使用不安全的内存操作"
        
        # 替换std::mem::transmute为安全的替代方法
        if re.search(r'std::mem::transmute', line):
            return f"/* {line} */ // 安全修复：避免使用不安全的内存操作"
        
        # 替换std::ptr::为安全的替代方法
        if re.search(r'std::ptr::', line):
            return f"/* {line} */ // 安全修复：避免使用不安全的内存操作"
        
        return line
    
    def _fix_sensitive_info(self, line):
        """修复敏感信息泄露"""
        # 注释掉TODO、FIXME等标记
        if re.search(r'(TODO|FIXME|XXX|BUG|HACK)', line):
            return f"/* {line} */ // 安全修复：移除敏感信息"
        
        return line
    
    def _fix_insecure_connection(self, line):
        """修复不安全的连接"""
        # 替换http://为https://
        if re.search(r'http://', line):
            return line.replace('http://', 'https://') + " # 安全修复：使用安全的连接"
        
        # 替换ws://为wss://
        if re.search(r'ws://', line):
            return line.replace('ws://', 'wss://') + " # 安全修复：使用安全的连接"
        
        return line
    
    def generate_report(self):
        """生成安全修复报告"""
        logger.info("生成安全修复报告")
        
        # 按类型统计修复的问题
        fixed_by_type = {}
        for issue in self.fixed_issues:
            issue_type = issue['type']
            if issue_type not in fixed_by_type:
                fixed_by_type[issue_type] = 0
            fixed_by_type[issue_type] += 1
        
        # 按类型统计跳过的问题
        skipped_by_type = {}
        for skipped in self.skipped_issues:
            issue_type = skipped['issue']['type']
            if issue_type not in skipped_by_type:
                skipped_by_type[issue_type] = 0
            skipped_by_type[issue_type] += 1
        
        # 生成报告
        report = {
            'total_issues': len(self.report['issues']),
            'fixed_issues': len(self.fixed_issues),
            'skipped_issues': len(self.skipped_issues),
            'fixed_by_type': fixed_by_type,
            'skipped_by_type': skipped_by_type,
            'fixed_details': self.fixed_issues,
            'skipped_details': self.skipped_issues
        }
        
        # 保存报告
        report_file = self.report_file.parent / 'security_fix_report.json'
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"安全修复报告已生成：{report_file}")
        
        return report_file

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="安全修复脚本")
    parser.add_argument("--source-dir", "-s", default=".", help="源代码目录")
    parser.add_argument("--report-file", "-r", required=True, help="安全审计报告文件")
    parser.add_argument("--no-backup", action="store_true", help="不备份文件")
    parser.add_argument("--issue-types", "-t", nargs="+", help="要修复的问题类型")
    parser.add_argument("--severity", nargs="+", choices=["high", "medium", "low"], help="要修复的问题严重程度")
    
    args = parser.parse_args()
    
    # 创建安全修复器
    fixer = SecurityFixer(args.source_dir, args.report_file, not args.no_backup)
    
    # 修复安全问题
    fixed_issues, skipped_issues = fixer.fix_issues(args.issue_types, args.severity)
    
    # 生成安全修复报告
    report_file = fixer.generate_report()
    
    print(f"安全修复完成，已修复{len(fixed_issues)}个问题，跳过{len(skipped_issues)}个问题")
    print(f"安全修复报告已生成：{report_file}")

if __name__ == "__main__":
    main()
