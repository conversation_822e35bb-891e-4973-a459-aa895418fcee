#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
零拷贝机制安全配置检查工具

这个脚本检查配置文件的安全性，包括：
1. 检查硬编码的敏感信息
2. 检查不安全的连接
3. 检查调试模式
4. 检查权限设置
5. 生成安全配置报告
"""

import os
import sys
import re
import json
import yaml
import configparser
import argparse
import datetime
from pathlib import Path

class ConfigChecker:
    """配置检查类"""
    
    def __init__(self, config_dir, output_dir):
        """初始化配置检查器"""
        self.config_dir = config_dir
        self.output_dir = output_dir
        self.issues = []
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
    
    def check_configs(self):
        """检查配置文件"""
        print("正在检查配置文件...")
        
        # 查找配置文件
        config_files = []
        for root, dirs, files in os.walk(self.config_dir):
            for file in files:
                if file.endswith((".json", ".yaml", ".yml", ".toml", ".ini", ".conf", ".config", ".env")):
                    config_files.append(os.path.join(root, file))
        
        # 检查每个配置文件
        for config_file in config_files:
            self._check_config_file(config_file)
        
        print(f"配置检查完成，发现{len(self.issues)}个问题")
    
    def _check_config_file(self, config_file):
        """检查单个配置文件"""
        print(f"检查配置文件: {config_file}")
        
        # 根据文件扩展名选择解析方法
        if config_file.endswith((".json")):
            self._check_json_config(config_file)
        elif config_file.endswith((".yaml", ".yml")):
            self._check_yaml_config(config_file)
        elif config_file.endswith((".toml")):
            self._check_toml_config(config_file)
        elif config_file.endswith((".ini", ".conf", ".config")):
            self._check_ini_config(config_file)
        elif config_file.endswith((".env")):
            self._check_env_config(config_file)
        else:
            self._check_generic_config(config_file)
    
    def _check_json_config(self, config_file):
        """检查JSON配置文件"""
        try:
            with open(config_file, "r", encoding="utf-8") as f:
                config = json.load(f)
            
            # 检查配置
            self._check_config_dict(config, config_file)
        except Exception as e:
            self.issues.append({
                "file": config_file,
                "line": None,
                "code": None,
                "type": "解析错误",
                "severity": "medium",
                "description": f"无法解析JSON配置文件: {str(e)}",
                "recommendation": "确保配置文件是有效的JSON格式"
            })
    
    def _check_yaml_config(self, config_file):
        """检查YAML配置文件"""
        try:
            with open(config_file, "r", encoding="utf-8") as f:
                config = yaml.safe_load(f)
            
            # 检查配置
            self._check_config_dict(config, config_file)
        except Exception as e:
            self.issues.append({
                "file": config_file,
                "line": None,
                "code": None,
                "type": "解析错误",
                "severity": "medium",
                "description": f"无法解析YAML配置文件: {str(e)}",
                "recommendation": "确保配置文件是有效的YAML格式"
            })
    
    def _check_toml_config(self, config_file):
        """检查TOML配置文件"""
        try:
            import toml
            with open(config_file, "r", encoding="utf-8") as f:
                config = toml.load(f)
            
            # 检查配置
            self._check_config_dict(config, config_file)
        except ImportError:
            self.issues.append({
                "file": config_file,
                "line": None,
                "code": None,
                "type": "依赖项缺失",
                "severity": "low",
                "description": "缺少toml模块，无法解析TOML配置文件",
                "recommendation": "安装toml模块: pip install toml"
            })
        except Exception as e:
            self.issues.append({
                "file": config_file,
                "line": None,
                "code": None,
                "type": "解析错误",
                "severity": "medium",
                "description": f"无法解析TOML配置文件: {str(e)}",
                "recommendation": "确保配置文件是有效的TOML格式"
            })
    
    def _check_ini_config(self, config_file):
        """检查INI配置文件"""
        try:
            config = configparser.ConfigParser()
            config.read(config_file)
            
            # 检查每个部分
            for section in config.sections():
                for key, value in config[section].items():
                    self._check_config_value(key, value, config_file, section)
        except Exception as e:
            self.issues.append({
                "file": config_file,
                "line": None,
                "code": None,
                "type": "解析错误",
                "severity": "medium",
                "description": f"无法解析INI配置文件: {str(e)}",
                "recommendation": "确保配置文件是有效的INI格式"
            })
    
    def _check_env_config(self, config_file):
        """检查ENV配置文件"""
        try:
            with open(config_file, "r", encoding="utf-8") as f:
                for line_number, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # 跳过注释和空行
                    if not line or line.startswith("#"):
                        continue
                    
                    # 解析键值对
                    if "=" in line:
                        key, value = line.split("=", 1)
                        key = key.strip()
                        value = value.strip()
                        
                        # 检查配置值
                        self._check_config_value(key, value, config_file, None, line_number, line)
        except Exception as e:
            self.issues.append({
                "file": config_file,
                "line": None,
                "code": None,
                "type": "解析错误",
                "severity": "medium",
                "description": f"无法解析ENV配置文件: {str(e)}",
                "recommendation": "确保配置文件是有效的ENV格式"
            })
    
    def _check_generic_config(self, config_file):
        """检查通用配置文件"""
        try:
            with open(config_file, "r", encoding="utf-8") as f:
                content = f.read()
            
            # 定义安全问题模式
            patterns = {
                "硬编码密码": r"(password|passwd)\s*[=:]\s*['\"]?[^'\"]+['\"]?",
                "硬编码密钥": r"(api_key|secret_key|access_key)\s*[=:]\s*['\"]?[^'\"]+['\"]?",
                "不安全的连接": r"(http://|ws://)",
                "调试模式": r"(debug|development)\s*[=:]\s*(true|1|yes)",
                "敏感信息": r"(private_key|certificate)\s*[=:]\s*['\"]?[^'\"]+['\"]?",
            }
            
            for issue_type, pattern in patterns.items():
                for match in re.finditer(pattern, content):
                    line_number = content[:match.start()].count("\n") + 1
                    line = content.split("\n")[line_number - 1].strip()
                    
                    self.issues.append({
                        "file": config_file,
                        "line": line_number,
                        "code": line,
                        "type": issue_type,
                        "severity": "high" if issue_type in ["硬编码密码", "硬编码密钥", "敏感信息"] else "medium",
                        "description": f"配置文件中发现{issue_type}问题",
                        "recommendation": self._get_recommendation(issue_type)
                    })
        except Exception as e:
            self.issues.append({
                "file": config_file,
                "line": None,
                "code": None,
                "type": "解析错误",
                "severity": "medium",
                "description": f"无法解析配置文件: {str(e)}",
                "recommendation": "确保配置文件格式正确"
            })
    
    def _check_config_dict(self, config, config_file, prefix=""):
        """递归检查配置字典"""
        if isinstance(config, dict):
            for key, value in config.items():
                full_key = f"{prefix}.{key}" if prefix else key
                
                # 检查配置值
                if isinstance(value, (str, int, float, bool)):
                    self._check_config_value(full_key, str(value), config_file)
                
                # 递归检查嵌套字典
                elif isinstance(value, dict):
                    self._check_config_dict(value, config_file, full_key)
                
                # 检查列表中的字典
                elif isinstance(value, list):
                    for i, item in enumerate(value):
                        if isinstance(item, dict):
                            self._check_config_dict(item, config_file, f"{full_key}[{i}]")
    
    def _check_config_value(self, key, value, config_file, section=None, line_number=None, line=None):
        """检查配置值"""
        # 检查硬编码的敏感信息
        if re.search(r"password|passwd", key, re.IGNORECASE) and not re.search(r"\$\{.*?\}", value):
            self.issues.append({
                "file": config_file,
                "line": line_number,
                "code": line or f"{key} = {value}",
                "type": "硬编码密码",
                "severity": "high",
                "description": f"配置文件中发现硬编码密码: {key}",
                "recommendation": "使用环境变量或安全的密钥管理系统存储密码"
            })
        
        if re.search(r"api_key|secret_key|access_key", key, re.IGNORECASE) and not re.search(r"\$\{.*?\}", value):
            self.issues.append({
                "file": config_file,
                "line": line_number,
                "code": line or f"{key} = {value}",
                "type": "硬编码密钥",
                "severity": "high",
                "description": f"配置文件中发现硬编码密钥: {key}",
                "recommendation": "使用环境变量或安全的密钥管理系统存储密钥"
            })
        
        # 检查不安全的连接
        if re.search(r"url|endpoint|host", key, re.IGNORECASE) and re.search(r"http://|ws://", value):
            self.issues.append({
                "file": config_file,
                "line": line_number,
                "code": line or f"{key} = {value}",
                "type": "不安全的连接",
                "severity": "medium",
                "description": f"配置文件中发现不安全的连接: {key}",
                "recommendation": "使用HTTPS或WSS代替HTTP或WS"
            })
        
        # 检查调试模式
        if re.search(r"debug|development", key, re.IGNORECASE) and re.search(r"true|1|yes", value, re.IGNORECASE):
            self.issues.append({
                "file": config_file,
                "line": line_number,
                "code": line or f"{key} = {value}",
                "type": "调试模式",
                "severity": "medium",
                "description": f"配置文件中启用了调试模式: {key}",
                "recommendation": "在生产环境中禁用调试模式"
            })
        
        # 检查敏感信息
        if re.search(r"private_key|certificate", key, re.IGNORECASE) and not re.search(r"\$\{.*?\}", value):
            self.issues.append({
                "file": config_file,
                "line": line_number,
                "code": line or f"{key} = {value}",
                "type": "敏感信息",
                "severity": "high",
                "description": f"配置文件中发现敏感信息: {key}",
                "recommendation": "使用环境变量或安全的密钥管理系统存储敏感信息"
            })
    
    def _get_recommendation(self, issue_type):
        """获取安全问题的建议"""
        recommendations = {
            "硬编码密码": "使用环境变量或安全的密钥管理系统存储密码",
            "硬编码密钥": "使用环境变量或安全的密钥管理系统存储密钥",
            "不安全的连接": "使用HTTPS或WSS代替HTTP或WS",
            "调试模式": "在生产环境中禁用调试模式",
            "敏感信息": "使用环境变量或安全的密钥管理系统存储敏感信息",
        }
        
        return recommendations.get(issue_type, "修复安全问题")
    
    def check_permissions(self):
        """检查权限设置"""
        print("正在检查权限设置...")
        
        # 查找配置文件
        config_files = []
        for root, dirs, files in os.walk(self.config_dir):
            for file in files:
                if file.endswith((".json", ".yaml", ".yml", ".toml", ".ini", ".conf", ".config", ".env")):
                    config_files.append(os.path.join(root, file))
        
        # 检查每个配置文件的权限
        for config_file in config_files:
            self._check_file_permissions(config_file)
        
        print(f"权限检查完成，发现{len(self.issues)}个问题")
    
    def _check_file_permissions(self, file_path):
        """检查文件权限"""
        # 获取文件权限
        try:
            file_stat = os.stat(file_path)
            file_mode = file_stat.st_mode
            
            # 检查文件是否对组或其他用户可写
            if file_mode & 0o022:
                self.issues.append({
                    "file": file_path,
                    "line": None,
                    "code": None,
                    "type": "不安全的文件权限",
                    "severity": "high",
                    "description": f"配置文件对组或其他用户可写: {oct(file_mode & 0o777)}",
                    "recommendation": "修改文件权限，确保只有所有者可写: chmod 600 " + file_path
                })
            
            # 检查文件是否对其他用户可读
            if file_mode & 0o004:
                self.issues.append({
                    "file": file_path,
                    "line": None,
                    "code": None,
                    "type": "不安全的文件权限",
                    "severity": "medium",
                    "description": f"配置文件对其他用户可读: {oct(file_mode & 0o777)}",
                    "recommendation": "修改文件权限，确保只有所有者和组可读: chmod 640 " + file_path
                })
        except Exception as e:
            self.issues.append({
                "file": file_path,
                "line": None,
                "code": None,
                "type": "权限检查错误",
                "severity": "low",
                "description": f"无法检查文件权限: {str(e)}",
                "recommendation": "确保有足够的权限检查文件权限"
            })
    
    def generate_report(self):
        """生成安全配置报告"""
        print("正在生成安全配置报告...")
        
        # 按严重程度统计问题
        severity_count = {"high": 0, "medium": 0, "low": 0}
        for issue in self.issues:
            severity_count[issue["severity"]] += 1
        
        # 按类型统计问题
        type_count = {}
        for issue in self.issues:
            issue_type = issue["type"]
            if issue_type not in type_count:
                type_count[issue_type] = 0
            type_count[issue_type] += 1
        
        # 生成报告
        report = {
            "timestamp": datetime.datetime.now().isoformat(),
            "config_dir": self.config_dir,
            "total_issues": len(self.issues),
            "severity_count": severity_count,
            "type_count": type_count,
            "issues": self.issues
        }
        
        # 保存报告
        report_file = os.path.join(self.output_dir, "config_security_report.json")
        with open(report_file, "w") as f:
            json.dump(report, f, indent=2)
        
        # 生成HTML报告
        self._generate_html_report(report)
        
        print(f"安全配置报告已生成: {report_file}")
        print(f"总问题数: {len(self.issues)}")
        print(f"高危问题: {severity_count['high']}")
        print(f"中危问题: {severity_count['medium']}")
        print(f"低危问题: {severity_count['low']}")
    
    def _generate_html_report(self, report):
        """生成HTML安全配置报告"""
        html_report_file = os.path.join(self.output_dir, "config_security_report.html")
        
        html = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>零拷贝机制安全配置报告</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .summary {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .summary-item {
            text-align: center;
            padding: 10px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            flex: 1;
            margin: 0 10px;
        }
        .summary-item.high {
            border-top: 3px solid #e74c3c;
        }
        .summary-item.medium {
            border-top: 3px solid #f39c12;
        }
        .summary-item.low {
            border-top: 3px solid #3498db;
        }
        .summary-item h3 {
            margin: 0;
            font-size: 1.2em;
        }
        .summary-item p {
            font-size: 2em;
            margin: 10px 0;
        }
        .issue {
            margin-bottom: 10px;
            padding: 15px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .issue.high {
            border-left: 5px solid #e74c3c;
        }
        .issue.medium {
            border-left: 5px solid #f39c12;
        }
        .issue.low {
            border-left: 5px solid #3498db;
        }
        .issue-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .issue-title {
            font-weight: bold;
        }
        .issue-severity {
            padding: 2px 5px;
            border-radius: 3px;
            color: white;
            font-size: 0.8em;
        }
        .issue-severity.high {
            background-color: #e74c3c;
        }
        .issue-severity.medium {
            background-color: #f39c12;
        }
        .issue-severity.low {
            background-color: #3498db;
        }
        .issue-details {
            margin-bottom: 10px;
        }
        .issue-file {
            font-family: monospace;
            margin-bottom: 5px;
        }
        .issue-code {
            font-family: monospace;
            background-color: #f5f5f5;
            padding: 5px;
            border-radius: 3px;
            margin-bottom: 10px;
            white-space: pre;
            overflow-x: auto;
        }
        .issue-description {
            margin-bottom: 10px;
        }
        .issue-recommendation {
            background-color: #eafaf1;
            padding: 10px;
            border-radius: 3px;
            border-left: 3px solid #27ae60;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>零拷贝机制安全配置报告</h1>
        <p>生成时间: """ + report["timestamp"] + """</p>
        <p>配置目录: """ + report["config_dir"] + """</p>
        
        <div class="section">
            <h2>问题摘要</h2>
            <div class="summary">
                <div class="summary-item high">
                    <h3>高危问题</h3>
                    <p>""" + str(report["severity_count"]["high"]) + """</p>
                </div>
                <div class="summary-item medium">
                    <h3>中危问题</h3>
                    <p>""" + str(report["severity_count"]["medium"]) + """</p>
                </div>
                <div class="summary-item low">
                    <h3>低危问题</h3>
                    <p>""" + str(report["severity_count"]["low"]) + """</p>
                </div>
                <div class="summary-item">
                    <h3>总问题数</h3>
                    <p>""" + str(report["total_issues"]) + """</p>
                </div>
            </div>
            
            <h3>按类型统计</h3>
            <ul>
        """
        
        for issue_type, count in report["type_count"].items():
            html += f"<li>{issue_type}: {count}</li>"
        
        html += """
            </ul>
        </div>
        
        <div class="section">
            <h2>问题详情</h2>
        """
        
        # 按严重程度排序问题
        sorted_issues = sorted(report["issues"], key=lambda x: {"high": 0, "medium": 1, "low": 2}[x["severity"]])
        
        for issue in sorted_issues:
            html += f"""
            <div class="issue {issue['severity']}">
                <div class="issue-header">
                    <div class="issue-title">{issue['type']}</div>
                    <div class="issue-severity {issue['severity']}">{issue['severity']}</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">{issue['file']}{':' + str(issue['line']) if issue['line'] else ''}</div>
                    {f'<div class="issue-code">{issue["code"]}</div>' if issue['code'] else ''}
                </div>
                <div class="issue-description">{issue['description']}</div>
                <div class="issue-recommendation">{issue['recommendation']}</div>
            </div>
            """
        
        html += """
        </div>
    </div>
</body>
</html>
        """
        
        with open(html_report_file, "w") as f:
            f.write(html)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="零拷贝机制安全配置检查工具")
    parser.add_argument("--config", "-c", default=".", help="配置目录")
    parser.add_argument("--output", "-o", default="security_config", help="输出目录")
    parser.add_argument("--skip-configs", action="store_true", help="跳过配置检查")
    parser.add_argument("--skip-permissions", action="store_true", help="跳过权限检查")
    
    args = parser.parse_args()
    
    # 创建配置检查器
    checker = ConfigChecker(args.config, args.output)
    
    # 检查配置
    if not args.skip_configs:
        checker.check_configs()
    
    # 检查权限
    if not args.skip_permissions:
        checker.check_permissions()
    
    # 生成报告
    checker.generate_report()

if __name__ == "__main__":
    main()
