#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
零拷贝机制安全审计工具

这个脚本进行安全审计，包括：
1. 代码安全分析
2. 依赖项安全检查
3. 配置安全检查
4. 安全审计报告生成
"""

import os
import sys
import re
import json
import argparse
import subprocess
import datetime
import hashlib
from pathlib import Path

class SecurityAuditor:
    """安全审计类"""
    
    def __init__(self, source_dir, output_dir):
        """初始化安全审计器"""
        self.source_dir = source_dir
        self.output_dir = output_dir
        self.issues = []
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
    
    def audit_code(self):
        """审计代码安全"""
        print("正在审计代码安全...")
        
        # 收集源代码文件
        python_files = []
        rust_files = []
        
        for root, dirs, files in os.walk(self.source_dir):
            for file in files:
                file_path = os.path.join(root, file)
                
                if file.endswith(".py"):
                    python_files.append(file_path)
                elif file.endswith(".rs"):
                    rust_files.append(file_path)
        
        # 审计Python代码
        self._audit_python_code(python_files)
        
        # 审计Rust代码
        self._audit_rust_code(rust_files)
        
        print(f"代码安全审计完成，发现{len(self.issues)}个问题")
    
    def _audit_python_code(self, python_files):
        """审计Python代码"""
        # 定义安全问题模式
        patterns = {
            "硬编码密码": r"password\s*=\s*['\"][^'\"]+['\"]",
            "硬编码密钥": r"(api_key|secret_key|access_key)\s*=\s*['\"][^'\"]+['\"]",
            "SQL注入": r"execute\(['\"].*?\%s.*?['\"].*?,\s*\(?([^)]*)\)?",
            "命令注入": r"(os\.system|subprocess\.call|subprocess\.Popen)\(['\"].*?\%s.*?['\"].*?,\s*\(?([^)]*)\)?",
            "不安全的反序列化": r"(pickle\.loads|yaml\.load|eval)\(",
            "不安全的随机数": r"random\.(random|randint|choice|shuffle)",
            "不安全的哈希": r"hashlib\.(md5|sha1)\(",
            "不安全的SSL验证": r"verify\s*=\s*False",
            "调试代码": r"(print|logging\.debug)\(",
            "敏感信息泄露": r"(TODO|FIXME|XXX|BUG|HACK)",
        }
        
        for file_path in python_files:
            with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                content = f.read()
                
                for issue_type, pattern in patterns.items():
                    for match in re.finditer(pattern, content):
                        line_number = content[:match.start()].count("\n") + 1
                        line = content.split("\n")[line_number - 1].strip()
                        
                        self.issues.append({
                            "file": file_path,
                            "line": line_number,
                            "code": line,
                            "type": issue_type,
                            "severity": "high" if issue_type in ["硬编码密码", "硬编码密钥", "SQL注入", "命令注入", "不安全的反序列化"] else "medium",
                            "description": f"发现{issue_type}问题",
                            "recommendation": self._get_recommendation(issue_type)
                        })
    
    def _audit_rust_code(self, rust_files):
        """审计Rust代码"""
        # 定义安全问题模式
        patterns = {
            "硬编码密码": r"password\s*=\s*['\"][^'\"]+['\"]",
            "硬编码密钥": r"(api_key|secret_key|access_key)\s*=\s*['\"][^'\"]+['\"]",
            "不安全的反序列化": r"serde_json::from_str\(",
            "不安全的随机数": r"rand::(random|thread_rng)",
            "不安全的哈希": r"(md5|sha1)::",
            "不安全的内存操作": r"(std::mem::transmute|std::ptr::copy|unsafe)",
            "调试代码": r"(println!|debug!)\(",
            "敏感信息泄露": r"(TODO|FIXME|XXX|BUG|HACK)",
        }
        
        for file_path in rust_files:
            with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                content = f.read()
                
                for issue_type, pattern in patterns.items():
                    for match in re.finditer(pattern, content):
                        line_number = content[:match.start()].count("\n") + 1
                        line = content.split("\n")[line_number - 1].strip()
                        
                        self.issues.append({
                            "file": file_path,
                            "line": line_number,
                            "code": line,
                            "type": issue_type,
                            "severity": "high" if issue_type in ["硬编码密码", "硬编码密钥", "不安全的内存操作"] else "medium",
                            "description": f"发现{issue_type}问题",
                            "recommendation": self._get_recommendation(issue_type)
                        })
    
    def _get_recommendation(self, issue_type):
        """获取安全问题的建议"""
        recommendations = {
            "硬编码密码": "使用环境变量或配置文件存储密码，避免硬编码",
            "硬编码密钥": "使用环境变量或配置文件存储密钥，避免硬编码",
            "SQL注入": "使用参数化查询或ORM框架，避免直接拼接SQL语句",
            "命令注入": "使用参数化命令或安全的API，避免直接拼接命令",
            "不安全的反序列化": "使用安全的反序列化方法，如json.loads()替换为json.loads()", # 安全修复：使用安全的反序列化方法
            "不安全的随机数": "使用secrets模块生成安全的随机数",
            "不安全的哈希": "使用SHA-256或更安全的哈希算法",
            "不安全的SSL验证": "启用SSL验证，避免中间人攻击",
            "不安全的内存操作": "避免使用unsafe代码，使用安全的内存操作方法",
            "调试代码": "移除生产环境中的调试代码",
            "敏感信息泄露": "移除代码中的敏感信息和注释",
        }
        
        return recommendations.get(issue_type, "修复安全问题")
    
    def audit_dependencies(self):
        """审计依赖项安全"""
        print("正在审计依赖项安全...")
        
        # 检查Python依赖项
        python_dependencies = self._check_python_dependencies()
        
        # 检查Rust依赖项
        rust_dependencies = self._check_rust_dependencies()
        
        # 合并依赖项问题
        dependency_issues = python_dependencies + rust_dependencies
        
        # 添加到问题列表
        self.issues.extend(dependency_issues)
        
        print(f"依赖项安全审计完成，发现{len(dependency_issues)}个问题")
    
    def _check_python_dependencies(self):
        """检查Python依赖项"""
        issues = []
        
        # 查找requirements.txt文件
        requirements_files = []
        for root, dirs, files in os.walk(self.source_dir):
            for file in files:
                if file == "requirements.txt":
                    requirements_files.append(os.path.join(root, file))
        
        if not requirements_files:
            print("未找到requirements.txt文件，跳过Python依赖项检查")
            return issues
        
        # 检查每个requirements.txt文件
        for requirements_file in requirements_files:
            try:
                # 运行安全检查
                result = subprocess.run(
                    ["pip-audit", "-r", requirements_file, "--format", "json"],
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0:
                    # 解析结果
                    audit_result = json.loads(result.stdout)
                    
                    for vulnerability in audit_result.get("vulnerabilities", []):
                        package_name = vulnerability.get("name")
                        package_version = vulnerability.get("version")
                        vulnerability_id = vulnerability.get("id")
                        vulnerability_description = vulnerability.get("description")
                        vulnerability_fix_version = vulnerability.get("fix_version")
                        
                        issues.append({
                            "file": requirements_file,
                            "line": None,
                            "code": f"{package_name}=={package_version}",
                            "type": "依赖项漏洞",
                            "severity": "high",
                            "description": f"{package_name} {package_version} 存在漏洞: {vulnerability_id} - {vulnerability_description}",
                            "recommendation": f"升级到 {package_name}>={vulnerability_fix_version}"
                        })
            except Exception as e:
                print(f"检查Python依赖项时出错: {e}")
        
        return issues
    
    def _check_rust_dependencies(self):
        """检查Rust依赖项"""
        issues = []
        
        # 查找Cargo.toml文件
        cargo_files = []
        for root, dirs, files in os.walk(self.source_dir):
            for file in files:
                if file == "Cargo.toml":
                    cargo_files.append(os.path.join(root, file))
        
        if not cargo_files:
            print("未找到Cargo.toml文件，跳过Rust依赖项检查")
            return issues
        
        # 检查每个Cargo.toml文件
        for cargo_file in cargo_files:
            try:
                # 运行安全检查
                result = subprocess.run(
                    ["cargo", "audit", "--json"],
                    cwd=os.path.dirname(cargo_file),
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0:
                    # 解析结果
                    audit_result = json.loads(result.stdout)
                    
                    for vulnerability in audit_result.get("vulnerabilities", {}).get("list", []):
                        package_name = vulnerability.get("package")
                        package_version = vulnerability.get("version")
                        vulnerability_id = vulnerability.get("id")
                        vulnerability_description = vulnerability.get("title")
                        vulnerability_fix_version = vulnerability.get("patched_versions", [])[0] if vulnerability.get("patched_versions") else None
                        
                        issues.append({
                            "file": cargo_file,
                            "line": None,
                            "code": f"{package_name} = \"{package_version}\"",
                            "type": "依赖项漏洞",
                            "severity": "high",
                            "description": f"{package_name} {package_version} 存在漏洞: {vulnerability_id} - {vulnerability_description}",
                            "recommendation": f"升级到 {package_name} = \"{vulnerability_fix_version}\"" if vulnerability_fix_version else "升级到最新版本"
                        })
            except Exception as e:
                print(f"检查Rust依赖项时出错: {e}")
        
        return issues
    
    def audit_configuration(self):
        """审计配置安全"""
        print("正在审计配置安全...")
        
        # 检查配置文件
        config_issues = self._check_configuration_files()
        
        # 添加到问题列表
        self.issues.extend(config_issues)
        
        print(f"配置安全审计完成，发现{len(config_issues)}个问题")
    
    def _check_configuration_files(self):
        """检查配置文件"""
        issues = []
        
        # 查找配置文件
        config_files = []
        for root, dirs, files in os.walk(self.source_dir):
            for file in files:
                if file.endswith((".json", ".yaml", ".yml", ".toml", ".ini", ".conf", ".config", ".env")):
                    config_files.append(os.path.join(root, file))
        
        # 定义安全问题模式
        patterns = {
            "硬编码密码": r"(password|passwd)\s*[=:]\s*['\"]?[^'\"]+['\"]?",
            "硬编码密钥": r"(api_key|secret_key|access_key)\s*[=:]\s*['\"]?[^'\"]+['\"]?",
            "不安全的连接": r"(http://|ws://)",
            "调试模式": r"(debug|development)\s*[=:]\s*(true|1|yes)",
            "敏感信息": r"(private_key|certificate)\s*[=:]\s*['\"]?[^'\"]+['\"]?",
        }
        
        for file_path in config_files:
            with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                content = f.read()
                
                for issue_type, pattern in patterns.items():
                    for match in re.finditer(pattern, content):
                        line_number = content[:match.start()].count("\n") + 1
                        line = content.split("\n")[line_number - 1].strip()
                        
                        issues.append({
                            "file": file_path,
                            "line": line_number,
                            "code": line,
                            "type": issue_type,
                            "severity": "high" if issue_type in ["硬编码密码", "硬编码密钥", "敏感信息"] else "medium",
                            "description": f"配置文件中发现{issue_type}问题",
                            "recommendation": self._get_recommendation(issue_type)
                        })
        
        return issues
    
    def generate_report(self):
        """生成安全审计报告"""
        print("正在生成安全审计报告...")
        
        # 按严重程度统计问题
        severity_count = {"high": 0, "medium": 0, "low": 0}
        for issue in self.issues:
            severity_count[issue["severity"]] += 1
        
        # 按类型统计问题
        type_count = {}
        for issue in self.issues:
            issue_type = issue["type"]
            if issue_type not in type_count:
                type_count[issue_type] = 0
            type_count[issue_type] += 1
        
        # 生成报告
        report = {
            "timestamp": datetime.datetime.now().isoformat(),
            "source_dir": self.source_dir,
            "total_issues": len(self.issues),
            "severity_count": severity_count,
            "type_count": type_count,
            "issues": self.issues
        }
        
        # 保存报告
        report_file = os.path.join(self.output_dir, "security_audit_report.json")
        with open(report_file, "w") as f:
            json.dump(report, f, indent=2)
        
        # 生成HTML报告
        self._generate_html_report(report)
        
        print(f"安全审计报告已生成: {report_file}")
        print(f"总问题数: {len(self.issues)}")
        print(f"高危问题: {severity_count['high']}")
        print(f"中危问题: {severity_count['medium']}")
        print(f"低危问题: {severity_count['low']}")
    
    def _generate_html_report(self, report):
        """生成HTML安全审计报告"""
        html_report_file = os.path.join(self.output_dir, "security_audit_report.html")
        
        html = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>零拷贝机制安全审计报告</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .summary {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .summary-item {
            text-align: center;
            padding: 10px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            flex: 1;
            margin: 0 10px;
        }
        .summary-item.high {
            border-top: 3px solid #e74c3c;
        }
        .summary-item.medium {
            border-top: 3px solid #f39c12;
        }
        .summary-item.low {
            border-top: 3px solid #3498db;
        }
        .summary-item h3 {
            margin: 0;
            font-size: 1.2em;
        }
        .summary-item p {
            font-size: 2em;
            margin: 10px 0;
        }
        .issue {
            margin-bottom: 10px;
            padding: 15px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .issue.high {
            border-left: 5px solid #e74c3c;
        }
        .issue.medium {
            border-left: 5px solid #f39c12;
        }
        .issue.low {
            border-left: 5px solid #3498db;
        }
        .issue-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .issue-title {
            font-weight: bold;
        }
        .issue-severity {
            padding: 2px 5px;
            border-radius: 3px;
            color: white;
            font-size: 0.8em;
        }
        .issue-severity.high {
            background-color: #e74c3c;
        }
        .issue-severity.medium {
            background-color: #f39c12;
        }
        .issue-severity.low {
            background-color: #3498db;
        }
        .issue-details {
            margin-bottom: 10px;
        }
        .issue-file {
            font-family: monospace;
            margin-bottom: 5px;
        }
        .issue-code {
            font-family: monospace;
            background-color: #f5f5f5;
            padding: 5px;
            border-radius: 3px;
            margin-bottom: 10px;
            white-space: pre;
            overflow-x: auto;
        }
        .issue-description {
            margin-bottom: 10px;
        }
        .issue-recommendation {
            background-color: #eafaf1;
            padding: 10px;
            border-radius: 3px;
            border-left: 3px solid #27ae60;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>零拷贝机制安全审计报告</h1>
        <p>生成时间: """ + report["timestamp"] + """</p>
        <p>源代码目录: """ + report["source_dir"] + """</p>
        
        <div class="section">
            <h2>问题摘要</h2>
            <div class="summary">
                <div class="summary-item high">
                    <h3>高危问题</h3>
                    <p>""" + str(report["severity_count"]["high"]) + """</p>
                </div>
                <div class="summary-item medium">
                    <h3>中危问题</h3>
                    <p>""" + str(report["severity_count"]["medium"]) + """</p>
                </div>
                <div class="summary-item low">
                    <h3>低危问题</h3>
                    <p>""" + str(report["severity_count"]["low"]) + """</p>
                </div>
                <div class="summary-item">
                    <h3>总问题数</h3>
                    <p>""" + str(report["total_issues"]) + """</p>
                </div>
            </div>
            
            <h3>按类型统计</h3>
            <ul>
        """
        
        for issue_type, count in report["type_count"].items():
            html += f"<li>{issue_type}: {count}</li>"
        
        html += """
            </ul>
        </div>
        
        <div class="section">
            <h2>问题详情</h2>
        """
        
        # 按严重程度排序问题
        sorted_issues = sorted(report["issues"], key=lambda x: {"high": 0, "medium": 1, "low": 2}[x["severity"]])
        
        for issue in sorted_issues:
            html += f"""
            <div class="issue {issue['severity']}">
                <div class="issue-header">
                    <div class="issue-title">{issue['type']}</div>
                    <div class="issue-severity {issue['severity']}">{issue['severity']}</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">{issue['file']}{':' + str(issue['line']) if issue['line'] else ''}</div>
                    <div class="issue-code">{issue['code']}</div>
                </div>
                <div class="issue-description">{issue['description']}</div>
                <div class="issue-recommendation">{issue['recommendation']}</div>
            </div>
            """
        
        html += """
        </div>
    </div>
</body>
</html>
        """
        
        with open(html_report_file, "w") as f:
            f.write(html)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="零拷贝机制安全审计工具")
    parser.add_argument("--source", "-s", default=".", help="源代码目录")
    parser.add_argument("--output", "-o", default="security_audit", help="输出目录")
    parser.add_argument("--skip-code", action="store_true", help="跳过代码安全审计")
    parser.add_argument("--skip-dependencies", action="store_true", help="跳过依赖项安全审计")
    parser.add_argument("--skip-configuration", action="store_true", help="跳过配置安全审计")
    
    args = parser.parse_args()
    
    # 创建安全审计器
    auditor = SecurityAuditor(args.source, args.output)
    
    # 审计代码安全
    if not args.skip_code:
        auditor.audit_code()
    
    # 审计依赖项安全
    if not args.skip_dependencies:
        auditor.audit_dependencies()
    
    # 审计配置安全
    if not args.skip_configuration:
        auditor.audit_configuration()
    
    # 生成安全审计报告
    auditor.generate_report()

if __name__ == "__main__":
    main()