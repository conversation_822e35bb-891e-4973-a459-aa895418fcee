
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>零拷贝机制性能基准测试结果</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .chart {
            margin: 20px 0;
        }
        .chart-container {
            height: 400px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>零拷贝机制性能基准测试结果</h1>
        
        <div class="section">
            <h2>NumPy数组零拷贝转换性能</h2>
            
    
    <div class="chart">
        <h3>NumPy数组零拷贝转换吞吐量</h3>
        <div class="chart-container">
            <canvas id="NumPy数组零拷贝转换吞吐量"></canvas>
        </div>
        <script>
            var ctx = document.getElementById('NumPy数组零拷贝转换吞吐量').getContext('2d');
            var chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ["1000", "10000", "100000", "1000000", "10000000"],
                    datasets: [
    
                        {
                            label: '零拷贝',
                            data: [6324.110671936759, 70953.43680709535, 633663.3663366337, 4692082.11143695, 10603048.376408217],
                            borderColor: 'hsl(0, 70%, 50%)',
                            backgroundColor: 'hsl(0, 70%, 50%)',
                            fill: false,
                            tension: 0.1
                        },
        
                        {
                            label: '普通复制',
                            data: [9014.084507042255, 18465.089440276977, 15848.645436085384, 7311.812232661866, 1644.9493936459512],
                            borderColor: 'hsl(180, 70%, 50%)',
                            backgroundColor: 'hsl(180, 70%, 50%)',
                            fill: false,
                            tension: 0.1
                        },
        
                    ]
                },
                options: {
                    responsive: true,
                    title: {
                        display: true,
                        text: 'NumPy数组零拷贝转换吞吐量'
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '数组大小'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: '吞吐量 (MB/s)'
                            }
                        }
                    }
                }
            });
        </script>
    </div>
    
    <div class="chart">
        <h3>NumPy数组零拷贝转换加速比</h3>
        <div class="chart-container">
            <canvas id="NumPy数组零拷贝转换加速比"></canvas>
        </div>
        <script>
            var ctx = document.getElementById('NumPy数组零拷贝转换加速比').getContext('2d');
            var chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ["1000", "10000", "100000", "1000000", "10000000"],
                    datasets: [
    
                        {
                            label: '加速比',
                            data: [0.7015810276679842, 3.842572062084257, 39.98217821782178, 641.7126099706744, 6445.820410868124],
                            borderColor: 'hsl(0, 70%, 50%)',
                            backgroundColor: 'hsl(0, 70%, 50%)',
                            fill: false,
                            tension: 0.1
                        },
        
                    ]
                },
                options: {
                    responsive: true,
                    title: {
                        display: true,
                        text: 'NumPy数组零拷贝转换加速比'
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '数组大小'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: '加速比'
                            }
                        }
                    }
                }
            });
        </script>
    </div>
    
        </div>
        
        <div class="section">
            <h2>量子态序列化和反序列化性能</h2>
            
    
    <div class="chart">
        <h3>量子态序列化和反序列化吞吐量</h3>
        <div class="chart-container">
            <canvas id="量子态序列化和反序列化吞吐量"></canvas>
        </div>
        <script>
            var ctx = document.getElementById('量子态序列化和反序列化吞吐量').getContext('2d');
            var chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"],
                    datasets: [
    
                        {
                            label: '序列化',
                            data: [3.3534189153785694, 5.174853446533253, 5.3344446759741615, 6.290312672768598, 6.7906760834245175, 6.89864250345269, 7.238219779637205, 7.2939018635420645, 7.513976344549571, 7.475671784974055],
                            borderColor: 'hsl(0, 70%, 50%)',
                            backgroundColor: 'hsl(0, 70%, 50%)',
                            fill: false,
                            tension: 0.1
                        },
        
                        {
                            label: '反序列化',
                            data: [4.612612612612613, 6.863270777479893, 9.602400600150037, 10.95187165775401, 12.722867615083556, 13.098816757275344, 13.262960204643331, 13.41620196362624, 13.73016504858437, 13.84578981171619],
                            borderColor: 'hsl(180, 70%, 50%)',
                            backgroundColor: 'hsl(180, 70%, 50%)',
                            fill: false,
                            tension: 0.1
                        },
        
                    ]
                },
                options: {
                    responsive: true,
                    title: {
                        display: true,
                        text: '量子态序列化和反序列化吞吐量'
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '量子比特数'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: '吞吐量 (MB/s)'
                            }
                        }
                    }
                }
            });
        </script>
    </div>
    
    <div class="chart">
        <h3>量子态序列化和反序列化时间</h3>
        <div class="chart-container">
            <canvas id="量子态序列化和反序列化时间"></canvas>
        </div>
        <script>
            var ctx = document.getElementById('量子态序列化和反序列化时间').getContext('2d');
            var chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"],
                    datasets: [
    
                        {
                            label: '序列化',
                            data: [9.100437164306641e-06, 1.1794567108154297e-05, 2.288341522216797e-05, 3.881216049194336e-05, 7.190465927124023e-05, 0.00014155864715576172, 0.0002698349952697754, 0.0005355501174926758, 0.0010397291183471679, 0.0020901131629943847],
                            borderColor: 'hsl(0, 70%, 50%)',
                            backgroundColor: 'hsl(0, 70%, 50%)',
                            fill: false,
                            tension: 0.1
                        },
        
                        {
                            label: '反序列化',
                            data: [6.616115570068359e-06, 8.89301300048828e-06, 1.2712478637695313e-05, 2.2292137145996094e-05, 3.8378238677978515e-05, 7.455348968505859e-05, 0.00014726161956787109, 0.00029115915298461915, 0.0005690026283264161, 0.0011285018920898437],
                            borderColor: 'hsl(180, 70%, 50%)',
                            backgroundColor: 'hsl(180, 70%, 50%)',
                            fill: false,
                            tension: 0.1
                        },
        
                    ]
                },
                options: {
                    responsive: true,
                    title: {
                        display: true,
                        text: '量子态序列化和反序列化时间'
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '量子比特数'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: '时间 (毫秒)'
                            }
                        }
                    }
                }
            });
        </script>
    </div>
    
    <div class="chart">
        <h3>量子态维度与序列化/反序列化时间的关系</h3>
        <div class="chart-container">
            <canvas id="量子态维度与序列化/反序列化时间的关系"></canvas>
        </div>
        <script>
            var ctx = document.getElementById('量子态维度与序列化/反序列化时间的关系').getContext('2d');
            var chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ["2", "4", "8", "16", "32", "64", "128", "256", "512", "1024"],
                    datasets: [
    
                        {
                            label: '序列化',
                            data: [0.00910043716430664, 0.011794567108154297, 0.02288341522216797, 0.03881216049194336, 0.07190465927124023, 0.14155864715576172, 0.2698349952697754, 0.5355501174926758, 1.039729118347168, 2.0901131629943848],
                            borderColor: 'hsl(0, 70%, 50%)',
                            backgroundColor: 'hsl(0, 70%, 50%)',
                            fill: false,
                            tension: 0.1
                        },
        
                        {
                            label: '反序列化',
                            data: [0.006616115570068359, 0.008893013000488281, 0.012712478637695312, 0.022292137145996094, 0.038378238677978516, 0.0745534896850586, 0.1472616195678711, 0.29115915298461914, 0.569002628326416, 1.1285018920898438],
                            borderColor: 'hsl(180, 70%, 50%)',
                            backgroundColor: 'hsl(180, 70%, 50%)',
                            fill: false,
                            tension: 0.1
                        },
        
                    ]
                },
                options: {
                    responsive: true,
                    title: {
                        display: true,
                        text: '量子态维度与序列化/反序列化时间的关系'
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '维度'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: '时间 (毫秒)'
                            }
                        }
                    }
                }
            });
        </script>
    </div>
    
        </div>
    </div>
</body>
</html>
    