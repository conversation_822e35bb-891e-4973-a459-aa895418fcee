<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>零拷贝机制测试报告</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .success {
            color: #27ae60;
        }
        .warning {
            color: #f39c12;
        }
        .error {
            color: #e74c3c;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .chart {
            margin: 20px 0;
            text-align: center;
        }
        .chart img {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>零拷贝机制测试报告</h1>
        <p>生成时间：Wed May 14 10:31:43 AM CST 2025</p>

        <div class="section">
            <h2>测试摘要</h2>
            <p>本报告总结了零拷贝机制的测试结果，包括功能测试和性能基准测试。</p>

            <h3>测试环境</h3>
            <ul>
                <li>操作系统：Linux matrix 5.15.0-139-generic #149-Ubuntu SMP Fri Apr 11 22:06:13 UTC 2025 x86_64 x86_64 x86_64 GNU/Linux</li>
                <li>Python版本：Python 3.13.3</li>
                <li>NumPy版本：2.2.5</li>
            </ul>
        </div>

        <div class="section">
            <h2>测试结果</h2>

            <h3>Python测试</h3>
            <p class="success">✓ 所有Python测试通过</p>

            <h3>性能基准测试</h3>
            <p>详细的性能基准测试结果请查看性能图表。</p>
        </div>

        <div class="section">
            <h2>性能图表</h2>

            <div class="chart">
                <h3>性能基准测试结果</h3>
                <iframe src="charts/benchmark_results.html" width="100%" height="800" frameborder="0"></iframe>
            </div>
        </div>

        <div class="section">
            <h2>详细文档</h2>
            <p>详细的测试文档请查看 <a href="TEST_DOCUMENTATION.md">TEST_DOCUMENTATION.md</a>。</p>
        </div>
    </div>
</body>
</html>
