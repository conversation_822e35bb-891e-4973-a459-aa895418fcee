
=== NumPy数组零拷贝转换性能基准测试 ===
数组大小: 1000
  零拷贝时间: 0.001毫秒, 吞吐量: 6324.11 MB/s
  普通复制时间: 0.001毫秒, 吞吐量: 9014.08 MB/s
  加速比: 0.70x
数组大小: 10000
  零拷贝时间: 0.001毫秒, 吞吐量: 70953.44 MB/s
  普通复制时间: 0.004毫秒, 吞吐量: 18465.09 MB/s
  加速比: 3.84x
数组大小: 100000
  零拷贝时间: 0.001毫秒, 吞吐量: 633663.37 MB/s
  普通复制时间: 0.048毫秒, 吞吐量: 15848.65 MB/s
  加速比: 39.98x
数组大小: 1000000
  零拷贝时间: 0.002毫秒, 吞吐量: 4692082.11 MB/s
  普通复制时间: 1.043毫秒, 吞吐量: 7311.81 MB/s
  加速比: 641.71x
数组大小: 10000000
  零拷贝时间: 0.007毫秒, 吞吐量: 10603048.38 MB/s
  普通复制时间: 46.381毫秒, 吞吐量: 1644.95 MB/s
  加速比: 6445.82x

=== NumPy数组文件映射性能基准测试 ===
数组大小: 1000
  内存映射时间: 0.213毫秒, 吞吐量: 35.88 MB/s
  普通加载时间: 0.117毫秒, 吞吐量: 65.12 MB/s
  加速比: 0.55x
数组大小: 10000
  内存映射时间: 0.148毫秒, 吞吐量: 514.63 MB/s
  普通加载时间: 0.120毫秒, 吞吐量: 633.41 MB/s
  加速比: 0.81x
数组大小: 100000
  内存映射时间: 0.151毫秒, 吞吐量: 5064.09 MB/s
  普通加载时间: 0.302毫秒, 吞吐量: 2522.27 MB/s
  加速比: 2.01x
数组大小: 1000000
  内存映射时间: 0.175毫秒, 吞吐量: 43614.56 MB/s
  普通加载时间: 1.853毫秒, 吞吐量: 4116.87 MB/s
  加速比: 10.59x
数组大小: 10000000
  内存映射时间: 0.202毫秒, 吞吐量: 377937.88 MB/s
  普通加载时间: 41.921毫秒, 吞吐量: 1819.94 MB/s
  加速比: 207.67x

=== 量子态序列化和反序列化性能基准测试 ===
量子比特数: 1, 维度: 2
  序列化时间: 0.009毫秒, 吞吐量: 3.35 MB/s
  反序列化时间: 0.007毫秒, 吞吐量: 4.61 MB/s
量子比特数: 2, 维度: 4
  序列化时间: 0.012毫秒, 吞吐量: 5.17 MB/s
  反序列化时间: 0.009毫秒, 吞吐量: 6.86 MB/s
量子比特数: 3, 维度: 8
  序列化时间: 0.023毫秒, 吞吐量: 5.33 MB/s
  反序列化时间: 0.013毫秒, 吞吐量: 9.60 MB/s
量子比特数: 4, 维度: 16
  序列化时间: 0.039毫秒, 吞吐量: 6.29 MB/s
  反序列化时间: 0.022毫秒, 吞吐量: 10.95 MB/s
量子比特数: 5, 维度: 32
  序列化时间: 0.072毫秒, 吞吐量: 6.79 MB/s
  反序列化时间: 0.038毫秒, 吞吐量: 12.72 MB/s
量子比特数: 6, 维度: 64
  序列化时间: 0.142毫秒, 吞吐量: 6.90 MB/s
  反序列化时间: 0.075毫秒, 吞吐量: 13.10 MB/s
量子比特数: 7, 维度: 128
  序列化时间: 0.270毫秒, 吞吐量: 7.24 MB/s
  反序列化时间: 0.147毫秒, 吞吐量: 13.26 MB/s
量子比特数: 8, 维度: 256
  序列化时间: 0.536毫秒, 吞吐量: 7.29 MB/s
  反序列化时间: 0.291毫秒, 吞吐量: 13.42 MB/s
量子比特数: 9, 维度: 512
  序列化时间: 1.040毫秒, 吞吐量: 7.51 MB/s
  反序列化时间: 0.569毫秒, 吞吐量: 13.73 MB/s
量子比特数: 10, 维度: 1024
  序列化时间: 2.090毫秒, 吞吐量: 7.48 MB/s
  反序列化时间: 1.129毫秒, 吞吐量: 13.85 MB/s

=== 分形结构序列化和反序列化性能基准测试 ===
深度: 1, 节点数: 6, 连接数: 6
  序列化时间: 0.019毫秒, 吞吐量: 39.21 MB/s
  反序列化时间: 0.015毫秒, 吞吐量: 49.55 MB/s
深度: 2, 节点数: 12, 连接数: 12
  序列化时间: 0.035毫秒, 吞吐量: 40.75 MB/s
  反序列化时间: 0.029毫秒, 吞吐量: 48.74 MB/s
深度: 3, 节点数: 24, 连接数: 24
  序列化时间: 0.067毫秒, 吞吐量: 41.74 MB/s
  反序列化时间: 0.054毫秒, 吞吐量: 51.64 MB/s
深度: 4, 节点数: 48, 连接数: 48
  序列化时间: 0.128毫秒, 吞吐量: 43.73 MB/s
  反序列化时间: 0.108毫秒, 吞吐量: 51.93 MB/s
深度: 5, 节点数: 96, 连接数: 96
  序列化时间: 0.257毫秒, 吞吐量: 43.54 MB/s
  反序列化时间: 0.217毫秒, 吞吐量: 51.70 MB/s
深度: 6, 节点数: 192, 连接数: 192
  序列化时间: 0.507毫秒, 吞吐量: 44.92 MB/s
  反序列化时间: 0.428毫秒, 吞吐量: 53.28 MB/s

结果已保存到：/home/<USER>/CascadeProjects/TFF/rust/arrow_integration_new/test_results/benchmark_results.json
