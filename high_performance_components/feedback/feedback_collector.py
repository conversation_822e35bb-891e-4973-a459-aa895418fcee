#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
零拷贝机制用户反馈收集

这个脚本收集用户反馈，包括：
1. 问题报告
2. 功能请求
3. 性能反馈
4. 用户体验反馈
5. 文档反馈
"""

import os
import sys
import json
import argparse
import datetime
import sqlite3
import uuid
from pathlib import Path

class FeedbackCollector:
    """用户反馈收集类"""
    
    def __init__(self, db_path):
        """初始化反馈收集器"""
        self.db_path = db_path
        self.conn = None
        self.create_database()
    
    def create_database(self):
        """创建数据库"""
        self.conn = sqlite3.connect(self.db_path)
        cursor = self.conn.cursor()
        
        # 创建反馈表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS feedback (
            id TEXT PRIMARY KEY,
            timestamp TEXT NOT NULL,
            type TEXT NOT NULL,
            title TEXT NOT NULL,
            description TEXT NOT NULL,
            user_name TEXT,
            user_email TEXT,
            user_organization TEXT,
            environment TEXT,
            severity TEXT,
            status TEXT DEFAULT 'new',
            assignee TEXT,
            tags TEXT,
            attachments TEXT
        )
        ''')
        
        # 创建评论表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS comments (
            id TEXT PRIMARY KEY,
            feedback_id TEXT NOT NULL,
            timestamp TEXT NOT NULL,
            user_name TEXT,
            user_email TEXT,
            content TEXT NOT NULL,
            FOREIGN KEY (feedback_id) REFERENCES feedback (id)
        )
        ''')
        
        # 创建投票表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS votes (
            id TEXT PRIMARY KEY,
            feedback_id TEXT NOT NULL,
            timestamp TEXT NOT NULL,
            user_name TEXT,
            user_email TEXT,
            vote INTEGER NOT NULL,
            FOREIGN KEY (feedback_id) REFERENCES feedback (id)
        )
        ''')
        
        self.conn.commit()
    
    def add_feedback(self, type, title, description, user_name=None, user_email=None, user_organization=None, environment=None, severity=None, tags=None, attachments=None):
        """添加反馈"""
        cursor = self.conn.cursor()
        
        feedback_id = str(uuid.uuid4())
        timestamp = datetime.datetime.now().isoformat()
        
        cursor.execute('''
        INSERT INTO feedback (id, timestamp, type, title, description, user_name, user_email, user_organization, environment, severity, tags, attachments)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (feedback_id, timestamp, type, title, description, user_name, user_email, user_organization, environment, severity, json.dumps(tags) if tags else None, json.dumps(attachments) if attachments else None))
        
        self.conn.commit()
        
        return feedback_id
    
    def add_comment(self, feedback_id, content, user_name=None, user_email=None):
        """添加评论"""
        cursor = self.conn.cursor()
        
        comment_id = str(uuid.uuid4())
        timestamp = datetime.datetime.now().isoformat()
        
        cursor.execute('''
        INSERT INTO comments (id, feedback_id, timestamp, user_name, user_email, content)
        VALUES (?, ?, ?, ?, ?, ?)
        ''', (comment_id, feedback_id, timestamp, user_name, user_email, content))
        
        self.conn.commit()
        
        return comment_id
    
    def add_vote(self, feedback_id, vote, user_name=None, user_email=None):
        """添加投票"""
        cursor = self.conn.cursor()
        
        vote_id = str(uuid.uuid4())
        timestamp = datetime.datetime.now().isoformat()
        
        cursor.execute('''
        INSERT INTO votes (id, feedback_id, timestamp, user_name, user_email, vote)
        VALUES (?, ?, ?, ?, ?, ?)
        ''', (vote_id, feedback_id, timestamp, user_name, user_email, vote))
        
        self.conn.commit()
        
        return vote_id
    
    def get_feedback(self, feedback_id):
        """获取反馈"""
        cursor = self.conn.cursor()
        
        cursor.execute('''
        SELECT id, timestamp, type, title, description, user_name, user_email, user_organization, environment, severity, status, assignee, tags, attachments
        FROM feedback
        WHERE id = ?
        ''', (feedback_id,))
        
        row = cursor.fetchone()
        if not row:
            return None
        
        feedback = {
            "id": row[0],
            "timestamp": row[1],
            "type": row[2],
            "title": row[3],
            "description": row[4],
            "user_name": row[5],
            "user_email": row[6],
            "user_organization": row[7],
            "environment": row[8],
            "severity": row[9],
            "status": row[10],
            "assignee": row[11],
            "tags": json.loads(row[12]) if row[12] else None,
            "attachments": json.loads(row[13]) if row[13] else None
        }
        
        # 获取评论
        cursor.execute('''
        SELECT id, timestamp, user_name, user_email, content
        FROM comments
        WHERE feedback_id = ?
        ORDER BY timestamp
        ''', (feedback_id,))
        
        comments = []
        for row in cursor.fetchall():
            comments.append({
                "id": row[0],
                "timestamp": row[1],
                "user_name": row[2],
                "user_email": row[3],
                "content": row[4]
            })
        
        feedback["comments"] = comments
        
        # 获取投票
        cursor.execute('''
        SELECT id, timestamp, user_name, user_email, vote
        FROM votes
        WHERE feedback_id = ?
        ORDER BY timestamp
        ''', (feedback_id,))
        
        votes = []
        for row in cursor.fetchall():
            votes.append({
                "id": row[0],
                "timestamp": row[1],
                "user_name": row[2],
                "user_email": row[3],
                "vote": row[4]
            })
        
        feedback["votes"] = votes
        
        return feedback
    
    def get_all_feedback(self, type=None, status=None, limit=100):
        """获取所有反馈"""
        cursor = self.conn.cursor()
        
        query = "SELECT id, timestamp, type, title, description, user_name, user_email, user_organization, environment, severity, status, assignee, tags, attachments FROM feedback"
        params = []
        
        if type or status:
            query += " WHERE"
            
            if type:
                query += " type = ?"
                params.append(type)
                
                if status:
                    query += " AND"
            
            if status:
                query += " status = ?"
                params.append(status)
        
        query += " ORDER BY timestamp DESC LIMIT ?"
        params.append(limit)
        
        cursor.execute(query, params)
        
        feedbacks = []
        for row in cursor.fetchall():
            feedbacks.append({
                "id": row[0],
                "timestamp": row[1],
                "type": row[2],
                "title": row[3],
                "description": row[4],
                "user_name": row[5],
                "user_email": row[6],
                "user_organization": row[7],
                "environment": row[8],
                "severity": row[9],
                "status": row[10],
                "assignee": row[11],
                "tags": json.loads(row[12]) if row[12] else None,
                "attachments": json.loads(row[13]) if row[13] else None
            })
        
        return feedbacks
    
    def update_feedback_status(self, feedback_id, status, assignee=None):
        """更新反馈状态"""
        cursor = self.conn.cursor()
        
        if assignee:
            cursor.execute('''
            UPDATE feedback
            SET status = ?, assignee = ?
            WHERE id = ?
            ''', (status, assignee, feedback_id))
        else:
            cursor.execute('''
            UPDATE feedback
            SET status = ?
            WHERE id = ?
            ''', (status, feedback_id))
        
        self.conn.commit()
    
    def generate_feedback_report(self, output_file):
        """生成反馈报告"""
        # 获取所有反馈
        feedbacks = self.get_all_feedback()
        
        # 按类型统计反馈
        feedback_by_type = {}
        for feedback in feedbacks:
            if feedback["type"] not in feedback_by_type:
                feedback_by_type[feedback["type"]] = []
            
            feedback_by_type[feedback["type"]].append(feedback)
        
        # 按状态统计反馈
        feedback_by_status = {}
        for feedback in feedbacks:
            if feedback["status"] not in feedback_by_status:
                feedback_by_status[feedback["status"]] = []
            
            feedback_by_status[feedback["status"]].append(feedback)
        
        # 生成报告
        report = {
            "total_feedback": len(feedbacks),
            "feedback_by_type": {type: len(feedbacks) for type, feedbacks in feedback_by_type.items()},
            "feedback_by_status": {status: len(feedbacks) for status, feedbacks in feedback_by_status.items()},
            "recent_feedback": feedbacks[:10]
        }
        
        with open(output_file, "w") as f:
            json.dump(report, f, indent=2)
        
        return report
