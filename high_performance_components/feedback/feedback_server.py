#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
零拷贝机制用户反馈服务器

这个脚本提供用户反馈收集的Web服务器，包括：
1. 反馈提交API
2. 反馈查询API
3. 反馈管理API
4. 反馈报告API
5. 反馈导出API
"""

import os
import sys
import json
import argparse
import datetime
import sqlite3
import uuid
from pathlib import Path
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import cgi
import ssl

# 导入反馈收集器
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from feedback.feedback_collector import FeedbackCollector

class FeedbackHandler(BaseHTTPRequestHandler):
    """反馈处理器"""
    
    def __init__(self, *args, **kwargs):
        """初始化反馈处理器"""
        self.feedback_collector = None
        super().__init__(*args, **kwargs)
    
    def set_feedback_collector(self, feedback_collector):
        """设置反馈收集器"""
        self.feedback_collector = feedback_collector
    
    def do_GET(self):
        """处理GET请求"""
        # 解析URL
        parsed_url = urlparse(self.path)
        path = parsed_url.path
        query = parse_qs(parsed_url.query)
        
        # 处理API请求
        if path == "/api/feedback":
            # 获取反馈
            if "id" in query:
                feedback_id = query["id"][0]
                feedback = self.feedback_collector.get_feedback(feedback_id)
                
                if feedback:
                    self.send_response(200)
                    self.send_header("Content-type", "application/json")
                    self.end_headers()
                    self.wfile.write(json.dumps(feedback).encode())
                else:
                    self.send_response(404)
                    self.send_header("Content-type", "application/json")
                    self.end_headers()
                    self.wfile.write(json.dumps({"error": "Feedback not found"}).encode())
            else:
                # 获取所有反馈
                type = query.get("type", [None])[0]
                status = query.get("status", [None])[0]
                limit = int(query.get("limit", [100])[0])
                
                feedbacks = self.feedback_collector.get_all_feedback(type, status, limit)
                
                self.send_response(200)
                self.send_header("Content-type", "application/json")
                self.end_headers()
                self.wfile.write(json.dumps(feedbacks).encode())
        elif path == "/api/feedback/report":
            # 生成反馈报告
            report_file = "feedback_report.json"
            report = self.feedback_collector.generate_feedback_report(report_file)
            
            self.send_response(200)
            self.send_header("Content-type", "application/json")
            self.end_headers()
            self.wfile.write(json.dumps(report).encode())
        elif path == "/":
            # 返回HTML页面
            self.send_response(200)
            self.send_header("Content-type", "text/html")
            self.end_headers()
            
            html = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>零拷贝机制用户反馈</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"],
        input[type="email"],
        textarea,
        select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 150px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #2980b9;
        }
        .feedback-list {
            margin-top: 30px;
        }
        .feedback-item {
            margin-bottom: 15px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        .feedback-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .feedback-meta {
            font-size: 0.9em;
            color: #7f8c8d;
            margin-bottom: 10px;
        }
        .feedback-description {
            margin-bottom: 10px;
        }
        .feedback-status {
            display: inline-block;
            padding: 3px 6px;
            border-radius: 3px;
            font-size: 0.8em;
            color: white;
        }
        .feedback-status.new {
            background-color: #3498db;
        }
        .feedback-status.in-progress {
            background-color: #f39c12;
        }
        .feedback-status.resolved {
            background-color: #27ae60;
        }
        .feedback-status.closed {
            background-color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>零拷贝机制用户反馈</h1>
        
        <h2>提交反馈</h2>
        <form id="feedback-form">
            <div class="form-group">
                <label for="type">反馈类型</label>
                <select id="type" name="type" required>
                    <option value="bug">问题报告</option>
                    <option value="feature">功能请求</option>
                    <option value="performance">性能反馈</option>
                    <option value="usability">用户体验反馈</option>
                    <option value="documentation">文档反馈</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="title">标题</label>
                <input type="text" id="title" name="title" required>
            </div>
            
            <div class="form-group">
                <label for="description">描述</label>
                <textarea id="description" name="description" required></textarea>
            </div>
            
            <div class="form-group">
                <label for="user_name">姓名</label>
                <input type="text" id="user_name" name="user_name">
            </div>
            
            <div class="form-group">
                <label for="user_email">邮箱</label>
                <input type="email" id="user_email" name="user_email">
            </div>
            
            <div class="form-group">
                <label for="user_organization">组织</label>
                <input type="text" id="user_organization" name="user_organization">
            </div>
            
            <div class="form-group">
                <label for="environment">环境</label>
                <input type="text" id="environment" name="environment" placeholder="操作系统、Python版本、Arrow版本等">
            </div>
            
            <div class="form-group">
                <label for="severity">严重程度</label>
                <select id="severity" name="severity">
                    <option value="low">低</option>
                    <option value="medium">中</option>
                    <option value="high">高</option>
                    <option value="critical">严重</option>
                </select>
            </div>
            
            <button type="submit">提交反馈</button>
        </form>
        
        <div class="feedback-list" id="feedback-list">
            <h2>最近反馈</h2>
            <!-- 反馈列表将在这里动态加载 -->
        </div>
    </div>
    
    <script>
        // 提交反馈
        document.getElementById('feedback-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            var formData = new FormData(this);
            var data = {};
            
            for (var pair of formData.entries()) {
                data[pair[0]] = pair[1];
            }
            
            fetch('/api/feedback', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                alert('反馈已提交，ID: ' + result.id);
                this.reset();
                loadFeedback();
            })
            .catch(error => {
                alert('提交反馈失败: ' + error);
            });
        });
        
        // 加载反馈
        function loadFeedback() {
            fetch('/api/feedback?limit=10')
            .then(response => response.json())
            .then(feedbacks => {
                var feedbackList = document.getElementById('feedback-list');
                
                // 清空列表
                while (feedbackList.children.length > 1) {
                    feedbackList.removeChild(feedbackList.lastChild);
                }
                
                // 添加反馈
                feedbacks.forEach(function(feedback) {
                    var feedbackItem = document.createElement('div');
                    feedbackItem.className = 'feedback-item';
                    
                    var feedbackTitle = document.createElement('div');
                    feedbackTitle.className = 'feedback-title';
                    feedbackTitle.textContent = feedback.title;
                    
                    var feedbackMeta = document.createElement('div');
                    feedbackMeta.className = 'feedback-meta';
                    feedbackMeta.textContent = '类型: ' + feedback.type + ' | 提交者: ' + (feedback.user_name || '匿名') + ' | 时间: ' + new Date(feedback.timestamp).toLocaleString();
                    
                    var feedbackDescription = document.createElement('div');
                    feedbackDescription.className = 'feedback-description';
                    feedbackDescription.textContent = feedback.description;
                    
                    var feedbackStatus = document.createElement('div');
                    feedbackStatus.className = 'feedback-status ' + feedback.status;
                    feedbackStatus.textContent = feedback.status;
                    
                    feedbackItem.appendChild(feedbackTitle);
                    feedbackItem.appendChild(feedbackMeta);
                    feedbackItem.appendChild(feedbackDescription);
                    feedbackItem.appendChild(feedbackStatus);
                    
                    feedbackList.appendChild(feedbackItem);
                });
            })
            .catch(error => {
                console.error('加载反馈失败: ' + error);
            });
        }
        
        // 页面加载时加载反馈
        window.addEventListener('load', loadFeedback);
    </script>
</body>
</html>
            """
            
            self.wfile.write(html.encode())
        else:
            # 返回404
            self.send_response(404)
            self.send_header("Content-type", "text/plain")
            self.end_headers()
            self.wfile.write(b"404 Not Found")
    
    def do_POST(self):
        """处理POST请求"""
        # 解析URL
        parsed_url = urlparse(self.path)
        path = parsed_url.path
        
        # 处理API请求
        if path == "/api/feedback":
            # 获取请求内容长度
            content_length = int(self.headers["Content-Length"])
            
            # 读取请求内容
            post_data = self.rfile.read(content_length)
            
            # 解析JSON数据
            data = json.loads(post_data.decode())
            
            # 添加反馈
            feedback_id = self.feedback_collector.add_feedback(
                data.get("type"),
                data.get("title"),
                data.get("description"),
                data.get("user_name"),
                data.get("user_email"),
                data.get("user_organization"),
                data.get("environment"),
                data.get("severity"),
                data.get("tags"),
                data.get("attachments")
            )
            
            # 返回反馈ID
            self.send_response(201)
            self.send_header("Content-type", "application/json")
            self.end_headers()
            self.wfile.write(json.dumps({"id": feedback_id}).encode())
        elif path == "/api/feedback/comment":
            # 获取请求内容长度
            content_length = int(self.headers["Content-Length"])
            
            # 读取请求内容
            post_data = self.rfile.read(content_length)
            
            # 解析JSON数据
            data = json.loads(post_data.decode())
            
            # 添加评论
            comment_id = self.feedback_collector.add_comment(
                data.get("feedback_id"),
                data.get("content"),
                data.get("user_name"),
                data.get("user_email")
            )
            
            # 返回评论ID
            self.send_response(201)
            self.send_header("Content-type", "application/json")
            self.end_headers()
            self.wfile.write(json.dumps({"id": comment_id}).encode())
        elif path == "/api/feedback/vote":
            # 获取请求内容长度
            content_length = int(self.headers["Content-Length"])
            
            # 读取请求内容
            post_data = self.rfile.read(content_length)
            
            # 解析JSON数据
            data = json.loads(post_data.decode())
            
            # 添加投票
            vote_id = self.feedback_collector.add_vote(
                data.get("feedback_id"),
                data.get("vote"),
                data.get("user_name"),
                data.get("user_email")
            )
            
            # 返回投票ID
            self.send_response(201)
            self.send_header("Content-type", "application/json")
            self.end_headers()
            self.wfile.write(json.dumps({"id": vote_id}).encode())
        elif path == "/api/feedback/status":
            # 获取请求内容长度
            content_length = int(self.headers["Content-Length"])
            
            # 读取请求内容
            post_data = self.rfile.read(content_length)
            
            # 解析JSON数据
            data = json.loads(post_data.decode())
            
            # 更新反馈状态
            self.feedback_collector.update_feedback_status(
                data.get("feedback_id"),
                data.get("status"),
                data.get("assignee")
            )
            
            # 返回成功
            self.send_response(200)
            self.send_header("Content-type", "application/json")
            self.end_headers()
            self.wfile.write(json.dumps({"success": True}).encode())
        else:
            # 返回404
            self.send_response(404)
            self.send_header("Content-type", "text/plain")
            self.end_headers()
            self.wfile.write(b"404 Not Found")

def run_server(host, port, db_path, ssl_cert=None, ssl_key=None):
    """运行服务器"""
    # 创建反馈收集器
    feedback_collector = FeedbackCollector(db_path)
    
    # 创建服务器
    server = HTTPServer((host, port), FeedbackHandler)
    
    # 设置反馈收集器
    server.RequestHandlerClass.feedback_collector = feedback_collector
    
    # 设置SSL
    if ssl_cert and ssl_key:
        server.socket = ssl.wrap_socket(
            server.socket,
            certfile=ssl_cert,
            keyfile=ssl_key,
            server_side=True
        )
    
    print(f"服务器已启动，地址：http{'s' if ssl_cert and ssl_key else ''}://{host}:{port}")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("服务器已停止")
    finally:
        server.server_close()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="零拷贝机制用户反馈服务器")
    parser.add_argument("--host", default="localhost", help="服务器主机名")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--db", default="feedback.db", help="数据库文件路径")
    parser.add_argument("--ssl-cert", help="SSL证书文件路径")
    parser.add_argument("--ssl-key", help="SSL密钥文件路径")
    
    args = parser.parse_args()
    
    # 创建数据库目录
    os.makedirs(os.path.dirname(os.path.abspath(args.db)), exist_ok=True)
    
    # 运行服务器
    run_server(args.host, args.port, args.db, args.ssl_cert, args.ssl_key)

if __name__ == "__main__":
    main()
