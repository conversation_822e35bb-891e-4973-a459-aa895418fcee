#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
零拷贝机制用户反馈命令行工具

这个脚本提供用户反馈收集的命令行工具，包括：
1. 提交反馈
2. 查看反馈
3. 添加评论
4. 添加投票
5. 更新状态
"""

import os
import sys
import json
import argparse
import datetime
import sqlite3
import uuid
from pathlib import Path

# 导入反馈收集器
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from feedback.feedback_collector import FeedbackCollector

def submit_feedback(args):
    """提交反馈"""
    # 创建反馈收集器
    feedback_collector = FeedbackCollector(args.db)
    
    # 添加反馈
    feedback_id = feedback_collector.add_feedback(
        args.type,
        args.title,
        args.description,
        args.user_name,
        args.user_email,
        args.user_organization,
        args.environment,
        args.severity,
        args.tags.split(",") if args.tags else None,
        None
    )
    
    print(f"反馈已提交，ID：{feedback_id}")

def view_feedback(args):
    """查看反馈"""
    # 创建反馈收集器
    feedback_collector = FeedbackCollector(args.db)
    
    if args.id:
        # 获取指定反馈
        feedback = feedback_collector.get_feedback(args.id)
        
        if feedback:
            print(f"ID：{feedback['id']}")
            print(f"时间：{feedback['timestamp']}")
            print(f"类型：{feedback['type']}")
            print(f"标题：{feedback['title']}")
            print(f"描述：{feedback['description']}")
            print(f"用户：{feedback['user_name'] or '匿名'}")
            print(f"邮箱：{feedback['user_email'] or '无'}")
            print(f"组织：{feedback['user_organization'] or '无'}")
            print(f"环境：{feedback['environment'] or '无'}")
            print(f"严重程度：{feedback['severity'] or '无'}")
            print(f"状态：{feedback['status']}")
            print(f"处理人：{feedback['assignee'] or '无'}")
            print(f"标签：{', '.join(feedback['tags']) if feedback['tags'] else '无'}")
            
            if feedback["comments"]:
                print("\n评论：")
                for comment in feedback["comments"]:
                    print(f"  {comment['timestamp']} {comment['user_name'] or '匿名'}：{comment['content']}")
            
            if feedback["votes"]:
                print("\n投票：")
                upvotes = sum(1 for vote in feedback["votes"] if vote["vote"] > 0)
                downvotes = sum(1 for vote in feedback["votes"] if vote["vote"] < 0)
                print(f"  赞成：{upvotes}，反对：{downvotes}")
        else:
            print(f"未找到ID为{args.id}的反馈")
    else:
        # 获取所有反馈
        feedbacks = feedback_collector.get_all_feedback(args.type, args.status, args.limit)
        
        if feedbacks:
            print(f"共找到{len(feedbacks)}条反馈：")
            for feedback in feedbacks:
                print(f"ID：{feedback['id']}")
                print(f"时间：{feedback['timestamp']}")
                print(f"类型：{feedback['type']}")
                print(f"标题：{feedback['title']}")
                print(f"状态：{feedback['status']}")
                print()
        else:
            print("未找到反馈")

def add_comment(args):
    """添加评论"""
    # 创建反馈收集器
    feedback_collector = FeedbackCollector(args.db)
    
    # 添加评论
    comment_id = feedback_collector.add_comment(
        args.id,
        args.content,
        args.user_name,
        args.user_email
    )
    
    print(f"评论已添加，ID：{comment_id}")

def add_vote(args):
    """添加投票"""
    # 创建反馈收集器
    feedback_collector = FeedbackCollector(args.db)
    
    # 添加投票
    vote_id = feedback_collector.add_vote(
        args.id,
        args.vote,
        args.user_name,
        args.user_email
    )
    
    print(f"投票已添加，ID：{vote_id}")

def update_status(args):
    """更新状态"""
    # 创建反馈收集器
    feedback_collector = FeedbackCollector(args.db)
    
    # 更新反馈状态
    feedback_collector.update_feedback_status(
        args.id,
        args.status,
        args.assignee
    )
    
    print(f"反馈状态已更新为：{args.status}")

def generate_report(args):
    """生成报告"""
    # 创建反馈收集器
    feedback_collector = FeedbackCollector(args.db)
    
    # 生成反馈报告
    report = feedback_collector.generate_feedback_report(args.output)
    
    print(f"反馈报告已生成：{args.output}")
    print(f"总反馈数：{report['total_feedback']}")
    print("按类型统计：")
    for type, count in report["feedback_by_type"].items():
        print(f"  {type}：{count}")
    print("按状态统计：")
    for status, count in report["feedback_by_status"].items():
        print(f"  {status}：{count}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="零拷贝机制用户反馈命令行工具")
    parser.add_argument("--db", default="feedback.db", help="数据库文件路径")
    
    subparsers = parser.add_subparsers(dest="command", help="命令")
    
    # 提交反馈命令
    submit_parser = subparsers.add_parser("submit", help="提交反馈")
    submit_parser.add_argument("--type", required=True, choices=["bug", "feature", "performance", "usability", "documentation"], help="反馈类型")
    submit_parser.add_argument("--title", required=True, help="标题")
    submit_parser.add_argument("--description", required=True, help="描述")
    submit_parser.add_argument("--user-name", help="用户名")
    submit_parser.add_argument("--user-email", help="用户邮箱")
    submit_parser.add_argument("--user-organization", help="用户组织")
    submit_parser.add_argument("--environment", help="环境")
    submit_parser.add_argument("--severity", choices=["low", "medium", "high", "critical"], help="严重程度")
    submit_parser.add_argument("--tags", help="标签，用逗号分隔")
    
    # 查看反馈命令
    view_parser = subparsers.add_parser("view", help="查看反馈")
    view_parser.add_argument("--id", help="反馈ID")
    view_parser.add_argument("--type", choices=["bug", "feature", "performance", "usability", "documentation"], help="反馈类型")
    view_parser.add_argument("--status", choices=["new", "in-progress", "resolved", "closed"], help="反馈状态")
    view_parser.add_argument("--limit", type=int, default=100, help="最大返回数量")
    
    # 添加评论命令
    comment_parser = subparsers.add_parser("comment", help="添加评论")
    comment_parser.add_argument("--id", required=True, help="反馈ID")
    comment_parser.add_argument("--content", required=True, help="评论内容")
    comment_parser.add_argument("--user-name", help="用户名")
    comment_parser.add_argument("--user-email", help="用户邮箱")
    
    # 添加投票命令
    vote_parser = subparsers.add_parser("vote", help="添加投票")
    vote_parser.add_argument("--id", required=True, help="反馈ID")
    vote_parser.add_argument("--vote", required=True, type=int, choices=[-1, 1], help="投票值")
    vote_parser.add_argument("--user-name", help="用户名")
    vote_parser.add_argument("--user-email", help="用户邮箱")
    
    # 更新状态命令
    status_parser = subparsers.add_parser("status", help="更新状态")
    status_parser.add_argument("--id", required=True, help="反馈ID")
    status_parser.add_argument("--status", required=True, choices=["new", "in-progress", "resolved", "closed"], help="反馈状态")
    status_parser.add_argument("--assignee", help="处理人")
    
    # 生成报告命令
    report_parser = subparsers.add_parser("report", help="生成报告")
    report_parser.add_argument("--output", required=True, help="输出文件路径")
    
    args = parser.parse_args()
    
    # 创建数据库目录
    os.makedirs(os.path.dirname(os.path.abspath(args.db)), exist_ok=True)
    
    # 执行命令
    if args.command == "submit":
        submit_feedback(args)
    elif args.command == "view":
        view_feedback(args)
    elif args.command == "comment":
        add_comment(args)
    elif args.command == "vote":
        add_vote(args)
    elif args.command == "status":
        update_status(args)
    elif args.command == "report":
        generate_report(args)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
