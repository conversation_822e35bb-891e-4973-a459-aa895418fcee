//! # Arrow Integration for TFF
//!
//! This crate provides high-performance Arrow integration for the TFF (Transcendental Fractal Framework).
//! It includes support for:
//!
//! - Custom Arrow data types for quantum states, fractal structures, and holographic data
//! - Zero-copy data exchange between Python and Rust
//! - High-performance SIMD-accelerated operations
//! - Memory management optimizations
//! - Serialization and deserialization
//! - Flight protocol integration
//!
//! ## Architecture
//!
//! The crate is organized into several modules:
//!
//! - `types`: Custom Arrow data types (complex numbers, quantum states, etc.)
//! - `zero_copy`: Zero-copy data exchange mechanisms
//! - `internal`: Internal utilities and helpers

#![warn(missing_docs)]

use pyo3::prelude::*;

// Re-export Arrow types for convenience
pub use arrow;

// Module declarations
pub mod types;
pub mod zero_copy;
pub mod internal;

// Feature-gated modules
#[cfg(feature = "flight")]
pub mod flight;

#[cfg(feature = "parquet-support")]
pub mod parquet_integration;

/// Python module initialization
#[pymodule]
fn arrow_integration_new(_py: Python<'_>, m: &PyModule) -> PyResult<()> {
    // Register the Python module
    m.add("__version__", env!("CARGO_PKG_VERSION"))?;

    // Add submodules
    let types = PyModule::new(_py, "types")?;
    types::register_module(_py, types)?;
    m.add_submodule(types)?;

    let zero_copy = PyModule::new(_py, "zero_copy")?;
    zero_copy::register_module(_py, zero_copy)?;
    m.add_submodule(zero_copy)?;

    #[cfg(feature = "flight")]
    {
        let flight = PyModule::new(_py, "flight")?;
        m.add_submodule(flight)?;
    }

    #[cfg(feature = "parquet-support")]
    {
        let parquet = PyModule::new(_py, "parquet")?;
        m.add_submodule(parquet)?;
    }

    // Register module functions
    m.add_function(wrap_pyfunction!(version, m)?)?;

    Ok(())
}

/// Returns the version of the arrow_integration crate
#[pyfunction]
fn version() -> String {
    env!("CARGO_PKG_VERSION").to_string()
}
