//! Memory mapping implementation for zero-copy data exchange

use std::fs::{File, OpenOptions};
use std::io::{Read, Write, Seek, SeekFrom};
use std::path::{Path, PathBuf};
use std::os::unix::fs::OpenOptionsExt;

use memmap2::{Mmap, MmapMut, MmapOptions};

use crate::internal::error::Error;
use crate::internal::Result;

/// Memory-mapped buffer
pub struct MappedBuffer {
    /// Memory-mapped file
    mmap: MmapMut,
    
    /// File handle
    #[allow(dead_code)]
    file: File,
    
    /// Path to the file
    path: PathBuf,
    
    /// Size of the buffer
    size: usize,
}

impl MappedBuffer {
    /// Creates a new memory-mapped buffer
    pub fn new(path: PathBuf, size: usize) -> Result<Self> {
        // Create the file
        let file = OpenOptions::new()
            .read(true)
            .write(true)
            .create(true)
            .truncate(true)
            .mode(0o600)
            .open(&path)?;
        
        // Set the file size
        file.set_len(size as u64)?;
        
        // Memory-map the file
        let mmap = unsafe { MmapOptions::new().map_mut(&file)? };
        
        Ok(Self {
            mmap,
            file,
            path,
            size,
        })
    }
    
    /// Opens an existing memory-mapped buffer
    pub fn open(path: PathBuf) -> Result<Self> {
        // Open the file
        let file = OpenOptions::new()
            .read(true)
            .write(true)
            .open(&path)?;
        
        // Get the file size
        let size = file.metadata()?.len() as usize;
        
        // Memory-map the file
        let mmap = unsafe { MmapOptions::new().map_mut(&file)? };
        
        Ok(Self {
            mmap,
            file,
            path,
            size,
        })
    }
    
    /// Writes data to the buffer
    pub fn write(&mut self, offset: usize, data: &[u8]) -> Result<usize> {
        if offset + data.len() > self.size {
            return Err(Error::ZeroCopy(format!(
                "Write would exceed buffer size: offset={}, data_len={}, buffer_size={}",
                offset, data.len(), self.size
            )));
        }
        
        self.mmap[offset..offset + data.len()].copy_from_slice(data);
        
        Ok(data.len())
    }
    
    /// Reads data from the buffer
    pub fn read(&self, offset: usize, size: usize) -> Result<Vec<u8>> {
        if offset + size > self.size {
            return Err(Error::ZeroCopy(format!(
                "Read would exceed buffer size: offset={}, size={}, buffer_size={}",
                offset, size, self.size
            )));
        }
        
        let mut result = vec![0u8; size];
        result.copy_from_slice(&self.mmap[offset..offset + size]);
        
        Ok(result)
    }
    
    /// Returns the path to the file
    pub fn path(&self) -> &Path {
        &self.path
    }
    
    /// Returns the size of the buffer
    pub fn size(&self) -> usize {
        self.size
    }
    
    /// Flushes the memory-mapped file to disk
    pub fn flush(&self) -> Result<()> {
        self.mmap.flush()?;
        Ok(())
    }
}

/// Memory-mapped file reader
pub struct MappedFileReader {
    /// Memory-mapped file
    mmap: Mmap,
    
    /// File handle
    #[allow(dead_code)]
    file: File,
    
    /// Path to the file
    path: PathBuf,
    
    /// Current position
    position: usize,
}

impl MappedFileReader {
    /// Creates a new memory-mapped file reader
    pub fn new(path: PathBuf) -> Result<Self> {
        // Open the file
        let file = File::open(&path)?;
        
        // Memory-map the file
        let mmap = unsafe { MmapOptions::new().map(&file)? };
        
        Ok(Self {
            mmap,
            file,
            path,
            position: 0,
        })
    }
    
    /// Reads data from the file
    pub fn read(&mut self, size: usize) -> Result<Vec<u8>> {
        let available = self.mmap.len() - self.position;
        let size = std::cmp::min(size, available);
        
        let mut result = vec![0u8; size];
        result.copy_from_slice(&self.mmap[self.position..self.position + size]);
        
        self.position += size;
        
        Ok(result)
    }
    
    /// Sets the current position
    pub fn set_position(&mut self, position: usize) -> Result<()> {
        if position > self.mmap.len() {
            return Err(Error::ZeroCopy(format!(
                "Position out of bounds: position={}, file_size={}",
                position, self.mmap.len()
            )));
        }
        
        self.position = position;
        
        Ok(())
    }
    
    /// Returns the current position
    pub fn position(&self) -> usize {
        self.position
    }
    
    /// Returns the path to the file
    pub fn path(&self) -> &Path {
        &self.path
    }
    
    /// Returns the size of the file
    pub fn size(&self) -> usize {
        self.mmap.len()
    }
}

/// Memory-mapped file writer
pub struct MappedFileWriter {
    /// Memory-mapped file
    mmap: MmapMut,
    
    /// File handle
    #[allow(dead_code)]
    file: File,
    
    /// Path to the file
    path: PathBuf,
    
    /// Current position
    position: usize,
}

impl MappedFileWriter {
    /// Creates a new memory-mapped file writer
    pub fn new(path: PathBuf, size: usize) -> Result<Self> {
        // Create the file
        let file = OpenOptions::new()
            .read(true)
            .write(true)
            .create(true)
            .truncate(true)
            .open(&path)?;
        
        // Set the file size
        file.set_len(size as u64)?;
        
        // Memory-map the file
        let mmap = unsafe { MmapOptions::new().map_mut(&file)? };
        
        Ok(Self {
            mmap,
            file,
            path,
            position: 0,
        })
    }
    
    /// Writes data to the file
    pub fn write(&mut self, data: &[u8]) -> Result<usize> {
        let available = self.mmap.len() - self.position;
        let size = std::cmp::min(data.len(), available);
        
        self.mmap[self.position..self.position + size].copy_from_slice(&data[..size]);
        
        self.position += size;
        
        Ok(size)
    }
    
    /// Sets the current position
    pub fn set_position(&mut self, position: usize) -> Result<()> {
        if position > self.mmap.len() {
            return Err(Error::ZeroCopy(format!(
                "Position out of bounds: position={}, file_size={}",
                position, self.mmap.len()
            )));
        }
        
        self.position = position;
        
        Ok(())
    }
    
    /// Returns the current position
    pub fn position(&self) -> usize {
        self.position
    }
    
    /// Returns the path to the file
    pub fn path(&self) -> &Path {
        &self.path
    }
    
    /// Returns the size of the file
    pub fn size(&self) -> usize {
        self.mmap.len()
    }
    
    /// Flushes the memory-mapped file to disk
    pub fn flush(&mut self) -> Result<()> {
        self.mmap.flush()?;
        Ok(())
    }
}
