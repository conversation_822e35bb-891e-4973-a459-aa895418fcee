//! Large data transfer implementation for zero-copy data exchange

use std::collections::{HashMap, VecDeque};
use std::sync::{<PERSON>, <PERSON>tex, RwLock};
use std::sync::atomic::{AtomicU64, Ordering};
use std::time::{Duration, Instant};

use once_cell::sync::OnceCell;

use crate::internal::error::Error;
use crate::internal::Result;

/// Transfer session status
#[derive(Debu<PERSON>, <PERSON>lone, <PERSON><PERSON>, PartialEq, Eq)]
pub enum TransferSessionStatus {
    /// Session is initialized
    Initialized,
    
    /// Session is in progress
    InProgress,
    
    /// Session is completed
    Completed,
    
    /// Session is failed
    Failed,
    
    /// Session is cancelled
    Cancelled,
}

/// Transfer session
pub struct TransferSession {
    /// Session ID
    id: u64,
    
    /// Session name
    name: String,
    
    /// Total size
    total_size: Option<usize>,
    
    /// Chunk size
    chunk_size: usize,
    
    /// Transferred size
    transferred_size: usize,
    
    /// Status
    status: TransferSessionStatus,
    
    /// Chunk queue
    chunk_queue: VecDeque<Vec<u8>>,
    
    /// Start time
    start_time: Instant,
    
    /// Last update time
    last_update_time: Instant,
}

impl TransferSession {
    /// Creates a new sender session
    pub fn new_sender(id: u64, name: &str, total_size: usize, chunk_size: usize) -> Self {
        Self {
            id,
            name: name.to_string(),
            total_size: Some(total_size),
            chunk_size,
            transferred_size: 0,
            status: TransferSessionStatus::Initialized,
            chunk_queue: VecDeque::new(),
            start_time: Instant::now(),
            last_update_time: Instant::now(),
        }
    }
    
    /// Creates a new receiver session
    pub fn new_receiver(id: u64, name: &str, chunk_size: usize) -> Self {
        Self {
            id,
            name: name.to_string(),
            total_size: None,
            chunk_size,
            transferred_size: 0,
            status: TransferSessionStatus::Initialized,
            chunk_queue: VecDeque::new(),
            start_time: Instant::now(),
            last_update_time: Instant::now(),
        }
    }
    
    /// Sends a chunk
    pub fn send_chunk(&mut self, data: &[u8]) -> Result<usize> {
        if self.status == TransferSessionStatus::Completed {
            return Err(Error::ZeroCopy("Session is already completed".to_string()));
        }
        
        if self.status == TransferSessionStatus::Failed {
            return Err(Error::ZeroCopy("Session is failed".to_string()));
        }
        
        if self.status == TransferSessionStatus::Cancelled {
            return Err(Error::ZeroCopy("Session is cancelled".to_string()));
        }
        
        if let Some(total_size) = self.total_size {
            if self.transferred_size + data.len() > total_size {
                return Err(Error::ZeroCopy(format!(
                    "Chunk would exceed total size: transferred_size={}, chunk_size={}, total_size={}",
                    self.transferred_size, data.len(), total_size
                )));
            }
        }
        
        // Update status
        self.status = TransferSessionStatus::InProgress;
        
        // Add the chunk to the queue
        let chunk = data.to_vec();
        self.chunk_queue.push_back(chunk);
        
        // Update transferred size
        self.transferred_size += data.len();
        
        // Update last update time
        self.last_update_time = Instant::now();
        
        // Check if the transfer is completed
        if let Some(total_size) = self.total_size {
            if self.transferred_size == total_size {
                self.status = TransferSessionStatus::Completed;
            }
        }
        
        Ok(data.len())
    }
    
    /// Receives a chunk
    pub fn receive_chunk(&mut self, timeout: Option<Duration>) -> Result<Vec<u8>> {
        if self.status == TransferSessionStatus::Failed {
            return Err(Error::ZeroCopy("Session is failed".to_string()));
        }
        
        if self.status == TransferSessionStatus::Cancelled {
            return Err(Error::ZeroCopy("Session is cancelled".to_string()));
        }
        
        // Wait for a chunk
        let start_time = Instant::now();
        loop {
            // Check if there is a chunk in the queue
            if let Some(chunk) = self.chunk_queue.pop_front() {
                // Update last update time
                self.last_update_time = Instant::now();
                
                return Ok(chunk);
            }
            
            // Check if the session is completed
            if self.status == TransferSessionStatus::Completed && self.chunk_queue.is_empty() {
                return Ok(Vec::new());
            }
            
            // Check if the timeout has expired
            if let Some(timeout) = timeout {
                if start_time.elapsed() >= timeout {
                    return Ok(Vec::new());
                }
            }
            
            // Sleep for a short time
            std::thread::sleep(Duration::from_millis(1));
        }
    }
    
    /// Returns the session ID
    pub fn id(&self) -> u64 {
        self.id
    }
    
    /// Returns the session name
    pub fn name(&self) -> &str {
        &self.name
    }
    
    /// Returns the total size
    pub fn total_size(&self) -> Option<usize> {
        self.total_size
    }
    
    /// Returns the chunk size
    pub fn chunk_size(&self) -> usize {
        self.chunk_size
    }
    
    /// Returns the transferred size
    pub fn transferred_size(&self) -> usize {
        self.transferred_size
    }
    
    /// Returns the status
    pub fn status(&self) -> TransferSessionStatus {
        self.status
    }
    
    /// Returns the progress (0.0 - 100.0)
    pub fn progress(&self) -> f64 {
        if let Some(total_size) = self.total_size {
            if total_size == 0 {
                return 100.0;
            }
            
            (self.transferred_size as f64 / total_size as f64) * 100.0
        } else {
            0.0
        }
    }
    
    /// Returns the elapsed time
    pub fn elapsed_time(&self) -> Duration {
        self.start_time.elapsed()
    }
    
    /// Returns the time since the last update
    pub fn time_since_last_update(&self) -> Duration {
        self.last_update_time.elapsed()
    }
    
    /// Cancels the session
    pub fn cancel(&mut self) {
        self.status = TransferSessionStatus::Cancelled;
        self.chunk_queue.clear();
    }
}

/// Large transfer manager
pub struct LargeTransferManager {
    /// Next session ID
    next_id: AtomicU64,
    
    /// Sessions
    sessions: RwLock<HashMap<u64, Arc<Mutex<TransferSession>>>>,
}

impl LargeTransferManager {
    /// Creates a new large transfer manager
    pub fn new() -> Self {
        Self {
            next_id: AtomicU64::new(1),
            sessions: RwLock::new(HashMap::new()),
        }
    }
    
    /// Creates a new sender session
    pub fn create_sender_session(&self, name: &str, total_size: usize, chunk_size: usize) -> Result<u64> {
        let id = self.next_id.fetch_add(1, Ordering::SeqCst);
        let session = TransferSession::new_sender(id, name, total_size, chunk_size);
        let session = Arc::new(Mutex::new(session));
        
        let mut sessions = self.sessions.write().unwrap();
        sessions.insert(id, session);
        
        Ok(id)
    }
    
    /// Creates a new receiver session
    pub fn create_receiver_session(&self, name: &str, chunk_size: usize) -> Result<u64> {
        let id = self.next_id.fetch_add(1, Ordering::SeqCst);
        let session = TransferSession::new_receiver(id, name, chunk_size);
        let session = Arc::new(Mutex::new(session));
        
        let mut sessions = self.sessions.write().unwrap();
        sessions.insert(id, session);
        
        Ok(id)
    }
    
    /// Gets a session
    pub fn get_session(&self, id: u64) -> Result<Arc<Mutex<TransferSession>>> {
        let sessions = self.sessions.read().unwrap();
        sessions.get(&id)
            .cloned()
            .ok_or_else(|| Error::ZeroCopy(format!("Session not found: {}", id)))
    }
    
    /// Removes a session
    pub fn remove_session(&self, id: u64) -> Result<()> {
        let mut sessions = self.sessions.write().unwrap();
        sessions.remove(&id)
            .ok_or_else(|| Error::ZeroCopy(format!("Session not found: {}", id)))?;
        
        Ok(())
    }
    
    /// Lists all sessions
    pub fn list_sessions(&self) -> Vec<u64> {
        let sessions = self.sessions.read().unwrap();
        sessions.keys().cloned().collect()
    }
    
    /// Cleans up completed sessions
    pub fn cleanup(&self) -> usize {
        let mut sessions = self.sessions.write().unwrap();
        let mut to_remove = Vec::new();
        
        for (id, session) in sessions.iter() {
            let session = session.lock().unwrap();
            if session.status() == TransferSessionStatus::Completed {
                to_remove.push(*id);
            }
        }
        
        for id in &to_remove {
            sessions.remove(id);
        }
        
        to_remove.len()
    }
}

// Global large transfer manager
static LARGE_TRANSFER_MANAGER: OnceCell<LargeTransferManager> = OnceCell::new();

/// Returns the global large transfer manager
pub fn global_large_transfer_manager() -> &'static LargeTransferManager {
    LARGE_TRANSFER_MANAGER.get_or_init(|| LargeTransferManager::new())
}
