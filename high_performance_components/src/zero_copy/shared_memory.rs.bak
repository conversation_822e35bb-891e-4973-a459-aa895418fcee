//! Shared memory implementation for zero-copy data exchange

use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::{<PERSON>, <PERSON>tex, RwLock};
use std::fs::{File, OpenOptions};
use std::io::{Read, Write, Seek, Seek<PERSON>rom};
use std::os::unix::io::AsRawFd;
use std::os::unix::fs::OpenOptionsExt;

use memmap2::{MmapMut, MmapOptions};
use once_cell::sync::OnceCell;

use crate::internal::error::Error;
use crate::internal::Result;

/// Shared memory region
pub struct SharedMemoryRegion {
    /// Name of the region
    name: String,
    
    /// Size of the region
    size: usize,
    
    /// Memory-mapped file
    mmap: MmapMut,
    
    /// File handle
    #[allow(dead_code)]
    file: File,
}

impl SharedMemoryRegion {
    /// Creates a new shared memory region
    pub fn new(name: &str, size: usize, base_dir: &Path) -> Result<Self> {
        let path = base_dir.join(format!("{}.shm", name));
        
        // Create the file
        let file = OpenOptions::new()
            .read(true)
            .write(true)
            .create(true)
            .truncate(true)
            .mode(0o600)
            .open(&path)?;
        
        // Set the file size
        file.set_len(size as u64)?;
        
        // Memory-map the file
        let mmap = unsafe { MmapOptions::new().map_mut(&file)? };
        
        Ok(Self {
            name: name.to_string(),
            size,
            mmap,
            file,
        })
    }
    
    /// Opens an existing shared memory region
    pub fn open(name: &str, base_dir: &Path) -> Result<Self> {
        let path = base_dir.join(format!("{}.shm", name));
        
        // Open the file
        let file = OpenOptions::new()
            .read(true)
            .write(true)
            .open(&path)?;
        
        // Get the file size
        let size = file.metadata()?.len() as usize;
        
        // Memory-map the file
        let mmap = unsafe { MmapOptions::new().map_mut(&file)? };
        
        Ok(Self {
            name: name.to_string(),
            size,
            mmap,
            file,
        })
    }
    
    /// Writes data to the shared memory region
    pub fn write(&self, offset: usize, data: &[u8]) -> Result<usize> {
        if offset + data.len() > self.size {
            return Err(Error::ZeroCopy(format!(
                "Write would exceed region size: offset={}, data_len={}, region_size={}",
                offset, data.len(), self.size
            )));
        }
        
        let mut mmap = &mut self.mmap.as_mut();
        mmap[offset..offset + data.len()].copy_from_slice(data);
        
        Ok(data.len())
    }
    
    /// Reads data from the shared memory region
    pub fn read(&self, offset: usize, size: usize) -> Result<Vec<u8>> {
        if offset + size > self.size {
            return Err(Error::ZeroCopy(format!(
                "Read would exceed region size: offset={}, size={}, region_size={}",
                offset, size, self.size
            )));
        }
        
        let mut result = vec![0u8; size];
        result.copy_from_slice(&self.mmap[offset..offset + size]);
        
        Ok(result)
    }
    
    /// Returns the name of the region
    pub fn name(&self) -> &str {
        &self.name
    }
    
    /// Returns the size of the region
    pub fn size(&self) -> usize {
        self.size
    }
    
    /// Flushes the memory-mapped file to disk
    pub fn flush(&self) -> Result<()> {
        self.mmap.flush()?;
        Ok(())
    }
}

/// Shared memory manager
pub struct SharedMemoryManager {
    /// Base directory for shared memory files
    base_dir: PathBuf,
    
    /// Shared memory regions
    regions: RwLock<HashMap<String, Arc<SharedMemoryRegion>>>,
}

impl SharedMemoryManager {
    /// Creates a new shared memory manager
    pub fn new(base_dir: PathBuf) -> Self {
        Self {
            base_dir,
            regions: RwLock::new(HashMap::new()),
        }
    }
    
    /// Creates a new shared memory region
    pub fn create_region(&self, name: &str, size: usize) -> Result<Arc<SharedMemoryRegion>> {
        let region = SharedMemoryRegion::new(name, size, &self.base_dir)?;
        let region = Arc::new(region);
        
        let mut regions = self.regions.write().unwrap();
        regions.insert(name.to_string(), region.clone());
        
        Ok(region)
    }
    
    /// Opens an existing shared memory region
    pub fn open_region(&self, name: &str) -> Result<Arc<SharedMemoryRegion>> {
        // Check if the region is already open
        {
            let regions = self.regions.read().unwrap();
            if let Some(region) = regions.get(name) {
                return Ok(region.clone());
            }
        }
        
        // Open the region
        let region = SharedMemoryRegion::open(name, &self.base_dir)?;
        let region = Arc::new(region);
        
        let mut regions = self.regions.write().unwrap();
        regions.insert(name.to_string(), region.clone());
        
        Ok(region)
    }
    
    /// Gets a shared memory region
    pub fn get_region(&self, name: &str) -> Result<Arc<SharedMemoryRegion>> {
        let regions = self.regions.read().unwrap();
        regions.get(name)
            .cloned()
            .ok_or_else(|| Error::ZeroCopy(format!("Region not found: {}", name)))
    }
    
    /// Removes a shared memory region
    pub fn remove_region(&self, name: &str) -> Result<()> {
        // Remove the region from the map
        {
            let mut regions = self.regions.write().unwrap();
            regions.remove(name);
        }
        
        // Remove the file
        let path = self.base_dir.join(format!("{}.shm", name));
        if path.exists() {
            std::fs::remove_file(path)?;
        }
        
        Ok(())
    }
    
    /// Lists all shared memory regions
    pub fn list_regions(&self) -> Vec<String> {
        let regions = self.regions.read().unwrap();
        regions.keys().cloned().collect()
    }
}

// Global shared memory manager
static SHARED_MEMORY_MANAGER: OnceCell<Mutex<SharedMemoryManager>> = OnceCell::new();

/// Initializes the global shared memory manager
pub fn init_shared_memory_manager(base_dir: PathBuf) -> Result<()> {
    // Create the base directory if it doesn't exist
    if !base_dir.exists() {
        std::fs::create_dir_all(&base_dir)?;
    }
    
    // Initialize the global shared memory manager
    let manager = SharedMemoryManager::new(base_dir);
    SHARED_MEMORY_MANAGER.set(Mutex::new(manager))
        .map_err(|_| Error::ZeroCopy("Shared memory manager already initialized".to_string()))?;
    
    Ok(())
}

/// Returns the global shared memory manager
pub fn global_shared_memory_manager() -> Result<impl SharedMemoryManagerTrait> {
    let manager = SHARED_MEMORY_MANAGER.get()
        .ok_or_else(|| Error::ZeroCopy("Shared memory manager not initialized".to_string()))?;
    
    Ok(GlobalSharedMemoryManager(manager))
}

/// Shared memory manager trait
pub trait SharedMemoryManagerTrait {
    /// Creates a new shared memory region
    fn create_region(&self, name: &str, size: usize) -> Result<Arc<SharedMemoryRegion>>;
    
    /// Opens an existing shared memory region
    fn open_region(&self, name: &str) -> Result<Arc<SharedMemoryRegion>>;
    
    /// Gets a shared memory region
    fn get_region(&self, name: &str) -> Result<Arc<SharedMemoryRegion>>;
    
    /// Removes a shared memory region
    fn remove_region(&self, name: &str) -> Result<()>;
    
    /// Lists all shared memory regions
    fn list_regions(&self) -> Vec<String>;
}

/// Global shared memory manager wrapper
struct GlobalSharedMemoryManager<'a>(&'a Mutex<SharedMemoryManager>);

impl<'a> SharedMemoryManagerTrait for GlobalSharedMemoryManager<'a> {
    fn create_region(&self, name: &str, size: usize) -> Result<Arc<SharedMemoryRegion>> {
        self.0.lock().unwrap().create_region(name, size)
    }
    
    fn open_region(&self, name: &str) -> Result<Arc<SharedMemoryRegion>> {
        self.0.lock().unwrap().open_region(name)
    }
    
    fn get_region(&self, name: &str) -> Result<Arc<SharedMemoryRegion>> {
        self.0.lock().unwrap().get_region(name)
    }
    
    fn remove_region(&self, name: &str) -> Result<()> {
        self.0.lock().unwrap().remove_region(name)
    }
    
    fn list_regions(&self) -> Vec<String> {
        self.0.lock().unwrap().list_regions()
    }
}
