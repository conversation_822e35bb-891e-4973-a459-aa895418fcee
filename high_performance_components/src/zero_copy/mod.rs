//! Zero-copy mechanisms for efficient data exchange

use std::collections::HashMap;
use pyo3::prelude::*;

pub mod shared_memory;
pub mod memory_mapping;
pub mod memory_pool;
pub mod large_transfer;

/// Register the zero_copy module
pub fn register_module(_py: Python, m: &PyModule) -> PyResult<()> {
    // Register shared memory functions
    #[pyfn(m)]
    fn py_init_shared_memory_manager(base_dir: &str) -> PyResult<()> {
        shared_memory::init_shared_memory_manager(base_dir.into()).map_err(|e| e.into())
    }

    #[pyfn(m)]
    fn py_create_shared_memory_region(name: &str, size: usize) -> PyResult<()> {
        let manager = shared_memory::global_shared_memory_manager()
            .map_err(|e| e.into())?;
        manager.create_region(name, size).map(|_| ()).map_err(|e| e.into())
    }

    #[pyfn(m)]
    fn py_open_shared_memory_region(name: &str) -> PyResult<()> {
        let manager = shared_memory::global_shared_memory_manager()
            .map_err(|e| e.into())?;
        manager.open_region(name).map(|_| ()).map_err(|e| e.into())
    }

    #[pyfn(m)]
    fn py_remove_shared_memory_region(name: &str) -> PyResult<()> {
        let manager = shared_memory::global_shared_memory_manager()
            .map_err(|e| e.into())?;
        manager.remove_region(name).map_err(|e| e.into())
    }

    #[pyfn(m)]
    fn py_list_shared_memory_regions() -> PyResult<Vec<String>> {
        let manager = shared_memory::global_shared_memory_manager()
            .map_err(|e| e.into())?;
        Ok(manager.list_regions())
    }

    #[pyfn(m)]
    fn py_write_to_shared_memory(name: &str, offset: usize, data: &[u8]) -> PyResult<usize> {
        let manager = shared_memory::global_shared_memory_manager()
            .map_err(|e| e.into())?;
        let region = manager.get_region(name).map_err(|e| e.into())?;
        region.write(offset, data).map_err(|e| e.into())
    }

    #[pyfn(m)]
    fn py_read_from_shared_memory(name: &str, offset: usize, size: usize) -> PyResult<Vec<u8>> {
        let manager = shared_memory::global_shared_memory_manager()
            .map_err(|e| e.into())?;
        let region = manager.get_region(name).map_err(|e| e.into())?;
        region.read(offset, size).map_err(|e| e.into())
    }

    // Register memory mapping functions
    #[pyfn(m)]
    fn py_create_mapped_file(path: &str, size: usize) -> PyResult<()> {
        let mut writer = memory_mapping::MappedFileWriter::new(path.into(), size)
            .map_err(|e| e.into())?;
        writer.flush().map_err(|e| e.into())
    }

    #[pyfn(m)]
    fn py_open_mapped_file(path: &str) -> PyResult<()> {
        memory_mapping::MappedFileReader::new(path.into()).map(|_| ()).map_err(|e| e.into())
    }

    #[pyfn(m)]
    fn py_write_to_mapped_file(path: &str, offset: usize, data: &[u8]) -> PyResult<usize> {
        let mut writer = memory_mapping::MappedFileWriter::new(path.into(), offset + data.len())
            .map_err(|e| e.into())?;
        writer.set_position(offset).map_err(|e| e.into())?;
        writer.write(data).map_err(|e| e.into())
    }

    #[pyfn(m)]
    fn py_read_from_mapped_file(path: &str, offset: usize, size: usize) -> PyResult<Vec<u8>> {
        let mut reader = memory_mapping::MappedFileReader::new(path.into())
            .map_err(|e| e.into())?;
        reader.set_position(offset).map_err(|e| e.into())?;
        reader.read(size).map_err(|e| e.into())
    }

    // Register memory pool functions
    #[pyfn(m)]
    fn py_allocate_buffer(size: usize) -> PyResult<u64> {
        let buffer = memory_pool::global_memory_pool().allocate(size);
        Ok(buffer.id)
    }

    #[pyfn(m)]
    fn py_deallocate_buffer(id: u64) -> PyResult<bool> {
        Ok(memory_pool::global_memory_pool().deallocate(id))
    }

    #[pyfn(m)]
    fn py_get_memory_pool_stats() -> PyResult<HashMap<String, usize>> {
        let stats = memory_pool::global_memory_pool().get_stats();
        let mut result = HashMap::new();
        result.insert("total_allocations".to_string(), stats.total_allocations);
        result.insert("total_deallocations".to_string(), stats.total_deallocations);
        result.insert("active_allocations".to_string(), stats.active_allocations);
        result.insert("total_bytes_allocated".to_string(), stats.total_bytes_allocated);
        result.insert("total_bytes_deallocated".to_string(), stats.total_bytes_deallocated);
        result.insert("active_bytes".to_string(), stats.active_bytes);
        Ok(result)
    }

    #[pyfn(m)]
    fn py_clear_memory_pool() -> PyResult<()> {
        memory_pool::global_memory_pool().clear();
        Ok(())
    }

    #[pyfn(m)]
    fn py_shrink_memory_pool() -> PyResult<usize> {
        Ok(memory_pool::global_memory_pool().shrink())
    }

    // Register large transfer functions
    #[pyfn(m)]
    fn py_create_sender_session(name: &str, total_size: usize, chunk_size: usize) -> PyResult<u64> {
        large_transfer::global_large_transfer_manager().create_sender_session(name, total_size, chunk_size)
            .map_err(|e| e.into())
    }

    #[pyfn(m)]
    fn py_create_receiver_session(name: &str, chunk_size: usize) -> PyResult<u64> {
        large_transfer::global_large_transfer_manager().create_receiver_session(name, chunk_size)
            .map_err(|e| e.into())
    }

    #[pyfn(m)]
    fn py_send_chunk(session_id: u64, data: &[u8]) -> PyResult<usize> {
        let session = large_transfer::global_large_transfer_manager().get_session(session_id)
            .map_err(|e| e.into())?;
        session.send_chunk(data).map_err(|e| e.into())
    }

    #[pyfn(m)]
    fn py_receive_chunk(session_id: u64) -> PyResult<Vec<u8>> {
        let session = large_transfer::global_large_transfer_manager().get_session(session_id)
            .map_err(|e| e.into())?;
        session.receive_chunk(None).map_err(|e| e.into())
    }

    #[pyfn(m)]
    fn py_get_session_status(session_id: u64) -> PyResult<String> {
        let session = large_transfer::global_large_transfer_manager().get_session(session_id)
            .map_err(|e| e.into())?;
        Ok(format!("{:?}", session.status()).to_lowercase())
    }

    #[pyfn(m)]
    fn py_get_session_progress(session_id: u64) -> PyResult<f64> {
        let session = large_transfer::global_large_transfer_manager().get_session(session_id)
            .map_err(|e| e.into())?;
        Ok(session.progress())
    }

    Ok(())
}
