//! Memory pool implementation for zero-copy data exchange

use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::sync::atomic::{AtomicU64, Ordering};

use once_cell::sync::OnceCell;

use crate::internal::error::Error;
use crate::internal::Result;

/// Memory buffer
pub struct MemoryBuffer {
    /// Buffer ID
    pub id: u64,
    
    /// Buffer data
    pub data: Vec<u8>,
}

impl MemoryBuffer {
    /// Creates a new memory buffer
    pub fn new(id: u64, size: usize) -> Self {
        Self {
            id,
            data: vec![0; size],
        }
    }
    
    /// Returns the size of the buffer
    pub fn size(&self) -> usize {
        self.data.len()
    }
}

/// Memory pool statistics
#[derive(Debug, <PERSON>lone, Copy)]
pub struct MemoryPoolStats {
    /// Total number of allocations
    pub total_allocations: usize,
    
    /// Total number of deallocations
    pub total_deallocations: usize,
    
    /// Number of active allocations
    pub active_allocations: usize,
    
    /// Total bytes allocated
    pub total_bytes_allocated: usize,
    
    /// Total bytes deallocated
    pub total_bytes_deallocated: usize,
    
    /// Active bytes
    pub active_bytes: usize,
}

/// Memory pool
pub struct MemoryPool {
    /// Next buffer ID
    next_id: AtomicU64,
    
    /// Buffers
    buffers: Mutex<HashMap<u64, Arc<MemoryBuffer>>>,
    
    /// Statistics
    stats: Mutex<MemoryPoolStats>,
}

impl MemoryPool {
    /// Creates a new memory pool
    pub fn new() -> Self {
        Self {
            next_id: AtomicU64::new(1),
            buffers: Mutex::new(HashMap::new()),
            stats: Mutex::new(MemoryPoolStats {
                total_allocations: 0,
                total_deallocations: 0,
                active_allocations: 0,
                total_bytes_allocated: 0,
                total_bytes_deallocated: 0,
                active_bytes: 0,
            }),
        }
    }
    
    /// Allocates a new buffer
    pub fn allocate(&self, size: usize) -> Arc<MemoryBuffer> {
        let id = self.next_id.fetch_add(1, Ordering::SeqCst);
        let buffer = Arc::new(MemoryBuffer::new(id, size));
        
        // Update statistics
        {
            let mut stats = self.stats.lock().unwrap();
            stats.total_allocations += 1;
            stats.active_allocations += 1;
            stats.total_bytes_allocated += size;
            stats.active_bytes += size;
        }
        
        // Store the buffer
        {
            let mut buffers = self.buffers.lock().unwrap();
            buffers.insert(id, buffer.clone());
        }
        
        buffer
    }
    
    /// Deallocates a buffer
    pub fn deallocate(&self, id: u64) -> bool {
        let buffer = {
            let mut buffers = self.buffers.lock().unwrap();
            buffers.remove(&id)
        };
        
        if let Some(buffer) = buffer {
            // Update statistics
            let size = buffer.size();
            let mut stats = self.stats.lock().unwrap();
            stats.total_deallocations += 1;
            stats.active_allocations -= 1;
            stats.total_bytes_deallocated += size;
            stats.active_bytes -= size;
            
            true
        } else {
            false
        }
    }
    
    /// Gets a buffer
    pub fn get(&self, id: u64) -> Option<Arc<MemoryBuffer>> {
        let buffers = self.buffers.lock().unwrap();
        buffers.get(&id).cloned()
    }
    
    /// Gets the statistics
    pub fn get_stats(&self) -> MemoryPoolStats {
        *self.stats.lock().unwrap()
    }
    
    /// Clears the memory pool
    pub fn clear(&self) {
        let mut buffers = self.buffers.lock().unwrap();
        let mut stats = self.stats.lock().unwrap();
        
        // Update statistics
        stats.total_deallocations += buffers.len();
        stats.active_allocations = 0;
        stats.total_bytes_deallocated += stats.active_bytes;
        stats.active_bytes = 0;
        
        // Clear buffers
        buffers.clear();
    }
    
    /// Shrinks the memory pool
    pub fn shrink(&self) -> usize {
        let mut buffers = self.buffers.lock().unwrap();
        let capacity_before = buffers.capacity();
        buffers.shrink_to_fit();
        let capacity_after = buffers.capacity();
        
        capacity_before - capacity_after
    }
}

// Global memory pool
static MEMORY_POOL: OnceCell<MemoryPool> = OnceCell::new();

/// Returns the global memory pool
pub fn global_memory_pool() -> &'static MemoryPool {
    MEMORY_POOL.get_or_init(|| MemoryPool::new())
}
