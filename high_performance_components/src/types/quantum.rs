//! Quantum state types for <PERSON>

use std::collections::HashMap;
use pyo3::prelude::*;
use num_complex::Complex64;
use serde::{Serialize, Deserialize};

use crate::internal::error::Error;
use crate::internal::Result;
use crate::types::complex::PyComplex;

/// Quantum state type
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum QuantumStateType {
    /// Pure state (state vector)
    Pure,
    
    /// Mixed state (density matrix)
    Mixed,
    
    /// Stabilizer state
    Stabilizer,
    
    /// Matrix product state
    MatrixProduct,
}

/// Quantum state array
#[pyclass]
#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize)]
pub struct QuantumStateArray {
    /// State vector or density matrix
    pub state: Vec<Complex64>,
    
    /// Dimensions of the state
    pub dimensions: Vec<usize>,
    
    /// Type of quantum state
    pub state_type: QuantumStateType,
    
    /// Metadata
    pub metadata: HashMap<String, String>,
}

/// Python quantum state array
#[pyclass]
#[derive(<PERSON><PERSON>)]
pub struct PyQuantumStateArray {
    /// Inner quantum state array
    pub inner: QuantumStateArray,
}

#[pymethods]
impl PyQuantumStateArray {
    /// Creates a new quantum state array
    #[new]
    fn new(state: Vec<Complex64>, dimensions: Vec<usize>) -> Self {
        let inner = QuantumStateArray {
            state,
            dimensions,
            state_type: QuantumStateType::Pure,
            metadata: HashMap::new(),
        };
        Self { inner }
    }
    
    /// Returns the state vector
    #[getter]
    fn state(&self) -> Vec<PyComplex> {
        self.inner.state.iter().map(|c| PyComplex::from(*c)).collect()
    }
    
    /// Returns the dimensions of the state
    #[getter]
    fn dimensions(&self) -> Vec<usize> {
        self.inner.dimensions.clone()
    }
    
    /// Returns the type of quantum state
    #[getter]
    fn state_type(&self) -> String {
        match self.inner.state_type {
            QuantumStateType::Pure => "pure".to_string(),
            QuantumStateType::Mixed => "mixed".to_string(),
            QuantumStateType::Stabilizer => "stabilizer".to_string(),
            QuantumStateType::MatrixProduct => "matrix_product".to_string(),
        }
    }
    
    /// Returns the metadata
    #[getter]
    fn metadata(&self) -> HashMap<String, String> {
        self.inner.metadata.clone()
    }
    
    /// Sets the metadata
    #[setter]
    fn set_metadata(&mut self, metadata: HashMap<String, String>) {
        self.inner.metadata = metadata;
    }
    
    /// Serializes the array to bytes
    fn serialize(&self) -> PyResult<Vec<u8>> {
        bincode::serialize(&self.inner)
            .map_err(|e| Error::Serialization(e).into())
    }
    
    /// Deserializes the array from bytes
    fn deserialize(&self, data: &[u8]) -> PyResult<Self> {
        let inner = bincode::deserialize(data)
            .map_err(|e| Error::Serialization(e).into())?;
        Ok(Self { inner })
    }
    
    /// Computes the norm of the state
    fn norm(&self) -> f64 {
        let mut norm_squared = 0.0;
        for c in &self.inner.state {
            norm_squared += c.norm_sqr();
        }
        norm_squared.sqrt()
    }
    
    /// Normalizes the state
    fn normalize(&mut self) {
        let norm = self.norm();
        if norm > 0.0 {
            for c in &mut self.inner.state {
                *c /= Complex64::new(norm, 0.0);
            }
        }
    }
    
    /// Computes the expectation value of an operator
    fn expectation_value(&self, operator: Vec<Vec<PyComplex>>) -> PyResult<PyComplex> {
        // Convert operator to Complex64
        let operator: Vec<Vec<Complex64>> = operator.into_iter()
            .map(|row| row.into_iter().map(|c| c.into()).collect())
            .collect();
        
        // Check dimensions
        let state_size = self.inner.state.len();
        let operator_size = operator.len();
        if operator_size == 0 || operator[0].len() != operator_size {
            return Err(Error::QuantumState("Operator must be a square matrix".to_string()).into());
        }
        if state_size != operator_size {
            return Err(Error::QuantumState("Operator dimensions do not match state dimensions".to_string()).into());
        }
        
        // Compute expectation value
        let mut result = Complex64::new(0.0, 0.0);
        for i in 0..state_size {
            for j in 0..state_size {
                result += self.inner.state[i].conj() * operator[i][j] * self.inner.state[j];
            }
        }
        
        Ok(result.into())
    }
    
    /// String representation
    fn __repr__(&self) -> String {
        format!("QuantumStateArray(dimensions={:?}, type={})", 
                self.inner.dimensions, self.state_type())
    }
    
    /// String representation
    fn __str__(&self) -> String {
        self.__repr__()
    }
}
