//! Fractal structure types for Arrow

use std::collections::HashMap;
use pyo3::prelude::*;
use serde::{Serialize, Deserialize};

use crate::internal::error::Error;
use crate::internal::Result;

/// Fractal node
#[pyclass]
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct FractalNode {
    /// Node ID
    pub id: usize,
    
    /// Node position
    pub position: Vec<f64>,
    
    /// Node properties
    pub properties: HashMap<String, String>,
}

/// Python fractal node
#[pyclass]
#[derive(Clone)]
pub struct PyFractalNode {
    /// Node ID
    #[pyo3(get, set)]
    pub id: usize,
    
    /// Node position
    #[pyo3(get, set)]
    pub position: Vec<f64>,
    
    /// Node properties
    #[pyo3(get, set)]
    pub properties: HashMap<String, String>,
}

#[pymethods]
impl PyFractalNode {
    /// Creates a new fractal node
    #[new]
    fn new(id: usize, position: Vec<f64>, properties: Option<HashMap<String, String>>) -> Self {
        Self {
            id,
            position,
            properties: properties.unwrap_or_default(),
        }
    }
}

impl From<PyFractalNode> for FractalNode {
    fn from(node: PyFractalNode) -> Self {
        Self {
            id: node.id,
            position: node.position,
            properties: node.properties,
        }
    }
}

impl From<FractalNode> for PyFractalNode {
    fn from(node: FractalNode) -> Self {
        Self {
            id: node.id,
            position: node.position,
            properties: node.properties,
        }
    }
}

/// Fractal connection
#[pyclass]
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct FractalConnection {
    /// Source node ID
    pub source: usize,
    
    /// Target node ID
    pub target: usize,
    
    /// Connection weight
    pub weight: f64,
    
    /// Connection properties
    pub properties: HashMap<String, String>,
}

/// Python fractal connection
#[pyclass]
#[derive(Clone)]
pub struct PyFractalConnection {
    /// Source node ID
    #[pyo3(get, set)]
    pub source: usize,
    
    /// Target node ID
    #[pyo3(get, set)]
    pub target: usize,
    
    /// Connection weight
    #[pyo3(get, set)]
    pub weight: f64,
    
    /// Connection properties
    #[pyo3(get, set)]
    pub properties: HashMap<String, String>,
}

#[pymethods]
impl PyFractalConnection {
    /// Creates a new fractal connection
    #[new]
    fn new(source: usize, target: usize, weight: f64, properties: Option<HashMap<String, String>>) -> Self {
        Self {
            source,
            target,
            weight,
            properties: properties.unwrap_or_default(),
        }
    }
}

impl From<PyFractalConnection> for FractalConnection {
    fn from(connection: PyFractalConnection) -> Self {
        Self {
            source: connection.source,
            target: connection.target,
            weight: connection.weight,
            properties: connection.properties,
        }
    }
}

impl From<FractalConnection> for PyFractalConnection {
    fn from(connection: FractalConnection) -> Self {
        Self {
            source: connection.source,
            target: connection.target,
            weight: connection.weight,
            properties: connection.properties,
        }
    }
}

/// Fractal structure type
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FractalStructureType {
    /// Sierpinski triangle
    Sierpinski,
    
    /// Mandelbrot set
    Mandelbrot,
    
    /// Julia set
    Julia,
    
    /// Custom fractal
    Custom,
}

/// Fractal structure array
#[pyclass]
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct FractalStructureArray {
    /// Nodes
    pub nodes: Vec<FractalNode>,
    
    /// Connections
    pub connections: Vec<FractalConnection>,
    
    /// Metadata
    pub metadata: HashMap<String, String>,
    
    /// Dimension
    pub dimension: usize,
    
    /// Depth
    pub depth: usize,
    
    /// Branching factor
    pub branching_factor: usize,
    
    /// Type of fractal structure
    pub structure_type: FractalStructureType,
}

/// Python fractal structure array
#[pyclass]
#[derive(Clone)]
pub struct PyFractalStructureArray {
    /// Inner fractal structure array
    pub inner: FractalStructureArray,
}

#[pymethods]
impl PyFractalStructureArray {
    /// Creates a new fractal structure array
    #[new]
    fn new(
        nodes: Vec<PyFractalNode>,
        connections: Vec<PyFractalConnection>,
        metadata: HashMap<String, String>,
        dimension: usize,
        depth: usize,
        branching_factor: usize,
    ) -> Self {
        let nodes = nodes.into_iter().map(|n| n.into()).collect();
        let connections = connections.into_iter().map(|c| c.into()).collect();
        
        let inner = FractalStructureArray {
            nodes,
            connections,
            metadata,
            dimension,
            depth,
            branching_factor,
            structure_type: FractalStructureType::Custom,
        };
        
        Self { inner }
    }
    
    /// Returns the nodes
    #[getter]
    fn nodes(&self) -> Vec<PyFractalNode> {
        self.inner.nodes.iter().map(|n| n.clone().into()).collect()
    }
    
    /// Returns the connections
    #[getter]
    fn connections(&self) -> Vec<PyFractalConnection> {
        self.inner.connections.iter().map(|c| c.clone().into()).collect()
    }
    
    /// Returns the metadata
    #[getter]
    fn metadata(&self) -> HashMap<String, String> {
        self.inner.metadata.clone()
    }
    
    /// Returns the dimension
    #[getter]
    fn dimension(&self) -> usize {
        self.inner.dimension
    }
    
    /// Returns the depth
    #[getter]
    fn depth(&self) -> usize {
        self.inner.depth
    }
    
    /// Returns the branching factor
    #[getter]
    fn branching_factor(&self) -> usize {
        self.inner.branching_factor
    }
    
    /// Returns the type of fractal structure
    #[getter]
    fn structure_type(&self) -> String {
        match self.inner.structure_type {
            FractalStructureType::Sierpinski => "sierpinski".to_string(),
            FractalStructureType::Mandelbrot => "mandelbrot".to_string(),
            FractalStructureType::Julia => "julia".to_string(),
            FractalStructureType::Custom => "custom".to_string(),
        }
    }
    
    /// Serializes the array to bytes
    fn serialize(&self) -> PyResult<Vec<u8>> {
        bincode::serialize(&self.inner)
            .map_err(|e| Error::Serialization(e).into())
    }
    
    /// Deserializes the array from bytes
    fn deserialize(&self, data: &[u8]) -> PyResult<Self> {
        let inner = bincode::deserialize(data)
            .map_err(|e| Error::Serialization(e).into())?;
        Ok(Self { inner })
    }
    
    /// String representation
    fn __repr__(&self) -> String {
        format!("FractalStructureArray(nodes={}, connections={}, dimension={}, depth={})",
                self.inner.nodes.len(), self.inner.connections.len(),
                self.inner.dimension, self.inner.depth)
    }
    
    /// String representation
    fn __str__(&self) -> String {
        self.__repr__()
    }
}
