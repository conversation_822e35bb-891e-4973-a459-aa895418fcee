//! Complex number types for Arrow

use std::fmt;
use std::ops::{Add, Sub, Mul, Div};
use std::collections::HashMap;

use pyo3::prelude::*;
use pyo3::types::PyComplex as PyComplexType;
use num_complex::Complex64;
use serde::{Serialize, Deserialize};

use crate::internal::error::Error;
use crate::internal::Result;

/// Python complex number wrapper
#[pyclass]
#[derive(<PERSON><PERSON>, Debug)]
pub struct PyComplex {
    /// Real part
    #[pyo3(get)]
    pub real: f64,
    
    /// Imaginary part
    #[pyo3(get)]
    pub imag: f64,
}

#[pymethods]
impl PyComplex {
    /// Creates a new complex number
    #[new]
    fn new(real: f64, imag: f64) -> Self {
        Self { real, imag }
    }
    
    /// Returns the complex conjugate
    fn conjugate(&self) -> Self {
        Self {
            real: self.real,
            imag: -self.imag,
        }
    }
    
    /// Returns the absolute value (modulus)
    fn abs(&self) -> f64 {
        (self.real * self.real + self.imag * self.imag).sqrt()
    }
    
    /// Returns the phase (argument)
    fn phase(&self) -> f64 {
        self.imag.atan2(self.real)
    }
    
    /// Converts to a Python complex number
    fn to_python(&self, py: Python) -> PyResult<PyObject> {
        let complex = PyComplexType::from_doubles(py, self.real, self.imag);
        Ok(complex.into())
    }
    
    /// String representation
    fn __repr__(&self) -> String {
        if self.imag >= 0.0 {
            format!("({} + {}j)", self.real, self.imag)
        } else {
            format!("({} - {}j)", self.real, -self.imag)
        }
    }
    
    /// String representation
    fn __str__(&self) -> String {
        self.__repr__()
    }
    
    /// Addition
    fn __add__(&self, other: &Self) -> Self {
        Self {
            real: self.real + other.real,
            imag: self.imag + other.imag,
        }
    }
    
    /// Subtraction
    fn __sub__(&self, other: &Self) -> Self {
        Self {
            real: self.real - other.real,
            imag: self.imag - other.imag,
        }
    }
    
    /// Multiplication
    fn __mul__(&self, other: &Self) -> Self {
        Self {
            real: self.real * other.real - self.imag * other.imag,
            imag: self.real * other.imag + self.imag * other.real,
        }
    }
    
    /// Division
    fn __truediv__(&self, other: &Self) -> Self {
        let denom = other.real * other.real + other.imag * other.imag;
        Self {
            real: (self.real * other.real + self.imag * other.imag) / denom,
            imag: (self.imag * other.real - self.real * other.imag) / denom,
        }
    }
}

impl From<Complex64> for PyComplex {
    fn from(c: Complex64) -> Self {
        Self {
            real: c.re,
            imag: c.im,
        }
    }
}

impl From<PyComplex> for Complex64 {
    fn from(c: PyComplex) -> Self {
        Complex64::new(c.real, c.imag)
    }
}

/// Complex array
#[pyclass]
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct ComplexArray {
    /// Complex values
    pub values: Vec<Complex64>,
    
    /// Metadata
    pub metadata: HashMap<String, String>,
}

#[pymethods]
impl ComplexArray {
    /// Creates a new complex array
    #[new]
    fn new(values: Vec<PyComplex>, metadata: Option<HashMap<String, String>>) -> Self {
        let values = values.into_iter().map(|c| c.into()).collect();
        let metadata = metadata.unwrap_or_default();
        Self { values, metadata }
    }
    
    /// Returns the length of the array
    #[getter]
    fn len(&self) -> usize {
        self.values.len()
    }
    
    /// Returns whether the array is empty
    #[getter]
    fn is_empty(&self) -> bool {
        self.values.is_empty()
    }
    
    /// Returns the value at the given index
    fn get(&self, index: usize) -> PyResult<PyComplex> {
        self.values.get(index)
            .map(|c| PyComplex::from(*c))
            .ok_or_else(|| Error::Complex(format!("Index out of bounds: {}", index)).into())
    }
    
    /// Sets the value at the given index
    fn set(&mut self, index: usize, value: PyComplex) -> PyResult<()> {
        if index < self.values.len() {
            self.values[index] = value.into();
            Ok(())
        } else {
            Err(Error::Complex(format!("Index out of bounds: {}", index)).into())
        }
    }
    
    /// Serializes the array to bytes
    fn serialize(&self) -> PyResult<Vec<u8>> {
        bincode::serialize(self)
            .map_err(|e| Error::Serialization(e).into())
    }
    
    /// Deserializes the array from bytes
    fn deserialize(data: &[u8]) -> PyResult<Self> {
        bincode::deserialize(data)
            .map_err(|e| Error::Serialization(e).into())
    }
}
