//! Special data types for quantum states, fractal structures, and holographic data

use pyo3::prelude::*;

pub mod complex;
pub mod quantum;
pub mod fractal;
pub mod holographic;

/// Register the types module
pub fn register_module(_py: Python, m: &PyModule) -> PyResult<()> {
    // Register quantum state functions
    #[pyfn(m)]
    fn py_create_quantum_state(state: Vec<complex::PyComplex>, dimensions: Vec<usize>) -> PyResult<quantum::PyQuantumStateArray> {
        let state_vec = state.into_iter().map(|c| c.into()).collect();
        Ok(quantum::PyQuantumStateArray::new(state_vec, dimensions))
    }
    
    // Register fractal structure functions
    #[pyfn(m)]
    fn py_create_fractal_structure(
        nodes: Vec<fractal::PyFractalNode>,
        connections: Vec<fractal::PyFractalConnection>,
        metadata: std::collections::HashMap<String, String>,
        dimension: usize,
        depth: usize,
        branching_factor: usize
    ) -> PyResult<fractal::PyFractalStructureArray> {
        Ok(fractal::PyFractalStructureArray::new(
            nodes, connections, metadata, dimension, depth, branching_factor
        ))
    }
    
    // Register holographic data functions
    #[pyfn(m)]
    fn py_create_holographic_data(
        data: Vec<complex::PyComplex>,
        encoding_matrix: Vec<Vec<complex::PyComplex>>,
        encoding_type: String,
        original_dimensions: Vec<usize>,
        holographic_dimensions: Vec<usize>,
        metadata: std::collections::HashMap<String, String>
    ) -> PyResult<holographic::PyHolographicDataArray> {
        Ok(holographic::PyHolographicDataArray::new(
            data, encoding_matrix, encoding_type, 
            original_dimensions, holographic_dimensions, metadata
        ))
    }
    
    Ok(())
}
