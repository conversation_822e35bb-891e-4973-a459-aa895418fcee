//! Utility functions for the arrow_integration crate

use std::sync::atomic::{AtomicUsize, Ordering};

// Global counter for zero-copy operations
static ZERO_COPY_COUNTER: AtomicUsize = AtomicUsize::new(0);

/// Increments the zero-copy counter
pub fn increment_zero_copy_counter() {
    ZERO_COPY_COUNTER.fetch_add(1, Ordering::SeqCst);
}

/// Gets the current zero-copy counter value
pub fn get_zero_copy_counter() -> usize {
    ZERO_COPY_COUNTER.load(Ordering::SeqCst)
}

/// Resets the zero-copy counter
pub fn reset_zero_copy_counter() {
    ZERO_COPY_COUNTER.store(0, Ordering::SeqCst);
}

/// Checks if a pointer is aligned to the given alignment
pub fn is_aligned(ptr: *const u8, alignment: usize) -> bool {
    (ptr as usize) % alignment == 0
}

/// Aligns a pointer to the given alignment
pub fn align_ptr(ptr: *const u8, alignment: usize) -> *const u8 {
    let addr = ptr as usize;
    let aligned_addr = (addr + alignment - 1) & !(alignment - 1);
    aligned_addr as *const u8
}

/// Converts a slice of bytes to a slice of a different type
///
/// # Safety
///
/// This function is unsafe because it creates a slice of a different type
/// without checking if the conversion is valid.
pub unsafe fn bytes_to_slice<T>(bytes: &[u8]) -> &[T] {
    let element_size = std::mem::size_of::<T>();
    let len = bytes.len() / element_size;
    let ptr = bytes.as_ptr() as *const T;
    std::slice::from_raw_parts(ptr, len)
}

/// Converts a mutable slice of bytes to a mutable slice of a different type
///
/// # Safety
///
/// This function is unsafe because it creates a slice of a different type
/// without checking if the conversion is valid.
pub unsafe fn bytes_to_slice_mut<T>(bytes: &mut [u8]) -> &mut [T] {
    let element_size = std::mem::size_of::<T>();
    let len = bytes.len() / element_size;
    let ptr = bytes.as_mut_ptr() as *mut T;
    std::slice::from_raw_parts_mut(ptr, len)
}

/// Rounds up to the next multiple of the given alignment
pub fn round_up_to_multiple(value: usize, alignment: usize) -> usize {
    (value + alignment - 1) & !(alignment - 1)
}
