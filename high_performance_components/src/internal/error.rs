//! Error types for the arrow_integration crate

use std::fmt;
use thiserror::Error;
use pyo3::PyErr;
use pyo3::exceptions::{PyValueError, PyRuntimeError, PyTypeError, PyIOError};

/// Error type for the arrow_integration crate
#[derive(E<PERSON><PERSON>, Debug)]
pub enum Error {
    /// Arrow error
    #[error("Arrow error: {0}")]
    Arrow(#[from] arrow::error::ArrowError),
    
    /// PyO3 error
    #[error("Python error: {0}")]
    Python(#[from] PyErr),
    
    /// IO error
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    /// Serialization error
    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),
    
    /// Type conversion error
    #[error("Type conversion error: {0}")]
    TypeConversion(String),
    
    /// Memory error
    #[error("Memory error: {0}")]
    Memory(String),
    
    /// Zero-copy error
    #[error("Zero-copy error: {0}")]
    ZeroCopy(String),
    
    /// Complex number error
    #[error("Complex number error: {0}")]
    Complex(String),
    
    /// Quantum state error
    #[error("Quantum state error: {0}")]
    QuantumState(String),
    
    /// Fractal structure error
    #[error("Fractal structure error: {0}")]
    FractalStructure(String),
    
    /// Holographic data error
    #[error("Holographic data error: {0}")]
    HolographicData(String),
    
    /// Flight error
    #[cfg(feature = "flight")]
    #[error("Flight error: {0}")]
    Flight(String),
    
    /// Parquet error
    #[cfg(feature = "parquet-support")]
    #[error("Parquet error: {0}")]
    Parquet(#[from] parquet::errors::ParquetError),
    
    /// Other error
    #[error("{0}")]
    Other(String),
}

impl From<Error> for PyErr {
    fn from(err: Error) -> PyErr {
        match err {
            Error::Arrow(e) => PyRuntimeError::new_err(format!("Arrow error: {}", e)),
            Error::Python(e) => e,
            Error::Io(e) => PyIOError::new_err(format!("IO error: {}", e)),
            Error::Serialization(e) => PyValueError::new_err(format!("Serialization error: {}", e)),
            Error::TypeConversion(e) => PyTypeError::new_err(format!("Type conversion error: {}", e)),
            Error::Memory(e) => PyRuntimeError::new_err(format!("Memory error: {}", e)),
            Error::ZeroCopy(e) => PyRuntimeError::new_err(format!("Zero-copy error: {}", e)),
            Error::Complex(e) => PyValueError::new_err(format!("Complex number error: {}", e)),
            Error::QuantumState(e) => PyValueError::new_err(format!("Quantum state error: {}", e)),
            Error::FractalStructure(e) => PyValueError::new_err(format!("Fractal structure error: {}", e)),
            Error::HolographicData(e) => PyValueError::new_err(format!("Holographic data error: {}", e)),
            #[cfg(feature = "flight")]
            Error::Flight(e) => PyRuntimeError::new_err(format!("Flight error: {}", e)),
            #[cfg(feature = "parquet-support")]
            Error::Parquet(e) => PyRuntimeError::new_err(format!("Parquet error: {}", e)),
            Error::Other(e) => PyRuntimeError::new_err(e),
        }
    }
}

impl fmt::Display for Error {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self)
    }
}
