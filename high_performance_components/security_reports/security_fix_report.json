{"total_issues": 258, "fixed_issues": 10, "skipped_issues": 4, "fixed_by_type": {"不安全的反序列化": 2, "不安全的内存操作": 8}, "skipped_by_type": {"不安全的内存操作": 4}, "fixed_details": [{"file": "./security/security_audit.py", "line": 136, "code": "\"不安全的反序列化\": \"使用安全的反序列化方法，如pickle.loads()替换为json.loads()\",", "type": "不安全的反序列化", "severity": "high", "description": "发现不安全的反序列化问题", "recommendation": "使用安全的反序列化方法，如pickle.loads()替换为json.loads()"}, {"file": "./security/vulnerability_scanner.py", "line": 418, "code": "\"不安全的反序列化\": \"使用安全的反序列化方法，如pickle.loads()替换为json.loads()\",", "type": "不安全的反序列化", "severity": "high", "description": "发现不安全的反序列化问题", "recommendation": "使用安全的反序列化方法，如pickle.loads()替换为json.loads()"}, {"file": "./src/zero_copy/memory_mapping.rs", "line": 230, "code": "let mmap = unsafe { MmapOptions::new().map_mut(&file)? };", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}, {"file": "./src/zero_copy/memory_mapping.rs", "line": 146, "code": "let mmap = unsafe { MmapOptions::new().map(&file)? };", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}, {"file": "./src/zero_copy/memory_mapping.rs", "line": 67, "code": "let mmap = unsafe { MmapOptions::new().map_mut(&file)? };", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}, {"file": "./src/zero_copy/memory_mapping.rs", "line": 45, "code": "let mmap = unsafe { MmapOptions::new().map_mut(&file)? };", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}, {"file": "./src/zero_copy/shared_memory.rs", "line": 75, "code": "let mmap = unsafe { MmapOptions::new().map_mut(&file)? };", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}, {"file": "./src/zero_copy/shared_memory.rs", "line": 51, "code": "let mmap = unsafe { MmapOptions::new().map_mut(&file)? };", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}, {"file": "./tests/test_zero_copy.rs", "line": 75, "code": "let mut mmap = unsafe { memmap2::MmapMut::map_mut(&file).unwrap() };", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}, {"file": "./tests/test_zero_copy.rs", "line": 51, "code": "let mmap = unsafe { memmap2::Mmap::map(&file).unwrap() };", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}], "skipped_details": [{"issue": {"file": "./src/internal/utils.rs", "line": 54, "code": "pub unsafe fn bytes_to_slice_mut<T>(bytes: &mut [u8]) -> &mut [T] {", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}, "reason": "无法修复"}, {"issue": {"file": "./src/internal/utils.rs", "line": 52, "code": "/// This function is unsafe because it creates a slice of a different type", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}, "reason": "无法修复"}, {"issue": {"file": "./src/internal/utils.rs", "line": 41, "code": "pub unsafe fn bytes_to_slice<T>(bytes: &[u8]) -> &[T] {", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}, "reason": "无法修复"}, {"issue": {"file": "./src/internal/utils.rs", "line": 39, "code": "/// This function is unsafe because it creates a slice of a different type", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}, "reason": "无法修复"}]}