{"timestamp": "2025-05-16T22:33:39.824369", "source_dir": ".", "total_issues": 258, "severity_count": {"high": 14, "medium": 244, "low": 0}, "type_count": {"调试代码": 213, "不安全的反序列化": 2, "敏感信息泄露": 10, "不安全的随机数": 18, "不安全的内存操作": 12, "不安全的连接": 3}, "issues": [{"file": "./generate_docs.py", "line": 529, "code": "print(f\"API文档已生成：{api_docs_file}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./generate_docs.py", "line": 535, "code": "print(f\"用户指南已生成：{user_guide_file}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./generate_docs.py", "line": 541, "code": "print(f\"开发者指南已生成：{developer_guide_file}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./generate_docs.py", "line": 547, "code": "print(f\"示例文档已生成：{example_docs_file}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./generate_docs.py", "line": 553, "code": "print(f\"性能文档已生成：{performance_docs_file}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./generate_docs.py", "line": 578, "code": "print(f\"索引文档已生成：{index_file}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/run_security_audit.py", "line": 24, "code": "print(\"正在运行安全审计...\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/run_security_audit.py", "line": 30, "code": "print(\"\\n=== 运行代码安全审计 ===\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/run_security_audit.py", "line": 39, "code": "print(\"代码安全审计完成\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/run_security_audit.py", "line": 41, "code": "print(f\"代码安全审计失败: {e}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/run_security_audit.py", "line": 44, "code": "print(\"\\n=== 运行配置安全检查 ===\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/run_security_audit.py", "line": 53, "code": "print(\"配置安全检查完成\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/run_security_audit.py", "line": 55, "code": "print(f\"配置安全检查失败: {e}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/run_security_audit.py", "line": 58, "code": "print(\"\\n=== 运行安全漏洞扫描 ===\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/run_security_audit.py", "line": 67, "code": "print(\"安全漏洞扫描完成\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/run_security_audit.py", "line": 69, "code": "print(f\"安全漏洞扫描失败: {e}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/run_security_audit.py", "line": 72, "code": "print(\"\\n=== 生成综合安全审计报告 ===\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/run_security_audit.py", "line": 74, "code": "print(\"综合安全审计报告生成完成\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/run_security_audit.py", "line": 76, "code": "print(\"\\n安全审计完成\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/run_security_audit.py", "line": 89, "code": "print(f\"加载代码安全审计报告失败: {e}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/run_security_audit.py", "line": 95, "code": "print(f\"加载配置安全检查报告失败: {e}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/run_security_audit.py", "line": 101, "code": "print(f\"加载安全漏洞扫描报告失败: {e}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/security_audit.py", "line": 136, "code": "\"不安全的反序列化\": \"使用安全的反序列化方法，如pickle.loads()替换为json.loads()\",", "type": "不安全的反序列化", "severity": "high", "description": "发现不安全的反序列化问题", "recommendation": "使用安全的反序列化方法，如pickle.loads()替换为json.loads()"}, {"file": "./security/security_audit.py", "line": 38, "code": "print(\"正在审计代码安全...\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/security_audit.py", "line": 59, "code": "print(f\"代码安全审计完成，发现{len(self.issues)}个问题\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/security_audit.py", "line": 149, "code": "print(\"正在审计依赖项安全...\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/security_audit.py", "line": 163, "code": "print(f\"依赖项安全审计完成，发现{len(dependency_issues)}个问题\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/security_audit.py", "line": 177, "code": "print(\"未找到requirements.txt文件，跳过Python依赖项检查\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/security_audit.py", "line": 211, "code": "print(f\"检查Python依赖项时出错: {e}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/security_audit.py", "line": 227, "code": "print(\"未找到Cargo.toml文件，跳过Rust依赖项检查\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/security_audit.py", "line": 262, "code": "print(f\"检查Rust依赖项时出错: {e}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/security_audit.py", "line": 268, "code": "print(\"正在审计配置安全...\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/security_audit.py", "line": 276, "code": "print(f\"配置安全审计完成，发现{len(config_issues)}个问题\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/security_audit.py", "line": 321, "code": "print(\"正在生成安全审计报告...\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/security_audit.py", "line": 354, "code": "print(f\"安全审计报告已生成: {report_file}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/security_audit.py", "line": 355, "code": "print(f\"总问题数: {len(self.issues)}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/security_audit.py", "line": 356, "code": "print(f\"高危问题: {severity_count['high']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/security_audit.py", "line": 357, "code": "print(f\"中危问题: {severity_count['medium']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/security_audit.py", "line": 358, "code": "print(f\"低危问题: {severity_count['low']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/security_audit.py", "line": 74, "code": "\"敏感信息泄露\": r\"(TODO|FIXME|XXX|BUG|HACK)\",", "type": "敏感信息泄露", "severity": "medium", "description": "发现敏感信息泄露问题", "recommendation": "移除代码中的敏感信息和注释"}, {"file": "./security/security_audit.py", "line": 74, "code": "\"敏感信息泄露\": r\"(TODO|FIXME|XXX|BUG|HACK)\",", "type": "敏感信息泄露", "severity": "medium", "description": "发现敏感信息泄露问题", "recommendation": "移除代码中的敏感信息和注释"}, {"file": "./security/security_audit.py", "line": 74, "code": "\"敏感信息泄露\": r\"(TODO|FIXME|XXX|BUG|HACK)\",", "type": "敏感信息泄露", "severity": "medium", "description": "发现敏感信息泄露问题", "recommendation": "移除代码中的敏感信息和注释"}, {"file": "./security/security_audit.py", "line": 74, "code": "\"敏感信息泄露\": r\"(TODO|FIXME|XXX|BUG|HACK)\",", "type": "敏感信息泄露", "severity": "medium", "description": "发现敏感信息泄露问题", "recommendation": "移除代码中的敏感信息和注释"}, {"file": "./security/security_audit.py", "line": 74, "code": "\"敏感信息泄露\": r\"(TODO|FIXME|XXX|BUG|HACK)\",", "type": "敏感信息泄露", "severity": "medium", "description": "发现敏感信息泄露问题", "recommendation": "移除代码中的敏感信息和注释"}, {"file": "./security/security_audit.py", "line": 107, "code": "\"敏感信息泄露\": r\"(TODO|FIXME|XXX|BUG|HACK)\",", "type": "敏感信息泄露", "severity": "medium", "description": "发现敏感信息泄露问题", "recommendation": "移除代码中的敏感信息和注释"}, {"file": "./security/security_audit.py", "line": 107, "code": "\"敏感信息泄露\": r\"(TODO|FIXME|XXX|BUG|HACK)\",", "type": "敏感信息泄露", "severity": "medium", "description": "发现敏感信息泄露问题", "recommendation": "移除代码中的敏感信息和注释"}, {"file": "./security/security_audit.py", "line": 107, "code": "\"敏感信息泄露\": r\"(TODO|FIXME|XXX|BUG|HACK)\",", "type": "敏感信息泄露", "severity": "medium", "description": "发现敏感信息泄露问题", "recommendation": "移除代码中的敏感信息和注释"}, {"file": "./security/security_audit.py", "line": 107, "code": "\"敏感信息泄露\": r\"(TODO|FIXME|XXX|BUG|HACK)\",", "type": "敏感信息泄露", "severity": "medium", "description": "发现敏感信息泄露问题", "recommendation": "移除代码中的敏感信息和注释"}, {"file": "./security/security_audit.py", "line": 107, "code": "\"敏感信息泄露\": r\"(TODO|FIXME|XXX|BUG|HACK)\",", "type": "敏感信息泄露", "severity": "medium", "description": "发现敏感信息泄露问题", "recommendation": "移除代码中的敏感信息和注释"}, {"file": "./security/config_checker.py", "line": 39, "code": "print(\"正在检查配置文件...\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/config_checker.py", "line": 52, "code": "print(f\"配置检查完成，发现{len(self.issues)}个问题\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/config_checker.py", "line": 56, "code": "print(f\"检查配置文件: {config_file}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/config_checker.py", "line": 326, "code": "print(\"正在检查权限设置...\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/config_checker.py", "line": 339, "code": "print(f\"权限检查完成，发现{len(self.issues)}个问题\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/config_checker.py", "line": 384, "code": "print(\"正在生成安全配置报告...\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/config_checker.py", "line": 417, "code": "print(f\"安全配置报告已生成: {report_file}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/config_checker.py", "line": 418, "code": "print(f\"总问题数: {len(self.issues)}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/config_checker.py", "line": 419, "code": "print(f\"高危问题: {severity_count['high']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/config_checker.py", "line": 420, "code": "print(f\"中危问题: {severity_count['medium']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/config_checker.py", "line": 421, "code": "print(f\"低危问题: {severity_count['low']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/vulnerability_scanner.py", "line": 418, "code": "\"不安全的反序列化\": \"使用安全的反序列化方法，如pickle.loads()替换为json.loads()\",", "type": "不安全的反序列化", "severity": "high", "description": "发现不安全的反序列化问题", "recommendation": "使用安全的反序列化方法，如pickle.loads()替换为json.loads()"}, {"file": "./security/vulnerability_scanner.py", "line": 37, "code": "print(\"正在扫描代码中的安全漏洞...\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/vulnerability_scanner.py", "line": 67, "code": "print(f\"代码扫描完成，发现{len(self.vulnerabilities)}个安全漏洞\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/vulnerability_scanner.py", "line": 71, "code": "print(f\"扫描Python文件: {file_path}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/vulnerability_scanner.py", "line": 90, "code": "print(f\"扫描Python文件时出错: {e}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/vulnerability_scanner.py", "line": 228, "code": "print(f\"扫描Rust文件: {file_path}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/vulnerability_scanner.py", "line": 244, "code": "print(f\"扫描Rust文件时出错: {e}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/vulnerability_scanner.py", "line": 320, "code": "print(f\"扫描C++文件: {file_path}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/vulnerability_scanner.py", "line": 336, "code": "print(f\"扫描C++文件时出错: {e}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/vulnerability_scanner.py", "line": 470, "code": "print(\"正在生成安全漏洞报告...\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/vulnerability_scanner.py", "line": 512, "code": "print(f\"安全漏洞报告已生成: {report_file}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/vulnerability_scanner.py", "line": 513, "code": "print(f\"总漏洞数: {len(self.vulnerabilities)}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/vulnerability_scanner.py", "line": 514, "code": "print(f\"高危漏洞: {severity_count['high']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/vulnerability_scanner.py", "line": 515, "code": "print(f\"中危漏洞: {severity_count['medium']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./security/vulnerability_scanner.py", "line": 516, "code": "print(f\"低危漏洞: {severity_count['low']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 47, "code": "print(f\"反馈已提交，ID：{feedback_id}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 59, "code": "print(f\"ID：{feedback['id']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 60, "code": "print(f\"时间：{feedback['timestamp']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 61, "code": "print(f\"类型：{feedback['type']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 62, "code": "print(f\"标题：{feedback['title']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 63, "code": "print(f\"描述：{feedback['description']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 64, "code": "print(f\"用户：{feedback['user_name'] or '匿名'}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 65, "code": "print(f\"邮箱：{feedback['user_email'] or '无'}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 66, "code": "print(f\"组织：{feedback['user_organization'] or '无'}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 67, "code": "print(f\"环境：{feedback['environment'] or '无'}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 68, "code": "print(f\"严重程度：{feedback['severity'] or '无'}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 69, "code": "print(f\"状态：{feedback['status']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 70, "code": "print(f\"处理人：{feedback['assignee'] or '无'}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 71, "code": "print(f\"标签：{', '.join(feedback['tags']) if feedback['tags'] else '无'}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 74, "code": "print(\"\\n评论：\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 76, "code": "print(f\"  {comment['timestamp']} {comment['user_name'] or '匿名'}：{comment['content']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 79, "code": "print(\"\\n投票：\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 82, "code": "print(f\"  赞成：{upvotes}，反对：{downvotes}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 84, "code": "print(f\"未找到ID为{args.id}的反馈\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 90, "code": "print(f\"共找到{len(feedbacks)}条反馈：\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 92, "code": "print(f\"ID：{feedback['id']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 93, "code": "print(f\"时间：{feedback['timestamp']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 94, "code": "print(f\"类型：{feedback['type']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 95, "code": "print(f\"标题：{feedback['title']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 96, "code": "print(f\"状态：{feedback['status']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 97, "code": "print()", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 99, "code": "print(\"未找到反馈\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 114, "code": "print(f\"评论已添加，ID：{comment_id}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 129, "code": "print(f\"投票已添加，ID：{vote_id}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 143, "code": "print(f\"反馈状态已更新为：{args.status}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 153, "code": "print(f\"反馈报告已生成：{args.output}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 154, "code": "print(f\"总反馈数：{report['total_feedback']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 155, "code": "print(\"按类型统计：\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 157, "code": "print(f\"  {type}：{count}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 158, "code": "print(\"按状态统计：\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_cli.py", "line": 160, "code": "print(f\"  {status}：{count}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_server.py", "line": 477, "code": "print(f\"服务器已启动，地址：http{'s' if ssl_cert and ssl_key else ''}://{host}:{port}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./feedback/feedback_server.py", "line": 482, "code": "print(\"服务器已停止\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/performance_database.py", "line": 441, "code": "print(f\"导入成功，运行ID：{run_id}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/performance_database.py", "line": 444, "code": "print(f\"导出成功，趋势报告已保存到：{args.output}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/visualize_benchmark.py", "line": 122, "code": "print(f\"可视化结果已保存到：{args.output_dir}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/visualize_benchmark_html.py", "line": 237, "code": "print(f\"HTML报告已生成：{args.output}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/auto_optimize.py", "line": 424, "code": "print(json.dumps(results, indent=2))", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/auto_optimize.py", "line": 436, "code": "print(f\"测试脚本运行失败：{result.stderr}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/auto_optimize.py", "line": 443, "code": "print(f\"无法解析测试结果：{result.stdout}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/auto_optimize.py", "line": 651, "code": "print(\"未发现性能瓶颈\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/auto_optimize.py", "line": 654, "code": "print(f\"发现{len(bottlenecks)}个性能瓶颈\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/auto_optimize.py", "line": 660, "code": "print(\"未生成优化建议\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/auto_optimize.py", "line": 663, "code": "print(f\"生成{len(suggestions)}个优化建议\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/auto_optimize.py", "line": 668, "code": "print(f\"应用优化建议到{len(suggestions_by_file)}个文件\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/auto_optimize.py", "line": 674, "code": "print(\"验证优化效果成功\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/auto_optimize.py", "line": 678, "code": "print(f\"  {function_name}: {result['speedup']:.2f}x\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/auto_optimize.py", "line": 680, "code": "print(\"验证优化效果失败\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/auto_optimize.py", "line": 690, "code": "print(f\"优化报告已生成：{report_file}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/auto_optimize.py", "line": 691, "code": "print(f\"HTML优化报告已生成：{html_report_file}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/optimize_performance.py", "line": 197, "code": "nonzero_indices = np.random.choice(dim, num_nonzero, replace=False)", "type": "不安全的随机数", "severity": "medium", "description": "发现不安全的随机数问题", "recommendation": "使用secrets模块生成安全的随机数"}, {"file": "./tests/optimize_performance.py", "line": 27, "code": "print(\"\\n=== 内存对齐优化 ===\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/optimize_performance.py", "line": 56, "code": "print(f\"未对齐数组时间: {unaligned_time*1000:.3f}毫秒\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/optimize_performance.py", "line": 57, "code": "print(f\"对齐数组时间: {aligned_time*1000:.3f}毫秒\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/optimize_performance.py", "line": 58, "code": "print(f\"性能提升: {speedup:.2f}x\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/optimize_performance.py", "line": 68, "code": "print(\"\\n=== 缓存优化 ===\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/optimize_performance.py", "line": 110, "code": "print(f\"行优先访问时间: {row_major_time*1000:.3f}毫秒\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/optimize_performance.py", "line": 111, "code": "print(f\"列优先访问时间: {column_major_time*1000:.3f}毫秒\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/optimize_performance.py", "line": 112, "code": "print(f\"分块访问时间: {blocked_time*1000:.3f}毫秒\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/optimize_performance.py", "line": 113, "code": "print(f\"行优先vs列优先性能提升: {row_vs_column_speedup:.2f}x\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/optimize_performance.py", "line": 114, "code": "print(f\"行优先vs分块性能提升: {row_vs_blocked_speedup:.2f}x\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/optimize_performance.py", "line": 126, "code": "print(\"\\n=== 并行处理优化 ===\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/optimize_performance.py", "line": 176, "code": "print(f\"串行处理时间: {serial_time*1000:.3f}毫秒\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/optimize_performance.py", "line": 177, "code": "print(f\"并行处理时间: {parallel_time*1000:.3f}毫秒\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/optimize_performance.py", "line": 178, "code": "print(f\"性能提升: {speedup:.2f}x\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/optimize_performance.py", "line": 189, "code": "print(\"\\n=== 稀疏表示优化 ===\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/optimize_performance.py", "line": 247, "code": "print(f\"密集表示时间: {dense_time*1000:.3f}毫秒, 大小: {dense_size/1024/1024:.2f} MB\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/optimize_performance.py", "line": 248, "code": "print(f\"稀疏表示时间: {sparse_time*1000:.3f}毫秒, 大小: {sparse_size/1024/1024:.2f} MB\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/optimize_performance.py", "line": 249, "code": "print(f\"性能提升: {speedup:.2f}x\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/optimize_performance.py", "line": 250, "code": "print(f\"内存节省: {memory_saving:.2f}%\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/optimize_performance.py", "line": 295, "code": "print(f\"\\n结果已保存到：{args.output}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/benchmark_zero_copy.py", "line": 25, "code": "print(\"\\n=== NumPy数组零拷贝转换性能基准测试 ===\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/benchmark_zero_copy.py", "line": 66, "code": "print(f\"数组大小: {size}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/benchmark_zero_copy.py", "line": 67, "code": "print(f\"  零拷贝时间: {zero_copy_time*1000:.3f}毫秒, 吞吐量: {zero_copy_throughput:.2f} MB/s\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/benchmark_zero_copy.py", "line": 68, "code": "print(f\"  普通复制时间: {copy_time*1000:.3f}毫秒, 吞吐量: {copy_throughput:.2f} MB/s\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/benchmark_zero_copy.py", "line": 69, "code": "print(f\"  加速比: {speedup:.2f}x\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/benchmark_zero_copy.py", "line": 75, "code": "print(\"\\n=== NumPy数组文件映射性能基准测试 ===\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/benchmark_zero_copy.py", "line": 126, "code": "print(f\"数组大小: {size}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/benchmark_zero_copy.py", "line": 127, "code": "print(f\"  内存映射时间: {mmap_time*1000:.3f}毫秒, 吞吐量: {mmap_throughput:.2f} MB/s\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/benchmark_zero_copy.py", "line": 128, "code": "print(f\"  普通加载时间: {load_time*1000:.3f}毫秒, 吞吐量: {load_throughput:.2f} MB/s\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/benchmark_zero_copy.py", "line": 129, "code": "print(f\"  加速比: {speedup:.2f}x\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/benchmark_zero_copy.py", "line": 139, "code": "print(\"\\n=== 量子态序列化和反序列化性能基准测试 ===\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/benchmark_zero_copy.py", "line": 187, "code": "print(f\"量子比特数: {n_qubits}, 维度: {dim}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/benchmark_zero_copy.py", "line": 188, "code": "print(f\"  序列化时间: {serialize_time*1000:.3f}毫秒, 吞吐量: {serialize_throughput:.2f} MB/s\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/benchmark_zero_copy.py", "line": 189, "code": "print(f\"  反序列化时间: {deserialize_time*1000:.3f}毫秒, 吞吐量: {deserialize_throughput:.2f} MB/s\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/benchmark_zero_copy.py", "line": 195, "code": "print(\"\\n=== 分形结构序列化和反序列化性能基准测试 ===\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/benchmark_zero_copy.py", "line": 276, "code": "print(f\"深度: {depth}, 节点数: {len(nodes)}, 连接数: {len(connections)}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/benchmark_zero_copy.py", "line": 277, "code": "print(f\"  序列化时间: {serialize_time*1000:.3f}毫秒, 吞吐量: {serialize_throughput:.2f} MB/s\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/benchmark_zero_copy.py", "line": 278, "code": "print(f\"  反序列化时间: {deserialize_time*1000:.3f}毫秒, 吞吐量: {deserialize_throughput:.2f} MB/s\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/benchmark_zero_copy.py", "line": 316, "code": "print(f\"\\n结果已保存到：{args.output}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/generate_physics_reports.py", "line": 575, "code": "print(f\"量子态物理正确性测试报告已生成：{quantum_report}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/generate_physics_reports.py", "line": 579, "code": "print(f\"分形结构物理正确性测试报告已生成：{fractal_report}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/generate_physics_reports.py", "line": 583, "code": "print(f\"全息数据物理正确性测试报告已生成：{holographic_report}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/detect_performance_regression.py", "line": 129, "code": "print(\"没有检测到性能退化\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/detect_performance_regression.py", "line": 132, "code": "print(\"检测到性能退化：\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/detect_performance_regression.py", "line": 135, "code": "print(\"\\nNumPy数组零拷贝转换性能退化：\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/detect_performance_regression.py", "line": 137, "code": "print(f\"  数组大小: {regression['size']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/detect_performance_regression.py", "line": 138, "code": "print(f\"    当前吞吐量: {regression['current_throughput']:.2f} MB/s\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/detect_performance_regression.py", "line": 139, "code": "print(f\"    基准线吞吐量: {regression['baseline_throughput']:.2f} MB/s\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/detect_performance_regression.py", "line": 140, "code": "print(f\"    性能下降: {regression['change_percent']:.2f}%\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/detect_performance_regression.py", "line": 143, "code": "print(\"\\n量子态序列化和反序列化性能退化：\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/detect_performance_regression.py", "line": 145, "code": "print(f\"  量子比特数: {regression['n_qubits']}, 操作: {regression['operation']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/detect_performance_regression.py", "line": 146, "code": "print(f\"    当前吞吐量: {regression['current_throughput']:.2f} MB/s\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/detect_performance_regression.py", "line": 147, "code": "print(f\"    基准线吞吐量: {regression['baseline_throughput']:.2f} MB/s\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/detect_performance_regression.py", "line": 148, "code": "print(f\"    性能下降: {regression['change_percent']:.2f}%\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/detect_performance_regression.py", "line": 179, "code": "print(f\"更新基准线: {baseline_path}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/detect_performance_regression.py", "line": 192, "code": "print(f\"性能退化信息已保存到: {args.output}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/performance_monitor.py", "line": 82, "code": "print(f\"性能监控已启动，间隔：{self.interval}秒，阈值：{self.threshold}%\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/performance_monitor.py", "line": 90, "code": "print(\"性能监控已停止\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/performance_monitor.py", "line": 109, "code": "print(f\"监控循环异常：{e}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/performance_monitor.py", "line": 143, "code": "print(f\"收集NumPy数组零拷贝转换性能数据异常：{e}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/performance_monitor.py", "line": 169, "code": "print(f\"收集量子态序列化性能数据异常：{e}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/performance_monitor.py", "line": 237, "code": "print(f\"警报：{alert['test_name']} {alert['metric_name']} 性能退化 {alert['change_percent']:.2f}%，严重程度：{alert['severity']}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/performance_monitor.py", "line": 580, "code": "print(f\"性能报告已生成：{args.report}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/performance_monitor.py", "line": 586, "code": "print(f\"HTML性能报告已生成：{args.html_report}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/performance_monitor.py", "line": 595, "code": "print(f\"性能监控已作为守护进程启动，数据库：{args.db}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/performance_monitor.py", "line": 602, "code": "print(\"性能监控已启动，按Ctrl+C退出\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/test_multithreading.py", "line": 60, "code": "start = random.randint(0, self.array_size - 1000)", "type": "不安全的随机数", "severity": "medium", "description": "发现不安全的随机数问题", "recommendation": "使用secrets模块生成安全的随机数"}, {"file": "./tests/test_multithreading.py", "line": 107, "code": "start = random.randint(0, self.array_size - 1000)", "type": "不安全的随机数", "severity": "medium", "description": "发现不安全的随机数问题", "recommendation": "使用secrets模块生成安全的随机数"}, {"file": "./tests/test_multithreading.py", "line": 146, "code": "start = random.randint(0, self.array_size - 1000)", "type": "不安全的随机数", "severity": "medium", "description": "发现不安全的随机数问题", "recommendation": "使用secrets模块生成安全的随机数"}, {"file": "./tests/test_multithreading.py", "line": 242, "code": "start = random.randint(0, self.array_size - 1000)", "type": "不安全的随机数", "severity": "medium", "description": "发现不安全的随机数问题", "recommendation": "使用secrets模块生成安全的随机数"}, {"file": "./tests/test_multithreading.py", "line": 211, "code": "print(f\"预期值: {expected_value}, 实际值: {actual_value}, 差异: {expected_value - actual_value}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/cross_platform_test.py", "line": 685, "code": "print(f\"跨平台测试报告已生成：{report_file}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/cross_platform_test.py", "line": 686, "code": "print(f\"HTML跨平台测试报告已生成：{html_report_file}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/analyze_coverage.py", "line": 335, "code": "print(f\"覆盖率报告已生成：{args.output_dir}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/analyze_coverage.py", "line": 336, "code": "print(f\"总体覆盖率：{report['total_coverage']:.2f}%\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/analyze_coverage.py", "line": 339, "code": "print(\"\\n未覆盖代码模式：\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/analyze_coverage.py", "line": 341, "code": "print(f\"  {pattern_name}: {pattern_count}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/analyze_coverage.py", "line": 344, "code": "print(\"\\n覆盖率最低的文件：\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/analyze_coverage.py", "line": 346, "code": "print(f\"  {file_path}: {file_data['percent_covered']:.2f}%\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/test_fractal_physics.py", "line": 70, "code": "func = np.random.choice(self.<PERSON><PERSON><PERSON><PERSON>_ifs)", "type": "不安全的随机数", "severity": "medium", "description": "发现不安全的随机数问题", "recommendation": "使用secrets模块生成安全的随机数"}, {"file": "./tests/test_fractal_physics.py", "line": 85, "code": "func = np.random.choice(self.koch_ifs)", "type": "不安全的随机数", "severity": "medium", "description": "发现不安全的随机数问题", "recommendation": "使用secrets模块生成安全的随机数"}, {"file": "./tests/python/test_python_rust_interop.py", "line": 234, "code": "original_data = [complex(random.random(), random.random()) for _ in range(n)]", "type": "不安全的随机数", "severity": "medium", "description": "发现不安全的随机数问题", "recommendation": "使用secrets模块生成安全的随机数"}, {"file": "./tests/python/test_python_rust_interop.py", "line": 234, "code": "original_data = [complex(random.random(), random.random()) for _ in range(n)]", "type": "不安全的随机数", "severity": "medium", "description": "发现不安全的随机数问题", "recommendation": "使用secrets模块生成安全的随机数"}, {"file": "./tests/python/test_python_rust_interop.py", "line": 237, "code": "encoding_matrix = [[complex(random.random(), random.random()) for _ in range(n)] for _ in range(n)]", "type": "不安全的随机数", "severity": "medium", "description": "发现不安全的随机数问题", "recommendation": "使用secrets模块生成安全的随机数"}, {"file": "./tests/python/test_python_rust_interop.py", "line": 237, "code": "encoding_matrix = [[complex(random.random(), random.random()) for _ in range(n)] for _ in range(n)]", "type": "不安全的随机数", "severity": "medium", "description": "发现不安全的随机数问题", "recommendation": "使用secrets模块生成安全的随机数"}, {"file": "./tests/python/test_python_rust_interop.py", "line": 283, "code": "data = bytes([random.randint(0, 255) for _ in range(total_size)])", "type": "不安全的随机数", "severity": "medium", "description": "发现不安全的随机数问题", "recommendation": "使用secrets模块生成安全的随机数"}, {"file": "./tests/python/test_python_rust_interop.py", "line": 68, "code": "print(\"警告：arrow_integration模块不可用，跳过测试\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/python/benchmark_special_types.py", "line": 152, "code": "original_data = [complex(random.random(), random.random()) for _ in range(n)]", "type": "不安全的随机数", "severity": "medium", "description": "发现不安全的随机数问题", "recommendation": "使用secrets模块生成安全的随机数"}, {"file": "./tests/python/benchmark_special_types.py", "line": 152, "code": "original_data = [complex(random.random(), random.random()) for _ in range(n)]", "type": "不安全的随机数", "severity": "medium", "description": "发现不安全的随机数问题", "recommendation": "使用secrets模块生成安全的随机数"}, {"file": "./tests/python/benchmark_special_types.py", "line": 69, "code": "print(\"警告：arrow_integration模块不可用，跳过基准测试\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/python/benchmark_special_types.py", "line": 178, "code": "print(\"\\n=== 量子态性能基准测试 ===\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/python/benchmark_special_types.py", "line": 238, "code": "print(f\"量子比特数: {n_qubits}, 维度: {dim}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/python/benchmark_special_types.py", "line": 239, "code": "print(f\"  序列化时间: {serialize_time*1000:.3f}毫秒, 吞吐量: {serialize_throughput:.2f} MB/s\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/python/benchmark_special_types.py", "line": 240, "code": "print(f\"  反序列化时间: {deserialize_time*1000:.3f}毫秒, 吞吐量: {deserialize_throughput:.2f} MB/s\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/python/benchmark_special_types.py", "line": 241, "code": "print(f\"  传输时间: {transfer_time*1000:.3f}毫秒, 吞吐量: {transfer_throughput:.2f} MB/s\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/python/benchmark_special_types.py", "line": 251, "code": "print(\"\\n=== 分形结构性能基准测试 ===\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/python/benchmark_special_types.py", "line": 310, "code": "print(f\"深度: {depth}, 节点数: {node_count}, 连接数: {connection_count}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/python/benchmark_special_types.py", "line": 311, "code": "print(f\"  序列化时间: {serialize_time*1000:.3f}毫秒, 吞吐量: {serialize_throughput:.2f} MB/s\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/python/benchmark_special_types.py", "line": 312, "code": "print(f\"  反序列化时间: {deserialize_time*1000:.3f}毫秒, 吞吐量: {deserialize_throughput:.2f} MB/s\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/python/benchmark_special_types.py", "line": 313, "code": "print(f\"  传输时间: {transfer_time*1000:.3f}毫秒, 吞吐量: {transfer_throughput:.2f} MB/s\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/python/benchmark_special_types.py", "line": 323, "code": "print(\"\\n=== 全息数据性能基准测试 ===\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/python/benchmark_special_types.py", "line": 396, "code": "print(f\"大小: {size}, 序列化大小: {len(serialized)/1024:.1f} KB\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/python/benchmark_special_types.py", "line": 397, "code": "print(f\"  序列化时间: {serialize_time*1000:.3f}毫秒, 吞吐量: {serialize_throughput:.2f} MB/s\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/python/benchmark_special_types.py", "line": 398, "code": "print(f\"  反序列化时间: {deserialize_time*1000:.3f}毫秒, 吞吐量: {deserialize_throughput:.2f} MB/s\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/python/benchmark_special_types.py", "line": 399, "code": "print(f\"  传输时间: {transfer_time*1000:.3f}毫秒, 吞吐量: {transfer_throughput:.2f} MB/s\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/python/benchmark_special_types.py", "line": 410, "code": "print(\"arrow_integration模块不可用，跳过基准测试\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/python/benchmark_special_types.py", "line": 441, "code": "print(f\"\\n结果已保存到：{args.output}\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./tests/python/test_end_to_end.py", "line": 212, "code": "original_data = [complex(random.random(), random.random()) for _ in range(n)]", "type": "不安全的随机数", "severity": "medium", "description": "发现不安全的随机数问题", "recommendation": "使用secrets模块生成安全的随机数"}, {"file": "./tests/python/test_end_to_end.py", "line": 212, "code": "original_data = [complex(random.random(), random.random()) for _ in range(n)]", "type": "不安全的随机数", "severity": "medium", "description": "发现不安全的随机数问题", "recommendation": "使用secrets模块生成安全的随机数"}, {"file": "./tests/python/test_end_to_end.py", "line": 213, "code": "encoding_matrix = [[complex(random.random(), random.random()) for _ in range(n)] for _ in range(n)]", "type": "不安全的随机数", "severity": "medium", "description": "发现不安全的随机数问题", "recommendation": "使用secrets模块生成安全的随机数"}, {"file": "./tests/python/test_end_to_end.py", "line": 213, "code": "encoding_matrix = [[complex(random.random(), random.random()) for _ in range(n)] for _ in range(n)]", "type": "不安全的随机数", "severity": "medium", "description": "发现不安全的随机数问题", "recommendation": "使用secrets模块生成安全的随机数"}, {"file": "./tests/python/test_end_to_end.py", "line": 68, "code": "print(\"警告：arrow_integration模块不可用，跳过测试\")", "type": "调试代码", "severity": "medium", "description": "发现调试代码问题", "recommendation": "移除生产环境中的调试代码"}, {"file": "./src/zero_copy/memory_mapping.rs", "line": 45, "code": "let mmap = unsafe { MmapOptions::new().map_mut(&file)? };", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}, {"file": "./src/zero_copy/memory_mapping.rs", "line": 67, "code": "let mmap = unsafe { MmapOptions::new().map_mut(&file)? };", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}, {"file": "./src/zero_copy/memory_mapping.rs", "line": 146, "code": "let mmap = unsafe { MmapOptions::new().map(&file)? };", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}, {"file": "./src/zero_copy/memory_mapping.rs", "line": 230, "code": "let mmap = unsafe { MmapOptions::new().map_mut(&file)? };", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}, {"file": "./src/zero_copy/shared_memory.rs", "line": 51, "code": "let mmap = unsafe { MmapOptions::new().map_mut(&file)? };", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}, {"file": "./src/zero_copy/shared_memory.rs", "line": 75, "code": "let mmap = unsafe { MmapOptions::new().map_mut(&file)? };", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}, {"file": "./src/internal/utils.rs", "line": 39, "code": "/// This function is unsafe because it creates a slice of a different type", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}, {"file": "./src/internal/utils.rs", "line": 41, "code": "pub unsafe fn bytes_to_slice<T>(bytes: &[u8]) -> &[T] {", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}, {"file": "./src/internal/utils.rs", "line": 52, "code": "/// This function is unsafe because it creates a slice of a different type", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}, {"file": "./src/internal/utils.rs", "line": 54, "code": "pub unsafe fn bytes_to_slice_mut<T>(bytes: &mut [u8]) -> &mut [T] {", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}, {"file": "./tests/test_zero_copy.rs", "line": 51, "code": "let mmap = unsafe { memmap2::Mmap::map(&file).unwrap() };", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}, {"file": "./tests/test_zero_copy.rs", "line": 75, "code": "let mut mmap = unsafe { memmap2::MmapMut::map_mut(&file).unwrap() };", "type": "不安全的内存操作", "severity": "high", "description": "发现不安全的内存操作问题", "recommendation": "避免使用unsafe代码，使用安全的内存操作方法"}, {"file": "./deploy/staging.env", "line": 15, "code": "DEPLOY_HEALTH_CHECK_URL=\"http://staging.example.com:8080/health\"", "type": "不安全的连接", "severity": "medium", "description": "配置文件中发现不安全的连接问题", "recommendation": "修复安全问题"}, {"file": "./deploy/development.env", "line": 15, "code": "DEPLOY_HEALTH_CHECK_URL=\"http://localhost:8080/health\"", "type": "不安全的连接", "severity": "medium", "description": "配置文件中发现不安全的连接问题", "recommendation": "修复安全问题"}, {"file": "./deploy/production.env", "line": 15, "code": "DEPLOY_HEALTH_CHECK_URL=\"http://production.example.com:8080/health\"", "type": "不安全的连接", "severity": "medium", "description": "配置文件中发现不安全的连接问题", "recommendation": "修复安全问题"}]}