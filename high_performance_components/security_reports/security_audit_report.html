
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>零拷贝机制安全审计报告</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .summary {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .summary-item {
            text-align: center;
            padding: 10px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            flex: 1;
            margin: 0 10px;
        }
        .summary-item.high {
            border-top: 3px solid #e74c3c;
        }
        .summary-item.medium {
            border-top: 3px solid #f39c12;
        }
        .summary-item.low {
            border-top: 3px solid #3498db;
        }
        .summary-item h3 {
            margin: 0;
            font-size: 1.2em;
        }
        .summary-item p {
            font-size: 2em;
            margin: 10px 0;
        }
        .issue {
            margin-bottom: 10px;
            padding: 15px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .issue.high {
            border-left: 5px solid #e74c3c;
        }
        .issue.medium {
            border-left: 5px solid #f39c12;
        }
        .issue.low {
            border-left: 5px solid #3498db;
        }
        .issue-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .issue-title {
            font-weight: bold;
        }
        .issue-severity {
            padding: 2px 5px;
            border-radius: 3px;
            color: white;
            font-size: 0.8em;
        }
        .issue-severity.high {
            background-color: #e74c3c;
        }
        .issue-severity.medium {
            background-color: #f39c12;
        }
        .issue-severity.low {
            background-color: #3498db;
        }
        .issue-details {
            margin-bottom: 10px;
        }
        .issue-file {
            font-family: monospace;
            margin-bottom: 5px;
        }
        .issue-code {
            font-family: monospace;
            background-color: #f5f5f5;
            padding: 5px;
            border-radius: 3px;
            margin-bottom: 10px;
            white-space: pre;
            overflow-x: auto;
        }
        .issue-description {
            margin-bottom: 10px;
        }
        .issue-recommendation {
            background-color: #eafaf1;
            padding: 10px;
            border-radius: 3px;
            border-left: 3px solid #27ae60;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>零拷贝机制安全审计报告</h1>
        <p>生成时间: 2025-05-16T22:33:39.824369</p>
        <p>源代码目录: .</p>
        
        <div class="section">
            <h2>问题摘要</h2>
            <div class="summary">
                <div class="summary-item high">
                    <h3>高危问题</h3>
                    <p>14</p>
                </div>
                <div class="summary-item medium">
                    <h3>中危问题</h3>
                    <p>244</p>
                </div>
                <div class="summary-item low">
                    <h3>低危问题</h3>
                    <p>0</p>
                </div>
                <div class="summary-item">
                    <h3>总问题数</h3>
                    <p>258</p>
                </div>
            </div>
            
            <h3>按类型统计</h3>
            <ul>
        <li>调试代码: 213</li><li>不安全的反序列化: 2</li><li>敏感信息泄露: 10</li><li>不安全的随机数: 18</li><li>不安全的内存操作: 12</li><li>不安全的连接: 3</li>
            </ul>
        </div>
        
        <div class="section">
            <h2>问题详情</h2>
        
            <div class="issue high">
                <div class="issue-header">
                    <div class="issue-title">不安全的反序列化</div>
                    <div class="issue-severity high">high</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:136</div>
                    <div class="issue-code">"不安全的反序列化": "使用安全的反序列化方法，如pickle.loads()替换为json.loads()",</div>
                </div>
                <div class="issue-description">发现不安全的反序列化问题</div>
                <div class="issue-recommendation">使用安全的反序列化方法，如pickle.loads()替换为json.loads()</div>
            </div>
            
            <div class="issue high">
                <div class="issue-header">
                    <div class="issue-title">不安全的反序列化</div>
                    <div class="issue-severity high">high</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/vulnerability_scanner.py:418</div>
                    <div class="issue-code">"不安全的反序列化": "使用安全的反序列化方法，如pickle.loads()替换为json.loads()",</div>
                </div>
                <div class="issue-description">发现不安全的反序列化问题</div>
                <div class="issue-recommendation">使用安全的反序列化方法，如pickle.loads()替换为json.loads()</div>
            </div>
            
            <div class="issue high">
                <div class="issue-header">
                    <div class="issue-title">不安全的内存操作</div>
                    <div class="issue-severity high">high</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./src/zero_copy/memory_mapping.rs:45</div>
                    <div class="issue-code">let mmap = unsafe { MmapOptions::new().map_mut(&file)? };</div>
                </div>
                <div class="issue-description">发现不安全的内存操作问题</div>
                <div class="issue-recommendation">避免使用unsafe代码，使用安全的内存操作方法</div>
            </div>
            
            <div class="issue high">
                <div class="issue-header">
                    <div class="issue-title">不安全的内存操作</div>
                    <div class="issue-severity high">high</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./src/zero_copy/memory_mapping.rs:67</div>
                    <div class="issue-code">let mmap = unsafe { MmapOptions::new().map_mut(&file)? };</div>
                </div>
                <div class="issue-description">发现不安全的内存操作问题</div>
                <div class="issue-recommendation">避免使用unsafe代码，使用安全的内存操作方法</div>
            </div>
            
            <div class="issue high">
                <div class="issue-header">
                    <div class="issue-title">不安全的内存操作</div>
                    <div class="issue-severity high">high</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./src/zero_copy/memory_mapping.rs:146</div>
                    <div class="issue-code">let mmap = unsafe { MmapOptions::new().map(&file)? };</div>
                </div>
                <div class="issue-description">发现不安全的内存操作问题</div>
                <div class="issue-recommendation">避免使用unsafe代码，使用安全的内存操作方法</div>
            </div>
            
            <div class="issue high">
                <div class="issue-header">
                    <div class="issue-title">不安全的内存操作</div>
                    <div class="issue-severity high">high</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./src/zero_copy/memory_mapping.rs:230</div>
                    <div class="issue-code">let mmap = unsafe { MmapOptions::new().map_mut(&file)? };</div>
                </div>
                <div class="issue-description">发现不安全的内存操作问题</div>
                <div class="issue-recommendation">避免使用unsafe代码，使用安全的内存操作方法</div>
            </div>
            
            <div class="issue high">
                <div class="issue-header">
                    <div class="issue-title">不安全的内存操作</div>
                    <div class="issue-severity high">high</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./src/zero_copy/shared_memory.rs:51</div>
                    <div class="issue-code">let mmap = unsafe { MmapOptions::new().map_mut(&file)? };</div>
                </div>
                <div class="issue-description">发现不安全的内存操作问题</div>
                <div class="issue-recommendation">避免使用unsafe代码，使用安全的内存操作方法</div>
            </div>
            
            <div class="issue high">
                <div class="issue-header">
                    <div class="issue-title">不安全的内存操作</div>
                    <div class="issue-severity high">high</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./src/zero_copy/shared_memory.rs:75</div>
                    <div class="issue-code">let mmap = unsafe { MmapOptions::new().map_mut(&file)? };</div>
                </div>
                <div class="issue-description">发现不安全的内存操作问题</div>
                <div class="issue-recommendation">避免使用unsafe代码，使用安全的内存操作方法</div>
            </div>
            
            <div class="issue high">
                <div class="issue-header">
                    <div class="issue-title">不安全的内存操作</div>
                    <div class="issue-severity high">high</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./src/internal/utils.rs:39</div>
                    <div class="issue-code">/// This function is unsafe because it creates a slice of a different type</div>
                </div>
                <div class="issue-description">发现不安全的内存操作问题</div>
                <div class="issue-recommendation">避免使用unsafe代码，使用安全的内存操作方法</div>
            </div>
            
            <div class="issue high">
                <div class="issue-header">
                    <div class="issue-title">不安全的内存操作</div>
                    <div class="issue-severity high">high</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./src/internal/utils.rs:41</div>
                    <div class="issue-code">pub unsafe fn bytes_to_slice<T>(bytes: &[u8]) -> &[T] {</div>
                </div>
                <div class="issue-description">发现不安全的内存操作问题</div>
                <div class="issue-recommendation">避免使用unsafe代码，使用安全的内存操作方法</div>
            </div>
            
            <div class="issue high">
                <div class="issue-header">
                    <div class="issue-title">不安全的内存操作</div>
                    <div class="issue-severity high">high</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./src/internal/utils.rs:52</div>
                    <div class="issue-code">/// This function is unsafe because it creates a slice of a different type</div>
                </div>
                <div class="issue-description">发现不安全的内存操作问题</div>
                <div class="issue-recommendation">避免使用unsafe代码，使用安全的内存操作方法</div>
            </div>
            
            <div class="issue high">
                <div class="issue-header">
                    <div class="issue-title">不安全的内存操作</div>
                    <div class="issue-severity high">high</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./src/internal/utils.rs:54</div>
                    <div class="issue-code">pub unsafe fn bytes_to_slice_mut<T>(bytes: &mut [u8]) -> &mut [T] {</div>
                </div>
                <div class="issue-description">发现不安全的内存操作问题</div>
                <div class="issue-recommendation">避免使用unsafe代码，使用安全的内存操作方法</div>
            </div>
            
            <div class="issue high">
                <div class="issue-header">
                    <div class="issue-title">不安全的内存操作</div>
                    <div class="issue-severity high">high</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/test_zero_copy.rs:51</div>
                    <div class="issue-code">let mmap = unsafe { memmap2::Mmap::map(&file).unwrap() };</div>
                </div>
                <div class="issue-description">发现不安全的内存操作问题</div>
                <div class="issue-recommendation">避免使用unsafe代码，使用安全的内存操作方法</div>
            </div>
            
            <div class="issue high">
                <div class="issue-header">
                    <div class="issue-title">不安全的内存操作</div>
                    <div class="issue-severity high">high</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/test_zero_copy.rs:75</div>
                    <div class="issue-code">let mut mmap = unsafe { memmap2::MmapMut::map_mut(&file).unwrap() };</div>
                </div>
                <div class="issue-description">发现不安全的内存操作问题</div>
                <div class="issue-recommendation">避免使用unsafe代码，使用安全的内存操作方法</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./generate_docs.py:529</div>
                    <div class="issue-code">print(f"API文档已生成：{api_docs_file}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./generate_docs.py:535</div>
                    <div class="issue-code">print(f"用户指南已生成：{user_guide_file}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./generate_docs.py:541</div>
                    <div class="issue-code">print(f"开发者指南已生成：{developer_guide_file}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./generate_docs.py:547</div>
                    <div class="issue-code">print(f"示例文档已生成：{example_docs_file}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./generate_docs.py:553</div>
                    <div class="issue-code">print(f"性能文档已生成：{performance_docs_file}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./generate_docs.py:578</div>
                    <div class="issue-code">print(f"索引文档已生成：{index_file}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/run_security_audit.py:24</div>
                    <div class="issue-code">print("正在运行安全审计...")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/run_security_audit.py:30</div>
                    <div class="issue-code">print("\n=== 运行代码安全审计 ===")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/run_security_audit.py:39</div>
                    <div class="issue-code">print("代码安全审计完成")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/run_security_audit.py:41</div>
                    <div class="issue-code">print(f"代码安全审计失败: {e}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/run_security_audit.py:44</div>
                    <div class="issue-code">print("\n=== 运行配置安全检查 ===")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/run_security_audit.py:53</div>
                    <div class="issue-code">print("配置安全检查完成")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/run_security_audit.py:55</div>
                    <div class="issue-code">print(f"配置安全检查失败: {e}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/run_security_audit.py:58</div>
                    <div class="issue-code">print("\n=== 运行安全漏洞扫描 ===")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/run_security_audit.py:67</div>
                    <div class="issue-code">print("安全漏洞扫描完成")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/run_security_audit.py:69</div>
                    <div class="issue-code">print(f"安全漏洞扫描失败: {e}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/run_security_audit.py:72</div>
                    <div class="issue-code">print("\n=== 生成综合安全审计报告 ===")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/run_security_audit.py:74</div>
                    <div class="issue-code">print("综合安全审计报告生成完成")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/run_security_audit.py:76</div>
                    <div class="issue-code">print("\n安全审计完成")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/run_security_audit.py:89</div>
                    <div class="issue-code">print(f"加载代码安全审计报告失败: {e}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/run_security_audit.py:95</div>
                    <div class="issue-code">print(f"加载配置安全检查报告失败: {e}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/run_security_audit.py:101</div>
                    <div class="issue-code">print(f"加载安全漏洞扫描报告失败: {e}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:38</div>
                    <div class="issue-code">print("正在审计代码安全...")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:59</div>
                    <div class="issue-code">print(f"代码安全审计完成，发现{len(self.issues)}个问题")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:149</div>
                    <div class="issue-code">print("正在审计依赖项安全...")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:163</div>
                    <div class="issue-code">print(f"依赖项安全审计完成，发现{len(dependency_issues)}个问题")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:177</div>
                    <div class="issue-code">print("未找到requirements.txt文件，跳过Python依赖项检查")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:211</div>
                    <div class="issue-code">print(f"检查Python依赖项时出错: {e}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:227</div>
                    <div class="issue-code">print("未找到Cargo.toml文件，跳过Rust依赖项检查")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:262</div>
                    <div class="issue-code">print(f"检查Rust依赖项时出错: {e}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:268</div>
                    <div class="issue-code">print("正在审计配置安全...")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:276</div>
                    <div class="issue-code">print(f"配置安全审计完成，发现{len(config_issues)}个问题")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:321</div>
                    <div class="issue-code">print("正在生成安全审计报告...")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:354</div>
                    <div class="issue-code">print(f"安全审计报告已生成: {report_file}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:355</div>
                    <div class="issue-code">print(f"总问题数: {len(self.issues)}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:356</div>
                    <div class="issue-code">print(f"高危问题: {severity_count['high']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:357</div>
                    <div class="issue-code">print(f"中危问题: {severity_count['medium']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:358</div>
                    <div class="issue-code">print(f"低危问题: {severity_count['low']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">敏感信息泄露</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:74</div>
                    <div class="issue-code">"敏感信息泄露": r"(TODO|FIXME|XXX|BUG|HACK)",</div>
                </div>
                <div class="issue-description">发现敏感信息泄露问题</div>
                <div class="issue-recommendation">移除代码中的敏感信息和注释</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">敏感信息泄露</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:74</div>
                    <div class="issue-code">"敏感信息泄露": r"(TODO|FIXME|XXX|BUG|HACK)",</div>
                </div>
                <div class="issue-description">发现敏感信息泄露问题</div>
                <div class="issue-recommendation">移除代码中的敏感信息和注释</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">敏感信息泄露</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:74</div>
                    <div class="issue-code">"敏感信息泄露": r"(TODO|FIXME|XXX|BUG|HACK)",</div>
                </div>
                <div class="issue-description">发现敏感信息泄露问题</div>
                <div class="issue-recommendation">移除代码中的敏感信息和注释</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">敏感信息泄露</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:74</div>
                    <div class="issue-code">"敏感信息泄露": r"(TODO|FIXME|XXX|BUG|HACK)",</div>
                </div>
                <div class="issue-description">发现敏感信息泄露问题</div>
                <div class="issue-recommendation">移除代码中的敏感信息和注释</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">敏感信息泄露</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:74</div>
                    <div class="issue-code">"敏感信息泄露": r"(TODO|FIXME|XXX|BUG|HACK)",</div>
                </div>
                <div class="issue-description">发现敏感信息泄露问题</div>
                <div class="issue-recommendation">移除代码中的敏感信息和注释</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">敏感信息泄露</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:107</div>
                    <div class="issue-code">"敏感信息泄露": r"(TODO|FIXME|XXX|BUG|HACK)",</div>
                </div>
                <div class="issue-description">发现敏感信息泄露问题</div>
                <div class="issue-recommendation">移除代码中的敏感信息和注释</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">敏感信息泄露</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:107</div>
                    <div class="issue-code">"敏感信息泄露": r"(TODO|FIXME|XXX|BUG|HACK)",</div>
                </div>
                <div class="issue-description">发现敏感信息泄露问题</div>
                <div class="issue-recommendation">移除代码中的敏感信息和注释</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">敏感信息泄露</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:107</div>
                    <div class="issue-code">"敏感信息泄露": r"(TODO|FIXME|XXX|BUG|HACK)",</div>
                </div>
                <div class="issue-description">发现敏感信息泄露问题</div>
                <div class="issue-recommendation">移除代码中的敏感信息和注释</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">敏感信息泄露</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:107</div>
                    <div class="issue-code">"敏感信息泄露": r"(TODO|FIXME|XXX|BUG|HACK)",</div>
                </div>
                <div class="issue-description">发现敏感信息泄露问题</div>
                <div class="issue-recommendation">移除代码中的敏感信息和注释</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">敏感信息泄露</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/security_audit.py:107</div>
                    <div class="issue-code">"敏感信息泄露": r"(TODO|FIXME|XXX|BUG|HACK)",</div>
                </div>
                <div class="issue-description">发现敏感信息泄露问题</div>
                <div class="issue-recommendation">移除代码中的敏感信息和注释</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/config_checker.py:39</div>
                    <div class="issue-code">print("正在检查配置文件...")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/config_checker.py:52</div>
                    <div class="issue-code">print(f"配置检查完成，发现{len(self.issues)}个问题")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/config_checker.py:56</div>
                    <div class="issue-code">print(f"检查配置文件: {config_file}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/config_checker.py:326</div>
                    <div class="issue-code">print("正在检查权限设置...")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/config_checker.py:339</div>
                    <div class="issue-code">print(f"权限检查完成，发现{len(self.issues)}个问题")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/config_checker.py:384</div>
                    <div class="issue-code">print("正在生成安全配置报告...")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/config_checker.py:417</div>
                    <div class="issue-code">print(f"安全配置报告已生成: {report_file}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/config_checker.py:418</div>
                    <div class="issue-code">print(f"总问题数: {len(self.issues)}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/config_checker.py:419</div>
                    <div class="issue-code">print(f"高危问题: {severity_count['high']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/config_checker.py:420</div>
                    <div class="issue-code">print(f"中危问题: {severity_count['medium']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/config_checker.py:421</div>
                    <div class="issue-code">print(f"低危问题: {severity_count['low']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/vulnerability_scanner.py:37</div>
                    <div class="issue-code">print("正在扫描代码中的安全漏洞...")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/vulnerability_scanner.py:67</div>
                    <div class="issue-code">print(f"代码扫描完成，发现{len(self.vulnerabilities)}个安全漏洞")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/vulnerability_scanner.py:71</div>
                    <div class="issue-code">print(f"扫描Python文件: {file_path}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/vulnerability_scanner.py:90</div>
                    <div class="issue-code">print(f"扫描Python文件时出错: {e}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/vulnerability_scanner.py:228</div>
                    <div class="issue-code">print(f"扫描Rust文件: {file_path}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/vulnerability_scanner.py:244</div>
                    <div class="issue-code">print(f"扫描Rust文件时出错: {e}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/vulnerability_scanner.py:320</div>
                    <div class="issue-code">print(f"扫描C++文件: {file_path}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/vulnerability_scanner.py:336</div>
                    <div class="issue-code">print(f"扫描C++文件时出错: {e}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/vulnerability_scanner.py:470</div>
                    <div class="issue-code">print("正在生成安全漏洞报告...")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/vulnerability_scanner.py:512</div>
                    <div class="issue-code">print(f"安全漏洞报告已生成: {report_file}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/vulnerability_scanner.py:513</div>
                    <div class="issue-code">print(f"总漏洞数: {len(self.vulnerabilities)}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/vulnerability_scanner.py:514</div>
                    <div class="issue-code">print(f"高危漏洞: {severity_count['high']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/vulnerability_scanner.py:515</div>
                    <div class="issue-code">print(f"中危漏洞: {severity_count['medium']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./security/vulnerability_scanner.py:516</div>
                    <div class="issue-code">print(f"低危漏洞: {severity_count['low']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:47</div>
                    <div class="issue-code">print(f"反馈已提交，ID：{feedback_id}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:59</div>
                    <div class="issue-code">print(f"ID：{feedback['id']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:60</div>
                    <div class="issue-code">print(f"时间：{feedback['timestamp']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:61</div>
                    <div class="issue-code">print(f"类型：{feedback['type']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:62</div>
                    <div class="issue-code">print(f"标题：{feedback['title']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:63</div>
                    <div class="issue-code">print(f"描述：{feedback['description']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:64</div>
                    <div class="issue-code">print(f"用户：{feedback['user_name'] or '匿名'}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:65</div>
                    <div class="issue-code">print(f"邮箱：{feedback['user_email'] or '无'}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:66</div>
                    <div class="issue-code">print(f"组织：{feedback['user_organization'] or '无'}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:67</div>
                    <div class="issue-code">print(f"环境：{feedback['environment'] or '无'}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:68</div>
                    <div class="issue-code">print(f"严重程度：{feedback['severity'] or '无'}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:69</div>
                    <div class="issue-code">print(f"状态：{feedback['status']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:70</div>
                    <div class="issue-code">print(f"处理人：{feedback['assignee'] or '无'}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:71</div>
                    <div class="issue-code">print(f"标签：{', '.join(feedback['tags']) if feedback['tags'] else '无'}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:74</div>
                    <div class="issue-code">print("\n评论：")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:76</div>
                    <div class="issue-code">print(f"  {comment['timestamp']} {comment['user_name'] or '匿名'}：{comment['content']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:79</div>
                    <div class="issue-code">print("\n投票：")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:82</div>
                    <div class="issue-code">print(f"  赞成：{upvotes}，反对：{downvotes}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:84</div>
                    <div class="issue-code">print(f"未找到ID为{args.id}的反馈")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:90</div>
                    <div class="issue-code">print(f"共找到{len(feedbacks)}条反馈：")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:92</div>
                    <div class="issue-code">print(f"ID：{feedback['id']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:93</div>
                    <div class="issue-code">print(f"时间：{feedback['timestamp']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:94</div>
                    <div class="issue-code">print(f"类型：{feedback['type']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:95</div>
                    <div class="issue-code">print(f"标题：{feedback['title']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:96</div>
                    <div class="issue-code">print(f"状态：{feedback['status']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:97</div>
                    <div class="issue-code">print()</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:99</div>
                    <div class="issue-code">print("未找到反馈")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:114</div>
                    <div class="issue-code">print(f"评论已添加，ID：{comment_id}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:129</div>
                    <div class="issue-code">print(f"投票已添加，ID：{vote_id}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:143</div>
                    <div class="issue-code">print(f"反馈状态已更新为：{args.status}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:153</div>
                    <div class="issue-code">print(f"反馈报告已生成：{args.output}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:154</div>
                    <div class="issue-code">print(f"总反馈数：{report['total_feedback']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:155</div>
                    <div class="issue-code">print("按类型统计：")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:157</div>
                    <div class="issue-code">print(f"  {type}：{count}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:158</div>
                    <div class="issue-code">print("按状态统计：")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_cli.py:160</div>
                    <div class="issue-code">print(f"  {status}：{count}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_server.py:477</div>
                    <div class="issue-code">print(f"服务器已启动，地址：http{'s' if ssl_cert and ssl_key else ''}://{host}:{port}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./feedback/feedback_server.py:482</div>
                    <div class="issue-code">print("服务器已停止")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/performance_database.py:441</div>
                    <div class="issue-code">print(f"导入成功，运行ID：{run_id}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/performance_database.py:444</div>
                    <div class="issue-code">print(f"导出成功，趋势报告已保存到：{args.output}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/visualize_benchmark.py:122</div>
                    <div class="issue-code">print(f"可视化结果已保存到：{args.output_dir}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/visualize_benchmark_html.py:237</div>
                    <div class="issue-code">print(f"HTML报告已生成：{args.output}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/auto_optimize.py:424</div>
                    <div class="issue-code">print(json.dumps(results, indent=2))</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/auto_optimize.py:436</div>
                    <div class="issue-code">print(f"测试脚本运行失败：{result.stderr}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/auto_optimize.py:443</div>
                    <div class="issue-code">print(f"无法解析测试结果：{result.stdout}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/auto_optimize.py:651</div>
                    <div class="issue-code">print("未发现性能瓶颈")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/auto_optimize.py:654</div>
                    <div class="issue-code">print(f"发现{len(bottlenecks)}个性能瓶颈")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/auto_optimize.py:660</div>
                    <div class="issue-code">print("未生成优化建议")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/auto_optimize.py:663</div>
                    <div class="issue-code">print(f"生成{len(suggestions)}个优化建议")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/auto_optimize.py:668</div>
                    <div class="issue-code">print(f"应用优化建议到{len(suggestions_by_file)}个文件")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/auto_optimize.py:674</div>
                    <div class="issue-code">print("验证优化效果成功")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/auto_optimize.py:678</div>
                    <div class="issue-code">print(f"  {function_name}: {result['speedup']:.2f}x")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/auto_optimize.py:680</div>
                    <div class="issue-code">print("验证优化效果失败")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/auto_optimize.py:690</div>
                    <div class="issue-code">print(f"优化报告已生成：{report_file}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/auto_optimize.py:691</div>
                    <div class="issue-code">print(f"HTML优化报告已生成：{html_report_file}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">不安全的随机数</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/optimize_performance.py:197</div>
                    <div class="issue-code">nonzero_indices = np.random.choice(dim, num_nonzero, replace=False)</div>
                </div>
                <div class="issue-description">发现不安全的随机数问题</div>
                <div class="issue-recommendation">使用secrets模块生成安全的随机数</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/optimize_performance.py:27</div>
                    <div class="issue-code">print("\n=== 内存对齐优化 ===")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/optimize_performance.py:56</div>
                    <div class="issue-code">print(f"未对齐数组时间: {unaligned_time*1000:.3f}毫秒")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/optimize_performance.py:57</div>
                    <div class="issue-code">print(f"对齐数组时间: {aligned_time*1000:.3f}毫秒")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/optimize_performance.py:58</div>
                    <div class="issue-code">print(f"性能提升: {speedup:.2f}x")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/optimize_performance.py:68</div>
                    <div class="issue-code">print("\n=== 缓存优化 ===")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/optimize_performance.py:110</div>
                    <div class="issue-code">print(f"行优先访问时间: {row_major_time*1000:.3f}毫秒")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/optimize_performance.py:111</div>
                    <div class="issue-code">print(f"列优先访问时间: {column_major_time*1000:.3f}毫秒")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/optimize_performance.py:112</div>
                    <div class="issue-code">print(f"分块访问时间: {blocked_time*1000:.3f}毫秒")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/optimize_performance.py:113</div>
                    <div class="issue-code">print(f"行优先vs列优先性能提升: {row_vs_column_speedup:.2f}x")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/optimize_performance.py:114</div>
                    <div class="issue-code">print(f"行优先vs分块性能提升: {row_vs_blocked_speedup:.2f}x")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/optimize_performance.py:126</div>
                    <div class="issue-code">print("\n=== 并行处理优化 ===")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/optimize_performance.py:176</div>
                    <div class="issue-code">print(f"串行处理时间: {serial_time*1000:.3f}毫秒")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/optimize_performance.py:177</div>
                    <div class="issue-code">print(f"并行处理时间: {parallel_time*1000:.3f}毫秒")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/optimize_performance.py:178</div>
                    <div class="issue-code">print(f"性能提升: {speedup:.2f}x")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/optimize_performance.py:189</div>
                    <div class="issue-code">print("\n=== 稀疏表示优化 ===")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/optimize_performance.py:247</div>
                    <div class="issue-code">print(f"密集表示时间: {dense_time*1000:.3f}毫秒, 大小: {dense_size/1024/1024:.2f} MB")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/optimize_performance.py:248</div>
                    <div class="issue-code">print(f"稀疏表示时间: {sparse_time*1000:.3f}毫秒, 大小: {sparse_size/1024/1024:.2f} MB")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/optimize_performance.py:249</div>
                    <div class="issue-code">print(f"性能提升: {speedup:.2f}x")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/optimize_performance.py:250</div>
                    <div class="issue-code">print(f"内存节省: {memory_saving:.2f}%")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/optimize_performance.py:295</div>
                    <div class="issue-code">print(f"\n结果已保存到：{args.output}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/benchmark_zero_copy.py:25</div>
                    <div class="issue-code">print("\n=== NumPy数组零拷贝转换性能基准测试 ===")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/benchmark_zero_copy.py:66</div>
                    <div class="issue-code">print(f"数组大小: {size}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/benchmark_zero_copy.py:67</div>
                    <div class="issue-code">print(f"  零拷贝时间: {zero_copy_time*1000:.3f}毫秒, 吞吐量: {zero_copy_throughput:.2f} MB/s")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/benchmark_zero_copy.py:68</div>
                    <div class="issue-code">print(f"  普通复制时间: {copy_time*1000:.3f}毫秒, 吞吐量: {copy_throughput:.2f} MB/s")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/benchmark_zero_copy.py:69</div>
                    <div class="issue-code">print(f"  加速比: {speedup:.2f}x")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/benchmark_zero_copy.py:75</div>
                    <div class="issue-code">print("\n=== NumPy数组文件映射性能基准测试 ===")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/benchmark_zero_copy.py:126</div>
                    <div class="issue-code">print(f"数组大小: {size}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/benchmark_zero_copy.py:127</div>
                    <div class="issue-code">print(f"  内存映射时间: {mmap_time*1000:.3f}毫秒, 吞吐量: {mmap_throughput:.2f} MB/s")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/benchmark_zero_copy.py:128</div>
                    <div class="issue-code">print(f"  普通加载时间: {load_time*1000:.3f}毫秒, 吞吐量: {load_throughput:.2f} MB/s")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/benchmark_zero_copy.py:129</div>
                    <div class="issue-code">print(f"  加速比: {speedup:.2f}x")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/benchmark_zero_copy.py:139</div>
                    <div class="issue-code">print("\n=== 量子态序列化和反序列化性能基准测试 ===")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/benchmark_zero_copy.py:187</div>
                    <div class="issue-code">print(f"量子比特数: {n_qubits}, 维度: {dim}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/benchmark_zero_copy.py:188</div>
                    <div class="issue-code">print(f"  序列化时间: {serialize_time*1000:.3f}毫秒, 吞吐量: {serialize_throughput:.2f} MB/s")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/benchmark_zero_copy.py:189</div>
                    <div class="issue-code">print(f"  反序列化时间: {deserialize_time*1000:.3f}毫秒, 吞吐量: {deserialize_throughput:.2f} MB/s")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/benchmark_zero_copy.py:195</div>
                    <div class="issue-code">print("\n=== 分形结构序列化和反序列化性能基准测试 ===")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/benchmark_zero_copy.py:276</div>
                    <div class="issue-code">print(f"深度: {depth}, 节点数: {len(nodes)}, 连接数: {len(connections)}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/benchmark_zero_copy.py:277</div>
                    <div class="issue-code">print(f"  序列化时间: {serialize_time*1000:.3f}毫秒, 吞吐量: {serialize_throughput:.2f} MB/s")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/benchmark_zero_copy.py:278</div>
                    <div class="issue-code">print(f"  反序列化时间: {deserialize_time*1000:.3f}毫秒, 吞吐量: {deserialize_throughput:.2f} MB/s")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/benchmark_zero_copy.py:316</div>
                    <div class="issue-code">print(f"\n结果已保存到：{args.output}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/generate_physics_reports.py:575</div>
                    <div class="issue-code">print(f"量子态物理正确性测试报告已生成：{quantum_report}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/generate_physics_reports.py:579</div>
                    <div class="issue-code">print(f"分形结构物理正确性测试报告已生成：{fractal_report}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/generate_physics_reports.py:583</div>
                    <div class="issue-code">print(f"全息数据物理正确性测试报告已生成：{holographic_report}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/detect_performance_regression.py:129</div>
                    <div class="issue-code">print("没有检测到性能退化")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/detect_performance_regression.py:132</div>
                    <div class="issue-code">print("检测到性能退化：")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/detect_performance_regression.py:135</div>
                    <div class="issue-code">print("\nNumPy数组零拷贝转换性能退化：")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/detect_performance_regression.py:137</div>
                    <div class="issue-code">print(f"  数组大小: {regression['size']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/detect_performance_regression.py:138</div>
                    <div class="issue-code">print(f"    当前吞吐量: {regression['current_throughput']:.2f} MB/s")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/detect_performance_regression.py:139</div>
                    <div class="issue-code">print(f"    基准线吞吐量: {regression['baseline_throughput']:.2f} MB/s")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/detect_performance_regression.py:140</div>
                    <div class="issue-code">print(f"    性能下降: {regression['change_percent']:.2f}%")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/detect_performance_regression.py:143</div>
                    <div class="issue-code">print("\n量子态序列化和反序列化性能退化：")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/detect_performance_regression.py:145</div>
                    <div class="issue-code">print(f"  量子比特数: {regression['n_qubits']}, 操作: {regression['operation']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/detect_performance_regression.py:146</div>
                    <div class="issue-code">print(f"    当前吞吐量: {regression['current_throughput']:.2f} MB/s")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/detect_performance_regression.py:147</div>
                    <div class="issue-code">print(f"    基准线吞吐量: {regression['baseline_throughput']:.2f} MB/s")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/detect_performance_regression.py:148</div>
                    <div class="issue-code">print(f"    性能下降: {regression['change_percent']:.2f}%")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/detect_performance_regression.py:179</div>
                    <div class="issue-code">print(f"更新基准线: {baseline_path}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/detect_performance_regression.py:192</div>
                    <div class="issue-code">print(f"性能退化信息已保存到: {args.output}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/performance_monitor.py:82</div>
                    <div class="issue-code">print(f"性能监控已启动，间隔：{self.interval}秒，阈值：{self.threshold}%")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/performance_monitor.py:90</div>
                    <div class="issue-code">print("性能监控已停止")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/performance_monitor.py:109</div>
                    <div class="issue-code">print(f"监控循环异常：{e}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/performance_monitor.py:143</div>
                    <div class="issue-code">print(f"收集NumPy数组零拷贝转换性能数据异常：{e}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/performance_monitor.py:169</div>
                    <div class="issue-code">print(f"收集量子态序列化性能数据异常：{e}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/performance_monitor.py:237</div>
                    <div class="issue-code">print(f"警报：{alert['test_name']} {alert['metric_name']} 性能退化 {alert['change_percent']:.2f}%，严重程度：{alert['severity']}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/performance_monitor.py:580</div>
                    <div class="issue-code">print(f"性能报告已生成：{args.report}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/performance_monitor.py:586</div>
                    <div class="issue-code">print(f"HTML性能报告已生成：{args.html_report}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/performance_monitor.py:595</div>
                    <div class="issue-code">print(f"性能监控已作为守护进程启动，数据库：{args.db}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/performance_monitor.py:602</div>
                    <div class="issue-code">print("性能监控已启动，按Ctrl+C退出")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">不安全的随机数</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/test_multithreading.py:60</div>
                    <div class="issue-code">start = random.randint(0, self.array_size - 1000)</div>
                </div>
                <div class="issue-description">发现不安全的随机数问题</div>
                <div class="issue-recommendation">使用secrets模块生成安全的随机数</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">不安全的随机数</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/test_multithreading.py:107</div>
                    <div class="issue-code">start = random.randint(0, self.array_size - 1000)</div>
                </div>
                <div class="issue-description">发现不安全的随机数问题</div>
                <div class="issue-recommendation">使用secrets模块生成安全的随机数</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">不安全的随机数</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/test_multithreading.py:146</div>
                    <div class="issue-code">start = random.randint(0, self.array_size - 1000)</div>
                </div>
                <div class="issue-description">发现不安全的随机数问题</div>
                <div class="issue-recommendation">使用secrets模块生成安全的随机数</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">不安全的随机数</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/test_multithreading.py:242</div>
                    <div class="issue-code">start = random.randint(0, self.array_size - 1000)</div>
                </div>
                <div class="issue-description">发现不安全的随机数问题</div>
                <div class="issue-recommendation">使用secrets模块生成安全的随机数</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/test_multithreading.py:211</div>
                    <div class="issue-code">print(f"预期值: {expected_value}, 实际值: {actual_value}, 差异: {expected_value - actual_value}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/cross_platform_test.py:685</div>
                    <div class="issue-code">print(f"跨平台测试报告已生成：{report_file}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/cross_platform_test.py:686</div>
                    <div class="issue-code">print(f"HTML跨平台测试报告已生成：{html_report_file}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/analyze_coverage.py:335</div>
                    <div class="issue-code">print(f"覆盖率报告已生成：{args.output_dir}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/analyze_coverage.py:336</div>
                    <div class="issue-code">print(f"总体覆盖率：{report['total_coverage']:.2f}%")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/analyze_coverage.py:339</div>
                    <div class="issue-code">print("\n未覆盖代码模式：")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/analyze_coverage.py:341</div>
                    <div class="issue-code">print(f"  {pattern_name}: {pattern_count}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/analyze_coverage.py:344</div>
                    <div class="issue-code">print("\n覆盖率最低的文件：")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/analyze_coverage.py:346</div>
                    <div class="issue-code">print(f"  {file_path}: {file_data['percent_covered']:.2f}%")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">不安全的随机数</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/test_fractal_physics.py:70</div>
                    <div class="issue-code">func = np.random.choice(self.sierpinski_ifs)</div>
                </div>
                <div class="issue-description">发现不安全的随机数问题</div>
                <div class="issue-recommendation">使用secrets模块生成安全的随机数</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">不安全的随机数</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/test_fractal_physics.py:85</div>
                    <div class="issue-code">func = np.random.choice(self.koch_ifs)</div>
                </div>
                <div class="issue-description">发现不安全的随机数问题</div>
                <div class="issue-recommendation">使用secrets模块生成安全的随机数</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">不安全的随机数</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/test_python_rust_interop.py:234</div>
                    <div class="issue-code">original_data = [complex(random.random(), random.random()) for _ in range(n)]</div>
                </div>
                <div class="issue-description">发现不安全的随机数问题</div>
                <div class="issue-recommendation">使用secrets模块生成安全的随机数</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">不安全的随机数</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/test_python_rust_interop.py:234</div>
                    <div class="issue-code">original_data = [complex(random.random(), random.random()) for _ in range(n)]</div>
                </div>
                <div class="issue-description">发现不安全的随机数问题</div>
                <div class="issue-recommendation">使用secrets模块生成安全的随机数</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">不安全的随机数</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/test_python_rust_interop.py:237</div>
                    <div class="issue-code">encoding_matrix = [[complex(random.random(), random.random()) for _ in range(n)] for _ in range(n)]</div>
                </div>
                <div class="issue-description">发现不安全的随机数问题</div>
                <div class="issue-recommendation">使用secrets模块生成安全的随机数</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">不安全的随机数</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/test_python_rust_interop.py:237</div>
                    <div class="issue-code">encoding_matrix = [[complex(random.random(), random.random()) for _ in range(n)] for _ in range(n)]</div>
                </div>
                <div class="issue-description">发现不安全的随机数问题</div>
                <div class="issue-recommendation">使用secrets模块生成安全的随机数</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">不安全的随机数</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/test_python_rust_interop.py:283</div>
                    <div class="issue-code">data = bytes([random.randint(0, 255) for _ in range(total_size)])</div>
                </div>
                <div class="issue-description">发现不安全的随机数问题</div>
                <div class="issue-recommendation">使用secrets模块生成安全的随机数</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/test_python_rust_interop.py:68</div>
                    <div class="issue-code">print("警告：arrow_integration模块不可用，跳过测试")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">不安全的随机数</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/benchmark_special_types.py:152</div>
                    <div class="issue-code">original_data = [complex(random.random(), random.random()) for _ in range(n)]</div>
                </div>
                <div class="issue-description">发现不安全的随机数问题</div>
                <div class="issue-recommendation">使用secrets模块生成安全的随机数</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">不安全的随机数</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/benchmark_special_types.py:152</div>
                    <div class="issue-code">original_data = [complex(random.random(), random.random()) for _ in range(n)]</div>
                </div>
                <div class="issue-description">发现不安全的随机数问题</div>
                <div class="issue-recommendation">使用secrets模块生成安全的随机数</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/benchmark_special_types.py:69</div>
                    <div class="issue-code">print("警告：arrow_integration模块不可用，跳过基准测试")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/benchmark_special_types.py:178</div>
                    <div class="issue-code">print("\n=== 量子态性能基准测试 ===")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/benchmark_special_types.py:238</div>
                    <div class="issue-code">print(f"量子比特数: {n_qubits}, 维度: {dim}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/benchmark_special_types.py:239</div>
                    <div class="issue-code">print(f"  序列化时间: {serialize_time*1000:.3f}毫秒, 吞吐量: {serialize_throughput:.2f} MB/s")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/benchmark_special_types.py:240</div>
                    <div class="issue-code">print(f"  反序列化时间: {deserialize_time*1000:.3f}毫秒, 吞吐量: {deserialize_throughput:.2f} MB/s")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/benchmark_special_types.py:241</div>
                    <div class="issue-code">print(f"  传输时间: {transfer_time*1000:.3f}毫秒, 吞吐量: {transfer_throughput:.2f} MB/s")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/benchmark_special_types.py:251</div>
                    <div class="issue-code">print("\n=== 分形结构性能基准测试 ===")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/benchmark_special_types.py:310</div>
                    <div class="issue-code">print(f"深度: {depth}, 节点数: {node_count}, 连接数: {connection_count}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/benchmark_special_types.py:311</div>
                    <div class="issue-code">print(f"  序列化时间: {serialize_time*1000:.3f}毫秒, 吞吐量: {serialize_throughput:.2f} MB/s")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/benchmark_special_types.py:312</div>
                    <div class="issue-code">print(f"  反序列化时间: {deserialize_time*1000:.3f}毫秒, 吞吐量: {deserialize_throughput:.2f} MB/s")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/benchmark_special_types.py:313</div>
                    <div class="issue-code">print(f"  传输时间: {transfer_time*1000:.3f}毫秒, 吞吐量: {transfer_throughput:.2f} MB/s")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/benchmark_special_types.py:323</div>
                    <div class="issue-code">print("\n=== 全息数据性能基准测试 ===")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/benchmark_special_types.py:396</div>
                    <div class="issue-code">print(f"大小: {size}, 序列化大小: {len(serialized)/1024:.1f} KB")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/benchmark_special_types.py:397</div>
                    <div class="issue-code">print(f"  序列化时间: {serialize_time*1000:.3f}毫秒, 吞吐量: {serialize_throughput:.2f} MB/s")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/benchmark_special_types.py:398</div>
                    <div class="issue-code">print(f"  反序列化时间: {deserialize_time*1000:.3f}毫秒, 吞吐量: {deserialize_throughput:.2f} MB/s")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/benchmark_special_types.py:399</div>
                    <div class="issue-code">print(f"  传输时间: {transfer_time*1000:.3f}毫秒, 吞吐量: {transfer_throughput:.2f} MB/s")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/benchmark_special_types.py:410</div>
                    <div class="issue-code">print("arrow_integration模块不可用，跳过基准测试")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/benchmark_special_types.py:441</div>
                    <div class="issue-code">print(f"\n结果已保存到：{args.output}")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">不安全的随机数</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/test_end_to_end.py:212</div>
                    <div class="issue-code">original_data = [complex(random.random(), random.random()) for _ in range(n)]</div>
                </div>
                <div class="issue-description">发现不安全的随机数问题</div>
                <div class="issue-recommendation">使用secrets模块生成安全的随机数</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">不安全的随机数</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/test_end_to_end.py:212</div>
                    <div class="issue-code">original_data = [complex(random.random(), random.random()) for _ in range(n)]</div>
                </div>
                <div class="issue-description">发现不安全的随机数问题</div>
                <div class="issue-recommendation">使用secrets模块生成安全的随机数</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">不安全的随机数</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/test_end_to_end.py:213</div>
                    <div class="issue-code">encoding_matrix = [[complex(random.random(), random.random()) for _ in range(n)] for _ in range(n)]</div>
                </div>
                <div class="issue-description">发现不安全的随机数问题</div>
                <div class="issue-recommendation">使用secrets模块生成安全的随机数</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">不安全的随机数</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/test_end_to_end.py:213</div>
                    <div class="issue-code">encoding_matrix = [[complex(random.random(), random.random()) for _ in range(n)] for _ in range(n)]</div>
                </div>
                <div class="issue-description">发现不安全的随机数问题</div>
                <div class="issue-recommendation">使用secrets模块生成安全的随机数</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">调试代码</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./tests/python/test_end_to_end.py:68</div>
                    <div class="issue-code">print("警告：arrow_integration模块不可用，跳过测试")</div>
                </div>
                <div class="issue-description">发现调试代码问题</div>
                <div class="issue-recommendation">移除生产环境中的调试代码</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">不安全的连接</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./deploy/staging.env:15</div>
                    <div class="issue-code">DEPLOY_HEALTH_CHECK_URL="http://staging.example.com:8080/health"</div>
                </div>
                <div class="issue-description">配置文件中发现不安全的连接问题</div>
                <div class="issue-recommendation">修复安全问题</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">不安全的连接</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./deploy/development.env:15</div>
                    <div class="issue-code">DEPLOY_HEALTH_CHECK_URL="http://localhost:8080/health"</div>
                </div>
                <div class="issue-description">配置文件中发现不安全的连接问题</div>
                <div class="issue-recommendation">修复安全问题</div>
            </div>
            
            <div class="issue medium">
                <div class="issue-header">
                    <div class="issue-title">不安全的连接</div>
                    <div class="issue-severity medium">medium</div>
                </div>
                <div class="issue-details">
                    <div class="issue-file">./deploy/production.env:15</div>
                    <div class="issue-code">DEPLOY_HEALTH_CHECK_URL="http://production.example.com:8080/health"</div>
                </div>
                <div class="issue-description">配置文件中发现不安全的连接问题</div>
                <div class="issue-recommendation">修复安全问题</div>
            </div>
            
        </div>
    </div>
</body>
</html>
        