#!/bin/bash

# 零拷贝机制测试运行脚本
# 这个脚本运行所有测试和基准测试，并生成测试报告

set -e

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
function print_header() {
    echo -e "${BLUE}==== $1 ====${NC}"
}

function print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

function print_error() {
    echo -e "${RED}✗ $1${NC}"
}

function print_warning() {
    echo -e "${YELLOW}! $1${NC}"
}

# 当前目录
SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
cd "$SCRIPT_DIR"

# 创建输出目录
OUTPUT_DIR="$SCRIPT_DIR/test_results"
mkdir -p "$OUTPUT_DIR"

# 解析命令行参数
RUN_PYTHON_TESTS=true
RUN_PYTHON_BENCHMARKS=true
GENERATE_REPORT=true
RUN_COVERAGE=false
RUN_CROSS_PLATFORM=false
RUN_OPTIMIZE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-python-tests)
            RUN_PYTHON_TESTS=false
            shift
            ;;
        --skip-python-benchmarks)
            RUN_PYTHON_BENCHMARKS=false
            shift
            ;;
        --skip-report)
            GENERATE_REPORT=false
            shift
            ;;
        --coverage)
            RUN_COVERAGE=true
            shift
            ;;
        --cross-platform)
            RUN_CROSS_PLATFORM=true
            shift
            ;;
        --optimize)
            RUN_OPTIMIZE=true
            shift
            ;;
        *)
            print_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# 运行Python测试
if $RUN_PYTHON_TESTS; then
    print_header "运行Python测试"

    # 运行基本功能测试
    print_header "运行基本功能测试"
    python3 tests/test_zero_copy.py > "$OUTPUT_DIR/test_zero_copy.log" 2>&1

    if [ $? -eq 0 ]; then
        print_success "基本功能测试通过"
    else
        print_error "基本功能测试失败"
        cat "$OUTPUT_DIR/test_zero_copy.log"
        exit 1
    fi

    # 运行物理正确性测试
    print_header "运行物理正确性测试"

    # 运行量子态物理正确性测试
    print_header "运行量子态物理正确性测试"
    python3 tests/test_quantum_physics.py > "$OUTPUT_DIR/test_quantum_physics.log" 2>&1

    if [ $? -eq 0 ]; then
        print_success "量子态物理正确性测试通过"
    else
        print_error "量子态物理正确性测试失败"
        cat "$OUTPUT_DIR/test_quantum_physics.log"
        exit 1
    fi

    # 运行分形结构物理正确性测试
    print_header "运行分形结构物理正确性测试"
    python3 tests/test_fractal_physics.py > "$OUTPUT_DIR/test_fractal_physics.log" 2>&1

    if [ $? -eq 0 ]; then
        print_success "分形结构物理正确性测试通过"
    else
        print_error "分形结构物理正确性测试失败"
        cat "$OUTPUT_DIR/test_fractal_physics.log"
        exit 1
    fi

    # 运行全息数据物理正确性测试
    print_header "运行全息数据物理正确性测试"
    python3 tests/test_holographic_physics.py > "$OUTPUT_DIR/test_holographic_physics.log" 2>&1

    if [ $? -eq 0 ]; then
        print_success "全息数据物理正确性测试通过"
    else
        print_error "全息数据物理正确性测试失败"
        cat "$OUTPUT_DIR/test_holographic_physics.log"
        exit 1
    fi
fi

# 运行Python基准测试
if $RUN_PYTHON_BENCHMARKS; then
    print_header "运行Python基准测试"

    # 运行零拷贝基准测试
    python3 tests/benchmark_zero_copy.py --all --output "$OUTPUT_DIR/benchmark_results.json" > "$OUTPUT_DIR/python_benchmarks.log" 2>&1

    if [ $? -eq 0 ]; then
        print_success "零拷贝基准测试完成"
    else
        print_warning "零拷贝基准测试失败，请查看日志文件了解详情"
        cat "$OUTPUT_DIR/python_benchmarks.log"
    fi

    # 可视化基准测试结果
    if [ -f "$OUTPUT_DIR/benchmark_results.json" ]; then
        mkdir -p "$OUTPUT_DIR/charts"
        python3 tests/visualize_benchmark_html.py --input "$OUTPUT_DIR/benchmark_results.json" --output "$OUTPUT_DIR/charts/benchmark_results.html" > "$OUTPUT_DIR/visualize_benchmarks.log" 2>&1

        if [ $? -eq 0 ]; then
            print_success "基准测试结果可视化完成"
        else
            print_warning "基准测试结果可视化失败，请查看日志文件了解详情"
            cat "$OUTPUT_DIR/visualize_benchmarks.log"
        fi
    fi
fi

# 生成测试报告
if $GENERATE_REPORT; then
    print_header "生成测试报告"

    # 创建报告目录
    REPORT_DIR="$OUTPUT_DIR/report"
    mkdir -p "$REPORT_DIR"

    # 复制测试文档
    cp tests/TEST_DOCUMENTATION.md "$REPORT_DIR/"

    # 复制性能图表
    if [ -d "$OUTPUT_DIR/charts" ] && [ "$(ls -A "$OUTPUT_DIR/charts")" ]; then
        mkdir -p "$REPORT_DIR/charts"
        cp -r "$OUTPUT_DIR/charts"/* "$REPORT_DIR/charts/"
    else
        mkdir -p "$REPORT_DIR/charts"
        print_warning "没有性能图表可供复制"
    fi

    # 生成HTML报告
    cat > "$REPORT_DIR/index.html" << EOF
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>零拷贝机制测试报告</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .success {
            color: #27ae60;
        }
        .warning {
            color: #f39c12;
        }
        .error {
            color: #e74c3c;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .chart {
            margin: 20px 0;
            text-align: center;
        }
        .chart img {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>零拷贝机制测试报告</h1>
        <p>生成时间：$(date)</p>

        <div class="section">
            <h2>测试摘要</h2>
            <p>本报告总结了零拷贝机制的测试结果，包括功能测试和性能基准测试。</p>

            <h3>测试环境</h3>
            <ul>
                <li>操作系统：$(uname -a)</li>
                <li>Python版本：$(python3 --version)</li>
                <li>NumPy版本：$(python3 -c "import numpy; print(numpy.__version__)")</li>
            </ul>
        </div>

        <div class="section">
            <h2>测试结果</h2>

            <h3>Python测试</h3>
            <p class="success">✓ 所有Python测试通过</p>

            <h3>性能基准测试</h3>
            <p>详细的性能基准测试结果请查看性能图表。</p>
        </div>

        <div class="section">
            <h2>性能图表</h2>

            <div class="chart">
                <h3>性能基准测试结果</h3>
                <iframe src="charts/benchmark_results.html" width="100%" height="800" frameborder="0"></iframe>
            </div>
        </div>

        <div class="section">
            <h2>详细文档</h2>
            <p>详细的测试文档请查看 <a href="TEST_DOCUMENTATION.md">TEST_DOCUMENTATION.md</a>。</p>
        </div>

        <div class="section">
            <h2>覆盖率报告</h2>
            <p>覆盖率报告请查看 <a href="coverage/coverage_report.html">覆盖率报告</a>。</p>
        </div>

        <div class="section">
            <h2>跨平台测试报告</h2>
            <p>跨平台测试报告请查看 <a href="cross_platform/cross_platform_report.html">跨平台测试报告</a>。</p>
        </div>

        <div class="section">
            <h2>自动化性能优化报告</h2>
            <p>自动化性能优化报告请查看 <a href="optimizations/optimization_report.html">自动化性能优化报告</a>。</p>
        </div>

        <div class="section">
            <h2>物理正确性测试报告</h2>
            <p>物理正确性测试报告包括：</p>
            <ul>
                <li><a href="physics/quantum_physics_report.html">量子态物理正确性测试报告</a></li>
                <li><a href="physics/fractal_physics_report.html">分形结构物理正确性测试报告</a></li>
                <li><a href="physics/holographic_physics_report.html">全息数据物理正确性测试报告</a></li>
            </ul>
        </div>
    </div>
</body>
</html>
EOF

    print_success "测试报告生成完成，请查看 $REPORT_DIR/index.html"
fi

# 运行覆盖率分析
if $RUN_COVERAGE; then
    print_header "运行覆盖率分析"

    # 安装覆盖率工具
    pip install coverage > /dev/null 2>&1

    # 运行覆盖率分析
    python3 tests/analyze_coverage.py --source . --tests tests/test_zero_copy.py tests/test_edge_cases.py tests/test_multithreading.py --output-dir "$OUTPUT_DIR/coverage" > "$OUTPUT_DIR/coverage.log" 2>&1

    if [ $? -eq 0 ]; then
        print_success "覆盖率分析完成"

        # 提取总体覆盖率
        TOTAL_COVERAGE=$(grep "总体覆盖率" "$OUTPUT_DIR/coverage.log" | awk '{print $2}')
        print_success "总体覆盖率: $TOTAL_COVERAGE"

        # 复制覆盖率报告到报告目录
        if $GENERATE_REPORT; then
            mkdir -p "$OUTPUT_DIR/report/coverage"
            cp -r "$OUTPUT_DIR/coverage"/* "$OUTPUT_DIR/report/coverage/"
            print_success "覆盖率报告已复制到报告目录"
        fi
    else
        print_warning "覆盖率分析失败，请查看日志文件了解详情"
        cat "$OUTPUT_DIR/coverage.log"
    fi
fi

# 运行跨平台测试
if $RUN_CROSS_PLATFORM; then
    print_header "运行跨平台测试"

    # 运行跨平台测试
    python3 tests/cross_platform_test.py --output-dir "$OUTPUT_DIR/cross_platform" > "$OUTPUT_DIR/cross_platform.log" 2>&1

    if [ $? -eq 0 ]; then
        print_success "跨平台测试完成"

        # 复制跨平台测试报告到报告目录
        if $GENERATE_REPORT; then
            mkdir -p "$OUTPUT_DIR/report/cross_platform"
            cp -r "$OUTPUT_DIR/cross_platform"/* "$OUTPUT_DIR/report/cross_platform/"
            print_success "跨平台测试报告已复制到报告目录"
        fi
    else
        print_warning "跨平台测试失败，请查看日志文件了解详情"
        cat "$OUTPUT_DIR/cross_platform.log"
    fi
fi

# 运行自动化性能优化
if $RUN_OPTIMIZE; then
    print_header "运行自动化性能优化"

    # 运行自动化性能优化
    python3 tests/auto_optimize.py --input "$OUTPUT_DIR/benchmark_results.json" --output-dir "$OUTPUT_DIR/optimizations" > "$OUTPUT_DIR/optimize.log" 2>&1

    if [ $? -eq 0 ]; then
        print_success "自动化性能优化完成"

        # 复制优化报告到报告目录
        if $GENERATE_REPORT; then
            mkdir -p "$OUTPUT_DIR/report/optimizations"
            cp -r "$OUTPUT_DIR/optimizations"/* "$OUTPUT_DIR/report/optimizations/"
            print_success "优化报告已复制到报告目录"
        fi
    else
        print_warning "自动化性能优化失败，请查看日志文件了解详情"
        cat "$OUTPUT_DIR/optimize.log"
    fi
fi

print_header "测试总结"
print_success "所有测试和基准测试已完成"
echo "详细的测试结果请查看 $OUTPUT_DIR 目录"

exit 0
