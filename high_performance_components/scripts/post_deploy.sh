#!/bin/bash

# 零拷贝机制部署后脚本
# 这个脚本在部署完成后执行，用于执行一些部署后的操作

set -e

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
function print_header() {
    echo -e "${BLUE}==== $1 ====${NC}"
}

function print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

function print_error() {
    echo -e "${RED}✗ $1${NC}"
}

function print_warning() {
    echo -e "${YELLOW}! $1${NC}"
}

# 当前目录
SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
cd "$SCRIPT_DIR"

# 获取环境
if [[ -f "../config/environment" ]]; then
    ENVIRONMENT=$(cat "../config/environment")
else
    ENVIRONMENT="development"
    print_warning "环境配置文件不存在，使用默认环境: $ENVIRONMENT"
fi

print_header "部署后操作 - $ENVIRONMENT"

# 设置权限
print_header "设置权限"
chmod 755 ../libarrow_integration_new.so
print_success "已设置权限"

# 创建符号链接
print_header "创建符号链接"
if [[ -f "/usr/local/lib/libarrow_integration_new.so" ]]; then
    rm -f /usr/local/lib/libarrow_integration_new.so
fi
ln -s "$(pwd)/../libarrow_integration_new.so" /usr/local/lib/libarrow_integration_new.so
print_success "已创建符号链接"

# 更新库缓存
print_header "更新库缓存"
ldconfig
print_success "已更新库缓存"

# 安装Python包
print_header "安装Python包"
if [[ -f "../requirements.txt" ]]; then
    pip install -r ../requirements.txt
    print_success "已安装Python包"
else
    print_warning "requirements.txt不存在，跳过安装Python包"
fi

# 安装systemd服务
print_header "安装systemd服务"
if [[ -f "../arrow_integration_new.service" ]]; then
    cp ../arrow_integration_new.service /etc/systemd/system/
    systemctl daemon-reload
    print_success "已安装systemd服务"
else
    print_warning "systemd服务文件不存在，跳过安装systemd服务"
fi

# 运行数据库迁移
print_header "运行数据库迁移"
if [[ -f "../migrations/migrate.py" ]]; then
    python ../migrations/migrate.py
    print_success "已运行数据库迁移"
else
    print_warning "数据库迁移脚本不存在，跳过运行数据库迁移"
fi

# 清理缓存
print_header "清理缓存"
if [[ -d "../cache" ]]; then
    rm -rf ../cache/*
    print_success "已清理缓存"
else
    print_warning "缓存目录不存在，跳过清理缓存"
fi

# 发送通知
print_header "发送通知"
if [[ -f "../config/$ENVIRONMENT.json" ]]; then
    NOTIFY_EMAIL=$(grep -o '"notify_email": *"[^"]*"' ../config/$ENVIRONMENT.json | cut -d'"' -f4)
    NOTIFY_SLACK=$(grep -o '"notify_slack": *"[^"]*"' ../config/$ENVIRONMENT.json | cut -d'"' -f4)
    
    if [[ -n "$NOTIFY_EMAIL" ]]; then
        echo "部署完成通知" | mail -s "零拷贝机制已部署到$ENVIRONMENT环境" "$NOTIFY_EMAIL"
        print_success "已发送邮件通知到: $NOTIFY_EMAIL"
    fi
    
    if [[ -n "$NOTIFY_SLACK" ]]; then
        curl -X POST -H 'Content-type: application/json' --data '{"text":"零拷贝机制已部署到'"$ENVIRONMENT"'环境"}' "$NOTIFY_SLACK"
        print_success "已发送Slack通知到: $NOTIFY_SLACK"
    fi
else
    print_warning "配置文件不存在，跳过发送通知"
fi

print_header "部署后操作完成"
print_success "零拷贝机制已成功部署到$ENVIRONMENT环境"

exit 0
