# 零拷贝机制部署配置 - 测试环境

# 部署目标
DEPLOY_HOST="staging.example.com"
DEPLOY_USER="deploy"
DEPLOY_PATH="/opt/arrow_integration_new"
DEPLOY_PORT="22"
DEPLOY_KEY="~/.ssh/id_rsa"

# 备份配置
DEPLOY_BACKUP_PATH="/opt/arrow_integration_new_backup"

# 服务配置
DEPLOY_RESTART_COMMAND="sudo systemctl restart arrow_integration_new"
DEPLOY_HEALTH_CHECK_URL="http://staging.example.com:8080/health"
DEPLOY_HEALTH_CHECK_TIMEOUT="30"
DEPLOY_HEALTH_CHECK_INTERVAL="5"
DEPLOY_HEALTH_CHECK_RETRIES="6"

# 通知配置
DEPLOY_NOTIFY_EMAIL="<EMAIL>"
DEPLOY_NOTIFY_SLACK="#deployments"

# 部署文件
DEPLOY_ARTIFACTS="target/release/libarrow_integration_new.so"
DEPLOY_CONFIG_FILES="config/staging.json"
DEPLOY_SCRIPTS="scripts/post_deploy.sh"
DEPLOY_SYSTEMD_SERVICE="arrow_integration_new.service"

# Docker配置
DEPLOY_DOCKER_IMAGE="arrow_integration_new"
DEPLOY_DOCKER_TAG="staging"
DEPLOY_DOCKER_REGISTRY="registry.example.com"
DEPLOY_DOCKER_USERNAME="docker_user"
DEPLOY_DOCKER_PASSWORD="docker_password"
DEPLOY_DOCKER_COMPOSE_FILE="docker-compose.staging.yml"

# Kubernetes配置
DEPLOY_KUBERNETES_NAMESPACE="staging"
DEPLOY_KUBERNETES_DEPLOYMENT="arrow-integration-new"
DEPLOY_KUBERNETES_CONTAINER="arrow-integration-new"
DEPLOY_KUBERNETES_CONTEXT="staging"
DEPLOY_KUBERNETES_MANIFEST="kubernetes/staging.yml"
