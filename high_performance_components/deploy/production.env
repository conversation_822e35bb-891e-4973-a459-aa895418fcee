# 零拷贝机制部署配置 - 生产环境

# 部署目标
DEPLOY_HOST="production.example.com"
DEPLOY_USER="deploy"
DEPLOY_PATH="/opt/arrow_integration_new"
DEPLOY_PORT="22"
DEPLOY_KEY="~/.ssh/id_rsa"

# 备份配置
DEPLOY_BACKUP_PATH="/opt/arrow_integration_new_backup"

# 服务配置
DEPLOY_RESTART_COMMAND="sudo systemctl restart arrow_integration_new"
DEPLOY_HEALTH_CHECK_URL="http://production.example.com:8080/health"
DEPLOY_HEALTH_CHECK_TIMEOUT="60"
DEPLOY_HEALTH_CHECK_INTERVAL="10"
DEPLOY_HEALTH_CHECK_RETRIES="12"

# 通知配置
DEPLOY_NOTIFY_EMAIL="<EMAIL>,<EMAIL>"
DEPLOY_NOTIFY_SLACK="#production-deployments"

# 部署文件
DEPLOY_ARTIFACTS="target/release/libarrow_integration_new.so"
DEPLOY_CONFIG_FILES="config/production.json"
DEPLOY_SCRIPTS="scripts/post_deploy.sh"
DEPLOY_SYSTEMD_SERVICE="arrow_integration_new.service"

# Docker配置
DEPLOY_DOCKER_IMAGE="arrow_integration_new"
DEPLOY_DOCKER_TAG="production"
DEPLOY_DOCKER_REGISTRY="registry.example.com"
DEPLOY_DOCKER_USERNAME="docker_user"
DEPLOY_DOCKER_PASSWORD="docker_password"
DEPLOY_DOCKER_COMPOSE_FILE="docker-compose.production.yml"

# Kubernetes配置
DEPLOY_KUBERNETES_NAMESPACE="production"
DEPLOY_KUBERNETES_DEPLOYMENT="arrow-integration-new"
DEPLOY_KUBERNETES_CONTAINER="arrow-integration-new"
DEPLOY_KUBERNETES_CONTEXT="production"
DEPLOY_KUBERNETES_MANIFEST="kubernetes/production.yml"
