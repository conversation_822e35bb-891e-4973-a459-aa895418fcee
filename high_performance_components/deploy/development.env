# 零拷贝机制部署配置 - 开发环境

# 部署目标
DEPLOY_HOST="localhost"
DEPLOY_USER="$USER"
DEPLOY_PATH="/opt/arrow_integration_new"
DEPLOY_PORT=""
DEPLOY_KEY=""

# 备份配置
DEPLOY_BACKUP_PATH="/opt/arrow_integration_new_backup"

# 服务配置
DEPLOY_RESTART_COMMAND="sudo systemctl restart arrow_integration_new"
DEPLOY_HEALTH_CHECK_URL="http://localhost:8080/health"
DEPLOY_HEALTH_CHECK_TIMEOUT="10"
DEPLOY_HEALTH_CHECK_INTERVAL="2"
DEPLOY_HEALTH_CHECK_RETRIES="3"

# 通知配置
DEPLOY_NOTIFY_EMAIL=""
DEPLOY_NOTIFY_SLACK=""

# 部署文件
DEPLOY_ARTIFACTS="target/release/libarrow_integration_new.so"
DEPLOY_CONFIG_FILES="config/development.json"
DEPLOY_SCRIPTS="scripts/post_deploy.sh"
DEPLOY_SYSTEMD_SERVICE="arrow_integration_new.service"

# Docker配置
DEPLOY_DOCKER_IMAGE="arrow_integration_new"
DEPLOY_DOCKER_TAG="development"
DEPLOY_DOCKER_REGISTRY=""
DEPLOY_DOCKER_USERNAME=""
DEPLOY_DOCKER_PASSWORD=""
DEPLOY_DOCKER_COMPOSE_FILE="docker-compose.development.yml"

# Kubernetes配置
DEPLOY_KUBERNETES_NAMESPACE="development"
DEPLOY_KUBERNETES_DEPLOYMENT="arrow-integration-new"
DEPLOY_KUBERNETES_CONTAINER="arrow-integration-new"
DEPLOY_KUBERNETES_CONTEXT="development"
DEPLOY_KUBERNETES_MANIFEST="kubernetes/development.yml"
