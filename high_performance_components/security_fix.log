2025-05-16 22:41:41,405 - security_fix - INFO - 开始修复安全问题
2025-05-16 22:41:41,405 - security_fix - INFO - 修复文件：./security/security_audit.py
2025-05-16 22:41:41,406 - security_fix - INFO - 已备份文件：./security/security_audit.py.bak
2025-05-16 22:41:41,406 - security_fix - INFO - 已修复问题：不安全的反序列化 在 ./security/security_audit.py:136
2025-05-16 22:41:41,407 - security_fix - INFO - 已保存修改后的文件：./security/security_audit.py
2025-05-16 22:41:41,407 - security_fix - INFO - 修复文件：./security/vulnerability_scanner.py
2025-05-16 22:41:41,408 - security_fix - INFO - 已备份文件：./security/vulnerability_scanner.py.bak
2025-05-16 22:41:41,408 - security_fix - INFO - 已修复问题：不安全的反序列化 在 ./security/vulnerability_scanner.py:418
2025-05-16 22:41:41,408 - security_fix - INFO - 已保存修改后的文件：./security/vulnerability_scanner.py
2025-05-16 22:41:41,408 - security_fix - INFO - 修复文件：./src/zero_copy/memory_mapping.rs
2025-05-16 22:41:41,409 - security_fix - INFO - 已备份文件：./src/zero_copy/memory_mapping.rs.bak
2025-05-16 22:41:41,409 - security_fix - INFO - 已修复问题：不安全的内存操作 在 ./src/zero_copy/memory_mapping.rs:230
2025-05-16 22:41:41,409 - security_fix - INFO - 已修复问题：不安全的内存操作 在 ./src/zero_copy/memory_mapping.rs:146
2025-05-16 22:41:41,409 - security_fix - INFO - 已修复问题：不安全的内存操作 在 ./src/zero_copy/memory_mapping.rs:67
2025-05-16 22:41:41,409 - security_fix - INFO - 已修复问题：不安全的内存操作 在 ./src/zero_copy/memory_mapping.rs:45
2025-05-16 22:41:41,410 - security_fix - INFO - 已保存修改后的文件：./src/zero_copy/memory_mapping.rs
2025-05-16 22:41:41,410 - security_fix - INFO - 修复文件：./src/zero_copy/shared_memory.rs
2025-05-16 22:41:41,411 - security_fix - INFO - 已备份文件：./src/zero_copy/shared_memory.rs.bak
2025-05-16 22:41:41,411 - security_fix - INFO - 已修复问题：不安全的内存操作 在 ./src/zero_copy/shared_memory.rs:75
2025-05-16 22:41:41,411 - security_fix - INFO - 已修复问题：不安全的内存操作 在 ./src/zero_copy/shared_memory.rs:51
2025-05-16 22:41:41,411 - security_fix - INFO - 已保存修改后的文件：./src/zero_copy/shared_memory.rs
2025-05-16 22:41:41,411 - security_fix - INFO - 修复文件：./src/internal/utils.rs
2025-05-16 22:41:41,412 - security_fix - INFO - 已备份文件：./src/internal/utils.rs.bak
2025-05-16 22:41:41,412 - security_fix - WARNING - 无法修复问题：不安全的内存操作 在 ./src/internal/utils.rs:54
2025-05-16 22:41:41,412 - security_fix - WARNING - 无法修复问题：不安全的内存操作 在 ./src/internal/utils.rs:52
2025-05-16 22:41:41,413 - security_fix - WARNING - 无法修复问题：不安全的内存操作 在 ./src/internal/utils.rs:41
2025-05-16 22:41:41,413 - security_fix - WARNING - 无法修复问题：不安全的内存操作 在 ./src/internal/utils.rs:39
2025-05-16 22:41:41,413 - security_fix - INFO - 修复文件：./tests/test_zero_copy.rs
2025-05-16 22:41:41,413 - security_fix - INFO - 已备份文件：./tests/test_zero_copy.rs.bak
2025-05-16 22:41:41,413 - security_fix - INFO - 已修复问题：不安全的内存操作 在 ./tests/test_zero_copy.rs:75
2025-05-16 22:41:41,413 - security_fix - INFO - 已修复问题：不安全的内存操作 在 ./tests/test_zero_copy.rs:51
2025-05-16 22:41:41,414 - security_fix - INFO - 已保存修改后的文件：./tests/test_zero_copy.rs
2025-05-16 22:41:41,414 - security_fix - INFO - 安全问题修复完成，已修复10个问题，跳过4个问题
2025-05-16 22:41:41,414 - security_fix - INFO - 生成安全修复报告
2025-05-16 22:41:41,415 - security_fix - INFO - 安全修复报告已生成：security_reports/security_fix_report.json
