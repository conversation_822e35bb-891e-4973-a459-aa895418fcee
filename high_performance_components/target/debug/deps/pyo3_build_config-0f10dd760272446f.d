/home/<USER>/CascadeProjects/TFF/rust/arrow_integration_new/target/debug/deps/libpyo3_build_config-0f10dd760272446f.rmeta: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/pyo3-build-config-0.20.3/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/pyo3-build-config-0.20.3/src/errors.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/pyo3-build-config-0.20.3/src/impl_.rs /home/<USER>/CascadeProjects/TFF/rust/arrow_integration_new/target/debug/build/pyo3-build-config-89e0b7778d925b11/out/pyo3-build-config-file.txt /home/<USER>/CascadeProjects/TFF/rust/arrow_integration_new/target/debug/build/pyo3-build-config-89e0b7778d925b11/out/pyo3-build-config.txt

/home/<USER>/CascadeProjects/TFF/rust/arrow_integration_new/target/debug/deps/libpyo3_build_config-0f10dd760272446f.rlib: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/pyo3-build-config-0.20.3/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/pyo3-build-config-0.20.3/src/errors.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/pyo3-build-config-0.20.3/src/impl_.rs /home/<USER>/CascadeProjects/TFF/rust/arrow_integration_new/target/debug/build/pyo3-build-config-89e0b7778d925b11/out/pyo3-build-config-file.txt /home/<USER>/CascadeProjects/TFF/rust/arrow_integration_new/target/debug/build/pyo3-build-config-89e0b7778d925b11/out/pyo3-build-config.txt

/home/<USER>/CascadeProjects/TFF/rust/arrow_integration_new/target/debug/deps/pyo3_build_config-0f10dd760272446f.d: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/pyo3-build-config-0.20.3/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/pyo3-build-config-0.20.3/src/errors.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/pyo3-build-config-0.20.3/src/impl_.rs /home/<USER>/CascadeProjects/TFF/rust/arrow_integration_new/target/debug/build/pyo3-build-config-89e0b7778d925b11/out/pyo3-build-config-file.txt /home/<USER>/CascadeProjects/TFF/rust/arrow_integration_new/target/debug/build/pyo3-build-config-89e0b7778d925b11/out/pyo3-build-config.txt

/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/pyo3-build-config-0.20.3/src/lib.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/pyo3-build-config-0.20.3/src/errors.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/pyo3-build-config-0.20.3/src/impl_.rs:
/home/<USER>/CascadeProjects/TFF/rust/arrow_integration_new/target/debug/build/pyo3-build-config-89e0b7778d925b11/out/pyo3-build-config-file.txt:
/home/<USER>/CascadeProjects/TFF/rust/arrow_integration_new/target/debug/build/pyo3-build-config-89e0b7778d925b11/out/pyo3-build-config.txt:

# env-dep:CARGO_PKG_VERSION=0.20.3
# env-dep:OUT_DIR=/home/<USER>/CascadeProjects/TFF/rust/arrow_integration_new/target/debug/build/pyo3-build-config-89e0b7778d925b11/out
