//! Benchmarks for special data types

#![feature(test)]

extern crate test;

use std::sync::Arc;
use std::path::Path;
use std::thread;
use std::time::{Duration, Instant};
use std::collections::HashMap;

use tempfile::tempdir;
use rand::{Rng, thread_rng};
use test::Bencher;
use num_complex::Complex64;
use ndarray::{Array1, Array2};

use arrow_integration::zero_copy::shared_memory::{
    init_shared_memory_manager, global_shared_memory_manager
};
use arrow_integration::zero_copy::memory_mapping::{
    MappedBuffer, MappedFileWriter, MappedFileReader
};
use arrow_integration::types::quantum::{QuantumStateArray, QuantumStateType};
use arrow_integration::types::fractal::{
    FractalStructureArray, FractalStructureType, FractalNode, FractalConnection
};
use arrow_integration::types::holographic::{
    HolographicDataArray, HolographicDataType
};
use arrow_integration::types::complex::ComplexArray;

// 辅助函数：创建随机量子态
fn create_random_quantum_state(n_qubits: usize) -> QuantumStateArray {
    // 计算维度
    let dim = 1 << n_qubits;
    
    // 创建随机复数系数
    let mut rng = thread_rng();
    let mut state = Vec::with_capacity(dim);
    
    for _ in 0..dim {
        let re = rng.gen_range(-1.0..1.0);
        let im = rng.gen_range(-1.0..1.0);
        state.push(Complex64::new(re, im));
    }
    
    // 归一化
    let norm_squared: f64 = state.iter().map(|c| c.norm_squared()).sum();
    let norm = norm_squared.sqrt();
    
    for c in &mut state {
        *c /= Complex64::new(norm, 0.0);
    }
    
    // 创建维度向量
    let dimensions = vec![2; n_qubits];
    
    // 创建量子态数组
    QuantumStateArray::new(&state, dimensions)
}

// 辅助函数：创建谢尔宾斯基三角形分形
fn create_sierpinski_triangle(depth: usize) -> FractalStructureArray {
    // 基本三角形顶点
    let mut nodes = vec![
        FractalNode {
            id: 0,
            position: vec![0.0, 0.0],
            properties: HashMap::new(),
        },
        FractalNode {
            id: 1,
            position: vec![1.0, 0.0],
            properties: HashMap::new(),
        },
        FractalNode {
            id: 2,
            position: vec![0.5, 0.866],
            properties: HashMap::new(),
        },
    ];
    
    // 连接基本三角形
    let mut connections = vec![
        FractalConnection {
            source: 0,
            target: 1,
            weight: 1.0,
            properties: HashMap::new(),
        },
        FractalConnection {
            source: 1,
            target: 2,
            weight: 1.0,
            properties: HashMap::new(),
        },
        FractalConnection {
            source: 2,
            target: 0,
            weight: 1.0,
            properties: HashMap::new(),
        },
    ];
    
    // 递归生成更深层次的三角形
    let mut next_id = 3;
    
    for d in 1..depth {
        let scale_factor = 1.0 / (2.0_f64.powi(d as i32));
        let nodes_count = nodes.len();
        
        let mut new_nodes = Vec::new();
        let mut new_connections = Vec::new();
        
        for i in (0..nodes_count).step_by(3) {
            if i + 2 < nodes_count {
                let p0 = &nodes[i].position;
                let p1 = &nodes[i+1].position;
                let p2 = &nodes[i+2].position;
                
                // 计算新三角形的顶点
                let new_p0 = vec![
                    p0[0] + (p1[0] - p0[0]) * 0.5,
                    p0[1] + (p1[1] - p0[1]) * 0.5,
                ];
                
                let new_p1 = vec![
                    p1[0] + (p2[0] - p1[0]) * 0.5,
                    p1[1] + (p2[1] - p1[1]) * 0.5,
                ];
                
                let new_p2 = vec![
                    p2[0] + (p0[0] - p2[0]) * 0.5,
                    p2[1] + (p0[1] - p2[1]) * 0.5,
                ];
                
                // 添加新节点
                new_nodes.push(FractalNode {
                    id: next_id,
                    position: new_p0,
                    properties: HashMap::new(),
                });
                
                new_nodes.push(FractalNode {
                    id: next_id + 1,
                    position: new_p1,
                    properties: HashMap::new(),
                });
                
                new_nodes.push(FractalNode {
                    id: next_id + 2,
                    position: new_p2,
                    properties: HashMap::new(),
                });
                
                // 添加新连接
                new_connections.push(FractalConnection {
                    source: next_id,
                    target: next_id + 1,
                    weight: scale_factor,
                    properties: HashMap::new(),
                });
                
                new_connections.push(FractalConnection {
                    source: next_id + 1,
                    target: next_id + 2,
                    weight: scale_factor,
                    properties: HashMap::new(),
                });
                
                new_connections.push(FractalConnection {
                    source: next_id + 2,
                    target: next_id,
                    weight: scale_factor,
                    properties: HashMap::new(),
                });
                
                next_id += 3;
            }
        }
        
        nodes.extend(new_nodes);
        connections.extend(new_connections);
    }
    
    // 创建分形结构数组
    let metadata = HashMap::new();
    
    FractalStructureArray::new(
        nodes,
        connections,
        metadata,
        2, // 维度
        depth, // 深度
        3, // 分支因子
    )
}

// 辅助函数：创建傅里叶变换矩阵
fn create_fourier_matrix(n: usize) -> Array2<Complex64> {
    let mut matrix = Array2::zeros((n, n));
    let scale = 1.0 / (n as f64).sqrt();
    
    for i in 0..n {
        for j in 0..n {
            let angle = -2.0 * std::f64::consts::PI * (i as f64) * (j as f64) / (n as f64);
            matrix[[i, j]] = Complex64::new(angle.cos(), angle.sin()) * scale;
        }
    }
    
    matrix
}

// 辅助函数：创建全息数据
fn create_holographic_data(n: usize) -> HolographicDataArray {
    // 创建原始数据
    let mut rng = thread_rng();
    let mut original_data = Vec::with_capacity(n);
    
    for _ in 0..n {
        let re = rng.gen_range(-1.0..1.0);
        let im = rng.gen_range(-1.0..1.0);
        original_data.push(Complex64::new(re, im));
    }
    
    // 创建编码矩阵
    let encoding_matrix = create_fourier_matrix(n);
    
    // 创建全息数据数组
    let encoding_type = "fourier".to_string();
    let original_dimensions = vec![n];
    let holographic_dimensions = vec![n];
    let metadata = HashMap::new();
    
    HolographicDataArray::new(
        &original_data,
        encoding_matrix,
        encoding_type,
        original_dimensions,
        holographic_dimensions,
        metadata,
    )
}

// 量子态基准测试
#[bench]
fn bench_quantum_state_serialize_1_qubit(b: &mut Bencher) {
    let quantum_state = create_random_quantum_state(1);
    
    b.iter(|| {
        let serialized = bincode::serialize(&quantum_state).unwrap();
        test::black_box(serialized);
    });
}

#[bench]
fn bench_quantum_state_deserialize_1_qubit(b: &mut Bencher) {
    let quantum_state = create_random_quantum_state(1);
    let serialized = bincode::serialize(&quantum_state).unwrap();
    
    b.iter(|| {
        let deserialized: QuantumStateArray = bincode::deserialize(&serialized).unwrap();
        test::black_box(deserialized);
    });
}

#[bench]
fn bench_quantum_state_serialize_5_qubits(b: &mut Bencher) {
    let quantum_state = create_random_quantum_state(5);
    
    b.iter(|| {
        let serialized = bincode::serialize(&quantum_state).unwrap();
        test::black_box(serialized);
    });
}

#[bench]
fn bench_quantum_state_deserialize_5_qubits(b: &mut Bencher) {
    let quantum_state = create_random_quantum_state(5);
    let serialized = bincode::serialize(&quantum_state).unwrap();
    
    b.iter(|| {
        let deserialized: QuantumStateArray = bincode::deserialize(&serialized).unwrap();
        test::black_box(deserialized);
    });
}

#[bench]
fn bench_quantum_state_serialize_10_qubits(b: &mut Bencher) {
    let quantum_state = create_random_quantum_state(10);
    
    b.iter(|| {
        let serialized = bincode::serialize(&quantum_state).unwrap();
        test::black_box(serialized);
    });
}

#[bench]
fn bench_quantum_state_deserialize_10_qubits(b: &mut Bencher) {
    let quantum_state = create_random_quantum_state(10);
    let serialized = bincode::serialize(&quantum_state).unwrap();
    
    b.iter(|| {
        let deserialized: QuantumStateArray = bincode::deserialize(&serialized).unwrap();
        test::black_box(deserialized);
    });
}

// 分形结构基准测试
#[bench]
fn bench_fractal_structure_serialize_depth_3(b: &mut Bencher) {
    let fractal = create_sierpinski_triangle(3);
    
    b.iter(|| {
        let serialized = bincode::serialize(&fractal).unwrap();
        test::black_box(serialized);
    });
}

#[bench]
fn bench_fractal_structure_deserialize_depth_3(b: &mut Bencher) {
    let fractal = create_sierpinski_triangle(3);
    let serialized = bincode::serialize(&fractal).unwrap();
    
    b.iter(|| {
        let deserialized: FractalStructureArray = bincode::deserialize(&serialized).unwrap();
        test::black_box(deserialized);
    });
}

#[bench]
fn bench_fractal_structure_serialize_depth_5(b: &mut Bencher) {
    let fractal = create_sierpinski_triangle(5);
    
    b.iter(|| {
        let serialized = bincode::serialize(&fractal).unwrap();
        test::black_box(serialized);
    });
}

#[bench]
fn bench_fractal_structure_deserialize_depth_5(b: &mut Bencher) {
    let fractal = create_sierpinski_triangle(5);
    let serialized = bincode::serialize(&fractal).unwrap();
    
    b.iter(|| {
        let deserialized: FractalStructureArray = bincode::deserialize(&serialized).unwrap();
        test::black_box(deserialized);
    });
}

// 全息数据基准测试
#[bench]
fn bench_holographic_data_serialize_size_64(b: &mut Bencher) {
    let holographic_data = create_holographic_data(64);
    
    b.iter(|| {
        let serialized = bincode::serialize(&holographic_data).unwrap();
        test::black_box(serialized);
    });
}

#[bench]
fn bench_holographic_data_deserialize_size_64(b: &mut Bencher) {
    let holographic_data = create_holographic_data(64);
    let serialized = bincode::serialize(&holographic_data).unwrap();
    
    b.iter(|| {
        let deserialized: HolographicDataArray = bincode::deserialize(&serialized).unwrap();
        test::black_box(deserialized);
    });
}

#[bench]
fn bench_holographic_data_serialize_size_256(b: &mut Bencher) {
    let holographic_data = create_holographic_data(256);
    
    b.iter(|| {
        let serialized = bincode::serialize(&holographic_data).unwrap();
        test::black_box(serialized);
    });
}

#[bench]
fn bench_holographic_data_deserialize_size_256(b: &mut Bencher) {
    let holographic_data = create_holographic_data(256);
    let serialized = bincode::serialize(&holographic_data).unwrap();
    
    b.iter(|| {
        let deserialized: HolographicDataArray = bincode::deserialize(&serialized).unwrap();
        test::black_box(deserialized);
    });
}

// 组合基准测试
#[bench]
fn bench_combined_special_types(b: &mut Bencher) {
    // 创建临时目录
    let dir = tempdir().unwrap();
    let base_dir = dir.path();
    
    // 初始化共享内存管理器
    init_shared_memory_manager(base_dir).unwrap();
    let manager = global_shared_memory_manager().unwrap();
    
    // 创建特殊数据类型
    let quantum_state = create_random_quantum_state(5);
    let fractal = create_sierpinski_triangle(3);
    let holographic_data = create_holographic_data(64);
    
    // 序列化
    let quantum_serialized = bincode::serialize(&quantum_state).unwrap();
    let fractal_serialized = bincode::serialize(&fractal).unwrap();
    let holographic_serialized = bincode::serialize(&holographic_data).unwrap();
    
    // 创建共享内存区域
    let region_name = "combined_test";
    let region_size = quantum_serialized.len() + fractal_serialized.len() + holographic_serialized.len() + 100;
    let region = manager.create_region(region_name, region_size).unwrap();
    
    b.iter(|| {
        // 写入量子态
        region.write(0, &quantum_serialized).unwrap();
        
        // 写入分形结构
        region.write(quantum_serialized.len(), &fractal_serialized).unwrap();
        
        // 写入全息数据
        region.write(quantum_serialized.len() + fractal_serialized.len(), &holographic_serialized).unwrap();
        
        // 读取量子态
        let quantum_read = region.read(0, quantum_serialized.len()).unwrap();
        
        // 读取分形结构
        let fractal_read = region.read(quantum_serialized.len(), fractal_serialized.len()).unwrap();
        
        // 读取全息数据
        let holographic_read = region.read(
            quantum_serialized.len() + fractal_serialized.len(),
            holographic_serialized.len()
        ).unwrap();
        
        // 反序列化
        let quantum_deserialized: QuantumStateArray = bincode::deserialize(&quantum_read).unwrap();
        let fractal_deserialized: FractalStructureArray = bincode::deserialize(&fractal_read).unwrap();
        let holographic_deserialized: HolographicDataArray = bincode::deserialize(&holographic_read).unwrap();
        
        test::black_box((quantum_deserialized, fractal_deserialized, holographic_deserialized));
    });
    
    // 清理
    manager.remove_region(region_name).unwrap();
}
