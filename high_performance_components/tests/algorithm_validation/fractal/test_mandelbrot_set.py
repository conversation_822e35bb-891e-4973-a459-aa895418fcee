#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
曼德勃罗集算法验证测试

这个脚本验证曼德勃罗集算法的正确性，包括：
1. 与解析解的比较
2. 收敛性测试
3. 稳定性测试
"""

import os
import sys
import unittest
import numpy as np
from pathlib import Path

# 添加父目录到路径
sys.path.append(str(Path(__file__).parent.parent))
from test_framework import AlgorithmValidationTestCase, AlgorithmValidator

class MandelbrotSet:
    """曼德勃罗集实现"""

    @staticmethod
    def is_in_mandelbrot(c, max_iter=100, escape_radius=2.0):
        """判断复数c是否在曼德勃罗集中

        Args:
            c: 复数
            max_iter: 最大迭代次数
            escape_radius: 逃逸半径

        Returns:
            如果c在曼德勃罗集中，返回True；否则返回False
        """
        z = 0
        for i in range(max_iter):
            z = z**2 + c
            if abs(z) > escape_radius:
                return False
        return True

    @staticmethod
    def mandelbrot_iterations(c, max_iter=100, escape_radius=2.0):
        """计算复数c的曼德勃罗迭代次数

        Args:
            c: 复数
            max_iter: 最大迭代次数
            escape_radius: 逃逸半径

        Returns:
            迭代次数，如果c在曼德勃罗集中，返回max_iter
        """
        z = complex(0, 0)
        for i in range(max_iter):
            z = z**2 + c
            if abs(z) > escape_radius:
                return i
        return max_iter

    @staticmethod
    def mandelbrot_set(xmin, xmax, ymin, ymax, width, height, max_iter=100, escape_radius=2.0):
        """计算曼德勃罗集

        Args:
            xmin: x轴最小值
            xmax: x轴最大值
            ymin: y轴最小值
            ymax: y轴最大值
            width: 宽度
            height: 高度
            max_iter: 最大迭代次数
            escape_radius: 逃逸半径

        Returns:
            迭代次数矩阵
        """
        x = np.linspace(xmin, xmax, width)
        y = np.linspace(ymin, ymax, height)
        iterations = np.zeros((height, width), dtype=np.int32)

        for i in range(height):
            for j in range(width):
                c = complex(x[j], y[i])
                iterations[i, j] = MandelbrotSet.mandelbrot_iterations(c, max_iter, escape_radius)

        return iterations

    @staticmethod
    def mandelbrot_set_vectorized(xmin, xmax, ymin, ymax, width, height, max_iter=100, escape_radius=2.0):
        """向量化实现的曼德勃罗集

        Args:
            xmin: x轴最小值
            xmax: x轴最大值
            ymin: y轴最小值
            ymax: y轴最大值
            width: 宽度
            height: 高度
            max_iter: 最大迭代次数
            escape_radius: 逃逸半径

        Returns:
            迭代次数矩阵
        """
        x = np.linspace(xmin, xmax, width)
        y = np.linspace(ymin, ymax, height)
        c = x[np.newaxis, :] + 1j * y[:, np.newaxis]

        z = np.zeros_like(c)
        iterations = np.zeros_like(c, dtype=np.int32)
        mask = np.ones_like(c, dtype=bool)

        for i in range(max_iter):
            z[mask] = z[mask]**2 + c[mask]
            escaped = np.abs(z) > escape_radius
            iterations[mask & escaped] = i
            mask &= ~escaped

        # 确保未逃逸的点设置为max_iter
        iterations[mask] = max_iter

        return iterations

    @staticmethod
    def mandelbrot_set_iterative(xmin, xmax, ymin, ymax, width, height, iterations, escape_radius=2.0):
        """迭代实现的曼德勃罗集

        Args:
            xmin: x轴最小值
            xmax: x轴最大值
            ymin: y轴最小值
            ymax: y轴最大值
            width: 宽度
            height: 高度
            iterations: 迭代次数
            escape_radius: 逃逸半径

        Returns:
            迭代次数矩阵
        """
        x = np.linspace(xmin, xmax, width)
        y = np.linspace(ymin, ymax, height)
        c = x[np.newaxis, :] + 1j * y[:, np.newaxis]

        z = np.zeros_like(c)
        result = np.zeros_like(c, dtype=np.int32)
        mask = np.ones_like(c, dtype=bool)

        for i in range(iterations):
            z[mask] = z[mask]**2 + c[mask]
            escaped = np.abs(z) > escape_radius
            result[mask & escaped] = i
            mask &= ~escaped

        return result

    @staticmethod
    def mandelbrot_analytical(c, max_iter=100, escape_radius=2.0):
        """解析解实现的曼德勃罗迭代次数

        Args:
            c: 复数
            max_iter: 最大迭代次数
            escape_radius: 逃逸半径

        Returns:
            迭代次数，如果c在曼德勃罗集中，返回max_iter
        """
        # 对于|c| > 2的点，可以证明它们不在曼德勃罗集中
        if abs(c) > 2:
            return 0

        # 对于|c - (-1, 0)| < 1/4的点，可以证明它们在主心形区域内
        if abs(c - complex(-1, 0)) < 0.25:
            return max_iter

        # 对于|c - (0.25, 0)| < 0.5的点，可以证明它们在周围的圆形区域内
        if abs(c - complex(0.25, 0)) < 0.5:
            return max_iter

        # 对于其他点，使用标准迭代
        z = complex(0, 0)
        for i in range(max_iter):
            z = z**2 + c
            if abs(z) > escape_radius:
                return i
        return max_iter

class TestMandelbrotSet(AlgorithmValidationTestCase):
    """曼德勃罗集测试"""

    def test_mandelbrot_analytical(self):
        """测试曼德勃罗集与解析解的一致性"""
        # 创建测试输入
        inputs = [
            complex(-2, 0),      # 不在曼德勃罗集中
            complex(-1, 0),      # 在主心形区域内
            complex(0, 0),       # 在曼德勃罗集中
            complex(0.25, 0),    # 在周围的圆形区域内
            complex(0.5, 0),     # 不在曼德勃罗集中
            complex(0, 1),       # 不在曼德勃罗集中
            complex(-0.75, 0.1), # 在曼德勃罗集中
            complex(-0.1, 0.8)   # 不在曼德勃罗集中
        ]

        # 验证曼德勃罗迭代次数与解析解的一致性
        validator = self.validate_algorithm(
            algorithm_name="Mandelbrot Set",
            algorithm_func=lambda c: MandelbrotSet.mandelbrot_iterations(c, max_iter=100),
            analytical_func=lambda c: MandelbrotSet.mandelbrot_analytical(c, max_iter=100),
            inputs=inputs,
            rtol=0,  # 整数比较，不需要相对误差
            atol=0,  # 整数比较，不需要绝对误差
            plot=False
        )

    def test_mandelbrot_convergence(self):
        """测试曼德勃罗集的收敛性"""
        # 创建测试输入
        inputs = [
            complex(-0.75, 0.1),  # 在曼德勃罗集中
            complex(-0.1, 0.8),   # 不在曼德勃罗集中
            complex(-1.25, 0.2),  # 在曼德勃罗集边界附近
            complex(0.25, 0.5)    # 不在曼德勃罗集中
        ]

        # 迭代次数
        iterations = [10, 20, 50, 100, 200]

        # 验证曼德勃罗集的收敛性
        validator = self.validate_algorithm(
            algorithm_name="Mandelbrot Set Convergence",
            algorithm_func=lambda c, iter_count: MandelbrotSet.mandelbrot_iterations(c, max_iter=iter_count),
            inputs=inputs,
            iterations=iterations,
            rtol=0,  # 整数比较，不需要相对误差
            atol=0,  # 整数比较，不需要绝对误差
            plot=True
        )

    def test_mandelbrot_stability(self):
        """测试曼德勃罗集的稳定性"""
        # 创建基准输入
        base_input = complex(-0.75, 0.1)

        # 扰动大小
        perturbations = [1e-10, 1e-8, 1e-6, 1e-4, 1e-2]

        # 验证曼德勃罗集的稳定性
        validator = self.validate_algorithm(
            algorithm_name="Mandelbrot Set Stability",
            algorithm_func=lambda c: MandelbrotSet.mandelbrot_iterations(c, max_iter=100),
            base_input=base_input,
            perturbations=perturbations,
            rtol=0,  # 整数比较，不需要相对误差
            atol=0,  # 整数比较，不需要绝对误差
            plot=True
        )

    def test_vectorized_implementation(self):
        """测试向量化实现与标准实现的一致性"""
        # 创建测试输入
        inputs = [
            (-2, 1, -1.5, 1.5, 10, 10),  # 小尺寸
            (-2, 1, -1.5, 1.5, 20, 20),  # 中等尺寸
            (-2, 1, -1.5, 1.5, 30, 30)   # 大尺寸
        ]

        # 验证向量化实现与标准实现的一致性
        for params in inputs:
            # 标准实现
            standard = MandelbrotSet.mandelbrot_set(*params, max_iter=100)

            # 向量化实现
            vectorized = MandelbrotSet.mandelbrot_set_vectorized(*params, max_iter=100)

            # 验证结果一致
            np.testing.assert_array_equal(standard, vectorized)

if __name__ == "__main__":
    unittest.main()
