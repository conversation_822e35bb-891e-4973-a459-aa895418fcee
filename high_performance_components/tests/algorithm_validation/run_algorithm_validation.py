#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
算法验证测试运行脚本

这个脚本运行所有算法验证测试，并生成报告。
"""

import os
import sys
import unittest
import argparse
import logging
import json
import datetime
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('algorithm_validation.log')
    ]
)

logger = logging.getLogger('algorithm_validation')

def run_tests(test_modules=None, output_dir="algorithm_validation_results"):
    """运行算法验证测试
    
    Args:
        test_modules: 测试模块列表，如果为None则运行所有测试
        output_dir: 输出目录
        
    Returns:
        测试结果
    """
    logger.info("运行算法验证测试")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 如果没有指定测试模块，则查找所有测试模块
    if test_modules is None:
        test_modules = []
        
        # 查找量子算法测试
        quantum_dir = Path(__file__).parent / "quantum"
        if quantum_dir.exists():
            for file in quantum_dir.glob("test_*.py"):
                module_name = f"quantum.{file.stem}"
                test_modules.append(module_name)
        
        # 查找分形算法测试
        fractal_dir = Path(__file__).parent / "fractal"
        if fractal_dir.exists():
            for file in fractal_dir.glob("test_*.py"):
                module_name = f"fractal.{file.stem}"
                test_modules.append(module_name)
        
        # 查找全息算法测试
        holographic_dir = Path(__file__).parent / "holographic"
        if holographic_dir.exists():
            for file in holographic_dir.glob("test_*.py"):
                module_name = f"holographic.{file.stem}"
                test_modules.append(module_name)
    
    logger.info(f"找到测试模块：{test_modules}")
    
    # 运行测试
    results = {}
    for module_name in test_modules:
        logger.info(f"运行测试模块：{module_name}")
        
        try:
            # 导入测试模块
            module = __import__(module_name, fromlist=["*"])
            
            # 查找测试类
            test_classes = []
            for name in dir(module):
                obj = getattr(module, name)
                if isinstance(obj, type) and issubclass(obj, unittest.TestCase) and obj.__module__ == module.__name__:
                    test_classes.append(obj)
            
            if not test_classes:
                logger.warning(f"测试模块 {module_name} 中没有找到测试类")
                continue
            
            # 运行测试
            for test_class in test_classes:
                logger.info(f"运行测试类：{test_class.__name__}")
                
                # 创建测试套件
                suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
                
                # 运行测试
                runner = unittest.TextTestRunner(verbosity=2)
                result = runner.run(suite)
                
                # 记录结果
                results[f"{module_name}.{test_class.__name__}"] = {
                    "tests": result.testsRun,
                    "failures": len(result.failures),
                    "errors": len(result.errors),
                    "skipped": len(result.skipped),
                    "passed": result.testsRun - len(result.failures) - len(result.errors) - len(result.skipped)
                }
        except Exception as e:
            logger.error(f"运行测试模块 {module_name} 时出错：{e}")
            results[module_name] = {
                "error": str(e)
            }
    
    # 生成报告
    generate_report(results, output_dir)
    
    return results

def generate_report(results, output_dir):
    """生成测试报告
    
    Args:
        results: 测试结果
        output_dir: 输出目录
    """
    logger.info("生成测试报告")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存JSON报告
    json_report_file = Path(output_dir) / "algorithm_validation_report.json"
    with open(json_report_file, "w") as f:
        json.dump(results, f, indent=2)
    
    # 生成HTML报告
    html_report_file = Path(output_dir) / "algorithm_validation_report.html"
    
    with open(html_report_file, "w") as f:
        f.write(f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>算法验证测试报告</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }}
        h1, h2, h3 {{
            color: #2c3e50;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
        }}
        .section {{
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }}
        .success {{
            color: #27ae60;
        }}
        .warning {{
            color: #f39c12;
        }}
        .error {{
            color: #e74c3c;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }}
        th, td {{
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        th {{
            background-color: #f2f2f2;
        }}
        .result-badge {{
            display: inline-block;
            padding: 3px 6px;
            border-radius: 3px;
            color: white;
            font-weight: bold;
        }}
        .result-badge.pass {{
            background-color: #27ae60;
        }}
        .result-badge.fail {{
            background-color: #e74c3c;
        }}
        .summary {{
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }}
        .summary-item {{
            flex: 1;
            padding: 10px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-right: 10px;
            text-align: center;
        }}
        .summary-item:last-child {{
            margin-right: 0;
        }}
        .summary-number {{
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }}
        .summary-label {{
            font-size: 0.9em;
            color: #7f8c8d;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>算法验证测试报告</h1>
        <p>生成时间：{datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
        
        <div class="section">
            <h2>测试摘要</h2>
            <div class="summary">
        """)
        
        # 计算总数
        total_tests = sum(result.get("tests", 0) for result in results.values() if isinstance(result, dict) and "tests" in result)
        total_passed = sum(result.get("passed", 0) for result in results.values() if isinstance(result, dict) and "passed" in result)
        total_failures = sum(result.get("failures", 0) for result in results.values() if isinstance(result, dict) and "failures" in result)
        total_errors = sum(result.get("errors", 0) for result in results.values() if isinstance(result, dict) and "errors" in result)
        total_skipped = sum(result.get("skipped", 0) for result in results.values() if isinstance(result, dict) and "skipped" in result)
        
        f.write(f"""
                <div class="summary-item">
                    <div class="summary-number">{total_tests}</div>
                    <div class="summary-label">总测试数</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">{total_passed}</div>
                    <div class="summary-label">通过</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">{total_failures}</div>
                    <div class="summary-label">失败</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">{total_errors}</div>
                    <div class="summary-label">错误</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">{total_skipped}</div>
                    <div class="summary-label">跳过</div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>测试详情</h2>
            <table>
                <tr>
                    <th>测试模块</th>
                    <th>测试数</th>
                    <th>通过</th>
                    <th>失败</th>
                    <th>错误</th>
                    <th>跳过</th>
                    <th>结果</th>
                </tr>
        """)
        
        # 添加测试详情
        for module_name, result in results.items():
            if isinstance(result, dict) and "tests" in result:
                tests = result.get("tests", 0)
                passed = result.get("passed", 0)
                failures = result.get("failures", 0)
                errors = result.get("errors", 0)
                skipped = result.get("skipped", 0)
                
                result_class = "pass" if failures == 0 and errors == 0 else "fail"
                result_text = "PASS" if failures == 0 and errors == 0 else "FAIL"
                
                f.write(f"""
                <tr>
                    <td>{module_name}</td>
                    <td>{tests}</td>
                    <td>{passed}</td>
                    <td>{failures}</td>
                    <td>{errors}</td>
                    <td>{skipped}</td>
                    <td><span class="result-badge {result_class}">{result_text}</span></td>
                </tr>
                """)
            else:
                error = result.get("error", "未知错误")
                
                f.write(f"""
                <tr>
                    <td>{module_name}</td>
                    <td colspan="5">{error}</td>
                    <td><span class="result-badge fail">ERROR</span></td>
                </tr>
                """)
        
        f.write("""
            </table>
        </div>
        
        <div class="section">
            <h2>算法验证报告</h2>
            <p>以下是各算法的验证报告链接：</p>
            <ul>
        """)
        
        # 添加算法验证报告链接
        for root, dirs, files in os.walk(output_dir):
            for file in files:
                if file.endswith("_validation_report.html") and file != "algorithm_validation_report.html":
                    report_path = Path(root) / file
                    relative_path = report_path.relative_to(Path(output_dir))
                    algorithm_name = file.replace("_validation_report.html", "")
                    
                    f.write(f"""
                <li><a href="{relative_path}">{algorithm_name}</a></li>
                """)
        
        f.write("""
            </ul>
        </div>
    </div>
</body>
</html>
        """)
    
    logger.info(f"测试报告已生成：{html_report_file}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="算法验证测试运行脚本")
    parser.add_argument("--modules", "-m", nargs="+", help="要运行的测试模块")
    parser.add_argument("--output-dir", "-o", default="algorithm_validation_results", help="输出目录")
    
    args = parser.parse_args()
    
    # 运行测试
    run_tests(args.modules, args.output_dir)

if __name__ == "__main__":
    main()
