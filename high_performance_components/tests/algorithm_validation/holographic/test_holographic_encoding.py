#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全息编码算法验证测试

这个脚本验证全息编码算法的正确性，包括：
1. 与解析解的比较
2. 收敛性测试
3. 稳定性测试
"""

import os
import sys
import unittest
import numpy as np
from pathlib import Path

# 添加父目录到路径
sys.path.append(str(Path(__file__).parent.parent))
from test_framework import AlgorithmValidationTestCase, AlgorithmValidator

class HolographicEncoding:
    """全息编码实现"""

    @staticmethod
    def fourier_encode(data):
        """使用傅里叶变换进行全息编码

        Args:
            data: 输入数据

        Returns:
            全息编码结果
        """
        return np.fft.fft(data)

    @staticmethod
    def fourier_decode(encoded_data):
        """使用傅里叶变换进行全息解码

        Args:
            encoded_data: 编码数据

        Returns:
            全息解码结果
        """
        return np.fft.ifft(encoded_data)

    @staticmethod
    def wavelet_encode(data, level=None):
        """使用小波变换进行全息编码

        Args:
            data: 输入数据
            level: 分解级别，如果为None则使用最大级别

        Returns:
            全息编码结果
        """
        # 确保数据是numpy数组
        data = np.array(data, dtype=np.float64)

        # 确保数据长度为2的幂
        n = len(data)
        if n & (n - 1) != 0:
            # 不是2的幂，填充到最近的2的幂
            next_power = 2 ** (n - 1).bit_length()
            data = np.pad(data, (0, next_power - n))
            n = next_power

        if level is None:
            level = int(np.log2(n))

        # 限制级别
        level = min(level, int(np.log2(n)))

        # 初始化结果
        result = data.copy()

        # 执行小波变换
        for l in range(level):
            # 当前处理的长度
            current_n = n // (2 ** l)
            half_n = current_n // 2

            # 临时数组
            temp = np.zeros(current_n, dtype=np.float64)

            # 计算平均值和差值
            for i in range(half_n):
                temp[i] = (result[2*i] + result[2*i+1]) / np.sqrt(2)
                temp[i + half_n] = (result[2*i] - result[2*i+1]) / np.sqrt(2)

            # 更新结果
            result[:current_n] = temp

        return result

    @staticmethod
    def wavelet_decode(encoded_data, level=None):
        """使用小波变换进行全息解码

        Args:
            encoded_data: 编码数据
            level: 分解级别，如果为None则使用最大级别

        Returns:
            全息解码结果
        """
        # 确保数据是numpy数组
        encoded_data = np.array(encoded_data, dtype=np.float64)

        # 确保数据长度为2的幂
        n = len(encoded_data)
        if n & (n - 1) != 0:
            raise ValueError("编码数据长度必须为2的幂")

        if level is None:
            level = int(np.log2(n))

        # 限制级别
        level = min(level, int(np.log2(n)))

        # 初始化结果
        result = encoded_data.copy()

        # 执行逆小波变换
        for l in range(level - 1, -1, -1):
            # 当前处理的长度
            current_n = n // (2 ** l)
            half_n = current_n // 2

            # 临时数组
            temp = np.zeros(current_n, dtype=np.float64)

            # 从平均值和差值重建原始数据
            for i in range(half_n):
                temp[2*i] = (result[i] + result[i + half_n]) / np.sqrt(2)
                temp[2*i+1] = (result[i] - result[i + half_n]) / np.sqrt(2)

            # 更新结果
            result[:current_n] = temp

        return result

    @staticmethod
    def holographic_encode(data, method='fourier'):
        """全息编码

        Args:
            data: 输入数据
            method: 编码方法，'fourier'或'wavelet'

        Returns:
            全息编码结果
        """
        if method == 'fourier':
            return HolographicEncoding.fourier_encode(data)
        elif method == 'wavelet':
            return HolographicEncoding.wavelet_encode(data)
        else:
            raise ValueError(f"不支持的编码方法：{method}")

    @staticmethod
    def holographic_decode(encoded_data, method='fourier'):
        """全息解码

        Args:
            encoded_data: 编码数据
            method: 编码方法，'fourier'或'wavelet'

        Returns:
            全息解码结果
        """
        if method == 'fourier':
            return HolographicEncoding.fourier_decode(encoded_data)
        elif method == 'wavelet':
            return HolographicEncoding.wavelet_decode(encoded_data)
        else:
            raise ValueError(f"不支持的编码方法：{method}")

    @staticmethod
    def holographic_encode_iterative(data, iterations, method='fourier'):
        """迭代实现的全息编码

        Args:
            data: 输入数据
            iterations: 迭代次数
            method: 编码方法，'fourier'或'wavelet'

        Returns:
            全息编码结果
        """
        if method == 'fourier':
            # 对于傅里叶变换，迭代次数没有意义，直接返回结果
            return HolographicEncoding.fourier_encode(data)
        elif method == 'wavelet':
            # 对于小波变换，迭代次数表示分解级别
            return HolographicEncoding.wavelet_encode(data, level=iterations)
        else:
            raise ValueError(f"不支持的编码方法：{method}")

    @staticmethod
    def partial_decode(encoded_data, keep_ratio=0.5, method='fourier'):
        """部分解码

        Args:
            encoded_data: 编码数据
            keep_ratio: 保留系数的比例
            method: 编码方法，'fourier'或'wavelet'

        Returns:
            部分解码结果
        """
        n = len(encoded_data)
        keep_count = int(n * keep_ratio)

        if method == 'fourier':
            # 对于傅里叶变换，保留低频部分
            partial_encoded = encoded_data.copy()
            partial_encoded[keep_count:] = 0
            return HolographicEncoding.fourier_decode(partial_encoded)
        elif method == 'wavelet':
            # 对于小波变换，保留低频部分
            partial_encoded = encoded_data.copy()
            partial_encoded[keep_count:] = 0
            return HolographicEncoding.wavelet_decode(partial_encoded)
        else:
            raise ValueError(f"不支持的编码方法：{method}")

class TestHolographicEncoding(AlgorithmValidationTestCase):
    """全息编码测试"""

    def test_fourier_encoding(self):
        """测试傅里叶全息编码与解码的一致性"""
        # 创建测试输入
        inputs = [
            np.array([1, 2, 3, 4, 5, 6, 7, 8]),
            np.array([1, 0, 0, 0, 0, 0, 0, 0]),
            np.array([0, 1, 0, 0, 0, 0, 0, 0]),
            np.array([1, 1, 1, 1, 0, 0, 0, 0]),
            np.random.rand(8)
        ]

        # 验证傅里叶编码和解码的一致性
        def encode_decode(data):
            encoded = HolographicEncoding.fourier_encode(data)
            decoded = HolographicEncoding.fourier_decode(encoded)
            return decoded.real  # 返回实部，因为解码结果可能有很小的虚部

        validator = self.validate_algorithm(
            algorithm_name="Fourier Holographic Encoding",
            algorithm_func=encode_decode,
            analytical_func=lambda x: x,  # 恒等函数
            inputs=inputs,
            rtol=1e-10,
            atol=1e-10,
            plot=True
        )

    def test_wavelet_encoding(self):
        """测试小波全息编码与解码的一致性"""
        # 创建测试输入
        inputs = [
            np.array([1, 2, 3, 4, 5, 6, 7, 8]),
            np.array([1, 0, 0, 0, 0, 0, 0, 0]),
            np.array([0, 1, 0, 0, 0, 0, 0, 0]),
            np.array([1, 1, 1, 1, 0, 0, 0, 0]),
            np.random.rand(8)
        ]

        # 验证小波编码和解码的一致性
        def encode_decode(data):
            encoded = HolographicEncoding.wavelet_encode(data)
            decoded = HolographicEncoding.wavelet_decode(encoded)
            return decoded

        validator = self.validate_algorithm(
            algorithm_name="Wavelet Holographic Encoding",
            algorithm_func=encode_decode,
            analytical_func=lambda x: x,  # 恒等函数
            inputs=inputs,
            rtol=1e-10,
            atol=1e-10,
            plot=True
        )

    def test_wavelet_convergence(self):
        """测试小波全息编码的收敛性"""
        # 创建测试输入
        inputs = [
            np.array([1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0]),
            np.array([1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]),
            np.array([0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]),
            np.array([1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0])
        ]

        # 定义一个自定义的收敛性测试函数
        def wavelet_encode_with_error(data, level):
            """计算小波编码与原始数据的误差

            Args:
                data: 输入数据
                level: 小波分解级别

            Returns:
                编码后的数据与原始数据的误差
            """
            # 编码
            encoded = HolographicEncoding.wavelet_encode(data, level=level)

            # 解码
            decoded = HolographicEncoding.wavelet_decode(encoded, level=level)

            # 计算误差
            error = np.linalg.norm(decoded - data)

            return error

        # 迭代次数（小波分解级别）
        iterations = [1, 2, 3]

        # 验证小波编码的收敛性
        validator = self.validate_algorithm(
            algorithm_name="Wavelet Holographic Encoding Convergence",
            algorithm_func=wavelet_encode_with_error,
            inputs=inputs,
            iterations=iterations,
            rtol=1e-10,
            atol=1e-10,
            plot=True
        )

    def test_holographic_stability(self):
        """测试全息编码的稳定性"""
        # 创建基准输入
        base_input = np.random.rand(8)

        # 扰动大小
        perturbations = [1e-6, 1e-5, 1e-4, 1e-3, 1e-2]

        # 验证傅里叶全息编码的稳定性
        validator = self.validate_algorithm(
            algorithm_name="Fourier Holographic Encoding Stability",
            algorithm_func=lambda data: HolographicEncoding.holographic_encode(data, method='fourier'),
            base_input=base_input,
            perturbations=perturbations,
            rtol=1e-10,
            atol=1e-10,
            plot=True
        )

        # 验证小波全息编码的稳定性
        validator = self.validate_algorithm(
            algorithm_name="Wavelet Holographic Encoding Stability",
            algorithm_func=lambda data: HolographicEncoding.holographic_encode(data, method='wavelet'),
            base_input=base_input,
            perturbations=perturbations,
            rtol=1e-10,
            atol=1e-10,
            plot=True
        )

    def test_partial_decoding(self):
        """测试部分解码"""
        # 创建测试输入
        data = np.random.rand(16)

        # 傅里叶编码
        fourier_encoded = HolographicEncoding.fourier_encode(data)

        # 小波编码
        wavelet_encoded = HolographicEncoding.wavelet_encode(data)

        # 测试不同保留比例的部分解码
        keep_ratios = [0.1, 0.25, 0.5, 0.75, 1.0]

        for keep_ratio in keep_ratios:
            # 傅里叶部分解码
            fourier_partial_decoded = HolographicEncoding.partial_decode(fourier_encoded, keep_ratio, method='fourier')

            # 小波部分解码
            wavelet_partial_decoded = HolographicEncoding.partial_decode(wavelet_encoded, keep_ratio, method='wavelet')

            # 计算误差
            fourier_error = np.linalg.norm(fourier_partial_decoded.real - data) / np.linalg.norm(data)
            wavelet_error = np.linalg.norm(wavelet_partial_decoded - data) / np.linalg.norm(data)

            print(f"保留比例：{keep_ratio}")
            print(f"傅里叶部分解码误差：{fourier_error}")
            print(f"小波部分解码误差：{wavelet_error}")

            # 验证误差随保留比例增加而减小
            if keep_ratio < 1.0:
                next_keep_ratio = keep_ratios[keep_ratios.index(keep_ratio) + 1]

                # 下一个保留比例的部分解码
                fourier_next_decoded = HolographicEncoding.partial_decode(fourier_encoded, next_keep_ratio, method='fourier')
                wavelet_next_decoded = HolographicEncoding.partial_decode(wavelet_encoded, next_keep_ratio, method='wavelet')

                # 计算误差
                fourier_next_error = np.linalg.norm(fourier_next_decoded.real - data) / np.linalg.norm(data)
                wavelet_next_error = np.linalg.norm(wavelet_next_decoded - data) / np.linalg.norm(data)

                # 验证误差减小
                self.assertLess(fourier_next_error, fourier_error)
                self.assertLess(wavelet_next_error, wavelet_error)

if __name__ == "__main__":
    unittest.main()
