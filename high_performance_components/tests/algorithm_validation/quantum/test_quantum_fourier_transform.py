#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
量子傅里叶变换算法验证测试

这个脚本验证量子傅里叶变换算法的正确性，包括：
1. 与经典傅里叶变换的比较
2. 收敛性测试
3. 稳定性测试
"""

import os
import sys
import unittest
import numpy as np
from pathlib import Path

# 添加父目录到路径
sys.path.append(str(Path(__file__).parent.parent))
from test_framework import AlgorithmValidationTestCase, AlgorithmValidator

class QuantumFourierTransform:
    """量子傅里叶变换实现"""

    @staticmethod
    def qft(x, n_qubits=None):
        """量子傅里叶变换

        Args:
            x: 输入向量
            n_qubits: 量子比特数，如果为None则自动计算

        Returns:
            量子傅里叶变换结果
        """
        if isinstance(x, list):
            x = np.array(x)

        # 确保输入是复数数组
        x = np.array(x, dtype=np.complex128)

        if n_qubits is None:
            n_qubits = int(np.log2(len(x)))

        N = 2 ** n_qubits

        # 确保输入长度为2^n_qubits
        if len(x) < N:
            x = np.pad(x, (0, N - len(x)))
        elif len(x) > N:
            x = x[:N]

        # 创建QFT矩阵
        omega = np.exp(2j * np.pi / N)
        qft_matrix = np.zeros((N, N), dtype=np.complex128)

        for i in range(N):
            for j in range(N):
                qft_matrix[i, j] = omega ** (i * j)

        qft_matrix /= np.sqrt(N)

        # 应用QFT
        result = qft_matrix @ x

        return result

    @staticmethod
    def inverse_qft(x, n_qubits=None):
        """逆量子傅里叶变换

        Args:
            x: 输入向量
            n_qubits: 量子比特数，如果为None则自动计算

        Returns:
            逆量子傅里叶变换结果
        """
        if isinstance(x, list):
            x = np.array(x)

        # 确保输入是复数数组
        x = np.array(x, dtype=np.complex128)

        if n_qubits is None:
            n_qubits = int(np.log2(len(x)))

        N = 2 ** n_qubits

        # 确保输入长度为2^n_qubits
        if len(x) < N:
            x = np.pad(x, (0, N - len(x)))
        elif len(x) > N:
            x = x[:N]

        # 创建逆QFT矩阵
        omega = np.exp(-2j * np.pi / N)
        iqft_matrix = np.zeros((N, N), dtype=np.complex128)

        for i in range(N):
            for j in range(N):
                iqft_matrix[i, j] = omega ** (i * j)

        iqft_matrix /= np.sqrt(N)

        # 应用逆QFT
        result = iqft_matrix @ x

        return result

    @staticmethod
    def qft_iterative(x, iterations, n_qubits=None):
        """迭代实现的量子傅里叶变换

        Args:
            x: 输入向量
            iterations: 迭代次数
            n_qubits: 量子比特数，如果为None则自动计算

        Returns:
            量子傅里叶变换结果
        """
        if isinstance(x, list):
            x = np.array(x)

        # 确保输入是复数数组
        x = np.array(x, dtype=np.complex128)

        if n_qubits is None:
            n_qubits = int(np.log2(len(x)))

        N = 2 ** n_qubits

        # 确保输入长度为2^n_qubits
        if len(x) < N:
            x = np.pad(x, (0, N - len(x)))
        elif len(x) > N:
            x = x[:N]

        # 初始化结果
        result = x.copy()

        # 迭代应用QFT
        for iter in range(iterations):
            # 应用Hadamard门到每个量子比特
            for i in range(n_qubits):
                # 计算影响的索引
                indices = np.arange(N)
                mask = (indices >> i) & 1
                indices0 = indices[mask == 0]
                indices1 = indices[mask == 1]

                # 应用Hadamard变换
                temp = result[indices0] + result[indices1]
                result[indices1] = result[indices0] - result[indices1]
                result[indices0] = temp

                # 归一化
                result = result / np.sqrt(2)

            # 应用受控相位门
            if iter < iterations - 1:  # 最后一次迭代不需要应用相位门
                for i in range(n_qubits):
                    for j in range(i + 1, n_qubits):
                        # 计算相位
                        phase = np.exp(2j * np.pi / (2 ** (j - i)))

                        # 计算影响的索引
                        indices = np.arange(N)
                        mask_i = (indices >> i) & 1
                        mask_j = (indices >> j) & 1
                        mask = mask_i & mask_j
                        indices_phase = indices[mask == 1]

                        # 应用相位
                        if len(indices_phase) > 0:
                            result[indices_phase] = result[indices_phase] * phase

        # 重新排列结果（比特反转）
        bit_reversed = np.zeros_like(result)
        for i in range(N):
            reversed_i = int(format(i, f'0{n_qubits}b')[::-1], 2)
            bit_reversed[reversed_i] = result[i]

        return bit_reversed

def classical_fft(x):
    """经典傅里叶变换

    Args:
        x: 输入向量

    Returns:
        傅里叶变换结果
    """
    # 确保输入是复数数组
    x = np.array(x, dtype=np.complex128)

    # 计算FFT并归一化
    result = np.fft.fft(x) / np.sqrt(len(x))

    return result

class TestQuantumFourierTransform(AlgorithmValidationTestCase):
    """量子傅里叶变换测试"""

    def test_qft_analytical(self):
        """测试量子傅里叶变换与经典傅里叶变换的一致性"""
        # 创建测试输入
        inputs = [
            np.array([1, 0, 0, 0, 0, 0, 0, 0]),  # 基态 |0⟩
            np.array([0, 1, 0, 0, 0, 0, 0, 0]),  # 基态 |1⟩
            np.array([0, 0, 1, 0, 0, 0, 0, 0]),  # 基态 |2⟩
            np.array([0, 0, 0, 1, 0, 0, 0, 0]),  # 基态 |3⟩
            np.array([1, 1, 0, 0, 0, 0, 0, 0]) / np.sqrt(2),  # 叠加态 (|0⟩ + |1⟩)/√2
            np.array([1, 0, 1, 0, 0, 0, 0, 0]) / np.sqrt(2),  # 叠加态 (|0⟩ + |2⟩)/√2
            np.array([1, 1, 1, 1, 0, 0, 0, 0]) / 2,  # 叠加态 (|0⟩ + |1⟩ + |2⟩ + |3⟩)/2
            np.random.rand(8) + 1j * np.random.rand(8)  # 随机态
        ]

        # 归一化随机态
        inputs[-1] = inputs[-1] / np.linalg.norm(inputs[-1])

        # 验证QFT与经典FFT的一致性
        validator = self.validate_algorithm(
            algorithm_name="Quantum Fourier Transform",
            algorithm_func=QuantumFourierTransform.qft,
            analytical_func=classical_fft,
            inputs=inputs,
            rtol=1e-10,
            atol=1e-10,
            plot=True
        )

        # 验证逆QFT
        def inverse_transform(x):
            return QuantumFourierTransform.inverse_qft(QuantumFourierTransform.qft(x))

        def identity(x):
            return x

        validator = self.validate_algorithm(
            algorithm_name="Inverse Quantum Fourier Transform",
            algorithm_func=inverse_transform,
            analytical_func=identity,
            inputs=inputs,
            rtol=1e-10,
            atol=1e-10,
            plot=True
        )

    def test_qft_convergence(self):
        """测试迭代实现的量子傅里叶变换的收敛性"""
        # 创建测试输入
        inputs = [
            np.array([1, 0, 0, 0, 0, 0, 0, 0]),  # 基态 |0⟩
            np.array([0, 1, 0, 0, 0, 0, 0, 0]),  # 基态 |1⟩
            np.array([1, 1, 0, 0, 0, 0, 0, 0]) / np.sqrt(2),  # 叠加态 (|0⟩ + |1⟩)/√2
            np.random.rand(8) + 1j * np.random.rand(8)  # 随机态
        ]

        # 归一化随机态
        inputs[-1] = inputs[-1] / np.linalg.norm(inputs[-1])

        # 迭代次数
        iterations = [1, 2, 3, 4, 5]

        # 验证迭代实现的QFT的收敛性
        validator = self.validate_algorithm(
            algorithm_name="Iterative Quantum Fourier Transform",
            algorithm_func=QuantumFourierTransform.qft_iterative,
            inputs=inputs,
            iterations=iterations,
            rtol=1e-10,
            atol=1e-10,
            plot=True
        )

        # 验证迭代实现的QFT与解析实现的一致性
        def iterative_qft(x):
            return QuantumFourierTransform.qft_iterative(x, iterations=5)

        validator = self.validate_algorithm(
            algorithm_name="Iterative vs Analytical QFT",
            algorithm_func=iterative_qft,
            analytical_func=QuantumFourierTransform.qft,
            inputs=inputs,
            rtol=1e-10,
            atol=1e-10,
            plot=True
        )

    def test_qft_stability(self):
        """测试量子傅里叶变换的稳定性"""
        # 创建基准输入
        base_input = np.random.rand(8) + 1j * np.random.rand(8)
        base_input = base_input / np.linalg.norm(base_input)

        # 扰动大小
        perturbations = [1e-6, 1e-5, 1e-4, 1e-3, 1e-2]

        # 验证QFT的稳定性
        validator = self.validate_algorithm(
            algorithm_name="Quantum Fourier Transform Stability",
            algorithm_func=QuantumFourierTransform.qft,
            base_input=base_input,
            perturbations=perturbations,
            rtol=1e-10,
            atol=1e-10,
            plot=True
        )

if __name__ == "__main__":
    unittest.main()
