#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
算法验证测试框架

这个脚本提供了算法验证测试的框架，包括：
1. 数值算法验证：与已知解析解进行比较
2. 收敛性测试：验证迭代算法的收敛性
3. 稳定性测试：验证算法在不同输入下的稳定性
"""

import os
import sys
import unittest
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import logging
import json
import numpy as np

# 自定义JSON编码器，处理复数和NumPy类型
class ComplexEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, complex):
            return {"real": obj.real, "imag": obj.imag}
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        if isinstance(obj, np.bool_):
            return bool(obj)
        return super().default(obj)
from typing import Callable, Dict, List, Tuple, Union, Any, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('algorithm_validation.log')
    ]
)

logger = logging.getLogger('algorithm_validation')

class AlgorithmValidator:
    """算法验证器基类"""

    def __init__(self, algorithm_name: str, output_dir: str = "algorithm_validation_results"):
        """初始化算法验证器

        Args:
            algorithm_name: 算法名称
            output_dir: 输出目录
        """
        self.algorithm_name = algorithm_name
        self.output_dir = Path(output_dir) / algorithm_name
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.results = {}

    def validate_with_analytical_solution(self,
                                         algorithm_func: Callable,
                                         analytical_func: Callable,
                                         inputs: List[Any],
                                         rtol: float = 1e-5,
                                         atol: float = 1e-8,
                                         plot: bool = True) -> Dict:
        """与解析解进行比较验证

        Args:
            algorithm_func: 算法函数
            analytical_func: 解析解函数
            inputs: 输入数据列表
            rtol: 相对误差容忍度
            atol: 绝对误差容忍度
            plot: 是否生成对比图

        Returns:
            验证结果
        """
        logger.info(f"验证算法 {self.algorithm_name} 与解析解的一致性")

        results = {
            "algorithm_name": self.algorithm_name,
            "validation_type": "analytical_solution",
            "inputs": [],
            "algorithm_outputs": [],
            "analytical_outputs": [],
            "relative_errors": [],
            "absolute_errors": [],
            "passed": True
        }

        for i, input_data in enumerate(inputs):
            logger.info(f"处理输入 {i+1}/{len(inputs)}")

            # 计算算法输出
            algorithm_output = algorithm_func(input_data)

            # 计算解析解输出
            analytical_output = analytical_func(input_data)

            # 计算误差
            if isinstance(algorithm_output, (np.ndarray, list)):
                algorithm_output = np.array(algorithm_output)
                analytical_output = np.array(analytical_output)

                # 计算相对误差和绝对误差
                with np.errstate(divide='ignore', invalid='ignore'):
                    relative_error = np.abs((algorithm_output - analytical_output) / analytical_output)
                    relative_error = np.nan_to_num(relative_error, nan=0.0, posinf=0.0, neginf=0.0)
                    max_relative_error = np.max(relative_error)

                absolute_error = np.abs(algorithm_output - analytical_output)
                max_absolute_error = np.max(absolute_error)

                # 检查误差是否在容忍范围内
                passed = (max_relative_error <= rtol) or (max_absolute_error <= atol)
            else:
                # 标量值
                relative_error = abs((algorithm_output - analytical_output) / analytical_output) if analytical_output != 0 else 0
                absolute_error = abs(algorithm_output - analytical_output)
                max_relative_error = relative_error
                max_absolute_error = absolute_error

                # 检查误差是否在容忍范围内
                passed = (relative_error <= rtol) or (absolute_error <= atol)

            # 记录结果
            results["inputs"].append(input_data if not isinstance(input_data, np.ndarray) else input_data.tolist())
            results["algorithm_outputs"].append(algorithm_output if not isinstance(algorithm_output, np.ndarray) else algorithm_output.tolist())
            results["analytical_outputs"].append(analytical_output if not isinstance(analytical_output, np.ndarray) else analytical_output.tolist())
            results["relative_errors"].append(max_relative_error if not isinstance(relative_error, np.ndarray) else relative_error.tolist())
            results["absolute_errors"].append(max_absolute_error if not isinstance(absolute_error, np.ndarray) else absolute_error.tolist())

            if not passed:
                results["passed"] = False
                logger.warning(f"输入 {i+1} 验证失败：相对误差 {max_relative_error}，绝对误差 {max_absolute_error}")
            else:
                logger.info(f"输入 {i+1} 验证通过：相对误差 {max_relative_error}，绝对误差 {max_absolute_error}")

            # 生成对比图
            if plot and isinstance(algorithm_output, np.ndarray) and algorithm_output.ndim == 1:
                plt.figure(figsize=(10, 6))
                plt.plot(algorithm_output, label='Algorithm')
                plt.plot(analytical_output, label='Analytical', linestyle='--')
                plt.legend()
                plt.title(f"{self.algorithm_name} vs Analytical Solution - Input {i+1}")
                plt.xlabel("Index")
                plt.ylabel("Value")
                plt.grid(True)
                plt.savefig(self.output_dir / f"analytical_comparison_{i+1}.png")
                plt.close()

        # 保存结果
        with open(self.output_dir / "analytical_validation_results.json", "w") as f:
            json.dump(results, f, indent=2, cls=ComplexEncoder)

        self.results["analytical_validation"] = results

        return results

    def validate_convergence(self,
                            algorithm_func: Callable,
                            inputs: List[Any],
                            iterations: List[int],
                            rtol: float = 1e-5,
                            atol: float = 1e-8,
                            plot: bool = True) -> Dict:
        """验证迭代算法的收敛性

        Args:
            algorithm_func: 算法函数，接受输入数据和迭代次数
            inputs: 输入数据列表
            iterations: 迭代次数列表
            rtol: 相对误差容忍度
            atol: 绝对误差容忍度
            plot: 是否生成收敛图

        Returns:
            验证结果
        """
        logger.info(f"验证算法 {self.algorithm_name} 的收敛性")

        results = {
            "algorithm_name": self.algorithm_name,
            "validation_type": "convergence",
            "inputs": [],
            "iterations": iterations,
            "outputs": [],
            "convergence_rates": [],
            "passed": True
        }

        for i, input_data in enumerate(inputs):
            logger.info(f"处理输入 {i+1}/{len(inputs)}")

            # 计算不同迭代次数下的输出
            outputs = []
            for iter_count in iterations:
                output = algorithm_func(input_data, iter_count)
                outputs.append(output)

            # 计算收敛率
            convergence_rates = []
            for j in range(1, len(outputs)):
                if isinstance(outputs[j], np.ndarray):
                    error = np.linalg.norm(outputs[j] - outputs[j-1])
                    prev_error = np.linalg.norm(outputs[j-1] - outputs[j-2]) if j > 1 else 1.0
                else:
                    error = abs(outputs[j] - outputs[j-1])
                    prev_error = abs(outputs[j-1] - outputs[j-2]) if j > 1 else 1.0

                # 避免除以零
                if prev_error < 1e-10:
                    rate = 0.0
                else:
                    rate = error / prev_error

                convergence_rates.append(rate)

            # 检查收敛率是否小于1
            passed = all(rate < 1.0 for rate in convergence_rates)

            # 记录结果
            results["inputs"].append(input_data if not isinstance(input_data, np.ndarray) else input_data.tolist())
            results["outputs"].append([output if not isinstance(output, np.ndarray) else output.tolist() for output in outputs])
            results["convergence_rates"].append(convergence_rates)

            if not passed:
                results["passed"] = False
                logger.warning(f"输入 {i+1} 收敛性验证失败：收敛率 {convergence_rates}")
            else:
                logger.info(f"输入 {i+1} 收敛性验证通过：收敛率 {convergence_rates}")

            # 生成收敛图
            if plot:
                plt.figure(figsize=(10, 6))
                plt.semilogy(iterations[1:], convergence_rates, 'o-')
                plt.axhline(y=1.0, color='r', linestyle='--', label='Convergence Threshold')
                plt.title(f"{self.algorithm_name} Convergence Rate - Input {i+1}")
                plt.xlabel("Iteration")
                plt.ylabel("Convergence Rate")
                plt.grid(True)
                plt.legend()
                plt.savefig(self.output_dir / f"convergence_rate_{i+1}.png")
                plt.close()

        # 保存结果
        with open(self.output_dir / "convergence_validation_results.json", "w") as f:
            json.dump(results, f, indent=2, cls=ComplexEncoder)

        self.results["convergence_validation"] = results

        return results

    def validate_stability(self,
                          algorithm_func: Callable,
                          base_input: Any,
                          perturbations: List[float],
                          rtol: float = 1e-5,
                          atol: float = 1e-8,
                          plot: bool = True) -> Dict:
        """验证算法的稳定性

        Args:
            algorithm_func: 算法函数
            base_input: 基准输入数据
            perturbations: 扰动大小列表
            rtol: 相对误差容忍度
            atol: 绝对误差容忍度
            plot: 是否生成稳定性图

        Returns:
            验证结果
        """
        logger.info(f"验证算法 {self.algorithm_name} 的稳定性")

        results = {
            "algorithm_name": self.algorithm_name,
            "validation_type": "stability",
            "base_input": base_input if not isinstance(base_input, np.ndarray) else base_input.tolist(),
            "perturbations": perturbations,
            "perturbed_inputs": [],
            "base_output": None,
            "perturbed_outputs": [],
            "relative_errors": [],
            "condition_numbers": [],
            "passed": True
        }

        # 计算基准输出
        base_output = algorithm_func(base_input)
        results["base_output"] = base_output if not isinstance(base_output, np.ndarray) else base_output.tolist()

        # 对每个扰动大小进行测试
        for i, perturbation in enumerate(perturbations):
            logger.info(f"处理扰动 {i+1}/{len(perturbations)}: {perturbation}")

            # 创建扰动输入
            if isinstance(base_input, np.ndarray):
                noise = np.random.normal(0, perturbation, base_input.shape)
                perturbed_input = base_input + noise
            else:
                noise = np.random.normal(0, perturbation)
                perturbed_input = base_input + noise

            # 计算扰动输出
            perturbed_output = algorithm_func(perturbed_input)

            # 计算误差和条件数
            if isinstance(base_output, np.ndarray):
                input_norm = np.linalg.norm(noise) / np.linalg.norm(base_input)
                output_diff = np.linalg.norm(perturbed_output - base_output) / np.linalg.norm(base_output)
                condition_number = output_diff / input_norm if input_norm > 0 else float('inf')
            else:
                input_norm = abs(noise) / abs(base_input) if base_input != 0 else abs(noise)
                output_diff = abs(perturbed_output - base_output) / abs(base_output) if base_output != 0 else abs(perturbed_output - base_output)
                condition_number = output_diff / input_norm if input_norm > 0 else float('inf')

            # 检查条件数是否在合理范围内
            passed = condition_number < 1.0 / perturbation

            # 记录结果
            results["perturbed_inputs"].append(perturbed_input if not isinstance(perturbed_input, np.ndarray) else perturbed_input.tolist())
            results["perturbed_outputs"].append(perturbed_output if not isinstance(perturbed_output, np.ndarray) else perturbed_output.tolist())
            results["relative_errors"].append(output_diff)
            results["condition_numbers"].append(condition_number)

            if not passed:
                results["passed"] = False
                logger.warning(f"扰动 {perturbation} 稳定性验证失败：条件数 {condition_number}")
            else:
                logger.info(f"扰动 {perturbation} 稳定性验证通过：条件数 {condition_number}")

        # 生成稳定性图
        if plot:
            plt.figure(figsize=(10, 6))
            plt.loglog(perturbations, results["condition_numbers"], 'o-')
            plt.title(f"{self.algorithm_name} Stability - Condition Number vs Perturbation")
            plt.xlabel("Perturbation")
            plt.ylabel("Condition Number")
            plt.grid(True)
            plt.savefig(self.output_dir / "stability.png")
            plt.close()

        # 保存结果
        with open(self.output_dir / "stability_validation_results.json", "w") as f:
            json.dump(results, f, indent=2, cls=ComplexEncoder)

        self.results["stability_validation"] = results

        return results

    def generate_report(self) -> str:
        """生成验证报告

        Returns:
            报告文件路径
        """
        logger.info(f"生成算法 {self.algorithm_name} 的验证报告")

        report_file = self.output_dir / f"{self.algorithm_name}_validation_report.html"

        with open(report_file, "w") as f:
            f.write(f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{self.algorithm_name} 算法验证报告</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }}
        h1, h2, h3 {{
            color: #2c3e50;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
        }}
        .section {{
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }}
        .success {{
            color: #27ae60;
        }}
        .warning {{
            color: #f39c12;
        }}
        .error {{
            color: #e74c3c;
        }}
        pre {{
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }}
        code {{
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }}
        th, td {{
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        th {{
            background-color: #f2f2f2;
        }}
        .result-badge {{
            display: inline-block;
            padding: 3px 6px;
            border-radius: 3px;
            color: white;
            font-weight: bold;
        }}
        .result-badge.pass {{
            background-color: #27ae60;
        }}
        .result-badge.fail {{
            background-color: #e74c3c;
        }}
        .image-container {{
            text-align: center;
            margin: 20px 0;
        }}
        .image-container img {{
            max-width: 100%;
            height: auto;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>{self.algorithm_name} 算法验证报告</h1>

        <div class="section">
            <h2>验证摘要</h2>
            <table>
                <tr>
                    <th>验证类型</th>
                    <th>结果</th>
                </tr>
            """)

            # 添加验证结果摘要
            if "analytical_validation" in self.results:
                result = "pass" if self.results["analytical_validation"]["passed"] else "fail"
                f.write(f"""
                <tr>
                    <td>与解析解比较</td>
                    <td><span class="result-badge {result}">{result.upper()}</span></td>
                </tr>
                """)

            if "convergence_validation" in self.results:
                result = "pass" if self.results["convergence_validation"]["passed"] else "fail"
                f.write(f"""
                <tr>
                    <td>收敛性验证</td>
                    <td><span class="result-badge {result}">{result.upper()}</span></td>
                </tr>
                """)

            if "stability_validation" in self.results:
                result = "pass" if self.results["stability_validation"]["passed"] else "fail"
                f.write(f"""
                <tr>
                    <td>稳定性验证</td>
                    <td><span class="result-badge {result}">{result.upper()}</span></td>
                </tr>
                """)

            f.write("""
            </table>
        </div>
        """)

            # 添加与解析解比较的详细结果
            if "analytical_validation" in self.results:
                f.write("""
        <div class="section">
            <h2>与解析解比较</h2>
            <table>
                <tr>
                    <th>输入</th>
                    <th>相对误差</th>
                    <th>绝对误差</th>
                    <th>结果</th>
                </tr>
                """)

                for i in range(len(self.results["analytical_validation"]["inputs"])):
                    relative_error = self.results["analytical_validation"]["relative_errors"][i]
                    absolute_error = self.results["analytical_validation"]["absolute_errors"][i]

                    if isinstance(relative_error, list):
                        relative_error = max(relative_error)
                    if isinstance(absolute_error, list):
                        absolute_error = max(absolute_error)

                    result = "pass" if relative_error <= 1e-5 or absolute_error <= 1e-8 else "fail"

                    f.write(f"""
                <tr>
                    <td>输入 {i+1}</td>
                    <td>{relative_error:.2e}</td>
                    <td>{absolute_error:.2e}</td>
                    <td><span class="result-badge {result}">{result.upper()}</span></td>
                </tr>
                """)

                f.write("""
            </table>

            <h3>对比图</h3>
            <div class="image-container">
                """)

                for i in range(len(self.results["analytical_validation"]["inputs"])):
                    image_path = f"analytical_comparison_{i+1}.png"
                    if (self.output_dir / image_path).exists():
                        f.write(f"""
                <img src="{image_path}" alt="Analytical Comparison {i+1}">
                """)

                f.write("""
            </div>
        </div>
                """)

            # 添加收敛性验证的详细结果
            if "convergence_validation" in self.results:
                f.write("""
        <div class="section">
            <h2>收敛性验证</h2>
            <table>
                <tr>
                    <th>输入</th>
                    <th>收敛率</th>
                    <th>结果</th>
                </tr>
                """)

                for i in range(len(self.results["convergence_validation"]["inputs"])):
                    convergence_rates = self.results["convergence_validation"]["convergence_rates"][i]
                    result = "pass" if all(rate < 1.0 for rate in convergence_rates) else "fail"

                    f.write(f"""
                <tr>
                    <td>输入 {i+1}</td>
                    <td>{[f"{rate:.2f}" for rate in convergence_rates]}</td>
                    <td><span class="result-badge {result}">{result.upper()}</span></td>
                </tr>
                """)

                f.write("""
            </table>

            <h3>收敛图</h3>
            <div class="image-container">
                """)

                for i in range(len(self.results["convergence_validation"]["inputs"])):
                    image_path = f"convergence_rate_{i+1}.png"
                    if (self.output_dir / image_path).exists():
                        f.write(f"""
                <img src="{image_path}" alt="Convergence Rate {i+1}">
                """)

                f.write("""
            </div>
        </div>
                """)

            # 添加稳定性验证的详细结果
            if "stability_validation" in self.results:
                f.write("""
        <div class="section">
            <h2>稳定性验证</h2>
            <table>
                <tr>
                    <th>扰动</th>
                    <th>条件数</th>
                    <th>结果</th>
                </tr>
                """)

                for i in range(len(self.results["stability_validation"]["perturbations"])):
                    perturbation = self.results["stability_validation"]["perturbations"][i]
                    condition_number = self.results["stability_validation"]["condition_numbers"][i]
                    result = "pass" if condition_number < 1.0 / perturbation else "fail"

                    f.write(f"""
                <tr>
                    <td>{perturbation:.2e}</td>
                    <td>{condition_number:.2f}</td>
                    <td><span class="result-badge {result}">{result.upper()}</span></td>
                </tr>
                """)

                f.write("""
            </table>

            <h3>稳定性图</h3>
            <div class="image-container">
                <img src="stability.png" alt="Stability">
            </div>
        </div>
                """)

            f.write("""
    </div>
</body>
</html>
            """)

        logger.info(f"验证报告已生成：{report_file}")

        return str(report_file)

class AlgorithmValidationTestCase(unittest.TestCase):
    """算法验证测试用例基类"""

    def setUp(self):
        """设置测试环境"""
        self.output_dir = "algorithm_validation_results"
        os.makedirs(self.output_dir, exist_ok=True)

    def validate_algorithm(self,
                          algorithm_name: str,
                          algorithm_func: Callable,
                          analytical_func: Optional[Callable] = None,
                          inputs: Optional[List[Any]] = None,
                          iterations: Optional[List[int]] = None,
                          base_input: Optional[Any] = None,
                          perturbations: Optional[List[float]] = None,
                          rtol: float = 1e-5,
                          atol: float = 1e-8,
                          plot: bool = True) -> AlgorithmValidator:
        """验证算法

        Args:
            algorithm_name: 算法名称
            algorithm_func: 算法函数
            analytical_func: 解析解函数
            inputs: 输入数据列表
            iterations: 迭代次数列表
            base_input: 基准输入数据
            perturbations: 扰动大小列表
            rtol: 相对误差容忍度
            atol: 绝对误差容忍度
            plot: 是否生成图表

        Returns:
            算法验证器
        """
        validator = AlgorithmValidator(algorithm_name, self.output_dir)

        # 与解析解比较
        if analytical_func is not None and inputs is not None:
            results = validator.validate_with_analytical_solution(
                algorithm_func=algorithm_func,
                analytical_func=analytical_func,
                inputs=inputs,
                rtol=rtol,
                atol=atol,
                plot=plot
            )
            self.assertTrue(results["passed"], "与解析解比较验证失败")

        # 收敛性验证
        if iterations is not None and inputs is not None:
            results = validator.validate_convergence(
                algorithm_func=algorithm_func,
                inputs=inputs,
                iterations=iterations,
                rtol=rtol,
                atol=atol,
                plot=plot
            )
            self.assertTrue(results["passed"], "收敛性验证失败")

        # 稳定性验证
        if base_input is not None and perturbations is not None:
            results = validator.validate_stability(
                algorithm_func=algorithm_func,
                base_input=base_input,
                perturbations=perturbations,
                rtol=rtol,
                atol=atol,
                plot=plot
            )
            self.assertTrue(results["passed"], "稳定性验证失败")

        # 生成报告
        validator.generate_report()

        return validator
