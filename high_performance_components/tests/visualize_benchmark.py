#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
零拷贝机制性能基准测试结果可视化

这个脚本可视化零拷贝机制的性能基准测试结果，包括：
1. NumPy数组的零拷贝转换性能
2. 量子态的序列化和反序列化性能
"""

import os
import sys
import json
import argparse
import matplotlib.pyplot as plt
import numpy as np

def visualize_numpy_array_zero_copy(results):
    """可视化NumPy数组的零拷贝转换性能"""
    sizes = [int(size) for size in results.keys()]
    sizes.sort()
    
    zero_copy_throughputs = [results[str(size)]["zero_copy_throughput"] for size in sizes]
    copy_throughputs = [results[str(size)]["copy_throughput"] for size in sizes]
    speedups = [results[str(size)]["speedup"] for size in sizes]
    
    # 创建吞吐量图表
    plt.figure(figsize=(10, 6))
    plt.plot(sizes, zero_copy_throughputs, "o-", label="零拷贝")
    plt.plot(sizes, copy_throughputs, "o-", label="普通复制")
    plt.xscale("log")
    plt.yscale("log")
    plt.xlabel("数组大小")
    plt.ylabel("吞吐量 (MB/s)")
    plt.title("NumPy数组零拷贝转换性能")
    plt.legend()
    plt.grid(True)
    plt.savefig("numpy_array_zero_copy_throughput.png")
    
    # 创建加速比图表
    plt.figure(figsize=(10, 6))
    plt.plot(sizes, speedups, "o-")
    plt.xscale("log")
    plt.yscale("log")
    plt.xlabel("数组大小")
    plt.ylabel("加速比")
    plt.title("NumPy数组零拷贝转换加速比")
    plt.grid(True)
    plt.savefig("numpy_array_zero_copy_speedup.png")

def visualize_quantum_state_serialization(results):
    """可视化量子态的序列化和反序列化性能"""
    qubit_counts = [int(n_qubits) for n_qubits in results.keys()]
    qubit_counts.sort()
    
    dims = [results[str(n_qubits)]["dim"] for n_qubits in qubit_counts]
    serialize_throughputs = [results[str(n_qubits)]["serialize_throughput"] for n_qubits in qubit_counts]
    deserialize_throughputs = [results[str(n_qubits)]["deserialize_throughput"] for n_qubits in qubit_counts]
    
    # 创建吞吐量图表
    plt.figure(figsize=(10, 6))
    plt.plot(qubit_counts, serialize_throughputs, "o-", label="序列化")
    plt.plot(qubit_counts, deserialize_throughputs, "o-", label="反序列化")
    plt.xlabel("量子比特数")
    plt.ylabel("吞吐量 (MB/s)")
    plt.title("量子态序列化和反序列化性能")
    plt.legend()
    plt.grid(True)
    plt.savefig("quantum_state_serialization_throughput.png")
    
    # 创建时间图表
    serialize_times = [results[str(n_qubits)]["serialize_time"] * 1000 for n_qubits in qubit_counts]
    deserialize_times = [results[str(n_qubits)]["deserialize_time"] * 1000 for n_qubits in qubit_counts]
    
    plt.figure(figsize=(10, 6))
    plt.plot(qubit_counts, serialize_times, "o-", label="序列化")
    plt.plot(qubit_counts, deserialize_times, "o-", label="反序列化")
    plt.xlabel("量子比特数")
    plt.ylabel("时间 (毫秒)")
    plt.title("量子态序列化和反序列化时间")
    plt.legend()
    plt.grid(True)
    plt.savefig("quantum_state_serialization_time.png")
    
    # 创建维度与时间的关系图表
    plt.figure(figsize=(10, 6))
    plt.plot(dims, serialize_times, "o-", label="序列化")
    plt.plot(dims, deserialize_times, "o-", label="反序列化")
    plt.xscale("log")
    plt.yscale("log")
    plt.xlabel("维度")
    plt.ylabel("时间 (毫秒)")
    plt.title("量子态维度与序列化/反序列化时间的关系")
    plt.legend()
    plt.grid(True)
    plt.savefig("quantum_state_dimension_time.png")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="零拷贝机制性能基准测试结果可视化")
    parser.add_argument("--input", "-i", required=True, help="输入JSON文件路径")
    parser.add_argument("--output-dir", "-o", default=".", help="输出目录")
    
    args = parser.parse_args()
    
    # 加载结果
    with open(args.input, "r") as f:
        results = json.load(f)
    
    # 切换到输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    os.chdir(args.output_dir)
    
    # 可视化结果
    if "numpy_array_zero_copy" in results:
        visualize_numpy_array_zero_copy(results["numpy_array_zero_copy"])
    
    if "quantum_state_serialization" in results:
        visualize_quantum_state_serialization(results["quantum_state_serialization"])
    
    print(f"可视化结果已保存到：{args.output_dir}")

if __name__ == "__main__":
    main()
