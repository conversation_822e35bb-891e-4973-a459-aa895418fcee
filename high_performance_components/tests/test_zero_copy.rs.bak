//! Zero-copy mechanism tests

use std::path::Path;
use std::fs::File;
use std::io::{Read, Write};
use tempfile::tempdir;

#[test]
fn test_file_io() {
    // Create a temporary directory
    let dir = tempdir().unwrap();
    let file_path = dir.path().join("test.bin");
    
    // Create test data
    let test_data = b"Hello, world!";
    
    // Write data to file
    {
        let mut file = File::create(&file_path).unwrap();
        file.write_all(test_data).unwrap();
    }
    
    // Read data from file
    let mut read_data = Vec::new();
    {
        let mut file = File::open(&file_path).unwrap();
        file.read_to_end(&mut read_data).unwrap();
    }
    
    // Verify data
    assert_eq!(read_data, test_data);
}

#[test]
fn test_memory_mapping() {
    // Create a temporary directory
    let dir = tempdir().unwrap();
    let file_path = dir.path().join("test.bin");
    
    // Create test data
    let test_data = b"Hello, world!";
    
    // Create file and set size
    {
        let mut file = File::create(&file_path).unwrap();
        file.write_all(test_data).unwrap();
    }
    
    // Memory-map the file
    let file = File::open(&file_path).unwrap();
    let mmap = unsafe { memmap2::Mmap::map(&file).unwrap() };
    
    // Verify data
    assert_eq!(&mmap[..], test_data);
}

#[test]
fn test_memory_mapping_mut() {
    // Create a temporary directory
    let dir = tempdir().unwrap();
    let file_path = dir.path().join("test.bin");
    
    // Create file and set size
    {
        let file = File::create(&file_path).unwrap();
        file.set_len(13).unwrap();
    }
    
    // Memory-map the file
    let file = std::fs::OpenOptions::new()
        .read(true)
        .write(true)
        .open(&file_path)
        .unwrap();
    let mut mmap = unsafe { memmap2::MmapMut::map_mut(&file).unwrap() };
    
    // Write data
    let test_data = b"Hello, world!";
    mmap[..test_data.len()].copy_from_slice(test_data);
    mmap.flush().unwrap();
    
    // Read data from file
    let mut read_data = Vec::new();
    {
        let mut file = File::open(&file_path).unwrap();
        file.read_to_end(&mut read_data).unwrap();
    }
    
    // Verify data
    assert_eq!(read_data, test_data);
}

#[test]
fn test_zero_copy_buffer() {
    // Create a buffer
    let mut buffer = vec![0u8; 1024];
    
    // Write data
    let test_data = b"Hello, world!";
    buffer[..test_data.len()].copy_from_slice(test_data);
    
    // Create a slice
    let slice = &buffer[..test_data.len()];
    
    // Verify data
    assert_eq!(slice, test_data);
}

#[test]
fn test_arrow_buffer() {
    // Create an Arrow buffer
    let test_data = b"Hello, world!";
    let buffer = arrow::buffer::Buffer::from_slice_ref(test_data);
    
    // Verify data
    assert_eq!(buffer.as_slice(), test_data);
}

#[test]
fn test_arrow_array() {
    // Create an Arrow array
    let values: Vec<i32> = vec![1, 2, 3, 4, 5];
    let array = arrow::array::Int32Array::from(values.clone());
    
    // Verify data
    for i in 0..values.len() {
        assert_eq!(array.value(i), values[i]);
    }
}

#[test]
fn test_numpy_compatibility() {
    // This test would normally use PyO3 and numpy crates
    // For simplicity, we'll just test the basic functionality
    
    // Create a buffer
    let values: Vec<f64> = vec![1.0, 2.0, 3.0, 4.0, 5.0];
    
    // In a real test, we would convert this to a NumPy array
    // and back, but for now we'll just verify the data
    
    // Verify data
    assert_eq!(values.len(), 5);
    assert_eq!(values[0], 1.0);
    assert_eq!(values[4], 5.0);
}

#[test]
fn test_quantum_state_serialization() {
    // Create a quantum state
    let state = vec![
        num_complex::Complex64::new(1.0 / std::f64::consts::SQRT_2, 0.0),
        num_complex::Complex64::new(1.0 / std::f64::consts::SQRT_2, 0.0),
    ];
    
    // Serialize
    let serialized = bincode::serialize(&state).unwrap();
    
    // Deserialize
    let deserialized: Vec<num_complex::Complex64> = bincode::deserialize(&serialized).unwrap();
    
    // Verify data
    assert_eq!(deserialized.len(), state.len());
    for i in 0..state.len() {
        assert!((deserialized[i].re - state[i].re).abs() < 1e-10);
        assert!((deserialized[i].im - state[i].im).abs() < 1e-10);
    }
}

#[test]
fn test_fractal_structure_serialization() {
    // Create a simple fractal structure
    #[derive(serde::Serialize, serde::Deserialize, Debug, PartialEq)]
    struct FractalNode {
        id: usize,
        position: Vec<f64>,
    }
    
    let nodes = vec![
        FractalNode { id: 0, position: vec![0.0, 0.0] },
        FractalNode { id: 1, position: vec![1.0, 0.0] },
        FractalNode { id: 2, position: vec![0.5, 0.866] },
    ];
    
    // Serialize
    let serialized = bincode::serialize(&nodes).unwrap();
    
    // Deserialize
    let deserialized: Vec<FractalNode> = bincode::deserialize(&serialized).unwrap();
    
    // Verify data
    assert_eq!(deserialized, nodes);
}

#[test]
fn test_holographic_data_serialization() {
    // Create a simple holographic data structure
    #[derive(serde::Serialize, serde::Deserialize, Debug)]
    struct HolographicData {
        data: Vec<num_complex::Complex64>,
        encoding_type: String,
    }
    
    let data = HolographicData {
        data: vec![
            num_complex::Complex64::new(1.0, 0.0),
            num_complex::Complex64::new(0.0, 1.0),
            num_complex::Complex64::new(-1.0, 0.0),
            num_complex::Complex64::new(0.0, -1.0),
        ],
        encoding_type: "fourier".to_string(),
    };
    
    // Serialize
    let serialized = bincode::serialize(&data).unwrap();
    
    // Deserialize
    let deserialized: HolographicData = bincode::deserialize(&serialized).unwrap();
    
    // Verify data
    assert_eq!(deserialized.data.len(), data.data.len());
    assert_eq!(deserialized.encoding_type, data.encoding_type);
    for i in 0..data.data.len() {
        assert!((deserialized.data[i].re - data.data[i].re).abs() < 1e-10);
        assert!((deserialized.data[i].im - data.data[i].im).abs() < 1e-10);
    }
}
