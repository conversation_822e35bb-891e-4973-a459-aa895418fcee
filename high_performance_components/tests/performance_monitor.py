#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
零拷贝机制性能监控

这个脚本实时监控零拷贝机制的性能，包括：
1. 性能数据收集
2. 性能数据分析
3. 性能数据可视化
4. 性能警报
5. 性能报告生成
"""

import os
import sys
import json
import time
import argparse
import datetime
import sqlite3
import numpy as np
import threading
import subprocess
from pathlib import Path

class PerformanceMonitor:
    """性能监控类"""
    
    def __init__(self, db_path, interval=60, threshold=10):
        """初始化性能监控"""
        self.db_path = db_path
        self.interval = interval  # 监控间隔（秒）
        self.threshold = threshold  # 性能退化阈值（百分比）
        self.running = False
        self.thread = None
        self.conn = None
        self.create_database()
    
    def create_database(self):
        """创建数据库"""
        self.conn = sqlite3.connect(self.db_path)
        cursor = self.conn.cursor()
        
        # 创建性能数据表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS performance_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp TEXT NOT NULL,
            test_name TEXT NOT NULL,
            metric_name TEXT NOT NULL,
            metric_value REAL NOT NULL
        )
        ''')
        
        # 创建警报表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS alerts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp TEXT NOT NULL,
            test_name TEXT NOT NULL,
            metric_name TEXT NOT NULL,
            current_value REAL NOT NULL,
            baseline_value REAL NOT NULL,
            change_percent REAL NOT NULL,
            severity TEXT NOT NULL
        )
        ''')
        
        self.conn.commit()
    
    def start(self):
        """启动性能监控"""
        if self.running:
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._monitor_loop)
        self.thread.daemon = True
        self.thread.start()
        
        print(f"性能监控已启动，间隔：{self.interval}秒，阈值：{self.threshold}%")
    
    def stop(self):
        """停止性能监控"""
        self.running = False
        if self.thread:
            self.thread.join()
        
        print("性能监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.running:
            try:
                # 收集性能数据
                performance_data = self.collect_performance_data()
                
                # 存储性能数据
                self.store_performance_data(performance_data)
                
                # 分析性能数据
                alerts = self.analyze_performance_data(performance_data)
                
                # 处理警报
                if alerts:
                    self.handle_alerts(alerts)
            except Exception as e:
                print(f"监控循环异常：{e}")
            
            # 等待下一个监控间隔
            time.sleep(self.interval)
    
    def collect_performance_data(self):
        """收集性能数据"""
        performance_data = {}
        timestamp = datetime.datetime.now().isoformat()
        
        # 运行基准测试
        try:
            # 运行NumPy数组零拷贝转换基准测试
            result = subprocess.run(
                [sys.executable, "tests/benchmark_zero_copy.py", "--numpy", "--output", "/dev/null"],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                # 解析输出
                for line in result.stdout.split("\n"):
                    if "数组大小:" in line and "零拷贝吞吐量:" in line:
                        parts = line.split(",")
                        size = parts[0].split(":")[1].strip()
                        throughput = float(parts[1].split(":")[1].strip().split()[0])
                        
                        performance_data[f"numpy_array_zero_copy_{size}"] = {
                            "timestamp": timestamp,
                            "test_name": "numpy_array_zero_copy",
                            "metric_name": f"throughput_{size}",
                            "metric_value": throughput
                        }
        except Exception as e:
            print(f"收集NumPy数组零拷贝转换性能数据异常：{e}")
        
        # 运行量子态序列化基准测试
        try:
            # 运行量子态序列化基准测试
            result = subprocess.run(
                [sys.executable, "tests/benchmark_zero_copy.py", "--quantum", "--output", "/dev/null"],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                # 解析输出
                for line in result.stdout.split("\n"):
                    if "量子比特数:" in line and "序列化吞吐量:" in line:
                        parts = line.split(",")
                        n_qubits = parts[0].split(":")[1].strip()
                        throughput = float(parts[1].split(":")[1].strip().split()[0])
                        
                        performance_data[f"quantum_state_serialization_{n_qubits}"] = {
                            "timestamp": timestamp,
                            "test_name": "quantum_state_serialization",
                            "metric_name": f"throughput_{n_qubits}",
                            "metric_value": throughput
                        }
        except Exception as e:
            print(f"收集量子态序列化性能数据异常：{e}")
        
        return performance_data
    
    def store_performance_data(self, performance_data):
        """存储性能数据"""
        cursor = self.conn.cursor()
        
        for key, data in performance_data.items():
            cursor.execute('''
            INSERT INTO performance_data (timestamp, test_name, metric_name, metric_value)
            VALUES (?, ?, ?, ?)
            ''', (data["timestamp"], data["test_name"], data["metric_name"], data["metric_value"]))
        
        self.conn.commit()
    
    def analyze_performance_data(self, performance_data):
        """分析性能数据"""
        alerts = []
        cursor = self.conn.cursor()
        
        for key, data in performance_data.items():
            # 获取基准值
            cursor.execute('''
            SELECT AVG(metric_value)
            FROM performance_data
            WHERE test_name = ? AND metric_name = ?
            ORDER BY timestamp DESC
            LIMIT 10
            ''', (data["test_name"], data["metric_name"]))
            
            result = cursor.fetchone()
            if result and result[0]:
                baseline_value = result[0]
                current_value = data["metric_value"]
                
                # 计算变化百分比
                change_percent = (baseline_value - current_value) / baseline_value * 100
                
                # 如果性能退化超过阈值，生成警报
                if change_percent > self.threshold:
                    severity = "high" if change_percent > self.threshold * 2 else "medium" if change_percent > self.threshold * 1.5 else "low"
                    
                    alert = {
                        "timestamp": data["timestamp"],
                        "test_name": data["test_name"],
                        "metric_name": data["metric_name"],
                        "current_value": current_value,
                        "baseline_value": baseline_value,
                        "change_percent": change_percent,
                        "severity": severity
                    }
                    
                    alerts.append(alert)
                    
                    # 存储警报
                    cursor.execute('''
                    INSERT INTO alerts (timestamp, test_name, metric_name, current_value, baseline_value, change_percent, severity)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (alert["timestamp"], alert["test_name"], alert["metric_name"], alert["current_value"], alert["baseline_value"], alert["change_percent"], alert["severity"]))
        
        self.conn.commit()
        
        return alerts
    
    def handle_alerts(self, alerts):
        """处理警报"""
        for alert in alerts:
            print(f"警报：{alert['test_name']} {alert['metric_name']} 性能退化 {alert['change_percent']:.2f}%，严重程度：{alert['severity']}")
            
            # 这里可以添加发送邮件、短信等通知方式
    
    def get_performance_data(self, test_name=None, metric_name=None, limit=100):
        """获取性能数据"""
        cursor = self.conn.cursor()
        
        query = "SELECT timestamp, test_name, metric_name, metric_value FROM performance_data"
        params = []
        
        if test_name or metric_name:
            query += " WHERE"
            
            if test_name:
                query += " test_name = ?"
                params.append(test_name)
                
                if metric_name:
                    query += " AND"
            
            if metric_name:
                query += " metric_name = ?"
                params.append(metric_name)
        
        query += " ORDER BY timestamp DESC LIMIT ?"
        params.append(limit)
        
        cursor.execute(query, params)
        
        return cursor.fetchall()
    
    def get_alerts(self, severity=None, limit=100):
        """获取警报"""
        cursor = self.conn.cursor()
        
        query = "SELECT timestamp, test_name, metric_name, current_value, baseline_value, change_percent, severity FROM alerts"
        params = []
        
        if severity:
            query += " WHERE severity = ?"
            params.append(severity)
        
        query += " ORDER BY timestamp DESC LIMIT ?"
        params.append(limit)
        
        cursor.execute(query, params)
        
        return cursor.fetchall()
    
    def generate_performance_report(self, output_file):
        """生成性能报告"""
        # 获取性能数据
        performance_data = {}
        cursor = self.conn.cursor()
        
        cursor.execute('''
        SELECT test_name, metric_name, timestamp, metric_value
        FROM performance_data
        ORDER BY timestamp
        ''')
        
        for row in cursor.fetchall():
            test_name, metric_name, timestamp, metric_value = row
            
            if test_name not in performance_data:
                performance_data[test_name] = {}
            
            if metric_name not in performance_data[test_name]:
                performance_data[test_name][metric_name] = []
            
            performance_data[test_name][metric_name].append({
                "timestamp": timestamp,
                "value": metric_value
            })
        
        # 获取警报
        alerts = []
        cursor.execute('''
        SELECT timestamp, test_name, metric_name, current_value, baseline_value, change_percent, severity
        FROM alerts
        ORDER BY timestamp DESC
        ''')
        
        for row in cursor.fetchall():
            timestamp, test_name, metric_name, current_value, baseline_value, change_percent, severity = row
            
            alerts.append({
                "timestamp": timestamp,
                "test_name": test_name,
                "metric_name": metric_name,
                "current_value": current_value,
                "baseline_value": baseline_value,
                "change_percent": change_percent,
                "severity": severity
            })
        
        # 生成报告
        report = {
            "performance_data": performance_data,
            "alerts": alerts
        }
        
        with open(output_file, "w") as f:
            json.dump(report, f, indent=2)
        
        return report
    
    def generate_html_report(self, output_file):
        """生成HTML性能报告"""
        # 获取性能数据
        performance_data = {}
        cursor = self.conn.cursor()
        
        cursor.execute('''
        SELECT test_name, metric_name, timestamp, metric_value
        FROM performance_data
        ORDER BY timestamp
        ''')
        
        for row in cursor.fetchall():
            test_name, metric_name, timestamp, metric_value = row
            
            if test_name not in performance_data:
                performance_data[test_name] = {}
            
            if metric_name not in performance_data[test_name]:
                performance_data[test_name][metric_name] = []
            
            performance_data[test_name][metric_name].append({
                "timestamp": timestamp,
                "value": metric_value
            })
        
        # 获取警报
        alerts = []
        cursor.execute('''
        SELECT timestamp, test_name, metric_name, current_value, baseline_value, change_percent, severity
        FROM alerts
        ORDER BY timestamp DESC
        ''')
        
        for row in cursor.fetchall():
            timestamp, test_name, metric_name, current_value, baseline_value, change_percent, severity = row
            
            alerts.append({
                "timestamp": timestamp,
                "test_name": test_name,
                "metric_name": metric_name,
                "current_value": current_value,
                "baseline_value": baseline_value,
                "change_percent": change_percent,
                "severity": severity
            })
        
        # 生成HTML报告
        html = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>零拷贝机制性能监控报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .chart {
            margin-bottom: 20px;
        }
        .chart-container {
            height: 400px;
        }
        .alert {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .alert-timestamp {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        .alert-test {
            font-weight: bold;
        }
        .alert-metric {
            font-style: italic;
        }
        .alert-change {
            font-weight: bold;
            color: #e74c3c;
        }
        .alert-severity {
            float: right;
            padding: 2px 5px;
            border-radius: 3px;
            color: white;
        }
        .alert-severity.high {
            background-color: #e74c3c;
        }
        .alert-severity.medium {
            background-color: #f39c12;
        }
        .alert-severity.low {
            background-color: #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>零拷贝机制性能监控报告</h1>
        
        <div class="section">
            <h2>性能数据</h2>
        """
        
        # 添加性能数据图表
        for test_name, metrics in performance_data.items():
            html += f"""
            <h3>{test_name}</h3>
            """
            
            for metric_name, data_points in metrics.items():
                chart_id = f"{test_name}_{metric_name}".replace(" ", "_")
                
                html += f"""
                <div class="chart">
                    <h4>{metric_name}</h4>
                    <div class="chart-container">
                        <canvas id="{chart_id}"></canvas>
                    </div>
                    <script>
                        var ctx = document.getElementById('{chart_id}').getContext('2d');
                        var chart = new Chart(ctx, {{
                            type: 'line',
                            data: {{
                                labels: {json.dumps([d["timestamp"] for d in data_points])},
                                datasets: [{{
                                    label: '{metric_name}',
                                    data: {json.dumps([d["value"] for d in data_points])},
                                    borderColor: '#3498db',
                                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                                    fill: true,
                                    tension: 0.1
                                }}]
                            }},
                            options: {{
                                responsive: true,
                                maintainAspectRatio: false,
                                scales: {{
                                    x: {{
                                        type: 'time',
                                        time: {{
                                            unit: 'minute'
                                        }}
                                    }},
                                    y: {{
                                        beginAtZero: false
                                    }}
                                }}
                            }}
                        }});
                    </script>
                </div>
                """
        
        html += f"""
        </div>
        
        <div class="section">
            <h2>警报</h2>
        """
        
        # 添加警报
        if alerts:
            for alert in alerts:
                html += f"""
                <div class="alert">
                    <span class="alert-severity {alert['severity']}">{alert['severity']}</span>
                    <div class="alert-timestamp">{alert['timestamp']}</div>
                    <div>
                        <span class="alert-test">{alert['test_name']}</span>
                        <span class="alert-metric">{alert['metric_name']}</span>
                        性能退化
                        <span class="alert-change">{alert['change_percent']:.2f}%</span>
                    </div>
                    <div>
                        当前值: {alert['current_value']:.2f}, 基准值: {alert['baseline_value']:.2f}
                    </div>
                </div>
                """
        else:
            html += """
            <p>没有警报</p>
            """
        
        html += """
        </div>
    </div>
</body>
</html>
        """
        
        with open(output_file, "w") as f:
            f.write(html)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="零拷贝机制性能监控")
    parser.add_argument("--db", "-d", default="performance_monitor.db", help="数据库文件路径")
    parser.add_argument("--interval", "-i", type=int, default=60, help="监控间隔（秒）")
    parser.add_argument("--threshold", "-t", type=float, default=10.0, help="性能退化阈值（百分比）")
    parser.add_argument("--report", "-r", help="生成性能报告")
    parser.add_argument("--html-report", "-html", help="生成HTML性能报告")
    parser.add_argument("--daemon", action="store_true", help="作为守护进程运行")
    
    args = parser.parse_args()
    
    # 创建性能监控器
    monitor = PerformanceMonitor(args.db, args.interval, args.threshold)
    
    # 生成性能报告
    if args.report:
        monitor.generate_performance_report(args.report)
        print(f"性能报告已生成：{args.report}")
        return
    
    # 生成HTML性能报告
    if args.html_report:
        monitor.generate_html_report(args.html_report)
        print(f"HTML性能报告已生成：{args.html_report}")
        return
    
    # 启动性能监控
    try:
        monitor.start()
        
        if args.daemon:
            # 作为守护进程运行
            print(f"性能监控已作为守护进程启动，数据库：{args.db}")
            
            # 阻止主线程退出
            while True:
                time.sleep(1)
        else:
            # 交互式运行
            print("性能监控已启动，按Ctrl+C退出")
            
            while True:
                try:
                    time.sleep(1)
                except KeyboardInterrupt:
                    break
    finally:
        monitor.stop()

if __name__ == "__main__":
    main()
