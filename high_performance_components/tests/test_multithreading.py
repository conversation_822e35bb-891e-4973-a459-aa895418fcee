#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
零拷贝机制多线程测试

这个脚本测试零拷贝机制在多线程环境下的性能，包括：
1. 多线程读取
2. 多线程写入
3. 多线程读写
4. 线程安全性
"""

import os
import sys
import tempfile
import unittest
import numpy as np
import threading
import time
import queue
import random
from pathlib import Path

class TestZeroCopyMultithreading(unittest.TestCase):
    """零拷贝机制多线程测试类"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.num_threads = 4
        self.array_size = 1_000_000
        self.iterations = 10
    
    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_multithreaded_reading(self):
        """测试多线程读取"""
        # 创建共享数组
        shared_array = np.ones(self.array_size, dtype=np.float64)
        
        # 创建内存视图
        memory_view = memoryview(shared_array)
        
        # 创建线程
        threads = []
        results = queue.Queue()
        
        def reader_thread(thread_id):
            """读取线程"""
            # 创建一个新的NumPy数组，共享相同的内存
            local_array = np.frombuffer(memory_view, dtype=np.float64)
            
            # 读取数组
            for _ in range(self.iterations):
                # 随机选择一个区域读取
                start = random.randint(0, self.array_size - 1000)
                end = start + 1000
                
                # 读取数据
                data = local_array[start:end].copy()
                
                # 验证数据
                if not np.all(data == 1.0):
                    results.put((thread_id, False))
                    return
            
            results.put((thread_id, True))
        
        # 启动线程
        for i in range(self.num_threads):
            thread = threading.Thread(target=reader_thread, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待线程完成
        for thread in threads:
            thread.join()
        
        # 检查结果
        while not results.empty():
            thread_id, success = results.get()
            self.assertTrue(success, f"线程 {thread_id} 读取失败")
    
    def test_multithreaded_writing(self):
        """测试多线程写入"""
        # 创建共享数组
        shared_array = np.zeros(self.array_size, dtype=np.float64)
        
        # 创建内存视图
        memory_view = memoryview(shared_array)
        
        # 创建线程
        threads = []
        
        def writer_thread(thread_id):
            """写入线程"""
            # 创建一个新的NumPy数组，共享相同的内存
            local_array = np.frombuffer(memory_view, dtype=np.float64)
            
            # 写入数组
            for _ in range(self.iterations):
                # 随机选择一个区域写入
                start = random.randint(0, self.array_size - 1000)
                end = start + 1000
                
                # 写入数据
                local_array[start:end] = thread_id + 1
        
        # 启动线程
        for i in range(self.num_threads):
            thread = threading.Thread(target=writer_thread, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待线程完成
        for thread in threads:
            thread.join()
        
        # 验证数据
        # 注意：由于多线程写入，我们不能确定具体的值，但可以确保所有值都被写入
        self.assertTrue(np.all(shared_array > 0))
    
    def test_multithreaded_reading_writing(self):
        """测试多线程读写"""
        # 创建共享数组
        shared_array = np.zeros(self.array_size, dtype=np.float64)
        
        # 创建内存视图
        memory_view = memoryview(shared_array)
        
        # 创建线程
        threads = []
        results = queue.Queue()
        
        def reader_writer_thread(thread_id, is_writer):
            """读写线程"""
            # 创建一个新的NumPy数组，共享相同的内存
            local_array = np.frombuffer(memory_view, dtype=np.float64)
            
            for _ in range(self.iterations):
                # 随机选择一个区域
                start = random.randint(0, self.array_size - 1000)
                end = start + 1000
                
                if is_writer:
                    # 写入数据
                    local_array[start:end] = thread_id + 1
                else:
                    # 读取数据
                    data = local_array[start:end].copy()
            
            results.put((thread_id, True))
        
        # 启动线程
        for i in range(self.num_threads):
            # 一半线程写入，一半线程读取
            is_writer = i < self.num_threads // 2
            thread = threading.Thread(target=reader_writer_thread, args=(i, is_writer))
            threads.append(thread)
            thread.start()
        
        # 等待线程完成
        for thread in threads:
            thread.join()
        
        # 检查结果
        while not results.empty():
            thread_id, success = results.get()
            self.assertTrue(success, f"线程 {thread_id} 读写失败")
    
    def test_thread_safety(self):
        """测试线程安全性"""
        # 创建共享数组
        shared_array = np.zeros(1, dtype=np.int64)
        
        # 创建内存视图
        memory_view = memoryview(shared_array)
        
        # 创建线程
        threads = []
        
        def increment_thread():
            """增量线程"""
            # 创建一个新的NumPy数组，共享相同的内存
            local_array = np.frombuffer(memory_view, dtype=np.int64)
            
            # 增加计数器
            for _ in range(1000):
                # 注意：这不是线程安全的操作
                local_array[0] += 1
        
        # 启动线程
        for _ in range(self.num_threads):
            thread = threading.Thread(target=increment_thread)
            threads.append(thread)
            thread.start()
        
        # 等待线程完成
        for thread in threads:
            thread.join()
        
        # 验证数据
        # 注意：由于竞争条件，计数器的值可能小于预期值
        expected_value = self.num_threads * 1000
        actual_value = shared_array[0]
        
        print(f"预期值: {expected_value}, 实际值: {actual_value}, 差异: {expected_value - actual_value}")
        
        # 由于竞争条件，我们不能确保实际值等于预期值
        # 但我们可以确保实际值大于0且小于等于预期值
        self.assertGreater(actual_value, 0)
        self.assertLessEqual(actual_value, expected_value)
    
    def test_file_mapping_multithreading(self):
        """测试文件映射多线程"""
        # 创建文件
        file_path = os.path.join(self.temp_dir, "test.npy")
        
        # 创建数组
        array = np.ones(self.array_size, dtype=np.float64)
        
        # 保存到文件
        np.save(file_path, array)
        
        # 创建线程
        threads = []
        results = queue.Queue()
        
        def reader_thread(thread_id):
            """读取线程"""
            try:
                # 使用内存映射加载文件
                mmap_array = np.load(file_path, mmap_mode='r')
                
                # 读取数组
                for _ in range(self.iterations):
                    # 随机选择一个区域读取
                    start = random.randint(0, self.array_size - 1000)
                    end = start + 1000
                    
                    # 读取数据
                    data = mmap_array[start:end].copy()
                    
                    # 验证数据
                    if not np.all(data == 1.0):
                        results.put((thread_id, False))
                        return
                
                results.put((thread_id, True))
            except Exception as e:
                results.put((thread_id, False, str(e)))
        
        # 启动线程
        for i in range(self.num_threads):
            thread = threading.Thread(target=reader_thread, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待线程完成
        for thread in threads:
            thread.join()
        
        # 检查结果
        while not results.empty():
            result = results.get()
            if len(result) == 2:
                thread_id, success = result
                self.assertTrue(success, f"线程 {thread_id} 读取失败")
            else:
                thread_id, success, error = result
                self.assertTrue(success, f"线程 {thread_id} 读取失败: {error}")

if __name__ == "__main__":
    unittest.main()
