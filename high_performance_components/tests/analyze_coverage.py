#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
零拷贝机制测试覆盖率分析

这个脚本分析零拷贝机制的测试覆盖率，包括：
1. 代码覆盖率分析
2. 未覆盖代码识别
3. 测试覆盖率报告生成
"""

import os
import sys
import json
import argparse
import subprocess
import re
from pathlib import Path
from collections import defaultdict

def run_coverage(python_files, test_files, output_dir):
    """运行覆盖率分析"""
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 运行覆盖率分析
    cmd = [
        "coverage", "run", "--source", ",".join(python_files),
        "--omit", "*/tests/*,*/venv/*,*/env/*",
        "-m", "unittest", "discover", "-s", "tests"
    ]
    
    if test_files:
        cmd = [
            "coverage", "run", "--source", ",".join(python_files),
            "--omit", "*/tests/*,*/venv/*,*/env/*",
        ] + test_files
    
    subprocess.run(cmd, check=True)
    
    # 生成覆盖率报告
    subprocess.run(["coverage", "report", "-m"], check=True)
    
    # 生成HTML报告
    subprocess.run(["coverage", "html", "-d", os.path.join(output_dir, "html")], check=True)
    
    # 生成XML报告
    subprocess.run(["coverage", "xml", "-o", os.path.join(output_dir, "coverage.xml")], check=True)
    
    # 生成JSON报告
    subprocess.run(["coverage", "json", "-o", os.path.join(output_dir, "coverage.json")], check=True)
    
    return os.path.join(output_dir, "coverage.json")

def analyze_coverage(coverage_file):
    """分析覆盖率数据"""
    with open(coverage_file, "r") as f:
        coverage_data = json.load(f)
    
    # 总体覆盖率
    total_coverage = coverage_data["totals"]["percent_covered"]
    
    # 文件覆盖率
    file_coverage = {}
    for file_path, file_data in coverage_data["files"].items():
        file_coverage[file_path] = {
            "percent_covered": file_data["summary"]["percent_covered"],
            "missing_lines": file_data["missing_lines"],
            "excluded_lines": file_data["excluded_lines"],
        }
    
    return {
        "total_coverage": total_coverage,
        "file_coverage": file_coverage,
    }

def identify_uncovered_code(coverage_data, source_dir):
    """识别未覆盖的代码"""
    uncovered_code = {}
    
    for file_path, file_data in coverage_data["file_coverage"].items():
        if file_data["percent_covered"] < 100:
            # 读取文件内容
            with open(os.path.join(source_dir, file_path), "r") as f:
                lines = f.readlines()
            
            # 提取未覆盖的代码
            missing_lines = file_data["missing_lines"]
            uncovered_lines = []
            
            for line_num in missing_lines:
                if line_num <= len(lines):
                    line = lines[line_num - 1].rstrip()
                    uncovered_lines.append((line_num, line))
            
            uncovered_code[file_path] = uncovered_lines
    
    return uncovered_code

def analyze_uncovered_patterns(uncovered_code):
    """分析未覆盖代码的模式"""
    patterns = defaultdict(int)
    
    # 定义模式正则表达式
    pattern_regexes = {
        "error_handling": r"(except|raise|try|catch|error|exception)",
        "edge_cases": r"(if|else|elif)\s+.*?(==|!=|>|<|>=|<=|is|not|in)",
        "complex_logic": r"(and|or|not|\&\&|\|\||\!)",
        "function_definition": r"(def|fn|function)\s+\w+",
        "class_definition": r"(class|struct|enum)\s+\w+",
    }
    
    for file_path, uncovered_lines in uncovered_code.items():
        for line_num, line in uncovered_lines:
            for pattern_name, pattern_regex in pattern_regexes.items():
                if re.search(pattern_regex, line, re.IGNORECASE):
                    patterns[pattern_name] += 1
    
    return patterns

def generate_coverage_report(coverage_data, uncovered_code, uncovered_patterns, output_file):
    """生成覆盖率报告"""
    report = {
        "total_coverage": coverage_data["total_coverage"],
        "file_coverage": coverage_data["file_coverage"],
        "uncovered_code": uncovered_code,
        "uncovered_patterns": uncovered_patterns,
    }
    
    with open(output_file, "w") as f:
        json.dump(report, f, indent=2)
    
    return report

def generate_html_report(coverage_data, uncovered_code, uncovered_patterns, output_file):
    """生成HTML覆盖率报告"""
    html = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>零拷贝机制测试覆盖率报告</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .progress {
            height: 20px;
            background-color: #f5f5f5;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .progress-bar {
            height: 100%;
            background-color: #4caf50;
            border-radius: 4px;
            text-align: center;
            line-height: 20px;
            color: white;
        }
        .progress-bar.warning {
            background-color: #ff9800;
        }
        .progress-bar.danger {
            background-color: #f44336;
        }
        .file-list {
            list-style-type: none;
            padding: 0;
        }
        .file-item {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .file-name {
            font-weight: bold;
        }
        .file-coverage {
            float: right;
        }
        .uncovered-code {
            font-family: monospace;
            white-space: pre;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 4px;
            margin-top: 10px;
        }
        .pattern-list {
            list-style-type: none;
            padding: 0;
        }
        .pattern-item {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .pattern-name {
            font-weight: bold;
        }
        .pattern-count {
            float: right;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>零拷贝机制测试覆盖率报告</h1>
        
        <div class="section">
            <h2>总体覆盖率</h2>
            <div class="progress">
                <div class="progress-bar {progress_class}" style="width: {total_coverage}%">
                    {total_coverage:.2f}%
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>文件覆盖率</h2>
            <ul class="file-list">
    """.format(
        total_coverage=coverage_data["total_coverage"],
        progress_class="danger" if coverage_data["total_coverage"] < 70 else "warning" if coverage_data["total_coverage"] < 90 else ""
    )
    
    # 添加文件覆盖率
    for file_path, file_data in sorted(coverage_data["file_coverage"].items(), key=lambda x: x[1]["percent_covered"]):
        progress_class = "danger" if file_data["percent_covered"] < 70 else "warning" if file_data["percent_covered"] < 90 else ""
        
        html += f"""
                <li class="file-item">
                    <span class="file-name">{file_path}</span>
                    <span class="file-coverage">{file_data["percent_covered"]:.2f}%</span>
                    <div class="progress">
                        <div class="progress-bar {progress_class}" style="width: {file_data["percent_covered"]}%"></div>
                    </div>
        """
        
        # 添加未覆盖的代码
        if file_path in uncovered_code and uncovered_code[file_path]:
            html += f"""
                    <div class="uncovered-code">
            """
            
            for line_num, line in uncovered_code[file_path]:
                html += f"{line_num}: {line}\n"
            
            html += f"""
                    </div>
            """
        
        html += f"""
                </li>
        """
    
    html += f"""
            </ul>
        </div>
        
        <div class="section">
            <h2>未覆盖代码模式</h2>
            <ul class="pattern-list">
    """
    
    # 添加未覆盖代码模式
    for pattern_name, pattern_count in sorted(uncovered_patterns.items(), key=lambda x: x[1], reverse=True):
        html += f"""
                <li class="pattern-item">
                    <span class="pattern-name">{pattern_name}</span>
                    <span class="pattern-count">{pattern_count}</span>
                </li>
        """
    
    html += f"""
            </ul>
        </div>
    </div>
</body>
</html>
    """
    
    with open(output_file, "w") as f:
        f.write(html)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="零拷贝机制测试覆盖率分析")
    parser.add_argument("--source", "-s", nargs="+", required=True, help="源代码文件或目录")
    parser.add_argument("--tests", "-t", nargs="+", help="测试文件")
    parser.add_argument("--output-dir", "-o", default="coverage", help="输出目录")
    
    args = parser.parse_args()
    
    # 运行覆盖率分析
    coverage_file = run_coverage(args.source, args.tests, args.output_dir)
    
    # 分析覆盖率数据
    coverage_data = analyze_coverage(coverage_file)
    
    # 识别未覆盖的代码
    uncovered_code = identify_uncovered_code(coverage_data, ".")
    
    # 分析未覆盖代码的模式
    uncovered_patterns = analyze_uncovered_patterns(uncovered_code)
    
    # 生成覆盖率报告
    report = generate_coverage_report(coverage_data, uncovered_code, uncovered_patterns, os.path.join(args.output_dir, "coverage_report.json"))
    
    # 生成HTML报告
    generate_html_report(coverage_data, uncovered_code, uncovered_patterns, os.path.join(args.output_dir, "coverage_report.html"))
    
    print(f"覆盖率报告已生成：{args.output_dir}")
    print(f"总体覆盖率：{report['total_coverage']:.2f}%")
    
    # 输出未覆盖代码模式
    print("\n未覆盖代码模式：")
    for pattern_name, pattern_count in sorted(uncovered_patterns.items(), key=lambda x: x[1], reverse=True):
        print(f"  {pattern_name}: {pattern_count}")
    
    # 输出覆盖率最低的文件
    print("\n覆盖率最低的文件：")
    for file_path, file_data in sorted(coverage_data["file_coverage"].items(), key=lambda x: x[1]["percent_covered"])[:5]:
        print(f"  {file_path}: {file_data['percent_covered']:.2f}%")

if __name__ == "__main__":
    main()
