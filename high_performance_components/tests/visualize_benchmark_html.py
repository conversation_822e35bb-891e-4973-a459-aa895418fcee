#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
零拷贝机制性能基准测试结果HTML可视化

这个脚本生成HTML格式的零拷贝机制性能基准测试结果可视化，包括：
1. NumPy数组的零拷贝转换性能
2. 量子态的序列化和反序列化性能
"""

import os
import sys
import json
import argparse
from pathlib import Path

def generate_html_chart(data, title, x_label, y_label, series):
    """生成HTML图表"""
    html = f"""
    <div class="chart">
        <h3>{title}</h3>
        <div class="chart-container">
            <canvas id="{title.replace(' ', '_')}"></canvas>
        </div>
        <script>
            var ctx = document.getElementById('{title.replace(' ', '_')}').getContext('2d');
            var chart = new Chart(ctx, {{
                type: 'line',
                data: {{
                    labels: {json.dumps(list(data.keys()))},
                    datasets: [
    """
    
    for i, (name, key) in enumerate(series):
        color = f"hsl({i * 360 // len(series)}, 70%, 50%)"
        html += f"""
                        {{
                            label: '{name}',
                            data: {json.dumps([data[x][key] for x in data.keys()])},
                            borderColor: '{color}',
                            backgroundColor: '{color}',
                            fill: false,
                            tension: 0.1
                        }},
        """
    
    html += f"""
                    ]
                }},
                options: {{
                    responsive: true,
                    title: {{
                        display: true,
                        text: '{title}'
                    }},
                    scales: {{
                        x: {{
                            display: true,
                            title: {{
                                display: true,
                                text: '{x_label}'
                            }}
                        }},
                        y: {{
                            display: true,
                            title: {{
                                display: true,
                                text: '{y_label}'
                            }}
                        }}
                    }}
                }}
            }});
        </script>
    </div>
    """
    
    return html

def visualize_numpy_array_zero_copy(results):
    """可视化NumPy数组的零拷贝转换性能"""
    throughput_chart = generate_html_chart(
        results,
        "NumPy数组零拷贝转换吞吐量",
        "数组大小",
        "吞吐量 (MB/s)",
        [
            ("零拷贝", "zero_copy_throughput"),
            ("普通复制", "copy_throughput"),
        ]
    )
    
    speedup_chart = generate_html_chart(
        results,
        "NumPy数组零拷贝转换加速比",
        "数组大小",
        "加速比",
        [
            ("加速比", "speedup"),
        ]
    )
    
    return throughput_chart + speedup_chart

def visualize_quantum_state_serialization(results):
    """可视化量子态的序列化和反序列化性能"""
    throughput_chart = generate_html_chart(
        results,
        "量子态序列化和反序列化吞吐量",
        "量子比特数",
        "吞吐量 (MB/s)",
        [
            ("序列化", "serialize_throughput"),
            ("反序列化", "deserialize_throughput"),
        ]
    )
    
    time_chart = generate_html_chart(
        results,
        "量子态序列化和反序列化时间",
        "量子比特数",
        "时间 (毫秒)",
        [
            ("序列化", "serialize_time"),
            ("反序列化", "deserialize_time"),
        ]
    )
    
    # 创建维度与时间的关系图表
    dimension_time_data = {
        str(results[n_qubits]["dim"]): {
            "serialize_time": results[n_qubits]["serialize_time"] * 1000,
            "deserialize_time": results[n_qubits]["deserialize_time"] * 1000,
        }
        for n_qubits in results.keys()
    }
    
    dimension_time_chart = generate_html_chart(
        dimension_time_data,
        "量子态维度与序列化/反序列化时间的关系",
        "维度",
        "时间 (毫秒)",
        [
            ("序列化", "serialize_time"),
            ("反序列化", "deserialize_time"),
        ]
    )
    
    return throughput_chart + time_chart + dimension_time_chart

def generate_html_report(results, output_file):
    """生成HTML报告"""
    html = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>零拷贝机制性能基准测试结果</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .chart {
            margin: 20px 0;
        }
        .chart-container {
            height: 400px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>零拷贝机制性能基准测试结果</h1>
        
        <div class="section">
            <h2>NumPy数组零拷贝转换性能</h2>
            
    """
    
    if "numpy_array_zero_copy" in results:
        html += visualize_numpy_array_zero_copy(results["numpy_array_zero_copy"])
    
    html += """
        </div>
        
        <div class="section">
            <h2>量子态序列化和反序列化性能</h2>
            
    """
    
    if "quantum_state_serialization" in results:
        html += visualize_quantum_state_serialization(results["quantum_state_serialization"])
    
    html += """
        </div>
    </div>
</body>
</html>
    """
    
    with open(output_file, "w") as f:
        f.write(html)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="零拷贝机制性能基准测试结果HTML可视化")
    parser.add_argument("--input", "-i", required=True, help="输入JSON文件路径")
    parser.add_argument("--output", "-o", default="benchmark_results.html", help="输出HTML文件路径")
    
    args = parser.parse_args()
    
    # 加载结果
    with open(args.input, "r") as f:
        results = json.load(f)
    
    # 生成HTML报告
    generate_html_report(results, args.output)
    
    print(f"HTML报告已生成：{args.output}")

if __name__ == "__main__":
    main()
