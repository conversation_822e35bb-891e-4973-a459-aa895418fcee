#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
量子态物理正确性测试

这个脚本测试量子态的物理正确性，包括：
1. 归一化性质：量子态的模平方和为1
2. 密度矩阵特性：密度矩阵的迹为1和厄米性
3. 量子算子关系：泡利矩阵的反对易关系
"""

import os
import sys
import unittest
import numpy as np
from pathlib import Path

class TestQuantumPhysics(unittest.TestCase):
    """量子态物理正确性测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 定义泡利矩阵
        self.pauli_x = np.array([[0, 1], [1, 0]], dtype=np.complex128)
        self.pauli_y = np.array([[0, -1j], [1j, 0]], dtype=np.complex128)
        self.pauli_z = np.array([[1, 0], [0, -1]], dtype=np.complex128)
        self.identity = np.array([[1, 0], [0, 1]], dtype=np.complex128)
        
        # 定义常用量子态
        # |0⟩ 态
        self.state_0 = np.array([1, 0], dtype=np.complex128)
        # |1⟩ 态
        self.state_1 = np.array([0, 1], dtype=np.complex128)
        # |+⟩ 态 = (|0⟩ + |1⟩)/√2
        self.state_plus = np.array([1, 1], dtype=np.complex128) / np.sqrt(2)
        # |-⟩ 态 = (|0⟩ - |1⟩)/√2
        self.state_minus = np.array([1, -1], dtype=np.complex128) / np.sqrt(2)
        # |+i⟩ 态 = (|0⟩ + i|1⟩)/√2
        self.state_plus_i = np.array([1, 1j], dtype=np.complex128) / np.sqrt(2)
        # |-i⟩ 态 = (|0⟩ - i|1⟩)/√2
        self.state_minus_i = np.array([1, -1j], dtype=np.complex128) / np.sqrt(2)
        
        # 定义精度要求
        # 注意：我们选择1e-10作为精度标准，因为这足够检测数值误差，
        # 同时避免浮点运算的舍入误差导致的假阳性结果
        self.epsilon = 1e-10
    
    def test_state_normalization(self):
        """测试量子态归一化性质"""
        # 测试单量子比特态的归一化性质
        states = [
            self.state_0,
            self.state_1,
            self.state_plus,
            self.state_minus,
            self.state_plus_i,
            self.state_minus_i
        ]
        
        for state in states:
            # 计算模平方和
            norm_squared = np.sum(np.abs(state) ** 2)
            # 验证模平方和为1
            self.assertAlmostEqual(norm_squared, 1.0, delta=self.epsilon,
                                  msg=f"状态 {state} 的模平方和应为1，实际为 {norm_squared}")
        
        # 测试多量子比特态的归一化性质
        # 创建2量子比特Bell态 (|00⟩ + |11⟩)/√2
        bell_state = np.zeros(4, dtype=np.complex128)
        bell_state[0] = 1 / np.sqrt(2)
        bell_state[3] = 1 / np.sqrt(2)
        
        # 计算模平方和
        norm_squared = np.sum(np.abs(bell_state) ** 2)
        # 验证模平方和为1
        self.assertAlmostEqual(norm_squared, 1.0, delta=self.epsilon,
                              msg=f"Bell态的模平方和应为1，实际为 {norm_squared}")
        
        # 测试随机量子态的归一化性质
        for n_qubits in range(1, 6):  # 测试1到5个量子比特
            dim = 2 ** n_qubits
            # 创建随机量子态
            random_state = np.random.normal(0, 1, dim) + 1j * np.random.normal(0, 1, dim)
            # 归一化
            random_state = random_state / np.linalg.norm(random_state)
            
            # 计算模平方和
            norm_squared = np.sum(np.abs(random_state) ** 2)
            # 验证模平方和为1
            self.assertAlmostEqual(norm_squared, 1.0, delta=self.epsilon,
                                  msg=f"{n_qubits}量子比特随机态的模平方和应为1，实际为 {norm_squared}")
    
    def test_density_matrix_properties(self):
        """测试密度矩阵特性"""
        # 测试纯态密度矩阵
        states = [
            self.state_0,
            self.state_1,
            self.state_plus,
            self.state_minus,
            self.state_plus_i,
            self.state_minus_i
        ]
        
        for state in states:
            # 计算密度矩阵 ρ = |ψ⟩⟨ψ|
            density_matrix = np.outer(state, np.conjugate(state))
            
            # 验证迹为1
            trace = np.trace(density_matrix)
            self.assertAlmostEqual(trace, 1.0, delta=self.epsilon,
                                  msg=f"密度矩阵的迹应为1，实际为 {trace}")
            
            # 验证厄米性 (ρ† = ρ)
            hermitian_diff = np.linalg.norm(density_matrix - density_matrix.conj().T)
            self.assertAlmostEqual(hermitian_diff, 0.0, delta=self.epsilon,
                                  msg=f"密度矩阵应为厄米矩阵，差异为 {hermitian_diff}")
            
            # 验证正定性 (所有特征值非负)
            eigenvalues = np.linalg.eigvalsh(density_matrix)
            self.assertTrue(np.all(eigenvalues >= -self.epsilon),
                           msg=f"密度矩阵应为正定矩阵，特征值为 {eigenvalues}")
            
            # 验证纯态性质 (ρ² = ρ)
            purity = np.trace(density_matrix @ density_matrix)
            self.assertAlmostEqual(purity, 1.0, delta=self.epsilon,
                                  msg=f"纯态密度矩阵的纯度应为1，实际为 {purity}")
        
        # 测试混合态密度矩阵
        # 创建混合态 ρ = 0.7|0⟩⟨0| + 0.3|1⟩⟨1|
        density_matrix_0 = np.outer(self.state_0, np.conjugate(self.state_0))
        density_matrix_1 = np.outer(self.state_1, np.conjugate(self.state_1))
        mixed_density_matrix = 0.7 * density_matrix_0 + 0.3 * density_matrix_1
        
        # 验证迹为1
        trace = np.trace(mixed_density_matrix)
        self.assertAlmostEqual(trace, 1.0, delta=self.epsilon,
                              msg=f"混合态密度矩阵的迹应为1，实际为 {trace}")
        
        # 验证厄米性 (ρ† = ρ)
        hermitian_diff = np.linalg.norm(mixed_density_matrix - mixed_density_matrix.conj().T)
        self.assertAlmostEqual(hermitian_diff, 0.0, delta=self.epsilon,
                              msg=f"混合态密度矩阵应为厄米矩阵，差异为 {hermitian_diff}")
        
        # 验证正定性 (所有特征值非负)
        eigenvalues = np.linalg.eigvalsh(mixed_density_matrix)
        self.assertTrue(np.all(eigenvalues >= -self.epsilon),
                       msg=f"混合态密度矩阵应为正定矩阵，特征值为 {eigenvalues}")
        
        # 验证混合态性质 (ρ² ≠ ρ，纯度 < 1)
        purity = np.trace(mixed_density_matrix @ mixed_density_matrix)
        self.assertLess(purity, 1.0 - self.epsilon,
                       msg=f"混合态密度矩阵的纯度应小于1，实际为 {purity}")
        self.assertAlmostEqual(purity, 0.7**2 + 0.3**2, delta=self.epsilon,
                              msg=f"混合态密度矩阵的纯度计算错误，应为 {0.7**2 + 0.3**2}，实际为 {purity}")
    
    def test_pauli_matrices_properties(self):
        """测试泡利矩阵特性"""
        # 验证泡利矩阵的厄米性 (σ† = σ)
        pauli_matrices = [self.pauli_x, self.pauli_y, self.pauli_z]
        
        for i, pauli in enumerate(['X', 'Y', 'Z']):
            hermitian_diff = np.linalg.norm(pauli_matrices[i] - pauli_matrices[i].conj().T)
            self.assertAlmostEqual(hermitian_diff, 0.0, delta=self.epsilon,
                                  msg=f"泡利{pauli}矩阵应为厄米矩阵，差异为 {hermitian_diff}")
        
        # 验证泡利矩阵的酉性 (σ†σ = I)
        for i, pauli in enumerate(['X', 'Y', 'Z']):
            unitary_diff = np.linalg.norm(pauli_matrices[i].conj().T @ pauli_matrices[i] - self.identity)
            self.assertAlmostEqual(unitary_diff, 0.0, delta=self.epsilon,
                                  msg=f"泡利{pauli}矩阵应为酉矩阵，差异为 {unitary_diff}")
        
        # 验证泡利矩阵的迹为0
        for i, pauli in enumerate(['X', 'Y', 'Z']):
            trace = np.trace(pauli_matrices[i])
            self.assertAlmostEqual(trace, 0.0, delta=self.epsilon,
                                  msg=f"泡利{pauli}矩阵的迹应为0，实际为 {trace}")
        
        # 验证泡利矩阵的行列式为-1
        for i, pauli in enumerate(['X', 'Y', 'Z']):
            det = np.linalg.det(pauli_matrices[i])
            self.assertAlmostEqual(det, -1.0, delta=self.epsilon,
                                  msg=f"泡利{pauli}矩阵的行列式应为-1，实际为 {det}")
        
        # 验证泡利矩阵的反对易关系 [σᵢ, σⱼ] = 2i εᵢⱼₖ σₖ
        # [σₓ, σᵧ] = 2i σᵦ
        commutator_xy = self.pauli_x @ self.pauli_y - self.pauli_y @ self.pauli_x
        expected_xy = 2j * self.pauli_z
        diff_xy = np.linalg.norm(commutator_xy - expected_xy)
        self.assertAlmostEqual(diff_xy, 0.0, delta=self.epsilon,
                              msg=f"[σₓ, σᵧ] 应等于 2i σᵦ，差异为 {diff_xy}")
        
        # [σᵧ, σᵦ] = 2i σₓ
        commutator_yz = self.pauli_y @ self.pauli_z - self.pauli_z @ self.pauli_y
        expected_yz = 2j * self.pauli_x
        diff_yz = np.linalg.norm(commutator_yz - expected_yz)
        self.assertAlmostEqual(diff_yz, 0.0, delta=self.epsilon,
                              msg=f"[σᵧ, σᵦ] 应等于 2i σₓ，差异为 {diff_yz}")
        
        # [σᵦ, σₓ] = 2i σᵧ
        commutator_zx = self.pauli_z @ self.pauli_x - self.pauli_x @ self.pauli_z
        expected_zx = 2j * self.pauli_y
        diff_zx = np.linalg.norm(commutator_zx - expected_zx)
        self.assertAlmostEqual(diff_zx, 0.0, delta=self.epsilon,
                              msg=f"[σᵦ, σₓ] 应等于 2i σᵧ，差异为 {diff_zx}")
    
    def test_quantum_measurement(self):
        """测试量子测量"""
        # 测试在计算基底上测量 |0⟩ 态
        prob_0_in_0 = np.abs(np.vdot(self.state_0, self.state_0)) ** 2
        self.assertAlmostEqual(prob_0_in_0, 1.0, delta=self.epsilon,
                              msg=f"|0⟩态在|0⟩基底上的测量概率应为1，实际为 {prob_0_in_0}")
        
        prob_1_in_0 = np.abs(np.vdot(self.state_1, self.state_0)) ** 2
        self.assertAlmostEqual(prob_1_in_0, 0.0, delta=self.epsilon,
                              msg=f"|0⟩态在|1⟩基底上的测量概率应为0，实际为 {prob_1_in_0}")
        
        # 测试在计算基底上测量 |+⟩ 态
        prob_0_in_plus = np.abs(np.vdot(self.state_0, self.state_plus)) ** 2
        self.assertAlmostEqual(prob_0_in_plus, 0.5, delta=self.epsilon,
                              msg=f"|+⟩态在|0⟩基底上的测量概率应为0.5，实际为 {prob_0_in_plus}")
        
        prob_1_in_plus = np.abs(np.vdot(self.state_1, self.state_plus)) ** 2
        self.assertAlmostEqual(prob_1_in_plus, 0.5, delta=self.epsilon,
                              msg=f"|+⟩态在|1⟩基底上的测量概率应为0.5，实际为 {prob_1_in_plus}")
        
        # 验证测量概率之和为1
        self.assertAlmostEqual(prob_0_in_plus + prob_1_in_plus, 1.0, delta=self.epsilon,
                              msg=f"测量概率之和应为1，实际为 {prob_0_in_plus + prob_1_in_plus}")

if __name__ == "__main__":
    unittest.main()
