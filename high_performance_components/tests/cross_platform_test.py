#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
零拷贝机制跨平台测试

这个脚本在不同的操作系统和硬件平台上测试零拷贝机制，包括：
1. 操作系统兼容性测试
2. 硬件平台兼容性测试
3. 编译器兼容性测试
4. 内存对齐测试
5. 大小端测试
"""

import os
import sys
import json
import argparse
import platform
import subprocess
import tempfile
import numpy as np
from pathlib import Path

def detect_platform():
    """检测平台信息"""
    system = platform.system()
    machine = platform.machine()
    processor = platform.processor()
    python_version = platform.python_version()
    python_implementation = platform.python_implementation()
    
    # 检测CPU特性
    cpu_features = {}
    
    if system == "Linux":
        try:
            with open("/proc/cpuinfo", "r") as f:
                cpuinfo = f.read()
            
            for line in cpuinfo.split("\n"):
                if "flags" in line or "Features" in line:
                    features = line.split(": ")[1].split()
                    for feature in features:
                        cpu_features[feature] = True
        except:
            pass
    elif system == "Darwin":
        try:
            result = subprocess.run(["sysctl", "-a"], capture_output=True, text=True)
            if result.returncode == 0:
                for line in result.stdout.split("\n"):
                    if "hw.optional." in line:
                        feature = line.split(": ")
                        if len(feature) == 2:
                            cpu_features[feature[0].replace("hw.optional.", "")] = feature[1] == "1"
        except:
            pass
    elif system == "Windows":
        try:
            import ctypes
            
            # 检测AVX支持
            def is_avx_supported():
                try:
                    # 检查是否支持CPUID指令
                    ctypes.windll.kernel32.IsProcessorFeaturePresent(10)  # PF_XMMI_INSTRUCTIONS_AVAILABLE
                    return True
                except:
                    return False
            
            cpu_features["avx"] = is_avx_supported()
        except:
            pass
    
    # 检测NumPy配置
    numpy_config = {}
    
    try:
        numpy_config["version"] = np.__version__
        numpy_config["has_svml"] = bool(np.__config__.blas_mkl_info)
        numpy_config["simd"] = hasattr(np, "__SIMD_ENABLED__") and np.__SIMD_ENABLED__
    except:
        pass
    
    # 检测Arrow配置
    arrow_config = {}
    
    try:
        import pyarrow as pa
        
        arrow_config["version"] = pa.__version__
        arrow_config["build_type"] = pa.build_info.build_type
        arrow_config["compiler_id"] = pa.build_info.compiler_id
        arrow_config["compiler_version"] = pa.build_info.compiler_version
        arrow_config["flags"] = pa.build_info.compiler_flags
    except:
        pass
    
    return {
        "system": system,
        "machine": machine,
        "processor": processor,
        "python_version": python_version,
        "python_implementation": python_implementation,
        "cpu_features": cpu_features,
        "numpy_config": numpy_config,
        "arrow_config": arrow_config,
    }

def test_memory_alignment():
    """测试内存对齐"""
    results = {}
    
    # 测试不同对齐的数组
    for alignment in [1, 2, 4, 8, 16, 32, 64, 128]:
        # 创建对齐的数组
        array = np.ones(1000, dtype=np.float64)
        aligned_array = np.zeros(1000 + alignment, dtype=np.float64)
        offset = (alignment - aligned_array.ctypes.data % alignment) % alignment
        aligned_view = aligned_array[offset:offset + 1000]
        aligned_view[:] = 1.0
        
        # 验证对齐
        is_aligned = aligned_view.ctypes.data % alignment == 0
        
        # 测量性能
        iterations = 1000
        
        # 测量未对齐数组的性能
        start_time = time.time()
        for _ in range(iterations):
            result = array.sum()
        end_time = time.time()
        unaligned_time = (end_time - start_time) / iterations
        
        # 测量对齐数组的性能
        start_time = time.time()
        for _ in range(iterations):
            result = aligned_view.sum()
        end_time = time.time()
        aligned_time = (end_time - start_time) / iterations
        
        # 计算性能提升
        speedup = unaligned_time / aligned_time
        
        results[alignment] = {
            "is_aligned": is_aligned,
            "unaligned_time": unaligned_time,
            "aligned_time": aligned_time,
            "speedup": speedup,
        }
    
    return results

def test_endianness():
    """测试大小端"""
    results = {}
    
    # 检测系统大小端
    system_is_little_endian = sys.byteorder == "little"
    
    # 创建不同大小端的数组
    array_little = np.ones(1000, dtype=np.float64)
    array_big = np.ones(1000, dtype=np.float64).byteswap()
    
    # 验证大小端
    is_little_endian = array_little.dtype.byteorder in ["<", "="] and system_is_little_endian
    is_big_endian = array_big.dtype.byteorder == ">"
    
    # 测量性能
    iterations = 1000
    
    # 测量小端数组的性能
    start_time = time.time()
    for _ in range(iterations):
        result = array_little.sum()
    end_time = time.time()
    little_endian_time = (end_time - start_time) / iterations
    
    # 测量大端数组的性能
    start_time = time.time()
    for _ in range(iterations):
        result = array_big.sum()
    end_time = time.time()
    big_endian_time = (end_time - start_time) / iterations
    
    # 计算性能差异
    speedup = little_endian_time / big_endian_time
    
    results = {
        "system_is_little_endian": system_is_little_endian,
        "array_is_little_endian": is_little_endian,
        "array_is_big_endian": is_big_endian,
        "little_endian_time": little_endian_time,
        "big_endian_time": big_endian_time,
        "speedup": speedup,
    }
    
    return results

def test_compiler_compatibility():
    """测试编译器兼容性"""
    results = {}
    
    # 检测C编译器
    c_compiler = None
    try:
        result = subprocess.run(["cc", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            c_compiler = result.stdout.split("\n")[0]
    except:
        pass
    
    # 检测C++编译器
    cpp_compiler = None
    try:
        result = subprocess.run(["c++", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            cpp_compiler = result.stdout.split("\n")[0]
    except:
        pass
    
    # 检测Rust编译器
    rust_compiler = None
    try:
        result = subprocess.run(["rustc", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            rust_compiler = result.stdout.strip()
    except:
        pass
    
    # 检测LLVM版本
    llvm_version = None
    try:
        result = subprocess.run(["llvm-config", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            llvm_version = result.stdout.strip()
    except:
        pass
    
    results = {
        "c_compiler": c_compiler,
        "cpp_compiler": cpp_compiler,
        "rust_compiler": rust_compiler,
        "llvm_version": llvm_version,
    }
    
    return results

def test_hardware_compatibility():
    """测试硬件兼容性"""
    results = {}
    
    # 检测CPU核心数
    cpu_count = os.cpu_count()
    
    # 检测内存大小
    memory_size = None
    if platform.system() == "Linux":
        try:
            with open("/proc/meminfo", "r") as f:
                for line in f:
                    if "MemTotal" in line:
                        memory_size = int(line.split()[1]) * 1024  # 转换为字节
                        break
        except:
            pass
    elif platform.system() == "Darwin":
        try:
            result = subprocess.run(["sysctl", "-n", "hw.memsize"], capture_output=True, text=True)
            if result.returncode == 0:
                memory_size = int(result.stdout.strip())
        except:
            pass
    elif platform.system() == "Windows":
        try:
            import ctypes
            
            kernel32 = ctypes.windll.kernel32
            c_ulonglong = ctypes.c_ulonglong
            
            class MEMORYSTATUSEX(ctypes.Structure):
                _fields_ = [
                    ("dwLength", ctypes.c_ulong),
                    ("dwMemoryLoad", ctypes.c_ulong),
                    ("ullTotalPhys", c_ulonglong),
                    ("ullAvailPhys", c_ulonglong),
                    ("ullTotalPageFile", c_ulonglong),
                    ("ullAvailPageFile", c_ulonglong),
                    ("ullTotalVirtual", c_ulonglong),
                    ("ullAvailVirtual", c_ulonglong),
                    ("ullAvailExtendedVirtual", c_ulonglong),
                ]
                
                def __init__(self):
                    self.dwLength = ctypes.sizeof(self)
                    super(MEMORYSTATUSEX, self).__init__()
            
            stat = MEMORYSTATUSEX()
            kernel32.GlobalMemoryStatusEx(ctypes.byref(stat))
            memory_size = stat.ullTotalPhys
        except:
            pass
    
    # 检测GPU
    gpu_info = None
    if platform.system() == "Linux":
        try:
            result = subprocess.run(["nvidia-smi", "--query-gpu=name,memory.total,driver_version", "--format=csv,noheader"], capture_output=True, text=True)
            if result.returncode == 0:
                gpu_info = result.stdout.strip()
        except:
            pass
    elif platform.system() == "Darwin":
        try:
            result = subprocess.run(["system_profiler", "SPDisplaysDataType"], capture_output=True, text=True)
            if result.returncode == 0:
                gpu_info = result.stdout.strip()
        except:
            pass
    elif platform.system() == "Windows":
        try:
            result = subprocess.run(["wmic", "path", "win32_VideoController", "get", "name,AdapterRAM,DriverVersion"], capture_output=True, text=True)
            if result.returncode == 0:
                gpu_info = result.stdout.strip()
        except:
            pass
    
    results = {
        "cpu_count": cpu_count,
        "memory_size": memory_size,
        "gpu_info": gpu_info,
    }
    
    return results

def run_cross_platform_tests():
    """运行跨平台测试"""
    results = {}
    
    # 检测平台信息
    results["platform"] = detect_platform()
    
    # 测试内存对齐
    try:
        import time
        results["memory_alignment"] = test_memory_alignment()
    except Exception as e:
        results["memory_alignment"] = {"error": str(e)}
    
    # 测试大小端
    try:
        import time
        results["endianness"] = test_endianness()
    except Exception as e:
        results["endianness"] = {"error": str(e)}
    
    # 测试编译器兼容性
    try:
        results["compiler_compatibility"] = test_compiler_compatibility()
    except Exception as e:
        results["compiler_compatibility"] = {"error": str(e)}
    
    # 测试硬件兼容性
    try:
        results["hardware_compatibility"] = test_hardware_compatibility()
    except Exception as e:
        results["hardware_compatibility"] = {"error": str(e)}
    
    return results

def generate_cross_platform_report(results, output_file):
    """生成跨平台测试报告"""
    with open(output_file, "w") as f:
        json.dump(results, f, indent=2)
    
    return results

def generate_html_report(results, output_file):
    """生成HTML跨平台测试报告"""
    html = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>零拷贝机制跨平台测试报告</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .info {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .info-name {
            font-weight: bold;
        }
        .info-value {
            float: right;
        }
        .test-result {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .test-name {
            font-weight: bold;
        }
        .test-value {
            float: right;
        }
        .test-value.success {
            color: #27ae60;
        }
        .test-value.warning {
            color: #f39c12;
        }
        .test-value.error {
            color: #e74c3c;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>零拷贝机制跨平台测试报告</h1>
        
        <div class="section">
            <h2>平台信息</h2>
    """
    
    platform_info = results["platform"]
    
    html += f"""
            <div class="info">
                <span class="info-name">操作系统</span>
                <span class="info-value">{platform_info["system"]}</span>
            </div>
            <div class="info">
                <span class="info-name">机器架构</span>
                <span class="info-value">{platform_info["machine"]}</span>
            </div>
            <div class="info">
                <span class="info-name">处理器</span>
                <span class="info-value">{platform_info["processor"]}</span>
            </div>
            <div class="info">
                <span class="info-name">Python版本</span>
                <span class="info-value">{platform_info["python_version"]}</span>
            </div>
            <div class="info">
                <span class="info-name">Python实现</span>
                <span class="info-value">{platform_info["python_implementation"]}</span>
            </div>
    """
    
    if "numpy_config" in platform_info and platform_info["numpy_config"]:
        numpy_config = platform_info["numpy_config"]
        html += f"""
            <div class="info">
                <span class="info-name">NumPy版本</span>
                <span class="info-value">{numpy_config.get("version", "未知")}</span>
            </div>
            <div class="info">
                <span class="info-name">NumPy SIMD支持</span>
                <span class="info-value">{numpy_config.get("simd", False)}</span>
            </div>
        """
    
    if "arrow_config" in platform_info and platform_info["arrow_config"]:
        arrow_config = platform_info["arrow_config"]
        html += f"""
            <div class="info">
                <span class="info-name">Arrow版本</span>
                <span class="info-value">{arrow_config.get("version", "未知")}</span>
            </div>
            <div class="info">
                <span class="info-name">Arrow构建类型</span>
                <span class="info-value">{arrow_config.get("build_type", "未知")}</span>
            </div>
            <div class="info">
                <span class="info-name">Arrow编译器</span>
                <span class="info-value">{arrow_config.get("compiler_id", "未知")} {arrow_config.get("compiler_version", "")}</span>
            </div>
        """
    
    html += f"""
        </div>
        
        <div class="section">
            <h2>内存对齐测试</h2>
    """
    
    if "memory_alignment" in results and "error" not in results["memory_alignment"]:
        memory_alignment = results["memory_alignment"]
        
        for alignment, data in memory_alignment.items():
            html += f"""
            <div class="test-result">
                <span class="test-name">对齐: {alignment}字节</span>
                <span class="test-value success">{data["speedup"]:.2f}x</span>
                <div>
                    <p>对齐状态: {"已对齐" if data["is_aligned"] else "未对齐"}</p>
                    <p>未对齐时间: {data["unaligned_time"]*1000:.3f}毫秒</p>
                    <p>对齐时间: {data["aligned_time"]*1000:.3f}毫秒</p>
                </div>
            </div>
            """
    else:
        html += f"""
            <div class="test-result">
                <span class="test-name">内存对齐测试</span>
                <span class="test-value error">失败</span>
                <div>
                    <p>错误: {results.get("memory_alignment", {}).get("error", "未知错误")}</p>
                </div>
            </div>
        """
    
    html += f"""
        </div>
        
        <div class="section">
            <h2>大小端测试</h2>
    """
    
    if "endianness" in results and "error" not in results["endianness"]:
        endianness = results["endianness"]
        
        html += f"""
            <div class="test-result">
                <span class="test-name">系统大小端</span>
                <span class="test-value">{"小端" if endianness["system_is_little_endian"] else "大端"}</span>
            </div>
            <div class="test-result">
                <span class="test-name">数组大小端</span>
                <span class="test-value">{"小端" if endianness["array_is_little_endian"] else "大端"}</span>
            </div>
            <div class="test-result">
                <span class="test-name">性能比较</span>
                <span class="test-value success">{endianness["speedup"]:.2f}x</span>
                <div>
                    <p>小端时间: {endianness["little_endian_time"]*1000:.3f}毫秒</p>
                    <p>大端时间: {endianness["big_endian_time"]*1000:.3f}毫秒</p>
                </div>
            </div>
        """
    else:
        html += f"""
            <div class="test-result">
                <span class="test-name">大小端测试</span>
                <span class="test-value error">失败</span>
                <div>
                    <p>错误: {results.get("endianness", {}).get("error", "未知错误")}</p>
                </div>
            </div>
        """
    
    html += f"""
        </div>
        
        <div class="section">
            <h2>编译器兼容性测试</h2>
    """
    
    if "compiler_compatibility" in results and "error" not in results["compiler_compatibility"]:
        compiler_compatibility = results["compiler_compatibility"]
        
        html += f"""
            <div class="test-result">
                <span class="test-name">C编译器</span>
                <span class="test-value">{compiler_compatibility.get("c_compiler", "未检测到")}</span>
            </div>
            <div class="test-result">
                <span class="test-name">C++编译器</span>
                <span class="test-value">{compiler_compatibility.get("cpp_compiler", "未检测到")}</span>
            </div>
            <div class="test-result">
                <span class="test-name">Rust编译器</span>
                <span class="test-value">{compiler_compatibility.get("rust_compiler", "未检测到")}</span>
            </div>
            <div class="test-result">
                <span class="test-name">LLVM版本</span>
                <span class="test-value">{compiler_compatibility.get("llvm_version", "未检测到")}</span>
            </div>
        """
    else:
        html += f"""
            <div class="test-result">
                <span class="test-name">编译器兼容性测试</span>
                <span class="test-value error">失败</span>
                <div>
                    <p>错误: {results.get("compiler_compatibility", {}).get("error", "未知错误")}</p>
                </div>
            </div>
        """
    
    html += f"""
        </div>
        
        <div class="section">
            <h2>硬件兼容性测试</h2>
    """
    
    if "hardware_compatibility" in results and "error" not in results["hardware_compatibility"]:
        hardware_compatibility = results["hardware_compatibility"]
        
        html += f"""
            <div class="test-result">
                <span class="test-name">CPU核心数</span>
                <span class="test-value">{hardware_compatibility.get("cpu_count", "未检测到")}</span>
            </div>
            <div class="test-result">
                <span class="test-name">内存大小</span>
                <span class="test-value">{hardware_compatibility.get("memory_size", "未检测到") and f"{hardware_compatibility['memory_size'] / (1024**3):.2f} GB" or "未检测到"}</span>
            </div>
            <div class="test-result">
                <span class="test-name">GPU信息</span>
                <span class="test-value">{hardware_compatibility.get("gpu_info", "未检测到")}</span>
            </div>
        """
    else:
        html += f"""
            <div class="test-result">
                <span class="test-name">硬件兼容性测试</span>
                <span class="test-value error">失败</span>
                <div>
                    <p>错误: {results.get("hardware_compatibility", {}).get("error", "未知错误")}</p>
                </div>
            </div>
        """
    
    html += f"""
        </div>
    </div>
</body>
</html>
    """
    
    with open(output_file, "w") as f:
        f.write(html)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="零拷贝机制跨平台测试")
    parser.add_argument("--output-dir", "-o", default="cross_platform_tests", help="输出目录")
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 运行跨平台测试
    results = run_cross_platform_tests()
    
    # 生成跨平台测试报告
    report_file = os.path.join(args.output_dir, "cross_platform_report.json")
    generate_cross_platform_report(results, report_file)
    
    # 生成HTML跨平台测试报告
    html_report_file = os.path.join(args.output_dir, "cross_platform_report.html")
    generate_html_report(results, html_report_file)
    
    print(f"跨平台测试报告已生成：{report_file}")
    print(f"HTML跨平台测试报告已生成：{html_report_file}")

if __name__ == "__main__":
    main()
