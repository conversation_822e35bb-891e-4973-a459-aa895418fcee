#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Python-Rust互操作测试

这个模块测试Python和Rust之间的数据交换，验证零拷贝机制在跨语言场景下的正确性。
"""

import os
import sys
import tempfile
import unittest
import numpy as np
import time
import threading
import random
from pathlib import Path

# 导入arrow_integration模块
# 注意：在实际运行测试前，需要先构建并安装arrow_integration
# 这里假设模块已经安装
try:
    import arrow_integration
    from arrow_integration.zero_copy import (
        py_init_shared_memory_manager,
        py_create_shared_memory_region,
        py_open_shared_memory_region,
        py_remove_shared_memory_region,
        py_list_shared_memory_regions,
        py_write_to_shared_memory,
        py_read_from_shared_memory,
        py_create_mapped_file,
        py_open_mapped_file,
        py_write_to_mapped_file,
        py_read_from_mapped_file,
        py_register_buffer,
        py_get_buffer,
        py_remove_buffer,
        py_register_array,
        py_get_array,
        py_remove_array,
        py_get_data_accessor_stats,
        py_clear_data_accessor,
        py_allocate_buffer,
        py_deallocate_buffer,
        py_get_memory_pool_stats,
        py_clear_memory_pool,
        py_shrink_memory_pool,
        py_create_sender_session,
        py_create_receiver_session,
        py_send_chunk,
        py_receive_chunk,
        py_get_session_status,
        py_get_session_progress,
        py_get_large_transfer_stats,
        py_buffer_to_arrow,
        py_array_to_arrow
    )
    from arrow_integration.types import (
        py_create_quantum_state,
        py_create_fractal_structure,
        py_create_holographic_data
    )
    ARROW_INTEGRATION_AVAILABLE = True
except ImportError:
    ARROW_INTEGRATION_AVAILABLE = False
    print("警告：arrow_integration模块不可用，跳过测试")

@unittest.skipIf(not ARROW_INTEGRATION_AVAILABLE, "arrow_integration模块不可用")
class TestPythonRustInterop(unittest.TestCase):
    """Python-Rust互操作测试类"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        py_init_shared_memory_manager(self.temp_dir)
    
    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_shared_memory_python_to_rust(self):
        """测试从Python写入共享内存，然后在Rust中读取"""
        # 创建共享内存区域
        region_name = "python_to_rust"
        region_size = 1024
        py_create_shared_memory_region(region_name, region_size)
        
        # 创建测试数据
        test_data = bytes([i % 256 for i in range(512)])
        
        # 写入数据
        bytes_written = py_write_to_shared_memory(region_name, 0, test_data)
        self.assertEqual(bytes_written, len(test_data))
        
        # 读取数据（通过Python API，但内部调用Rust）
        read_data = py_read_from_shared_memory(region_name, 0, len(test_data))
        self.assertEqual(read_data, test_data)
        
        # 移除共享内存区域
        py_remove_shared_memory_region(region_name)
    
    def test_memory_mapping_python_to_rust(self):
        """测试从Python写入内存映射文件，然后在Rust中读取"""
        # 创建内存映射文件
        file_path = os.path.join(self.temp_dir, "python_to_rust.bin")
        file_size = 1024
        py_create_mapped_file(file_path, file_size)
        
        # 创建测试数据
        test_data = bytes([i % 256 for i in range(512)])
        
        # 写入数据
        bytes_written = py_write_to_mapped_file(file_path, 0, test_data)
        self.assertEqual(bytes_written, len(test_data))
        
        # 读取数据（通过Python API，但内部调用Rust）
        read_data = py_read_from_mapped_file(file_path, 0, len(test_data))
        self.assertEqual(read_data, test_data)
    
    def test_numpy_array_zero_copy(self):
        """测试NumPy数组的零拷贝转换"""
        # 创建NumPy数组
        array = np.array([1, 2, 3, 4, 5], dtype=np.int32)
        
        # 转换为Arrow数组（零拷贝）
        arrow_array = py_array_to_arrow(array)
        
        # 验证转换结果
        self.assertIsNotNone(arrow_array)
        
        # 在实际实现中，我们会验证数据内容
        # 这里只是示例
    
    def test_quantum_state_interop(self):
        """测试量子态的Python-Rust互操作"""
        # 创建量子态
        # |ψ⟩ = (|0⟩ + |1⟩)/√2
        state = [complex(1/np.sqrt(2), 0), complex(1/np.sqrt(2), 0)]
        dimensions = [2]  # 2维希尔伯特空间
        
        # 创建量子态数组
        quantum_array = py_create_quantum_state(state, dimensions)
        
        # 创建共享内存区域
        region_name = "quantum_state_test"
        region_size = 1024
        py_create_shared_memory_region(region_name, region_size)
        
        # 序列化量子态
        serialized = quantum_array.serialize()
        
        # 写入共享内存
        py_write_to_shared_memory(region_name, 0, serialized)
        
        # 从共享内存读取
        read_data = py_read_from_shared_memory(region_name, 0, len(serialized))
        
        # 反序列化量子态
        deserialized_array = quantum_array.deserialize(read_data)
        
        # 验证量子态保持不变
        deserialized_state = deserialized_array.state
        
        # 验证维度保持不变
        self.assertEqual(deserialized_array.dimensions, dimensions)
        
        # 验证状态向量保持不变
        for i in range(len(state)):
            self.assertAlmostEqual(state[i].real, deserialized_state[i].real, places=10)
            self.assertAlmostEqual(state[i].imag, deserialized_state[i].imag, places=10)
        
        # 移除共享内存区域
        py_remove_shared_memory_region(region_name)
    
    def test_fractal_structure_interop(self):
        """测试分形结构的Python-Rust互操作"""
        # 创建分形结构
        nodes = [
            {"id": 0, "position": [0.0, 0.0], "properties": {}},
            {"id": 1, "position": [1.0, 0.0], "properties": {}},
            {"id": 2, "position": [0.5, 0.866], "properties": {}}
        ]
        
        connections = [
            {"source": 0, "target": 1, "weight": 1.0, "properties": {}},
            {"source": 1, "target": 2, "weight": 1.0, "properties": {}},
            {"source": 2, "target": 0, "weight": 1.0, "properties": {}}
        ]
        
        metadata = {}
        dimension = 2
        depth = 1
        branching_factor = 3
        
        # 创建分形结构数组
        fractal_array = py_create_fractal_structure(
            nodes, connections, metadata, dimension, depth, branching_factor
        )
        
        # 创建内存映射文件
        file_path = os.path.join(self.temp_dir, "fractal_structure.bin")
        file_size = 1024
        py_create_mapped_file(file_path, file_size)
        
        # 序列化分形结构
        serialized = fractal_array.serialize()
        
        # 写入内存映射文件
        py_write_to_mapped_file(file_path, 0, serialized)
        
        # 从内存映射文件读取
        read_data = py_read_from_mapped_file(file_path, 0, len(serialized))
        
        # 反序列化分形结构
        deserialized_array = fractal_array.deserialize(read_data)
        
        # 验证分形结构保持不变
        deserialized_nodes = deserialized_array.nodes
        deserialized_connections = deserialized_array.connections
        
        # 验证节点数量保持不变
        self.assertEqual(len(deserialized_nodes), len(nodes))
        
        # 验证连接数量保持不变
        self.assertEqual(len(deserialized_connections), len(connections))
    
    def test_holographic_data_interop(self):
        """测试全息数据的Python-Rust互操作"""
        # 创建原始数据
        n = 16
        original_data = [complex(random.random(), random.random()) for _ in range(n)]
        
        # 创建编码矩阵（简化版）
        encoding_matrix = [[complex(random.random(), random.random()) for _ in range(n)] for _ in range(n)]
        
        # 创建全息数据
        encoding_type = "fourier"
        original_dimensions = [n]
        holographic_dimensions = [n]
        metadata = {}
        
        # 创建全息数据数组
        holographic_array = py_create_holographic_data(
            original_data, encoding_matrix, encoding_type,
            original_dimensions, holographic_dimensions, metadata
        )
        
        # 创建共享内存区域
        region_name = "holographic_data_test"
        region_size = 4096
        py_create_shared_memory_region(region_name, region_size)
        
        # 序列化全息数据
        serialized = holographic_array.serialize()
        
        # 写入共享内存
        py_write_to_shared_memory(region_name, 0, serialized)
        
        # 从共享内存读取
        read_data = py_read_from_shared_memory(region_name, 0, len(serialized))
        
        # 反序列化全息数据
        deserialized_array = holographic_array.deserialize(read_data)
        
        # 验证全息数据保持不变
        self.assertEqual(deserialized_array.encoding_type, encoding_type)
        self.assertEqual(deserialized_array.original_dimensions, original_dimensions)
        self.assertEqual(deserialized_array.holographic_dimensions, holographic_dimensions)
        
        # 移除共享内存区域
        py_remove_shared_memory_region(region_name)
    
    def test_large_transfer_interop(self):
        """测试大数据传输的Python-Rust互操作"""
        # 创建大数据
        total_size = 1024 * 1024  # 1MB
        chunk_size = 64 * 1024    # 64KB
        
        # 创建随机数据
        data = bytes([random.randint(0, 255) for _ in range(total_size)])
        
        # 创建发送和接收会话
        sender_id = py_create_sender_session("interop_test", total_size, chunk_size)
        receiver_id = py_create_receiver_session("interop_test", chunk_size)
        
        # 在单独线程中发送数据
        def send_data():
            offset = 0
            while offset < total_size:
                end = min(offset + chunk_size, total_size)
                chunk = data[offset:end]
                bytes_sent = py_send_chunk(sender_id, chunk)
                offset += bytes_sent
                time.sleep(0.001)  # 模拟网络延迟
        
        send_thread = threading.Thread(target=send_data)
        send_thread.start()
        
        # 在主线程中接收数据
        received_data = bytearray()
        while len(received_data) < total_size:
            chunk = py_receive_chunk(receiver_id)
            if chunk:
                received_data.extend(chunk)
            time.sleep(0.002)  # 模拟处理延迟
        
        # 等待发送线程完成
        send_thread.join()
        
        # 验证数据完整性
        self.assertEqual(len(received_data), total_size)
        self.assertEqual(bytes(received_data), data)
        
        # 检查会话状态
        self.assertEqual(py_get_session_status(sender_id), "completed")
        self.assertEqual(py_get_session_status(receiver_id), "completed")
        self.assertEqual(py_get_session_progress(sender_id), 100.0)
        self.assertEqual(py_get_session_progress(receiver_id), 100.0)

if __name__ == "__main__":
    unittest.main()
