#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
端到端集成测试

这个模块测试零拷贝机制的端到端流程，包括数据创建、序列化、传输和反序列化。
"""

import os
import sys
import tempfile
import unittest
import numpy as np
import time
import threading
import random
from pathlib import Path

# 导入arrow_integration模块
# 注意：在实际运行测试前，需要先构建并安装arrow_integration
# 这里假设模块已经安装
try:
    import arrow_integration
    from arrow_integration.zero_copy import (
        py_init_shared_memory_manager,
        py_create_shared_memory_region,
        py_open_shared_memory_region,
        py_remove_shared_memory_region,
        py_list_shared_memory_regions,
        py_write_to_shared_memory,
        py_read_from_shared_memory,
        py_create_mapped_file,
        py_open_mapped_file,
        py_write_to_mapped_file,
        py_read_from_mapped_file,
        py_register_buffer,
        py_get_buffer,
        py_remove_buffer,
        py_register_array,
        py_get_array,
        py_remove_array,
        py_get_data_accessor_stats,
        py_clear_data_accessor,
        py_allocate_buffer,
        py_deallocate_buffer,
        py_get_memory_pool_stats,
        py_clear_memory_pool,
        py_shrink_memory_pool,
        py_create_sender_session,
        py_create_receiver_session,
        py_send_chunk,
        py_receive_chunk,
        py_get_session_status,
        py_get_session_progress,
        py_get_large_transfer_stats,
        py_buffer_to_arrow,
        py_array_to_arrow
    )
    from arrow_integration.types import (
        py_create_quantum_state,
        py_create_fractal_structure,
        py_create_holographic_data
    )
    ARROW_INTEGRATION_AVAILABLE = True
except ImportError:
    ARROW_INTEGRATION_AVAILABLE = False
    print("警告：arrow_integration模块不可用，跳过测试")

@unittest.skipIf(not ARROW_INTEGRATION_AVAILABLE, "arrow_integration模块不可用")
class TestEndToEnd(unittest.TestCase):
    """端到端集成测试类"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        py_init_shared_memory_manager(self.temp_dir)
    
    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_numpy_to_arrow_to_shared_memory_to_file(self):
        """测试NumPy数组到Arrow到共享内存到文件的端到端流程"""
        # 1. 创建NumPy数组
        array_size = 1000
        numpy_array = np.random.rand(array_size).astype(np.float64)
        
        # 2. 转换为Arrow数组
        arrow_array = py_array_to_arrow(numpy_array)
        
        # 3. 注册Arrow数组
        array_id = py_register_array(arrow_array)
        
        # 4. 创建共享内存区域
        region_name = "numpy_to_arrow_test"
        region_size = array_size * 8 * 2  # 假设每个元素8字节，预留2倍空间
        py_create_shared_memory_region(region_name, region_size)
        
        # 5. 获取Arrow数组
        retrieved_array = py_get_array(array_id)
        
        # 6. 序列化Arrow数组
        serialized = retrieved_array.serialize()
        
        # 7. 写入共享内存
        py_write_to_shared_memory(region_name, 0, serialized)
        
        # 8. 从共享内存读取
        read_data = py_read_from_shared_memory(region_name, 0, len(serialized))
        
        # 9. 创建内存映射文件
        file_path = os.path.join(self.temp_dir, "arrow_array.bin")
        py_create_mapped_file(file_path, len(read_data))
        
        # 10. 写入内存映射文件
        py_write_to_mapped_file(file_path, 0, read_data)
        
        # 11. 从内存映射文件读取
        file_data = py_read_from_mapped_file(file_path, 0, len(read_data))
        
        # 12. 反序列化Arrow数组
        deserialized_array = retrieved_array.deserialize(file_data)
        
        # 13. 转换回NumPy数组
        result_array = deserialized_array.to_numpy()
        
        # 14. 验证数据完整性
        np.testing.assert_allclose(numpy_array, result_array)
        
        # 15. 清理资源
        py_remove_array(array_id)
        py_remove_shared_memory_region(region_name)
    
    def test_quantum_state_through_large_transfer(self):
        """测试量子态通过大数据传输的端到端流程"""
        # 1. 创建量子态
        # |ψ⟩ = (|0⟩ + |1⟩)/√2
        state = [complex(1/np.sqrt(2), 0), complex(1/np.sqrt(2), 0)]
        dimensions = [2]  # 2维希尔伯特空间
        
        # 2. 创建量子态数组
        quantum_array = py_create_quantum_state(state, dimensions)
        
        # 3. 序列化量子态
        serialized = quantum_array.serialize()
        
        # 4. 创建发送和接收会话
        total_size = len(serialized)
        chunk_size = 1024
        sender_id = py_create_sender_session("quantum_transfer", total_size, chunk_size)
        receiver_id = py_create_receiver_session("quantum_transfer", chunk_size)
        
        # 5. 发送数据
        offset = 0
        while offset < total_size:
            end = min(offset + chunk_size, total_size)
            chunk = serialized[offset:end]
            bytes_sent = py_send_chunk(sender_id, chunk)
            offset += bytes_sent
        
        # 6. 接收数据
        received_data = bytearray()
        while True:
            chunk = py_receive_chunk(receiver_id)
            if not chunk:
                break
            received_data.extend(chunk)
        
        # 7. 反序列化量子态
        deserialized_array = quantum_array.deserialize(received_data)
        
        # 8. 验证量子态保持不变
        deserialized_state = deserialized_array.state
        
        # 9. 验证维度保持不变
        self.assertEqual(deserialized_array.dimensions, dimensions)
        
        # 10. 验证状态向量保持不变
        for i in range(len(state)):
            self.assertAlmostEqual(state[i].real, deserialized_state[i].real, places=10)
            self.assertAlmostEqual(state[i].imag, deserialized_state[i].imag, places=10)
    
    def test_fractal_holographic_combined_flow(self):
        """测试分形结构和全息数据的组合流程"""
        # 1. 创建分形结构
        nodes = [
            {"id": 0, "position": [0.0, 0.0], "properties": {}},
            {"id": 1, "position": [1.0, 0.0], "properties": {}},
            {"id": 2, "position": [0.5, 0.866], "properties": {}}
        ]
        
        connections = [
            {"source": 0, "target": 1, "weight": 1.0, "properties": {}},
            {"source": 1, "target": 2, "weight": 1.0, "properties": {}},
            {"source": 2, "target": 0, "weight": 1.0, "properties": {}}
        ]
        
        metadata = {}
        dimension = 2
        depth = 1
        branching_factor = 3
        
        # 2. 创建分形结构数组
        fractal_array = py_create_fractal_structure(
            nodes, connections, metadata, dimension, depth, branching_factor
        )
        
        # 3. 创建全息数据
        n = 16
        original_data = [complex(random.random(), random.random()) for _ in range(n)]
        encoding_matrix = [[complex(random.random(), random.random()) for _ in range(n)] for _ in range(n)]
        encoding_type = "fourier"
        original_dimensions = [n]
        holographic_dimensions = [n]
        holo_metadata = {}
        
        # 4. 创建全息数据数组
        holographic_array = py_create_holographic_data(
            original_data, encoding_matrix, encoding_type,
            original_dimensions, holographic_dimensions, holo_metadata
        )
        
        # 5. 序列化分形结构和全息数据
        fractal_serialized = fractal_array.serialize()
        holographic_serialized = holographic_array.serialize()
        
        # 6. 创建共享内存区域
        region_name = "combined_test"
        region_size = len(fractal_serialized) + len(holographic_serialized) + 100
        py_create_shared_memory_region(region_name, region_size)
        
        # 7. 写入分形结构
        py_write_to_shared_memory(region_name, 0, fractal_serialized)
        
        # 8. 写入全息数据
        py_write_to_shared_memory(region_name, len(fractal_serialized), holographic_serialized)
        
        # 9. 读取分形结构
        fractal_read = py_read_from_shared_memory(region_name, 0, len(fractal_serialized))
        
        # 10. 读取全息数据
        holographic_read = py_read_from_shared_memory(
            region_name, len(fractal_serialized), len(holographic_serialized)
        )
        
        # 11. 反序列化分形结构
        deserialized_fractal = fractal_array.deserialize(fractal_read)
        
        # 12. 反序列化全息数据
        deserialized_holographic = holographic_array.deserialize(holographic_read)
        
        # 13. 验证分形结构
        self.assertEqual(len(deserialized_fractal.nodes), len(nodes))
        self.assertEqual(len(deserialized_fractal.connections), len(connections))
        
        # 14. 验证全息数据
        self.assertEqual(deserialized_holographic.encoding_type, encoding_type)
        self.assertEqual(deserialized_holographic.original_dimensions, original_dimensions)
        
        # 15. 清理资源
        py_remove_shared_memory_region(region_name)
    
    def test_complete_pipeline(self):
        """测试完整的零拷贝管道"""
        # 1. 创建NumPy数组
        array_size = 1000
        numpy_array = np.random.rand(array_size).astype(np.float64)
        
        # 2. 转换为Arrow数组
        arrow_array = py_array_to_arrow(numpy_array)
        
        # 3. 注册Arrow数组
        array_id = py_register_array(arrow_array)
        
        # 4. 分配内存池缓冲区
        buffer_size = array_size * 8
        buffer_id = py_allocate_buffer(buffer_size)
        
        # 5. 获取Arrow数组
        retrieved_array = py_get_array(array_id)
        
        # 6. 序列化Arrow数组
        serialized = retrieved_array.serialize()
        
        # 7. 创建共享内存区域
        region_name = "pipeline_test"
        region_size = len(serialized) * 2
        py_create_shared_memory_region(region_name, region_size)
        
        # 8. 写入共享内存
        py_write_to_shared_memory(region_name, 0, serialized)
        
        # 9. 从共享内存读取
        read_data = py_read_from_shared_memory(region_name, 0, len(serialized))
        
        # 10. 创建内存映射文件
        file_path = os.path.join(self.temp_dir, "pipeline.bin")
        py_create_mapped_file(file_path, len(read_data))
        
        # 11. 写入内存映射文件
        py_write_to_mapped_file(file_path, 0, read_data)
        
        # 12. 从内存映射文件读取
        file_data = py_read_from_mapped_file(file_path, 0, len(read_data))
        
        # 13. 创建发送和接收会话
        total_size = len(file_data)
        chunk_size = 1024
        sender_id = py_create_sender_session("pipeline_transfer", total_size, chunk_size)
        receiver_id = py_create_receiver_session("pipeline_transfer", chunk_size)
        
        # 14. 发送数据
        offset = 0
        while offset < total_size:
            end = min(offset + chunk_size, total_size)
            chunk = file_data[offset:end]
            bytes_sent = py_send_chunk(sender_id, chunk)
            offset += bytes_sent
        
        # 15. 接收数据
        received_data = bytearray()
        while True:
            chunk = py_receive_chunk(receiver_id)
            if not chunk:
                break
            received_data.extend(chunk)
        
        # 16. 反序列化Arrow数组
        deserialized_array = retrieved_array.deserialize(received_data)
        
        # 17. 转换回NumPy数组
        result_array = deserialized_array.to_numpy()
        
        # 18. 验证数据完整性
        np.testing.assert_allclose(numpy_array, result_array)
        
        # 19. 清理资源
        py_remove_array(array_id)
        py_deallocate_buffer(buffer_id)
        py_remove_shared_memory_region(region_name)

if __name__ == "__main__":
    unittest.main()
