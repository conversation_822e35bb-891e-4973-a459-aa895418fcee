#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
特殊数据类型性能基准测试

这个模块测量量子态、分形结构和全息数据的性能。
"""

import os
import sys
import tempfile
import time
import numpy as np
import random
import json
import argparse
from tabulate import tabulate
from pathlib import Path

# 导入arrow_integration模块
# 注意：在实际运行测试前，需要先构建并安装arrow_integration
# 这里假设模块已经安装
try:
    import arrow_integration
    from arrow_integration.zero_copy import (
        py_init_shared_memory_manager,
        py_create_shared_memory_region,
        py_open_shared_memory_region,
        py_remove_shared_memory_region,
        py_list_shared_memory_regions,
        py_write_to_shared_memory,
        py_read_from_shared_memory,
        py_create_mapped_file,
        py_open_mapped_file,
        py_write_to_mapped_file,
        py_read_from_mapped_file,
        py_register_buffer,
        py_get_buffer,
        py_remove_buffer,
        py_register_array,
        py_get_array,
        py_remove_array,
        py_get_data_accessor_stats,
        py_clear_data_accessor,
        py_allocate_buffer,
        py_deallocate_buffer,
        py_get_memory_pool_stats,
        py_clear_memory_pool,
        py_shrink_memory_pool,
        py_create_sender_session,
        py_create_receiver_session,
        py_send_chunk,
        py_receive_chunk,
        py_get_session_status,
        py_get_session_progress,
        py_get_large_transfer_stats,
        py_buffer_to_arrow,
        py_array_to_arrow
    )
    from arrow_integration.types import (
        py_create_quantum_state,
        py_create_fractal_structure,
        py_create_holographic_data
    )
    ARROW_INTEGRATION_AVAILABLE = True
except ImportError:
    ARROW_INTEGRATION_AVAILABLE = False
    print("警告：arrow_integration模块不可用，跳过基准测试")

def create_random_quantum_state(n_qubits):
    """创建随机量子态"""
    # 创建随机复数系数
    dim = 2 ** n_qubits
    real_parts = np.random.normal(0, 1, dim)
    imag_parts = np.random.normal(0, 1, dim)
    
    # 组合为复数
    coeffs = [complex(real_parts[i], imag_parts[i]) for i in range(dim)]
    
    # 归一化
    norm = np.sqrt(sum(abs(c)**2 for c in coeffs))
    normalized_coeffs = [c / norm for c in coeffs]
    
    # 创建量子态
    dimensions = [2] * n_qubits
    return py_create_quantum_state(normalized_coeffs, dimensions)

def create_sierpinski_triangle(depth):
    """创建谢尔宾斯基三角形分形"""
    # 基本三角形顶点
    nodes = [
        {"id": 0, "position": [0.0, 0.0], "properties": {}},
        {"id": 1, "position": [1.0, 0.0], "properties": {}},
        {"id": 2, "position": [0.5, 0.866], "properties": {}}
    ]
    
    # 连接基本三角形
    connections = [
        {"source": 0, "target": 1, "weight": 1.0, "properties": {}},
        {"source": 1, "target": 2, "weight": 1.0, "properties": {}},
        {"source": 2, "target": 0, "weight": 1.0, "properties": {}}
    ]
    
    # 递归生成更深层次的三角形
    next_id = 3
    for d in range(1, depth):
        scale_factor = 1.0 / (2.0 ** d)
        nodes_count = len(nodes)
        
        new_nodes = []
        new_connections = []
        
        for i in range(0, nodes_count, 3):
            if i + 2 < nodes_count:
                p0 = nodes[i]["position"]
                p1 = nodes[i+1]["position"]
                p2 = nodes[i+2]["position"]
                
                # 计算新三角形的顶点
                new_p0 = [(p0[0] + p1[0]) * 0.5, (p0[1] + p1[1]) * 0.5]
                new_p1 = [(p1[0] + p2[0]) * 0.5, (p1[1] + p2[1]) * 0.5]
                new_p2 = [(p2[0] + p0[0]) * 0.5, (p2[1] + p0[1]) * 0.5]
                
                # 添加新节点
                new_nodes.append({"id": next_id, "position": new_p0, "properties": {}})
                new_nodes.append({"id": next_id+1, "position": new_p1, "properties": {}})
                new_nodes.append({"id": next_id+2, "position": new_p2, "properties": {}})
                
                # 添加新连接
                new_connections.append({"source": next_id, "target": next_id+1, "weight": scale_factor, "properties": {}})
                new_connections.append({"source": next_id+1, "target": next_id+2, "weight": scale_factor, "properties": {}})
                new_connections.append({"source": next_id+2, "target": next_id, "weight": scale_factor, "properties": {}})
                
                next_id += 3
        
        nodes.extend(new_nodes)
        connections.extend(new_connections)
    
    # 创建分形结构
    metadata = {}
    dimension = 2
    branching_factor = 3
    
    return py_create_fractal_structure(
        nodes, connections, metadata, dimension, depth, branching_factor
    )

def create_holographic_data(n):
    """创建全息数据"""
    # 创建原始数据
    original_data = [complex(random.random(), random.random()) for _ in range(n)]
    
    # 创建傅里叶变换矩阵
    encoding_matrix = []
    scale = 1.0 / np.sqrt(n)
    
    for i in range(n):
        row = []
        for j in range(n):
            angle = -2.0 * np.pi * i * j / n
            row.append(complex(np.cos(angle), np.sin(angle)) * scale)
        encoding_matrix.append(row)
    
    # 创建全息数据
    encoding_type = "fourier"
    original_dimensions = [n]
    holographic_dimensions = [n]
    metadata = {}
    
    return py_create_holographic_data(
        original_data, encoding_matrix, encoding_type,
        original_dimensions, holographic_dimensions, metadata
    )

def benchmark_quantum_state():
    """测量量子态性能"""
    print("\n=== 量子态性能基准测试 ===")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    py_init_shared_memory_manager(temp_dir)
    
    qubit_counts = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    iterations = 10
    
    results = {}
    
    for n_qubits in qubit_counts:
        # 创建量子态
        quantum_state = create_random_quantum_state(n_qubits)
        
        # 序列化
        start_serialize = time.time()
        for _ in range(iterations):
            serialized = quantum_state.serialize()
        end_serialize = time.time()
        
        # 反序列化
        start_deserialize = time.time()
        for _ in range(iterations):
            deserialized = quantum_state.deserialize(serialized)
        end_deserialize = time.time()
        
        # 共享内存传输
        region_name = f"quantum_bench_{n_qubits}"
        region_size = len(serialized) * 2
        py_create_shared_memory_region(region_name, region_size)
        
        start_transfer = time.time()
        for _ in range(iterations):
            py_write_to_shared_memory(region_name, 0, serialized)
            read_data = py_read_from_shared_memory(region_name, 0, len(serialized))
        end_transfer = time.time()
        
        py_remove_shared_memory_region(region_name)
        
        # 计算性能指标
        serialize_time = (end_serialize - start_serialize) / iterations
        deserialize_time = (end_deserialize - start_deserialize) / iterations
        transfer_time = (end_transfer - start_transfer) / iterations
        
        dim = 2 ** n_qubits
        serialize_throughput = dim * 16 / serialize_time / 1024 / 1024  # MB/s
        deserialize_throughput = dim * 16 / deserialize_time / 1024 / 1024  # MB/s
        transfer_throughput = len(serialized) / transfer_time / 1024 / 1024  # MB/s
        
        results[n_qubits] = {
            'dim': dim,
            'serialize_time': serialize_time,
            'deserialize_time': deserialize_time,
            'transfer_time': transfer_time,
            'serialize_throughput': serialize_throughput,
            'deserialize_throughput': deserialize_throughput,
            'transfer_throughput': transfer_throughput,
        }
        
        print(f"量子比特数: {n_qubits}, 维度: {dim}")
        print(f"  序列化时间: {serialize_time*1000:.3f}毫秒, 吞吐量: {serialize_throughput:.2f} MB/s")
        print(f"  反序列化时间: {deserialize_time*1000:.3f}毫秒, 吞吐量: {deserialize_throughput:.2f} MB/s")
        print(f"  传输时间: {transfer_time*1000:.3f}毫秒, 吞吐量: {transfer_throughput:.2f} MB/s")
    
    # 清理临时目录
    import shutil
    shutil.rmtree(temp_dir)
    
    return results

def benchmark_fractal_structure():
    """测量分形结构性能"""
    print("\n=== 分形结构性能基准测试 ===")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    depths = [1, 2, 3, 4, 5, 6]
    iterations = 10
    
    results = {}
    
    for depth in depths:
        # 创建分形结构
        fractal = create_sierpinski_triangle(depth)
        
        # 序列化
        start_serialize = time.time()
        for _ in range(iterations):
            serialized = fractal.serialize()
        end_serialize = time.time()
        
        # 反序列化
        start_deserialize = time.time()
        for _ in range(iterations):
            deserialized = fractal.deserialize(serialized)
        end_deserialize = time.time()
        
        # 内存映射传输
        file_path = os.path.join(temp_dir, f"fractal_bench_{depth}.bin")
        py_create_mapped_file(file_path, len(serialized))
        
        start_transfer = time.time()
        for _ in range(iterations):
            py_write_to_mapped_file(file_path, 0, serialized)
            read_data = py_read_from_mapped_file(file_path, 0, len(serialized))
        end_transfer = time.time()
        
        # 计算性能指标
        serialize_time = (end_serialize - start_serialize) / iterations
        deserialize_time = (end_deserialize - start_deserialize) / iterations
        transfer_time = (end_transfer - start_transfer) / iterations
        
        node_count = len(fractal.nodes)
        connection_count = len(fractal.connections)
        
        serialize_throughput = len(serialized) / serialize_time / 1024 / 1024  # MB/s
        deserialize_throughput = len(serialized) / deserialize_time / 1024 / 1024  # MB/s
        transfer_throughput = len(serialized) / transfer_time / 1024 / 1024  # MB/s
        
        results[depth] = {
            'node_count': node_count,
            'connection_count': connection_count,
            'serialize_time': serialize_time,
            'deserialize_time': deserialize_time,
            'transfer_time': transfer_time,
            'serialize_throughput': serialize_throughput,
            'deserialize_throughput': deserialize_throughput,
            'transfer_throughput': transfer_throughput,
        }
        
        print(f"深度: {depth}, 节点数: {node_count}, 连接数: {connection_count}")
        print(f"  序列化时间: {serialize_time*1000:.3f}毫秒, 吞吐量: {serialize_throughput:.2f} MB/s")
        print(f"  反序列化时间: {deserialize_time*1000:.3f}毫秒, 吞吐量: {deserialize_throughput:.2f} MB/s")
        print(f"  传输时间: {transfer_time*1000:.3f}毫秒, 吞吐量: {transfer_throughput:.2f} MB/s")
    
    # 清理临时目录
    import shutil
    shutil.rmtree(temp_dir)
    
    return results

def benchmark_holographic_data():
    """测量全息数据性能"""
    print("\n=== 全息数据性能基准测试 ===")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    py_init_shared_memory_manager(temp_dir)
    
    sizes = [16, 32, 64, 128, 256, 512, 1024]
    iterations = 10
    
    results = {}
    
    for size in sizes:
        # 创建全息数据
        holographic_data = create_holographic_data(size)
        
        # 序列化
        start_serialize = time.time()
        for _ in range(iterations):
            serialized = holographic_data.serialize()
        end_serialize = time.time()
        
        # 反序列化
        start_deserialize = time.time()
        for _ in range(iterations):
            deserialized = holographic_data.deserialize(serialized)
        end_deserialize = time.time()
        
        # 大数据传输
        total_size = len(serialized)
        chunk_size = min(1024, total_size)
        
        start_transfer = time.time()
        for _ in range(iterations):
            sender_id = py_create_sender_session(f"holo_bench_{size}", total_size, chunk_size)
            receiver_id = py_create_receiver_session(f"holo_bench_{size}", chunk_size)
            
            # 发送数据
            offset = 0
            while offset < total_size:
                end = min(offset + chunk_size, total_size)
                chunk = serialized[offset:end]
                bytes_sent = py_send_chunk(sender_id, chunk)
                offset += bytes_sent
            
            # 接收数据
            received_data = bytearray()
            while True:
                chunk = py_receive_chunk(receiver_id)
                if not chunk:
                    break
                received_data.extend(chunk)
        end_transfer = time.time()
        
        # 计算性能指标
        serialize_time = (end_serialize - start_serialize) / iterations
        deserialize_time = (end_deserialize - start_deserialize) / iterations
        transfer_time = (end_transfer - start_transfer) / iterations
        
        serialize_throughput = len(serialized) / serialize_time / 1024 / 1024  # MB/s
        deserialize_throughput = len(serialized) / deserialize_time / 1024 / 1024  # MB/s
        transfer_throughput = len(serialized) / transfer_time / 1024 / 1024  # MB/s
        
        results[size] = {
            'size': size,
            'serialized_size': len(serialized),
            'serialize_time': serialize_time,
            'deserialize_time': deserialize_time,
            'transfer_time': transfer_time,
            'serialize_throughput': serialize_throughput,
            'deserialize_throughput': deserialize_throughput,
            'transfer_throughput': transfer_throughput,
        }
        
        print(f"大小: {size}, 序列化大小: {len(serialized)/1024:.1f} KB")
        print(f"  序列化时间: {serialize_time*1000:.3f}毫秒, 吞吐量: {serialize_throughput:.2f} MB/s")
        print(f"  反序列化时间: {deserialize_time*1000:.3f}毫秒, 吞吐量: {deserialize_throughput:.2f} MB/s")
        print(f"  传输时间: {transfer_time*1000:.3f}毫秒, 吞吐量: {transfer_throughput:.2f} MB/s")
    
    # 清理临时目录
    import shutil
    shutil.rmtree(temp_dir)
    
    return results

def main():
    """主函数"""
    if not ARROW_INTEGRATION_AVAILABLE:
        print("arrow_integration模块不可用，跳过基准测试")
        return
    
    parser = argparse.ArgumentParser(description="特殊数据类型性能基准测试")
    parser.add_argument("--output", "-o", help="输出JSON文件路径")
    parser.add_argument("--quantum", action="store_true", help="运行量子态基准测试")
    parser.add_argument("--fractal", action="store_true", help="运行分形结构基准测试")
    parser.add_argument("--holographic", action="store_true", help="运行全息数据基准测试")
    parser.add_argument("--all", action="store_true", help="运行所有基准测试")
    
    args = parser.parse_args()
    
    # 如果没有指定任何选项，则运行所有基准测试
    if not (args.quantum or args.fractal or args.holographic or args.all):
        args.all = True
    
    results = {}
    
    if args.all or args.quantum:
        results["quantum_state"] = benchmark_quantum_state()
    
    if args.all or args.fractal:
        results["fractal_structure"] = benchmark_fractal_structure()
    
    if args.all or args.holographic:
        results["holographic_data"] = benchmark_holographic_data()
    
    # 保存结果
    if args.output:
        with open(args.output, "w") as f:
            json.dump(results, f, indent=2)
        print(f"\n结果已保存到：{args.output}")

if __name__ == "__main__":
    main()
