#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
零拷贝机制边界条件测试

这个脚本测试零拷贝机制的边界条件，包括：
1. 空数据
2. 极小数据
3. 极大数据
4. 特殊值
5. 错误处理
"""

import os
import sys
import tempfile
import unittest
import numpy as np
import json
from pathlib import Path

class TestZeroCopyEdgeCases(unittest.TestCase):
    """零拷贝机制边界条件测试类"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_empty_array(self):
        """测试空数组"""
        # 创建空NumPy数组
        array = np.array([], dtype=np.float64)
        
        # 获取数组的内存视图
        memory_view = memoryview(array)
        
        # 创建一个新的NumPy数组，共享相同的内存
        new_array = np.frombuffer(memory_view, dtype=np.float64)
        
        # 验证两个数组共享相同的内存
        self.assertEqual(len(new_array), 0)
    
    def test_tiny_array(self):
        """测试极小数组"""
        # 创建极小NumPy数组
        array = np.array([1], dtype=np.float64)
        
        # 获取数组的内存视图
        memory_view = memoryview(array)
        
        # 创建一个新的NumPy数组，共享相同的内存
        new_array = np.frombuffer(memory_view, dtype=np.float64)
        
        # 验证两个数组共享相同的内存
        self.assertTrue(np.array_equal(array, new_array))
        
        # 修改原始数组
        array[0] = 10
        
        # 验证新数组也被修改
        self.assertEqual(new_array[0], 10)
    
    def test_huge_array(self):
        """测试极大数组"""
        # 创建极大NumPy数组
        array_size = 100_000_000  # 1亿个元素
        array = np.ones(array_size, dtype=np.int8)
        
        # 获取数组的内存视图
        memory_view = memoryview(array)
        
        # 创建一个新的NumPy数组，共享相同的内存
        new_array = np.frombuffer(memory_view, dtype=np.int8)
        
        # 验证两个数组共享相同的内存
        self.assertEqual(len(new_array), array_size)
        self.assertEqual(new_array[0], 1)
        
        # 修改原始数组的一部分
        array[1000:2000] = 2
        
        # 验证新数组也被修改
        self.assertEqual(new_array[1500], 2)
    
    def test_special_values(self):
        """测试特殊值"""
        # 创建包含特殊值的NumPy数组
        array = np.array([np.nan, np.inf, -np.inf, 0.0, -0.0], dtype=np.float64)
        
        # 获取数组的内存视图
        memory_view = memoryview(array)
        
        # 创建一个新的NumPy数组，共享相同的内存
        new_array = np.frombuffer(memory_view, dtype=np.float64)
        
        # 验证特殊值
        self.assertTrue(np.isnan(new_array[0]))
        self.assertTrue(np.isinf(new_array[1]))
        self.assertTrue(np.isinf(new_array[2]))
        self.assertEqual(new_array[3], 0.0)
        self.assertEqual(new_array[4], -0.0)
    
    def test_quantum_state_edge_cases(self):
        """测试量子态边界条件"""
        # 测试空量子态
        empty_state = []
        empty_dimensions = []
        
        # 序列化
        serialized = {
            "state": [(c.real, c.imag) for c in empty_state],
            "dimensions": empty_dimensions,
            "type": "pure"
        }
        serialized_json = json.dumps(serialized)
        
        # 反序列化
        deserialized = json.loads(serialized_json)
        deserialized_state = [complex(re, im) for re, im in deserialized["state"]]
        
        # 验证空量子态
        self.assertEqual(len(deserialized_state), 0)
        self.assertEqual(deserialized["dimensions"], [])
        
        # 测试极大量子态
        n_qubits = 20  # 2^20 = 1,048,576个元素
        dim = 2 ** n_qubits
        
        # 创建一个简单的量子态（只有一个非零元素）
        large_state = [complex(0, 0)] * dim
        large_state[0] = complex(1, 0)
        large_dimensions = [2] * n_qubits
        
        # 序列化（只序列化非零元素）
        sparse_serialized = {
            "state": [(0, 1.0, 0.0)],  # (索引, 实部, 虚部)
            "dimensions": large_dimensions,
            "type": "pure",
            "sparse": True,
            "size": dim
        }
        sparse_serialized_json = json.dumps(sparse_serialized)
        
        # 反序列化
        sparse_deserialized = json.loads(sparse_serialized_json)
        
        # 验证稀疏表示
        self.assertEqual(sparse_deserialized["size"], dim)
        self.assertEqual(len(sparse_deserialized["state"]), 1)
        self.assertEqual(sparse_deserialized["state"][0][0], 0)  # 索引
        self.assertEqual(sparse_deserialized["state"][0][1], 1.0)  # 实部
        self.assertEqual(sparse_deserialized["state"][0][2], 0.0)  # 虚部
    
    def test_fractal_structure_edge_cases(self):
        """测试分形结构边界条件"""
        # 测试空分形结构
        empty_nodes = []
        empty_connections = []
        
        empty_fractal = {
            "nodes": empty_nodes,
            "connections": empty_connections,
            "dimension": 0,
            "depth": 0,
            "branching_factor": 0,
            "type": "custom"
        }
        
        # 序列化
        empty_serialized_json = json.dumps(empty_fractal)
        
        # 反序列化
        empty_deserialized = json.loads(empty_serialized_json)
        
        # 验证空分形结构
        self.assertEqual(len(empty_deserialized["nodes"]), 0)
        self.assertEqual(len(empty_deserialized["connections"]), 0)
        
        # 测试单节点分形结构
        single_node = [{"id": 0, "position": [0.0, 0.0], "properties": {}}]
        single_connections = []
        
        single_fractal = {
            "nodes": single_node,
            "connections": single_connections,
            "dimension": 0,
            "depth": 0,
            "branching_factor": 0,
            "type": "custom"
        }
        
        # 序列化
        single_serialized_json = json.dumps(single_fractal)
        
        # 反序列化
        single_deserialized = json.loads(single_serialized_json)
        
        # 验证单节点分形结构
        self.assertEqual(len(single_deserialized["nodes"]), 1)
        self.assertEqual(len(single_deserialized["connections"]), 0)
    
    def test_holographic_data_edge_cases(self):
        """测试全息数据边界条件"""
        # 测试空全息数据
        empty_data = []
        empty_encoding_matrix = []
        
        empty_holographic = {
            "original_data": [(d.real, d.imag) for d in empty_data],
            "encoding_matrix": [[(c.real, c.imag) for c in row] for row in empty_encoding_matrix],
            "encoding_type": "fourier",
            "original_dimensions": [],
            "holographic_dimensions": []
        }
        
        # 序列化
        empty_serialized_json = json.dumps(empty_holographic)
        
        # 反序列化
        empty_deserialized = json.loads(empty_serialized_json)
        
        # 验证空全息数据
        self.assertEqual(len(empty_deserialized["original_data"]), 0)
        self.assertEqual(len(empty_deserialized["encoding_matrix"]), 0)
        
        # 测试单元素全息数据
        single_data = [complex(1.0, 0.0)]
        single_encoding_matrix = [[complex(1.0, 0.0)]]
        
        single_holographic = {
            "original_data": [(d.real, d.imag) for d in single_data],
            "encoding_matrix": [[(c.real, c.imag) for c in row] for row in single_encoding_matrix],
            "encoding_type": "fourier",
            "original_dimensions": [1],
            "holographic_dimensions": [1]
        }
        
        # 序列化
        single_serialized_json = json.dumps(single_holographic)
        
        # 反序列化
        single_deserialized = json.loads(single_serialized_json)
        
        # 验证单元素全息数据
        self.assertEqual(len(single_deserialized["original_data"]), 1)
        self.assertEqual(len(single_deserialized["encoding_matrix"]), 1)
        self.assertEqual(len(single_deserialized["encoding_matrix"][0]), 1)
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试类型不匹配
        array = np.array([1, 2, 3], dtype=np.int32)
        memory_view = memoryview(array)
        
        # 尝试使用错误的数据类型
        with self.assertRaises(ValueError):
            new_array = np.frombuffer(memory_view, dtype=np.float64)
        
        # 测试内存映射文件不存在
        non_existent_file = os.path.join(self.temp_dir, "non_existent.npy")
        
        with self.assertRaises(FileNotFoundError):
            np.load(non_existent_file, mmap_mode='r')
        
        # 测试JSON解析错误
        invalid_json = "{"
        
        with self.assertRaises(json.JSONDecodeError):
            json.loads(invalid_json)

if __name__ == "__main__":
    unittest.main()
