#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
性能退化检测脚本

这个脚本检测零拷贝机制的性能退化，包括：
1. 与基准线比较
2. 与历史数据比较
3. 发出性能退化警告
"""

import os
import sys
import json
import argparse
import datetime
import numpy as np
from pathlib import Path

def load_benchmark_results(file_path):
    """加载基准测试结果"""
    with open(file_path, "r") as f:
        return json.load(f)

def load_baseline(file_path):
    """加载基准线"""
    if os.path.exists(file_path):
        with open(file_path, "r") as f:
            return json.load(f)
    return None

def save_baseline(file_path, data):
    """保存基准线"""
    with open(file_path, "w") as f:
        json.dump(data, f, indent=2)

def detect_regression_numpy_array_zero_copy(current, baseline, threshold):
    """检测NumPy数组零拷贝转换性能退化"""
    regressions = []
    
    for size in current.keys():
        if size in baseline:
            current_throughput = current[size]["zero_copy_throughput"]
            baseline_throughput = baseline[size]["zero_copy_throughput"]
            
            # 计算性能变化百分比
            change_percent = (baseline_throughput - current_throughput) / baseline_throughput * 100
            
            if change_percent > threshold:
                regressions.append({
                    "size": size,
                    "current_throughput": current_throughput,
                    "baseline_throughput": baseline_throughput,
                    "change_percent": change_percent,
                })
    
    return regressions

def detect_regression_quantum_state_serialization(current, baseline, threshold):
    """检测量子态序列化和反序列化性能退化"""
    regressions = []
    
    for n_qubits in current.keys():
        if n_qubits in baseline:
            current_serialize_throughput = current[n_qubits]["serialize_throughput"]
            baseline_serialize_throughput = baseline[n_qubits]["serialize_throughput"]
            
            current_deserialize_throughput = current[n_qubits]["deserialize_throughput"]
            baseline_deserialize_throughput = baseline[n_qubits]["deserialize_throughput"]
            
            # 计算序列化性能变化百分比
            serialize_change_percent = (baseline_serialize_throughput - current_serialize_throughput) / baseline_serialize_throughput * 100
            
            # 计算反序列化性能变化百分比
            deserialize_change_percent = (baseline_deserialize_throughput - current_deserialize_throughput) / baseline_deserialize_throughput * 100
            
            if serialize_change_percent > threshold:
                regressions.append({
                    "n_qubits": n_qubits,
                    "operation": "serialize",
                    "current_throughput": current_serialize_throughput,
                    "baseline_throughput": baseline_serialize_throughput,
                    "change_percent": serialize_change_percent,
                })
            
            if deserialize_change_percent > threshold:
                regressions.append({
                    "n_qubits": n_qubits,
                    "operation": "deserialize",
                    "current_throughput": current_deserialize_throughput,
                    "baseline_throughput": baseline_deserialize_throughput,
                    "change_percent": deserialize_change_percent,
                })
    
    return regressions

def detect_regression(current_results, baseline_results, threshold):
    """检测性能退化"""
    regressions = {}
    
    # 检测NumPy数组零拷贝转换性能退化
    if "numpy_array_zero_copy" in current_results and "numpy_array_zero_copy" in baseline_results:
        numpy_regressions = detect_regression_numpy_array_zero_copy(
            current_results["numpy_array_zero_copy"],
            baseline_results["numpy_array_zero_copy"],
            threshold
        )
        
        if numpy_regressions:
            regressions["numpy_array_zero_copy"] = numpy_regressions
    
    # 检测量子态序列化和反序列化性能退化
    if "quantum_state_serialization" in current_results and "quantum_state_serialization" in baseline_results:
        quantum_regressions = detect_regression_quantum_state_serialization(
            current_results["quantum_state_serialization"],
            baseline_results["quantum_state_serialization"],
            threshold
        )
        
        if quantum_regressions:
            regressions["quantum_state_serialization"] = quantum_regressions
    
    return regressions

def print_regressions(regressions):
    """打印性能退化信息"""
    if not regressions:
        print("没有检测到性能退化")
        return
    
    print("检测到性能退化：")
    
    if "numpy_array_zero_copy" in regressions:
        print("\nNumPy数组零拷贝转换性能退化：")
        for regression in regressions["numpy_array_zero_copy"]:
            print(f"  数组大小: {regression['size']}")
            print(f"    当前吞吐量: {regression['current_throughput']:.2f} MB/s")
            print(f"    基准线吞吐量: {regression['baseline_throughput']:.2f} MB/s")
            print(f"    性能下降: {regression['change_percent']:.2f}%")
    
    if "quantum_state_serialization" in regressions:
        print("\n量子态序列化和反序列化性能退化：")
        for regression in regressions["quantum_state_serialization"]:
            print(f"  量子比特数: {regression['n_qubits']}, 操作: {regression['operation']}")
            print(f"    当前吞吐量: {regression['current_throughput']:.2f} MB/s")
            print(f"    基准线吞吐量: {regression['baseline_throughput']:.2f} MB/s")
            print(f"    性能下降: {regression['change_percent']:.2f}%")

def save_regressions(regressions, file_path):
    """保存性能退化信息"""
    with open(file_path, "w") as f:
        json.dump(regressions, f, indent=2)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="性能退化检测")
    parser.add_argument("--input", "-i", required=True, help="输入JSON文件路径")
    parser.add_argument("--baseline", "-b", help="基准线JSON文件路径")
    parser.add_argument("--threshold", "-t", type=float, default=5.0, help="性能退化阈值（百分比）")
    parser.add_argument("--output", "-o", help="输出JSON文件路径")
    parser.add_argument("--update-baseline", "-u", action="store_true", help="更新基准线")
    
    args = parser.parse_args()
    
    # 加载当前基准测试结果
    current_results = load_benchmark_results(args.input)
    
    # 确定基准线文件路径
    baseline_path = args.baseline
    if not baseline_path:
        baseline_path = os.path.join(os.path.dirname(args.input), "baseline.json")
    
    # 加载基准线
    baseline_results = load_baseline(baseline_path)
    
    # 如果没有基准线或者需要更新基准线
    if baseline_results is None or args.update_baseline:
        print(f"更新基准线: {baseline_path}")
        save_baseline(baseline_path, current_results)
        return
    
    # 检测性能退化
    regressions = detect_regression(current_results, baseline_results, args.threshold)
    
    # 打印性能退化信息
    print_regressions(regressions)
    
    # 保存性能退化信息
    if args.output and regressions:
        save_regressions(regressions, args.output)
        print(f"性能退化信息已保存到: {args.output}")
    
    # 如果检测到性能退化，返回非零退出码
    if regressions:
        sys.exit(1)

if __name__ == "__main__":
    main()
