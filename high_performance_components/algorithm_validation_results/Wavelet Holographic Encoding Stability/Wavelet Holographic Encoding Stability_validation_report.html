
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Wavelet Holographic Encoding Stability 算法验证报告</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .success {
            color: #27ae60;
        }
        .warning {
            color: #f39c12;
        }
        .error {
            color: #e74c3c;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        .result-badge {
            display: inline-block;
            padding: 3px 6px;
            border-radius: 3px;
            color: white;
            font-weight: bold;
        }
        .result-badge.pass {
            background-color: #27ae60;
        }
        .result-badge.fail {
            background-color: #e74c3c;
        }
        .image-container {
            text-align: center;
            margin: 20px 0;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Wavelet Holographic Encoding Stability 算法验证报告</h1>

        <div class="section">
            <h2>验证摘要</h2>
            <table>
                <tr>
                    <th>验证类型</th>
                    <th>结果</th>
                </tr>
            
                <tr>
                    <td>稳定性验证</td>
                    <td><span class="result-badge pass">PASS</span></td>
                </tr>
                
            </table>
        </div>
        
        <div class="section">
            <h2>稳定性验证</h2>
            <table>
                <tr>
                    <th>扰动</th>
                    <th>条件数</th>
                    <th>结果</th>
                </tr>
                
                <tr>
                    <td>1.00e-06</td>
                    <td>1.00</td>
                    <td><span class="result-badge pass">PASS</span></td>
                </tr>
                
                <tr>
                    <td>1.00e-05</td>
                    <td>1.00</td>
                    <td><span class="result-badge pass">PASS</span></td>
                </tr>
                
                <tr>
                    <td>1.00e-04</td>
                    <td>1.00</td>
                    <td><span class="result-badge pass">PASS</span></td>
                </tr>
                
                <tr>
                    <td>1.00e-03</td>
                    <td>1.00</td>
                    <td><span class="result-badge pass">PASS</span></td>
                </tr>
                
                <tr>
                    <td>1.00e-02</td>
                    <td>1.00</td>
                    <td><span class="result-badge pass">PASS</span></td>
                </tr>
                
            </table>

            <h3>稳定性图</h3>
            <div class="image-container">
                <img src="stability.png" alt="Stability">
            </div>
        </div>
                
    </div>
</body>
</html>
            