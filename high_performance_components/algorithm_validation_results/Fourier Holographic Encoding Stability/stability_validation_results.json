{"algorithm_name": "Fourier Holographic Encoding Stability", "validation_type": "stability", "base_input": [0.012047104454361413, 0.0826298059001982, 0.6141834072457448, 0.23990666214516987, 0.7014605729582967, 0.6602607897624805, 0.0006420579994282916, 0.8857651429776026], "perturbations": [1e-06, 1e-05, 0.0001, 0.001, 0.01], "perturbed_inputs": [[0.012047062044578904, 0.08262961866901662, 0.6141836878031326, 0.2399049752428166, 0.7014600521693566, 0.6602611548675528, 0.0006436097383625966, 0.8857666643846897], [0.012047573430930695, 0.0826274925091918, 0.6142000931171272, 0.23991599881435033, 0.7014572168950218, 0.6602568353617903, 0.0006307196852765875, 0.8857684296054854], [0.011932567653392307, 0.0825375180539324, 0.6142157735156241, 0.23988723914191554, 0.7012683479487928, 0.6605002620974052, 0.0006487380906588118, 0.8856470727172512], [0.011831526104553172, 0.08333369911806673, 0.6145587515492431, 0.2393729669965423, 0.7017397099964225, 0.6602542240565218, 0.0022806977573137394, 0.8863055483140542], [0.01495521274951684, 0.08747221374003981, 0.6103523164942066, 0.2294342900531402, 0.6978041417635967, 0.6658123871025899, -0.005226924778396003, 0.8801042595301096]], "base_output": [{"real": 3.1968955434432824, "imag": 0.0}, {"real": -0.6411693427329572, "imag": 0.2515963479496155}, {"real": 0.09868221216748496, "imag": 0.38278120946009386}, {"real": -0.7376575942749133, "imag": 1.4786790464422486}, {"real": -0.5402292581276202, "imag": 0.0}, {"real": -0.7376575942749133, "imag": -1.4786790464422486}, {"real": 0.09868221216748496, "imag": -0.38278120946009386}, {"real": -0.6411693427329572, "imag": -0.2515963479496155}], "perturbed_outputs": [[{"real": 3.1968968249195067, "imag": 0.0}, {"real": -0.6411669862971489, "imag": 0.25160027830923404}, {"real": 0.0986798166724403, "imag": 0.38278086609093676}, {"real": -0.7376589939524064, "imag": 1.478680434438774}, {"real": -0.5402280014086449, "imag": 0.0}, {"real": -0.7376589939524064, "imag": -1.478680434438774}, {"real": 0.0986798166724403, "imag": -0.38278086609093676}, {"real": -0.6411669862971489, "imag": -0.25160027830923404}], [{"real": 3.196904359419174, "imag": 0.0}, {"real": -0.6411686353492658, "imag": 0.251562885369778}, {"real": 0.09867397752354867, "imag": 0.3828001005488537}, {"real": -0.7376506515789165, "imag": 1.4787016322334792}, {"real": -0.5402331531624616, "imag": 0.0}, {"real": -0.7376506515789165, "imag": -1.4787016322334792}, {"real": 0.09867397752354867, "imag": -0.3828001005488537}, {"real": -0.6411686353492658, "imag": -0.251562885369778}], [{"real": 3.1966375192189727, "imag": 0.0}, {"real": -0.6413959985427088, "imag": 0.2517354975003754}, {"real": 0.09833640399590227, "imag": 0.38249653170782916}, {"real": -0.7372755620480921, "imag": 1.478869568350306}, {"real": -0.5405066648020365, "imag": 0.0}, {"real": -0.7372755620480921, "imag": -1.478869568350306}, {"real": 0.09833640399590227, "imag": -0.38249653170782916}, {"real": -0.6413959985427088, "imag": -0.2517354975003754}], [{"real": 3.1996771238927177, "imag": 0.0}, {"real": -0.6404021840614234, "imag": 0.25311677681788514}, {"real": 0.09673178679441885, "imag": 0.38209059213600804}, {"real": -0.7394141837223154, "imag": 1.477672884401744}, {"real": -0.5388557530776525, "imag": 0.0}, {"real": -0.7394141837223154, "imag": -1.477672884401744}, {"real": 0.09673178679441885, "imag": -0.38209059213600804}, {"real": -0.6404021840614234, "imag": -0.25311677681788514}], [{"real": 3.1807078966548037, "imag": 0.0}, {"real": -0.6317040396997337, "imag": 0.25346216487626905}, {"real": 0.10763396279730297, "imag": 0.35625394874062}, {"real": -0.7339938183284261, "imag": 1.4846206474214743}, {"real": -0.5449384041969552, "imag": 0.0}, {"real": -0.7339938183284261, "imag": -1.4846206474214743}, {"real": 0.10763396279730297, "imag": -0.35625394874062}, {"real": -0.6317040396997337, "imag": -0.25346216487626905}]], "relative_errors": [1.9376417963894703e-06, 1.579439497063876e-05, 0.0002469156600793833, 0.0013664967910604682, 0.011131324859220389], "condition_numbers": [0.9999999999980912, 0.9999999999965978, 1.000000000000068, 1.0000000000000182, 1.0000000000000029], "passed": true}