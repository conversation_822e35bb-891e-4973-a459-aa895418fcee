{"algorithm_name": "Wavelet Holographic Encoding", "validation_type": "analytical_solution", "inputs": [[1, 2, 3, 4, 5, 6, 7, 8], [1, 0, 0, 0, 0, 0, 0, 0], [0, 1, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 0, 0, 0, 0], [0.21704870757386485, 0.9665932993033625, 0.8963978739939676, 0.4672890546947147, 0.3200024133113698, 0.2344358023815749, 0.768220788276561, 0.4216124030788839]], "algorithm_outputs": [[1.0000000000000004, 2.0000000000000004, 3.0, 3.9999999999999996, 5.0, 5.999999999999999, 6.999999999999997, 7.999999999999997], [0.9999999999999999, -7.850462293418875e-17, -5.551115123125782e-17, -5.551115123125782e-17, 0.0, 0.0, 0.0, 0.0], [-7.850462293418875e-17, 0.9999999999999999, -5.551115123125782e-17, -5.551115123125782e-17, 0.0, 0.0, 0.0, 0.0], [0.9999999999999996, 0.9999999999999996, 0.9999999999999996, 0.9999999999999996, 0.0, 0.0, 0.0, 0.0], [0.21704870757386457, 0.9665932993033621, 0.8963978739939674, 0.46728905469471455, 0.32000241331136964, 0.2344358023815748, 0.7682207882765607, 0.4216124030788836]], "analytical_outputs": [[1, 2, 3, 4, 5, 6, 7, 8], [1, 0, 0, 0, 0, 0, 0, 0], [0, 1, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 0, 0, 0, 0], [0.21704870757386485, 0.9665932993033625, 0.8963978739939676, 0.4672890546947147, 0.3200024133113698, 0.2344358023815749, 0.768220788276561, 0.4216124030788839]], "relative_errors": [[4.440892098500626e-16, 2.220446049250313e-16, 0.0, 1.1102230246251565e-16, 0.0, 1.4802973661668753e-16, 3.806478941571965e-16, 3.3306690738754696e-16], [1.1102230246251565e-16, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], [0.0, 1.1102230246251565e-16, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], [4.440892098500626e-16, 4.440892098500626e-16, 4.440892098500626e-16, 4.440892098500626e-16, 0.0, 0.0, 0.0, 0.0], [1.2787717524732684e-15, 3.44578125699395e-16, 2.477076434102805e-16, 3.563820980197614e-16, 5.20413118046496e-16, 4.735723013919706e-16, 4.335562281967853e-16, 7.899836554979858e-16]], "absolute_errors": [[4.440892098500626e-16, 4.440892098500626e-16, 0.0, 4.440892098500626e-16, 0.0, 8.881784197001252e-16, 2.6645352591003757e-15, 2.6645352591003757e-15], [1.1102230246251565e-16, 7.850462293418875e-17, 5.551115123125782e-17, 5.551115123125782e-17, 0.0, 0.0, 0.0, 0.0], [7.850462293418875e-17, 1.1102230246251565e-16, 5.551115123125782e-17, 5.551115123125782e-17, 0.0, 0.0, 0.0, 0.0], [4.440892098500626e-16, 4.440892098500626e-16, 4.440892098500626e-16, 4.440892098500626e-16, 0.0, 0.0, 0.0, 0.0], [2.7755575615628914e-16, 3.3306690738754696e-16, 2.220446049250313e-16, 1.6653345369377348e-16, 1.6653345369377348e-16, 1.1102230246251565e-16, 3.3306690738754696e-16, 3.3306690738754696e-16]], "passed": true}