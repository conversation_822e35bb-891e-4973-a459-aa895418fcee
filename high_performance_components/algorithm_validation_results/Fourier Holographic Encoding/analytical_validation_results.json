{"algorithm_name": "Fourier Holographic Encoding", "validation_type": "analytical_solution", "inputs": [[1, 2, 3, 4, 5, 6, 7, 8], [1, 0, 0, 0, 0, 0, 0, 0], [0, 1, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 0, 0, 0, 0], [0.5104650861019175, 0.5912650266104067, 0.9503969633425736, 0.21705510154407293, 0.3241799344759334, 0.828852796430459, 0.6055152711880875, 0.3662618734837363]], "algorithm_outputs": [[1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0], [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], [0.0, 1.0, 0.0, 0.0, 0.0, -1.1102230246251565e-16, 0.0, 0.0], [1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 5.551115123125783e-17, 0.0], [0.5104650861019173, 0.5912650266104067, 0.9503969633425735, 0.21705510154407287, 0.32417993447593324, 0.8288527964304588, 0.6055152711880873, 0.36626187348373623]], "analytical_outputs": [[1, 2, 3, 4, 5, 6, 7, 8], [1, 0, 0, 0, 0, 0, 0, 0], [0, 1, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 0, 0, 0, 0], [0.5104650861019175, 0.5912650266104067, 0.9503969633425736, 0.21705510154407293, 0.3241799344759334, 0.828852796430459, 0.6055152711880875, 0.3662618734837363]], "relative_errors": [[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], [4.3498490096676016e-16, 0.0, 1.1681676893415885e-16, 2.557468165289187e-16, 5.13706852223874e-16, 1.3394694804752397e-16, 1.833517794599593e-16, 1.515613697468917e-16]], "absolute_errors": [[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0, 0.0, 1.1102230246251565e-16, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.551115123125783e-17, 0.0], [2.220446049250313e-16, 0.0, 1.1102230246251565e-16, 5.551115123125783e-17, 1.6653345369377348e-16, 1.1102230246251565e-16, 1.1102230246251565e-16, 5.551115123125783e-17]], "passed": true}