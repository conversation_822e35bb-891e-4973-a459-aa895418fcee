
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Fourier Holographic Encoding 算法验证报告</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .success {
            color: #27ae60;
        }
        .warning {
            color: #f39c12;
        }
        .error {
            color: #e74c3c;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        .result-badge {
            display: inline-block;
            padding: 3px 6px;
            border-radius: 3px;
            color: white;
            font-weight: bold;
        }
        .result-badge.pass {
            background-color: #27ae60;
        }
        .result-badge.fail {
            background-color: #e74c3c;
        }
        .image-container {
            text-align: center;
            margin: 20px 0;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Fourier Holographic Encoding 算法验证报告</h1>

        <div class="section">
            <h2>验证摘要</h2>
            <table>
                <tr>
                    <th>验证类型</th>
                    <th>结果</th>
                </tr>
            
                <tr>
                    <td>与解析解比较</td>
                    <td><span class="result-badge pass">PASS</span></td>
                </tr>
                
            </table>
        </div>
        
        <div class="section">
            <h2>与解析解比较</h2>
            <table>
                <tr>
                    <th>输入</th>
                    <th>相对误差</th>
                    <th>绝对误差</th>
                    <th>结果</th>
                </tr>
                
                <tr>
                    <td>输入 1</td>
                    <td>0.00e+00</td>
                    <td>0.00e+00</td>
                    <td><span class="result-badge pass">PASS</span></td>
                </tr>
                
                <tr>
                    <td>输入 2</td>
                    <td>0.00e+00</td>
                    <td>0.00e+00</td>
                    <td><span class="result-badge pass">PASS</span></td>
                </tr>
                
                <tr>
                    <td>输入 3</td>
                    <td>0.00e+00</td>
                    <td>1.11e-16</td>
                    <td><span class="result-badge pass">PASS</span></td>
                </tr>
                
                <tr>
                    <td>输入 4</td>
                    <td>0.00e+00</td>
                    <td>5.55e-17</td>
                    <td><span class="result-badge pass">PASS</span></td>
                </tr>
                
                <tr>
                    <td>输入 5</td>
                    <td>5.14e-16</td>
                    <td>2.22e-16</td>
                    <td><span class="result-badge pass">PASS</span></td>
                </tr>
                
            </table>

            <h3>对比图</h3>
            <div class="image-container">
                
                <img src="analytical_comparison_1.png" alt="Analytical Comparison 1">
                
                <img src="analytical_comparison_2.png" alt="Analytical Comparison 2">
                
                <img src="analytical_comparison_3.png" alt="Analytical Comparison 3">
                
                <img src="analytical_comparison_4.png" alt="Analytical Comparison 4">
                
                <img src="analytical_comparison_5.png" alt="Analytical Comparison 5">
                
            </div>
        </div>
                
    </div>
</body>
</html>
            