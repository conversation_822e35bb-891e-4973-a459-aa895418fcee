{"algorithm_name": "Mandelbrot Set", "validation_type": "analytical_solution", "inputs": [{"real": -2.0, "imag": 0.0}, {"real": -1.0, "imag": 0.0}, {"real": 0.0, "imag": 0.0}, {"real": 0.25, "imag": 0.0}, {"real": 0.5, "imag": 0.0}, {"real": 0.0, "imag": 1.0}, {"real": -0.75, "imag": 0.1}, {"real": -0.1, "imag": 0.8}], "algorithm_outputs": [100, 100, 100, 100, 4, 100, 32, 100], "analytical_outputs": [100, 100, 100, 100, 100, 100, 32, 100], "relative_errors": [0.0, 0.0, 0.0, 0.0, 0.96, 0.0, 0.0, 0.0], "absolute_errors": [0, 0, 0, 0, 96, 0, 0, 0], "passed": false}