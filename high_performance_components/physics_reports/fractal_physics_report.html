
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>分形结构物理正确性测试报告</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .success {
            color: #27ae60;
        }
        .warning {
            color: #f39c12;
        }
        .error {
            color: #e74c3c;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
        }
        .test-case {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .test-name {
            font-weight: bold;
        }
        .test-result {
            float: right;
            padding: 2px 5px;
            border-radius: 3px;
            color: white;
        }
        .test-result.pass {
            background-color: #27ae60;
        }
        .test-result.fail {
            background-color: #e74c3c;
        }
        .test-result.error {
            background-color: #f39c12;
        }
        .test-description {
            margin-top: 5px;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>分形结构物理正确性测试报告</h1>
        <p>生成时间：2025-05-16 21:37:05</p>

        <div class="section">
            <h2>测试摘要</h2>
            <p>本报告总结了分形结构物理正确性测试的结果。</p>

            <h3>测试结果</h3>
            <ul>
                <li>运行测试：5</li>
                <li>通过测试：5</li>
                <li>失败测试：0</li>
                <li>错误测试：0</li>
            </ul>
        </div>

        <div class="section">
            <h2>测试详情</h2>

        </div>

        <div class="section">
            <h2>分形结构物理原理</h2>

            <h3>分形维数</h3>
            <p>分形维数是描述分形结构复杂度的度量，通常是非整数。例如：</p>
            <ul>
                <li>谢尔宾斯基三角形的分形维数：log(3)/log(2) ≈ 1.585</li>
                <li>科赫曲线的分形维数：log(4)/log(3) ≈ 1.262</li>
            </ul>

            <h3>自相似性</h3>
            <p>分形结构具有自相似性，即部分与整体在统计意义上相似。</p>

            <h3>迭代函数系统</h3>
            <p>迭代函数系统（IFS）是生成分形的一种方法，通过迭代应用一组函数来生成分形。</p>
            <p>例如，谢尔宾斯基三角形的IFS包含三个函数：</p>
            <pre><code>f₁(x, y) = (0.5x, 0.5y)
f₂(x, y) = (0.5x + 0.5, 0.5y)
f₃(x, y) = (0.5x + 0.25, 0.5y + 0.5)</code></pre>
        </div>
    </div>
</body>
</html>
