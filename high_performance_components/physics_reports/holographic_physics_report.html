
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>全息数据物理正确性测试报告</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .success {
            color: #27ae60;
        }
        .warning {
            color: #f39c12;
        }
        .error {
            color: #e74c3c;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
        }
        .test-case {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .test-name {
            font-weight: bold;
        }
        .test-result {
            float: right;
            padding: 2px 5px;
            border-radius: 3px;
            color: white;
        }
        .test-result.pass {
            background-color: #27ae60;
        }
        .test-result.fail {
            background-color: #e74c3c;
        }
        .test-result.error {
            background-color: #f39c12;
        }
        .test-description {
            margin-top: 5px;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>全息数据物理正确性测试报告</h1>
        <p>生成时间：2025-05-16 21:37:05</p>

        <div class="section">
            <h2>测试摘要</h2>
            <p>本报告总结了全息数据物理正确性测试的结果。</p>

            <h3>测试结果</h3>
            <ul>
                <li>运行测试：7</li>
                <li>通过测试：7</li>
                <li>失败测试：0</li>
                <li>错误测试：0</li>
            </ul>
        </div>

        <div class="section">
            <h2>测试详情</h2>

        </div>

        <div class="section">
            <h2>全息数据物理原理</h2>

            <h3>编码和解码</h3>
            <p>全息编码是将信息分布在整个数据中的过程，使得部分数据可以恢复整体信息。常见的全息编码方法包括：</p>
            <ul>
                <li>傅里叶变换：将信息从空间域转换到频率域</li>
                <li>离散余弦变换：类似于傅里叶变换，但只使用实数</li>
                <li>小波变换：在时间和频率上同时具有局部性</li>
            </ul>

            <h3>部分信息恢复</h3>
            <p>全息数据的一个重要特性是可以从部分数据中恢复整体信息，尽管可能有一定的误差。</p>

            <h3>信息分布</h3>
            <p>在全息编码中，信息通常分布在整个数据中，但不同频率分量的能量分布可能不同。通常，低频分量包含更多的能量。</p>
        </div>
    </div>
</body>
</html>
