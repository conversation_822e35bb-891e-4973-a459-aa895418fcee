"""
博弈优化资源调度算法直接测试

这是一个直接测试脚本，不依赖于项目的其他部分。
"""

import numpy as np
from typing import Any, Dict, List, Optional, Tuple, Union, Callable
from abc import ABC, abstractmethod
import time
import random


# 定义算法接口
class AlgorithmInterface(ABC):
    """超越态算法接口基类"""
    
    @abstractmethod
    def __init__(self, **kwargs):
        """初始化算法"""
        pass
    
    @abstractmethod
    def compute(self, input_data: Any, **kwargs) -> Any:
        """执行算法计算"""
        pass
    
    @abstractmethod
    def get_metadata(self) -> Dict[str, Any]:
        """获取算法元数据"""
        pass
    
    @abstractmethod
    def is_compatible_with(self, other_algorithm: 'AlgorithmInterface') -> bool:
        """检查与其他算法的兼容性"""
        pass
    
    @abstractmethod
    def get_performance_metrics(self) -> Dict[str, float]:
        """获取性能指标"""
        pass


# 定义博弈模型构建器
class GameModelBuilder:
    """博弈模型构建器"""
    
    def __init__(self, game_type: str = 'non_cooperative', random_seed: Optional[int] = None):
        """初始化博弈模型构建器"""
        self.game_type = game_type
        self.random_seed = random_seed
        
        # 初始化随机数生成器
        self.rng = random.Random(random_seed)
    
    def build_model(self, players: List[Dict[str, Any]], resources: List[Dict[str, Any]], 
                   tasks: List[Dict[str, Any]], constraints: Dict[str, Any], 
                   preferences: Dict[str, Any]) -> Dict[str, Any]:
        """构建博弈模型"""
        # 创建基础模型
        model = {
            'players': players,
            'resources': resources,
            'tasks': tasks,
            'constraints': constraints,
            'preferences': preferences,
            'game_type': self.game_type
        }
        
        # 添加效用函数
        model['utility_functions'] = self._generate_utility_functions(players, preferences)
        
        return model
    
    def _generate_utility_functions(self, players: List[Dict[str, Any]], 
                                  preferences: Dict[str, Any]) -> Dict[str, Callable]:
        """生成效用函数"""
        utility_functions = {}
        
        for player in players:
            player_id = player['id']
            
            # 创建效用函数
            def utility_function(resources, tasks, player_id=player_id):
                # 简单的效用函数：资源数量 + 任务数量
                return len(resources) + len(tasks)
            
            utility_functions[player_id] = utility_function
        
        return utility_functions


# 定义纳什均衡求解器
class NashEquilibriumSolver:
    """纳什均衡求解器"""
    
    def __init__(self, max_iterations: int = 100, tolerance: float = 1e-6,
                use_parallel: bool = True, num_workers: int = 4,
                random_seed: Optional[int] = None):
        """初始化纳什均衡求解器"""
        self.max_iterations = max_iterations
        self.tolerance = tolerance
        self.use_parallel = use_parallel
        self.num_workers = num_workers
        self.random_seed = random_seed
        
        # 初始化随机数生成器
        self.rng = random.Random(random_seed)
    
    def find_equilibrium(self, game_model: Dict[str, Any]) -> Dict[str, Any]:
        """求解纳什均衡"""
        # 获取玩家
        players = game_model.get('players', [])
        
        # 初始化均衡
        equilibrium = {}
        
        # 为每个玩家分配随机策略
        for player in players:
            player_id = player['id']
            equilibrium[player_id] = {'strategy': 'random'}
        
        return equilibrium


# 定义资源分配分析器
class ResourceAllocationAnalyzer:
    """资源分配分析器"""
    
    def __init__(self, verbose: bool = False):
        """初始化资源分配分析器"""
        self.verbose = verbose
    
    def analyze(self, players: List[Dict[str, Any]], resources: List[Dict[str, Any]], 
               tasks: List[Dict[str, Any]], allocation: Dict[str, Any], 
               utilities: Dict[str, float]) -> Dict[str, float]:
        """分析资源分配结果"""
        # 计算公平性指数
        fairness_index = self.calculate_fairness_index(utilities)
        
        # 计算资源利用率
        resource_utilization = self.calculate_resource_utilization(resources, allocation)
        
        # 计算任务完成率
        task_completion_rate = self.calculate_task_completion_rate(tasks, allocation)
        
        # 构建结果
        results = {
            'fairness_index': fairness_index,
            'resource_utilization': resource_utilization,
            'task_completion_rate': task_completion_rate
        }
        
        # 如果启用了详细输出，则打印结果
        if self.verbose:
            print("=" * 50)
            print("资源分配分析")
            print("=" * 50)
            for key, value in results.items():
                print(f"{key}: {value:.6f}")
            print("=" * 50)
        
        return results
    
    def calculate_fairness_index(self, utilities: Dict[str, float]) -> float:
        """计算公平性指数"""
        # 获取效用值列表
        utility_values = list(utilities.values())
        
        # 如果效用值列表为空，则返回1.0（完全公平）
        if not utility_values:
            return 1.0
        
        # 计算公平性指数
        sum_utilities = sum(utility_values)
        sum_squared_utilities = sum(u ** 2 for u in utility_values)
        
        # 避免除以零
        if sum_squared_utilities == 0:
            return 1.0
        
        # 计算公平性指数
        n = len(utility_values)
        fairness_index = (sum_utilities ** 2) / (n * sum_squared_utilities)
        
        return fairness_index
    
    def calculate_resource_utilization(self, resources: List[Dict[str, Any]], 
                                     allocation: Dict[str, Any]) -> float:
        """计算资源利用率"""
        # 获取资源分配
        player_resources = allocation.get('player_resources', {})
        
        # 计算资源利用率
        total_capacity = 0.0
        total_allocated = 0.0
        
        for resource in resources:
            resource_id = resource['id']
            capacity = resource.get('capacity', 1.0)
            
            # 计算总容量
            total_capacity += capacity
            
            # 计算已分配的资源量
            allocated = 0.0
            for player_id, player_allocation in player_resources.items():
                allocated += player_allocation.get(resource_id, 0.0)
            
            # 更新总分配量
            total_allocated += min(allocated, capacity)  # 不超过容量
        
        # 避免除以零
        if total_capacity == 0:
            return 0.0
        
        # 计算资源利用率
        resource_utilization = total_allocated / total_capacity
        
        return resource_utilization
    
    def calculate_task_completion_rate(self, tasks: List[Dict[str, Any]], 
                                     allocation: Dict[str, Any]) -> float:
        """计算任务完成率"""
        # 获取任务分配
        task_assignments = allocation.get('task_assignments', {})
        
        # 计算任务完成率
        completed_tasks = 0
        total_tasks = len(tasks)
        
        for task in tasks:
            task_id = task['id']
            assigned_players = task_assignments.get(task_id, [])
            
            # 如果任务有分配给玩家，则认为已完成
            if assigned_players:
                completed_tasks += 1
        
        # 避免除以零
        if total_tasks == 0:
            return 1.0
        
        # 计算任务完成率
        task_completion_rate = completed_tasks / total_tasks
        
        return task_completion_rate


# 定义博弈优化资源调度算法
class GameTheoreticScheduler(AlgorithmInterface):
    """博弈优化资源调度算法"""
    
    def __init__(self, game_type: str = 'non_cooperative',
                 max_iterations: int = 100,
                 tolerance: float = 1e-6,
                 use_parallel: bool = True,
                 num_workers: int = 4,
                 adaptive_strategy: bool = True,
                 fairness_weight: float = 0.5,
                 efficiency_weight: float = 0.5,
                 **kwargs):
        """初始化博弈优化资源调度算法"""
        self.game_type = game_type
        self.max_iterations = max_iterations
        self.tolerance = tolerance
        self.use_parallel = use_parallel
        self.num_workers = num_workers
        self.adaptive_strategy = adaptive_strategy
        self.fairness_weight = fairness_weight
        self.efficiency_weight = efficiency_weight
        
        # 性能指标
        self._performance_metrics = {
            'total_time': 0.0,
            'iterations': 0,
            'convergence_achieved': False,
            'final_utility': 0.0,
            'fairness_index': 0.0,
            'resource_utilization': 0.0
        }
        
        # 创建博弈模型构建器、纳什均衡求解器和资源分配分析器
        self.model_builder = GameModelBuilder(game_type=game_type)
        self.nash_solver = NashEquilibriumSolver(
            max_iterations=max_iterations,
            tolerance=tolerance,
            use_parallel=use_parallel,
            num_workers=num_workers
        )
        self.allocation_analyzer = ResourceAllocationAnalyzer()
    
    def compute(self, input_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """执行博弈优化资源调度计算"""
        # 记录开始时间
        start_time = time.time()
        
        # 解析输入数据
        players = input_data.get('players', [])
        resources = input_data.get('resources', [])
        tasks = input_data.get('tasks', [])
        constraints = input_data.get('constraints', {})
        preferences = input_data.get('preferences', {})
        
        # 构建博弈模型
        game_model = self.model_builder.build_model(
            players=players,
            resources=resources,
            tasks=tasks,
            constraints=constraints,
            preferences=preferences
        )
        
        # 求解纳什均衡
        equilibrium = self.nash_solver.find_equilibrium(game_model)
        
        # 根据均衡计算资源分配
        allocation = self._compute_allocation(game_model, equilibrium)
        
        # 计算效用值
        utilities = self._calculate_utilities(game_model, allocation)
        
        # 分析资源分配结果
        analysis = self.allocation_analyzer.analyze(
            players=players,
            resources=resources,
            tasks=tasks,
            allocation=allocation,
            utilities=utilities
        )
        
        # 记录结束时间
        end_time = time.time()
        
        # 更新性能指标
        self._performance_metrics.update({
            'total_time': end_time - start_time,
            'iterations': 1,
            'convergence_achieved': True,
            'final_utility': sum(utilities.values()),
            'fairness_index': analysis['fairness_index'],
            'resource_utilization': analysis['resource_utilization']
        })
        
        # 返回结果
        return {
            'allocation': allocation,
            'utilities': utilities,
            'equilibrium': equilibrium,
            'iterations': 1,
            'convergence_achieved': True,
            'performance': self.get_performance_metrics(),
            'analysis': analysis
        }
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取算法元数据"""
        return {
            'name': 'GameTheoreticScheduler',
            'version': '1.0.0',
            'description': '博弈优化资源调度算法，用于在多智能体环境中实现高效资源分配',
            'parameters': {
                'game_type': self.game_type,
                'max_iterations': self.max_iterations,
                'tolerance': self.tolerance,
                'use_parallel': self.use_parallel,
                'num_workers': self.num_workers,
                'adaptive_strategy': self.adaptive_strategy,
                'fairness_weight': self.fairness_weight,
                'efficiency_weight': self.efficiency_weight
            },
            'performance_metrics': self.get_performance_metrics()
        }
    
    def is_compatible_with(self, other_algorithm: AlgorithmInterface) -> bool:
        """检查与其他算法的兼容性"""
        # 检查其他算法是否是GameTheoreticScheduler的实例
        if not isinstance(other_algorithm, GameTheoreticScheduler):
            return False
        
        # 检查其他算法的参数是否与当前算法兼容
        other_metadata = other_algorithm.get_metadata()
        
        # 检查博弈类型是否兼容
        if other_metadata['parameters']['game_type'] != self.game_type:
            return False
        
        return True
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """获取性能指标"""
        return self._performance_metrics
    
    def _compute_allocation(self, game_model: Dict[str, Any], equilibrium: Dict[str, Any]) -> Dict[str, Any]:
        """根据均衡计算资源分配"""
        # 获取玩家、资源和任务
        players = game_model.get('players', [])
        resources = game_model.get('resources', [])
        tasks = game_model.get('tasks', [])
        
        # 初始化分配结果
        allocation = {
            'player_resources': {player['id']: {} for player in players},
            'task_assignments': {task['id']: [] for task in tasks}
        }
        
        # 随机分配资源和任务
        for player in players:
            player_id = player['id']
            
            # 随机分配资源
            for resource in resources:
                resource_id = resource['id']
                capacity = resource.get('capacity', 1.0)
                
                # 随机分配资源
                allocation_amount = random.uniform(0, capacity / len(players))
                allocation['player_resources'][player_id][resource_id] = allocation_amount
            
            # 随机分配任务
            for task in tasks:
                task_id = task['id']
                
                # 随机决定是否分配任务
                if random.random() > 0.5:
                    allocation['task_assignments'][task_id].append(player_id)
        
        return allocation
    
    def _calculate_utilities(self, game_model: Dict[str, Any], allocation: Dict[str, Any]) -> Dict[str, float]:
        """计算各玩家的效用值"""
        # 获取玩家和效用函数
        players = game_model.get('players', [])
        utility_functions = game_model.get('utility_functions', {})
        
        # 初始化效用值
        utilities = {}
        
        # 计算各玩家的效用值
        for player in players:
            player_id = player['id']
            
            # 获取玩家的资源分配
            player_resources = allocation['player_resources'].get(player_id, {})
            
            # 获取玩家的任务分配
            player_tasks = []
            for task_id, assigned_players in allocation['task_assignments'].items():
                if player_id in assigned_players:
                    player_tasks.append(task_id)
            
            # 计算效用值
            utility_function = utility_functions.get(player_id)
            if utility_function:
                utilities[player_id] = utility_function(player_resources, player_tasks)
            else:
                # 默认效用函数：资源数量 + 任务数量
                utilities[player_id] = len(player_resources) + len(player_tasks)
        
        return utilities


def create_test_data():
    """创建测试数据"""
    # 创建玩家
    players = [
        {'id': 'player1', 'name': 'Player 1'},
        {'id': 'player2', 'name': 'Player 2'},
        {'id': 'player3', 'name': 'Player 3'}
    ]
    
    # 创建资源
    resources = [
        {'id': 'cpu', 'name': 'CPU', 'capacity': 100.0},
        {'id': 'memory', 'name': 'Memory', 'capacity': 256.0},
        {'id': 'storage', 'name': 'Storage', 'capacity': 1024.0}
    ]
    
    # 创建任务
    tasks = [
        {'id': 'task1', 'name': 'Task 1', 'requirements': {'cpu': 20.0, 'memory': 64.0}},
        {'id': 'task2', 'name': 'Task 2', 'requirements': {'memory': 128.0, 'storage': 512.0}},
        {'id': 'task3', 'name': 'Task 3', 'requirements': {'cpu': 30.0, 'storage': 256.0}}
    ]
    
    # 创建约束条件
    constraints = {
        'resource_constraints': {
            'cpu': {'min': 0.0, 'max': 100.0},
            'memory': {'min': 0.0, 'max': 256.0},
            'storage': {'min': 0.0, 'max': 1024.0}
        },
        'task_constraints': {
            'task1': {'min_players': 1, 'max_players': 2},
            'task2': {'min_players': 1, 'max_players': 2},
            'task3': {'min_players': 1, 'max_players': 2}
        }
    }
    
    # 创建偏好设置
    preferences = {
        'player1': {'cpu': 0.8, 'memory': 0.5, 'storage': 0.3, 'task1': 0.9, 'task2': 0.4, 'task3': 0.2},
        'player2': {'cpu': 0.3, 'memory': 0.9, 'storage': 0.6, 'task1': 0.2, 'task2': 0.8, 'task3': 0.5},
        'player3': {'cpu': 0.5, 'memory': 0.4, 'storage': 0.9, 'task1': 0.3, 'task2': 0.6, 'task3': 0.9}
    }
    
    return players, resources, tasks, constraints, preferences


def main():
    """主函数"""
    # 创建测试数据
    players, resources, tasks, constraints, preferences = create_test_data()
    
    # 创建输入数据
    input_data = {
        'players': players,
        'resources': resources,
        'tasks': tasks,
        'constraints': constraints,
        'preferences': preferences
    }
    
    # 创建调度器
    scheduler = GameTheoreticScheduler(
        game_type='non_cooperative',
        max_iterations=50,
        tolerance=1e-6,
        use_parallel=True,
        num_workers=4,
        adaptive_strategy=True,
        fairness_weight=0.5,
        efficiency_weight=0.5
    )
    
    # 执行计算
    print("执行博弈优化资源调度计算...")
    start_time = time.time()
    result = scheduler.compute(input_data)
    end_time = time.time()
    
    print(f"计算完成，耗时: {end_time - start_time:.2f}秒")
    
    # 提取结果
    allocation = result['allocation']
    utilities = result['utilities']
    equilibrium = result['equilibrium']
    iterations = result['iterations']
    convergence_achieved = result['convergence_achieved']
    performance = result['performance']
    analysis = result['analysis']
    
    # 打印结果
    print(f"迭代次数: {iterations}")
    print(f"是否收敛: {convergence_achieved}")
    print(f"最终效用: {performance['final_utility']:.6f}")
    print(f"公平性指数: {analysis['fairness_index']:.6f}")
    print(f"资源利用率: {analysis['resource_utilization']:.6f}")
    print(f"任务完成率: {analysis['task_completion_rate']:.6f}")
    
    # 打印资源分配
    print("\n资源分配:")
    for player_id, resources in allocation['player_resources'].items():
        print(f"  {player_id}:")
        for resource_id, amount in resources.items():
            print(f"    {resource_id}: {amount:.2f}")
    
    # 打印任务分配
    print("\n任务分配:")
    for task_id, players in allocation['task_assignments'].items():
        print(f"  {task_id}: {', '.join(players)}")
    
    # 打印效用值
    print("\n效用值:")
    for player_id, utility in utilities.items():
        print(f"  {player_id}: {utility:.2f}")
    
    print("\n测试成功完成！")


if __name__ == "__main__":
    main()
