{"rustc": 13226066032359371072, "features": "[\"default\"]", "declared_features": "[\"cli\", \"default\", \"flight-sql-experimental\", \"tls\", \"tokio\"]", "target": 15226427695780953572, "profile": 15657897354478470176, "path": 7380031216109327461, "deps": [[2706460456408817945, "futures", false, 10750231164116568363], [5370994501882384776, "arrow_schema", false, 92595926931868472], [5377160196570360979, "arrow_cast", false, 10893139984898086066], [6244480430978224180, "arrow_buffer", false, 1101207356586788460], [9298649433536336071, "prost", false, 4182852693182887228], [9991401082583515693, "tonic", false, 8808944659197741667], [11507250275887045969, "arrow_array", false, 1387162509170326270], [11824034268484523742, "arrow_ipc", false, 11653613958821745262], [13077212702700853852, "base64", false, 40038684936539116], [16066129441945555748, "bytes", false, 2626756779534997469], [16470553738848018267, "prost_types", false, 105126799046711215]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/arrow-flight-51e0bde858857939/dep-lib-arrow_flight", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}