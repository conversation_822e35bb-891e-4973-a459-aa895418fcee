{"rustc": 13226066032359371072, "features": "[\"arrow-csv\", \"arrow-ipc\", \"arrow-json\", \"csv\", \"default\", \"ipc\", \"json\"]", "declared_features": "[\"arrow-csv\", \"arrow-ipc\", \"arrow-json\", \"canonical_extension_types\", \"chrono-tz\", \"csv\", \"default\", \"ffi\", \"force_validate\", \"ipc\", \"ipc_compression\", \"json\", \"prettyprint\", \"pyarrow\", \"pyo3\", \"test_utils\"]", "target": 6254409934500757753, "profile": 2241668132362809309, "path": 5873770993934851230, "deps": [[359296887094853619, "arrow_json", false, 14726168706139649697], [457631175462367683, "arrow_select", false, 16949465469628705462], [2697301849534791614, "arrow_csv", false, 13629197106071807901], [4312534352495186819, "arrow_string", false, 2629150260531267932], [4867563676105868085, "arrow_row", false, 6214625210925240996], [5370994501882384776, "arrow_schema", false, 12637599982198294164], [5377160196570360979, "arrow_cast", false, 14763837457583589410], [6244480430978224180, "arrow_buffer", false, 14299475262679452691], [8537392653425637794, "arrow_ord", false, 16509662279542468169], [8612977049395292279, "arrow_arith", false, 12324887686635307692], [11507250275887045969, "arrow_array", false, 6463442442524130483], [11824034268484523742, "arrow_ipc", false, 17200873135361288127], [12709051586360424948, "arrow_data", false, 11836425489378231442]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/arrow-2a1a71686d4752fb/dep-lib-arrow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}