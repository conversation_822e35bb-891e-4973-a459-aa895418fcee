{"rustc": 13226066032359371072, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17467636112133979524, "path": 3511054296942749999, "deps": [[5103565458935487, "futures_io", false, 13345288332962097788], [1615478164327904835, "pin_utils", false, 16651227997794524394], [1811549171721445101, "futures_channel", false, 7117039781080843523], [1906322745568073236, "pin_project_lite", false, 431471450991212587], [3129130049864710036, "memchr", false, 2985287970849895116], [6955678925937229351, "slab", false, 742437048718792916], [7013762810557009322, "futures_sink", false, 7053051807862522950], [7620660491849607393, "futures_core", false, 3738023453919675176], [10565019901765856648, "futures_macro", false, 14179645623399560976], [16240732885093539806, "futures_task", false, 14176287850545725209]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-485f2af78750c69b/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}