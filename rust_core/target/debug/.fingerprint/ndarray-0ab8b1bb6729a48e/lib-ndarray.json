{"rustc": 13226066032359371072, "features": "[\"default\", \"std\"]", "declared_features": "[\"approx\", \"approx-0_5\", \"blas\", \"cblas-sys\", \"default\", \"docs\", \"libc\", \"matrixmultiply-threading\", \"rayon\", \"rayon_\", \"serde\", \"serde-1\", \"std\", \"test\"]", "target": 2233090415856294416, "profile": 15657897354478470176, "path": 429488923856175601, "deps": [[5157631553186200874, "num_traits", false, 9155455488443963496], [12319020793864570031, "num_complex", false, 15004055040890373913], [15709748443193639506, "rawpointer", false, 15897979953384009314], [15826188163127377936, "matrixmultiply", false, 1573020654766679164], [16795989132585092538, "num_integer", false, 17125775120555800463]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ndarray-0ab8b1bb6729a48e/dep-lib-ndarray", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}