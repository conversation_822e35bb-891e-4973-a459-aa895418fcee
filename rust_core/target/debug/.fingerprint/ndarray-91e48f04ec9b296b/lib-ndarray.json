{"rustc": 13226066032359371072, "features": "[\"default\", \"std\"]", "declared_features": "[\"approx\", \"approx-0_5\", \"blas\", \"cblas-sys\", \"default\", \"docs\", \"libc\", \"matrixmultiply-threading\", \"rayon\", \"rayon_\", \"serde\", \"serde-1\", \"std\", \"test\"]", "target": 2233090415856294416, "profile": 2241668132362809309, "path": 429488923856175601, "deps": [[5157631553186200874, "num_traits", false, 16787472827759539394], [12319020793864570031, "num_complex", false, 12225366026564709614], [15709748443193639506, "rawpointer", false, 15797172221423081889], [15826188163127377936, "matrixmultiply", false, 8219961805580714873], [16795989132585092538, "num_integer", false, 17079396202525017675]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ndarray-91e48f04ec9b296b/dep-lib-ndarray", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}