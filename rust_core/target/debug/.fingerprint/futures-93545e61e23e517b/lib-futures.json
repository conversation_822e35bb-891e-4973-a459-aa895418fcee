{"rustc": 13226066032359371072, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 13318305459243126790, "path": 11789211180150123972, "deps": [[5103565458935487, "futures_io", false, 10300281526247872792], [1811549171721445101, "futures_channel", false, 3321927880123255341], [7013762810557009322, "futures_sink", false, 8622789913132139204], [7620660491849607393, "futures_core", false, 7643683528266894211], [10629569228670356391, "futures_util", false, 7021962610964873032], [12779779637805422465, "futures_executor", false, 1975188349543800210], [16240732885093539806, "futures_task", false, 2707135702929036295]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-93545e61e23e517b/dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}