{"rustc": 13226066032359371072, "features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"default\", \"extension-module\", \"indoc\", \"macros\", \"pyo3-macros\", \"unindent\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"anyhow\", \"auto-initialize\", \"chrono\", \"default\", \"either\", \"experimental-inspect\", \"extension-module\", \"eyre\", \"full\", \"generate-import-lib\", \"hashbrown\", \"indexmap\", \"indoc\", \"inventory\", \"macros\", \"multiple-pymethods\", \"nightly\", \"num-bigint\", \"num-complex\", \"pyo3-macros\", \"rust_decimal\", \"serde\", \"smallvec\", \"unindent\"]", "target": 11844115677734887287, "profile": 17744942687396784568, "path": 13187180090976813578, "deps": [[629381703529241162, "indoc", false, 4319403548948822943], [2062481783838671931, "parking_lot", false, 14077842671351088507], [2924422107542798392, "libc", false, 5477580387888684338], [7614379793313454108, "pyo3_ffi", false, 14919487981623370260], [10411997081178400487, "cfg_if", false, 17603217987811522046], [12478333222061076338, "pyo3_macros", false, 1224055917755416678], [14643204177830147187, "memoffset", false, 7710589800712282442], [14748792705540276325, "unindent", false, 1207919907841720791], [17396333227350152061, "build_script_build", false, 16256598346626779647], [17740406991540315704, "portable_atomic", false, 5252477130779229216]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pyo3-ac37da54de77726d/dep-lib-pyo3", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}