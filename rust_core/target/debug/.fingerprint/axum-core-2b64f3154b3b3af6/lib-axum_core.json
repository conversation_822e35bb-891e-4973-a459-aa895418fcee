{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 2241668132362809309, "path": 15127117283758481443, "deps": [[784494742817713399, "tower_service", false, 14782059218744798820], [1906322745568073236, "pin_project_lite", false, 431471450991212587], [1999399154011168049, "rustversion", false, 12579807949576350984], [2517136641825875337, "sync_wrapper", false, 17639110624607000279], [7712452662827335977, "tower_layer", false, 2355903465708312296], [9010263965687315507, "http", false, 10913597338917236200], [10229185211513642314, "mime", false, 9501530197875289932], [10629569228670356391, "futures_util", false, 16001624943992241104], [11946729385090170470, "async_trait", false, 11845848770405234667], [14084095096285906100, "http_body", false, 6088749174087079432], [16066129441945555748, "bytes", false, 7729698581097156661], [16900715236047033623, "http_body_util", false, 10754447612119515352]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-core-2b64f3154b3b3af6/dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}