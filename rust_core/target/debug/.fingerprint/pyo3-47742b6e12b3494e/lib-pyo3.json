{"rustc": 13226066032359371072, "features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"default\", \"extension-module\", \"indoc\", \"macros\", \"pyo3-macros\", \"unindent\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"anyhow\", \"auto-initialize\", \"chrono\", \"default\", \"either\", \"experimental-inspect\", \"extension-module\", \"eyre\", \"full\", \"generate-import-lib\", \"hashbrown\", \"indexmap\", \"indoc\", \"inventory\", \"macros\", \"multiple-pymethods\", \"nightly\", \"num-bigint\", \"num-complex\", \"pyo3-macros\", \"rust_decimal\", \"serde\", \"smallvec\", \"unindent\"]", "target": 11844115677734887287, "profile": 4317675006741846053, "path": 13187180090976813578, "deps": [[629381703529241162, "indoc", false, 4319403548948822943], [2062481783838671931, "parking_lot", false, 17713790772618051331], [2924422107542798392, "libc", false, 1541233552286326018], [7614379793313454108, "pyo3_ffi", false, 8154844082375363247], [10411997081178400487, "cfg_if", false, 17229243447972769405], [12478333222061076338, "pyo3_macros", false, 12719014440125043343], [14643204177830147187, "memoffset", false, 15090173407198264432], [14748792705540276325, "unindent", false, 8345992790266990493], [17396333227350152061, "build_script_build", false, 14382360662567741225], [17740406991540315704, "portable_atomic", false, 16047426576461538389]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pyo3-47742b6e12b3494e/dep-lib-pyo3", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}