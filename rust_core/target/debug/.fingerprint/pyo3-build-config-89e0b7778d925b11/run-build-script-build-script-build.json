{"rustc": 13226066032359371072, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[11057642215492122650, "build_script_build", false, 8844086077579994856]], "local": [{"RerunIfEnvChanged": {"var": "PYO3_CONFIG_FILE", "val": null}}, {"RerunIfEnvChanged": {"var": "PYO3_NO_PYTHON", "val": null}}, {"RerunIfEnvChanged": {"var": "PYO3_ENVIRONMENT_SIGNATURE", "val": null}}, {"RerunIfEnvChanged": {"var": "PYO3_PYTHON", "val": null}}, {"RerunIfEnvChanged": {"var": "VIRTUAL_ENV", "val": "/home/<USER>/CascadeProjects/Zed/venv"}}, {"RerunIfEnvChanged": {"var": "CONDA_PREFIX", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}