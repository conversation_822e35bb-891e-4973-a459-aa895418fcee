{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 15657897354478470176, "path": 15127117283758481443, "deps": [[784494742817713399, "tower_service", false, 3921364612621644100], [1906322745568073236, "pin_project_lite", false, 10219473703885776191], [1999399154011168049, "rustversion", false, 12579807949576350984], [2517136641825875337, "sync_wrapper", false, 2482890946333381759], [7712452662827335977, "tower_layer", false, 10697365192507209817], [9010263965687315507, "http", false, 911498097730049735], [10229185211513642314, "mime", false, 12263391148771443117], [10629569228670356391, "futures_util", false, 7021962610964873032], [11946729385090170470, "async_trait", false, 11845848770405234667], [14084095096285906100, "http_body", false, 3497072618099069283], [16066129441945555748, "bytes", false, 2626756779534997469], [16900715236047033623, "http_body_util", false, 7771789399703501887]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-core-424738c9d45ce6e2/dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}