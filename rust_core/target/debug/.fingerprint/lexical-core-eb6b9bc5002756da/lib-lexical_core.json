{"rustc": 13226066032359371072, "features": "[\"floats\", \"integers\", \"lexical-parse-float\", \"lexical-parse-integer\", \"lexical-write-float\", \"lexical-write-integer\", \"parse\", \"parse-floats\", \"parse-integers\", \"write\", \"write-floats\", \"write-integers\"]", "declared_features": "[\"compact\", \"default\", \"f128\", \"f16\", \"floats\", \"format\", \"integers\", \"lexical-parse-float\", \"lexical-parse-integer\", \"lexical-write-float\", \"lexical-write-integer\", \"lint\", \"parse\", \"parse-floats\", \"parse-integers\", \"power-of-two\", \"radix\", \"std\", \"write\", \"write-floats\", \"write-integers\"]", "target": 5789473667312678097, "profile": 2241668132362809309, "path": 13866431313623127622, "deps": [[757373295804312759, "lexical_parse_float", false, 16918099984315777418], [3800171689049377331, "lexical_util", false, 4414340926958660850], [5207363620914184311, "lexical_write_integer", false, 5336971934190327833], [15474330194059950047, "lexical_parse_integer", false, 8973342502414853860], [17217489311556736288, "lexical_write_float", false, 5805619365680907122]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/lexical-core-eb6b9bc5002756da/dep-lib-lexical_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}