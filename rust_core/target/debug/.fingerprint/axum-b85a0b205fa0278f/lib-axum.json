{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"__private_docs\", \"default\", \"form\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 13920321295547257648, "profile": 2241668132362809309, "path": 17700493244490661120, "deps": [[40386456601120721, "percent_encoding", false, 5647422406843111675], [784494742817713399, "tower_service", false, 14782059218744798820], [1906322745568073236, "pin_project_lite", false, 431471450991212587], [1999399154011168049, "rustversion", false, 12579807949576350984], [2517136641825875337, "sync_wrapper", false, 17639110624607000279], [3129130049864710036, "memchr", false, 2985287970849895116], [4359148418957042248, "axum_core", false, 2279277022013668995], [5695049318159433696, "tower", false, 6259505817353434977], [7695812897323945497, "itoa", false, 11733316141510970148], [7712452662827335977, "tower_layer", false, 2355903465708312296], [9010263965687315507, "http", false, 10913597338917236200], [9678799920983747518, "matchit", false, 15751422357933584151], [9689903380558560274, "serde", false, 12458167863772874876], [10229185211513642314, "mime", false, 9501530197875289932], [10629569228670356391, "futures_util", false, 16001624943992241104], [11946729385090170470, "async_trait", false, 11845848770405234667], [14084095096285906100, "http_body", false, 6088749174087079432], [16066129441945555748, "bytes", false, 7729698581097156661], [16900715236047033623, "http_body_util", false, 10754447612119515352]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-b85a0b205fa0278f/dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}