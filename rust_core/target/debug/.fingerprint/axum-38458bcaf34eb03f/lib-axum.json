{"rustc": 13226066032359371072, "features": "[]", "declared_features": "[\"__private_docs\", \"default\", \"form\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 13920321295547257648, "profile": 15657897354478470176, "path": 17700493244490661120, "deps": [[40386456601120721, "percent_encoding", false, 2547838272034858867], [784494742817713399, "tower_service", false, 3921364612621644100], [1906322745568073236, "pin_project_lite", false, 10219473703885776191], [1999399154011168049, "rustversion", false, 12579807949576350984], [2517136641825875337, "sync_wrapper", false, 2482890946333381759], [3129130049864710036, "memchr", false, 8524624104848506511], [4359148418957042248, "axum_core", false, 14870567217493868495], [5695049318159433696, "tower", false, 1977481503671568961], [7695812897323945497, "itoa", false, 10344182848757983763], [7712452662827335977, "tower_layer", false, 10697365192507209817], [9010263965687315507, "http", false, 911498097730049735], [9678799920983747518, "matchit", false, 14887712327452020625], [9689903380558560274, "serde", false, 11419664211998439373], [10229185211513642314, "mime", false, 12263391148771443117], [10629569228670356391, "futures_util", false, 7021962610964873032], [11946729385090170470, "async_trait", false, 11845848770405234667], [14084095096285906100, "http_body", false, 3497072618099069283], [16066129441945555748, "bytes", false, 2626756779534997469], [16900715236047033623, "http_body_util", false, 7771789399703501887]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-38458bcaf34eb03f/dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}