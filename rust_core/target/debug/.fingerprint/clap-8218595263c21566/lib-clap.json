{"rustc": 13226066032359371072, "features": "[\"std\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 11439587820120798860, "path": 7916915258837616218, "deps": [[8750560705953570236, "clap_builder", false, 15934634489153383342]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-8218595263c21566/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}