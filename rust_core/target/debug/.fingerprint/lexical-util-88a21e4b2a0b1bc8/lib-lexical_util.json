{"rustc": 13226066032359371072, "features": "[\"floats\", \"integers\", \"parse\", \"parse-floats\", \"parse-integers\", \"write\", \"write-floats\", \"write-integers\"]", "declared_features": "[\"compact\", \"default\", \"f128\", \"f16\", \"floats\", \"format\", \"integers\", \"lint\", \"parse\", \"parse-floats\", \"parse-integers\", \"power-of-two\", \"radix\", \"std\", \"write\", \"write-floats\", \"write-integers\"]", "target": 2702788345393153884, "profile": 2241668132362809309, "path": 17266738881417524129, "deps": [[13785866025199020095, "static_assertions", false, 10546545467773579847]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/lexical-util-88a21e4b2a0b1bc8/dep-lib-lexical_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}