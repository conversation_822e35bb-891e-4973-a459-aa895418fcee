{"rustc": 13226066032359371072, "features": "[\"bit-set\", \"default\", \"fork\", \"lazy_static\", \"regex-syntax\", \"rusty-fork\", \"std\", \"tempfile\", \"timeout\"]", "declared_features": "[\"alloc\", \"atomic64bit\", \"attr-macro\", \"bit-set\", \"default\", \"default-code-coverage\", \"fork\", \"handle-panics\", \"hardware-rng\", \"lazy_static\", \"no_std\", \"proptest-macro\", \"regex-syntax\", \"rusty-fork\", \"std\", \"tempfile\", \"timeout\", \"unstable\", \"x86\"]", "target": 7997875189344830950, "profile": 15657897354478470176, "path": 6868823376281314207, "deps": [[1441306149310335789, "tempfile", false, 15099599503354987053], [1573238666360410412, "rand_chacha", false, 11866408746887025748], [4344686646292094751, "rusty_fork", false, 16720794295791544564], [5157631553186200874, "num_traits", false, 9155455488443963496], [5692597712387868707, "bit_vec", false, 15547160023757769934], [6166349630582887940, "bitflags", false, 13355940121173629255], [9408802513701742484, "regex_syntax", false, 712524333793950531], [9519969280819313548, "bit_set", false, 15104007228899302575], [13208667028893622512, "rand", false, 4979318978774088965], [14014736296291115408, "unarray", false, 3852919123126648893], [16658906400288386541, "rand_xorshift", false, 6052603365286604058], [17917672826516349275, "lazy_static", false, 3829959076625360447]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/proptest-3c63e98175154318/dep-lib-proptest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}