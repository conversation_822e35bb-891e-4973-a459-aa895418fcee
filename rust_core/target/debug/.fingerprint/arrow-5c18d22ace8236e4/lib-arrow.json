{"rustc": 13226066032359371072, "features": "[\"arrow-csv\", \"arrow-ipc\", \"arrow-json\", \"csv\", \"default\", \"ipc\", \"json\"]", "declared_features": "[\"arrow-csv\", \"arrow-ipc\", \"arrow-json\", \"canonical_extension_types\", \"chrono-tz\", \"csv\", \"default\", \"ffi\", \"force_validate\", \"ipc\", \"ipc_compression\", \"json\", \"prettyprint\", \"pyarrow\", \"pyo3\", \"test_utils\"]", "target": 6254409934500757753, "profile": 15657897354478470176, "path": 5873770993934851230, "deps": [[359296887094853619, "arrow_json", false, 13850100142627515035], [457631175462367683, "arrow_select", false, 2376124187007925526], [2697301849534791614, "arrow_csv", false, 6556028095830227794], [4312534352495186819, "arrow_string", false, 6768787276899458062], [4867563676105868085, "arrow_row", false, 31786436941455935], [5370994501882384776, "arrow_schema", false, 92595926931868472], [5377160196570360979, "arrow_cast", false, 10893139984898086066], [6244480430978224180, "arrow_buffer", false, 1101207356586788460], [8537392653425637794, "arrow_ord", false, 10098646917598116136], [8612977049395292279, "arrow_arith", false, 8690402620330341193], [11507250275887045969, "arrow_array", false, 1387162509170326270], [11824034268484523742, "arrow_ipc", false, 11653613958821745262], [12709051586360424948, "arrow_data", false, 4542784461584690925]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/arrow-5c18d22ace8236e4/dep-lib-arrow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}