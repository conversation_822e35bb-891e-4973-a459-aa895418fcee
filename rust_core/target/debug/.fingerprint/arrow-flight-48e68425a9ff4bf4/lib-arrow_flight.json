{"rustc": 13226066032359371072, "features": "[\"default\"]", "declared_features": "[\"cli\", \"default\", \"flight-sql-experimental\", \"tls\", \"tokio\"]", "target": 15226427695780953572, "profile": 2241668132362809309, "path": 7380031216109327461, "deps": [[2706460456408817945, "futures", false, 3727440048197130027], [5370994501882384776, "arrow_schema", false, 12637599982198294164], [5377160196570360979, "arrow_cast", false, 14763837457583589410], [6244480430978224180, "arrow_buffer", false, 14299475262679452691], [9298649433536336071, "prost", false, 15489849612872413283], [9991401082583515693, "tonic", false, 14453452877715486808], [11507250275887045969, "arrow_array", false, 6463442442524130483], [11824034268484523742, "arrow_ipc", false, 17200873135361288127], [13077212702700853852, "base64", false, 9448018957755325647], [16066129441945555748, "bytes", false, 7729698581097156661], [16470553738848018267, "prost_types", false, 9554294883187530980]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/arrow-flight-48e68425a9ff4bf4/dep-lib-arrow_flight", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}