"""
重构后的持续同调算子测试脚本

这个脚本测试重构后的持续同调算子的基本功能。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

import numpy as np
import matplotlib.pyplot as plt

# 导入我们刚刚重构的模块
# 直接导入我们重构的模块，避免通过src包导入
sys.path.insert(0, os.path.join(os.path.abspath('.'), 'src'))
from operators.topology.homology import PersistentHomologyOperator


def generate_circle_data(n_points=100, noise=0.1):
    """生成圆形点云数据"""
    theta = np.linspace(0, 2*np.pi, n_points)
    x = np.cos(theta) + np.random.normal(0, noise, n_points)
    y = np.sin(theta) + np.random.normal(0, noise, n_points)
    return np.column_stack((x, y))


def test_persistent_homology_operator():
    """测试PersistentHomologyOperator"""
    print("\n测试PersistentHomologyOperator...")

    # 生成圆形点云数据
    circle_data = generate_circle_data(n_points=50, noise=0.05)

    # 创建持续同调算子
    ph_operator = PersistentHomologyOperator(max_dimension=1, method='standard')
    print(f"创建算子: {ph_operator}")

    # 应用算子
    result = ph_operator.apply(circle_data, dimensions=[0, 1])

    # 打印结果
    print(f"持续同调计算结果:")
    print(f"  方法: {result['method']}")
    print(f"  滤值类型: {result['filtration_type']}")

    # 打印0维持续图
    if 0 in result['persistence_diagrams']:
        diagram_0 = result['persistence_diagrams'][0]
        print(f"  0维持续图: {len(diagram_0)}个点")
        if len(diagram_0) > 0:
            print(f"    前5个点: {diagram_0[:5]}")

    # 打印1维持续图
    if 1 in result['persistence_diagrams']:
        diagram_1 = result['persistence_diagrams'][1]
        print(f"  1维持续图: {len(diagram_1)}个点")
        if len(diagram_1) > 0:
            print(f"    前5个点: {diagram_1[:5]}")

    print("PersistentHomologyOperator测试完成")

    return result


def main():
    """主函数"""
    print("开始测试重构后的持续同调算子...")

    # 测试PersistentHomologyOperator
    ph_result = test_persistent_homology_operator()

    print("\n所有测试完成")


if __name__ == "__main__":
    main()
