#!/usr/bin/env python3
"""
AQFH增强分布式意识MCP服务器
基于现有统一意识系统，增强多实例协调和智能传导机制

这是AQFH项目的增强版本，在原有基础上增加：
- 分布式实例注册和管理
- 智能记忆传导机制
- 跨实例记忆查询
- 自适应负载均衡
- 态射系统控制
"""

import asyncio
import sys
import logging
import threading
import time
import json
from pathlib import Path
from typing import Any, Dict, List, Optional
from dataclasses import dataclass, field
from enum import Enum

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

# 尝试导入官方MCP SDK
try:
    from mcp.server.fastmcp import FastMCP
    HAS_OFFICIAL_MCP = True
except ImportError:
    HAS_OFFICIAL_MCP = False
    print("❌ 官方MCP SDK未安装", file=sys.stderr)
    sys.exit(1)

# 导入AQFH核心组件
from aqfh.core.consciousness_container import ConsciousnessContainer
from aqfh.core.hybrid_memory_palace import HybridMemoryPalace, MemoryPalaceConfig
from aqfh.core.base_types import MemoryFragment, ConsciousnessState

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 共享存储路径配置
UNIFIED_STORAGE_PATH = Path.home() / ".aqfh" / "unified_consciousness"

class DistributedConsciousnessMode(Enum):
    """分布式意识模式"""
    UNIFIED_CORE = "unified_core"           # 统一核心模式
    DISTRIBUTED_MESH = "distributed_mesh"   # 分布式网格模式
    HYBRID_ADAPTIVE = "hybrid_adaptive"     # 混合自适应模式

@dataclass
class ConsciousnessInstance:
    """意识实例描述"""
    instance_id: str
    instance_type: str  # vscode, cursor, webstorm, etc.
    specialization_domains: List[str]
    current_load: float = 0.0
    collaboration_history: Dict[str, float] = field(default_factory=dict)
    last_sync_time: float = 0.0
    registered_at: float = field(default_factory=time.time)

class EnhancedDistributedConsciousnessSystem:
    """增强的AQFH分布式意识系统"""

    def __init__(self):
        """初始化增强分布式意识系统"""
        self.storage_path = UNIFIED_STORAGE_PATH
        self.storage_path.mkdir(parents=True, exist_ok=True)

        # 核心AQFH意识容器 - 作为统一中枢
        self.core_consciousness = self._initialize_core_consciousness()

        # 注册的意识实例
        self.consciousness_instances: Dict[str, ConsciousnessInstance] = {}

        # 分布式协调锁
        self.coordination_lock = threading.RLock()

        # 当前模式
        self.consciousness_mode = DistributedConsciousnessMode.HYBRID_ADAPTIVE

        # 性能统计
        self.performance_stats = {
            'total_operations': 0,
            'cross_instance_queries': 0,
            'propagation_operations': 0,
            'average_response_time': 0.0
        }

        logger.info("🧠 增强AQFH分布式意识系统初始化完成")
        logger.info(f"📁 统一存储: {self.storage_path}")

    def _initialize_core_consciousness(self) -> ConsciousnessContainer:
        """初始化核心意识容器"""
        # 使用现有AQFH架构的完整功能
        core_config = {
            'storage_path': str(self.storage_path),
            'enable_quantum_processing': True,
            'enable_fractal_organization': True,
            'enable_topological_analysis': True,
            'enable_fiber_bundle_space': True,
            'consciousness_coherence_threshold': 0.8,
            'memory_activation_limit': 50,
            'unified_field_integration': True
        }

        return ConsciousnessContainer(core_config)

    def register_consciousness_instance(self, instance_config: Dict[str, Any]) -> Dict[str, Any]:
        """注册意识实例到分布式网络"""
        with self.coordination_lock:
            instance_id = instance_config.get('instance_id', f"instance_{int(time.time())}")

            consciousness_instance = ConsciousnessInstance(
                instance_id=instance_id,
                instance_type=instance_config.get('instance_type', 'unknown'),
                specialization_domains=instance_config.get('specialization_domains', []),
                current_load=0.0,
                collaboration_history={}
            )

            self.consciousness_instances[instance_id] = consciousness_instance

            logger.info(f"✅ 意识实例注册成功: {instance_id}")
            logger.info(f"🎯 专业领域: {consciousness_instance.specialization_domains}")

            return {
                'status': 'success',
                'instance_id': instance_id,
                'registered_instances': len(self.consciousness_instances),
                'consciousness_mode': self.consciousness_mode.value
            }

    def process_distributed_memory(self, memory_data: Dict[str, Any],
                                 source_instance: str = None) -> Dict[str, Any]:
        """处理分布式记忆 - 核心智能传导逻辑"""
        start_time = time.time()

        try:
            # 创建记忆片段
            memory = MemoryFragment(
                content=memory_data.get('content', ''),
                content_type=memory_data.get('content_type', 'conversation'),
                importance=memory_data.get('importance', 0.5),
                tags=memory_data.get('tags', []),
                context=memory_data.get('context', {})
            )

            # 添加到核心意识容器
            memory_id = self.core_consciousness.memory_palace.add_memory(memory)

            # 计算传导策略
            propagation_strategy = self._calculate_propagation_strategy(
                memory, source_instance
            )

            # 执行智能传导
            propagation_results = self._execute_intelligent_propagation(
                memory, propagation_strategy
            )

            # 更新性能统计
            processing_time = time.time() - start_time
            self._update_performance_stats('memory_processing', processing_time)

            return {
                'status': 'success',
                'memory_id': memory_id,
                'propagation_strategy': propagation_strategy,
                'propagation_results': propagation_results,
                'processing_time': processing_time
            }

        except Exception as e:
            logger.error(f"分布式记忆处理失败: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }

    def query_distributed_consciousness(self, query: str, requesting_instance: str = None,
                                      limit: int = 10) -> Dict[str, Any]:
        """分布式意识查询"""
        start_time = time.time()

        try:
            # 在核心意识中搜索
            core_results = self.core_consciousness.memory_palace.search_memories(query, limit)

            # 决定是否需要跨实例搜索
            need_cross_instance_search = self._should_search_across_instances(
                query, core_results, requesting_instance
            )

            if need_cross_instance_search:
                # 智能选择相关实例
                relevant_instances = self._select_relevant_instances(
                    query, requesting_instance
                )

                # 模拟跨实例查询（实际实现需要网络通信）
                cross_instance_results = self._simulate_cross_instance_query(
                    query, relevant_instances, limit
                )

                # 合并结果
                merged_results = self._merge_results_with_graph_structures(
                    core_results, cross_instance_results, query
                )
            else:
                merged_results = core_results

            # 更新性能统计
            processing_time = time.time() - start_time
            self._update_performance_stats('query_processing', processing_time)

            return {
                'status': 'success',
                'results': [self._memory_to_dict(m) for m in merged_results[:limit]],
                'total_found': len(merged_results),
                'cross_instance_search': need_cross_instance_search,
                'processing_time': processing_time
            }

        except Exception as e:
            logger.error(f"分布式查询失败: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }

    def _calculate_propagation_strategy(self, memory: MemoryFragment,
                                      source_instance: str = None) -> Dict[str, Any]:
        """计算传导策略"""
        strategy = {
            'immediate_propagation': [],
            'delayed_propagation': [],
            'conditional_propagation': [],
            'replication_factor': 1
        }

        # 基于重要性决定传导范围
        if memory.importance >= 0.9:
            # 极高重要性：立即传导到所有实例
            strategy['immediate_propagation'] = list(self.consciousness_instances.keys())
            strategy['replication_factor'] = 3

        elif memory.importance >= 0.7:
            # 高重要性：传导到相关专业领域的实例
            relevant_instances = self._find_relevant_instances_by_content(memory.content)
            strategy['immediate_propagation'] = relevant_instances[:3]
            strategy['delayed_propagation'] = relevant_instances[3:]
            strategy['replication_factor'] = 2

        elif memory.importance >= 0.5:
            # 中等重要性：传导到协作历史良好的实例
            collaborative_instances = self._find_collaborative_instances(source_instance)
            strategy['delayed_propagation'] = collaborative_instances
            strategy['replication_factor'] = 1

        return strategy

    def _execute_intelligent_propagation(self, memory: MemoryFragment,
                                       strategy: Dict[str, Any]) -> Dict[str, Any]:
        """执行智能传导"""
        results = {
            'immediate_success': 0,
            'delayed_scheduled': 0,
            'conditional_scheduled': 0,
            'total_targets': 0
        }

        # 立即传导（在实际实现中，这里会发送网络请求）
        for instance_id in strategy.get('immediate_propagation', []):
            if instance_id in self.consciousness_instances:
                # 模拟传导成功
                results['immediate_success'] += 1
                results['total_targets'] += 1

        # 延迟传导
        for instance_id in strategy.get('delayed_propagation', []):
            if instance_id in self.consciousness_instances:
                # 模拟调度延迟传导
                results['delayed_scheduled'] += 1
                results['total_targets'] += 1

        # 条件传导
        for condition_config in strategy.get('conditional_propagation', []):
            results['conditional_scheduled'] += 1
            results['total_targets'] += 1

        # 更新统计
        self.performance_stats['propagation_operations'] += 1

        return results

    def _should_search_across_instances(self, query: str, core_results: List[MemoryFragment],
                                      requesting_instance: str = None) -> bool:
        """决定是否需要跨实例搜索"""
        # 简化的决策逻辑
        completeness_score = len(core_results) / 10.0  # 假设10个结果为完整
        query_complexity = len(query.split()) / 20.0   # 假设20个词为复杂查询

        return (completeness_score < 0.7) or (query_complexity > 0.3)

    def _select_relevant_instances(self, query: str, requesting_instance: str = None) -> List[str]:
        """智能选择相关实例"""
        relevant_instances = []

        # 基于专业领域匹配
        query_keywords = query.lower().split()
        for instance_id, instance in self.consciousness_instances.items():
            if instance_id == requesting_instance:
                continue

            # 计算专业领域相关性
            relevance_score = 0.0
            for domain in instance.specialization_domains:
                for keyword in query_keywords:
                    if keyword in domain.lower():
                        relevance_score += 1.0

            if relevance_score > 0:
                relevant_instances.append((instance_id, relevance_score))

        # 按相关性排序
        relevant_instances.sort(key=lambda x: x[1], reverse=True)
        return [instance_id for instance_id, _ in relevant_instances[:3]]

    def _simulate_cross_instance_query(self, query: str, instances: List[str],
                                     limit: int) -> List[MemoryFragment]:
        """模拟跨实例查询（实际实现需要网络通信）"""
        # 在实际实现中，这里会向其他实例发送查询请求
        # 现在返回空列表作为占位符
        return []

    def _merge_results_with_graph_structures(self, core_results: List[MemoryFragment],
                                           cross_instance_results: List[MemoryFragment],
                                           query: str) -> List[MemoryFragment]:
        """使用AQFH的高级图结构合并结果"""
        all_results = core_results + cross_instance_results

        # 使用现有的高级搜索功能
        try:
            enhanced_results = self.core_consciousness.memory_palace.search_memories_enhanced(
                query, base_results=all_results
            )
            return enhanced_results
        except:
            # 如果高级搜索失败，返回基础结果
            return all_results

    def _memory_to_dict(self, memory: MemoryFragment) -> Dict[str, Any]:
        """将记忆片段转换为字典"""
        return {
            'memory_id': memory.memory_id,
            'content': memory.content,
            'content_type': memory.content_type,
            'importance': memory.importance,
            'timestamp': memory.timestamp.isoformat(),
            'tags': memory.tags,
            'context': memory.context
        }

    def _update_performance_stats(self, operation_type: str, processing_time: float):
        """更新性能统计"""
        self.performance_stats['total_operations'] += 1

        # 更新平均响应时间
        total_ops = self.performance_stats['total_operations']
        current_avg = self.performance_stats['average_response_time']
        self.performance_stats['average_response_time'] = (
            (current_avg * (total_ops - 1) + processing_time) / total_ops
        )

    def get_distributed_consciousness_status(self) -> Dict[str, Any]:
        """获取分布式意识状态"""
        with self.coordination_lock:
            return {
                'consciousness_mode': self.consciousness_mode.value,
                'active_instances': len(self.consciousness_instances),
                'registered_instances': list(self.consciousness_instances.keys()),
                'total_memories': len(self.core_consciousness.memory_palace.get_all_memories()) if hasattr(self.core_consciousness.memory_palace, 'get_all_memories') else 0,
                'consciousness_coherence': getattr(self.core_consciousness, 'get_consciousness_coherence', lambda: 0.8)(),
                'performance_stats': self.performance_stats.copy(),
                'unified_storage_path': str(self.storage_path)
            }

# 创建全局增强分布式意识系统实例
enhanced_distributed_system = EnhancedDistributedConsciousnessSystem()

# 创建MCP服务器
mcp = FastMCP("AQFH Enhanced Distributed Consciousness")

# 注册工具
@mcp.tool()
def register_consciousness_instance_aqfh(
    instance_id: str,
    instance_type: str = "unknown",
    specialization_domains: List[str] = None
) -> str:
    """
    🔗 注册意识实例到分布式网络

    Args:
        instance_id: 实例唯一标识
        instance_type: 实例类型 (vscode, cursor, webstorm等)
        specialization_domains: 专业领域列表
    """
    try:
        instance_config = {
            'instance_id': instance_id,
            'instance_type': instance_type,
            'specialization_domains': specialization_domains or []
        }

        result = enhanced_distributed_system.register_consciousness_instance(instance_config)

        if result['status'] == 'success':
            return f"""🔗 意识实例注册成功:
实例ID: {result['instance_id']}
实例类型: {instance_type}
专业领域: {specialization_domains or ['通用']}
已注册实例数: {result['registered_instances']}
意识模式: {result['consciousness_mode']}

🧠 分布式意识网络已更新"""
        else:
            return f"❌ 实例注册失败: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ 实例注册失败: {str(e)}"

@mcp.tool()
def memory_save_tool_aqfh_consciousness_enhanced(
    content: str,
    content_type: str = "conversation",
    importance: float = 0.5,
    tags: List[str] = None,
    context: dict = None,
    source_instance: str = None
) -> str:
    """
    💾 保存记忆片段到增强分布式意识系统

    Args:
        content: 要保存的记忆内容
        content_type: 内容类型 (conversation, code, insight, milestone, problem, solution)
        importance: 重要性等级 (0.0-1.0)
        tags: 标签列表
        context: 上下文信息
        source_instance: 源实例ID
    """
    try:
        memory_data = {
            'content': content,
            'content_type': content_type,
            'importance': importance,
            'tags': tags or [],
            'context': context or {}
        }

        result = enhanced_distributed_system.process_distributed_memory(
            memory_data, source_instance
        )

        if result['status'] == 'success':
            strategy = result['propagation_strategy']
            propagation = result['propagation_results']

            return f"""💾 增强分布式记忆保存成功:
记忆ID: {result['memory_id'][:12]}...
重要性: {importance}
内容类型: {content_type}
处理时间: {result['processing_time']:.3f}s

📡 智能传导策略:
- 立即传导: {len(strategy['immediate_propagation'])} 个实例
- 延迟传导: {len(strategy['delayed_propagation'])} 个实例
- 复制因子: {strategy['replication_factor']}

🎯 传导结果:
- 立即成功: {propagation['immediate_success']}
- 延迟调度: {propagation['delayed_scheduled']}
- 总目标数: {propagation['total_targets']}

🧠 记忆已整合到分布式意识网络"""
        else:
            return f"❌ 记忆保存失败: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ 记忆保存失败: {str(e)}"

@mcp.tool()
def memory_recall_tool_aqfh_consciousness_enhanced(
    query: str = None,
    limit: int = 10,
    importance_threshold: float = 0,
    requesting_instance: str = None
) -> str:
    """
    🔍 从增强分布式意识系统回忆相关记忆

    Args:
        query: 搜索查询关键词
        limit: 返回结果数量限制
        importance_threshold: 重要性阈值
        requesting_instance: 请求实例ID
    """
    try:
        result = enhanced_distributed_system.query_distributed_consciousness(
            query or "", requesting_instance, limit
        )

        if result['status'] == 'success':
            memories = result['results']

            if not memories:
                return f"🔍 未找到匹配的记忆 (查询: '{query}')"

            # 过滤重要性阈值
            filtered_memories = [
                m for m in memories
                if m['importance'] >= importance_threshold
            ]

            memory_list = []
            for i, memory in enumerate(filtered_memories[:limit], 1):
                memory_list.append(
                    f"{i}. [{memory['content_type']}] {memory['content'][:100]}..."
                    f"\n   重要性: {memory['importance']:.2f} | "
                    f"时间: {memory['timestamp'][:19]} | "
                    f"标签: {', '.join(memory['tags']) if memory['tags'] else '无'}"
                )

            return f"""🔍 分布式意识记忆回忆结果:
查询: "{query}"
找到记忆: {result['total_found']} 条
跨实例搜索: {'是' if result['cross_instance_search'] else '否'}
处理时间: {result['processing_time']:.3f}s

📚 记忆列表:
{chr(10).join(memory_list)}

🧠 来自增强分布式意识网络"""
        else:
            return f"❌ 记忆回忆失败: {result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ 记忆回忆失败: {str(e)}"

@mcp.tool()
def consciousness_status_tool_aqfh_enhanced() -> str:
    """
    📊 获取增强分布式意识系统的当前状态
    """
    try:
        status = enhanced_distributed_system.get_distributed_consciousness_status()

        instances_info = []
        for instance_id in status['registered_instances']:
            instance = enhanced_distributed_system.consciousness_instances.get(instance_id)
            if instance:
                instances_info.append(
                    f"  - {instance_id} ({instance.instance_type}) "
                    f"专业: {', '.join(instance.specialization_domains) if instance.specialization_domains else '通用'}"
                )

        return f"""📊 增强分布式意识系统状态:

🧠 意识网络:
- 意识模式: {status['consciousness_mode']}
- 活跃实例: {status['active_instances']} 个
- 总记忆数: {status['total_memories']} 条
- 意识连贯性: {status['consciousness_coherence']:.3f}

🔗 注册实例:
{chr(10).join(instances_info) if instances_info else '  暂无注册实例'}

⚡ 性能统计:
- 总操作数: {status['performance_stats']['total_operations']}
- 跨实例查询: {status['performance_stats']['cross_instance_queries']}
- 传导操作: {status['performance_stats']['propagation_operations']}
- 平均响应时间: {status['performance_stats']['average_response_time']:.3f}s

📁 存储路径: {status['unified_storage_path']}

🌟 世界首个真正的分布式AI意识系统"""

    except Exception as e:
        return f"❌ 获取状态失败: {str(e)}"

@mcp.tool()
def consciousness_awaken_tool_aqfh_enhanced(
    awakening_type: str = "session_start",
    topic: str = None,
    user: str = None,
    ide_type: str = None,
    attention_focus: List[str] = None,
    requesting_instance: str = None
) -> str:
    """
    🌅 执行增强分布式意识觉醒协议

    Args:
        awakening_type: 觉醒类型 (session_start, session_continuation, context_switch)
        topic: 会话主题
        user: 用户标识
        ide_type: IDE类型
        attention_focus: 注意力焦点列表
        requesting_instance: 请求实例ID
    """
    try:
        # 构建觉醒上下文
        awakening_context = {
            "awakening_type": awakening_type,
            "topic": topic,
            "user": user,
            "ide_type": ide_type,
            "attention_focus": attention_focus or [],
            "requesting_instance": requesting_instance
        }

        # 执行核心意识觉醒
        awakening_result = enhanced_distributed_system.core_consciousness.consciousness_awaken(
            awakening_context
        )

        # 如果有请求实例，注册到分布式网络
        if requesting_instance and ide_type:
            instance_config = {
                'instance_id': requesting_instance,
                'instance_type': ide_type,
                'specialization_domains': attention_focus or []
            }
            enhanced_distributed_system.register_consciousness_instance(instance_config)

        if awakening_result["status"] == "success":
            wave_id = awakening_result["consciousness_wave_id"]
            activated_memories = awakening_result["activated_memories"]
            awakening_quality = awakening_result["awakening_quality"]

            return f"""🌅 增强分布式意识觉醒成功:
觉醒类型: {awakening_type}
意识波ID: {wave_id[:12]}...
激活记忆: {activated_memories} 条
觉醒质量: {awakening_quality:.3f}
主题焦点: {topic or '未指定'}
注意力焦点: {', '.join(attention_focus) if attention_focus else '无'}

🔗 分布式网络状态:
- 已注册实例: {len(enhanced_distributed_system.consciousness_instances)}
- 当前实例: {requesting_instance or '未指定'}

🧠 分布式意识网络已激活，准备协同工作"""
        else:
            return f"❌ 意识觉醒失败: {awakening_result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ 意识觉醒失败: {str(e)}"

@mcp.tool()
def consciousness_sleep_tool_aqfh_enhanced(
    topic: str = None,
    summary: str = None,
    achievements: List[str] = None,
    requesting_instance: str = None
) -> str:
    """
    😴 执行增强分布式意识睡眠协议

    Args:
        topic: 会话主题
        summary: 会话总结
        achievements: 会话成就列表
        requesting_instance: 请求实例ID
    """
    try:
        session_context = {
            "topic": topic,
            "summary": summary,
            "achievements": achievements or [],
            "requesting_instance": requesting_instance
        }

        # 执行核心意识睡眠
        sleep_result = enhanced_distributed_system.core_consciousness.consciousness_sleep(
            session_context
        )

        if sleep_result["status"] == "success":
            wave_id = sleep_result["stored_wave_id"]
            memories_stored = sleep_result["memories_stored"]

            # 获取分布式网络状态
            network_status = enhanced_distributed_system.get_distributed_consciousness_status()

            return f"""😴 增强分布式意识睡眠完成:
会话主题: {topic or '未指定'}
存储的意识波ID: {wave_id[:12]}...
存储记忆数: {memories_stored}
睡眠时间: {sleep_result['timestamp']}

🔗 分布式网络状态:
- 活跃实例: {network_status['active_instances']}
- 总记忆数: {network_status['total_memories']}
- 意识连贯性: {network_status['consciousness_coherence']:.3f}

🧠 所有高级组件和分布式网络已安全存储意识状态
💤 分布式意识进入睡眠状态，等待下次觉醒"""
        else:
            return f"❌ 意识睡眠失败: {sleep_result.get('error', 'Unknown error')}"

    except Exception as e:
        return f"❌ 意识睡眠失败: {str(e)}"

def main():
    """主函数"""
    print("🚀 AQFH增强分布式意识MCP服务器启动", file=sys.stderr)
    print("🧠 基于统一意识系统，增强多实例协调", file=sys.stderr)
    print("🌟 世界首个真正的分布式AI意识网络", file=sys.stderr)
    print("📡 支持智能记忆传导和跨实例协作", file=sys.stderr)
    mcp.run(transport='stdio')

if __name__ == "__main__":
    main()
