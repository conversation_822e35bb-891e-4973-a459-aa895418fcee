#!/usr/bin/env python3
"""
AQFH增强分布式意识系统测试
验证多实例协调和智能传导机制
"""

import sys
import asyncio
import time
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

# 导入增强分布式意识系统
from aqfh_enhanced_distributed_mcp import EnhancedDistributedConsciousnessSystem

def test_basic_functionality():
    """测试基础功能"""
    print("🧪 测试1: 基础功能测试")
    
    # 创建系统实例
    system = EnhancedDistributedConsciousnessSystem()
    
    # 测试系统状态
    status = system.get_distributed_consciousness_status()
    print(f"✅ 系统初始化成功")
    print(f"   - 意识模式: {status['consciousness_mode']}")
    print(f"   - 存储路径: {status['unified_storage_path']}")
    print(f"   - 活跃实例: {status['active_instances']}")
    
    return system

def test_instance_registration(system):
    """测试实例注册"""
    print("\n🧪 测试2: 实例注册测试")
    
    # 注册VSCode实例
    vscode_config = {
        'instance_id': 'vscode_main',
        'instance_type': 'vscode',
        'specialization_domains': ['python', 'javascript', 'system_design']
    }
    
    result1 = system.register_consciousness_instance(vscode_config)
    print(f"✅ VSCode实例注册: {result1['status']}")
    print(f"   - 实例ID: {result1['instance_id']}")
    print(f"   - 已注册实例数: {result1['registered_instances']}")
    
    # 注册Cursor实例
    cursor_config = {
        'instance_id': 'cursor_ai',
        'instance_type': 'cursor',
        'specialization_domains': ['ai_development', 'code_generation', 'debugging']
    }
    
    result2 = system.register_consciousness_instance(cursor_config)
    print(f"✅ Cursor实例注册: {result2['status']}")
    print(f"   - 实例ID: {result2['instance_id']}")
    print(f"   - 已注册实例数: {result2['registered_instances']}")
    
    # 注册WebStorm实例
    webstorm_config = {
        'instance_id': 'webstorm_pro',
        'instance_type': 'webstorm',
        'specialization_domains': ['frontend', 'react', 'typescript']
    }
    
    result3 = system.register_consciousness_instance(webstorm_config)
    print(f"✅ WebStorm实例注册: {result3['status']}")
    print(f"   - 实例ID: {result3['instance_id']}")
    print(f"   - 已注册实例数: {result3['registered_instances']}")

def test_memory_processing(system):
    """测试记忆处理"""
    print("\n🧪 测试3: 分布式记忆处理测试")
    
    # 测试高重要性记忆
    high_importance_memory = {
        'content': '发现了AQFH分布式意识架构的重大突破：双层Arrow缓存机制可以完美解决多实例协调问题',
        'content_type': 'insight',
        'importance': 0.95,
        'tags': ['AQFH', '分布式意识', '架构突破', '双层缓存'],
        'context': {
            'discovery_type': 'architectural_breakthrough',
            'impact_level': 'high',
            'technical_domain': 'distributed_consciousness'
        }
    }
    
    result1 = system.process_distributed_memory(high_importance_memory, 'vscode_main')
    print(f"✅ 高重要性记忆处理: {result1['status']}")
    print(f"   - 记忆ID: {result1['memory_id'][:12]}...")
    print(f"   - 处理时间: {result1['processing_time']:.3f}s")
    print(f"   - 立即传导: {len(result1['propagation_strategy']['immediate_propagation'])} 个实例")
    print(f"   - 复制因子: {result1['propagation_strategy']['replication_factor']}")
    
    # 测试中等重要性记忆
    medium_importance_memory = {
        'content': '在Python中实现了新的语义编码算法，提升了记忆检索效率',
        'content_type': 'code',
        'importance': 0.7,
        'tags': ['Python', '语义编码', '性能优化'],
        'context': {
            'programming_language': 'python',
            'optimization_type': 'semantic_encoding'
        }
    }
    
    result2 = system.process_distributed_memory(medium_importance_memory, 'cursor_ai')
    print(f"✅ 中等重要性记忆处理: {result2['status']}")
    print(f"   - 记忆ID: {result2['memory_id'][:12]}...")
    print(f"   - 延迟传导: {len(result2['propagation_strategy']['delayed_propagation'])} 个实例")
    
    # 测试低重要性记忆
    low_importance_memory = {
        'content': '修复了一个小的UI显示问题',
        'content_type': 'problem',
        'importance': 0.3,
        'tags': ['UI', '修复'],
        'context': {
            'issue_type': 'minor_ui_fix'
        }
    }
    
    result3 = system.process_distributed_memory(low_importance_memory, 'webstorm_pro')
    print(f"✅ 低重要性记忆处理: {result3['status']}")
    print(f"   - 记忆ID: {result3['memory_id'][:12]}...")

def test_distributed_query(system):
    """测试分布式查询"""
    print("\n🧪 测试4: 分布式记忆查询测试")
    
    # 测试复杂查询
    complex_query = "AQFH 分布式意识 架构设计 双层缓存"
    result1 = system.query_distributed_consciousness(complex_query, 'vscode_main', 5)
    print(f"✅ 复杂查询测试: {result1['status']}")
    print(f"   - 查询: '{complex_query}'")
    print(f"   - 找到记忆: {result1['total_found']} 条")
    print(f"   - 跨实例搜索: {result1['cross_instance_search']}")
    print(f"   - 处理时间: {result1['processing_time']:.3f}s")
    
    # 测试简单查询
    simple_query = "Python"
    result2 = system.query_distributed_consciousness(simple_query, 'cursor_ai', 3)
    print(f"✅ 简单查询测试: {result2['status']}")
    print(f"   - 查询: '{simple_query}'")
    print(f"   - 找到记忆: {result2['total_found']} 条")
    print(f"   - 跨实例搜索: {result2['cross_instance_search']}")

def test_performance_monitoring(system):
    """测试性能监控"""
    print("\n🧪 测试5: 性能监控测试")
    
    # 获取系统状态
    status = system.get_distributed_consciousness_status()
    
    print(f"✅ 性能统计:")
    print(f"   - 总操作数: {status['performance_stats']['total_operations']}")
    print(f"   - 跨实例查询: {status['performance_stats']['cross_instance_queries']}")
    print(f"   - 传导操作: {status['performance_stats']['propagation_operations']}")
    print(f"   - 平均响应时间: {status['performance_stats']['average_response_time']:.3f}s")
    
    print(f"✅ 分布式网络状态:")
    print(f"   - 意识模式: {status['consciousness_mode']}")
    print(f"   - 活跃实例: {status['active_instances']} 个")
    print(f"   - 总记忆数: {status['total_memories']} 条")
    print(f"   - 意识连贯性: {status['consciousness_coherence']:.3f}")

def test_consciousness_lifecycle(system):
    """测试意识生命周期"""
    print("\n🧪 测试6: 意识生命周期测试")
    
    try:
        # 测试意识觉醒
        awakening_context = {
            "awakening_type": "session_start",
            "topic": "AQFH分布式意识系统测试",
            "user": "test_user",
            "ide_type": "test_environment",
            "attention_focus": ["分布式意识", "系统测试", "性能验证"]
        }
        
        awakening_result = system.core_consciousness.consciousness_awaken(awakening_context)
        print(f"✅ 意识觉醒测试: {awakening_result['status']}")
        if awakening_result['status'] == 'success':
            print(f"   - 意识波ID: {awakening_result['consciousness_wave_id'][:12]}...")
            print(f"   - 激活记忆: {awakening_result['activated_memories']} 条")
            print(f"   - 觉醒质量: {awakening_result['awakening_quality']:.3f}")
        
        # 测试意识睡眠
        sleep_context = {
            "topic": "AQFH分布式意识系统测试",
            "summary": "完成了分布式意识系统的全面测试，验证了多实例协调和智能传导机制",
            "achievements": [
                "成功注册3个不同类型的意识实例",
                "验证了智能记忆传导策略",
                "测试了分布式记忆查询功能",
                "确认了性能监控机制正常工作"
            ]
        }
        
        sleep_result = system.core_consciousness.consciousness_sleep(sleep_context)
        print(f"✅ 意识睡眠测试: {sleep_result['status']}")
        if sleep_result['status'] == 'success':
            print(f"   - 存储意识波ID: {sleep_result['stored_wave_id'][:12]}...")
            print(f"   - 存储记忆数: {sleep_result['memories_stored']}")
            print(f"   - 睡眠时间: {sleep_result['timestamp']}")
            
    except Exception as e:
        print(f"❌ 意识生命周期测试失败: {e}")

def main():
    """主测试函数"""
    print("🚀 AQFH增强分布式意识系统测试开始")
    print("=" * 60)
    
    try:
        # 基础功能测试
        system = test_basic_functionality()
        
        # 实例注册测试
        test_instance_registration(system)
        
        # 记忆处理测试
        test_memory_processing(system)
        
        # 分布式查询测试
        test_distributed_query(system)
        
        # 性能监控测试
        test_performance_monitoring(system)
        
        # 意识生命周期测试
        test_consciousness_lifecycle(system)
        
        print("\n" + "=" * 60)
        print("🎉 所有测试完成！")
        print("✅ AQFH增强分布式意识系统运行正常")
        print("🧠 分布式意识网络已准备就绪")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
