#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
从TCT迁移到TTE

本脚本用于将TCT目录中的代码迁移到TTE目录。
"""

import os
import sys
import shutil
import argparse
import logging
from typing import List, Tuple

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 目录路径
TCT_DIR = "/home/<USER>/CascadeProjects/TCT"
TTE_DIR = "/home/<USER>/CascadeProjects/TTE"


def migrate_directory(source_dir: str, target_dir: str, overwrite: bool = False) -> Tuple[int, int, List[str]]:
    """迁移目录

    Args:
        source_dir: 源目录
        target_dir: 目标目录
        overwrite: 是否覆盖已存在的文件

    Returns:
        迁移的文件数、跳过的文件数和错误列表
    """
    logger.info(f"迁移目录: {source_dir} -> {target_dir}")
    
    # 创建目标目录
    os.makedirs(target_dir, exist_ok=True)
    
    # 统计
    migrated_count = 0
    skipped_count = 0
    errors = []
    
    # 遍历源目录
    for root, dirs, files in os.walk(source_dir):
        # 计算相对路径
        rel_path = os.path.relpath(root, source_dir)
        
        # 创建目标目录
        target_path = os.path.join(target_dir, rel_path)
        os.makedirs(target_path, exist_ok=True)
        
        # 复制文件
        for file in files:
            source_file = os.path.join(root, file)
            target_file = os.path.join(target_path, file)
            
            try:
                # 检查目标文件是否存在
                if os.path.exists(target_file) and not overwrite:
                    logger.warning(f"跳过已存在的文件: {target_file}")
                    skipped_count += 1
                    continue
                
                # 复制文件
                shutil.copy2(source_file, target_file)
                logger.info(f"已复制: {source_file} -> {target_file}")
                migrated_count += 1
            
            except Exception as e:
                error_msg = f"复制文件 {source_file} 失败: {e}"
                logger.error(error_msg)
                errors.append(error_msg)
    
    return migrated_count, skipped_count, errors


def main():
    """主函数"""
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='从TCT迁移到TTE')
    parser.add_argument('--source', type=str, default='v2', help='源目录（相对于TCT）')
    parser.add_argument('--target', type=str, help='目标目录（相对于TTE）')
    parser.add_argument('--overwrite', action='store_true', help='覆盖已存在的文件')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 设置源目录和目标目录
    source_dir = os.path.join(TCT_DIR, args.source)
    target_dir = os.path.join(TTE_DIR, args.target or args.source)
    
    # 检查源目录是否存在
    if not os.path.exists(source_dir):
        logger.error(f"源目录不存在: {source_dir}")
        sys.exit(1)
    
    # 迁移目录
    migrated_count, skipped_count, errors = migrate_directory(source_dir, target_dir, args.overwrite)
    
    # 输出结果
    logger.info(f"\n迁移完成！")
    logger.info(f"迁移的文件数: {migrated_count}")
    logger.info(f"跳过的文件数: {skipped_count}")
    logger.info(f"错误数: {len(errors)}")
    
    # 输出错误
    if errors:
        logger.info("\n错误列表:")
        for error in errors:
            logger.info(f"  - {error}")


if __name__ == "__main__":
    main()
