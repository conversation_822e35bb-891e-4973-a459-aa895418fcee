"""
基本测试脚本

本脚本测试自解释与可验证性算子的基本功能。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

# 打印Python路径
print("Python路径:", sys.path)

# 列出src/operators目录下的文件
print("\nsrc/operators目录下的文件:")
try:
    for item in os.listdir('src/operators'):
        print(f"- {item}")
except Exception as e:
    print(f"无法列出目录: {e}")

# 列出src/operators/explanation目录下的文件
print("\nsrc/operators/explanation目录下的文件:")
try:
    for item in os.listdir('src/operators/explanation'):
        print(f"- {item}")
except Exception as e:
    print(f"无法列出目录: {e}")

# 列出src/operators/verification目录下的文件
print("\nsrc/operators/verification目录下的文件:")
try:
    for item in os.listdir('src/operators/verification'):
        print(f"- {item}")
except Exception as e:
    print(f"无法列出目录: {e}")

print("\n测试完成！")
