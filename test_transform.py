"""
简单的TransformOperator测试脚本
"""

import numpy as np
import matplotlib.pyplot as plt
from src.operators.transform.operator import TransformOperator


def test_linear_transform():
    """测试线性变换"""
    print("\n测试线性变换...")
    
    # 创建随机数据
    np.random.seed(42)
    data = np.random.randn(100, 2)
    
    # 创建线性变换算子
    transform_op = TransformOperator(
        transform_type='linear',
        dimension=2,
        parameters={
            'matrix': np.array([[np.cos(np.pi/4), -np.sin(np.pi/4)], 
                               [np.sin(np.pi/4), np.cos(np.pi/4)]]),
            'offset': np.array([0, 0])
        }
    )
    
    # 应用变换
    transformed_data = transform_op.apply(data)
    
    # 打印结果
    print(f"变换类型: {transform_op.transform_type}")
    print(f"变换参数: {transform_op.parameters}")
    print(f"原始数据形状: {data.shape}")
    print(f"变换后数据形状: {transformed_data.shape}")
    
    # 可视化原始数据和变换后的数据
    plt.figure(figsize=(10, 5))
    
    plt.subplot(1, 2, 1)
    plt.scatter(data[:, 0], data[:, 1], alpha=0.7)
    plt.title('Original Data')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.scatter(transformed_data[:, 0], transformed_data[:, 1], alpha=0.7)
    plt.title('Transformed Data')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('linear_transform_test.png')
    plt.close()
    
    print("线性变换测试完成")


def test_nonlinear_transform():
    """测试非线性变换"""
    print("\n测试非线性变换...")
    
    # 创建随机数据
    np.random.seed(42)
    data = np.random.randn(100, 2)
    
    # 创建非线性变换算子
    transform_op = TransformOperator(
        transform_type='nonlinear',
        dimension=2,
        parameters={
            'function': 'sigmoid'
        }
    )
    
    # 应用变换
    transformed_data = transform_op.apply(data)
    
    # 打印结果
    print(f"变换类型: {transform_op.transform_type}")
    print(f"变换参数: {transform_op.parameters}")
    print(f"原始数据形状: {data.shape}")
    print(f"变换后数据形状: {transformed_data.shape}")
    
    # 可视化原始数据和变换后的数据
    plt.figure(figsize=(10, 5))
    
    plt.subplot(1, 2, 1)
    plt.scatter(data[:, 0], data[:, 1], alpha=0.7)
    plt.title('Original Data')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.scatter(transformed_data[:, 0], transformed_data[:, 1], alpha=0.7)
    plt.title('Transformed Data')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('nonlinear_transform_test.png')
    plt.close()
    
    print("非线性变换测试完成")


def test_inverse_transform():
    """测试逆变换"""
    print("\n测试逆变换...")
    
    # 创建随机数据
    np.random.seed(42)
    data = np.random.randn(100, 2)
    
    # 创建线性变换算子
    transform_op = TransformOperator(
        transform_type='linear',
        dimension=2,
        parameters={
            'matrix': np.array([[np.cos(np.pi/4), -np.sin(np.pi/4)], 
                               [np.sin(np.pi/4), np.cos(np.pi/4)]]),
            'offset': np.array([0, 0])
        }
    )
    
    # 应用变换
    transformed_data = transform_op.apply(data)
    
    # 应用逆变换
    reconstructed_data = transform_op.apply(transformed_data, inverse=True)
    
    # 计算重构误差
    reconstruction_error = np.mean(np.abs(data - reconstructed_data))
    
    # 打印结果
    print(f"变换类型: {transform_op.transform_type}")
    print(f"重构误差: {reconstruction_error}")
    
    # 可视化原始数据、变换后的数据和重构数据
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.scatter(data[:, 0], data[:, 1], alpha=0.7)
    plt.title('Original Data')
    plt.grid(True)
    
    plt.subplot(1, 3, 2)
    plt.scatter(transformed_data[:, 0], transformed_data[:, 1], alpha=0.7)
    plt.title('Transformed Data')
    plt.grid(True)
    
    plt.subplot(1, 3, 3)
    plt.scatter(reconstructed_data[:, 0], reconstructed_data[:, 1], alpha=0.7)
    plt.title('Reconstructed Data')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('inverse_transform_test.png')
    plt.close()
    
    print("逆变换测试完成")


def main():
    """主函数"""
    print("开始测试TransformOperator...")
    
    # 测试各种变换
    test_linear_transform()
    test_nonlinear_transform()
    test_inverse_transform()
    
    print("\n所有测试完成")


if __name__ == "__main__":
    main()
