# NumPy 2.x 和 PyArrow 14.0.0+ 兼容性修复报告
修复时间: 2025-05-14 19:30:59,837
项目路径: .
总问题数: 280
成功修复: 39
修复失败: 241

## 成功修复的问题

- **examples/realtime_fusion_example.py:164** (return_type_changes)
  ```python
  X, Y = np.meshgrid(x, y)
  ```
- **prototype/octopus_intelligence/core/holographic_memory.py:17** (return_type_changes)
  ```python
  x, y = np.meshgrid(np.linspace(-1, 1, self.memory_size[0]),
  ```
- **scripts/analyze_interference_performance.py:38** (return_type_changes)
  ```python
  X, Y = np.meshgrid(x, y)
  ```
- **scripts/compare_interference_optimizers.py:34** (return_type_changes)
  ```python
  X, Y = np.meshgrid(x, y)
  ```
- **src/algorithms/chaos_control/stability.py:222** (return_type_changes)
  ```python
  X, Y = np.meshgrid(x, y)
  ```
- **src/algorithms/fft_fusion/filters.py:164** (return_type_changes)
  ```python
  u_grid, v_grid = np.meshgrid(u, v, indexing='ij')
  ```
- **src/algorithms/topology/features.py:321** (return_type_changes)
  ```python
  xx, yy = np.meshgrid(x_grid, y_grid)
  ```
- **src/algorithms/transcendental_evolution/analysis.py:390** (return_type_changes)
  ```python
  X, Y = np.meshgrid(np.arange(len(state_history[0].flatten())), time_points)
  ```
- **src/algorithms/transcendental_evolution/transcendental_evolution_py.py:227** (return_type_changes)
  ```python
  X, Y, Z = np.meshgrid(x, y, z)
  ```
- **src/algorithms/transcendental_evolution/transcendental_evolution_py.py:217** (return_type_changes)
  ```python
  X, Y = np.meshgrid(x, y)
  ```
- **src/algorithms/transcendental_evolution/transcendental_evolution_py.py:156** (return_type_changes)
  ```python
  X, Y, Z = np.meshgrid(x, y, z)
  ```
- **src/algorithms/transcendental_evolution/transcendental_evolution_py.py:145** (return_type_changes)
  ```python
  KX, KY, KZ = np.meshgrid(kx, ky, kz)
  ```
- **src/algorithms/transcendental_evolution/transcendental_evolution_py.py:136** (return_type_changes)
  ```python
  X, Y = np.meshgrid(x, y)
  ```
- **src/algorithms/transcendental_evolution/transcendental_evolution_py.py:126** (return_type_changes)
  ```python
  KX, KY = np.meshgrid(kx, ky)
  ```
- **src/algorithms/transcendental_evolution/transcendental_evolution_py.py:60** (return_type_changes)
  ```python
  X, Y, Z = np.meshgrid(x, y, z)
  ```
- **src/algorithms/transcendental_evolution/transcendental_evolution_py.py:52** (return_type_changes)
  ```python
  X, Y = np.meshgrid(x, y)
  ```
- **src/graphics_processing/filters/blur.py:145** (return_type_changes)
  ```python
  xx, yy = np.meshgrid(ax, ax)
  ```
- **src/graphics_processing/filters/edge_detection.py:313** (return_type_changes)
  ```python
  xx, yy = np.meshgrid(ax, ax)
  ```
- **src/graphics_processing/processors/feature_extraction.py:348** (return_type_changes)
  ```python
  xx, yy = np.meshgrid(ax, ax)
  ```
- **src/operators/interference/patterns.py:306** (return_type_changes)
  ```python
  X, Y = np.meshgrid(x, y)
  ```
- **src/operators/transform/analysis.py:100** (return_type_changes)
  ```python
  X, Y = np.meshgrid(x, y)
  ```
- **src/transcendental_tensor/multi_scale_coordination/fractal_mapping/generators.py:367** (return_type_changes)
  ```python
  x, y = np.meshgrid(np.arange(width), np.arange(height))
  ```
- **src/transcendental_tensor/multi_scale_coordination/fractal_mapping/generators.py:276** (return_type_changes)
  ```python
  x, y = np.meshgrid(np.arange(width), np.arange(height))
  ```
- **src/transcendental_tensor/multi_scale_coordination/fractal_mapping/generators.py:96** (return_type_changes)
  ```python
  X, Y = np.meshgrid(x, y)
  ```
- **src/transcendental_tensor/multi_scale_coordination/fractal_mapping/generators.py:42** (return_type_changes)
  ```python
  X, Y = np.meshgrid(x, y)
  ```
- **src/transcendental_tensor_backup/multi_scale_coordination/fractal_mapping/generators.py:367** (return_type_changes)
  ```python
  x, y = np.meshgrid(np.arange(width), np.arange(height))
  ```
- **src/transcendental_tensor_backup/multi_scale_coordination/fractal_mapping/generators.py:276** (return_type_changes)
  ```python
  x, y = np.meshgrid(np.arange(width), np.arange(height))
  ```
- **src/transcendental_tensor_backup/multi_scale_coordination/fractal_mapping/generators.py:96** (return_type_changes)
  ```python
  X, Y = np.meshgrid(x, y)
  ```
- **src/transcendental_tensor_backup/multi_scale_coordination/fractal_mapping/generators.py:42** (return_type_changes)
  ```python
  X, Y = np.meshgrid(x, y)
  ```
- **src/vision_processing/filters/blur.py:278** (return_type_changes)
  ```python
  xx, yy = np.meshgrid(x, y)
  ```
- **tests/integration/test_game_topology_integration/base_test.py:86** (return_type_changes)
  ```python
  phi, theta = np.meshgrid(phi, theta)
  ```
- **tests/integration/test_interference_integration/base_test.py:46** (return_type_changes)
  ```python
  X, Y = np.meshgrid(x, y)
  ```
- **tests/test_graphics_processing.py:58** (return_type_changes)
  ```python
  xx, yy = np.meshgrid(x, y)
  ```
- **tests/test_graphics_processing.py:37** (return_type_changes)
  ```python
  xx, yy = np.meshgrid(x, y)
  ```
- **tools/numpy_compatibility_fixer.py:464** (return_type_changes)
  ```python
  report.append("phi, theta = np.meshgrid(phi, theta)\n\n")
  ```
- **tools/numpy_compatibility_fixer.py:371** (gil_state_usage)
  ```python
  with np.gil_state(acquire):
  ```
- **tools/numpy_compatibility_scanner.py:387** (gil_state_usage)
  ```python
  report.append("with np.gil_state(False):\n")
  ```
- **tools/numpy_compatibility_scanner.py:378** (gil_state_usage)
  ```python
  report.append("        with np.gil_state(acquire):\n")
  ```
- **tools/numpy_compatibility_scanner.py:351** (return_type_changes)
  ```python
  report.append("phi, theta = np.meshgrid(phi, theta)\n\n")
  ```

## 修复失败的问题

- **examples/core/metacognition/metacognition_example.py:101** (unknown)
  ```python
  max_pos = np.unravel_index(np.argmax(image), image.shape)
  ```
- **examples/metacognition/cognitive_state_example.py:337** (unknown)
  ```python
  max_pos = np.unravel_index(np.argmax(image), image.shape)
  ```
- **examples/metacognition/metacognitive_mapping_example.py:125** (unknown)
  ```python
  max_pos = np.unravel_index(np.argmax(image), image.shape)
  ```
- **prototype/octopus_intelligence/core/operators.py:75** (unknown)
  ```python
  max_pos = np.unravel_index(np.argmax(np.abs(reference)), reference.shape)
  ```
- **prototype/octopus_intelligence/tests/test_edge_cases.py:105** (unknown)
  ```python
  max_point = np.unravel_index(np.argmax(np.abs(result)), result.shape)
  ```
- **prototype/octopus_intelligence/tests/test_edge_cases.py:99** (unknown)
  ```python
  max_point = np.unravel_index(np.argmax(result), result.shape)
  ```
- **simple_test_interference.py:336** (unknown)
  ```python
  phase = np.where(mask, phase1, phase2)
  ```
- **src/algorithms/chaos_control/methods.py:114** (unknown)
  ```python
  unstable_indices = np.where(np.real(eigenvalues) > 0)[0]
  ```
- **src/algorithms/fft_fusion/fusion.py:372** (unknown)
  ```python
  grid = np.indices(shape)
  ```
- **src/algorithms/fft_fusion/fusion.py:357** (unknown)
  ```python
  grid = np.indices(shape)
  ```
- **src/algorithms/game_theory/incremental.py:114** (unknown)
  ```python
  for resource_id in np.where(over_allocated)[0]:
  ```
- **src/algorithms/game_theory/nash_solver_optimized.py:387** (unknown)
  ```python
  utility_rates = np.where(demands[agent_id] > 0, utility_rates, 0)
  ```
- **src/algorithms/game_theory/nash_solver_optimized.py:315** (unknown)
  ```python
  utility_rates = np.where(demands[agent_id] > 0, utility_rates, 0)
  ```
- **src/algorithms/topology/persistence_calculator_optimized.py:552** (unknown)
  ```python
  non_zero_rows = np.where(col)[0]
  ```
- **src/algorithms/topology/persistence_calculator_optimized.py:548** (unknown)
  ```python
  non_zero_rows = np.where(col)[0]
  ```
- **src/algorithms/topology/persistence_calculator_optimized.py:520** (unknown)
  ```python
  non_zero_rows = np.where(col)[0]
  ```
- **src/algorithms/topology/persistence_calculator_optimized.py:516** (unknown)
  ```python
  non_zero_rows = np.where(col)[0]
  ```
- **src/algorithms/topology/persistence_calculator_optimized.py:495** (unknown)
  ```python
  non_zero_rows = np.where(col)[0]
  ```
- **src/algorithms/topology/persistence_calculator_optimized.py:491** (unknown)
  ```python
  non_zero_rows = np.where(col)[0]
  ```
- **src/algorithms/topology/persistence_calculator_optimized.py:406** (unknown)
  ```python
  non_zero_rows = np.where(col)[0]
  ```
- **src/algorithms/topology/persistence_calculator_optimized.py:402** (unknown)
  ```python
  non_zero_rows = np.where(col)[0]
  ```
- **src/algorithms/topology/persistence_calculator_optimized.py:366** (unknown)
  ```python
  non_zero_rows = np.where(col)[0]
  ```
- **src/algorithms/topology/persistence_calculator_optimized.py:362** (unknown)
  ```python
  non_zero_rows = np.where(col)[0]
  ```
- **src/algorithms/topology/persistence_calculator_optimized.py:341** (unknown)
  ```python
  non_zero_rows = np.where(col)[0]
  ```
- **src/algorithms/topology/persistence_calculator_optimized.py:337** (unknown)
  ```python
  non_zero_rows = np.where(col)[0]
  ```
- **src/core/distributed/fault_tolerance/fractal_dimension_fault_handler.py:199** (unknown)
  ```python
  strong_correlations = np.where(np.abs(correlation_matrix) > self.correlation_threshold)
  ```
- **src/operators/evolution/operator.py:385** (unknown)
  ```python
  neighbors = np.where(distances < interaction_radius)[0]
  ```
- **src/operators/gpu_acceleration/mock_gpu_adapter.py:155** (unknown)
  ```python
  dist = np.where(adjacency_matrix > 0, 1.0 / adjacency_matrix, np.inf)
  ```
- **src/operators/gpu_acceleration/mock_gpu_adapter.py:129** (unknown)
  ```python
  dist = np.where(adjacency_matrix > 0, 1.0 / adjacency_matrix, np.inf)
  ```
- **src/operators/interference/analysis.py:79** (unknown)
  ```python
  y, x = np.unravel_index(peak_idx, fft_amplitude.shape)
  ```
- **src/operators/interference/interference_operator.py:444** (unknown)
  ```python
  phase = np.where(mask, phase1, phase2)
  ```
- **src/operators/interference/patterns.py:362** (unknown)
  ```python
  idx = np.unravel_index(np.argmax(fft_amplitude), fft_amplitude.shape)
  ```
- **src/transcendental_tensor/multimodal_fusion/multimodal_fusion_operator.py:479** (return_type_changes)
  ```python
  freq_grid = np.meshgrid(*[np.fft.fftfreq(s) for s in shape])
  ```
- **src/transcendental_tensor/performance_optimization/network_topology/analyzer.py:336** (unknown)
  ```python
  neighbors = np.where(adjacency[i] > 0)[0]
  ```
- **src/transcendental_tensor_backup/performance_optimization/network_topology/analyzer.py:336** (unknown)
  ```python
  neighbors = np.where(adjacency[i] > 0)[0]
  ```
- **src/vision_processing/filters/edge.py:347** (unknown)
  ```python
  edge_data = np.where(edge_data > self.params["threshold"], 255, 0).astype(np.uint8)
  ```
- **src/vision_processing/filters/edge.py:325** (unknown)
  ```python
  edge_data = np.where(edge_data > self.params["threshold"], 255, 0).astype(np.uint8)
  ```
- **src/vision_processing/filters/edge.py:306** (unknown)
  ```python
  edge_data = np.where(edge_data > self.params["threshold"], 255, 0).astype(np.uint8)
  ```
- **src/vision_processing/filters/edge.py:285** (unknown)
  ```python
  edge_data = np.where(edge_data > self.params["threshold"], 255, 0).astype(np.uint8)
  ```
- **src/vision_processing/filters/edge.py:240** (unknown)
  ```python
  edge_data = np.where(edge_data > self.params["threshold"], 255, 0).astype(np.uint8)
  ```
- **src/vision_processing/filters/edge.py:223** (unknown)
  ```python
  edge_data = np.where(edge_data > self.params["threshold"], 255, 0).astype(np.uint8)
  ```
- **src/vision_processing/filters/edge.py:207** (unknown)
  ```python
  edge_data = np.where(edge_data > self.params["threshold"], 255, 0).astype(np.uint8)
  ```
- **src/vision_processing/filters/edge.py:192** (unknown)
  ```python
  edge_data = np.where(edge_data > self.params["threshold"], 255, 0).astype(np.uint8)
  ```
- **tests/integration/test_metacognition_integration_part1.py:266** (unknown)
  ```python
  max_pos = np.unravel_index(np.argmax(image), image.shape)
  ```
- **tests/performance/test_metacognition_performance.py:320** (unknown)
  ```python
  max_pos = np.unravel_index(np.argmax(image), image.shape)
  ```
- **tools/numpy_compatibility_fixer.py:466** (return_type_changes)
  ```python
  report.append("phi, theta = list(np.meshgrid(phi, theta))\n")
  ```
- **tools/numpy_compatibility_fixer.py:272** (gil_state_usage)
  ```python
  # 替换 np.gil_state 为 gil_state
  ```
- **tools/numpy_compatibility_fixer.py:243** (return_type_changes)
  ```python
  # 匹配 np.meshgrid, np.ogrid 等函数调用的解包赋值
  ```
- **tools/numpy_compatibility_fixer.py:243** (return_type_changes)
  ```python
  # 匹配 np.meshgrid, np.ogrid 等函数调用的解包赋值
  ```
- **tools/numpy_compatibility_fixer.py:76** (gil_state_usage)
  ```python
  elif "np.gil_state" in content or "numpy.gil_state" in content:
  ```
- **tools/numpy_compatibility_fixer.py:76** (gil_state_usage)
  ```python
  elif "np.gil_state" in content or "numpy.gil_state" in content:
  ```
- **tools/numpy_compatibility_fixer.py:74** (return_type_changes)
  ```python
  if "np.meshgrid" in content or "numpy.meshgrid" in content or "np.ogrid" in content:
  ```
- **tools/numpy_compatibility_fixer.py:74** (return_type_changes)
  ```python
  if "np.meshgrid" in content or "numpy.meshgrid" in content or "np.ogrid" in content:
  ```
- **tools/numpy_compatibility_fixer.py:74** (return_type_changes)
  ```python
  if "np.meshgrid" in content or "numpy.meshgrid" in content or "np.ogrid" in content:
  ```
- **tools/numpy_compatibility_scanner.py:384** (gil_state_usage)
  ```python
  report.append("然后替换所有 `np.gil_state` 调用:\n\n")
  ```
- **tools/numpy_compatibility_scanner.py:353** (return_type_changes)
  ```python
  report.append("phi, theta = list(np.meshgrid(phi, theta))\n")
  ```
- **tools/numpy_compatibility_scanner.py:102** (unknown)
  ```python
  "__arrow_ext_deserialize__"
  ```
- **tools/numpy_compatibility_scanner.py:101** (unknown)
  ```python
  "__arrow_ext_serialize__",
  ```
- **tools/numpy_compatibility_scanner.py:100** (unknown)
  ```python
  "pa.ExtensionArray",
  ```
- **tools/numpy_compatibility_scanner.py:99** (unknown)
  ```python
  "pa.ExtensionType",
  ```
- **tools/numpy_compatibility_scanner.py:94** (unknown)
  ```python
  "pa.map_"
  ```
- **tools/numpy_compatibility_scanner.py:93** (unknown)
  ```python
  "pa.list_",
  ```
- **tools/numpy_compatibility_scanner.py:92** (unknown)
  ```python
  "pa.struct",
  ```
- **tools/numpy_compatibility_scanner.py:91** (unknown)
  ```python
  "pa.field",
  ```
- **tools/numpy_compatibility_scanner.py:90** (unknown)
  ```python
  "pa.Schema",
  ```
- **tools/numpy_compatibility_scanner.py:89** (unknown)
  ```python
  "pa.DataType",
  ```
- **tools/numpy_compatibility_scanner.py:84** (unknown)
  ```python
  "FlightEndpoint"
  ```
- **tools/numpy_compatibility_scanner.py:83** (unknown)
  ```python
  "FlightInfo",
  ```
- **tools/numpy_compatibility_scanner.py:82** (unknown)
  ```python
  "FlightDescriptor",
  ```
- **tools/numpy_compatibility_scanner.py:81** (unknown)
  ```python
  "FlightClient",
  ```
- **tools/numpy_compatibility_scanner.py:80** (unknown)
  ```python
  "FlightServerBase",
  ```
- **tools/numpy_compatibility_scanner.py:72** (unknown)
  ```python
  r"str\(numpy\.[a-z]+\([^)]+\)\)"
  ```
- **tools/numpy_compatibility_scanner.py:71** (unknown)
  ```python
  r"str\(np\.[a-z]+\([^)]+\)\)",
  ```
- **tools/numpy_compatibility_scanner.py:66** (unknown)
  ```python
  r"numpy\.all\(.*object\(\).*\)"
  ```
- **tools/numpy_compatibility_scanner.py:65** (unknown)
  ```python
  r"np\.all\(.*object\(\).*\)",
  ```
- **tools/numpy_compatibility_scanner.py:64** (unknown)
  ```python
  r"numpy\.any\(.*object\(\).*\)",
  ```
- **tools/numpy_compatibility_scanner.py:63** (unknown)
  ```python
  r"np\.any\(.*object\(\).*\)",
  ```
- **tools/numpy_compatibility_scanner.py:58** (unknown)
  ```python
  r"numpy\.array\([^)]+copy\s*=\s*False"
  ```
- **tools/numpy_compatibility_scanner.py:57** (unknown)
  ```python
  r"np\.array\([^)]+copy\s*=\s*False",
  ```
- **tools/numpy_compatibility_scanner.py:52** (type_promotion)
  ```python
  r"numpy\.array\([^)]+dtype=[^)]+\)\s*[\+\-\*\/]\s*numpy\.array"
  ```
- **tools/numpy_compatibility_scanner.py:51** (type_promotion)
  ```python
  r"np\.array\([^)]+dtype=[^)]+\)\s*[\+\-\*\/]\s*np\.array",
  ```
- **tools/numpy_compatibility_scanner.py:45** (gil_state_usage)
  ```python
  "numpy.gil_state"
  ```
- **tools/numpy_compatibility_scanner.py:44** (gil_state_usage)
  ```python
  "np.gil_state",
  ```
- **tools/numpy_compatibility_scanner.py:39** (unknown)
  ```python
  "numpy.where"
  ```
- **tools/numpy_compatibility_scanner.py:38** (unknown)
  ```python
  "np.where",
  ```
- **tools/numpy_compatibility_scanner.py:37** (unknown)
  ```python
  "numpy.unravel_index",
  ```
- **tools/numpy_compatibility_scanner.py:36** (unknown)
  ```python
  "np.unravel_index",
  ```
- **tools/numpy_compatibility_scanner.py:35** (unknown)
  ```python
  "numpy.indices",
  ```
- **tools/numpy_compatibility_scanner.py:34** (unknown)
  ```python
  "np.indices",
  ```
- **tools/numpy_compatibility_scanner.py:33** (unknown)
  ```python
  "numpy.ogrid",
  ```
- **tools/numpy_compatibility_scanner.py:32** (return_type_changes)
  ```python
  "np.ogrid",
  ```
- **tools/numpy_compatibility_scanner.py:31** (return_type_changes)
  ```python
  "numpy.meshgrid",
  ```
- **tools/numpy_compatibility_scanner.py:30** (return_type_changes)
  ```python
  "np.meshgrid",
  ```
- **src/arrow_types/complex.py:216** (unknown)
  ```python
  def arrow_to_numpy_complex(arr: pa.ExtensionArray) -> np.ndarray:
  ```
- **src/arrow_types/complex.py:207** (unknown)
  ```python
  def numpy_to_arrow_complex(arr: np.ndarray) -> pa.ExtensionArray:
  ```
- **src/arrow_types/complex.py:203** (unknown)
  ```python
  def arrow_to_numpy_complex128(arr: pa.ExtensionArray) -> np.ndarray:
  ```
- **src/arrow_types/complex.py:199** (unknown)
  ```python
  def numpy_to_arrow_complex128(arr: np.ndarray) -> pa.ExtensionArray:
  ```
- **src/arrow_types/complex.py:195** (unknown)
  ```python
  def arrow_to_numpy_complex64(arr: pa.ExtensionArray) -> np.ndarray:
  ```
- **src/arrow_types/complex.py:191** (unknown)
  ```python
  def numpy_to_arrow_complex64(arr: np.ndarray) -> pa.ExtensionArray:
  ```
- **src/arrow_types/complex.py:164** (unknown)
  ```python
  def to_numpy(arr: pa.ExtensionArray) -> np.ndarray:
  ```
- **src/arrow_types/complex.py:161** (unknown)
  ```python
  return pa.ExtensionArray.from_storage(Complex128Type(), struct_array)
  ```
- **src/arrow_types/complex.py:137** (unknown)
  ```python
  def from_numpy(arr: np.ndarray) -> pa.ExtensionArray:
  ```
- **src/arrow_types/complex.py:104** (unknown)
  ```python
  def to_numpy(arr: pa.ExtensionArray) -> np.ndarray:
  ```
- **src/arrow_types/complex.py:101** (unknown)
  ```python
  return pa.ExtensionArray.from_storage(Complex64Type(), struct_array)
  ```
- **src/arrow_types/complex.py:77** (unknown)
  ```python
  def from_numpy(arr: np.ndarray) -> pa.ExtensionArray:
  ```
- **src/arrow_types/complex.py:56** (unknown)
  ```python
  def __arrow_ext_deserialize__(cls, storage_type, serialized) -> 'Complex128Type':
  ```
- **src/arrow_types/complex.py:51** (unknown)
  ```python
  def __arrow_ext_serialize__(self) -> bytes:
  ```
- **src/arrow_types/complex.py:45** (unknown)
  ```python
  self._storage_type = pa.struct([
  ```
- **src/arrow_types/complex.py:38** (unknown)
  ```python
  class Complex128Type(pa.ExtensionType):
  ```
- **src/arrow_types/complex.py:29** (unknown)
  ```python
  def __arrow_ext_deserialize__(cls, storage_type, serialized) -> 'Complex64Type':
  ```
- **src/arrow_types/complex.py:24** (unknown)
  ```python
  def __arrow_ext_serialize__(self) -> bytes:
  ```
- **src/arrow_types/complex.py:18** (unknown)
  ```python
  self._storage_type = pa.struct([
  ```
- **src/arrow_types/complex.py:11** (unknown)
  ```python
  class Complex64Type(pa.ExtensionType):
  ```
- **src/arrow_types/fractal.py:144** (unknown)
  ```python
  def arrow_to_numpy_fractal(arr: pa.ExtensionArray) -> np.ndarray:
  ```
- **src/arrow_types/fractal.py:141** (unknown)
  ```python
  return pa.ExtensionArray.from_storage(fractal_type_obj, struct_array)
  ```
- **src/arrow_types/fractal.py:89** (unknown)
  ```python
  ) -> pa.ExtensionArray:
  ```
- **src/arrow_types/fractal.py:57** (unknown)
  ```python
  def __arrow_ext_deserialize__(cls, storage_type, serialized) -> 'FractalStructureType':
  ```
- **src/arrow_types/fractal.py:47** (unknown)
  ```python
  def __arrow_ext_serialize__(self) -> bytes:
  ```
- **src/arrow_types/fractal.py:39** (unknown)
  ```python
  ('shape', pa.list_(pa.int32()))
  ```
- **src/arrow_types/fractal.py:38** (unknown)
  ```python
  ('data', pa.list_(pa.float32())),
  ```
- **src/arrow_types/fractal.py:37** (unknown)
  ```python
  self._storage_type = pa.struct([
  ```
- **src/arrow_types/fractal.py:13** (unknown)
  ```python
  class FractalStructureType(pa.ExtensionType):
  ```
- **src/arrow_types/holographic.py:152** (unknown)
  ```python
  def arrow_to_numpy_holographic(arr: pa.ExtensionArray) -> Tuple[np.ndarray, Optional[np.ndarray]]:
  ```
- **src/arrow_types/holographic.py:149** (unknown)
  ```python
  return pa.ExtensionArray.from_storage(holographic_type, struct_array)
  ```
- **src/arrow_types/holographic.py:83** (unknown)
  ```python
  ) -> pa.ExtensionArray:
  ```
- **src/arrow_types/holographic.py:56** (unknown)
  ```python
  def __arrow_ext_deserialize__(cls, storage_type, serialized) -> 'HolographicEncodingType':
  ```
- **src/arrow_types/holographic.py:47** (unknown)
  ```python
  def __arrow_ext_serialize__(self) -> bytes:
  ```
- **src/arrow_types/holographic.py:40** (unknown)
  ```python
  ('indices', pa.list_(pa.int32()))
  ```
- **src/arrow_types/holographic.py:39** (unknown)
  ```python
  ('data', pa.list_(pa.float32())),
  ```
- **src/arrow_types/holographic.py:38** (unknown)
  ```python
  self._storage_type = pa.struct([
  ```
- **src/arrow_types/holographic.py:34** (unknown)
  ```python
  ('data', pa.list_(pa.float32())),
  ```
- **src/arrow_types/holographic.py:33** (unknown)
  ```python
  self._storage_type = pa.struct([
  ```
- **src/arrow_types/holographic.py:13** (unknown)
  ```python
  class HolographicEncodingType(pa.ExtensionType):
  ```
- **src/arrow_types/quantum.py:186** (unknown)
  ```python
  ) -> pa.ExtensionArray:
  ```
- **src/arrow_types/quantum.py:152** (unknown)
  ```python
  def create_basis_state(dimensions: List[int], index: int) -> pa.ExtensionArray:
  ```
- **src/arrow_types/quantum.py:138** (unknown)
  ```python
  complex_array = pa.ExtensionArray.from_storage(Complex128Type(), arr.storage)
  ```
- **src/arrow_types/quantum.py:119** (unknown)
  ```python
  def arrow_to_numpy_quantum_state(arr: pa.ExtensionArray, reshape: bool = True) -> np.ndarray:
  ```
- **src/arrow_types/quantum.py:116** (unknown)
  ```python
  return pa.ExtensionArray.from_storage(quantum_type, complex_array.storage)
  ```
- **src/arrow_types/quantum.py:84** (unknown)
  ```python
  ) -> pa.ExtensionArray:
  ```
- **src/arrow_types/quantum.py:40** (unknown)
  ```python
  def __arrow_ext_deserialize__(cls, storage_type, serialized) -> 'QuantumStateType':
  ```
- **src/arrow_types/quantum.py:34** (unknown)
  ```python
  def __arrow_ext_serialize__(self) -> bytes:
  ```
- **src/arrow_types/quantum.py:16** (unknown)
  ```python
  class QuantumStateType(pa.ExtensionType):
  ```
- **src/core/data/arrow_types/complex_array.py:365** (unknown)
  ```python
  def arrow_to_numpy_complex128(arr: pa.ExtensionArray) -> np.ndarray:
  ```
- **src/core/data/arrow_types/complex_array.py:350** (unknown)
  ```python
  def numpy_to_arrow_complex128(arr: np.ndarray) -> pa.ExtensionArray:
  ```
- **src/core/data/arrow_types/complex_array.py:335** (unknown)
  ```python
  def arrow_to_numpy_complex64(arr: pa.ExtensionArray) -> np.ndarray:
  ```
- **src/core/data/arrow_types/complex_array.py:319** (unknown)
  ```python
  def arrow_to_numpy_complex128(arr: pa.ExtensionArray) -> np.ndarray:
  ```
- **src/core/data/arrow_types/complex_array.py:304** (unknown)
  ```python
  def numpy_to_arrow_complex128(arr: np.ndarray) -> pa.ExtensionArray:
  ```
- **src/core/data/arrow_types/complex_array.py:288** (unknown)
  ```python
  def arrow_to_numpy_complex64(arr: pa.ExtensionArray) -> np.ndarray:
  ```
- **src/core/data/arrow_types/complex_array.py:273** (unknown)
  ```python
  def numpy_to_arrow_complex64(arr: np.ndarray) -> pa.ExtensionArray:
  ```
- **src/core/data/arrow_types/complex_array.py:259** (unknown)
  ```python
  def arrow_to_numpy_complex(arr: pa.ExtensionArray) -> np.ndarray:
  ```
- **src/core/data/arrow_types/complex_array.py:246** (unknown)
  ```python
  def numpy_to_arrow_complex(arr: np.ndarray) -> pa.ExtensionArray:
  ```
- **src/core/data/arrow_types/complex_array.py:218** (unknown)
  ```python
  def to_numpy(arr: pa.ExtensionArray) -> np.ndarray:
  ```
- **src/core/data/arrow_types/complex_array.py:215** (unknown)
  ```python
  return pa.ExtensionArray.from_storage(Complex128Type(), struct_array)
  ```
- **src/core/data/arrow_types/complex_array.py:189** (unknown)
  ```python
  def from_numpy(arr: np.ndarray) -> pa.ExtensionArray:
  ```
- **src/core/data/arrow_types/complex_array.py:155** (unknown)
  ```python
  def to_numpy(arr: pa.ExtensionArray) -> np.ndarray:
  ```
- **src/core/data/arrow_types/complex_array.py:152** (unknown)
  ```python
  return pa.ExtensionArray.from_storage(Complex64Type(), struct_array)
  ```
- **src/core/data/arrow_types/complex_array.py:126** (unknown)
  ```python
  def from_numpy(arr: np.ndarray) -> pa.ExtensionArray:
  ```
- **src/core/data/arrow_types/complex_array.py:101** (unknown)
  ```python
  def to_numpy(arr: pa.ExtensionArray) -> np.ndarray:
  ```
- **src/core/data/arrow_types/complex_array.py:83** (unknown)
  ```python
  def from_numpy(arr: np.ndarray) -> pa.ExtensionArray:
  ```
- **src/core/data/arrow_types/complex_array.py:62** (unknown)
  ```python
  def __arrow_ext_deserialize__(cls, storage_type, serialized) -> 'Complex128Type':
  ```
- **src/core/data/arrow_types/complex_array.py:57** (unknown)
  ```python
  def __arrow_ext_serialize__(self) -> bytes:
  ```
- **src/core/data/arrow_types/complex_array.py:51** (unknown)
  ```python
  self._storage_type = pa.struct([
  ```
- **src/core/data/arrow_types/complex_array.py:44** (unknown)
  ```python
  class Complex128Type(pa.ExtensionType):
  ```
- **src/core/data/arrow_types/complex_array.py:35** (unknown)
  ```python
  def __arrow_ext_deserialize__(cls, storage_type, serialized) -> 'Complex64Type':
  ```
- **src/core/data/arrow_types/complex_array.py:30** (unknown)
  ```python
  def __arrow_ext_serialize__(self) -> bytes:
  ```
- **src/core/data/arrow_types/complex_array.py:24** (unknown)
  ```python
  self._storage_type = pa.struct([
  ```
- **src/core/data/arrow_types/complex_array.py:17** (unknown)
  ```python
  class Complex64Type(pa.ExtensionType):
  ```
- **src/core/data/arrow_types/quantum_state_array.py:234** (unknown)
  ```python
  def arrow_to_numpy_quantum_state(arr: pa.ExtensionArray, reshape: bool = True) -> np.ndarray:
  ```
- **src/core/data/arrow_types/quantum_state_array.py:219** (unknown)
  ```python
  def numpy_to_arrow_quantum_state(arr: np.ndarray, validate: bool = True, dimensions: Optional[List[int]] = None) -> pa.ExtensionArray:
  ```
- **src/core/data/arrow_types/quantum_state_array.py:190** (unknown)
  ```python
  def create_superposition(dimensions: List[int], coefficients: Optional[List[complex]] = None) -> pa.ExtensionArray:
  ```
- **src/core/data/arrow_types/quantum_state_array.py:167** (unknown)
  ```python
  def create_basis_state(dimensions: List[int], index: int) -> pa.ExtensionArray:
  ```
- **src/core/data/arrow_types/quantum_state_array.py:122** (unknown)
  ```python
  complex_array = pa.ExtensionArray.from_storage(Complex128Type(), arr.storage)
  ```
- **src/core/data/arrow_types/quantum_state_array.py:107** (unknown)
  ```python
  def to_numpy(arr: pa.ExtensionArray, reshape: bool = True) -> np.ndarray:
  ```
- **src/core/data/arrow_types/quantum_state_array.py:104** (unknown)
  ```python
  return pa.ExtensionArray.from_storage(QuantumStateType(dimensions), complex_array.storage)
  ```
- **src/core/data/arrow_types/quantum_state_array.py:69** (unknown)
  ```python
  def from_numpy(arr: np.ndarray, validate: bool = True, dimensions: Optional[List[int]] = None) -> pa.ExtensionArray:
  ```
- **src/core/data/arrow_types/quantum_state_array.py:39** (unknown)
  ```python
  def __arrow_ext_deserialize__(cls, storage_type, serialized) -> 'QuantumStateType':
  ```
- **src/core/data/arrow_types/quantum_state_array.py:32** (unknown)
  ```python
  def __arrow_ext_serialize__(self) -> bytes:
  ```
- **src/core/data/arrow_types/quantum_state_array.py:20** (unknown)
  ```python
  class QuantumStateType(pa.ExtensionType):
  ```
- **src/core/data/arrow_types/tensor_array.py:312** (unknown)
  ```python
  def arrow_to_numpy_tensor(arr: pa.ExtensionArray) -> np.ndarray:
  ```
- **src/core/data/arrow_types/tensor_array.py:299** (unknown)
  ```python
  def numpy_to_arrow_tensor(arr: np.ndarray) -> pa.ExtensionArray:
  ```
- **src/core/data/arrow_types/tensor_array.py:281** (unknown)
  ```python
  def create_identity(n: int, dtype: Union[str, np.dtype] = 'float64') -> pa.ExtensionArray:
  ```
- **src/core/data/arrow_types/tensor_array.py:250** (unknown)
  ```python
  **kwargs) -> pa.ExtensionArray:
  ```
- **src/core/data/arrow_types/tensor_array.py:230** (unknown)
  ```python
  def create_ones(shape: Sequence[int], dtype: Union[str, np.dtype] = 'float64') -> pa.ExtensionArray:
  ```
- **src/core/data/arrow_types/tensor_array.py:213** (unknown)
  ```python
  def create_zeros(shape: Sequence[int], dtype: Union[str, np.dtype] = 'float64') -> pa.ExtensionArray:
  ```
- **src/core/data/arrow_types/tensor_array.py:201** (unknown)
  ```python
  pa.ExtensionArray.from_storage(Complex128Type(), storage)
  ```
- **src/core/data/arrow_types/tensor_array.py:197** (unknown)
  ```python
  pa.ExtensionArray.from_storage(Complex64Type(), storage)
  ```
- **src/core/data/arrow_types/tensor_array.py:177** (unknown)
  ```python
  def to_numpy(arr: pa.ExtensionArray) -> np.ndarray:
  ```
- **src/core/data/arrow_types/tensor_array.py:174** (unknown)
  ```python
  return pa.ExtensionArray.from_storage(tensor_type, storage_array)
  ```
- **src/core/data/arrow_types/tensor_array.py:136** (unknown)
  ```python
  def from_numpy(arr: np.ndarray) -> pa.ExtensionArray:
  ```
- **src/core/data/arrow_types/tensor_array.py:119** (unknown)
  ```python
  def base_type(self) -> Optional[pa.DataType]:
  ```
- **src/core/data/arrow_types/tensor_array.py:84** (unknown)
  ```python
  def __arrow_ext_deserialize__(cls, storage_type, serialized) -> 'TensorType':
  ```
- **src/core/data/arrow_types/tensor_array.py:73** (unknown)
  ```python
  def __arrow_ext_serialize__(self) -> bytes:
  ```
- **src/core/data/arrow_types/tensor_array.py:29** (unknown)
  ```python
  dtype: Union[pa.DataType, str],
  ```
- **src/core/data/arrow_types/tensor_array.py:21** (unknown)
  ```python
  class TensorType(pa.ExtensionType):
  ```
- **src/core/data/arrow_types/parquet_storage.py:128** (unknown)
  ```python
  fields = [pa.field(name, array.type) for name, array in zip(column_names, arrays)]
  ```
- **src/core/data/cache/flight/server.py:76** (unknown)
  ```python
  return flight.FlightInfo(schema, descriptor, endpoints, -1, -1)
  ```
- **src/core/data/cache/flight/server.py:73** (unknown)
  ```python
  endpoints = [flight.FlightEndpoint(key.encode(), [flight.Location.for_grpc_tcp(self.location.host, self.location.port)])]
  ```
- **src/core/data/cache/flight/server.py:70** (unknown)
  ```python
  descriptor = flight.FlightDescriptor.for_path(key.encode())
  ```
- **src/core/data/cache/flight/server.py:59** (unknown)
  ```python
  def _make_flight_info(self, key: str, schema: pa.Schema) -> flight.FlightInfo:
  ```
- **src/core/data/cache/flight/server.py:59** (unknown)
  ```python
  def _make_flight_info(self, key: str, schema: pa.Schema) -> flight.FlightInfo:
  ```
- **src/core/data/cache/flight/server.py:22** (unknown)
  ```python
  class CacheFlightServer(flight.FlightServerBase):
  ```
- **src/core/data/cache/zero_copy.py:139** (unknown)
  ```python
  struct_type = pa.struct([('real', arrow_type), ('imag', arrow_type)])
  ```
- **src/core/data/cache/zero_copy.py:136** (unknown)
  ```python
  struct_type = pa.struct([('real', arrow_type), ('imag', arrow_type)])
  ```
- **src/core/transcendental/state.py:117** (unknown)
  ```python
  fields.append(pa.field('metadata', pa.string()))
  ```
- **src/core/transcendental/state.py:110** (unknown)
  ```python
  fields.append(pa.field('fractal_component', self._fractal_component.type))
  ```
- **src/core/transcendental/state.py:106** (unknown)
  ```python
  fields.append(pa.field('holographic_component', self._holographic_component.type))
  ```
- **src/core/transcendental/state.py:102** (unknown)
  ```python
  fields.append(pa.field('quantum_component', self._quantum_component.type))
  ```
- **src/operators/arrow_operators/python/numpy_conversions.py:284** (unknown)
  ```python
  dtype: Optional[pa.DataType] = None
  ```
- **src/operators/arrow_operators/python/numpy_conversions.py:156** (unknown)
  ```python
  dtype: Optional[pa.DataType] = None
  ```
- **src/operators/arrow_operators/python/numpy_conversions.py:27** (unknown)
  ```python
  dtype: Optional[pa.DataType] = None
  ```
- **tests/arrow_types/test_fractal.py:30** (unknown)
  ```python
  self.assertTrue(isinstance(fractal_type.storage_type, pa.DataType))
  ```
- **tests/arrow_types/test_holographic.py:30** (unknown)
  ```python
  self.assertTrue(isinstance(holographic_type.storage_type, pa.DataType))
  ```
- **tests/arrow_types/test_quantum.py:34** (unknown)
  ```python
  self.assertTrue(isinstance(quantum_type.storage_type, pa.DataType))
  ```
- **src/core/data/cache/flight/client.py:130** (unknown)
  ```python
  descriptor = flight.FlightDescriptor.for_path(key.encode())
  ```
- **src/core/data/cache/flight/client.py:35** (unknown)
  ```python
  self.client = flight.FlightClient(location, options)
  ```
- **src/core/data/cache/flight/client.py:32** (unknown)
  ```python
  options = flight.FlightClientOptions()
  ```
- **src/core/data/cache/flight/client.py:23** (unknown)
  ```python
  def __init__(self, location: str, options: Optional[flight.FlightClientOptions] = None):
  ```
- **src/core/data/cache/flight/client.py:20** (unknown)
  ```python
  class CacheFlightClient:
  ```
- **src/core/data/cache/flight/distributed_cache_manager.py:122** (unknown)
  ```python
  client = CacheFlightClient(location, options)
  ```
- **src/core/data/cache/flight/distributed_cache_manager.py:119** (unknown)
  ```python
  options = flight.FlightClientOptions()
  ```
- **src/core/data/cache/flight/distributed_cache_manager.py:21** (unknown)
  ```python
  from .client import CacheFlightClient
  ```
- **src/core/data/distributed/arrow_flight_service.py:493** (unknown)
  ```python
  self._client = TTEFlightClient(server_location)
  ```
- **src/core/data/distributed/arrow_flight_service.py:390** (unknown)
  ```python
  descriptor = flight.FlightDescriptor.for_path(path.encode('utf-8'))
  ```
- **src/core/data/distributed/arrow_flight_service.py:320** (unknown)
  ```python
  descriptor = flight.FlightDescriptor.for_path(
  ```
- **src/core/data/distributed/arrow_flight_service.py:280** (unknown)
  ```python
  self._client = flight.FlightClient(location)
  ```
- **src/core/data/distributed/arrow_flight_service.py:267** (unknown)
  ```python
  class TTEFlightClient:
  ```
- **src/core/data/distributed/arrow_flight_service.py:201** (unknown)
  ```python
  descriptor = flight.FlightDescriptor.for_path(path.encode('utf-8'))
  ```
- **src/core/data/distributed/arrow_flight_service.py:194** (unknown)
  ```python
  FlightInfo对象列表
  ```
- **src/core/data/distributed/arrow_flight_service.py:186** (unknown)
  ```python
  criteria: bytes) -> List[flight.FlightInfo]:
  ```
- **src/core/data/distributed/arrow_flight_service.py:125** (unknown)
  ```python
  descriptor: flight.FlightDescriptor,
  ```
- **src/core/data/distributed/arrow_flight_service.py:118** (unknown)
  ```python
  FlightInfo对象
  ```
- **src/core/data/distributed/arrow_flight_service.py:110** (unknown)
  ```python
  descriptor: flight.FlightDescriptor) -> flight.FlightInfo:
  ```
- **src/core/data/distributed/arrow_flight_service.py:110** (unknown)
  ```python
  descriptor: flight.FlightDescriptor) -> flight.FlightInfo:
  ```
- **src/core/data/distributed/arrow_flight_service.py:100** (unknown)
  ```python
  return flight.FlightInfo(
  ```
- **src/core/data/distributed/arrow_flight_service.py:99** (unknown)
  ```python
  # 创建FlightInfo
  ```
- **src/core/data/distributed/arrow_flight_service.py:94** (unknown)
  ```python
  endpoints = [flight.FlightEndpoint(
  ```
- **src/core/data/distributed/arrow_flight_service.py:81** (unknown)
  ```python
  FlightInfo对象
  ```
- **src/core/data/distributed/arrow_flight_service.py:74** (unknown)
  ```python
  """创建FlightInfo对象
  ```
- **src/core/data/distributed/arrow_flight_service.py:73** (unknown)
  ```python
  def _make_flight_info(self, dataset_path: str, descriptor: flight.FlightDescriptor) -> flight.FlightInfo:
  ```
- **src/core/data/distributed/arrow_flight_service.py:73** (unknown)
  ```python
  def _make_flight_info(self, dataset_path: str, descriptor: flight.FlightDescriptor) -> flight.FlightInfo:
  ```
- **src/core/data/distributed/arrow_flight_service.py:47** (unknown)
  ```python
  class TTEFlightServer(flight.FlightServerBase):
  ```

## 手动修复建议

### 返回类型变更修复

```python
# 修复前
phi, theta = np.meshgrid(phi, theta)

# 修复后
phi, theta = list(np.meshgrid(phi, theta))
```

### 类型提升行为修复

```python
# 修复前
result = np.array([1], dtype=np.int8) + np.array([256], dtype=np.uint16)

# 修复后 - 显式指定结果类型
result = (np.array([1], dtype=np.int8) + np.array([256], dtype=np.uint16)).astype(np.int32)
```

