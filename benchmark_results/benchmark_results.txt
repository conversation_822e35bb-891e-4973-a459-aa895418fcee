向量运算性能测试结果

向量大小: 1000
  dot_product: NumPy = 0.000007秒, SIMD = 0.000047秒, 加速比 = 0.14倍
  vector_add: NumPy = 0.000003秒, SIMD = 0.000031秒, 加速比 = 0.10倍
  vector_sub: NumPy = 0.000003秒, SIMD = 0.000031秒, 加速比 = 0.10倍
  vector_mul: NumPy = 0.000003秒, SIMD = 0.000032秒, 加速比 = 0.10倍
  normalize: NumPy = 0.000015秒, SIMD = 0.000154秒, 加速比 = 0.10倍

向量大小: 10000
  dot_product: NumPy = 0.000006秒, SIMD = 0.000143秒, 加速比 = 0.04倍
  vector_add: NumPy = 0.000010秒, SIMD = 0.000036秒, 加速比 = 0.28倍
  vector_sub: NumPy = 0.000009秒, SIMD = 0.000036秒, 加速比 = 0.26倍
  vector_mul: NumPy = 0.000009秒, SIMD = 0.000035秒, 加速比 = 0.24倍
  normalize: NumPy = 0.000023秒, SIMD = 0.000188秒, 加速比 = 0.12倍

向量大小: 100000
  dot_product: NumPy = 0.000060秒, SIMD = 0.014339秒, 加速比 = 0.00倍
  vector_add: NumPy = 0.000147秒, SIMD = 0.002237秒, 加速比 = 0.07倍
  vector_sub: NumPy = 0.000088秒, SIMD = 0.001403秒, 加速比 = 0.06倍
  vector_mul: NumPy = 0.000083秒, SIMD = 0.000062秒, 加速比 = 1.34倍
  normalize: NumPy = 0.002244秒, SIMD = 0.017854秒, 加速比 = 0.13倍

向量大小: 1000000
  dot_product: NumPy = 0.000166秒, SIMD = 0.061226秒, 加速比 = 0.00倍
  vector_add: NumPy = 0.001783秒, SIMD = 0.028166秒, 加速比 = 0.06倍
  vector_sub: NumPy = 0.001127秒, SIMD = 0.028026秒, 加速比 = 0.04倍
  vector_mul: NumPy = 0.001306秒, SIMD = 0.009260秒, 加速比 = 0.14倍
  normalize: NumPy = 0.003815秒, SIMD = 0.095508秒, 加速比 = 0.04倍

