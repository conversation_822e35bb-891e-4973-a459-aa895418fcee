
    <!DOCTYPE html>
    <html>
    <head>
        <title>超越态思维引擎算子库基准测试报告</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
                line-height: 1.6;
            }
            h1, h2, h3 {
                color: #333;
            }
            table {
                border-collapse: collapse;
                width: 100%;
                margin-bottom: 20px;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }
            th {
                background-color: #f2f2f2;
            }
            tr:nth-child(even) {
                background-color: #f9f9f9;
            }
            .success {
                color: green;
            }
            .failed {
                color: red;
            }
            .chart {
                margin: 20px 0;
                max-width: 100%;
            }
        </style>
    </head>
    <body>
        <h1>超越态思维引擎算子库基准测试报告</h1>
        <p>生成时间: 2025-04-30 22:02:44</p>
        
        <h2>测试结果摘要</h2>
        <table>
            <tr>
                <th>类别</th>
                <th>算子</th>
                <th>状态</th>
                <th>执行时间 (秒)</th>
                <th>内存使用 (MB)</th>
                <th>错误信息</th>
            </tr>
    
            <tr>
                <td>explanation</td>
                <td>multilevel_explanation</td>
                <td class="success">Success</td>
                <td>0.0000 ± 0.0000</td>
                <td>0.00</td>
                <td></td>
            </tr>
        
            <tr>
                <td>explanation</td>
                <td>explanation_quality</td>
                <td class="success">Success</td>
                <td>0.0000 ± 0.0000</td>
                <td>0.00</td>
                <td></td>
            </tr>
        
            <tr>
                <td>explanation</td>
                <td>visualization</td>
                <td class="success">Success</td>
                <td>0.0000 ± 0.0000</td>
                <td>0.00</td>
                <td></td>
            </tr>
        
            <tr>
                <td>verification</td>
                <td>multi_method_verification</td>
                <td class="success">Success</td>
                <td>0.0000 ± 0.0000</td>
                <td>0.00</td>
                <td></td>
            </tr>
        
            <tr>
                <td>verification</td>
                <td>consistency_verification</td>
                <td class="success">Success</td>
                <td>0.0001 ± 0.0000</td>
                <td>0.00</td>
                <td></td>
            </tr>
        
            <tr>
                <td>verification</td>
                <td>realtime_verification</td>
                <td class="success">Success</td>
                <td>0.0000 ± 0.0000</td>
                <td>0.01</td>
                <td></td>
            </tr>
        
        </table>
        
        <h2>性能图表</h2>
        <img class="chart" src="benchmark_results.png" alt="性能图表">
        
        <h2>详细结果</h2>
        <p>详细结果请查看 <a href="benchmark_results.json">benchmark_results.json</a> 和 <a href="benchmark_report.csv">benchmark_report.csv</a>。</p>
    </body>
    </html>
    