"""
分形动力学路由算法直接测试

这是一个直接测试脚本，不依赖于项目的其他部分。
"""

import numpy as np
import networkx as nx
import matplotlib.pyplot as plt
from typing import Any, Dict, List, Optional, Set, Tuple, Union
from abc import ABC, abstractmethod
import time
import heapq
import random
from concurrent.futures import ThreadPoolExecutor, as_completed


# 定义算法接口
class AlgorithmInterface(ABC):
    """超越态算法接口基类"""
    
    @abstractmethod
    def __init__(self, **kwargs):
        """初始化算法"""
        pass
    
    @abstractmethod
    def compute(self, input_data: Any, **kwargs) -> Any:
        """执行算法计算"""
        pass
    
    @abstractmethod
    def get_metadata(self) -> Dict[str, Any]:
        """获取算法元数据"""
        pass
    
    @abstractmethod
    def is_compatible_with(self, other_algorithm: 'AlgorithmInterface') -> bool:
        """检查与其他算法的兼容性"""
        pass
    
    @abstractmethod
    def get_performance_metrics(self) -> Dict[str, float]:
        """获取性能指标"""
        pass


# 定义分形模式生成器
class FractalPatternGenerator:
    """分形模式生成器"""
    
    def __init__(self, pattern_type: str = 'standard', 
                 fractal_dimension: float = 1.5,
                 random_seed: Optional[int] = None,
                 use_cache: bool = True):
        """初始化分形模式生成器"""
        self.pattern_type = pattern_type
        self.fractal_dimension = fractal_dimension
        self.random_seed = random_seed
        self.use_cache = use_cache
        
        # 初始化随机数生成器
        self.rng = random.Random(random_seed)
        
        # 初始化缓存
        self._cache = {}
    
    def generate(self, network: nx.Graph) -> Dict[Any, float]:
        """生成分形模式"""
        # 计算节点的度
        degrees = dict(network.degree())
        
        # 计算最大度
        max_degree = max(degrees.values()) if degrees else 1
        
        # 为每个节点分配分形因子
        pattern = {}
        for node, degree in degrees.items():
            # 标准模式：使用节点度的分形维度次方
            pattern[node] = (degree / max_degree) ** self.fractal_dimension
        
        # 确保所有节点都有分形因子
        for node in network.nodes:
            if node not in pattern:
                pattern[node] = 0.1  # 默认值
        
        # 添加随机扰动
        for node in pattern:
            pattern[node] *= 0.8 + 0.4 * self.rng.random()
        
        # 归一化分形模式
        max_factor = max(pattern.values()) if pattern else 1
        for node in pattern:
            pattern[node] /= max_factor
        
        return pattern


# 定义路由性能分析器
class RoutingPerformanceAnalyzer:
    """路由性能分析器"""
    
    def __init__(self, verbose: bool = False):
        """初始化路由性能分析器"""
        self.verbose = verbose
    
    def analyze(self, network: nx.Graph, routes: Dict[Tuple[Any, Any], List[Any]], 
               fractal_pattern: Optional[Dict[Any, float]] = None) -> Dict[str, float]:
        """分析路由性能"""
        # 计算路由成功率
        success_count = sum(1 for route in routes.values() if route is not None)
        total_routes = len(routes)
        success_rate = success_count / total_routes if total_routes > 0 else 0.0
        
        # 计算平均路径长度
        path_lengths = [len(route) - 1 for route in routes.values() if route is not None]
        avg_path_length = sum(path_lengths) / len(path_lengths) if path_lengths else 0.0
        
        # 构建结果
        results = {
            'success_rate': success_rate,
            'average_path_length': avg_path_length
        }
        
        # 如果启用了详细输出，则打印结果
        if self.verbose:
            print("=" * 50)
            print("路由性能分析")
            print("=" * 50)
            for key, value in results.items():
                print(f"{key}: {value:.6f}")
            print("=" * 50)
        
        return results


# 定义分形动力学路由算法
class FractalDynamicsRouter(AlgorithmInterface):
    """分形动力学路由算法"""
    
    def __init__(self, fractal_dimension: float = 1.5,
                 max_iterations: int = 100,
                 tolerance: float = 1e-6,
                 use_parallel: bool = True,
                 num_workers: int = 4,
                 adaptive_routing: bool = True,
                 route_cache_size: int = 1000,
                 route_cache_ttl: int = 300,
                 **kwargs):
        """初始化分形动力学路由算法"""
        self.fractal_dimension = fractal_dimension
        self.max_iterations = max_iterations
        self.tolerance = tolerance
        self.use_parallel = use_parallel
        self.num_workers = num_workers
        self.adaptive_routing = adaptive_routing
        self.route_cache_size = route_cache_size
        self.route_cache_ttl = route_cache_ttl
        
        # 性能指标
        self._performance_metrics = {
            'total_time': 0.0,
            'iterations': 0,
            'convergence_achieved': False,
            'route_success_rate': 0.0,
            'average_path_length': 0.0,
            'route_cache_hit_rate': 0.0
        }
        
        # 创建分形模式生成器和路由性能分析器
        self.pattern_generator = FractalPatternGenerator(fractal_dimension=fractal_dimension)
        self.performance_analyzer = RoutingPerformanceAnalyzer()
        
        # 初始化路由表和路由缓存
        self._routing_table = {}
        self._route_cache = {}
        self._route_cache_timestamps = {}
    
    def compute(self, input_data: Union[nx.Graph, Dict[str, Any]], **kwargs) -> Dict[str, Any]:
        """执行分形动力学路由计算"""
        # 记录开始时间
        start_time = time.time()
        
        # 解析输入数据
        if isinstance(input_data, nx.Graph):
            network = input_data
            source = kwargs.get('source')
            destination = kwargs.get('destination')
            all_pairs = kwargs.get('all_pairs', False)
        elif isinstance(input_data, dict):
            network = input_data.get('network')
            source = input_data.get('source', kwargs.get('source'))
            destination = input_data.get('destination', kwargs.get('destination'))
            all_pairs = input_data.get('all_pairs', kwargs.get('all_pairs', False))
        else:
            raise ValueError("输入数据格式不正确")
        
        # 生成分形路由模式
        fractal_pattern = self.pattern_generator.generate(network)
        
        # 初始化路由结果
        routes = {}
        
        # 如果计算所有节点对之间的路由
        if all_pairs:
            nodes = list(network.nodes)
            for i, src in enumerate(nodes):
                for dst in nodes[i+1:]:
                    # 计算路由
                    route = self._compute_route(network, src, dst, fractal_pattern)
                    routes[(src, dst)] = route
                    routes[(dst, src)] = list(reversed(route)) if route else None
        else:
            # 计算单个路由
            route = self._compute_route(network, source, destination, fractal_pattern)
            routes[(source, destination)] = route
        
        # 记录结束时间
        end_time = time.time()
        
        # 更新性能指标
        success_count = sum(1 for route in routes.values() if route is not None)
        total_routes = len(routes)
        success_rate = success_count / total_routes if total_routes > 0 else 0.0
        
        path_lengths = [len(route) - 1 for route in routes.values() if route is not None]
        avg_path_length = sum(path_lengths) / len(path_lengths) if path_lengths else 0.0
        
        self._performance_metrics.update({
            'total_time': end_time - start_time,
            'iterations': 1,
            'convergence_achieved': True,
            'route_success_rate': success_rate,
            'average_path_length': avg_path_length
        })
        
        # 返回结果
        return {
            'routing_table': self._routing_table,
            'routes': routes,
            'iterations': 1,
            'convergence_achieved': True,
            'performance': self.get_performance_metrics()
        }
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取算法元数据"""
        return {
            'name': 'FractalDynamicsRouter',
            'version': '1.0.0',
            'description': '分形动力学路由算法，用于在动态网络中实现高效路由',
            'parameters': {
                'fractal_dimension': self.fractal_dimension,
                'max_iterations': self.max_iterations,
                'tolerance': self.tolerance,
                'use_parallel': self.use_parallel,
                'num_workers': self.num_workers,
                'adaptive_routing': self.adaptive_routing,
                'route_cache_size': self.route_cache_size,
                'route_cache_ttl': self.route_cache_ttl
            },
            'performance_metrics': self.get_performance_metrics()
        }
    
    def is_compatible_with(self, other_algorithm: AlgorithmInterface) -> bool:
        """检查与其他算法的兼容性"""
        # 检查其他算法是否是FractalDynamicsRouter的实例
        if not isinstance(other_algorithm, FractalDynamicsRouter):
            return False
        
        # 检查其他算法的参数是否与当前算法兼容
        other_metadata = other_algorithm.get_metadata()
        
        # 检查分形维度是否在可接受范围内
        if abs(other_metadata['parameters']['fractal_dimension'] - self.fractal_dimension) > 0.5:
            return False
        
        return True
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """获取性能指标"""
        return self._performance_metrics
    
    def _compute_route(self, network: nx.Graph, source: Any, destination: Any, 
                      fractal_pattern: Dict[Any, float]) -> List[Any]:
        """计算单个路由"""
        if source == destination:
            return [source]
        
        # 使用分形A*算法计算路由
        return self._fractal_astar(network, source, destination, fractal_pattern)
    
    def _fractal_astar(self, network: nx.Graph, source: Any, destination: Any, 
                      fractal_pattern: Dict[Any, float]) -> List[Any]:
        """使用分形A*算法计算路由"""
        # 初始化开放列表和关闭列表
        open_list = [(0, source, [source])]
        closed_set = set()
        
        # 初始化g_score和f_score
        g_score = {source: 0}
        f_score = {source: self._heuristic(network, source, destination, fractal_pattern)}
        
        while open_list:
            # 获取f_score最小的节点
            current_f, current, path = heapq.heappop(open_list)
            
            # 如果到达目标节点，则返回路径
            if current == destination:
                return path
            
            # 如果当前节点已经在关闭列表中，则跳过
            if current in closed_set:
                continue
            
            # 将当前节点添加到关闭列表
            closed_set.add(current)
            
            # 遍历当前节点的邻居
            for neighbor in network.neighbors(current):
                # 如果邻居已经在关闭列表中，则跳过
                if neighbor in closed_set:
                    continue
                
                # 计算从起点到邻居的距离
                tentative_g_score = g_score[current] + network[current][neighbor].get('weight', 1.0)
                
                # 如果邻居不在开放列表中，或者找到了更短的路径
                if neighbor not in g_score or tentative_g_score < g_score[neighbor]:
                    # 更新g_score和f_score
                    g_score[neighbor] = tentative_g_score
                    f_score[neighbor] = tentative_g_score + self._heuristic(network, neighbor, destination, fractal_pattern)
                    
                    # 将邻居添加到开放列表
                    new_path = path + [neighbor]
                    heapq.heappush(open_list, (f_score[neighbor], neighbor, new_path))
        
        # 如果没有找到路径，则返回None
        return None
    
    def _heuristic(self, network: nx.Graph, node: Any, destination: Any, 
                  fractal_pattern: Dict[Any, float]) -> float:
        """计算启发式函数值"""
        # 如果网络有节点位置信息，则使用欧几里得距离
        if all('pos' in network.nodes[n] for n in [node, destination]):
            pos1 = network.nodes[node]['pos']
            pos2 = network.nodes[destination]['pos']
            distance = np.sqrt(sum((p1 - p2) ** 2 for p1, p2 in zip(pos1, pos2)))
        else:
            # 否则使用跳数作为距离
            try:
                distance = nx.shortest_path_length(network, node, destination)
            except nx.NetworkXNoPath:
                distance = float('inf')
        
        # 考虑分形模式
        fractal_factor = fractal_pattern.get(node, 1.0)
        
        # 返回启发式函数值
        return distance / fractal_factor


def create_test_network(num_nodes=30, p=0.15):
    """创建测试网络"""
    # 创建随机图
    network = nx.erdos_renyi_graph(num_nodes, p, seed=42)
    
    # 确保图是连通的
    if not nx.is_connected(network):
        # 找到最大连通分量
        largest_cc = max(nx.connected_components(network), key=len)
        network = network.subgraph(largest_cc).copy()
    
    # 添加节点位置
    pos = nx.spring_layout(network, seed=42)
    for node, position in pos.items():
        network.nodes[node]['pos'] = position
    
    # 添加边权重
    for u, v in network.edges:
        pos_u = network.nodes[u]['pos']
        pos_v = network.nodes[v]['pos']
        distance = np.sqrt(sum((p1 - p2) ** 2 for p1, p2 in zip(pos_u, pos_v)))
        network[u][v]['weight'] = distance
    
    return network


def main():
    """主函数"""
    # 创建测试网络
    print("创建测试网络...")
    network = create_test_network(num_nodes=30, p=0.15)
    
    # 创建路由器
    print("创建路由器...")
    router = FractalDynamicsRouter(
        fractal_dimension=1.5,
        max_iterations=20,
        tolerance=1e-6,
        use_parallel=True,
        num_workers=4,
        adaptive_routing=True
    )
    
    # 选择源节点和目标节点
    source = 0
    destination = len(network.nodes) - 1
    
    # 执行路由计算
    print(f"计算从节点 {source} 到节点 {destination} 的路由...")
    start_time = time.time()
    result = router.compute({
        'network': network,
        'source': source,
        'destination': destination
    })
    end_time = time.time()
    
    print(f"路由计算完成，耗时: {end_time - start_time:.2f}秒")
    
    # 提取路由
    route = result['routes'].get((source, destination))
    if route:
        print(f"找到路由: {route}")
        print(f"路由长度: {len(route) - 1}")
    else:
        print("未找到路由")
    
    # 创建路由性能分析器
    print("创建路由性能分析器...")
    analyzer = RoutingPerformanceAnalyzer(verbose=True)
    
    # 分析路由性能
    print("分析路由性能...")
    analysis = analyzer.analyze(network, {(source, destination): route})
    
    # 计算所有节点对之间的路由
    print("计算所有节点对之间的路由...")
    start_time = time.time()
    all_pairs_result = router.compute({
        'network': network,
        'all_pairs': True
    })
    end_time = time.time()
    
    print(f"所有节点对路由计算完成，耗时: {end_time - start_time:.2f}秒")
    
    # 分析所有路由的性能
    print("分析所有路由的性能...")
    all_pairs_analysis = analyzer.analyze(network, all_pairs_result['routes'])
    
    print("测试成功完成！")


if __name__ == "__main__":
    main()
