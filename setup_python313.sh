#!/bin/bash

# Backup the original .bashrc file
cp ~/.bashrc ~/.bashrc.backup

# Add Python 3.13.3 to PATH in .bashrc
echo '
# Python 3.13.3 configuration
export PATH="/home/<USER>/python_gil_test/Python-3.13.3:$PATH"
alias python="python3.13"
alias python3="python3.13"
alias pip="pip3.13"
alias pip3="pip3.13"
' >> ~/.bashrc

echo "Python 3.13.3 has been set as your default Python version."
echo "The changes will take effect in new terminal sessions."
echo "To apply changes to the current session, run: source ~/.bashrc"
echo "Your original .bashrc has been backed up to ~/.bashrc.backup"
