#!/bin/bash

# 安装Rust库脚本
# 该脚本用于安装超越态思维引擎的Rust库

set -e  # 遇到错误立即退出

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 设置日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建目录
create_directories() {
    log_info "创建目录..."
    mkdir -p lib
    mkdir -p src/rust_bindings/lib
    log_success "目录创建完成"
}

# 复制库文件
copy_libraries() {
    log_info "复制库文件..."
    
    # 复制错误处理算子
    if [ -f "$PROJECT_ROOT/target/release/liboperator_interface_core.so" ]; then
        cp "$PROJECT_ROOT/target/release/liboperator_interface_core.so" "$PROJECT_ROOT/lib/"
        cp "$PROJECT_ROOT/target/release/liboperator_interface_core.so" "$PROJECT_ROOT/src/rust_bindings/lib/"
        log_success "复制错误处理算子成功"
    else
        log_warning "错误处理算子文件不存在，跳过复制"
    fi
    
    # 复制rust_operators库
    if [ -f "$PROJECT_ROOT/target/release/librust_operators.so" ]; then
        cp "$PROJECT_ROOT/target/release/librust_operators.so" "$PROJECT_ROOT/lib/"
        cp "$PROJECT_ROOT/target/release/librust_operators.so" "$PROJECT_ROOT/src/rust_bindings/lib/"
        log_success "复制rust_operators库成功"
    else
        log_warning "rust_operators库文件不存在，跳过复制"
    fi
}

# 创建Python包
create_python_package() {
    log_info "创建Python包..."
    
    # 创建__init__.py文件
    cat > "$PROJECT_ROOT/src/rust_bindings/lib/__init__.py" << EOF
"""
Rust库包装模块

该模块提供了对Rust库的Python包装。
"""

import os
import sys
import logging
from pathlib import Path

# 配置日志
logger = logging.getLogger(__name__)

# 获取当前目录
current_dir = Path(__file__).parent

# 添加当前目录到库路径
os.environ["LD_LIBRARY_PATH"] = f"{os.environ.get('LD_LIBRARY_PATH', '')}:{current_dir}"

# 导入Rust库
try:
    import operator_interface_core
    logger.info("成功导入错误处理算子")
    RUST_AVAILABLE = True
except ImportError as e:
    logger.warning(f"无法导入错误处理算子: {e}")
    RUST_AVAILABLE = False

# 导出模块
__all__ = ["operator_interface_core", "RUST_AVAILABLE"]
EOF
    
    log_success "Python包创建完成"
}

# 设置环境变量
setup_environment() {
    log_info "设置环境变量..."
    
    # 创建环境变量设置脚本
    cat > "$PROJECT_ROOT/setup_env.sh" << EOF
#!/bin/bash

# 设置环境变量
export RUST_LIBRARY_PATH="$PROJECT_ROOT/lib"
export LD_LIBRARY_PATH="\$LD_LIBRARY_PATH:$PROJECT_ROOT/lib"
export PYTHONPATH="\$PYTHONPATH:$PROJECT_ROOT"

echo "环境变量设置完成"
echo "RUST_LIBRARY_PATH: \$RUST_LIBRARY_PATH"
echo "LD_LIBRARY_PATH: \$LD_LIBRARY_PATH"
echo "PYTHONPATH: \$PYTHONPATH"
EOF
    
    chmod +x "$PROJECT_ROOT/setup_env.sh"
    log_success "环境变量设置脚本创建成功"
}

# 主函数
main() {
    # 设置项目根目录
    PROJECT_ROOT=$(pwd)
    
    log_info "开始安装Rust库..."
    log_info "项目根目录: $PROJECT_ROOT"
    
    # 创建目录
    create_directories
    
    # 复制库文件
    copy_libraries
    
    # 创建Python包
    create_python_package
    
    # 设置环境变量
    setup_environment
    
    log_success "Rust库安装完成"
}

# 执行主函数
main
