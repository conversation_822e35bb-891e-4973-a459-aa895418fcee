"""
测试NoncommutativeBundle的Rust实现
"""

import numpy as np
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 直接导入微分几何算子
import importlib.util
import importlib.machinery

# 尝试导入Rust实现
RUST_AVAILABLE = False
try:
    # 查找differential_geometry模块
    module_paths = [
        os.path.join(os.path.dirname(__file__), 'src/operators/differential_geometry/differential_geometry.so'),
        os.path.join(os.path.dirname(__file__), 'target/debug/libdifferential_geometry.so'),
        os.path.join(os.path.dirname(__file__), 'target/release/libdifferential_geometry.so')
    ]

    # 查找存在的模块路径
    module_path = None
    for path in module_paths:
        if os.path.exists(path):
            module_path = path
            break
    if os.path.exists(module_path):
        # 加载模块
        spec = importlib.util.spec_from_file_location("differential_geometry", module_path)
        differential_geometry = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(differential_geometry)

        # 从模块中导入类和函数
        NoncommutativeBundle = differential_geometry.NoncommutativeBundle
        register_bundle = differential_geometry.register_bundle
        RUST_AVAILABLE = True
        print(f"成功导入Rust实现，模块路径: {module_path}")
    else:
        print(f"Rust模块文件不存在: {module_path}")
        raise ImportError("Rust模块文件不存在")
except (ImportError, AttributeError) as e:
    print(f"无法导入differential_geometry模块，可能是Rust实现未正确构建: {e}")

    # 使用测试文件中的Python实现
    from test_bundle import NoncommutativeBundle

    def register_bundle(*args, **kwargs):
        return "dummy_id"

def main():
    """主函数"""
    # 检查Rust实现是否可用
    print(f"Rust实现可用: {RUST_AVAILABLE}")

    if not RUST_AVAILABLE:
        print("Rust实现不可用，使用Python实现")

    # 创建NoncommutativeBundle算子
    bundle = NoncommutativeBundle(
        dimension=3,
        fiber_dimension=2,
        connection_type="levi-civita",
        curvature_enabled=True
    )

    # 打印算子信息
    print(f"算子: {bundle}")

    # 创建测试点集
    points = np.array([
        [1.0, 0.0, 0.0],
        [0.0, 1.0, 0.0],
        [0.0, 0.0, 1.0]
    ])

    # 处理点集
    result = bundle.apply(points)
    print(f"点集处理结果形状: {result.shape}")
    print(f"点集处理结果: \n{result}")

    # 创建测试点
    point = np.array([1.0, 0.0, 0.0])

    # 计算曲率
    curvature = bundle.compute_curvature(point)
    print(f"曲率形状: {curvature.shape}")

    # 计算挠率（只在Rust实现可用时）
    if RUST_AVAILABLE and hasattr(bundle, 'compute_torsion'):
        torsion = bundle.compute_torsion(point)
        print(f"挠率形状: {torsion.shape}")
    else:
        print("Python实现不支持compute_torsion方法")

    # 获取算子元数据
    metadata = bundle.get_metadata()
    print(f"算子元数据: {metadata}")

    # 获取性能指标（只在Rust实现可用时）
    if hasattr(bundle, 'get_performance_metrics'):
        performance_metrics = bundle.get_performance_metrics()
        print(f"性能指标: {performance_metrics}")
    else:
        print("Python实现不支持get_performance_metrics方法")

    # 获取算子复杂度信息（只在Rust实现可用时）
    if hasattr(bundle, 'get_complexity'):
        complexity = bundle.get_complexity()
        print(f"算子复杂度信息: {complexity}")
    else:
        print("Python实现不支持get_complexity方法")

    print("测试完成")

if __name__ == "__main__":
    main()
