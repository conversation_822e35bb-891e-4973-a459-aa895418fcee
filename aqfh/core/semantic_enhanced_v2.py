"""
语义增强版记忆引擎 V2 - 健壮版本

采用渐进式集成策略，确保在各种环境下都能正常工作。
优先使用可用的组件，优雅降级到基础功能。
"""

import sys
import os
import time
import logging
import numpy as np
import pyarrow as pa
import pyarrow.compute as pc
from typing import List, Dict, Any, Optional, Union, Tuple
from datetime import datetime, timedelta
from pathlib import Path
import json
import hashlib

# 导入基础组件
from .enhanced_memory_engine import EnhancedArrowMemoryEngine
from .base_types import MemoryFragment

# 设置日志
logger = logging.getLogger(__name__)

# 组件可用性检查
COMPONENTS_STATUS = {
    'sentence_transformers': False,
    'faiss': False,
    'sklearn': False,
    'numpy_advanced': False
}

# 尝试导入可选组件
try:
    from sentence_transformers import SentenceTransformer
    COMPONENTS_STATUS['sentence_transformers'] = True
    logger.info("✅ SentenceTransformers 可用")
except ImportError:
    logger.info("⚠️ SentenceTransformers 不可用，将使用简单编码")

try:
    import faiss
    COMPONENTS_STATUS['faiss'] = True
    logger.info("✅ FAISS 可用")
except ImportError:
    logger.info("⚠️ FAISS 不可用，将使用暴力搜索")

try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    COMPONENTS_STATUS['sklearn'] = True
    logger.info("✅ Scikit-learn 可用")
except ImportError:
    logger.info("⚠️ Scikit-learn 不可用，将使用基础相似度计算")

# 检查NumPy高级功能
try:
    # 检查是否支持复数和高级操作
    test_complex = np.array([1+2j, 3+4j])
    test_fft = np.fft.fft(test_complex)
    COMPONENTS_STATUS['numpy_advanced'] = True
    logger.info("✅ NumPy高级功能 可用")
except:
    logger.info("⚠️ NumPy高级功能 受限")


class SimpleSemanticEncoder:
    """简单语义编码器 - 不依赖外部库"""

    def __init__(self, dimension: int = 128):
        self.dimension = dimension
        self.vocab = {}
        self.vocab_size = 0

    def _tokenize(self, text: str) -> List[str]:
        """简单分词"""
        # 简单的中英文分词
        import re
        # 分离中文字符和英文单词
        tokens = re.findall(r'[\u4e00-\u9fff]|[a-zA-Z]+', text.lower())
        return tokens

    def _get_word_vector(self, word: str) -> np.ndarray:
        """获取词向量（简单哈希编码）"""
        if word not in self.vocab:
            # 使用哈希函数生成稳定的向量
            hash_obj = hashlib.md5(word.encode())
            hash_bytes = hash_obj.digest()

            # 转换为向量
            vector = np.frombuffer(hash_bytes, dtype=np.uint8)
            # 扩展到指定维度
            if len(vector) < self.dimension:
                vector = np.tile(vector, (self.dimension // len(vector) + 1))[:self.dimension]
            else:
                vector = vector[:self.dimension]

            # 归一化
            vector = vector.astype(np.float32)
            vector = vector / (np.linalg.norm(vector) + 1e-8)

            self.vocab[word] = vector
            self.vocab_size += 1

        return self.vocab[word]

    def encode(self, text: str) -> np.ndarray:
        """编码文本为向量"""
        tokens = self._tokenize(text)
        if not tokens:
            return np.zeros(self.dimension, dtype=np.float32)

        # 平均词向量
        vectors = [self._get_word_vector(token) for token in tokens]
        result = np.mean(vectors, axis=0)

        # 归一化
        norm = np.linalg.norm(result)
        if norm > 0:
            result = result / norm

        return result


class TfidfSemanticEncoder:
    """基于TF-IDF的语义编码器"""

    def __init__(self, max_features: int = 1000):
        if not COMPONENTS_STATUS['sklearn']:
            raise ImportError("Scikit-learn 不可用")

        self.vectorizer = TfidfVectorizer(
            max_features=max_features,
            stop_words=None,  # 保留所有词
            ngram_range=(1, 2)  # 使用1-2gram
        )
        self.fitted = False
        self.corpus = []

    def fit(self, texts: List[str]):
        """训练编码器"""
        self.corpus.extend(texts)
        self.vectorizer.fit(self.corpus)
        self.fitted = True

    def encode(self, text: str) -> np.ndarray:
        """编码文本"""
        if not self.fitted:
            # 如果没有训练，先用当前文本训练
            self.fit([text])

        try:
            vector = self.vectorizer.transform([text]).toarray()[0]
            return vector.astype(np.float32)
        except:
            # 如果失败，返回零向量
            return np.zeros(self.vectorizer.max_features, dtype=np.float32)


class TransformerSemanticEncoder:
    """基于Transformer的语义编码器"""

    def __init__(self, model_name: str = 'paraphrase-multilingual-MiniLM-L12-v2'):
        if not COMPONENTS_STATUS['sentence_transformers']:
            raise ImportError("SentenceTransformers 不可用")

        try:
            self.model = SentenceTransformer(model_name)
            logger.info(f"✅ 加载Transformer模型: {model_name}")
        except Exception as e:
            logger.warning(f"加载Transformer模型失败: {e}")
            raise

    def encode(self, text: str) -> np.ndarray:
        """编码文本"""
        try:
            vector = self.model.encode(text)
            return vector.astype(np.float32)
        except Exception as e:
            logger.error(f"Transformer编码失败: {e}")
            return np.zeros(384, dtype=np.float32)  # 默认维度


class SemanticEnhancedMemoryEngineV2(EnhancedArrowMemoryEngine):
    """
    语义增强版记忆引擎 V2

    健壮的实现，支持多种语义编码器的渐进式集成。
    """

    def __init__(self, storage_path: Optional[Path] = None,
                 cache_config: Optional[Dict[str, Any]] = None,
                 semantic_config: Optional[Dict[str, Any]] = None):
        """
        初始化语义增强版记忆引擎 V2

        Args:
            storage_path: 存储路径
            cache_config: 缓存配置
            semantic_config: 语义配置
        """
        # 初始化基础引擎
        super().__init__(storage_path, cache_config)

        # 默认语义配置
        default_semantic_config = {
            'encoder_type': 'auto',  # auto, simple, tfidf, transformer
            'vector_dimension': 128,
            'similarity_threshold': 0.7,
            'enable_vector_cache': True,
            'enable_faiss_index': COMPONENTS_STATUS['faiss'],
            'transformer_model': 'paraphrase-multilingual-MiniLM-L12-v2'
        }

        # 合并用户配置
        if semantic_config:
            default_semantic_config.update(semantic_config)

        self.semantic_config = default_semantic_config

        # 初始化语义编码器
        self.semantic_encoder = self._init_semantic_encoder()

        # 初始化向量索引
        self.vector_index = self._init_vector_index()

        # 语义向量缓存
        self.vector_cache = {}

        # 统计信息
        self.semantic_stats = {
            'encodings': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'searches': 0,
            'index_searches': 0
        }

        # 为了兼容性，创建完整的stats字典
        self.stats = {
            'total_memories': 0,
            'disk_reads': 0,
            'disk_writes': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'encodings': 0,
            'searches': 0,
            'index_searches': 0
        }

        logger.info(f"语义增强版记忆引擎V2初始化完成")
        logger.info(f"编码器类型: {type(self.semantic_encoder).__name__}")
        logger.info(f"向量索引: {'✅' if self.vector_index else '❌'}")

    def _init_semantic_encoder(self):
        """初始化语义编码器"""
        encoder_type = self.semantic_config['encoder_type']

        if encoder_type == 'auto':
            # 自动选择最佳可用编码器
            if COMPONENTS_STATUS['sentence_transformers']:
                try:
                    return TransformerSemanticEncoder(
                        self.semantic_config['transformer_model']
                    )
                except:
                    pass

            if COMPONENTS_STATUS['sklearn']:
                try:
                    return TfidfSemanticEncoder(
                        max_features=self.semantic_config['vector_dimension']
                    )
                except:
                    pass

            # 回退到简单编码器
            return SimpleSemanticEncoder(
                dimension=self.semantic_config['vector_dimension']
            )

        elif encoder_type == 'transformer':
            return TransformerSemanticEncoder(
                self.semantic_config['transformer_model']
            )

        elif encoder_type == 'tfidf':
            return TfidfSemanticEncoder(
                max_features=self.semantic_config['vector_dimension']
            )

        elif encoder_type == 'simple':
            return SimpleSemanticEncoder(
                dimension=self.semantic_config['vector_dimension']
            )

        else:
            raise ValueError(f"未知的编码器类型: {encoder_type}")

    def _init_vector_index(self):
        """初始化向量索引"""
        if not self.semantic_config['enable_faiss_index'] or not COMPONENTS_STATUS['faiss']:
            return None

        try:
            # 获取向量维度
            if hasattr(self.semantic_encoder, 'model'):
                # Transformer编码器
                dimension = 384  # 默认维度
            elif hasattr(self.semantic_encoder, 'vectorizer'):
                # TF-IDF编码器
                dimension = self.semantic_encoder.vectorizer.max_features
            else:
                # 简单编码器
                dimension = self.semantic_encoder.dimension

            # 创建FAISS索引
            index = faiss.IndexFlatIP(dimension)  # 内积索引
            logger.info(f"✅ FAISS索引初始化成功，维度: {dimension}")
            return index

        except Exception as e:
            logger.warning(f"FAISS索引初始化失败: {e}")
            return None

    def encode_memory_semantics(self, memory: MemoryFragment) -> Optional[np.ndarray]:
        """
        对记忆进行语义编码

        Args:
            memory: 记忆片段

        Returns:
            语义向量，如果编码失败则返回None
        """
        try:
            # 检查缓存
            if self.semantic_config['enable_vector_cache']:
                cached_vector = self.vector_cache.get(memory.memory_id)
                if cached_vector is not None:
                    self.semantic_stats['cache_hits'] += 1
                    return cached_vector
                self.semantic_stats['cache_misses'] += 1

            # 准备输入文本
            input_text = f"{memory.content} {' '.join(memory.tags)}"

            # 语义编码
            start_time = time.time()
            semantic_vector = self.semantic_encoder.encode(input_text)
            encoding_time = time.time() - start_time

            # 缓存结果
            if self.semantic_config['enable_vector_cache']:
                self.vector_cache[memory.memory_id] = semantic_vector

            # 更新统计
            self.semantic_stats['encodings'] += 1

            logger.debug(f"语义编码耗时: {encoding_time*1000:.2f}ms, 维度: {len(semantic_vector)}")

            return semantic_vector

        except Exception as e:
            logger.error(f"语义编码失败: {e}")
            return None

    def semantic_search(self, query: str,
                       limit: int = 10,
                       similarity_threshold: float = None) -> List[MemoryFragment]:
        """
        语义搜索记忆

        Args:
            query: 搜索查询
            limit: 返回结果数量限制
            similarity_threshold: 相似度阈值

        Returns:
            匹配的记忆片段列表
        """
        try:
            start_time = time.time()

            if similarity_threshold is None:
                similarity_threshold = self.semantic_config['similarity_threshold']

            # 1. 对查询进行语义编码
            query_vector = self.semantic_encoder.encode(query)
            if query_vector is None or len(query_vector) == 0:
                logger.warning("查询编码失败，回退到传统搜索")
                return self.search_memories(query, limit=limit)

            # 2. 如果有FAISS索引，使用高效搜索
            if self.vector_index is not None:
                return self._faiss_search(query_vector, limit, similarity_threshold)

            # 3. 否则使用暴力搜索
            return self._brute_force_search(query_vector, limit, similarity_threshold)

        except Exception as e:
            logger.error(f"语义搜索失败: {e}")
            # 回退到传统搜索
            return self.search_memories(query, limit=limit)
        finally:
            search_time = time.time() - start_time
            self.semantic_stats['searches'] += 1
            logger.debug(f"语义搜索耗时: {search_time*1000:.2f}ms")

    def _faiss_search(self, query_vector: np.ndarray,
                     limit: int, similarity_threshold: float) -> List[MemoryFragment]:
        """使用FAISS索引进行高效搜索"""
        try:
            # 确保查询向量是正确的形状
            query_vector = query_vector.reshape(1, -1).astype(np.float32)

            # 搜索最相似的向量
            scores, indices = self.vector_index.search(query_vector, min(limit * 2, 100))

            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx >= 0 and score >= similarity_threshold:
                    # 根据索引获取记忆
                    memory = self._get_memory_by_index(idx)
                    if memory:
                        results.append((memory, score))

            # 按相似度排序并限制数量
            results.sort(key=lambda x: x[1], reverse=True)

            self.semantic_stats['index_searches'] += 1
            return [memory for memory, _ in results[:limit]]

        except Exception as e:
            logger.error(f"FAISS搜索失败: {e}")
            return self._brute_force_search(query_vector.flatten(), limit, similarity_threshold)

    def _brute_force_search(self, query_vector: np.ndarray,
                           limit: int, similarity_threshold: float) -> List[MemoryFragment]:
        """暴力语义搜索"""
        try:
            if self._memory_table is None or len(self._memory_table) == 0:
                return []

            # 获取所有记忆的语义向量
            memory_vectors = []
            memory_fragments = []

            for i in range(len(self._memory_table)):
                memory = self._table_row_to_memory_fragment(self._memory_table, i)
                semantic_vector = self.encode_memory_semantics(memory)

                if semantic_vector is not None:
                    memory_vectors.append(semantic_vector)
                    memory_fragments.append(memory)

            if not memory_vectors:
                return []

            # 计算相似度
            similarities = self._compute_similarities(query_vector, memory_vectors)

            # 过滤和排序结果
            results = []
            for i, similarity in enumerate(similarities):
                if similarity >= similarity_threshold:
                    results.append((memory_fragments[i], similarity))

            results.sort(key=lambda x: x[1], reverse=True)
            return [memory for memory, _ in results[:limit]]

        except Exception as e:
            logger.error(f"暴力语义搜索失败: {e}")
            return []

    def _compute_similarities(self, query_vector: np.ndarray,
                            memory_vectors: List[np.ndarray]) -> List[float]:
        """计算相似度"""
        if COMPONENTS_STATUS['sklearn']:
            try:
                # 使用sklearn的余弦相似度
                from sklearn.metrics.pairwise import cosine_similarity

                # 重塑向量
                query_vector = query_vector.reshape(1, -1)
                memory_matrix = np.vstack(memory_vectors)

                similarities = cosine_similarity(query_vector, memory_matrix)[0]
                return similarities.tolist()

            except Exception as e:
                logger.debug(f"sklearn相似度计算失败: {e}")

        # 回退到手动计算
        similarities = []
        for vec in memory_vectors:
            try:
                # 余弦相似度
                dot_product = np.dot(query_vector, vec)
                norm_query = np.linalg.norm(query_vector)
                norm_vec = np.linalg.norm(vec)
                similarity = dot_product / (norm_query * norm_vec + 1e-8)
                similarities.append(float(similarity))
            except:
                similarities.append(0.0)

        return similarities

    def _get_memory_by_index(self, index: int) -> Optional[MemoryFragment]:
        """根据索引获取记忆"""
        try:
            if self._memory_table is None or index >= len(self._memory_table):
                return None
            return self._table_row_to_memory_fragment(self._memory_table, index)
        except Exception as e:
            logger.error(f"根据索引获取记忆失败: {e}")
            return None

    def build_vector_index(self):
        """构建向量索引"""
        if self.vector_index is None:
            logger.warning("向量索引不可用")
            return

        try:
            if self._memory_table is None or len(self._memory_table) == 0:
                logger.info("没有记忆数据，跳过索引构建")
                return

            logger.info("开始构建向量索引...")
            vectors = []

            for i in range(len(self._memory_table)):
                memory = self._table_row_to_memory_fragment(self._memory_table, i)
                semantic_vector = self.encode_memory_semantics(memory)

                if semantic_vector is not None:
                    vectors.append(semantic_vector)
                else:
                    # 添加零向量占位
                    vectors.append(np.zeros_like(vectors[0] if vectors else
                                                self.semantic_encoder.encode("placeholder")))

            if vectors:
                # 转换为numpy数组
                vector_matrix = np.vstack(vectors).astype(np.float32)

                # 添加到FAISS索引
                self.vector_index.add(vector_matrix)

                logger.info(f"✅ 向量索引构建完成，包含 {len(vectors)} 个向量")

        except Exception as e:
            logger.error(f"构建向量索引失败: {e}")

    def get_semantic_stats(self) -> Dict[str, Any]:
        """获取语义统计信息"""
        base_stats = self.get_cache_stats()
        base_stats['semantic_stats'] = self.semantic_stats
        base_stats['semantic_config'] = {
            'encoder_type': type(self.semantic_encoder).__name__,
            'vector_index_available': self.vector_index is not None,
            'components_status': COMPONENTS_STATUS
        }
        return base_stats
