"""
增强版Arrow记忆引擎

集成AQFH多级缓存系统的高性能记忆存储引擎。
实现L0(RAM) -> L1(NVMe) -> L2(SAS) -> L3(冷存储)的多级缓存架构。
"""

import pyarrow as pa
import pyarrow.compute as pc
import pyarrow.parquet as pq
import numpy as np
import json
import time
import threading
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
from pathlib import Path
import uuid
import logging
import hashlib

from .base_types import MemoryFragment, ConsciousnessState, AssociationStrength

# 设置日志
logger = logging.getLogger(__name__)


class CacheLevel:
    """缓存级别定义"""
    L0 = 0  # RAM缓存
    L1 = 1  # NVMe缓存
    L2 = 2  # SAS缓存
    L3 = 3  # 冷存储


class CachePolicy:
    """缓存策略定义"""
    LRU = "lru"
    LFU = "lfu"
    FIFO = "fifo"
    TTL = "ttl"


class CacheEntry:
    """缓存条目"""

    def __init__(self, value: Any, expire_time: Optional[float] = None):
        self.value = value
        self.create_time = time.time()
        self.last_access_time = time.time()
        self.access_count = 0
        self.expire_time = expire_time

    def access(self) -> Any:
        """访问缓存条目"""
        self.last_access_time = time.time()
        self.access_count += 1
        return self.value

    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.expire_time is None:
            return False
        return time.time() > self.expire_time


class MemoryCache:
    """L0级内存缓存"""

    def __init__(self, capacity: int = 1000, policy: str = CachePolicy.LRU):
        self.capacity = capacity
        self.policy = policy
        self.cache = {}
        self.lock = threading.RLock()

        # 统计信息
        self.hits = 0
        self.misses = 0
        self.evictions = 0

        logger.info(f"L0内存缓存初始化，容量: {capacity}, 策略: {policy}")

    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self.lock:
            if key not in self.cache:
                self.misses += 1
                return None

            entry = self.cache[key]
            if entry.is_expired():
                self.remove(key)
                self.misses += 1
                return None

            self.hits += 1
            return entry.access()

    def put(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        """放入缓存值"""
        with self.lock:
            expire_time = None
            if ttl is not None:
                expire_time = time.time() + ttl

            entry = CacheEntry(value, expire_time)

            # 检查是否需要淘汰
            if len(self.cache) >= self.capacity and key not in self.cache:
                self._evict()

            self.cache[key] = entry
            return True

    def remove(self, key: str) -> bool:
        """移除缓存值"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False

    def clear(self) -> bool:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            return True

    def contains(self, key: str) -> bool:
        """检查是否包含键"""
        with self.lock:
            return key in self.cache and not self.cache[key].is_expired()

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.lock:
            return {
                'level': 'L0',
                'capacity': self.capacity,
                'size': len(self.cache),
                'hits': self.hits,
                'misses': self.misses,
                'evictions': self.evictions,
                'hit_rate': self.hits / (self.hits + self.misses) if (self.hits + self.misses) > 0 else 0
            }

    def _evict(self) -> bool:
        """淘汰缓存条目"""
        if not self.cache:
            return False

        if self.policy == CachePolicy.LRU:
            # 最近最少使用
            key_to_evict = min(self.cache.keys(),
                             key=lambda k: self.cache[k].last_access_time)
        elif self.policy == CachePolicy.LFU:
            # 最少使用频率
            key_to_evict = min(self.cache.keys(),
                             key=lambda k: self.cache[k].access_count)
        elif self.policy == CachePolicy.FIFO:
            # 先进先出
            key_to_evict = min(self.cache.keys(),
                             key=lambda k: self.cache[k].create_time)
        else:
            # 默认LRU
            key_to_evict = min(self.cache.keys(),
                             key=lambda k: self.cache[k].last_access_time)

        if key_to_evict:
            self.remove(key_to_evict)
            self.evictions += 1
            return True

        return False


class DiskCache:
    """L1/L2级磁盘缓存"""

    def __init__(self, cache_dir: str, capacity_mb: int = 1024,
                 policy: str = CachePolicy.LRU, compression: str = "zstd"):
        self.cache_dir = Path(cache_dir)
        self.capacity_mb = capacity_mb
        self.policy = policy
        self.compression = compression
        self.lock = threading.RLock()

        # 创建缓存目录
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.meta_dir = self.cache_dir / "meta"
        self.data_dir = self.cache_dir / "data"
        self.meta_dir.mkdir(exist_ok=True)
        self.data_dir.mkdir(exist_ok=True)

        # 加载元数据
        self.metadata = self._load_metadata()

        # 统计信息
        self.hits = 0
        self.misses = 0
        self.evictions = 0

        logger.info(f"磁盘缓存初始化，目录: {cache_dir}, 容量: {capacity_mb}MB, 压缩: {compression}")

    def _hash_key(self, key: str) -> str:
        """生成键的哈希值"""
        return hashlib.md5(key.encode()).hexdigest()

    def _load_metadata(self) -> Dict[str, Any]:
        """加载元数据"""
        metadata_file = self.cache_dir / "metadata.json"
        if metadata_file.exists():
            try:
                with open(metadata_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载元数据失败: {e}")
        return {}

    def _save_metadata(self, key_hash: str, meta: Dict[str, Any]):
        """保存单个元数据"""
        self.metadata[key_hash] = meta
        self._save_all_metadata()

    def _save_all_metadata(self):
        """保存所有元数据"""
        metadata_file = self.cache_dir / "metadata.json"
        try:
            with open(metadata_file, 'w') as f:
                json.dump(self.metadata, f, indent=2)
        except Exception as e:
            logger.error(f"保存元数据失败: {e}")

    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self.lock:
            key_hash = self._hash_key(key)

            if key_hash not in self.metadata:
                self.misses += 1
                return None

            meta = self.metadata[key_hash]

            # 检查是否过期
            if meta.get('expire_time') and time.time() > meta['expire_time']:
                self.remove(key)
                self.misses += 1
                return None

            # 读取数据文件
            data_path = self.data_dir / f"{key_hash}.parquet"
            if not data_path.exists():
                self.misses += 1
                return None

            try:
                # 读取Parquet文件
                table = pq.read_table(data_path)

                # 更新访问时间
                meta['last_access_time'] = time.time()
                meta['access_count'] = meta.get('access_count', 0) + 1
                self._save_metadata(key_hash, meta)

                self.hits += 1
                return table

            except Exception as e:
                logger.error(f"读取缓存数据失败: {e}")
                self.misses += 1
                return None

    def put(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        """放入缓存值"""
        with self.lock:
            try:
                key_hash = self._hash_key(key)

                # 检查是否需要淘汰
                self._check_capacity()

                # 转换为Arrow表格（如果不是的话）
                if isinstance(value, pa.Table):
                    table = value
                else:
                    # 简单转换，实际应用中可能需要更复杂的转换逻辑
                    table = pa.table({'data': [str(value)]})

                # 写入Parquet文件
                data_path = self.data_dir / f"{key_hash}.parquet"
                pq.write_table(table, data_path, compression=self.compression)

                # 创建元数据
                meta = {
                    'key_hash': key_hash,
                    'create_time': time.time(),
                    'last_access_time': time.time(),
                    'access_count': 0,
                    'size': data_path.stat().st_size,
                    'expire_time': time.time() + ttl if ttl is not None else None,
                }

                # 保存元数据
                self._save_metadata(key_hash, meta)

                return True

            except Exception as e:
                logger.error(f"写入缓存数据失败: {e}")
                return False

    def remove(self, key: str) -> bool:
        """移除缓存值"""
        with self.lock:
            key_hash = self._hash_key(key)

            if key_hash not in self.metadata:
                return False

            try:
                # 删除数据文件
                data_path = self.data_dir / f"{key_hash}.parquet"
                if data_path.exists():
                    data_path.unlink()

                # 删除元数据
                del self.metadata[key_hash]
                self._save_all_metadata()

                return True

            except Exception as e:
                logger.error(f"删除缓存数据失败: {e}")
                return False

    def clear(self) -> bool:
        """清空缓存"""
        with self.lock:
            try:
                # 删除所有数据文件
                for file_path in self.data_dir.glob("*.parquet"):
                    file_path.unlink()

                # 清空元数据
                self.metadata.clear()
                self._save_all_metadata()

                return True

            except Exception as e:
                logger.error(f"清空缓存失败: {e}")
                return False

    def contains(self, key: str) -> bool:
        """检查是否包含键"""
        with self.lock:
            key_hash = self._hash_key(key)

            if key_hash not in self.metadata:
                return False

            meta = self.metadata[key_hash]

            # 检查是否过期
            if meta.get('expire_time') and time.time() > meta['expire_time']:
                self.remove(key)
                return False

            return True

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.lock:
            total_size = sum(meta.get('size', 0) for meta in self.metadata.values())
            return {
                'level': 'L1/L2',
                'capacity_mb': self.capacity_mb,
                'size_mb': total_size / (1024 * 1024),
                'count': len(self.metadata),
                'hits': self.hits,
                'misses': self.misses,
                'evictions': self.evictions,
                'hit_rate': self.hits / (self.hits + self.misses) if (self.hits + self.misses) > 0 else 0
            }

    def _check_capacity(self):
        """检查容量并进行淘汰"""
        total_size_mb = sum(meta.get('size', 0) for meta in self.metadata.values()) / (1024 * 1024)

        while total_size_mb > self.capacity_mb and self.metadata:
            self._evict()
            total_size_mb = sum(meta.get('size', 0) for meta in self.metadata.values()) / (1024 * 1024)

    def _evict(self) -> bool:
        """淘汰缓存条目"""
        if not self.metadata:
            return False

        if self.policy == CachePolicy.LRU:
            # 最近最少使用
            key_to_evict = min(self.metadata.keys(),
                             key=lambda k: self.metadata[k].get('last_access_time', 0))
        elif self.policy == CachePolicy.LFU:
            # 最少使用频率
            key_to_evict = min(self.metadata.keys(),
                             key=lambda k: self.metadata[k].get('access_count', 0))
        elif self.policy == CachePolicy.FIFO:
            # 先进先出
            key_to_evict = min(self.metadata.keys(),
                             key=lambda k: self.metadata[k].get('create_time', 0))
        else:
            # 默认LRU
            key_to_evict = min(self.metadata.keys(),
                             key=lambda k: self.metadata[k].get('last_access_time', 0))

        if key_to_evict:
            # 通过key_hash删除
            try:
                data_path = self.data_dir / f"{key_to_evict}.parquet"
                if data_path.exists():
                    data_path.unlink()
                del self.metadata[key_to_evict]
                self._save_all_metadata()
                self.evictions += 1
                return True
            except Exception as e:
                logger.error(f"淘汰缓存条目失败: {e}")

        return False


class MultiLevelCache:
    """多级缓存管理器"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化多级缓存管理器

        Args:
            config: 缓存配置
                - base_dir: 基础目录
                - L0: L0缓存配置 {capacity: int, policy: str}
                - L1: L1缓存配置 {capacity_mb: int, policy: str, compression: str}
                - L2: L2缓存配置 {capacity_mb: int, policy: str, compression: str}
                - L3: L3缓存配置 {capacity_mb: int, policy: str, compression: str}
        """
        self.config = config
        self.base_dir = Path(config.get('base_dir', '~/.aqfh/mcp_memory')).expanduser()
        self.base_dir.mkdir(parents=True, exist_ok=True)

        # 初始化各级缓存
        self.caches = {}

        # L0: RAM缓存
        l0_config = config.get('L0', {})
        self.caches[CacheLevel.L0] = MemoryCache(
            capacity=l0_config.get('capacity', 1000),
            policy=l0_config.get('policy', CachePolicy.LRU)
        )

        # L1: NVMe缓存
        l1_config = config.get('L1', {})
        if l1_config.get('enabled', True):
            self.caches[CacheLevel.L1] = DiskCache(
                cache_dir=str(self.base_dir / 'L1'),
                capacity_mb=l1_config.get('capacity_mb', 500),
                policy=l1_config.get('policy', CachePolicy.LRU),
                compression=l1_config.get('compression', 'zstd')
            )

        # L2: SAS缓存
        l2_config = config.get('L2', {})
        if l2_config.get('enabled', True):
            self.caches[CacheLevel.L2] = DiskCache(
                cache_dir=str(self.base_dir / 'L2'),
                capacity_mb=l2_config.get('capacity_mb', 2048),
                policy=l2_config.get('policy', CachePolicy.LRU),
                compression=l2_config.get('compression', 'zstd')
            )

        # L3: 冷存储
        l3_config = config.get('L3', {})
        if l3_config.get('enabled', False):
            self.caches[CacheLevel.L3] = DiskCache(
                cache_dir=str(self.base_dir / 'L3'),
                capacity_mb=l3_config.get('capacity_mb', 10240),
                policy=l3_config.get('policy', CachePolicy.LRU),
                compression=l3_config.get('compression', 'zstd')
            )

        self.lock = threading.RLock()

        logger.info(f"多级缓存管理器初始化完成，基础目录: {self.base_dir}")

    def get(self, key: str) -> Optional[Any]:
        """获取缓存值，按L0->L1->L2->L3顺序查找"""
        with self.lock:
            # 按级别顺序查找
            for level in [CacheLevel.L0, CacheLevel.L1, CacheLevel.L2, CacheLevel.L3]:
                if level in self.caches:
                    value = self.caches[level].get(key)
                    if value is not None:
                        # 将值提升到更高级别的缓存
                        self._promote_value(key, value, level)
                        return value

            return None

    def put(self, key: str, value: Any, ttl: Optional[float] = None,
           target_levels: Optional[List[int]] = None) -> bool:
        """放入缓存值到指定级别"""
        with self.lock:
            if target_levels is None:
                target_levels = [CacheLevel.L0, CacheLevel.L1]

            success = True
            for level in target_levels:
                if level in self.caches:
                    if not self.caches[level].put(key, value, ttl):
                        success = False

            return success

    def remove(self, key: str) -> bool:
        """从所有级别移除缓存值"""
        with self.lock:
            success = True
            for level, cache in self.caches.items():
                if not cache.remove(key):
                    success = False
            return success

    def clear(self) -> bool:
        """清空所有级别的缓存"""
        with self.lock:
            success = True
            for level, cache in self.caches.items():
                if not cache.clear():
                    success = False
            return success

    def contains(self, key: str) -> bool:
        """检查是否包含键（任意级别）"""
        with self.lock:
            for level, cache in self.caches.items():
                if cache.contains(key):
                    return True
            return False

    def get_stats(self) -> Dict[str, Any]:
        """获取所有级别的统计信息"""
        with self.lock:
            stats = {}
            for level, cache in self.caches.items():
                stats[f'L{level}'] = cache.get_stats()
            return stats

    def _promote_value(self, key: str, value: Any, current_level: int):
        """将值提升到更高级别的缓存"""
        # 提升到更高级别（数字更小的级别）
        for level in range(current_level):
            if level in self.caches:
                self.caches[level].put(key, value)

    def demote_to_lower_level(self, key: str, target_level: int) -> bool:
        """将值降级到更低级别"""
        with self.lock:
            # 从高级别获取值
            value = None
            for level in range(target_level):
                if level in self.caches and self.caches[level].contains(key):
                    value = self.caches[level].get(key)
                    self.caches[level].remove(key)
                    break

            if value is not None and target_level in self.caches:
                return self.caches[target_level].put(key, value)

            return False


class EnhancedArrowMemoryEngine:
    """
    增强版Arrow记忆引擎

    集成多级缓存系统的高性能记忆存储引擎。
    实现L0(RAM) -> L1(NVMe) -> L2(SAS) -> L3(冷存储)的多级缓存架构。
    """

    def __init__(self, storage_path: Optional[Path] = None, cache_config: Optional[Dict[str, Any]] = None):
        """
        初始化增强版记忆引擎

        Args:
            storage_path: 存储路径
            cache_config: 缓存配置
        """
        self.storage_path = storage_path or Path("~/.aqfh/mcp_memory").expanduser()
        self.storage_path.mkdir(parents=True, exist_ok=True)

        # 默认缓存配置
        default_cache_config = {
            'base_dir': str(self.storage_path),
            'L0': {
                'capacity': 100,  # 100个记忆片段
                'policy': CachePolicy.LRU
            },
            'L1': {
                'enabled': True,
                'capacity_mb': 100,  # 100MB
                'policy': CachePolicy.LRU,
                'compression': 'zstd'
            },
            'L2': {
                'enabled': True,
                'capacity_mb': 500,  # 500MB
                'policy': CachePolicy.LRU,
                'compression': 'zstd'
            },
            'L3': {
                'enabled': False,  # 默认不启用冷存储
                'capacity_mb': 2048,  # 2GB
                'policy': CachePolicy.LRU,
                'compression': 'zstd'
            }
        }

        # 合并用户配置
        if cache_config:
            default_cache_config.update(cache_config)

        # 初始化多级缓存
        self.cache = MultiLevelCache(default_cache_config)

        # 内存中的记忆表（用于快速访问）
        self._memory_table: Optional[pa.Table] = None
        self._association_table: Optional[pa.Table] = None

        # 初始化空表结构
        self._init_memory_schema()
        self._init_association_schema()

        # 加载已有数据
        self._load_existing_data()

        # 统计信息
        self.stats = {
            'total_memories': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'disk_reads': 0,
            'disk_writes': 0
        }

        logger.info(f"增强版Arrow记忆引擎初始化完成，存储路径: {self.storage_path}")

    def _init_memory_schema(self):
        """初始化记忆表的Schema"""
        self.memory_schema = pa.schema([
            ("memory_id", pa.string()),
            ("content", pa.string()),
            ("content_type", pa.string()),
            ("timestamp", pa.timestamp("us")),
            ("importance", pa.float64()),
            ("emotional_valence", pa.float64()),
            ("context_json", pa.string()),  # JSON序列化的上下文
            ("embedding", pa.list_(pa.float64())),  # 向量嵌入
            ("quantum_real", pa.list_(pa.float64())),  # 量子态实部
            ("quantum_imag", pa.list_(pa.float64())),  # 量子态虚部
            ("associations_json", pa.string()),  # JSON序列化的关联列表
            ("tags_json", pa.string()),  # JSON序列化的标签列表
        ])

        # 创建空表
        self._memory_table = pa.Table.from_arrays(
            [pa.array([], type=field.type) for field in self.memory_schema],
            schema=self.memory_schema
        )

    def _init_association_schema(self):
        """初始化关联表的Schema"""
        self.association_schema = pa.schema([
            ("source_id", pa.string()),
            ("target_id", pa.string()),
            ("semantic_similarity", pa.float64()),
            ("temporal_proximity", pa.float64()),
            ("contextual_overlap", pa.float64()),
            ("quantum_entanglement", pa.float64()),
            ("overall_strength", pa.float64()),
            ("association_type", pa.string()),
            ("created_at", pa.timestamp("us")),
        ])

        # 创建空表
        self._association_table = pa.Table.from_arrays(
            [pa.array([], type=field.type) for field in self.association_schema],
            schema=self.association_schema
        )

    def _load_existing_data(self):
        """加载已有的数据文件"""
        # 尝试从各级缓存加载数据
        memory_file = self.storage_path / "memories.parquet"
        association_file = self.storage_path / "associations.parquet"

        try:
            if memory_file.exists():
                self._memory_table = pq.read_table(memory_file)
                logger.info(f"从磁盘加载了 {len(self._memory_table)} 条记忆")
                self.stats['disk_reads'] += 1
                # 更新记忆总数统计
                self.stats['total_memories'] = len(self._memory_table)

                # 将重要的记忆加载到缓存中
                self._preload_important_memories()

        except Exception as e:
            logger.error(f"加载记忆数据失败: {e}")

        try:
            if association_file.exists():
                self._association_table = pq.read_table(association_file)
                logger.info(f"从磁盘加载了 {len(self._association_table)} 条关联")
                self.stats['disk_reads'] += 1
        except Exception as e:
            logger.error(f"加载关联数据失败: {e}")

    def _preload_important_memories(self):
        """预加载重要的记忆到缓存"""
        if self._memory_table is None or len(self._memory_table) == 0:
            return

        try:
            # 按重要性排序，取前50个
            indices = pc.sort_indices(self._memory_table, [("importance", "descending")])
            top_memories = self._memory_table.take(indices.slice(0, min(50, len(indices))))

            # 加载到L0缓存
            for i in range(len(top_memories)):
                memory = self._table_row_to_memory_fragment(top_memories, i)
                memory_table = self._memory_fragment_to_table(memory)
                self.cache.put(memory.memory_id, memory_table, target_levels=[CacheLevel.L0])

            logger.info(f"预加载了 {len(top_memories)} 条重要记忆到缓存")

        except Exception as e:
            logger.error(f"预加载重要记忆失败: {e}")

    def _memory_fragment_to_table(self, memory: MemoryFragment) -> pa.Table:
        """将MemoryFragment转换为Arrow表格"""
        data = {
            "memory_id": [memory.memory_id],
            "content": [memory.content],
            "content_type": [memory.content_type],
            "timestamp": [memory.timestamp],
            "importance": [memory.importance],
            "emotional_valence": [memory.emotional_valence],
            "context_json": [json.dumps(memory.context)],
            "embedding": [memory.embedding.tolist() if memory.embedding is not None else []],
            "quantum_real": [memory.quantum_real.tolist() if memory.quantum_real is not None else []],
            "quantum_imag": [memory.quantum_imag.tolist() if memory.quantum_imag is not None else []],
            "associations_json": [json.dumps(memory.associations)],
            "tags_json": [json.dumps(memory.tags)],
        }
        return pa.Table.from_pydict(data, schema=self.memory_schema)

    def _table_row_to_memory_fragment(self, table: pa.Table, row_idx: int) -> MemoryFragment:
        """将表格行转换为MemoryFragment对象"""
        row = table.take([row_idx])

        memory = MemoryFragment(
            memory_id=row["memory_id"][0].as_py(),
            content=row["content"][0].as_py(),
            content_type=row["content_type"][0].as_py(),
            timestamp=row["timestamp"][0].as_py(),
            importance=row["importance"][0].as_py(),
            emotional_valence=row["emotional_valence"][0].as_py(),
            context=json.loads(row["context_json"][0].as_py()),
            associations=json.loads(row["associations_json"][0].as_py()),
            tags=json.loads(row["tags_json"][0].as_py()),
        )

        # 处理向量数据
        embedding_data = row["embedding"][0].as_py()
        if embedding_data:
            memory.embedding = np.array(embedding_data)

        quantum_real_data = row["quantum_real"][0].as_py()
        quantum_imag_data = row["quantum_imag"][0].as_py()
        if quantum_real_data and quantum_imag_data:
            memory.quantum_real = np.array(quantum_real_data)
            memory.quantum_imag = np.array(quantum_imag_data)

        return memory

    def add_memory(self, memory: MemoryFragment) -> str:
        """
        添加记忆片段

        Args:
            memory: 记忆片段对象

        Returns:
            memory_id: 记忆片段的唯一标识
        """
        try:
            # 转换为Arrow表格
            memory_table = self._memory_fragment_to_table(memory)

            # 添加到内存表
            self._memory_table = pa.concat_tables([self._memory_table, memory_table])

            # 根据重要性决定缓存级别
            target_levels = self._determine_cache_levels(memory.importance)

            # 添加到缓存
            self.cache.put(memory.memory_id, memory_table, target_levels=target_levels)

            # 更新统计信息
            self.stats['total_memories'] += 1

            # 异步保存到磁盘（重要记忆立即保存）
            if memory.importance >= 0.8:
                self.save_to_disk()

            logger.debug(f"添加记忆: {memory.memory_id[:8]} - {memory.content[:50]}")
            return memory.memory_id

        except Exception as e:
            logger.error(f"添加记忆失败: {e}")
            return ""

    def _determine_cache_levels(self, importance: float) -> List[int]:
        """根据重要性确定缓存级别"""
        if importance >= 0.9:
            # 极重要：所有级别
            return [CacheLevel.L0, CacheLevel.L1, CacheLevel.L2]
        elif importance >= 0.7:
            # 重要：L0和L1
            return [CacheLevel.L0, CacheLevel.L1]
        elif importance >= 0.5:
            # 中等：L0
            return [CacheLevel.L0]
        else:
            # 低重要性：直接到L2
            return [CacheLevel.L2]



    def get_memory(self, memory_id: str) -> Optional[MemoryFragment]:
        """
        获取指定的记忆片段

        Args:
            memory_id: 记忆片段ID

        Returns:
            记忆片段对象，如果不存在则返回None
        """
        try:
            # 首先尝试从缓存获取
            cached_table = self.cache.get(memory_id)
            if cached_table is not None:
                self.stats['cache_hits'] += 1
                return self._table_row_to_memory_fragment(cached_table, 0)

            # 缓存未命中，从内存表查找
            self.stats['cache_misses'] += 1

            if self._memory_table is None or len(self._memory_table) == 0:
                return None

            # 过滤查询
            mask = pc.equal(self._memory_table["memory_id"], memory_id)
            filtered_table = self._memory_table.filter(mask)

            if len(filtered_table) == 0:
                return None

            # 转换为MemoryFragment对象
            memory = self._table_row_to_memory_fragment(filtered_table, 0)

            # 将找到的记忆加入缓存
            memory_table = self._memory_fragment_to_table(memory)
            target_levels = self._determine_cache_levels(memory.importance)
            self.cache.put(memory_id, memory_table, target_levels=target_levels)

            return memory

        except Exception as e:
            logger.error(f"获取记忆失败: {e}")
            return None

    def search_memories(self,
                       query: str = None,
                       content_type: str = None,
                       tags: List[str] = None,
                       importance_threshold: float = 0.0,
                       limit: int = 10) -> List[MemoryFragment]:
        """
        搜索记忆片段

        Args:
            query: 搜索查询（在内容中搜索）
            content_type: 内容类型过滤
            tags: 标签过滤
            importance_threshold: 重要性阈值
            limit: 返回结果数量限制

        Returns:
            匹配的记忆片段列表
        """
        try:
            if self._memory_table is None or len(self._memory_table) == 0:
                return []

            # 构建过滤条件
            filters = []

            # 重要性过滤
            if importance_threshold > 0:
                filters.append(pc.greater_equal(self._memory_table["importance"], importance_threshold))

            # 内容类型过滤
            if content_type:
                filters.append(pc.equal(self._memory_table["content_type"], content_type))

            # 文本搜索
            if query:
                # 简单的文本包含搜索
                filters.append(pc.match_substring(self._memory_table["content"], query))

            # 应用过滤器
            filtered_table = self._memory_table
            for filter_condition in filters:
                filtered_table = filtered_table.filter(filter_condition)

            # 按重要性排序
            if len(filtered_table) > 0:
                indices = pc.sort_indices(filtered_table, [("importance", "descending")])
                filtered_table = filtered_table.take(indices)

            # 限制结果数量
            if len(filtered_table) > limit:
                filtered_table = filtered_table.slice(0, limit)

            # 转换为MemoryFragment对象
            results = []
            for i in range(len(filtered_table)):
                memory = self._table_row_to_memory_fragment(filtered_table, i)
                results.append(memory)

                # 将搜索到的记忆加入缓存
                memory_table = self._memory_fragment_to_table(memory)
                target_levels = self._determine_cache_levels(memory.importance)
                self.cache.put(memory.memory_id, memory_table, target_levels=target_levels)

            return results

        except Exception as e:
            logger.error(f"搜索记忆失败: {e}")
            return []

    def save_to_disk(self):
        """将内存中的数据保存到磁盘"""
        try:
            if self._memory_table is not None and len(self._memory_table) > 0:
                memory_file = self.storage_path / "memories.parquet"

                # 使用ZSTD压缩保存
                pq.write_table(
                    self._memory_table,
                    memory_file,
                    compression='zstd',
                    compression_level=3,
                    use_dictionary=True,
                    write_statistics=True
                )

                logger.info(f"💾 保存了 {len(self._memory_table)} 条记忆到 {memory_file}")
                self.stats['disk_writes'] += 1

            if self._association_table is not None and len(self._association_table) > 0:
                association_file = self.storage_path / "associations.parquet"

                pq.write_table(
                    self._association_table,
                    association_file,
                    compression='zstd',
                    compression_level=3,
                    use_dictionary=True,
                    write_statistics=True
                )

                logger.info(f"💾 保存了 {len(self._association_table)} 条关联到 {association_file}")
                self.stats['disk_writes'] += 1

        except Exception as e:
            logger.error(f"💥 保存数据失败: {e}")

    def get_memory_count(self) -> int:
        """获取记忆总数"""
        return len(self._memory_table) if self._memory_table else 0

    def get_recent_memories(self, hours: int = 24, limit: int = 10) -> List[MemoryFragment]:
        """获取最近的记忆"""
        try:
            if self._memory_table is None or len(self._memory_table) == 0:
                return []

            # 计算时间阈值
            threshold = datetime.now() - timedelta(hours=hours)

            # 过滤最近的记忆
            mask = pc.greater_equal(self._memory_table["timestamp"], threshold)
            filtered_table = self._memory_table.filter(mask)

            # 按时间排序
            if len(filtered_table) > 0:
                indices = pc.sort_indices(filtered_table, [("timestamp", "descending")])
                filtered_table = filtered_table.take(indices)

            # 限制结果数量
            if len(filtered_table) > limit:
                filtered_table = filtered_table.slice(0, limit)

            # 转换为MemoryFragment对象
            results = []
            for i in range(len(filtered_table)):
                memory = self._table_row_to_memory_fragment(filtered_table, i)
                results.append(memory)

            return results

        except Exception as e:
            logger.error(f"获取最近记忆失败: {e}")
            return []

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        cache_stats = self.cache.get_stats()

        return {
            'engine_stats': self.stats,
            'cache_stats': cache_stats,
            'memory_count': self.get_memory_count(),
            'storage_path': str(self.storage_path)
        }

    def optimize_cache(self):
        """优化缓存性能"""
        try:
            # 获取最重要的记忆并确保它们在L0缓存中
            if self._memory_table is not None and len(self._memory_table) > 0:
                # 按重要性排序，取前20个
                indices = pc.sort_indices(self._memory_table, [("importance", "descending")])
                top_memories = self._memory_table.take(indices.slice(0, min(20, len(indices))))

                for i in range(len(top_memories)):
                    memory = self._table_row_to_memory_fragment(top_memories, i)
                    memory_table = self._memory_fragment_to_table(memory)
                    self.cache.put(memory.memory_id, memory_table, target_levels=[CacheLevel.L0])

                logger.info(f"🚀 优化缓存：将 {len(top_memories)} 条重要记忆提升到L0缓存")

        except Exception as e:
            logger.error(f"缓存优化失败: {e}")