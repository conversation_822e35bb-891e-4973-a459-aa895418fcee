"""
非线性干涉优化算法简单测试

这是一个简单的测试脚本，用于测试非线性干涉优化算法的基本功能。
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import sys

# 直接导入我们的实现
sys.path.append('.')
from src.algorithms.interference.optimizer import NonlinearInterferenceOptimizer
from src.algorithms.interference.patterns import InterferencePatternGenerator
from src.algorithms.interference.analysis import InterferenceResultAnalyzer


def create_test_states():
    """创建测试状态"""
    # 创建量子态
    quantum_state = np.zeros(10, dtype=np.complex128)
    quantum_state[0] = 0.7
    quantum_state[1] = 0.3j
    quantum_state[2] = 0.5
    quantum_state[3] = 0.2 + 0.3j
    quantum_state = quantum_state / np.linalg.norm(quantum_state)
    
    # 创建全息态
    holographic_state = np.zeros(10, dtype=np.complex128)
    holographic_state[1] = 0.6
    holographic_state[2] = 0.4j
    holographic_state[3] = 0.3
    holographic_state[4] = 0.5 + 0.2j
    holographic_state = holographic_state / np.linalg.norm(holographic_state)
    
    return quantum_state, holographic_state


def main():
    """主函数"""
    print("创建测试状态...")
    quantum_state, holographic_state = create_test_states()
    
    print("创建优化器...")
    optimizer = NonlinearInterferenceOptimizer(
        lambda_init=0.5,
        max_iterations=10,
        tolerance=1e-6,
        use_parallel=True,
        num_workers=2,
        adaptive_lambda=True
    )
    
    print("执行计算...")
    result = optimizer.compute((quantum_state, holographic_state))
    
    print("计算完成！")
    print(f"迭代次数: {result['iterations']}")
    print(f"最终lambda参数: {result['lambda_param']:.6f}")
    print(f"最终熵损失: {result['entropy_loss']:.6f}")
    
    print("创建结果分析器...")
    analyzer = InterferenceResultAnalyzer(verbose=True)
    
    print("分析结果...")
    analysis = analyzer.analyze(
        quantum_state, holographic_state, result['fused_state'], result['lambda_param']
    )
    
    print("测试成功完成！")


if __name__ == "__main__":
    main()
