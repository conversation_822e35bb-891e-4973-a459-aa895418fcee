"""
非线性干涉优化算法测试

本脚本包含非线性干涉优化算法的测试用例，独立于项目的其他部分。
"""

import unittest
import numpy as np
import os
import tempfile
import sys
import matplotlib.pyplot as plt

# 添加算法目录到Python路径
sys.path.append('.')

# 导入我们的实现
from src.interfaces.algorithm import AlgorithmInterface
from src.algorithms.interference.optimizer import NonlinearInterferenceOptimizer
from src.algorithms.interference.patterns import InterferencePatternGenerator
from src.algorithms.interference.analysis import InterferenceResultAnalyzer


class TestNonlinearInterferenceOptimizer(unittest.TestCase):
    """非线性干涉优化算法测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        # 创建测试数据
        self.quantum_state = self._create_test_quantum_state()
        self.holographic_state = self._create_test_holographic_state()
        
        # 创建临时目录用于保存中间结果
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建优化器
        self.optimizer = NonlinearInterferenceOptimizer(
            lambda_init=0.5,
            max_iterations=10,
            tolerance=1e-6,
            use_parallel=True,
            num_workers=2,
            adaptive_lambda=True,
            save_intermediate=True,
            intermediate_dir=self.temp_dir
        )
    
    def tearDown(self):
        """测试后的清理工作"""
        # 清理临时目录
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.optimizer.lambda_param, 0.5)
        self.assertEqual(self.optimizer.max_iterations, 10)
        self.assertEqual(self.optimizer.tolerance, 1e-6)
        self.assertTrue(self.optimizer.use_parallel)
        self.assertEqual(self.optimizer.num_workers, 2)
        self.assertTrue(self.optimizer.adaptive_lambda)
        self.assertTrue(self.optimizer.save_intermediate)
        self.assertEqual(self.optimizer.intermediate_dir, self.temp_dir)
    
    def test_compute(self):
        """测试计算方法"""
        # 执行计算
        result = self.optimizer.compute((self.quantum_state, self.holographic_state))
        
        # 检查结果
        self.assertIn('fused_state', result)
        self.assertIn('lambda_param', result)
        self.assertIn('iterations', result)
        self.assertIn('convergence_achieved', result)
        self.assertIn('entropy_loss', result)
        self.assertIn('performance', result)
        
        # 检查融合态
        fused_state = result['fused_state']
        self.assertEqual(fused_state.shape, self.quantum_state.shape)
        
        # 检查归一化
        self.assertAlmostEqual(np.linalg.norm(fused_state), 1.0, places=6)
    
    def test_get_metadata(self):
        """测试获取元数据"""
        metadata = self.optimizer.get_metadata()
        
        self.assertIn('name', metadata)
        self.assertIn('version', metadata)
        self.assertIn('description', metadata)
        self.assertIn('parameters', metadata)
        self.assertIn('performance_metrics', metadata)
    
    def test_is_compatible_with(self):
        """测试兼容性检查"""
        # 创建兼容的优化器
        compatible_optimizer = NonlinearInterferenceOptimizer(
            lambda_init=0.6,
            max_iterations=20
        )
        
        # 创建不兼容的优化器
        incompatible_optimizer = NonlinearInterferenceOptimizer(
            lambda_init=0.1,
            max_iterations=5
        )
        
        # 检查兼容性
        self.assertTrue(self.optimizer.is_compatible_with(compatible_optimizer))
        self.assertFalse(self.optimizer.is_compatible_with(incompatible_optimizer))
    
    def test_get_performance_metrics(self):
        """测试获取性能指标"""
        # 执行计算
        self.optimizer.compute((self.quantum_state, self.holographic_state))
        
        # 获取性能指标
        metrics = self.optimizer.get_performance_metrics()
        
        self.assertIn('total_time', metrics)
        self.assertIn('iterations', metrics)
        self.assertIn('convergence_achieved', metrics)
        self.assertIn('final_entropy_loss', metrics)
        self.assertIn('lambda_history', metrics)
        self.assertIn('entropy_history', metrics)
    
    def test_intermediate_results(self):
        """测试中间结果保存"""
        # 执行计算
        self.optimizer.compute((self.quantum_state, self.holographic_state))
        
        # 检查中间结果文件
        files = os.listdir(self.temp_dir)
        self.assertTrue(any(f.startswith('state_') for f in files))
        self.assertTrue(any(f.startswith('metadata_') for f in files))
    
    def test_pattern_generator(self):
        """测试干涉模式生成器"""
        # 创建干涉模式生成器
        generator = InterferencePatternGenerator(pattern_type='standard')
        
        # 生成干涉模式
        pattern = generator.generate(self.quantum_state, self.holographic_state, 0.5)
        
        # 检查干涉模式
        self.assertEqual(pattern.shape, self.quantum_state.shape)
        self.assertAlmostEqual(np.linalg.norm(pattern), 1.0, places=6)
    
    def test_result_analyzer(self):
        """测试干涉结果分析器"""
        # 创建干涉结果分析器
        analyzer = InterferenceResultAnalyzer()
        
        # 执行计算
        result = self.optimizer.compute((self.quantum_state, self.holographic_state))
        fused_state = result['fused_state']
        
        # 分析结果
        analysis = analyzer.analyze(
            self.quantum_state, self.holographic_state, fused_state, 0.5
        )
        
        # 检查分析结果
        self.assertIn('fidelity_q', analysis)
        self.assertIn('fidelity_h', analysis)
        self.assertIn('entropy', analysis)
        self.assertIn('entropy_loss', analysis)
        self.assertIn('information_gain', analysis)
        self.assertIn('coherence', analysis)
        self.assertIn('interference_strength', analysis)
    
    def _create_test_quantum_state(self):
        """创建测试量子态"""
        # 创建一个简单的量子态
        state = np.zeros(10, dtype=np.complex128)
        state[0] = 0.7
        state[1] = 0.3j
        state[2] = 0.5
        state[3] = 0.2 + 0.3j
        
        # 归一化
        norm = np.linalg.norm(state)
        state = state / norm
        
        return state
    
    def _create_test_holographic_state(self):
        """创建测试全息态"""
        # 创建一个简单的全息态
        state = np.zeros(10, dtype=np.complex128)
        state[1] = 0.6
        state[2] = 0.4j
        state[3] = 0.3
        state[4] = 0.5 + 0.2j
        
        # 归一化
        norm = np.linalg.norm(state)
        state = state / norm
        
        return state


if __name__ == '__main__':
    unittest.main()
