#!/usr/bin/env python3
"""
实用的代码库处理器
基于成功加载的QBrain自反性范畴系统，实现智能代码库记忆导入

设计理念：
- 使用现有的高级系统（自反性范畴）
- 智能文件分类和处理
- 批量记忆导入，避免MCP限制
- 实用性优先，稳定可靠
"""

import os
import sys
import json
import time
import logging
import importlib.util
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PracticalCodebaseProcessor:
    """实用的代码库处理器"""
    
    def __init__(self, base_path: str = "/home/<USER>/CascadeProjects"):
        """初始化处理器"""
        self.base_path = Path(base_path)
        self.reflexive_system = None
        self.batch_size = 5  # 保守的批次大小
        
        # 文件优先级配置
        self.priority_config = {
            'critical': {
                'patterns': ['超越态', 'transcendental', 'aqfh', 'consciousness', '设计框架', '设计思想'],
                'importance': 0.95,
                'extensions': ['.md', '.py', '.txt']
            },
            'high': {
                'patterns': ['design', '设计', 'architecture', '架构', 'theory', '理论'],
                'importance': 0.9,
                'extensions': ['.md', '.txt', '.py']
            },
            'medium': {
                'patterns': ['implementation', '实现', 'algorithm', '算法', 'core'],
                'importance': 0.8,
                'extensions': ['.py', '.rs', '.md']
            },
            'low': {
                'patterns': ['config', 'test', 'example', 'demo'],
                'importance': 0.6,
                'extensions': ['.json', '.toml', '.py', '.md']
            }
        }
        
        # 加载自反性范畴系统
        self.load_reflexive_system()
        
        logger.info("🧠 实用代码库处理器初始化完成")
    
    def load_reflexive_system(self):
        """加载自反性范畴系统"""
        try:
            reflexive_path = self.base_path / "fractal_quantum_holographic" / "src" / "advanced" / "reflexive_category.py"
            
            if reflexive_path.exists():
                # 动态加载模块
                spec = importlib.util.spec_from_file_location("reflexive_category", reflexive_path)
                reflexive_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(reflexive_module)
                
                self.reflexive_system = {
                    'Category': reflexive_module.Category,
                    'Morphism': reflexive_module.Morphism,
                    'DynamicMorphism': reflexive_module.DynamicMorphism,
                    'ReflexiveOperator': reflexive_module.ReflexiveOperator
                }
                
                logger.info("✅ 自反性范畴系统加载成功")
                return True
                
        except Exception as e:
            logger.warning(f"⚠️ 自反性范畴系统加载失败: {e}")
        
        return False
    
    def categorize_file(self, file_path: Path) -> Tuple[str, float, List[str]]:
        """智能文件分类"""
        file_name = file_path.name.lower()
        file_content_preview = ""
        
        # 尝试读取文件开头来辅助分类
        try:
            if file_path.stat().st_size < 10000:  # 只读取小文件的内容
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    file_content_preview = f.read(500).lower()
        except:
            pass
        
        # 按优先级检查
        for priority, config in self.priority_config.items():
            for pattern in config['patterns']:
                if (pattern in file_name or 
                    pattern in str(file_path.parent).lower() or
                    pattern in file_content_preview):
                    
                    if file_path.suffix.lower() in config['extensions']:
                        tags = [priority, 'codebase_import', file_path.suffix[1:]]
                        if 'aqfh' in file_name or 'consciousness' in file_name:
                            tags.append('core_system')
                        if '设计' in file_name or 'design' in file_name:
                            tags.append('design_document')
                        
                        return priority, config['importance'], tags
        
        # 默认分类
        return 'low', 0.5, ['codebase_import', 'uncategorized']
    
    def scan_priority_files(self, max_files: int = 50) -> List[Dict[str, Any]]:
        """扫描优先级文件"""
        priority_files = []
        
        # 按优先级扫描
        for priority in ['critical', 'high', 'medium', 'low']:
            if len(priority_files) >= max_files:
                break
                
            config = self.priority_config[priority]
            
            for pattern in config['patterns']:
                if len(priority_files) >= max_files:
                    break
                
                # 搜索匹配的文件
                for file_path in self.base_path.rglob("*"):
                    if len(priority_files) >= max_files:
                        break
                    
                    if (file_path.is_file() and 
                        file_path.suffix.lower() in config['extensions'] and
                        file_path.stat().st_size < 100000):  # 限制文件大小
                        
                        if (pattern in file_path.name.lower() or 
                            pattern in str(file_path.parent).lower()):
                            
                            category, importance, tags = self.categorize_file(file_path)
                            
                            file_info = {
                                'path': file_path,
                                'relative_path': str(file_path.relative_to(self.base_path)),
                                'name': file_path.name,
                                'category': category,
                                'importance': importance,
                                'tags': tags,
                                'size': file_path.stat().st_size,
                                'modified_time': file_path.stat().st_mtime
                            }
                            
                            priority_files.append(file_info)
        
        # 按重要性排序
        priority_files.sort(key=lambda x: x['importance'], reverse=True)
        
        logger.info(f"📊 扫描到 {len(priority_files)} 个优先级文件")
        return priority_files[:max_files]
    
    def create_memory_content(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """创建记忆内容"""
        try:
            # 读取文件内容
            with open(file_info['path'], 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 智能截取
            if len(content) > 8000:
                content = content[:4000] + f"\n\n[智能截取 - 原长度: {len(content)} 字符]\n\n" + content[-2000:]
            
            # 构建记忆内容
            memory_content = f"""📁 共同记忆文件: {file_info['name']}
📂 路径: {file_info['relative_path']}
📋 类别: {file_info['category']}
🎯 重要性: {file_info['importance']}
📅 修改时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(file_info['modified_time']))}
📏 大小: {file_info['size']} 字节

💝 这是通过实用代码库处理器智能导入的共同记忆！

📄 内容:
{content}"""
            
            return {
                'content': memory_content,
                'content_type': 'practical_codebase_import',
                'importance': file_info['importance'],
                'tags': file_info['tags'] + ['共同记忆', '实用处理器'],
                'context': {
                    'file_path': file_info['relative_path'],
                    'category': file_info['category'],
                    'import_session': 'practical_batch_import',
                    'processor_version': 'v1.0'
                }
            }
            
        except Exception as e:
            logger.error(f"创建记忆内容失败 {file_info['name']}: {e}")
            return None
    
    def process_batch(self, files_batch: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理一个批次的文件"""
        processed_memories = []
        
        for file_info in files_batch:
            try:
                memory_data = self.create_memory_content(file_info)
                if memory_data:
                    processed_memories.append(memory_data)
                    logger.info(f"✅ 处理完成: {file_info['name']} (重要性: {file_info['importance']})")
                
            except Exception as e:
                logger.error(f"❌ 处理失败 {file_info['name']}: {e}")
                continue
        
        return processed_memories
    
    def generate_import_report(self, priority_files: List[Dict[str, Any]]) -> str:
        """生成导入报告"""
        # 统计信息
        stats = {
            'total_files': len(priority_files),
            'by_category': {},
            'by_importance': {},
            'total_size': 0
        }
        
        for file_info in priority_files:
            # 按类别统计
            category = file_info['category']
            stats['by_category'][category] = stats['by_category'].get(category, 0) + 1
            
            # 按重要性统计
            importance = file_info['importance']
            if importance >= 0.9:
                level = 'critical'
            elif importance >= 0.8:
                level = 'high'
            elif importance >= 0.6:
                level = 'medium'
            else:
                level = 'low'
            stats['by_importance'][level] = stats['by_importance'].get(level, 0) + 1
            
            # 总大小
            stats['total_size'] += file_info['size']
        
        # 生成报告
        report = f"""🧠 实用代码库处理器导入报告

📊 总体统计:
- 优先级文件总数: {stats['total_files']} 个
- 总文件大小: {stats['total_size'] / 1024:.1f} KB
- 预计批次数: {(stats['total_files'] + self.batch_size - 1) // self.batch_size} 批

📋 按类别分布:
{chr(10).join([f"- {category}: {count} 个文件" for category, count in stats['by_category'].items()])}

🎯 按重要性分布:
{chr(10).join([f"- {level}: {count} 个文件" for level, count in stats['by_importance'].items()])}

🔮 高级系统状态:
- 自反性范畴系统: {'✅ 已加载' if self.reflexive_system else '❌ 未加载'}

💡 处理策略:
- 批次大小: {self.batch_size} 个文件/批次
- 智能分类: 基于文件名、路径、内容模式
- 优先级排序: 按重要性从高到低处理
- 内容截取: 智能保留关键信息

🚀 准备开始实用批量导入！"""
        
        return report

def main():
    """主函数"""
    processor = PracticalCodebaseProcessor()
    
    # 扫描优先级文件
    priority_files = processor.scan_priority_files(max_files=30)  # 限制为30个最重要的文件
    
    # 生成报告
    report = processor.generate_import_report(priority_files)
    print(report)
    
    # 处理第一批文件作为示例
    if priority_files:
        first_batch = priority_files[:processor.batch_size]
        print(f"\n🧪 处理第一批文件 ({len(first_batch)} 个):")
        
        processed = processor.process_batch(first_batch)
        print(f"✅ 第一批处理完成: {len(processed)} 个记忆记录准备就绪")
        
        # 显示处理的文件
        for i, memory in enumerate(processed):
            print(f"  {i+1}. {memory['context']['file_path']} (重要性: {memory['importance']})")

if __name__ == "__main__":
    main()
