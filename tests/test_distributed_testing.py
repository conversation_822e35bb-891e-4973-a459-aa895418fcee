#!/usr/bin/env python3
"""
分布式测试环境测试脚本

这个脚本测试分布式网络的测试环境，验证其基本功能是否正常工作。
"""

import os
import sys
import time
import json
import uuid
import logging
import unittest
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.distributed.testing.local import LocalTestEnvironment, LocalCluster, LocalNode
from src.distributed.testing.network import NetworkTestEnvironment, NetworkCluster, NetworkNode
from src.distributed.testing.chaos import ChaosTestEnvironment, ChaosMonkey, FailureInjector

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

class TestDistributedTesting(unittest.TestCase):
    """测试分布式测试环境"""
    
    def test_local_environment(self):
        """测试本地测试环境"""
        # 创建本地测试环境
        env = LocalTestEnvironment(name="test_local_env")
        
        # 创建集群
        cluster = env.create_cluster(name="test_cluster")
        
        # 创建节点
        node1 = cluster.create_node(name="node1")
        node2 = cluster.create_node(name="node2")
        node3 = cluster.create_node(name="node3")
        
        # 连接节点
        cluster.connect_nodes(node1.node_id, node2.node_id)
        cluster.connect_nodes(node2.node_id, node3.node_id)
        cluster.connect_nodes(node3.node_id, node1.node_id)
        
        # 启动环境
        env.start()
        
        # 验证环境状态
        self.assertEqual(env.status, "running")
        
        # 获取环境指标
        metrics = env.get_metrics()
        
        # 验证指标
        self.assertEqual(metrics["cluster_count"], 1)
        self.assertEqual(metrics["node_count"], 3)
        self.assertEqual(metrics["active_clusters"], 1)
        self.assertEqual(metrics["active_nodes"], 3)
        
        # 停止环境
        env.stop()
        
        # 验证环境状态
        self.assertEqual(env.status, "stopped")
    
    def test_network_environment(self):
        """测试网络测试环境"""
        # 创建网络测试环境
        env = NetworkTestEnvironment(name="test_network_env")
        
        # 创建集群
        cluster = env.create_cluster(name="test_cluster")
        
        # 创建节点（使用localhost模拟网络节点）
        node1 = cluster.create_node(name="node1", host="localhost", port=8001, api_port=8101)
        node2 = cluster.create_node(name="node2", host="localhost", port=8002, api_port=8102)
        
        # 验证节点创建
        self.assertEqual(len(cluster.nodes), 2)
        
        # 验证环境指标
        metrics = env.get_metrics()
        self.assertEqual(metrics["cluster_count"], 1)
        self.assertEqual(metrics["node_count"], 2)
    
    def test_chaos_environment(self):
        """测试混沌测试环境"""
        # 创建本地测试环境作为基础环境
        base_env = LocalTestEnvironment(name="base_env")
        
        # 创建集群
        cluster = base_env.create_cluster(name="test_cluster")
        
        # 创建节点
        node1 = cluster.create_node(name="node1")
        node2 = cluster.create_node(name="node2")
        node3 = cluster.create_node(name="node3")
        
        # 创建网格网络
        cluster.create_mesh_network()
        
        # 启动基础环境
        base_env.start()
        
        # 创建混沌测试环境
        chaos_env = ChaosTestEnvironment(name="test_chaos_env", base_environment=base_env)
        
        # 创建混沌猴子
        monkey = chaos_env.create_monkey(name="test_monkey")
        
        # 启动混沌测试环境
        chaos_env.start()
        
        # 验证环境状态
        self.assertEqual(chaos_env.status, "running")
        
        # 注入故障
        success = chaos_env.inject_failure(
            failure_type="node",
            target=node1,
            failure_type="restart"
        )
        
        # 验证故障注入
        self.assertTrue(success)
        
        # 获取环境指标
        metrics = chaos_env.get_metrics()
        
        # 验证指标
        self.assertEqual(metrics["monkey_count"], 1)
        self.assertEqual(metrics["failures_injected"], 1)
        self.assertEqual(metrics["node_failures"], 1)
        
        # 停止混沌测试环境
        chaos_env.stop()
        
        # 验证环境状态
        self.assertEqual(chaos_env.status, "stopped")
        
        # 停止基础环境
        base_env.stop()
    
    def test_failure_injector(self):
        """测试故障注入器"""
        # 创建本地测试环境
        env = LocalTestEnvironment(name="test_env")
        
        # 创建集群
        cluster = env.create_cluster(name="test_cluster")
        
        # 创建节点
        node1 = cluster.create_node(name="node1")
        node2 = cluster.create_node(name="node2")
        
        # 连接节点
        cluster.connect_nodes(node1.node_id, node2.node_id)
        
        # 启动环境
        env.start()
        
        # 创建故障注入器
        injector = FailureInjector(name="test_injector")
        
        # 注入节点故障
        success = injector.inject_node_failure(
            node=node1,
            failure_type="crash",
            duration=2.0,
            recovery=True
        )
        
        # 验证故障注入
        self.assertTrue(success)
        self.assertEqual(node1.status, "stopped")
        
        # 等待恢复
        time.sleep(3.0)
        
        # 验证恢复
        self.assertEqual(node1.status, "running")
        
        # 注入网络故障
        success = injector.inject_network_failure(
            source=node1,
            target=node2,
            failure_type="disconnect",
            duration=2.0,
            recovery=True
        )
        
        # 验证故障注入
        self.assertTrue(success)
        self.assertNotIn((node1.node_id, node2.node_id), cluster.connections)
        self.assertNotIn((node2.node_id, node1.node_id), cluster.connections)
        
        # 等待恢复
        time.sleep(3.0)
        
        # 验证恢复
        self.assertTrue(
            (node1.node_id, node2.node_id) in cluster.connections or
            (node2.node_id, node1.node_id) in cluster.connections
        )
        
        # 获取注入器指标
        metrics = injector.get_metrics()
        
        # 验证指标
        self.assertEqual(metrics["failures_injected"], 2)
        self.assertEqual(metrics["node_failures"], 1)
        self.assertEqual(metrics["network_failures"], 1)
        
        # 停止环境
        env.stop()
    
    def test_chaos_monkey(self):
        """测试混沌猴子"""
        # 创建本地测试环境
        env = LocalTestEnvironment(name="test_env")
        
        # 创建集群
        cluster = env.create_cluster(name="test_cluster")
        
        # 创建节点
        for i in range(5):
            cluster.create_node(name=f"node{i+1}")
        
        # 创建网格网络
        cluster.create_mesh_network()
        
        # 启动环境
        env.start()
        
        # 创建混沌猴子
        monkey = ChaosMonkey(
            name="test_monkey",
            environment=env,
            config={
                "failure_probabilities": {
                    "node": 1.0,  # 100%节点故障
                    "network": 0.0,
                    "resource": 0.0,
                    "data": 0.0,
                    "timing": 0.0
                },
                "failure_duration_range": {
                    "min": 1.0,
                    "max": 2.0
                },
                "failure_interval_range": {
                    "min": 1.0,
                    "max": 2.0
                }
            }
        )
        
        # 启动混沌猴子，运行5秒
        monkey.start(duration=5.0)
        
        # 等待混沌猴子完成
        time.sleep(7.0)
        
        # 验证混沌猴子状态
        self.assertEqual(monkey.status, "stopped")
        
        # 获取混沌猴子指标
        metrics = monkey.get_metrics()
        
        # 验证指标
        self.assertGreater(metrics["runs"], 0)
        self.assertGreater(metrics["failures_injected"], 0)
        self.assertGreater(metrics["node_failures"], 0)
        
        # 停止环境
        env.stop()
    
    def test_run_chaos_test(self):
        """测试运行混沌测试"""
        # 创建本地测试环境
        base_env = LocalTestEnvironment(name="base_env")
        
        # 创建集群
        cluster = base_env.create_cluster(name="test_cluster")
        
        # 创建节点
        for i in range(3):
            cluster.create_node(name=f"node{i+1}")
        
        # 创建环形网络
        cluster.create_ring_network()
        
        # 创建混沌测试环境
        chaos_env = ChaosTestEnvironment(name="test_chaos_env", base_environment=base_env)
        
        # 定义测试函数
        def test_func(env):
            # 启动环境
            env.start()
            
            # 创建混沌猴子
            monkey = env.create_monkey(name="test_monkey")
            
            # 启动混沌猴子，运行3秒
            env.start_monkey(monkey_id=list(env.monkeys.keys())[0], duration=3.0)
            
            # 等待混沌猴子完成
            time.sleep(5.0)
            
            # 获取环境指标
            metrics = env.get_metrics()
            
            # 返回测试结果
            return {
                "metrics": metrics,
                "success": metrics["failures_injected"] > 0
            }
        
        # 运行混沌测试
        results = chaos_env.run_chaos_test(
            test_name="test_chaos",
            test_func=test_func,
            save_results=True
        )
        
        # 验证测试结果
        self.assertEqual(results["status"], "success")
        self.assertTrue(results["success"])
        self.assertGreater(results["failures"]["total"], 0)
        
        # 停止混沌测试环境
        chaos_env.stop()
        
        # 停止基础环境
        base_env.stop()

if __name__ == '__main__':
    unittest.main()
