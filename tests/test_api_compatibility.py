"""
API版本适配算子测试

本测试脚本测试API版本适配算子的基本功能。
"""

import sys
import os
import unittest
from unittest.mock import MagicMock, patch
import json
import tempfile

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入API版本适配算子
from src.transcendental_tensor.api_compatibility import (
    Version, VersionRange, APIVersion, APIMethod, APIInterface,
    VersionMapping, AdapterConfig, AdaptationResult,
    VersionFormat, CompatibilityLevel, AdaptationStrategy,
    VersionManager, APIVersionAdapterOperator
)


class TestAPICompatibility(unittest.TestCase):
    """API版本适配算子测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建版本管理器
        self.version_manager = VersionManager(name="test_manager", current_version="1.0.0")
        
        # 添加版本
        self.version_manager.add_version(APIVersion(
            name="API v1.0",
            version=Version("1.0.0"),
            description="初始版本"
        ))
        
        self.version_manager.add_version(APIVersion(
            name="API v2.0",
            version=Version("2.0.0"),
            description="更新版本"
        ))
        
        # 设置兼容性
        self.version_manager.set_compatibility("1.0.0", "2.0.0", bidirectional=True)
        
        # 创建版本映射
        self.mapping = VersionMapping(
            source_version=Version("1.0.0"),
            target_version=Version("2.0.0"),
            method_mappings={
                "get_data": "getData",
                "set_config": "setConfig"
            },
            parameter_mappings={
                "get_data": {
                    "id": "identifier",
                    "type": "dataType"
                },
                "set_config": {
                    "config": "configuration",
                    "options": "settings"
                }
            },
            bidirectional=True
        )
        
        self.version_manager.add_mapping(self.mapping)
        
        # 创建API版本适配算子
        self.adapter_operator = APIVersionAdapterOperator(
            name="test_adapter",
            version_manager=self.version_manager
        )
        
        # 创建测试数据
        self.test_data_v1 = {
            "get_data": {
                "id": 123,
                "type": "user"
            },
            "set_config": {
                "config": {
                    "theme": "dark",
                    "language": "en"
                },
                "options": {
                    "cache": True,
                    "timeout": 30
                }
            }
        }
        
        self.test_data_v2 = {
            "getData": {
                "identifier": 123,
                "dataType": "user"
            },
            "setConfig": {
                "configuration": {
                    "theme": "dark",
                    "language": "en"
                },
                "settings": {
                    "cache": True,
                    "timeout": 30
                }
            }
        }
    
    def test_version(self):
        """测试版本类"""
        # 创建版本
        version = Version("1.2.3")
        
        # 验证版本属性
        self.assertEqual(version.value, "1.2.3")
        self.assertEqual(version.format, VersionFormat.SEMANTIC)
        self.assertEqual(version.components, [1, 2, 3])
        
        # 测试兼容性
        version1 = Version("1.0.0")
        version2 = Version("1.1.0")
        version3 = Version("2.0.0")
        
        # 精确匹配
        self.assertTrue(version1.is_compatible_with(version1, CompatibilityLevel.EXACT))
        self.assertFalse(version1.is_compatible_with(version2, CompatibilityLevel.EXACT))
        
        # 主版本匹配
        self.assertTrue(version1.is_compatible_with(version2, CompatibilityLevel.MAJOR))
        self.assertFalse(version1.is_compatible_with(version3, CompatibilityLevel.MAJOR))
        
        # 次版本匹配
        self.assertTrue(version1.is_compatible_with(version1, CompatibilityLevel.MINOR))
        self.assertFalse(version1.is_compatible_with(version2, CompatibilityLevel.MINOR))
    
    def test_version_manager(self):
        """测试版本管理器"""
        # 验证版本
        self.assertEqual(len(self.version_manager.versions), 2)
        self.assertIn("1.0.0", self.version_manager.versions)
        self.assertIn("2.0.0", self.version_manager.versions)
        
        # 验证兼容性
        self.assertTrue(self.version_manager.is_compatible("1.0.0", "2.0.0"))
        self.assertTrue(self.version_manager.is_compatible("2.0.0", "1.0.0"))
        
        # 验证兼容版本
        compatible_versions = self.version_manager.get_compatible_versions("1.0.0")
        self.assertEqual(len(compatible_versions), 2)
        self.assertIn("1.0.0", compatible_versions)
        self.assertIn("2.0.0", compatible_versions)
        
        # 验证迁移路径
        path = self.version_manager.find_migration_path("1.0.0", "2.0.0")
        self.assertEqual(path, ["1.0.0", "2.0.0"])
    
    def test_version_manager_save_load(self):
        """测试版本管理器保存和加载"""
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix=".json", delete=False) as temp_file:
            temp_path = temp_file.name
        
        try:
            # 保存配置
            self.version_manager.save_config(temp_path)
            
            # 加载配置
            new_manager = VersionManager()
            new_manager.load_config(temp_path)
            
            # 验证加载的配置
            self.assertEqual(len(new_manager.versions), 2)
            self.assertIn("1.0.0", new_manager.versions)
            self.assertIn("2.0.0", new_manager.versions)
            self.assertTrue(new_manager.is_compatible("1.0.0", "2.0.0"))
        finally:
            # 清理
            if os.path.exists(temp_path):
                os.remove(temp_path)
    
    def test_direct_adapter(self):
        """测试直接适配器"""
        # 适配v1到v2
        result_v2 = self.adapter_operator.apply(
            self.test_data_v1,
            source_version="1.0.0",
            target_version="2.0.0",
            strategy=AdaptationStrategy.DIRECT
        )
        
        # 验证结果
        self.assertEqual(result_v2["getData"]["identifier"], 123)
        self.assertEqual(result_v2["getData"]["dataType"], "user")
        self.assertEqual(result_v2["setConfig"]["configuration"]["theme"], "dark")
        self.assertEqual(result_v2["setConfig"]["settings"]["cache"], True)
        
        # 适配v2到v1
        result_v1 = self.adapter_operator.apply(
            self.test_data_v2,
            source_version="2.0.0",
            target_version="1.0.0",
            strategy=AdaptationStrategy.DIRECT
        )
        
        # 验证结果
        self.assertEqual(result_v1["get_data"]["id"], 123)
        self.assertEqual(result_v1["get_data"]["type"], "user")
        self.assertEqual(result_v1["set_config"]["config"]["theme"], "dark")
        self.assertEqual(result_v1["set_config"]["options"]["cache"], True)
    
    def test_wrapper_adapter(self):
        """测试包装适配器"""
        # 创建测试类
        class TestClass:
            def __init__(self):
                self.value = 42
            
            def get_data(self, id, type):
                return {"id": id, "type": type, "value": self.value}
            
            def set_config(self, config, options):
                self.value = config.get("value", self.value)
                return {"success": True, "options": options}
        
        test_obj = TestClass()
        
        # 适配对象
        wrapped_obj = self.adapter_operator.apply(
            test_obj,
            source_version="1.0.0",
            target_version="2.0.0",
            strategy=AdaptationStrategy.WRAPPER
        )
        
        # 验证包装对象
        self.assertEqual(wrapped_obj.value, 42)
        
        # 调用方法
        result = wrapped_obj.getData(identifier=123, dataType="user")
        self.assertEqual(result["id"], 123)
        self.assertEqual(result["type"], "user")
        self.assertEqual(result["value"], 42)
        
        # 调用另一个方法
        result = wrapped_obj.setConfig(
            configuration={"value": 99},
            settings={"timeout": 60}
        )
        self.assertEqual(result["success"], True)
        self.assertEqual(result["options"], {"timeout": 60})
        self.assertEqual(wrapped_obj.value, 99)
    
    def test_adapter_decorator(self):
        """测试适配器装饰器"""
        # 创建装饰器
        decorator = self.adapter_operator.get_adapter_decorator(
            source_version="1.0.0",
            target_version="2.0.0"
        )
        
        # 创建测试函数
        @decorator
        def get_data():
            return self.test_data_v1
        
        # 调用函数
        result = get_data()
        
        # 验证结果
        self.assertEqual(result["getData"]["identifier"], 123)
        self.assertEqual(result["getData"]["dataType"], "user")
        self.assertEqual(result["setConfig"]["configuration"]["theme"], "dark")
        self.assertEqual(result["setConfig"]["settings"]["cache"], True)
    
    def test_parameter_adapter_decorator(self):
        """测试参数适配器装饰器"""
        # 创建装饰器
        decorator = self.adapter_operator.get_parameter_adapter_decorator(
            source_version="2.0.0",
            target_version="1.0.0",
            parameter_mappings={
                "identifier": "id",
                "dataType": "type"
            }
        )
        
        # 创建测试函数
        @decorator
        def get_data(id, type):
            return {"id": id, "type": type}
        
        # 调用函数
        result = get_data(identifier=123, dataType="user")
        
        # 验证结果
        self.assertEqual(result["id"], 123)
        self.assertEqual(result["type"], "user")
    
    def test_return_adapter_decorator(self):
        """测试返回值适配器装饰器"""
        # 创建装饰器
        decorator = self.adapter_operator.get_return_adapter_decorator(
            source_version="1.0.0",
            target_version="2.0.0"
        )
        
        # 创建测试函数
        @decorator
        def get_data():
            return self.test_data_v1
        
        # 调用函数
        result = get_data()
        
        # 验证结果
        self.assertEqual(result["getData"]["identifier"], 123)
        self.assertEqual(result["getData"]["dataType"], "user")
        self.assertEqual(result["setConfig"]["configuration"]["theme"], "dark")
        self.assertEqual(result["setConfig"]["settings"]["cache"], True)
    
    def test_operator_interface(self):
        """测试算子接口"""
        # 测试元数据
        metadata = self.adapter_operator.get_metadata()
        self.assertEqual(metadata["name"], "test_adapter")
        self.assertEqual(metadata["type"], "api_version_adapter")
        
        # 测试兼容性
        self.assertTrue(self.adapter_operator.is_compatible_with(self.adapter_operator))
        
        # 测试性能指标
        metrics = self.adapter_operator.get_performance_metrics()
        self.assertIn("version_count", metrics)
        self.assertEqual(metrics["version_count"], 2)
        
        # 测试参数
        params = self.adapter_operator.get_parameters()
        self.assertEqual(params["name"], "test_adapter")
        self.assertEqual(params["default_strategy"], "direct")
        
        # 设置参数
        self.adapter_operator.set_parameters({
            "name": "new_name",
            "default_strategy": "bridge"
        })
        self.assertEqual(self.adapter_operator.name, "new_name")
        self.assertEqual(self.adapter_operator.adapter.default_strategy, AdaptationStrategy.BRIDGE)
        
        # 测试复杂度
        complexity = self.adapter_operator.get_complexity()
        self.assertEqual(complexity["time"], "O(n)")
        self.assertEqual(complexity["space"], "O(n)")


if __name__ == "__main__":
    unittest.main()
