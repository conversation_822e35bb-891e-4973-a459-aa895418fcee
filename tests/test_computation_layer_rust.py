#!/usr/bin/env python3
"""
计算层Rust实现测试脚本

这个脚本测试计算层的Rust实现，验证其基本功能是否正常工作。
"""

import os
import sys
import time
import json
import uuid
import logging
import unittest
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.distributed.layers.computation import ComputationLayer, TaskPriority, TaskStatus

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

class TestComputationLayerRust(unittest.TestCase):
    """测试计算层Rust实现"""
    
    def setUp(self):
        """测试前准备"""
        self.node_id = str(uuid.uuid4())
        self.computation_layer = ComputationLayer(
            node_id=self.node_id,
            max_workers=5,
            use_rust=True
        )
        
        # 检查是否使用了Rust实现
        self.assertTrue(hasattr(self.computation_layer, '_rust_layer'), 
                        "Rust implementation not available")
        
        # 启动计算层
        self.computation_layer.start()
    
    def tearDown(self):
        """测试后清理"""
        # 停止计算层
        self.computation_layer.stop()
    
    def test_register_function(self):
        """测试注册函数"""
        # 定义一个简单的函数
        def add(a, b):
            return a + b
        
        # 注册函数
        function_name = self.computation_layer.register_function(add)
        
        # 验证函数名
        self.assertEqual(function_name, "add")
    
    def test_submit_task(self):
        """测试提交任务"""
        # 定义一个简单的函数
        def multiply(a, b):
            return a * b
        
        # 注册函数
        function_name = self.computation_layer.register_function(multiply)
        
        # 提交任务
        task_id = self.computation_layer.submit_task(
            function_name=function_name,
            args=[3, 4],
            priority=TaskPriority.HIGH
        )
        
        # 验证任务ID
        self.assertIsNotNone(task_id)
        self.assertTrue(isinstance(task_id, str))
        
        # 获取任务信息
        task = self.computation_layer.get_task(task_id)
        
        # 验证任务信息
        self.assertIsNotNone(task)
        self.assertEqual(task.function_name, function_name)
        self.assertEqual(task.args, [3, 4])
        self.assertEqual(task.priority, TaskPriority.HIGH)
    
    def test_get_task_result(self):
        """测试获取任务结果"""
        # 定义一个简单的函数
        def subtract(a, b):
            return a - b
        
        # 注册函数
        function_name = self.computation_layer.register_function(subtract)
        
        # 提交任务
        task_id = self.computation_layer.submit_task(
            function_name=function_name,
            args=[10, 3],
            priority=TaskPriority.NORMAL
        )
        
        # 等待任务完成并获取结果
        result = self.computation_layer.get_task_result(task_id, timeout=5.0)
        
        # 验证结果
        self.assertEqual(result, 7)
    
    def test_cancel_task(self):
        """测试取消任务"""
        # 定义一个耗时的函数
        def long_task(seconds):
            time.sleep(seconds)
            return f"Slept for {seconds} seconds"
        
        # 注册函数
        function_name = self.computation_layer.register_function(long_task)
        
        # 提交任务
        task_id = self.computation_layer.submit_task(
            function_name=function_name,
            args=[10],  # 睡眠10秒
            priority=TaskPriority.LOW
        )
        
        # 等待任务开始执行
        time.sleep(0.5)
        
        # 取消任务
        success = self.computation_layer.cancel_task(task_id)
        
        # 验证取消成功
        self.assertTrue(success)
        
        # 获取任务信息
        task = self.computation_layer.get_task(task_id)
        
        # 验证任务状态
        self.assertEqual(task.status, TaskStatus.CANCELED)
    
    def test_task_status_handler(self):
        """测试任务状态处理器"""
        # 状态变更记录
        status_changes = []
        
        # 状态处理函数
        def status_handler(task_id, status):
            status_changes.append((task_id, status))
        
        # 注册状态处理器
        self.computation_layer.register_task_status_handler(status_handler)
        
        # 定义一个简单的函数
        def identity(x):
            return x
        
        # 注册函数
        function_name = self.computation_layer.register_function(identity)
        
        # 提交任务
        task_id = self.computation_layer.submit_task(
            function_name=function_name,
            args=["test"],
            priority=TaskPriority.NORMAL
        )
        
        # 等待任务完成
        self.computation_layer.get_task_result(task_id, timeout=5.0)
        
        # 验证状态变更
        self.assertGreaterEqual(len(status_changes), 2)  # 至少有RUNNING和COMPLETED两种状态
        self.assertEqual(status_changes[0][0], task_id)
        self.assertEqual(status_changes[0][1], TaskStatus.RUNNING)
        self.assertEqual(status_changes[-1][0], task_id)
        self.assertEqual(status_changes[-1][1], TaskStatus.COMPLETED)
    
    def test_task_result_handler(self):
        """测试任务结果处理器"""
        # 结果记录
        results = []
        
        # 结果处理函数
        def result_handler(task_id, result):
            results.append((task_id, result))
        
        # 注册结果处理器
        self.computation_layer.register_task_result_handler(result_handler)
        
        # 定义一个简单的函数
        def square(x):
            return x * x
        
        # 注册函数
        function_name = self.computation_layer.register_function(square)
        
        # 提交任务
        task_id = self.computation_layer.submit_task(
            function_name=function_name,
            args=[5],
            priority=TaskPriority.NORMAL
        )
        
        # 等待任务完成
        self.computation_layer.get_task_result(task_id, timeout=5.0)
        
        # 等待结果处理器被调用
        time.sleep(0.5)
        
        # 验证结果
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0][0], task_id)
        self.assertEqual(results[0][1], 25)

if __name__ == '__main__':
    unittest.main()
