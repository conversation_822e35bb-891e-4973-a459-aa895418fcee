#!/usr/bin/env python3
"""
自解释与可验证性算子集成测试脚本

该脚本测试自解释与可验证性算子与思维引擎的集成。
"""

import os
import sys
import unittest
import logging
from typing import Dict, Any, List, Optional, Tuple, Union

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('..'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 尝试导入集成模块
try:
    from src.integration.explanation_verification_integration import (
        ExplanationVerificationIntegration,
        create_integration,
        integrate_with_engine
    )
    
    INTEGRATION_AVAILABLE = True
except ImportError as e:
    logger.error(f"导入集成模块失败: {e}")
    INTEGRATION_AVAILABLE = False

# 尝试导入思维引擎组件
try:
    # 决策协调系统
    from src.transcendental_tensor.multi_scale_coordination.decision_coordination import (
        DecisionCoordinator,
        Decision,
        CoordinationStrategy
    )
    
    # 推理引擎
    from src.core.reasoning.reasoning_engine import ReasoningEngine
    
    ENGINE_COMPONENTS_AVAILABLE = True
except ImportError as e:
    logger.error(f"导入思维引擎组件失败: {e}")
    ENGINE_COMPONENTS_AVAILABLE = False

# 尝试导入算子
try:
    # 算子注册表
    from src.operators.registry import operator_registry
    
    OPERATORS_AVAILABLE = True
except ImportError as e:
    logger.error(f"导入算子失败: {e}")
    OPERATORS_AVAILABLE = False


class TestExplanationVerificationIntegration(unittest.TestCase):
    """自解释与可验证性算子集成测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not INTEGRATION_AVAILABLE:
            raise unittest.SkipTest("集成模块不可用，跳过测试")
        
        if not ENGINE_COMPONENTS_AVAILABLE:
            raise unittest.SkipTest("思维引擎组件不可用，跳过测试")
        
        if not OPERATORS_AVAILABLE:
            raise unittest.SkipTest("算子不可用，跳过测试")
    
    def setUp(self):
        """设置测试环境"""
        # 创建集成实例
        self.integration = create_integration(use_rust=False)
        
        # 创建决策协调系统
        self.decision_coordinator = self._create_mock_decision_coordinator()
        
        # 创建推理引擎
        self.reasoning_engine = self._create_mock_reasoning_engine()
    
    def _create_mock_decision_coordinator(self):
        """创建模拟决策协调系统"""
        class MockEntity:
            def __init__(self, entity_id):
                self.entity_id = entity_id
        
        class MockDecisionCoordinator:
            def __init__(self):
                self.decisions = {}
                self.stats = {"decisions_created": 0, "decisions_executed": 0}
                self.config = type('obj', (object,), {'strategy': CoordinationStrategy.CENTRALIZED})
            
            def get_entity(self, entity_id):
                return MockEntity(entity_id)
            
            def create_decision(self, entity_id, options, context=None, metadata=None):
                decision = Decision(entity_id=entity_id, options=options, context=context, metadata=metadata)
                self.decisions[decision.decision_id] = decision
                self.stats["decisions_created"] += 1
                return decision
            
            def evaluate_decision(self, decision):
                if decision.decision_id not in self.decisions:
                    self.decisions[decision.decision_id] = decision
                
                # 计算权重
                weights = {}
                for option in decision.options:
                    weights[option] = 1.0
                
                # 应用自定义权重
                if decision.weights:
                    for option, weight in decision.weights.items():
                        if option in weights:
                            weights[option] = weight
                
                # 选择最佳选项
                if weights:
                    selected_option = max(weights.items(), key=lambda x: x[1])[0]
                    confidence = weights[selected_option] / sum(weights.values())
                else:
                    selected_option = None
                    confidence = 0.0
                
                # 更新决策
                decision.selected_option = selected_option
                decision.confidence = confidence
                decision.status = "evaluated"
                
                return decision
            
            def execute_decision(self, decision_id, option=None):
                decision = self.decisions.get(decision_id)
                if not decision:
                    return None
                
                # 使用指定选项或已选择的选项
                option = option or decision.selected_option
                
                # 更新决策
                decision.status = "executed"
                decision.execution_result = {"option": option, "success": True}
                
                self.stats["decisions_executed"] += 1
                
                return decision.execution_result
            
            def coordinate_decisions(self, decisions, strategy=None):
                if not decisions:
                    return None
                
                # 使用指定策略或默认策略
                actual_strategy = strategy or self.config.strategy
                
                # 选择置信度最高的决策
                best_decision = max(decisions, key=lambda d: getattr(d, "confidence", 0.0))
                
                # 创建协调决策
                coordinated_decision = Decision(
                    entity_id="coordinator",
                    options={"coordinated": True},
                    context={"source_decisions": [d.decision_id for d in decisions]},
                    metadata={"strategy": str(actual_strategy)}
                )
                
                # 设置选项和置信度
                coordinated_decision.selected_option = "coordinated"
                coordinated_decision.confidence = getattr(best_decision, "confidence", 0.5)
                coordinated_decision.status = "coordinated"
                
                return coordinated_decision
        
        return MockDecisionCoordinator()
    
    def _create_mock_reasoning_engine(self):
        """创建模拟推理引擎"""
        class MockReasoningEngine:
            def __init__(self):
                self.verification_properties = [
                    {
                        "id": "logical_consistency",
                        "type": "safety",
                        "expression": "G(result is consistent with input and context)"
                    },
                    {
                        "id": "completeness",
                        "type": "liveness",
                        "expression": "F(result addresses all aspects of input)"
                    }
                ]
                
                self.consistency_properties = [
                    {
                        "id": "prop_consistency",
                        "type": "consistency",
                        "expression": "result is consistent"
                    }
                ]
                
                self.monitoring_properties = [
                    {
                        "id": "resource_usage",
                        "type": "safety",
                        "expression": "G(memory_usage < 90%)"
                    }
                ]
            
            def reason(self, input_data, context=None, **kwargs):
                # 简单的推理逻辑
                if isinstance(input_data, dict):
                    result = {k: v for k, v in input_data.items()}
                    result["reasoning_applied"] = True
                elif isinstance(input_data, list):
                    result = [item for item in input_data]
                    result.append("reasoning_applied")
                else:
                    result = f"{input_data} (reasoning_applied)"
                
                return result
            
            def evaluate_result(self, result, context=None):
                # 简单的评估逻辑
                if isinstance(result, dict):
                    result["evaluation_applied"] = True
                elif isinstance(result, list):
                    result.append("evaluation_applied")
                else:
                    result = f"{result} (evaluation_applied)"
                
                return result
            
            def handle_violations(self, violations):
                # 简单的违规处理逻辑
                logger.warning(f"处理违规: {violations}")
        
        return MockReasoningEngine()
    
    def test_create_integration(self):
        """测试创建集成实例"""
        # 测试使用Python实现
        integration_py = create_integration(use_rust=False)
        self.assertIsInstance(integration_py, ExplanationVerificationIntegration)
        
        # 测试使用Rust实现（如果可用）
        try:
            integration_rust = create_integration(use_rust=True)
            self.assertIsInstance(integration_rust, ExplanationVerificationIntegration)
        except ImportError:
            logger.warning("Rust实现不可用，跳过Rust集成测试")
    
    def test_integrate_with_decision_coordinator(self):
        """测试集成到决策协调系统"""
        # 集成到决策协调系统
        result = self.integration.integrate_with_decision_coordinator(self.decision_coordinator)
        
        # 验证集成结果
        self.assertTrue(result)
        self.assertTrue(self.integration.integration_status["decision_coordinator_integrated"])
        
        # 验证方法是否被替换
        self.assertNotEqual(self.decision_coordinator.evaluate_decision.__name__, "evaluate_decision")
        self.assertNotEqual(self.decision_coordinator.execute_decision.__name__, "execute_decision")
        self.assertNotEqual(self.decision_coordinator.coordinate_decisions.__name__, "coordinate_decisions")
    
    def test_decision_explanation_integration(self):
        """测试决策解释集成"""
        # 集成到决策协调系统
        self.integration.integrate_with_decision_coordinator(self.decision_coordinator)
        
        # 创建决策
        decision = self.decision_coordinator.create_decision(
            entity_id="test_entity",
            options={"option1": "value1", "option2": "value2"},
            context={"context_key": "context_value"},
            metadata={"metadata_key": "metadata_value"}
        )
        
        # 评估决策
        evaluated_decision = self.decision_coordinator.evaluate_decision(decision)
        
        # 验证解释是否生成
        self.assertTrue(hasattr(evaluated_decision, "explanation"))
        self.assertIn("technical_explanation", evaluated_decision.explanation)
        self.assertIn("conceptual_explanation", evaluated_decision.explanation)
        self.assertIn("analogical_explanation", evaluated_decision.explanation)
        
        # 执行决策
        result = self.decision_coordinator.execute_decision(decision.decision_id)
        
        # 验证解释质量是否评估
        self.assertTrue(hasattr(evaluated_decision, "explanation_quality"))
        self.assertIn("accuracy_score", evaluated_decision.explanation_quality)
        self.assertIn("completeness_score", evaluated_decision.explanation_quality)
        self.assertIn("consistency_score", evaluated_decision.explanation_quality)
        
        # 协调决策
        decisions = [decision]
        coordinated_decision = self.decision_coordinator.coordinate_decisions(decisions)
        
        # 验证可视化解释是否生成
        self.assertTrue(hasattr(coordinated_decision, "visualization"))
        self.assertIn("decision_tree", coordinated_decision.visualization)
        self.assertIn("feature_importance", coordinated_decision.visualization)
    
    def test_integrate_with_reasoning_engine(self):
        """测试集成到推理引擎"""
        # 集成到推理引擎
        result = self.integration.integrate_with_reasoning_engine(self.reasoning_engine)
        
        # 验证集成结果
        self.assertTrue(result)
        self.assertTrue(self.integration.integration_status["reasoning_engine_integrated"])
        
        # 验证方法是否被替换
        self.assertNotEqual(self.reasoning_engine.reason.__name__, "reason")
        self.assertNotEqual(self.reasoning_engine.evaluate_result.__name__, "evaluate_result")
        
        # 验证实时监控器是否添加
        self.assertTrue(hasattr(self.reasoning_engine, "realtime_monitor"))
    
    def test_reasoning_verification_integration(self):
        """测试推理验证集成"""
        # 集成到推理引擎
        self.integration.integrate_with_reasoning_engine(self.reasoning_engine)
        
        # 执行推理
        input_data = {"key1": "value1", "key2": "value2"}
        context = {"context_key": "context_value"}
        result = self.reasoning_engine.reason(input_data, context)
        
        # 验证验证结果是否生成
        self.assertTrue(isinstance(result, dict))
        self.assertIn("verification", result)
        self.assertIn("verification_results", result["verification"])
        self.assertIn("verification_properties", result["verification"])
        
        # 评估结果
        evaluated_result = self.reasoning_engine.evaluate_result(result)
        
        # 验证一致性结果是否生成
        self.assertIn("consistency", evaluated_result)
        self.assertIn("pairwise_consistency", evaluated_result["consistency"])
        self.assertIn("global_consistency", evaluated_result["consistency"])
        self.assertIn("is_consistent", evaluated_result["consistency"])
    
    def test_integrate_with_registry(self):
        """测试集成到算子注册表"""
        # 集成到算子注册表
        result = self.integration.integrate_with_registry()
        
        # 验证集成结果
        self.assertTrue(result)
        self.assertTrue(self.integration.integration_status["registry_integrated"])
        
        # 验证算子是否注册
        self.assertIsNotNone(operator_registry.get_operator("explanation", "multilevel_explanation"))
        self.assertIsNotNone(operator_registry.get_operator("explanation", "explanation_quality"))
        self.assertIsNotNone(operator_registry.get_operator("explanation", "visualization"))
        self.assertIsNotNone(operator_registry.get_operator("verification", "multi_method_verification"))
        self.assertIsNotNone(operator_registry.get_operator("verification", "consistency_verification"))
        self.assertIsNotNone(operator_registry.get_operator("verification", "realtime_verification"))
    
    def test_integrate_with_engine(self):
        """测试集成到思维引擎"""
        # 集成到思维引擎
        status = integrate_with_engine(
            self.integration,
            self.decision_coordinator,
            self.reasoning_engine
        )
        
        # 验证集成状态
        self.assertTrue(status["decision_coordinator_integrated"])
        self.assertTrue(status["reasoning_engine_integrated"])
        self.assertTrue(status["registry_integrated"])


if __name__ == "__main__":
    unittest.main()
