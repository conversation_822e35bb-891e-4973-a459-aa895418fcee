"""
动态态射测试脚本

本脚本测试动态态射模块的基本功能。
"""

import sys
import os
import time
import unittest

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入动态态射模块
from src.transcendental_tensor.self_reflective_category.dynamic_morphism import (
    OptimizationStrategy,
    DynamicMorphismConfig,
    MorphismMetrics,
    DynamicMorphism,
    CompositionMetrics,
    CompositionOptimizer,
    ComposableDynamicMorphism,
    Environment,
    global_environment,
    EnvironmentResponse,
    EnvironmentSensitiveMorphism,
    SystemState,
    global_system_state,
    FeedbackTransform,
    FeedbackEmbed,
    FeedbackController,
    FeedbackMorphism
)

class TestDynamicMorphism(unittest.TestCase):
    """动态态射测试类"""
    
    def test_basic_morphism(self):
        """测试基本态射功能"""
        # 创建测试函数
        def add(a, b):
            return a + b
        
        # 创建动态态射
        morphism = DynamicMorphism(add, name="TestAdd")
        
        # 测试基本调用
        result = morphism(3, 4)
        self.assertEqual(result, 7)
        
        # 测试修改器
        def double_modifier(args, kwargs):
            new_args = tuple(arg * 2 if isinstance(arg, (int, float)) else arg for arg in args)
            new_kwargs = {k: v * 2 if isinstance(v, (int, float)) else v for k, v in kwargs.items()}
            return new_args, new_kwargs
        
        morphism.add_modifier(double_modifier)
        result = morphism(3, 4)
        self.assertEqual(result, 14)  # (3*2) + (4*2) = 14
        
        # 测试历史记录
        history = morphism.get_history()
        self.assertEqual(len(history), 2)
        self.assertEqual(history[0]["result"], 7)
        self.assertEqual(history[1]["result"], 14)
        
        # 测试元数据
        morphism.set_metadata("test_key", "test_value")
        self.assertEqual(morphism.get_metadata("test_key"), "test_value")
        
        # 测试度量
        metrics = morphism.get_metrics()
        self.assertEqual(metrics["call_count"], 2)
        self.assertEqual(metrics["success_count"], 2)
        self.assertEqual(metrics["error_count"], 0)
    
    def test_composition(self):
        """测试态射组合功能"""
        # 创建测试函数
        def add_one(x):
            return x + 1
        
        def multiply_by_two(x):
            return x * 2
        
        # 创建可组合动态态射
        add_one_morphism = ComposableDynamicMorphism(add_one, name="AddOne")
        multiply_by_two_morphism = ComposableDynamicMorphism(multiply_by_two, name="MultiplyByTwo")
        
        # 测试组合方法
        composed_morphism = add_one_morphism.compose(multiply_by_two_morphism)
        result = composed_morphism(3)
        self.assertEqual(result, 8)  # (3 + 1) * 2 = 8
        
        # 测试运算符
        composed_morphism2 = add_one_morphism >> multiply_by_two_morphism
        result2 = composed_morphism2(3)
        self.assertEqual(result2, 8)  # (3 + 1) * 2 = 8
        
        # 测试组合度量
        composition_metrics = add_one_morphism.get_composition_metrics()
        self.assertGreaterEqual(composition_metrics["composition_count"], 1)
    
    def test_environment_sensitivity(self):
        """测试环境敏感功能"""
        # 创建测试环境
        env = Environment(name="TestEnvironment")
        
        # 创建测试函数
        def adaptive_add(a, b):
            return a + b
        
        # 创建环境敏感态射
        morphism = EnvironmentSensitiveMorphism(adaptive_add, env, name="AdaptiveAdd")
        
        # 创建环境条件和行为
        def is_debug_mode(env):
            return env.get("debug_mode", False)
        
        def multiply_args_behavior(args, kwargs, env):
            factor = env.get("multiply_factor", 1)
            new_args = tuple(arg * factor if isinstance(arg, (int, float)) else arg for arg in args)
            new_kwargs = {k: v * factor if isinstance(v, (int, float)) else v for k, v in kwargs.items()}
            return new_args, new_kwargs
        
        # 添加环境响应
        morphism.add_response(is_debug_mode, multiply_args_behavior, name="DebugMultiply")
        
        # 测试正常调用
        result = morphism(3, 4)
        self.assertEqual(result, 7)  # 3 + 4 = 7
        
        # 设置环境为调试模式，并设置乘数因子
        env.set("debug_mode", True)
        env.set("multiply_factor", 2)
        
        # 测试环境敏感调用
        result = morphism(3, 4)
        self.assertEqual(result, 14)  # (3*2) + (4*2) = 14
        
        # 测试获取活动响应
        active_responses = morphism.get_active_responses()
        self.assertEqual(len(active_responses), 1)
        
        # 测试响应统计信息
        response_stats = morphism.get_response_stats()
        self.assertTrue("DebugMultiply" in response_stats)
        self.assertTrue(response_stats["DebugMultiply"]["is_active"])
    
    def test_feedback_mechanism(self):
        """测试反馈机制功能"""
        # 创建自定义反馈变换
        class TestTransform(FeedbackTransform):
            def transform(self, cell_state, system_state):
                super().transform(cell_state, system_state)
                return {"performance": cell_state.get("execution_time", 0.0)}
        
        # 创建自定义反馈嵌入
        class TestEmbed(FeedbackEmbed):
            def embed(self, category_state, system_state):
                super().embed(category_state, system_state)
                return {"optimization_level": 1}
        
        # 创建反馈控制器
        transform = TestTransform(name="TestTransform")
        embed = TestEmbed(name="TestEmbed")
        controller = FeedbackController(transform, embed, name="TestController")
        
        # 创建测试函数
        def adaptive_multiply(a, b):
            return a * b
        
        # 创建反馈态射
        morphism = FeedbackMorphism(adaptive_multiply, controller, name="AdaptiveMultiply")
        
        # 测试调用
        result = morphism(3, 4)
        self.assertEqual(result, 12)  # 3 * 4 = 12
        
        # 测试反馈统计信息
        feedback_stats = morphism.get_feedback_stats()
        self.assertEqual(feedback_stats["morphism_name"], "AdaptiveMultiply")
        self.assertEqual(feedback_stats["controller_stats"]["controller_name"], "TestController")
        self.assertEqual(feedback_stats["feedback_state"]["optimization_level"], 1)


if __name__ == "__main__":
    unittest.main()
