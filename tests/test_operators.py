#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试TTE项目中的Rust算子
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# 导入Rust算子
try:
    from src.operators.rust.tte_operators import (
        FractionalFeatureOperator,
        FractalDimensionOperator,
        MultiscaleEntropyOperator,
        FractalInterpolationOperator,
        PyComplex64,
        WaveletTransformOperator
    )
    print("成功导入TTE Rust算子")
except ImportError as e:
    print(f"导入TTE Rust算子失败: {e}")
    sys.exit(1)

def test_fractional_feature_operator():
    """测试分数阶特征算子"""
    print("\n测试分数阶特征算子...")
    
    # 创建测试数据
    x = np.linspace(0, 10, 1000)
    y = np.sin(x) + 0.1 * np.random.randn(len(x))
    
    # 创建分数阶特征算子
    operator = FractionalFeatureOperator(order=0.5, window_size=100)
    
    # 计算分数阶导数
    result = operator.fractional_derivative(y.tolist())
    
    print(f"分数阶导数结果长度: {len(result)}")
    print(f"分数阶导数前5个值: {result[:5]}")
    
    # 绘制结果
    plt.figure(figsize=(10, 6))
    plt.subplot(211)
    plt.plot(x, y)
    plt.title("原始信号")
    plt.subplot(212)
    plt.plot(x, result)
    plt.title("分数阶导数 (order=0.5)")
    plt.tight_layout()
    plt.savefig(os.path.join(project_root, "tests", "fractional_feature_test.png"))
    
    return True

def test_fractal_dimension_operator():
    """测试分形维数算子"""
    print("\n测试分形维数算子...")
    
    # 创建测试数据 - 分形时间序列
    t = np.linspace(0, 10, 1000)
    brownian = np.cumsum(np.random.randn(len(t)))
    
    # 创建分形维数算子
    operator = FractalDimensionOperator()
    
    # 计算盒维数
    result = operator.box_counting_dimension(brownian.tolist())
    
    print(f"盒维数: {result.dimension}")
    print(f"R²: {result.r_squared}")
    
    # 绘制结果
    plt.figure(figsize=(10, 6))
    plt.subplot(211)
    plt.plot(t, brownian)
    plt.title("布朗运动时间序列")
    plt.subplot(212)
    plt.scatter(result.log_scales, result.log_counts)
    plt.plot(result.log_scales, result.intercept + result.slope * np.array(result.log_scales), 'r')
    plt.title(f"盒维数: {result.dimension:.4f}, R²: {result.r_squared:.4f}")
    plt.tight_layout()
    plt.savefig(os.path.join(project_root, "tests", "fractal_dimension_test.png"))
    
    return True

def test_multiscale_entropy_operator():
    """测试多尺度熵算子"""
    print("\n测试多尺度熵算子...")
    
    # 创建测试数据
    np.random.seed(42)
    # 随机数据
    random_data = np.random.rand(1000)
    # 正弦波
    t = np.linspace(0, 10, 1000)
    sine_data = np.sin(2 * np.pi * t)
    
    # 创建多尺度熵算子
    operator = MultiscaleEntropyOperator(m=2, r=0.2)
    
    # 计算多尺度熵
    random_result = operator.calculate(random_data.tolist(), scales=5)
    sine_result = operator.calculate(sine_data.tolist(), scales=5)
    
    print(f"随机数据多尺度熵: {random_result.entropy_values}")
    print(f"正弦波多尺度熵: {sine_result.entropy_values}")
    
    # 绘制结果
    plt.figure(figsize=(10, 8))
    plt.subplot(311)
    plt.plot(t[:100], random_data[:100])
    plt.title("随机数据")
    plt.subplot(312)
    plt.plot(t[:100], sine_data[:100])
    plt.title("正弦波")
    plt.subplot(313)
    plt.plot(range(1, 6), random_result.entropy_values, 'o-', label="随机数据")
    plt.plot(range(1, 6), sine_result.entropy_values, 's-', label="正弦波")
    plt.xlabel("尺度")
    plt.ylabel("样本熵")
    plt.legend()
    plt.title("多尺度熵分析")
    plt.tight_layout()
    plt.savefig(os.path.join(project_root, "tests", "multiscale_entropy_test.png"))
    
    return True

def test_fractal_interpolation_operator():
    """测试分形插值算子"""
    print("\n测试分形插值算子...")
    
    # 创建测试数据
    x = np.linspace(0, 10, 10)
    y = np.sin(x)
    
    # 创建分形插值算子
    operator = FractalInterpolationOperator(vertical_scaling=0.5)
    
    # 执行分形插值
    x_dense = np.linspace(0, 10, 100)
    result = operator.interpolate(x.tolist(), y.tolist(), x_dense.tolist())
    
    print(f"插值结果长度: {len(result)}")
    print(f"插值结果前5个值: {result[:5]}")
    
    # 绘制结果
    plt.figure(figsize=(10, 6))
    plt.plot(x, y, 'o', label="原始数据点")
    plt.plot(x_dense, result, label="分形插值")
    plt.title("分形插值")
    plt.legend()
    plt.savefig(os.path.join(project_root, "tests", "fractal_interpolation_test.png"))
    
    return True

def test_wavelet_transform_operator():
    """测试小波变换算子"""
    print("\n测试小波变换算子...")
    
    # 创建测试数据
    t = np.linspace(0, 1, 512)
    signal = np.sin(2 * np.pi * 10 * t) + 0.5 * np.sin(2 * np.pi * 25 * t)
    
    # 创建小波变换算子
    operator = WaveletTransformOperator(thread_count=4)
    
    # 执行离散小波变换
    result = operator.discrete_wavelet_transform(signal.tolist(), "db4", 3)
    
    print(f"近似系数长度: {len(result['approx'])}")
    print(f"细节系数数量: {len(result['details'])}")
    
    # 重构信号
    reconstructed = operator.inverse_discrete_wavelet_transform(
        result['approx'], 
        result['details'], 
        "db4", 
        len(signal)
    )
    
    # 计算重构误差
    error = np.mean(np.abs(signal - reconstructed))
    print(f"重构误差: {error}")
    
    # 绘制结果
    plt.figure(figsize=(10, 8))
    plt.subplot(311)
    plt.plot(t, signal)
    plt.title("原始信号")
    plt.subplot(312)
    plt.plot(result['approx'])
    plt.title("近似系数")
    plt.subplot(313)
    plt.plot(t, reconstructed)
    plt.title(f"重构信号 (误差: {error:.6f})")
    plt.tight_layout()
    plt.savefig(os.path.join(project_root, "tests", "wavelet_transform_test.png"))
    
    return True

def test_complex_wrapper():
    """测试复数包装类型"""
    print("\n测试复数包装类型...")
    
    # 创建复数
    c1 = PyComplex64(1.0, 2.0)
    
    # 获取实部和虚部
    real = c1.real
    imag = c1.imag
    
    print(f"复数: {c1}")
    print(f"实部: {real}, 虚部: {imag}")
    
    return True

def main():
    """主函数"""
    print("开始测试TTE Rust算子...")
    
    # 测试所有算子
    tests = [
        test_fractional_feature_operator,
        test_fractal_dimension_operator,
        test_multiscale_entropy_operator,
        test_fractal_interpolation_operator,
        test_wavelet_transform_operator,
        test_complex_wrapper
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"测试失败: {e}")
            results.append(False)
    
    # 打印测试结果
    print("\n测试结果汇总:")
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "通过" if result else "失败"
        print(f"{i+1}. {test.__name__}: {status}")
    
    # 计算通过率
    pass_rate = sum(results) / len(results) * 100
    print(f"\n测试通过率: {pass_rate:.2f}%")

if __name__ == "__main__":
    main()
