#!/usr/bin/env python3
"""
算子集成测试脚本

测试算子之间的组合和交互。
"""

import os
import sys
import unittest
import logging
from typing import Dict, Any, List, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('..'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 尝试导入算子
try:
    # 解释算子
    from src.operators.explanation.multilevel_explanation import MultilevelExplanationOperator
    from src.operators.explanation.explanation_quality import ExplanationQualityOperator
    from src.operators.explanation.visualization import VisualizationExplanationOperator
    
    # 验证算子
    from src.operators.verification.multi_method_verification import MultiMethodVerificationOperator
    from src.operators.verification.consistency_verification import ConsistencyVerificationOperator
    from src.operators.verification.realtime_verification import RealtimeVerificationOperator
    
    # 算子注册表
    from src.operators.registry import operator_registry
    
    OPERATORS_AVAILABLE = True
except ImportError as e:
    logger.error(f"导入算子失败: {e}")
    OPERATORS_AVAILABLE = False


class TestOperatorIntegration(unittest.TestCase):
    """算子集成测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not OPERATORS_AVAILABLE:
            raise unittest.SkipTest("算子不可用，跳过测试")
    
    def setUp(self):
        """设置测试环境"""
        # 创建算子实例
        self.multilevel_explanation_operator = MultilevelExplanationOperator()
        self.explanation_quality_operator = ExplanationQualityOperator()
        self.visualization_operator = VisualizationExplanationOperator()
        self.multi_method_verification_operator = MultiMethodVerificationOperator()
        self.consistency_verification_operator = ConsistencyVerificationOperator()
        self.realtime_verification_operator = RealtimeVerificationOperator()
        
        # 准备测试数据
        self.model_output = {
            "prediction": "positive",
            "confidence": 0.85,
            "features": {
                "text_length": 120,
                "sentiment_score": 0.75,
                "topic_relevance": 0.9
            }
        }
        
        self.system_state = {
            "variables": {
                "x": 10,
                "y": 20,
                "z": 30
            }
        }
        
        self.properties = [
            {
                "id": "safety1",
                "type": "safety",
                "expression": "G(x > 0)"
            },
            {
                "id": "liveness1",
                "type": "liveness",
                "expression": "F(y = 1)"
            },
            {
                "id": "runtime1",
                "type": "runtime",
                "expression": "x + y < 100"
            }
        ]
    
    def test_explanation_pipeline(self):
        """测试解释算子管道"""
        # 生成多层次解释
        explanation_input = {
            "model_output": self.model_output,
            "explanation_level": "all",
            "user_expertise": "beginner"
        }
        
        explanation_result = self.multilevel_explanation_operator.apply(explanation_input)
        
        # 验证解释结果
        self.assertIn("technical_explanation", explanation_result)
        self.assertIn("conceptual_explanation", explanation_result)
        self.assertIn("analogical_explanation", explanation_result)
        self.assertIn("combined_explanation", explanation_result)
        
        # 评估解释质量
        quality_input = {
            "explanation": explanation_result,
            "model_output": self.model_output,
            "user_expertise": "beginner"
        }
        
        quality_result = self.explanation_quality_operator.apply(quality_input)
        
        # 验证质量评估结果
        self.assertIn("accuracy_score", quality_result)
        self.assertIn("completeness_score", quality_result)
        self.assertIn("consistency_score", quality_result)
        self.assertIn("understandability_score", quality_result)
        self.assertIn("overall_quality_score", quality_result)
        
        # 生成可视化解释
        visualization_input = {
            "explanation": explanation_result,
            "model_output": self.model_output,
            "visualization_type": "all"
        }
        
        visualization_result = self.visualization_operator.apply(visualization_input)
        
        # 验证可视化结果
        self.assertIn("decision_tree", visualization_result)
        self.assertIn("feature_importance", visualization_result)
        self.assertIn("attention_heatmap", visualization_result)
    
    def test_verification_pipeline(self):
        """测试验证算子管道"""
        # 进行多方法验证
        verification_input = {
            "state": self.system_state,
            "properties": self.properties
        }
        
        verification_result = self.multi_method_verification_operator.apply(verification_input)
        
        # 验证验证结果
        self.assertIn("verification_results", verification_result)
        self.assertIn("verification_properties", verification_result)
        self.assertIn("verification_methods_used", verification_result)
        
        # 检查一致性
        consistency_input = {
            "properties": self.properties
        }
        
        consistency_result = self.consistency_verification_operator.apply(consistency_input)
        
        # 验证一致性结果
        self.assertIn("pairwise_consistency", consistency_result)
        self.assertIn("global_consistency", consistency_result)
        self.assertIn("inconsistencies", consistency_result)
        self.assertIn("is_consistent", consistency_result)
        self.assertIn("consistency_score", consistency_result)
        
        # 进行实时验证
        realtime_input = {
            "state": self.system_state,
            "properties": self.properties
        }
        
        realtime_result = self.realtime_verification_operator.apply(realtime_input)
        
        # 验证实时验证结果
        self.assertIn("verification_results", realtime_result)
        self.assertIn("violations", realtime_result)
        self.assertIn("monitoring_active", realtime_result)
        self.assertIn("history_size", realtime_result)
    
    def test_explanation_verification_integration(self):
        """测试解释和验证算子的集成"""
        # 生成多层次解释
        explanation_input = {
            "model_output": self.model_output,
            "explanation_level": "all",
            "user_expertise": "beginner"
        }
        
        explanation_result = self.multilevel_explanation_operator.apply(explanation_input)
        
        # 验证解释
        verification_input = {
            "state": {
                "explanation": explanation_result,
                "model_output": self.model_output
            },
            "properties": [
                {
                    "id": "explanation_accuracy",
                    "type": "safety",
                    "expression": "G(explanation.accuracy_score > 0.7)"
                },
                {
                    "id": "explanation_completeness",
                    "type": "safety",
                    "expression": "G(explanation.completeness_score > 0.7)"
                }
            ]
        }
        
        verification_result = self.multi_method_verification_operator.apply(verification_input)
        
        # 验证验证结果
        self.assertIn("verification_results", verification_result)
        self.assertIn("verification_properties", verification_result)
        self.assertIn("verification_methods_used", verification_result)
    
    def test_operator_registry_integration(self):
        """测试算子注册表集成"""
        # 获取解释算子
        multilevel_explanation_operator_class = operator_registry.get_operator("explanation", "multilevel_explanation")
        self.assertIsNotNone(multilevel_explanation_operator_class)
        
        # 创建算子实例
        multilevel_explanation_operator = multilevel_explanation_operator_class()
        
        # 生成多层次解释
        explanation_input = {
            "model_output": self.model_output,
            "explanation_level": "all",
            "user_expertise": "beginner"
        }
        
        explanation_result = multilevel_explanation_operator.apply(explanation_input)
        
        # 验证解释结果
        self.assertIn("technical_explanation", explanation_result)
        self.assertIn("conceptual_explanation", explanation_result)
        self.assertIn("analogical_explanation", explanation_result)
        self.assertIn("combined_explanation", explanation_result)
        
        # 获取验证算子
        multi_method_verification_operator_class = operator_registry.get_operator("verification", "multi_method_verification")
        self.assertIsNotNone(multi_method_verification_operator_class)
        
        # 创建算子实例
        multi_method_verification_operator = multi_method_verification_operator_class()
        
        # 进行多方法验证
        verification_input = {
            "state": self.system_state,
            "properties": self.properties
        }
        
        verification_result = multi_method_verification_operator.apply(verification_input)
        
        # 验证验证结果
        self.assertIn("verification_results", verification_result)
        self.assertIn("verification_properties", verification_result)
        self.assertIn("verification_methods_used", verification_result)
    
    def test_rust_python_compatibility(self):
        """测试Rust和Python实现的兼容性"""
        # 获取Python实现的多层次解释生成算子
        py_multilevel_explanation_operator_class = operator_registry.get_operator("explanation", "multilevel_explanation")
        self.assertIsNotNone(py_multilevel_explanation_operator_class)
        
        # 创建Python实现的算子实例
        py_multilevel_explanation_operator = py_multilevel_explanation_operator_class()
        
        # 获取Rust实现的多层次解释生成算子
        rust_multilevel_explanation_operator_class = operator_registry.get_operator("explanation", "rust_multilevel_explanation")
        
        # 如果Rust实现可用，则进行兼容性测试
        if rust_multilevel_explanation_operator_class is not None:
            # 创建Rust实现的算子实例
            rust_multilevel_explanation_operator = rust_multilevel_explanation_operator_class()
            
            # 准备输入数据
            explanation_input = {
                "model_output": self.model_output,
                "explanation_level": "all",
                "user_expertise": "beginner"
            }
            
            # 应用Python实现的算子
            py_result = py_multilevel_explanation_operator.apply(explanation_input)
            
            # 应用Rust实现的算子
            rust_result = rust_multilevel_explanation_operator.apply(explanation_input)
            
            # 验证结果结构是否相同
            self.assertEqual(set(py_result.keys()), set(rust_result.keys()))
            
            # 验证结果类型是否相同
            for key in py_result:
                self.assertEqual(type(py_result[key]), type(rust_result[key]))
        else:
            logger.warning("Rust实现的多层次解释生成算子不可用，跳过兼容性测试")


if __name__ == "__main__":
    unittest.main()
