"""
超越态融合算子稳定性测试

本脚本用于测试超越态融合算子在不同场景下的稳定性和可靠性。
"""

import sys
import os
import time
import numpy as np
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# 导入融合算子
try:
    from src.operators.fusion_registry import (
        quantum_superposition_fusion,
        holographic_interference_fusion,
        fractal_fusion,
        topological_fusion,
        fusion_with_method,
        get_fusion_operator
    )
    print("成功导入融合算子")
except ImportError as e:
    print(f"导入融合算子失败: {e}")
    sys.exit(1)

def test_edge_cases():
    """测试边界情况"""
    print("\n测试边界情况:")
    
    # 测试空状态
    try:
        empty_state = []
        result = quantum_superposition_fusion(empty_state, empty_state)
        print("空状态测试结果:", result)
    except Exception as e:
        print(f"空状态测试异常: {e}")
    
    # 测试零状态
    try:
        zero_state = [0.0 + 0.0j, 0.0 + 0.0j]
        result = quantum_superposition_fusion(zero_state, zero_state)
        print("零状态测试结果:", result)
    except Exception as e:
        print(f"零状态测试异常: {e}")
    
    # 测试非归一化状态
    try:
        non_normalized_state = [2.0 + 0.0j, 0.0 + 0.0j]
        result = quantum_superposition_fusion(non_normalized_state, non_normalized_state)
        print("非归一化状态测试结果:", result)
        print("结果范数:", np.sqrt(np.sum(np.abs(np.array(result))**2)))
    except Exception as e:
        print(f"非归一化状态测试异常: {e}")
    
    # 测试不同维度状态
    try:
        state_a = [1.0 + 0.0j, 0.0 + 0.0j]
        state_b = [1.0 + 0.0j, 0.0 + 0.0j, 0.0 + 0.0j]
        result = quantum_superposition_fusion(state_a, state_b)
        print("不同维度状态测试结果:", result)
    except Exception as e:
        print(f"不同维度状态测试异常: {e}")
    
    # 测试复杂状态
    try:
        complex_state_a = [0.5 + 0.5j, 0.5 - 0.5j]
        complex_state_b = [0.0 + 1.0j, 1.0 + 0.0j]
        result = quantum_superposition_fusion(complex_state_a, complex_state_b)
        print("复杂状态测试结果:", result)
        print("结果范数:", np.sqrt(np.sum(np.abs(np.array(result))**2)))
    except Exception as e:
        print(f"复杂状态测试异常: {e}")

def test_large_states():
    """测试大规模状态"""
    print("\n测试大规模状态:")
    
    # 测试不同维度的状态
    dimensions = [10, 100, 1000, 10000]
    
    for dim in dimensions:
        try:
            # 创建随机状态
            state_a = np.random.normal(0, 1, dim) + 1j * np.random.normal(0, 1, dim)
            state_b = np.random.normal(0, 1, dim) + 1j * np.random.normal(0, 1, dim)
            
            # 归一化
            state_a = state_a / np.sqrt(np.sum(np.abs(state_a)**2))
            state_b = state_b / np.sqrt(np.sum(np.abs(state_b)**2))
            
            # 转换为列表
            state_a = state_a.tolist()
            state_b = state_b.tolist()
            
            # 测量性能
            start_time = time.time()
            result = quantum_superposition_fusion(state_a, state_b)
            elapsed_time = time.time() - start_time
            
            # 验证结果
            result_array = np.array(result)
            norm = np.sqrt(np.sum(np.abs(result_array)**2))
            
            print(f"维度 {dim}:")
            print(f"  执行时间: {elapsed_time:.6f} 秒")
            print(f"  结果范数: {norm:.10f}")
            print(f"  前5个元素: {result[:5]}")
        except Exception as e:
            print(f"维度 {dim} 测试异常: {e}")
            traceback.print_exc()

def test_method_consistency():
    """测试不同方法的一致性"""
    print("\n测试不同方法的一致性:")
    
    # 创建测试状态
    state_a = [1.0 + 0.0j, 0.0 + 0.0j]
    state_b = [0.0 + 0.0j, 1.0 + 0.0j]
    
    # 测试不同的融合方法
    methods = {
        "quantum_superposition": quantum_superposition_fusion,
        "holographic_interference": holographic_interference_fusion,
        "fractal_fusion": fractal_fusion,
        "topological_fusion": topological_fusion,
    }
    
    # 直接调用方法
    direct_results = {}
    for name, method in methods.items():
        try:
            result = method(state_a, state_b)
            norm = np.sqrt(np.sum(np.abs(np.array(result))**2))
            direct_results[name] = (result, norm)
            print(f"方法 {name} 直接调用结果范数: {norm:.10f}")
        except Exception as e:
            print(f"方法 {name} 直接调用异常: {e}")
    
    # 通过通用方法调用
    generic_results = {}
    for name in methods.keys():
        try:
            result = fusion_with_method(state_a, state_b, name)
            norm = np.sqrt(np.sum(np.abs(np.array(result))**2))
            generic_results[name] = (result, norm)
            print(f"方法 {name} 通用调用结果范数: {norm:.10f}")
        except Exception as e:
            print(f"方法 {name} 通用调用异常: {e}")
    
    # 比较结果
    for name in methods.keys():
        if name in direct_results and name in generic_results:
            direct_result, direct_norm = direct_results[name]
            generic_result, generic_norm = generic_results[name]
            
            # 计算结果差异
            diff = np.array(direct_result) - np.array(generic_result)
            diff_norm = np.sqrt(np.sum(np.abs(diff)**2))
            
            print(f"方法 {name} 结果差异: {diff_norm:.10f}")

def test_concurrent_execution():
    """测试并发执行"""
    print("\n测试并发执行:")
    
    try:
        import threading
        
        # 创建测试状态
        state_a = [1.0 + 0.0j, 0.0 + 0.0j]
        state_b = [0.0 + 0.0j, 1.0 + 0.0j]
        
        # 定义线程函数
        def thread_func(method, name, results, index):
            try:
                result = method(state_a, state_b)
                norm = np.sqrt(np.sum(np.abs(np.array(result))**2))
                results[index] = (name, result, norm)
            except Exception as e:
                results[index] = (name, None, str(e))
        
        # 创建多个线程
        threads = []
        results = [None] * 20
        
        methods = [
            (quantum_superposition_fusion, "quantum_superposition"),
            (holographic_interference_fusion, "holographic_interference"),
            (fractal_fusion, "fractal_fusion"),
            (topological_fusion, "topological_fusion"),
        ]
        
        for i in range(20):
            method, name = methods[i % 4]
            thread = threading.Thread(target=thread_func, args=(method, name, results, i))
            threads.append(thread)
        
        # 启动所有线程
        for thread in threads:
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 检查结果
        success_count = 0
        for i, result in enumerate(results):
            if result is not None and result[1] is not None:
                name, _, norm = result
                print(f"线程 {i} ({name}) 成功, 结果范数: {norm:.10f}")
                success_count += 1
            else:
                name, _, error = result
                print(f"线程 {i} ({name}) 失败: {error}")
        
        print(f"并发测试: {success_count}/{len(threads)} 线程成功")
    except Exception as e:
        print(f"并发测试异常: {e}")
        traceback.print_exc()

def test_error_handling():
    """测试错误处理"""
    print("\n测试错误处理:")
    
    # 测试无效参数
    try:
        result = quantum_superposition_fusion(None, None)
        print("无效参数测试结果:", result)
    except Exception as e:
        print(f"无效参数测试异常: {e}")
    
    # 测试无效方法
    try:
        result = fusion_with_method([1.0 + 0.0j], [0.0 + 0.0j], "invalid_method")
        print("无效方法测试结果:", result)
    except Exception as e:
        print(f"无效方法测试异常: {e}")
    
    # 测试无效权重
    try:
        result = quantum_superposition_fusion([1.0 + 0.0j], [0.0 + 0.0j], -1.0, 2.0)
        print("无效权重测试结果:", result)
    except Exception as e:
        print(f"无效权重测试异常: {e}")

if __name__ == "__main__":
    print("开始超越态融合算子稳定性测试...")
    
    # 测试边界情况
    test_edge_cases()
    
    # 测试大规模状态
    test_large_states()
    
    # 测试不同方法的一致性
    test_method_consistency()
    
    # 测试并发执行
    test_concurrent_execution()
    
    # 测试错误处理
    test_error_handling()
    
    print("\n稳定性测试完成")
