"""
超越态融合算子简单测试脚本

本脚本用于测试超越态融合算子的基本功能。
"""

import sys
import os
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# 导入融合算子包装器
try:
    from src.operators.fusion_wrapper import TranscendentalFusionOperator, FusionMethod
except ImportError as e:
    print(f"导入融合算子包装器失败: {e}")
    sys.exit(1)

def test_fusion_methods():
    """测试各种融合方法"""
    # 创建融合算子
    try:
        fusion_op = TranscendentalFusionOperator()
        print("成功创建融合算子实例")
    except Exception as e:
        print(f"创建融合算子实例失败: {e}")
        return False
    
    # 创建两个测试状态
    state_a = np.array([1.0, 0.0], dtype=np.complex128)  # |0⟩
    state_b = np.array([0.0, 1.0], dtype=np.complex128)  # |1⟩
    
    print("\n测试融合算子功能:")
    print("状态A:", state_a)
    print("状态B:", state_b)
    print()
    
    # 测试各种融合方法
    methods = {
        "量子叠加": FusionMethod.QUANTUM_SUPERPOSITION,
        "全息干涉": FusionMethod.HOLOGRAPHIC_INTERFERENCE,
        "分形融合": FusionMethod.FRACTAL_FUSION,
        "拓扑融合": FusionMethod.TOPOLOGICAL_FUSION,
    }
    
    results = {}
    
    for name, method in methods.items():
        try:
            result = fusion_op.fuse(state_a, state_b, method=method)
            results[name] = result
            
            print(f"方法: {name}")
            print("融合结果:", result)
            print("结果概率:", np.abs(result)**2)
            
            # 验证结果是否为单位向量
            norm = np.sqrt(np.sum(np.abs(result)**2))
            print("结果范数:", norm)
            print()
        except Exception as e:
            print(f"方法 {name} 测试失败: {e}")
    
    return len(results) > 0

def test_fusion_performance():
    """测试融合算子性能"""
    # 创建融合算子
    try:
        fusion_op = TranscendentalFusionOperator()
    except Exception as e:
        print(f"创建融合算子实例失败: {e}")
        return False
    
    # 创建大型测试状态
    dim = 1000
    state_a = np.zeros(dim, dtype=np.complex128)
    state_b = np.zeros(dim, dtype=np.complex128)
    
    # 初始化状态
    state_a[0] = 1.0
    state_b[1] = 1.0
    
    print("\n测试融合算子性能:")
    print(f"状态维度: {dim}")
    print()
    
    # 测试各种融合方法的性能
    methods = {
        "量子叠加": FusionMethod.QUANTUM_SUPERPOSITION,
        "全息干涉": FusionMethod.HOLOGRAPHIC_INTERFERENCE,
        "分形融合": FusionMethod.FRACTAL_FUSION,
        "拓扑融合": FusionMethod.TOPOLOGICAL_FUSION,
    }
    
    for name, method in methods.items():
        try:
            # 预热
            fusion_op.fuse(state_a, state_b, method=method)
            
            # 计时
            iterations = 5
            start_time = time.time()
            
            for _ in range(iterations):
                fusion_op.fuse(state_a, state_b, method=method)
            
            elapsed_time = time.time() - start_time
            avg_time = elapsed_time / iterations * 1000  # 毫秒
            
            print(f"方法: {name} 平均执行时间: {avg_time:.2f} 毫秒")
        except Exception as e:
            print(f"方法 {name} 性能测试失败: {e}")
    
    return True

if __name__ == "__main__":
    print("开始测试超越态融合算子...")
    
    # 测试融合算子功能
    functionality_ok = test_fusion_methods()
    
    if functionality_ok:
        # 测试融合算子性能
        test_fusion_performance()
    
    print("\n测试完成")
