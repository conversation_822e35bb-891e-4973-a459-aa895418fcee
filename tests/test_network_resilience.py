"""
网络弹性计算算子测试

本测试脚本测试网络弹性计算算子的基本功能。
"""

import sys
import os
import unittest
import numpy as np
from unittest.mock import MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入网络弹性计算算子
from src.transcendental_tensor.network_resilience import (
    NodeType, NodeStatus, LinkStatus, FailureType,
    ResilienceNode, ResilienceLink, ResilienceNetwork,
    FailureEvent, ResilienceResult,
    ResilienceAnalyzer, ResilienceSimulator, NetworkResilienceOperator
)


class TestNetworkResilience(unittest.TestCase):
    """网络弹性计算算子测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建测试网络
        self.network = ResilienceNetwork(name="TestNetwork")
        
        # 创建节点
        self.node1 = ResilienceNode(
            node_id="node-1",
            name="Node 1",
            node_type=NodeType.COMPUTE,
            status=NodeStatus.ONLINE
        )
        
        self.node2 = ResilienceNode(
            node_id="node-2",
            name="Node 2",
            node_type=NodeType.STORAGE,
            status=NodeStatus.ONLINE
        )
        
        self.node3 = ResilienceNode(
            node_id="node-3",
            name="Node 3",
            node_type=NodeType.GATEWAY,
            status=NodeStatus.ONLINE
        )
        
        self.node4 = ResilienceNode(
            node_id="node-4",
            name="Node 4",
            node_type=NodeType.COORDINATOR,
            status=NodeStatus.ONLINE
        )
        
        # 添加节点
        self.network.add_node(self.node1)
        self.network.add_node(self.node2)
        self.network.add_node(self.node3)
        self.network.add_node(self.node4)
        
        # 创建链接
        self.link1 = ResilienceLink(
            source_id="node-1",
            target_id="node-2",
            status=LinkStatus.ACTIVE,
            latency=10.0,
            bandwidth=100.0,
            packet_loss=0.01
        )
        
        self.link2 = ResilienceLink(
            source_id="node-2",
            target_id="node-3",
            status=LinkStatus.ACTIVE,
            latency=20.0,
            bandwidth=50.0,
            packet_loss=0.02
        )
        
        self.link3 = ResilienceLink(
            source_id="node-3",
            target_id="node-4",
            status=LinkStatus.ACTIVE,
            latency=15.0,
            bandwidth=75.0,
            packet_loss=0.015
        )
        
        # 添加链接
        self.network.add_link(self.link1)
        self.network.add_link(self.link2)
        self.network.add_link(self.link3)
        
        # 创建故障事件
        self.failure_event = FailureEvent(
            failure_type=FailureType.NODE_FAILURE,
            target_id="node-2",
            target_type="node",
            severity=0.8,
            start_time=100.0,
            duration=60.0
        )
        
        # 创建分析器
        self.analyzer = ResilienceAnalyzer()
        
        # 创建模拟器
        self.simulator = ResilienceSimulator()
        
        # 创建算子
        self.operator = NetworkResilienceOperator()
    
    def test_resilience_node(self):
        """测试弹性节点"""
        # 测试节点属性
        self.assertEqual(self.node1.node_id, "node-1")
        self.assertEqual(self.node1.name, "Node 1")
        self.assertEqual(self.node1.node_type, NodeType.COMPUTE)
        self.assertEqual(self.node1.status, NodeStatus.ONLINE)
        
        # 测试节点方法
        self.assertTrue(self.node1.is_active())
        
        # 更新状态
        self.node1.update_status(NodeStatus.DEGRADED)
        self.assertEqual(self.node1.status, NodeStatus.DEGRADED)
        self.assertTrue(self.node1.is_active())
        
        # 更新状态
        self.node1.update_status(NodeStatus.FAILED)
        self.assertEqual(self.node1.status, NodeStatus.FAILED)
        self.assertFalse(self.node1.is_active())
    
    def test_resilience_link(self):
        """测试弹性链接"""
        # 测试链接属性
        self.assertEqual(self.link1.source_id, "node-1")
        self.assertEqual(self.link1.target_id, "node-2")
        self.assertEqual(self.link1.status, LinkStatus.ACTIVE)
        self.assertEqual(self.link1.latency, 10.0)
        self.assertEqual(self.link1.bandwidth, 100.0)
        self.assertEqual(self.link1.packet_loss, 0.01)
        
        # 测试链接方法
        self.assertTrue(self.link1.is_active())
        
        # 更新状态
        self.link1.update_status(LinkStatus.DEGRADED)
        self.assertEqual(self.link1.status, LinkStatus.DEGRADED)
        self.assertFalse(self.link1.is_active())
    
    def test_resilience_network(self):
        """测试弹性网络"""
        # 测试网络属性
        self.assertEqual(len(self.network.nodes), 4)
        self.assertEqual(len(self.network.links), 3)
        
        # 测试网络方法
        node = self.network.get_node("node-1")
        self.assertEqual(node.node_id, "node-1")
        
        link = self.network.get_link(self.link1.link_id)
        self.assertEqual(link.source_id, "node-1")
        self.assertEqual(link.target_id, "node-2")
        
        # 测试移除节点
        self.network.remove_node("node-4")
        self.assertEqual(len(self.network.nodes), 3)
        self.assertEqual(len(self.network.links), 2)
        
        # 测试移除链接
        self.network.remove_link(self.link1.link_id)
        self.assertEqual(len(self.network.links), 1)
    
    def test_failure_event(self):
        """测试故障事件"""
        # 测试事件属性
        self.assertEqual(self.failure_event.failure_type, FailureType.NODE_FAILURE)
        self.assertEqual(self.failure_event.target_id, "node-2")
        self.assertEqual(self.failure_event.target_type, "node")
        self.assertEqual(self.failure_event.severity, 0.8)
        self.assertEqual(self.failure_event.start_time, 100.0)
        self.assertEqual(self.failure_event.duration, 60.0)
        
        # 测试事件方法
        self.assertTrue(self.failure_event.is_active(110.0))
        self.assertFalse(self.failure_event.is_active(170.0))
        
        # 结束事件
        self.failure_event.end(150.0)
        self.assertEqual(self.failure_event.end_time, 150.0)
        self.assertFalse(self.failure_event.is_active(160.0))
    
    def test_resilience_analyzer(self):
        """测试弹性分析器"""
        # 分析网络
        analysis = self.analyzer.analyze_network(self.network)
        
        # 验证分析结果
        self.assertIn("resilience_score", analysis)
        self.assertIn("metrics", analysis)
        self.assertIn("node_metrics", analysis)
        self.assertIn("link_metrics", analysis)
        
        # 验证指标
        metrics = analysis["metrics"]
        self.assertIn("robustness", metrics)
        self.assertIn("redundancy", metrics)
        self.assertIn("diversity", metrics)
        self.assertIn("adaptability", metrics)
        
        # 验证节点指标
        node_metrics = analysis["node_metrics"]
        for node_id in self.network.nodes:
            self.assertIn(node_id, node_metrics)
            self.assertIn("degree", node_metrics[node_id])
            self.assertIn("centrality", node_metrics[node_id])
            self.assertIn("clustering", node_metrics[node_id])
        
        # 验证链接指标
        link_metrics = analysis["link_metrics"]
        for link_id in self.network.links:
            self.assertIn(link_id, link_metrics)
            self.assertIn("betweenness", link_metrics[link_id])
    
    def test_resilience_simulator(self):
        """测试弹性模拟器"""
        # 设置模拟参数
        self.simulator.parameters["simulation_duration"] = 100.0
        self.simulator.parameters["time_step"] = 10.0
        
        # 模拟网络
        result = self.simulator.simulate(self.network, [self.failure_event])
        
        # 验证模拟结果
        self.assertIsInstance(result, ResilienceResult)
        self.assertEqual(result.network_id, self.network.network_id)
        self.assertIn("average_resilience_score", result.metrics)
        self.assertIn("min_resilience_score", result.metrics)
        self.assertIn("average_recovery_time", result.metrics)
        self.assertGreaterEqual(result.resilience_score, 0.0)
        self.assertLessEqual(result.resilience_score, 1.0)
    
    def test_network_resilience_operator(self):
        """测试网络弹性计算算子"""
        # 分析网络
        analysis = self.operator.analyze_network(self.network)
        
        # 验证分析结果
        self.assertIn("resilience_score", analysis)
        self.assertIn("metrics", analysis)
        
        # 模拟网络
        result = self.operator.simulate_network(self.network, [self.failure_event])
        
        # 验证模拟结果
        self.assertIsInstance(result, ResilienceResult)
        self.assertEqual(result.network_id, self.network.network_id)
        
        # 计算弹性
        resilience = self.operator.calculate_resilience(self.network)
        
        # 验证弹性计算结果
        self.assertIn("resilience_score", resilience)
        self.assertIn("metrics", resilience)
        self.assertIn("simulation", resilience)
        
        # 优化网络
        optimized_network, optimization_result = self.operator.optimize_network(self.network)
        
        # 验证优化结果
        self.assertIsInstance(optimized_network, ResilienceNetwork)
        self.assertIn("original_score", optimization_result)
        self.assertIn("optimized_score", optimization_result)
        self.assertIn("improvement", optimization_result)
        
        # 比较网络
        networks = [self.network, optimized_network]
        comparison = self.operator.compare_networks(networks)
        
        # 验证比较结果
        self.assertIn("results", comparison)
        self.assertIn("average_score", comparison)
        self.assertIn("best_network", comparison)
        self.assertIn("worst_network", comparison)


if __name__ == "__main__":
    unittest.main()
