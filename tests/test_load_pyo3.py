"""
测试使用PyO3方式加载库文件

本脚本测试是否可以使用PyO3的方式加载库文件。
"""

import os
import sys
import importlib.util

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def test_load_explanation_lib():
    """测试加载解释算子库"""
    # 构建库文件路径
    lib_path = os.path.join(os.path.dirname(__file__), "..", "src", "operators", "explanation", "libexplanation_v24.so")
    lib_path = os.path.abspath(lib_path)

    print(f"尝试加载库文件: {lib_path}")
    print(f"文件是否存在: {os.path.exists(lib_path)}")

    try:
        # 使用importlib.util加载动态库
        spec = importlib.util.spec_from_file_location("explanation_v24", lib_path)
        if spec is None:
            raise ImportError(f"无法创建模块规范: {lib_path}")

        explanation_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(explanation_module)

        print(f"成功加载库 {lib_path}")
        print(f"模块对象: {explanation_module}")

        # 尝试获取模块中的属性
        attributes = []
        for attr in dir(explanation_module):
            if not attr.startswith('_'):
                attributes.append(attr)

        print(f"模块中的属性: {attributes}")

        return True
    except Exception as e:
        print(f"错误：无法加载库 {lib_path}: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_load_verification_lib():
    """测试加载验证算子库"""
    # 构建库文件路径
    lib_path = os.path.join(os.path.dirname(__file__), "..", "src", "operators", "verification", "libverifiability_v24.so")
    lib_path = os.path.abspath(lib_path)

    print(f"尝试加载库文件: {lib_path}")
    print(f"文件是否存在: {os.path.exists(lib_path)}")

    try:
        # 使用importlib.util加载动态库
        spec = importlib.util.spec_from_file_location("verifiability_v24", lib_path)
        if spec is None:
            raise ImportError(f"无法创建模块规范: {lib_path}")

        verification_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(verification_module)

        print(f"成功加载库 {lib_path}")
        print(f"模块对象: {verification_module}")

        # 尝试获取模块中的属性
        attributes = []
        for attr in dir(verification_module):
            if not attr.startswith('_'):
                attributes.append(attr)

        print(f"模块中的属性: {attributes}")

        return True
    except Exception as e:
        print(f"错误：无法加载库 {lib_path}: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("测试加载Rust库...")
    explanation_success = test_load_explanation_lib()
    print("\n" + "-" * 50 + "\n")
    verification_success = test_load_verification_lib()

    if explanation_success and verification_success:
        print("\n所有库加载成功！")
        sys.exit(0)
    else:
        print("\n库加载失败！")
        sys.exit(1)
