"""
故障恢复算子测试

本测试脚本测试故障恢复算子的基本功能。
"""

import sys
import os
import unittest
import time
from unittest.mock import MagicMock, patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入故障恢复算子
from src.transcendental_tensor.performance_optimization.fault_recovery import (
    NodeState, ServiceState, FailureType, RecoveryStrategy, RecoveryStatus,
    HealthStatus, FailureEvent, RecoveryPlan,
    HeartbeatDetector, MetricThresholdDetector,
    RestartStrategy, FailoverStrategy,
    FaultRecoveryManager
)


class TestFaultRecovery(unittest.TestCase):
    """故障恢复算子测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建故障恢复管理器
        self.manager = FaultRecoveryManager(
            auto_recovery=False,  # 禁用自动恢复，以便手动测试
            strategy_selection="priority",
            max_concurrent_recoveries=2
        )
        
        # 启动管理器
        self.manager.start()
        
        # 初始化节点和服务
        self.node_id = "test-node"
        self.service_id = "test-service"
        
        self.manager.update_node_heartbeat(self.node_id)
        self.manager.update_service_heartbeat(self.service_id)
    
    def tearDown(self):
        """清理测试环境"""
        # 停止管理器
        self.manager.stop()
    
    def test_heartbeat_detection(self):
        """测试心跳检测"""
        # 获取初始健康状态
        node_health = self.manager.get_node_health(self.node_id)
        self.assertIsNotNone(node_health)
        self.assertEqual(node_health.state, NodeState.ONLINE)
        
        # 模拟心跳超时
        detector = HeartbeatDetector(timeout=1.0, suspect_timeout=0.5)
        
        # 等待超时
        time.sleep(1.5)
        
        # 检查故障
        is_failure, failure_type = detector.check(node_health)
        
        # 验证结果
        self.assertTrue(is_failure)
        self.assertEqual(failure_type, FailureType.TIMEOUT)
    
    def test_metric_threshold_detection(self):
        """测试指标阈值检测"""
        # 获取初始健康状态
        node_health = self.manager.get_node_health(self.node_id)
        self.assertIsNotNone(node_health)
        
        # 更新指标
        node_health.metrics = {
            "cpu_usage": 96.0,
            "memory_usage": 85.0,
            "disk_usage": 75.0
        }
        
        # 创建指标阈值检测器
        detector = MetricThresholdDetector(
            thresholds={"cpu_usage": 95.0, "memory_usage": 95.0, "disk_usage": 95.0},
            failure_type=FailureType.PERFORMANCE
        )
        
        # 检查故障
        is_failure, failure_type = detector.check(node_health)
        
        # 验证结果
        self.assertTrue(is_failure)
        self.assertEqual(failure_type, FailureType.PERFORMANCE)
    
    def test_restart_strategy(self):
        """测试重启策略"""
        # 创建故障事件
        event = FailureEvent(
            entity_id=self.node_id,
            entity_type="node",
            failure_type=FailureType.TIMEOUT
        )
        
        # 获取健康状态
        health_status = self.manager.get_node_health(self.node_id)
        self.assertIsNotNone(health_status)
        
        # 创建重启策略
        strategy = RestartStrategy()
        
        # 创建恢复计划
        plan = strategy.create_plan(event, health_status)
        
        # 验证计划
        self.assertIsNotNone(plan)
        self.assertEqual(plan.entity_id, self.node_id)
        self.assertEqual(plan.entity_type, "node")
        self.assertEqual(plan.strategy, RecoveryStrategy.RESTART)
        self.assertEqual(len(plan.steps), 2)
        self.assertEqual(plan.steps[0].step_type, "restart")
        self.assertEqual(plan.steps[1].step_type, "verify")
    
    def test_failover_strategy(self):
        """测试故障转移策略"""
        # 创建故障事件
        event = FailureEvent(
            entity_id=self.service_id,
            entity_type="service",
            failure_type=FailureType.TIMEOUT
        )
        
        # 获取健康状态
        health_status = self.manager.get_service_health(self.service_id)
        self.assertIsNotNone(health_status)
        
        # 设置当前节点
        health_status.metadata["node_id"] = "source-node"
        
        # 创建故障转移策略
        strategy = FailoverStrategy(
            get_backup_nodes=lambda service_id: ["backup-node-1", "backup-node-2"]
        )
        
        # 创建恢复计划
        plan = strategy.create_plan(event, health_status)
        
        # 验证计划
        self.assertIsNotNone(plan)
        self.assertEqual(plan.entity_id, self.service_id)
        self.assertEqual(plan.entity_type, "service")
        self.assertEqual(plan.strategy, RecoveryStrategy.FAILOVER)
        self.assertEqual(len(plan.steps), 2)
        self.assertEqual(plan.steps[0].step_type, "failover")
        self.assertEqual(plan.steps[1].step_type, "verify")
        self.assertEqual(plan.steps[0].parameters["source_node"], "source-node")
        self.assertEqual(plan.steps[0].parameters["target_node"], "backup-node-1")
    
    def test_recovery_executor(self):
        """测试恢复执行器"""
        # 创建故障事件
        event = FailureEvent(
            entity_id=self.node_id,
            entity_type="node",
            failure_type=FailureType.TIMEOUT
        )
        
        # 获取健康状态
        health_status = self.manager.get_node_health(self.node_id)
        self.assertIsNotNone(health_status)
        
        # 创建重启策略
        strategy = RestartStrategy()
        
        # 注册策略
        self.manager.recovery_executor.register_strategy(strategy)
        
        # 创建恢复计划
        plan = strategy.create_plan(event, health_status)
        self.assertIsNotNone(plan)
        
        # 提交计划
        result = self.manager.recovery_executor.submit_plan(plan)
        self.assertTrue(result)
        
        # 验证计划已提交
        plans = self.manager.recovery_executor.get_plans()
        self.assertEqual(len(plans), 1)
        self.assertEqual(plans[0].plan_id, plan.plan_id)
    
    def test_fault_recovery_manager(self):
        """测试故障恢复管理器"""
        # 创建回调函数
        node_failure_callback = MagicMock()
        service_failure_callback = MagicMock()
        plan_completed_callback = MagicMock()
        
        # 注册回调函数
        self.manager.detection_manager.register_callback("node_failure", node_failure_callback)
        self.manager.detection_manager.register_callback("service_failure", service_failure_callback)
        self.manager.recovery_executor.register_callback("plan_completed", plan_completed_callback)
        
        # 手动恢复节点
        plan = self.manager.recover_entity("node", self.node_id, RecoveryStrategy.RESTART)
        
        # 验证计划
        self.assertIsNotNone(plan)
        self.assertEqual(plan.entity_id, self.node_id)
        self.assertEqual(plan.entity_type, "node")
        self.assertEqual(plan.strategy, RecoveryStrategy.RESTART)
        
        # 获取恢复计划
        plans = self.manager.get_recovery_plans()
        self.assertEqual(len(plans), 1)
        self.assertEqual(plans[0].plan_id, plan.plan_id)


if __name__ == "__main__":
    unittest.main()
