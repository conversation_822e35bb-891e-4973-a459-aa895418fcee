"""
图形处理测试脚本

本脚本测试图形处理模块的基本功能。
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt
import importlib.util

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入图形处理模块
from src.graphics_processing import (
    ColorSpace, InterpolationMethod,
    Image, ImageFactory,
    BlurFilter, EdgeDetectionFilter,
    ResizeTransform, RotateTransform,
    PipelineProcessor, FeatureExtractionProcessor,
    CV2_AVAILABLE, PIL_AVAILABLE
)

if CV2_AVAILABLE:
    from src.graphics_processing import OpenCVInterface

if PIL_AVAILABLE:
    from src.graphics_processing import PILInterface

def create_test_image(width: int = 300, height: int = 200) -> Image:
    """创建测试图像"""
    # 创建渐变图像
    x = np.linspace(0, 1, width)
    y = np.linspace(0, 1, height)
    xx, yy = np.meshgrid(x, y)
    
    # 创建RGB通道
    r = xx
    g = yy
    b = np.abs(xx - yy)
    
    # 合并通道
    data = np.stack([r, g, b], axis=-1)
    
    # 转换为uint8
    data = (data * 255).astype(np.uint8)
    
    # 创建图像
    return Image(data, ColorSpace.RGB)

def create_test_pattern(width: int = 300, height: int = 200) -> Image:
    """创建测试图案"""
    # 创建棋盘图案
    x = np.linspace(0, 10, width)
    y = np.linspace(0, 10, height)
    xx, yy = np.meshgrid(x, y)
    
    # 创建棋盘
    pattern = np.sin(xx) * np.sin(yy)
    
    # 二值化
    pattern = (pattern > 0).astype(np.uint8) * 255
    
    # 创建图像
    return Image(pattern, ColorSpace.GRAY)

def save_image(image: Image, file_path: str):
    """保存图像"""
    # 创建目录
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    # 保存图像
    ImageFactory.save_to_file(image, file_path)

def test_image_creation():
    """测试图像创建"""
    print("\n测试图像创建...")
    
    # 创建空图像
    empty_image = ImageFactory.create_empty(100, 100, 3, ColorSpace.RGB)
    print(f"空图像: {empty_image}")
    
    # 创建测试图像
    test_image = create_test_image()
    print(f"测试图像: {test_image}")
    
    # 创建测试图案
    test_pattern = create_test_pattern()
    print(f"测试图案: {test_pattern}")
    
    # 保存图像
    save_image(test_image, "output/test_image.png")
    save_image(test_pattern, "output/test_pattern.png")
    
    print("图像创建测试完成")

def test_blur_filter():
    """测试模糊滤镜"""
    print("\n测试模糊滤镜...")
    
    # 创建测试图像
    test_image = create_test_image()
    
    # 创建模糊滤镜
    gaussian_blur = BlurFilter(kernel_size=5, sigma=1.0, method="gaussian")
    box_blur = BlurFilter(kernel_size=5, method="box")
    median_blur = BlurFilter(kernel_size=5, method="median")
    
    # 应用滤镜
    gaussian_result = gaussian_blur.apply(test_image)
    box_result = box_blur.apply(test_image)
    median_result = median_blur.apply(test_image)
    
    # 保存结果
    save_image(gaussian_result, "output/gaussian_blur.png")
    save_image(box_result, "output/box_blur.png")
    save_image(median_result, "output/median_blur.png")
    
    print("模糊滤镜测试完成")

def test_edge_detection_filter():
    """测试边缘检测滤镜"""
    print("\n测试边缘检测滤镜...")
    
    # 创建测试图案
    test_pattern = create_test_pattern()
    
    # 创建边缘检测滤镜
    sobel_edge = EdgeDetectionFilter(method="sobel", threshold=50.0)
    canny_edge = EdgeDetectionFilter(method="canny", threshold=100.0)
    laplacian_edge = EdgeDetectionFilter(method="laplacian", threshold=50.0)
    prewitt_edge = EdgeDetectionFilter(method="prewitt", threshold=50.0)
    
    # 应用滤镜
    sobel_result = sobel_edge.apply(test_pattern)
    canny_result = canny_edge.apply(test_pattern)
    laplacian_result = laplacian_edge.apply(test_pattern)
    prewitt_result = prewitt_edge.apply(test_pattern)
    
    # 保存结果
    save_image(sobel_result, "output/sobel_edge.png")
    save_image(canny_result, "output/canny_edge.png")
    save_image(laplacian_result, "output/laplacian_edge.png")
    save_image(prewitt_result, "output/prewitt_edge.png")
    
    print("边缘检测滤镜测试完成")

def test_resize_transform():
    """测试调整大小变换"""
    print("\n测试调整大小变换...")
    
    # 创建测试图像
    test_image = create_test_image()
    
    # 创建调整大小变换
    resize_smaller = ResizeTransform(width=150, height=100, interpolation=InterpolationMethod.BILINEAR)
    resize_larger = ResizeTransform(width=600, height=400, interpolation=InterpolationMethod.BILINEAR)
    resize_scale = ResizeTransform(scale=0.5, interpolation=InterpolationMethod.BILINEAR)
    
    # 应用变换
    smaller_result = resize_smaller.apply(test_image)
    larger_result = resize_larger.apply(test_image)
    scale_result = resize_scale.apply(test_image)
    
    # 保存结果
    save_image(smaller_result, "output/resize_smaller.png")
    save_image(larger_result, "output/resize_larger.png")
    save_image(scale_result, "output/resize_scale.png")
    
    print("调整大小变换测试完成")

def test_rotate_transform():
    """测试旋转变换"""
    print("\n测试旋转变换...")
    
    # 创建测试图像
    test_image = create_test_image()
    
    # 创建旋转变换
    rotate_45 = RotateTransform(angle=45, interpolation=InterpolationMethod.BILINEAR)
    rotate_90 = RotateTransform(angle=90, interpolation=InterpolationMethod.BILINEAR)
    rotate_180 = RotateTransform(angle=180, interpolation=InterpolationMethod.BILINEAR)
    
    # 应用变换
    rotate_45_result = rotate_45.apply(test_image)
    rotate_90_result = rotate_90.apply(test_image)
    rotate_180_result = rotate_180.apply(test_image)
    
    # 保存结果
    save_image(rotate_45_result, "output/rotate_45.png")
    save_image(rotate_90_result, "output/rotate_90.png")
    save_image(rotate_180_result, "output/rotate_180.png")
    
    print("旋转变换测试完成")

def test_pipeline_processor():
    """测试管道处理器"""
    print("\n测试管道处理器...")
    
    # 创建测试图像
    test_image = create_test_image()
    
    # 创建处理步骤
    blur = BlurFilter(kernel_size=5, sigma=1.0, method="gaussian")
    resize = ResizeTransform(scale=0.5, interpolation=InterpolationMethod.BILINEAR)
    rotate = RotateTransform(angle=45, interpolation=InterpolationMethod.BILINEAR)
    
    # 创建管道处理器
    pipeline = PipelineProcessor(steps=[blur, resize, rotate])
    
    # 应用处理器
    result = pipeline.process(test_image)
    
    # 保存结果
    save_image(result, "output/pipeline_result.png")
    
    print("管道处理器测试完成")

def test_feature_extraction_processor():
    """测试特征提取处理器"""
    print("\n测试特征提取处理器...")
    
    # 创建测试图案
    test_pattern = create_test_pattern()
    
    # 创建特征提取处理器
    harris_extractor = FeatureExtractionProcessor(method="harris")
    
    # 应用处理器
    result = harris_extractor.process(test_pattern)
    
    # 保存结果
    save_image(result, "output/harris_features.png")
    
    # 如果OpenCV可用，测试更多特征提取方法
    if CV2_AVAILABLE:
        # 创建特征提取处理器
        sift_extractor = FeatureExtractionProcessor(method="sift")
        orb_extractor = FeatureExtractionProcessor(method="orb")
        
        # 应用处理器
        sift_result = sift_extractor.process(test_pattern)
        orb_result = orb_extractor.process(test_pattern)
        
        # 保存结果
        save_image(sift_result, "output/sift_features.png")
        save_image(orb_result, "output/orb_features.png")
    
    print("特征提取处理器测试完成")

def test_opencv_interface():
    """测试OpenCV接口"""
    if not CV2_AVAILABLE:
        print("\nOpenCV未安装，跳过测试")
        return
    
    print("\n测试OpenCV接口...")
    
    # 创建测试图像
    test_image = create_test_image()
    
    # 转换为OpenCV图像
    cv_image = OpenCVInterface.to_cv_image(test_image)
    
    # 应用OpenCV滤镜
    blur_result = OpenCVInterface.apply_filter(test_image, "blur", ksize=5, sigma=1.0)
    canny_result = OpenCVInterface.apply_filter(test_image, "canny", threshold1=100, threshold2=200)
    
    # 应用OpenCV变换
    resize_result = OpenCVInterface.apply_transform(test_image, "resize", width=150, height=100)
    rotate_result = OpenCVInterface.apply_transform(test_image, "rotate", angle=45)
    
    # 保存结果
    save_image(blur_result, "output/opencv_blur.png")
    save_image(canny_result, "output/opencv_canny.png")
    save_image(resize_result, "output/opencv_resize.png")
    save_image(rotate_result, "output/opencv_rotate.png")
    
    # 检测特征
    keypoints, descriptors = OpenCVInterface.detect_features(test_image, "sift")
    
    # 绘制特征
    features_result = OpenCVInterface.draw_features(test_image, keypoints)
    
    # 保存结果
    save_image(features_result, "output/opencv_features.png")
    
    print("OpenCV接口测试完成")

def main():
    """主函数"""
    print("开始测试图形处理模块...")
    
    # 创建输出目录
    os.makedirs("output", exist_ok=True)
    
    # 测试图像创建
    test_image_creation()
    
    # 测试模糊滤镜
    test_blur_filter()
    
    # 测试边缘检测滤镜
    test_edge_detection_filter()
    
    # 测试调整大小变换
    test_resize_transform()
    
    # 测试旋转变换
    test_rotate_transform()
    
    # 测试管道处理器
    test_pipeline_processor()
    
    # 测试特征提取处理器
    test_feature_extraction_processor()
    
    # 测试OpenCV接口
    test_opencv_interface()
    
    print("\n所有测试完成")

if __name__ == "__main__":
    main()
