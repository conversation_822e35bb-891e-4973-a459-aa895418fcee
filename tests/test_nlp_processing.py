"""
自然语言处理测试脚本

本脚本测试自然语言处理模块的基本功能。
"""

import sys
import os
import importlib.util
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入自然语言处理模块
from src.nlp_processing import (
    Language, TokenType, PartOfSpeech, NamedEntityType,
    Token, Span, Document,
    SimpleTokenizer, RegexTokenizer,
    LowercaseProcessor, StopwordsProcessor, PipelineProcessor,
    BagOfWordsExtractor, TFIDFExtractor,
    ClassifierModel, SentimentModel,
    DocumentFactory,
    SPACY_AVAILABLE, NLTK_AVAILABLE, TRANSFORMERS_AVAILABLE
)

if SPACY_AVAILABLE:
    from src.nlp_processing import SpacyInterface, SpacyTokenizer

def test_tokenization():
    """测试分词"""
    print("\n测试分词...")
    
    # 测试文本
    text = "Hello, world! This is a test. The quick brown fox jumps over the lazy dog."
    
    # 创建简单分词器
    simple_tokenizer = SimpleTokenizer()
    
    # 分词
    tokens = simple_tokenizer.tokenize(text)
    
    # 打印标记
    print(f"简单分词器标记数: {len(tokens)}")
    for i, token in enumerate(tokens[:10]):
        print(f"  标记 {i}: {token}")
    
    # 创建正则表达式分词器
    regex_tokenizer = RegexTokenizer()
    
    # 分词
    tokens = regex_tokenizer.tokenize(text)
    
    # 打印标记
    print(f"正则表达式分词器标记数: {len(tokens)}")
    for i, token in enumerate(tokens[:10]):
        print(f"  标记 {i}: {token}")
    
    # 如果spaCy可用，测试spaCy分词器
    if SPACY_AVAILABLE:
        try:
            # 创建spaCy分词器
            spacy_tokenizer = SpacyTokenizer()
            
            # 分词
            tokens = spacy_tokenizer.tokenize(text)
            
            # 打印标记
            print(f"spaCy分词器标记数: {len(tokens)}")
            for i, token in enumerate(tokens[:10]):
                print(f"  标记 {i}: {token}")
        except Exception as e:
            print(f"spaCy分词器测试失败: {e}")
    
    print("分词测试完成")

def test_document_creation():
    """测试文档创建"""
    print("\n测试文档创建...")
    
    # 测试文本
    text = "Hello, world! This is a test. The quick brown fox jumps over the lazy dog."
    
    # 创建简单分词器
    tokenizer = SimpleTokenizer()
    
    # 创建文档
    document = DocumentFactory.create_document(text, tokenizer)
    
    # 打印文档信息
    print(f"文档: {document}")
    print(f"标记数: {document.num_tokens}")
    print(f"文本长度: {document.length}")
    
    # 打印标记
    print("标记:")
    for i, token in enumerate(document.tokens[:10]):
        print(f"  标记 {i}: {token}")
    
    print("文档创建测试完成")

def test_text_processing():
    """测试文本处理"""
    print("\n测试文本处理...")
    
    # 测试文本
    text = "Hello, world! This is a test. The quick brown fox jumps over the lazy dog."
    
    # 创建文档
    document = DocumentFactory.create_document(text, SimpleTokenizer())
    
    # 创建小写处理器
    lowercase_processor = LowercaseProcessor()
    
    # 处理文档
    lowercase_document = lowercase_processor.process(document)
    
    # 打印处理结果
    print("原始文档:")
    for i, token in enumerate(document.tokens[:10]):
        print(f"  标记 {i}: {token}")
    
    print("小写处理后:")
    for i, token in enumerate(lowercase_document.tokens[:10]):
        print(f"  标记 {i}: {token}")
    
    # 创建停用词处理器
    stopwords_processor = StopwordsProcessor()
    
    # 处理文档
    stopwords_document = stopwords_processor.process(lowercase_document)
    
    # 打印处理结果
    print("停用词处理后:")
    for i, token in enumerate(stopwords_document.tokens[:10]):
        print(f"  标记 {i}: {token}")
    
    # 创建管道处理器
    pipeline = PipelineProcessor(processors=[lowercase_processor, stopwords_processor])
    
    # 处理文档
    pipeline_document = pipeline.process(document)
    
    # 打印处理结果
    print("管道处理后:")
    for i, token in enumerate(pipeline_document.tokens[:10]):
        print(f"  标记 {i}: {token}")
    
    print("文本处理测试完成")

def test_feature_extraction():
    """测试特征提取"""
    print("\n测试特征提取...")
    
    # 测试文本
    text = "Hello, world! This is a test. The quick brown fox jumps over the lazy dog."
    
    # 创建文档
    document = DocumentFactory.create_document(text, SimpleTokenizer())
    
    # 创建词袋模型特征提取器
    bow_extractor = BagOfWordsExtractor()
    
    # 提取特征
    bow_features = bow_extractor.extract(document)
    
    # 打印特征
    print("词袋模型特征:")
    print(f"  特征类型: {bow_features['type']}")
    print(f"  词汇表大小: {bow_features['vocabulary_size']}")
    print("  特征:")
    for word, count in list(bow_features['features'].items())[:10]:
        print(f"    {word}: {count}")
    
    # 创建TF-IDF特征提取器
    tfidf_extractor = TFIDFExtractor()
    
    # 提取特征
    tfidf_features = tfidf_extractor.extract(document)
    
    # 打印特征
    print("TF-IDF特征:")
    print(f"  特征类型: {tfidf_features['type']}")
    print(f"  词汇表大小: {tfidf_features['vocabulary_size']}")
    print("  特征:")
    for word, value in list(tfidf_features['features'].items())[:10]:
        print(f"    {word}: {value}")
    
    print("特征提取测试完成")

def test_sentiment_analysis():
    """测试情感分析"""
    print("\n测试情感分析...")
    
    # 测试文本
    positive_text = "I love this product! It's amazing and works great."
    negative_text = "This is terrible. I hate it and it doesn't work at all."
    neutral_text = "The product arrived yesterday. It's a blue shirt."
    
    # 创建文档
    positive_doc = DocumentFactory.create_document(positive_text, SimpleTokenizer())
    negative_doc = DocumentFactory.create_document(negative_text, SimpleTokenizer())
    neutral_doc = DocumentFactory.create_document(neutral_text, SimpleTokenizer())
    
    # 创建情感分析模型
    sentiment_model = SentimentModel(model_type="rule_based")
    
    # 分析情感
    positive_result = sentiment_model.predict(positive_doc)
    negative_result = sentiment_model.predict(negative_doc)
    neutral_result = sentiment_model.predict(neutral_doc)
    
    # 打印结果
    print("规则基础情感分析:")
    print(f"  积极文本: {positive_result}")
    print(f"  消极文本: {negative_result}")
    print(f"  中性文本: {neutral_result}")
    
    # 创建词典基础情感分析模型
    lexicon_model = SentimentModel(model_type="lexicon_based")
    
    # 分析情感
    positive_result = lexicon_model.predict(positive_doc)
    negative_result = lexicon_model.predict(negative_doc)
    neutral_result = lexicon_model.predict(neutral_doc)
    
    # 打印结果
    print("词典基础情感分析:")
    print(f"  积极文本: {positive_result}")
    print(f"  消极文本: {negative_result}")
    print(f"  中性文本: {neutral_result}")
    
    print("情感分析测试完成")

def test_spacy_interface():
    """测试spaCy接口"""
    if not SPACY_AVAILABLE:
        print("\nspaCy未安装，跳过测试")
        return
    
    print("\n测试spaCy接口...")
    
    try:
        # 测试文本
        text = "Apple Inc. is planning to open a new store in New York City. The company's CEO, Tim Cook, announced this on Monday."
        
        # 处理文本
        spacy_doc = SpacyInterface.process_text(text)
        
        # 转换为文档
        document = SpacyInterface.to_document(spacy_doc)
        
        # 打印文档信息
        print(f"文档: {document}")
        print(f"标记数: {document.num_tokens}")
        print(f"跨度数: {document.num_spans}")
        
        # 打印标记
        print("标记:")
        for i, token in enumerate(document.tokens[:10]):
            print(f"  标记 {i}: {token}")
        
        # 打印命名实体
        print("命名实体:")
        for i, span in enumerate(document.spans):
            print(f"  实体 {i}: {span}")
    except Exception as e:
        print(f"spaCy接口测试失败: {e}")
    
    print("spaCy接口测试完成")

def main():
    """主函数"""
    print("开始测试自然语言处理模块...")
    
    # 测试分词
    test_tokenization()
    
    # 测试文档创建
    test_document_creation()
    
    # 测试文本处理
    test_text_processing()
    
    # 测试特征提取
    test_feature_extraction()
    
    # 测试情感分析
    test_sentiment_analysis()
    
    # 测试spaCy接口
    test_spacy_interface()
    
    print("\n所有测试完成")

if __name__ == "__main__":
    main()
