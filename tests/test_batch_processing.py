"""
任务批处理测试模块

本模块测试任务批处理功能，包括任务批次、批处理调度器和批处理执行器。
"""

import unittest
import time
import threading
from typing import Any, Dict, List
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.core.distributed.task.task import Task
from src.core.distributed.task.task_types import TaskType, TaskPriority, TaskState
from src.core.distributed.task.task_batch import TaskBatch, BatchState, BatchStrategy
from src.core.distributed.scheduler.batch_scheduler import BatchScheduler, BatchingPolicy
from src.core.distributed.executor.batch_executor import BatchExecutor, ExecutionMode

class TestTaskBatch(unittest.TestCase):
    """测试任务批次"""

    def test_create_batch(self):
        """测试创建批次"""
        batch = TaskBatch(
            batch_id="test-batch",
            strategy=BatchStrategy.SAME_TYPE,
            max_size=10,
            max_wait_time=1.0,
            priority=TaskPriority.NORMAL
        )

        self.assertEqual(batch.id, "test-batch")
        self.assertEqual(batch.strategy, BatchStrategy.SAME_TYPE)
        self.assertEqual(batch.max_size, 10)
        self.assertEqual(batch.max_wait_time, 1.0)
        self.assertEqual(batch.priority, TaskPriority.NORMAL)
        self.assertEqual(batch.state, BatchState.CREATED)
        self.assertEqual(len(batch.tasks), 0)
        self.assertTrue(batch.is_empty())
        self.assertFalse(batch.is_full())

    def test_add_task(self):
        """测试添加任务"""
        batch = TaskBatch(
            batch_id="test-batch",
            strategy=BatchStrategy.SAME_TYPE,
            max_size=3
        )

        task1 = Task(task_id="task1", task_type=TaskType.COMPUTE)
        task2 = Task(task_id="task2", task_type=TaskType.COMPUTE)
        task3 = Task(task_id="task3", task_type=TaskType.COMPUTE)
        task4 = Task(task_id="task4", task_type=TaskType.STORAGE)

        # 添加任务
        self.assertTrue(batch.add_task(task1))
        self.assertEqual(batch.state, BatchState.FILLING)
        self.assertEqual(len(batch.tasks), 1)
        self.assertFalse(batch.is_empty())
        self.assertFalse(batch.is_full())

        # 添加相同类型的任务
        self.assertTrue(batch.add_task(task2))
        self.assertEqual(len(batch.tasks), 2)

        # 添加不同类型的任务（应该失败）
        self.assertFalse(batch.add_task(task4))
        self.assertEqual(len(batch.tasks), 2)

        # 添加任务直到批次满
        self.assertTrue(batch.add_task(task3))
        self.assertEqual(len(batch.tasks), 3)
        self.assertTrue(batch.is_full())
        self.assertEqual(batch.state, BatchState.READY)

        # 批次已满，添加任务应该失败
        task5 = Task(task_id="task5", task_type=TaskType.COMPUTE)
        self.assertFalse(batch.add_task(task5))

    def test_remove_task(self):
        """测试移除任务"""
        batch = TaskBatch(
            batch_id="test-batch",
            strategy=BatchStrategy.SAME_TYPE
        )

        task1 = Task(task_id="task1", task_type=TaskType.COMPUTE)
        task2 = Task(task_id="task2", task_type=TaskType.COMPUTE)

        # 添加任务
        batch.add_task(task1)
        batch.add_task(task2)
        self.assertEqual(len(batch.tasks), 2)

        # 移除任务
        self.assertTrue(batch.remove_task("task1"))
        self.assertEqual(len(batch.tasks), 1)
        self.assertIsNone(batch.get_task("task1"))
        self.assertIsNotNone(batch.get_task("task2"))

        # 移除不存在的任务
        self.assertFalse(batch.remove_task("task3"))

    def test_batch_state_transitions(self):
        """测试批次状态转换"""
        batch = TaskBatch(
            batch_id="test-batch",
            strategy=BatchStrategy.SAME_TYPE
        )

        task = Task(task_id="task1", task_type=TaskType.COMPUTE)
        batch.add_task(task)

        # 标记为准备就绪
        self.assertTrue(batch.mark_ready())
        self.assertEqual(batch.state, BatchState.READY)

        # 标记为已调度
        self.assertTrue(batch.mark_scheduled())
        self.assertEqual(batch.state, BatchState.SCHEDULED)
        self.assertIsNotNone(batch.scheduled_at)

        # 标记为运行中
        self.assertTrue(batch.mark_running())
        self.assertEqual(batch.state, BatchState.RUNNING)
        self.assertIsNotNone(batch.started_at)

        # 标记为已完成
        self.assertTrue(batch.mark_completed())
        self.assertEqual(batch.state, BatchState.COMPLETED)
        self.assertIsNotNone(batch.completed_at)

        # 无效的状态转换
        batch = TaskBatch(batch_id="test-batch2")
        self.assertFalse(batch.mark_ready())  # 空批次不能标记为准备就绪

        batch.add_task(Task(task_id="task2"))
        batch.mark_ready()
        self.assertFalse(batch.mark_ready())  # 已经是READY状态，不能再次标记为READY

        self.assertFalse(batch.mark_running())  # 必须先标记为SCHEDULED

class TestBatchScheduler(unittest.TestCase):
    """测试批处理调度器"""

    def test_create_scheduler(self):
        """测试创建调度器"""
        scheduler = BatchScheduler(
            scheduler_id="test-scheduler",
            batching_policy=BatchingPolicy.HYBRID,
            default_batch_size=10,
            default_batch_timeout=1.0
        )

        self.assertEqual(scheduler.scheduler_id, "test-scheduler")
        self.assertEqual(scheduler.batching_policy, BatchingPolicy.HYBRID)
        self.assertEqual(scheduler.default_batch_size, 10)
        self.assertEqual(scheduler.default_batch_timeout, 1.0)
        self.assertEqual(len(scheduler.batches), 0)

    def test_submit_task(self):
        """测试提交任务"""
        scheduler = BatchScheduler(
            scheduler_id="test-scheduler",
            batching_policy=BatchingPolicy.HYBRID,
            default_batch_size=3
        )

        task1 = Task(task_id="task1", task_type=TaskType.COMPUTE)
        task2 = Task(task_id="task2", task_type=TaskType.COMPUTE)
        task3 = Task(task_id="task3", task_type=TaskType.COMPUTE)
        task4 = Task(task_id="task4", task_type=TaskType.STORAGE)

        # 提交任务
        self.assertTrue(scheduler.submit_task(task1))
        self.assertEqual(len(scheduler.batches), 1)
        self.assertEqual(len(scheduler.task_to_batch), 1)

        # 获取批次
        batch_id = scheduler.task_to_batch[task1.id]
        batch = scheduler.get_batch(batch_id)
        self.assertIsNotNone(batch)
        self.assertEqual(batch.get_task_count(), 1)

        # 提交相同类型的任务
        self.assertTrue(scheduler.submit_task(task2))
        self.assertEqual(len(scheduler.batches), 1)  # 应该使用相同的批次
        self.assertEqual(batch.get_task_count(), 2)

        # 提交不同类型的任务
        self.assertTrue(scheduler.submit_task(task4))
        self.assertEqual(len(scheduler.batches), 2)  # 应该创建新批次

        # 提交任务直到批次满
        self.assertTrue(scheduler.submit_task(task3))
        self.assertEqual(batch.get_task_count(), 3)
        self.assertTrue(batch.is_full())
        self.assertEqual(batch.state, BatchState.READY)

    def test_cancel_task(self):
        """测试取消任务"""
        scheduler = BatchScheduler(
            scheduler_id="test-scheduler",
            batching_policy=BatchingPolicy.HYBRID
        )

        task1 = Task(task_id="task1", task_type=TaskType.COMPUTE)
        task2 = Task(task_id="task2", task_type=TaskType.COMPUTE)

        # 提交任务
        scheduler.submit_task(task1)
        scheduler.submit_task(task2)

        # 取消任务
        self.assertTrue(scheduler.cancel_task("task1"))
        self.assertEqual(len(scheduler.task_to_batch), 1)
        self.assertIsNone(scheduler.get_task_batch("task1"))
        self.assertIsNotNone(scheduler.get_task_batch("task2"))

        # 取消不存在的任务
        self.assertFalse(scheduler.cancel_task("task3"))

    def test_get_next_batch(self):
        """测试获取下一个批次"""
        scheduler = BatchScheduler(
            scheduler_id="test-scheduler",
            batching_policy=BatchingPolicy.HYBRID,
            default_batch_size=2
        )

        # 启动调度器
        scheduler.start()

        try:
            task1 = Task(task_id="task1", task_type=TaskType.COMPUTE)
            task2 = Task(task_id="task2", task_type=TaskType.COMPUTE)

            # 提交任务
            scheduler.submit_task(task1)
            scheduler.submit_task(task2)

            # 等待批次准备就绪
            time.sleep(0.5)

            # 获取下一个批次
            batch = scheduler.get_next_batch()
            self.assertIsNotNone(batch)
            self.assertEqual(batch.state, BatchState.SCHEDULED)
            self.assertEqual(batch.get_task_count(), 2)
            self.assertTrue(batch.id in scheduler.running_batches)

        finally:
            # 停止调度器
            scheduler.stop()

    def test_complete_batch(self):
        """测试完成批次"""
        scheduler = BatchScheduler(
            scheduler_id="test-scheduler",
            batching_policy=BatchingPolicy.HYBRID
        )

        task = Task(task_id="task1", task_type=TaskType.COMPUTE)
        scheduler.submit_task(task)

        batch_id = scheduler.task_to_batch[task.id]
        batch = scheduler.get_batch(batch_id)
        batch.mark_ready()
        batch.mark_scheduled()
        batch.mark_running()

        # 完成批次
        result = {"status": "success"}
        self.assertTrue(scheduler.complete_batch(batch_id, result))
        self.assertEqual(batch.state, BatchState.COMPLETED)
        self.assertTrue(batch_id in scheduler.completed_batches)
        self.assertEqual(scheduler.batch_results[batch_id], result)

    def test_fail_batch(self):
        """测试失败批次"""
        scheduler = BatchScheduler(
            scheduler_id="test-scheduler",
            batching_policy=BatchingPolicy.HYBRID
        )

        task = Task(task_id="task1", task_type=TaskType.COMPUTE)
        scheduler.submit_task(task)

        batch_id = scheduler.task_to_batch[task.id]
        batch = scheduler.get_batch(batch_id)
        batch.mark_ready()
        batch.mark_scheduled()

        # 失败批次
        error = "Test error"
        self.assertTrue(scheduler.fail_batch(batch_id, error))
        self.assertEqual(batch.state, BatchState.FAILED)
        self.assertTrue(batch_id in scheduler.failed_batches)
        self.assertEqual(scheduler.batch_results[batch_id], error)

class TestBatchExecutor(unittest.TestCase):
    """测试批处理执行器"""

    def test_create_executor(self):
        """测试创建执行器"""
        scheduler = BatchScheduler(scheduler_id="test-scheduler")
        executor = BatchExecutor(
            executor_id="test-executor",
            batch_scheduler=scheduler,
            execution_mode=ExecutionMode.SEQUENTIAL,
            max_workers=2
        )

        self.assertEqual(executor.executor_id, "test-executor")
        self.assertEqual(executor.batch_scheduler, scheduler)
        self.assertEqual(executor.execution_mode, ExecutionMode.SEQUENTIAL)
        self.assertEqual(executor.max_workers, 2)

    def test_execute_batch_sequential(self):
        """测试顺序执行批次"""
        scheduler = BatchScheduler(scheduler_id="test-scheduler")

        # 定义任务执行函数
        def task_executor(task: Task) -> Dict[str, Any]:
            return {"task_id": task.id, "result": "success"}

        executor = BatchExecutor(
            executor_id="test-executor",
            batch_scheduler=scheduler,
            execution_mode=ExecutionMode.SEQUENTIAL,
            task_executor=task_executor
        )

        # 创建批次
        batch = TaskBatch(batch_id="test-batch")
        task1 = Task(task_id="task1", task_type=TaskType.COMPUTE)
        task2 = Task(task_id="task2", task_type=TaskType.COMPUTE)
        batch.add_task(task1)
        batch.add_task(task2)
        batch.mark_ready()
        batch.mark_scheduled()

        # 执行批次
        result = executor._execute_batch_sequential(batch)

        self.assertEqual(result["batch_id"], "test-batch")
        self.assertEqual(result["task_count"], 2)
        self.assertEqual(result["success_count"], 2)
        self.assertEqual(result["failure_count"], 0)
        self.assertIn("execution_time", result)
        self.assertIn("results", result)
        self.assertEqual(len(result["results"]), 2)
        self.assertTrue(result["results"]["task1"]["success"])
        self.assertTrue(result["results"]["task2"]["success"])

    def test_execute_batch_parallel(self):
        """测试并行执行批次"""
        scheduler = BatchScheduler(scheduler_id="test-scheduler")

        # 定义任务执行函数
        def task_executor(task: Task) -> Dict[str, Any]:
            return {"task_id": task.id, "result": "success"}

        executor = BatchExecutor(
            executor_id="test-executor",
            batch_scheduler=scheduler,
            execution_mode=ExecutionMode.PARALLEL,
            task_executor=task_executor
        )

        # 创建批次
        batch = TaskBatch(batch_id="test-batch")
        task1 = Task(task_id="task1", task_type=TaskType.COMPUTE)
        task2 = Task(task_id="task2", task_type=TaskType.COMPUTE)
        batch.add_task(task1)
        batch.add_task(task2)
        batch.mark_ready()
        batch.mark_scheduled()

        # 执行批次
        result = executor._execute_batch_parallel(batch)

        self.assertEqual(result["batch_id"], "test-batch")
        self.assertEqual(result["task_count"], 2)
        self.assertEqual(result["success_count"], 2)
        self.assertEqual(result["failure_count"], 0)
        self.assertIn("execution_time", result)
        self.assertIn("results", result)
        self.assertEqual(len(result["results"]), 2)
        self.assertTrue(result["results"]["task1"]["success"])
        self.assertTrue(result["results"]["task2"]["success"])

    def test_execute_batch(self):
        """测试执行批次"""
        scheduler = BatchScheduler(scheduler_id="test-scheduler")

        # 定义任务执行函数
        def task_executor(task: Task) -> Dict[str, Any]:
            return {"task_id": task.id, "result": "success"}

        executor = BatchExecutor(
            executor_id="test-executor",
            batch_scheduler=scheduler,
            execution_mode=ExecutionMode.SEQUENTIAL,
            task_executor=task_executor
        )

        # 创建批次
        batch = TaskBatch(batch_id="test-batch")
        task = Task(task_id="task1", task_type=TaskType.COMPUTE)
        batch.add_task(task)
        batch.mark_ready()
        batch.mark_scheduled()

        # 执行批次
        future = executor.execute_batch(batch)
        self.assertEqual(batch.state, BatchState.RUNNING)
        self.assertTrue(batch.id in executor.running_batches)

        # 等待执行完成
        result = future.result()
        self.assertEqual(result["batch_id"], "test-batch")
        self.assertEqual(result["success_count"], 1)

    def test_integration(self):
        """集成测试"""
        # 创建调度器
        scheduler = BatchScheduler(
            scheduler_id="test-scheduler",
            batching_policy=BatchingPolicy.HYBRID,
            default_batch_size=2
        )

        # 定义任务执行函数
        def task_executor(task: Task) -> Dict[str, Any]:
            time.sleep(0.1)  # 模拟任务执行时间
            return {"task_id": task.id, "result": "success"}

        # 创建执行器
        executor = BatchExecutor(
            executor_id="test-executor",
            batch_scheduler=scheduler,
            execution_mode=ExecutionMode.PARALLEL,
            task_executor=task_executor
        )

        # 启动调度器和执行器
        scheduler.start()
        executor.start()

        try:
            # 提交任务
            tasks = []
            for i in range(5):
                task = Task(task_id=f"task{i}", task_type=TaskType.COMPUTE)
                scheduler.submit_task(task)
                tasks.append(task)

            # 等待所有任务完成
            time.sleep(3.0)

            # 验证结果
            for task in tasks:
                batch = scheduler.get_task_batch(task.id)
                self.assertIsNotNone(batch)
                self.assertEqual(batch.state, BatchState.COMPLETED)

            # 检查统计信息
            stats = executor.get_stats()
            self.assertEqual(stats["task_count"], 5)
            self.assertEqual(stats["success_count"], 3)  # 3个批次（2+2+1）

        finally:
            # 停止调度器和执行器
            scheduler.stop()
            executor.stop()

if __name__ == "__main__":
    unittest.main()
