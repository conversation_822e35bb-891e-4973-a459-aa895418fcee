"""
混沌系统稳定控制器测试

本模块包含混沌系统稳定控制器的测试用例。
"""

import unittest
import numpy as np
import os
import sys
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入我们的实现
from src.algorithms.chaos_control import (
    ChaosSystemController,
    ChaoticSystemModel,
    LorenzModel,
    RosslerModel,
    ChuaModel,
    ModelFactory,
    StabilityAnalyzer,
    ControlMethod,
    OGYMethod,
    DelayedFeedbackMethod,
    AdaptiveMethod,
    PredictiveMethod,
    CustomMethod,
    ControlMethodFactory,
    ControlAnalyzer,
    create_test_system,
    calculate_lyapunov_exponents,
    is_chaotic
)


class TestChaosSystemController(unittest.TestCase):
    """混沌系统稳定控制器测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        # 创建测试系统
        self.system_type, self.parameters, self.initial_state, self.target_state = create_test_system(
            system_type='lorenz',
            seed=42
        )
        
        # 创建控制器
        self.controller = ChaosSystemController(
            control_method='ogy',
            stability_threshold=1e-3,
            max_iterations=100,
            time_step=0.01,
            use_parallel=True,
            num_workers=2,
            use_cache=True,
            cache_size=10
        )
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.controller.control_method, 'ogy')
        self.assertEqual(self.controller.stability_threshold, 1e-3)
        self.assertEqual(self.controller.max_iterations, 100)
        self.assertEqual(self.controller.time_step, 0.01)
        self.assertTrue(self.controller.use_parallel)
        self.assertEqual(self.controller.num_workers, 2)
        self.assertTrue(self.controller.use_cache)
        self.assertEqual(self.controller.cache_size, 10)
    
    def test_compute(self):
        """测试计算方法"""
        # 创建输入数据
        input_data = {
            'system': self.system_type,
            'parameters': self.parameters,
            'initial_state': self.initial_state,
            'target_state': self.target_state
        }
        
        # 执行计算
        result = self.controller.compute(input_data)
        
        # 检查结果
        self.assertIn('controlled_states', result)
        self.assertIn('control_signals', result)
        self.assertIn('final_state', result)
        self.assertIn('is_stabilized', result)
        self.assertIn('iterations', result)
        self.assertIn('stability_measures', result)
        self.assertIn('analysis', result)
        self.assertIn('performance', result)
        
        # 检查状态历史
        controlled_states = result['controlled_states']
        self.assertGreater(len(controlled_states), 1)
        self.assertEqual(controlled_states[0].shape, self.initial_state.shape)
        
        # 检查控制信号历史
        control_signals = result['control_signals']
        self.assertEqual(len(control_signals), len(controlled_states))
        self.assertEqual(control_signals[0].shape, self.initial_state.shape)
        
        # 检查最终状态
        final_state = result['final_state']
        self.assertEqual(final_state.shape, self.initial_state.shape)
        
        # 检查稳定性度量历史
        stability_measures = result['stability_measures']
        self.assertEqual(len(stability_measures), len(controlled_states))
        
        # 检查分析结果
        analysis = result['analysis']
        self.assertIn('control_error', analysis)
        self.assertIn('mean_error', analysis)
        self.assertIn('error_history', analysis)
        self.assertIn('control_signal_mean_norm', analysis)
        self.assertIn('control_signal_energy', analysis)
        self.assertIn('stability_measure', analysis)
        self.assertIn('is_stable', analysis)
        self.assertIn('lyapunov_exponents', analysis)
        self.assertIn('control_efficiency', analysis)
        
        # 检查性能指标
        performance = result['performance']
        self.assertIn('total_time', performance)
        self.assertIn('model_construction_time', performance)
        self.assertIn('control_time', performance)
        self.assertIn('analysis_time', performance)
        self.assertIn('memory_usage', performance)
        self.assertIn('num_iterations', performance)
        self.assertIn('control_error', performance)
        self.assertIn('stability_measure', performance)
        self.assertIn('lyapunov_exponents', performance)
    
    def test_different_control_methods(self):
        """测试不同的控制方法"""
        # 创建输入数据
        input_data = {
            'system': self.system_type,
            'parameters': self.parameters,
            'initial_state': self.initial_state,
            'target_state': self.target_state
        }
        
        # 测试不同的控制方法
        control_methods = ['ogy', 'delayed_feedback', 'adaptive', 'predictive']
        
        for method in control_methods:
            # 创建控制器
            controller = ChaosSystemController(
                control_method=method,
                stability_threshold=1e-2,
                max_iterations=50
            )
            
            # 执行计算
            result = controller.compute(input_data)
            
            # 检查结果
            self.assertIn('controlled_states', result)
            self.assertIn('control_signals', result)
            self.assertIn('final_state', result)
            self.assertIn('is_stabilized', result)
            self.assertIn('iterations', result)
            self.assertIn('stability_measures', result)
            self.assertIn('analysis', result)
            self.assertIn('performance', result)
    
    def test_different_systems(self):
        """测试不同的混沌系统"""
        # 测试不同的系统类型
        system_types = ['lorenz', 'rossler', 'chua']
        
        for system_type in system_types:
            # 创建测试系统
            system, parameters, initial_state, target_state = create_test_system(
                system_type=system_type,
                seed=42
            )
            
            # 创建输入数据
            input_data = {
                'system': system,
                'parameters': parameters,
                'initial_state': initial_state,
                'target_state': target_state
            }
            
            # 创建控制器
            controller = ChaosSystemController(
                control_method='ogy',
                stability_threshold=1e-2,
                max_iterations=50
            )
            
            # 执行计算
            result = controller.compute(input_data)
            
            # 检查结果
            self.assertIn('controlled_states', result)
            self.assertIn('control_signals', result)
            self.assertIn('final_state', result)
            self.assertIn('is_stabilized', result)
            self.assertIn('iterations', result)
            self.assertIn('stability_measures', result)
            self.assertIn('analysis', result)
            self.assertIn('performance', result)
    
    def test_model_factory(self):
        """测试模型工厂"""
        # 创建模型工厂
        factory = ModelFactory()
        
        # 创建洛伦兹模型
        lorenz_model = factory.create_lorenz_model(self.parameters)
        self.assertIsInstance(lorenz_model, LorenzModel)
        self.assertEqual(lorenz_model.get_dimension(), 3)
        
        # 创建罗斯勒模型
        rossler_model = factory.create_rossler_model(self.parameters)
        self.assertIsInstance(rossler_model, RosslerModel)
        self.assertEqual(rossler_model.get_dimension(), 3)
        
        # 创建蔡氏模型
        chua_model = factory.create_chua_model(self.parameters)
        self.assertIsInstance(chua_model, ChuaModel)
        self.assertEqual(chua_model.get_dimension(), 3)
    
    def test_stability_analyzer(self):
        """测试稳定性分析器"""
        # 创建模型
        model_factory = ModelFactory()
        model = model_factory.create_model(self.system_type, self.parameters)
        
        # 创建稳定性分析器
        analyzer = StabilityAnalyzer()
        
        # 计算稳定性度量
        stability_measure = analyzer.calculate_stability(model, self.initial_state, self.target_state)
        self.assertIsInstance(stability_measure, float)
        
        # 分析固定点
        fixed_points_analysis = analyzer.analyze_fixed_points(model)
        self.assertIsInstance(fixed_points_analysis, dict)
        self.assertIn('stable_fixed_points', fixed_points_analysis)
        self.assertIn('unstable_fixed_points', fixed_points_analysis)
        self.assertIn('saddle_fixed_points', fixed_points_analysis)
        
        # 计算李雅普诺夫谱
        lyapunov_spectrum = analyzer.calculate_lyapunov_spectrum(model, self.initial_state)
        self.assertIsInstance(lyapunov_spectrum, np.ndarray)
        self.assertEqual(len(lyapunov_spectrum), 3)
    
    def test_control_method_factory(self):
        """测试控制方法工厂"""
        # 创建控制方法工厂
        factory = ControlMethodFactory()
        
        # 创建OGY方法
        ogy_method = factory.create_ogy_method()
        self.assertIsInstance(ogy_method, OGYMethod)
        
        # 创建延迟反馈控制方法
        delayed_feedback_method = factory.create_delayed_feedback_method()
        self.assertIsInstance(delayed_feedback_method, DelayedFeedbackMethod)
        
        # 创建自适应控制方法
        adaptive_method = factory.create_adaptive_method()
        self.assertIsInstance(adaptive_method, AdaptiveMethod)
        
        # 创建预测控制方法
        predictive_method = factory.create_predictive_method()
        self.assertIsInstance(predictive_method, PredictiveMethod)
        
        # 创建自定义控制方法
        def custom_control_function(model, state, target_state, time_step, parameters):
            return np.zeros_like(state)
        
        custom_method = factory.create_custom_method(custom_control_function)
        self.assertIsInstance(custom_method, CustomMethod)
    
    def test_control_analyzer(self):
        """测试控制分析器"""
        # 创建输入数据
        input_data = {
            'system': self.system_type,
            'parameters': self.parameters,
            'initial_state': self.initial_state,
            'target_state': self.target_state
        }
        
        # 执行计算
        result = self.controller.compute(input_data)
        
        # 提取结果
        controlled_states = result['controlled_states']
        control_signals = result['control_signals']
        target_state = self.target_state
        stability_measures = result['stability_measures']
        time_step = self.controller.time_step
        
        # 创建模型
        model_factory = ModelFactory()
        model = model_factory.create_model(self.system_type, self.parameters)
        
        # 创建控制分析器
        analyzer = ControlAnalyzer()
        
        # 分析控制结果
        analysis_result = analyzer.analyze_control(
            model, controlled_states, control_signals, target_state, 
            stability_measures, time_step
        )
        
        # 检查分析结果
        self.assertIsInstance(analysis_result, dict)
        self.assertIn('control_error', analysis_result)
        self.assertIn('mean_error', analysis_result)
        self.assertIn('error_history', analysis_result)
        self.assertIn('control_signal_mean_norm', analysis_result)
        self.assertIn('control_signal_energy', analysis_result)
        self.assertIn('stability_measure', analysis_result)
        self.assertIn('is_stable', analysis_result)
        self.assertIn('lyapunov_exponents', analysis_result)
        self.assertIn('control_efficiency', analysis_result)
    
    def test_get_metadata(self):
        """测试获取元数据"""
        metadata = self.controller.get_metadata()
        
        self.assertIn('name', metadata)
        self.assertIn('version', metadata)
        self.assertIn('description', metadata)
        self.assertIn('parameters', metadata)
        self.assertIn('performance_metrics', metadata)
    
    def test_is_compatible_with(self):
        """测试兼容性检查"""
        # 创建兼容的控制器
        compatible_controller = ChaosSystemController(
            control_method='ogy',
            stability_threshold=1e-4
        )
        
        # 创建不兼容的控制器
        incompatible_controller = ChaosSystemController(
            control_method='adaptive',
            stability_threshold=1e-4
        )
        
        # 检查兼容性
        self.assertTrue(self.controller.is_compatible_with(compatible_controller))
        self.assertFalse(self.controller.is_compatible_with(incompatible_controller))


if __name__ == '__main__':
    unittest.main()
