"""性能基准测试单元测试"""

import unittest
import tempfile
import shutil
from pathlib import Path
import numpy as np
import json

from benchmarks.fusion_benchmarks import FusionBenchmark

class TestFusionBenchmark(unittest.TestCase):
    """测试性能基准测试框架"""
    
    def setUp(self):
        # 创建临时输出目录
        self.test_dir = tempfile.mkdtemp()
        self.benchmark = FusionBenchmark(output_dir=self.test_dir)
        
    def tearDown(self):
        # 清理临时文件
        shutil.rmtree(self.test_dir)
        
    def test_encoding_benchmark(self):
        """测试编码性能测试"""
        # 使用较小的数据集进行测试
        sizes = [10, 20]
        results = self.benchmark.benchmark_encoding(sizes, repeats=2)
        
        # 验证结果格式
        self.assertIn("quantum", results)
        self.assertIn("holographic", results)
        self.assertIn("fractal", results)
        
        # 验证结果数量
        self.assertEqual(len(results["quantum"]), len(sizes))
        
        # 验证时间值的合理性
        for modality in results:
            for time_value in results[modality]:
                self.assertGreater(time_value, 0)
                self.assertLess(time_value, 10)  # 假设不会超过10秒
                
    def test_fusion_benchmark(self):
        """测试融合性能测试"""
        num_modalities = [2, 3]
        results = self.benchmark.benchmark_fusion(
            num_modalities,
            data_size=100,
            repeats=2
        )
        
        # 验证结果
        self.assertEqual(len(results), len(num_modalities))
        for time_value in results:
            self.assertGreater(time_value, 0)
            
    def test_parallel_benchmark(self):
        """测试并行性能测试"""
        num_threads = [1, 2]
        results = self.benchmark.benchmark_parallel_encoding(
            num_threads,
            data_size=100
        )
        
        # 验证结果格式
        self.assertIn("thread", results)
        self.assertIn("process", results)
        
        # 验证结果数量
        self.assertEqual(len(results["thread"]), len(num_threads))
        self.assertEqual(len(results["process"]), len(num_threads))
        
    def test_full_benchmark_run(self):
        """测试完整的基准测试流程"""
        # 运行测试
        self.benchmark.run_all_benchmarks()
        
        # 验证输出文件
        results_file = Path(self.test_dir) / "benchmark_results.json"
        self.assertTrue(results_file.exists())
        
        # 验证图表文件
        expected_plots = [
            "encoding_performance.png",
            "fusion_performance.png",
            "parallel_scaling.png",
            "memory_usage.png"
        ]
        
        for plot_file in expected_plots:
            self.assertTrue(
                (Path(self.test_dir) / plot_file).exists(),
                f"缺少图表文件: {plot_file}"
            )
            
        # 验证结果内容
        with open(results_file) as f:
            results = json.load(f)
            
        self.assertIn("encoding", results)
        self.assertIn("fusion", results)
        self.assertIn("parallel", results)
        self.assertIn("memory", results)
        
    def test_memory_tracking(self):
        """测试内存使用跟踪"""
        # 运行一个小型测试
        sizes = [10]
        self.benchmark.benchmark_encoding(sizes)
        
        # 验证内存指标
        self.assertGreater(len(self.benchmark.metrics["memory_usage"]), 0)
        for mem_usage in self.benchmark.metrics["memory_usage"]:
            self.assertGreater(mem_usage, 0)  # 内存使用应该为正
            
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效的数据大小
        with self.assertRaises(Exception):
            self.benchmark.benchmark_encoding([-1])
            
        # 测试无效的模态数量
        with self.assertRaises(Exception):
            self.benchmark.benchmark_fusion([0])
            
        # 测试无效的线程数
        with self.assertRaises(Exception):
            self.benchmark.benchmark_parallel_encoding([-1])
            
if __name__ == "__main__":
    unittest.main()