"""
量子模拟器测试脚本

本脚本测试量子模拟器的基本功能。
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入量子模拟器模块
from src.quantum_simulator import (
    QuantumSimulatorFactory,
    QuantumCircuitFactory,
    StatevectorSimulator,
    DensityMatrixSimulator,
    QISKIT_AVAILABLE,
    CIRQ_AVAILABLE,
    PENNYLANE_AVAILABLE
)

def test_statevector_simulator():
    """测试态矢量模拟器"""
    print("\n测试态矢量模拟器...")
    
    # 创建模拟器
    simulator = StatevectorSimulator(2)
    
    # 创建Bell态电路
    circuit = QuantumCircuitFactory.create_bell_state_circuit()
    
    # 运行电路
    simulator.run_circuit(circuit)
    
    # 获取态矢量
    statevector = simulator.get_statevector()
    
    # 打印结果
    print(f"Bell态的态矢量: {statevector}")
    
    # 测量结果
    counts = simulator.measure(shots=1024)
    
    # 打印结果
    print(f"测量结果: {counts}")
    
    # 可视化结果
    plt.figure(figsize=(10, 6))
    plt.bar(counts.keys(), counts.values())
    plt.title('Bell State Measurement')
    plt.xlabel('State')
    plt.ylabel('Counts')
    plt.savefig('bell_state_measurement.png')
    plt.close()
    
    print("态矢量模拟器测试完成")

def test_density_matrix_simulator():
    """测试密度矩阵模拟器"""
    print("\n测试密度矩阵模拟器...")
    
    # 创建模拟器
    simulator = DensityMatrixSimulator(2)
    
    # 创建Bell态电路
    circuit = QuantumCircuitFactory.create_bell_state_circuit()
    
    # 运行电路
    simulator.run_circuit(circuit)
    
    # 获取密度矩阵
    density_matrix = simulator.get_density_matrix()
    
    # 打印结果
    print(f"Bell态的密度矩阵:\n{density_matrix}")
    
    # 应用去极化噪声
    simulator.apply_depolarizing_noise(0.1)
    
    # 获取噪声后的密度矩阵
    noisy_density_matrix = simulator.get_density_matrix()
    
    # 打印结果
    print(f"应用噪声后的密度矩阵:\n{noisy_density_matrix}")
    
    # 测量结果
    counts = simulator.measure(shots=1024)
    
    # 打印结果
    print(f"测量结果: {counts}")
    
    # 可视化结果
    plt.figure(figsize=(10, 6))
    plt.bar(counts.keys(), counts.values())
    plt.title('Noisy Bell State Measurement')
    plt.xlabel('State')
    plt.ylabel('Counts')
    plt.savefig('noisy_bell_state_measurement.png')
    plt.close()
    
    print("密度矩阵模拟器测试完成")

def test_qiskit_simulator():
    """测试Qiskit模拟器接口"""
    if not QISKIT_AVAILABLE:
        print("\nQiskit未安装，跳过测试")
        return
    
    print("\n测试Qiskit模拟器接口...")
    
    # 导入Qiskit模拟器
    from src.quantum_simulator import QiskitSimulator
    
    # 创建模拟器
    simulator = QiskitSimulator(2)
    
    # 创建Bell态电路
    circuit = QuantumCircuitFactory.create_bell_state_circuit()
    
    # 运行电路
    simulator.run_circuit(circuit)
    
    # 获取态矢量
    statevector = simulator.get_statevector()
    
    # 打印结果
    print(f"Bell态的态矢量: {statevector}")
    
    # 测量结果
    counts = simulator.measure(shots=1024)
    
    # 打印结果
    print(f"测量结果: {counts}")
    
    # 可视化结果
    plt.figure(figsize=(10, 6))
    plt.bar(counts.keys(), counts.values())
    plt.title('Qiskit Bell State Measurement')
    plt.xlabel('State')
    plt.ylabel('Counts')
    plt.savefig('qiskit_bell_state_measurement.png')
    plt.close()
    
    print("Qiskit模拟器接口测试完成")

def test_cirq_simulator():
    """测试Cirq模拟器接口"""
    if not CIRQ_AVAILABLE:
        print("\nCirq未安装，跳过测试")
        return
    
    print("\n测试Cirq模拟器接口...")
    
    # 导入Cirq模拟器
    from src.quantum_simulator import CirqSimulator
    
    # 创建模拟器
    simulator = CirqSimulator(2)
    
    # 创建Bell态电路
    circuit = QuantumCircuitFactory.create_bell_state_circuit()
    
    # 运行电路
    simulator.run_circuit(circuit)
    
    # 获取态矢量
    statevector = simulator.get_statevector()
    
    # 打印结果
    print(f"Bell态的态矢量: {statevector}")
    
    # 测量结果
    counts = simulator.measure(shots=1024)
    
    # 打印结果
    print(f"测量结果: {counts}")
    
    # 可视化结果
    plt.figure(figsize=(10, 6))
    plt.bar(counts.keys(), counts.values())
    plt.title('Cirq Bell State Measurement')
    plt.xlabel('State')
    plt.ylabel('Counts')
    plt.savefig('cirq_bell_state_measurement.png')
    plt.close()
    
    print("Cirq模拟器接口测试完成")

def test_pennylane_simulator():
    """测试PennyLane模拟器接口"""
    if not PENNYLANE_AVAILABLE:
        print("\nPennyLane未安装，跳过测试")
        return
    
    print("\n测试PennyLane模拟器接口...")
    
    # 导入PennyLane模拟器
    from src.quantum_simulator import PennyLaneSimulator
    
    # 创建模拟器
    simulator = PennyLaneSimulator(2)
    
    # 创建Bell态电路
    circuit = QuantumCircuitFactory.create_bell_state_circuit()
    
    # 运行电路
    simulator.run_circuit(circuit)
    
    # 获取态矢量
    statevector = simulator.get_statevector()
    
    # 打印结果
    print(f"Bell态的态矢量: {statevector}")
    
    # 测量结果
    counts = simulator.measure(shots=1024)
    
    # 打印结果
    print(f"测量结果: {counts}")
    
    # 可视化结果
    plt.figure(figsize=(10, 6))
    plt.bar(counts.keys(), counts.values())
    plt.title('PennyLane Bell State Measurement')
    plt.xlabel('State')
    plt.ylabel('Counts')
    plt.savefig('pennylane_bell_state_measurement.png')
    plt.close()
    
    print("PennyLane模拟器接口测试完成")

def test_ghz_state():
    """测试GHZ态"""
    print("\n测试GHZ态...")
    
    # 创建模拟器
    simulator = StatevectorSimulator(3)
    
    # 创建GHZ态电路
    circuit = QuantumCircuitFactory.create_ghz_state_circuit(3)
    
    # 运行电路
    simulator.run_circuit(circuit)
    
    # 获取态矢量
    statevector = simulator.get_statevector()
    
    # 打印结果
    print(f"GHZ态的态矢量: {statevector}")
    
    # 测量结果
    counts = simulator.measure(shots=1024)
    
    # 打印结果
    print(f"测量结果: {counts}")
    
    # 可视化结果
    plt.figure(figsize=(10, 6))
    plt.bar(counts.keys(), counts.values())
    plt.title('GHZ State Measurement')
    plt.xlabel('State')
    plt.ylabel('Counts')
    plt.savefig('ghz_state_measurement.png')
    plt.close()
    
    print("GHZ态测试完成")

def test_quantum_teleportation():
    """测试量子隐形传态"""
    print("\n测试量子隐形传态...")
    
    # 创建模拟器
    simulator = StatevectorSimulator(3)
    
    # 创建量子隐形传态电路
    circuit = QuantumCircuit(3, name="QuantumTeleportation")
    
    # 准备要传送的状态（第一个量子比特）
    circuit.h(0)
    circuit.p(0, np.pi/4)
    
    # 创建Bell对（第二和第三个量子比特）
    circuit.h(1)
    circuit.cnot(1, 2)
    
    # 执行隐形传态协议
    circuit.cnot(0, 1)
    circuit.h(0)
    
    # 测量前两个量子比特
    circuit.measure([0, 1])
    
    # 根据测量结果应用校正操作（在实际情况下，这需要经典通信）
    # 这里我们模拟所有可能的测量结果
    
    # 运行电路
    simulator.run_circuit(circuit)
    
    # 获取态矢量
    statevector = simulator.get_statevector()
    
    # 打印结果
    print(f"隐形传态后的态矢量: {statevector}")
    
    # 测量第三个量子比特
    counts = simulator.measure([2], shots=1024)
    
    # 打印结果
    print(f"第三个量子比特的测量结果: {counts}")
    
    # 可视化结果
    plt.figure(figsize=(10, 6))
    plt.bar(counts.keys(), counts.values())
    plt.title('Quantum Teleportation')
    plt.xlabel('State')
    plt.ylabel('Counts')
    plt.savefig('quantum_teleportation.png')
    plt.close()
    
    print("量子隐形传态测试完成")

def main():
    """主函数"""
    print("开始测试量子模拟器...")
    
    # 测试态矢量模拟器
    test_statevector_simulator()
    
    # 测试密度矩阵模拟器
    test_density_matrix_simulator()
    
    # 测试Qiskit模拟器接口
    test_qiskit_simulator()
    
    # 测试Cirq模拟器接口
    test_cirq_simulator()
    
    # 测试PennyLane模拟器接口
    test_pennylane_simulator()
    
    # 测试GHZ态
    test_ghz_state()
    
    # 测试量子隐形传态
    test_quantum_teleportation()
    
    print("\n所有测试完成")

if __name__ == "__main__":
    main()
