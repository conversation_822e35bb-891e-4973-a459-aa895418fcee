"""
超越态融合算子测试脚本

本脚本用于测试超越态融合算子的功能和性能。
"""

import sys
import os
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# 导入超越态思维引擎
try:
    # 尝试直接导入
    from src.core.registry import OperatorRegistry
except ImportError:
    # 如果失败，尝试从当前目录导入
    sys.path.insert(0, str(Path(__file__).parent))
    from src.core.registry import OperatorRegistry

# 导入融合算子
try:
    from src.operators.fusion_registry import (
        quantum_superposition_fusion,
        holographic_interference_fusion,
        fractal_fusion,
        topological_fusion,
        fusion_with_method,
        register_fusion_operators
    )

    # 确保融合算子已注册
    register_fusion_operators()
except ImportError as e:
    print(f"导入融合算子失败: {e}")
    sys.exit(1)

def test_fusion_operators_registration():
    """测试融合算子注册"""
    # 获取算子注册表
    registry = OperatorRegistry.get_instance()

    # 检查融合算子是否已注册
    fusion_operators = [
        "fusion.quantum_superposition",
        "fusion.holographic_interference",
        "fusion.fractal_fusion",
        "fusion.topological_fusion",
        "fusion.with_method",
    ]

    for op_name in fusion_operators:
        if registry.has_operator(op_name):
            print(f"算子 {op_name} 已成功注册")
        else:
            print(f"错误: 算子 {op_name} 未注册")

    return all(registry.has_operator(op_name) for op_name in fusion_operators)

def test_fusion_operators_functionality():
    """测试融合算子功能"""
    # 获取算子注册表
    registry = OperatorRegistry.get_instance()

    # 创建测试状态
    state_a = np.array([1.0, 0.0], dtype=np.complex128).tolist()  # |0⟩
    state_b = np.array([0.0, 1.0], dtype=np.complex128).tolist()  # |1⟩

    print("\n测试融合算子功能:")
    print("状态A:", state_a)
    print("状态B:", state_b)
    print()

    # 测试量子叠加融合
    quantum_superposition = registry.get_operator("fusion.quantum_superposition")
    result_qs = quantum_superposition(state_a, state_b)
    print("量子叠加融合结果:", result_qs)
    print("结果概率:", [abs(c)**2 for c in result_qs])
    print()

    # 测试全息干涉融合
    holographic_interference = registry.get_operator("fusion.holographic_interference")
    result_hi = holographic_interference(state_a, state_b)
    print("全息干涉融合结果:", result_hi)
    print("结果概率:", [abs(c)**2 for c in result_hi])
    print()

    # 测试分形融合
    fractal_fusion = registry.get_operator("fusion.fractal_fusion")
    result_ff = fractal_fusion(state_a, state_b)
    print("分形融合结果:", result_ff)
    print("结果概率:", [abs(c)**2 for c in result_ff])
    print()

    # 测试拓扑融合
    topological_fusion = registry.get_operator("fusion.topological_fusion")
    result_tf = topological_fusion(state_a, state_b)
    print("拓扑融合结果:", result_tf)
    print("结果概率:", [abs(c)**2 for c in result_tf])
    print()

    # 测试通用融合算子
    fusion_with_method = registry.get_operator("fusion.with_method")

    methods = [
        "quantum_superposition",
        "holographic_interference",
        "fractal_fusion",
        "topological_fusion",
    ]

    for method in methods:
        result = fusion_with_method(state_a, state_b, method)
        print(f"通用融合算子 ({method}) 结果:", result)
        print("结果概率:", [abs(c)**2 for c in result])
        print()

    return True

def test_fusion_operators_performance():
    """测试融合算子性能"""
    # 获取算子注册表
    registry = OperatorRegistry.get_instance()

    # 创建大型测试状态
    dim = 1000
    state_a = np.zeros(dim, dtype=np.complex128)
    state_b = np.zeros(dim, dtype=np.complex128)

    # 初始化状态
    state_a[0] = 1.0
    state_b[1] = 1.0

    # 转换为列表
    state_a = state_a.tolist()
    state_b = state_b.tolist()

    print("\n测试融合算子性能:")
    print(f"状态维度: {dim}")
    print()

    # 测试各种融合方法的性能
    fusion_operators = [
        "fusion.quantum_superposition",
        "fusion.holographic_interference",
        "fusion.fractal_fusion",
        "fusion.topological_fusion",
    ]

    for op_name in fusion_operators:
        operator = registry.get_operator(op_name)

        # 预热
        operator(state_a, state_b)

        # 计时
        iterations = 10
        start_time = time.time()

        for _ in range(iterations):
            operator(state_a, state_b)

        elapsed_time = time.time() - start_time
        avg_time = elapsed_time / iterations * 1000  # 毫秒

        print(f"算子 {op_name} 平均执行时间: {avg_time:.2f} 毫秒")

    return True

if __name__ == "__main__":
    print("开始测试超越态融合算子...")

    # 测试融合算子注册
    registration_ok = test_fusion_operators_registration()

    if registration_ok:
        # 测试融合算子功能
        test_fusion_operators_functionality()

        # 测试融合算子性能
        test_fusion_operators_performance()
    else:
        print("融合算子注册失败，无法继续测试")

    print("\n测试完成")
