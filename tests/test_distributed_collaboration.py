"""
分布式协同算子测试。

测试分布式协同算子的功能和性能。
"""

import unittest
import numpy as np
from typing import List

from src.core import TranscendentalState, DistributedNode
from src.distributed.testing.local import LocalTestEnvironment
from src.operators.distributed import (
    sync_distributed_state,
    schedule_distributed_computation,
    recover_from_failure,
    optimize_network_topology
)


class TestDistributedCollaboration(unittest.TestCase):
    """测试分布式协同算子。"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境。"""
        # 创建本地分布式测试环境
        cls.test_env = LocalTestEnvironment(num_nodes=3)
        cls.test_env.start()
        
        # 获取测试节点
        cls.nodes = cls.test_env.get_nodes()
    
    @classmethod
    def tearDownClass(cls):
        """清理测试环境。"""
        cls.test_env.stop()
    
    def test_sync_distributed_state(self):
        """测试分布式状态同步算子。"""
        # 创建测试状态
        state = TranscendentalState(
            name="test_sync_state",
            dimensions=[4, 4, 4],
            state_type="computational"
        )
        state.initialize_random()
        
        # 同步状态到所有节点
        result = sync_distributed_state(state, self.nodes)
        
        # 验证结果
        self.assertIsNotNone(result)
        
        # 验证所有节点上的状态是否一致
        for node in self.nodes:
            node_state = node.get_object(state.id)
            self.assertIsNotNone(node_state)
            # 验证状态属性
            self.assertEqual(node_state.name, state.name)
            self.assertEqual(node_state.dimensions, state.dimensions)
            self.assertEqual(node_state.state_type, state.state_type)
    
    def test_schedule_distributed_computation(self):
        """测试分布式计算调度算子。"""
        # 定义测试计算函数
        def test_computation(data):
            return np.sum(data)
        
        # 创建测试数据
        test_data = np.random.random((10, 10))
        
        # 调度计算
        result = schedule_distributed_computation(
            test_computation,
            test_data,
            self.nodes
        )
        
        # 验证结果
        expected_result = np.sum(test_data)
        self.assertAlmostEqual(result, expected_result, places=5)


if __name__ == '__main__':
    unittest.main()
