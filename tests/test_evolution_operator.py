"""
测试EvolutionOperator

本脚本测试EvolutionOperator的基本功能。
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 定义一个简单的EvolutionOperator类
class EvolutionOperator:
    """简化的演化算子类"""
    
    def __init__(self, evolution_type='differential_equation', dimension=3, parameters=None, **kwargs):
        """初始化演化算子"""
        self.evolution_type = evolution_type
        self.dimension = dimension
        self.parameters = parameters or {}
        self.name = "EvolutionOperator"
    
    def apply(self, input_data, **kwargs):
        """应用演化到输入数据"""
        # 提取参数
        return_trajectory = kwargs.get('return_trajectory', False)
        time_step = kwargs.get('time_step', 0.01)
        num_steps = kwargs.get('num_steps', 100)
        
        # 预处理输入数据
        data = input_data
        
        # 应用演化
        if self.evolution_type == 'differential_equation':
            # 微分方程演化
            equation = self.parameters.get('equation', 'harmonic_oscillator')
            
            if equation == 'harmonic_oscillator':
                # 简谐振荡器: d^2x/dt^2 = -x
                # 转换为一阶系统: dx/dt = v, dv/dt = -x
                trajectory = [data.copy()]
                
                for _ in range(num_steps):
                    last_state = trajectory[-1]
                    next_state = last_state.copy()
                    
                    for i in range(last_state.shape[0]):
                        x = last_state[i, 0]
                        v = last_state[i, 1]
                        
                        # 使用四阶龙格-库塔方法
                        k1_x = v
                        k1_v = -x
                        
                        k2_x = v + time_step / 2 * k1_v
                        k2_v = -(x + time_step / 2 * k1_x)
                        
                        k3_x = v + time_step / 2 * k2_v
                        k3_v = -(x + time_step / 2 * k2_x)
                        
                        k4_x = v + time_step * k3_v
                        k4_v = -(x + time_step * k3_x)
                        
                        next_state[i, 0] = x + time_step / 6 * (k1_x + 2 * k2_x + 2 * k3_x + k4_x)
                        next_state[i, 1] = v + time_step / 6 * (k1_v + 2 * k2_v + 2 * k3_v + k4_v)
                    
                    trajectory.append(next_state)
                
                if return_trajectory:
                    return trajectory
                else:
                    return trajectory[-1]
            else:
                # 未知方程，返回输入数据
                if return_trajectory:
                    return [data.copy() for _ in range(num_steps + 1)]
                else:
                    return data.copy()
        else:
            # 简化实现，返回输入数据
            if return_trajectory:
                return [data.copy() for _ in range(num_steps + 1)]
            else:
                return data.copy()
    
    def get_metadata(self):
        """获取算子元数据"""
        return {
            "name": self.name,
            "type": "evolution",
            "evolution_type": self.evolution_type,
            "dimension": self.dimension,
            "description": "Evolution operator for applying various evolutionary processes to data"
        }
    
    def is_compatible_with(self, other_operator):
        """检查与其他算子的兼容性"""
        # 简化实现，始终返回True
        return True
    
    def get_performance_metrics(self):
        """获取性能指标"""
        return {
            "time_complexity": 1.0,
            "space_complexity": 1.0,
            "numerical_stability": 0.9,
            "parallelizability": 0.8
        }
    
    def compose(self, other_operator):
        """与其他算子组合"""
        # 简化实现，返回self
        return self
    
    def get_parameters(self):
        """获取算子参数"""
        return {
            "evolution_type": self.evolution_type,
            "dimension": self.dimension,
            "parameters": self.parameters
        }
    
    def set_parameters(self, parameters):
        """设置算子参数"""
        if "evolution_type" in parameters:
            self.evolution_type = parameters["evolution_type"]
        if "dimension" in parameters:
            self.dimension = parameters["dimension"]
        if "parameters" in parameters:
            self.parameters = parameters["parameters"]
    
    def to_rust(self):
        """检查是否有Rust实现"""
        return False
    
    def get_complexity(self):
        """获取算子复杂度信息"""
        return {
            "time_complexity": "O(n*t)",
            "space_complexity": "O(n)",
            "computational_complexity": "Medium",
            "numerical_stability": "Medium",
            "parallelizable": True
        }


def test_differential_equation():
    """测试微分方程演化"""
    print("\n测试微分方程演化...")
    
    # 创建随机数据
    np.random.seed(42)
    data = np.random.randn(10, 2)
    
    # 创建演化算子
    evolution_op = EvolutionOperator(
        evolution_type='differential_equation',
        dimension=2,
        parameters={
            'equation': 'harmonic_oscillator'
        }
    )
    
    # 应用演化
    trajectory = evolution_op.apply(data, return_trajectory=True, time_step=0.1, num_steps=100)
    
    # 打印结果
    print(f"演化类型: {evolution_op.evolution_type}")
    print(f"演化参数: {evolution_op.parameters}")
    print(f"原始数据形状: {data.shape}")
    print(f"轨迹长度: {len(trajectory)}")
    print(f"最终状态形状: {trajectory[-1].shape}")
    
    # 可视化轨迹
    plt.figure(figsize=(10, 8))
    
    # 绘制第一个点的轨迹
    trajectory_point = np.array([state[0] for state in trajectory])
    plt.plot(trajectory_point[:, 0], trajectory_point[:, 1], 'b-', alpha=0.7)
    plt.scatter(trajectory_point[0, 0], trajectory_point[0, 1], c='g', s=100, label='Start')
    plt.scatter(trajectory_point[-1, 0], trajectory_point[-1, 1], c='r', s=100, label='End')
    
    plt.title('Harmonic Oscillator Trajectory')
    plt.xlabel('Position')
    plt.ylabel('Velocity')
    plt.grid(True)
    plt.legend()
    
    plt.savefig('differential_equation_test.png')
    plt.close()
    
    print("微分方程演化测试完成")


def main():
    """主函数"""
    print("开始测试EvolutionOperator...")
    
    # 测试微分方程演化
    test_differential_equation()
    
    print("\n所有测试完成")


if __name__ == "__main__":
    main()
