"""
测试NoncommutativeBundle的Rust实现
"""

import unittest
import numpy as np
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入算子注册表
from src.core.registry import OperatorRegistry

# 导入微分几何算子
from src.operators.differential_geometry import (
    NoncommutativeBundle, 
    RUST_AVAILABLE,
    register_bundle
)

class TestNoncommutativeBundleRust(unittest.TestCase):
    """测试NoncommutativeBundle的Rust实现"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建算子注册表
        self.registry = OperatorRegistry()
        
        # 如果Rust实现不可用，跳过测试
        if not RUST_AVAILABLE:
            self.skipTest("Rust实现不可用")
        
        # 注册NoncommutativeBundle算子
        self.operator_id = register_bundle(
            self.registry,
            "test_noncommutative_bundle",
            "differential_geometry",
            {"description": "Test NoncommutativeBundle"}
        )
        
        # 获取注册的算子
        self.bundle = self.registry.get_operator(self.operator_id)
    
    def test_creation(self):
        """测试创建NoncommutativeBundle"""
        # 创建NoncommutativeBundle
        bundle = NoncommutativeBundle(
            dimension=3,
            fiber_dimension=2,
            connection_type="levi-civita",
            curvature_enabled=True
        )
        
        # 检查属性
        self.assertEqual(bundle.get_metadata()["dimension"], "3")
        self.assertEqual(bundle.get_metadata()["fiber_dimension"], "2")
        self.assertEqual(bundle.get_metadata()["connection_type"], "levi-civita")
        self.assertEqual(bundle.get_metadata()["curvature_enabled"], "True")
    
    def test_process_points(self):
        """测试处理点集"""
        # 创建测试点集
        points = np.array([
            [1.0, 0.0, 0.0],
            [0.0, 1.0, 0.0],
            [0.0, 0.0, 1.0]
        ])
        
        # 处理点集
        result = self.bundle.apply(points)
        
        # 检查结果形状
        self.assertEqual(result.shape, (3, 5))
        
        # 检查前3个坐标是否与输入点相同
        np.testing.assert_array_almost_equal(result[:, :3], points)
    
    def test_compute_curvature(self):
        """测试计算曲率"""
        # 创建测试点
        point = np.array([1.0, 0.0, 0.0])
        
        # 计算曲率
        curvature = self.bundle.compute_curvature(point)
        
        # 检查曲率形状
        self.assertEqual(curvature.shape, (3, 3, 3, 3))
    
    def test_compute_torsion(self):
        """测试计算挠率"""
        # 创建测试点
        point = np.array([1.0, 0.0, 0.0])
        
        # 计算挠率
        torsion = self.bundle.compute_torsion(point)
        
        # 检查挠率形状
        self.assertEqual(torsion.shape, (3, 3, 3))
    
    def test_registry_integration(self):
        """测试与注册表的集成"""
        # 检查算子是否已注册
        self.assertIn(self.operator_id, self.registry.get_operator_ids())
        
        # 检查算子类型
        self.assertEqual(self.registry.get_operator_type(self.operator_id), "differential_geometry")
        
        # 检查算子元数据
        metadata = self.registry.get_operator_metadata(self.operator_id)
        self.assertEqual(metadata["description"], "Test NoncommutativeBundle")
    
    def test_to_rust(self):
        """测试to_rust方法"""
        # 检查to_rust方法
        self.assertTrue(self.bundle.to_rust())

if __name__ == '__main__':
    unittest.main()
