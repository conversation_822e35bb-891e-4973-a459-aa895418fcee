"""
测试分布式版本的TransformOperator

本脚本测试分布式版本的TransformOperator的基本功能和性能。
"""

import numpy as np
import matplotlib.pyplot as plt
import time
import sys
import os
import socket
import threading
import json
import uuid

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.operators.transform import (
    TransformOperator,
    RUST_DISTRIBUTED_AVAILABLE
)

if RUST_DISTRIBUTED_AVAILABLE:
    from src.operators.transform import RustDistributedTransformOperator, DistributedTransformError, NodeRole


def test_python_transform_operator():
    """测试Python实现的TransformOperator"""
    print("\n测试Python实现的TransformOperator...")
    
    # 创建随机数据
    np.random.seed(42)
    data = np.random.randn(1000, 2)
    
    # 创建线性变换算子
    transform_op = TransformOperator(
        transform_type='linear',
        dimension=2,
        parameters={
            'matrix': np.array([[np.cos(np.pi/4), -np.sin(np.pi/4)], 
                               [np.sin(np.pi/4), np.cos(np.pi/4)]]),
            'offset': np.array([0, 0])
        }
    )
    
    # 测量性能
    start_time = time.time()
    
    # 应用变换
    transformed_data = transform_op.apply(data)
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    # 打印结果
    print(f"变换类型: {transform_op.transform_type}")
    print(f"变换参数: {transform_op.parameters}")
    print(f"原始数据形状: {data.shape}")
    print(f"变换后数据形状: {transformed_data.shape}")
    print(f"执行时间: {elapsed_time:.6f} 秒")
    
    # 可视化原始数据和变换后的数据
    plt.figure(figsize=(10, 5))
    
    plt.subplot(1, 2, 1)
    plt.scatter(data[:100, 0], data[:100, 1], alpha=0.7)
    plt.title('Original Data')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.scatter(transformed_data[:100, 0], transformed_data[:100, 1], alpha=0.7)
    plt.title('Transformed Data (Python)')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('python_transform_operator_test.png')
    plt.close()
    
    print("Python实现的TransformOperator测试完成")
    
    return data, transformed_data, elapsed_time


def start_worker_node(port):
    """启动一个工作节点"""
    if not RUST_DISTRIBUTED_AVAILABLE:
        print("分布式版本的TransformOperator不可用，跳过测试")
        return
    
    print(f"\n启动工作节点，端口: {port}...")
    
    # 创建分布式版本的TransformOperator
    worker_id = f"worker-{uuid.uuid4()}"
    worker_op = RustDistributedTransformOperator(
        transform_type='linear',
        dimension=2,
        distributed=True,
        node_id=worker_id,
        address="127.0.0.1",
        port=port,
        role=NodeRole.WORKER
    )
    
    # 启动工作节点
    try:
        worker_op.start()
        print(f"工作节点 {worker_id} 启动成功")
        
        # 保持工作节点运行
        while True:
            time.sleep(1)
    
    except DistributedTransformError as e:
        print(f"工作节点启动失败: {e}")
    
    except KeyboardInterrupt:
        print("工作节点被中断")
    
    finally:
        # 停止工作节点
        try:
            worker_op.stop()
            print(f"工作节点 {worker_id} 已停止")
        except:
            pass


def test_distributed_transform_operator(data):
    """测试分布式版本的TransformOperator"""
    if not RUST_DISTRIBUTED_AVAILABLE:
        print("\n分布式版本的TransformOperator不可用，跳过测试")
        return None, None
    
    print("\n测试分布式版本的TransformOperator...")
    
    # 启动工作节点
    worker_port = 8000
    worker_thread = threading.Thread(target=start_worker_node, args=(worker_port,))
    worker_thread.daemon = True
    worker_thread.start()
    
    # 等待工作节点启动
    time.sleep(2)
    
    # 创建分布式版本的TransformOperator
    master_id = f"master-{uuid.uuid4()}"
    transform_op = RustDistributedTransformOperator(
        transform_type='linear',
        dimension=2,
        parameters={
            'matrix': np.array([[np.cos(np.pi/4), -np.sin(np.pi/4)], 
                               [np.sin(np.pi/4), np.cos(np.pi/4)]]),
            'offset': np.array([0, 0])
        },
        distributed=True,
        node_id=master_id,
        address="127.0.0.1",
        port=8001,
        role=NodeRole.MASTER
    )
    
    # 启动主节点
    try:
        transform_op.start()
        print(f"主节点 {master_id} 启动成功")
        
        # 检查分布式计算是否可用
        is_distributed_available = transform_op.is_distributed_available()
        print(f"分布式计算是否可用: {is_distributed_available}")
        
        # 获取节点角色
        node_role = transform_op.get_node_role()
        print(f"节点角色: {NodeRole.to_string(node_role)}")
        
        # 测量性能
        start_time = time.time()
        
        # 应用变换
        transformed_data = transform_op.apply(data)
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        # 打印结果
        print(f"变换类型: {transform_op.transform_type}")
        print(f"变换参数: {transform_op.get_parameters()}")
        print(f"原始数据形状: {data.shape}")
        print(f"变换后数据形状: {transformed_data.shape}")
        print(f"执行时间: {elapsed_time:.6f} 秒")
        
        # 可视化原始数据和变换后的数据
        plt.figure(figsize=(10, 5))
        
        plt.subplot(1, 2, 1)
        plt.scatter(data[:100, 0], data[:100, 1], alpha=0.7)
        plt.title('Original Data')
        plt.grid(True)
        
        plt.subplot(1, 2, 2)
        plt.scatter(transformed_data[:100, 0], transformed_data[:100, 1], alpha=0.7)
        plt.title('Transformed Data (Distributed)')
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('distributed_transform_operator_test.png')
        plt.close()
        
        print("分布式版本的TransformOperator测试完成")
        
        return transformed_data, elapsed_time
    
    except DistributedTransformError as e:
        print(f"分布式变换错误: {e}")
        return None, None
    
    finally:
        # 停止主节点
        try:
            transform_op.stop()
            print(f"主节点 {master_id} 已停止")
        except:
            pass


def compare_implementations(python_result, python_time, distributed_result, distributed_time):
    """比较不同实现的结果和性能"""
    if distributed_result is None:
        print("\n分布式版本的TransformOperator不可用，跳过比较")
        return
    
    print("\n比较不同实现的结果和性能...")
    
    # 计算差异
    diff = np.abs(python_result - distributed_result)
    max_diff = np.max(diff)
    mean_diff = np.mean(diff)
    
    print(f"Python vs Distributed:")
    print(f"  最大差异: {max_diff}")
    print(f"  平均差异: {mean_diff}")
    print(f"  Python执行时间: {python_time:.6f} 秒")
    print(f"  Distributed执行时间: {distributed_time:.6f} 秒")
    print(f"  加速比: {python_time / distributed_time:.2f}x")
    
    # 可视化性能比较
    implementations = ['Python', 'Distributed']
    times = [python_time, distributed_time]
    
    plt.figure(figsize=(10, 6))
    
    plt.bar(implementations, times)
    plt.title('Performance Comparison')
    plt.ylabel('Execution Time (seconds)')
    plt.grid(True, axis='y')
    
    # 添加数值标签
    for i, v in enumerate(times):
        plt.text(i, v + 0.01, f"{v:.6f}s", ha='center')
    
    plt.tight_layout()
    plt.savefig('distributed_performance_comparison.png')
    plt.close()
    
    print("比较完成")


def test_large_scale_performance():
    """测试大规模数据的性能"""
    if not RUST_DISTRIBUTED_AVAILABLE:
        print("\n分布式版本的TransformOperator不可用，跳过测试")
        return
    
    print("\n测试大规模数据的性能...")
    
    # 启动工作节点
    worker_port = 8002
    worker_thread = threading.Thread(target=start_worker_node, args=(worker_port,))
    worker_thread.daemon = True
    worker_thread.start()
    
    # 等待工作节点启动
    time.sleep(2)
    
    # 创建大规模随机数据
    np.random.seed(42)
    data_sizes = [1000, 10000, 100000]
    
    # 创建结果表格
    print(f"{'数据大小':>10} | {'Python (s)':>12} | {'Distributed (s)':>16} | {'加速比':>10}")
    print(f"{'-'*10} | {'-'*12} | {'-'*16} | {'-'*10}")
    
    # 测试不同数据大小
    sizes = []
    python_times = []
    distributed_times = []
    speedups = []
    
    try:
        # 创建分布式版本的TransformOperator
        master_id = f"master-{uuid.uuid4()}"
        transform_op_distributed = RustDistributedTransformOperator(
            transform_type='linear',
            dimension=2,
            parameters={
                'matrix': np.array([[np.cos(np.pi/4), -np.sin(np.pi/4)], 
                                   [np.sin(np.pi/4), np.cos(np.pi/4)]]),
                'offset': np.array([0, 0])
            },
            distributed=True,
            node_id=master_id,
            address="127.0.0.1",
            port=8003,
            role=NodeRole.MASTER
        )
        
        # 启动主节点
        transform_op_distributed.start()
        print(f"主节点 {master_id} 启动成功")
        
        for size in data_sizes:
            data = np.random.randn(size, 2)
            
            # 测试Python实现
            transform_op_python = TransformOperator(
                transform_type='linear',
                dimension=2,
                parameters={
                    'matrix': np.array([[np.cos(np.pi/4), -np.sin(np.pi/4)], 
                                       [np.sin(np.pi/4), np.cos(np.pi/4)]]),
                    'offset': np.array([0, 0])
                }
            )
            
            start_time = time.time()
            transform_op_python.apply(data)
            python_time = time.time() - start_time
            
            # 测试分布式实现
            try:
                start_time = time.time()
                transform_op_distributed.apply(data)
                distributed_time = time.time() - start_time
                
                # 计算加速比
                speedup = python_time / distributed_time
                
                # 打印结果
                print(f"{size:>10} | {python_time:>12.6f} | {distributed_time:>16.6f} | {speedup:>10.2f}")
                
                # 保存结果
                sizes.append(size)
                python_times.append(python_time)
                distributed_times.append(distributed_time)
                speedups.append(speedup)
            
            except DistributedTransformError:
                print(f"{size:>10} | {python_time:>12.6f} | {'Error':>16} | {'N/A':>10}")
        
        # 可视化性能比较
        plt.figure(figsize=(12, 10))
        
        # 执行时间比较
        plt.subplot(2, 1, 1)
        plt.plot(sizes, python_times, 'o-', label='Python')
        plt.plot(sizes, distributed_times, 'o-', label='Distributed')
        plt.title('Execution Time Comparison')
        plt.xlabel('Data Size')
        plt.ylabel('Execution Time (seconds)')
        plt.grid(True)
        plt.legend()
        plt.xscale('log')
        plt.yscale('log')
        
        # 加速比比较
        plt.subplot(2, 1, 2)
        plt.plot(sizes, speedups, 'o-')
        plt.title('Speedup Comparison')
        plt.xlabel('Data Size')
        plt.ylabel('Speedup (x)')
        plt.grid(True)
        plt.xscale('log')
        
        plt.tight_layout()
        plt.savefig('distributed_large_scale_performance.png')
        plt.close()
        
        print("大规模数据性能测试完成")
    
    except DistributedTransformError as e:
        print(f"分布式变换错误: {e}")
    
    finally:
        # 停止主节点
        try:
            transform_op_distributed.stop()
            print(f"主节点 {master_id} 已停止")
        except:
            pass


def test_nonlinear_transform():
    """测试非线性变换"""
    if not RUST_DISTRIBUTED_AVAILABLE:
        print("\n分布式版本的TransformOperator不可用，跳过测试")
        return
    
    print("\n测试非线性变换...")
    
    # 启动工作节点
    worker_port = 8004
    worker_thread = threading.Thread(target=start_worker_node, args=(worker_port,))
    worker_thread.daemon = True
    worker_thread.start()
    
    # 等待工作节点启动
    time.sleep(2)
    
    # 创建随机数据
    np.random.seed(42)
    data = np.random.randn(1000, 2)
    
    # 测试不同的非线性函数
    nonlinear_functions = ['sigmoid', 'tanh', 'relu', 'quadratic']
    
    try:
        # 创建分布式版本的TransformOperator
        master_id = f"master-{uuid.uuid4()}"
        transform_op_distributed = RustDistributedTransformOperator(
            transform_type='nonlinear',
            dimension=2,
            distributed=True,
            node_id=master_id,
            address="127.0.0.1",
            port=8005,
            role=NodeRole.MASTER
        )
        
        # 启动主节点
        transform_op_distributed.start()
        print(f"主节点 {master_id} 启动成功")
        
        for function in nonlinear_functions:
            print(f"\n测试 {function} 函数...")
            
            # 测试Python实现
            transform_op_python = TransformOperator(
                transform_type='nonlinear',
                dimension=2,
                parameters={
                    'function': function
                }
            )
            
            start_time = time.time()
            python_result = transform_op_python.apply(data)
            python_time = time.time() - start_time
            
            # 测试分布式实现
            transform_op_distributed.set_parameters({
                'parameters': {
                    'function': function
                }
            })
            
            try:
                start_time = time.time()
                distributed_result = transform_op_distributed.apply(data)
                distributed_time = time.time() - start_time
                
                # 计算差异
                diff = np.abs(python_result - distributed_result)
                max_diff = np.max(diff)
                mean_diff = np.mean(diff)
                
                # 计算加速比
                speedup = python_time / distributed_time
                
                # 打印结果
                print(f"  Python执行时间: {python_time:.6f} 秒")
                print(f"  Distributed执行时间: {distributed_time:.6f} 秒")
                print(f"  加速比: {speedup:.2f}x")
                print(f"  最大差异: {max_diff}")
                print(f"  平均差异: {mean_diff}")
                
                # 可视化结果
                plt.figure(figsize=(15, 5))
                
                plt.subplot(1, 3, 1)
                plt.scatter(data[:100, 0], data[:100, 1], alpha=0.7)
                plt.title('Original Data')
                plt.grid(True)
                
                plt.subplot(1, 3, 2)
                plt.scatter(python_result[:100, 0], python_result[:100, 1], alpha=0.7)
                plt.title(f'Python {function}')
                plt.grid(True)
                
                plt.subplot(1, 3, 3)
                plt.scatter(distributed_result[:100, 0], distributed_result[:100, 1], alpha=0.7)
                plt.title(f'Distributed {function}')
                plt.grid(True)
                
                plt.tight_layout()
                plt.savefig(f'distributed_{function}_test.png')
                plt.close()
            
            except DistributedTransformError as e:
                print(f"  Distributed变换错误: {e}")
        
        print("非线性变换测试完成")
    
    except DistributedTransformError as e:
        print(f"分布式变换错误: {e}")
    
    finally:
        # 停止主节点
        try:
            transform_op_distributed.stop()
            print(f"主节点 {master_id} 已停止")
        except:
            pass


def main():
    """主函数"""
    print("开始测试分布式版本的TransformOperator...")
    
    # 测试Python实现
    data, python_result, python_time = test_python_transform_operator()
    
    # 测试分布式版本的实现
    distributed_result, distributed_time = test_distributed_transform_operator(data)
    
    # 比较实现
    compare_implementations(python_result, python_time, distributed_result, distributed_time)
    
    # 测试大规模数据的性能
    test_large_scale_performance()
    
    # 测试非线性变换
    test_nonlinear_transform()
    
    print("\n所有测试完成")


if __name__ == "__main__":
    main()
