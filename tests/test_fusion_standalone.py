"""
超越态融合算子独立测试脚本

本脚本用于测试超越态融合算子的基本功能，不依赖于项目的其他部分。
"""

import sys
import os
import time
import enum
import ctypes
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# 融合方法枚举
class FusionMethod(enum.IntEnum):
    """融合方法枚举"""
    QUANTUM_SUPERPOSITION = 0
    HOLOGRAPHIC_INTERFERENCE = 1
    FRACTAL_FUSION = 2
    TOPOLOGICAL_FUSION = 3

# 获取动态库路径
def get_lib_path():
    """获取动态库路径"""
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    
    # 构建动态库路径
    lib_path = project_root / "src" / "operators" / "rust_simple" / "target" / "release"
    
    # 根据操作系统选择正确的文件扩展名
    if os.name == "posix":  # Linux/Mac
        lib_file = lib_path / "libtte_fusion.so"
    elif os.name == "nt":  # Windows
        lib_file = lib_path / "tte_fusion.dll"
    else:
        raise RuntimeError(f"不支持的操作系统: {os.name}")
    
    if not lib_file.exists():
        raise FileNotFoundError(f"找不到动态库文件: {lib_file}")
    
    return str(lib_file)

# 加载动态库
def load_fusion_lib():
    """加载融合算子动态库"""
    lib_path = get_lib_path()
    try:
        lib = ctypes.CDLL(lib_path)
        return lib
    except Exception as e:
        raise RuntimeError(f"加载动态库失败: {e}")

# 定义超越态融合算子类
class TranscendentalFusionOperator:
    """超越态融合算子类"""
    
    def __init__(self):
        """初始化超越态融合算子"""
        self.lib = load_fusion_lib()
        
        # 定义带方法的融合函数签名
        self.lib.fusion_with_method.argtypes = [
            ctypes.POINTER(ctypes.c_double),  # state_a_real
            ctypes.POINTER(ctypes.c_double),  # state_a_imag
            ctypes.POINTER(ctypes.c_double),  # state_b_real
            ctypes.POINTER(ctypes.c_double),  # state_b_imag
            ctypes.POINTER(ctypes.c_double),  # result_real
            ctypes.POINTER(ctypes.c_double),  # result_imag
            ctypes.c_size_t,                  # length
            ctypes.c_double,                  # weight_a
            ctypes.c_double,                  # weight_b
            ctypes.c_int,                     # method
        ]
        self.lib.fusion_with_method.restype = None
    
    def fuse(self, state_a, state_b, weight_a=0.5, weight_b=0.5, method=FusionMethod.QUANTUM_SUPERPOSITION):
        """
        融合两个量子态
        
        参数:
            state_a: 第一个量子态，复数数组
            state_b: 第二个量子态，复数数组
            weight_a: 第一个量子态的权重
            weight_b: 第二个量子态的权重
            method: 融合方法，默认为量子叠加
            
        返回:
            融合后的量子态，复数数组
        """
        # 确保输入是NumPy数组
        state_a = np.asarray(state_a, dtype=np.complex128)
        state_b = np.asarray(state_b, dtype=np.complex128)
        
        # 检查输入维度
        if state_a.shape != state_b.shape:
            raise ValueError(f"状态维度不匹配: {state_a.shape} vs {state_b.shape}")
        
        # 获取长度
        length = len(state_a)
        
        # 准备输入数据
        state_a_real = state_a.real.astype(np.float64)
        state_a_imag = state_a.imag.astype(np.float64)
        state_b_real = state_b.real.astype(np.float64)
        state_b_imag = state_b.imag.astype(np.float64)
        
        # 准备输出数据
        result_real = np.zeros(length, dtype=np.float64)
        result_imag = np.zeros(length, dtype=np.float64)
        
        # 调用Rust函数
        self.lib.fusion_with_method(
            state_a_real.ctypes.data_as(ctypes.POINTER(ctypes.c_double)),
            state_a_imag.ctypes.data_as(ctypes.POINTER(ctypes.c_double)),
            state_b_real.ctypes.data_as(ctypes.POINTER(ctypes.c_double)),
            state_b_imag.ctypes.data_as(ctypes.POINTER(ctypes.c_double)),
            result_real.ctypes.data_as(ctypes.POINTER(ctypes.c_double)),
            result_imag.ctypes.data_as(ctypes.POINTER(ctypes.c_double)),
            length,
            weight_a,
            weight_b,
            int(method),
        )
        
        # 构建复数结果
        result = result_real + 1j * result_imag
        
        return result
    
    def quantum_superposition(self, state_a, state_b, weight_a=0.5, weight_b=0.5):
        """量子叠加融合"""
        return self.fuse(state_a, state_b, weight_a, weight_b, FusionMethod.QUANTUM_SUPERPOSITION)
    
    def holographic_interference(self, state_a, state_b, weight_a=0.5, weight_b=0.5):
        """全息干涉融合"""
        return self.fuse(state_a, state_b, weight_a, weight_b, FusionMethod.HOLOGRAPHIC_INTERFERENCE)
    
    def fractal_fusion(self, state_a, state_b, weight_a=0.5, weight_b=0.5):
        """分形融合"""
        return self.fuse(state_a, state_b, weight_a, weight_b, FusionMethod.FRACTAL_FUSION)
    
    def topological_fusion(self, state_a, state_b, weight_a=0.5, weight_b=0.5):
        """拓扑融合"""
        return self.fuse(state_a, state_b, weight_a, weight_b, FusionMethod.TOPOLOGICAL_FUSION)

def test_fusion_methods():
    """测试各种融合方法"""
    # 创建融合算子
    try:
        fusion_op = TranscendentalFusionOperator()
        print("成功创建融合算子实例")
    except Exception as e:
        print(f"创建融合算子实例失败: {e}")
        return False
    
    # 创建两个测试状态
    state_a = np.array([1.0, 0.0], dtype=np.complex128)  # |0⟩
    state_b = np.array([0.0, 1.0], dtype=np.complex128)  # |1⟩
    
    print("\n测试融合算子功能:")
    print("状态A:", state_a)
    print("状态B:", state_b)
    print()
    
    # 测试各种融合方法
    methods = {
        "量子叠加": FusionMethod.QUANTUM_SUPERPOSITION,
        "全息干涉": FusionMethod.HOLOGRAPHIC_INTERFERENCE,
        "分形融合": FusionMethod.FRACTAL_FUSION,
        "拓扑融合": FusionMethod.TOPOLOGICAL_FUSION,
    }
    
    results = {}
    
    for name, method in methods.items():
        try:
            result = fusion_op.fuse(state_a, state_b, method=method)
            results[name] = result
            
            print(f"方法: {name}")
            print("融合结果:", result)
            print("结果概率:", np.abs(result)**2)
            
            # 验证结果是否为单位向量
            norm = np.sqrt(np.sum(np.abs(result)**2))
            print("结果范数:", norm)
            print()
        except Exception as e:
            print(f"方法 {name} 测试失败: {e}")
    
    return len(results) > 0

def test_fusion_performance():
    """测试融合算子性能"""
    # 创建融合算子
    try:
        fusion_op = TranscendentalFusionOperator()
    except Exception as e:
        print(f"创建融合算子实例失败: {e}")
        return False
    
    # 创建大型测试状态
    dim = 1000
    state_a = np.zeros(dim, dtype=np.complex128)
    state_b = np.zeros(dim, dtype=np.complex128)
    
    # 初始化状态
    state_a[0] = 1.0
    state_b[1] = 1.0
    
    print("\n测试融合算子性能:")
    print(f"状态维度: {dim}")
    print()
    
    # 测试各种融合方法的性能
    methods = {
        "量子叠加": FusionMethod.QUANTUM_SUPERPOSITION,
        "全息干涉": FusionMethod.HOLOGRAPHIC_INTERFERENCE,
        "分形融合": FusionMethod.FRACTAL_FUSION,
        "拓扑融合": FusionMethod.TOPOLOGICAL_FUSION,
    }
    
    for name, method in methods.items():
        try:
            # 预热
            fusion_op.fuse(state_a, state_b, method=method)
            
            # 计时
            iterations = 5
            start_time = time.time()
            
            for _ in range(iterations):
                fusion_op.fuse(state_a, state_b, method=method)
            
            elapsed_time = time.time() - start_time
            avg_time = elapsed_time / iterations * 1000  # 毫秒
            
            print(f"方法: {name} 平均执行时间: {avg_time:.2f} 毫秒")
        except Exception as e:
            print(f"方法 {name} 性能测试失败: {e}")
    
    return True

if __name__ == "__main__":
    print("开始测试超越态融合算子...")
    
    # 测试融合算子功能
    functionality_ok = test_fusion_methods()
    
    if functionality_ok:
        # 测试融合算子性能
        test_fusion_performance()
    
    print("\n测试完成")
