"""
中心节点识别算子测试

本测试脚本测试中心节点识别算子的基本功能。
"""

import sys
import os
import unittest
import numpy as np
from unittest.mock import MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入中心节点识别算子
from src.transcendental_tensor.network_analysis import (
    NodeType, NodeStatus, CentralityMethod,
    NetworkNode, NetworkLink, Network,
    CentralityResult, CentralityCalculator, CentralNodeIdentifier
)


class TestCentralNodes(unittest.TestCase):
    """中心节点识别算子测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建测试网络
        self.network = Network(name="TestNetwork")
        
        # 创建节点
        self.node1 = NetworkNode(
            node_id="node-1",
            name="Node 1",
            node_type=NodeType.COMPUTE,
            status=NodeStatus.ONLINE
        )
        
        self.node2 = NetworkNode(
            node_id="node-2",
            name="Node 2",
            node_type=NodeType.STORAGE,
            status=NodeStatus.ONLINE
        )
        
        self.node3 = NetworkNode(
            node_id="node-3",
            name="Node 3",
            node_type=NodeType.GATEWAY,
            status=NodeStatus.ONLINE
        )
        
        self.node4 = NetworkNode(
            node_id="node-4",
            name="Node 4",
            node_type=NodeType.COORDINATOR,
            status=NodeStatus.ONLINE
        )
        
        self.node5 = NetworkNode(
            node_id="node-5",
            name="Node 5",
            node_type=NodeType.HYBRID,
            status=NodeStatus.ONLINE
        )
        
        # 添加节点
        self.network.add_node(self.node1)
        self.network.add_node(self.node2)
        self.network.add_node(self.node3)
        self.network.add_node(self.node4)
        self.network.add_node(self.node5)
        
        # 创建链接
        self.link1 = NetworkLink(
            source_id="node-1",
            target_id="node-2",
            bidirectional=True,
            weight=1.0
        )
        
        self.link2 = NetworkLink(
            source_id="node-2",
            target_id="node-3",
            bidirectional=True,
            weight=1.0
        )
        
        self.link3 = NetworkLink(
            source_id="node-3",
            target_id="node-4",
            bidirectional=True,
            weight=1.0
        )
        
        self.link4 = NetworkLink(
            source_id="node-4",
            target_id="node-5",
            bidirectional=True,
            weight=1.0
        )
        
        self.link5 = NetworkLink(
            source_id="node-5",
            target_id="node-1",
            bidirectional=True,
            weight=1.0
        )
        
        self.link6 = NetworkLink(
            source_id="node-1",
            target_id="node-3",
            bidirectional=True,
            weight=1.0
        )
        
        self.link7 = NetworkLink(
            source_id="node-2",
            target_id="node-4",
            bidirectional=True,
            weight=1.0
        )
        
        self.link8 = NetworkLink(
            source_id="node-3",
            target_id="node-5",
            bidirectional=True,
            weight=1.0
        )
        
        # 添加链接
        self.network.add_link(self.link1)
        self.network.add_link(self.link2)
        self.network.add_link(self.link3)
        self.network.add_link(self.link4)
        self.network.add_link(self.link5)
        self.network.add_link(self.link6)
        self.network.add_link(self.link7)
        self.network.add_link(self.link8)
        
        # 创建中心性计算器
        self.calculator = CentralityCalculator()
        
        # 创建中心节点识别算子
        self.identifier = CentralNodeIdentifier()
    
    def test_network_node(self):
        """测试网络节点"""
        # 测试节点属性
        self.assertEqual(self.node1.node_id, "node-1")
        self.assertEqual(self.node1.name, "Node 1")
        self.assertEqual(self.node1.node_type, NodeType.COMPUTE)
        self.assertEqual(self.node1.status, NodeStatus.ONLINE)
        
        # 测试节点方法
        self.assertTrue(self.node1.is_active())
        
        # 更新状态
        self.node1.update_status(NodeStatus.DEGRADED)
        self.assertEqual(self.node1.status, NodeStatus.DEGRADED)
        self.assertTrue(self.node1.is_active())
        
        # 更新状态
        self.node1.update_status(NodeStatus.FAILED)
        self.assertEqual(self.node1.status, NodeStatus.FAILED)
        self.assertFalse(self.node1.is_active())
    
    def test_network_link(self):
        """测试网络链接"""
        # 测试链接属性
        self.assertEqual(self.link1.source_id, "node-1")
        self.assertEqual(self.link1.target_id, "node-2")
        self.assertTrue(self.link1.bidirectional)
        self.assertEqual(self.link1.weight, 1.0)
        
        # 更新指标
        self.link1.update_metrics({"throughput": 100.0})
        self.assertEqual(self.link1.metrics["throughput"], 100.0)
    
    def test_network(self):
        """测试网络"""
        # 测试网络属性
        self.assertEqual(len(self.network.nodes), 5)
        self.assertEqual(len(self.network.links), 8)
        
        # 测试网络方法
        node = self.network.get_node("node-1")
        self.assertEqual(node.node_id, "node-1")
        
        link = self.network.get_link(self.link1.link_id)
        self.assertEqual(link.source_id, "node-1")
        self.assertEqual(link.target_id, "node-2")
        
        # 测试获取两个节点之间的链接
        links = self.network.get_links_between("node-1", "node-2")
        self.assertEqual(len(links), 1)
        self.assertEqual(links[0].source_id, "node-1")
        self.assertEqual(links[0].target_id, "node-2")
    
    def test_centrality_calculator(self):
        """测试中心性计算器"""
        # 测试度中心性
        self.calculator.parameters["method"] = CentralityMethod.DEGREE.value
        result = self.calculator.calculate_centrality(self.network)
        
        # 验证结果
        self.assertEqual(result.method, CentralityMethod.DEGREE)
        self.assertEqual(len(result.centrality), 5)
        
        # 测试PageRank中心性
        self.calculator.parameters["method"] = CentralityMethod.PAGERANK.value
        result = self.calculator.calculate_centrality(self.network)
        
        # 验证结果
        self.assertEqual(result.method, CentralityMethod.PAGERANK)
        self.assertEqual(len(result.centrality), 5)
        
        # 测试介数中心性
        self.calculator.parameters["method"] = CentralityMethod.BETWEENNESS.value
        result = self.calculator.calculate_centrality(self.network)
        
        # 验证结果
        self.assertEqual(result.method, CentralityMethod.BETWEENNESS)
        self.assertEqual(len(result.centrality), 5)
    
    def test_central_node_identifier(self):
        """测试中心节点识别算子"""
        # 测试识别中心节点
        result = self.identifier.identify_central_nodes(self.network)
        
        # 验证结果
        self.assertIn("central_nodes", result)
        self.assertIn("method", result)
        self.assertIn("network_id", result)
        self.assertEqual(len(result["central_nodes"]), 5)
        
        # 测试比较方法
        methods = [
            CentralityMethod.DEGREE,
            CentralityMethod.PAGERANK,
            CentralityMethod.BETWEENNESS
        ]
        
        comparison = self.identifier.compare_methods(self.network, methods)
        
        # 验证结果
        self.assertIn("results", comparison)
        self.assertIn("similarities", comparison)
        self.assertIn("network_id", comparison)
        self.assertEqual(len(comparison["results"]), 3)
        self.assertEqual(len(comparison["similarities"]), 3)
        
        # 测试获取节点中心性分布
        distribution = self.identifier.get_node_centrality_distribution(self.network)
        
        # 验证结果
        self.assertIn("quantiles", distribution)
        self.assertIn("histogram", distribution)
        self.assertIn("method", distribution)
        self.assertIn("network_id", distribution)
        
        # 测试获取社区中心节点
        communities = {
            "node-1": "community-1",
            "node-2": "community-1",
            "node-3": "community-2",
            "node-4": "community-2",
            "node-5": "community-3"
        }
        
        community_result = self.identifier.get_community_central_nodes(self.network, communities)
        
        # 验证结果
        self.assertIn("community_central_nodes", community_result)
        self.assertIn("method", community_result)
        self.assertIn("network_id", community_result)
        self.assertEqual(len(community_result["community_central_nodes"]), 3)


if __name__ == "__main__":
    unittest.main()
