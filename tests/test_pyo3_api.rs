/*
 * PyO3 API 测试
 *
 * 该文件包含用于测试 PyO3 API 的单元测试，特别是在升级到 PyO3 0.24+ 后的兼容性。
 */

use pyo3::prelude::*;
use pyo3::types::{PyDict, PyList, PyTuple};

// 测试 PyTuple 创建
#[test]
fn test_pytuple_creation() {
    Python::with_gil(|py| {
        // 创建一个 PyTuple
        let tup = PyTuple::new(py, [1, 2, 3]);
        
        // 验证 PyTuple 属性
        assert_eq!(tup.len(), 3);
        assert_eq!(tup.get_item(0).unwrap().extract::<i32>().unwrap(), 1);
        assert_eq!(tup.get_item(1).unwrap().extract::<i32>().unwrap(), 2);
        assert_eq!(tup.get_item(2).unwrap().extract::<i32>().unwrap(), 3);
    });
}

// 测试 PyList 创建
#[test]
fn test_pylist_creation() {
    Python::with_gil(|py| {
        // 创建一个 PyList
        let list = PyList::new(py, [1, 2, 3]);
        
        // 验证 PyList 属性
        assert_eq!(list.len(), 3);
        assert_eq!(list.get_item(0).unwrap().extract::<i32>().unwrap(), 1);
        assert_eq!(list.get_item(1).unwrap().extract::<i32>().unwrap(), 2);
        assert_eq!(list.get_item(2).unwrap().extract::<i32>().unwrap(), 3);
    });
}

// 测试 PyDict 创建
#[test]
fn test_pydict_creation() {
    Python::with_gil(|py| {
        // 创建一个 PyDict
        let dict = PyDict::new(py);
        
        // 添加键值对
        dict.set_item("key1", "value1").unwrap();
        dict.set_item("key2", 42).unwrap();
        dict.set_item("key3", true).unwrap();
        
        // 验证 PyDict 属性
        assert_eq!(dict.len(), 3);
        assert_eq!(dict.get_item("key1").unwrap().extract::<String>().unwrap(), "value1");
        assert_eq!(dict.get_item("key2").unwrap().extract::<i32>().unwrap(), 42);
        assert_eq!(dict.get_item("key3").unwrap().extract::<bool>().unwrap(), true);
    });
}

// 测试类型转换
#[test]
fn test_type_conversion() {
    Python::with_gil(|py| {
        // 创建一个 PyList
        let list = PyList::new(py, [1, 2, 3]);
        
        // 获取第一个元素
        let item = list.get_item(0).unwrap();
        
        // 使用 downcast 进行类型转换
        let int = item.downcast::<pyo3::types::PyInt>().unwrap();
        
        // 验证转换结果
        assert_eq!(int.extract::<i32>().unwrap(), 1);
    });
}

// 测试异常创建
#[test]
fn test_exception_creation() {
    use pyo3::exceptions::PyValueError;
    
    Python::with_gil(|py| {
        // 创建一个异常
        let err = PyValueError::new_err("测试错误消息");
        
        // 验证异常属性
        assert_eq!(err.to_string(), "ValueError: 测试错误消息");
        
        // 获取异常实例
        let instance = err.instance(py);
        
        // 验证实例类型
        assert!(instance.is_instance_of::<PyValueError>(py).unwrap());
    });
}

// 测试 GIL 引用转换
#[test]
fn test_gil_ref_conversion() {
    Python::with_gil(|py| {
        // 创建一个 PyList
        let list = PyList::new(py, [1, 2, 3]);
        
        // 获取 GIL 引用
        let gil_ref: &PyAny = list.as_ref();
        
        // 转换为 Bound 引用
        let bound = gil_ref.as_borrowed();
        
        // 验证转换结果
        assert_eq!(bound.len().unwrap(), 3);
    });
}

// 测试 Py<T> 和 Bound<T> 之间的转换
#[test]
fn test_py_bound_conversion() {
    Python::with_gil(|py| {
        // 创建一个 Py<PyList>
        let py_list = Py::new(py, vec![1, 2, 3]).unwrap();
        
        // 转换为 Bound<PyList>
        let bound_list = py_list.bind(py);
        
        // 验证转换结果
        assert_eq!(bound_list.len(), 3);
        
        // 转换回 Py<PyList>
        let py_list_again = bound_list.unbind();
        
        // 验证转换结果
        assert_eq!(py_list_again.as_ref(py).len(), 3);
    });
}

// 测试 Option<Bound<T>> 到指针的转换
#[test]
fn test_option_bound_to_pointer() {
    use pyo3::ffi;
    use std::ptr;
    
    Python::with_gil(|py| {
        // 创建一个 Option<Bound<PyAny>>
        let some_bound: Option<Bound<PyAny>> = Some(PyList::new(py, [1, 2, 3]).into_any());
        let none_bound: Option<Bound<PyAny>> = None;
        
        // 转换为指针
        let some_ptr: *mut ffi::PyObject = some_bound.as_ref().map_or(ptr::null_mut(), Bound::as_ptr);
        let none_ptr: *mut ffi::PyObject = none_bound.as_ref().map_or(ptr::null_mut(), Bound::as_ptr);
        
        // 验证转换结果
        assert!(!some_ptr.is_null());
        assert!(none_ptr.is_null());
    });
}

// 测试字节集合转换
#[test]
fn test_byte_collection_conversion() {
    Python::with_gil(|py| {
        // 创建一个 Vec<u8>
        let bytes = vec![0u8, 1, 2, 3];
        
        // 转换为 Python 对象
        let py_obj = bytes.into_py(py);
        
        // 验证转换结果（应该是 PyBytes）
        let py_any = py_obj.as_ref(py);
        assert!(py_any.is_instance_of::<pyo3::types::PyBytes>(py).unwrap());
        
        // 创建一个 Vec<u16>
        let u16_vec = vec![0u16, 1, 2, 3];
        
        // 转换为 Python 对象
        let py_obj = u16_vec.into_py(py);
        
        // 验证转换结果（应该是 PyList）
        let py_any = py_obj.as_ref(py);
        assert!(py_any.is_instance_of::<pyo3::types::PyList>(py).unwrap());
    });
}

// 测试字符串转换
#[test]
fn test_string_conversion() {
    Python::with_gil(|py| {
        // 创建一个 &str
        let s = "测试字符串";
        
        // 转换为 Python 对象
        let py_obj: Py<PyAny> = s.into_py(py);
        
        // 验证转换结果
        let py_any = py_obj.as_ref(py);
        assert!(py_any.is_instance_of::<pyo3::types::PyString>(py).unwrap());
        assert_eq!(py_any.extract::<String>().unwrap(), s);
    });
}
