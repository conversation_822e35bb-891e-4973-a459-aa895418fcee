"""
超越态思维引擎 - NoncommutativeBundle算子优化测试

本脚本用于测试NoncommutativeBundle算子的优化和注册表
"""

import numpy as np
import time
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple

# 导入TTE模块
try:
    from tte.differential_geometry.bundle_registry import (
        NoncommutativeBundleOperator,
        register_noncommutative_bundle_operators,
        get_noncommutative_bundle_operator,
        benchmark_noncommutative_bundle_operator,
        compare_optimized_vs_standard,
        compare_parallel_vs_serial
    )
except ImportError:
    print("无法导入TTE模块，请确保已正确安装")
    raise

def test_register_operators():
    """测试注册算子"""
    print("测试注册算子...")
    
    # 注册算子
    register_noncommutative_bundle_operators()
    
    # 获取算子
    standard_bundle = get_noncommutative_bundle_operator("standard_noncommutative_bundle")
    high_dim_bundle = get_noncommutative_bundle_operator("high_dim_noncommutative_bundle")
    cartan_bundle = get_noncommutative_bundle_operator("cartan_noncommutative_bundle")
    
    # 检查算子是否存在
    assert standard_bundle is not None, "标准算子不存在"
    assert high_dim_bundle is not None, "高维算子不存在"
    assert cartan_bundle is not None, "Cartan算子不存在"
    
    # 获取算子元数据
    standard_metadata = standard_bundle.get_metadata()
    high_dim_metadata = high_dim_bundle.get_metadata()
    cartan_metadata = cartan_bundle.get_metadata()
    
    # 打印算子元数据
    print(f"标准算子元数据: {standard_metadata}")
    print(f"高维算子元数据: {high_dim_metadata}")
    print(f"Cartan算子元数据: {cartan_metadata}")
    
    print("注册算子测试通过")

def test_process_points():
    """测试处理点"""
    print("测试处理点...")
    
    # 获取算子
    standard_bundle = get_noncommutative_bundle_operator("standard_noncommutative_bundle")
    assert standard_bundle is not None, "标准算子不存在"
    
    # 创建测试数据
    n_points = 100
    dimension = 3
    points = np.random.rand(n_points, dimension)
    
    # 处理点
    result = standard_bundle.process_points(points)
    
    # 检查结果
    assert result.shape == (n_points, dimension + 3), f"结果形状错误: {result.shape}"
    
    print("处理点测试通过")

def test_process_vector_field():
    """测试处理向量场"""
    print("测试处理向量场...")
    
    # 获取算子
    standard_bundle = get_noncommutative_bundle_operator("standard_noncommutative_bundle")
    assert standard_bundle is not None, "标准算子不存在"
    
    # 创建测试数据
    n_points = 100
    dimension = 3
    fiber_dimension = 3
    vector_field = np.random.rand(n_points, dimension, fiber_dimension)
    
    # 处理向量场
    result = standard_bundle.process_vector_field(vector_field)
    
    # 检查结果
    assert result.shape == (n_points, dimension, fiber_dimension), f"结果形状错误: {result.shape}"
    
    print("处理向量场测试通过")

def test_compute_curvature():
    """测试计算曲率"""
    print("测试计算曲率...")
    
    # 获取算子
    standard_bundle = get_noncommutative_bundle_operator("standard_noncommutative_bundle")
    assert standard_bundle is not None, "标准算子不存在"
    
    # 创建测试数据
    dimension = 3
    point = np.random.rand(dimension)
    
    # 计算曲率
    result = standard_bundle.compute_curvature(point)
    
    # 检查结果
    assert result.shape == (dimension, dimension, dimension, dimension), f"结果形状错误: {result.shape}"
    
    print("计算曲率测试通过")

def test_benchmark():
    """测试基准测试"""
    print("测试基准测试...")
    
    # 获取算子
    standard_bundle = get_noncommutative_bundle_operator("standard_noncommutative_bundle")
    assert standard_bundle is not None, "标准算子不存在"
    
    # 基准测试
    results = benchmark_noncommutative_bundle_operator(
        "standard_noncommutative_bundle",
        n_points=1000,
        dimension=3,
        fiber_dimension=3,
        n_runs=5
    )
    
    # 打印结果
    print(f"基准测试结果: {results}")
    
    print("基准测试通过")

def test_compare_optimized_vs_standard():
    """测试比较优化版本和标准版本"""
    print("测试比较优化版本和标准版本...")
    
    # 获取算子
    standard_bundle = get_noncommutative_bundle_operator("standard_noncommutative_bundle")
    assert standard_bundle is not None, "标准算子不存在"
    
    # 比较优化版本和标准版本
    results = compare_optimized_vs_standard(
        "standard_noncommutative_bundle",
        n_points=1000,
        dimension=3,
        fiber_dimension=3,
        n_runs=5
    )
    
    # 打印结果
    print(f"优化版本结果: {results['optimized']}")
    print(f"标准版本结果: {results['standard']}")
    print(f"加速比: {results['speedup']}")
    
    print("比较优化版本和标准版本测试通过")

def test_compare_parallel_vs_serial():
    """测试比较并行版本和串行版本"""
    print("测试比较并行版本和串行版本...")
    
    # 获取算子
    standard_bundle = get_noncommutative_bundle_operator("standard_noncommutative_bundle")
    assert standard_bundle is not None, "标准算子不存在"
    
    # 比较并行版本和串行版本
    results = compare_parallel_vs_serial(
        "standard_noncommutative_bundle",
        n_points=1000,
        dimension=3,
        fiber_dimension=3,
        n_runs=5
    )
    
    # 打印结果
    print(f"并行版本结果: {results['parallel']}")
    print(f"串行版本结果: {results['serial']}")
    print(f"加速比: {results['speedup']}")
    
    print("比较并行版本和串行版本测试通过")

def plot_benchmark_results(results: Dict[str, Dict[str, float]], title: str, ylabel: str):
    """绘制基准测试结果"""
    # 创建图表
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # 设置数据
    labels = list(results.keys())
    metrics = list(results[labels[0]].keys())
    
    # 设置柱状图
    x = np.arange(len(metrics))
    width = 0.35
    
    # 绘制柱状图
    for i, label in enumerate(labels):
        values = [results[label][metric] for metric in metrics]
        ax.bar(x + i * width, values, width, label=label)
    
    # 设置图表属性
    ax.set_xlabel('指标')
    ax.set_ylabel(ylabel)
    ax.set_title(title)
    ax.set_xticks(x + width / 2)
    ax.set_xticklabels(metrics)
    ax.legend()
    
    # 显示图表
    plt.tight_layout()
    plt.savefig(f"{title.replace(' ', '_')}.png")
    plt.close()

def plot_speedup(speedup: Dict[str, float], title: str):
    """绘制加速比"""
    # 创建图表
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # 设置数据
    metrics = list(speedup.keys())
    values = list(speedup.values())
    
    # 绘制柱状图
    ax.bar(metrics, values)
    
    # 设置图表属性
    ax.set_xlabel('指标')
    ax.set_ylabel('加速比')
    ax.set_title(title)
    
    # 添加水平线表示1倍加速比
    ax.axhline(y=1, color='r', linestyle='-')
    
    # 显示图表
    plt.tight_layout()
    plt.savefig(f"{title.replace(' ', '_')}.png")
    plt.close()

def run_all_tests():
    """运行所有测试"""
    test_register_operators()
    test_process_points()
    test_process_vector_field()
    test_compute_curvature()
    test_benchmark()
    test_compare_optimized_vs_standard()
    test_compare_parallel_vs_serial()

def run_benchmark_suite():
    """运行基准测试套件"""
    print("运行基准测试套件...")
    
    # 注册算子
    register_noncommutative_bundle_operators()
    
    # 比较优化版本和标准版本
    optimized_vs_standard = compare_optimized_vs_standard(
        "standard_noncommutative_bundle",
        n_points=10000,
        dimension=3,
        fiber_dimension=3,
        n_runs=10
    )
    
    # 比较并行版本和串行版本
    parallel_vs_serial = compare_parallel_vs_serial(
        "standard_noncommutative_bundle",
        n_points=10000,
        dimension=3,
        fiber_dimension=3,
        n_runs=10
    )
    
    # 绘制结果
    plot_benchmark_results(
        {"优化版本": optimized_vs_standard["optimized"], "标准版本": optimized_vs_standard["standard"]},
        "优化版本 vs 标准版本",
        "时间 (ms)"
    )
    
    plot_speedup(
        optimized_vs_standard["speedup"],
        "优化版本加速比"
    )
    
    plot_benchmark_results(
        {"并行版本": parallel_vs_serial["parallel"], "串行版本": parallel_vs_serial["serial"]},
        "并行版本 vs 串行版本",
        "时间 (ms)"
    )
    
    plot_speedup(
        parallel_vs_serial["speedup"],
        "并行版本加速比"
    )
    
    print("基准测试套件完成")

if __name__ == "__main__":
    # 运行所有测试
    run_all_tests()
    
    # 运行基准测试套件
    run_benchmark_suite()
