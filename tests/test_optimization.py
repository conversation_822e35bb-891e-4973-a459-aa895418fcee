"""
算子优化测试。

测试算子并行化、内存优化、缓存机制和算子融合的功能。
"""

import unittest
import numpy as np
import time
from typing import List, Dict, Any

from src.operators.optimization.parallel import (
    parallelize, parallel_map, parallel_reduce, parallel_filter,
    ThreadPoolExecutor, ProcessPoolExecutor, timing, auto_parallelize
)
from src.operators.optimization.memory import (
    memory_profile, memory_efficient, lazy_evaluation, streaming_operator,
    optimize_memory, LazySequence
)
from src.operators.optimization.cache import (
    cache_result, memoize, LRUCache, TTLCache, clear_cache, get_cache_stats
)
from src.operators.optimization.fusion import (
    register_fusion_rule, fuse_operators, create_fusion_pipeline,
    optimize_pipeline, register_common_fusion_rules
)
from src.operators.transform import TransformOperator
from src.operators.evolution import EvolutionOperator


class TestParallelization(unittest.TestCase):
    """测试并行化优化。"""
    
    def test_parallelize_decorator(self):
        """测试parallelize装饰器。"""
        # 创建测试函数
        @parallelize(mode='thread', max_workers=4)
        def square(x):
            return x * x
        
        # 创建测试数据
        data = list(range(100))
        
        # 调用函数
        result = square(data)
        
        # 验证结果
        self.assertEqual(result, [x * x for x in data])
    
    def test_parallel_map(self):
        """测试parallel_map函数。"""
        # 创建测试函数
        def cube(x):
            return x ** 3
        
        # 创建测试数据
        data = list(range(100))
        
        # 调用函数
        result = parallel_map(cube, data, mode='thread', max_workers=4)
        
        # 验证结果
        self.assertEqual(result, [x ** 3 for x in data])
    
    def test_parallel_reduce(self):
        """测试parallel_reduce函数。"""
        # 创建测试函数
        def add(x, y):
            return x + y
        
        # 创建测试数据
        data = list(range(100))
        
        # 调用函数
        result = parallel_reduce(add, data, initial=0, mode='thread', max_workers=4)
        
        # 验证结果
        self.assertEqual(result, sum(data))
    
    def test_parallel_filter(self):
        """测试parallel_filter函数。"""
        # 创建测试函数
        def is_even(x):
            return x % 2 == 0
        
        # 创建测试数据
        data = list(range(100))
        
        # 调用函数
        result = parallel_filter(is_even, data, mode='thread', max_workers=4)
        
        # 验证结果
        self.assertEqual(result, [x for x in data if x % 2 == 0])
    
    def test_timing_decorator(self):
        """测试timing装饰器。"""
        # 创建测试函数
        @timing(log=False)
        def slow_function():
            time.sleep(0.1)
            return 42
        
        # 调用函数
        result, execution_time = slow_function()
        
        # 验证结果
        self.assertEqual(result, 42)
        self.assertGreaterEqual(execution_time, 0.1)
    
    def test_auto_parallelize_decorator(self):
        """测试auto_parallelize装饰器。"""
        # 创建测试函数
        @auto_parallelize(threshold=50, mode='thread')
        def square(x):
            return x * x
        
        # 创建小数据集（不会并行化）
        small_data = list(range(10))
        small_result = square(small_data)
        self.assertEqual(small_result, [x * x for x in small_data])
        
        # 创建大数据集（会并行化）
        large_data = list(range(100))
        large_result = square(large_data)
        self.assertEqual(large_result, [x * x for x in large_data])


class TestMemoryOptimization(unittest.TestCase):
    """测试内存优化。"""
    
    def test_memory_profile_decorator(self):
        """测试memory_profile装饰器。"""
        # 创建测试函数
        @memory_profile(log=False)
        def create_list():
            return list(range(1000))
        
        # 调用函数
        result, memory_usage = create_list()
        
        # 验证结果
        self.assertEqual(result, list(range(1000)))
        self.assertIn('start_memory', memory_usage)
        self.assertIn('peak_memory', memory_usage)
        self.assertIn('end_memory', memory_usage)
        self.assertIn('memory_increase', memory_usage)
    
    def test_memory_efficient_decorator(self):
        """测试memory_efficient装饰器。"""
        # 创建测试函数
        @memory_efficient(chunk_size=10)
        def process_data(data):
            return [x * 2 for x in data]
        
        # 创建测试数据
        data = list(range(100))
        
        # 调用函数
        result = process_data(data)
        
        # 验证结果
        self.assertEqual(result, [x * 2 for x in data])
    
    def test_lazy_evaluation_decorator(self):
        """测试lazy_evaluation装饰器。"""
        # 创建测试函数
        @lazy_evaluation
        def generate_data():
            for i in range(100):
                yield i
        
        # 调用函数
        result = generate_data()
        
        # 验证结果是LazySequence
        self.assertIsInstance(result, LazySequence)
        
        # 验证可以迭代
        items = list(result)
        self.assertEqual(items, list(range(100)))
        
        # 验证可以索引
        self.assertEqual(result[10], 10)
    
    def test_streaming_operator_decorator(self):
        """测试streaming_operator装饰器。"""
        # 创建测试函数
        @streaming_operator(chunk_size=10)
        def process_stream(data):
            return [x * 2 for x in data]
        
        # 创建测试数据
        data = list(range(100))
        
        # 调用函数
        result = process_stream(data)
        
        # 验证结果是生成器
        self.assertTrue(hasattr(result, '__iter__'))
        
        # 验证结果
        items = list(result)
        self.assertEqual(items, [x * 2 for x in data])
    
    def test_optimize_memory(self):
        """测试optimize_memory函数。"""
        # 创建测试数据
        float64_array = np.array([1.0, 2.0, 3.0], dtype=np.float64)
        int64_array = np.array([1, 2, 3], dtype=np.int64)
        float_list = [1.0, 2.0, 3.0]
        int_list = [1, 2, 3]
        
        # 优化数据
        optimized_float64 = optimize_memory(float64_array)
        optimized_int64 = optimize_memory(int64_array)
        optimized_float_list = optimize_memory(float_list)
        optimized_int_list = optimize_memory(int_list)
        
        # 验证结果
        self.assertEqual(optimized_float64.dtype, np.float32)
        self.assertEqual(optimized_int64.dtype, np.int32)
        self.assertIsInstance(optimized_float_list, np.ndarray)
        self.assertEqual(optimized_float_list.dtype, np.float32)
        self.assertIsInstance(optimized_int_list, np.ndarray)
        self.assertEqual(optimized_int_list.dtype, np.int32)


class TestCaching(unittest.TestCase):
    """测试缓存机制。"""
    
    def setUp(self):
        """设置测试环境。"""
        clear_cache()
    
    def test_cache_result_decorator(self):
        """测试cache_result装饰器。"""
        # 创建计数器
        counter = [0]
        
        # 创建测试函数
        @cache_result
        def fibonacci(n):
            counter[0] += 1
            if n <= 1:
                return n
            return fibonacci(n - 1) + fibonacci(n - 2)
        
        # 调用函数
        result1 = fibonacci(10)
        count1 = counter[0]
        
        # 再次调用函数（应该从缓存获取）
        result2 = fibonacci(10)
        count2 = counter[0]
        
        # 验证结果
        self.assertEqual(result1, 55)
        self.assertEqual(result2, 55)
        self.assertEqual(count1, count2)  # 计数器不应该增加
    
    def test_memoize_decorator(self):
        """测试memoize装饰器。"""
        # 创建计数器
        counter = [0]
        
        # 创建测试函数
        @memoize
        def factorial(n):
            counter[0] += 1
            if n <= 1:
                return 1
            return n * factorial(n - 1)
        
        # 调用函数
        result1 = factorial(5)
        count1 = counter[0]
        
        # 再次调用函数（应该从缓存获取）
        result2 = factorial(5)
        count2 = counter[0]
        
        # 验证结果
        self.assertEqual(result1, 120)
        self.assertEqual(result2, 120)
        self.assertEqual(count1, count2)  # 计数器不应该增加
    
    def test_lru_cache(self):
        """测试LRU缓存。"""
        # 创建缓存
        cache = LRUCache(capacity=2)
        
        # 添加缓存项
        cache.put('a', 1)
        cache.put('b', 2)
        
        # 验证缓存命中
        self.assertEqual(cache.get('a'), 1)
        self.assertEqual(cache.get('b'), 2)
        
        # 添加新缓存项，应该淘汰最久未使用的项
        cache.put('c', 3)
        
        # 验证缓存命中和未命中
        self.assertIsNone(cache.get('a'))  # 'a'应该被淘汰
        self.assertEqual(cache.get('b'), 2)
        self.assertEqual(cache.get('c'), 3)
        
        # 验证缓存统计
        stats = cache.get_stats()
        self.assertEqual(stats['capacity'], 2)
        self.assertEqual(stats['size'], 2)
        self.assertEqual(stats['hits'], 3)
        self.assertEqual(stats['misses'], 1)
    
    def test_ttl_cache(self):
        """测试TTL缓存。"""
        # 创建缓存
        cache = TTLCache(ttl=0.1, capacity=2)
        
        # 添加缓存项
        cache.put('a', 1)
        cache.put('b', 2)
        
        # 验证缓存命中
        self.assertEqual(cache.get('a'), 1)
        self.assertEqual(cache.get('b'), 2)
        
        # 等待缓存过期
        time.sleep(0.2)
        
        # 验证缓存未命中
        self.assertIsNone(cache.get('a'))
        self.assertIsNone(cache.get('b'))
        
        # 验证缓存统计
        stats = cache.get_stats()
        self.assertEqual(stats['capacity'], 2)
        self.assertEqual(stats['size'], 2)  # 过期项仍在缓存中，但不可用
        self.assertEqual(stats['valid_items'], 0)  # 没有有效项
        self.assertEqual(stats['hits'], 2)
        self.assertEqual(stats['misses'], 2)


class TestFusion(unittest.TestCase):
    """测试算子融合。"""
    
    def setUp(self):
        """设置测试环境。"""
        register_common_fusion_rules()
    
    def test_fuse_transform_operators(self):
        """测试融合变换算子。"""
        # 创建两个线性变换算子
        transform1 = TransformOperator(
            transform_type='linear',
            dimension=2,
            parameters={
                'matrix': np.array([[1, 0], [0, 2]]),
                'offset': np.array([1, 1])
            }
        )
        
        transform2 = TransformOperator(
            transform_type='linear',
            dimension=2,
            parameters={
                'matrix': np.array([[2, 0], [0, 1]]),
                'offset': np.array([2, 2])
            }
        )
        
        # 融合算子
        fused = fuse_operators([transform1, transform2])
        
        # 验证结果
        self.assertEqual(len(fused), 1)  # 应该只有一个融合后的算子
        self.assertIsInstance(fused[0], TransformOperator)
        self.assertEqual(fused[0].transform_type, 'linear')
        
        # 验证融合后的矩阵和偏移
        expected_matrix = np.array([[2, 0], [0, 2]])
        expected_offset = np.array([4, 4])
        
        self.assertTrue(np.allclose(fused[0].parameters['matrix'], expected_matrix))
        self.assertTrue(np.allclose(fused[0].parameters['offset'], expected_offset))
    
    def test_fusion_pipeline(self):
        """测试融合流水线。"""
        # 创建变换算子
        transform1 = TransformOperator(
            transform_type='linear',
            dimension=2,
            parameters={
                'matrix': np.array([[1, 0], [0, 2]]),
                'offset': np.array([1, 1])
            }
        )
        
        transform2 = TransformOperator(
            transform_type='linear',
            dimension=2,
            parameters={
                'matrix': np.array([[2, 0], [0, 1]]),
                'offset': np.array([2, 2])
            }
        )
        
        # 创建演化算子
        evolution = EvolutionOperator(
            evolution_type='differential_equation',
            dimension=2,
            parameters={
                'equation': lambda t, x: -0.1 * x,
                'time_step': 0.1,
                'num_steps': 10
            }
        )
        
        # 创建融合流水线
        pipeline = create_fusion_pipeline([transform1, transform2, evolution])
        
        # 优化流水线
        optimized = optimize_pipeline(pipeline)
        
        # 验证结果
        self.assertEqual(len(optimized.operators), 2)  # 应该有两个算子（融合后的变换和演化）
        
        # 创建测试数据
        data = np.array([[1, 2], [3, 4]])
        
        # 应用流水线
        result = pipeline.apply(data)
        
        # 验证结果不为空
        self.assertIsNotNone(result)


if __name__ == '__main__':
    unittest.main()
