"""
测试优化版本的TransformOperator

本脚本测试优化版本的TransformOperator的基本功能和性能。
"""

import numpy as np
import matplotlib.pyplot as plt
import time
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.operators.transform import (
    TransformOperator,
    RUST_CORE_AVAILABLE,
    RUST_OPTIMIZED_AVAILABLE
)

if RUST_CORE_AVAILABLE:
    from src.operators.transform import RustCoreTransformOperator

if RUST_OPTIMIZED_AVAILABLE:
    from src.operators.transform import RustOptimizedTransformOperator, TransformError


def test_python_transform_operator():
    """测试Python实现的TransformOperator"""
    print("\n测试Python实现的TransformOperator...")
    
    # 创建随机数据
    np.random.seed(42)
    data = np.random.randn(1000, 2)
    
    # 创建线性变换算子
    transform_op = TransformOperator(
        transform_type='linear',
        dimension=2,
        parameters={
            'matrix': np.array([[np.cos(np.pi/4), -np.sin(np.pi/4)], 
                               [np.sin(np.pi/4), np.cos(np.pi/4)]]),
            'offset': np.array([0, 0])
        }
    )
    
    # 测量性能
    start_time = time.time()
    
    # 应用变换
    transformed_data = transform_op.apply(data)
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    # 打印结果
    print(f"变换类型: {transform_op.transform_type}")
    print(f"变换参数: {transform_op.parameters}")
    print(f"原始数据形状: {data.shape}")
    print(f"变换后数据形状: {transformed_data.shape}")
    print(f"执行时间: {elapsed_time:.6f} 秒")
    
    # 可视化原始数据和变换后的数据
    plt.figure(figsize=(10, 5))
    
    plt.subplot(1, 2, 1)
    plt.scatter(data[:100, 0], data[:100, 1], alpha=0.7)
    plt.title('Original Data')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.scatter(transformed_data[:100, 0], transformed_data[:100, 1], alpha=0.7)
    plt.title('Transformed Data (Python)')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('python_transform_operator_test.png')
    plt.close()
    
    print("Python实现的TransformOperator测试完成")
    
    return data, transformed_data, elapsed_time


def test_rust_core_transform_operator(data):
    """测试Rust核心实现的TransformOperator"""
    if not RUST_CORE_AVAILABLE:
        print("\nRust核心实现的TransformOperator不可用，跳过测试")
        return None, None
    
    print("\n测试Rust核心实现的TransformOperator...")
    
    # 创建线性变换算子
    transform_op = RustCoreTransformOperator(
        transform_type='linear',
        dimension=2,
        parameters={
            'matrix': np.array([[np.cos(np.pi/4), -np.sin(np.pi/4)], 
                               [np.sin(np.pi/4), np.cos(np.pi/4)]]),
            'offset': np.array([0, 0])
        }
    )
    
    # 测量性能
    start_time = time.time()
    
    # 应用变换
    transformed_data = transform_op.apply(data)
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    # 打印结果
    print(f"变换类型: {transform_op.transform_type}")
    print(f"变换参数: {transform_op.get_parameters()}")
    print(f"原始数据形状: {data.shape}")
    print(f"变换后数据形状: {transformed_data.shape}")
    print(f"执行时间: {elapsed_time:.6f} 秒")
    
    # 可视化原始数据和变换后的数据
    plt.figure(figsize=(10, 5))
    
    plt.subplot(1, 2, 1)
    plt.scatter(data[:100, 0], data[:100, 1], alpha=0.7)
    plt.title('Original Data')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.scatter(transformed_data[:100, 0], transformed_data[:100, 1], alpha=0.7)
    plt.title('Transformed Data (Rust Core)')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('rust_core_transform_operator_test.png')
    plt.close()
    
    print("Rust核心实现的TransformOperator测试完成")
    
    return transformed_data, elapsed_time


def test_rust_optimized_transform_operator(data):
    """测试优化版本的TransformOperator"""
    if not RUST_OPTIMIZED_AVAILABLE:
        print("\n优化版本的TransformOperator不可用，跳过测试")
        return None, None
    
    print("\n测试优化版本的TransformOperator...")
    
    # 创建线性变换算子
    transform_op = RustOptimizedTransformOperator(
        transform_type='linear',
        dimension=2,
        parameters={
            'matrix': np.array([[np.cos(np.pi/4), -np.sin(np.pi/4)], 
                               [np.sin(np.pi/4), np.cos(np.pi/4)]]),
            'offset': np.array([0, 0])
        }
    )
    
    # 测量性能
    start_time = time.time()
    
    # 应用变换
    try:
        transformed_data = transform_op.apply(data)
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        # 打印结果
        print(f"变换类型: {transform_op.transform_type}")
        print(f"变换参数: {transform_op.get_parameters()}")
        print(f"原始数据形状: {data.shape}")
        print(f"变换后数据形状: {transformed_data.shape}")
        print(f"执行时间: {elapsed_time:.6f} 秒")
        print(f"线程数: {transform_op.num_threads}")
        
        # 可视化原始数据和变换后的数据
        plt.figure(figsize=(10, 5))
        
        plt.subplot(1, 2, 1)
        plt.scatter(data[:100, 0], data[:100, 1], alpha=0.7)
        plt.title('Original Data')
        plt.grid(True)
        
        plt.subplot(1, 2, 2)
        plt.scatter(transformed_data[:100, 0], transformed_data[:100, 1], alpha=0.7)
        plt.title('Transformed Data (Optimized)')
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('optimized_transform_operator_test.png')
        plt.close()
        
        print("优化版本的TransformOperator测试完成")
        
        return transformed_data, elapsed_time
    
    except TransformError as e:
        print(f"变换错误: {e}")
        return None, None


def compare_implementations(python_result, python_time, rust_core_result, rust_core_time, rust_optimized_result, rust_optimized_time):
    """比较不同实现的结果和性能"""
    print("\n比较不同实现的结果和性能...")
    
    # 计算差异
    if rust_core_result is not None:
        diff_python_rust_core = np.abs(python_result - rust_core_result)
        max_diff_python_rust_core = np.max(diff_python_rust_core)
        mean_diff_python_rust_core = np.mean(diff_python_rust_core)
        
        print(f"Python vs Rust Core:")
        print(f"  最大差异: {max_diff_python_rust_core}")
        print(f"  平均差异: {mean_diff_python_rust_core}")
        print(f"  Python执行时间: {python_time:.6f} 秒")
        print(f"  Rust Core执行时间: {rust_core_time:.6f} 秒")
        print(f"  加速比: {python_time / rust_core_time:.2f}x")
    
    if rust_optimized_result is not None:
        diff_python_rust_optimized = np.abs(python_result - rust_optimized_result)
        max_diff_python_rust_optimized = np.max(diff_python_rust_optimized)
        mean_diff_python_rust_optimized = np.mean(diff_python_rust_optimized)
        
        print(f"Python vs Rust Optimized:")
        print(f"  最大差异: {max_diff_python_rust_optimized}")
        print(f"  平均差异: {mean_diff_python_rust_optimized}")
        print(f"  Python执行时间: {python_time:.6f} 秒")
        print(f"  Rust Optimized执行时间: {rust_optimized_time:.6f} 秒")
        print(f"  加速比: {python_time / rust_optimized_time:.2f}x")
    
    if rust_core_result is not None and rust_optimized_result is not None:
        diff_rust_core_rust_optimized = np.abs(rust_core_result - rust_optimized_result)
        max_diff_rust_core_rust_optimized = np.max(diff_rust_core_rust_optimized)
        mean_diff_rust_core_rust_optimized = np.mean(diff_rust_core_rust_optimized)
        
        print(f"Rust Core vs Rust Optimized:")
        print(f"  最大差异: {max_diff_rust_core_rust_optimized}")
        print(f"  平均差异: {mean_diff_rust_core_rust_optimized}")
        print(f"  Rust Core执行时间: {rust_core_time:.6f} 秒")
        print(f"  Rust Optimized执行时间: {rust_optimized_time:.6f} 秒")
        print(f"  加速比: {rust_core_time / rust_optimized_time:.2f}x")
    
    # 可视化性能比较
    implementations = []
    times = []
    
    implementations.append('Python')
    times.append(python_time)
    
    if rust_core_time is not None:
        implementations.append('Rust Core')
        times.append(rust_core_time)
    
    if rust_optimized_time is not None:
        implementations.append('Rust Optimized')
        times.append(rust_optimized_time)
    
    plt.figure(figsize=(10, 6))
    
    plt.bar(implementations, times)
    plt.title('Performance Comparison')
    plt.ylabel('Execution Time (seconds)')
    plt.grid(True, axis='y')
    
    # 添加数值标签
    for i, v in enumerate(times):
        plt.text(i, v + 0.01, f"{v:.6f}s", ha='center')
    
    plt.tight_layout()
    plt.savefig('performance_comparison.png')
    plt.close()
    
    print("比较完成")


def test_large_scale_performance():
    """测试大规模数据的性能"""
    print("\n测试大规模数据的性能...")
    
    # 创建大规模随机数据
    np.random.seed(42)
    data_sizes = [1000, 10000, 100000]
    
    # 创建结果表格
    print(f"{'数据大小':>10} | {'Python (s)':>12} | {'Rust Core (s)':>12} | {'Rust Optimized (s)':>18} | {'Core加速比':>10} | {'Optimized加速比':>16}")
    print(f"{'-'*10} | {'-'*12} | {'-'*12} | {'-'*18} | {'-'*10} | {'-'*16}")
    
    for size in data_sizes:
        data = np.random.randn(size, 2)
        
        # 测试Python实现
        transform_op_python = TransformOperator(
            transform_type='linear',
            dimension=2,
            parameters={
                'matrix': np.array([[np.cos(np.pi/4), -np.sin(np.pi/4)], 
                                   [np.sin(np.pi/4), np.cos(np.pi/4)]]),
                'offset': np.array([0, 0])
            }
        )
        
        start_time = time.time()
        transform_op_python.apply(data)
        python_time = time.time() - start_time
        
        # 测试Rust核心实现
        if RUST_CORE_AVAILABLE:
            transform_op_rust_core = RustCoreTransformOperator(
                transform_type='linear',
                dimension=2,
                parameters={
                    'matrix': np.array([[np.cos(np.pi/4), -np.sin(np.pi/4)], 
                                       [np.sin(np.pi/4), np.cos(np.pi/4)]]),
                    'offset': np.array([0, 0])
                }
            )
            
            start_time = time.time()
            transform_op_rust_core.apply(data)
            rust_core_time = time.time() - start_time
        else:
            rust_core_time = None
        
        # 测试优化版本
        if RUST_OPTIMIZED_AVAILABLE:
            transform_op_rust_optimized = RustOptimizedTransformOperator(
                transform_type='linear',
                dimension=2,
                parameters={
                    'matrix': np.array([[np.cos(np.pi/4), -np.sin(np.pi/4)], 
                                       [np.sin(np.pi/4), np.cos(np.pi/4)]]),
                    'offset': np.array([0, 0])
                }
            )
            
            try:
                start_time = time.time()
                transform_op_rust_optimized.apply(data)
                rust_optimized_time = time.time() - start_time
            except TransformError:
                rust_optimized_time = None
        else:
            rust_optimized_time = None
        
        # 计算加速比
        core_speedup = python_time / rust_core_time if rust_core_time else None
        optimized_speedup = python_time / rust_optimized_time if rust_optimized_time else None
        
        # 打印结果
        print(f"{size:>10} | {python_time:>12.6f} | {rust_core_time:>12.6f if rust_core_time else 'N/A':>12} | {rust_optimized_time:>18.6f if rust_optimized_time else 'N/A':>18} | {core_speedup:>10.2f if core_speedup else 'N/A':>10} | {optimized_speedup:>16.2f if optimized_speedup else 'N/A':>16}")
    
    print("大规模数据性能测试完成")


def main():
    """主函数"""
    print("开始测试优化版本的TransformOperator...")
    
    # 测试Python实现
    data, python_result, python_time = test_python_transform_operator()
    
    # 测试Rust核心实现
    rust_core_result, rust_core_time = test_rust_core_transform_operator(data)
    
    # 测试优化版本
    rust_optimized_result, rust_optimized_time = test_rust_optimized_transform_operator(data)
    
    # 比较实现
    compare_implementations(python_result, python_time, rust_core_result, rust_core_time, rust_optimized_result, rust_optimized_time)
    
    # 测试大规模数据的性能
    test_large_scale_performance()
    
    print("\n所有测试完成")


if __name__ == "__main__":
    main()
