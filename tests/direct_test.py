"""
直接测试模块

本模块直接测试我们实现的范畴算子，不依赖于项目的导入结构。
"""

import sys
import os
import unittest

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 直接导入我们实现的算子
sys.path.append('/home/<USER>/CascadeProjects/TTE')

# 创建测试数据
source_category = {
    'id': 'source_category',
    'type': 'category',
    'objects': {
        'A': {'id': 'A', 'type': 'object'},
        'B': {'id': 'B', 'type': 'object'}
    },
    'morphisms': {
        'f': {'id': 'f', 'type': 'morphism', 'source': 'A', 'target': 'B'}
    }
}

target_category = {
    'id': 'target_category',
    'type': 'category',
    'objects': {
        'X': {'id': 'X', 'type': 'object'},
        'Y': {'id': 'Y', 'type': 'object'}
    },
    'morphisms': {
        'g': {'id': 'g', 'type': 'morphism', 'source': 'X', 'target': 'Y'}
    }
}

functor = {
    'id': 'F',
    'type': 'functor',
    'source_category': source_category,
    'target_category': target_category,
    'mapping': {
        'A': 'X',
        'B': 'Y',
        'f': 'g'
    }
}

diagram = {
    'id': 'diagram',
    'type': 'diagram',
    'objects': {
        'A': {'id': 'A', 'type': 'object'},
        'B': {'id': 'B', 'type': 'object'}
    },
    'morphisms': {}
}

# 定义一个简单的接口类
class OperatorInterface:
    def __init__(self, **kwargs):
        pass
    
    def apply(self, input_data, **kwargs):
        pass
    
    def get_metadata(self):
        pass
    
    def is_compatible_with(self, other_operator):
        pass
    
    def get_performance_metrics(self):
        pass
    
    def compose(self, other_operator):
        pass
    
    def get_parameters(self):
        pass
    
    def set_parameters(self, parameters):
        pass
    
    def to_rust(self):
        pass
    
    def get_complexity(self):
        pass

# 测试伴随函子算子
print("测试伴随函子算子...")
try:
    # 导入伴随函子算子
    from src.operators.category.adjoint import AdjointFunctorOperator
    
    # 创建伴随函子算子
    adjoint_operator = AdjointFunctorOperator(adjoint_type='left')
    
    # 应用算子
    result = adjoint_operator.apply({
        'functor': functor,
        'source_category': source_category,
        'target_category': target_category
    })
    
    # 验证结果
    assert 'adjoint_functor' in result
    assert 'adjoint_type' in result
    assert 'unit' in result
    assert 'counit' in result
    assert result['adjoint_type'] == 'left'
    
    # 获取元数据
    metadata = adjoint_operator.get_metadata()
    
    # 验证元数据
    assert metadata['name'] == 'AdjointFunctorOperator'
    assert metadata['type'] == 'category_theory'
    assert metadata['input_type'] == 'functor'
    assert metadata['output_type'] == 'adjoint_functor'
    
    print("伴随函子算子测试通过")
except ImportError as e:
    print(f"导入伴随函子算子失败: {e}")
except Exception as e:
    print(f"伴随函子算子测试失败: {e}")

# 测试极限算子
print("\n测试极限算子...")
try:
    # 导入极限算子
    from src.operators.category.limit import LimitOperator
    
    # 创建极限算子
    limit_operator = LimitOperator(diagram_type='product')
    
    # 应用算子
    result = limit_operator.apply({
        'category': source_category,
        'diagram': diagram
    })
    
    # 验证结果
    assert 'type' in result
    assert result['type'] == 'limit'
    assert 'limit_type' in result
    assert result['limit_type'] == 'product'
    assert 'diagram' in result
    assert 'limit_object' in result
    assert 'projections' in result
    
    # 获取元数据
    metadata = limit_operator.get_metadata()
    
    # 验证元数据
    assert metadata['name'] == 'LimitOperator'
    assert metadata['type'] == 'category_theory'
    assert metadata['input_type'] == 'category_diagram'
    assert metadata['output_type'] == 'limit'
    
    print("极限算子测试通过")
except ImportError as e:
    print(f"导入极限算子失败: {e}")
except Exception as e:
    print(f"极限算子测试失败: {e}")

# 测试余极限算子
print("\n测试余极限算子...")
try:
    # 导入余极限算子
    from src.operators.category.limit import ColimitOperator
    
    # 创建余极限算子
    colimit_operator = ColimitOperator(diagram_type='coproduct')
    
    # 应用算子
    result = colimit_operator.apply({
        'category': source_category,
        'diagram': diagram
    })
    
    # 验证结果
    assert 'type' in result
    assert result['type'] == 'colimit'
    assert 'colimit_type' in result
    assert result['colimit_type'] == 'coproduct'
    assert 'diagram' in result
    assert 'colimit_object' in result
    assert 'injections' in result
    
    # 获取元数据
    metadata = colimit_operator.get_metadata()
    
    # 验证元数据
    assert metadata['name'] == 'ColimitOperator'
    assert metadata['type'] == 'category_theory'
    assert metadata['input_type'] == 'category_diagram'
    assert metadata['output_type'] == 'colimit'
    
    print("余极限算子测试通过")
except ImportError as e:
    print(f"导入余极限算子失败: {e}")
except Exception as e:
    print(f"余极限算子测试失败: {e}")

# 测试范畴积算子
print("\n测试范畴积算子...")
try:
    # 导入范畴积算子
    from src.operators.category.product import ProductOperator
    
    # 创建范畴积算子
    product_operator = ProductOperator(product_type='direct')
    
    # 应用算子
    result = product_operator.apply({
        'categories': [source_category, target_category]
    })
    
    # 验证结果
    assert 'id' in result
    assert 'type' in result
    assert result['type'] == 'category'
    assert 'product_type' in result
    assert result['product_type'] == 'direct'
    assert 'component_categories' in result
    assert 'objects' in result
    assert 'morphisms' in result
    
    # 获取元数据
    metadata = product_operator.get_metadata()
    
    # 验证元数据
    assert metadata['name'] == 'ProductOperator'
    assert metadata['type'] == 'category_theory'
    assert metadata['input_type'] == 'category_list'
    assert metadata['output_type'] == 'category_product'
    
    print("范畴积算子测试通过")
except ImportError as e:
    print(f"导入范畴积算子失败: {e}")
except Exception as e:
    print(f"范畴积算子测试失败: {e}")

# 测试范畴余积算子
print("\n测试范畴余积算子...")
try:
    # 导入范畴余积算子
    from src.operators.category.product import CoproductOperator
    
    # 创建范畴余积算子
    coproduct_operator = CoproductOperator(coproduct_type='direct')
    
    # 应用算子
    result = coproduct_operator.apply({
        'categories': [source_category, target_category]
    })
    
    # 验证结果
    assert 'id' in result
    assert 'type' in result
    assert result['type'] == 'category'
    assert 'coproduct_type' in result
    assert result['coproduct_type'] == 'direct'
    assert 'component_categories' in result
    assert 'objects' in result
    assert 'morphisms' in result
    
    # 获取元数据
    metadata = coproduct_operator.get_metadata()
    
    # 验证元数据
    assert metadata['name'] == 'CoproductOperator'
    assert metadata['type'] == 'category_theory'
    assert metadata['input_type'] == 'category_list'
    assert metadata['output_type'] == 'category_coproduct'
    
    print("范畴余积算子测试通过")
except ImportError as e:
    print(f"导入范畴余积算子失败: {e}")
except Exception as e:
    print(f"范畴余积算子测试失败: {e}")

print("\n所有测试完成")
