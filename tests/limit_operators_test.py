"""
极限和余极限算子简单测试模块

本模块直接测试极限和余极限算子的基本功能，不依赖于项目的复杂导入结构。
"""

import unittest
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))


class TestLimitOperators(unittest.TestCase):
    """极限和余极限算子测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建测试数据
        self.category = {
            'id': 'category',
            'type': 'category',
            'objects': {
                'A': {'id': 'A', 'type': 'object'},
                'B': {'id': 'B', 'type': 'object'},
                'C': {'id': 'C', 'type': 'object'}
            },
            'morphisms': {
                'f': {'id': 'f', 'type': 'morphism', 'source': 'A', 'target': 'B'},
                'g': {'id': 'g', 'type': 'morphism', 'source': 'B', 'target': 'C'}
            }
        }
        
        self.diagram = {
            'id': 'diagram',
            'type': 'diagram',
            'objects': {
                'A': {'id': 'A', 'type': 'object'},
                'B': {'id': 'B', 'type': 'object'}
            },
            'morphisms': {}
        }
    
    def test_limit_operator(self):
        """测试极限算子"""
        try:
            # 直接导入极限算子
            from src.operators.category.limit import LimitOperator
            
            # 创建极限算子
            limit_operator = LimitOperator(diagram_type='product')
            
            # 应用算子
            result = limit_operator.apply({
                'category': self.category,
                'diagram': self.diagram
            })
            
            # 验证结果
            self.assertIn('type', result)
            self.assertEqual(result['type'], 'limit')
            self.assertIn('limit_type', result)
            self.assertEqual(result['limit_type'], 'product')
            self.assertIn('diagram', result)
            self.assertIn('limit_object', result)
            self.assertIn('projections', result)
            
            # 获取元数据
            metadata = limit_operator.get_metadata()
            
            # 验证元数据
            self.assertEqual(metadata['name'], 'LimitOperator')
            self.assertEqual(metadata['type'], 'category_theory')
            self.assertEqual(metadata['input_type'], 'category_diagram')
            self.assertEqual(metadata['output_type'], 'limit')
            
            print("极限算子测试通过")
        except ImportError as e:
            print(f"导入极限算子失败: {e}")
            raise
    
    def test_colimit_operator(self):
        """测试余极限算子"""
        try:
            # 直接导入余极限算子
            from src.operators.category.limit import ColimitOperator
            
            # 创建余极限算子
            colimit_operator = ColimitOperator(diagram_type='coproduct')
            
            # 应用算子
            result = colimit_operator.apply({
                'category': self.category,
                'diagram': self.diagram
            })
            
            # 验证结果
            self.assertIn('type', result)
            self.assertEqual(result['type'], 'colimit')
            self.assertIn('colimit_type', result)
            self.assertEqual(result['colimit_type'], 'coproduct')
            self.assertIn('diagram', result)
            self.assertIn('colimit_object', result)
            self.assertIn('injections', result)
            
            # 获取元数据
            metadata = colimit_operator.get_metadata()
            
            # 验证元数据
            self.assertEqual(metadata['name'], 'ColimitOperator')
            self.assertEqual(metadata['type'], 'category_theory')
            self.assertEqual(metadata['input_type'], 'category_diagram')
            self.assertEqual(metadata['output_type'], 'colimit')
            
            print("余极限算子测试通过")
        except ImportError as e:
            print(f"导入余极限算子失败: {e}")
            raise


if __name__ == '__main__':
    unittest.main()
