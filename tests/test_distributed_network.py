#!/usr/bin/env python3
"""
分布式网络测试脚本

该脚本测试超越态思维引擎的分布式网络功能，包括物理层、数据层和计算层。
"""

import unittest
import time
import threading
import logging
from src.distributed import (
    PhysicalLayer,
    DataLayer,
    ConsistencyLevel,
    ComputationLayer,
    TaskPriority,
    TaskStatus,
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class TestPhysicalLayer(unittest.TestCase):
    """物理层测试"""
    
    def setUp(self):
        """测试前准备"""
        self.node1 = PhysicalLayer(
            node_id="test_node1",
            host="127.0.0.1",
            port=9100,
            discovery_port=9101
        )
        self.node2 = PhysicalLayer(
            node_id="test_node2",
            host="127.0.0.1",
            port=9102,
            discovery_port=9103
        )
        
        self.node1.start()
        self.node2.start()
        
        # 等待节点发现
        time.sleep(2)
    
    def tearDown(self):
        """测试后清理"""
        self.node1.stop()
        self.node2.stop()
    
    def test_node_discovery(self):
        """测试节点发现"""
        nodes = self.node1.get_nodes()
        self.assertIn("test_node2", nodes)
        
        nodes = self.node2.get_nodes()
        self.assertIn("test_node1", nodes)
    
    def test_message_sending(self):
        """测试消息发送"""
        received_messages = []
        
        def message_handler(sender_id, message):
            received_messages.append((sender_id, message))
        
        self.node2.register_message_handler(message_handler)
        
        # 发送消息
        message = b"Hello, node2!"
        self.node1.send_message("test_node2", message)
        
        # 等待消息接收
        time.sleep(1)
        
        self.assertEqual(len(received_messages), 1)
        self.assertEqual(received_messages[0][0], "test_node1")
        self.assertEqual(received_messages[0][1], message)

class TestDataLayer(unittest.TestCase):
    """数据层测试"""
    
    def setUp(self):
        """测试前准备"""
        self.physical_node1 = PhysicalLayer(
            node_id="test_data_node1",
            host="127.0.0.1",
            port=9200,
            discovery_port=9201
        )
        self.physical_node2 = PhysicalLayer(
            node_id="test_data_node2",
            host="127.0.0.1",
            port=9202,
            discovery_port=9203
        )
        
        self.data_node1 = DataLayer(
            node_id="test_data_node1",
            physical_layer=self.physical_node1,
            consistency_level=ConsistencyLevel.EVENTUAL
        )
        self.data_node2 = DataLayer(
            node_id="test_data_node2",
            physical_layer=self.physical_node2,
            consistency_level=ConsistencyLevel.EVENTUAL
        )
        
        self.physical_node1.start()
        self.physical_node2.start()
        self.data_node1.start()
        self.data_node2.start()
        
        # 等待节点发现
        time.sleep(2)
    
    def tearDown(self):
        """测试后清理"""
        self.data_node1.stop()
        self.data_node2.stop()
        self.physical_node1.stop()
        self.physical_node2.stop()
    
    def test_data_operations(self):
        """测试数据操作"""
        # 存储数据
        self.data_node1.put("test_key", "test_value")
        
        # 获取本地数据
        value = self.data_node1.get("test_key")
        self.assertEqual(value, "test_value")
        
        # 等待数据同步
        time.sleep(2)
        
        # 获取远程数据
        value = self.data_node2.get("test_key")
        self.assertEqual(value, "test_value")
        
        # 删除数据
        self.data_node1.delete("test_key")
        
        # 等待数据同步
        time.sleep(2)
        
        # 检查数据是否已删除
        self.assertFalse(self.data_node1.exists("test_key"))
        self.assertFalse(self.data_node2.exists("test_key"))

class TestComputationLayer(unittest.TestCase):
    """计算层测试"""
    
    def setUp(self):
        """测试前准备"""
        self.physical_node1 = PhysicalLayer(
            node_id="test_comp_node1",
            host="127.0.0.1",
            port=9300,
            discovery_port=9301
        )
        self.physical_node2 = PhysicalLayer(
            node_id="test_comp_node2",
            host="127.0.0.1",
            port=9302,
            discovery_port=9303
        )
        
        self.data_node1 = DataLayer(
            node_id="test_comp_node1",
            physical_layer=self.physical_node1,
            consistency_level=ConsistencyLevel.EVENTUAL
        )
        self.data_node2 = DataLayer(
            node_id="test_comp_node2",
            physical_layer=self.physical_node2,
            consistency_level=ConsistencyLevel.EVENTUAL
        )
        
        self.comp_node1 = ComputationLayer(
            node_id="test_comp_node1",
            physical_layer=self.physical_node1,
            data_layer=self.data_node1
        )
        self.comp_node2 = ComputationLayer(
            node_id="test_comp_node2",
            physical_layer=self.physical_node2,
            data_layer=self.data_node2
        )
        
        self.physical_node1.start()
        self.physical_node2.start()
        self.data_node1.start()
        self.data_node2.start()
        self.comp_node1.start()
        self.comp_node2.start()
        
        # 等待节点发现
        time.sleep(2)
    
    def tearDown(self):
        """测试后清理"""
        self.comp_node1.stop()
        self.comp_node2.stop()
        self.data_node1.stop()
        self.data_node2.stop()
        self.physical_node1.stop()
        self.physical_node2.stop()
    
    def test_function_execution(self):
        """测试函数执行"""
        # 定义测试函数
        def add(a, b):
            return a + b
        
        # 注册函数
        self.comp_node1.register_function(add)
        
        # 提交任务
        task_id = self.comp_node1.submit_task(
            function_name="add",
            args=[10, 20],
            priority=TaskPriority.NORMAL
        )
        
        # 获取任务结果
        result = self.comp_node1.get_task_result(task_id, timeout=5)
        self.assertEqual(result, 30)
    
    def test_task_cancellation(self):
        """测试任务取消"""
        # 定义测试函数
        def long_task():
            time.sleep(10)
            return "completed"
        
        # 注册函数
        self.comp_node1.register_function(long_task)
        
        # 提交任务
        task_id = self.comp_node1.submit_task(
            function_name="long_task",
            priority=TaskPriority.NORMAL
        )
        
        # 等待任务开始执行
        time.sleep(1)
        
        # 取消任务
        success = self.comp_node1.cancel_task(task_id)
        self.assertTrue(success)
        
        # 检查任务状态
        task = self.comp_node1.get_task(task_id)
        self.assertEqual(task.status, TaskStatus.CANCELED)

if __name__ == "__main__":
    unittest.main()
