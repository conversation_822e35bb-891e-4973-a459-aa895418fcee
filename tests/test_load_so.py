"""
测试直接加载.so文件

本脚本测试是否可以直接使用ctypes加载.so文件。
"""

import os
import sys
import ctypes

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def test_load_explanation_lib():
    """测试加载解释算子库"""
    # 构建库文件路径
    lib_path = os.path.join(os.path.dirname(__file__), "..", "src", "operators", "explanation", "libexplanation_v24.so")
    lib_path = os.path.abspath(lib_path)
    
    print(f"尝试加载库文件: {lib_path}")
    print(f"文件是否存在: {os.path.exists(lib_path)}")
    
    try:
        # 尝试加载库
        lib = ctypes.CDLL(lib_path)
        print(f"成功加载库 {lib_path}")
        print(f"库对象: {lib}")
        
        # 尝试获取库中的符号
        symbols = []
        for sym in dir(lib):
            if not sym.startswith('_'):
                symbols.append(sym)
        
        print(f"库中的符号: {symbols}")
        
        return True
    except Exception as e:
        print(f"错误：无法加载库 {lib_path}: {e}")
        return False

def test_load_verification_lib():
    """测试加载验证算子库"""
    # 构建库文件路径
    lib_path = os.path.join(os.path.dirname(__file__), "..", "src", "operators", "verification", "libverifiability_v24.so")
    lib_path = os.path.abspath(lib_path)
    
    print(f"尝试加载库文件: {lib_path}")
    print(f"文件是否存在: {os.path.exists(lib_path)}")
    
    try:
        # 尝试加载库
        lib = ctypes.CDLL(lib_path)
        print(f"成功加载库 {lib_path}")
        print(f"库对象: {lib}")
        
        # 尝试获取库中的符号
        symbols = []
        for sym in dir(lib):
            if not sym.startswith('_'):
                symbols.append(sym)
        
        print(f"库中的符号: {symbols}")
        
        return True
    except Exception as e:
        print(f"错误：无法加载库 {lib_path}: {e}")
        return False

if __name__ == "__main__":
    print("测试加载Rust库...")
    explanation_success = test_load_explanation_lib()
    print("\n" + "-" * 50 + "\n")
    verification_success = test_load_verification_lib()
    
    if explanation_success and verification_success:
        print("\n所有库加载成功！")
        sys.exit(0)
    else:
        print("\n库加载失败！")
        sys.exit(1)
