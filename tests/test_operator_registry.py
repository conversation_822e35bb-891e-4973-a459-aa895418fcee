"""
算子注册表测试脚本
"""

import sys
import os
import unittest

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

from src.operators.registry import operator_registry


class TestOperatorRegistry(unittest.TestCase):
    """算子注册表测试类"""
    
    def test_registry_initialization(self):
        """测试注册表初始化"""
        # 验证注册表是否已初始化
        self.assertIsNotNone(operator_registry)
        
        # 验证注册表是否包含预期的类别
        categories = operator_registry.get_categories()
        self.assertIn("explanation", categories)
        self.assertIn("verification", categories)
    
    def test_explanation_operators(self):
        """测试解释算子注册"""
        # 获取解释算子
        explanation_operators = operator_registry.get_operators_in_category("explanation")
        
        # 验证是否包含预期的算子
        self.assertIn("multilevel_explanation", explanation_operators)
        self.assertIn("explanation_quality", explanation_operators)
        self.assertIn("visualization", explanation_operators)
        
        # 尝试获取多层次解释生成算子
        multilevel_explanation_operator = operator_registry.get_operator("explanation", "multilevel_explanation")
        self.assertIsNotNone(multilevel_explanation_operator)
        
        # 验证是否可以创建算子实例
        try:
            operator_instance = multilevel_explanation_operator()
            self.assertIsNotNone(operator_instance)
            
            # 验证算子是否具有预期的方法
            self.assertTrue(hasattr(operator_instance, "apply"))
            self.assertTrue(hasattr(operator_instance, "get_metadata"))
        except Exception as e:
            print(f"创建多层次解释生成算子实例失败: {e}")
    
    def test_verification_operators(self):
        """测试验证算子注册"""
        # 获取验证算子
        verification_operators = operator_registry.get_operators_in_category("verification")
        
        # 验证是否包含预期的算子
        self.assertIn("multi_method_verification", verification_operators)
        self.assertIn("consistency_verification", verification_operators)
        self.assertIn("realtime_verification", verification_operators)
        
        # 尝试获取多方法验证算子
        multi_method_verification_operator = operator_registry.get_operator("verification", "multi_method_verification")
        self.assertIsNotNone(multi_method_verification_operator)
        
        # 验证是否可以创建算子实例
        try:
            operator_instance = multi_method_verification_operator()
            self.assertIsNotNone(operator_instance)
            
            # 验证算子是否具有预期的方法
            self.assertTrue(hasattr(operator_instance, "apply"))
            self.assertTrue(hasattr(operator_instance, "get_metadata"))
        except Exception as e:
            print(f"创建多方法验证算子实例失败: {e}")
    
    def test_rust_operators(self):
        """测试Rust实现的算子注册"""
        # 获取Rust实现的解释算子
        rust_multilevel_explanation_operator = operator_registry.get_operator("explanation", "rust_multilevel_explanation")
        
        # 如果Rust实现可用，验证是否可以创建算子实例
        if rust_multilevel_explanation_operator is not None:
            try:
                operator_instance = rust_multilevel_explanation_operator()
                self.assertIsNotNone(operator_instance)
                
                # 验证算子是否具有预期的方法
                self.assertTrue(hasattr(operator_instance, "apply"))
                self.assertTrue(hasattr(operator_instance, "get_metadata"))
                
                # 验证是否是Rust实现
                self.assertTrue(hasattr(operator_instance, "to_rust"))
                self.assertTrue(operator_instance.to_rust())
            except Exception as e:
                print(f"创建Rust实现的多层次解释生成算子实例失败: {e}")
        else:
            print("Rust实现的多层次解释生成算子不可用")
        
        # 获取Rust实现的验证算子
        rust_multi_method_verification_operator = operator_registry.get_operator("verification", "rust_multi_method_verification")
        
        # 如果Rust实现可用，验证是否可以创建算子实例
        if rust_multi_method_verification_operator is not None:
            try:
                operator_instance = rust_multi_method_verification_operator()
                self.assertIsNotNone(operator_instance)
                
                # 验证算子是否具有预期的方法
                self.assertTrue(hasattr(operator_instance, "apply"))
                self.assertTrue(hasattr(operator_instance, "get_metadata"))
                
                # 验证是否是Rust实现
                self.assertTrue(hasattr(operator_instance, "to_rust"))
                self.assertTrue(operator_instance.to_rust())
            except Exception as e:
                print(f"创建Rust实现的多方法验证算子实例失败: {e}")
        else:
            print("Rust实现的多方法验证算子不可用")
    
    def test_registry_stats(self):
        """测试注册表统计信息"""
        # 获取注册表统计信息
        stats = operator_registry.get_stats()
        
        # 验证是否包含预期的统计信息
        self.assertIn("explanation_count", stats)
        self.assertIn("verification_count", stats)
        self.assertIn("total_count", stats)
        
        # 验证统计信息是否合理
        self.assertGreaterEqual(stats["explanation_count"], 3)  # 至少包含3个解释算子
        self.assertGreaterEqual(stats["verification_count"], 3)  # 至少包含3个验证算子
        self.assertGreaterEqual(stats["total_count"], 6)  # 至少包含6个算子


if __name__ == '__main__':
    unittest.main()
