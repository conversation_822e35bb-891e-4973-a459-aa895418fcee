"""
范畴积和余积算子简单测试模块

本模块直接测试范畴积和余积算子的基本功能，不依赖于项目的复杂导入结构。
"""

import unittest
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))


class TestProductOperators(unittest.TestCase):
    """范畴积和余积算子测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建测试数据
        self.category1 = {
            'id': 'category1',
            'type': 'category',
            'objects': {
                'A': {'id': 'A', 'type': 'object'},
                'B': {'id': 'B', 'type': 'object'}
            },
            'morphisms': {
                'f': {'id': 'f', 'type': 'morphism', 'source': 'A', 'target': 'B'}
            }
        }
        
        self.category2 = {
            'id': 'category2',
            'type': 'category',
            'objects': {
                'X': {'id': 'X', 'type': 'object'},
                'Y': {'id': 'Y', 'type': 'object'}
            },
            'morphisms': {
                'g': {'id': 'g', 'type': 'morphism', 'source': 'X', 'target': 'Y'}
            }
        }
    
    def test_product_operator(self):
        """测试范畴积算子"""
        try:
            # 直接导入范畴积算子
            from src.operators.category.product import ProductOperator
            
            # 创建范畴积算子
            product_operator = ProductOperator(product_type='direct')
            
            # 应用算子
            result = product_operator.apply({
                'categories': [self.category1, self.category2]
            })
            
            # 验证结果
            self.assertIn('id', result)
            self.assertIn('type', result)
            self.assertEqual(result['type'], 'category')
            self.assertIn('product_type', result)
            self.assertEqual(result['product_type'], 'direct')
            self.assertIn('component_categories', result)
            self.assertIn('objects', result)
            self.assertIn('morphisms', result)
            
            # 获取元数据
            metadata = product_operator.get_metadata()
            
            # 验证元数据
            self.assertEqual(metadata['name'], 'ProductOperator')
            self.assertEqual(metadata['type'], 'category_theory')
            self.assertEqual(metadata['input_type'], 'category_list')
            self.assertEqual(metadata['output_type'], 'category_product')
            
            print("范畴积算子测试通过")
        except ImportError as e:
            print(f"导入范畴积算子失败: {e}")
            raise
    
    def test_coproduct_operator(self):
        """测试范畴余积算子"""
        try:
            # 直接导入范畴余积算子
            from src.operators.category.product import CoproductOperator
            
            # 创建范畴余积算子
            coproduct_operator = CoproductOperator(coproduct_type='direct')
            
            # 应用算子
            result = coproduct_operator.apply({
                'categories': [self.category1, self.category2]
            })
            
            # 验证结果
            self.assertIn('id', result)
            self.assertIn('type', result)
            self.assertEqual(result['type'], 'category')
            self.assertIn('coproduct_type', result)
            self.assertEqual(result['coproduct_type'], 'direct')
            self.assertIn('component_categories', result)
            self.assertIn('objects', result)
            self.assertIn('morphisms', result)
            
            # 获取元数据
            metadata = coproduct_operator.get_metadata()
            
            # 验证元数据
            self.assertEqual(metadata['name'], 'CoproductOperator')
            self.assertEqual(metadata['type'], 'category_theory')
            self.assertEqual(metadata['input_type'], 'category_list')
            self.assertEqual(metadata['output_type'], 'category_coproduct')
            
            print("范畴余积算子测试通过")
        except ImportError as e:
            print(f"导入范畴余积算子失败: {e}")
            raise


if __name__ == '__main__':
    unittest.main()
