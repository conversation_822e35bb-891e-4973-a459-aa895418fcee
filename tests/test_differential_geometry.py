"""
微分几何算子测试模块

本模块包含对微分几何算子的测试，如NoncommutativeBundle和ParallelTransport。
"""

import unittest
import numpy as np
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入要测试的模块
from src.operators.differential_geometry.bundle import NoncommutativeBundle
from src.operators.differential_geometry.transport import ParallelTransport
from src.operators.differential_geometry.connection import ConnectionCalculator
from src.operators.differential_geometry.curvature import CurvatureAnalyzer


class TestNoncommutativeBundle(unittest.TestCase):
    """测试NoncommutativeBundle算子"""

    def setUp(self):
        """设置测试环境"""
        self.bundle = NoncommutativeBundle(dimension=3, fiber_dimension=2)

    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.bundle.dimension, 3)
        self.assertEqual(self.bundle.fiber_dimension, 2)
        self.assertEqual(self.bundle.connection_type, "levi-civita")
        self.assertTrue(self.bundle.curvature_enabled)

    def test_apply_to_points(self):
        """测试应用到点集"""
        points = np.array([[1.0, 0.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 1.0]])
        result = self.bundle.apply(points)

        # 检查结果形状
        self.assertEqual(result.shape, (3, 5))  # 3个点，每个点5个坐标（3+2）

        # 检查前3个坐标是否与输入点相同
        np.testing.assert_array_almost_equal(result[:, :3], points)

    def test_apply_to_vector_field(self):
        """测试应用到向量场"""
        vector_field = np.ones((3, 3, 2))  # 3个点，每个点一个3x2的向量
        result = self.bundle.apply(vector_field)

        # 检查结果形状
        self.assertEqual(result.shape, (3, 3, 2))

    def test_get_metadata(self):
        """测试获取元数据"""
        metadata = self.bundle.get_metadata()
        self.assertEqual(metadata["name"], "NoncommutativeBundle")
        self.assertEqual(metadata["dimension"], 3)
        self.assertEqual(metadata["fiber_dimension"], 2)

    def test_compute_curvature(self):
        """测试计算曲率"""
        point = np.array([1.0, 0.0, 0.0])
        curvature = self.bundle.compute_curvature(point)

        # 检查曲率形状
        self.assertEqual(curvature.shape, (3, 3, 2))

    def test_compute_torsion(self):
        """测试计算挠率"""
        point = np.array([1.0, 0.0, 0.0])
        torsion = self.bundle.compute_torsion(point)

        # 检查挠率形状
        self.assertEqual(torsion.shape, (3, 3, 3))


class TestParallelTransport(unittest.TestCase):
    """测试ParallelTransport算子"""

    def setUp(self):
        """设置测试环境"""
        self.transport = ParallelTransport(dimension=3, fiber_dimension=2)

    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.transport.dimension, 3)
        self.assertEqual(self.transport.fiber_dimension, 2)
        self.assertEqual(self.transport.step_size, 0.01)
        self.assertTrue(self.transport.adaptive_steps)

    def test_transport_vector(self):
        """测试传输向量"""
        # 创建一个圆形路径
        t = np.linspace(0, 2*np.pi, 100)
        path = np.zeros((100, 3))
        path[:, 0] = np.cos(t)
        path[:, 1] = np.sin(t)

        # 初始向量
        vector = np.array([1.0, 0.0])

        # 传输向量
        result = self.transport.apply((path, vector))

        # 检查结果形状
        self.assertEqual(result.shape, (100, 2))

        # 检查初始向量是否正确
        np.testing.assert_array_almost_equal(result[0], vector)

    def test_transport_vector_field(self):
        """测试传输向量场"""
        # 创建一个直线路径
        path = np.zeros((10, 3))
        path[:, 0] = np.linspace(0, 1, 10)

        # 创建向量场
        vector_field = np.ones((10, 2))

        # 传输向量场
        result = self.transport.apply((path, vector_field))

        # 检查结果形状
        self.assertEqual(result.shape, (10, 2))

    def test_get_metadata(self):
        """测试获取元数据"""
        metadata = self.transport.get_metadata()
        self.assertEqual(metadata["name"], "ParallelTransport")
        self.assertEqual(metadata["dimension"], 3)
        self.assertEqual(metadata["fiber_dimension"], 2)

    def test_compute_holonomy(self):
        """测试计算全息变换"""
        # 创建一个圆形路径
        t = np.linspace(0, 2*np.pi, 100)
        loop = np.zeros((100, 3))
        loop[:, 0] = np.cos(t)
        loop[:, 1] = np.sin(t)

        # 初始向量
        initial_vector = np.array([1.0, 0.0])

        # 计算全息变换
        final_vector = self.transport.compute_holonomy(loop, initial_vector)

        # 检查结果形状
        self.assertEqual(final_vector.shape, (2,))


class TestConnectionCalculator(unittest.TestCase):
    """测试ConnectionCalculator"""

    def setUp(self):
        """设置测试环境"""
        self.calculator = ConnectionCalculator(dimension=3)

    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.calculator.dimension, 3)
        self.assertEqual(self.calculator.connection_type, "levi-civita")

    def test_get_connection(self):
        """测试获取连接形式"""
        connection = self.calculator.get_connection()
        self.assertEqual(connection.shape, (3, 3, 3))

    def test_compute_christoffel_symbols(self):
        """测试计算Christoffel符号"""
        metric = np.eye(3)  # 单位度量
        christoffel = self.calculator.compute_christoffel_symbols(metric)
        self.assertEqual(christoffel.shape, (3, 3, 3))

        # 对于单位度量，Christoffel符号应该为零
        np.testing.assert_array_almost_equal(christoffel, np.zeros((3, 3, 3)))

    def test_compute_curvature(self):
        """测试计算曲率"""
        curvature = self.calculator.compute_curvature()
        self.assertEqual(curvature.shape, (3, 3, 3, 3))

    def test_compute_torsion(self):
        """测试计算挠率"""
        torsion = self.calculator.compute_torsion()
        self.assertEqual(torsion.shape, (3, 3, 3))

    def test_compute_ricci_curvature(self):
        """测试计算Ricci曲率"""
        ricci = self.calculator.compute_ricci_curvature()
        self.assertEqual(ricci.shape, (3, 3))

    def test_compute_scalar_curvature(self):
        """测试计算标量曲率"""
        scalar = self.calculator.compute_scalar_curvature()
        self.assertIsInstance(scalar, float)

    def test_parallel_transport_vector(self):
        """测试平行传输向量"""
        # 创建一个直线路径
        path = np.zeros((10, 3))
        path[:, 0] = np.linspace(0, 1, 10)

        # 初始向量
        vector = np.array([1.0, 0.0, 0.0])

        # 传输向量
        result = self.calculator.parallel_transport_vector(path, vector)

        # 检查结果形状
        self.assertEqual(result.shape, (10, 3))

        # 检查初始向量是否正确
        np.testing.assert_array_almost_equal(result[0], vector)


class TestCurvatureAnalyzer(unittest.TestCase):
    """测试CurvatureAnalyzer"""

    def setUp(self):
        """设置测试环境"""
        self.analyzer = CurvatureAnalyzer(dimension=3, visualization_enabled=False)

        # 创建一个简单的曲率张量
        self.curvature = np.zeros((3, 3, 3, 3))
        for i in range(3):
            for j in range(3):
                for k in range(3):
                    for l in range(3):
                        if i == j and k == l:
                            self.curvature[i, j, k, l] = 1.0

        # 创建一个简单的Ricci曲率
        self.ricci = np.eye(3)

    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.analyzer.dimension, 3)
        self.assertFalse(self.analyzer.visualization_enabled)

    def test_analyze_curvature(self):
        """测试分析曲率"""
        result = self.analyzer.analyze_curvature(self.curvature)
        self.assertIn("norm", result)
        self.assertIn("eigenvalues", result)
        self.assertIn("trace", result)
        self.assertIn("determinant", result)
        self.assertIn("is_symmetric", result)

    def test_analyze_ricci_curvature(self):
        """测试分析Ricci曲率"""
        result = self.analyzer.analyze_ricci_curvature(self.ricci)
        self.assertIn("eigenvalues", result)
        self.assertIn("eigenvectors", result)
        self.assertIn("scalar_curvature", result)
        self.assertIn("determinant", result)
        self.assertIn("is_positive_definite", result)

    def test_analyze_torsion(self):
        """测试分析挠率"""
        torsion = np.zeros((3, 3, 3))
        result = self.analyzer.analyze_torsion(torsion)
        self.assertIn("norm", result)
        self.assertIn("max_component", result)
        self.assertIn("is_antisymmetric", result)

    def test_compute_sectional_curvature(self):
        """测试计算截面曲率"""
        v1 = np.array([1.0, 0.0, 0.0])
        v2 = np.array([0.0, 1.0, 0.0])
        sectional = self.analyzer.compute_sectional_curvature(self.curvature, v1, v2)
        self.assertIsInstance(sectional, float)

    def test_compute_holonomy_angle(self):
        """测试计算全息角"""
        v1 = np.array([1.0, 0.0, 0.0])
        v2 = np.array([0.0, 1.0, 0.0])
        angle = self.analyzer.compute_holonomy_angle(v1, v2)
        self.assertIsInstance(angle, float)
        self.assertAlmostEqual(angle, np.pi/2)  # 正交向量的夹角为90度


if __name__ == '__main__':
    unittest.main()
