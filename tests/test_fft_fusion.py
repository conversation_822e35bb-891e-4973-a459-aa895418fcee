"""
FFT融合方法测试

本模块包含FFT融合方法的测试用例。
"""

import unittest
import numpy as np
import os
import sys
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入我们的实现
from src.algorithms.fft_fusion.fusion import FFTFusion
from src.algorithms.fft_fusion.transforms import FFTTransformer
from src.algorithms.fft_fusion.filters import FrequencyDomainFilter
from src.algorithms.fft_fusion.analysis import FusionAnalyzer


class TestFFTFusion(unittest.TestCase):
    """FFT融合方法测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        # 创建测试数据
        self.signals = self._create_test_signals()
        self.images = self._create_test_images()
        
        # 创建融合器
        self.fusion = FFTFusion(
            fusion_method='weighted',
            window_type='hann',
            overlap_ratio=0.5,
            use_parallel=True,
            num_workers=2,
            use_cache=True,
            cache_size=10
        )
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.fusion.fusion_method, 'weighted')
        self.assertEqual(self.fusion.window_type, 'hann')
        self.assertEqual(self.fusion.overlap_ratio, 0.5)
        self.assertTrue(self.fusion.use_parallel)
        self.assertEqual(self.fusion.num_workers, 2)
        self.assertTrue(self.fusion.use_cache)
        self.assertEqual(self.fusion.cache_size, 10)
    
    def test_compute_signals(self):
        """测试计算方法（信号）"""
        # 执行计算
        result = self.fusion.compute(self.signals)
        
        # 检查结果
        self.assertIn('fused_signal', result)
        self.assertIn('frequency_domain', result)
        self.assertIn('fusion_quality', result)
        self.assertIn('performance', result)
        
        # 检查融合信号
        fused_signal = result['fused_signal']
        self.assertEqual(fused_signal.shape, self.signals[0].shape)
        
        # 检查频域表示
        frequency_domain = result['frequency_domain']
        self.assertEqual(frequency_domain.shape, self.signals[0].shape)
        
        # 检查融合质量
        fusion_quality = result['fusion_quality']
        self.assertGreaterEqual(fusion_quality, 0.0)
        self.assertLessEqual(fusion_quality, 1.0)
        
        # 检查性能指标
        performance = result['performance']
        self.assertIn('total_time', performance)
        self.assertIn('transform_time', performance)
        self.assertIn('fusion_time', performance)
        self.assertIn('inverse_transform_time', performance)
        self.assertIn('memory_usage', performance)
        self.assertIn('fusion_quality', performance)
    
    def test_compute_images(self):
        """测试计算方法（图像）"""
        # 执行计算
        result = self.fusion.compute(self.images)
        
        # 检查结果
        self.assertIn('fused_signal', result)
        self.assertIn('frequency_domain', result)
        self.assertIn('fusion_quality', result)
        self.assertIn('performance', result)
        
        # 检查融合图像
        fused_image = result['fused_signal']
        self.assertEqual(fused_image.shape, self.images[0].shape)
        
        # 检查频域表示
        frequency_domain = result['frequency_domain']
        self.assertEqual(frequency_domain.shape, self.images[0].shape)
        
        # 检查融合质量
        fusion_quality = result['fusion_quality']
        self.assertGreaterEqual(fusion_quality, 0.0)
        self.assertLessEqual(fusion_quality, 1.0)
    
    def test_get_metadata(self):
        """测试获取元数据"""
        metadata = self.fusion.get_metadata()
        
        self.assertIn('name', metadata)
        self.assertIn('version', metadata)
        self.assertIn('description', metadata)
        self.assertIn('parameters', metadata)
        self.assertIn('performance_metrics', metadata)
    
    def test_is_compatible_with(self):
        """测试兼容性检查"""
        # 创建兼容的融合器
        compatible_fusion = FFTFusion(
            fusion_method='max',
            window_type='hann',
            overlap_ratio=0.5
        )
        
        # 创建不兼容的融合器
        incompatible_fusion = FFTFusion(
            fusion_method='min',
            window_type='hamming',
            overlap_ratio=0.7
        )
        
        # 检查兼容性
        self.assertTrue(self.fusion.is_compatible_with(compatible_fusion))
        self.assertFalse(self.fusion.is_compatible_with(incompatible_fusion))
    
    def test_get_performance_metrics(self):
        """测试获取性能指标"""
        # 执行计算
        self.fusion.compute(self.signals)
        
        # 获取性能指标
        metrics = self.fusion.get_performance_metrics()
        
        self.assertIn('total_time', metrics)
        self.assertIn('transform_time', metrics)
        self.assertIn('fusion_time', metrics)
        self.assertIn('inverse_transform_time', metrics)
        self.assertIn('memory_usage', metrics)
        self.assertIn('fusion_quality', metrics)
    
    def test_fft_transformer(self):
        """测试FFT变换器"""
        # 创建FFT变换器
        transformer = FFTTransformer(
            window_type='hann',
            overlap_ratio=0.5
        )
        
        # 执行变换
        frequency_domain = transformer.transform(self.signals[0])
        
        # 检查结果
        self.assertEqual(frequency_domain.shape, self.signals[0].shape)
        
        # 执行逆变换
        signal = transformer.inverse_transform(frequency_domain)
        
        # 检查结果
        self.assertEqual(signal.shape, self.signals[0].shape)
    
    def test_frequency_domain_filter(self):
        """测试频域滤波器"""
        # 创建频域滤波器
        filter = FrequencyDomainFilter()
        
        # 创建FFT变换器
        transformer = FFTTransformer()
        
        # 执行变换
        frequency_domain = transformer.transform(self.signals[0])
        
        # 应用低通滤波器
        filter_params = {
            'type': 'lowpass',
            'cutoff': 0.5,
            'order': 1
        }
        filtered_domain = filter.apply_filter(frequency_domain, filter_params)
        
        # 检查结果
        self.assertEqual(filtered_domain.shape, frequency_domain.shape)
        
        # 应用高通滤波器
        filter_params = {
            'type': 'highpass',
            'cutoff': 0.5,
            'order': 1
        }
        filtered_domain = filter.apply_filter(frequency_domain, filter_params)
        
        # 检查结果
        self.assertEqual(filtered_domain.shape, frequency_domain.shape)
    
    def test_fusion_analyzer(self):
        """测试融合分析器"""
        # 创建融合分析器
        analyzer = FusionAnalyzer()
        
        # 评估融合质量
        quality = analyzer.evaluate_fusion_quality(self.signals, self.signals[0])
        
        # 检查结果
        self.assertGreaterEqual(quality, 0.0)
        self.assertLessEqual(quality, 1.0)
        
        # 生成融合报告
        report = analyzer.generate_fusion_report(self.signals, self.signals[0])
        
        # 检查报告
        self.assertIn('overall_quality', report)
        self.assertIn('metrics', report)
    
    def _create_test_signals(self):
        """创建测试信号"""
        # 创建时间轴
        t = np.linspace(0, 1, 1000)
        
        # 创建信号1：正弦波
        signal1 = np.sin(2 * np.pi * 5 * t)
        
        # 创建信号2：余弦波
        signal2 = np.cos(2 * np.pi * 10 * t)
        
        # 创建信号3：方波
        signal3 = np.sign(np.sin(2 * np.pi * 2 * t))
        
        return [signal1, signal2, signal3]
    
    def _create_test_images(self):
        """创建测试图像"""
        # 创建图像1：水平条纹
        image1 = np.zeros((100, 100))
        for i in range(0, 100, 10):
            image1[i:i+5, :] = 1.0
        
        # 创建图像2：垂直条纹
        image2 = np.zeros((100, 100))
        for i in range(0, 100, 10):
            image2[:, i:i+5] = 1.0
        
        # 创建图像3：对角线条纹
        image3 = np.zeros((100, 100))
        for i in range(100):
            image3[i, i] = 1.0
            if i < 95:
                image3[i, i+5] = 1.0
            if i > 4:
                image3[i, i-5] = 1.0
        
        return [image1, image2, image3]


if __name__ == '__main__':
    unittest.main()
