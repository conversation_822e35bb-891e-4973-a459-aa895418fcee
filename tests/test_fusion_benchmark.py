"""
超越态融合算子性能基准测试

本脚本用于测量超越态融合算子在不同规模状态下的性能。
"""

import sys
import os
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# 导入警告抑制工具
from src.utils.warning_suppressor import suppress_warnings

# 使用警告抑制工具
with suppress_warnings():
    # 导入融合算子
    from src.operators.fusion import (
        quantum_superposition_fusion,
        holographic_interference_fusion,
        fractal_fusion,
        topological_fusion,
        fusion_with_method
    )

def create_random_state(n):
    """创建随机量子态"""
    state = np.random.normal(0, 1, n) + 1j * np.random.normal(0, 1, n)
    state = state / np.sqrt(np.sum(np.abs(state)**2))
    return state.tolist()

def benchmark_fusion_method(method, state_a, state_b, num_runs=10):
    """测量融合方法的性能"""
    # 预热
    method(state_a, state_b)
    
    # 测量性能
    start_time = time.time()
    for _ in range(num_runs):
        result = method(state_a, state_b)
    end_time = time.time()
    
    # 计算平均执行时间
    avg_time = (end_time - start_time) / num_runs
    
    return avg_time

def run_benchmark():
    """运行性能基准测试"""
    print("\n超越态融合算子性能基准测试")
    print("=" * 50)
    
    # 测试不同维度
    dimensions = [10, 100, 1000, 10000]
    
    # 测试不同方法
    methods = {
        "量子叠加融合": quantum_superposition_fusion,
        "全息干涉融合": holographic_interference_fusion,
        "分形融合": fractal_fusion,
        "拓扑融合": topological_fusion,
    }
    
    # 创建结果表格
    print("\n性能测试结果（平均执行时间，单位：秒）：")
    print("-" * 80)
    print(f"{'维度':<10} | {'量子叠加融合':<15} | {'全息干涉融合':<15} | {'分形融合':<15} | {'拓扑融合':<15}")
    print("-" * 80)
    
    # 运行测试
    for dim in dimensions:
        # 创建随机状态
        state_a = create_random_state(dim)
        state_b = create_random_state(dim)
        
        # 测量各方法的性能
        results = {}
        for name, method in methods.items():
            try:
                avg_time = benchmark_fusion_method(method, state_a, state_b)
                results[name] = avg_time
            except Exception as e:
                print(f"方法 {name} 在维度 {dim} 上测试失败: {e}")
                results[name] = float('nan')
        
        # 打印结果
        print(f"{dim:<10} | {results['量子叠加融合']:<15.6f} | {results['全息干涉融合']:<15.6f} | {results['分形融合']:<15.6f} | {results['拓扑融合']:<15.6f}")
    
    print("-" * 80)
    
    # 测试不同权重
    print("\n不同权重的性能测试（维度：1000）：")
    print("-" * 80)
    print(f"{'权重':<15} | {'量子叠加融合':<15} | {'全息干涉融合':<15} | {'分形融合':<15} | {'拓扑融合':<15}")
    print("-" * 80)
    
    # 创建随机状态
    dim = 1000
    state_a = create_random_state(dim)
    state_b = create_random_state(dim)
    
    # 测试不同权重
    weights = [(0.5, 0.5), (0.8, 0.2), (0.2, 0.8), (0.0, 1.0), (1.0, 0.0)]
    
    for weight_a, weight_b in weights:
        # 测量各方法的性能
        results = {}
        for name, method in methods.items():
            try:
                avg_time = benchmark_fusion_method(
                    lambda a, b: method(a, b, weight_a=weight_a, weight_b=weight_b),
                    state_a, state_b
                )
                results[name] = avg_time
            except Exception as e:
                print(f"方法 {name} 在权重 ({weight_a}, {weight_b}) 上测试失败: {e}")
                results[name] = float('nan')
        
        # 打印结果
        print(f"({weight_a}, {weight_b})".ljust(15) + f" | {results['量子叠加融合']:<15.6f} | {results['全息干涉融合']:<15.6f} | {results['分形融合']:<15.6f} | {results['拓扑融合']:<15.6f}")
    
    print("-" * 80)

if __name__ == "__main__":
    run_benchmark()
