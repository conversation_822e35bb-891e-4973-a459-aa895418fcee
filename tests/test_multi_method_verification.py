"""
多方法验证算子测试脚本
"""

import sys
import os
import unittest
import time
import numpy as np

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

from src.operators.verification.multi_method_verification import MultiMethodVerificationOperator

# 检查Rust实现是否可用
try:
    from src.operators.verification.rust_wrapper import (
        RustMultiMethodVerificationOperator,
        RUST_AVAILABLE
    )
except ImportError:
    RUST_AVAILABLE = False


class TestMultiMethodVerificationOperator(unittest.TestCase):
    """多方法验证算子测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建Python实现的算子
        self.py_operator = MultiMethodVerificationOperator(
            model_checking_enabled=True,
            theorem_proving_enabled=True,
            runtime_monitoring_enabled=True,
            model_checking_weight=0.33,
            theorem_proving_weight=0.33,
            runtime_monitoring_weight=0.34,
            verification_properties=[
                {
                    "id": "safety1",
                    "type": "safety",
                    "expression": "G(x > 0)"
                },
                {
                    "id": "liveness1",
                    "type": "liveness",
                    "expression": "F(y = 1)"
                },
                {
                    "id": "runtime1",
                    "type": "runtime",
                    "expression": "x + y < 100"
                }
            ]
        )
        
        # 如果Rust实现可用，创建Rust实现的算子
        if RUST_AVAILABLE:
            self.rust_operator = RustMultiMethodVerificationOperator(
                model_checking_enabled=True,
                theorem_proving_enabled=True,
                runtime_monitoring_enabled=True,
                model_checking_weight=0.33,
                theorem_proving_weight=0.33,
                runtime_monitoring_weight=0.34,
                verification_properties=[
                    {
                        "id": "safety1",
                        "type": "safety",
                        "expression": "G(x > 0)"
                    },
                    {
                        "id": "liveness1",
                        "type": "liveness",
                        "expression": "F(y = 1)"
                    },
                    {
                        "id": "runtime1",
                        "type": "runtime",
                        "expression": "x + y < 100"
                    }
                ]
            )
        
        # 准备测试数据
        self.test_data = {
            "state": {
                "variables": {
                    "x": 10,
                    "y": 20,
                    "z": 30
                }
            },
            "action": {
                "type": "transform",
                "parameters": {
                    "matrix": [[0.8, -0.6, 0.0], [0.6, 0.8, 0.0], [0.0, 0.0, 1.0]],
                    "offset": [1.0, 2.0, 3.0]
                }
            }
        }
    
    def test_python_implementation(self):
        """测试Python实现"""
        # 应用算子
        result = self.py_operator.apply(self.test_data)
        
        # 验证结果
        self.assertIn('verification_results', result)
        self.assertIn('verification_properties', result)
        self.assertIn('verification_methods_used', result)
        
        # 验证验证结果
        verification_results = result['verification_results']
        self.assertIn('safety1', verification_results)
        self.assertIn('liveness1', verification_results)
        self.assertIn('runtime1', verification_results)
        
        # 验证使用的验证方法
        methods_used = result['verification_methods_used']
        self.assertIn('model_checking', methods_used)
        self.assertIn('theorem_proving', methods_used)
        self.assertIn('runtime_monitoring', methods_used)
    
    @unittest.skipIf(not RUST_AVAILABLE, "Rust implementation not available")
    def test_rust_implementation(self):
        """测试Rust实现"""
        # 应用算子
        result = self.rust_operator.apply(self.test_data)
        
        # 验证结果
        self.assertIn('verification_results', result)
        self.assertIn('verification_properties', result)
        self.assertIn('verification_methods_used', result)
        
        # 验证验证结果
        verification_results = result['verification_results']
        self.assertIn('safety1', verification_results)
        self.assertIn('liveness1', verification_results)
        self.assertIn('runtime1', verification_results)
        
        # 验证使用的验证方法
        methods_used = result['verification_methods_used']
        self.assertIn('model_checking', methods_used)
        self.assertIn('theorem_proving', methods_used)
        self.assertIn('runtime_monitoring', methods_used)
    
    @unittest.skipIf(not RUST_AVAILABLE, "Rust implementation not available")
    def test_performance_comparison(self):
        """比较Python和Rust实现的性能"""
        # 准备大量测试数据
        test_data_list = []
        for i in range(100):
            test_data = {
                "state": {
                    "variables": {
                        "x": i * 0.1,
                        "y": i * 0.2,
                        "z": i * 0.3
                    }
                },
                "action": {
                    "type": "transform",
                    "parameters": {
                        "matrix": [[0.8, -0.6, 0.0], [0.6, 0.8, 0.0], [0.0, 0.0, 1.0]],
                        "offset": [1.0, 2.0, 3.0]
                    }
                }
            }
            test_data_list.append(test_data)
        
        # 测量Python实现的性能
        py_start_time = time.time()
        for test_data in test_data_list:
            self.py_operator.apply(test_data)
        py_end_time = time.time()
        py_elapsed_time = py_end_time - py_start_time
        
        # 测量Rust实现的性能
        rust_start_time = time.time()
        for test_data in test_data_list:
            self.rust_operator.apply(test_data)
        rust_end_time = time.time()
        rust_elapsed_time = rust_end_time - rust_start_time
        
        # 输出性能比较结果
        print(f"Python implementation: {py_elapsed_time:.4f} seconds")
        print(f"Rust implementation: {rust_elapsed_time:.4f} seconds")
        print(f"Speedup: {py_elapsed_time / rust_elapsed_time:.2f}x")
        
        # 验证Rust实现比Python实现快
        self.assertLess(rust_elapsed_time, py_elapsed_time)
    
    @unittest.skipIf(not RUST_AVAILABLE, "Rust implementation not available")
    def test_method_selection(self):
        """测试验证方法选择"""
        # 创建只启用模型检查的算子
        model_checking_operator = RustMultiMethodVerificationOperator(
            model_checking_enabled=True,
            theorem_proving_enabled=False,
            runtime_monitoring_enabled=False,
            verification_properties=[
                {
                    "id": "safety1",
                    "type": "safety",
                    "expression": "G(x > 0)"
                },
                {
                    "id": "liveness1",
                    "type": "liveness",
                    "expression": "F(y = 1)"
                },
                {
                    "id": "runtime1",
                    "type": "runtime",
                    "expression": "x + y < 100"
                }
            ]
        )
        
        # 应用算子
        result = model_checking_operator.apply(self.test_data)
        
        # 验证使用的验证方法
        methods_used = result['verification_methods_used']
        self.assertGreater(methods_used['model_checking'], 0)
        self.assertEqual(methods_used['theorem_proving'], 0)
        self.assertEqual(methods_used['runtime_monitoring'], 0)
    
    @unittest.skipIf(not RUST_AVAILABLE, "Rust implementation not available")
    def test_parameter_customization(self):
        """测试参数自定义"""
        # 创建自定义参数的算子
        custom_operator = RustMultiMethodVerificationOperator(
            model_checking_enabled=True,
            theorem_proving_enabled=True,
            runtime_monitoring_enabled=True,
            model_checking_weight=0.5,
            theorem_proving_weight=0.3,
            runtime_monitoring_weight=0.2,
            verification_properties=[]
        )
        
        # 设置参数
        custom_operator.set_parameters({
            "model_checking_weight": 0.6,
            "theorem_proving_weight": 0.2,
            "runtime_monitoring_weight": 0.2,
            "verification_properties": [
                {
                    "id": "custom1",
                    "type": "safety",
                    "expression": "G(x > 0)"
                }
            ]
        })
        
        # 获取参数
        params = custom_operator.get_parameters()
        self.assertEqual(params['model_checking_weight'], 0.6)
        self.assertEqual(params['theorem_proving_weight'], 0.2)
        self.assertEqual(params['runtime_monitoring_weight'], 0.2)
        self.assertEqual(len(params['verification_properties']), 1)
        
        # 应用算子
        result = custom_operator.apply(self.test_data)
        
        # 验证验证结果
        verification_results = result['verification_results']
        self.assertIn('custom1', verification_results)


if __name__ == '__main__':
    unittest.main()
