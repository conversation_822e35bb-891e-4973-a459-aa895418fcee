import os
import time
import json
import asyncio
import argparse
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 尝试导入VastAI客户端
try:
    import vastai
    import requests

    class VastAI:
        def __init__(self, api_key=None):
            self.api_key = api_key
            self.base_url = "https://console.vast.ai/api/v0"
            self.headers = {"Accept": "application/json"}

        async def get_offers(self, query=""):
            url = f"{self.base_url}/bundles"
            params = {
                "api_key": self.api_key,
                "q": query
            }
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            data = response.json()
            return data.get("offers", [])

        async def create_instance(self, offer_id, image, disk=10, interruptible=True, onstart=""):
            url = f"{self.base_url}/instances/create"
            params = {
                "api_key": self.api_key
            }
            data = {
                "client_id": "me",
                "image": image,
                "disk": disk,
                "onstart": onstart,
                "runtype": "interruptible" if interruptible else "on-demand",
                "offer_id": offer_id
            }
            response = requests.put(url, headers=self.headers, params=params, json=data)
            response.raise_for_status()
            return response.json()

        async def wait_for_instance(self, instance_id):
            url = f"{self.base_url}/instances/{instance_id}"
            params = {
                "api_key": self.api_key
            }
            for _ in range(30):  # 尝试30次，每次等待10秒
                response = requests.get(url, headers=self.headers, params=params)
                response.raise_for_status()
                data = response.json()
                if data.get("status") == "running":
                    return True
                await asyncio.sleep(10)
            return False

        async def exec_command(self, instance_id, command):
            url = f"{self.base_url}/instances/{instance_id}/exec"
            params = {
                "api_key": self.api_key
            }
            data = {
                "command": command
            }
            response = requests.put(url, headers=self.headers, params=params, json=data)
            response.raise_for_status()
            return response.json()

        async def upload_file(self, instance_id, local_path, remote_path):
            url = f"{self.base_url}/instances/{instance_id}/upload"
            params = {
                "api_key": self.api_key,
                "dst": remote_path
            }
            with open(local_path, "rb") as f:
                response = requests.put(url, headers=self.headers, params=params, data=f)
                response.raise_for_status()
            return True

        async def download_file(self, instance_id, remote_path, local_path):
            url = f"{self.base_url}/instances/{instance_id}/download"
            params = {
                "api_key": self.api_key,
                "src": remote_path
            }
            response = requests.get(url, headers=self.headers, params=params, stream=True)
            response.raise_for_status()
            with open(local_path, "wb") as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            return True

        async def destroy_instance(self, instance_id):
            url = f"{self.base_url}/instances/{instance_id}"
            params = {
                "api_key": self.api_key
            }
            response = requests.delete(url, headers=self.headers, params=params)
            response.raise_for_status()
            return True

except ImportError:
    logger.error("未安装requests，请运行: pip install requests")
    exit(1)

class GPUBenchmarker:
    def __init__(self, api_key=None):
        """初始化GPU基准测试器"""
        self.api_key = api_key or os.environ.get("VASTAI_API_KEY")
        if not self.api_key:
            raise ValueError("未提供VastAI API密钥，请设置VASTAI_API_KEY环境变量或通过参数传入")

        self.client = VastAI(api_key=self.api_key)
        self.results_dir = "gpu_benchmark_results"
        os.makedirs(self.results_dir, exist_ok=True)

    async def get_available_instances(self, interruptible=True):
        """获取可用的GPU实例列表"""
        logger.info("获取可用GPU实例列表...")

        # 构建查询条件
        query = ""  # 不使用复杂查询条件

        # 获取实例列表
        instances = await self.client.get_offers(query=query)

        # 按GPU类型分组
        gpu_types = {}
        for instance in instances:
            gpu_name = instance.get("gpu_name", "Unknown")
            if gpu_name not in gpu_types:
                gpu_types[gpu_name] = []
            gpu_types[gpu_name].append(instance)

        # 每种GPU类型选择最便宜的实例
        best_instances = {}
        for gpu_name, instances in gpu_types.items():
            # 按价格排序
            sorted_instances = sorted(instances, key=lambda x: x.get("dph", float("inf")))
            if sorted_instances:
                best_instances[gpu_name] = sorted_instances[0]

        logger.info(f"找到 {len(best_instances)} 种可用GPU类型")
        return best_instances

    async def test_gpu(self, gpu_instance, test_duration=30):
        """测试特定GPU实例的性能"""
        gpu_name = gpu_instance.get("gpu_name", "Unknown")
        instance_id = gpu_instance.get("id")
        dph = gpu_instance.get("dph", 0)

        logger.info(f"开始测试 {gpu_name} (实例ID: {instance_id}, 价格: ${dph}/小时)...")

        # 创建实例
        try:
            instance = await self.client.create_instance(
                offer_id=instance_id,
                image="nvidia/cuda:11.8.0-devel-ubuntu22.04",
                disk=10,
                interruptible=True,  # 使用可中断实例
                onstart="""
                    apt-get update && apt-get install -y python3-pip
                    pip install torch numpy
                """
            )
        except Exception as e:
            logger.error(f"创建 {gpu_name} 实例失败: {str(e)}")
            return None

        instance_id = instance.get("id")
        logger.info(f"{gpu_name} 实例已创建: ID={instance_id}")

        try:
            # 等待实例就绪
            logger.info(f"等待 {gpu_name} 实例就绪...")
            await self.client.wait_for_instance(instance_id)

            # 上传测试脚本
            test_script = self._generate_test_script()
            with open("gpu_test.py", "w") as f:
                f.write(test_script)

            logger.info(f"上传测试脚本到 {gpu_name} 实例...")
            await self.client.upload_file(instance_id, "gpu_test.py", "gpu_test.py")

            # 运行测试
            logger.info(f"在 {gpu_name} 上运行GPU测试...")
            result = await self.client.exec_command(
                instance_id,
                "python3 gpu_test.py"
            )
            logger.info(f"{gpu_name} 测试执行完成")

            # 下载结果
            result_file = f"{self.results_dir}/{gpu_name.replace(' ', '_')}_results.json"
            await self.client.download_file(instance_id, "benchmark_results.json", result_file)

            # 加载结果
            with open(result_file, "r") as f:
                benchmark_results = json.load(f)

            # 添加成本和实例信息
            benchmark_results["cost_per_hour"] = dph
            benchmark_results["instance_info"] = {
                "id": instance_id,
                "gpu_name": gpu_name,
                "gpu_ram": gpu_instance.get("gpu_ram", 0),
                "num_gpus": gpu_instance.get("num_gpus", 1),
                "cuda_max_good": gpu_instance.get("cuda_max_good", "unknown"),
                "reliability": gpu_instance.get("reliability", 0),
                "dlperf": gpu_instance.get("dlperf", 0),
                "flops": gpu_instance.get("flops", 0)
            }

            # 保存更新后的结果
            with open(result_file, "w") as f:
                json.dump(benchmark_results, f, indent=2)

            logger.info(f"{gpu_name} 测试结果已保存到 {result_file}")
            return benchmark_results

        except Exception as e:
            logger.error(f"测试 {gpu_name} 时出错: {str(e)}")
            return None

        finally:
            # 销毁实例
            logger.info(f"销毁 {gpu_name} 实例 {instance_id}...")
            try:
                await self.client.destroy_instance(instance_id)
                logger.info(f"{gpu_name} 实例已销毁")
            except Exception as e:
                logger.error(f"销毁 {gpu_name} 实例时出错: {str(e)}")

    def _generate_test_script(self):
        """生成GPU测试脚本，特别关注双精度性能"""
        return """
import time
import json
import torch
import numpy as np

def run_tests():
    results = {}
    device = torch.device("cuda")

    # 基本信息
    results["gpu_info"] = {
        "name": torch.cuda.get_device_name(),
        "memory": torch.cuda.get_device_properties(0).total_memory,
        "compute_capability": torch.cuda.get_device_capability(),
        "cuda_version": torch.version.cuda
    }

    # 检测是否支持双精度
    try:
        # 创建一个小的双精度张量测试
        test_tensor = torch.ones(10, 10, dtype=torch.float64, device=device)
        test_result = test_tensor * test_tensor
        supports_fp64 = True
    except Exception as e:
        supports_fp64 = False
        results["fp64_support_error"] = str(e)

    results["supports_fp64"] = supports_fp64

    # 如果支持双精度，测试双精度性能
    if supports_fp64:
        # 双精度矩阵乘法测试
        for size in [1000, 3000, 5000]:
            try:
                a = torch.randn(size, size, dtype=torch.float64, device=device)
                b = torch.randn(size, size, dtype=torch.float64, device=device)

                # 预热
                for _ in range(3):
                    c = torch.matmul(a, b)
                    torch.cuda.synchronize()

                # 计时
                torch.cuda.synchronize()
                start = time.time()
                for _ in range(5):
                    c = torch.matmul(a, b)
                    torch.cuda.synchronize()
                end = time.time()

                results[f"fp64_matmul_{size}"] = (end - start) / 5
            except Exception as e:
                results[f"fp64_matmul_{size}_error"] = str(e)

        # 双精度复数运算测试（量子模拟常用）
        for size in [500, 1000, 2000]:
            try:
                # 创建复数张量
                real = torch.randn(size, size, dtype=torch.float64, device=device)
                imag = torch.randn(size, size, dtype=torch.float64, device=device)
                a = torch.complex(real, imag)
                b = torch.complex(real, imag)

                # 预热
                for _ in range(3):
                    c = torch.matmul(a, b)
                    torch.cuda.synchronize()

                # 计时
                torch.cuda.synchronize()
                start = time.time()
                for _ in range(5):
                    c = torch.matmul(a, b)
                    torch.cuda.synchronize()
                end = time.time()

                results[f"complex64_matmul_{size}"] = (end - start) / 5
            except Exception as e:
                results[f"complex64_matmul_{size}_error"] = str(e)

        # 双精度FFT测试（量子模拟常用）
        for dims in [(64, 64, 64), (32, 32, 32, 32)]:
            try:
                tensor = torch.randn(*dims, dtype=torch.float64, device=device)

                # 预热
                for _ in range(3):
                    result = torch.fft.fftn(tensor)
                    torch.cuda.synchronize()

                # 计时
                torch.cuda.synchronize()
                start = time.time()
                for _ in range(5):
                    result = torch.fft.fftn(tensor)
                    torch.cuda.synchronize()
                end = time.time()

                results[f"fp64_fft_{len(dims)}d"] = (end - start) / 5
            except Exception as e:
                results[f"fp64_fft_{len(dims)}d_error"] = str(e)

    # 单精度测试（用于比较）
    # 单精度矩阵乘法测试
    for size in [1000, 3000, 5000]:
        try:
            a = torch.randn(size, size, dtype=torch.float32, device=device)
            b = torch.randn(size, size, dtype=torch.float32, device=device)

            # 预热
            for _ in range(3):
                c = torch.matmul(a, b)
                torch.cuda.synchronize()

            # 计时
            torch.cuda.synchronize()
            start = time.time()
            for _ in range(5):
                c = torch.matmul(a, b)
                torch.cuda.synchronize()
            end = time.time()

            results[f"fp32_matmul_{size}"] = (end - start) / 5
        except Exception as e:
            results[f"fp32_matmul_{size}_error"] = str(e)

    # 单精度FFT测试
    for dims in [(64, 64, 64), (32, 32, 32, 32)]:
        try:
            tensor = torch.randn(*dims, dtype=torch.float32, device=device)

            # 预热
            for _ in range(3):
                result = torch.fft.fftn(tensor)
                torch.cuda.synchronize()

            # 计时
            torch.cuda.synchronize()
            start = time.time()
            for _ in range(5):
                result = torch.fft.fftn(tensor)
                torch.cuda.synchronize()
            end = time.time()

            results[f"fp32_fft_{len(dims)}d"] = (end - start) / 5
        except Exception as e:
            results[f"fp32_fft_{len(dims)}d_error"] = str(e)

    # 计算FP64/FP32性能比率
    if supports_fp64:
        fp64_fp32_ratios = {}

        # 矩阵乘法比率
        for size in [1000, 3000, 5000]:
            fp64_key = f"fp64_matmul_{size}"
            fp32_key = f"fp32_matmul_{size}"

            if fp64_key in results and fp32_key in results:
                # 注意：较小的时间表示更好的性能
                # 因此比率是 fp32_time / fp64_time
                # 如果比率接近1，表示双精度性能接近单精度
                # 如果比率很小（如0.125），表示双精度性能远低于单精度
                ratio = results[fp32_key] / results[fp64_key]
                fp64_fp32_ratios[f"matmul_{size}_ratio"] = ratio

        # FFT比率
        for dims in [3, 4]:
            fp64_key = f"fp64_fft_{dims}d"
            fp32_key = f"fp32_fft_{dims}d"

            if fp64_key in results and fp32_key in results:
                ratio = results[fp32_key] / results[fp64_key]
                fp64_fp32_ratios[f"fft_{dims}d_ratio"] = ratio

        # 添加比率到结果
        results["fp64_fp32_ratios"] = fp64_fp32_ratios

        # 计算平均比率
        if fp64_fp32_ratios:
            avg_ratio = sum(fp64_fp32_ratios.values()) / len(fp64_fp32_ratios)
            results["fp64_fp32_avg_ratio"] = avg_ratio

    # 内存带宽测试
    try:
        # 创建大型张量
        size = 1000000000  # 1B元素
        try:
            tensor = torch.randn(size, device=device)
        except Exception:
            # 如果内存不足，尝试较小的大小
            size = 100000000  # 100M元素
            tensor = torch.randn(size, device=device)

        # 预热
        for _ in range(3):
            result = tensor + tensor
            torch.cuda.synchronize()

        # 计时
        torch.cuda.synchronize()
        start = time.time()
        for _ in range(5):
            result = tensor + tensor
            torch.cuda.synchronize()
        end = time.time()

        # 计算带宽 (GB/s)
        elapsed = (end - start) / 5
        bytes_processed = size * 4 * 3  # 读取2个张量，写入1个张量，每个元素4字节
        bandwidth = bytes_processed / elapsed / (1024**3)

        results["memory_bandwidth_GBps"] = bandwidth
    except Exception as e:
        results["memory_bandwidth_error"] = str(e)

    return results

if __name__ == "__main__":
    results = run_tests()
    with open("benchmark_results.json", "w") as f:
        json.dump(results, f, indent=2)
    print("测试完成，结果已保存到 benchmark_results.json")
"""

    async def analyze_results(self):
        """分析所有GPU测试结果并计算性价比，特别关注双精度性能"""
        logger.info("分析GPU测试结果...")

        # 加载所有结果
        results = {}
        for filename in os.listdir(self.results_dir):
            if filename.endswith("_results.json"):
                gpu_name = filename.replace("_results.json", "").replace("_", " ")
                with open(f"{self.results_dir}/{filename}", "r") as f:
                    results[gpu_name] = json.load(f)

        if not results:
            logger.warning("未找到任何测试结果")
            return

        # 计算性价比
        price_performance = {}
        for gpu_name, data in results.items():
            price = data.get("cost_per_hour", 0)
            if price <= 0:
                logger.warning(f"跳过 {gpu_name}: 无效的价格信息")
                continue

            pp_ratios = {}
            supports_fp64 = data.get("supports_fp64", False)

            # 双精度性价比（如果支持）
            if supports_fp64:
                # 双精度矩阵乘法性价比
                for size in [1000, 3000, 5000]:
                    key = f"fp64_matmul_{size}"
                    if key in data and isinstance(data[key], (int, float)):
                        # 性能越高，时间越短，所以用1/时间
                        perf = 1.0 / data[key]
                        pp_ratio = perf / price
                        pp_ratios[key] = pp_ratio

                # 复数矩阵乘法性价比
                for size in [500, 1000, 2000]:
                    key = f"complex64_matmul_{size}"
                    if key in data and isinstance(data[key], (int, float)):
                        perf = 1.0 / data[key]
                        pp_ratio = perf / price
                        pp_ratios[key] = pp_ratio

                # 双精度FFT性价比
                for dims in [3, 4]:
                    key = f"fp64_fft_{dims}d"
                    if key in data and isinstance(data[key], (int, float)):
                        perf = 1.0 / data[key]
                        pp_ratio = perf / price
                        pp_ratios[key] = pp_ratio

            # 单精度性价比（用于比较）
            # 单精度矩阵乘法性价比
            for size in [1000, 3000, 5000]:
                key = f"fp32_matmul_{size}"
                if key in data and isinstance(data[key], (int, float)):
                    perf = 1.0 / data[key]
                    pp_ratio = perf / price
                    pp_ratios[key] = pp_ratio

            # 单精度FFT性价比
            for dims in [3, 4]:
                key = f"fp32_fft_{dims}d"
                if key in data and isinstance(data[key], (int, float)):
                    perf = 1.0 / data[key]
                    pp_ratio = perf / price
                    pp_ratios[key] = pp_ratio

            # 内存带宽性价比
            if "memory_bandwidth_GBps" in data and isinstance(data["memory_bandwidth_GBps"], (int, float)):
                bandwidth = data["memory_bandwidth_GBps"]
                pp_ratio = bandwidth / price
                pp_ratios["memory_bandwidth"] = pp_ratio

            # 计算综合性价比得分
            # 如果支持双精度，双精度测试权重更高
            if supports_fp64:
                weights = {
                    # 双精度测试权重高
                    "fp64_matmul_1000": 0.05,
                    "fp64_matmul_3000": 0.10,
                    "fp64_matmul_5000": 0.15,
                    "complex64_matmul_500": 0.05,
                    "complex64_matmul_1000": 0.10,
                    "complex64_matmul_2000": 0.15,
                    "fp64_fft_3d": 0.10,
                    "fp64_fft_4d": 0.15,
                    # 单精度测试权重低
                    "fp32_matmul_5000": 0.05,
                    "fp32_fft_4d": 0.05,
                    "memory_bandwidth": 0.05
                }
            else:
                # 如果不支持双精度，只考虑单精度性能
                weights = {
                    "fp32_matmul_1000": 0.10,
                    "fp32_matmul_3000": 0.15,
                    "fp32_matmul_5000": 0.25,
                    "fp32_fft_3d": 0.15,
                    "fp32_fft_4d": 0.25,
                    "memory_bandwidth": 0.10
                }

            total_weight = 0
            weighted_score = 0
            for key, weight in weights.items():
                if key in pp_ratios:
                    weighted_score += pp_ratios[key] * weight
                    total_weight += weight

            if total_weight > 0:
                overall_score = weighted_score / total_weight
            else:
                overall_score = 0

            # 如果支持双精度，给予额外加分
            if supports_fp64:
                # 检查双精度/单精度比率
                fp64_fp32_avg_ratio = data.get("fp64_fp32_avg_ratio", 0)

                # 如果双精度性能接近单精度（比率接近1），给予高额外分数
                if fp64_fp32_avg_ratio > 0.5:  # 双精度性能至少是单精度的一半
                    fp64_bonus = 1.5  # 50%加分
                elif fp64_fp32_avg_ratio > 0.25:  # 双精度性能至少是单精度的四分之一
                    fp64_bonus = 1.3  # 30%加分
                elif fp64_fp32_avg_ratio > 0.125:  # 双精度性能至少是单精度的八分之一
                    fp64_bonus = 1.1  # 10%加分
                else:
                    fp64_bonus = 1.0  # 无加分

                overall_score *= fp64_bonus

            price_performance[gpu_name] = {
                "detailed_ratios": pp_ratios,
                "overall_score": overall_score,
                "cost_per_hour": price,
                "supports_fp64": supports_fp64,
                "fp64_fp32_avg_ratio": data.get("fp64_fp32_avg_ratio", 0) if supports_fp64 else 0,
                "gpu_info": data.get("gpu_info", {}),
                "instance_info": data.get("instance_info", {})
            }

        # 按综合性价比排序
        sorted_gpus = sorted(
            price_performance.items(),
            key=lambda x: x[1]["overall_score"],
            reverse=True
        )

        # 生成报告
        report = {
            "timestamp": datetime.now().isoformat(),
            "sorted_by_price_performance": [
                {
                    "gpu_name": gpu_name,
                    "overall_score": data["overall_score"],
                    "cost_per_hour": data["cost_per_hour"],
                    "supports_fp64": data["supports_fp64"],
                    "fp64_fp32_avg_ratio": data.get("fp64_fp32_avg_ratio", 0),
                    "gpu_info": data["gpu_info"],
                    "detailed_ratios": data["detailed_ratios"]
                }
                for gpu_name, data in sorted_gpus
            ],
            "raw_results": results
        }

        # 保存报告
        report_file = f"{self.results_dir}/gpu_benchmark_report.json"
        with open(report_file, "w") as f:
            json.dump(report, f, indent=2)

        # 打印结果摘要
        logger.info("\n===== GPU性价比排名 =====")
        logger.info("(特别关注双精度性能，适合量子计算)")
        for i, (gpu_name, data) in enumerate(sorted_gpus):
            fp64_status = "支持双精度" if data["supports_fp64"] else "不支持双精度"
            fp64_ratio = f", FP64/FP32比率={data.get('fp64_fp32_avg_ratio', 0):.2f}" if data["supports_fp64"] else ""
            logger.info(f"{i+1}. {gpu_name}: 得分={data['overall_score']:.2f}, 价格=${data['cost_per_hour']}/小时, {fp64_status}{fp64_ratio}")

        logger.info(f"\n详细报告已保存到 {report_file}")

        return report

async def main():
    parser = argparse.ArgumentParser(description="GPU性价比测试工具")
    parser.add_argument("--api-key", help="VastAI API密钥")
    parser.add_argument("--gpu-types", nargs="+", help="要测试的GPU类型列表，如不指定则测试所有可用类型")
    parser.add_argument("--analyze-only", action="store_true", help="仅分析现有结果，不运行新测试")
    args = parser.parse_args()

    benchmarker = GPUBenchmarker(api_key=args.api_key)

    if not args.analyze_only:
        # 获取可用实例
        available_instances = await benchmarker.get_available_instances(interruptible=True)

        # 过滤GPU类型
        if args.gpu_types:
            filtered_instances = {}
            for gpu_type in args.gpu_types:
                for gpu_name, instance in available_instances.items():
                    if gpu_type.lower() in gpu_name.lower():
                        filtered_instances[gpu_name] = instance
            available_instances = filtered_instances

        if not available_instances:
            logger.error("未找到符合条件的GPU实例")
            return

        logger.info(f"将测试以下GPU类型: {', '.join(available_instances.keys())}")

        # 测试每种GPU
        for gpu_name, instance in available_instances.items():
            await benchmarker.test_gpu(instance)

    # 分析结果
    await benchmarker.analyze_results()

if __name__ == "__main__":
    asyncio.run(main())
