"""
分形动力学路由算法测试

本模块包含分形动力学路由算法的测试用例。
"""

import unittest
import numpy as np
import networkx as nx
import os
import sys
import tempfile
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 直接导入我们的实现
from src.algorithms.fractal_routing.router import FractalDynamicsRouter
from src.algorithms.fractal_routing.patterns import FractalPatternGenerator
from src.algorithms.fractal_routing.analysis import RoutingPerformanceAnalyzer


class TestFractalDynamicsRouter(unittest.TestCase):
    """分形动力学路由算法测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        # 创建测试网络
        self.network = self._create_test_network()
        
        # 创建路由器
        self.router = FractalDynamicsRouter(
            fractal_dimension=1.5,
            max_iterations=10,
            tolerance=1e-6,
            use_parallel=True,
            num_workers=2,
            adaptive_routing=True,
            route_cache_size=100,
            route_cache_ttl=300
        )
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.router.fractal_dimension, 1.5)
        self.assertEqual(self.router.max_iterations, 10)
        self.assertEqual(self.router.tolerance, 1e-6)
        self.assertTrue(self.router.use_parallel)
        self.assertEqual(self.router.num_workers, 2)
        self.assertTrue(self.router.adaptive_routing)
        self.assertEqual(self.router.route_cache_size, 100)
        self.assertEqual(self.router.route_cache_ttl, 300)
    
    def test_compute_single_route(self):
        """测试计算单个路由"""
        # 选择源节点和目标节点
        source = 0
        destination = 9
        
        # 执行计算
        result = self.router.compute({
            'network': self.network,
            'source': source,
            'destination': destination
        })
        
        # 检查结果
        self.assertIn('routing_table', result)
        self.assertIn('routes', result)
        self.assertIn('iterations', result)
        self.assertIn('convergence_achieved', result)
        self.assertIn('performance', result)
        
        # 检查路由
        routes = result['routes']
        self.assertIn((source, destination), routes)
        route = routes[(source, destination)]
        self.assertIsNotNone(route)
        self.assertEqual(route[0], source)
        self.assertEqual(route[-1], destination)
    
    def test_compute_all_pairs(self):
        """测试计算所有节点对之间的路由"""
        # 执行计算
        result = self.router.compute({
            'network': self.network,
            'all_pairs': True
        })
        
        # 检查结果
        self.assertIn('routing_table', result)
        self.assertIn('routes', result)
        self.assertIn('iterations', result)
        self.assertIn('convergence_achieved', result)
        self.assertIn('performance', result)
        
        # 检查路由
        routes = result['routes']
        self.assertEqual(len(routes), len(self.network.nodes) * (len(self.network.nodes) - 1))
        
        # 检查路由表
        routing_table = result['routing_table']
        self.assertGreater(len(routing_table), 0)
    
    def test_get_metadata(self):
        """测试获取元数据"""
        metadata = self.router.get_metadata()
        
        self.assertIn('name', metadata)
        self.assertIn('version', metadata)
        self.assertIn('description', metadata)
        self.assertIn('parameters', metadata)
        self.assertIn('performance_metrics', metadata)
    
    def test_is_compatible_with(self):
        """测试兼容性检查"""
        # 创建兼容的路由器
        compatible_router = FractalDynamicsRouter(
            fractal_dimension=1.6,
            max_iterations=20
        )
        
        # 创建不兼容的路由器
        incompatible_router = FractalDynamicsRouter(
            fractal_dimension=2.5,
            max_iterations=5
        )
        
        # 检查兼容性
        self.assertTrue(self.router.is_compatible_with(compatible_router))
        self.assertFalse(self.router.is_compatible_with(incompatible_router))
    
    def test_get_performance_metrics(self):
        """测试获取性能指标"""
        # 执行计算
        self.router.compute({
            'network': self.network,
            'source': 0,
            'destination': 9
        })
        
        # 获取性能指标
        metrics = self.router.get_performance_metrics()
        
        self.assertIn('total_time', metrics)
        self.assertIn('iterations', metrics)
        self.assertIn('convergence_achieved', metrics)
        self.assertIn('route_success_rate', metrics)
        self.assertIn('average_path_length', metrics)
        self.assertIn('route_cache_hit_rate', metrics)
    
    def test_update_network(self):
        """测试更新网络拓扑"""
        # 执行计算
        self.router.compute({
            'network': self.network,
            'source': 0,
            'destination': 9
        })
        
        # 创建新的网络拓扑
        new_network = self._create_test_network(20, 0.2)
        
        # 更新网络拓扑
        self.router.update_network(new_network)
        
        # 执行计算
        result = self.router.compute({
            'network': new_network,
            'source': 0,
            'destination': 19
        })
        
        # 检查结果
        self.assertIn('routing_table', result)
        self.assertIn('routes', result)
        self.assertIn((0, 19), result['routes'])
    
    def test_get_next_hop(self):
        """测试获取下一跳节点"""
        # 执行计算
        self.router.compute({
            'network': self.network,
            'source': 0,
            'destination': 9
        })
        
        # 获取下一跳节点
        next_hop = self.router.get_next_hop(0, 9)
        
        # 检查下一跳节点
        self.assertIsNotNone(next_hop)
        self.assertIn(next_hop, self.network.neighbors(0))
    
    def test_get_route(self):
        """测试获取路由"""
        # 执行计算
        self.router.compute({
            'network': self.network,
            'source': 0,
            'destination': 9
        })
        
        # 获取路由
        route = self.router.get_route(0, 9)
        
        # 检查路由
        self.assertIsNotNone(route)
        self.assertEqual(route[0], 0)
        self.assertEqual(route[-1], 9)
    
    def test_pattern_generator(self):
        """测试分形模式生成器"""
        # 创建分形模式生成器
        generator = FractalPatternGenerator(pattern_type='standard', fractal_dimension=1.5)
        
        # 生成分形模式
        pattern = generator.generate(self.network)
        
        # 检查分形模式
        self.assertEqual(len(pattern), len(self.network.nodes))
        for node in self.network.nodes:
            self.assertIn(node, pattern)
            self.assertGreaterEqual(pattern[node], 0.0)
            self.assertLessEqual(pattern[node], 1.0)
    
    def test_performance_analyzer(self):
        """测试路由性能分析器"""
        # 执行计算
        result = self.router.compute({
            'network': self.network,
            'all_pairs': True
        })
        
        # 创建路由性能分析器
        analyzer = RoutingPerformanceAnalyzer()
        
        # 分析路由性能
        analysis = analyzer.analyze(self.network, result['routes'])
        
        # 检查分析结果
        self.assertIn('success_rate', analysis)
        self.assertIn('average_path_length', analysis)
        self.assertIn('average_stretch', analysis)
        self.assertIn('load_balance', analysis)
        self.assertIn('congestion', analysis)
        self.assertIn('fractal_utilization', analysis)
    
    def _create_test_network(self, num_nodes=10, p=0.3):
        """创建测试网络"""
        # 创建随机图
        network = nx.erdos_renyi_graph(num_nodes, p, seed=42)
        
        # 确保图是连通的
        if not nx.is_connected(network):
            # 找到最大连通分量
            largest_cc = max(nx.connected_components(network), key=len)
            network = network.subgraph(largest_cc).copy()
        
        # 添加节点位置
        pos = nx.spring_layout(network, seed=42)
        for node, position in pos.items():
            network.nodes[node]['pos'] = position
        
        # 添加边权重
        for u, v in network.edges:
            pos_u = network.nodes[u]['pos']
            pos_v = network.nodes[v]['pos']
            distance = np.sqrt(sum((p1 - p2) ** 2 for p1, p2 in zip(pos_u, pos_v)))
            network[u][v]['weight'] = distance
        
        return network


if __name__ == '__main__':
    unittest.main()
