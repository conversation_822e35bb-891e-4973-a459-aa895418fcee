#!/usr/bin/env python3
"""
集成接口测试脚本

这个脚本测试分布式网络与其他模块的集成接口，验证其基本功能是否正常工作。
"""

import os
import sys
import time
import json
import uuid
import logging
import unittest
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.distributed.layers import (
    PhysicalLayer,
    DataLayer,
    ComputationLayer,
    CoordinationLayer,
    ApplicationLayer
)

from src.distributed.integration.core import CoreIntegration
from src.distributed.integration.algorithms import AlgorithmIntegration
from src.distributed.integration.operators import (
    OperatorIntegration,
    OperatorType,
    OperatorResult
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# 创建测试算子
class TestMathOperator:
    """测试数学算子"""
    
    @staticmethod
    def apply(a, b, operation="add", **kwargs):
        """应用数学运算"""
        if operation == "add":
            return a + b
        elif operation == "subtract":
            return a - b
        elif operation == "multiply":
            return a * b
        elif operation == "divide":
            return a / b
        else:
            raise ValueError(f"Unknown operation: {operation}")

class TestLogicOperator:
    """测试逻辑算子"""
    
    @staticmethod
    def apply(a, b, operation="and", **kwargs):
        """应用逻辑运算"""
        if operation == "and":
            return a and b
        elif operation == "or":
            return a or b
        elif operation == "xor":
            return bool(a) != bool(b)
        else:
            raise ValueError(f"Unknown operation: {operation}")

class TestTransformOperator:
    """测试变换算子"""
    
    @staticmethod
    def apply(data, transform_type="reverse", **kwargs):
        """应用变换"""
        if transform_type == "reverse":
            if isinstance(data, list):
                return list(reversed(data))
            elif isinstance(data, str):
                return data[::-1]
            else:
                raise ValueError(f"Cannot reverse data of type: {type(data)}")
        elif transform_type == "uppercase":
            if isinstance(data, str):
                return data.upper()
            else:
                raise ValueError(f"Cannot uppercase data of type: {type(data)}")
        else:
            raise ValueError(f"Unknown transform type: {transform_type}")

class TestIntegration(unittest.TestCase):
    """测试集成接口"""
    
    def setUp(self):
        """测试前准备"""
        self.node_id = str(uuid.uuid4())
        
        # 创建物理层
        self.physical_layer = PhysicalLayer(
            node_id=self.node_id
        )
        
        # 创建数据层
        self.data_layer = DataLayer(
            node_id=self.node_id,
            physical_layer=self.physical_layer
        )
        
        # 创建计算层
        self.computation_layer = ComputationLayer(
            node_id=self.node_id,
            physical_layer=self.physical_layer,
            data_layer=self.data_layer
        )
        
        # 创建协调层
        self.coordination_layer = CoordinationLayer(
            node_id=self.node_id,
            physical_layer=self.physical_layer,
            data_layer=self.data_layer,
            computation_layer=self.computation_layer
        )
        
        # 创建应用层
        self.application_layer = ApplicationLayer(
            node_id=self.node_id,
            physical_layer=self.physical_layer,
            data_layer=self.data_layer,
            computation_layer=self.computation_layer,
            coordination_layer=self.coordination_layer
        )
        
        # 启动各层
        self.physical_layer.start()
        self.data_layer.start()
        self.computation_layer.start()
        self.coordination_layer.start()
        self.application_layer.start()
        
        # 创建核心集成接口
        self.core_integration = CoreIntegration(
            node_id=self.node_id,
            physical_layer=self.physical_layer,
            data_layer=self.data_layer,
            computation_layer=self.computation_layer,
            coordination_layer=self.coordination_layer,
            application_layer=self.application_layer
        )
        
        # 创建算法集成接口
        self.algorithm_integration = AlgorithmIntegration(
            node_id=self.node_id,
            physical_layer=self.physical_layer,
            data_layer=self.data_layer,
            computation_layer=self.computation_layer,
            coordination_layer=self.coordination_layer,
            application_layer=self.application_layer
        )
        
        # 创建算子集成接口
        self.operator_integration = OperatorIntegration(
            node_id=self.node_id,
            physical_layer=self.physical_layer,
            data_layer=self.data_layer,
            computation_layer=self.computation_layer,
            coordination_layer=self.coordination_layer,
            application_layer=self.application_layer
        )
        
        # 启动集成接口
        self.core_integration.start()
        self.algorithm_integration.start()
        self.operator_integration.start()
    
    def tearDown(self):
        """测试后清理"""
        # 停止集成接口
        self.operator_integration.stop()
        self.algorithm_integration.stop()
        self.core_integration.stop()
        
        # 停止各层
        self.application_layer.stop()
        self.coordination_layer.stop()
        self.computation_layer.stop()
        self.data_layer.stop()
        self.physical_layer.stop()
    
    def test_operator_integration(self):
        """测试算子集成接口"""
        # 注册测试算子
        math_operator_id = self.operator_integration.register_operator(
            "test_math_operator",
            TestMathOperator,
            OperatorType.MATH,
            {
                "description": "测试数学算子",
                "version": "0.1.0",
                "parallelizable": True,
                "distributed": True
            }
        )
        
        logic_operator_id = self.operator_integration.register_operator(
            "test_logic_operator",
            TestLogicOperator,
            OperatorType.LOGIC,
            {
                "description": "测试逻辑算子",
                "version": "0.1.0",
                "parallelizable": True,
                "distributed": True
            }
        )
        
        transform_operator_id = self.operator_integration.register_operator(
            "test_transform_operator",
            TestTransformOperator,
            OperatorType.TRANSFORM,
            {
                "description": "测试变换算子",
                "version": "0.1.0",
                "parallelizable": True,
                "distributed": True
            }
        )
        
        # 获取所有算子
        all_operators = self.operator_integration.get_all_operators()
        self.assertIn(math_operator_id, all_operators)
        self.assertIn(logic_operator_id, all_operators)
        self.assertIn(transform_operator_id, all_operators)
        
        # 获取指定类型的算子
        math_operators = self.operator_integration.get_operators_by_type(OperatorType.MATH)
        self.assertIn(math_operator_id, math_operators)
        
        logic_operators = self.operator_integration.get_operators_by_type(OperatorType.LOGIC)
        self.assertIn(logic_operator_id, logic_operators)
        
        transform_operators = self.operator_integration.get_operators_by_type(OperatorType.TRANSFORM)
        self.assertIn(transform_operator_id, transform_operators)
        
        # 执行数学算子
        result = self.operator_integration.execute_operator(
            math_operator_id,
            args=[10, 5],
            kwargs={"operation": "add"},
            distributed=False
        )
        
        self.assertTrue(result.success)
        self.assertEqual(result.value, 15)
        
        # 执行逻辑算子
        result = self.operator_integration.execute_operator(
            logic_operator_id,
            args=[True, False],
            kwargs={"operation": "or"},
            distributed=False
        )
        
        self.assertTrue(result.success)
        self.assertEqual(result.value, True)
        
        # 执行变换算子
        result = self.operator_integration.execute_operator(
            transform_operator_id,
            args=["hello"],
            kwargs={"transform_type": "uppercase"},
            distributed=False
        )
        
        self.assertTrue(result.success)
        self.assertEqual(result.value, "HELLO")
        
        # 批量执行算子
        batch_results = self.operator_integration.execute_operator_batch(
            math_operator_id,
            batch_args=[[1, 2], [3, 4], [5, 6]],
            batch_kwargs=[
                {"operation": "add"},
                {"operation": "multiply"},
                {"operation": "subtract"}
            ],
            distributed=False,
            parallel=True
        )
        
        self.assertEqual(len(batch_results), 3)
        self.assertTrue(all(result.success for result in batch_results))
        self.assertEqual(batch_results[0].value, 3)
        self.assertEqual(batch_results[1].value, 12)
        self.assertEqual(batch_results[2].value, -1)
        
        # 组合算子
        composed_operator_id = self.operator_integration.compose_operators(
            [math_operator_id, transform_operator_id],
            "composed_math_transform",
            {
                "description": "组合数学和变换算子",
                "version": "0.1.0"
            }
        )
        
        # 执行组合算子
        result = self.operator_integration.execute_operator(
            composed_operator_id,
            args=[10, 5],
            kwargs={
                "operation": "add",
                "transform_type": "reverse"
            },
            distributed=False
        )
        
        self.assertTrue(result.success)
        self.assertEqual(result.value, 15)
        
        # 更新算子元数据
        self.operator_integration.update_operator_metadata(
            math_operator_id,
            {
                "description": "更新后的测试数学算子",
                "version": "0.2.0",
                "parallelizable": True,
                "distributed": True,
                "author": "Test"
            }
        )
        
        # 获取更新后的算子信息
        operator_info = self.operator_integration.get_operator_info(math_operator_id)
        self.assertEqual(operator_info.metadata["description"], "更新后的测试数学算子")
        self.assertEqual(operator_info.metadata["version"], "0.2.0")
        self.assertEqual(operator_info.metadata["author"], "Test")
        
        # 删除算子
        self.operator_integration.delete_operator(math_operator_id)
        self.operator_integration.delete_operator(logic_operator_id)
        self.operator_integration.delete_operator(transform_operator_id)
        self.operator_integration.delete_operator(composed_operator_id)
        
        # 验证删除成功
        all_operators = self.operator_integration.get_all_operators()
        self.assertNotIn(math_operator_id, all_operators)
        self.assertNotIn(logic_operator_id, all_operators)
        self.assertNotIn(transform_operator_id, all_operators)
        self.assertNotIn(composed_operator_id, all_operators)
    
    def test_algorithm_integration(self):
        """测试算法集成接口"""
        # 获取所有算法
        all_algorithms = self.algorithm_integration.get_all_algorithms()
        
        # 由于我们没有实现真实的算法，这里只是验证接口能够正常工作
        self.assertIsInstance(all_algorithms, dict)
    
    def test_core_integration(self):
        """测试核心集成接口"""
        # 创建分布式节点
        node = self.core_integration.create_node("compute", node_id=f"test_node_{uuid.uuid4()}")
        
        # 注册节点
        node_id = self.core_integration.register_node(node)
        
        # 获取节点
        retrieved_node = self.core_integration.get_node(node_id)
        
        # 验证节点
        self.assertIsNotNone(retrieved_node)
        self.assertEqual(retrieved_node.get_id(), node_id)
        
        # 更新节点
        node.set_status("running")
        self.core_integration.update_node(node)
        
        # 获取更新后的节点
        updated_node = self.core_integration.get_node(node_id)
        
        # 验证更新
        self.assertEqual(updated_node.get_status(), "running")
        
        # 删除节点
        self.core_integration.delete_node(node_id)
        
        # 验证删除成功
        deleted_node = self.core_integration.get_node(node_id)
        self.assertIsNone(deleted_node)
        
        # 获取所有节点
        all_nodes = self.core_integration.get_all_nodes()
        self.assertIsInstance(all_nodes, dict)
        self.assertNotIn(node_id, all_nodes)

if __name__ == '__main__':
    unittest.main()
