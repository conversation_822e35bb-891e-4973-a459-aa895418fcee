"""
错误处理模块测试

这个模块测试超越态思维引擎的错误处理模块。
"""

import os
import sys
import unittest

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# 直接导入错误处理模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../src/utils")))
from errors import TTEError, TTEValueError, TTETypeError


class TestErrors(unittest.TestCase):
    """错误处理测试"""

    def test_tte_error(self):
        """测试TTEError"""
        # 创建错误
        error = TTEError("Test error", 100, {"key": "value"})

        # 检查属性
        self.assertEqual(error.message, "Test error")
        self.assertEqual(error.error_code, 100)
        self.assertEqual(error.details, {"key": "value"})

        # 检查字符串表示
        self.assertIn("Test error", str(error))
        self.assertIn("100", str(error))
        self.assertIn("key", str(error))
        self.assertIn("value", str(error))

        # 检查字典表示
        error_dict = error.to_dict()
        self.assertEqual(error_dict["error"], "TTEError")
        self.assertEqual(error_dict["message"], "Test error")
        self.assertEqual(error_dict["error_code"], 100)
        self.assertEqual(error_dict["details"], {"key": "value"})

    def test_tte_value_error(self):
        """测试TTEValueError"""
        # 创建错误
        error = TTEValueError("Invalid value", 42, int, 101)

        # 检查属性
        self.assertEqual(error.message, "Invalid value")
        self.assertEqual(error.error_code, 101)
        self.assertEqual(error.details["value"], "42")
        self.assertEqual(error.details["expected_type"], str(int))

        # 检查字符串表示
        self.assertIn("Invalid value", str(error))
        self.assertIn("101", str(error))
        self.assertIn("42", str(error))
        self.assertIn(str(int), str(error))

    def test_tte_type_error(self):
        """测试TTETypeError"""
        # 创建错误
        error = TTETypeError("Invalid type", "42", int, 102)

        # 检查属性
        self.assertEqual(error.message, "Invalid type")
        self.assertEqual(error.error_code, 102)
        self.assertEqual(error.details["value"], "42")
        self.assertEqual(error.details["actual_type"], str(type("42")))
        self.assertEqual(error.details["expected_type"], str(int))

        # 检查字符串表示
        self.assertIn("Invalid type", str(error))
        self.assertIn("102", str(error))
        self.assertIn("'42'", str(error))
        self.assertIn(str(type("42")), str(error))
        self.assertIn(str(int), str(error))


if __name__ == "__main__":
    unittest.main()
