"""
负载均衡算子测试

本测试脚本测试负载均衡算子的基本功能。
"""

import sys
import os
import unittest
from unittest.mock import MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入负载均衡算子
from src.transcendental_tensor.performance_optimization.load_balancing import (
    Node, NodeStatus, Task, TaskStatus, TaskPriority,
    ResourceType, BalancingStrategy,
    LoadMonitor, LoadAnalyzer, StrategyFactory, LoadBalancer
)


class TestLoadBalancing(unittest.TestCase):
    """负载均衡算子测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建负载均衡器
        self.balancer = LoadBalancer(
            name="TestBalancer",
            strategy_type=BalancingStrategy.RESOURCE_BASED
        )
        
        # 创建测试节点
        self.node1 = Node(
            node_id="node-1",
            name="Node 1",
            status=NodeStatus.ONLINE
        )
        
        # 添加CPU资源
        self.node1.resources[ResourceType.CPU] = self.node1.NodeResource(
            resource_type=ResourceType.CPU,
            capacity=8.0,
            available=8.0
        )
        
        # 添加内存资源
        self.node1.resources[ResourceType.MEMORY] = self.node1.NodeResource(
            resource_type=ResourceType.MEMORY,
            capacity=16.0,
            available=16.0
        )
        
        # 创建第二个节点
        self.node2 = Node(
            node_id="node-2",
            name="Node 2",
            status=NodeStatus.ONLINE
        )
        
        # 添加CPU资源
        self.node2.resources[ResourceType.CPU] = self.node2.NodeResource(
            resource_type=ResourceType.CPU,
            capacity=4.0,
            available=4.0
        )
        
        # 添加内存资源
        self.node2.resources[ResourceType.MEMORY] = self.node2.NodeResource(
            resource_type=ResourceType.MEMORY,
            capacity=8.0,
            available=8.0
        )
        
        # 注册节点
        self.balancer.register_node(self.node1)
        self.balancer.register_node(self.node2)
        
        # 创建测试任务
        self.task1 = Task(
            task_id="task-1",
            name="Task 1",
            priority=TaskPriority.MEDIUM,
            status=TaskStatus.PENDING
        )
        
        # 添加CPU需求
        cpu_requirement = self.task1.ResourceRequirement(
            resource_type=ResourceType.CPU,
            amount=1.0
        )
        self.task1.resource_requirements.append(cpu_requirement)
        
        # 添加内存需求
        memory_requirement = self.task1.ResourceRequirement(
            resource_type=ResourceType.MEMORY,
            amount=2.0
        )
        self.task1.resource_requirements.append(memory_requirement)
        
        # 创建第二个任务
        self.task2 = Task(
            task_id="task-2",
            name="Task 2",
            priority=TaskPriority.HIGH,
            status=TaskStatus.PENDING
        )
        
        # 添加CPU需求
        cpu_requirement = self.task2.ResourceRequirement(
            resource_type=ResourceType.CPU,
            amount=2.0
        )
        self.task2.resource_requirements.append(cpu_requirement)
        
        # 添加内存需求
        memory_requirement = self.task2.ResourceRequirement(
            resource_type=ResourceType.MEMORY,
            amount=4.0
        )
        self.task2.resource_requirements.append(memory_requirement)
        
        # 注册任务
        self.balancer.register_task(self.task1)
        self.balancer.register_task(self.task2)
    
    def test_register_node(self):
        """测试注册节点"""
        # 创建新节点
        node3 = Node(
            node_id="node-3",
            name="Node 3",
            status=NodeStatus.ONLINE
        )
        
        # 注册节点
        result = self.balancer.register_node(node3)
        
        # 验证结果
        self.assertTrue(result)
        
        # 获取节点
        node = self.balancer.get_node("node-3")
        
        # 验证节点
        self.assertIsNotNone(node)
        self.assertEqual(node.node_id, "node-3")
        self.assertEqual(node.name, "Node 3")
        self.assertEqual(node.status, NodeStatus.ONLINE)
    
    def test_register_task(self):
        """测试注册任务"""
        # 创建新任务
        task3 = Task(
            task_id="task-3",
            name="Task 3",
            priority=TaskPriority.LOW,
            status=TaskStatus.PENDING
        )
        
        # 注册任务
        result = self.balancer.register_task(task3)
        
        # 验证结果
        self.assertTrue(result)
        
        # 获取任务
        task = self.balancer.get_task("task-3")
        
        # 验证任务
        self.assertIsNotNone(task)
        self.assertEqual(task.task_id, "task-3")
        self.assertEqual(task.name, "Task 3")
        self.assertEqual(task.priority, TaskPriority.LOW)
        self.assertEqual(task.status, TaskStatus.PENDING)
    
    def test_balance(self):
        """测试负载均衡"""
        # 执行负载均衡
        plan = self.balancer.balance()
        
        # 验证计划
        self.assertIsNotNone(plan)
        self.assertEqual(plan.strategy, BalancingStrategy.RESOURCE_BASED)
        
        # 验证动作
        self.assertGreater(len(plan.actions), 0)
        
        # 验证任务分配
        scheduled_tasks = self.balancer.get_tasks(TaskStatus.SCHEDULED)
        self.assertGreater(len(scheduled_tasks), 0)
        
        # 验证节点资源
        node1 = self.balancer.get_node("node-1")
        self.assertLess(node1.resources[ResourceType.CPU].available, 8.0)
        self.assertLess(node1.resources[ResourceType.MEMORY].available, 16.0)
    
    def test_update_task_status(self):
        """测试更新任务状态"""
        # 执行负载均衡
        self.balancer.balance()
        
        # 获取已调度的任务
        scheduled_tasks = self.balancer.get_tasks(TaskStatus.SCHEDULED)
        self.assertGreater(len(scheduled_tasks), 0)
        
        task = scheduled_tasks[0]
        
        # 更新任务状态
        result = self.balancer.update_task_status(task.task_id, TaskStatus.RUNNING)
        
        # 验证结果
        self.assertTrue(result)
        
        # 获取任务
        updated_task = self.balancer.get_task(task.task_id)
        
        # 验证任务状态
        self.assertEqual(updated_task.status, TaskStatus.RUNNING)
        self.assertIsNotNone(updated_task.started_at)
    
    def test_unregister_task(self):
        """测试注销任务"""
        # 注销任务
        result = self.balancer.unregister_task("task-1")
        
        # 验证结果
        self.assertTrue(result)
        
        # 获取任务
        task = self.balancer.get_task("task-1")
        
        # 验证任务已注销
        self.assertIsNone(task)
    
    def test_unregister_node(self):
        """测试注销节点"""
        # 注销节点
        result = self.balancer.unregister_node("node-2")
        
        # 验证结果
        self.assertTrue(result)
        
        # 获取节点
        node = self.balancer.get_node("node-2")
        
        # 验证节点已注销
        self.assertIsNone(node)


if __name__ == "__main__":
    unittest.main()
