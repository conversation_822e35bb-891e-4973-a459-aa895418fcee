import os
import sys
import json
import time
import requests
import argparse
from datetime import datetime

# VastAI API配置
API_KEY = "686c13c06944fde37dfd034d5f0994348ba9b12c10ff61280fd3ca3fd34f9084"
BASE_URL = "https://console.vast.ai/api/v0"
HEADERS = {"Accept": "application/json"}

# 测试脚本内容
TEST_SCRIPT = """
import time
import json
import torch
import numpy as np

def run_tests():
    results = {}
    device = torch.device("cuda")

    # 基本信息
    results["gpu_info"] = {
        "name": torch.cuda.get_device_name(),
        "memory": torch.cuda.get_device_properties(0).total_memory,
        "compute_capability": torch.cuda.get_device_capability(),
        "cuda_version": torch.version.cuda
    }

    # 检测是否支持双精度
    try:
        # 创建一个小的双精度张量测试
        test_tensor = torch.ones(10, 10, dtype=torch.float64, device=device)
        test_result = test_tensor * test_tensor
        supports_fp64 = True
    except Exception as e:
        supports_fp64 = False
        results["fp64_support_error"] = str(e)

    results["supports_fp64"] = supports_fp64

    # 如果支持双精度，测试双精度性能
    if supports_fp64:
        # 双精度矩阵乘法测试
        for size in [1000, 3000, 5000]:
            try:
                a = torch.randn(size, size, dtype=torch.float64, device=device)
                b = torch.randn(size, size, dtype=torch.float64, device=device)

                # 预热
                for _ in range(3):
                    c = torch.matmul(a, b)
                    torch.cuda.synchronize()

                # 计时
                torch.cuda.synchronize()
                start = time.time()
                for _ in range(5):
                    c = torch.matmul(a, b)
                    torch.cuda.synchronize()
                end = time.time()

                results[f"fp64_matmul_{size}"] = (end - start) / 5
            except Exception as e:
                results[f"fp64_matmul_{size}_error"] = str(e)

        # 双精度复数运算测试（量子模拟常用）
        for size in [500, 1000, 2000]:
            try:
                # 创建复数张量
                real = torch.randn(size, size, dtype=torch.float64, device=device)
                imag = torch.randn(size, size, dtype=torch.float64, device=device)
                a = torch.complex(real, imag)
                b = torch.complex(real, imag)

                # 预热
                for _ in range(3):
                    c = torch.matmul(a, b)
                    torch.cuda.synchronize()

                # 计时
                torch.cuda.synchronize()
                start = time.time()
                for _ in range(5):
                    c = torch.matmul(a, b)
                    torch.cuda.synchronize()
                end = time.time()

                results[f"complex64_matmul_{size}"] = (end - start) / 5
            except Exception as e:
                results[f"complex64_matmul_{size}_error"] = str(e)

        # 双精度FFT测试（量子模拟常用）
        for dims in [(64, 64, 64), (32, 32, 32, 32)]:
            try:
                tensor = torch.randn(*dims, dtype=torch.float64, device=device)

                # 预热
                for _ in range(3):
                    result = torch.fft.fftn(tensor)
                    torch.cuda.synchronize()

                # 计时
                torch.cuda.synchronize()
                start = time.time()
                for _ in range(5):
                    result = torch.fft.fftn(tensor)
                    torch.cuda.synchronize()
                end = time.time()

                results[f"fp64_fft_{len(dims)}d"] = (end - start) / 5
            except Exception as e:
                results[f"fp64_fft_{len(dims)}d_error"] = str(e)

    # 单精度测试（用于比较）
    # 单精度矩阵乘法测试
    for size in [1000, 3000, 5000]:
        try:
            a = torch.randn(size, size, dtype=torch.float32, device=device)
            b = torch.randn(size, size, dtype=torch.float32, device=device)

            # 预热
            for _ in range(3):
                c = torch.matmul(a, b)
                torch.cuda.synchronize()

            # 计时
            torch.cuda.synchronize()
            start = time.time()
            for _ in range(5):
                c = torch.matmul(a, b)
                torch.cuda.synchronize()
            end = time.time()

            results[f"fp32_matmul_{size}"] = (end - start) / 5
        except Exception as e:
            results[f"fp32_matmul_{size}_error"] = str(e)

    # 单精度FFT测试
    for dims in [(64, 64, 64), (32, 32, 32, 32)]:
        try:
            tensor = torch.randn(*dims, dtype=torch.float32, device=device)

            # 预热
            for _ in range(3):
                result = torch.fft.fftn(tensor)
                torch.cuda.synchronize()

            # 计时
            torch.cuda.synchronize()
            start = time.time()
            for _ in range(5):
                result = torch.fft.fftn(tensor)
                torch.cuda.synchronize()
            end = time.time()

            results[f"fp32_fft_{len(dims)}d"] = (end - start) / 5
        except Exception as e:
            results[f"fp32_fft_{len(dims)}d_error"] = str(e)

    # 计算FP64/FP32性能比率
    if supports_fp64:
        fp64_fp32_ratios = {}

        # 矩阵乘法比率
        for size in [1000, 3000, 5000]:
            fp64_key = f"fp64_matmul_{size}"
            fp32_key = f"fp32_matmul_{size}"

            if fp64_key in results and fp32_key in results:
                # 注意：较小的时间表示更好的性能
                # 因此比率是 fp32_time / fp64_time
                # 如果比率接近1，表示双精度性能接近单精度
                # 如果比率很小（如0.125），表示双精度性能远低于单精度
                ratio = results[fp32_key] / results[fp64_key]
                fp64_fp32_ratios[f"matmul_{size}_ratio"] = ratio

        # FFT比率
        for dims in [3, 4]:
            fp64_key = f"fp64_fft_{dims}d"
            fp32_key = f"fp32_fft_{dims}d"

            if fp64_key in results and fp32_key in results:
                ratio = results[fp32_key] / results[fp64_key]
                fp64_fp32_ratios[f"fft_{dims}d_ratio"] = ratio

        # 添加比率到结果
        results["fp64_fp32_ratios"] = fp64_fp32_ratios

        # 计算平均比率
        if fp64_fp32_ratios:
            avg_ratio = sum(fp64_fp32_ratios.values()) / len(fp64_fp32_ratios)
            results["fp64_fp32_avg_ratio"] = avg_ratio

    # 内存带宽测试
    try:
        # 创建大型张量
        size = 1000000000  # 1B元素
        try:
            tensor = torch.randn(size, device=device)
        except Exception:
            # 如果内存不足，尝试较小的大小
            size = 100000000  # 100M元素
            tensor = torch.randn(size, device=device)

        # 预热
        for _ in range(3):
            result = tensor + tensor
            torch.cuda.synchronize()

        # 计时
        torch.cuda.synchronize()
        start = time.time()
        for _ in range(5):
            result = tensor + tensor
            torch.cuda.synchronize()
        end = time.time()

        # 计算带宽 (GB/s)
        elapsed = (end - start) / 5
        bytes_processed = size * 4 * 3  # 读取2个张量，写入1个张量，每个元素4字节
        bandwidth = bytes_processed / elapsed / (1024**3)

        results["memory_bandwidth_GBps"] = bandwidth
    except Exception as e:
        results["memory_bandwidth_error"] = str(e)

    return results

if __name__ == "__main__":
    results = run_tests()
    with open("/root/benchmark_results.json", "w") as f:
        json.dump(results, f, indent=2)
    print("测试完成，结果已保存到 benchmark_results.json")
"""

# 启动脚本内容
STARTUP_SCRIPT = """#!/bin/bash
apt-get update && apt-get install -y python3-pip
pip install torch numpy
echo '{}' > /root/gpu_test.py
python3 /root/gpu_test.py
""".format(TEST_SCRIPT)

def get_offers():
    """获取可用的GPU实例列表"""
    url = f"{BASE_URL}/bundles"
    params = {
        "api_key": API_KEY
    }
    response = requests.get(url, headers=HEADERS, params=params)
    response.raise_for_status()
    data = response.json()
    return data.get("offers", [])

def create_instance(offer_id, image, disk=10, interruptible=True, onstart=""):
    """创建实例"""
    url = f"{BASE_URL}/instances/create"
    params = {
        "api_key": API_KEY
    }
    data = {
        "client_id": "me",
        "image": image,
        "disk": disk,
        "onstart": onstart,
        "runtype": "interruptible" if interruptible else "on-demand",
        "offer_id": offer_id
    }
    response = requests.put(url, headers=HEADERS, params=params, json=data)
    response.raise_for_status()
    return response.json()

def wait_for_instance(instance_id, max_wait=300):
    """等待实例就绪"""
    url = f"{BASE_URL}/instances/{instance_id}"
    params = {
        "api_key": API_KEY
    }
    start_time = time.time()
    while time.time() - start_time < max_wait:
        response = requests.get(url, headers=HEADERS, params=params)
        response.raise_for_status()
        data = response.json()
        if data.get("status") == "running":
            return True
        print(f"等待实例就绪，当前状态: {data.get('status')}")
        time.sleep(10)
    return False

def exec_command(instance_id, command):
    """在实例上执行命令"""
    url = f"{BASE_URL}/instances/{instance_id}/exec"
    params = {
        "api_key": API_KEY
    }
    data = {
        "command": command
    }
    response = requests.put(url, headers=HEADERS, params=params, json=data)
    response.raise_for_status()
    return response.json()

def download_file(instance_id, remote_path, local_path):
    """从实例下载文件"""
    url = f"{BASE_URL}/instances/{instance_id}/download"
    params = {
        "api_key": API_KEY,
        "src": remote_path
    }
    response = requests.get(url, headers=HEADERS, params=params, stream=True)
    response.raise_for_status()
    with open(local_path, "wb") as f:
        for chunk in response.iter_content(chunk_size=8192):
            f.write(chunk)
    return True

def destroy_instance(instance_id):
    """销毁实例"""
    url = f"{BASE_URL}/instances/{instance_id}"
    params = {
        "api_key": API_KEY
    }
    response = requests.delete(url, headers=HEADERS, params=params)
    response.raise_for_status()
    return True

def test_gpu(gpu_type, max_price=None):
    """测试特定GPU类型的性能"""
    print(f"开始测试 {gpu_type} GPU...")

    # 获取所有实例列表
    all_offers = get_offers()

    # 过滤符合条件的实例
    offers = []
    for offer in all_offers:
        offer_gpu_name = offer.get("gpu_name", "")
        offer_price = offer.get("dph", float("inf"))

        # 检查GPU类型是否匹配
        if gpu_type.lower() in offer_gpu_name.lower():
            # 检查价格是否在限制范围内
            if max_price is None or offer_price <= max_price:
                offers.append(offer)

    if not offers:
        print(f"未找到符合条件的 {gpu_type} 实例")
        return None

    # 按价格排序
    offers.sort(key=lambda x: x.get("dph", float("inf")))

    # 选择最便宜的实例
    offer = offers[0]
    offer_id = offer.get("id")
    price = offer.get("dph", 0)

    print(f"选择实例: ID={offer_id}, 价格=${price}/小时")

    # 创建实例
    instance = create_instance(
        offer_id=offer_id,
        image="nvidia/cuda:11.8.0-devel-ubuntu22.04",
        disk=10,
        interruptible=True,
        onstart=STARTUP_SCRIPT
    )

    instance_id = instance.get("id")
    print(f"实例已创建: ID={instance_id}")

    try:
        # 等待实例就绪
        print("等待实例就绪...")
        if not wait_for_instance(instance_id):
            print("等待实例就绪超时")
            return None

        # 等待测试完成
        print("等待测试完成...")
        time.sleep(300)  # 等待5分钟

        # 下载测试结果
        result_file = f"results_{gpu_type.replace(' ', '_')}.json"
        print(f"下载测试结果到 {result_file}...")
        download_file(instance_id, "/root/benchmark_results.json", result_file)

        # 加载结果
        with open(result_file, "r") as f:
            results = json.load(f)

        # 添加价格信息
        results["cost_per_hour"] = price
        results["instance_info"] = {
            "id": instance_id,
            "offer_id": offer_id,
            "gpu_name": gpu_type,
            "gpu_ram": offer.get("gpu_ram", 0),
            "num_gpus": offer.get("num_gpus", 1)
        }

        # 保存更新后的结果
        with open(result_file, "w") as f:
            json.dump(results, f, indent=2)

        print(f"{gpu_type} 测试完成，结果已保存到 {result_file}")
        return results

    except Exception as e:
        print(f"测试 {gpu_type} 时出错: {str(e)}")
        return None

    finally:
        # 销毁实例
        print(f"销毁实例 {instance_id}...")
        destroy_instance(instance_id)
        print(f"实例已销毁")

def analyze_results():
    """分析所有GPU测试结果并计算性价比"""
    print("分析GPU测试结果...")

    # 加载所有结果
    results = {}
    for filename in os.listdir("."):
        if filename.startswith("results_") and filename.endswith(".json"):
            gpu_name = filename[8:-5].replace("_", " ")
            with open(filename, "r") as f:
                results[gpu_name] = json.load(f)

    if not results:
        print("未找到任何测试结果")
        return

    # 计算性价比
    price_performance = {}
    for gpu_name, data in results.items():
        price = data.get("cost_per_hour", 0)
        if price <= 0:
            print(f"跳过 {gpu_name}: 无效的价格信息")
            continue

        pp_ratios = {}
        supports_fp64 = data.get("supports_fp64", False)

        # 双精度性价比（如果支持）
        if supports_fp64:
            # 双精度矩阵乘法性价比
            for size in [1000, 3000, 5000]:
                key = f"fp64_matmul_{size}"
                if key in data and isinstance(data[key], (int, float)):
                    # 性能越高，时间越短，所以用1/时间
                    perf = 1.0 / data[key]
                    pp_ratio = perf / price
                    pp_ratios[key] = pp_ratio

            # 复数矩阵乘法性价比
            for size in [500, 1000, 2000]:
                key = f"complex64_matmul_{size}"
                if key in data and isinstance(data[key], (int, float)):
                    perf = 1.0 / data[key]
                    pp_ratio = perf / price
                    pp_ratios[key] = pp_ratio

            # 双精度FFT性价比
            for dims in [3, 4]:
                key = f"fp64_fft_{dims}d"
                if key in data and isinstance(data[key], (int, float)):
                    perf = 1.0 / data[key]
                    pp_ratio = perf / price
                    pp_ratios[key] = pp_ratio

        # 单精度性价比（用于比较）
        # 单精度矩阵乘法性价比
        for size in [1000, 3000, 5000]:
            key = f"fp32_matmul_{size}"
            if key in data and isinstance(data[key], (int, float)):
                perf = 1.0 / data[key]
                pp_ratio = perf / price
                pp_ratios[key] = pp_ratio

        # 单精度FFT性价比
        for dims in [3, 4]:
            key = f"fp32_fft_{dims}d"
            if key in data and isinstance(data[key], (int, float)):
                perf = 1.0 / data[key]
                pp_ratio = perf / price
                pp_ratios[key] = pp_ratio

        # 内存带宽性价比
        if "memory_bandwidth_GBps" in data and isinstance(data["memory_bandwidth_GBps"], (int, float)):
            bandwidth = data["memory_bandwidth_GBps"]
            pp_ratio = bandwidth / price
            pp_ratios["memory_bandwidth"] = pp_ratio

        # 计算综合性价比得分
        # 如果支持双精度，双精度测试权重更高
        if supports_fp64:
            weights = {
                # 双精度测试权重高
                "fp64_matmul_1000": 0.05,
                "fp64_matmul_3000": 0.10,
                "fp64_matmul_5000": 0.15,
                "complex64_matmul_500": 0.05,
                "complex64_matmul_1000": 0.10,
                "complex64_matmul_2000": 0.15,
                "fp64_fft_3d": 0.10,
                "fp64_fft_4d": 0.15,
                # 单精度测试权重低
                "fp32_matmul_5000": 0.05,
                "fp32_fft_4d": 0.05,
                "memory_bandwidth": 0.05
            }
        else:
            # 如果不支持双精度，只考虑单精度性能
            weights = {
                "fp32_matmul_1000": 0.10,
                "fp32_matmul_3000": 0.15,
                "fp32_matmul_5000": 0.25,
                "fp32_fft_3d": 0.15,
                "fp32_fft_4d": 0.25,
                "memory_bandwidth": 0.10
            }

        total_weight = 0
        weighted_score = 0
        for key, weight in weights.items():
            if key in pp_ratios:
                weighted_score += pp_ratios[key] * weight
                total_weight += weight

        if total_weight > 0:
            overall_score = weighted_score / total_weight
        else:
            overall_score = 0

        # 如果支持双精度，给予额外加分
        if supports_fp64:
            # 检查双精度/单精度比率
            fp64_fp32_avg_ratio = data.get("fp64_fp32_avg_ratio", 0)

            # 如果双精度性能接近单精度（比率接近1），给予高额外分数
            if fp64_fp32_avg_ratio > 0.5:  # 双精度性能至少是单精度的一半
                fp64_bonus = 1.5  # 50%加分
            elif fp64_fp32_avg_ratio > 0.25:  # 双精度性能至少是单精度的四分之一
                fp64_bonus = 1.3  # 30%加分
            elif fp64_fp32_avg_ratio > 0.125:  # 双精度性能至少是单精度的八分之一
                fp64_bonus = 1.1  # 10%加分
            else:
                fp64_bonus = 1.0  # 无加分

            overall_score *= fp64_bonus

        price_performance[gpu_name] = {
            "detailed_ratios": pp_ratios,
            "overall_score": overall_score,
            "cost_per_hour": price,
            "supports_fp64": supports_fp64,
            "fp64_fp32_avg_ratio": data.get("fp64_fp32_avg_ratio", 0) if supports_fp64 else 0,
            "gpu_info": data.get("gpu_info", {}),
            "instance_info": data.get("instance_info", {})
        }

    # 按综合性价比排序
    sorted_gpus = sorted(
        price_performance.items(),
        key=lambda x: x[1]["overall_score"],
        reverse=True
    )

    # 生成报告
    report = {
        "timestamp": datetime.now().isoformat(),
        "sorted_by_price_performance": [
            {
                "gpu_name": gpu_name,
                "overall_score": data["overall_score"],
                "cost_per_hour": data["cost_per_hour"],
                "supports_fp64": data["supports_fp64"],
                "fp64_fp32_avg_ratio": data.get("fp64_fp32_avg_ratio", 0),
                "gpu_info": data["gpu_info"],
                "detailed_ratios": data["detailed_ratios"]
            }
            for gpu_name, data in sorted_gpus
        ],
        "raw_results": results
    }

    # 保存报告
    report_file = "gpu_benchmark_report.json"
    with open(report_file, "w") as f:
        json.dump(report, f, indent=2)

    # 打印结果摘要
    print("\n===== GPU性价比排名 =====")
    print("(特别关注双精度性能，适合量子计算)")
    for i, (gpu_name, data) in enumerate(sorted_gpus):
        fp64_status = "支持双精度" if data["supports_fp64"] else "不支持双精度"
        fp64_ratio = f", FP64/FP32比率={data.get('fp64_fp32_avg_ratio', 0):.2f}" if data["supports_fp64"] else ""
        print(f"{i+1}. {gpu_name}: 得分={data['overall_score']:.2f}, 价格=${data['cost_per_hour']}/小时, {fp64_status}{fp64_ratio}")

    print(f"\n详细报告已保存到 {report_file}")

    return report

def main():
    parser = argparse.ArgumentParser(description="GPU性价比测试工具")
    parser.add_argument("--gpu-types", nargs="+", help="要测试的GPU类型列表，如不指定则测试所有可用类型")
    parser.add_argument("--analyze-only", action="store_true", help="仅分析现有结果，不运行新测试")
    parser.add_argument("--max-price", type=float, help="最大每小时价格限制")
    args = parser.parse_args()

    if not args.analyze_only:
        # 要测试的GPU类型
        gpu_types = args.gpu_types or ["V100", "A100", "RTX 3090", "RTX 4090", "T4"]

        for gpu_type in gpu_types:
            test_gpu(gpu_type, args.max_price)

    # 分析结果
    analyze_results()

if __name__ == "__main__":
    main()
