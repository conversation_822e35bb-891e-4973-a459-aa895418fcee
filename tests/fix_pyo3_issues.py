#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复PyO3 0.23版本兼容性问题的脚本
"""

import os
import re
import sys
from pathlib import Path

def fix_sparse_matrix():
    """修复sparse_matrix.rs文件中的PyO3兼容性问题"""
    file_path = "src/sparse_matrix.rs"
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复acquire_gil调用
    content = re.sub(
        r'let py = Python::acquire_gil\(\);',
        r'let py = Python::with_gil(|py| {',
        content
    )
    
    # 修复函数结尾
    content = re.sub(
        r'Ok\(result\.into\(\)\)',
        r'Ok(result.into())\n        })',
        content
    )
    
    # 修复into_py调用
    content = re.sub(
        r'result\.insert\("format"\.to_string\(\), "csc"\.into_py\(py\)\);',
        r'result.insert("format".to_string(), "csc".into_py(py)?);',
        content
    )
    
    content = re.sub(
        r'result\.insert\("format"\.to_string\(\), "csr"\.into_py\(py\)\);',
        r'result.insert("format".to_string(), "csr".into_py(py)?);',
        content
    )
    
    # 修复PyList和PyTuple的into方法
    content = re.sub(
        r'(py_[a-z_]+)\.into\(\)',
        r'\1.into_py(py)',
        content
    )
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已修复: {file_path}")
    return True

def fix_multidim_correlation():
    """修复multidim_correlation.rs文件中的泛型函数问题"""
    file_path = "src/multidim_correlation.rs"
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复泛型函数问题
    # 查找generate_subsets<F>函数并移除泛型参数
    pattern = r'fn generate_subsets<F>\('
    replacement = r'fn generate_subsets('
    content = re.sub(pattern, replacement, content)
    
    # 修改函数签名，将F类型参数改为具体类型
    pattern = r'indices: &\[usize\], k: usize, callback: F\)\s+where\s+F: FnMut\(&\[usize\]\),'
    replacement = r'indices: &[usize], k: usize, callback: &mut dyn FnMut(&[usize]),)'
    content = re.sub(pattern, replacement, content)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已修复: {file_path}")
    return True

def fix_unified_field_evolution():
    """修复unified_field_evolution.rs文件中的Complex64问题"""
    file_path = "src/unified_field_evolution.rs"
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复Complex64问题
    pattern = r'Option<Vec<Complex64>>'
    replacement = r'Option<Vec<Complex64>>'
    
    # 添加FromPyObject实现
    if 'impl<\'py> FromPyObject<\'py> for Complex64' not in content:
        # 在文件末尾添加实现
        content += """

// 为Complex64实现FromPyObject特性
impl<'py> FromPyObject<'py> for Complex64 {
    fn extract_bound(obj: &Bound<'py, PyAny>) -> PyResult<Self> {
        let complex_tuple = obj.downcast::<PyTuple>()?;
        if complex_tuple.len() != 2 {
            return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                "Expected a tuple of (real, imag)",
            ));
        }
        
        let real: f64 = complex_tuple.get_item(0)?.extract()?;
        let imag: f64 = complex_tuple.get_item(1)?.extract()?;
        
        Ok(Complex64::new(real, imag))
    }
}
"""
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已修复: {file_path}")
    return True

def add_signature_attributes():
    """为所有带有Option参数的函数添加signature属性"""
    for file_path in Path("src").glob("*.rs"):
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找带有Option参数的函数
        pattern = r'#\[pymethods\][\s\S]*?fn ([a-zA-Z_][a-zA-Z0-9_]*)\s*\((?:[^)]*?Option<[^>]*>.*?)\)'
        matches = re.finditer(pattern, content, re.MULTILINE)
        
        for match in matches:
            func_name = match.group(1)
            func_def = match.group(0)
            
            # 如果函数定义中没有#[pyo3(signature = ...)]
            if '#[pyo3(signature =' not in func_def:
                # 提取参数
                params_pattern = r'\(\s*(?:&(?:mut)?\s*self,?\s*)?([^)]*)\)'
                params_match = re.search(params_pattern, func_def)
                
                if params_match:
                    params_str = params_match.group(1)
                    params = [p.strip() for p in params_str.split(',') if p.strip()]
                    
                    # 构建signature字符串
                    signature_params = []
                    for param in params:
                        param_parts = param.split(':')
                        if len(param_parts) >= 2:
                            param_name = param_parts[0].strip()
                            param_type = ':'.join(param_parts[1:]).strip()
                            
                            if 'Option<' in param_type:
                                signature_params.append(f"{param_name}=None")
                            else:
                                signature_params.append(param_name)
                    
                    if signature_params:
                        signature_str = f"#[pyo3(signature = ({', '.join(signature_params)}))]"
                        
                        # 在函数定义前添加signature属性
                        new_func_def = func_def.replace(
                            f"fn {func_name}",
                            f"{signature_str}\n    fn {func_name}"
                        )
                        
                        content = content.replace(func_def, new_func_def)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"已处理: {file_path}")

def main():
    """主函数"""
    print("开始修复PyO3 0.23版本兼容性问题...")
    
    # 修复sparse_matrix.rs
    fix_sparse_matrix()
    
    # 修复multidim_correlation.rs
    fix_multidim_correlation()
    
    # 修复unified_field_evolution.rs
    fix_unified_field_evolution()
    
    # 为所有带有Option参数的函数添加signature属性
    add_signature_attributes()
    
    print("修复完成！")

if __name__ == "__main__":
    main()
