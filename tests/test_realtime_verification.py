"""
实时验证算子测试脚本
"""

import sys
import os
import unittest
import time
import threading
import numpy as np

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

from src.operators.verification.realtime_verification import RealtimeVerificationOperator

# 检查Rust实现是否可用
try:
    from src.operators.verification.rust_wrapper import (
        RustRealtimeVerificationOperator,
        RUST_AVAILABLE
    )
except ImportError:
    RUST_AVAILABLE = False


class TestRealtimeVerificationOperator(unittest.TestCase):
    """实时验证算子测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建Python实现的算子
        self.py_operator = RealtimeVerificationOperator(
            monitoring_interval=0.1,
            violation_threshold=0.8,
            max_history_size=100,
            auto_start=False,
            properties=[
                {
                    "id": "prop1",
                    "type": "safety",
                    "expression": "G(x > 0)"
                },
                {
                    "id": "prop2",
                    "type": "liveness",
                    "expression": "F(y = 1)"
                },
                {
                    "id": "prop3",
                    "type": "safety",
                    "expression": "G(x + y < 100)"
                }
            ]
        )
        
        # 如果Rust实现可用，创建Rust实现的算子
        if RUST_AVAILABLE:
            self.rust_operator = RustRealtimeVerificationOperator(
                monitoring_interval=0.1,
                violation_threshold=0.8,
                max_history_size=100,
                auto_start=False,
                properties=[
                    {
                        "id": "prop1",
                        "type": "safety",
                        "expression": "G(x > 0)"
                    },
                    {
                        "id": "prop2",
                        "type": "liveness",
                        "expression": "F(y = 1)"
                    },
                    {
                        "id": "prop3",
                        "type": "safety",
                        "expression": "G(x + y < 100)"
                    }
                ]
            )
        
        # 准备测试数据
        self.test_data = {
            "state": {
                "variables": {
                    "x": 10,
                    "y": 20,
                    "z": 30
                }
            },
            "properties": [
                {
                    "id": "prop1",
                    "type": "safety",
                    "expression": "G(x > 0)"
                },
                {
                    "id": "prop2",
                    "type": "liveness",
                    "expression": "F(y = 1)"
                },
                {
                    "id": "prop3",
                    "type": "safety",
                    "expression": "G(x + y < 100)"
                }
            ]
        }
    
    def tearDown(self):
        """清理测试环境"""
        # 停止Python实现的监控
        self.py_operator.stop_monitoring()
        
        # 如果Rust实现可用，停止Rust实现的监控
        if RUST_AVAILABLE:
            self.rust_operator.stop_monitoring()
    
    def test_python_implementation(self):
        """测试Python实现"""
        # 应用算子
        result = self.py_operator.apply(self.test_data)
        
        # 验证结果
        self.assertIn('verification_results', result)
        self.assertIn('violations', result)
        self.assertIn('monitoring_active', result)
        self.assertIn('history_size', result)
        
        # 验证验证结果
        verification_results = result['verification_results']
        self.assertIn('prop1', verification_results)
        self.assertIn('prop2', verification_results)
        self.assertIn('prop3', verification_results)
        
        # 验证监控状态
        self.assertFalse(result['monitoring_active'])
        
        # 启动监控
        start_result = self.py_operator.apply(self.test_data, action='start')
        self.assertEqual(start_result['status'], 'monitoring_started')
        
        # 等待一段时间
        time.sleep(0.3)
        
        # 停止监控
        stop_result = self.py_operator.apply(self.test_data, action='stop')
        self.assertEqual(stop_result['status'], 'monitoring_stopped')
        
        # 获取历史记录
        history = self.py_operator.get_history()
        self.assertGreater(len(history), 0)
    
    @unittest.skipIf(not RUST_AVAILABLE, "Rust implementation not available")
    def test_rust_implementation(self):
        """测试Rust实现"""
        # 应用算子
        result = self.rust_operator.apply(self.test_data)
        
        # 验证结果
        self.assertIn('verification_results', result)
        self.assertIn('violations', result)
        self.assertIn('monitoring_active', result)
        self.assertIn('history_size', result)
        
        # 验证验证结果
        verification_results = result['verification_results']
        self.assertIn('prop1', verification_results)
        self.assertIn('prop2', verification_results)
        self.assertIn('prop3', verification_results)
        
        # 验证监控状态
        self.assertFalse(result['monitoring_active'])
        
        # 启动监控
        start_result = self.rust_operator.apply(self.test_data, action='start')
        self.assertEqual(start_result['status'], 'monitoring_started')
        
        # 等待一段时间
        time.sleep(0.3)
        
        # 停止监控
        stop_result = self.rust_operator.apply(self.test_data, action='stop')
        self.assertEqual(stop_result['status'], 'monitoring_stopped')
        
        # 获取历史记录
        history = self.rust_operator.get_history()
        self.assertGreater(len(history), 0)
    
    @unittest.skipIf(not RUST_AVAILABLE, "Rust implementation not available")
    def test_performance_comparison(self):
        """比较Python和Rust实现的性能"""
        # 准备大量测试数据
        test_data_list = []
        for i in range(100):
            test_data = {
                "state": {
                    "variables": {
                        "x": i * 0.1,
                        "y": i * 0.2,
                        "z": i * 0.3
                    }
                },
                "properties": [
                    {
                        "id": "prop1",
                        "type": "safety",
                        "expression": f"G(x > {i * 0.05})"
                    },
                    {
                        "id": "prop2",
                        "type": "liveness",
                        "expression": f"F(y = {i * 0.1})"
                    },
                    {
                        "id": "prop3",
                        "type": "safety",
                        "expression": f"G(x + y < {100 + i})"
                    }
                ]
            }
            test_data_list.append(test_data)
        
        # 测量Python实现的性能
        py_start_time = time.time()
        for test_data in test_data_list:
            self.py_operator.apply(test_data)
        py_end_time = time.time()
        py_elapsed_time = py_end_time - py_start_time
        
        # 测量Rust实现的性能
        rust_start_time = time.time()
        for test_data in test_data_list:
            self.rust_operator.apply(test_data)
        rust_end_time = time.time()
        rust_elapsed_time = rust_end_time - rust_start_time
        
        # 输出性能比较结果
        print(f"Python implementation: {py_elapsed_time:.4f} seconds")
        print(f"Rust implementation: {rust_elapsed_time:.4f} seconds")
        print(f"Speedup: {py_elapsed_time / rust_elapsed_time:.2f}x")
        
        # 验证Rust实现比Python实现快
        self.assertLess(rust_elapsed_time, py_elapsed_time)
    
    @unittest.skipIf(not RUST_AVAILABLE, "Rust implementation not available")
    def test_monitoring_lifecycle(self):
        """测试监控生命周期"""
        # 启动监控
        self.rust_operator.start_monitoring()
        
        # 验证监控状态
        self.assertTrue(self.rust_operator.apply(self.test_data)['monitoring_active'])
        
        # 等待一段时间
        time.sleep(0.3)
        
        # 重置监控
        self.rust_operator.reset_monitoring()
        
        # 验证历史记录被清空
        history = self.rust_operator.get_history()
        self.assertEqual(len(history), 0)
        
        # 验证监控仍然活跃
        self.assertTrue(self.rust_operator.apply(self.test_data)['monitoring_active'])
        
        # 停止监控
        self.rust_operator.stop_monitoring()
        
        # 验证监控状态
        self.assertFalse(self.rust_operator.apply(self.test_data)['monitoring_active'])
    
    @unittest.skipIf(not RUST_AVAILABLE, "Rust implementation not available")
    def test_parameter_customization(self):
        """测试参数自定义"""
        # 创建自定义参数的算子
        custom_operator = RustRealtimeVerificationOperator(
            monitoring_interval=0.5,
            violation_threshold=0.7,
            max_history_size=50,
            auto_start=False,
            properties=[]
        )
        
        # 设置参数
        custom_operator.set_parameters({
            "monitoring_interval": 0.2,
            "violation_threshold": 0.6,
            "max_history_size": 200,
            "auto_start": True,
            "properties": [
                {
                    "id": "custom1",
                    "type": "safety",
                    "expression": "G(x > 0)"
                }
            ]
        })
        
        # 获取参数
        params = custom_operator.get_parameters()
        self.assertEqual(params['monitoring_interval'], 0.2)
        self.assertEqual(params['violation_threshold'], 0.6)
        self.assertEqual(params['max_history_size'], 200)
        self.assertEqual(params['auto_start'], True)
        self.assertEqual(len(params['properties']), 1)
        
        # 应用算子
        result = custom_operator.apply(self.test_data)
        
        # 验证结果
        self.assertIn('verification_results', result)
        self.assertIn('violations', result)
        self.assertIn('monitoring_active', result)
        self.assertIn('history_size', result)
        
        # 停止监控
        custom_operator.stop_monitoring()


if __name__ == '__main__':
    unittest.main()
