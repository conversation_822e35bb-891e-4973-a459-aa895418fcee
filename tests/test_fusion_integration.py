"""
超越态融合算子集成测试

本脚本用于测试超越态融合算子与其他模块的集成。
"""

import sys
import os
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# 导入警告抑制工具
from src.utils.warning_suppressor import suppress_warnings

# 使用警告抑制工具
with suppress_warnings():
    # 导入融合算子
    from src.operators.fusion import (
        quantum_superposition_fusion,
        holographic_interference_fusion,
        fractal_fusion,
        topological_fusion,
        fusion_with_method,
        get_fusion_operator_by_name,
        __version__
    )
    
    # 导入注册表
    try:
        from src.tri_fusion.core.registry import registry
        REGISTRY_AVAILABLE = True
    except ImportError:
        try:
            from src.registry import get_operator
            REGISTRY_AVAILABLE = True
        except ImportError:
            REGISTRY_AVAILABLE = False

def test_version():
    """测试版本信息"""
    print(f"\n版本信息: {__version__}")
    assert __version__ == "1.0.0", f"版本号不匹配: {__version__} != 1.0.0"

def test_direct_import():
    """测试直接导入"""
    print("\n测试直接导入:")
    
    # 创建测试状态
    state_a = [1.0 + 0.0j, 0.0 + 0.0j]  # |0⟩
    state_b = [0.0 + 0.0j, 1.0 + 0.0j]  # |1⟩
    
    # 使用量子叠加融合
    result = quantum_superposition_fusion(state_a, state_b)
    print("量子叠加融合结果:", result)
    assert len(result) == 2, f"结果维度不匹配: {len(result)} != 2"
    
    # 验证结果是否为单位向量
    norm = np.sqrt(np.sum(np.abs(np.array(result))**2))
    print("结果范数:", norm)
    assert abs(norm - 1.0) < 1e-10, f"结果不是单位向量: {norm} != 1.0"

def test_registry_integration():
    """测试注册表集成"""
    if not REGISTRY_AVAILABLE:
        print("\n注册表不可用，跳过注册表集成测试")
        return
    
    print("\n测试注册表集成:")
    
    # 通过注册表获取融合算子
    try:
        # 获取融合算子
        fusion_op = get_fusion_operator_by_name("fusion.quantum_superposition")
        assert fusion_op is not None, "无法通过注册表获取融合算子"
        
        # 创建测试状态
        state_a = [1.0 + 0.0j, 0.0 + 0.0j]  # |0⟩
        state_b = [0.0 + 0.0j, 1.0 + 0.0j]  # |1⟩
        
        # 使用融合算子
        result = fusion_op(state_a, state_b)
        print("通过注册表获取的融合算子结果:", result)
        assert len(result) == 2, f"结果维度不匹配: {len(result)} != 2"
        
        # 验证结果是否为单位向量
        norm = np.sqrt(np.sum(np.abs(np.array(result))**2))
        print("结果范数:", norm)
        assert abs(norm - 1.0) < 1e-10, f"结果不是单位向量: {norm} != 1.0"
    except Exception as e:
        print(f"注册表集成测试失败: {e}")
        return False
    
    return True

def test_method_integration():
    """测试不同方法的集成"""
    print("\n测试不同方法的集成:")
    
    # 创建测试状态
    state_a = [1.0 + 0.0j, 0.0 + 0.0j]  # |0⟩
    state_b = [0.0 + 0.0j, 1.0 + 0.0j]  # |1⟩
    
    # 使用不同的融合方法
    methods = {
        "quantum_superposition": quantum_superposition_fusion,
        "holographic_interference": holographic_interference_fusion,
        "fractal_fusion": fractal_fusion,
        "topological_fusion": topological_fusion,
    }
    
    for name, method in methods.items():
        try:
            # 直接调用方法
            result_direct = method(state_a, state_b)
            
            # 通过通用方法调用
            result_generic = fusion_with_method(state_a, state_b, name)
            
            # 验证结果是否一致
            diff = np.array(result_direct) - np.array(result_generic)
            diff_norm = np.sqrt(np.sum(np.abs(diff)**2))
            
            print(f"方法 {name} 结果差异: {diff_norm:.10f}")
            assert diff_norm < 1e-10, f"直接调用和通用方法调用的结果不一致: {diff_norm} > 1e-10"
        except Exception as e:
            print(f"方法 {name} 集成测试失败: {e}")
            return False
    
    return True

def test_numpy_integration():
    """测试与NumPy的集成"""
    print("\n测试与NumPy的集成:")
    
    # 创建NumPy数组
    state_a = np.array([1.0 + 0.0j, 0.0 + 0.0j])
    state_b = np.array([0.0 + 0.0j, 1.0 + 0.0j])
    
    try:
        # 转换为列表
        state_a_list = state_a.tolist()
        state_b_list = state_b.tolist()
        
        # 使用融合算子
        result = quantum_superposition_fusion(state_a_list, state_b_list)
        
        # 转换回NumPy数组
        result_array = np.array(result)
        
        # 验证结果是否为单位向量
        norm = np.sqrt(np.sum(np.abs(result_array)**2))
        
        print("NumPy集成结果:", result_array)
        print("结果范数:", norm)
        assert abs(norm - 1.0) < 1e-10, f"结果不是单位向量: {norm} != 1.0"
    except Exception as e:
        print(f"NumPy集成测试失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("开始超越态融合算子集成测试...")
    
    # 测试版本信息
    test_version()
    
    # 测试直接导入
    test_direct_import()
    
    # 测试注册表集成
    test_registry_integration()
    
    # 测试不同方法的集成
    test_method_integration()
    
    # 测试与NumPy的集成
    test_numpy_integration()
    
    print("\n集成测试完成")
