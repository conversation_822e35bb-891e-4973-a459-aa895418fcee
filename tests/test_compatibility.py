"""
接口兼容性算子测试。

测试接口适配器、版本桥接器和数据转换器的功能。
"""

import unittest
import numpy as np
import json
from typing import Dict, List, Any, Optional

from src.operators.compatibility.adapter import (
    create_adapter, adapt_interface, register_adapter, get_adapter,
    adapt_parameter, adapt_return
)
from src.operators.compatibility.version import (
    Version, check_version_compatibility, create_version_bridge,
    register_version_bridge, get_version_bridge, register_object_version,
    bridge_object, version_compatible, version_bridge
)
from src.operators.compatibility.converter import (
    convert_data, register_converter, get_converter, create_conversion_pipeline,
    convert_parameter, convert_return
)
from src.operators.compatibility.common_adapters import (
    DataSource, DataSink, Serializable, Observable, Iterator, Iterable,
    register_all_adapters
)
from src.operators.compatibility.common_bridges import (
    DataModelV1, DataModelV2, ConfigV1, ConfigV2, MatrixV1, MatrixV2,
    register_all_bridges
)
from src.operators.compatibility.common_converters import (
    Point, Vector, register_all_converters, create_common_pipelines
)


class TestAdapter(unittest.TestCase):
    """测试接口适配器。"""
    
    def setUp(self):
        """设置测试环境。"""
        register_all_adapters()
    
    def test_dict_to_data_source(self):
        """测试字典到DataSource的适配。"""
        # 创建测试数据
        test_dict = {
            'data': [1, 2, 3],
            'metadata': {'name': 'test', 'version': '1.0.0'}
        }
        
        # 适配到DataSource
        data_source = adapt_interface(test_dict, DataSource)
        
        # 验证适配结果
        self.assertIsNotNone(data_source)
        self.assertEqual(data_source.get_data(), test_dict['data'])
        self.assertEqual(data_source.get_metadata(), test_dict['metadata'])
    
    def test_list_to_iterator(self):
        """测试列表到Iterator的适配。"""
        # 创建测试数据
        test_list = [1, 2, 3, 4, 5]
        
        # 适配到Iterator
        iterator = adapt_interface(test_list, Iterator)
        
        # 验证适配结果
        self.assertIsNotNone(iterator)
        self.assertTrue(iterator.has_next())
        self.assertEqual(iterator.next(), 1)
        self.assertEqual(iterator.next(), 2)
        self.assertEqual(iterator.next(), 3)
        self.assertEqual(iterator.next(), 4)
        self.assertEqual(iterator.next(), 5)
        self.assertFalse(iterator.has_next())
    
    def test_ndarray_to_data_source(self):
        """测试NumPy数组到DataSource的适配。"""
        # 创建测试数据
        test_array = np.array([[1, 2, 3], [4, 5, 6]])
        
        # 适配到DataSource
        data_source = adapt_interface(test_array, DataSource)
        
        # 验证适配结果
        self.assertIsNotNone(data_source)
        self.assertTrue(np.array_equal(data_source.get_data(), test_array))
        self.assertEqual(data_source.get_metadata()['shape'], test_array.shape)
        self.assertEqual(data_source.get_metadata()['dtype'], str(test_array.dtype))
    
    def test_function_to_observable(self):
        """测试函数到Observable的适配。"""
        # 创建测试数据
        def test_func(x, y):
            return x + y
        
        # 适配到Observable
        observable = adapt_interface(test_func, Observable)
        
        # 验证适配结果
        self.assertIsNotNone(observable)
        
        # 测试观察者功能
        results = []
        def observer(result):
            results.append(result)
        
        observable.add_observer(observer)
        result = observable(2, 3)
        
        self.assertEqual(result, 5)
        self.assertEqual(results, [5])
    
    def test_iterator_to_iterable(self):
        """测试Iterator到Iterable的适配。"""
        # 创建测试数据
        test_list = [1, 2, 3, 4, 5]
        iterator = adapt_interface(test_list, Iterator)
        
        # 适配到Iterable
        iterable = adapt_interface(iterator, Iterable)
        
        # 验证适配结果
        self.assertIsNotNone(iterable)
        
        # 测试迭代功能
        items = list(iterable)
        self.assertEqual(items, test_list)
    
    def test_adapt_parameter_decorator(self):
        """测试adapt_parameter装饰器。"""
        # 创建测试函数
        @adapt_parameter('data_source', DataSource)
        def process_data(data_source):
            return data_source.get_data()
        
        # 创建测试数据
        test_dict = {
            'data': [1, 2, 3],
            'metadata': {'name': 'test', 'version': '1.0.0'}
        }
        
        # 调用函数
        result = process_data(test_dict)
        
        # 验证结果
        self.assertEqual(result, test_dict['data'])
    
    def test_adapt_return_decorator(self):
        """测试adapt_return装饰器。"""
        # 创建测试函数
        @adapt_return(Iterator)
        def get_data():
            return [1, 2, 3, 4, 5]
        
        # 调用函数
        result = get_data()
        
        # 验证结果
        self.assertTrue(hasattr(result, 'has_next'))
        self.assertTrue(hasattr(result, 'next'))
        self.assertTrue(result.has_next())
        self.assertEqual(result.next(), 1)


class TestVersionBridge(unittest.TestCase):
    """测试版本桥接器。"""
    
    def setUp(self):
        """设置测试环境。"""
        register_all_bridges()
    
    def test_version_compatibility(self):
        """测试版本兼容性检查。"""
        # 测试兼容的版本
        self.assertTrue(check_version_compatibility("1.0.0", "1.1.0"))
        self.assertTrue(check_version_compatibility("1.0.0", "1.0.1"))
        
        # 测试不兼容的版本
        self.assertFalse(check_version_compatibility("1.0.0", "2.0.0"))
        self.assertFalse(check_version_compatibility("2.0.0", "1.0.0"))
    
    def test_data_model_bridge(self):
        """测试DataModel版本桥接。"""
        # 创建V1版本对象
        model_v1 = DataModelV1("test", 42.0)
        
        # 桥接到V2版本
        model_v2 = bridge_object(model_v1, "2.0.0")
        
        # 验证桥接结果
        self.assertIsNotNone(model_v2)
        self.assertIsInstance(model_v2, DataModelV2)
        self.assertEqual(model_v2.name, model_v1.name)
        self.assertEqual(model_v2.value, model_v1.value)
        self.assertIsNotNone(model_v2.timestamp)
        
        # 桥接回V1版本
        model_v1_restored = bridge_object(model_v2, "1.0.0")
        
        # 验证桥接结果
        self.assertIsNotNone(model_v1_restored)
        self.assertIsInstance(model_v1_restored, DataModelV1)
        self.assertEqual(model_v1_restored.name, model_v1.name)
        self.assertEqual(model_v1_restored.value, model_v1.value)
    
    def test_config_bridge(self):
        """测试Config版本桥接。"""
        # 创建V1版本对象
        config_v1 = ConfigV1({
            'name': 'test',
            'value': 42.0,
            'debug.enabled': True,
            'debug.level': 'info'
        })
        
        # 桥接到V2版本
        config_v2 = bridge_object(config_v1, "2.0.0")
        
        # 验证桥接结果
        self.assertIsNotNone(config_v2)
        self.assertIsInstance(config_v2, ConfigV2)
        self.assertEqual(config_v2.get('name'), config_v1.get('name'))
        self.assertEqual(config_v2.get('value'), config_v1.get('value'))
        self.assertEqual(config_v2.get('debug.enabled'), config_v1.get('debug.enabled'))
        self.assertEqual(config_v2.get('debug.level'), config_v1.get('debug.level'))
        
        # 桥接回V1版本
        config_v1_restored = bridge_object(config_v2, "1.0.0")
        
        # 验证桥接结果
        self.assertIsNotNone(config_v1_restored)
        self.assertIsInstance(config_v1_restored, ConfigV1)
        self.assertEqual(config_v1_restored.get('name'), config_v1.get('name'))
        self.assertEqual(config_v1_restored.get('value'), config_v1.get('value'))
        self.assertEqual(config_v1_restored.get('debug.enabled'), config_v1.get('debug.enabled'))
        self.assertEqual(config_v1_restored.get('debug.level'), config_v1.get('debug.level'))
    
    def test_matrix_bridge(self):
        """测试Matrix版本桥接。"""
        # 创建V1版本对象
        matrix_v1 = MatrixV1([[1, 2, 3], [4, 5, 6]])
        
        # 桥接到V2版本
        matrix_v2 = bridge_object(matrix_v1, "2.0.0")
        
        # 验证桥接结果
        self.assertIsNotNone(matrix_v2)
        self.assertIsInstance(matrix_v2, MatrixV2)
        self.assertEqual(matrix_v2.shape(), matrix_v1.shape())
        self.assertEqual(matrix_v2.get(0, 0), matrix_v1.get(0, 0))
        self.assertEqual(matrix_v2.get(0, 1), matrix_v1.get(0, 1))
        self.assertEqual(matrix_v2.get(0, 2), matrix_v1.get(0, 2))
        self.assertEqual(matrix_v2.get(1, 0), matrix_v1.get(1, 0))
        self.assertEqual(matrix_v2.get(1, 1), matrix_v1.get(1, 1))
        self.assertEqual(matrix_v2.get(1, 2), matrix_v1.get(1, 2))
        
        # 桥接回V1版本
        matrix_v1_restored = bridge_object(matrix_v2, "1.0.0")
        
        # 验证桥接结果
        self.assertIsNotNone(matrix_v1_restored)
        self.assertIsInstance(matrix_v1_restored, MatrixV1)
        self.assertEqual(matrix_v1_restored.shape(), matrix_v1.shape())
        self.assertEqual(matrix_v1_restored.get(0, 0), matrix_v1.get(0, 0))
        self.assertEqual(matrix_v1_restored.get(0, 1), matrix_v1.get(0, 1))
        self.assertEqual(matrix_v1_restored.get(0, 2), matrix_v1.get(0, 2))
        self.assertEqual(matrix_v1_restored.get(1, 0), matrix_v1.get(1, 0))
        self.assertEqual(matrix_v1_restored.get(1, 1), matrix_v1.get(1, 1))
        self.assertEqual(matrix_v1_restored.get(1, 2), matrix_v1.get(1, 2))
    
    def test_version_compatible_decorator(self):
        """测试version_compatible装饰器。"""
        # 创建测试函数
        @version_compatible("1.0.0")
        def process_data_model(model):
            return model.name
        
        # 创建测试数据
        model_v1 = DataModelV1("test", 42.0)
        model_v2 = DataModelV2("test", 42.0, 123456789.0)
        
        # 调用函数
        result_v1 = process_data_model(model_v1)
        
        # 验证结果
        self.assertEqual(result_v1, model_v1.name)
        
        # 调用函数（应该抛出异常，因为版本不兼容）
        with self.assertRaises(ValueError):
            process_data_model(model_v2)
    
    def test_version_bridge_decorator(self):
        """测试version_bridge装饰器。"""
        # 创建测试函数
        @version_bridge("2.0.0")
        def get_data_model():
            return DataModelV1("test", 42.0)
        
        # 调用函数
        result = get_data_model()
        
        # 验证结果
        self.assertIsInstance(result, DataModelV2)
        self.assertEqual(result.name, "test")
        self.assertEqual(result.value, 42.0)
        self.assertIsNotNone(result.timestamp)


class TestConverter(unittest.TestCase):
    """测试数据转换器。"""
    
    def setUp(self):
        """设置测试环境。"""
        register_all_converters()
    
    def test_basic_conversions(self):
        """测试基本类型转换。"""
        # 字符串到整数
        self.assertEqual(convert_data("42", int), 42)
        
        # 字符串到浮点数
        self.assertEqual(convert_data("3.14", float), 3.14)
        
        # 字符串到布尔值
        self.assertEqual(convert_data("true", bool), True)
        self.assertEqual(convert_data("false", bool), False)
        
        # 整数到字符串
        self.assertEqual(convert_data(42, str), "42")
        
        # 浮点数到字符串
        self.assertEqual(convert_data(3.14, str), "3.14")
        
        # 布尔值到字符串
        self.assertEqual(convert_data(True, str), "True")
    
    def test_collection_conversions(self):
        """测试集合类型转换。"""
        # 列表到集合
        self.assertEqual(convert_data([1, 2, 3, 1], set), {1, 2, 3})
        
        # 集合到列表
        self.assertEqual(set(convert_data({1, 2, 3}, list)), {1, 2, 3})
        
        # 列表到元组
        self.assertEqual(convert_data([1, 2, 3], tuple), (1, 2, 3))
        
        # 元组到列表
        self.assertEqual(convert_data((1, 2, 3), list), [1, 2, 3])
        
        # 字典到列表（键）
        self.assertEqual(set(convert_data({'a': 1, 'b': 2}, list)), {'a', 'b'})
    
    def test_numpy_conversions(self):
        """测试NumPy相关转换。"""
        # 列表到NumPy数组
        array = convert_data([1, 2, 3], np.ndarray)
        self.assertIsInstance(array, np.ndarray)
        self.assertTrue(np.array_equal(array, np.array([1, 2, 3])))
        
        # NumPy数组到列表
        self.assertEqual(convert_data(np.array([1, 2, 3]), list), [1, 2, 3])
        
        # 字符串到NumPy数组
        array = convert_data("[[1, 2], [3, 4]]", np.ndarray)
        self.assertIsInstance(array, np.ndarray)
        self.assertTrue(np.array_equal(array, np.array([[1, 2], [3, 4]])))
    
    def test_custom_conversions(self):
        """测试自定义类型转换。"""
        # Point到Vector
        point = Point(1.0, 2.0)
        vector = convert_data(point, Vector)
        self.assertIsInstance(vector, Vector)
        self.assertEqual(vector.x, point.x)
        self.assertEqual(vector.y, point.y)
        
        # Vector到Point
        vector = Vector(3.0, 4.0)
        point = convert_data(vector, Point)
        self.assertIsInstance(point, Point)
        self.assertEqual(point.x, vector.x)
        self.assertEqual(point.y, vector.y)
        
        # Point到元组
        point = Point(1.0, 2.0)
        tup = convert_data(point, tuple)
        self.assertEqual(tup, (1.0, 2.0))
        
        # 元组到Point
        tup = (3.0, 4.0)
        point = convert_data(tup, Point)
        self.assertIsInstance(point, Point)
        self.assertEqual(point.x, tup[0])
        self.assertEqual(point.y, tup[1])
        
        # Point到字典
        point = Point(1.0, 2.0)
        d = convert_data(point, dict)
        self.assertEqual(d, {'x': 1.0, 'y': 2.0})
        
        # 字典到Point
        d = {'x': 3.0, 'y': 4.0}
        point = convert_data(d, Point)
        self.assertIsInstance(point, Point)
        self.assertEqual(point.x, d['x'])
        self.assertEqual(point.y, d['y'])
    
    def test_conversion_pipeline(self):
        """测试转换流水线。"""
        # 创建常用的转换流水线
        pipelines = create_common_pipelines()
        
        # 测试字典到Point的转换流水线
        d = {'x': 3.0, 'y': 4.0}
        pipeline = pipelines['dict_to_point']
        point = pipeline.convert(d)
        self.assertIsInstance(point, Point)
        self.assertEqual(point.x, d['x'])
        self.assertEqual(point.y, d['y'])
    
    def test_convert_parameter_decorator(self):
        """测试convert_parameter装饰器。"""
        # 创建测试函数
        @convert_parameter('point', Point)
        def process_point(point):
            return point.x + point.y
        
        # 创建测试数据
        test_dict = {'x': 3.0, 'y': 4.0}
        
        # 调用函数
        result = process_point(test_dict)
        
        # 验证结果
        self.assertEqual(result, 7.0)
    
    def test_convert_return_decorator(self):
        """测试convert_return装饰器。"""
        # 创建测试函数
        @convert_return(Vector)
        def get_point():
            return Point(3.0, 4.0)
        
        # 调用函数
        result = get_point()
        
        # 验证结果
        self.assertIsInstance(result, Vector)
        self.assertEqual(result.x, 3.0)
        self.assertEqual(result.y, 4.0)


if __name__ == '__main__':
    unittest.main()
