"""
一致性验证算子测试脚本
"""

import sys
import os
import unittest
import time
import numpy as np

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

from src.operators.verification.consistency_verification import ConsistencyVerificationOperator

# 检查Rust实现是否可用
try:
    from src.operators.verification.rust_wrapper import (
        RustConsistencyVerificationOperator,
        RUST_AVAILABLE
    )
except ImportError:
    RUST_AVAILABLE = False


class TestConsistencyVerificationOperator(unittest.TestCase):
    """一致性验证算子测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建Python实现的算子
        self.py_operator = ConsistencyVerificationOperator(
            consistency_threshold=0.8,
            check_logical_consistency=True,
            check_temporal_consistency=True,
            check_causal_consistency=True,
            properties=[
                {
                    "id": "prop1",
                    "type": "safety",
                    "expression": "G(x > 0)"
                },
                {
                    "id": "prop2",
                    "type": "liveness",
                    "expression": "F(y = 1)"
                },
                {
                    "id": "prop3",
                    "type": "safety",
                    "expression": "G(x + y < 100)"
                }
            ]
        )
        
        # 如果Rust实现可用，创建Rust实现的算子
        if RUST_AVAILABLE:
            self.rust_operator = RustConsistencyVerificationOperator(
                consistency_threshold=0.8,
                check_logical_consistency=True,
                check_temporal_consistency=True,
                check_causal_consistency=True,
                properties=[
                    {
                        "id": "prop1",
                        "type": "safety",
                        "expression": "G(x > 0)"
                    },
                    {
                        "id": "prop2",
                        "type": "liveness",
                        "expression": "F(y = 1)"
                    },
                    {
                        "id": "prop3",
                        "type": "safety",
                        "expression": "G(x + y < 100)"
                    }
                ]
            )
        
        # 准备测试数据
        self.test_data = {
            "state": {
                "variables": {
                    "x": 10,
                    "y": 20,
                    "z": 30
                }
            },
            "properties": [
                {
                    "id": "prop1",
                    "type": "safety",
                    "expression": "G(x > 0)"
                },
                {
                    "id": "prop2",
                    "type": "liveness",
                    "expression": "F(y = 1)"
                },
                {
                    "id": "prop3",
                    "type": "safety",
                    "expression": "G(x + y < 100)"
                }
            ]
        }
    
    def test_python_implementation(self):
        """测试Python实现"""
        # 应用算子
        result = self.py_operator.apply(self.test_data)
        
        # 验证结果
        self.assertIn('pairwise_consistency', result)
        self.assertIn('global_consistency', result)
        self.assertIn('inconsistencies', result)
        self.assertIn('is_consistent', result)
        self.assertIn('consistency_score', result)
        
        # 验证一致性评分
        self.assertGreaterEqual(result['consistency_score'], 0.0)
        self.assertLessEqual(result['consistency_score'], 1.0)
        
        # 验证全局一致性
        global_consistency = result['global_consistency']
        self.assertIn('logical', global_consistency)
        self.assertIn('temporal', global_consistency)
        self.assertIn('causal', global_consistency)
        self.assertIn('overall', global_consistency)
    
    @unittest.skipIf(not RUST_AVAILABLE, "Rust implementation not available")
    def test_rust_implementation(self):
        """测试Rust实现"""
        # 应用算子
        result = self.rust_operator.apply(self.test_data)
        
        # 验证结果
        self.assertIn('pairwise_consistency', result)
        self.assertIn('global_consistency', result)
        self.assertIn('inconsistencies', result)
        self.assertIn('is_consistent', result)
        self.assertIn('consistency_score', result)
        
        # 验证一致性评分
        self.assertGreaterEqual(result['consistency_score'], 0.0)
        self.assertLessEqual(result['consistency_score'], 1.0)
        
        # 验证全局一致性
        global_consistency = result['global_consistency']
        self.assertIn('logical', global_consistency)
        self.assertIn('temporal', global_consistency)
        self.assertIn('causal', global_consistency)
        self.assertIn('overall', global_consistency)
    
    @unittest.skipIf(not RUST_AVAILABLE, "Rust implementation not available")
    def test_performance_comparison(self):
        """比较Python和Rust实现的性能"""
        # 准备大量测试数据
        test_data_list = []
        for i in range(100):
            test_data = {
                "state": {
                    "variables": {
                        "x": i * 0.1,
                        "y": i * 0.2,
                        "z": i * 0.3
                    }
                },
                "properties": [
                    {
                        "id": "prop1",
                        "type": "safety",
                        "expression": f"G(x > {i * 0.05})"
                    },
                    {
                        "id": "prop2",
                        "type": "liveness",
                        "expression": f"F(y = {i * 0.1})"
                    },
                    {
                        "id": "prop3",
                        "type": "safety",
                        "expression": f"G(x + y < {100 + i})"
                    }
                ]
            }
            test_data_list.append(test_data)
        
        # 测量Python实现的性能
        py_start_time = time.time()
        for test_data in test_data_list:
            self.py_operator.apply(test_data)
        py_end_time = time.time()
        py_elapsed_time = py_end_time - py_start_time
        
        # 测量Rust实现的性能
        rust_start_time = time.time()
        for test_data in test_data_list:
            self.rust_operator.apply(test_data)
        rust_end_time = time.time()
        rust_elapsed_time = rust_end_time - rust_start_time
        
        # 输出性能比较结果
        print(f"Python implementation: {py_elapsed_time:.4f} seconds")
        print(f"Rust implementation: {rust_elapsed_time:.4f} seconds")
        print(f"Speedup: {py_elapsed_time / rust_elapsed_time:.2f}x")
        
        # 验证Rust实现比Python实现快
        self.assertLess(rust_elapsed_time, py_elapsed_time)
    
    @unittest.skipIf(not RUST_AVAILABLE, "Rust implementation not available")
    def test_consistency_types(self):
        """测试不同类型的一致性检查"""
        # 创建只检查逻辑一致性的算子
        logical_operator = RustConsistencyVerificationOperator(
            consistency_threshold=0.8,
            check_logical_consistency=True,
            check_temporal_consistency=False,
            check_causal_consistency=False,
            properties=self.test_data["properties"]
        )
        
        # 应用算子
        result = logical_operator.apply(self.test_data)
        
        # 验证全局一致性
        global_consistency = result['global_consistency']
        self.assertIn('logical', global_consistency)
        self.assertIn('temporal', global_consistency)
        self.assertIn('causal', global_consistency)
        self.assertIn('overall', global_consistency)
        
        # 验证逻辑一致性评分
        self.assertGreaterEqual(global_consistency['logical'], 0.0)
        self.assertLessEqual(global_consistency['logical'], 1.0)
        
        # 验证时间一致性和因果一致性评分为1.0（因为未检查）
        self.assertEqual(global_consistency['temporal'], 1.0)
        self.assertEqual(global_consistency['causal'], 1.0)
    
    @unittest.skipIf(not RUST_AVAILABLE, "Rust implementation not available")
    def test_parameter_customization(self):
        """测试参数自定义"""
        # 创建自定义参数的算子
        custom_operator = RustConsistencyVerificationOperator(
            consistency_threshold=0.7,
            check_logical_consistency=True,
            check_temporal_consistency=True,
            check_causal_consistency=False,
            properties=[]
        )
        
        # 设置参数
        custom_operator.set_parameters({
            "consistency_threshold": 0.6,
            "check_logical_consistency": False,
            "check_temporal_consistency": True,
            "check_causal_consistency": True,
            "properties": [
                {
                    "id": "custom1",
                    "type": "safety",
                    "expression": "G(x > 0)"
                }
            ]
        })
        
        # 获取参数
        params = custom_operator.get_parameters()
        self.assertEqual(params['consistency_threshold'], 0.6)
        self.assertEqual(params['check_logical_consistency'], False)
        self.assertEqual(params['check_temporal_consistency'], True)
        self.assertEqual(params['check_causal_consistency'], True)
        self.assertEqual(len(params['properties']), 1)
        
        # 应用算子
        result = custom_operator.apply(self.test_data)
        
        # 验证结果
        self.assertIn('pairwise_consistency', result)
        self.assertIn('global_consistency', result)
        self.assertIn('inconsistencies', result)
        self.assertIn('is_consistent', result)
        self.assertIn('consistency_score', result)


if __name__ == '__main__':
    unittest.main()
