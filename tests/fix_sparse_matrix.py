#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复sparse_matrix.rs文件中的括号问题
"""

import re

def fix_sparse_matrix():
    """修复sparse_matrix.rs文件中的括号问题"""
    with open("src/sparse_matrix.rs", "r") as f:
        content = f.read()
    
    # 修复Python::with_gil(|py| {和Ok(result)之间的括号
    pattern = r'Python::with_gil\(\|py\| \{([\s\S]*?)Ok\(result\)'
    replacement = r'Python::with_gil(|py| {\1Ok(result)'
    content = re.sub(pattern, replacement, content)
    
    # 修复Python::with_gil(|py| {和Ok(result_dict)之间的括号
    pattern = r'Python::with_gil\(\|py\| \{([\s\S]*?)Ok\(result_dict\)'
    replacement = r'Python::with_gil(|py| {\1Ok(result_dict)'
    content = re.sub(pattern, replacement, content)
    
    # 恢复原始的函数结构
    content = content.replace("Python::with_gil(|py| {", "let py = Python::acquire_gil();\n        let py = py.python();")
    content = content.replace("Ok(result)\n        })", "Ok(result)")
    content = content.replace("Ok(result_dict)\n        })", "Ok(result_dict)")
    
    with open("src/sparse_matrix.rs", "w") as f:
        f.write(content)
    
    print("已修复sparse_matrix.rs文件")

if __name__ == "__main__":
    fix_sparse_matrix()
