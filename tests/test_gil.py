import time
import threading
import numpy as np

# 创建一个计算密集型函数
def compute_intensive(size, id):
    print(f"Thread {id} starting...")
    start = time.time()
    
    # 创建大型矩阵并进行计算密集型操作
    a = np.random.random((size, size))
    b = np.random.random((size, size))
    
    # 执行多次矩阵乘法
    for _ in range(3):
        c = a @ b
        a = b @ c
    
    duration = time.time() - start
    print(f"Thread {id} finished in {duration:.2f} seconds")
    return duration

def run_threads(num_threads, size):
    threads = []
    start = time.time()
    
    # 创建并启动线程
    for i in range(num_threads):
        t = threading.Thread(target=compute_intensive, args=(size, i))
        threads.append(t)
        t.start()
    
    # 等待所有线程完成
    for t in threads:
        t.join()
    
    total_time = time.time() - start
    print(f"\nAll {num_threads} threads completed in {total_time:.2f} seconds")
    return total_time

if __name__ == "__main__":
    matrix_size = 1000
    print(f"Running with matrix size: {matrix_size}x{matrix_size}")
    
    # 测试不同数量的线程
    for num_threads in [1, 2, 4]:
        print(f"\n--- Testing with {num_threads} threads ---")
        run_threads(num_threads, matrix_size)
