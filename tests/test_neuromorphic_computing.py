"""
神经形态计算测试脚本

本脚本测试神经形态计算模块的基本功能。
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入神经形态计算模块
from src.neuromorphic_computing import (
    LIFNeuron,
    IzhikevichNeuron,
    StaticSynapse,
    STDPSynapse,
    FeedforwardNetwork,
    LiquidStateNetwork,
    BasicSimulator,
    EventDrivenSimulator,
    NENGO_AVAILABLE
)

if NENGO_AVAILABLE:
    from src.neuromorphic_computing import NengoSimulator

def test_lif_neuron():
    """测试LIF神经元"""
    print("\n测试LIF神经元...")
    
    # 创建LIF神经元
    neuron = LIFNeuron(
        tau_m=10.0,
        v_rest=-70.0,
        v_reset=-70.0,
        v_threshold=-50.0,
        r_membrane=1.0,
        refractory_period=2.0
    )
    
    # 创建输入
    duration = 100.0  # 毫秒
    dt = 0.1  # 毫秒
    steps = int(duration / dt)
    
    # 创建输入电流
    current = np.zeros(steps)
    current[int(10 / dt):int(20 / dt)] = 25.0  # 10-20毫秒内施加25pA电流
    current[int(40 / dt):int(50 / dt)] = 50.0  # 40-50毫秒内施加50pA电流
    current[int(70 / dt):int(80 / dt)] = 75.0  # 70-80毫秒内施加75pA电流
    
    # 存储结果
    times = np.arange(0, duration, dt)
    membrane_potentials = []
    spikes = []
    
    # 运行模拟
    for i in range(steps):
        # 更新神经元
        output = neuron.update([current[i]])
        
        # 存储结果
        membrane_potentials.append(neuron.state["v"])
        spikes.append(output)
    
    # 绘制结果
    plt.figure(figsize=(10, 8))
    
    # 绘制膜电位
    plt.subplot(3, 1, 1)
    plt.plot(times, membrane_potentials)
    plt.axhline(y=neuron.v_threshold, color='r', linestyle='--', label='Threshold')
    plt.axhline(y=neuron.v_rest, color='g', linestyle='--', label='Rest')
    plt.xlabel('Time (ms)')
    plt.ylabel('Membrane Potential (mV)')
    plt.title('LIF Neuron Membrane Potential')
    plt.legend()
    
    # 绘制脉冲
    plt.subplot(3, 1, 2)
    plt.plot(times, spikes)
    plt.xlabel('Time (ms)')
    plt.ylabel('Spike')
    plt.title('LIF Neuron Spikes')
    
    # 绘制输入电流
    plt.subplot(3, 1, 3)
    plt.plot(times, current)
    plt.xlabel('Time (ms)')
    plt.ylabel('Input Current (pA)')
    plt.title('Input Current')
    
    plt.tight_layout()
    plt.savefig('lif_neuron_test.png')
    plt.close()
    
    print("LIF神经元测试完成")

def test_izhikevich_neuron():
    """测试Izhikevich神经元"""
    print("\n测试Izhikevich神经元...")
    
    # 创建不同类型的Izhikevich神经元
    neuron_types = [
        ("Regular Spiking", IzhikevichNeuron.create_regular_spiking()),
        ("Chattering", IzhikevichNeuron.create_chattering()),
        ("Fast Spiking", IzhikevichNeuron.create_fast_spiking()),
        ("Low Threshold Spiking", IzhikevichNeuron.create_low_threshold_spiking()),
        ("Intrinsically Bursting", IzhikevichNeuron.create_intrinsically_bursting()),
        ("Resonator", IzhikevichNeuron.create_resonator())
    ]
    
    # 创建输入
    duration = 100.0  # 毫秒
    dt = 0.1  # 毫秒
    steps = int(duration / dt)
    
    # 创建输入电流
    current = np.zeros(steps)
    current[int(10 / dt):] = 10.0  # 10毫秒后施加10pA电流
    
    # 创建图形
    plt.figure(figsize=(15, 10))
    
    # 对于每种神经元类型
    for i, (name, neuron) in enumerate(neuron_types):
        # 重置神经元
        neuron.reset()
        
        # 存储结果
        times = np.arange(0, duration, dt)
        membrane_potentials = []
        spikes = []
        
        # 运行模拟
        for j in range(steps):
            # 更新神经元
            output = neuron.update([current[j]])
            
            # 存储结果
            membrane_potentials.append(neuron.state["v"])
            spikes.append(output)
        
        # 绘制膜电位
        plt.subplot(len(neuron_types), 2, 2*i+1)
        plt.plot(times, membrane_potentials)
        plt.xlabel('Time (ms)')
        plt.ylabel('Membrane Potential (mV)')
        plt.title(f'{name} Membrane Potential')
        
        # 绘制脉冲
        plt.subplot(len(neuron_types), 2, 2*i+2)
        plt.plot(times, spikes)
        plt.xlabel('Time (ms)')
        plt.ylabel('Spike')
        plt.title(f'{name} Spikes')
    
    plt.tight_layout()
    plt.savefig('izhikevich_neuron_test.png')
    plt.close()
    
    print("Izhikevich神经元测试完成")

def test_stdp_synapse():
    """测试STDP突触"""
    print("\n测试STDP突触...")
    
    # 创建前后神经元
    pre_neuron = LIFNeuron(name="PreNeuron")
    post_neuron = LIFNeuron(name="PostNeuron")
    
    # 创建STDP突触
    synapse = STDPSynapse(
        pre_neuron=pre_neuron,
        post_neuron=post_neuron,
        weight=0.5,
        a_plus=0.05,
        a_minus=0.05,
        tau_plus=20.0,
        tau_minus=20.0,
        w_min=0.0,
        w_max=1.0
    )
    
    # 创建输入
    duration = 1000.0  # 毫秒
    dt = 0.1  # 毫秒
    steps = int(duration / dt)
    
    # 创建前神经元输入
    pre_input = np.zeros(steps)
    pre_input[np.arange(int(100 / dt), steps, int(100 / dt))] = 100.0  # 每100毫秒施加一次脉冲
    
    # 创建后神经元输入
    post_input = np.zeros(steps)
    post_input[np.arange(int(100 / dt) + int(10 / dt), steps, int(100 / dt))] = 100.0  # 比前神经元晚10毫秒
    
    # 存储结果
    times = np.arange(0, duration, dt)
    weights = []
    pre_spikes = []
    post_spikes = []
    
    # 运行模拟
    for i in range(steps):
        # 更新前神经元
        pre_output = pre_neuron.update([pre_input[i]])
        
        # 传递信号
        post_input_from_synapse = synapse.transmit(pre_output)
        
        # 更新后神经元
        post_output = post_neuron.update([post_input[i] + post_input_from_synapse])
        
        # 更新突触权重
        synapse.update_weight(pre_spike=pre_output > 0, post_spike=post_output > 0)
        
        # 存储结果
        weights.append(synapse.state["weight"])
        pre_spikes.append(pre_output)
        post_spikes.append(post_output)
    
    # 绘制结果
    plt.figure(figsize=(10, 8))
    
    # 绘制权重
    plt.subplot(3, 1, 1)
    plt.plot(times, weights)
    plt.xlabel('Time (ms)')
    plt.ylabel('Weight')
    plt.title('STDP Synapse Weight')
    
    # 绘制前神经元脉冲
    plt.subplot(3, 1, 2)
    plt.plot(times, pre_spikes)
    plt.xlabel('Time (ms)')
    plt.ylabel('Spike')
    plt.title('Pre-synaptic Neuron Spikes')
    
    # 绘制后神经元脉冲
    plt.subplot(3, 1, 3)
    plt.plot(times, post_spikes)
    plt.xlabel('Time (ms)')
    plt.ylabel('Spike')
    plt.title('Post-synaptic Neuron Spikes')
    
    plt.tight_layout()
    plt.savefig('stdp_synapse_test.png')
    plt.close()
    
    print("STDP突触测试完成")

def test_feedforward_network():
    """测试前馈神经网络"""
    print("\n测试前馈神经网络...")
    
    # 创建前馈神经网络
    network = FeedforwardNetwork(
        layers=[2, 3, 1],
        neuron_type="lif",
        synapse_type="static",
        neuron_params={
            "tau_m": 10.0,
            "v_rest": -70.0,
            "v_reset": -70.0,
            "v_threshold": -50.0,
            "r_membrane": 1.0,
            "refractory_period": 2.0
        },
        synapse_params={
            "weight": 1.0
        }
    )
    
    # 创建模拟器
    simulator = BasicSimulator(network)
    
    # 创建输入
    duration = 100.0  # 毫秒
    dt = 0.1  # 毫秒
    steps = int(duration / dt)
    
    # 创建输入
    inputs = []
    for i in range(steps):
        if i % int(20 / dt) < int(10 / dt):
            inputs.append([20.0, 0.0])
        else:
            inputs.append([0.0, 20.0])
    
    # 运行模拟
    outputs = simulator.run(inputs, duration)
    
    # 绘制结果
    plt.figure(figsize=(10, 8))
    
    # 绘制输入
    plt.subplot(2, 1, 1)
    plt.plot(simulator.history["time"], [input[0] for input in simulator.history["inputs"]], label='Input 0')
    plt.plot(simulator.history["time"], [input[1] for input in simulator.history["inputs"]], label='Input 1')
    plt.xlabel('Time (ms)')
    plt.ylabel('Input')
    plt.title('Network Inputs')
    plt.legend()
    
    # 绘制输出
    plt.subplot(2, 1, 2)
    plt.plot(simulator.history["time"], [output[0] for output in simulator.history["outputs"]])
    plt.xlabel('Time (ms)')
    plt.ylabel('Output')
    plt.title('Network Output')
    
    plt.tight_layout()
    plt.savefig('feedforward_network_test.png')
    plt.close()
    
    # 绘制脉冲序列
    simulator.plot_spike_trains()
    plt.savefig('feedforward_network_spike_trains.png')
    plt.close()
    
    print("前馈神经网络测试完成")

def test_liquid_state_machine():
    """测试液态状态机"""
    print("\n测试液态状态机...")
    
    # 创建液态状态机
    network = LiquidStateNetwork(
        input_size=2,
        reservoir_size=20,
        output_size=1,
        neuron_type="lif",
        synapse_type="static",
        connectivity=0.3,
        spectral_radius=0.9,
        input_scaling=1.0,
        neuron_params={
            "tau_m": 10.0,
            "v_rest": -70.0,
            "v_reset": -70.0,
            "v_threshold": -50.0,
            "r_membrane": 1.0,
            "refractory_period": 2.0
        },
        synapse_params={
            "weight": 1.0
        }
    )
    
    # 创建模拟器
    simulator = BasicSimulator(network)
    
    # 创建输入
    duration = 500.0  # 毫秒
    dt = 0.1  # 毫秒
    steps = int(duration / dt)
    
    # 创建输入
    inputs = []
    for i in range(steps):
        t = i * dt
        input1 = 10.0 * np.sin(2 * np.pi * t / 100.0)
        input2 = 10.0 * np.cos(2 * np.pi * t / 100.0)
        inputs.append([input1, input2])
    
    # 创建目标
    targets = []
    for i in range(steps):
        t = i * dt
        target = [np.sin(2 * np.pi * t / 100.0) * np.cos(2 * np.pi * t / 100.0)]
        targets.append(target)
    
    # 训练网络
    print("训练液态状态机...")
    network.train(inputs, targets, learning_rate=0.01, epochs=1)
    
    # 运行模拟
    outputs = simulator.run(inputs, duration)
    
    # 绘制结果
    plt.figure(figsize=(10, 8))
    
    # 绘制输入
    plt.subplot(3, 1, 1)
    plt.plot(simulator.history["time"], [input[0] for input in simulator.history["inputs"]], label='Input 0')
    plt.plot(simulator.history["time"], [input[1] for input in simulator.history["inputs"]], label='Input 1')
    plt.xlabel('Time (ms)')
    plt.ylabel('Input')
    plt.title('Network Inputs')
    plt.legend()
    
    # 绘制输出
    plt.subplot(3, 1, 2)
    plt.plot(simulator.history["time"], [output[0] for output in simulator.history["outputs"]], label='Output')
    plt.plot(simulator.history["time"], [target[0] for target in targets], label='Target')
    plt.xlabel('Time (ms)')
    plt.ylabel('Output')
    plt.title('Network Output')
    plt.legend()
    
    # 绘制储备池状态
    plt.subplot(3, 1, 3)
    reservoir_states = []
    for i in range(len(simulator.history["time"])):
        state = np.mean([neuron_state.get("spike", False) for neuron_state in simulator.history["neuron_states"][i][2:22]])
        reservoir_states.append(state)
    
    plt.plot(simulator.history["time"], reservoir_states)
    plt.xlabel('Time (ms)')
    plt.ylabel('Reservoir Activity')
    plt.title('Reservoir Activity')
    
    plt.tight_layout()
    plt.savefig('liquid_state_machine_test.png')
    plt.close()
    
    # 绘制脉冲序列
    simulator.plot_spike_trains()
    plt.savefig('liquid_state_machine_spike_trains.png')
    plt.close()
    
    print("液态状态机测试完成")

def test_event_driven_simulator():
    """测试事件驱动模拟器"""
    print("\n测试事件驱动模拟器...")
    
    # 创建前馈神经网络
    network = FeedforwardNetwork(
        layers=[2, 3, 1],
        neuron_type="lif",
        synapse_type="static",
        neuron_params={
            "tau_m": 10.0,
            "v_rest": -70.0,
            "v_reset": -70.0,
            "v_threshold": -50.0,
            "r_membrane": 1.0,
            "refractory_period": 2.0
        },
        synapse_params={
            "weight": 1.0,
            "delay": 1.0
        }
    )
    
    # 创建事件驱动模拟器
    simulator = EventDrivenSimulator(network)
    
    # 创建输入
    duration = 100.0  # 毫秒
    dt = 0.1  # 毫秒
    steps = int(duration / dt)
    
    # 创建输入
    inputs = []
    for i in range(steps):
        if i % int(20 / dt) < int(10 / dt):
            inputs.append([20.0, 0.0])
        else:
            inputs.append([0.0, 20.0])
    
    # 运行模拟
    outputs = simulator.run(inputs, duration)
    
    # 绘制结果
    plt.figure(figsize=(10, 8))
    
    # 绘制输入
    plt.subplot(2, 1, 1)
    plt.plot(simulator.history["time"], [input[0] for input in simulator.history["inputs"]], label='Input 0')
    plt.plot(simulator.history["time"], [input[1] for input in simulator.history["inputs"]], label='Input 1')
    plt.xlabel('Time (ms)')
    plt.ylabel('Input')
    plt.title('Network Inputs')
    plt.legend()
    
    # 绘制输出
    plt.subplot(2, 1, 2)
    plt.plot(simulator.history["time"], [output[0] for output in simulator.history["outputs"]])
    plt.xlabel('Time (ms)')
    plt.ylabel('Output')
    plt.title('Network Output')
    
    plt.tight_layout()
    plt.savefig('event_driven_simulator_test.png')
    plt.close()
    
    # 绘制事件时间线
    simulator.plot_event_timeline()
    plt.savefig('event_driven_simulator_event_timeline.png')
    plt.close()
    
    # 获取事件计数
    event_counts = simulator.get_event_counts()
    print(f"事件计数: {event_counts}")
    
    print("事件驱动模拟器测试完成")

def test_nengo_simulator():
    """测试Nengo模拟器接口"""
    if not NENGO_AVAILABLE:
        print("\nNengo未安装，跳过测试")
        return
    
    print("\n测试Nengo模拟器接口...")
    
    # 创建前馈神经网络
    network = FeedforwardNetwork(
        layers=[2, 3, 1],
        neuron_type="lif",
        synapse_type="static",
        neuron_params={
            "tau_m": 10.0,
            "v_rest": -70.0,
            "v_reset": -70.0,
            "v_threshold": -50.0,
            "r_membrane": 1.0,
            "refractory_period": 2.0
        },
        synapse_params={
            "weight": 1.0
        }
    )
    
    # 创建Nengo模拟器
    simulator = NengoSimulator(network)
    
    # 创建输入
    duration = 100.0  # 毫秒
    dt = 0.1  # 毫秒
    steps = int(duration / dt)
    
    # 创建输入
    inputs = []
    for i in range(steps):
        if i % int(20 / dt) < int(10 / dt):
            inputs.append([20.0, 0.0])
        else:
            inputs.append([0.0, 20.0])
    
    # 运行模拟
    outputs = simulator.run(inputs, duration)
    
    # 绘制结果
    plt.figure(figsize=(10, 8))
    
    # 绘制输入
    plt.subplot(2, 1, 1)
    plt.plot(simulator.history["time"], [input[0] for input in simulator.history["inputs"]], label='Input 0')
    plt.plot(simulator.history["time"], [input[1] for input in simulator.history["inputs"]], label='Input 1')
    plt.xlabel('Time (ms)')
    plt.ylabel('Input')
    plt.title('Network Inputs')
    plt.legend()
    
    # 绘制输出
    plt.subplot(2, 1, 2)
    plt.plot(simulator.history["time"], [output[0] for output in simulator.history["outputs"]])
    plt.xlabel('Time (ms)')
    plt.ylabel('Output')
    plt.title('Network Output')
    
    plt.tight_layout()
    plt.savefig('nengo_simulator_test.png')
    plt.close()
    
    # 绘制Nengo输出
    simulator.plot_nengo_output()
    plt.savefig('nengo_simulator_output.png')
    plt.close()
    
    print("Nengo模拟器接口测试完成")

def main():
    """主函数"""
    print("开始测试神经形态计算模块...")
    
    # 测试LIF神经元
    test_lif_neuron()
    
    # 测试Izhikevich神经元
    test_izhikevich_neuron()
    
    # 测试STDP突触
    test_stdp_synapse()
    
    # 测试前馈神经网络
    test_feedforward_network()
    
    # 测试液态状态机
    test_liquid_state_machine()
    
    # 测试事件驱动模拟器
    test_event_driven_simulator()
    
    # 测试Nengo模拟器接口
    test_nengo_simulator()
    
    print("\n所有测试完成")

if __name__ == "__main__":
    main()
