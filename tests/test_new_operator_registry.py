"""
新算子注册表单元测试

本模块提供了新算子注册表的单元测试。
"""

import os
import sys
import unittest
import logging
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Union

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入算子注册表模块
from src.rust_bindings import (
    OperatorCategory,
    OperatorRegistry,
    OperatorMetadata,
    DependencyManager,
    CompatibilityLevel,
    CompatibilityChecker,
    OperatorFactory,
    get_global_registry,
    register_operator,
    get_operator,
    list_operators,
    get_operator_metadata,
    check_compatibility,
)

# 定义一些测试算子
def add(a, b):
    """加法算子"""
    return a + b

def multiply(a, b):
    """乘法算子"""
    return a * b

def matrix_multiply(a, b):
    """矩阵乘法算子"""
    return np.matmul(a, b)

class TestOperatorRegistry(unittest.TestCase):
    """算子注册表单元测试"""
    
    def setUp(self):
        """测试前准备"""
        self.registry = OperatorRegistry()
        
        # 注册算子
        self.registry.register(
            OperatorCategory.UTILITY,
            "add",
            add,
            "1.0.0",
            "加法算子",
            ["math", "basic"],
            [],
        )
        
        self.registry.register(
            OperatorCategory.UTILITY,
            "multiply",
            multiply,
            "1.0.0",
            "乘法算子",
            ["math", "basic"],
            [("utility.add", ">=1.0.0")],
        )
    
    def test_register_and_get(self):
        """测试注册和获取算子"""
        # 获取算子
        add_op = self.registry.get(OperatorCategory.UTILITY, "add")
        self.assertIsNotNone(add_op)
        self.assertEqual(add_op(1, 2), 3)
        
        # 获取元数据
        metadata = self.registry.get_metadata(OperatorCategory.UTILITY, "add")
        self.assertIsNotNone(metadata)
        self.assertEqual(metadata.name, "add")
        self.assertEqual(metadata.version, "1.0.0")
        self.assertEqual(metadata.description, "加法算子")
        self.assertEqual(metadata.tags, ["math", "basic"])
        self.assertEqual(metadata.dependencies, [])
    
    def test_list_operators(self):
        """测试列出算子"""
        # 列出所有算子
        operators = self.registry.list_all()
        self.assertEqual(len(operators), 2)
        self.assertIn(("utility", "add"), operators)
        self.assertIn(("utility", "multiply"), operators)
        
        # 列出特定类别的算子
        utility_operators = self.registry.list_by_category(OperatorCategory.UTILITY)
        self.assertEqual(len(utility_operators), 2)
        self.assertIn(("utility", "add"), utility_operators)
        self.assertIn(("utility", "multiply"), utility_operators)
    
    def test_exists(self):
        """测试算子是否存在"""
        self.assertTrue(self.registry.exists(OperatorCategory.UTILITY, "add"))
        self.assertTrue(self.registry.exists(OperatorCategory.UTILITY, "multiply"))
        self.assertFalse(self.registry.exists(OperatorCategory.UTILITY, "divide"))
    
    def test_get_operators_with_tag(self):
        """测试获取带有标签的算子"""
        math_operators = self.registry.get_operators_with_tag("math")
        self.assertEqual(len(math_operators), 2)
        self.assertIn(("utility", "add"), math_operators)
        self.assertIn(("utility", "multiply"), math_operators)
        
        basic_operators = self.registry.get_operators_with_tag("basic")
        self.assertEqual(len(basic_operators), 2)
        self.assertIn(("utility", "add"), basic_operators)
        self.assertIn(("utility", "multiply"), basic_operators)
    
    def test_get_operators_with_version(self):
        """测试获取带有版本的算子"""
        v1_operators = self.registry.get_operators_with_version(">=1.0.0")
        self.assertEqual(len(v1_operators), 2)
        self.assertIn(("utility", "add"), v1_operators)
        self.assertIn(("utility", "multiply"), v1_operators)
        
        v2_operators = self.registry.get_operators_with_version(">=2.0.0")
        self.assertEqual(len(v2_operators), 0)
    
    def test_get_operators_with_dependency(self):
        """测试获取带有依赖的算子"""
        dependent_operators = self.registry.get_operators_with_dependency("utility.add")
        self.assertEqual(len(dependent_operators), 1)
        self.assertIn(("utility", "multiply"), dependent_operators)
        
        dependent_operators = self.registry.get_operators_with_dependency("utility.add", ">=1.0.0")
        self.assertEqual(len(dependent_operators), 1)
        self.assertIn(("utility", "multiply"), dependent_operators)
        
        dependent_operators = self.registry.get_operators_with_dependency("utility.add", ">=2.0.0")
        self.assertEqual(len(dependent_operators), 0)
    
    def test_remove(self):
        """测试移除算子"""
        self.assertTrue(self.registry.remove(OperatorCategory.UTILITY, "add"))
        self.assertFalse(self.registry.exists(OperatorCategory.UTILITY, "add"))
        self.assertIsNone(self.registry.get(OperatorCategory.UTILITY, "add"))
        self.assertIsNone(self.registry.get_metadata(OperatorCategory.UTILITY, "add"))
    
    def test_clear(self):
        """测试清除所有算子"""
        self.registry.clear()
        self.assertEqual(len(self.registry.list_all()), 0)
        self.assertFalse(self.registry.exists(OperatorCategory.UTILITY, "add"))
        self.assertFalse(self.registry.exists(OperatorCategory.UTILITY, "multiply"))

class TestDependencyManager(unittest.TestCase):
    """依赖管理器单元测试"""
    
    def setUp(self):
        """测试前准备"""
        self.registry = OperatorRegistry()
        
        # 注册算子
        self.registry.register(
            OperatorCategory.UTILITY,
            "add",
            add,
            "1.0.0",
            "加法算子",
            ["math", "basic"],
            [],
        )
        
        self.registry.register(
            OperatorCategory.UTILITY,
            "multiply",
            multiply,
            "1.0.0",
            "乘法算子",
            ["math", "basic"],
            [("utility.add", ">=1.0.0")],
        )
        
        self.registry.register(
            OperatorCategory.NUMPY,
            "matrix_multiply",
            matrix_multiply,
            "1.0.0",
            "矩阵乘法算子",
            ["math", "matrix"],
            [("utility.multiply", ">=1.0.0")],
        )
        
        self.dependency_manager = DependencyManager(self.registry)
    
    def test_check_dependencies(self):
        """测试检查依赖"""
        satisfied, missing = self.dependency_manager.check_dependencies(OperatorCategory.UTILITY, "add")
        self.assertTrue(satisfied)
        self.assertEqual(len(missing), 0)
        
        satisfied, missing = self.dependency_manager.check_dependencies(OperatorCategory.UTILITY, "multiply")
        self.assertTrue(satisfied)
        self.assertEqual(len(missing), 0)
        
        satisfied, missing = self.dependency_manager.check_dependencies(OperatorCategory.NUMPY, "matrix_multiply")
        self.assertTrue(satisfied)
        self.assertEqual(len(missing), 0)
    
    def test_get_dependents(self):
        """测试获取依赖于算子的所有算子"""
        dependents = self.dependency_manager.get_dependents(OperatorCategory.UTILITY, "add")
        self.assertEqual(len(dependents), 1)
        self.assertIn(("utility", "multiply"), dependents)
        
        dependents = self.dependency_manager.get_dependents(OperatorCategory.UTILITY, "multiply")
        self.assertEqual(len(dependents), 1)
        self.assertIn(("numpy", "matrix_multiply"), dependents)
    
    def test_build_dependency_graph(self):
        """测试构建依赖图"""
        graph = self.dependency_manager.build_dependency_graph()
        self.assertEqual(len(graph), 3)
        self.assertEqual(graph[("utility", "add")], [])
        self.assertEqual(graph[("utility", "multiply")], [("utility", "add")])
        self.assertEqual(graph[("numpy", "matrix_multiply")], [("utility", "multiply")])
    
    def test_check_circular_dependencies(self):
        """测试检查循环依赖"""
        cycles = self.dependency_manager.check_circular_dependencies()
        self.assertEqual(len(cycles), 0)
    
    def test_resolve_dependencies(self):
        """测试解析依赖"""
        dependencies = self.dependency_manager.resolve_dependencies(OperatorCategory.NUMPY, "matrix_multiply")
        self.assertEqual(len(dependencies), 3)
        self.assertEqual(dependencies[0], ("utility", "add"))
        self.assertEqual(dependencies[1], ("utility", "multiply"))
        self.assertEqual(dependencies[2], ("numpy", "matrix_multiply"))

class TestCompatibilityChecker(unittest.TestCase):
    """兼容性检查器单元测试"""
    
    def setUp(self):
        """测试前准备"""
        self.registry = OperatorRegistry()
        
        # 注册算子
        self.registry.register(
            OperatorCategory.UTILITY,
            "add",
            add,
            "1.0.0",
            "加法算子",
            ["math", "basic"],
            [],
        )
        
        self.checker = CompatibilityChecker(self.registry)
        
        # 添加兼容性规则
        self.checker.add_rule(OperatorCategory.UTILITY, "add", ">=0.9.0", CompatibilityLevel.PARTIAL)
        self.checker.add_rule(OperatorCategory.UTILITY, "add", "<0.9.0", CompatibilityLevel.NONE)
    
    def test_check_compatibility(self):
        """测试检查兼容性"""
        level = self.checker.check_compatibility(OperatorCategory.UTILITY, "add", ">=1.0.0")
        self.assertEqual(level, CompatibilityLevel.FULL)
        
        level = self.checker.check_compatibility(OperatorCategory.UTILITY, "add", ">=0.9.0")
        self.assertEqual(level, CompatibilityLevel.FULL)
        
        level = self.checker.check_compatibility(OperatorCategory.UTILITY, "add", "<0.9.0")
        self.assertEqual(level, CompatibilityLevel.PARTIAL)
    
    def test_get_rules(self):
        """测试获取所有兼容性规则"""
        rules = self.checker.get_rules()
        self.assertEqual(len(rules), 2)
        self.assertIn(("utility", "add", ">=0.9.0", CompatibilityLevel.PARTIAL), rules)
        self.assertIn(("utility", "add", "<0.9.0", CompatibilityLevel.NONE), rules)
    
    def test_get_rules_for_operator(self):
        """测试获取算子的兼容性规则"""
        rules = self.checker.get_rules_for_operator(OperatorCategory.UTILITY, "add")
        self.assertEqual(len(rules), 2)
        self.assertIn((">=0.9.0", CompatibilityLevel.PARTIAL), rules)
        self.assertIn(("<0.9.0", CompatibilityLevel.NONE), rules)
    
    def test_remove_rule(self):
        """测试移除兼容性规则"""
        self.assertTrue(self.checker.remove_rule(OperatorCategory.UTILITY, "add", ">=0.9.0"))
        rules = self.checker.get_rules()
        self.assertEqual(len(rules), 1)
        self.assertIn(("utility", "add", "<0.9.0", CompatibilityLevel.NONE), rules)
    
    def test_clear_rules(self):
        """测试清除所有兼容性规则"""
        self.checker.clear_rules()
        rules = self.checker.get_rules()
        self.assertEqual(len(rules), 0)

if __name__ == "__main__":
    unittest.main()
