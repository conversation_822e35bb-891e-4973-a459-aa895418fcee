"""
分布式计算测试脚本

本脚本测试分布式计算模块的基本功能。
"""

import sys
import os
import time
import math
import random
import numpy as np
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入分布式计算模块
from src.distributed_computing import (
    TaskStatus, SimpleTask, MapTask,
    LocalWorker, ProcessWorker,
    SimpleScheduler,
    LocalCluster,
    DASK_AVAILABLE
)

if DASK_AVAILABLE:
    from src.distributed_computing import DaskCluster

def test_simple_task():
    """测试简单任务"""
    print("\n测试简单任务...")
    
    # 定义一个简单的函数
    def add(a, b, **kwargs):
        print(f"计算 {a} + {b}")
        return a + b
    
    # 创建简单任务
    task = SimpleTask(
        func=add,
        args=(1, 2),
        name="AddTask"
    )
    
    # 执行任务
    result = task.run()
    
    # 打印结果
    print(f"任务状态: {task.status.value}")
    print(f"任务结果: {result}")
    print(f"执行时间: {task.get_execution_time():.6f} 秒")
    
    print("简单任务测试完成")

def test_map_task():
    """测试Map任务"""
    print("\n测试Map任务...")
    
    # 定义一个简单的函数
    def square(x, **kwargs):
        print(f"计算 {x}^2")
        return x ** 2
    
    # 创建Map任务
    task = MapTask(
        func=square,
        items=range(10),
        name="SquareTask",
        chunksize=2
    )
    
    # 执行任务
    result = task.run()
    
    # 打印结果
    print(f"任务状态: {task.status.value}")
    print(f"任务结果: {result}")
    print(f"执行时间: {task.get_execution_time():.6f} 秒")
    
    print("Map任务测试完成")

def test_local_worker():
    """测试本地工作节点"""
    print("\n测试本地工作节点...")
    
    # 创建本地工作节点
    worker = LocalWorker(name="TestLocalWorker")
    
    # 启动工作节点
    worker.start()
    
    # 定义一个简单的函数
    def add(a, b, **kwargs):
        print(f"计算 {a} + {b}")
        time.sleep(0.1)  # 模拟计算时间
        return a + b
    
    # 创建简单任务
    task1 = SimpleTask(
        func=add,
        args=(1, 2),
        name="AddTask1"
    )
    
    task2 = SimpleTask(
        func=add,
        args=(3, 4),
        name="AddTask2"
    )
    
    # 添加任务
    worker.add_task(task1)
    worker.add_task(task2)
    
    # 等待任务完成
    while worker.task_queue or worker.running_tasks:
        time.sleep(0.1)
    
    # 打印结果
    print(f"任务1状态: {task1.status.value}")
    print(f"任务1结果: {task1.result}")
    print(f"任务2状态: {task2.status.value}")
    print(f"任务2结果: {task2.result}")
    
    # 停止工作节点
    worker.stop()
    
    print("本地工作节点测试完成")

def test_process_worker():
    """测试进程工作节点"""
    print("\n测试进程工作节点...")
    
    # 创建进程工作节点
    worker = ProcessWorker(name="TestProcessWorker")
    
    # 启动工作节点
    worker.start()
    
    # 定义一个简单的函数
    def add(a, b, **kwargs):
        print(f"计算 {a} + {b}")
        time.sleep(0.1)  # 模拟计算时间
        return a + b
    
    # 创建简单任务
    task = SimpleTask(
        func=add,
        args=(1, 2),
        name="AddTask"
    )
    
    # 执行任务
    result = worker.execute_task(task)
    
    # 打印结果
    print(f"任务状态: {task.status.value}")
    print(f"任务结果: {result}")
    
    # 停止工作节点
    worker.stop()
    
    print("进程工作节点测试完成")

def test_simple_scheduler():
    """测试简单调度器"""
    print("\n测试简单调度器...")
    
    # 创建简单调度器
    scheduler = SimpleScheduler(name="TestSimpleScheduler")
    
    # 创建本地工作节点
    worker1 = LocalWorker(name="TestLocalWorker1")
    worker2 = LocalWorker(name="TestLocalWorker2")
    
    # 添加工作节点
    scheduler.add_worker(worker1)
    scheduler.add_worker(worker2)
    
    # 启动调度器
    scheduler.start()
    
    # 定义一个简单的函数
    def add(a, b, **kwargs):
        print(f"计算 {a} + {b}")
        time.sleep(0.5)  # 模拟计算时间
        return a + b
    
    # 创建简单任务
    task1 = SimpleTask(
        func=add,
        args=(1, 2),
        name="AddTask1"
    )
    
    task2 = SimpleTask(
        func=add,
        args=(3, 4),
        name="AddTask2"
    )
    
    # 提交任务
    task1_id = scheduler.submit(task1)
    task2_id = scheduler.submit(task2)
    
    # 获取任务结果
    result1 = scheduler.get_result(task1_id)
    result2 = scheduler.get_result(task2_id)
    
    # 打印结果
    print(f"任务1结果: {result1}")
    print(f"任务2结果: {result2}")
    
    # 停止调度器
    scheduler.stop()
    
    print("简单调度器测试完成")

def test_local_cluster():
    """测试本地集群"""
    print("\n测试本地集群...")
    
    # 创建本地集群
    cluster = LocalCluster(
        num_workers=4,
        name="TestLocalCluster"
    )
    
    # 启动集群
    cluster.start()
    
    # 定义一个简单的函数
    def compute_pi(n, **kwargs):
        """使用蒙特卡洛方法计算π"""
        print(f"使用 {n} 个点计算π")
        inside_circle = 0
        for _ in range(n):
            x = random.random()
            y = random.random()
            if x**2 + y**2 <= 1:
                inside_circle += 1
        return 4 * inside_circle / n
    
    # 创建任务
    tasks = []
    for i in range(10):
        task = SimpleTask(
            func=compute_pi,
            args=(1000000,),
            name=f"ComputePiTask-{i}"
        )
        tasks.append(task)
    
    # 提交任务
    task_ids = []
    for task in tasks:
        task_id = cluster.submit(task)
        task_ids.append(task_id)
    
    # 获取任务结果
    results = []
    for task_id in task_ids:
        result = cluster.get_result(task_id)
        results.append(result)
    
    # 打印结果
    print(f"π的估计值: {np.mean(results):.6f}")
    print(f"标准差: {np.std(results):.6f}")
    
    # 绘制结果
    plt.figure(figsize=(10, 6))
    plt.hist(results, bins=10)
    plt.axvline(x=math.pi, color='r', linestyle='--', label=f'π = {math.pi:.6f}')
    plt.axvline(x=np.mean(results), color='g', linestyle='-', label=f'平均值 = {np.mean(results):.6f}')
    plt.xlabel('π的估计值')
    plt.ylabel('频率')
    plt.title('使用蒙特卡洛方法计算π')
    plt.legend()
    plt.savefig('pi_estimation.png')
    plt.close()
    
    # 停止集群
    cluster.stop()
    
    print("本地集群测试完成")

def test_dask_cluster():
    """测试Dask集群"""
    if not DASK_AVAILABLE:
        print("\nDask未安装，跳过测试")
        return
    
    print("\n测试Dask集群...")
    
    # 创建Dask集群
    cluster = DaskCluster(
        n_workers=4,
        threads_per_worker=1,
        name="TestDaskCluster"
    )
    
    # 启动集群
    cluster.start()
    
    # 打印仪表盘链接
    print(f"Dask仪表盘: {cluster.get_dashboard_link()}")
    
    # 定义一个简单的函数
    def compute_pi(n, **kwargs):
        """使用蒙特卡洛方法计算π"""
        print(f"使用 {n} 个点计算π")
        inside_circle = 0
        for _ in range(n):
            x = random.random()
            y = random.random()
            if x**2 + y**2 <= 1:
                inside_circle += 1
        return 4 * inside_circle / n
    
    # 创建任务
    tasks = []
    for i in range(10):
        task = SimpleTask(
            func=compute_pi,
            args=(1000000,),
            name=f"ComputePiTask-{i}"
        )
        tasks.append(task)
    
    # 提交任务
    task_ids = []
    for task in tasks:
        task_id = cluster.submit(task)
        task_ids.append(task_id)
    
    # 获取任务结果
    results = []
    for task_id in task_ids:
        result = cluster.get_result(task_id)
        results.append(result)
    
    # 打印结果
    print(f"π的估计值: {np.mean(results):.6f}")
    print(f"标准差: {np.std(results):.6f}")
    
    # 绘制结果
    plt.figure(figsize=(10, 6))
    plt.hist(results, bins=10)
    plt.axvline(x=math.pi, color='r', linestyle='--', label=f'π = {math.pi:.6f}')
    plt.axvline(x=np.mean(results), color='g', linestyle='-', label=f'平均值 = {np.mean(results):.6f}')
    plt.xlabel('π的估计值')
    plt.ylabel('频率')
    plt.title('使用Dask集群计算π')
    plt.legend()
    plt.savefig('dask_pi_estimation.png')
    plt.close()
    
    # 停止集群
    cluster.stop()
    
    print("Dask集群测试完成")

def test_parallel_performance():
    """测试并行性能"""
    print("\n测试并行性能...")
    
    # 定义一个计算密集型函数
    def calculate_primes(n, **kwargs):
        """计算小于n的素数"""
        primes = []
        for i in range(2, n):
            is_prime = True
            for j in range(2, int(i**0.5) + 1):
                if i % j == 0:
                    is_prime = False
                    break
            if is_prime:
                primes.append(i)
        return len(primes)
    
    # 测试不同工作节点数量的性能
    worker_counts = [1, 2, 4, 8]
    execution_times = []
    
    for num_workers in worker_counts:
        print(f"\n测试 {num_workers} 个工作节点...")
        
        # 创建本地集群
        cluster = LocalCluster(
            num_workers=num_workers,
            name=f"TestCluster-{num_workers}"
        )
        
        # 启动集群
        cluster.start()
        
        # 创建任务
        tasks = []
        for i in range(16):
            task = SimpleTask(
                func=calculate_primes,
                args=(100000,),
                name=f"PrimeTask-{i}"
            )
            tasks.append(task)
        
        # 提交任务并计时
        start_time = time.time()
        
        # 提交任务
        task_ids = []
        for task in tasks:
            task_id = cluster.submit(task)
            task_ids.append(task_id)
        
        # 获取任务结果
        results = []
        for task_id in task_ids:
            result = cluster.get_result(task_id)
            results.append(result)
        
        # 计算执行时间
        execution_time = time.time() - start_time
        execution_times.append(execution_time)
        
        # 打印结果
        print(f"执行时间: {execution_time:.2f} 秒")
        print(f"小于100000的素数个数: {results[0]}")
        
        # 停止集群
        cluster.stop()
    
    # 绘制性能图
    plt.figure(figsize=(10, 6))
    plt.plot(worker_counts, execution_times, 'o-')
    plt.xlabel('工作节点数量')
    plt.ylabel('执行时间（秒）')
    plt.title('并行性能测试')
    plt.grid(True)
    plt.savefig('parallel_performance.png')
    plt.close()
    
    # 计算加速比
    speedups = [execution_times[0] / t for t in execution_times]
    
    # 绘制加速比图
    plt.figure(figsize=(10, 6))
    plt.plot(worker_counts, speedups, 'o-')
    plt.plot(worker_counts, worker_counts, 'r--', label='理想加速比')
    plt.xlabel('工作节点数量')
    plt.ylabel('加速比')
    plt.title('并行加速比')
    plt.legend()
    plt.grid(True)
    plt.savefig('parallel_speedup.png')
    plt.close()
    
    print("并行性能测试完成")

def main():
    """主函数"""
    print("开始测试分布式计算模块...")
    
    # 测试简单任务
    test_simple_task()
    
    # 测试Map任务
    test_map_task()
    
    # 测试本地工作节点
    test_local_worker()
    
    # 测试进程工作节点
    test_process_worker()
    
    # 测试简单调度器
    test_simple_scheduler()
    
    # 测试本地集群
    test_local_cluster()
    
    # 测试Dask集群
    test_dask_cluster()
    
    # 测试并行性能
    test_parallel_performance()
    
    print("\n所有测试完成")

if __name__ == "__main__":
    main()
