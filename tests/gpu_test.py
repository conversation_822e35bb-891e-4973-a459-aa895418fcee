import time
import json
import torch
import numpy as np

def run_tests():
    results = {}
    device = torch.device("cuda")
    
    # 基本信息
    results["gpu_info"] = {
        "name": torch.cuda.get_device_name(),
        "memory": torch.cuda.get_device_properties(0).total_memory,
        "compute_capability": torch.cuda.get_device_capability(),
        "cuda_version": torch.version.cuda
    }
    
    # 检测是否支持双精度
    try:
        # 创建一个小的双精度张量测试
        test_tensor = torch.ones(10, 10, dtype=torch.float64, device=device)
        test_result = test_tensor * test_tensor
        supports_fp64 = True
    except Exception as e:
        supports_fp64 = False
        results["fp64_support_error"] = str(e)
    
    results["supports_fp64"] = supports_fp64
    
    # 如果支持双精度，测试双精度性能
    if supports_fp64:
        # 双精度矩阵乘法测试
        for size in [1000, 3000, 5000]:
            try:
                a = torch.randn(size, size, dtype=torch.float64, device=device)
                b = torch.randn(size, size, dtype=torch.float64, device=device)
                
                # 预热
                for _ in range(3):
                    c = torch.matmul(a, b)
                    torch.cuda.synchronize()
                
                # 计时
                torch.cuda.synchronize()
                start = time.time()
                for _ in range(5):
                    c = torch.matmul(a, b)
                    torch.cuda.synchronize()
                end = time.time()
                
                results[f"fp64_matmul_{size}"] = (end - start) / 5
            except Exception as e:
                results[f"fp64_matmul_{size}_error"] = str(e)
        
        # 双精度复数运算测试（量子模拟常用）
        for size in [500, 1000, 2000]:
            try:
                # 创建复数张量
                real = torch.randn(size, size, dtype=torch.float64, device=device)
                imag = torch.randn(size, size, dtype=torch.float64, device=device)
                a = torch.complex(real, imag)
                b = torch.complex(real, imag)
                
                # 预热
                for _ in range(3):
                    c = torch.matmul(a, b)
                    torch.cuda.synchronize()
                
                # 计时
                torch.cuda.synchronize()
                start = time.time()
                for _ in range(5):
                    c = torch.matmul(a, b)
                    torch.cuda.synchronize()
                end = time.time()
                
                results[f"complex64_matmul_{size}"] = (end - start) / 5
            except Exception as e:
                results[f"complex64_matmul_{size}_error"] = str(e)
        
        # 双精度FFT测试（量子模拟常用）
        for dims in [(64, 64, 64), (32, 32, 32, 32)]:
            try:
                tensor = torch.randn(*dims, dtype=torch.float64, device=device)
                
                # 预热
                for _ in range(3):
                    result = torch.fft.fftn(tensor)
                    torch.cuda.synchronize()
                
                # 计时
                torch.cuda.synchronize()
                start = time.time()
                for _ in range(5):
                    result = torch.fft.fftn(tensor)
                    torch.cuda.synchronize()
                end = time.time()
                
                results[f"fp64_fft_{len(dims)}d"] = (end - start) / 5
            except Exception as e:
                results[f"fp64_fft_{len(dims)}d_error"] = str(e)
    
    # 单精度测试（用于比较）
    # 单精度矩阵乘法测试
    for size in [1000, 3000, 5000]:
        try:
            a = torch.randn(size, size, dtype=torch.float32, device=device)
            b = torch.randn(size, size, dtype=torch.float32, device=device)
            
            # 预热
            for _ in range(3):
                c = torch.matmul(a, b)
                torch.cuda.synchronize()
            
            # 计时
            torch.cuda.synchronize()
            start = time.time()
            for _ in range(5):
                c = torch.matmul(a, b)
                torch.cuda.synchronize()
            end = time.time()
            
            results[f"fp32_matmul_{size}"] = (end - start) / 5
        except Exception as e:
            results[f"fp32_matmul_{size}_error"] = str(e)
    
    # 单精度FFT测试
    for dims in [(64, 64, 64), (32, 32, 32, 32)]:
        try:
            tensor = torch.randn(*dims, dtype=torch.float32, device=device)
            
            # 预热
            for _ in range(3):
                result = torch.fft.fftn(tensor)
                torch.cuda.synchronize()
            
            # 计时
            torch.cuda.synchronize()
            start = time.time()
            for _ in range(5):
                result = torch.fft.fftn(tensor)
                torch.cuda.synchronize()
            end = time.time()
            
            results[f"fp32_fft_{len(dims)}d"] = (end - start) / 5
        except Exception as e:
            results[f"fp32_fft_{len(dims)}d_error"] = str(e)
    
    # 计算FP64/FP32性能比率
    if supports_fp64:
        fp64_fp32_ratios = {}
        
        # 矩阵乘法比率
        for size in [1000, 3000, 5000]:
            fp64_key = f"fp64_matmul_{size}"
            fp32_key = f"fp32_matmul_{size}"
            
            if fp64_key in results and fp32_key in results:
                # 注意：较小的时间表示更好的性能
                # 因此比率是 fp32_time / fp64_time
                # 如果比率接近1，表示双精度性能接近单精度
                # 如果比率很小（如0.125），表示双精度性能远低于单精度
                ratio = results[fp32_key] / results[fp64_key]
                fp64_fp32_ratios[f"matmul_{size}_ratio"] = ratio
        
        # FFT比率
        for dims in [3, 4]:
            fp64_key = f"fp64_fft_{dims}d"
            fp32_key = f"fp32_fft_{dims}d"
            
            if fp64_key in results and fp32_key in results:
                ratio = results[fp32_key] / results[fp64_key]
                fp64_fp32_ratios[f"fft_{dims}d_ratio"] = ratio
        
        # 添加比率到结果
        results["fp64_fp32_ratios"] = fp64_fp32_ratios
        
        # 计算平均比率
        if fp64_fp32_ratios:
            avg_ratio = sum(fp64_fp32_ratios.values()) / len(fp64_fp32_ratios)
            results["fp64_fp32_avg_ratio"] = avg_ratio
    
    # 内存带宽测试
    try:
        # 创建大型张量
        size = 1000000000  # 1B元素
        try:
            tensor = torch.randn(size, device=device)
        except Exception:
            # 如果内存不足，尝试较小的大小
            size = 100000000  # 100M元素
            tensor = torch.randn(size, device=device)
        
        # 预热
        for _ in range(3):
            result = tensor + tensor
            torch.cuda.synchronize()
        
        # 计时
        torch.cuda.synchronize()
        start = time.time()
        for _ in range(5):
            result = tensor + tensor
            torch.cuda.synchronize()
        end = time.time()
        
        # 计算带宽 (GB/s)
        elapsed = (end - start) / 5
        bytes_processed = size * 4 * 3  # 读取2个张量，写入1个张量，每个元素4字节
        bandwidth = bytes_processed / elapsed / (1024**3)
        
        results["memory_bandwidth_GBps"] = bandwidth
    except Exception as e:
        results["memory_bandwidth_error"] = str(e)
    
    return results

if __name__ == "__main__":
    results = run_tests()
    with open("benchmark_results.json", "w") as f:
        json.dump(results, f, indent=2)
    print("测试完成，结果已保存到 benchmark_results.json")
