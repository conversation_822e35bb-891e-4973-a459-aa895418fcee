"""
音频处理测试脚本

本脚本测试音频处理模块的基本功能。
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt
import importlib.util

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入音频处理模块
from src.audio_processing import (
    AudioFormat, AudioChannelLayout,
    Audio, AudioFactory,
    LowpassFilter, HighpassFilter,
    ResampleTransform, PitchShiftTransform,
    PipelineProcessor, FeatureExtractionProcessor,
    LIBROSA_AVAILABLE, SOUNDFILE_AVAILABLE
)

if LIBROSA_AVAILABLE:
    from src.audio_processing import LibrosaInterface

if SOUNDFILE_AVAILABLE:
    from src.audio_processing import SoundfileInterface

def create_test_audio(duration: float = 1.0, sample_rate: int = 44100) -> Audio:
    """创建测试音频"""
    # 计算样本数
    num_samples = int(duration * sample_rate)
    
    # 创建时间轴
    t = np.linspace(0, duration, num_samples, endpoint=False)
    
    # 创建正弦波
    data = 0.5 * np.sin(2 * np.pi * 440 * t)  # 440 Hz
    
    # 添加谐波
    data += 0.3 * np.sin(2 * np.pi * 880 * t)  # 880 Hz
    data += 0.2 * np.sin(2 * np.pi * 1320 * t)  # 1320 Hz
    
    # 创建音频
    return Audio(data, sample_rate, AudioChannelLayout.MONO)

def create_test_stereo_audio(duration: float = 1.0, sample_rate: int = 44100) -> Audio:
    """创建测试立体声音频"""
    # 计算样本数
    num_samples = int(duration * sample_rate)
    
    # 创建时间轴
    t = np.linspace(0, duration, num_samples, endpoint=False)
    
    # 创建左声道
    left = 0.5 * np.sin(2 * np.pi * 440 * t)  # 440 Hz
    left += 0.3 * np.sin(2 * np.pi * 880 * t)  # 880 Hz
    
    # 创建右声道
    right = 0.5 * np.sin(2 * np.pi * 587.33 * t)  # 587.33 Hz
    right += 0.3 * np.sin(2 * np.pi * 1174.66 * t)  # 1174.66 Hz
    
    # 合并声道
    data = np.column_stack((left, right))
    
    # 创建音频
    return Audio(data, sample_rate, AudioChannelLayout.STEREO)

def save_audio(audio: Audio, file_path: str):
    """保存音频"""
    # 创建目录
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    # 保存音频
    AudioFactory.save_to_file(audio, file_path)

def plot_waveform(audio: Audio, file_path: str):
    """绘制波形图"""
    # 创建目录
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    # 创建时间轴
    t = np.linspace(0, audio.duration, audio.num_samples, endpoint=False)
    
    # 创建图形
    plt.figure(figsize=(10, 4))
    
    # 绘制波形
    if audio.num_channels == 1:
        # 单声道
        plt.plot(t, audio.data)
    else:
        # 多声道
        for i in range(audio.num_channels):
            plt.plot(t, audio.data[:, i], label=f"Channel {i+1}")
        plt.legend()
    
    # 设置标题和标签
    plt.title(f"Waveform - {audio.num_channels} channel(s), {audio.sample_rate} Hz")
    plt.xlabel("Time (s)")
    plt.ylabel("Amplitude")
    
    # 保存图形
    plt.savefig(file_path)
    plt.close()

def plot_spectrogram(audio: Audio, file_path: str):
    """绘制频谱图"""
    # 创建目录
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    # 检查scipy是否可用
    SCIPY_AVAILABLE = importlib.util.find_spec("scipy") is not None
    
    if SCIPY_AVAILABLE:
        from scipy import signal
        
        # 创建图形
        plt.figure(figsize=(10, 4))
        
        # 绘制频谱图
        if audio.num_channels == 1:
            # 单声道
            f, t, Sxx = signal.spectrogram(audio.data, audio.sample_rate)
            plt.pcolormesh(t, f, 10 * np.log10(Sxx), shading='gouraud')
        else:
            # 多声道（仅使用第一个通道）
            f, t, Sxx = signal.spectrogram(audio.data[:, 0], audio.sample_rate)
            plt.pcolormesh(t, f, 10 * np.log10(Sxx), shading='gouraud')
        
        # 设置标题和标签
        plt.title(f"Spectrogram - {audio.sample_rate} Hz")
        plt.xlabel("Time (s)")
        plt.ylabel("Frequency (Hz)")
        plt.colorbar(label="Power/Frequency (dB/Hz)")
        
        # 保存图形
        plt.savefig(file_path)
        plt.close()
    else:
        print("scipy未安装，无法绘制频谱图")

def test_audio_creation():
    """测试音频创建"""
    print("\n测试音频创建...")
    
    # 创建空音频
    empty_audio = AudioFactory.create_empty(1.0, 44100, 1)
    print(f"空音频: {empty_audio}")
    
    # 创建测试音频
    test_audio = create_test_audio()
    print(f"测试音频: {test_audio}")
    
    # 创建测试立体声音频
    test_stereo_audio = create_test_stereo_audio()
    print(f"测试立体声音频: {test_stereo_audio}")
    
    # 保存音频
    if SOUNDFILE_AVAILABLE:
        save_audio(test_audio, "output/test_audio.wav")
        save_audio(test_stereo_audio, "output/test_stereo_audio.wav")
    
    # 绘制波形图
    plot_waveform(test_audio, "output/test_audio_waveform.png")
    plot_waveform(test_stereo_audio, "output/test_stereo_audio_waveform.png")
    
    # 绘制频谱图
    plot_spectrogram(test_audio, "output/test_audio_spectrogram.png")
    plot_spectrogram(test_stereo_audio, "output/test_stereo_audio_spectrogram.png")
    
    print("音频创建测试完成")

def test_lowpass_filter():
    """测试低通滤波器"""
    print("\n测试低通滤波器...")
    
    # 创建测试音频
    test_audio = create_test_audio()
    
    # 创建低通滤波器
    lowpass_filter = LowpassFilter(cutoff_frequency=500, order=4, method="butterworth")
    
    # 应用滤波器
    filtered_audio = lowpass_filter.apply(test_audio)
    
    # 保存音频
    if SOUNDFILE_AVAILABLE:
        save_audio(filtered_audio, "output/lowpass_filtered_audio.wav")
    
    # 绘制波形图
    plot_waveform(filtered_audio, "output/lowpass_filtered_audio_waveform.png")
    
    # 绘制频谱图
    plot_spectrogram(filtered_audio, "output/lowpass_filtered_audio_spectrogram.png")
    
    print("低通滤波器测试完成")

def test_highpass_filter():
    """测试高通滤波器"""
    print("\n测试高通滤波器...")
    
    # 创建测试音频
    test_audio = create_test_audio()
    
    # 创建高通滤波器
    highpass_filter = HighpassFilter(cutoff_frequency=800, order=4, method="butterworth")
    
    # 应用滤波器
    filtered_audio = highpass_filter.apply(test_audio)
    
    # 保存音频
    if SOUNDFILE_AVAILABLE:
        save_audio(filtered_audio, "output/highpass_filtered_audio.wav")
    
    # 绘制波形图
    plot_waveform(filtered_audio, "output/highpass_filtered_audio_waveform.png")
    
    # 绘制频谱图
    plot_spectrogram(filtered_audio, "output/highpass_filtered_audio_spectrogram.png")
    
    print("高通滤波器测试完成")

def test_resample_transform():
    """测试重采样变换"""
    print("\n测试重采样变换...")
    
    # 创建测试音频
    test_audio = create_test_audio()
    
    # 创建重采样变换
    resample_up = ResampleTransform(target_sample_rate=48000, method="sinc")
    resample_down = ResampleTransform(target_sample_rate=22050, method="sinc")
    
    # 应用变换
    resampled_up_audio = resample_up.apply(test_audio)
    resampled_down_audio = resample_down.apply(test_audio)
    
    # 保存音频
    if SOUNDFILE_AVAILABLE:
        save_audio(resampled_up_audio, "output/resampled_up_audio.wav")
        save_audio(resampled_down_audio, "output/resampled_down_audio.wav")
    
    # 绘制波形图
    plot_waveform(resampled_up_audio, "output/resampled_up_audio_waveform.png")
    plot_waveform(resampled_down_audio, "output/resampled_down_audio_waveform.png")
    
    # 绘制频谱图
    plot_spectrogram(resampled_up_audio, "output/resampled_up_audio_spectrogram.png")
    plot_spectrogram(resampled_down_audio, "output/resampled_down_audio_spectrogram.png")
    
    print("重采样变换测试完成")

def test_pitch_shift_transform():
    """测试音高变换"""
    print("\n测试音高变换...")
    
    # 创建测试音频
    test_audio = create_test_audio()
    
    # 创建音高变换
    pitch_up = PitchShiftTransform(n_steps=4, method="resample")
    pitch_down = PitchShiftTransform(n_steps=-4, method="resample")
    
    # 应用变换
    pitched_up_audio = pitch_up.apply(test_audio)
    pitched_down_audio = pitch_down.apply(test_audio)
    
    # 保存音频
    if SOUNDFILE_AVAILABLE:
        save_audio(pitched_up_audio, "output/pitched_up_audio.wav")
        save_audio(pitched_down_audio, "output/pitched_down_audio.wav")
    
    # 绘制波形图
    plot_waveform(pitched_up_audio, "output/pitched_up_audio_waveform.png")
    plot_waveform(pitched_down_audio, "output/pitched_down_audio_waveform.png")
    
    # 绘制频谱图
    plot_spectrogram(pitched_up_audio, "output/pitched_up_audio_spectrogram.png")
    plot_spectrogram(pitched_down_audio, "output/pitched_down_audio_spectrogram.png")
    
    print("音高变换测试完成")

def test_pipeline_processor():
    """测试管道处理器"""
    print("\n测试管道处理器...")
    
    # 创建测试音频
    test_audio = create_test_audio()
    
    # 创建处理步骤
    lowpass = LowpassFilter(cutoff_frequency=1000, order=4, method="butterworth")
    resample = ResampleTransform(target_sample_rate=48000, method="sinc")
    pitch_shift = PitchShiftTransform(n_steps=2, method="resample")
    
    # 创建管道处理器
    pipeline = PipelineProcessor(steps=[lowpass, resample, pitch_shift])
    
    # 应用处理器
    processed_audio = pipeline.process(test_audio)
    
    # 保存音频
    if SOUNDFILE_AVAILABLE:
        save_audio(processed_audio, "output/pipeline_processed_audio.wav")
    
    # 绘制波形图
    plot_waveform(processed_audio, "output/pipeline_processed_audio_waveform.png")
    
    # 绘制频谱图
    plot_spectrogram(processed_audio, "output/pipeline_processed_audio_spectrogram.png")
    
    print("管道处理器测试完成")

def test_feature_extraction_processor():
    """测试特征提取处理器"""
    print("\n测试特征提取处理器...")
    
    # 创建测试音频
    test_audio = create_test_audio(duration=3.0)
    
    # 创建特征提取处理器
    mfcc_extractor = FeatureExtractionProcessor(feature_type="mfcc", n_features=13)
    
    # 应用处理器
    result_audio = mfcc_extractor.process(test_audio)
    
    # 获取特征
    features = result_audio.metadata.get("features", {})
    
    # 打印特征信息
    print(f"特征类型: {features.get('type')}")
    print(f"特征数量: {features.get('n_features')}")
    print(f"帧数: {features.get('n_frames')}")
    
    # 如果有特征数据，绘制特征图
    if "data" in features and features["data"]:
        # 创建图形
        plt.figure(figsize=(10, 4))
        
        # 绘制特征
        plt.imshow(features["data"], aspect="auto", origin="lower")
        
        # 设置标题和标签
        plt.title(f"{features.get('type', '').upper()} Features")
        plt.xlabel("Frame")
        plt.ylabel("Feature")
        plt.colorbar()
        
        # 保存图形
        plt.savefig("output/mfcc_features.png")
        plt.close()
    
    print("特征提取处理器测试完成")

def test_librosa_interface():
    """测试Librosa接口"""
    if not LIBROSA_AVAILABLE:
        print("\nLibrosa未安装，跳过测试")
        return
    
    print("\n测试Librosa接口...")
    
    # 创建测试音频
    test_audio = create_test_audio()
    
    # 转换为Librosa格式
    y, sr = LibrosaInterface.to_librosa(test_audio)
    
    # 提取特征
    mfcc = LibrosaInterface.extract_features(test_audio, "mfcc", n_mfcc=13)
    
    # 创建图形
    plt.figure(figsize=(10, 4))
    
    # 绘制特征
    plt.imshow(mfcc, aspect="auto", origin="lower")
    
    # 设置标题和标签
    plt.title("MFCC Features")
    plt.xlabel("Frame")
    plt.ylabel("MFCC")
    plt.colorbar()
    
    # 保存图形
    plt.savefig("output/librosa_mfcc_features.png")
    plt.close()
    
    # 应用音高变换
    shifted_audio = LibrosaInterface.effects_pitch_shift(test_audio, n_steps=4)
    
    # 保存音频
    if SOUNDFILE_AVAILABLE:
        save_audio(shifted_audio, "output/librosa_pitched_audio.wav")
    
    # 绘制波形图
    plot_waveform(shifted_audio, "output/librosa_pitched_audio_waveform.png")
    
    # 绘制频谱图
    plot_spectrogram(shifted_audio, "output/librosa_pitched_audio_spectrogram.png")
    
    print("Librosa接口测试完成")

def main():
    """主函数"""
    print("开始测试音频处理模块...")
    
    # 创建输出目录
    os.makedirs("output", exist_ok=True)
    
    # 测试音频创建
    test_audio_creation()
    
    # 测试低通滤波器
    test_lowpass_filter()
    
    # 测试高通滤波器
    test_highpass_filter()
    
    # 测试重采样变换
    test_resample_transform()
    
    # 测试音高变换
    test_pitch_shift_transform()
    
    # 测试管道处理器
    test_pipeline_processor()
    
    # 测试特征提取处理器
    test_feature_extraction_processor()
    
    # 测试Librosa接口
    test_librosa_interface()
    
    print("\n所有测试完成")

if __name__ == "__main__":
    main()
