"""
测试通过注册表系统使用Rust算子

本脚本测试是否可以通过注册表系统使用Rust算子。
"""

import os
import sys
import unittest

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入注册表
from src.operators.registry import operator_registry

# 导入模块（这会触发注册过程）
import src.operators.explanation
import src.operators.verification

class TestRegistryRustOperators(unittest.TestCase):
    """测试通过注册表系统使用Rust算子"""

    def setUp(self):
        """设置测试环境"""
        self.registry = operator_registry

    def test_explanation_operators_registered(self):
        """测试解释算子是否已注册"""
        # 检查多层次解释生成算子是否已注册
        operator_class = self.registry.get_operator("explanation", "multilevel_explanation")
        self.assertIsNotNone(operator_class, "多层次解释生成算子未注册")

        # 检查解释质量评估算子是否已注册
        operator_class = self.registry.get_operator("explanation", "explanation_quality")
        self.assertIsNotNone(operator_class, "解释质量评估算子未注册")

        # 检查可视化解释算子是否已注册
        operator_class = self.registry.get_operator("explanation", "visualization")
        self.assertIsNotNone(operator_class, "可视化解释算子未注册")

        # 检查Rust实现的多层次解释生成算子是否已注册
        operator_class = self.registry.get_operator("explanation", "rust_multilevel_explanation")
        if operator_class is None:
            self.skipTest("Rust实现的多层次解释生成算子未注册")

    def test_verification_operators_registered(self):
        """测试验证算子是否已注册"""
        # 检查多方法验证算子是否已注册
        operator_class = self.registry.get_operator("verification", "multi_method_verification")
        self.assertIsNotNone(operator_class, "多方法验证算子未注册")

        # 检查一致性验证算子是否已注册
        operator_class = self.registry.get_operator("verification", "consistency_verification")
        self.assertIsNotNone(operator_class, "一致性验证算子未注册")

        # 检查实时验证算子是否已注册
        operator_class = self.registry.get_operator("verification", "realtime_verification")
        self.assertIsNotNone(operator_class, "实时验证算子未注册")

        # 检查Rust实现的多方法验证算子是否已注册
        operator_class = self.registry.get_operator("verification", "rust_multi_method_verification")
        if operator_class is None:
            self.skipTest("Rust实现的多方法验证算子未注册")

    def test_create_explanation_operator(self):
        """测试创建解释算子"""
        try:
            # 尝试创建多层次解释生成算子
            operator_class = self.registry.get_operator("explanation", "multilevel_explanation")
            if operator_class:
                operator = operator_class(tech_level=0.5, concept_level=0.5, analogy_level=0.5)
                self.assertIsNotNone(operator, "无法创建多层次解释生成算子")
        except Exception as e:
            self.skipTest(f"无法创建多层次解释生成算子: {e}")

    def test_create_verification_operator(self):
        """测试创建验证算子"""
        try:
            # 尝试创建多方法验证算子
            operator_class = self.registry.get_operator("verification", "multi_method_verification")
            if operator_class:
                operator = operator_class(model_checking_enabled=True, theorem_proving_enabled=True)
                self.assertIsNotNone(operator, "无法创建多方法验证算子")
        except Exception as e:
            self.skipTest(f"无法创建多方法验证算子: {e}")

if __name__ == "__main__":
    unittest.main()
