"""
跨尺度传递优化算子测试

本测试脚本测试跨尺度传递优化算子的基本功能。
"""

import sys
import os
import unittest
from unittest.mock import MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入跨尺度传递优化算子
from src.transcendental_tensor.cross_scale_transfer import (
    ScaleLevel, TransferMode, OptimizationGoal,
    ScaleInfo, TransferChannel, ScaleNetwork, OptimizationResult,
    CrossScaleTransferOptimizer
)


class TestCrossScaleTransfer(unittest.TestCase):
    """跨尺度传递优化算子测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建测试网络
        self.network = ScaleNetwork()
        
        # 创建尺度信息
        self.quantum_scale = ScaleInfo(
            level=ScaleLevel.QUANTUM,
            dimension=2,
            resolution=0.1,
            coherence=0.9,
            distribution=0.7,
            self_similarity=0.5
        )
        
        self.micro_scale = ScaleInfo(
            level=ScaleLevel.MICRO,
            dimension=3,
            resolution=0.5,
            coherence=0.8,
            distribution=0.8,
            self_similarity=0.6
        )
        
        self.meso_scale = ScaleInfo(
            level=ScaleLevel.MESO,
            dimension=4,
            resolution=1.0,
            coherence=0.7,
            distribution=0.9,
            self_similarity=0.7
        )
        
        # 添加尺度信息
        self.network.add_scale(self.quantum_scale)
        self.network.add_scale(self.micro_scale)
        self.network.add_scale(self.meso_scale)
        
        # 创建通道
        self.channel1 = TransferChannel(
            source_level=ScaleLevel.QUANTUM,
            target_level=ScaleLevel.MICRO,
            bandwidth=0.9,
            latency=5.0,
            fidelity=0.95,
            energy_cost=10.0
        )
        
        self.channel2 = TransferChannel(
            source_level=ScaleLevel.MICRO,
            target_level=ScaleLevel.MESO,
            bandwidth=0.8,
            latency=10.0,
            fidelity=0.9,
            energy_cost=15.0
        )
        
        self.channel3 = TransferChannel(
            source_level=ScaleLevel.MESO,
            target_level=ScaleLevel.MICRO,
            bandwidth=0.7,
            latency=15.0,
            fidelity=0.85,
            energy_cost=20.0
        )
        
        self.channel4 = TransferChannel(
            source_level=ScaleLevel.MICRO,
            target_level=ScaleLevel.QUANTUM,
            bandwidth=0.6,
            latency=20.0,
            fidelity=0.8,
            energy_cost=25.0
        )
        
        # 添加通道
        self.network.add_channel(self.channel1)
        self.network.add_channel(self.channel2)
        self.network.add_channel(self.channel3)
        self.network.add_channel(self.channel4)
        
        # 创建跨尺度传递优化算子
        self.optimizer = CrossScaleTransferOptimizer()
    
    def test_scale_info(self):
        """测试尺度信息"""
        # 测试尺度信息属性
        self.assertEqual(self.quantum_scale.level, ScaleLevel.QUANTUM)
        self.assertEqual(self.quantum_scale.dimension, 2)
        self.assertEqual(self.quantum_scale.resolution, 0.1)
        self.assertEqual(self.quantum_scale.coherence, 0.9)
        self.assertEqual(self.quantum_scale.distribution, 0.7)
        self.assertEqual(self.quantum_scale.self_similarity, 0.5)
    
    def test_transfer_channel(self):
        """测试传递通道"""
        # 测试传递通道属性
        self.assertEqual(self.channel1.source_level, ScaleLevel.QUANTUM)
        self.assertEqual(self.channel1.target_level, ScaleLevel.MICRO)
        self.assertEqual(self.channel1.bandwidth, 0.9)
        self.assertEqual(self.channel1.latency, 5.0)
        self.assertEqual(self.channel1.fidelity, 0.95)
        self.assertEqual(self.channel1.energy_cost, 10.0)
        self.assertTrue(self.channel1.active)
    
    def test_scale_network(self):
        """测试尺度网络"""
        # 测试尺度网络属性
        self.assertEqual(len(self.network.scales), 3)
        self.assertEqual(len(self.network.channels), 4)
        
        # 测试尺度网络方法
        scale = self.network.get_scale(ScaleLevel.QUANTUM)
        self.assertEqual(scale, self.quantum_scale)
        
        channel = self.network.get_channel(self.channel1.channel_id)
        self.assertEqual(channel, self.channel1)
        
        channels = self.network.get_channels_between(ScaleLevel.QUANTUM, ScaleLevel.MICRO)
        self.assertEqual(len(channels), 1)
        self.assertEqual(channels[0], self.channel1)
        
        active_channels = self.network.get_active_channels()
        self.assertEqual(len(active_channels), 4)
    
    def test_optimization_efficiency(self):
        """测试效率优化"""
        # 设置优化参数
        self.optimizer.parameters["goal"] = OptimizationGoal.EFFICIENCY.value
        self.optimizer.parameters["mode"] = TransferMode.ADAPTIVE.value
        
        # 优化网络
        result = self.optimizer.optimize(self.network)
        
        # 验证结果
        self.assertIsInstance(result, OptimizationResult)
        self.assertEqual(result.goal, OptimizationGoal.EFFICIENCY)
        self.assertEqual(result.mode, TransferMode.ADAPTIVE)
        self.assertGreater(len(result.optimized_channels), 0)
        self.assertIn("average_bandwidth", result.metrics)
        self.assertIn("average_latency", result.metrics)
        self.assertIn("average_fidelity", result.metrics)
        self.assertIn("average_energy_cost", result.metrics)
    
    def test_optimization_fidelity(self):
        """测试保真度优化"""
        # 设置优化参数
        self.optimizer.parameters["goal"] = OptimizationGoal.FIDELITY.value
        self.optimizer.parameters["mode"] = TransferMode.ADAPTIVE.value
        
        # 优化网络
        result = self.optimizer.optimize(self.network)
        
        # 验证结果
        self.assertIsInstance(result, OptimizationResult)
        self.assertEqual(result.goal, OptimizationGoal.FIDELITY)
        self.assertEqual(result.mode, TransferMode.ADAPTIVE)
        self.assertGreater(len(result.optimized_channels), 0)
    
    def test_optimization_bandwidth(self):
        """测试带宽优化"""
        # 设置优化参数
        self.optimizer.parameters["goal"] = OptimizationGoal.BANDWIDTH.value
        self.optimizer.parameters["mode"] = TransferMode.BOTTOM_UP.value
        
        # 优化网络
        result = self.optimizer.optimize(self.network)
        
        # 验证结果
        self.assertIsInstance(result, OptimizationResult)
        self.assertEqual(result.goal, OptimizationGoal.BANDWIDTH)
        self.assertEqual(result.mode, TransferMode.BOTTOM_UP)
        self.assertGreater(len(result.optimized_channels), 0)
    
    def test_optimization_latency(self):
        """测试延迟优化"""
        # 设置优化参数
        self.optimizer.parameters["goal"] = OptimizationGoal.LATENCY.value
        self.optimizer.parameters["mode"] = TransferMode.TOP_DOWN.value
        
        # 优化网络
        result = self.optimizer.optimize(self.network)
        
        # 验证结果
        self.assertIsInstance(result, OptimizationResult)
        self.assertEqual(result.goal, OptimizationGoal.LATENCY)
        self.assertEqual(result.mode, TransferMode.TOP_DOWN)
        self.assertGreater(len(result.optimized_channels), 0)
    
    def test_optimization_energy(self):
        """测试能耗优化"""
        # 设置优化参数
        self.optimizer.parameters["goal"] = OptimizationGoal.ENERGY.value
        self.optimizer.parameters["mode"] = TransferMode.BIDIRECTIONAL.value
        
        # 优化网络
        result = self.optimizer.optimize(self.network)
        
        # 验证结果
        self.assertIsInstance(result, OptimizationResult)
        self.assertEqual(result.goal, OptimizationGoal.ENERGY)
        self.assertEqual(result.mode, TransferMode.BIDIRECTIONAL)
        self.assertGreater(len(result.optimized_channels), 0)
    
    def test_optimization_balanced(self):
        """测试平衡优化"""
        # 设置优化参数
        self.optimizer.parameters["goal"] = OptimizationGoal.BALANCED.value
        self.optimizer.parameters["mode"] = TransferMode.RESONANT.value
        
        # 优化网络
        result = self.optimizer.optimize(self.network)
        
        # 验证结果
        self.assertIsInstance(result, OptimizationResult)
        self.assertEqual(result.goal, OptimizationGoal.BALANCED)
        self.assertEqual(result.mode, TransferMode.RESONANT)
        self.assertGreater(len(result.optimized_channels), 0)
    
    def test_optimization_result_cache(self):
        """测试优化结果缓存"""
        # 设置优化参数
        self.optimizer.parameters["goal"] = OptimizationGoal.EFFICIENCY.value
        self.optimizer.parameters["mode"] = TransferMode.ADAPTIVE.value
        self.optimizer.parameters["cache_results"] = True
        
        # 优化网络
        result1 = self.optimizer.optimize(self.network)
        
        # 再次优化网络
        result2 = self.optimizer.optimize(self.network)
        
        # 验证结果
        self.assertEqual(result1.result_id, result2.result_id)


if __name__ == "__main__":
    unittest.main()
