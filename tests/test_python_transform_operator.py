"""
测试Python实现的TransformOperator

本脚本测试Python实现的TransformOperator的基本功能。
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.operators.transform import (
    TransformOperator,
    generate_linear_transform,
    generate_nonlinear_transform,
    generate_group_transform,
    generate_spectral_transform
)


def test_linear_transform():
    """测试线性变换"""
    print("\n测试线性变换...")
    
    # 创建随机数据
    np.random.seed(42)
    data = np.random.randn(10, 2)
    
    # 创建线性变换算子
    transform_op = TransformOperator(
        transform_type='linear',
        dimension=2,
        parameters={
            'matrix': np.array([[np.cos(np.pi/4), -np.sin(np.pi/4)], 
                               [np.sin(np.pi/4), np.cos(np.pi/4)]]),
            'offset': np.array([0, 0])
        }
    )
    
    # 应用变换
    transformed_data = transform_op.apply(data)
    
    # 打印结果
    print(f"变换类型: {transform_op.transform_type}")
    print(f"变换参数: {transform_op.parameters}")
    print(f"原始数据形状: {data.shape}")
    print(f"变换后数据形状: {transformed_data.shape}")
    
    # 可视化原始数据和变换后的数据
    plt.figure(figsize=(10, 5))
    
    plt.subplot(1, 2, 1)
    plt.scatter(data[:, 0], data[:, 1], alpha=0.7)
    plt.title('Original Data')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.scatter(transformed_data[:, 0], transformed_data[:, 1], alpha=0.7)
    plt.title('Transformed Data (Linear)')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('linear_transform_test.png')
    plt.close()
    
    print("线性变换测试完成")


def test_nonlinear_transform():
    """测试非线性变换"""
    print("\n测试非线性变换...")
    
    # 创建随机数据
    np.random.seed(42)
    data = np.random.randn(10, 2)
    
    # 创建非线性变换算子
    transform_op = TransformOperator(
        transform_type='nonlinear',
        dimension=2,
        parameters={
            'function': 'sigmoid',
            'alpha': 1.0
        }
    )
    
    # 应用变换
    transformed_data = transform_op.apply(data)
    
    # 打印结果
    print(f"变换类型: {transform_op.transform_type}")
    print(f"变换参数: {transform_op.parameters}")
    print(f"原始数据形状: {data.shape}")
    print(f"变换后数据形状: {transformed_data.shape}")
    
    # 可视化原始数据和变换后的数据
    plt.figure(figsize=(10, 5))
    
    plt.subplot(1, 2, 1)
    plt.scatter(data[:, 0], data[:, 1], alpha=0.7)
    plt.title('Original Data')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.scatter(transformed_data[:, 0], transformed_data[:, 1], alpha=0.7)
    plt.title('Transformed Data (Nonlinear)')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('nonlinear_transform_test.png')
    plt.close()
    
    print("非线性变换测试完成")


def test_group_transform():
    """测试群变换"""
    print("\n测试群变换...")
    
    # 创建随机数据
    np.random.seed(42)
    data = np.random.randn(10, 2)
    
    # 创建群变换算子
    transform_op = TransformOperator(
        transform_type='group',
        dimension=2,
        parameters={
            'group_type': 'rotation',
            'angle': np.pi/4,
            'axis': 2
        }
    )
    
    # 应用变换
    transformed_data = transform_op.apply(data)
    
    # 打印结果
    print(f"变换类型: {transform_op.transform_type}")
    print(f"变换参数: {transform_op.parameters}")
    print(f"原始数据形状: {data.shape}")
    print(f"变换后数据形状: {transformed_data.shape}")
    
    # 可视化原始数据和变换后的数据
    plt.figure(figsize=(10, 5))
    
    plt.subplot(1, 2, 1)
    plt.scatter(data[:, 0], data[:, 1], alpha=0.7)
    plt.title('Original Data')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.scatter(transformed_data[:, 0], transformed_data[:, 1], alpha=0.7)
    plt.title('Transformed Data (Group)')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('group_transform_test.png')
    plt.close()
    
    print("群变换测试完成")


def test_spectral_transform():
    """测试谱变换"""
    print("\n测试谱变换...")
    
    # 创建随机数据
    np.random.seed(42)
    data = np.random.randn(10, 2)
    
    # 创建谱变换算子
    transform_op = TransformOperator(
        transform_type='spectral',
        dimension=2,
        parameters={
            'spectral_type': 'fft',
            'real_output': True
        }
    )
    
    # 应用变换
    transformed_data = transform_op.apply(data)
    
    # 打印结果
    print(f"变换类型: {transform_op.transform_type}")
    print(f"变换参数: {transform_op.parameters}")
    print(f"原始数据形状: {data.shape}")
    print(f"变换后数据形状: {transformed_data.shape}")
    
    # 可视化原始数据和变换后的数据
    plt.figure(figsize=(10, 5))
    
    plt.subplot(1, 2, 1)
    plt.scatter(data[:, 0], data[:, 1], alpha=0.7)
    plt.title('Original Data')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.scatter(transformed_data[:, 0], transformed_data[:, 1], alpha=0.7)
    plt.title('Transformed Data (Spectral)')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('spectral_transform_test.png')
    plt.close()
    
    print("谱变换测试完成")


def test_generator_functions():
    """测试生成器函数"""
    print("\n测试生成器函数...")
    
    # 测试线性变换生成器
    linear_params = generate_linear_transform(
        dimension=2,
        transform_type='rotation',
        parameters={'angle': np.pi/4}
    )
    print(f"线性变换参数: {linear_params}")
    
    # 测试非线性变换生成器
    nonlinear_params = generate_nonlinear_transform(
        transform_type='sigmoid',
        parameters={'alpha': 1.0}
    )
    print(f"非线性变换参数: {nonlinear_params}")
    
    # 测试群变换生成器
    group_params = generate_group_transform(
        dimension=2,
        group_type='rotation',
        parameters={'angle': np.pi/4}
    )
    print(f"群变换参数: {group_params}")
    
    # 测试谱变换生成器
    spectral_params = generate_spectral_transform(
        spectral_type='fft',
        parameters={'real_output': True}
    )
    print(f"谱变换参数: {spectral_params}")
    
    print("生成器函数测试完成")


def main():
    """主函数"""
    print("开始测试TransformOperator...")
    
    # 测试线性变换
    test_linear_transform()
    
    # 测试非线性变换
    test_nonlinear_transform()
    
    # 测试群变换
    test_group_transform()
    
    # 测试谱变换
    test_spectral_transform()
    
    # 测试生成器函数
    test_generator_functions()
    
    print("\n所有测试完成")


if __name__ == "__main__":
    main()
