"""
测试算子接口

本脚本测试算子接口的基本功能。
"""

import numpy as np
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 定义一个简单的OperatorInterface类
class OperatorInterface:
    """简化的算子接口基类"""
    
    def __init__(self, **kwargs):
        """初始化算子"""
        pass
    
    def apply(self, input_data, **kwargs):
        """应用算子到输入数据"""
        pass
    
    def get_metadata(self):
        """获取算子元数据"""
        pass
    
    def is_compatible_with(self, other_operator):
        """检查与其他算子的兼容性"""
        pass
    
    def get_performance_metrics(self):
        """获取性能指标"""
        pass
    
    def compose(self, other_operator):
        """与其他算子组合"""
        pass
    
    def get_parameters(self):
        """获取算子参数"""
        pass
    
    def set_parameters(self, parameters):
        """设置算子参数"""
        pass
    
    def to_rust(self):
        """检查是否有Rust实现"""
        pass
    
    def get_complexity(self):
        """获取算子复杂度信息"""
        pass


# 定义一个简单的OperatorValidator类
class OperatorValidator:
    """简化的算子验证器类"""
    
    def validate(self, operator):
        """验证算子是否符合接口规范"""
        errors = []
        
        # 检查是否继承自OperatorInterface
        if not isinstance(operator, OperatorInterface):
            errors.append(f"算子 {operator.__class__.__name__} 未继承自OperatorInterface")
            return False, errors
        
        # 检查必需的方法
        required_methods = [
            'apply',
            'get_metadata',
            'is_compatible_with',
            'get_performance_metrics',
            'compose',
            'get_parameters',
            'set_parameters',
            'to_rust',
            'get_complexity'
        ]
        
        for method_name in required_methods:
            if not hasattr(operator, method_name) or not callable(getattr(operator, method_name)):
                errors.append(f"算子缺少必需的方法: {method_name}")
        
        # 检查元数据
        try:
            metadata = operator.get_metadata()
            if not isinstance(metadata, dict):
                errors.append(f"get_metadata 方法应返回字典，但返回了 {type(metadata)}")
            else:
                # 检查必需的元数据字段
                required_fields = ['name', 'type', 'description']
                for field in required_fields:
                    if field not in metadata:
                        errors.append(f"元数据缺少必需的字段: {field}")
        except Exception as e:
            errors.append(f"调用 get_metadata 方法时出错: {str(e)}")
        
        return len(errors) == 0, errors


# 定义一个简单的OperatorRegistry类
class OperatorRegistry:
    """简化的算子注册表类"""
    
    def __init__(self):
        """初始化算子注册表"""
        self.operators = {}
    
    def register(self, operator):
        """注册算子"""
        validator = OperatorValidator()
        valid, errors = validator.validate(operator)
        
        if not valid:
            for error in errors:
                print(f"验证错误: {error}")
            return False
        
        name = operator.get_metadata()['name']
        
        if name in self.operators:
            print(f"算子 {name} 已经注册")
            return False
        
        self.operators[name] = operator
        return True
    
    def unregister(self, name):
        """注销算子"""
        if name in self.operators:
            del self.operators[name]
            return True
        
        return False
    
    def get(self, name):
        """获取算子"""
        return self.operators.get(name)
    
    def list_operators(self):
        """列出所有算子"""
        return list(self.operators.keys())
    
    def find_compatible_operators(self, operator_name):
        """查找与指定算子兼容的算子"""
        if operator_name not in self.operators:
            return []
        
        operator = self.operators[operator_name]
        
        return [
            name for name, op in self.operators.items()
            if name != operator_name and operator.is_compatible_with(op)
        ]


# 定义一个简单的TestOperator类
class TestOperator(OperatorInterface):
    """测试算子类"""
    
    def __init__(self, name="TestOperator", **kwargs):
        """初始化测试算子"""
        self.name = name
    
    def apply(self, input_data, **kwargs):
        """应用算子到输入数据"""
        return input_data
    
    def get_metadata(self):
        """获取算子元数据"""
        return {
            "name": self.name,
            "type": "test",
            "description": "Test operator for testing purposes"
        }
    
    def is_compatible_with(self, other_operator):
        """检查与其他算子的兼容性"""
        return True
    
    def get_performance_metrics(self):
        """获取性能指标"""
        return {
            "time_complexity": 1.0,
            "space_complexity": 1.0,
            "numerical_stability": 0.9,
            "parallelizability": 0.8
        }
    
    def compose(self, other_operator):
        """与其他算子组合"""
        return self
    
    def get_parameters(self):
        """获取算子参数"""
        return {
            "name": self.name
        }
    
    def set_parameters(self, parameters):
        """设置算子参数"""
        if "name" in parameters:
            self.name = parameters["name"]
        return True
    
    def to_rust(self):
        """检查是否有Rust实现"""
        return False
    
    def get_complexity(self):
        """获取算子复杂度信息"""
        return {
            "time_complexity": "O(1)",
            "space_complexity": "O(1)",
            "computational_complexity": "Low",
            "numerical_stability": "High",
            "parallelizable": True
        }


def test_operator_validator():
    """测试算子验证器"""
    print("\n测试算子验证器...")
    
    # 创建算子验证器
    validator = OperatorValidator()
    
    # 创建有效的算子
    valid_operator = TestOperator()
    
    # 验证有效的算子
    valid, errors = validator.validate(valid_operator)
    print(f"验证有效算子:")
    print(f"  有效: {valid}")
    if not valid:
        for error in errors:
            print(f"  - {error}")
    
    # 创建无效的算子
    class InvalidOperator:
        pass
    
    invalid_operator = InvalidOperator()
    
    # 验证无效的算子
    valid, errors = validator.validate(invalid_operator)
    print(f"验证无效算子:")
    print(f"  有效: {valid}")
    if not valid:
        for error in errors:
            print(f"  - {error}")
    
    print("算子验证器测试完成")


def test_operator_registry():
    """测试算子注册表"""
    print("\n测试算子注册表...")
    
    # 创建算子注册表
    registry = OperatorRegistry()
    
    # 创建算子
    operator1 = TestOperator("Operator1")
    operator2 = TestOperator("Operator2")
    
    # 注册算子
    success = registry.register(operator1)
    print(f"注册算子1: {success}")
    
    success = registry.register(operator2)
    print(f"注册算子2: {success}")
    
    # 列出所有算子
    operators = registry.list_operators()
    print(f"所有算子: {operators}")
    
    # 获取算子
    operator = registry.get("Operator1")
    print(f"获取算子1: {operator.get_metadata()['name']}")
    
    # 查找兼容的算子
    compatible_operators = registry.find_compatible_operators("Operator1")
    print(f"与算子1兼容的算子: {compatible_operators}")
    
    # 注销算子
    success = registry.unregister("Operator1")
    print(f"注销算子1: {success}")
    
    # 列出所有算子
    operators = registry.list_operators()
    print(f"所有算子: {operators}")
    
    print("算子注册表测试完成")


def main():
    """主函数"""
    print("开始测试算子接口...")
    
    # 测试算子验证器
    test_operator_validator()
    
    # 测试算子注册表
    test_operator_registry()
    
    print("\n所有测试完成")


if __name__ == "__main__":
    main()
