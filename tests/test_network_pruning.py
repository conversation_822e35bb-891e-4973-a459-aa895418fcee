"""
网络剪枝算子测试

本测试脚本测试网络剪枝算子的基本功能。
"""

import sys
import os
import unittest
import networkx as nx
from unittest.mock import MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入网络剪枝算子
from src.transcendental_tensor.network_pruning import (
    PruningStrategy, PruningGoal, PruningScope, PruningMode,
    PruningResult, NetworkPruner
)


class TestNetworkPruning(unittest.TestCase):
    """网络剪枝算子测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建测试网络
        self.G = nx.Graph()
        
        # 添加节点
        for i in range(10):
            self.G.add_node(i)
        
        # 添加边
        edges = [(0, 1), (0, 2), (1, 2), (1, 3), (2, 3), (2, 4),
                 (3, 4), (3, 5), (4, 5), (4, 6), (5, 6), (5, 7),
                 (6, 7), (6, 8), (7, 8), (7, 9), (8, 9)]
        
        self.G.add_edges_from(edges)
        
        # 添加边权重
        for u, v in self.G.edges():
            self.G[u][v]['weight'] = 1.0
        
        # 创建网络剪枝算子
        self.pruner = NetworkPruner()
    
    def test_pruning_strategy_low_centrality(self):
        """测试低中心性剪枝策略"""
        # 设置剪枝参数
        self.pruner.parameters["strategy"] = PruningStrategy.LOW_CENTRALITY.value
        self.pruner.parameters["scope"] = PruningScope.LINKS.value
        self.pruner.parameters["threshold"] = 0.2
        
        # 剪枝网络
        result = self.pruner.prune_network(self.G)
        
        # 验证结果
        self.assertIsInstance(result, PruningResult)
        self.assertEqual(result.strategy, PruningStrategy.LOW_CENTRALITY)
        self.assertEqual(result.scope, PruningScope.LINKS)
        self.assertEqual(len(result.pruned_nodes), 0)
        self.assertGreater(len(result.pruned_links), 0)
    
    def test_pruning_strategy_high_centrality(self):
        """测试高中心性剪枝策略"""
        # 设置剪枝参数
        self.pruner.parameters["strategy"] = PruningStrategy.HIGH_CENTRALITY.value
        self.pruner.parameters["scope"] = PruningScope.LINKS.value
        self.pruner.parameters["threshold"] = 0.2
        
        # 剪枝网络
        result = self.pruner.prune_network(self.G)
        
        # 验证结果
        self.assertIsInstance(result, PruningResult)
        self.assertEqual(result.strategy, PruningStrategy.HIGH_CENTRALITY)
        self.assertEqual(result.scope, PruningScope.LINKS)
        self.assertEqual(len(result.pruned_nodes), 0)
        self.assertGreater(len(result.pruned_links), 0)
    
    def test_pruning_strategy_low_weight(self):
        """测试低权重剪枝策略"""
        # 设置剪枝参数
        self.pruner.parameters["strategy"] = PruningStrategy.LOW_WEIGHT.value
        self.pruner.parameters["scope"] = PruningScope.LINKS.value
        self.pruner.parameters["threshold"] = 0.2
        
        # 剪枝网络
        result = self.pruner.prune_network(self.G)
        
        # 验证结果
        self.assertIsInstance(result, PruningResult)
        self.assertEqual(result.strategy, PruningStrategy.LOW_WEIGHT)
        self.assertEqual(result.scope, PruningScope.LINKS)
        self.assertEqual(len(result.pruned_nodes), 0)
        self.assertGreater(len(result.pruned_links), 0)
    
    def test_pruning_strategy_redundant(self):
        """测试冗余度剪枝策略"""
        # 设置剪枝参数
        self.pruner.parameters["strategy"] = PruningStrategy.REDUNDANT.value
        self.pruner.parameters["scope"] = PruningScope.LINKS.value
        self.pruner.parameters["threshold"] = 0.2
        
        # 剪枝网络
        result = self.pruner.prune_network(self.G)
        
        # 验证结果
        self.assertIsInstance(result, PruningResult)
        self.assertEqual(result.strategy, PruningStrategy.REDUNDANT)
        self.assertEqual(result.scope, PruningScope.LINKS)
        self.assertEqual(len(result.pruned_nodes), 0)
        self.assertGreater(len(result.pruned_links), 0)
    
    def test_pruning_strategy_low_activity(self):
        """测试低活动度剪枝策略"""
        # 添加节点活动度
        for node in self.G.nodes():
            self.G.nodes[node]['activity'] = 1.0
        
        # 设置剪枝参数
        self.pruner.parameters["strategy"] = PruningStrategy.LOW_ACTIVITY.value
        self.pruner.parameters["scope"] = PruningScope.NODES.value
        self.pruner.parameters["threshold"] = 0.2
        
        # 剪枝网络
        result = self.pruner.prune_network(self.G)
        
        # 验证结果
        self.assertIsInstance(result, PruningResult)
        self.assertEqual(result.strategy, PruningStrategy.LOW_ACTIVITY)
        self.assertEqual(result.scope, PruningScope.NODES)
        self.assertGreater(len(result.pruned_nodes), 0)
        self.assertEqual(len(result.pruned_links), 0)
    
    def test_pruning_strategy_hybrid(self):
        """测试混合策略剪枝策略"""
        # 设置剪枝参数
        self.pruner.parameters["strategy"] = PruningStrategy.HYBRID.value
        self.pruner.parameters["scope"] = PruningScope.LINKS.value
        self.pruner.parameters["threshold"] = 0.2
        self.pruner.parameters["centrality_weight"] = 0.3
        self.pruner.parameters["weight_weight"] = 0.3
        self.pruner.parameters["redundancy_weight"] = 0.4
        
        # 剪枝网络
        result = self.pruner.prune_network(self.G)
        
        # 验证结果
        self.assertIsInstance(result, PruningResult)
        self.assertEqual(result.strategy, PruningStrategy.HYBRID)
        self.assertEqual(result.scope, PruningScope.LINKS)
        self.assertEqual(len(result.pruned_nodes), 0)
        self.assertGreater(len(result.pruned_links), 0)
    
    def test_pruning_preserve_connectivity(self):
        """测试保持连通性"""
        # 设置剪枝参数
        self.pruner.parameters["strategy"] = PruningStrategy.LOW_CENTRALITY.value
        self.pruner.parameters["scope"] = PruningScope.LINKS.value
        self.pruner.parameters["threshold"] = 0.5
        self.pruner.parameters["preserve_connectivity"] = True
        
        # 剪枝网络
        result = self.pruner.prune_network(self.G)
        
        # 验证结果
        self.assertIsInstance(result, PruningResult)
        
        # 创建剪枝后的图
        H = self.G.copy()
        H.remove_edges_from(result.pruned_links)
        
        # 验证连通性
        self.assertTrue(nx.is_connected(H))
    
    def test_pruning_metrics(self):
        """测试剪枝指标"""
        # 设置剪枝参数
        self.pruner.parameters["strategy"] = PruningStrategy.LOW_CENTRALITY.value
        self.pruner.parameters["scope"] = PruningScope.LINKS.value
        self.pruner.parameters["threshold"] = 0.2
        
        # 剪枝网络
        result = self.pruner.prune_network(self.G)
        
        # 验证指标
        self.assertIn("node_count", result.metrics)
        self.assertIn("pruned_node_count", result.metrics)
        self.assertIn("pruned_node_percentage", result.metrics)
        self.assertIn("edge_count", result.metrics)
        self.assertIn("pruned_edge_count", result.metrics)
        self.assertIn("pruned_edge_percentage", result.metrics)
        self.assertIn("average_degree", result.metrics)
        self.assertIn("pruned_average_degree", result.metrics)
        self.assertIn("density", result.metrics)
        self.assertIn("pruned_density", result.metrics)
        self.assertIn("clustering_coefficient", result.metrics)
        self.assertIn("pruned_clustering_coefficient", result.metrics)
        self.assertIn("average_shortest_path_length", result.metrics)
        self.assertIn("pruned_average_shortest_path_length", result.metrics)


if __name__ == "__main__":
    unittest.main()
