"""
网络拓扑优化算子测试

本测试脚本测试网络拓扑优化算子的基本功能。
"""

import sys
import os
import unittest
from unittest.mock import MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入网络拓扑优化算子
from src.transcendental_tensor.performance_optimization.network_topology import (
    NetworkNode, NetworkLink, NetworkTopology,
    NodeRole, NodeStatus, LinkType,
    OptimizationGoal, OptimizationStrategy, OptimizationResult,
    TopologyAnalyzer, TopologyOptimizer
)


class TestNetworkTopologyOptimization(unittest.TestCase):
    """网络拓扑优化算子测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建测试拓扑
        self.topology = NetworkTopology(name="TestTopology")
        
        # 创建节点
        self.node1 = NetworkNode(
            node_id="node-1",
            name="Node 1",
            role=NodeRole.MASTER,
            status=NodeStatus.ONLINE,
            address="***********",
            port=8001,
            location=(0, 0, 0)
        )
        
        self.node2 = NetworkNode(
            node_id="node-2",
            name="Node 2",
            role=NodeRole.WORKER,
            status=NodeStatus.ONLINE,
            address="***********",
            port=8002,
            location=(10, 0, 0)
        )
        
        self.node3 = NetworkNode(
            node_id="node-3",
            name="Node 3",
            role=NodeRole.WORKER,
            status=NodeStatus.ONLINE,
            address="***********",
            port=8003,
            location=(0, 10, 0)
        )
        
        self.node4 = NetworkNode(
            node_id="node-4",
            name="Node 4",
            role=NodeRole.WORKER,
            status=NodeStatus.ONLINE,
            address="***********",
            port=8004,
            location=(10, 10, 0)
        )
        
        # 添加节点
        self.topology.add_node(self.node1)
        self.topology.add_node(self.node2)
        self.topology.add_node(self.node3)
        self.topology.add_node(self.node4)
        
        # 创建链接
        self.link1 = NetworkLink(
            source_id="node-1",
            target_id="node-2",
            link_type=LinkType.PHYSICAL,
            latency=10.0,
            bandwidth=100.0,
            packet_loss=0.01,
            status=True,
            weight=1.0,
            bidirectional=True
        )
        
        self.link2 = NetworkLink(
            source_id="node-1",
            target_id="node-3",
            link_type=LinkType.PHYSICAL,
            latency=20.0,
            bandwidth=50.0,
            packet_loss=0.02,
            status=True,
            weight=1.0,
            bidirectional=True
        )
        
        self.link3 = NetworkLink(
            source_id="node-2",
            target_id="node-4",
            link_type=LinkType.PHYSICAL,
            latency=15.0,
            bandwidth=75.0,
            packet_loss=0.015,
            status=True,
            weight=1.0,
            bidirectional=True
        )
        
        self.link4 = NetworkLink(
            source_id="node-3",
            target_id="node-4",
            link_type=LinkType.PHYSICAL,
            latency=25.0,
            bandwidth=40.0,
            packet_loss=0.025,
            status=True,
            weight=1.0,
            bidirectional=True
        )
        
        self.link5 = NetworkLink(
            source_id="node-2",
            target_id="node-3",
            link_type=LinkType.PHYSICAL,
            latency=30.0,
            bandwidth=30.0,
            packet_loss=0.03,
            status=True,
            weight=1.0,
            bidirectional=True
        )
        
        # 添加链接
        self.topology.add_link(self.link1)
        self.topology.add_link(self.link2)
        self.topology.add_link(self.link3)
        self.topology.add_link(self.link4)
        self.topology.add_link(self.link5)
        
        # 创建拓扑分析器
        self.analyzer = TopologyAnalyzer()
        
        # 创建拓扑优化器
        self.optimizer = TopologyOptimizer(
            strategy_type=OptimizationStrategy.MINIMUM_SPANNING_TREE,
            analyzer=self.analyzer
        )
    
    def test_topology_analyzer(self):
        """测试拓扑分析器"""
        # 分析拓扑
        result = self.analyzer.analyze_topology(self.topology)
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertIn("metrics", result)
        self.assertIn("node_metrics", result)
        self.assertIn("link_metrics", result)
        
        # 验证指标
        metrics = result["metrics"]
        self.assertIn("node_count", metrics)
        self.assertIn("link_count", metrics)
        self.assertIn("average_degree", metrics)
        self.assertIn("density", metrics)
        self.assertIn("diameter", metrics)
        self.assertIn("average_path_length", metrics)
        self.assertIn("clustering_coefficient", metrics)
        
        # 验证节点指标
        node_metrics = result["node_metrics"]
        for node_id in self.topology.nodes:
            self.assertIn(node_id, node_metrics)
            self.assertIn("centrality", node_metrics[node_id])
            self.assertIn("degree", node_metrics[node_id])
        
        # 验证链接指标
        link_metrics = result["link_metrics"]
        for link_id in self.topology.links:
            self.assertIn(link_id, link_metrics)
            self.assertIn("utilization", link_metrics[link_id])
            self.assertIn("importance", link_metrics[link_id])
    
    def test_minimum_spanning_tree_optimization(self):
        """测试最小生成树优化"""
        # 优化拓扑
        result = self.optimizer.optimize_topology(
            self.topology,
            goal=OptimizationGoal.LATENCY,
            strategy_type=OptimizationStrategy.MINIMUM_SPANNING_TREE
        )
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(result.optimization_strategy, OptimizationStrategy.MINIMUM_SPANNING_TREE)
        self.assertEqual(result.optimization_goal, OptimizationGoal.LATENCY)
        
        # 验证优化后的拓扑
        optimized_topology = result.optimized_topology
        self.assertEqual(len(optimized_topology.nodes), len(self.topology.nodes))
        
        # 最小生成树应该有n-1条边（考虑双向链接，所以是2*(n-1)条链接）
        n = len(self.topology.nodes)
        expected_links = 2 * (n - 1)
        self.assertEqual(len(optimized_topology.links), expected_links)
        
        # 验证指标
        metrics = result.metrics
        self.assertIn("original_link_count", metrics)
        self.assertIn("optimized_link_count", metrics)
        self.assertIn("original_total_latency", metrics)
        self.assertIn("optimized_total_latency", metrics)
        self.assertIn("latency_improvement", metrics)
    
    def test_shortest_path_tree_optimization(self):
        """测试最短路径树优化"""
        # 优化拓扑
        result = self.optimizer.optimize_topology(
            self.topology,
            goal=OptimizationGoal.LATENCY,
            strategy_type=OptimizationStrategy.SHORTEST_PATH_TREE
        )
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(result.optimization_strategy, OptimizationStrategy.SHORTEST_PATH_TREE)
        self.assertEqual(result.optimization_goal, OptimizationGoal.LATENCY)
        
        # 验证优化后的拓扑
        optimized_topology = result.optimized_topology
        self.assertEqual(len(optimized_topology.nodes), len(self.topology.nodes))
        
        # 验证指标
        metrics = result.metrics
        self.assertIn("original_link_count", metrics)
        self.assertIn("optimized_link_count", metrics)
        self.assertIn("original_total_latency", metrics)
        self.assertIn("optimized_total_latency", metrics)
        self.assertIn("latency_improvement", metrics)
    
    def test_k_nearest_neighbors_optimization(self):
        """测试K近邻优化"""
        # 优化拓扑
        result = self.optimizer.optimize_topology(
            self.topology,
            goal=OptimizationGoal.LATENCY,
            strategy_type=OptimizationStrategy.K_NEAREST_NEIGHBORS
        )
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(result.optimization_strategy, OptimizationStrategy.K_NEAREST_NEIGHBORS)
        self.assertEqual(result.optimization_goal, OptimizationGoal.LATENCY)
        
        # 验证优化后的拓扑
        optimized_topology = result.optimized_topology
        self.assertEqual(len(optimized_topology.nodes), len(self.topology.nodes))
        
        # 验证指标
        metrics = result.metrics
        self.assertIn("original_link_count", metrics)
        self.assertIn("optimized_link_count", metrics)
        self.assertIn("original_total_latency", metrics)
        self.assertIn("optimized_total_latency", metrics)
        self.assertIn("latency_improvement", metrics)
    
    def test_multi_strategy_optimization(self):
        """测试多策略优化"""
        # 优化拓扑
        result = self.optimizer.optimize_topology_multi_strategy(
            self.topology,
            goal=OptimizationGoal.LATENCY
        )
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertIn(result.optimization_strategy, [
            OptimizationStrategy.MINIMUM_SPANNING_TREE,
            OptimizationStrategy.SHORTEST_PATH_TREE,
            OptimizationStrategy.K_NEAREST_NEIGHBORS
        ])
        self.assertEqual(result.optimization_goal, OptimizationGoal.LATENCY)
        
        # 验证优化后的拓扑
        optimized_topology = result.optimized_topology
        self.assertEqual(len(optimized_topology.nodes), len(self.topology.nodes))
        
        # 验证指标
        metrics = result.metrics
        self.assertIn("original_link_count", metrics)
        self.assertIn("optimized_link_count", metrics)
        self.assertIn("original_total_latency", metrics)
        self.assertIn("optimized_total_latency", metrics)
        self.assertIn("latency_improvement", metrics)
    
    def test_optimization_callback(self):
        """测试优化回调函数"""
        # 创建回调函数
        callback = MagicMock()
        
        # 添加回调函数
        self.optimizer.add_optimization_callback(callback)
        
        # 优化拓扑
        result = self.optimizer.optimize_topology(
            self.topology,
            goal=OptimizationGoal.LATENCY
        )
        
        # 验证回调函数被调用
        callback.assert_called_once_with(result)
        
        # 移除回调函数
        self.assertTrue(self.optimizer.remove_optimization_callback(callback))
        
        # 再次优化拓扑
        self.optimizer.optimize_topology(
            self.topology,
            goal=OptimizationGoal.LATENCY
        )
        
        # 验证回调函数没有被再次调用
        callback.assert_called_once()
    
    def test_optimization_history(self):
        """测试优化历史"""
        # 优化拓扑
        self.optimizer.optimize_topology(
            self.topology,
            goal=OptimizationGoal.LATENCY,
            strategy_type=OptimizationStrategy.MINIMUM_SPANNING_TREE
        )
        
        self.optimizer.optimize_topology(
            self.topology,
            goal=OptimizationGoal.BANDWIDTH,
            strategy_type=OptimizationStrategy.SHORTEST_PATH_TREE
        )
        
        # 获取优化历史
        history = self.optimizer.get_optimization_history()
        
        # 验证历史
        self.assertEqual(len(history), 2)
        self.assertEqual(history[0].optimization_strategy, OptimizationStrategy.MINIMUM_SPANNING_TREE)
        self.assertEqual(history[0].optimization_goal, OptimizationGoal.LATENCY)
        self.assertEqual(history[1].optimization_strategy, OptimizationStrategy.SHORTEST_PATH_TREE)
        self.assertEqual(history[1].optimization_goal, OptimizationGoal.BANDWIDTH)
        
        # 获取限制数量的历史
        limited_history = self.optimizer.get_optimization_history(1)
        self.assertEqual(len(limited_history), 1)
        self.assertEqual(limited_history[0].optimization_strategy, OptimizationStrategy.SHORTEST_PATH_TREE)
        self.assertEqual(limited_history[0].optimization_goal, OptimizationGoal.BANDWIDTH)


if __name__ == "__main__":
    unittest.main()
