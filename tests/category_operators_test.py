"""
范畴算子简单测试模块

本模块直接测试范畴算子的基本功能，不依赖于项目的复杂导入结构。
"""

import unittest
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))


class TestAdjointFunctorOperator(unittest.TestCase):
    """伴随函子算子测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建测试数据
        self.source_category = {
            'id': 'source_category',
            'type': 'category',
            'objects': {
                'A': {'id': 'A', 'type': 'object'},
                'B': {'id': 'B', 'type': 'object'}
            },
            'morphisms': {
                'f': {'id': 'f', 'type': 'morphism', 'source': 'A', 'target': 'B'}
            }
        }
        
        self.target_category = {
            'id': 'target_category',
            'type': 'category',
            'objects': {
                'X': {'id': 'X', 'type': 'object'},
                'Y': {'id': 'Y', 'type': 'object'}
            },
            'morphisms': {
                'g': {'id': 'g', 'type': 'morphism', 'source': 'X', 'target': 'Y'}
            }
        }
        
        self.functor = {
            'id': 'F',
            'type': 'functor',
            'source_category': self.source_category,
            'target_category': self.target_category,
            'mapping': {
                'A': 'X',
                'B': 'Y',
                'f': 'g'
            }
        }
    
    def test_basic_functionality(self):
        """测试基本功能"""
        try:
            # 直接导入伴随函子算子
            from src.operators.category.adjoint import AdjointFunctorOperator
            
            # 创建伴随函子算子
            adjoint_operator = AdjointFunctorOperator(adjoint_type='left')
            
            # 应用算子
            result = adjoint_operator.apply({
                'functor': self.functor,
                'source_category': self.source_category,
                'target_category': self.target_category
            })
            
            # 验证结果
            self.assertIn('adjoint_functor', result)
            self.assertIn('adjoint_type', result)
            self.assertIn('unit', result)
            self.assertIn('counit', result)
            self.assertEqual(result['adjoint_type'], 'left')
            
            # 获取元数据
            metadata = adjoint_operator.get_metadata()
            
            # 验证元数据
            self.assertEqual(metadata['name'], 'AdjointFunctorOperator')
            self.assertEqual(metadata['type'], 'category_theory')
            self.assertEqual(metadata['input_type'], 'functor')
            self.assertEqual(metadata['output_type'], 'adjoint_functor')
            
            print("伴随函子算子测试通过")
        except ImportError as e:
            print(f"导入伴随函子算子失败: {e}")
            raise


if __name__ == '__main__':
    unittest.main()
