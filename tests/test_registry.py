"""
范畴算子测试模块

本模块测试范畴算子的功能。
"""

import unittest
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.operators.category.adjoint import AdjointFunctorOperator
from src.operators.category.limit import LimitOperator, ColimitOperator
from src.operators.category.product import ProductOperator, CoproductOperator


class TestCategoryOperators(unittest.TestCase):
    """范畴算子测试类"""

    def setUp(self):
        """设置测试环境"""
        # 创建测试数据
        self.source_category = {
            'id': 'source_category',
            'type': 'category',
            'objects': {
                'A': {'id': 'A', 'type': 'object'},
                'B': {'id': 'B', 'type': 'object'}
            },
            'morphisms': {
                'f': {'id': 'f', 'type': 'morphism', 'source': 'A', 'target': 'B'}
            }
        }

        self.target_category = {
            'id': 'target_category',
            'type': 'category',
            'objects': {
                'X': {'id': 'X', 'type': 'object'},
                'Y': {'id': 'Y', 'type': 'object'}
            },
            'morphisms': {
                'g': {'id': 'g', 'type': 'morphism', 'source': 'X', 'target': 'Y'}
            }
        }

        self.functor = {
            'id': 'F',
            'type': 'functor',
            'source_category': self.source_category,
            'target_category': self.target_category,
            'mapping': {
                'A': 'X',
                'B': 'Y',
                'f': 'g'
            }
        }

        self.diagram = {
            'id': 'diagram',
            'type': 'diagram',
            'objects': {
                'A': {'id': 'A', 'type': 'object'},
                'B': {'id': 'B', 'type': 'object'}
            },
            'morphisms': {}
        }

    def test_adjoint_functor_operator(self):
        """测试伴随函子算子"""
        # 创建伴随函子算子
        adjoint_operator = AdjointFunctorOperator(adjoint_type='left')

        # 应用算子
        result = adjoint_operator.apply({
            'functor': self.functor,
            'source_category': self.source_category,
            'target_category': self.target_category
        })

        # 验证结果
        self.assertIn('adjoint_functor', result)
        self.assertIn('adjoint_type', result)
        self.assertIn('unit', result)
        self.assertIn('counit', result)
        self.assertEqual(result['adjoint_type'], 'left')

        # 获取元数据
        metadata = adjoint_operator.get_metadata()

        # 验证元数据
        self.assertEqual(metadata['name'], 'AdjointFunctorOperator')
        self.assertEqual(metadata['type'], 'category_theory')
        self.assertEqual(metadata['input_type'], 'functor')
        self.assertEqual(metadata['output_type'], 'adjoint_functor')

    def test_limit_operator(self):
        """测试极限算子"""
        # 创建极限算子
        limit_operator = LimitOperator(diagram_type='product')

        # 应用算子
        result = limit_operator.apply({
            'category': self.source_category,
            'diagram': self.diagram
        })

        # 验证结果
        self.assertIn('type', result)
        self.assertEqual(result['type'], 'limit')
        self.assertIn('limit_type', result)
        self.assertEqual(result['limit_type'], 'product')
        self.assertIn('diagram', result)
        self.assertIn('limit_object', result)
        self.assertIn('projections', result)

        # 获取元数据
        metadata = limit_operator.get_metadata()

        # 验证元数据
        self.assertEqual(metadata['name'], 'LimitOperator')
        self.assertEqual(metadata['type'], 'category_theory')
        self.assertEqual(metadata['input_type'], 'category_diagram')
        self.assertEqual(metadata['output_type'], 'limit')

    def test_colimit_operator(self):
        """测试余极限算子"""
        # 创建余极限算子
        colimit_operator = ColimitOperator(diagram_type='coproduct')

        # 应用算子
        result = colimit_operator.apply({
            'category': self.source_category,
            'diagram': self.diagram
        })

        # 验证结果
        self.assertIn('type', result)
        self.assertEqual(result['type'], 'colimit')
        self.assertIn('colimit_type', result)
        self.assertEqual(result['colimit_type'], 'coproduct')
        self.assertIn('diagram', result)
        self.assertIn('colimit_object', result)
        self.assertIn('injections', result)

        # 获取元数据
        metadata = colimit_operator.get_metadata()

        # 验证元数据
        self.assertEqual(metadata['name'], 'ColimitOperator')
        self.assertEqual(metadata['type'], 'category_theory')
        self.assertEqual(metadata['input_type'], 'category_diagram')
        self.assertEqual(metadata['output_type'], 'colimit')

    def test_product_operator(self):
        """测试范畴积算子"""
        # 创建范畴积算子
        product_operator = ProductOperator(product_type='direct')

        # 应用算子
        result = product_operator.apply({
            'categories': [self.source_category, self.target_category]
        })

        # 验证结果
        self.assertIn('id', result)
        self.assertIn('type', result)
        self.assertEqual(result['type'], 'category')
        self.assertIn('product_type', result)
        self.assertEqual(result['product_type'], 'direct')
        self.assertIn('component_categories', result)
        self.assertIn('objects', result)
        self.assertIn('morphisms', result)

        # 获取元数据
        metadata = product_operator.get_metadata()

        # 验证元数据
        self.assertEqual(metadata['name'], 'ProductOperator')
        self.assertEqual(metadata['type'], 'category_theory')
        self.assertEqual(metadata['input_type'], 'category_list')
        self.assertEqual(metadata['output_type'], 'category_product')

    def test_coproduct_operator(self):
        """测试范畴余积算子"""
        # 创建范畴余积算子
        coproduct_operator = CoproductOperator(coproduct_type='direct')

        # 应用算子
        result = coproduct_operator.apply({
            'categories': [self.source_category, self.target_category]
        })

        # 验证结果
        self.assertIn('id', result)
        self.assertIn('type', result)
        self.assertEqual(result['type'], 'category')
        self.assertIn('coproduct_type', result)
        self.assertEqual(result['coproduct_type'], 'direct')
        self.assertIn('component_categories', result)
        self.assertIn('objects', result)
        self.assertIn('morphisms', result)

        # 获取元数据
        metadata = coproduct_operator.get_metadata()

        # 验证元数据
        self.assertEqual(metadata['name'], 'CoproductOperator')
        self.assertEqual(metadata['type'], 'category_theory')
        self.assertEqual(metadata['input_type'], 'category_list')
        self.assertEqual(metadata['output_type'], 'category_coproduct')


if __name__ == '__main__':
    unittest.main()
