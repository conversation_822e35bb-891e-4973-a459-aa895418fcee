#!/usr/bin/env python3
"""
测试PyO3 0.24+兼容版本的优化多层次解释生成算子
"""

import os
import sys
import time
import json
import logging
import numpy as np
from typing import Dict, Any, List

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入算子
try:
    from src.operators.explanation import (
        MultilevelExplanationOperator,
        OptimizedMultilevelExplanationOperator,
        RUST_AVAILABLE
    )
except ImportError as e:
    logger.error(f"导入算子失败: {e}")
    sys.exit(1)

def generate_test_data(num_samples: int = 10) -> List[Dict[str, Any]]:
    """
    生成测试数据
    
    参数:
        num_samples: 样本数量
        
    返回:
        测试数据列表
    """
    return [
        {
            "model_output": {
                "prediction": "positive" if np.random.random() > 0.5 else "negative",
                "confidence": np.random.random(),
                "features": {
                    "text_length": int(np.random.random() * 1000),
                    "sentiment_score": np.random.random(),
                    "topic_relevance": np.random.random()
                }
            },
            "explanation_level": "all",
            "user_expertise": np.random.choice(["beginner", "intermediate", "expert"])
        }
        for _ in range(num_samples)
    ]

def test_python_implementation():
    """测试Python实现"""
    logger.info("测试Python实现...")
    
    # 创建算子
    operator = MultilevelExplanationOperator(
        tech_level=0.7,
        concept_level=0.5,
        analogy_level=0.3
    )
    
    # 生成测试数据
    test_data = generate_test_data(1)[0]
    
    # 测试性能
    start_time = time.time()
    result = operator.apply(test_data)
    end_time = time.time()
    
    logger.info(f"Python实现耗时: {(end_time - start_time) * 1000:.2f} ms")
    
    # 检查结果
    assert "technical" in result
    assert "conceptual" in result
    assert "analogy" in result
    assert "fused" in result
    assert "weights" in result
    
    logger.info("Python实现测试通过")
    
    return end_time - start_time

def test_optimized_python_implementation():
    """测试优化的Python实现"""
    logger.info("测试优化的Python实现...")
    
    # 创建算子
    operator = OptimizedMultilevelExplanationOperator(
        tech_level=0.7,
        concept_level=0.5,
        analogy_level=0.3,
        parallel_threshold=3,
        use_process_pool=False,
        max_workers=4,
        cache_size=100
    )
    
    # 生成测试数据
    test_data = generate_test_data(1)[0]
    
    # 测试性能
    start_time = time.time()
    result = operator.apply(test_data)
    end_time = time.time()
    
    logger.info(f"优化的Python实现耗时: {(end_time - start_time) * 1000:.2f} ms")
    
    # 检查结果
    assert "technical" in result
    assert "conceptual" in result
    assert "analogy" in result
    assert "fused" in result
    assert "weights" in result
    
    # 测试缓存
    logger.info("测试缓存...")
    
    # 再次运行，应该使用缓存
    start_time = time.time()
    result = operator.apply(test_data)
    end_time = time.time()
    
    logger.info(f"优化的Python实现（使用缓存）耗时: {(end_time - start_time) * 1000:.2f} ms")
    
    # 获取缓存统计信息
    cache_stats = operator.get_cache_stats()
    logger.info(f"缓存统计信息: {cache_stats}")
    
    # 检查缓存命中
    assert cache_stats["cache_hits"] > 0
    
    logger.info("优化的Python实现测试通过")
    
    return end_time - start_time

def test_python_batch_processing():
    """测试Python实现的批处理"""
    logger.info("测试Python实现的批处理...")
    
    # 创建算子
    operator = OptimizedMultilevelExplanationOperator(
        tech_level=0.7,
        concept_level=0.5,
        analogy_level=0.3,
        parallel_threshold=3,
        use_process_pool=False,
        max_workers=4,
        cache_size=100
    )
    
    # 生成测试数据
    batch_size = 10
    test_batch = generate_test_data(batch_size)
    
    # 测试性能
    start_time = time.time()
    results = operator.apply_batch(test_batch)
    end_time = time.time()
    
    logger.info(f"Python实现批处理耗时: {(end_time - start_time) * 1000:.2f} ms")
    
    # 检查结果
    assert len(results) == batch_size
    for result in results:
        assert "technical" in result
        assert "conceptual" in result
        assert "analogy" in result
        assert "fused" in result
        assert "weights" in result
    
    logger.info("Python实现批处理测试通过")

def compare_performance():
    """比较性能"""
    logger.info("比较性能...")
    
    # 测试Python实现
    python_time = test_python_implementation()
    
    # 测试优化的Python实现
    optimized_python_time = test_optimized_python_implementation()
    
    # 计算性能提升
    python_optimized_speedup = python_time / optimized_python_time if optimized_python_time else 0
    
    logger.info(f"优化的Python实现相比基础Python实现的性能提升: {python_optimized_speedup:.2f}x")

def main():
    """主函数"""
    logger.info("开始测试PyO3 0.24+兼容版本的优化多层次解释生成算子...")
    
    # 测试Python实现
    test_python_implementation()
    
    # 测试优化的Python实现
    test_optimized_python_implementation()
    
    # 测试Python批处理
    test_python_batch_processing()
    
    # 比较性能
    compare_performance()
    
    logger.info("测试完成")

if __name__ == "__main__":
    main()
