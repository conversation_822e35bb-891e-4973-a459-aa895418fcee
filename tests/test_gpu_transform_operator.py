"""
测试GPU加速的TransformOperator

本脚本测试GPU加速的TransformOperator的基本功能和性能。
"""

import numpy as np
import matplotlib.pyplot as plt
import time
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.operators.transform import (
    TransformOperator,
    RUST_CORE_AVAILABLE,
    RUST_OPTIMIZED_AVAILABLE,
    RUST_SIMD_AVAILABLE,
    RUST_GPU_AVAILABLE
)

if RUST_CORE_AVAILABLE:
    from src.operators.transform import RustCoreTransformOperator

if RUST_OPTIMIZED_AVAILABLE:
    from src.operators.transform import RustOptimizedTransformOperator, TransformError

if RUST_SIMD_AVAILABLE:
    from src.operators.transform import RustSimdTransformOperator, SimdTransformError, SimdInstructionSet

if RUST_GPU_AVAILABLE:
    from src.operators.transform import RustGpuTransformOperator, GpuTransformError, GpuDevice, GpuPlatform


def test_python_transform_operator():
    """测试Python实现的TransformOperator"""
    print("\n测试Python实现的TransformOperator...")
    
    # 创建随机数据
    np.random.seed(42)
    data = np.random.randn(1000, 2)
    
    # 创建线性变换算子
    transform_op = TransformOperator(
        transform_type='linear',
        dimension=2,
        parameters={
            'matrix': np.array([[np.cos(np.pi/4), -np.sin(np.pi/4)], 
                               [np.sin(np.pi/4), np.cos(np.pi/4)]]),
            'offset': np.array([0, 0])
        }
    )
    
    # 测量性能
    start_time = time.time()
    
    # 应用变换
    transformed_data = transform_op.apply(data)
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    # 打印结果
    print(f"变换类型: {transform_op.transform_type}")
    print(f"变换参数: {transform_op.parameters}")
    print(f"原始数据形状: {data.shape}")
    print(f"变换后数据形状: {transformed_data.shape}")
    print(f"执行时间: {elapsed_time:.6f} 秒")
    
    # 可视化原始数据和变换后的数据
    plt.figure(figsize=(10, 5))
    
    plt.subplot(1, 2, 1)
    plt.scatter(data[:100, 0], data[:100, 1], alpha=0.7)
    plt.title('Original Data')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.scatter(transformed_data[:100, 0], transformed_data[:100, 1], alpha=0.7)
    plt.title('Transformed Data (Python)')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('python_transform_operator_test.png')
    plt.close()
    
    print("Python实现的TransformOperator测试完成")
    
    return data, transformed_data, elapsed_time


def test_gpu_transform_operator(data):
    """测试GPU加速的TransformOperator"""
    if not RUST_GPU_AVAILABLE:
        print("\nGPU加速的TransformOperator不可用，跳过测试")
        return None, None
    
    print("\n测试GPU加速的TransformOperator...")
    
    # 检查GPU是否可用
    is_gpu_available = RustGpuTransformOperator.is_gpu_available()
    print(f"GPU加速是否可用: {is_gpu_available}")
    
    if not is_gpu_available:
        print("GPU加速不可用，跳过测试")
        return None, None
    
    # 获取可用的GPU设备
    available_devices = RustGpuTransformOperator.get_available_devices()
    print(f"可用的GPU设备数量: {len(available_devices)}")
    
    for i, device in enumerate(available_devices):
        print(f"设备 {i}: {device}")
    
    # 如果没有可用的GPU设备，跳过测试
    if not available_devices:
        print("没有可用的GPU设备，跳过测试")
        return None, None
    
    # 使用第一个可用的GPU设备
    device = available_devices[0]
    
    # 创建线性变换算子
    transform_op = RustGpuTransformOperator(
        transform_type='linear',
        dimension=2,
        parameters={
            'matrix': np.array([[np.cos(np.pi/4), -np.sin(np.pi/4)], 
                               [np.sin(np.pi/4), np.cos(np.pi/4)]]),
            'offset': np.array([0, 0])
        },
        device_id=device.device_id
    )
    
    # 测量性能
    start_time = time.time()
    
    # 应用变换
    try:
        transformed_data = transform_op.apply(data)
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        # 打印结果
        print(f"变换类型: {transform_op.transform_type}")
        print(f"变换参数: {transform_op.get_parameters()}")
        print(f"原始数据形状: {data.shape}")
        print(f"变换后数据形状: {transformed_data.shape}")
        print(f"执行时间: {elapsed_time:.6f} 秒")
        print(f"GPU设备: {transform_op.get_device()}")
        
        # 可视化原始数据和变换后的数据
        plt.figure(figsize=(10, 5))
        
        plt.subplot(1, 2, 1)
        plt.scatter(data[:100, 0], data[:100, 1], alpha=0.7)
        plt.title('Original Data')
        plt.grid(True)
        
        plt.subplot(1, 2, 2)
        plt.scatter(transformed_data[:100, 0], transformed_data[:100, 1], alpha=0.7)
        plt.title('Transformed Data (GPU)')
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('gpu_transform_operator_test.png')
        plt.close()
        
        print("GPU加速的TransformOperator测试完成")
        
        return transformed_data, elapsed_time
    
    except GpuTransformError as e:
        print(f"变换错误: {e}")
        return None, None


def compare_implementations(python_result, python_time, gpu_result, gpu_time):
    """比较不同实现的结果和性能"""
    if gpu_result is None:
        print("\nGPU加速的TransformOperator不可用，跳过比较")
        return
    
    print("\n比较不同实现的结果和性能...")
    
    # 计算差异
    diff = np.abs(python_result - gpu_result)
    max_diff = np.max(diff)
    mean_diff = np.mean(diff)
    
    print(f"Python vs GPU:")
    print(f"  最大差异: {max_diff}")
    print(f"  平均差异: {mean_diff}")
    print(f"  Python执行时间: {python_time:.6f} 秒")
    print(f"  GPU执行时间: {gpu_time:.6f} 秒")
    print(f"  加速比: {python_time / gpu_time:.2f}x")
    
    # 可视化性能比较
    implementations = ['Python', 'GPU']
    times = [python_time, gpu_time]
    
    plt.figure(figsize=(10, 6))
    
    plt.bar(implementations, times)
    plt.title('Performance Comparison')
    plt.ylabel('Execution Time (seconds)')
    plt.grid(True, axis='y')
    
    # 添加数值标签
    for i, v in enumerate(times):
        plt.text(i, v + 0.01, f"{v:.6f}s", ha='center')
    
    plt.tight_layout()
    plt.savefig('gpu_performance_comparison.png')
    plt.close()
    
    print("比较完成")


def test_large_scale_performance():
    """测试大规模数据的性能"""
    if not RUST_GPU_AVAILABLE:
        print("\nGPU加速的TransformOperator不可用，跳过测试")
        return
    
    # 检查GPU是否可用
    is_gpu_available = RustGpuTransformOperator.is_gpu_available()
    if not is_gpu_available:
        print("GPU加速不可用，跳过测试")
        return
    
    # 获取可用的GPU设备
    available_devices = RustGpuTransformOperator.get_available_devices()
    if not available_devices:
        print("没有可用的GPU设备，跳过测试")
        return
    
    # 使用第一个可用的GPU设备
    device = available_devices[0]
    
    print("\n测试大规模数据的性能...")
    
    # 创建大规模随机数据
    np.random.seed(42)
    data_sizes = [1000, 10000, 100000]
    
    # 创建结果表格
    print(f"{'数据大小':>10} | {'Python (s)':>12} | {'GPU (s)':>12} | {'加速比':>10}")
    print(f"{'-'*10} | {'-'*12} | {'-'*12} | {'-'*10}")
    
    # 测试不同数据大小
    sizes = []
    python_times = []
    gpu_times = []
    speedups = []
    
    for size in data_sizes:
        data = np.random.randn(size, 2)
        
        # 测试Python实现
        transform_op_python = TransformOperator(
            transform_type='linear',
            dimension=2,
            parameters={
                'matrix': np.array([[np.cos(np.pi/4), -np.sin(np.pi/4)], 
                                   [np.sin(np.pi/4), np.cos(np.pi/4)]]),
                'offset': np.array([0, 0])
            }
        )
        
        start_time = time.time()
        transform_op_python.apply(data)
        python_time = time.time() - start_time
        
        # 测试GPU实现
        transform_op_gpu = RustGpuTransformOperator(
            transform_type='linear',
            dimension=2,
            parameters={
                'matrix': np.array([[np.cos(np.pi/4), -np.sin(np.pi/4)], 
                                   [np.sin(np.pi/4), np.cos(np.pi/4)]]),
                'offset': np.array([0, 0])
            },
            device_id=device.device_id
        )
        
        try:
            start_time = time.time()
            transform_op_gpu.apply(data)
            gpu_time = time.time() - start_time
            
            # 计算加速比
            speedup = python_time / gpu_time
            
            # 打印结果
            print(f"{size:>10} | {python_time:>12.6f} | {gpu_time:>12.6f} | {speedup:>10.2f}")
            
            # 保存结果
            sizes.append(size)
            python_times.append(python_time)
            gpu_times.append(gpu_time)
            speedups.append(speedup)
        
        except GpuTransformError:
            print(f"{size:>10} | {python_time:>12.6f} | {'Error':>12} | {'N/A':>10}")
    
    # 可视化性能比较
    plt.figure(figsize=(12, 10))
    
    # 执行时间比较
    plt.subplot(2, 1, 1)
    plt.plot(sizes, python_times, 'o-', label='Python')
    plt.plot(sizes, gpu_times, 'o-', label='GPU')
    plt.title('Execution Time Comparison')
    plt.xlabel('Data Size')
    plt.ylabel('Execution Time (seconds)')
    plt.grid(True)
    plt.legend()
    plt.xscale('log')
    plt.yscale('log')
    
    # 加速比比较
    plt.subplot(2, 1, 2)
    plt.plot(sizes, speedups, 'o-')
    plt.title('Speedup Comparison')
    plt.xlabel('Data Size')
    plt.ylabel('Speedup (x)')
    plt.grid(True)
    plt.xscale('log')
    
    plt.tight_layout()
    plt.savefig('gpu_large_scale_performance.png')
    plt.close()
    
    print("大规模数据性能测试完成")


def test_nonlinear_transform():
    """测试非线性变换"""
    if not RUST_GPU_AVAILABLE:
        print("\nGPU加速的TransformOperator不可用，跳过测试")
        return
    
    # 检查GPU是否可用
    is_gpu_available = RustGpuTransformOperator.is_gpu_available()
    if not is_gpu_available:
        print("GPU加速不可用，跳过测试")
        return
    
    # 获取可用的GPU设备
    available_devices = RustGpuTransformOperator.get_available_devices()
    if not available_devices:
        print("没有可用的GPU设备，跳过测试")
        return
    
    # 使用第一个可用的GPU设备
    device = available_devices[0]
    
    print("\n测试非线性变换...")
    
    # 创建随机数据
    np.random.seed(42)
    data = np.random.randn(1000, 2)
    
    # 测试不同的非线性函数
    nonlinear_functions = ['sigmoid', 'tanh', 'relu', 'quadratic']
    
    for function in nonlinear_functions:
        print(f"\n测试 {function} 函数...")
        
        # 测试Python实现
        transform_op_python = TransformOperator(
            transform_type='nonlinear',
            dimension=2,
            parameters={
                'function': function
            }
        )
        
        start_time = time.time()
        python_result = transform_op_python.apply(data)
        python_time = time.time() - start_time
        
        # 测试GPU实现
        transform_op_gpu = RustGpuTransformOperator(
            transform_type='nonlinear',
            dimension=2,
            parameters={
                'function': function
            },
            device_id=device.device_id
        )
        
        try:
            start_time = time.time()
            gpu_result = transform_op_gpu.apply(data)
            gpu_time = time.time() - start_time
            
            # 计算差异
            diff = np.abs(python_result - gpu_result)
            max_diff = np.max(diff)
            mean_diff = np.mean(diff)
            
            # 计算加速比
            speedup = python_time / gpu_time
            
            # 打印结果
            print(f"  Python执行时间: {python_time:.6f} 秒")
            print(f"  GPU执行时间: {gpu_time:.6f} 秒")
            print(f"  加速比: {speedup:.2f}x")
            print(f"  最大差异: {max_diff}")
            print(f"  平均差异: {mean_diff}")
            
            # 可视化结果
            plt.figure(figsize=(15, 5))
            
            plt.subplot(1, 3, 1)
            plt.scatter(data[:100, 0], data[:100, 1], alpha=0.7)
            plt.title('Original Data')
            plt.grid(True)
            
            plt.subplot(1, 3, 2)
            plt.scatter(python_result[:100, 0], python_result[:100, 1], alpha=0.7)
            plt.title(f'Python {function}')
            plt.grid(True)
            
            plt.subplot(1, 3, 3)
            plt.scatter(gpu_result[:100, 0], gpu_result[:100, 1], alpha=0.7)
            plt.title(f'GPU {function}')
            plt.grid(True)
            
            plt.tight_layout()
            plt.savefig(f'gpu_{function}_test.png')
            plt.close()
        
        except GpuTransformError as e:
            print(f"  GPU变换错误: {e}")
    
    print("非线性变换测试完成")


def main():
    """主函数"""
    print("开始测试GPU加速的TransformOperator...")
    
    # 测试Python实现
    data, python_result, python_time = test_python_transform_operator()
    
    # 测试GPU加速的实现
    gpu_result, gpu_time = test_gpu_transform_operator(data)
    
    # 比较实现
    compare_implementations(python_result, python_time, gpu_result, gpu_time)
    
    # 测试大规模数据的性能
    test_large_scale_performance()
    
    # 测试非线性变换
    test_nonlinear_transform()
    
    print("\n所有测试完成")


if __name__ == "__main__":
    main()
