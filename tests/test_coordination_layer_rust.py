#!/usr/bin/env python3
"""
协调层Rust实现测试脚本

这个脚本测试协调层的Rust实现，验证其基本功能是否正常工作。
"""

import os
import sys
import time
import json
import uuid
import logging
import unittest
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.distributed.layers.physical import PhysicalLayer
from src.distributed.layers.data import DataLayer
from src.distributed.layers.computation import ComputationLayer
from src.distributed.layers.coordination.coordination_layer import CoordinationLayer
from src.distributed.layers.coordination.distributed_lock import LockType
from src.distributed.layers.coordination.consensus.consensus_algorithm import ConsensusType
from src.distributed.layers.coordination.conflict_resolver import ConflictResolutionStrategy

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

class TestCoordinationLayerRust(unittest.TestCase):
    """测试协调层Rust实现"""
    
    def setUp(self):
        """测试前准备"""
        self.node_id = str(uuid.uuid4())
        
        # 创建物理层
        self.physical_layer = PhysicalLayer(
            node_id=self.node_id,
            use_rust=True
        )
        
        # 创建数据层
        self.data_layer = DataLayer(
            node_id=self.node_id,
            physical_layer=self.physical_layer,
            use_rust=True
        )
        
        # 创建计算层
        self.computation_layer = ComputationLayer(
            node_id=self.node_id,
            physical_layer=self.physical_layer,
            data_layer=self.data_layer,
            use_rust=True
        )
        
        # 创建协调层
        self.coordination_layer = CoordinationLayer(
            node_id=self.node_id,
            physical_layer=self.physical_layer,
            data_layer=self.data_layer,
            computation_layer=self.computation_layer,
            use_rust=True
        )
        
        # 检查是否使用了Rust实现
        self.assertTrue(hasattr(self.coordination_layer, '_rust_layer'), 
                        "Rust implementation not available")
        
        # 启动各层
        self.physical_layer.start()
        self.data_layer.start()
        self.computation_layer.start()
        self.coordination_layer.start()
    
    def tearDown(self):
        """测试后清理"""
        # 停止各层
        self.coordination_layer.stop()
        self.computation_layer.stop()
        self.data_layer.stop()
        self.physical_layer.stop()
    
    def test_acquire_release_lock(self):
        """测试获取和释放锁"""
        # 获取锁
        resource_id = f"test_resource_{uuid.uuid4()}"
        acquired = self.coordination_layer.acquire_lock(resource_id, LockType.EXCLUSIVE, timeout=5.0)
        
        # 验证获取成功
        self.assertTrue(acquired)
        
        # 释放锁
        released = self.coordination_layer.release_lock(resource_id)
        
        # 验证释放成功
        self.assertTrue(released)
    
    def test_shared_lock(self):
        """测试共享锁"""
        # 获取共享锁
        resource_id = f"test_resource_{uuid.uuid4()}"
        acquired1 = self.coordination_layer.acquire_lock(resource_id, LockType.SHARED, timeout=5.0)
        
        # 验证获取成功
        self.assertTrue(acquired1)
        
        # 再次获取共享锁
        acquired2 = self.coordination_layer.acquire_lock(resource_id, LockType.SHARED, timeout=5.0)
        
        # 验证获取成功
        self.assertTrue(acquired2)
        
        # 释放锁
        released = self.coordination_layer.release_lock(resource_id)
        
        # 验证释放成功
        self.assertTrue(released)
    
    def test_transaction(self):
        """测试分布式事务"""
        # 开始事务
        transaction_id = self.coordination_layer.begin_transaction()
        
        # 验证事务ID
        self.assertIsNotNone(transaction_id)
        self.assertTrue(isinstance(transaction_id, str))
        
        # 添加操作
        key = f"test_key_{uuid.uuid4()}"
        value = {"test": "value"}
        self.coordination_layer.add_operation(transaction_id, "put", key, value)
        
        # 提交事务
        committed = self.coordination_layer.commit_transaction(transaction_id)
        
        # 验证提交成功
        self.assertTrue(committed)
        
        # 验证数据已写入
        stored_value = self.data_layer.get(key)
        self.assertEqual(stored_value, value)
    
    def test_run_consensus(self):
        """测试运行共识算法"""
        # 运行共识
        topic = f"test_topic_{uuid.uuid4()}"
        value = {"test": "value"}
        result = self.coordination_layer.run_consensus(topic, value, ConsensusType.RAFT)
        
        # 验证结果
        self.assertTrue(result.success)
        self.assertEqual(result.value, value)
    
    def test_resolve_conflict(self):
        """测试解决冲突"""
        # 解决冲突
        resource_id = f"test_resource_{uuid.uuid4()}"
        values = [{"id": 1, "value": "a"}, {"id": 2, "value": "b"}]
        result = self.coordination_layer.resolve_conflict(resource_id, values, ConflictResolutionStrategy.TIMESTAMP)
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertTrue(isinstance(result, dict))
    
    def test_register_resource(self):
        """测试注册资源"""
        # 注册资源
        resource_id = f"test_resource_{uuid.uuid4()}"
        resource_info = {"type": "test", "capacity": 100}
        registered = self.coordination_layer.register_resource(resource_id, resource_info)
        
        # 验证注册成功
        self.assertTrue(registered)
        
        # 获取资源
        retrieved_info = self.coordination_layer.get_resource(resource_id)
        
        # 验证资源信息
        self.assertEqual(retrieved_info, resource_info)
        
        # 注销资源
        unregistered = self.coordination_layer.unregister_resource(resource_id)
        
        # 验证注销成功
        self.assertTrue(unregistered)
        
        # 验证资源已删除
        self.assertIsNone(self.coordination_layer.get_resource(resource_id))
    
    def test_resource_change_handler(self):
        """测试资源变更处理器"""
        # 资源变更记录
        changes = []
        
        # 资源变更处理函数
        def resource_change_handler(resource_id, resource_info, change_type):
            changes.append((resource_id, resource_info, change_type))
        
        # 注册处理器
        self.coordination_layer.register_resource_change_handler(resource_change_handler)
        
        # 注册资源
        resource_id = f"test_resource_{uuid.uuid4()}"
        resource_info = {"type": "test", "capacity": 100}
        self.coordination_layer.register_resource(resource_id, resource_info)
        
        # 等待处理器被调用
        time.sleep(0.5)
        
        # 验证变更记录
        self.assertEqual(len(changes), 1)
        self.assertEqual(changes[0][0], resource_id)
        self.assertEqual(changes[0][2], "register")
        
        # 注销资源
        self.coordination_layer.unregister_resource(resource_id)
        
        # 等待处理器被调用
        time.sleep(0.5)
        
        # 验证变更记录
        self.assertEqual(len(changes), 2)
        self.assertEqual(changes[1][0], resource_id)
        self.assertEqual(changes[1][2], "unregister")

if __name__ == '__main__':
    unittest.main()
