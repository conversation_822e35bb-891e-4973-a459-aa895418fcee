"""多模态融合编码系统完整测试套件"""

import unittest
import numpy as np
import torch
import time
import threading
from typing import Dict, List, Any

from src.transcendental_tensor.multimodal_fusion.multimodal_fusion_operator import (
    MultimodalFusionOperator,
    ModalityType,
    ModalityConfig
)
from src.transcendental_tensor.multimodal_fusion.fusion_strategies import (
    FUSION_STRATEGIES,
    WeightedAverageFusion,
    NonlinearFusion,
    DeepFusion
)

class TestQuantumEncoding(unittest.TestCase):
    """量子编码测试"""
    
    def setUp(self):
        self.fusion_op = MultimodalFusionOperator()
        self.fusion_op.register_modality(
            "quantum",
            ModalityConfig(
                type=ModalityType.QUANTUM,
                dimension=8,
                encoding_params={
                    "encoding_type": "standard",
                    "normalize": True
                }
            )
        )
        
    def test_standard_encoding(self):
        """测试标准量子编码"""
        data = np.random.rand(4) + 1j * np.random.rand(4)
        result = self.fusion_op.encode_modality(data, "quantum")
        
        self.assertTrue(result["success"])
        self.assertTrue(np.allclose(np.linalg.norm(result["encoded_data"]), 1.0))
        
    def test_amplitude_encoding(self):
        """测试振幅编码"""
        self.fusion_op.modality_configs["quantum"].encoding_params["encoding_type"] = "amplitude"
        data = np.random.rand(4)
        result = self.fusion_op.encode_modality(data, "quantum")
        
        self.assertTrue(result["success"])
        self.assertTrue(np.all(np.abs(result["encoded_data"]) <= 1.0))
        
    def test_phase_encoding(self):
        """测试相位编码"""
        self.fusion_op.modality_configs["quantum"].encoding_params["encoding_type"] = "phase"
        data = np.random.rand(4)
        result = self.fusion_op.encode_modality(data, "quantum")
        
        self.assertTrue(result["success"])
        self.assertTrue(np.allclose(np.abs(result["encoded_data"]), 1.0))

class TestHolographicEncoding(unittest.TestCase):
    """全息编码测试"""
    
    def setUp(self):
        self.fusion_op = MultimodalFusionOperator()
        self.fusion_op.register_modality(
            "holographic",
            ModalityConfig(
                type=ModalityType.HOLOGRAPHIC,
                dimension=16,
                encoding_params={
                    "field_type": "scalar",
                    "resolution": 32,
                    "use_fft": True
                }
            )
        )
        
    def test_scalar_field(self):
        """测试标量场编码"""
        data = np.random.rand(4, 4)
        result = self.fusion_op.encode_modality(data, "holographic")
        
        self.assertTrue(result["success"])
        self.assertEqual(result["encoded_data"].size, 16)
        
    def test_vector_field(self):
        """测试向量场编码"""
        self.fusion_op.modality_configs["holographic"].encoding_params["field_type"] = "vector"
        data = np.random.rand(4, 4, 2)  # 2D向量场
        result = self.fusion_op.encode_modality(data, "holographic")
        
        self.assertTrue(result["success"])
        self.assertEqual(result["encoded_data"].size, 16)
        
    def test_frequency_selection(self):
        """测试频率选择"""
        data = np.random.rand(8, 8)
        result = self.fusion_op.encode_modality(data, "holographic")
        
        # 验证主要频率分量
        fft = np.fft.fft2(result["encoded_data"])
        significant_freqs = np.sum(np.abs(fft) > 1e-10)
        self.assertLess(significant_freqs, data.size)

class TestFractalEncoding(unittest.TestCase):
    """分形编码测试"""
    
    def setUp(self):
        self.fusion_op = MultimodalFusionOperator()
        self.fusion_op.register_modality(
            "fractal",
            ModalityConfig(
                type=ModalityType.FRACTAL,
                dimension=12,
                encoding_params={
                    "max_scales": 3,
                    "min_size": 4,
                    "use_wavelets": True
                }
            )
        )
        
    def test_wavelet_encoding(self):
        """测试小波编码"""
        data = np.random.rand(8, 8)
        result = self.fusion_op.encode_modality(data, "fractal")
        
        self.assertTrue(result["success"])
        self.assertEqual(result["encoded_data"].shape, (12,))
        
    def test_ifs_encoding(self):
        """测试IFS编码"""
        self.fusion_op.modality_configs["fractal"].encoding_params["use_wavelets"] = False
        data = np.random.rand(16, 16)
        result = self.fusion_op.encode_modality(data, "fractal")
        
        self.assertTrue(result["success"])
        self.assertEqual(result["encoded_data"].shape, (12,))

class TestFusionStrategies(unittest.TestCase):
    """融合策略测试"""
    
    def setUp(self):
        self.data1 = np.random.rand(8)
        self.data2 = np.random.rand(8)
        self.weights = [0.7, 0.3]
        
    def test_weighted_average(self):
        """测试加权平均融合"""
        strategy = FUSION_STRATEGIES["weighted_average"]
        result = strategy.fuse([self.data1, self.data2], self.weights)
        
        expected = self.weights[0] * self.data1 + self.weights[1] * self.data2
        self.assertTrue(np.allclose(result["fused_data"], expected))
        
    def test_attention_fusion(self):
        """测试注意力融合"""
        strategy = FUSION_STRATEGIES["attention"]
        result = strategy.fuse([self.data1, self.data2])
        
        self.assertTrue(result["success"])
        self.assertIsNotNone(result["metadata"]["attention_weights"])
        
    def test_cross_modal_fusion(self):
        """测试跨模态融合"""
        strategy = FUSION_STRATEGIES["cross_modal"]
        result = strategy.fuse([self.data1, self.data2])
        
        self.assertTrue(result["success"])
        self.assertEqual(result["fused_data"].shape, self.data1.shape)
        
    def test_deep_fusion(self):
        """测试深度学习融合"""
        strategy = DeepFusion(
            input_dims=[8, 8],
            fusion_dim=16,
            n_layers=2
        )
        result = strategy.fuse([self.data1, self.data2])
        
        self.assertTrue(result["success"])
        self.assertTrue("reconstruction_errors" in result["metadata"])

class TestStreamProcessing(unittest.TestCase):
    """数据流处理测试"""
    
    def setUp(self):
        self.fusion_op = MultimodalFusionOperator()
        
        # 注册测试模态
        self.fusion_op.register_modality(
            "quantum",
            ModalityConfig(type=ModalityType.QUANTUM, dimension=8)
        )
        self.fusion_op.register_modality(
            "holographic",
            ModalityConfig(type=ModalityType.HOLOGRAPHIC, dimension=16)
        )
        
        # 设置流处理
        self.fusion_op.setup_streaming(
            buffer_size=10,
            max_time_diff=0.1
        )
        
    def test_data_alignment(self):
        """测试数据对齐"""
        # 添加第一个模态数据
        result1 = self.fusion_op.add_stream_data(
            "quantum",
            np.random.rand(4),
            timestamp=time.time()
        )
        self.assertTrue(result1["success"])
        self.assertTrue(result1.get("pending", False))
        
        # 添加第二个模态数据
        time.sleep(0.05)  # 模拟时间差
        result2 = self.fusion_op.add_stream_data(
            "holographic",
            np.random.rand(4, 4),
            timestamp=time.time()
        )
        
        # 验证融合结果
        self.assertTrue(result2["success"])
        if not result2.get("pending", True):
            self.assertTrue("fusion_quality" in result2)
            
    def test_buffer_overflow(self):
        """测试缓冲区溢出处理"""
        for _ in range(15):  # 超过缓冲区大小
            self.fusion_op.add_stream_data(
                "quantum",
                np.random.rand(4),
                timestamp=time.time()
            )
            
        status = self.fusion_op.get_stream_status()
        self.assertEqual(
            status["buffer_status"]["quantum"]["current_size"],
            10  # 最大缓冲区大小
        )
        
    def test_sync_quality(self):
        """测试同步质量监控"""
        # 生成多组数据
        for _ in range(5):
            t = time.time()
            self.fusion_op.add_stream_data(
                "quantum",
                np.random.rand(4),
                timestamp=t
            )
            self.fusion_op.add_stream_data(
                "holographic",
                np.random.rand(4, 4),
                timestamp=t + 0.05
            )
            
        quality = self.fusion_op.get_stream_status()["sync_quality"]
        self.assertGreater(quality["alignment_rate"], 0)
        self.assertLess(quality["avg_time_diff"], 0.1)

class TestErrorHandling(unittest.TestCase):
    """错误处理测试"""
    
    def setUp(self):
        self.fusion_op = MultimodalFusionOperator()
        
    def test_invalid_modality(self):
        """测试无效模态"""
        with self.assertRaises(ValueError):
            self.fusion_op.encode_modality(
                np.random.rand(4),
                "invalid_modality"
            )
            
    def test_dimension_mismatch(self):
        """测试维度不匹配"""
        self.fusion_op.register_modality(
            "test",
            ModalityConfig(type=ModalityType.QUANTUM, dimension=8)
        )
        
        # 测试输入维度过大
        result = self.fusion_op.encode_modality(
            np.random.rand(16),
            "test"
        )
        self.assertTrue(result["success"])
        self.assertEqual(result["encoded_data"].shape, (8,))
        
    def test_fusion_error(self):
        """测试融合错误"""
        with self.assertRaises(ValueError):
            self.fusion_op.fuse_modalities([])  # 空数据
            
class TestPerformance(unittest.TestCase):
    """性能测试"""
    
    def setUp(self):
        self.fusion_op = MultimodalFusionOperator()
        self.fusion_op.register_modality(
            "quantum",
            ModalityConfig(type=ModalityType.QUANTUM, dimension=32)
        )
        self.fusion_op.register_modality(
            "holographic",
            ModalityConfig(type=ModalityType.HOLOGRAPHIC, dimension=64)
        )
        
    def test_encoding_speed(self):
        """测试编码速度"""
        data = np.random.rand(16)
        
        start_time = time.time()
        for _ in range(100):
            self.fusion_op.encode_modality(data, "quantum")
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 100
        self.assertLess(avg_time, 0.1)  # 平均时间应小于100ms
        
    def test_fusion_speed(self):
        """测试融合速度"""
        data1 = np.random.rand(32)
        data2 = np.random.rand(8, 8)
        
        encoded1 = self.fusion_op.encode_modality(data1, "quantum")
        encoded2 = self.fusion_op.encode_modality(data2, "holographic")
        
        start_time = time.time()
        for _ in range(50):
            self.fusion_op.fuse_modalities([encoded1, encoded2])
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 50
        self.assertLess(avg_time, 0.2)  # 平均时间应小于200ms
        
    def test_parallel_processing(self):
        """测试并行处理"""
        data = np.random.rand(16)
        results = []
        threads = []
        
        def encode_task():
            result = self.fusion_op.encode_modality(data, "quantum")
            results.append(result)
            
        # 创建10个并行线程
        for _ in range(10):
            thread = threading.Thread(target=encode_task)
            threads.append(thread)
            thread.start()
            
        # 等待所有线程完成
        for thread in threads:
            thread.join()
            
        # 验证所有处理都成功
        self.assertEqual(len(results), 10)
        self.assertTrue(all(r["success"] for r in results))

if __name__ == "__main__":
    unittest.main()