import time
import threading
import multiprocessing
import sys
import math

def complex_cpu_task(n):
    """执行更复杂的CPU密集型计算任务"""
    start_time = time.time()
    
    # 执行复杂计算密集型操作
    result = 0
    for i in range(n):
        # 添加一些浮点运算和数学函数调用
        result += math.sqrt(i) * math.sin(i) / (1 + math.cos(i))
    
    end_time = time.time()
    return end_time - start_time

def run_threads(num_workers, n):
    """使用多线程运行任务"""
    print(f"\n--- Running with {num_workers} threads ---")
    start_time = time.time()
    
    threads = []
    for _ in range(num_workers):
        t = threading.Thread(target=complex_cpu_task, args=(n,))
        threads.append(t)
        t.start()
    
    for t in threads:
        t.join()
    
    total_time = time.time() - start_time
    print(f"Total time with {num_workers} threads: {total_time:.2f} seconds")
    return total_time

def run_processes(num_workers, n):
    """使用多进程运行任务"""
    print(f"\n--- Running with {num_workers} processes ---")
    start_time = time.time()
    
    processes = []
    for _ in range(num_workers):
        p = multiprocessing.Process(target=complex_cpu_task, args=(n,))
        processes.append(p)
        p.start()
    
    for p in processes:
        p.join()
    
    total_time = time.time() - start_time
    print(f"Total time with {num_workers} processes: {total_time:.2f} seconds")
    return total_time

def main():
    n = 10_000_000  # 1千万（因为计算更复杂，所以减少迭代次数）
    
    # 获取CPU核心数
    cpu_count = multiprocessing.cpu_count()
    print(f"Running on a system with {cpu_count} CPU cores")
    
    # 单线程基准测试
    print("\n--- Running with 1 thread (baseline) ---")
    start_time = time.time()
    duration = complex_cpu_task(n)
    print(f"Single thread execution time: {duration:.2f} seconds")
    baseline_time = duration
    
    # 测试2个、4个、8个和16个线程
    thread_counts = [2, 4, 8, 16]
    for count in thread_counts:
        thread_time = run_threads(count, n)
        # 计算线程效率 (理想情况下应该接近1.0)
        efficiency = baseline_time / (thread_time / count)
        print(f"Thread efficiency: {efficiency:.2f}x (1.0 means perfect scaling)")
    
    # 测试2个、4个、8个和16个进程
    process_counts = [2, 4, 8, 16]
    for count in process_counts:
        process_time = run_processes(count, n)
        # 计算进程效率 (理想情况下应该接近1.0)
        efficiency = baseline_time / (process_time / count)
        print(f"Process efficiency: {efficiency:.2f}x (1.0 means perfect scaling)")

if __name__ == "__main__":
    print(f"Python version: {sys.version}")
    
    # 使用_is_gil_enabled()函数检查GIL状态
    gil_status = "enabled" if sys._is_gil_enabled() else "disabled"
    print(f"GIL status: {gil_status}")
    
    main()
