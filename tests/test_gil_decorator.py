#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
超越态计算框架GIL装饰器单元测试

测试GIL装饰器的功能和性能
"""

import os
import sys
import time
import unittest
import numpy as np
import threading
from unittest import mock
from typing import Dict, Any, List, Tuple, Optional

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入被测试模块
try:
    from src.core.parallel.tte_gil_decorator import (
        TTEGilOptimizer, quantum_task, holographic_task, fractal_task,
        parallel_task, compute_intensive, get_performance_data
    )
    from src.core.parallel.gil_manager import (
        nogil_mode, gil_state, detect_nogil_support, get_gil_status
    )
    MODULES_IMPORTED = True
except ImportError as e:
    print(f"警告: 无法导入GIL装饰器模块: {e}")
    MODULES_IMPORTED = False


@unittest.skipIf(not MODULES_IMPORTED, "GIL装饰器模块未导入")
class TestTTEGilOptimizer(unittest.TestCase):
    """测试TTEGilOptimizer类"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.optimizer = TTEGilOptimizer()
    
    def test_get_function_key(self):
        """测试获取函数唯一标识"""
        def test_func():
            pass
        
        key = self.optimizer._get_function_key(test_func)
        expected_key = f"{test_func.__module__}.{test_func.__qualname__}"
        self.assertEqual(key, expected_key)
    
    def test_update_performance_data(self):
        """测试更新性能数据"""
        func_key = "test_module.test_func"
        strategy = "nogil"
        execution_time = 0.5
        
        # 首次更新
        self.optimizer._update_performance_data(func_key, strategy, execution_time)
        
        # 检查数据是否正确
        self.assertIn(func_key, self.optimizer._performance_data)
        self.assertIn(strategy, self.optimizer._performance_data[func_key])
        
        data = self.optimizer._performance_data[func_key][strategy]
        self.assertEqual(data['calls'], 1)
        self.assertEqual(data['total_time'], execution_time)
        self.assertEqual(data['avg_time'], execution_time)
        self.assertEqual(data['min_time'], execution_time)
        self.assertEqual(data['max_time'], execution_time)
        
        # 再次更新
        execution_time2 = 0.3
        self.optimizer._update_performance_data(func_key, strategy, execution_time2)
        
        # 检查数据是否正确更新
        data = self.optimizer._performance_data[func_key][strategy]
        self.assertEqual(data['calls'], 2)
        self.assertEqual(data['total_time'], execution_time + execution_time2)
        self.assertEqual(data['avg_time'], (execution_time + execution_time2) / 2)
        self.assertEqual(data['min_time'], execution_time2)  # 最小值应该是0.3
        self.assertEqual(data['max_time'], execution_time)   # 最大值应该是0.5
    
    def test_update_best_strategy(self):
        """测试更新最佳策略"""
        func_key = "test_module.test_func"
        
        # 添加性能数据
        self.optimizer._performance_data[func_key] = {
            'normal': {
                'calls': 5,
                'total_time': 2.5,
                'avg_time': 0.5,
                'min_time': 0.4,
                'max_time': 0.6
            },
            'nogil': {
                'calls': 5,
                'total_time': 1.5,
                'avg_time': 0.3,
                'min_time': 0.2,
                'max_time': 0.4
            },
            'numpy_gil': {
                'calls': 5,
                'total_time': 2.0,
                'avg_time': 0.4,
                'min_time': 0.3,
                'max_time': 0.5
            }
        }
        
        # 更新最佳策略
        self.optimizer._update_best_strategy(func_key)
        
        # 检查最佳策略是否正确
        self.assertIn(func_key, self.optimizer._best_strategies)
        self.assertEqual(self.optimizer._best_strategies[func_key], 'nogil')
    
    def test_get_performance_data(self):
        """测试获取性能数据"""
        # 添加测试数据
        func_key = "test_module.test_func"
        self.optimizer._performance_data[func_key] = {
            'normal': {'calls': 1, 'total_time': 0.5, 'avg_time': 0.5, 'min_time': 0.5, 'max_time': 0.5}
        }
        
        # 测试获取特定函数的性能数据
        def test_func():
            pass
        
        with mock.patch.object(self.optimizer, '_get_function_key', return_value=func_key):
            data = self.optimizer.get_performance_data(test_func)
            self.assertEqual(data, self.optimizer._performance_data[func_key])
        
        # 测试获取所有性能数据
        all_data = self.optimizer.get_performance_data()
        self.assertEqual(all_data, self.optimizer._performance_data)
    
    def test_get_best_strategy(self):
        """测试获取最佳策略"""
        func_key = "test_module.test_func"
        self.optimizer._best_strategies[func_key] = 'nogil'
        
        def test_func():
            pass
        
        with mock.patch.object(self.optimizer, '_get_function_key', return_value=func_key):
            strategy = self.optimizer.get_best_strategy(test_func)
            self.assertEqual(strategy, 'nogil')
        
        # 测试获取不存在的函数的最佳策略
        with mock.patch.object(self.optimizer, '_get_function_key', return_value="nonexistent"):
            strategy = self.optimizer.get_best_strategy(test_func)
            self.assertEqual(strategy, 'normal')  # 应该返回默认策略


@unittest.skipIf(not MODULES_IMPORTED, "GIL装饰器模块未导入")
class TestQuantumTaskDecorator(unittest.TestCase):
    """测试量子任务装饰器"""
    
    def setUp(self):
        """测试前的准备工作"""
        # 创建一个新的优化器实例，避免影响全局实例
        self.optimizer = TTEGilOptimizer()
        self.quantum_task = self.optimizer.quantum_task
    
    def test_decorator_preserves_function_metadata(self):
        """测试装饰器保留函数元数据"""
        @self.quantum_task()
        def test_func(a, b):
            """测试函数"""
            return a + b
        
        self.assertEqual(test_func.__name__, 'test_func')
        self.assertEqual(test_func.__doc__, '测试函数')
        self.assertEqual(test_func(1, 2), 3)
    
    def test_decorator_adds_performance_methods(self):
        """测试装饰器添加性能方法"""
        @self.quantum_task()
        def test_func():
            return 42
        
        self.assertTrue(hasattr(test_func, 'get_performance_data'))
        self.assertTrue(hasattr(test_func, 'get_best_strategy'))
    
    def test_decorator_prevents_recursion(self):
        """测试装饰器防止递归调用"""
        call_count = 0
        
        @self.quantum_task()
        def recursive_func(depth):
            nonlocal call_count
            call_count += 1
            
            if depth > 0:
                recursive_func(depth - 1)
            
            return call_count
        
        # 调用递归函数
        result = recursive_func(3)
        
        # 验证递归调用正常工作
        self.assertEqual(result, 4)  # 应该有4次调用
        
        # 验证性能数据只记录了一次
        func_key = self.optimizer._get_function_key(recursive_func)
        self.assertIn(func_key, self.optimizer._performance_data)
        
        # 获取第一个策略的调用次数
        strategy = next(iter(self.optimizer._performance_data[func_key].keys()))
        self.assertEqual(self.optimizer._performance_data[func_key][strategy]['calls'], 1)
    
    @unittest.skipIf(not hasattr(sys, 'set_nogil'), "Python不支持nogil模式")
    def test_decorator_with_nogil_strategy(self):
        """测试使用nogil策略的装饰器"""
        # 模拟optimize_gil_for_task返回'nogil'
        with mock.patch('src.core.parallel.gil_manager.optimize_gil_for_task', return_value='nogil'):
            @self.quantum_task()
            def test_func():
                return 42
            
            # 调用函数
            result = test_func()
            self.assertEqual(result, 42)
            
            # 验证性能数据
            func_key = self.optimizer._get_function_key(test_func)
            self.assertIn(func_key, self.optimizer._performance_data)
            self.assertIn('nogil', self.optimizer._performance_data[func_key])


@unittest.skipIf(not MODULES_IMPORTED, "GIL装饰器模块未导入")
class TestHolographicTaskDecorator(unittest.TestCase):
    """测试全息任务装饰器"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.optimizer = TTEGilOptimizer()
        self.holographic_task = self.optimizer.holographic_task
    
    def test_holographic_task_decorator(self):
        """测试全息任务装饰器"""
        @self.holographic_task(task_size='small')
        def process_hologram(size=10):
            # 创建随机全息数据
            hologram = np.random.random((size, size))
            # 应用傅里叶变换
            transformed = np.fft.fft2(hologram)
            return transformed
        
        # 调用函数
        result = process_hologram(20)
        
        # 验证结果
        self.assertEqual(result.shape, (20, 20))
        
        # 验证性能数据
        func_key = self.optimizer._get_function_key(process_hologram)
        self.assertIn(func_key, self.optimizer._performance_data)
        
        # 验证任务类型
        self.assertIn(func_key, self.optimizer._task_types)
        self.assertEqual(self.optimizer._task_types[func_key][0], 'holographic')
        self.assertEqual(self.optimizer._task_types[func_key][1], 'small')


@unittest.skipIf(not MODULES_IMPORTED, "GIL装饰器模块未导入")
class TestFractalTaskDecorator(unittest.TestCase):
    """测试分形任务装饰器"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.optimizer = TTEGilOptimizer()
        self.fractal_task = self.optimizer.fractal_task
    
    def test_fractal_task_decorator(self):
        """测试分形任务装饰器"""
        @self.fractal_task(task_size='small')
        def generate_fractal(size=10, iterations=5):
            # 创建初始网格
            grid = np.zeros((size, size), dtype=np.int32)
            center = size // 2
            grid[center, center] = 1
            
            # 简单的分形生长
            for _ in range(iterations):
                new_grid = grid.copy()
                for i in range(1, size-1):
                    for j in range(1, size-1):
                        neighbors = np.sum(grid[i-1:i+2, j-1:j+2]) - grid[i, j]
                        if grid[i, j] == 0 and neighbors == 1:
                            new_grid[i, j] = 1
                grid = new_grid
            
            return grid
        
        # 调用函数
        result = generate_fractal(15, 3)
        
        # 验证结果
        self.assertEqual(result.shape, (15, 15))
        
        # 验证性能数据
        func_key = self.optimizer._get_function_key(generate_fractal)
        self.assertIn(func_key, self.optimizer._performance_data)
        
        # 验证任务类型
        self.assertIn(func_key, self.optimizer._task_types)
        self.assertEqual(self.optimizer._task_types[func_key][0], 'fractal')
        self.assertEqual(self.optimizer._task_types[func_key][1], 'small')


@unittest.skipIf(not MODULES_IMPORTED, "GIL装饰器模块未导入")
class TestParallelTaskDecorator(unittest.TestCase):
    """测试并行任务装饰器"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.optimizer = TTEGilOptimizer()
        self.parallel_task = self.optimizer.parallel_task
    
    def test_parallel_task_decorator(self):
        """测试并行任务装饰器"""
        @self.parallel_task(task_size='small')
        def parallel_process(data, num_threads=2):
            size = len(data)
            chunk_size = size // num_threads
            results = [None] * num_threads
            
            def process_chunk(start, end, chunk_id):
                chunk_sum = sum(data[start:end])
                results[chunk_id] = chunk_sum
            
            threads = []
            for i in range(num_threads):
                start = i * chunk_size
                end = (i + 1) * chunk_size if i < num_threads - 1 else size
                t = threading.Thread(target=process_chunk, args=(start, end, i))
                threads.append(t)
                t.start()
            
            for t in threads:
                t.join()
            
            return sum(results)
        
        # 创建测试数据
        test_data = list(range(100))
        
        # 调用函数
        result = parallel_process(test_data, 4)
        
        # 验证结果
        self.assertEqual(result, sum(test_data))
        
        # 验证性能数据
        func_key = self.optimizer._get_function_key(parallel_process)
        self.assertIn(func_key, self.optimizer._performance_data)
        
        # 验证任务类型
        self.assertIn(func_key, self.optimizer._task_types)
        self.assertEqual(self.optimizer._task_types[func_key][0], 'parallel')
        self.assertEqual(self.optimizer._task_types[func_key][1], 'small')


@unittest.skipIf(not MODULES_IMPORTED, "GIL装饰器模块未导入")
class TestComputeIntensiveDecorator(unittest.TestCase):
    """测试计算密集型任务装饰器"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.optimizer = TTEGilOptimizer()
        self.compute_intensive = self.optimizer.compute_intensive
    
    def test_compute_intensive_decorator(self):
        """测试计算密集型任务装饰器"""
        @self.compute_intensive(task_size='small')
        def matrix_multiply(size=10):
            # 创建随机矩阵
            a = np.random.random((size, size))
            b = np.random.random((size, size))
            # 矩阵乘法
            c = np.dot(a, b)
            return c
        
        # 调用函数
        result = matrix_multiply(20)
        
        # 验证结果
        self.assertEqual(result.shape, (20, 20))
        
        # 验证性能数据
        func_key = self.optimizer._get_function_key(matrix_multiply)
        self.assertIn(func_key, self.optimizer._performance_data)
        
        # 验证任务类型
        self.assertIn(func_key, self.optimizer._task_types)
        self.assertEqual(self.optimizer._task_types[func_key][0], 'compute_intensive')
        self.assertEqual(self.optimizer._task_types[func_key][1], 'small')


@unittest.skipIf(not MODULES_IMPORTED, "GIL装饰器模块未导入")
class TestAdaptiveStrategy(unittest.TestCase):
    """测试自适应策略"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.optimizer = TTEGilOptimizer()
    
    def test_adaptive_strategy_selection(self):
        """测试自适应策略选择"""
        # 创建一个装饰器，启用自适应模式
        quantum_task = self.optimizer.quantum_task(adaptive=True)
        
        @quantum_task
        def test_func():
            return 42
        
        # 模拟不同策略的性能数据
        func_key = self.optimizer._get_function_key(test_func)
        
        # 添加性能数据
        self.optimizer._performance_data[func_key] = {
            'normal': {
                'calls': 5,
                'total_time': 2.5,
                'avg_time': 0.5,
                'min_time': 0.4,
                'max_time': 0.6
            },
            'nogil': {
                'calls': 5,
                'total_time': 1.5,
                'avg_time': 0.3,
                'min_time': 0.2,
                'max_time': 0.4
            },
            'numpy_gil': {
                'calls': 5,
                'total_time': 2.0,
                'avg_time': 0.4,
                'min_time': 0.3,
                'max_time': 0.5
            }
        }
        
        # 更新最佳策略
        self.optimizer._update_best_strategy(func_key)
        
        # 验证最佳策略是否正确
        self.assertEqual(self.optimizer._best_strategies[func_key], 'nogil')
        
        # 使用mock模拟不同策略的执行
        strategies_used = []
        
        def mock_nogil_mode(enabled):
            class MockContext:
                def __enter__(self):
                    strategies_used.append('nogil')
                    return self
                def __exit__(self, *args):
                    pass
            return MockContext()
        
        def mock_gil_state(enabled):
            class MockContext:
                def __enter__(self):
                    strategies_used.append('numpy_gil')
                    return self
                def __exit__(self, *args):
                    pass
            return MockContext()
        
        # 模拟执行函数，应该使用最佳策略'nogil'
        with mock.patch('src.core.parallel.tte_gil_decorator.nogil_mode', mock_nogil_mode):
            with mock.patch('src.core.parallel.tte_gil_decorator.gil_state', mock_gil_state):
                test_func()
        
        # 验证使用了'nogil'策略
        self.assertIn('nogil', strategies_used)
        self.assertNotIn('numpy_gil', strategies_used)


if __name__ == '__main__':
    unittest.main()
