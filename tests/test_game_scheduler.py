"""
博弈优化资源调度算法测试

本模块包含博弈优化资源调度算法的测试用例。
"""

import unittest
import numpy as np
import os
import sys
import tempfile
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 直接导入我们的实现
from src.algorithms.game_theory.scheduler import GameTheoreticScheduler
from src.algorithms.game_theory.models import GameModelBuilder
from src.algorithms.game_theory.nash_solver import NashEquilibriumSolver
from src.algorithms.game_theory.analysis import ResourceAllocationAnalyzer


class TestGameTheoreticScheduler(unittest.TestCase):
    """博弈优化资源调度算法测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        # 创建测试数据
        self.players = self._create_test_players()
        self.resources = self._create_test_resources()
        self.tasks = self._create_test_tasks()
        self.constraints = self._create_test_constraints()
        self.preferences = self._create_test_preferences()
        
        # 创建调度器
        self.scheduler = GameTheoreticScheduler(
            game_type='non_cooperative',
            max_iterations=10,
            tolerance=1e-6,
            use_parallel=True,
            num_workers=2,
            adaptive_strategy=True,
            fairness_weight=0.5,
            efficiency_weight=0.5
        )
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.scheduler.game_type, 'non_cooperative')
        self.assertEqual(self.scheduler.max_iterations, 10)
        self.assertEqual(self.scheduler.tolerance, 1e-6)
        self.assertTrue(self.scheduler.use_parallel)
        self.assertEqual(self.scheduler.num_workers, 2)
        self.assertTrue(self.scheduler.adaptive_strategy)
        self.assertEqual(self.scheduler.fairness_weight, 0.5)
        self.assertEqual(self.scheduler.efficiency_weight, 0.5)
    
    def test_compute(self):
        """测试计算方法"""
        # 创建输入数据
        input_data = {
            'players': self.players,
            'resources': self.resources,
            'tasks': self.tasks,
            'constraints': self.constraints,
            'preferences': self.preferences
        }
        
        # 执行计算
        result = self.scheduler.compute(input_data)
        
        # 检查结果
        self.assertIn('allocation', result)
        self.assertIn('utilities', result)
        self.assertIn('equilibrium', result)
        self.assertIn('iterations', result)
        self.assertIn('convergence_achieved', result)
        self.assertIn('performance', result)
        self.assertIn('analysis', result)
        
        # 检查分配结果
        allocation = result['allocation']
        self.assertIn('player_resources', allocation)
        self.assertIn('task_assignments', allocation)
        
        # 检查效用值
        utilities = result['utilities']
        for player in self.players:
            player_id = player['id']
            self.assertIn(player_id, utilities)
            self.assertIsInstance(utilities[player_id], float)
    
    def test_get_metadata(self):
        """测试获取元数据"""
        metadata = self.scheduler.get_metadata()
        
        self.assertIn('name', metadata)
        self.assertIn('version', metadata)
        self.assertIn('description', metadata)
        self.assertIn('parameters', metadata)
        self.assertIn('performance_metrics', metadata)
    
    def test_is_compatible_with(self):
        """测试兼容性检查"""
        # 创建兼容的调度器
        compatible_scheduler = GameTheoreticScheduler(
            game_type='non_cooperative',
            max_iterations=20
        )
        
        # 创建不兼容的调度器
        incompatible_scheduler = GameTheoreticScheduler(
            game_type='cooperative',
            max_iterations=5
        )
        
        # 检查兼容性
        self.assertTrue(self.scheduler.is_compatible_with(compatible_scheduler))
        self.assertFalse(self.scheduler.is_compatible_with(incompatible_scheduler))
    
    def test_get_performance_metrics(self):
        """测试获取性能指标"""
        # 创建输入数据
        input_data = {
            'players': self.players,
            'resources': self.resources,
            'tasks': self.tasks,
            'constraints': self.constraints,
            'preferences': self.preferences
        }
        
        # 执行计算
        self.scheduler.compute(input_data)
        
        # 获取性能指标
        metrics = self.scheduler.get_performance_metrics()
        
        self.assertIn('total_time', metrics)
        self.assertIn('iterations', metrics)
        self.assertIn('convergence_achieved', metrics)
        self.assertIn('final_utility', metrics)
        self.assertIn('fairness_index', metrics)
        self.assertIn('resource_utilization', metrics)
        self.assertIn('strategy_history', metrics)
    
    def test_game_model_builder(self):
        """测试博弈模型构建器"""
        # 创建博弈模型构建器
        model_builder = GameModelBuilder(game_type='non_cooperative')
        
        # 构建博弈模型
        model = model_builder.build_model(
            players=self.players,
            resources=self.resources,
            tasks=self.tasks,
            constraints=self.constraints,
            preferences=self.preferences
        )
        
        # 检查模型
        self.assertIn('players', model)
        self.assertIn('resources', model)
        self.assertIn('tasks', model)
        self.assertIn('constraints', model)
        self.assertIn('preferences', model)
        self.assertIn('utility_functions', model)
        self.assertIn('allocation_function', model)
        self.assertIn('game_type', model)
        self.assertIn('strategy_spaces', model)
        self.assertIn('payoff_matrices', model)
        self.assertIn('best_response_calculator', model)
        self.assertIn('nash_equilibrium_calculator', model)
    
    def test_nash_equilibrium_solver(self):
        """测试纳什均衡求解器"""
        # 创建博弈模型构建器
        model_builder = GameModelBuilder(game_type='non_cooperative')
        
        # 构建博弈模型
        model = model_builder.build_model(
            players=self.players,
            resources=self.resources,
            tasks=self.tasks,
            constraints=self.constraints,
            preferences=self.preferences
        )
        
        # 创建纳什均衡求解器
        nash_solver = NashEquilibriumSolver(
            max_iterations=10,
            tolerance=1e-6,
            use_parallel=True,
            num_workers=2
        )
        
        # 求解纳什均衡
        equilibrium = nash_solver.find_equilibrium(model)
        
        # 检查均衡
        for player in self.players:
            player_id = player['id']
            self.assertIn(player_id, equilibrium)
    
    def test_resource_allocation_analyzer(self):
        """测试资源分配分析器"""
        # 创建输入数据
        input_data = {
            'players': self.players,
            'resources': self.resources,
            'tasks': self.tasks,
            'constraints': self.constraints,
            'preferences': self.preferences
        }
        
        # 执行计算
        result = self.scheduler.compute(input_data)
        
        # 获取分配结果和效用值
        allocation = result['allocation']
        utilities = result['utilities']
        
        # 创建资源分配分析器
        analyzer = ResourceAllocationAnalyzer()
        
        # 分析资源分配结果
        analysis = analyzer.analyze(
            players=self.players,
            resources=self.resources,
            tasks=self.tasks,
            allocation=allocation,
            utilities=utilities
        )
        
        # 检查分析结果
        self.assertIn('fairness_index', analysis)
        self.assertIn('resource_utilization', analysis)
        self.assertIn('task_completion_rate', analysis)
        self.assertIn('average_utility', analysis)
        self.assertIn('utility_variance', analysis)
        self.assertIn('resource_balance', analysis)
        self.assertIn('task_distribution', analysis)
    
    def _create_test_players(self):
        """创建测试玩家"""
        return [
            {'id': 'player1', 'name': 'Player 1'},
            {'id': 'player2', 'name': 'Player 2'},
            {'id': 'player3', 'name': 'Player 3'}
        ]
    
    def _create_test_resources(self):
        """创建测试资源"""
        return [
            {'id': 'resource1', 'name': 'Resource 1', 'capacity': 10.0},
            {'id': 'resource2', 'name': 'Resource 2', 'capacity': 5.0},
            {'id': 'resource3', 'name': 'Resource 3', 'capacity': 8.0}
        ]
    
    def _create_test_tasks(self):
        """创建测试任务"""
        return [
            {'id': 'task1', 'name': 'Task 1', 'requirements': {'resource1': 2.0, 'resource2': 1.0}},
            {'id': 'task2', 'name': 'Task 2', 'requirements': {'resource2': 2.0, 'resource3': 3.0}},
            {'id': 'task3', 'name': 'Task 3', 'requirements': {'resource1': 3.0, 'resource3': 2.0}}
        ]
    
    def _create_test_constraints(self):
        """创建测试约束条件"""
        return {
            'resource_constraints': {
                'resource1': {'min': 0.0, 'max': 10.0},
                'resource2': {'min': 0.0, 'max': 5.0},
                'resource3': {'min': 0.0, 'max': 8.0}
            },
            'task_constraints': {
                'task1': {'min_players': 1, 'max_players': 2},
                'task2': {'min_players': 1, 'max_players': 2},
                'task3': {'min_players': 1, 'max_players': 2}
            }
        }
    
    def _create_test_preferences(self):
        """创建测试偏好设置"""
        return {
            'player1': {
                'resource1': 0.8,
                'resource2': 0.5,
                'resource3': 0.3,
                'task1': 0.9,
                'task2': 0.4,
                'task3': 0.2
            },
            'player2': {
                'resource1': 0.3,
                'resource2': 0.9,
                'resource3': 0.6,
                'task1': 0.2,
                'task2': 0.8,
                'task3': 0.5
            },
            'player3': {
                'resource1': 0.5,
                'resource2': 0.4,
                'resource3': 0.9,
                'task1': 0.3,
                'task2': 0.6,
                'task3': 0.9
            }
        }


if __name__ == '__main__':
    unittest.main()
