"""
多层次递归优化器测试

本模块包含多层次递归优化器的测试用例。
"""

import unittest
import numpy as np
import os
import sys
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入我们的实现
from src.algorithms.multilevel_optimizer import (
    MultiLevelRecursiveOptimizer,
    Hierarchy,
    HierarchyNode,
    HierarchyBuilder,
    OptimizationStrategy,
    StrategySelector,
    OptimizationAnalyzer,
    create_test_problem
)


class TestMultiLevelRecursiveOptimizer(unittest.TestCase):
    """多层次递归优化器测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        # 创建测试问题
        self.problem, self.objective_function, self.constraints, self.initial_solution = create_test_problem(
            problem_type='quadratic',
            dimension=10,
            seed=42
        )
        
        # 创建优化器
        self.optimizer = MultiLevelRecursiveOptimizer(
            optimization_method='standard',
            recursion_strategy='top_down',
            max_recursion_depth=3,
            convergence_threshold=1e-6,
            use_parallel=True,
            num_workers=2,
            use_cache=True,
            cache_size=10
        )
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.optimizer.optimization_method, 'standard')
        self.assertEqual(self.optimizer.recursion_strategy, 'top_down')
        self.assertEqual(self.optimizer.max_recursion_depth, 3)
        self.assertEqual(self.optimizer.convergence_threshold, 1e-6)
        self.assertTrue(self.optimizer.use_parallel)
        self.assertEqual(self.optimizer.num_workers, 2)
        self.assertTrue(self.optimizer.use_cache)
        self.assertEqual(self.optimizer.cache_size, 10)
    
    def test_compute(self):
        """测试计算方法"""
        # 创建输入数据
        input_data = {
            'problem': self.problem,
            'objective_function': self.objective_function,
            'constraints': self.constraints,
            'initial_solution': self.initial_solution
        }
        
        # 执行计算
        result = self.optimizer.compute(input_data)
        
        # 检查结果
        self.assertIn('solution', result)
        self.assertIn('objective_value', result)
        self.assertIn('iterations', result)
        self.assertIn('hierarchy', result)
        self.assertIn('convergence_history', result)
        self.assertIn('analysis', result)
        self.assertIn('performance', result)
        
        # 检查解的维度
        self.assertEqual(result['solution'].shape, self.initial_solution.shape)
        
        # 检查目标函数值是否减小
        initial_objective = self.objective_function(self.initial_solution)
        final_objective = result['objective_value']
        self.assertLess(final_objective, initial_objective)
        
        # 检查收敛历史
        convergence_history = result['convergence_history']
        self.assertGreater(len(convergence_history), 1)
        self.assertLessEqual(convergence_history[-1], convergence_history[0])
        
        # 检查层次结构
        hierarchy = result['hierarchy']
        self.assertIsInstance(hierarchy, Hierarchy)
        self.assertGreater(hierarchy.get_num_nodes(), 1)
        
        # 检查分析结果
        analysis = result['analysis']
        self.assertIn('convergence_rate', analysis)
        self.assertIn('is_converged', analysis)
        
        # 检查性能指标
        performance = result['performance']
        self.assertIn('total_time', performance)
        self.assertIn('hierarchy_construction_time', performance)
        self.assertIn('optimization_time', performance)
        self.assertIn('num_iterations', performance)
    
    def test_different_optimization_methods(self):
        """测试不同的优化方法"""
        # 创建输入数据
        input_data = {
            'problem': self.problem,
            'objective_function': self.objective_function,
            'constraints': self.constraints,
            'initial_solution': self.initial_solution
        }
        
        # 测试不同的优化方法
        optimization_methods = ['standard', 'adaptive', 'hierarchical']
        
        for method in optimization_methods:
            # 创建优化器
            optimizer = MultiLevelRecursiveOptimizer(
                optimization_method=method,
                recursion_strategy='top_down',
                max_recursion_depth=2,
                convergence_threshold=1e-5
            )
            
            # 执行计算
            result = optimizer.compute(input_data)
            
            # 检查结果
            self.assertIn('solution', result)
            self.assertIn('objective_value', result)
            
            # 检查目标函数值是否减小
            initial_objective = self.objective_function(self.initial_solution)
            final_objective = result['objective_value']
            self.assertLess(final_objective, initial_objective)
    
    def test_different_recursion_strategies(self):
        """测试不同的递归策略"""
        # 创建输入数据
        input_data = {
            'problem': self.problem,
            'objective_function': self.objective_function,
            'constraints': self.constraints,
            'initial_solution': self.initial_solution
        }
        
        # 测试不同的递归策略
        recursion_strategies = ['top_down', 'bottom_up', 'bidirectional']
        
        for strategy in recursion_strategies:
            # 创建优化器
            optimizer = MultiLevelRecursiveOptimizer(
                optimization_method='standard',
                recursion_strategy=strategy,
                max_recursion_depth=2,
                convergence_threshold=1e-5
            )
            
            # 执行计算
            result = optimizer.compute(input_data)
            
            # 检查结果
            self.assertIn('solution', result)
            self.assertIn('objective_value', result)
            
            # 检查目标函数值是否减小
            initial_objective = self.objective_function(self.initial_solution)
            final_objective = result['objective_value']
            self.assertLess(final_objective, initial_objective)
    
    def test_different_problem_types(self):
        """测试不同的问题类型"""
        # 测试不同的问题类型
        problem_types = ['quadratic', 'rosenbrock', 'rastrigin']
        
        for problem_type in problem_types:
            # 创建测试问题
            problem, objective_function, constraints, initial_solution = create_test_problem(
                problem_type=problem_type,
                dimension=5,
                seed=42
            )
            
            # 创建输入数据
            input_data = {
                'problem': problem,
                'objective_function': objective_function,
                'constraints': constraints,
                'initial_solution': initial_solution
            }
            
            # 创建优化器
            optimizer = MultiLevelRecursiveOptimizer(
                optimization_method='standard',
                recursion_strategy='top_down',
                max_recursion_depth=2,
                convergence_threshold=1e-5
            )
            
            # 执行计算
            result = optimizer.compute(input_data)
            
            # 检查结果
            self.assertIn('solution', result)
            self.assertIn('objective_value', result)
            
            # 检查目标函数值是否减小
            initial_objective = objective_function(initial_solution)
            final_objective = result['objective_value']
            self.assertLessEqual(final_objective, initial_objective * 1.1)  # 允许一些误差
    
    def test_hierarchy_builder(self):
        """测试层次结构构建器"""
        # 创建层次结构构建器
        builder = HierarchyBuilder()
        
        # 构建层次结构
        hierarchy = builder.build_hierarchy(self.problem, self.constraints)
        
        # 检查层次结构
        self.assertIsInstance(hierarchy, Hierarchy)
        self.assertGreater(hierarchy.get_num_nodes(), 1)
        self.assertGreater(hierarchy.get_num_levels(), 1)
        
        # 检查根节点
        root = hierarchy.root
        self.assertIsInstance(root, HierarchyNode)
        self.assertEqual(root.level, 0)
        self.assertTrue(root.is_root())
        
        # 检查叶节点
        leaf_nodes = hierarchy.get_leaf_nodes()
        self.assertGreater(len(leaf_nodes), 0)
        for leaf in leaf_nodes:
            self.assertTrue(leaf.is_leaf())
    
    def test_strategy_selector(self):
        """测试策略选择器"""
        # 创建层次结构构建器
        builder = HierarchyBuilder()
        
        # 构建层次结构
        hierarchy = builder.build_hierarchy(self.problem, self.constraints)
        
        # 获取根节点
        root = hierarchy.root
        
        # 创建策略选择器
        selector = StrategySelector()
        
        # 选择优化策略
        strategy = selector.select_strategy(root, 'standard', 'top_down')
        
        # 检查策略
        self.assertIsInstance(strategy, OptimizationStrategy)
        self.assertEqual(strategy.strategy_type, 'gradient_descent')
        
        # 检查策略参数
        self.assertIn('max_iterations', strategy.parameters)
        self.assertIn('tolerance', strategy.parameters)
    
    def test_optimization_analyzer(self):
        """测试优化分析器"""
        # 创建优化分析器
        analyzer = OptimizationAnalyzer()
        
        # 创建输入数据
        input_data = {
            'problem': self.problem,
            'objective_function': self.objective_function,
            'constraints': self.constraints,
            'initial_solution': self.initial_solution
        }
        
        # 执行计算
        result = self.optimizer.compute(input_data)
        
        # 获取结果
        solution = result['solution']
        objective_value = result['objective_value']
        iterations = result['iterations']
        convergence_history = result['convergence_history']
        hierarchy = result['hierarchy']
        
        # 分析优化结果
        analysis_result = analyzer.analyze_optimization(
            self.problem, self.objective_function, solution, objective_value, 
            iterations, convergence_history, hierarchy
        )
        
        # 检查分析结果
        self.assertIn('convergence_rate', analysis_result)
        self.assertIn('is_converged', analysis_result)
        self.assertIn('convergence_iterations', analysis_result)
        self.assertIn('solution_norm', analysis_result)
        self.assertIn('solution_sparsity', analysis_result)
        self.assertIn('gradient_norm', analysis_result)
        self.assertIn('hierarchy_num_levels', analysis_result)
        self.assertIn('hierarchy_num_nodes', analysis_result)
        self.assertIn('iterations', analysis_result)
        self.assertIn('avg_improvement_per_iteration', analysis_result)
        self.assertIn('convergence_speed', analysis_result)
    
    def test_get_metadata(self):
        """测试获取元数据"""
        metadata = self.optimizer.get_metadata()
        
        self.assertIn('name', metadata)
        self.assertIn('version', metadata)
        self.assertIn('description', metadata)
        self.assertIn('parameters', metadata)
        self.assertIn('performance_metrics', metadata)
    
    def test_is_compatible_with(self):
        """测试兼容性检查"""
        # 创建兼容的优化器
        compatible_optimizer = MultiLevelRecursiveOptimizer(
            optimization_method='standard',
            recursion_strategy='top_down'
        )
        
        # 创建不兼容的优化器
        incompatible_optimizer = MultiLevelRecursiveOptimizer(
            optimization_method='adaptive',
            recursion_strategy='bottom_up'
        )
        
        # 检查兼容性
        self.assertTrue(self.optimizer.is_compatible_with(compatible_optimizer))
        self.assertFalse(self.optimizer.is_compatible_with(incompatible_optimizer))


if __name__ == '__main__':
    unittest.main()
