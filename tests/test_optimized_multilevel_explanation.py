#!/usr/bin/env python3
"""
测试优化的多层次解释生成算子
"""

import os
import sys
import time
import json
import logging
import numpy as np
from typing import Dict, Any, List

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入算子
try:
    from src.operators.explanation import (
        MultilevelExplanationOperator,
        OptimizedMultilevelExplanationOperator,
        RUST_AVAILABLE
    )

    # 如果Rust实现可用，导入Rust实现的算子
    if RUST_AVAILABLE:
        from src.operators.explanation import (
            RustMultilevelExplanationOperator,
            RustOptimizedMultilevelExplanationOperator
        )
except ImportError as e:
    logger.error(f"导入算子失败: {e}")
    sys.exit(1)

# 尝试导入算子注册表
try:
    from src.operators.registry import operator_registry
    REGISTRY_AVAILABLE = True
except ImportError:
    try:
        from operators.registry import operator_registry
        REGISTRY_AVAILABLE = True
    except ImportError:
        REGISTRY_AVAILABLE = False
        logger.warning("无法导入算子注册表，跳过注册表测试")

def generate_test_data(num_samples: int = 10) -> List[Dict[str, Any]]:
    """
    生成测试数据

    参数:
        num_samples: 样本数量

    返回:
        测试数据列表
    """
    return [
        {
            "model_output": {
                "prediction": "positive" if np.random.random() > 0.5 else "negative",
                "confidence": np.random.random(),
                "features": {
                    "text_length": int(np.random.random() * 1000),
                    "sentiment_score": np.random.random(),
                    "topic_relevance": np.random.random()
                }
            },
            "explanation_level": "all",
            "user_expertise": np.random.choice(["beginner", "intermediate", "expert"])
        }
        for _ in range(num_samples)
    ]

def test_python_implementation():
    """测试Python实现"""
    logger.info("测试Python实现...")

    # 创建算子
    operator = MultilevelExplanationOperator(
        tech_level=0.7,
        concept_level=0.5,
        analogy_level=0.3
    )

    # 生成测试数据
    test_data = generate_test_data(1)[0]

    # 测试性能
    start_time = time.time()
    result = operator.apply(test_data)
    end_time = time.time()

    logger.info(f"Python实现耗时: {(end_time - start_time) * 1000:.2f} ms")

    # 检查结果
    assert "technical" in result
    assert "conceptual" in result
    assert "analogy" in result
    assert "fused" in result
    assert "weights" in result

    logger.info("Python实现测试通过")

    return end_time - start_time

def test_rust_implementation():
    """测试Rust实现"""
    if not RUST_AVAILABLE:
        logger.warning("Rust实现不可用，跳过测试")
        return None

    logger.info("测试Rust实现...")

    # 创建算子
    operator = RustMultilevelExplanationOperator(
        tech_level=0.7,
        concept_level=0.5,
        analogy_level=0.3
    )

    # 生成测试数据
    test_data = generate_test_data(1)[0]

    # 测试性能
    start_time = time.time()
    result = operator.apply(test_data)
    end_time = time.time()

    logger.info(f"Rust实现耗时: {(end_time - start_time) * 1000:.2f} ms")

    # 检查结果
    assert "technical" in result
    assert "conceptual" in result
    assert "analogy" in result
    assert "fused" in result
    assert "weights" in result

    logger.info("Rust实现测试通过")

    return end_time - start_time

def test_optimized_python_implementation():
    """测试优化的Python实现"""
    logger.info("测试优化的Python实现...")

    # 创建算子
    operator = OptimizedMultilevelExplanationOperator(
        tech_level=0.7,
        concept_level=0.5,
        analogy_level=0.3,
        parallel_threshold=3,
        use_process_pool=False,
        max_workers=4,
        cache_size=100
    )

    # 生成测试数据
    test_data = generate_test_data(1)[0]

    # 测试性能
    start_time = time.time()
    result = operator.apply(test_data)
    end_time = time.time()

    logger.info(f"优化的Python实现耗时: {(end_time - start_time) * 1000:.2f} ms")

    # 检查结果
    assert "technical" in result
    assert "conceptual" in result
    assert "analogy" in result
    assert "fused" in result
    assert "weights" in result

    # 测试缓存
    logger.info("测试缓存...")

    # 再次运行，应该使用缓存
    start_time = time.time()
    result = operator.apply(test_data)
    end_time = time.time()

    logger.info(f"优化的Python实现（使用缓存）耗时: {(end_time - start_time) * 1000:.2f} ms")

    # 获取缓存统计信息
    cache_stats = operator.get_cache_stats()
    logger.info(f"缓存统计信息: {cache_stats}")

    # 检查缓存命中
    assert cache_stats["cache_hits"] > 0

    logger.info("优化的Python实现测试通过")

    return end_time - start_time

def test_optimized_rust_implementation():
    """测试优化的Rust实现"""
    if not RUST_AVAILABLE:
        logger.warning("Rust实现不可用，跳过测试")
        return None

    logger.info("测试优化的Rust实现...")

    # 创建算子
    operator = RustOptimizedMultilevelExplanationOperator(
        tech_level=0.7,
        concept_level=0.5,
        analogy_level=0.3,
        parallel_threshold=3
    )

    # 生成测试数据
    test_data = generate_test_data(1)[0]

    # 测试性能
    start_time = time.time()
    result = operator.apply(test_data)
    end_time = time.time()

    logger.info(f"优化的Rust实现耗时: {(end_time - start_time) * 1000:.2f} ms")

    # 检查结果
    assert "technical" in result
    assert "conceptual" in result
    assert "analogy" in result
    assert "fused" in result
    assert "weights" in result

    # 测试缓存
    logger.info("测试缓存...")

    # 再次运行，应该使用缓存
    start_time = time.time()
    result = operator.apply(test_data)
    end_time = time.time()

    logger.info(f"优化的Rust实现（使用缓存）耗时: {(end_time - start_time) * 1000:.2f} ms")

    # 获取缓存统计信息
    cache_stats = operator.get_cache_stats()
    logger.info(f"缓存统计信息: {cache_stats}")

    # 检查缓存命中
    assert cache_stats["cache_hits"] > 0

    logger.info("优化的Rust实现测试通过")

    return end_time - start_time

def test_python_batch_processing():
    """测试Python实现的批处理"""
    logger.info("测试Python实现的批处理...")

    # 创建算子
    operator = OptimizedMultilevelExplanationOperator(
        tech_level=0.7,
        concept_level=0.5,
        analogy_level=0.3,
        parallel_threshold=3,
        use_process_pool=False,
        max_workers=4,
        cache_size=100
    )

    # 生成测试数据
    batch_size = 10
    test_batch = generate_test_data(batch_size)

    # 测试性能
    start_time = time.time()
    results = operator.apply_batch(test_batch)
    end_time = time.time()

    logger.info(f"Python实现批处理耗时: {(end_time - start_time) * 1000:.2f} ms")

    # 检查结果
    assert len(results) == batch_size
    for result in results:
        assert "technical" in result
        assert "conceptual" in result
        assert "analogy" in result
        assert "fused" in result
        assert "weights" in result

    logger.info("Python实现批处理测试通过")

def test_rust_batch_processing():
    """测试Rust实现的批处理"""
    if not RUST_AVAILABLE:
        logger.warning("Rust实现不可用，跳过测试")
        return

    logger.info("测试Rust实现的批处理...")

    # 创建算子
    operator = RustOptimizedMultilevelExplanationOperator(
        tech_level=0.7,
        concept_level=0.5,
        analogy_level=0.3,
        parallel_threshold=3
    )

    # 生成测试数据
    batch_size = 10
    test_batch = generate_test_data(batch_size)

    # 测试性能
    start_time = time.time()
    results = operator.apply_batch(test_batch)
    end_time = time.time()

    logger.info(f"Rust实现批处理耗时: {(end_time - start_time) * 1000:.2f} ms")

    # 检查结果
    assert len(results) == batch_size
    for result in results:
        assert "technical" in result
        assert "conceptual" in result
        assert "analogy" in result
        assert "fused" in result
        assert "weights" in result

    logger.info("Rust实现批处理测试通过")

def test_registry():
    """测试算子注册表"""
    if not REGISTRY_AVAILABLE:
        logger.warning("算子注册表不可用，跳过注册表测试")
        return

    logger.info("测试算子注册表...")

    # 从注册表获取算子
    python_operator = operator_registry.get_operator("explanation", "multilevel_explanation")

    # 检查算子是否存在
    assert python_operator is not None

    # 尝试获取优化的多层次解释生成算子
    python_optimized_operator = operator_registry.get_operator("explanation", "optimized_multilevel_explanation")

    # 如果优化的多层次解释生成算子不存在，跳过相关测试
    if python_optimized_operator is None:
        logger.warning("优化的多层次解释生成算子未注册到注册表中，跳过相关测试")
        return

    # 创建算子实例
    python_instance = python_operator()
    python_optimized_instance = python_optimized_operator()

    # 生成测试数据
    test_data = generate_test_data(1)[0]

    # 测试算子
    python_result = python_instance.apply(test_data)
    python_optimized_result = python_optimized_instance.apply(test_data)

    # 检查结果
    assert "technical" in python_result
    assert "technical" in python_optimized_result

    # 如果Rust实现可用，测试Rust算子
    if RUST_AVAILABLE:
        rust_operator = operator_registry.get_operator("explanation", "rust_multilevel_explanation")
        rust_optimized_operator = operator_registry.get_operator("explanation", "rust_optimized_multilevel_explanation")

        assert rust_operator is not None
        assert rust_optimized_operator is not None

        # 创建算子实例
        rust_instance = rust_operator()
        rust_optimized_instance = rust_optimized_operator()

        # 测试算子
        rust_result = rust_instance.apply(test_data)
        rust_optimized_result = rust_optimized_instance.apply(test_data)

        # 检查结果
        assert "technical" in rust_result
        assert "technical" in rust_optimized_result
    else:
        logger.warning("Rust实现不可用，跳过Rust算子测试")

    logger.info("算子注册表测试通过")

def compare_performance():
    """比较性能"""
    logger.info("比较性能...")

    # 测试Python实现
    python_time = test_python_implementation()

    # 测试优化的Python实现
    optimized_python_time = test_optimized_python_implementation()

    # 计算性能提升
    python_optimized_speedup = python_time / optimized_python_time if optimized_python_time else 0

    logger.info(f"优化的Python实现相比基础Python实现的性能提升: {python_optimized_speedup:.2f}x")

    if RUST_AVAILABLE:
        # 测试Rust实现
        rust_time = test_rust_implementation()

        # 测试优化的Rust实现
        optimized_rust_time = test_optimized_rust_implementation()

        # 计算性能提升
        rust_speedup = python_time / rust_time if rust_time else 0
        optimized_rust_speedup = python_time / optimized_rust_time if optimized_rust_time else 0
        rust_optimized_speedup = rust_time / optimized_rust_time if optimized_rust_time and rust_time else 0

        logger.info(f"Rust实现相比Python实现的性能提升: {rust_speedup:.2f}x")
        logger.info(f"优化的Rust实现相比Python实现的性能提升: {optimized_rust_speedup:.2f}x")
        logger.info(f"优化的Rust实现相比基础Rust实现的性能提升: {rust_optimized_speedup:.2f}x")
    else:
        logger.warning("Rust实现不可用，跳过Rust性能比较")

def main():
    """主函数"""
    logger.info("开始测试优化的多层次解释生成算子...")

    # 测试Python实现
    test_python_implementation()

    # 测试优化的Python实现
    test_optimized_python_implementation()

    # 测试Python批处理
    test_python_batch_processing()

    # 如果Rust实现可用，测试Rust实现
    if RUST_AVAILABLE:
        # 测试Rust实现
        test_rust_implementation()

        # 测试优化的Rust实现
        test_optimized_rust_implementation()

        # 测试Rust批处理
        test_rust_batch_processing()

    # 测试算子注册表
    test_registry()

    # 比较性能
    compare_performance()

    logger.info("测试完成")

if __name__ == "__main__":
    main()
