"""
图形渲染测试脚本

本脚本测试图形渲染模块的基本功能。
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt
import importlib.util

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入图形渲染模块
from src.graphics_rendering import (
    Vector3, Color, Texture,
    BasicMaterial, PhongMaterial,
    DirectionalLight, PointLight, AmbientLight,
    Camera,
    WireframeRenderer, Rasterizer,
    SceneObject, Scene,
    create_cube, create_sphere, create_cylinder, create_plane,
    TENSOR_FUSION_AVAILABLE
)

if TENSOR_FUSION_AVAILABLE:
    from src.graphics_rendering import TensorFusionRenderer

def test_vector_operations():
    """测试向量操作"""
    print("\n测试向量操作...")
    
    # 创建向量
    v1 = Vector3(1.0, 2.0, 3.0)
    v2 = Vector3(4.0, 5.0, 6.0)
    
    # 测试向量加法
    v3 = v1 + v2
    print(f"v1 + v2 = {v3}")
    
    # 测试向量减法
    v4 = v2 - v1
    print(f"v2 - v1 = {v4}")
    
    # 测试向量乘以标量
    v5 = v1 * 2.0
    print(f"v1 * 2.0 = {v5}")
    
    # 测试向量除以标量
    v6 = v2 / 2.0
    print(f"v2 / 2.0 = {v6}")
    
    # 测试向量点积
    dot = v1.dot(v2)
    print(f"v1 · v2 = {dot}")
    
    # 测试向量叉积
    cross = v1.cross(v2)
    print(f"v1 × v2 = {cross}")
    
    # 测试向量长度
    length = v1.length()
    print(f"|v1| = {length}")
    
    # 测试向量归一化
    normalized = v1.normalize()
    print(f"normalize(v1) = {normalized}")
    print(f"|normalize(v1)| = {normalized.length()}")
    
    print("向量操作测试完成")

def test_color_operations():
    """测试颜色操作"""
    print("\n测试颜色操作...")
    
    # 创建颜色
    c1 = Color(0.5, 0.6, 0.7)
    c2 = Color(0.2, 0.3, 0.4)
    
    # 测试颜色加法
    c3 = c1 + c2
    print(f"c1 + c2 = {c3}")
    
    # 测试颜色乘以标量
    c4 = c1 * 0.5
    print(f"c1 * 0.5 = {c4}")
    
    # 测试颜色转换为十六进制
    hex_color = c1.to_hex()
    print(f"c1 to hex = {hex_color}")
    
    # 测试从十六进制创建颜色
    c5 = Color.from_hex(hex_color)
    print(f"Color from hex = {c5}")
    
    print("颜色操作测试完成")

def test_mesh_creation():
    """测试网格创建"""
    print("\n测试网格创建...")
    
    # 创建立方体
    cube = create_cube(1.0, "TestCube")
    print(f"立方体: {cube}")
    print(f"顶点数: {cube.vertex_count}")
    print(f"三角形数: {cube.triangle_count}")
    
    # 创建球体
    sphere = create_sphere(1.0, 16, 8, "TestSphere")
    print(f"球体: {sphere}")
    print(f"顶点数: {sphere.vertex_count}")
    print(f"三角形数: {sphere.triangle_count}")
    
    # 创建圆柱体
    cylinder = create_cylinder(1.0, 2.0, 16, "TestCylinder")
    print(f"圆柱体: {cylinder}")
    print(f"顶点数: {cylinder.vertex_count}")
    print(f"三角形数: {cylinder.triangle_count}")
    
    # 创建平面
    plane = create_plane(2.0, 2.0, 2, 2, "TestPlane")
    print(f"平面: {plane}")
    print(f"顶点数: {plane.vertex_count}")
    print(f"三角形数: {plane.triangle_count}")
    
    print("网格创建测试完成")

def test_scene_creation():
    """测试场景创建"""
    print("\n测试场景创建...")
    
    # 创建场景
    scene = Scene("TestScene")
    
    # 创建相机
    camera = Camera(
        position=Vector3(0.0, 0.0, 5.0),
        target=Vector3(0.0, 0.0, 0.0),
        up=Vector3(0.0, 1.0, 0.0),
        fov=60.0,
        aspect_ratio=1.0,
        near=0.1,
        far=1000.0,
        name="MainCamera"
    )
    
    # 添加相机到场景
    scene.add_camera(camera)
    
    # 创建光源
    ambient_light = AmbientLight(Color(0.1, 0.1, 0.1), 0.5, "AmbientLight")
    directional_light = DirectionalLight(Vector3(1.0, -1.0, 1.0).normalize(), Color(1.0, 1.0, 1.0), 0.8, "DirectionalLight")
    point_light = PointLight(Vector3(2.0, 2.0, 2.0), Color(1.0, 0.8, 0.6), 1.0, 10.0, name="PointLight")
    
    # 添加光源到场景
    scene.add_light(ambient_light)
    scene.add_light(directional_light)
    scene.add_light(point_light)
    
    # 创建材质
    red_material = BasicMaterial(Color(1.0, 0.0, 0.0), name="RedMaterial")
    green_material = BasicMaterial(Color(0.0, 1.0, 0.0), name="GreenMaterial")
    blue_material = BasicMaterial(Color(0.0, 0.0, 1.0), name="BlueMaterial")
    yellow_material = BasicMaterial(Color(1.0, 1.0, 0.0), name="YellowMaterial")
    
    # 创建网格
    cube = create_cube(1.0, "Cube")
    sphere = create_sphere(0.8, 16, 8, "Sphere")
    cylinder = create_cylinder(0.5, 1.5, 16, "Cylinder")
    plane = create_plane(5.0, 5.0, 1, 1, "Plane")
    
    # 创建场景对象
    cube_obj = SceneObject(cube, red_material, Vector3(-1.5, 0.0, 0.0), Vector3(0.0, 45.0, 0.0), Vector3(1.0, 1.0, 1.0), "CubeObject")
    sphere_obj = SceneObject(sphere, green_material, Vector3(1.5, 0.0, 0.0), Vector3(0.0, 0.0, 0.0), Vector3(1.0, 1.0, 1.0), "SphereObject")
    cylinder_obj = SceneObject(cylinder, blue_material, Vector3(0.0, 1.5, 0.0), Vector3(45.0, 0.0, 0.0), Vector3(1.0, 1.0, 1.0), "CylinderObject")
    plane_obj = SceneObject(plane, yellow_material, Vector3(0.0, -1.0, 0.0), Vector3(-90.0, 0.0, 0.0), Vector3(1.0, 1.0, 1.0), "PlaneObject")
    
    # 添加对象到场景
    scene.add_object(cube_obj)
    scene.add_object(sphere_obj)
    scene.add_object(cylinder_obj)
    scene.add_object(plane_obj)
    
    print(f"场景: {scene}")
    print(f"对象数: {len(scene.objects)}")
    print(f"光源数: {len(scene.lights)}")
    print(f"相机数: {len(scene.cameras)}")
    
    return scene

def test_wireframe_renderer(scene: Scene):
    """测试线框渲染器"""
    print("\n测试线框渲染器...")
    
    # 创建输出目录
    os.makedirs("output", exist_ok=True)
    
    # 创建线框渲染器
    renderer = WireframeRenderer(400, 400, Color(1.0, 1.0, 1.0), "TestWireframeRenderer")
    
    # 渲染场景
    result = renderer.render(scene)
    
    # 保存渲染结果
    result.save_to_file("output/wireframe_render.png")
    
    # 显示渲染结果
    plt.figure(figsize=(8, 8))
    plt.imshow(result.color_buffer)
    plt.title("线框渲染")
    plt.axis('off')
    plt.savefig("output/wireframe_render_plot.png")
    plt.close()
    
    print("线框渲染器测试完成")

def test_rasterizer(scene: Scene):
    """测试光栅化渲染器"""
    print("\n测试光栅化渲染器...")
    
    # 创建输出目录
    os.makedirs("output", exist_ok=True)
    
    # 创建光栅化渲染器
    renderer = Rasterizer(400, 400, Color(0.2, 0.2, 0.2), Color(0.1, 0.1, 0.1), "TestRasterizer")
    
    # 渲染场景
    result = renderer.render(scene)
    
    # 保存渲染结果
    result.save_to_file("output/rasterizer_render.png")
    
    # 显示渲染结果
    plt.figure(figsize=(8, 8))
    plt.imshow(result.color_buffer)
    plt.title("光栅化渲染")
    plt.axis('off')
    plt.savefig("output/rasterizer_render_plot.png")
    plt.close()
    
    print("光栅化渲染器测试完成")

def test_tensor_fusion_renderer(scene: Scene):
    """测试超越态张量融合渲染器"""
    if not TENSOR_FUSION_AVAILABLE:
        print("\n超越态张量融合算法不可用，跳过测试")
        return
    
    print("\n测试超越态张量融合渲染器...")
    
    # 创建输出目录
    os.makedirs("output", exist_ok=True)
    
    # 创建基础渲染器
    base_renderer = Rasterizer(400, 400, Color(0.2, 0.2, 0.2), Color(0.1, 0.1, 0.1))
    
    # 创建超越态张量融合渲染器
    renderer = TensorFusionRenderer(400, 400, "standard", base_renderer, "TestTensorFusionRenderer")
    
    # 渲染场景
    result = renderer.render(scene)
    
    # 保存渲染结果
    result.save_to_file("output/tensor_fusion_render.png")
    
    # 显示渲染结果
    plt.figure(figsize=(8, 8))
    plt.imshow(result.color_buffer)
    plt.title("超越态张量融合渲染")
    plt.axis('off')
    plt.savefig("output/tensor_fusion_render_plot.png")
    plt.close()
    
    print("超越态张量融合渲染器测试完成")

def main():
    """主函数"""
    print("开始测试图形渲染模块...")
    
    # 测试向量操作
    test_vector_operations()
    
    # 测试颜色操作
    test_color_operations()
    
    # 测试网格创建
    test_mesh_creation()
    
    # 测试场景创建
    scene = test_scene_creation()
    
    # 测试线框渲染器
    test_wireframe_renderer(scene)
    
    # 测试光栅化渲染器
    test_rasterizer(scene)
    
    # 测试超越态张量融合渲染器
    test_tensor_fusion_renderer(scene)
    
    print("\n所有测试完成")

if __name__ == "__main__":
    main()
