"""
超越态思维引擎核心数据结构测试

这个模块测试超越态思维引擎的核心数据结构，包括TranscendentalState和DistributedNode。
"""

import unittest
import numpy as np
import time
import pickle
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 直接导入我们实现的模块，避免导入其他未实现的模块
from src.core.transcendental_state import (
    TranscendentalState,
    ComputationalTranscendentalState,
    MemoryTranscendentalState,
    CreativeTranscendentalState,
    create_transcendental_state
)

from src.core.distributed_node import (
    DistributedNode,
    ComputeNode,
    StorageNode,
    NetworkNode,
    Task,
    ComputeTask,
    DataTransferTask
)


class TestTranscendentalState(unittest.TestCase):
    """测试TranscendentalState类"""

    def setUp(self):
        """测试前的准备工作"""
        # 创建一个简单的超越态
        self.data = np.random.random((10, 10))
        self.state = TranscendentalState(self.data)

    def test_creation(self):
        """测试创建超越态"""
        # 测试基本属性
        self.assertIsNotNone(self.state.state_id)
        self.assertEqual(self.state.state_type, "standard")
        self.assertEqual(self.state.get_dimension(), 100)

        # 测试从数组创建
        state2 = TranscendentalState.from_array(self.data)
        self.assertEqual(state2.get_dimension(), 100)

        # 测试从另一个超越态创建
        state3 = TranscendentalState(self.state)
        self.assertEqual(state3.get_dimension(), 100)

        # 测试工厂函数
        state4 = create_transcendental_state(self.data)
        self.assertEqual(state4.get_dimension(), 100)

    def test_to_array(self):
        """测试转换为数组"""
        array = self.state.to_array()
        self.assertTrue(np.array_equal(array, self.data))

    def test_entropy(self):
        """测试熵计算"""
        entropy = self.state.get_entropy()
        self.assertGreaterEqual(entropy, 0.0)

        # 测试均匀分布的熵
        uniform_data = np.ones((10, 10)) / 100
        uniform_state = TranscendentalState(uniform_data)
        uniform_entropy = uniform_state.get_entropy()
        self.assertAlmostEqual(uniform_entropy, np.log2(100), places=5)

        # 测试确定性分布的熵
        deterministic_data = np.zeros((10, 10))
        deterministic_data[0, 0] = 1.0
        deterministic_state = TranscendentalState(deterministic_data)
        deterministic_entropy = deterministic_state.get_entropy()
        self.assertAlmostEqual(deterministic_entropy, 0.0, places=5)

    def test_fuse(self):
        """测试融合操作"""
        # 创建另一个超越态
        data2 = np.random.random((10, 10))
        state2 = TranscendentalState(data2)

        # 融合
        fused_state = self.state.fuse(state2)

        # 检查融合结果
        self.assertEqual(fused_state.get_dimension(), 100)
        self.assertIn("fusion_source", fused_state.metadata)

        # 检查融合强度参数
        fused_state2 = self.state.fuse(state2, fusion_strength=0.5)
        self.assertNotEqual(fused_state.to_array().sum(), fused_state2.to_array().sum())

        # 检查非线性参数
        fused_state3 = self.state.fuse(state2, nonlinearity=0.0)
        expected_array = self.state.to_array() + state2.to_array()
        self.assertTrue(np.allclose(fused_state3.to_array(), expected_array))

    def test_evolve(self):
        """测试演化操作"""
        # 演化
        evolved_state = self.state.evolve(0.1)

        # 检查演化结果
        self.assertEqual(evolved_state.get_dimension(), 100)
        self.assertIn("evolution_source", evolved_state.metadata)

        # 检查时间步长参数
        evolved_state2 = self.state.evolve(0.2)
        self.assertNotEqual(evolved_state.to_array().sum(), evolved_state2.to_array().sum())

    def test_serialize_deserialize(self):
        """测试序列化和反序列化"""
        # 序列化
        serialized = self.state.serialize()
        self.assertIsInstance(serialized, bytes)

        # 反序列化
        deserialized = TranscendentalState.deserialize(serialized)
        self.assertEqual(deserialized.state_id, self.state.state_id)
        self.assertEqual(deserialized.state_type, self.state.state_type)
        self.assertEqual(deserialized.get_dimension(), self.state.get_dimension())
        self.assertTrue(np.array_equal(deserialized.to_array(), self.state.to_array()))

    def test_specialized_states(self):
        """测试特殊类型的超越态"""
        # 计算型超越态
        comp_state = ComputationalTranscendentalState(self.data, computation_capacity=2.0)
        self.assertEqual(comp_state.state_type, "computational")
        self.assertEqual(comp_state.computation_capacity, 2.0)

        # 记忆型超越态
        mem_state = MemoryTranscendentalState(self.data, storage_capacity=3.0)
        self.assertEqual(mem_state.state_type, "memory")
        self.assertEqual(mem_state.storage_capacity, 3.0)

        # 创造型超越态
        creative_state = CreativeTranscendentalState(self.data, creativity_level=1.5)
        self.assertEqual(creative_state.state_type, "creative")
        self.assertEqual(creative_state.creativity_level, 1.5)

        # 测试特殊方法
        accessed_data = mem_state.access()
        self.assertTrue(np.array_equal(accessed_data, self.data))
        self.assertEqual(mem_state.access_count, 1)

        generated_data = creative_state.generate()
        self.assertEqual(generated_data.shape, self.data.shape)


class TestDistributedNode(unittest.TestCase):
    """测试DistributedNode类"""

    def setUp(self):
        """测试前的准备工作"""
        # 创建几个不同类型的节点
        self.node = DistributedNode(node_id="node1")
        self.compute_node = ComputeNode(node_id="compute1", computation_capacity=2.0)
        self.storage_node = StorageNode(node_id="storage1", storage_capacity=3.0)
        self.network_node = NetworkNode(node_id="network1", bandwidth_capacity=4.0)

    def test_creation(self):
        """测试创建节点"""
        # 测试基本属性
        self.assertEqual(self.node.get_id(), "node1")
        self.assertEqual(self.node.node_type, "standard")

        # 测试特殊节点类型
        self.assertEqual(self.compute_node.node_type, "compute")
        self.assertEqual(self.storage_node.node_type, "storage")
        self.assertEqual(self.network_node.node_type, "network")

        # 测试能力
        self.assertEqual(self.compute_node.capabilities["computation"], 2.0)
        self.assertEqual(self.storage_node.capabilities["memory"], 3.0)
        self.assertEqual(self.network_node.capabilities["bandwidth"], 4.0)

        # 测试支持的任务类型
        self.assertIn("compute", self.compute_node.capabilities["supported_tasks"])
        self.assertIn("data_transfer", self.storage_node.capabilities["supported_tasks"])
        self.assertIn("routing", self.network_node.capabilities["supported_tasks"])

    def test_connections(self):
        """测试节点连接"""
        # 添加连接
        self.assertTrue(self.node.add_connection(self.compute_node))
        self.assertTrue(self.node.add_connection(self.storage_node))

        # 获取连接
        connections = self.node.get_connections()
        self.assertEqual(len(connections), 2)

        # 移除连接
        self.assertTrue(self.node.remove_connection("compute1"))
        connections = self.node.get_connections()
        self.assertEqual(len(connections), 1)

        # 测试不能连接到自己
        self.assertFalse(self.node.add_connection(self.node))

    def test_task_execution(self):
        """测试任务执行"""
        # 创建一个简单任务
        task = Task(task_id="task1", task_type="standard")

        # 执行任务
        result = self.node.execute_task(task)
        self.assertEqual(result["task_id"], "task1")
        self.assertEqual(result["status"], "completed")

        # 检查任务历史
        self.assertIn("task1", self.node.task_history)

    def test_compute_task(self):
        """测试计算任务"""
        # 创建一个计算函数
        def add_numbers(a, b):
            return a + b

        # 创建计算任务
        task = ComputeTask(
            computation_function=add_numbers,
            computation_args={"a": 2, "b": 3}
        )

        # 执行任务
        result = self.compute_node.execute_task(task)
        self.assertEqual(result, 5)

    def test_data_transfer(self):
        """测试数据传输"""
        # 创建数据传输任务
        data = {"key": "value"}
        task = DataTransferTask(
            source_node_id="node1",
            target_node_id="storage1",
            data=data
        )

        # 执行任务
        result = self.storage_node.execute_task(task)
        self.assertEqual(result["status"], "completed")

        # 检查资源需求
        self.assertIn("bandwidth", task.get_resource_requirements())

    def test_storage_operations(self):
        """测试存储操作"""
        # 存储数据
        self.assertTrue(self.storage_node.store_data("key1", "value1"))

        # 检索数据
        self.assertEqual(self.storage_node.retrieve_data("key1"), "value1")

        # 删除数据
        self.assertTrue(self.storage_node.delete_data("key1"))
        self.assertIsNone(self.storage_node.retrieve_data("key1"))

    def test_network_operations(self):
        """测试网络操作"""
        # 更新路由表
        self.network_node.update_routing_table("storage1", "compute1")

        # 获取下一跳
        self.assertEqual(self.network_node.get_next_hop("storage1"), "compute1")

        # 添加连接
        self.network_node.add_connection(self.compute_node)

        # 路由数据
        result = self.network_node.route_data("node1", "compute1", "test_data")
        self.assertEqual(result["status"], "routed")
        self.assertEqual(result["next_hop"], "compute1")


if __name__ == "__main__":
    unittest.main()
