"""批处理和自定义模态测试"""

import unittest
import numpy as np
import networkx as nx
from pathlib import Path
import shutil
import tempfile

from examples.batch_processing_example import BatchFusionProcessor
from examples.custom_modality_example import (
    GraphModalityEncoder,
    CustomGraphFusion
)

class TestBatchProcessing(unittest.TestCase):
    """批处理功能测试"""
    
    def setUp(self):
        # 创建临时目录
        self.test_dir = tempfile.mkdtemp()
        self.processor = BatchFusionProcessor(output_dir=self.test_dir)
        
    def tearDown(self):
        # 清理临时目录
        shutil.rmtree(self.test_dir)
        
    def test_batch_processing(self):
        """测试批量处理功能"""
        # 创建测试数据
        batch_data = {
            "quantum": np.random.rand(5, 4) + 1j * np.random.rand(5, 4),
            "holographic": np.random.rand(5, 4, 4),
            "fractal": np.random.rand(5, 8, 8)
        }
        
        # 处理批次
        results = self.processor.process_batch(batch_data)
        
        # 验证结果
        self.assertTrue(len(results) > 0)
        self.assertTrue(all(r["success"] for r in results))
        self.assertTrue(all(0 <= r["fusion_quality"] <= 1 for r in results))
        
    def test_progress_tracking(self):
        """测试进度跟踪"""
        # 处理多个批次
        for _ in range(3):
            batch_data = {
                "quantum": np.random.rand(5, 4),
                "holographic": np.random.rand(5, 4, 4),
                "fractal": np.random.rand(5, 8, 8)
            }
            self.processor.process_batch(batch_data)
            
        # 验证统计信息
        self.assertEqual(self.processor.stats["processed"], 3)
        self.assertGreater(self.processor.stats["succeeded"], 0)
        self.assertGreater(len(self.processor.stats["qualities"]), 0)
        
    def test_report_generation(self):
        """测试报告生成"""
        # 处理一些数据
        batch_data = {
            "quantum": np.random.rand(5, 4),
            "holographic": np.random.rand(5, 4, 4),
            "fractal": np.random.rand(5, 8, 8)
        }
        self.processor.process_batch(batch_data)
        
        # 生成报告
        self.processor._generate_report()
        
        # 验证报告文件
        report_file = Path(self.test_dir) / "processing_report.txt"
        self.assertTrue(report_file.exists())
        
        # 验证图表
        plot_file = Path(self.test_dir) / "quality_distribution.png"
        self.assertTrue(plot_file.exists())

class TestCustomGraphModality(unittest.TestCase):
    """自定义图模态测试"""
    
    def setUp(self):
        self.encoder = GraphModalityEncoder(dimension=16)
        self.fusion = CustomGraphFusion()
        
    def test_graph_encoding(self):
        """测试图编码"""
        # 创建测试图
        graph = nx.random_geometric_graph(10, 0.3)
        
        # 编码
        features = self.encoder.encode(graph)
        
        # 验证编码结果
        self.assertEqual(len(features), 16)
        self.assertTrue(np.all(np.isfinite(features)))
        
    def test_spectral_features(self):
        """测试谱特征提取"""
        # 创建具有明确结构的图
        graph = nx.cycle_graph(6)  # 环图
        
        # 使用谱方法的编码器
        encoder = GraphModalityEncoder(dimension=16, use_spectral=True)
        features1 = encoder.encode(graph)
        
        # 不使用谱方法的编码器
        encoder = GraphModalityEncoder(dimension=16, use_spectral=False)
        features2 = encoder.encode(graph)
        
        # 验证特征差异
        self.assertFalse(np.allclose(features1, features2))
        
    def test_graph_fusion(self):
        """测试图数据融合"""
        # 创建测试数据
        quantum_data = np.random.rand(4) + 1j * np.random.rand(4)
        quantum_data = quantum_data / np.linalg.norm(quantum_data)
        graph = nx.random_geometric_graph(15, 0.2)
        
        # 执行融合
        result = self.fusion.process_data(quantum_data, graph)
        
        # 验证结果
        self.assertTrue(result["success"])
        self.assertTrue(0 <= result["fusion_quality"] <= 1)
        self.assertIsNotNone(result["fused_data"])
        
    def test_large_graph(self):
        """测试大规模图处理"""
        # 创建大规模图
        graph = nx.random_geometric_graph(100, 0.1)
        
        # 编码
        features = self.encoder.encode(graph)
        
        # 验证结果
        self.assertEqual(len(features), 16)
        self.assertTrue(np.all(np.isfinite(features)))
        
    def test_error_handling(self):
        """测试错误处理"""
        # 测试空图
        empty_graph = nx.Graph()
        features = self.encoder.encode(empty_graph)
        self.assertEqual(len(features), 16)
        
        # 测试无效的量子数据
        invalid_quantum = np.zeros(10)  # 维度不匹配
        graph = nx.cycle_graph(5)
        
        result = self.fusion.process_data(invalid_quantum, graph)
        self.assertFalse(result["success"])
        
if __name__ == "__main__":
    unittest.main()