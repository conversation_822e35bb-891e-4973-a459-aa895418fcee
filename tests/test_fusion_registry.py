"""
超越态融合算子注册测试脚本

本脚本用于测试超越态融合算子的注册和使用。
"""

import sys
import os
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# 导入融合算子注册模块
try:
    from src.operators.fusion_registry import (
        quantum_superposition_fusion,
        holographic_interference_fusion,
        fractal_fusion,
        topological_fusion,
        fusion_with_method,
        get_fusion_operator,
        get_fusion_operator_by_name,
        REGISTRY_TYPE
    )
    print(f"成功导入融合算子注册模块，注册表类型: {REGISTRY_TYPE}")
except ImportError as e:
    print(f"导入融合算子注册模块失败: {e}")
    sys.exit(1)

def test_fusion_operators():
    """测试融合算子"""
    # 创建测试状态
    state_a = [1.0 + 0.0j, 0.0 + 0.0j]  # |0⟩
    state_b = [0.0 + 0.0j, 1.0 + 0.0j]  # |1⟩
    
    print("\n测试融合算子:")
    print("状态A:", state_a)
    print("状态B:", state_b)
    
    # 测试量子叠加融合
    try:
        result_qs = quantum_superposition_fusion(state_a, state_b)
        print("\n量子叠加融合结果:", result_qs)
        print("结果概率:", [abs(c)**2 for c in result_qs])
    except Exception as e:
        print(f"量子叠加融合测试失败: {e}")
    
    # 测试全息干涉融合
    try:
        result_hi = holographic_interference_fusion(state_a, state_b)
        print("\n全息干涉融合结果:", result_hi)
        print("结果概率:", [abs(c)**2 for c in result_hi])
    except Exception as e:
        print(f"全息干涉融合测试失败: {e}")
    
    # 测试分形融合
    try:
        result_ff = fractal_fusion(state_a, state_b)
        print("\n分形融合结果:", result_ff)
        print("结果概率:", [abs(c)**2 for c in result_ff])
    except Exception as e:
        print(f"分形融合测试失败: {e}")
    
    # 测试拓扑融合
    try:
        result_tf = topological_fusion(state_a, state_b)
        print("\n拓扑融合结果:", result_tf)
        print("结果概率:", [abs(c)**2 for c in result_tf])
    except Exception as e:
        print(f"拓扑融合测试失败: {e}")
    
    # 测试通用融合算子
    try:
        methods = [
            "quantum_superposition",
            "holographic_interference",
            "fractal_fusion",
            "topological_fusion",
        ]
        
        for method in methods:
            result = fusion_with_method(state_a, state_b, method)
            print(f"\n通用融合算子 ({method}) 结果:", result)
            print("结果概率:", [abs(c)**2 for c in result])
    except Exception as e:
        print(f"通用融合算子测试失败: {e}")
    
    return True

def test_fusion_operator_registry():
    """测试融合算子注册表"""
    print("\n测试融合算子注册表:")
    
    # 测试获取融合算子
    try:
        # 获取融合算子实例
        fusion_op = get_fusion_operator()
        print("成功获取融合算子实例")
        
        # 测试融合方法
        state_a = np.array([1.0, 0.0], dtype=np.complex128)
        state_b = np.array([0.0, 1.0], dtype=np.complex128)
        
        result = fusion_op.quantum_superposition(state_a, state_b)
        print("量子叠加融合结果:", result)
    except Exception as e:
        print(f"获取融合算子实例失败: {e}")
    
    # 测试通过名称获取融合算子
    try:
        # 尝试获取量子叠加融合算子
        qs_fusion = get_fusion_operator_by_name("fusion.quantum_superposition")
        if qs_fusion:
            print("成功获取量子叠加融合算子")
            
            # 测试算子
            result = qs_fusion([1.0 + 0.0j, 0.0 + 0.0j], [0.0 + 0.0j, 1.0 + 0.0j])
            print("量子叠加融合结果:", result)
        else:
            print("获取量子叠加融合算子失败: 返回None")
    except Exception as e:
        print(f"通过名称获取融合算子失败: {e}")
    
    return True

if __name__ == "__main__":
    print("开始测试超越态融合算子注册...")
    
    # 测试融合算子
    test_fusion_operators()
    
    # 测试融合算子注册表
    test_fusion_operator_registry()
    
    print("\n测试完成")
