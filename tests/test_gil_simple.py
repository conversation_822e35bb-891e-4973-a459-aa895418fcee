import time
import threading

# 创建一个计算密集型函数
def compute_intensive(id):
    print(f"Thread {id} starting...")
    start = time.time()
    
    # 执行计算密集型操作
    result = 0
    for i in range(50000000):  # 5千万次循环
        result += i
    
    duration = time.time() - start
    print(f"Thread {id} finished in {duration:.2f} seconds")
    return duration

def run_threads(num_threads):
    threads = []
    start = time.time()
    
    # 创建并启动线程
    for i in range(num_threads):
        t = threading.Thread(target=compute_intensive, args=(i,))
        threads.append(t)
        t.start()
    
    # 等待所有线程完成
    for t in threads:
        t.join()
    
    total_time = time.time() - start
    print(f"\nAll {num_threads} threads completed in {total_time:.2f} seconds")
    return total_time

if __name__ == "__main__":
    print("Testing Python threading performance")
    
    # 测试不同数量的线程
    for num_threads in [1, 2, 4]:
        print(f"\n--- Testing with {num_threads} threads ---")
        run_threads(num_threads)
