"""多模态融合编码系统测试"""

import unittest
import numpy as np
from src.transcendental_tensor.multimodal_fusion.multimodal_fusion_operator import (
    MultimodalFusionOperator,
    ModalityType,
    ModalityConfig
)

class TestMultimodalFusion(unittest.TestCase):
    """多模态融合测试类"""
    
    def setUp(self):
        """测试准备"""
        self.fusion_op = MultimodalFusionOperator()
        
        # 注册测试模态
        self.fusion_op.register_modality(
            "quantum",
            ModalityConfig(
                type=ModalityType.QUANTUM,
                dimension=8,
                encoding_params={"encoding_type": "standard"}
            )
        )
        
        self.fusion_op.register_modality(
            "holographic",
            ModalityConfig(
                type=ModalityType.HOLOGRAPHIC,
                dimension=16,
                encoding_params={"field_type": "scalar"}
            )
        )
        
        self.fusion_op.register_modality(
            "fractal",
            ModalityConfig(
                type=ModalityType.FRACTAL,
                dimension=12,
                encoding_params={"max_scales": 3}
            )
        )
        
    def test_quantum_encoding(self):
        """测试量子态编码"""
        # 准备测试数据
        data = np.random.rand(4) + 1j * np.random.rand(4)
        
        # 编码
        result = self.fusion_op.encode_modality(data, "quantum")
        
        # 验证
        self.assertTrue(result["success"])
        self.assertEqual(result["modality"], "quantum")
        self.assertEqual(result["encoded_data"].shape, (8,))
        self.assertTrue(np.allclose(np.linalg.norm(result["encoded_data"]), 1.0))
        
    def test_holographic_encoding(self):
        """测试全息场编码"""
        # 准备测试数据
        data = np.random.rand(4, 4)
        
        # 编码
        result = self.fusion_op.encode_modality(data, "holographic")
        
        # 验证
        self.assertTrue(result["success"])
        self.assertEqual(result["modality"], "holographic")
        self.assertEqual(result["encoded_data"].shape, (16,))
        
    def test_fractal_encoding(self):
        """测试分形模式编码"""
        # 准备测试数据
        data = np.random.rand(8, 8)
        
        # 编码
        result = self.fusion_op.encode_modality(data, "fractal")
        
        # 验证
        self.assertTrue(result["success"])
        self.assertEqual(result["modality"], "fractal")
        self.assertEqual(result["encoded_data"].shape, (12,))
        
    def test_fusion(self):
        """测试模态融合"""
        # 准备测试数据
        quantum_data = np.random.rand(4) + 1j * np.random.rand(4)
        holographic_data = np.random.rand(4, 4)
        fractal_data = np.random.rand(8, 8)
        
        # 编码
        encoded_quantum = self.fusion_op.encode_modality(quantum_data, "quantum")
        encoded_holographic = self.fusion_op.encode_modality(holographic_data, "holographic")
        encoded_fractal = self.fusion_op.encode_modality(fractal_data, "fractal")
        
        # 融合
        fusion_result = self.fusion_op.fuse_modalities([
            encoded_quantum,
            encoded_holographic,
            encoded_fractal
        ])
        
        # 验证
        self.assertTrue(fusion_result["success"])
        self.assertGreater(fusion_result["fusion_quality"], 0.0)
        self.assertEqual(len(fusion_result["metadata"]["modalities"]), 3)
        
    def test_fusion_error_handling(self):
        """测试融合错误处理"""
        # 测试单个模态融合
        with self.assertRaises(ValueError):
            self.fusion_op.fuse_modalities([
                {"success": True, "modality": "quantum", "encoded_data": np.zeros(8)}
            ])
            
        # 测试无效模态
        with self.assertRaises(ValueError):
            self.fusion_op.encode_modality(np.zeros(4), "invalid_modality")
            
    def test_modality_registration(self):
        """测试模态注册"""
        # 测试重复注册
        success = self.fusion_op.register_modality(
            "quantum",
            ModalityConfig(type=ModalityType.QUANTUM, dimension=8)
        )
        self.assertFalse(success)
        
        # 测试新模态注册
        success = self.fusion_op.register_modality(
            "semantic",
            ModalityConfig(type=ModalityType.SEMANTIC, dimension=32)
        )
        self.assertTrue(success)
        
if __name__ == '__main__':
    unittest.main()