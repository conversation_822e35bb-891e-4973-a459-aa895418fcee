"""
多层次解释生成算子测试脚本
"""

import sys
import os
import unittest
import time
import numpy as np

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

from src.operators.explanation.multilevel_explanation import MultilevelExplanationOperator

# 检查Rust实现是否可用
try:
    from src.operators.explanation.rust_wrapper import RustMultilevelExplanationOperator, RUST_AVAILABLE
except ImportError:
    RUST_AVAILABLE = False


class TestMultilevelExplanationOperator(unittest.TestCase):
    """多层次解释生成算子测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建Python实现的算子
        self.py_operator = MultilevelExplanationOperator(
            tech_level=0.7,
            concept_level=0.8,
            analogy_level=0.6,
            user_model={
                'technical_expertise': 0.6,
                'domain_knowledge': 0.7,
                'learning_style': 'balanced'
            }
        )
        
        # 如果Rust实现可用，创建Rust实现的算子
        if RUST_AVAILABLE:
            self.rust_operator = RustMultilevelExplanationOperator(
                tech_level=0.7,
                concept_level=0.8,
                analogy_level=0.6,
                user_model={
                    'technical_expertise': 0.6,
                    'domain_knowledge': 0.7,
                    'learning_style': 'balanced'
                }
            )
        
        # 准备测试数据
        self.test_data = {
            'state': {
                'variables': {
                    'x': 10,
                    'y': 20,
                    'z': 30
                }
            },
            'action': {
                'type': 'transform',
                'parameters': {
                    'matrix': [[0.8, -0.6, 0.0], [0.6, 0.8, 0.0], [0.0, 0.0, 1.0]],
                    'offset': [1.0, 2.0, 3.0]
                }
            }
        }
    
    def test_python_implementation(self):
        """测试Python实现"""
        # 应用算子
        result = self.py_operator.apply(self.test_data)
        
        # 验证结果
        self.assertIn('technical', result)
        self.assertIn('conceptual', result)
        self.assertIn('analogy', result)
        self.assertIn('fused', result)
        self.assertIn('weights', result)
        
        # 验证权重
        weights = result['weights']
        self.assertIn('technical', weights)
        self.assertIn('conceptual', weights)
        self.assertIn('analogy', weights)
        
        # 验证技术层面解释
        tech_explanation = result['technical']
        self.assertIn('trace', tech_explanation)
        self.assertIn('justification', tech_explanation)
        self.assertIn('counterfactual', tech_explanation)
        
        # 验证概念层面解释
        concept_explanation = result['conceptual']
        self.assertIn('trace', concept_explanation)
        self.assertIn('justification', concept_explanation)
        self.assertIn('counterfactual', concept_explanation)
        
        # 验证类比层面解释
        analogy_explanation = result['analogy']
        self.assertIn('trace', analogy_explanation)
        self.assertIn('justification', analogy_explanation)
        self.assertIn('counterfactual', analogy_explanation)
        
        # 验证融合解释
        fused_explanation = result['fused']
        self.assertIn('trace', fused_explanation)
        self.assertIn('justification', fused_explanation)
        self.assertIn('counterfactual', fused_explanation)
    
    @unittest.skipIf(not RUST_AVAILABLE, "Rust implementation not available")
    def test_rust_implementation(self):
        """测试Rust实现"""
        # 应用算子
        result = self.rust_operator.apply(self.test_data)
        
        # 验证结果
        self.assertIn('technical', result)
        self.assertIn('conceptual', result)
        self.assertIn('analogy', result)
        self.assertIn('fused', result)
        self.assertIn('weights', result)
        
        # 验证权重
        weights = result['weights']
        self.assertIn('technical', weights)
        self.assertIn('conceptual', weights)
        self.assertIn('analogy', weights)
        
        # 验证技术层面解释
        tech_explanation = result['technical']
        self.assertIn('trace', tech_explanation)
        self.assertIn('justification', tech_explanation)
        self.assertIn('counterfactual', tech_explanation)
        
        # 验证概念层面解释
        concept_explanation = result['conceptual']
        self.assertIn('trace', concept_explanation)
        self.assertIn('justification', concept_explanation)
        self.assertIn('counterfactual', concept_explanation)
        
        # 验证类比层面解释
        analogy_explanation = result['analogy']
        self.assertIn('trace', analogy_explanation)
        self.assertIn('justification', analogy_explanation)
        self.assertIn('counterfactual', analogy_explanation)
        
        # 验证融合解释
        fused_explanation = result['fused']
        self.assertIn('trace', fused_explanation)
        self.assertIn('justification', fused_explanation)
        self.assertIn('counterfactual', fused_explanation)
    
    @unittest.skipIf(not RUST_AVAILABLE, "Rust implementation not available")
    def test_performance_comparison(self):
        """比较Python和Rust实现的性能"""
        # 准备大量测试数据
        test_data_list = []
        for i in range(100):
            test_data = {
                'state': {
                    'variables': {
                        'x': i * 0.1,
                        'y': i * 0.2,
                        'z': i * 0.3
                    }
                },
                'action': {
                    'type': 'transform',
                    'parameters': {
                        'matrix': [[0.8, -0.6, 0.0], [0.6, 0.8, 0.0], [0.0, 0.0, 1.0]],
                        'offset': [1.0, 2.0, 3.0]
                    }
                }
            }
            test_data_list.append(test_data)
        
        # 测量Python实现的性能
        py_start_time = time.time()
        for test_data in test_data_list:
            self.py_operator.apply(test_data)
        py_end_time = time.time()
        py_elapsed_time = py_end_time - py_start_time
        
        # 测量Rust实现的性能
        rust_start_time = time.time()
        for test_data in test_data_list:
            self.rust_operator.apply(test_data)
        rust_end_time = time.time()
        rust_elapsed_time = rust_end_time - rust_start_time
        
        # 输出性能比较结果
        print(f"Python implementation: {py_elapsed_time:.4f} seconds")
        print(f"Rust implementation: {rust_elapsed_time:.4f} seconds")
        print(f"Speedup: {py_elapsed_time / rust_elapsed_time:.2f}x")
        
        # 验证Rust实现比Python实现快
        self.assertLess(rust_elapsed_time, py_elapsed_time)
    
    @unittest.skipIf(not RUST_AVAILABLE, "Rust implementation not available")
    def test_user_model_customization(self):
        """测试用户模型自定义"""
        # 创建不同的用户模型
        technical_user_model = {
            'technical_expertise': 0.9,
            'domain_knowledge': 0.7,
            'learning_style': 'technical'
        }
        
        conceptual_user_model = {
            'technical_expertise': 0.5,
            'domain_knowledge': 0.9,
            'learning_style': 'conceptual'
        }
        
        analogical_user_model = {
            'technical_expertise': 0.3,
            'domain_knowledge': 0.4,
            'learning_style': 'analogical'
        }
        
        # 使用不同的用户模型生成解释
        technical_result = self.rust_operator.apply(self.test_data, user_model=technical_user_model)
        conceptual_result = self.rust_operator.apply(self.test_data, user_model=conceptual_user_model)
        analogical_result = self.rust_operator.apply(self.test_data, user_model=analogical_user_model)
        
        # 验证权重不同
        self.assertNotEqual(technical_result['weights']['technical'], conceptual_result['weights']['technical'])
        self.assertNotEqual(technical_result['weights']['technical'], analogical_result['weights']['technical'])
        self.assertNotEqual(conceptual_result['weights']['technical'], analogical_result['weights']['technical'])
        
        # 验证技术用户模型的技术权重最高
        self.assertGreater(technical_result['weights']['technical'], conceptual_result['weights']['technical'])
        self.assertGreater(technical_result['weights']['technical'], analogical_result['weights']['technical'])
        
        # 验证概念用户模型的概念权重最高
        self.assertGreater(conceptual_result['weights']['conceptual'], technical_result['weights']['conceptual'])
        self.assertGreater(conceptual_result['weights']['conceptual'], analogical_result['weights']['conceptual'])
        
        # 验证类比用户模型的类比权重最高
        self.assertGreater(analogical_result['weights']['analogy'], technical_result['weights']['analogy'])
        self.assertGreater(analogical_result['weights']['analogy'], conceptual_result['weights']['analogy'])


if __name__ == '__main__':
    unittest.main()
