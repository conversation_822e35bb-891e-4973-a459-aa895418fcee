"""
错误处理算子注册测试

本测试脚本测试错误处理算子的注册和使用。
"""

import sys
import os
import unittest
from unittest.mock import MagicMock, patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入错误处理算子
from src.transcendental_tensor.error_handling import (
    ErrorSeverity, ErrorCategory, ErrorHandlingStrategy,
    ErrorContext, ErrorInfo, ErrorHandlingResult, ErrorHandlingConfig
)

# 导入注册表
from src.rust_bindings.operator_registry import (
    OperatorCategory,
    register_operator,
    get_global_registry,
    get_operator,
    list_operators,
    get_operator_metadata,
    check_compatibility
)

# 导入错误处理算子注册
from src.rust_bindings.operator_registry.error_handling import (
    register_error_handling_operator,
    get_error_handling_operator_from_registry
)


class TestErrorHandlingRegistry(unittest.TestCase):
    """错误处理算子注册测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 注册错误处理算子
        register_error_handling_operator()
        
        # 创建测试错误
        self.test_error = ValueError("Test error")
        
        # 创建测试上下文
        self.test_context = ErrorContext(
            component="test",
            operation="test_operation"
        )
    
    def test_register_error_handling_operator(self):
        """测试注册错误处理算子"""
        # 获取注册表
        registry = get_global_registry()
        
        # 检查算子是否已注册
        operators = list_operators(OperatorCategory.UTILITY)
        self.assertIn((OperatorCategory.UTILITY.value, "error_handling"), operators)
        
        # 获取算子元数据
        metadata = get_operator_metadata(OperatorCategory.UTILITY, "error_handling")
        self.assertIsNotNone(metadata)
        self.assertEqual(metadata.name, "error_handling")
        self.assertEqual(metadata.version, "1.0.0")
        self.assertIn("error_handling", metadata.tags)
    
    def test_get_error_handling_operator(self):
        """测试获取错误处理算子"""
        # 获取算子
        operator = get_operator(OperatorCategory.UTILITY, "error_handling")
        self.assertIsNotNone(operator)
        
        # 检查算子类型
        from src.interfaces.operator import OperatorInterface
        self.assertIsInstance(operator, OperatorInterface)
        
        # 检查算子元数据
        metadata = operator.get_metadata()
        self.assertIsNotNone(metadata)
        self.assertIn("name", metadata)
        self.assertIn("type", metadata)
        self.assertIn("description", metadata)
    
    def test_error_handling_operator_from_registry(self):
        """测试从注册表获取错误处理算子"""
        # 获取算子
        operator = get_error_handling_operator_from_registry(
            max_retries=5,
            retry_delay=0.5
        )
        self.assertIsNotNone(operator)
        
        # 检查参数
        params = operator.get_parameters()
        self.assertEqual(params.get("max_retries"), 5)
        self.assertEqual(params.get("retry_delay"), 0.5)
    
    def test_error_handling_operator_apply(self):
        """测试错误处理算子应用"""
        # 获取算子
        operator = get_error_handling_operator_from_registry()
        self.assertIsNotNone(operator)
        
        # 应用算子
        result = operator.apply(
            self.test_error,
            context=self.test_context,
            strategy=ErrorHandlingStrategy.SUPPRESS
        )
        
        # 验证结果
        self.assertIsInstance(result, ErrorHandlingResult)
        self.assertEqual(result.strategy, ErrorHandlingStrategy.SUPPRESS)
        self.assertTrue(result.success)
        self.assertEqual(result.error_info.error_type, "ValueError")
        self.assertEqual(result.error_info.error_message, "Test error")
    
    def test_error_handling_operator_compatibility(self):
        """测试错误处理算子兼容性"""
        # 获取算子
        operator = get_error_handling_operator_from_registry()
        self.assertIsNotNone(operator)
        
        # 创建模拟算子
        mock_operator = MagicMock()
        mock_operator.get_metadata.return_value = {"name": "mock_operator"}
        
        # 检查兼容性
        self.assertTrue(operator.is_compatible_with(mock_operator))
    
    def test_error_handling_operator_performance(self):
        """测试错误处理算子性能"""
        # 获取算子
        operator = get_error_handling_operator_from_registry()
        self.assertIsNotNone(operator)
        
        # 应用算子
        operator.apply(
            self.test_error,
            context=self.test_context,
            strategy=ErrorHandlingStrategy.SUPPRESS
        )
        
        # 获取性能指标
        metrics = operator.get_performance_metrics()
        self.assertIsNotNone(metrics)
        self.assertIn("handled_errors", metrics)
        self.assertGreaterEqual(metrics["handled_errors"], 1)


if __name__ == "__main__":
    unittest.main()
