"""
解释质量评估算子测试脚本
"""

import sys
import os
import unittest
import time
import numpy as np

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

from src.operators.explanation.multilevel_explanation import MultilevelExplanationOperator
from src.operators.explanation.explanation_quality import ExplanationQualityOperator

# 检查Rust实现是否可用
try:
    from src.operators.explanation.rust_wrapper import (
        RustMultilevelExplanationOperator,
        RustExplanationQualityOperator,
        RUST_AVAILABLE
    )
except ImportError:
    RUST_AVAILABLE = False


class TestExplanationQualityOperator(unittest.TestCase):
    """解释质量评估算子测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建Python实现的算子
        self.py_explanation_operator = MultilevelExplanationOperator(
            tech_level=0.7,
            concept_level=0.8,
            analogy_level=0.6,
            user_model={
                'technical_expertise': 0.6,
                'domain_knowledge': 0.7,
                'learning_style': 'balanced'
            }
        )
        
        self.py_quality_operator = ExplanationQualityOperator(
            completeness_weight=0.3,
            consistency_weight=0.3,
            comprehensibility_weight=0.4,
            reference_model={}
        )
        
        # 如果Rust实现可用，创建Rust实现的算子
        if RUST_AVAILABLE:
            self.rust_explanation_operator = RustMultilevelExplanationOperator(
                tech_level=0.7,
                concept_level=0.8,
                analogy_level=0.6,
                user_model={
                    'technical_expertise': 0.6,
                    'domain_knowledge': 0.7,
                    'learning_style': 'balanced'
                }
            )
            
            self.rust_quality_operator = RustExplanationQualityOperator(
                completeness_weight=0.3,
                consistency_weight=0.3,
                comprehensibility_weight=0.4,
                reference_model={}
            )
        
        # 准备测试数据
        self.test_data = {
            'state': {
                'variables': {
                    'x': 10,
                    'y': 20,
                    'z': 30
                }
            },
            'action': {
                'type': 'transform',
                'parameters': {
                    'matrix': [[0.8, -0.6, 0.0], [0.6, 0.8, 0.0], [0.0, 0.0, 1.0]],
                    'offset': [1.0, 2.0, 3.0]
                }
            }
        }
    
    def test_python_implementation(self):
        """测试Python实现"""
        # 生成解释
        explanation = self.py_explanation_operator.apply(self.test_data)
        
        # 评估解释质量
        quality = self.py_quality_operator.apply(explanation)
        
        # 验证结果
        self.assertIn('completeness', quality)
        self.assertIn('consistency', quality)
        self.assertIn('comprehensibility', quality)
        self.assertIn('quality', quality)
        
        # 验证评分范围
        self.assertGreaterEqual(quality['completeness'], 0.0)
        self.assertLessEqual(quality['completeness'], 1.0)
        self.assertGreaterEqual(quality['consistency'], 0.0)
        self.assertLessEqual(quality['consistency'], 1.0)
        self.assertGreaterEqual(quality['comprehensibility'], 0.0)
        self.assertLessEqual(quality['comprehensibility'], 1.0)
        self.assertGreaterEqual(quality['quality'], 0.0)
        self.assertLessEqual(quality['quality'], 1.0)
    
    @unittest.skipIf(not RUST_AVAILABLE, "Rust implementation not available")
    def test_rust_implementation(self):
        """测试Rust实现"""
        # 生成解释
        explanation = self.rust_explanation_operator.apply(self.test_data)
        
        # 评估解释质量
        quality = self.rust_quality_operator.apply(explanation)
        
        # 验证结果
        self.assertIn('completeness', quality)
        self.assertIn('consistency', quality)
        self.assertIn('comprehensibility', quality)
        self.assertIn('quality', quality)
        
        # 验证评分范围
        self.assertGreaterEqual(quality['completeness'], 0.0)
        self.assertLessEqual(quality['completeness'], 1.0)
        self.assertGreaterEqual(quality['consistency'], 0.0)
        self.assertLessEqual(quality['consistency'], 1.0)
        self.assertGreaterEqual(quality['comprehensibility'], 0.0)
        self.assertLessEqual(quality['comprehensibility'], 1.0)
        self.assertGreaterEqual(quality['quality'], 0.0)
        self.assertLessEqual(quality['quality'], 1.0)
    
    @unittest.skipIf(not RUST_AVAILABLE, "Rust implementation not available")
    def test_performance_comparison(self):
        """比较Python和Rust实现的性能"""
        # 生成解释
        py_explanation = self.py_explanation_operator.apply(self.test_data)
        rust_explanation = self.rust_explanation_operator.apply(self.test_data)
        
        # 准备大量测试数据
        test_data_list = []
        for i in range(100):
            test_data_list.append(py_explanation)
        
        # 测量Python实现的性能
        py_start_time = time.time()
        for test_data in test_data_list:
            self.py_quality_operator.apply(test_data)
        py_end_time = time.time()
        py_elapsed_time = py_end_time - py_start_time
        
        # 测量Rust实现的性能
        rust_start_time = time.time()
        for test_data in test_data_list:
            self.rust_quality_operator.apply(test_data)
        rust_end_time = time.time()
        rust_elapsed_time = rust_end_time - rust_start_time
        
        # 输出性能比较结果
        print(f"Python implementation: {py_elapsed_time:.4f} seconds")
        print(f"Rust implementation: {rust_elapsed_time:.4f} seconds")
        print(f"Speedup: {py_elapsed_time / rust_elapsed_time:.2f}x")
        
        # 验证Rust实现比Python实现快
        self.assertLess(rust_elapsed_time, py_elapsed_time)
    
    @unittest.skipIf(not RUST_AVAILABLE, "Rust implementation not available")
    def test_reference_model_customization(self):
        """测试参考模型自定义"""
        # 生成解释
        explanation = self.rust_explanation_operator.apply(self.test_data)
        
        # 创建不同的参考模型
        reference_model1 = {
            'required_components': ['trace', 'justification', 'counterfactual'],
            'min_completeness': 0.7,
            'min_consistency': 0.7,
            'min_comprehensibility': 0.7
        }
        
        reference_model2 = {
            'required_components': ['trace', 'justification'],
            'min_completeness': 0.5,
            'min_consistency': 0.5,
            'min_comprehensibility': 0.5
        }
        
        # 使用不同的参考模型评估解释质量
        quality1 = self.rust_quality_operator.apply(explanation, reference_model=reference_model1)
        quality2 = self.rust_quality_operator.apply(explanation, reference_model=reference_model2)
        
        # 验证结果
        self.assertIn('completeness', quality1)
        self.assertIn('consistency', quality1)
        self.assertIn('comprehensibility', quality1)
        self.assertIn('quality', quality1)
        
        self.assertIn('completeness', quality2)
        self.assertIn('consistency', quality2)
        self.assertIn('comprehensibility', quality2)
        self.assertIn('quality', quality2)
    
    @unittest.skipIf(not RUST_AVAILABLE, "Rust implementation not available")
    def test_weight_customization(self):
        """测试权重自定义"""
        # 生成解释
        explanation = self.rust_explanation_operator.apply(self.test_data)
        
        # 创建不同的权重配置
        weights1 = {
            'completeness_weight': 0.6,
            'consistency_weight': 0.2,
            'comprehensibility_weight': 0.2
        }
        
        weights2 = {
            'completeness_weight': 0.2,
            'consistency_weight': 0.6,
            'comprehensibility_weight': 0.2
        }
        
        weights3 = {
            'completeness_weight': 0.2,
            'consistency_weight': 0.2,
            'comprehensibility_weight': 0.6
        }
        
        # 创建不同权重配置的算子
        quality_operator1 = RustExplanationQualityOperator(**weights1)
        quality_operator2 = RustExplanationQualityOperator(**weights2)
        quality_operator3 = RustExplanationQualityOperator(**weights3)
        
        # 评估解释质量
        quality1 = quality_operator1.apply(explanation)
        quality2 = quality_operator2.apply(explanation)
        quality3 = quality_operator3.apply(explanation)
        
        # 验证结果
        self.assertIn('quality', quality1)
        self.assertIn('quality', quality2)
        self.assertIn('quality', quality3)
        
        # 验证不同权重配置产生不同的质量评分
        # 注意：这个测试可能会失败，因为不同权重配置可能产生相同的质量评分
        # 但这种情况很少见
        self.assertNotEqual(quality1['quality'], quality2['quality'])
        self.assertNotEqual(quality1['quality'], quality3['quality'])
        self.assertNotEqual(quality2['quality'], quality3['quality'])


if __name__ == '__main__':
    unittest.main()
