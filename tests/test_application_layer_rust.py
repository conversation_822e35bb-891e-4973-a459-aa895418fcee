#!/usr/bin/env python3
"""
应用层Rust实现测试脚本

这个脚本测试应用层的Rust实现，验证其基本功能是否正常工作。
"""

import os
import sys
import time
import json
import uuid
import logging
import unittest
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.distributed.layers.physical import PhysicalLayer
from src.distributed.layers.data import DataLayer
from src.distributed.layers.computation import ComputationLayer
from src.distributed.layers.coordination import CoordinationLayer
from src.distributed.layers.application import ApplicationLayer
from src.distributed.layers.application.service_registry import ServiceStatus
from src.distributed.layers.application.resource_manager import ResourceType
from src.distributed.layers.application.load_balancer import BalancingStrategy

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

class TestApplicationLayerRust(unittest.TestCase):
    """测试应用层Rust实现"""
    
    def setUp(self):
        """测试前准备"""
        self.node_id = str(uuid.uuid4())
        
        # 创建物理层
        self.physical_layer = PhysicalLayer(
            node_id=self.node_id,
            use_rust=True
        )
        
        # 创建数据层
        self.data_layer = DataLayer(
            node_id=self.node_id,
            physical_layer=self.physical_layer,
            use_rust=True
        )
        
        # 创建计算层
        self.computation_layer = ComputationLayer(
            node_id=self.node_id,
            physical_layer=self.physical_layer,
            data_layer=self.data_layer,
            use_rust=True
        )
        
        # 创建协调层
        self.coordination_layer = CoordinationLayer(
            node_id=self.node_id,
            physical_layer=self.physical_layer,
            data_layer=self.data_layer,
            computation_layer=self.computation_layer,
            use_rust=True
        )
        
        # 创建应用层
        self.application_layer = ApplicationLayer(
            node_id=self.node_id,
            physical_layer=self.physical_layer,
            data_layer=self.data_layer,
            computation_layer=self.computation_layer,
            coordination_layer=self.coordination_layer,
            use_rust=True
        )
        
        # 检查是否使用了Rust实现
        self.assertTrue(hasattr(self.application_layer, '_rust_layer'), 
                        "Rust implementation not available")
        
        # 启动各层
        self.physical_layer.start()
        self.data_layer.start()
        self.computation_layer.start()
        self.coordination_layer.start()
        self.application_layer.start()
    
    def tearDown(self):
        """测试后清理"""
        # 停止各层
        self.application_layer.stop()
        self.coordination_layer.stop()
        self.computation_layer.stop()
        self.data_layer.stop()
        self.physical_layer.stop()
    
    def test_register_unregister_service(self):
        """测试注册和注销服务"""
        # 注册服务
        service_name = f"test_service_{uuid.uuid4()}"
        service_info = {
            "endpoints": {
                "http": "http://localhost:8080",
                "grpc": "localhost:50051"
            },
            "metadata": {
                "version": "1.0.0",
                "weight": 10
            }
        }
        
        registered = self.application_layer.register_service(service_name, service_info)
        
        # 验证注册成功
        self.assertTrue(registered)
        
        # 发现服务
        discovered_service = self.application_layer.discover_service(service_name)
        
        # 验证发现成功
        self.assertIsNotNone(discovered_service)
        self.assertEqual(discovered_service.service_name, service_name)
        self.assertEqual(discovered_service.node_id, self.node_id)
        self.assertEqual(discovered_service.endpoints["http"], "http://localhost:8080")
        self.assertEqual(discovered_service.endpoints["grpc"], "localhost:50051")
        
        # 注销服务
        unregistered = self.application_layer.unregister_service(service_name)
        
        # 验证注销成功
        self.assertTrue(unregistered)
        
        # 尝试再次发现服务
        discovered_service = self.application_layer.discover_service(service_name)
        
        # 验证服务已被注销
        self.assertIsNone(discovered_service)
    
    def test_allocate_release_resource(self):
        """测试分配和释放资源"""
        # 分配资源
        resource_type = ResourceType.COMPUTE
        resource_info = {
            "capacity": {
                "cpu": 4,
                "memory": 8192
            },
            "usage": {
                "cpu": 0,
                "memory": 0
            },
            "metadata": {
                "location": "us-west",
                "priority": "high"
            }
        }
        
        resource_id = self.application_layer.allocate_resource(resource_type, resource_info)
        
        # 验证分配成功
        self.assertIsNotNone(resource_id)
        
        # 获取资源
        resource = self.application_layer.get_resource(resource_id)
        
        # 验证资源信息
        self.assertIsNotNone(resource)
        self.assertEqual(resource.resource_id, resource_id)
        self.assertEqual(resource.resource_type, resource_type)
        self.assertEqual(resource.node_id, self.node_id)
        self.assertEqual(resource.capacity["cpu"], 4)
        
        # 获取指定类型的资源
        resources = self.application_layer.get_resources_by_type(resource_type)
        
        # 验证资源列表
        self.assertEqual(len(resources), 1)
        self.assertEqual(resources[0].resource_id, resource_id)
        
        # 更新资源使用情况
        updated_usage = {
            "cpu": 2,
            "memory": 4096
        }
        
        updated = self.application_layer.update_resource_usage(resource_id, updated_usage)
        
        # 验证更新成功
        self.assertTrue(updated)
        
        # 获取更新后的资源
        resource = self.application_layer.get_resource(resource_id)
        
        # 验证使用情况已更新
        self.assertEqual(resource.usage["cpu"], 2)
        self.assertEqual(resource.usage["memory"], 4096)
        
        # 释放资源
        released = self.application_layer.release_resource(resource_id)
        
        # 验证释放成功
        self.assertTrue(released)
        
        # 尝试再次获取资源
        resource = self.application_layer.get_resource(resource_id)
        
        # 验证资源已被释放
        self.assertIsNone(resource)
    
    def test_load_balancer(self):
        """测试负载均衡器"""
        # 注册多个服务实例
        service_name = f"test_service_{uuid.uuid4()}"
        
        # 实例1
        service_info1 = {
            "endpoints": {"http": "http://server1:8080"},
            "metadata": {"weight": 10}
        }
        self.application_layer.register_service(service_name, service_info1)
        
        # 实例2
        service_info2 = {
            "endpoints": {"http": "http://server2:8080"},
            "metadata": {"weight": 20}
        }
        
        # 创建第二个节点
        node_id2 = str(uuid.uuid4())
        physical_layer2 = PhysicalLayer(node_id=node_id2, use_rust=True)
        data_layer2 = DataLayer(node_id=node_id2, physical_layer=physical_layer2, use_rust=True)
        computation_layer2 = ComputationLayer(node_id=node_id2, physical_layer=physical_layer2, data_layer=data_layer2, use_rust=True)
        coordination_layer2 = CoordinationLayer(node_id=node_id2, physical_layer=physical_layer2, data_layer=data_layer2, computation_layer=computation_layer2, use_rust=True)
        application_layer2 = ApplicationLayer(node_id=node_id2, physical_layer=physical_layer2, data_layer=data_layer2, computation_layer=computation_layer2, coordination_layer=coordination_layer2, use_rust=True)
        
        # 启动各层
        physical_layer2.start()
        data_layer2.start()
        computation_layer2.start()
        coordination_layer2.start()
        application_layer2.start()
        
        # 注册服务
        application_layer2.register_service(service_name, service_info2)
        
        # 等待服务发现
        time.sleep(1)
        
        try:
            # 使用轮询策略选择服务
            service1 = self.application_layer.select_service(service_name, BalancingStrategy.ROUND_ROBIN)
            service2 = self.application_layer.select_service(service_name, BalancingStrategy.ROUND_ROBIN)
            
            # 验证轮询结果
            self.assertIsNotNone(service1)
            self.assertIsNotNone(service2)
            self.assertNotEqual(service1.node_id, service2.node_id)
            
            # 使用随机策略选择服务
            service = self.application_layer.select_service(service_name, BalancingStrategy.RANDOM)
            
            # 验证随机结果
            self.assertIsNotNone(service)
            
            # 使用最少连接策略选择服务
            service = self.application_layer.select_service(service_name, BalancingStrategy.LEAST_CONNECTIONS)
            
            # 验证最少连接结果
            self.assertIsNotNone(service)
            
            # 增加连接计数
            self.application_layer.increment_connection(service_name, service.node_id)
            
            # 再次使用最少连接策略选择服务
            service2 = self.application_layer.select_service(service_name, BalancingStrategy.LEAST_CONNECTIONS)
            
            # 验证选择了不同的服务
            self.assertIsNotNone(service2)
            self.assertNotEqual(service.node_id, service2.node_id)
            
            # 使用加权策略选择服务
            services = []
            for _ in range(30):
                service = self.application_layer.select_service(service_name, BalancingStrategy.WEIGHTED)
                services.append(service.node_id)
            
            # 验证加权结果（节点2的权重是节点1的两倍，应该被选择的次数更多）
            count1 = services.count(self.node_id)
            count2 = services.count(node_id2)
            
            # 由于随机性，我们只能大致验证比例
            self.assertGreater(count2, count1)
        finally:
            # 停止第二个节点的各层
            application_layer2.stop()
            coordination_layer2.stop()
            computation_layer2.stop()
            data_layer2.stop()
            physical_layer2.stop()

if __name__ == '__main__':
    unittest.main()
