import time
import threading
import sys

def cpu_bound_task(n):
    """CPU密集型任务"""
    result = 0
    for i in range(n):
        result += i * i
    return result

def run_threads(num_threads, task_size):
    threads = []
    start_time = time.time()
    
    for _ in range(num_threads):
        t = threading.Thread(target=cpu_bound_task, args=(task_size,))
        threads.append(t)
        t.start()
    
    for t in threads:
        t.join()
    
    end_time = time.time()
    return end_time - start_time

if __name__ == "__main__":
    print(f"Python版本: {sys.version}")
    print(f"GIL启用状态: {'启用' if sys._is_gil_enabled() else '禁用'}")
    
    # 测试不同线程数的性能
    for num_threads in [1, 2, 4, 8, 16]:
        time_taken = run_threads(num_threads, 50_000_000)
        print(f"{num_threads}个线程耗时: {time_taken:.4f}秒")
