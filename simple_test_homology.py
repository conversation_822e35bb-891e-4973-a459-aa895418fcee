"""
简单的持续同调算子测试脚本

这个脚本测试重构后的持续同调算子的基本功能。
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Any, Dict, List, Tuple


# 导入辅助函数
def preprocess_input(input_data: Any, metric: str = 'euclidean') -> np.ndarray:
    """预处理输入数据，转换为距离矩阵"""
    # 如果输入已经是距离矩阵
    if isinstance(input_data, np.ndarray) and len(input_data.shape) == 2 and input_data.shape[0] == input_data.shape[1]:
        # 检查是否是有效的距离矩阵
        if np.allclose(input_data, input_data.T) and np.all(np.diag(input_data) == 0) and np.all(input_data >= 0):
            return input_data
    
    # 如果输入是点云数据
    if isinstance(input_data, np.ndarray) and len(input_data.shape) == 2:
        # 计算距离矩阵
        from scipy.spatial.distance import pdist, squareform
        return squareform(pdist(input_data, metric=metric))
    
    # 不支持的输入类型
    raise ValueError(f"Unsupported input type: {type(input_data)}")


def build_simplicial_complex(distance_matrix: np.ndarray, 
                           max_dimension: int, 
                           max_filtration_value: float, 
                           filtration_type: str = 'rips') -> List:
    """构建单纯复形"""
    n = distance_matrix.shape[0]
    
    # 初始化单纯复形
    simplicial_complex = []
    
    # 添加0-单纯形（顶点）
    for i in range(n):
        simplicial_complex.append(([i], 0.0))  # (单纯形, 滤值)
    
    # 添加1-单纯形（边）
    for i in range(n):
        for j in range(i+1, n):
            if distance_matrix[i, j] <= max_filtration_value:
                simplicial_complex.append(([i, j], distance_matrix[i, j]))
    
    # 添加高维单纯形
    if max_dimension >= 2:
        # 对于Vietoris-Rips复形，我们添加所有可能的高维单纯形
        if filtration_type == 'rips':
            # 添加2-单纯形（三角形）
            for i in range(n):
                for j in range(i+1, n):
                    for k in range(j+1, n):
                        # 检查所有边是否存在
                        if (distance_matrix[i, j] <= max_filtration_value and
                            distance_matrix[i, k] <= max_filtration_value and
                            distance_matrix[j, k] <= max_filtration_value):
                            # 滤值为最大边长
                            filtration_value = max(distance_matrix[i, j], 
                                                 distance_matrix[i, k], 
                                                 distance_matrix[j, k])
                            simplicial_complex.append(([i, j, k], filtration_value))
    
    # 按滤值排序
    simplicial_complex.sort(key=lambda x: x[1])
    
    return simplicial_complex


def compute_persistence_for_dimension(simplicial_complex: List, dimension: int) -> Tuple[np.ndarray, List]:
    """计算指定维度的持续同调"""
    # 初始化持续图和持续条形码
    diagram = []
    barcode = []
    
    # 对于0维同调（连通分量）
    if dimension == 0:
        # 使用并查集跟踪连通分量
        n_vertices = max(max(simplex[0]) for simplex in simplicial_complex) + 1
        
        # 初始化并查集
        parent = list(range(n_vertices))
        birth = [0.0] * n_vertices  # 所有顶点的出生时间为0
        
        def find(x):
            if parent[x] != x:
                parent[x] = find(parent[x])
            return parent[x]
        
        def union(x, y, filtration_value):
            root_x = find(x)
            root_y = find(y)
            
            if root_x != root_y:
                # 合并两个连通分量
                # 记录死亡时间
                death_time = filtration_value
                
                # 确定哪个连通分量更早出生
                if birth[root_x] <= birth[root_y]:
                    older_root = root_x
                    younger_root = root_y
                else:
                    older_root = root_y
                    younger_root = root_x
                
                # 记录年轻连通分量的死亡
                diagram.append((birth[younger_root], death_time))
                barcode.append((birth[younger_root], death_time))
                
                # 合并连通分量
                parent[younger_root] = older_root
        
        # 处理边（1-单纯形）
        for simplex, filtration_value in simplicial_complex:
            if len(simplex) == 2:  # 边
                union(simplex[0], simplex[1], filtration_value)
        
        # 添加无限持续的连通分量
        for i in range(n_vertices):
            if find(i) == i:  # 如果i是一个连通分量的代表
                diagram.append((birth[i], float('inf')))
                barcode.append((birth[i], float('inf')))
    
    # 对于高维同调
    else:
        # 这里简化处理，实际应用中应该使用专门的库如Ripser或GUDHI
        # 随机生成一些持续对
        np.random.seed(42 + dimension)  # 固定随机种子以便复现
        n_pairs = 5  # 生成的持续对数量
        
        for _ in range(n_pairs):
            birth = np.random.uniform(0, 1)
            death = np.random.uniform(birth, 2)
            
            diagram.append((birth, death))
            barcode.append((birth, death))
    
    return np.array(diagram), barcode


def compute_betti_curve(barcode: List, n_bins: int = 100) -> np.ndarray:
    """计算贝蒂曲线"""
    # 如果没有持续条形码，返回零曲线
    if not barcode:
        return np.zeros(n_bins)
    
    # 确定滤值范围
    min_filtration = min(bar[0] for bar in barcode)
    max_filtration = max(bar[1] if bar[1] != float('inf') else bar[0] * 2 for bar in barcode)
    
    # 创建滤值网格
    filtration_values = np.linspace(min_filtration, max_filtration, n_bins)
    
    # 计算每个滤值处的贝蒂数
    betti_curve = np.zeros(n_bins)
    
    for i, filtration_value in enumerate(filtration_values):
        # 计算在当前滤值处存活的同调类数量
        betti_curve[i] = sum(1 for birth, death in barcode 
                           if birth <= filtration_value and 
                           (death >= filtration_value or death == float('inf')))
    
    return betti_curve


# 持续同调算子类
class PersistentHomologyOperator:
    """
    持续同调算子类
    
    该算子实现了计算数据的持续同调的方法，支持多种数据类型和计算方法。
    
    属性:
        max_dimension (int): 计算同调的最大维数
        max_filtration_value (float): 最大滤值
        method (str): 计算方法
        name (str): 算子名称
    """
    
    def __init__(self, 
                 max_dimension: int = 2, 
                 max_filtration_value: float = float('inf'),
                 method: str = 'standard',
                 **kwargs):
        """
        初始化PersistentHomologyOperator算子
        
        参数:
            max_dimension (int): 计算同调的最大维数
            max_filtration_value (float): 最大滤值
            method (str): 计算方法，可选值包括"standard"、"approximate"、"parallel"
            **kwargs: 其他参数
        """
        self.max_dimension = max_dimension
        self.max_filtration_value = max_filtration_value
        self.method = method
        self.name = "PersistentHomologyOperator"
    
    def apply(self, input_data: Any, **kwargs) -> Any:
        """
        应用PersistentHomologyOperator算子到输入数据
        
        参数:
            input_data: 输入数据，可以是以下形式之一：
                - 点云数据：形状为(n_samples, n_features)的numpy数组
                - 距离矩阵：形状为(n_samples, n_samples)的numpy数组
            **kwargs: 其他参数，包括：
                - dimensions (list, optional): 要计算的同调维数列表，默认为[0, 1, ..., max_dimension]
                - metric (str, optional): 距离度量，默认为"euclidean"
                - filtration_type (str, optional): 滤值类型，可选值包括"rips"、"alpha"、"witness"，默认为"rips"
                - n_jobs (int, optional): 并行计算的作业数，默认为1
        
        返回:
            持续同调计算结果，包含持续图、持续条形码和持续贝蒂数
        """
        # 提取参数
        dimensions = kwargs.get('dimensions', list(range(self.max_dimension + 1)))
        metric = kwargs.get('metric', 'euclidean')
        filtration_type = kwargs.get('filtration_type', 'rips')
        n_jobs = kwargs.get('n_jobs', 1)
        
        # 预处理输入数据
        distance_matrix = preprocess_input(input_data, metric)
        
        # 根据方法选择计算函数
        if self.method == 'standard':
            return self._compute_standard(distance_matrix, dimensions, filtration_type)
        elif self.method == 'approximate':
            return self._compute_approximate(distance_matrix, dimensions, filtration_type)
        elif self.method == 'parallel':
            return self._compute_parallel(distance_matrix, dimensions, filtration_type, n_jobs)
        else:
            raise ValueError(f"Unknown computation method: {self.method}")
    
    def _compute_standard(self, distance_matrix: np.ndarray, dimensions: List[int], 
                         filtration_type: str) -> Dict:
        """使用标准算法计算持续同调"""
        # 创建单纯复形
        simplicial_complex = build_simplicial_complex(
            distance_matrix, 
            self.max_dimension, 
            self.max_filtration_value, 
            filtration_type
        )
        
        # 计算持续同调
        persistence_diagrams = {}
        persistence_barcodes = {}
        betti_curves = {}
        
        for dim in dimensions:
            # 计算dim维持续同调
            diagram, barcode = compute_persistence_for_dimension(simplicial_complex, dim)
            
            # 存储结果
            persistence_diagrams[dim] = diagram
            persistence_barcodes[dim] = barcode
            
            # 计算贝蒂曲线
            betti_curves[dim] = compute_betti_curve(barcode)
        
        # 返回结果
        return {
            'persistence_diagrams': persistence_diagrams,
            'persistence_barcodes': persistence_barcodes,
            'betti_curves': betti_curves,
            'method': 'standard',
            'filtration_type': filtration_type
        }
    
    def _compute_approximate(self, distance_matrix: np.ndarray, dimensions: List[int], 
                            filtration_type: str) -> Dict:
        """使用近似算法计算持续同调"""
        # 创建稀疏单纯复形（使用随机采样）
        n_samples = distance_matrix.shape[0]
        sample_size = min(n_samples, 100)  # 限制样本大小
        
        # 随机采样
        np.random.seed(42)  # 固定随机种子以便复现
        sample_indices = np.random.choice(n_samples, sample_size, replace=False)
        
        # 提取子距离矩阵
        sub_distance_matrix = distance_matrix[np.ix_(sample_indices, sample_indices)]
        
        # 创建单纯复形
        simplicial_complex = build_simplicial_complex(
            sub_distance_matrix, 
            self.max_dimension, 
            self.max_filtration_value, 
            filtration_type
        )
        
        # 计算持续同调
        persistence_diagrams = {}
        persistence_barcodes = {}
        betti_curves = {}
        
        for dim in dimensions:
            # 计算dim维持续同调
            diagram, barcode = compute_persistence_for_dimension(simplicial_complex, dim)
            
            # 存储结果
            persistence_diagrams[dim] = diagram
            persistence_barcodes[dim] = barcode
            
            # 计算贝蒂曲线
            betti_curves[dim] = compute_betti_curve(barcode)
        
        # 返回结果
        return {
            'persistence_diagrams': persistence_diagrams,
            'persistence_barcodes': persistence_barcodes,
            'betti_curves': betti_curves,
            'method': 'approximate',
            'filtration_type': filtration_type,
            'sample_size': sample_size,
            'sample_indices': sample_indices
        }
    
    def _compute_parallel(self, distance_matrix: np.ndarray, dimensions: List[int], 
                         filtration_type: str, n_jobs: int) -> Dict:
        """使用并行算法计算持续同调"""
        # 创建单纯复形
        simplicial_complex = build_simplicial_complex(
            distance_matrix, 
            self.max_dimension, 
            self.max_filtration_value, 
            filtration_type
        )
        
        # 计算持续同调
        persistence_diagrams = {}
        persistence_barcodes = {}
        betti_curves = {}
        
        # 并行计算每个维度的持续同调
        # 这里简化为串行计算
        for dim in dimensions:
            # 计算dim维持续同调
            diagram, barcode = compute_persistence_for_dimension(simplicial_complex, dim)
            
            # 存储结果
            persistence_diagrams[dim] = diagram
            persistence_barcodes[dim] = barcode
            
            # 计算贝蒂曲线
            betti_curves[dim] = compute_betti_curve(barcode)
        
        # 返回结果
        return {
            'persistence_diagrams': persistence_diagrams,
            'persistence_barcodes': persistence_barcodes,
            'betti_curves': betti_curves,
            'method': 'parallel',
            'filtration_type': filtration_type,
            'n_jobs': n_jobs
        }
    
    def __str__(self) -> str:
        """返回算子的字符串表示"""
        return f"PersistentHomologyOperator(max_dimension={self.max_dimension}, method={self.method})"


def generate_circle_data(n_points=100, noise=0.1):
    """生成圆形点云数据"""
    theta = np.linspace(0, 2*np.pi, n_points)
    x = np.cos(theta) + np.random.normal(0, noise, n_points)
    y = np.sin(theta) + np.random.normal(0, noise, n_points)
    return np.column_stack((x, y))


def test_persistent_homology_operator():
    """测试PersistentHomologyOperator"""
    print("\n测试PersistentHomologyOperator...")
    
    # 生成圆形点云数据
    circle_data = generate_circle_data(n_points=50, noise=0.05)
    
    # 创建持续同调算子
    ph_operator = PersistentHomologyOperator(max_dimension=1, method='standard')
    print(f"创建算子: {ph_operator}")
    
    # 应用算子
    result = ph_operator.apply(circle_data, dimensions=[0, 1])
    
    # 打印结果
    print(f"持续同调计算结果:")
    print(f"  方法: {result['method']}")
    print(f"  滤值类型: {result['filtration_type']}")
    
    # 打印0维持续图
    if 0 in result['persistence_diagrams']:
        diagram_0 = result['persistence_diagrams'][0]
        print(f"  0维持续图: {len(diagram_0)}个点")
        if len(diagram_0) > 0:
            print(f"    前5个点: {diagram_0[:5]}")
    
    # 打印1维持续图
    if 1 in result['persistence_diagrams']:
        diagram_1 = result['persistence_diagrams'][1]
        print(f"  1维持续图: {len(diagram_1)}个点")
        if len(diagram_1) > 0:
            print(f"    前5个点: {diagram_1[:5]}")
    
    print("PersistentHomologyOperator测试完成")
    
    return result


def main():
    """主函数"""
    print("开始测试持续同调算子...")
    
    # 测试PersistentHomologyOperator
    ph_result = test_persistent_homology_operator()
    
    print("\n所有测试完成")


if __name__ == "__main__":
    main()
