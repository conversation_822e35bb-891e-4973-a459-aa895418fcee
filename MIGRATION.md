# 从TCT迁移到TTE

本文档说明如何将TCT目录中的代码迁移到TTE目录。

## 背景

我们的项目应该在TTE目录中开发，而不是TCT目录。但是，由于历史原因，我们的一些代码可能仍然在TCT目录中。本文档提供了一个迁移脚本，用于将TCT目录中的代码迁移到TTE目录。

## 迁移脚本

我们提供了以下迁移脚本：

1. `migrate_from_tct.py`：用于将TCT目录中的代码迁移到TTE目录
2. `v2/tools/migrate_operators_algorithms.py`：用于将TCT目录中的算子和算法迁移到TTE目录
3. `v2/tools/check_registrations.py`：用于检查算子和算法的注册状态，并将未注册的算子和算法注册到注册表中

### 使用方法

```bash
python migrate_from_tct.py [--source SOURCE] [--target TARGET] [--overwrite]
```

参数说明：

- `--source SOURCE`：源目录（相对于TCT），默认为 `v2`
- `--target TARGET`：目标目录（相对于TTE），默认与源目录相同
- `--overwrite`：覆盖已存在的文件

### 示例

1. 迁移 `v2/tools` 目录：

```bash
python migrate_from_tct.py --source v2/tools
```

2. 迁移 `v2/tests` 目录并覆盖已存在的文件：

```bash
python migrate_from_tct.py --source v2/tests --overwrite
```

3. 迁移 `v2/src` 目录到 `v2/core`：

```bash
python migrate_from_tct.py --source v2/src --target v2/core
```

### 迁移算子和算法

```bash
cd /home/<USER>/CascadeProjects/TTE
python v2/tools/migrate_operators_algorithms.py [--overwrite]
```

参数说明：

- `--overwrite`：覆盖已存在的文件

### 检查算子和算法注册状态

```bash
cd /home/<USER>/CascadeProjects/TTE
python v2/tools/check_registrations.py --dir /home/<USER>/CascadeProjects/TTE [--register]
```

参数说明：

- `--dir DIR`：要检查的目录，默认为 `/home/<USER>/CascadeProjects/TTE`
- `--register`：注册未注册的算子和算法

## 注意事项

- 迁移脚本会保留目录结构
- 默认情况下，如果目标文件已存在，脚本会跳过该文件
- 使用 `--overwrite` 参数可以覆盖已存在的文件
- 迁移后，请检查代码中的导入路径，确保它们指向正确的位置
- 建议在迁移后运行测试，确保代码仍然可以正常工作
- 迁移算子和算法后，请运行 `check_registrations.py` 脚本，确保它们已正确注册

## 后续工作

迁移完成后，请确保：

1. 所有新的开发工作都在TTE目录中进行
2. 更新文档，指向TTE目录中的代码
3. 更新CI/CD配置，使用TTE目录中的代码
4. 通知团队成员，所有开发工作都应该在TTE目录中进行
5. 确保所有算子和算法都已正确注册到注册表中
6. 运行测试，确保迁移后的代码仍然可以正常工作

## 算子和算法注册

在超越态思维引擎中，所有的算子和算法都应该注册到注册表中，以便系统可以动态地发现和使用它们。

### 算子注册

算子应该注册到 `src.operators.registry.OperatorRegistry` 中。

### 算法注册

算法应该注册到 `src.core.distributed.algorithm.algorithm_registry.AlgorithmRegistry` 中。

### 注册检查

使用 `v2/tools/check_registrations.py` 脚本可以检查算子和算法的注册状态，并将未注册的算子和算法注册到注册表中。
