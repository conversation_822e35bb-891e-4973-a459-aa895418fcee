import numpy as np
import networkx as nx
from .quantum_core import QuantumCore
from .holographic_memory import HolographicMemory

class FractalNode:
    """分形节点
    
    实现基于分形几何的自组织和自适应特性
    每个节点都可以分裂和生长，形成分形结构
    """
    def __init__(self, level=0, max_level=3):
        self.level = level
        self.max_level = max_level
        self.quantum_core = QuantumCore()
        self.memory = HolographicMemory()
        self.children = []
        self.parent = None
        self.adaptation_score = 0.0
        
    def split(self):
        """节点分裂，创建子节点"""
        if self.level < self.max_level:
            # 创建两个子节点
            child1 = FractalNode(self.level + 1, self.max_level)
            child2 = FractalNode(self.level + 1, self.max_level)
            
            # 设置父子关系
            child1.parent = self
            child2.parent = self
            self.children = [child1, child2]
            
            return True
        return False
    
    def process_signal(self, signal):
        """处理输入信号
        
        结合量子计算和全息存储进行信号处理
        """
        # 量子处理
        self.quantum_core.apply_operation(signal)
        quantum_result = self.quantum_core.measure()
        
        # 全息存储
        self.memory.store(quantum_result)
        
        # 传递给子节点
        child_results = []
        for child in self.children:
            child_result = child.process_signal(signal)
            child_results.append(child_result)
            
        # 整合结果
        return self._integrate_results([quantum_result] + child_results)
    
    def adapt(self, environment_signal):
        """适应环境信号
        
        根据环境信号调整节点结构
        """
        # 计算适应度
        current_adaptation = self._calculate_adaptation(environment_signal)
        
        # 如果适应度低于阈值，考虑分裂或合并
        if current_adaptation < 0.5 and not self.children:
            self.split()
        elif current_adaptation > 0.8 and self.children:
            self.merge()
            
        self.adaptation_score = current_adaptation
        
        # 递归调整子节点
        for child in self.children:
            child.adapt(environment_signal)
            
    def merge(self):
        """合并子节点"""
        if self.children:
            # 整合子节点的信息
            for child in self.children:
                child_state = child.quantum_core.get_state()
                self.memory.store(child_state)
            
            # 清除子节点
            self.children = []
            
    def _calculate_adaptation(self, environment_signal):
        """计算对环境的适应度"""
        # 使用量子核心计算与环境的匹配度
        self.quantum_core.apply_operation(environment_signal)
        match_score = self.quantum_core.measure()
        
        # 归一化到[0,1]区间
        return (match_score + 1) / 2
    
    def _integrate_results(self, results):
        """整合所有结果"""
        if not results:
            return 0
        
        # 使用加权平均，权重随层级递减
        weights = [0.5 ** i for i in range(len(results))]
        weighted_sum = sum(w * r for w, r in zip(weights, results))
        return weighted_sum / sum(weights)
    
    def get_structure_graph(self):
        """获取节点结构的图表示"""
        G = nx.Graph()
        self._build_graph(G, "0")
        return G
    
    def _build_graph(self, G, node_id):
        """递归构建图结构"""
        G.add_node(node_id, level=self.level)
        for i, child in enumerate(self.children):
            child_id = f"{node_id}.{i}"
            G.add_edge(node_id, child_id)
            child._build_graph(G, child_id)
