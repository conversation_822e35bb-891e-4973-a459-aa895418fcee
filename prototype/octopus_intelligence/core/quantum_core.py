import numpy as np
from qutip import Qobj, basis, sigmax, sigmay, sigmaz

class QuantumCore:
    """量子核心处理单元
    
    实现基本的量子态操作和测量功能
    """
    def __init__(self, n_qubits=1):
        self.n_qubits = n_qubits
        self.state = self._initialize_state()
        
    def _initialize_state(self):
        """初始化量子态为|0⟩"""
        return basis([2] * self.n_qubits, [0] * self.n_qubits)
    
    def apply_operation(self, operator):
        """应用量子操作"""
        self.state = operator * self.state
        
    def measure(self):
        """执行测量操作"""
        # 获取密度矩阵
        rho = self.state * self.state.dag()
        # 返回期望值
        return np.real((rho * sigmaz()).tr())
    
    def get_state(self):
        """获取当前量子态"""
        return self.state
    
    def reset(self):
        """重置量子态"""
        self.state = self._initialize_state()
