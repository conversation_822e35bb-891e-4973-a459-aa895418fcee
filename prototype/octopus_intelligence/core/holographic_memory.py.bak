import numpy as np
from scipy.fft import fft2, ifft2

class HolographicMemory:
    """全息存储系统
    
    使用傅里叶变换实现全息存储和检索
    具有分布式存储和容错特性
    """
    def __init__(self, memory_size=(32, 32)):
        self.memory_size = memory_size
        self.memory_field = np.zeros(memory_size, dtype=np.complex128)
        self.reference_wave = self._generate_reference_wave()
        
    def _generate_reference_wave(self):
        """生成参考波"""
        x, y = np.meshgrid(np.linspace(-1, 1, self.memory_size[0]),
                          np.linspace(-1, 1, self.memory_size[1]))
        return np.exp(1j * (x + y))
    
    def store(self, data):
        """存储信息到全息场
        
        Args:
            data: 需要存储的数据，将被转换为2D数组
        """
        if not isinstance(data, np.ndarray):
            data = np.array(data)
        
        # 确保数据大小匹配
        data = np.resize(data, self.memory_size)
        
        # 计算全息图
        hologram = fft2(data * self.reference_wave)
        
        # 叠加到存储场
        self.memory_field += hologram
        
    def retrieve(self, reference_pattern):
        """从全息场检索信息
        
        Args:
            reference_pattern: 检索参考模式
        Returns:
            重建的数据
        """
        if not isinstance(reference_pattern, np.ndarray):
            reference_pattern = np.array(reference_pattern)
            
        # 确保模式大小匹配
        reference_pattern = np.resize(reference_pattern, self.memory_size)
        
        # 使用参考模式进行检索
        retrieved = ifft2(self.memory_field * np.conj(reference_pattern))
        
        return np.real(retrieved)
    
    def clear(self):
        """清空存储场"""
        self.memory_field = np.zeros(self.memory_size, dtype=np.complex128)
