import unittest
import numpy as np
from core.operators import (
    QuantumOperators,
    HolographicOperators,
    FractalOperators,
    UnifiedOperators
)

class TestOperatorComposition(unittest.TestCase):
    """测试算子组合效果"""
    
    def setUp(self):
        """设置测试环境"""
        # 量子态
        self.state1 = np.array([1, 0], dtype=np.complex128)  # |0⟩
        self.state2 = np.array([0, 1], dtype=np.complex128)  # |1⟩
        
        # 全息场
        self.reference = np.exp(1j * np.random.rand(10, 10))
        self.signal = np.zeros((10, 10), dtype=np.complex128)
        self.signal[4:7, 4:7] = 1.0  # 中心区域有信号
        
        # 分形场
        self.fractal_field = np.random.rand(10, 10)
        
        # 哈密顿算符
        self.hamiltonian = np.array([[1, 0], [0, -1]], dtype=np.complex128)
    
    def test_quantum_holographic_composition(self):
        """测试量子-全息复合操作"""
        # 1. 量子叠加态
        superposition = QuantumOperators.superposition(
            [self.state1, self.state2],
            np.array([1/np.sqrt(2), 1/np.sqrt(2)])
        )
        self.assertAlmostEqual(np.linalg.norm(superposition), 1.0)
        
        # 2. 量子态调制全息场
        modulated_signal = self.signal.copy()
        modulated_signal[4:7, 4:7] *= superposition[0]  # 只调制中心区域
        hologram = HolographicOperators.interference(modulated_signal, self.reference)
        
        # 3. 从全息图重建信号
        reconstructed = HolographicOperators.reconstruction(hologram, self.reference)
        
        # 验证重建信号的中心区域仍然有信息
        signal_energy = np.sum(np.abs(modulated_signal) ** 2)
        reconstructed_energy = np.sum(np.abs(reconstructed) ** 2)
        # 验证重建信号的能量与原始信号相关
        self.assertGreater(reconstructed_energy, 0)
        self.assertLess(abs(reconstructed_energy - signal_energy), signal_energy)
    
    def test_holographic_fractal_composition(self):
        """测试全息-分形复合操作"""
        # 1. 创建全息图
        hologram = HolographicOperators.interference(self.signal, self.reference)
        
        # 2. 应用分形迭代
        def logistic_rule(x):
            return 4 * x * (1 - x)
        
        # 对全息图进行分形迭代
        fractal_hologram = FractalOperators.iteration(
            hologram / np.max(hologram),  # 归一化到[0,1]
            logistic_rule
        )
        
        # 3. 验证分形特性
        # 计算不同尺度下的自相似性
        scale1 = FractalOperators.self_similarity(fractal_hologram, 2.0)
        scale2 = FractalOperators.self_similarity(fractal_hologram, 4.0)
        
        # 验证尺度关系
        ratio = np.mean(scale2) / np.mean(scale1)
        self.assertAlmostEqual(ratio, 2.0, places=1)
    
    def test_quantum_fractal_composition(self):
        """测试量子-分形复合操作"""
        # 1. 量子演化
        evolved_state = QuantumOperators.evolution(self.state1, self.hamiltonian, 0.1)
        
        # 2. 将量子态映射到分形场
        field = self.fractal_field * np.abs(evolved_state[0])
        
        # 3. 应用分形生长
        grown_field = FractalOperators.growth(field)
        
        # 验证场的规范化和生长特性
        self.assertTrue(np.all(grown_field != field))  # 场发生了变化
        self.assertTrue(np.all(np.isfinite(grown_field)))  # 没有发散
    
    def test_triple_composition(self):
        """测试三重复合操作"""
        # 1. 量子态准备
        quantum_state = QuantumOperators.superposition([self.state1, self.state2])
        
        # 2. 全息存储
        hologram = HolographicOperators.interference(
            self.signal * quantum_state[0],
            self.reference
        )
        
        # 3. 分形演化
        fractal_pattern = FractalOperators.growth(hologram)
        
        # 4. 统一场耦合
        unified_field = UnifiedOperators.field_coupling(
            quantum_state,
            hologram,
            fractal_pattern
        )
        
        # 5. 验证涌现特性
        emergence = UnifiedOperators.emergence(unified_field, threshold=0.5)
        coherence = UnifiedOperators.coherence(emergence)
        
        # 验证结果
        self.assertTrue(np.all(np.isfinite(unified_field)))  # 场是有限的
        self.assertTrue(0 <= coherence <= 1)  # 相干性在合理范围内
        self.assertTrue(np.sum(emergence != 0) > 0)  # 存在涌现模式
    
    def test_long_term_stability(self):
        """测试长期演化稳定性"""
        # 初始场
        field = self.signal.copy()
        energy_initial = np.sum(np.abs(field) ** 2)
        
        # 进行100步演化
        for _ in range(100):
            # 1. 量子演化
            quantum_state = QuantumOperators.evolution(
                self.state1,
                self.hamiltonian,
                0.01
            )
            
            # 2. 全息调制
            field = field * quantum_state[0]
            hologram = HolographicOperators.interference(field, self.reference)
            
            # 3. 分形生长
            field = FractalOperators.growth(hologram, rate=0.1)
            
            # 确保场没有发散
            self.assertTrue(np.all(np.isfinite(field)))
            
            # 验证能量约束
            energy = np.sum(np.abs(field) ** 2)
            self.assertLess(energy, 2 * energy_initial)  # 能量不应该无限增长

if __name__ == '__main__':
    unittest.main()
