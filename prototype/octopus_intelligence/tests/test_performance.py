import unittest
import numpy as np
import time
from typing import Tuple, Callable
from core.operators import (
    QuantumOperators,
    HolographicOperators,
    FractalOperators,
    UnifiedOperators
)

class PerformanceMetrics:
    """性能指标收集器"""
    
    def __init__(self):
        self.metrics = {}
    
    def measure(self, name: str, func: Callable, *args, **kwargs) -> Tuple[float, any]:
        """测量函数执行时间"""
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        duration = end_time - start_time
        
        if name not in self.metrics:
            self.metrics[name] = []
        self.metrics[name].append(duration)
        
        return duration, result
    
    def get_average(self, name: str) -> float:
        """获取平均执行时间"""
        return np.mean(self.metrics[name])
    
    def get_percentile(self, name: str, p: float) -> float:
        """获取执行时间的百分位数"""
        return np.percentile(self.metrics[name], p)

class TestPerformance(unittest.TestCase):
    """性能测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.metrics = PerformanceMetrics()
        
        # 测试规模配置
        self.sizes = {
            'small': (100, 100),
            'medium': (1000, 1000),
            'large': (5000, 5000)
        }
        
        # 性能目标（秒）
        self.targets = {
            'quantum_small': 0.001,
            'quantum_medium': 0.01,
            'quantum_large': 0.1,
            'holographic_small': 0.001,
            'holographic_medium': 0.01,
            'holographic_large': 0.1,
            'fractal_small': 0.001,
            'fractal_medium': 0.01,
            'fractal_large': 0.1
        }
    
    def test_quantum_performance(self):
        """测试量子算子性能"""
        print("\n量子算子性能测试")
        
        for size_name, (n, _) in self.sizes.items():
            # 准备测试数据
            states = [np.random.rand(n) + 1j * np.random.rand(n) for _ in range(10)]
            states = [state / np.linalg.norm(state) for state in states]
            hamiltonian = np.random.rand(n, n) + 1j * np.random.rand(n, n)
            hamiltonian = (hamiltonian + hamiltonian.conj().T) / 2  # 确保厄米性
            
            # 测试叠加态
            duration, _ = self.metrics.measure(
                f'quantum_superposition_{size_name}',
                QuantumOperators.superposition,
                states
            )
            print(f"叠加态 ({size_name}): {duration:.6f}秒")
            
            # 测试演化
            duration, _ = self.metrics.measure(
                f'quantum_evolution_{size_name}',
                QuantumOperators.evolution,
                states[0], hamiltonian, 0.1
            )
            print(f"演化 ({size_name}): {duration:.6f}秒")
            
            # 验证性能目标
            self.assertLess(
                duration,
                self.targets[f'quantum_{size_name}'],
                f"量子演化 {size_name} 性能不达标"
            )
    
    def test_holographic_performance(self):
        """测试全息算子性能"""
        print("\n全息算子性能测试")
        
        for size_name, size in self.sizes.items():
            # 准备测试数据
            signal = np.random.rand(*size) + 1j * np.random.rand(*size)
            reference = np.exp(1j * np.random.rand(*size))
            
            # 测试干涉
            duration, hologram = self.metrics.measure(
                f'holographic_interference_{size_name}',
                HolographicOperators.interference,
                signal, reference
            )
            print(f"干涉 ({size_name}): {duration:.6f}秒")
            
            # 测试重建
            duration, _ = self.metrics.measure(
                f'holographic_reconstruction_{size_name}',
                HolographicOperators.reconstruction,
                hologram, reference
            )
            print(f"重建 ({size_name}): {duration:.6f}秒")
            
            # 验证性能目标
            self.assertLess(
                duration,
                self.targets[f'holographic_{size_name}'],
                f"全息重建 {size_name} 性能不达标"
            )
    
    def test_fractal_performance(self):
        """测试分形算子性能"""
        print("\n分形算子性能测试")
        
        def logistic_rule(x):
            return 4 * x * (1 - x)
        
        for size_name, size in self.sizes.items():
            # 准备测试数据
            field = np.random.rand(*size)
            
            # 测试迭代
            duration, _ = self.metrics.measure(
                f'fractal_iteration_{size_name}',
                FractalOperators.iteration,
                field, logistic_rule
            )
            print(f"迭代 ({size_name}): {duration:.6f}秒")
            
            # 测试生长
            duration, _ = self.metrics.measure(
                f'fractal_growth_{size_name}',
                FractalOperators.growth,
                field
            )
            print(f"生长 ({size_name}): {duration:.6f}秒")
            
            # 验证性能目标
            self.assertLess(
                duration,
                self.targets[f'fractal_{size_name}'],
                f"分形生长 {size_name} 性能不达标"
            )
    
    def test_memory_usage(self):
        """测试内存使用"""
        import psutil
        import os
        
        def get_memory_usage():
            """获取当前进程的内存使用"""
            process = psutil.Process(os.getpid())
            return process.memory_info().rss / 1024 / 1024  # 转换为MB
        
        print("\n内存使用测试")
        
        # 记录初始内存
        initial_memory = get_memory_usage()
        print(f"初始内存使用: {initial_memory:.2f}MB")
        
        # 大规模测试
        size = self.sizes['large']
        field = np.random.rand(*size) + 1j * np.random.rand(*size)
        
        # 记录峰值内存
        peak_memory = get_memory_usage()
        print(f"峰值内存使用: {peak_memory:.2f}MB")
        print(f"内存增长: {peak_memory - initial_memory:.2f}MB")
        
        # 验证内存使用在合理范围内
        # 对于5000x5000的复数矩阵，理论内存使用约为: 5000 * 5000 * 16 bytes = 381.47MB
        theoretical_memory = size[0] * size[1] * 16 / 1024 / 1024
        self.assertLess(
            peak_memory - initial_memory,
            theoretical_memory * 2,  # 允许2倍的理论内存使用
            "内存使用超出预期"
        )
    
    def tearDown(self):
        """测试结束后的清理和报告"""
        print("\n性能统计:")
        for name, durations in self.metrics.metrics.items():
            avg = np.mean(durations)
            p95 = np.percentile(durations, 95)
            print(f"{name}:")
            print(f"  平均: {avg:.6f}秒")
            print(f"  95分位: {p95:.6f}秒")

if __name__ == '__main__':
    unittest.main(verbosity=2)
