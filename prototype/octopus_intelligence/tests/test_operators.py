import unittest
import numpy as np
from core.operators import (
    QuantumOperators, 
    HolographicOperators, 
    FractalOperators,
    UnifiedOperators
)

class TestQuantumOperators(unittest.TestCase):
    """量子算子测试"""
    
    def setUp(self):
        # 设置基本测试态
        self.state1 = np.array([1, 0], dtype=np.complex128)  # |0⟩
        self.state2 = np.array([0, 1], dtype=np.complex128)  # |1⟩
        self.hamiltonian = np.array([[1, 0], [0, -1]], dtype=np.complex128)
    
    def test_superposition(self):
        """测试叠加态算子"""
        states = [self.state1, self.state2]
        coeffs = np.array([1/np.sqrt(2), 1/np.sqrt(2)])
        result = QuantumOperators.superposition(states, coeffs)
        
        # 验证规范化
        self.assertAlmostEqual(np.linalg.norm(result), 1.0)
        # 验证叠加态系数
        expected = np.array([1/np.sqrt(2), 1/np.sqrt(2)])
        np.testing.assert_array_almost_equal(result, expected)
    
    def test_evolution(self):
        """测试时间演化算子"""
        dt = 0.1
        result = QuantumOperators.evolution(self.state1, self.hamiltonian, dt)
        
        # 验证规范化保持
        self.assertAlmostEqual(np.linalg.norm(result), 1.0)
        # 验证演化的幺正性
        expected = np.exp(-1j * self.hamiltonian * dt) @ self.state1
        expected = expected / np.linalg.norm(expected)  # 规范化预期值
        np.testing.assert_array_almost_equal(result, expected)

class TestHolographicOperators(unittest.TestCase):
    """全息算子测试"""
    
    def setUp(self):
        self.signal = np.random.rand(10, 10) + 1j * np.random.rand(10, 10)
        self.reference = np.exp(1j * np.random.rand(10, 10))
    
    def test_interference(self):
        """测试干涉算子"""
        result = HolographicOperators.interference(self.signal, self.reference)
        
        # 验证干涉图样是实数
        self.assertTrue(np.all(np.isreal(result)))
        # 验证干涉图样非负
        self.assertTrue(np.all(result >= 0))
    
    def test_reconstruction(self):
        """测试重建算子"""
        hologram = HolographicOperators.interference(self.signal, self.reference)
        result = HolographicOperators.reconstruction(hologram, self.reference)
        
        # 验证重建信号与原始信号的相关性
        correlation = np.abs(np.sum(result * np.conj(self.signal)))
        self.assertGreater(correlation, 0.5)

class TestFractalOperators(unittest.TestCase):
    """分形算子测试"""
    
    def setUp(self):
        self.field = np.random.rand(10, 10)
    
    def test_iteration(self):
        """测试迭代算子"""
        def simple_rule(x):
            return x * (1 - x)
        
        result = FractalOperators.iteration(self.field, simple_rule)
        self.assertEqual(result.shape, self.field.shape)
        
    def test_self_similarity(self):
        """测试自相似算子"""
        scale = 2.0
        result = FractalOperators.self_similarity(self.field, scale)
        np.testing.assert_array_almost_equal(result, scale * self.field)

class TestUnifiedOperators(unittest.TestCase):
    """统一场算子测试"""
    
    def setUp(self):
        self.quantum_state = np.array([1, 0], dtype=np.complex128)
        self.holo_field = np.random.rand(2, 2) + 1j * np.random.rand(2, 2)
        self.fractal_struct = np.random.rand(2, 2)
    
    def test_field_coupling(self):
        """测试场耦合算子"""
        result = UnifiedOperators.field_coupling(
            self.quantum_state,
            self.holo_field,
            self.fractal_struct
        )
        self.assertGreater(len(result.shape), 1)
    
    def test_emergence(self):
        """测试涌现算子"""
        field = np.random.rand(10, 10)
        result = UnifiedOperators.emergence(field, threshold=0.5)
        
        # 验证涌现特性
        self.assertTrue(np.all(np.abs(result[result != 0]) > 0.5))
    
    def test_coherence(self):
        """测试相干性算子"""
        field = np.random.rand(10, 10) + 1j * np.random.rand(10, 10)
        coherence = UnifiedOperators.coherence(field)
        
        # 验证相干性是实数且在[0,1]范围内
        self.assertTrue(np.isreal(coherence))
        self.assertTrue(0 <= coherence <= 1)

if __name__ == '__main__':
    unittest.main()
