import unittest
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入所有测试
from tests.test_operators import (
    TestQuantumOperators,
    TestHolographicOperators,
    TestFractalOperators,
    TestUnifiedOperators
)

def run_tests():
    """运行所有测试并生成报告"""
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加所有测试类
    suite.addTests(unittest.TestLoader().loadTestsFromTestCase(TestQuantumOperators))
    suite.addTests(unittest.TestLoader().loadTestsFromTestCase(TestHolographicOperators))
    suite.addTests(unittest.TestLoader().loadTestsFromTestCase(TestFractalOperators))
    suite.addTests(unittest.TestLoader().loadTestsFromTestCase(TestUnifiedOperators))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 返回测试结果
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
