import unittest
import numpy as np
from core.operators import (
    QuantumOperators,
    HolographicOperators,
    FractalOperators,
    UnifiedOperators
)

class TestQuantumEdgeCases(unittest.TestCase):
    """量子算子边界条件测试"""
    
    def setUp(self):
        """设置测试环境"""
        # 基本态
        self.zero_state = np.zeros(2, dtype=np.complex128)
        self.one_state = np.array([0, 1], dtype=np.complex128)
        
        # 高维态
        self.high_dim_state = np.random.rand(1000) + 1j * np.random.rand(1000)
        self.high_dim_state /= np.linalg.norm(self.high_dim_state)
        
        # 哈密顿算符
        self.hamiltonian = np.array([[1, 0], [0, -1]], dtype=np.complex128)
        self.high_dim_hamiltonian = np.eye(1000, dtype=np.complex128)
    
    def test_zero_state(self):
        """测试零态处理"""
        # 零态归一化
        with self.assertRaises(ValueError):
            QuantumOperators.superposition([self.zero_state])
        
        # 零态演化
        with self.assertRaises(ValueError):
            QuantumOperators.evolution(self.zero_state, self.hamiltonian, 0.1)
    
    def test_high_dimensional(self):
        """测试高维态处理"""
        # 高维叠加
        result = QuantumOperators.superposition([self.high_dim_state])
        self.assertEqual(result.shape, (1000,))
        self.assertAlmostEqual(np.linalg.norm(result), 1.0)
        
        # 高维演化
        evolved = QuantumOperators.evolution(
            self.high_dim_state,
            self.high_dim_hamiltonian,
            0.1
        )
        self.assertEqual(evolved.shape, (1000,))
        self.assertAlmostEqual(np.linalg.norm(evolved), 1.0)
    
    def test_large_superposition(self):
        """测试大规模叠加态"""
        # 生成100个随机态
        states = [np.random.rand(2) + 1j * np.random.rand(2) for _ in range(100)]
        states = [state / np.linalg.norm(state) for state in states]
        
        # 均匀叠加
        result = QuantumOperators.superposition(states)
        self.assertEqual(result.shape, (2,))
        self.assertAlmostEqual(np.linalg.norm(result), 1.0)
        
        # 随机系数叠加
        coeffs = np.random.rand(100) + 1j * np.random.rand(100)
        coeffs /= np.linalg.norm(coeffs)
        result = QuantumOperators.superposition(states, coeffs)
        self.assertEqual(result.shape, (2,))
        self.assertAlmostEqual(np.linalg.norm(result), 1.0)

class TestHolographicEdgeCases(unittest.TestCase):
    """全息算子边界条件测试"""
    
    def setUp(self):
        """设置测试环境"""
        # 空场
        self.zero_field = np.zeros((10, 10), dtype=np.complex128)
        # 单点场
        self.single_point = np.zeros((10, 10), dtype=np.complex128)
        self.single_point[5, 5] = 1.0
        # 大规模场
        self.large_field = np.random.rand(1000, 1000) + 1j * np.random.rand(1000, 1000)
    
    def test_zero_field(self):
        """测试零场处理"""
        # 零场干涉
        result = HolographicOperators.interference(self.zero_field, self.zero_field)
        self.assertTrue(np.all(result == 0))
        
        # 零场重建
        result = HolographicOperators.reconstruction(self.zero_field, self.zero_field)
        self.assertTrue(np.all(np.abs(result) < 1e-10))
    
    def test_single_point(self):
        """测试单点场处理"""
        # 单点干涉
        result = HolographicOperators.interference(self.single_point, self.single_point)
        # 验证干涉图样的峰值位置
        max_point = np.unravel_index(np.argmax(result), result.shape)
        self.assertEqual(max_point, (5, 5))
        
        # 单点重建
        result = HolographicOperators.reconstruction(result, self.single_point)
        # 验证重建信号的峰值位置
        max_point = np.unravel_index(np.argmax(np.abs(result)), result.shape)
        self.assertEqual(max_point, (5, 5))
    
    def test_large_field(self):
        """测试大规模场处理"""
        # 大规模干涉
        result = HolographicOperators.interference(self.large_field, self.large_field)
        self.assertEqual(result.shape, (1000, 1000))
        self.assertTrue(np.all(result >= 0))  # 干涉图样应该是非负的
        
        # 大规模重建
        result = HolographicOperators.reconstruction(result, self.large_field)
        self.assertEqual(result.shape, (1000, 1000))

class TestFractalEdgeCases(unittest.TestCase):
    """分形算子边界条件测试"""
    
    def setUp(self):
        """设置测试环境"""
        # 零场
        self.zero_field = np.zeros((10, 10))
        # 单值场
        self.uniform_field = np.ones((10, 10))
        # 随机场
        self.random_field = np.random.rand(10, 10)
    
    def test_zero_field(self):
        """测试零场处理"""
        def growth_rule(x):
            return x * (1 - x)
        
        # 零场迭代
        result = FractalOperators.iteration(self.zero_field, growth_rule)
        self.assertTrue(np.all(result == 0))
        
        # 零场自相似性
        result = FractalOperators.self_similarity(self.zero_field, 2.0)
        self.assertTrue(np.all(result == 0))
    
    def test_uniform_field(self):
        """测试均匀场处理"""
        def identity_rule(x):
            return x
        
        # 均匀场迭代
        result = FractalOperators.iteration(self.uniform_field, identity_rule)
        self.assertTrue(np.all(result == 1))
        
        # 均匀场自相似性
        result = FractalOperators.self_similarity(self.uniform_field, 2.0)
        self.assertTrue(np.all(result == 2))

if __name__ == '__main__':
    unittest.main()
