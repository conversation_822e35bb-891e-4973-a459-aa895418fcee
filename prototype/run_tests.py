"""
超越态张量原型测试运行器
"""

import sys
import os
import time
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from tests.test_fusion import test_fusion_operations
from tests.test_evolution import test_evolution_stability
from tests.test_nogil import test_numpy_nogil_performance
from transcendental_tensor.backend_selector import BackendSelectorPrototype

def run_all_tests():
    """运行所有测试"""
    print("=" * 80)
    print("超越态张量原型测试")
    print("=" * 80)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python版本: {sys.version}")
    
    results = {}
    
    # 测试融合操作
    print("\n" + "=" * 80)
    print("测试1: 融合操作")
    print("=" * 80)
    start_time = time.time()
    fusion_results = test_fusion_operations()
    fusion_time = time.time() - start_time
    print(f"融合操作测试完成，耗时: {fusion_time:.2f}秒")
    results["fusion"] = fusion_results
    
    # 测试演化稳定性
    print("\n" + "=" * 80)
    print("测试2: 演化稳定性")
    print("=" * 80)
    start_time = time.time()
    evolution_results = test_evolution_stability(steps=100, time_step=0.01)
    evolution_time = time.time() - start_time
    print(f"演化稳定性测试完成，耗时: {evolution_time:.2f}秒")
    results["evolution"] = {
        "initial_energy": evolution_results["initial_energy"],
        "final_energy": evolution_results["final_energy"],
        "energy_conservation": evolution_results["energy_conservation"],
        "final_stability": evolution_results["final_stability"]
    }
    
    # 测试NumPy No-GIL性能
    print("\n" + "=" * 80)
    print("测试3: NumPy No-GIL性能")
    print("=" * 80)
    start_time = time.time()
    nogil_results = test_numpy_nogil_performance(matrix_sizes=[500, 1000], num_matrices=5)
    nogil_time = time.time() - start_time
    print(f"NumPy No-GIL性能测试完成，耗时: {nogil_time:.2f}秒")
    results["nogil"] = {
        size: {
            "serial_time": data["serial_time"],
            "parallel_time": data["parallel_time"],
            "speedup": data["speedup"],
            "is_correct": data["is_correct"]
        }
        for size, data in nogil_results.items()
    }
    
    # 测试后端选择系统
    print("\n" + "=" * 80)
    print("测试4: 后端选择系统")
    print("=" * 80)
    selector = BackendSelectorPrototype()
    
    # 测试不同操作的后端选择
    backend_results = {}
    for operation in ["matrix_multiply", "svd", "fft"]:
        for size in [100, 500, 1000]:
            print(f"测试操作: {operation}, 大小: {size}")
            backend = selector.select_backend(operation, size)
            backend_results[f"{operation}_{size}"] = backend
    
    results["backend_selection"] = backend_results
    
    # 保存测试结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    result_file = f"test_results_{timestamp}.json"
    
    # 将NumPy数组转换为列表以便JSON序列化
    def convert_numpy(obj):
        if isinstance(obj, (np.ndarray, np.generic)):
            return obj.tolist()
        return obj
    
    with open(result_file, 'w') as f:
        json.dump(results, f, default=convert_numpy, indent=2)
    
    print("\n" + "=" * 80)
    print(f"所有测试完成!")
    print(f"结果已保存到: {result_file}")
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    return results

if __name__ == "__main__":
    run_all_tests()
