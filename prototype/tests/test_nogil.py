"""
NumPy No-GIL并行性能测试
"""

import sys
import os
import time
import numpy as np
from concurrent.futures import ThreadPoolExecutor
import multiprocessing
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from transcendental_tensor.backend_selector import BackendSelectorPrototype

def test_numpy_nogil_performance(matrix_sizes=[500, 1000, 2000], num_matrices=10, plot=True):
    """测试NumPy No-GIL并行性能"""
    selector = BackendSelectorPrototype()
    
    print(f"开始测试NumPy No-GIL并行性能...")
    print(f"Python版本: {sys.version}")
    print(f"NumPy版本: {np.__version__}")
    print(f"No-GIL支持: {'是' if selector.has_nogil else '否'}")
    print(f"CPU核心数: {selector.cpu_count}")
    
    results = {}
    
    for matrix_size in matrix_sizes:
        print(f"\n测试矩阵大小: {matrix_size}x{matrix_size}, 矩阵数量: {num_matrices}")
        
        # 创建大型测试数据
        matrices = [np.random.rand(matrix_size, matrix_size) for _ in range(num_matrices)]
        
        # 串行处理
        start_time = time.time()
        serial_results = [np.linalg.svd(matrix, full_matrices=False) for matrix in matrices]
        serial_time = time.time() - start_time
        print(f"串行处理时间: {serial_time:.4f}秒")
        
        # 并行处理
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=selector.cpu_count) as executor:
            futures = [executor.submit(np.linalg.svd, matrix, False) for matrix in matrices]
            parallel_results = [future.result() for future in futures]
        parallel_time = time.time() - start_time
        print(f"并行处理时间: {parallel_time:.4f}秒")
        
        # 计算加速比
        speedup = serial_time / parallel_time if parallel_time > 0 else 0
        print(f"加速比: {speedup:.2f}x")
        
        # 验证结果正确性
        is_correct = all(
            np.allclose(s1[0], s2[0]) and np.allclose(s1[1], s2[1]) and np.allclose(s1[2], s2[2])
            for s1, s2 in zip(serial_results, parallel_results)
        )
        print(f"结果正确性: {'正确' if is_correct else '错误'}")
        
        results[matrix_size] = {
            "serial_time": serial_time,
            "parallel_time": parallel_time,
            "speedup": speedup,
            "is_correct": is_correct
        }
    
    # 绘制性能图表
    if plot:
        plt.figure(figsize=(12, 6))
        
        # 处理时间对比
        plt.subplot(1, 2, 1)
        sizes = list(results.keys())
        serial_times = [results[size]["serial_time"] for size in sizes]
        parallel_times = [results[size]["parallel_time"] for size in sizes]
        
        plt.bar(
            [str(size) for size in sizes], 
            serial_times, 
            width=0.4, 
            label='串行', 
            align='edge'
        )
        plt.bar(
            [str(size) for size in sizes], 
            parallel_times, 
            width=-0.4, 
            label='并行', 
            align='edge'
        )
        plt.title('处理时间对比')
        plt.xlabel('矩阵大小')
        plt.ylabel('时间 (秒)')
        plt.legend()
        plt.grid(True, axis='y')
        
        # 加速比
        plt.subplot(1, 2, 2)
        speedups = [results[size]["speedup"] for size in sizes]
        plt.bar([str(size) for size in sizes], speedups)
        plt.axhline(y=1.0, color='r', linestyle='-', alpha=0.3)
        plt.title('加速比')
        plt.xlabel('矩阵大小')
        plt.ylabel('加速比')
        plt.grid(True, axis='y')
        
        plt.tight_layout()
        plt.savefig('nogil_performance.png')
        print(f"性能图表已保存为 'nogil_performance.png'")
    
    return results

if __name__ == "__main__":
    test_numpy_nogil_performance()
