"""
超越态张量融合操作测试
"""

import numpy as np
import time
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from transcendental_tensor import TranscendentalTensorPrototype

def quantum_holographic_fusion(tensor1, tensor2):
    """量子-全息复合融合"""
    return tensor1.fuse(tensor2, mode="quantum_holographic", weights=(0.6, 0.4, 0.0))

def holographic_fractal_fusion(tensor1, tensor2):
    """全息-分形复合融合"""
    return tensor1.fuse(tensor2, mode="holographic_fractal", weights=(0.0, 0.6, 0.4))

def quantum_fractal_fusion(tensor1, tensor2):
    """量子-分形复合融合"""
    return tensor1.fuse(tensor2, mode="quantum_fractal", weights=(0.6, 0.0, 0.4))

def triple_fusion(tensor1, tensor2):
    """三重复合融合"""
    return tensor1.fuse(tensor2, mode="triple_fusion", weights=(0.33, 0.33, 0.34))

def test_fusion_operations():
    """测试融合操作"""
    print("开始测试融合操作...")
    
    # 创建测试张量
    tensor1 = TranscendentalTensorPrototype([2, 2], [4, 4], [2, 2])
    tensor2 = TranscendentalTensorPrototype([2, 2], [4, 4], [2, 2])
    
    # 初始化随机数据
    tensor1.data = np.random.normal(0, 1, tensor1.shape) + 1j * np.random.normal(0, 1, tensor1.shape)
    tensor2.data = np.random.normal(0, 1, tensor2.shape) + 1j * np.random.normal(0, 1, tensor2.shape)
    
    # 归一化数据
    tensor1.data = tensor1.data / np.sqrt(np.sum(np.abs(tensor1.data) ** 2))
    tensor2.data = tensor2.data / np.sqrt(np.sum(np.abs(tensor2.data) ** 2))
    
    # 记录初始能量
    energy1 = tensor1.calculate_energy()
    energy2 = tensor2.calculate_energy()
    print(f"张量1初始能量: {energy1:.6f}")
    print(f"张量2初始能量: {energy2:.6f}")
    
    # 测试量子-全息融合
    print("\n测试量子-全息融合...")
    qh_fused = quantum_holographic_fusion(tensor1, tensor2)
    qh_energy = qh_fused.calculate_energy()
    qh_energy_conservation = abs(qh_energy - (energy1 + energy2)) / (energy1 + energy2)
    print(f"量子-全息融合能量: {qh_energy:.6f}")
    print(f"量子-全息融合能量守恒度: {1 - qh_energy_conservation:.6f}")
    print(f"量子-全息融合相干性: {qh_fused.metadata['coherence']:.6f}")
    print(f"量子-全息融合分布性: {qh_fused.metadata['distribution']:.6f}")
    print(f"量子-全息融合自相似性: {qh_fused.metadata['self_similarity']:.6f}")
    print(f"量子-全息融合涌现性: {qh_fused.metadata['emergence']:.6f}")
    
    # 测试全息-分形融合
    print("\n测试全息-分形融合...")
    hf_fused = holographic_fractal_fusion(tensor1, tensor2)
    hf_energy = hf_fused.calculate_energy()
    hf_energy_conservation = abs(hf_energy - (energy1 + energy2)) / (energy1 + energy2)
    print(f"全息-分形融合能量: {hf_energy:.6f}")
    print(f"全息-分形融合能量守恒度: {1 - hf_energy_conservation:.6f}")
    print(f"全息-分形融合相干性: {hf_fused.metadata['coherence']:.6f}")
    print(f"全息-分形融合分布性: {hf_fused.metadata['distribution']:.6f}")
    print(f"全息-分形融合自相似性: {hf_fused.metadata['self_similarity']:.6f}")
    print(f"全息-分形融合涌现性: {hf_fused.metadata['emergence']:.6f}")
    
    # 测试量子-分形融合
    print("\n测试量子-分形融合...")
    qf_fused = quantum_fractal_fusion(tensor1, tensor2)
    qf_energy = qf_fused.calculate_energy()
    qf_energy_conservation = abs(qf_energy - (energy1 + energy2)) / (energy1 + energy2)
    print(f"量子-分形融合能量: {qf_energy:.6f}")
    print(f"量子-分形融合能量守恒度: {1 - qf_energy_conservation:.6f}")
    print(f"量子-分形融合相干性: {qf_fused.metadata['coherence']:.6f}")
    print(f"量子-分形融合分布性: {qf_fused.metadata['distribution']:.6f}")
    print(f"量子-分形融合自相似性: {qf_fused.metadata['self_similarity']:.6f}")
    print(f"量子-分形融合涌现性: {qf_fused.metadata['emergence']:.6f}")
    
    # 测试三重融合
    print("\n测试三重融合...")
    triple_fused = triple_fusion(tensor1, tensor2)
    triple_energy = triple_fused.calculate_energy()
    triple_energy_conservation = abs(triple_energy - (energy1 + energy2)) / (energy1 + energy2)
    print(f"三重融合能量: {triple_energy:.6f}")
    print(f"三重融合能量守恒度: {1 - triple_energy_conservation:.6f}")
    print(f"三重融合相干性: {triple_fused.metadata['coherence']:.6f}")
    print(f"三重融合分布性: {triple_fused.metadata['distribution']:.6f}")
    print(f"三重融合自相似性: {triple_fused.metadata['self_similarity']:.6f}")
    print(f"三重融合涌现性: {triple_fused.metadata['emergence']:.6f}")
    
    # 比较涌现性
    print("\n涌现性比较:")
    print(f"量子-全息融合涌现性: {qh_fused.metadata['emergence']:.6f}")
    print(f"全息-分形融合涌现性: {hf_fused.metadata['emergence']:.6f}")
    print(f"量子-分形融合涌现性: {qf_fused.metadata['emergence']:.6f}")
    print(f"三重融合涌现性: {triple_fused.metadata['emergence']:.6f}")
    
    # 返回结果
    return {
        "quantum_holographic": {
            "energy": qh_energy,
            "energy_conservation": 1 - qh_energy_conservation,
            "coherence": qh_fused.metadata['coherence'],
            "distribution": qh_fused.metadata['distribution'],
            "self_similarity": qh_fused.metadata['self_similarity'],
            "emergence": qh_fused.metadata['emergence']
        },
        "holographic_fractal": {
            "energy": hf_energy,
            "energy_conservation": 1 - hf_energy_conservation,
            "coherence": hf_fused.metadata['coherence'],
            "distribution": hf_fused.metadata['distribution'],
            "self_similarity": hf_fused.metadata['self_similarity'],
            "emergence": hf_fused.metadata['emergence']
        },
        "quantum_fractal": {
            "energy": qf_energy,
            "energy_conservation": 1 - qf_energy_conservation,
            "coherence": qf_fused.metadata['coherence'],
            "distribution": qf_fused.metadata['distribution'],
            "self_similarity": qf_fused.metadata['self_similarity'],
            "emergence": qf_fused.metadata['emergence']
        },
        "triple_fusion": {
            "energy": triple_energy,
            "energy_conservation": 1 - triple_energy_conservation,
            "coherence": triple_fused.metadata['coherence'],
            "distribution": triple_fused.metadata['distribution'],
            "self_similarity": triple_fused.metadata['self_similarity'],
            "emergence": triple_fused.metadata['emergence']
        }
    }

if __name__ == "__main__":
    test_fusion_operations()
