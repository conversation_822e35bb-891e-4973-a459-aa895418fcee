"""
超越态张量演化稳定性测试
"""

import numpy as np
import time
import sys
import os
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from transcendental_tensor import TranscendentalTensorPrototype

def test_evolution_stability(steps=100, time_step=0.01, plot=True):
    """测试演化稳定性"""
    print(f"开始测试演化稳定性 (步数={steps}, 时间步长={time_step})...")
    
    # 创建测试张量
    tensor = TranscendentalTensorPrototype([2, 2], [4, 4], [2, 2])
    
    # 初始化随机数据
    tensor.data = np.random.normal(0, 1, tensor.shape) + 1j * np.random.normal(0, 1, tensor.shape)
    
    # 归一化数据
    tensor.data = tensor.data / np.sqrt(np.sum(np.abs(tensor.data) ** 2))
    
    # 记录初始能量
    initial_energy = tensor.calculate_energy()
    print(f"初始能量: {initial_energy:.6f}")
    
    # 记录初始状态
    initial_state = tensor.clone()
    
    # 记录历史数据
    energy_history = [initial_energy]
    coherence_history = [tensor.metadata['coherence']]
    distribution_history = [tensor.metadata['distribution']]
    self_similarity_history = [tensor.metadata['self_similarity']]
    emergence_history = [tensor.metadata['emergence']]
    stability_history = [1.0]  # 初始稳定性为1.0
    
    # 执行多步演化
    for step in range(steps):
        # 演化一步
        tensor = tensor.evolve(
            time_step=time_step,
            hamiltonian=None,
            diffusion_rate=0.1,
            fractal_factor=0.2,
            emergence_factor=0.1
        )
        
        # 计算当前能量
        current_energy = tensor.calculate_energy()
        
        # 计算能量变化
        energy_change = abs(current_energy - initial_energy) / initial_energy
        
        # 计算稳定性
        stability = tensor.measure_stability(initial_state)
        
        # 记录历史数据
        energy_history.append(current_energy)
        coherence_history.append(tensor.metadata['coherence'])
        distribution_history.append(tensor.metadata['distribution'])
        self_similarity_history.append(tensor.metadata['self_similarity'])
        emergence_history.append(tensor.metadata['emergence'])
        stability_history.append(stability)
        
        # 每10步打印一次状态
        if step % 10 == 0 or step == steps - 1:
            print(f"步骤 {step+1}: 能量={current_energy:.6f}, 能量变化={energy_change:.6f}, 稳定性={stability:.6f}")
    
    # 计算最终状态
    final_energy = tensor.calculate_energy()
    energy_conservation = abs(final_energy - initial_energy) / initial_energy
    
    print(f"\n演化完成:")
    print(f"初始能量: {initial_energy:.6f}")
    print(f"最终能量: {final_energy:.6f}")
    print(f"能量守恒度: {1 - energy_conservation:.6f}")
    print(f"最终稳定性: {stability_history[-1]:.6f}")
    print(f"最终相干性: {coherence_history[-1]:.6f}")
    print(f"最终分布性: {distribution_history[-1]:.6f}")
    print(f"最终自相似性: {self_similarity_history[-1]:.6f}")
    print(f"最终涌现性: {emergence_history[-1]:.6f}")
    
    # 绘制演化历史图表
    if plot:
        plt.figure(figsize=(15, 10))
        
        # 能量历史
        plt.subplot(2, 3, 1)
        plt.plot(range(steps+1), energy_history)
        plt.title('能量历史')
        plt.xlabel('步骤')
        plt.ylabel('能量')
        plt.grid(True)
        
        # 稳定性历史
        plt.subplot(2, 3, 2)
        plt.plot(range(steps+1), stability_history)
        plt.title('稳定性历史')
        plt.xlabel('步骤')
        plt.ylabel('稳定性')
        plt.grid(True)
        
        # 相干性历史
        plt.subplot(2, 3, 3)
        plt.plot(range(steps+1), coherence_history)
        plt.title('相干性历史')
        plt.xlabel('步骤')
        plt.ylabel('相干性')
        plt.grid(True)
        
        # 分布性历史
        plt.subplot(2, 3, 4)
        plt.plot(range(steps+1), distribution_history)
        plt.title('分布性历史')
        plt.xlabel('步骤')
        plt.ylabel('分布性')
        plt.grid(True)
        
        # 自相似性历史
        plt.subplot(2, 3, 5)
        plt.plot(range(steps+1), self_similarity_history)
        plt.title('自相似性历史')
        plt.xlabel('步骤')
        plt.ylabel('自相似性')
        plt.grid(True)
        
        # 涌现性历史
        plt.subplot(2, 3, 6)
        plt.plot(range(steps+1), emergence_history)
        plt.title('涌现性历史')
        plt.xlabel('步骤')
        plt.ylabel('涌现性')
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('evolution_history.png')
        print(f"演化历史图表已保存为 'evolution_history.png'")
    
    # 返回结果
    return {
        "initial_energy": initial_energy,
        "final_energy": final_energy,
        "energy_conservation": 1 - energy_conservation,
        "final_stability": stability_history[-1],
        "energy_history": energy_history,
        "stability_history": stability_history,
        "coherence_history": coherence_history,
        "distribution_history": distribution_history,
        "self_similarity_history": self_similarity_history,
        "emergence_history": emergence_history
    }

if __name__ == "__main__":
    test_evolution_stability()
