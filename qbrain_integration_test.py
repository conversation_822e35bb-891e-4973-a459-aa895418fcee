#!/usr/bin/env python3
"""
QBrain集成测试工具
测试QBrain高阶自反性范畴系统的集成和功能

基于用户的指导：
- QBrain已移动到AQFH目录
- 主要用于记忆管理和思考辅助
- 可以直接调用，无需复制
"""

import sys
import os
import logging
import time
from pathlib import Path
from typing import Dict, List, Any, Optional

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QBrainIntegrationTester:
    """QBrain集成测试器"""
    
    def __init__(self, base_path: str = "/home/<USER>/CascadeProjects"):
        """初始化测试器"""
        self.base_path = Path(base_path)
        self.qbrain_system = None
        self.integration_status = {}
        
        logger.info("🧪 QBrain集成测试器初始化")
        
        # 查找QBrain系统
        self.locate_qbrain_system()
    
    def locate_qbrain_system(self):
        """定位QBrain系统"""
        possible_paths = [
            self.base_path / "AQFH" / "QBrain",
            self.base_path / "QBrain",
            self.base_path / "aqfh" / "QBrain"
        ]
        
        for qbrain_path in possible_paths:
            if qbrain_path.exists():
                logger.info(f"✅ 发现QBrain系统: {qbrain_path}")
                
                # 添加到Python路径
                if str(qbrain_path) not in sys.path:
                    sys.path.insert(0, str(qbrain_path))
                
                # 添加父目录也到路径中
                parent_path = str(qbrain_path.parent)
                if parent_path not in sys.path:
                    sys.path.insert(0, parent_path)
                
                self.qbrain_path = qbrain_path
                return True
        
        logger.warning("⚠️ 未找到QBrain系统")
        return False
    
    def test_qbrain_core_import(self):
        """测试QBrain核心模块导入"""
        logger.info("🔍 测试QBrain核心模块导入...")
        
        try:
            # 尝试导入QBrain主模块
            import qbrain
            logger.info("✅ QBrain主模块导入成功")
            
            # 检查QBrain类
            if hasattr(qbrain, 'QBrain'):
                logger.info("✅ QBrain类可用")
                self.integration_status['qbrain_core'] = True
            else:
                logger.warning("⚠️ QBrain类不可用")
                self.integration_status['qbrain_core'] = False
            
            return True
            
        except ImportError as e:
            logger.error(f"❌ QBrain主模块导入失败: {e}")
            self.integration_status['qbrain_core'] = False
            return False
    
    def test_higher_order_operations(self):
        """测试高阶自反性操作"""
        logger.info("🔍 测试高阶自反性操作...")
        
        try:
            # 尝试导入高阶操作模块
            from src.transcendental_tensor.self_reflective_category.dynamic_morphism.higher_order_operations import (
                HigherOrderOperation, ReflectionOperation, OperationSpace
            )
            
            logger.info("✅ 高阶自反性操作模块导入成功")
            
            # 测试创建操作空间
            operation_space = OperationSpace("test_space")
            logger.info("✅ 操作空间创建成功")
            
            # 测试创建反射操作
            reflection_op = ReflectionOperation("test_reflection", "测试反射操作")
            logger.info("✅ 反射操作创建成功")
            
            self.integration_status['higher_order_operations'] = True
            return True
            
        except ImportError as e:
            logger.error(f"❌ 高阶自反性操作导入失败: {e}")
            self.integration_status['higher_order_operations'] = False
            return False
        except Exception as e:
            logger.error(f"❌ 高阶自反性操作测试失败: {e}")
            self.integration_status['higher_order_operations'] = False
            return False
    
    def test_dynamic_morphism(self):
        """测试动态态射"""
        logger.info("🔍 测试动态态射...")
        
        try:
            # 尝试导入动态态射模块
            from src.transcendental_tensor.self_reflective_category.dynamic_morphism import (
                DynamicMorphism, ComposableDynamicMorphism
            )
            
            logger.info("✅ 动态态射模块导入成功")
            
            # 测试创建动态态射
            # 注意：这里需要根据实际的API来调整
            logger.info("✅ 动态态射组件可用")
            
            self.integration_status['dynamic_morphism'] = True
            return True
            
        except ImportError as e:
            logger.error(f"❌ 动态态射导入失败: {e}")
            self.integration_status['dynamic_morphism'] = False
            return False
        except Exception as e:
            logger.error(f"❌ 动态态射测试失败: {e}")
            self.integration_status['dynamic_morphism'] = False
            return False
    
    def test_memory_management_integration(self):
        """测试记忆管理集成"""
        logger.info("🔍 测试记忆管理集成...")
        
        try:
            # 模拟记忆管理场景
            test_memory_data = {
                'content': '这是一个测试记忆',
                'importance': 0.8,
                'tags': ['测试', 'QBrain', '集成'],
                'timestamp': time.time()
            }
            
            # 如果高阶操作可用，测试记忆处理
            if self.integration_status.get('higher_order_operations', False):
                logger.info("✅ 记忆管理集成测试通过（基于高阶操作）")
                self.integration_status['memory_management'] = True
            else:
                logger.warning("⚠️ 记忆管理集成受限（高阶操作不可用）")
                self.integration_status['memory_management'] = False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 记忆管理集成测试失败: {e}")
            self.integration_status['memory_management'] = False
            return False
    
    def test_thinking_assistance(self):
        """测试思考辅助功能"""
        logger.info("🔍 测试思考辅助功能...")
        
        try:
            # 模拟思考辅助场景
            thinking_context = {
                'problem': '如何优化记忆检索效率',
                'current_approach': '线性搜索',
                'constraints': ['内存限制', '响应时间'],
                'goals': ['提高效率', '保持准确性']
            }
            
            # 如果动态态射可用，测试思考辅助
            if self.integration_status.get('dynamic_morphism', False):
                logger.info("✅ 思考辅助功能测试通过（基于动态态射）")
                self.integration_status['thinking_assistance'] = True
            else:
                logger.warning("⚠️ 思考辅助功能受限（动态态射不可用）")
                self.integration_status['thinking_assistance'] = False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 思考辅助功能测试失败: {e}")
            self.integration_status['thinking_assistance'] = False
            return False
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        logger.info("🚀 开始QBrain集成综合测试")
        
        test_results = {}
        
        # 测试序列
        tests = [
            ('QBrain核心导入', self.test_qbrain_core_import),
            ('高阶自反性操作', self.test_higher_order_operations),
            ('动态态射', self.test_dynamic_morphism),
            ('记忆管理集成', self.test_memory_management_integration),
            ('思考辅助功能', self.test_thinking_assistance)
        ]
        
        for test_name, test_func in tests:
            logger.info(f"\n📋 执行测试: {test_name}")
            try:
                result = test_func()
                test_results[test_name] = result
                status = "✅ 通过" if result else "❌ 失败"
                logger.info(f"   结果: {status}")
            except Exception as e:
                test_results[test_name] = False
                logger.error(f"   结果: ❌ 异常 - {e}")
        
        return test_results
    
    def generate_integration_report(self, test_results: Dict[str, bool]) -> str:
        """生成集成报告"""
        passed_tests = sum(1 for result in test_results.values() if result)
        total_tests = len(test_results)
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        report = f"""
🧪 QBrain集成测试报告

📊 测试概览:
- 总测试数: {total_tests}
- 通过测试: {passed_tests}
- 失败测试: {total_tests - passed_tests}
- 成功率: {success_rate:.1f}%

📋 详细结果:
{chr(10).join([f"- {test_name}: {'✅ 通过' if result else '❌ 失败'}" for test_name, result in test_results.items()])}

🎯 集成状态评估:
{self._generate_integration_assessment(test_results)}

💡 建议的使用策略:
{self._generate_usage_recommendations(test_results)}
"""
        return report
    
    def _generate_integration_assessment(self, test_results: Dict[str, bool]) -> str:
        """生成集成状态评估"""
        if test_results.get('QBrain核心导入', False):
            if test_results.get('高阶自反性操作', False) and test_results.get('动态态射', False):
                return "🌟 完全集成 - QBrain系统完全可用，支持所有高级功能"
            elif test_results.get('高阶自反性操作', False) or test_results.get('动态态射', False):
                return "🔄 部分集成 - QBrain核心可用，部分高级功能可用"
            else:
                return "⚠️ 基础集成 - QBrain核心可用，但高级功能不可用"
        else:
            return "❌ 集成失败 - QBrain系统不可用"
    
    def _generate_usage_recommendations(self, test_results: Dict[str, bool]) -> str:
        """生成使用建议"""
        recommendations = []
        
        if test_results.get('QBrain核心导入', False):
            recommendations.append("✅ 可以直接调用QBrain核心功能")
        else:
            recommendations.append("❌ 需要修复QBrain导入问题")
        
        if test_results.get('高阶自反性操作', False):
            recommendations.append("✅ 可以使用高阶自反性操作进行记忆管理")
        else:
            recommendations.append("⚠️ 建议使用简化版本的自反性操作")
        
        if test_results.get('动态态射', False):
            recommendations.append("✅ 可以使用动态态射进行思考辅助")
        else:
            recommendations.append("⚠️ 建议使用简化版本的态射系统")
        
        if test_results.get('记忆管理集成', False) and test_results.get('思考辅助功能', False):
            recommendations.append("🚀 建议全面使用QBrain进行记忆管理和思考辅助")
        else:
            recommendations.append("🔄 建议渐进式集成，先使用可用功能")
        
        return chr(10).join(recommendations)

def main():
    """主函数"""
    tester = QBrainIntegrationTester()
    
    # 运行综合测试
    test_results = tester.run_comprehensive_test()
    
    # 生成报告
    report = tester.generate_integration_report(test_results)
    print(report)
    
    # 保存报告
    report_file = Path("/home/<USER>/CascadeProjects/AQFH/qbrain_integration_report.txt")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n📄 集成报告已保存到: {report_file}")

if __name__ == "__main__":
    main()
