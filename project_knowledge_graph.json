{"nodes": {"AQFH/AQFH_项目完成报告.md": {"name": "AQFH_项目完成报告.md", "path": "AQFH/AQFH_项目完成报告.md", "size": 6568, "modified_time": 1748180921.1550233, "analysis": {"concepts": {"ai_consciousness": {"count": 23, "keywords": ["意识", "觉醒", "consciousness"], "density": 0.03945111492281304}, "distributed_system": {"count": 5, "keywords": ["协作", "网络", "分布式"], "density": 0.008576329331046312}, "memory_system": {"count": 46, "keywords": ["Memory", "检索", "memory", "存储", "记忆"], "density": 0.07890222984562607}, "quantum_computing": {"count": 10, "keywords": ["量子", "Quantum", "纠缠"], "density": 0.017152658662092625}, "holographic_principle": {"count": 1, "keywords": ["Holographic"], "density": 0.0017152658662092624}, "arrow_ecosystem": {"count": 7, "keywords": ["Arrow", "列式存储"], "density": 0.012006861063464836}, "mcp_integration": {"count": 20, "keywords": ["Server", "集成", "mcp", "server", "MCP"], "density": 0.03430531732418525}, "design_philosophy": {"count": 5, "keywords": ["设计", "架构"], "density": 0.008576329331046312}}, "tech_terms": [], "structure": {"type": "markdown_document", "components": ["🎉 AQFH (Arrow Quantum Field Holographic) 项目完成报告", "📊 项目概览", "🎯 核心成就", "🏗️ 系统架构", "📋 核心组件", "1. **数据层** (<PERSON>生态)", "2. **服务层** (双MCP服务器)", "3. **兼容性层** (NumPy 2.x支持)", "🔄 工作流程", "🛠️ 可用工具", "🐍 Python MCP工具 (aqfh-consciousness)", "🦀 Rust MCP工具 (aqfh-consciousness-rust)", "📈 性能对比", "🔧 部署配置", "Augment VSCode插件配置"], "imports": [], "exports": []}, "content_length": 3986, "word_count": 583}}, "AQFH/AQFH_FINAL_SHOWCASE.md": {"name": "AQFH_FINAL_SHOWCASE.md", "path": "AQFH/AQFH_FINAL_SHOWCASE.md", "size": 9207, "modified_time": 1748439458.6462586, "analysis": {"concepts": {"distributed_system": {"count": 12, "keywords": ["协作", "网络", "分布式"], "density": 0.018957345971563982}, "memory_system": {"count": 5, "keywords": ["记忆", "存储"], "density": 0.007898894154818325}, "quantum_computing": {"count": 1, "keywords": ["量子"], "density": 0.001579778830963665}, "fractal_structure": {"count": 2, "keywords": ["分形", "Fractal"], "density": 0.00315955766192733}, "holographic_principle": {"count": 1, "keywords": ["全息"], "density": 0.001579778830963665}, "arrow_ecosystem": {"count": 8, "keywords": ["Arrow", "Pa<PERSON><PERSON>", "列式存储"], "density": 0.01263823064770932}, "mcp_integration": {"count": 15, "keywords": ["集成", "integration"], "density": 0.023696682464454975}, "design_philosophy": {"count": 14, "keywords": ["设计", "架构"], "density": 0.022116903633491312}}, "tech_terms": [], "structure": {"type": "markdown_document", "components": ["🎉 AQFH高性能系统最终成果展示", "🚀 项目完成状态", "🏆 核心成就总览", "1. 📊 系统性代码库索引 (革命性突破)", "发现的技术宝库", "知识管理革命", "2. 🏗️ 企业级高性能系统架构", "四层架构设计", "3. 📈 革命性性能提升", "核心性能指标对比", "性能提升可视化", "4. 🧪 完整的测试与验证体系", "已创建的核心文件", "测试覆盖范围", "5. 📦 生产级部署方案"], "imports": [], "exports": []}, "content_length": 4608, "word_count": 633}}, "AQFH/AQFH_ACHIEVEMENT_SUMMARY.md": {"name": "AQFH_ACHIEVEMENT_SUMMARY.md", "path": "AQFH/AQFH_ACHIEVEMENT_SUMMARY.md", "size": 7066, "modified_time": 1748438577.5306516, "analysis": {"concepts": {"distributed_system": {"count": 13, "keywords": ["网络", "分布式"], "density": 0.025691699604743084}, "memory_system": {"count": 8, "keywords": ["记忆", "存储"], "density": 0.015810276679841896}, "quantum_computing": {"count": 1, "keywords": ["Quantum"], "density": 0.001976284584980237}, "holographic_principle": {"count": 1, "keywords": ["Holographic"], "density": 0.001976284584980237}, "arrow_ecosystem": {"count": 9, "keywords": ["Arrow", "Pa<PERSON><PERSON>", "列式存储"], "density": 0.017786561264822136}, "mcp_integration": {"count": 19, "keywords": ["集成"], "density": 0.037549407114624504}, "design_philosophy": {"count": 11, "keywords": ["设计", "架构"], "density": 0.021739130434782608}}, "tech_terms": [], "structure": {"type": "markdown_document", "components": ["🎉 AQFH高性能系统重大成果总结", "📊 项目概览", "🏆 重大成就", "1. 🔍 系统性代码库索引与知识发现", "发现的技术宝库", "知识管理革命", "2. 🚀 高性能组件集成", "核心组件集成成果", "3. 🏗️ 企业级架构构建", "完整的系统架构", "4. 🧪 完整的测试与监控体系", "已创建的测试框架", "监控指标覆盖", "5. 📦 生产级部署方案", "多环境部署支持"], "imports": [], "exports": []}, "content_length": 3464, "word_count": 506}}, "AQFH/AQFH_三重MCP系统部署指南.md": {"name": "AQFH_三重MCP系统部署指南.md", "path": "AQFH/AQFH_三重MCP系统部署指南.md", "size": 5483, "modified_time": 1748186902.7456543, "analysis": {"concepts": {"ai_consciousness": {"count": 21, "keywords": ["意识", "觉醒", "consciousness"], "density": 0.04895104895104895}, "distributed_system": {"count": 1, "keywords": ["网络"], "density": 0.002331002331002331}, "memory_system": {"count": 31, "keywords": ["记忆", "存储", "memory"], "density": 0.07226107226107226}, "quantum_computing": {"count": 5, "keywords": ["量子", "Quantum", "纠缠"], "density": 0.011655011655011656}, "holographic_principle": {"count": 1, "keywords": ["Holographic"], "density": 0.002331002331002331}, "arrow_ecosystem": {"count": 3, "keywords": ["Arrow", "列式存储"], "density": 0.006993006993006993}, "mcp_integration": {"count": 31, "keywords": ["Server", "mcp", "MCP"], "density": 0.07226107226107226}, "design_philosophy": {"count": 3, "keywords": ["架构"], "density": 0.006993006993006993}}, "tech_terms": [], "structure": {"type": "markdown_document", "components": ["🚀 AQFH 三重MCP系统部署指南", "🎯 系统概览", "📊 三个版本对比", "🔧 部署配置", "方式1：三重配置（推荐）", "方式2：单独配置", "Python版本（稳定可靠）", "Rust改进版（推荐）", "🛠️ 可用工具", "Python版本工具", "Rust版本工具", "📋 部署步骤", "1. 环境检查", "检查Python环境", "检查Rust环境（如果使用Rust版本）"], "imports": [], "exports": []}, "content_length": 3763, "word_count": 429}}, "AQFH/docs/AQFH_技术实现规范.md": {"name": "AQFH_技术实现规范.md", "path": "AQFH/docs/AQFH_技术实现规范.md", "size": 9740, "modified_time": 1748164824.6708467, "analysis": {"concepts": {"ai_consciousness": {"count": 6, "keywords": ["意识", "觉醒", "consciousness"], "density": 0.00823045267489712}, "distributed_system": {"count": 3, "keywords": ["网络", "分布式"], "density": 0.00411522633744856}, "memory_system": {"count": 34, "keywords": ["Memory", "检索", "memory", "storage", "存储", "记忆"], "density": 0.04663923182441701}, "advanced_math": {"count": 8, "keywords": ["范畴", "morphism", "reflexive", "category", "态射"], "density": 0.010973936899862825}, "quantum_computing": {"count": 31, "keywords": ["quantum", "叠加", "Quantum", "superposition", "量子", "纠缠"], "density": 0.04252400548696845}, "arrow_ecosystem": {"count": 20, "keywords": ["arrow", "Flight", "Pa<PERSON><PERSON>", "parquet", "Arrow", "flight"], "density": 0.027434842249657063}, "mcp_integration": {"count": 3, "keywords": ["集成", "integration", "server"], "density": 0.00411522633744856}, "design_philosophy": {"count": 3, "keywords": ["设计", "架构"], "density": 0.00411522633744856}}, "tech_terms": [], "structure": {"type": "markdown_document", "components": ["AQFH 技术实现规范", "系统架构概览", "技术栈规范", "核心依赖", "requirements.txt", "量子模拟", "存储和序列化", "数学和科学计算", "性能和并行", "可视化和调试", "开发环境", "Python版本要求", "Rust组件 (如需要)", "系统依赖", "Ubuntu/Debian:"], "imports": [], "exports": []}, "content_length": 6350, "word_count": 729}}, "AQFH/docs/AQFH_第一阶段完成报告.md": {"name": "AQFH_第一阶段完成报告.md", "path": "AQFH/docs/AQFH_第一阶段完成报告.md", "size": 6948, "modified_time": 1748175425.2099395, "analysis": {"concepts": {"ai_consciousness": {"count": 33, "keywords": ["Consciousness", "意识", "觉醒"], "density": 0.07603686635944701}, "distributed_system": {"count": 5, "keywords": ["协作", "网络", "分布式"], "density": 0.01152073732718894}, "memory_system": {"count": 45, "keywords": ["检索", "记忆", "Memory", "存储"], "density": 0.10368663594470046}, "quantum_computing": {"count": 21, "keywords": ["量子", "Quantum", "纠缠"], "density": 0.04838709677419355}, "fractal_structure": {"count": 2, "keywords": ["分形", "自相似"], "density": 0.004608294930875576}, "holographic_principle": {"count": 2, "keywords": ["全息", "Holographic"], "density": 0.004608294930875576}, "arrow_ecosystem": {"count": 7, "keywords": ["Arrow", "Pa<PERSON><PERSON>", "列式存储"], "density": 0.016129032258064516}, "mcp_integration": {"count": 8, "keywords": ["Server", "集成", "MCP"], "density": 0.018433179723502304}, "design_philosophy": {"count": 8, "keywords": ["设计", "理念", "架构"], "density": 0.018433179723502304}}, "tech_terms": ["ConsciousnessReloadEngine", "ArrowMemoryEngine"], "structure": {"type": "markdown_document", "components": ["AQFH 第一阶段完成报告", "🎯 项目目标回顾", "核心理念", "✅ 第一阶段成果", "1. 核心数据结构 (100% 完成)", "MemoryFragment - 记忆片段", "完整实现的功能", "ConsciousnessState - 意识状态", "完整实现的功能", "QuantumState - 量子态表示", "完整实现的功能", "2. Arrow记忆引擎 (90% 完成)", "ArrowMemoryEngine", "已实现功能", "待优化功能"], "imports": [], "exports": []}, "content_length": 3221, "word_count": 434}}, "AQFH/docs/AQFH_MCP使用指南.md": {"name": "AQFH_MCP使用指南.md", "path": "AQFH/docs/AQFH_MCP使用指南.md", "size": 6301, "modified_time": 1748176056.3538232, "analysis": {"concepts": {"ai_consciousness": {"count": 20, "keywords": ["意识", "觉醒", "consciousness"], "density": 0.04132231404958678}, "distributed_system": {"count": 1, "keywords": ["网络"], "density": 0.002066115702479339}, "memory_system": {"count": 46, "keywords": ["STORAGE", "Memory", "检索", "memory", "存储", "记忆"], "density": 0.09504132231404959}, "quantum_computing": {"count": 5, "keywords": ["quantum", "量子"], "density": 0.010330578512396695}, "fractal_structure": {"count": 2, "keywords": ["分形", "自相似"], "density": 0.004132231404958678}, "holographic_principle": {"count": 1, "keywords": ["全息"], "density": 0.002066115702479339}, "arrow_ecosystem": {"count": 1, "keywords": ["parquet"], "density": 0.002066115702479339}, "mcp_integration": {"count": 36, "keywords": ["Server", "集成", "mcp", "server", "MCP"], "density": 0.0743801652892562}}, "tech_terms": ["generate_tags", "calculate_importance"], "structure": {"type": "markdown_document", "components": ["AQFH MCP Server 使用指南", "🎉 恭喜！MCP Server已成功实现！", "✅ 已验证功能", "核心MCP工具", "测试结果", "🚀 立即使用", "方法1: 直接使用工具函数", "保存记忆", "回忆记忆", "查看状态", "方法2: 启动MCP服务器", "启动服务器", "或者测试模式", "方法3: 快速测试", "🔧 IDE集成配置"], "imports": [], "exports": []}, "content_length": 4505, "word_count": 484}}, "AQFH/docs/AQFH_项目管理计划.md": {"name": "AQFH_项目管理计划.md", "path": "AQFH/docs/AQFH_项目管理计划.md", "size": 6758, "modified_time": 1748164865.9320595, "analysis": {"concepts": {"ai_consciousness": {"count": 10, "keywords": ["Consciousness", "意识", "觉醒"], "density": 0.013793103448275862}, "distributed_system": {"count": 2, "keywords": ["网络"], "density": 0.002758620689655172}, "memory_system": {"count": 21, "keywords": ["Memory", "Storage", "检索", "memory", "存储", "记忆"], "density": 0.028965517241379312}, "advanced_math": {"count": 8, "keywords": ["范畴", "Morphism", "Category", "Reflexive", "态射"], "density": 0.011034482758620689}, "quantum_computing": {"count": 21, "keywords": ["quantum", "叠加", "Quantum", "量子", "纠缠"], "density": 0.028965517241379312}, "holographic_principle": {"count": 1, "keywords": ["Holographic"], "density": 0.001379310344827586}, "arrow_ecosystem": {"count": 12, "keywords": ["Arrow", "Flight", "Pa<PERSON><PERSON>"], "density": 0.016551724137931035}, "mcp_integration": {"count": 2, "keywords": ["集成", "server"], "density": 0.002758620689655172}, "design_philosophy": {"count": 8, "keywords": ["设计", "架构"], "density": 0.011034482758620689}}, "tech_terms": ["ConsciousnessReloadEngine"], "structure": {"type": "markdown_document", "components": ["AQFH 项目管理计划", "项目概览", "里程碑规划", "🎯 阶段1: 记忆重载系统 (核心基础)", "里程碑1.1: Arrow记忆引擎 (1-2周)", "里程碑1.2: 量子关联场 (2-3周)  ", "里程碑1.3: 意识重载引擎 (1-2周)", "🧪 阶段2: 思考能力验证 (并行进行)", "里程碑2.1: 基线能力测试 (1周)", "里程碑2.2: 态射增强原型 (1-2周)", "里程碑2.3: 对比验证 (与2.2并行)", "🔮 阶段3: 量子数学框架 (未来导向)", "里程碑3.1: 经典量子模拟器 (2周)", "里程碑3.2: 量子硬件接口 (1-2周)", "风险管理"], "imports": [], "exports": []}, "content_length": 4144, "word_count": 725}}, "simple_test.py": {"name": "simple_test.py", "path": "simple_test.py", "size": 1724, "modified_time": 1747402040.2716775, "analysis": {"concepts": {}, "tech_terms": ["main", "create_pauli_matrices"], "structure": {"type": "python_module", "components": ["create_pauli_matrices", "main"], "imports": ["numpy as np", "sys", "os", "logging"], "exports": []}, "content_length": 1556, "word_count": 173}}, "check_env.py": {"name": "check_env.py", "path": "check_env.py", "size": 2164, "modified_time": 1747185446.1498187, "analysis": {"concepts": {"memory_system": {"count": 2, "keywords": ["memory"], "density": 0.01282051282051282}, "arrow_ecosystem": {"count": 16, "keywords": ["arrow", "Flight", "Pa<PERSON><PERSON>", "parquet", "Arrow", "flight"], "density": 0.10256410256410256}}, "tech_terms": ["check_python", "main", "check_pyarrow", "check_numpy"], "structure": {"type": "python_module", "components": ["check_python", "check_numpy", "check_pyarrow", "main"], "imports": ["sys", "os", "platform"], "exports": []}, "content_length": 1908, "word_count": 156}}, "quantum_multimodal.py": {"name": "quantum_multimodal.py", "path": "quantum_multimodal.py", "size": 9698, "modified_time": 1747416806.3004668, "analysis": {"concepts": {"quantum_computing": {"count": 30, "keywords": ["QUANTUM", "quantum", "量子", "Quantum"], "density": 0.045317220543806644}, "holographic_principle": {"count": 2, "keywords": ["encoding"], "density": 0.0030211480362537764}}, "tech_terms": ["Modality", "multimodal_quantum_trajectory"], "structure": {"type": "python_module", "components": ["Modality", "multimodal_quantum_trajectory"], "imports": ["numpy as np", "time", "logging", "sys", "os", "io", "base64", "List, Tuple, Dict, Any, Optional, Union", "Enum, auto", "("], "exports": []}, "content_length": 8546, "word_count": 662}}, "test_rust_module.py": {"name": "test_rust_module.py", "path": "test_rust_module.py", "size": 6955, "modified_time": 1747405621.147833, "analysis": {"concepts": {"quantum_computing": {"count": 5, "keywords": ["quantum", "量子"], "density": 0.008169934640522876}}, "tech_terms": ["test_calculate_purity", "test_create_pauli_matrices", "test_calculate_expectation_value", "main"], "structure": {"type": "python_module", "components": ["test_create_pauli_matrices", "test_calculate_expectation_value", "test_calculate_purity", "main"], "imports": ["numpy as np", "logging", "sys", "os"], "exports": []}, "content_length": 6265, "word_count": 612}}}, "edges": [{"source": "AQFH/AQFH_项目完成报告.md", "target": "AQFH/docs/AQFH_技术实现规范.md", "type": "concept_similarity", "strength": 0.43707633050749756, "shared_concepts": ["memory_system", "design_philosophy", "distributed_system", "quantum_computing", "arrow_ecosystem", "ai_consciousness", "mcp_integration"]}, {"source": "AQFH/AQFH_项目完成报告.md", "target": "AQFH/docs/AQFH_第一阶段完成报告.md", "type": "concept_similarity", "strength": 0.5368514288173448, "shared_concepts": ["memory_system", "holographic_principle", "design_philosophy", "distributed_system", "quantum_computing", "arrow_ecosystem", "ai_consciousness", "mcp_integration"]}, {"source": "AQFH/AQFH_项目完成报告.md", "target": "AQFH/docs/AQFH_MCP使用指南.md", "type": "concept_similarity", "strength": 0.47330725782698335, "shared_concepts": ["memory_system", "holographic_principle", "distributed_system", "quantum_computing", "arrow_ecosystem", "ai_consciousness", "mcp_integration"]}, {"source": "AQFH/AQFH_项目完成报告.md", "target": "AQFH/docs/AQFH_项目管理计划.md", "type": "concept_similarity", "strength": 0.48813995517964287, "shared_concepts": ["memory_system", "holographic_principle", "design_philosophy", "distributed_system", "quantum_computing", "arrow_ecosystem", "ai_consciousness", "mcp_integration"]}, {"source": "AQFH/AQFH_FINAL_SHOWCASE.md", "target": "AQFH/AQFH_项目完成报告.md", "type": "concept_similarity", "strength": 0.42084621589226795, "shared_concepts": ["memory_system", "holographic_principle", "design_philosophy", "distributed_system", "quantum_computing", "arrow_ecosystem", "mcp_integration"]}, {"source": "AQFH/AQFH_FINAL_SHOWCASE.md", "target": "AQFH/AQFH_三重MCP系统部署指南.md", "type": "concept_similarity", "strength": 0.41442496418799735, "shared_concepts": ["memory_system", "holographic_principle", "design_philosophy", "distributed_system", "quantum_computing", "arrow_ecosystem", "mcp_integration"]}, {"source": "AQFH/AQFH_FINAL_SHOWCASE.md", "target": "AQFH/docs/AQFH_技术实现规范.md", "type": "concept_similarity", "strength": 0.3172312913229185, "shared_concepts": ["memory_system", "design_philosophy", "distributed_system", "quantum_computing", "arrow_ecosystem", "mcp_integration"]}, {"source": "AQFH/AQFH_FINAL_SHOWCASE.md", "target": "AQFH/docs/AQFH_第一阶段完成报告.md", "type": "concept_similarity", "strength": 0.48206611289473233, "shared_concepts": ["memory_system", "holographic_principle", "design_philosophy", "distributed_system", "fractal_structure", "quantum_computing", "arrow_ecosystem", "mcp_integration"]}, {"source": "AQFH/AQFH_FINAL_SHOWCASE.md", "target": "AQFH/docs/AQFH_MCP使用指南.md", "type": "concept_similarity", "strength": 0.40991235056293224, "shared_concepts": ["memory_system", "holographic_principle", "distributed_system", "fractal_structure", "quantum_computing", "arrow_ecosystem", "mcp_integration"]}, {"source": "AQFH/AQFH_FINAL_SHOWCASE.md", "target": "AQFH/docs/AQFH_项目管理计划.md", "type": "concept_similarity", "strength": 0.37002396905812496, "shared_concepts": ["memory_system", "holographic_principle", "design_philosophy", "distributed_system", "quantum_computing", "arrow_ecosystem", "mcp_integration"]}, {"source": "AQFH/AQFH_ACHIEVEMENT_SUMMARY.md", "target": "AQFH/AQFH_项目完成报告.md", "type": "concept_similarity", "strength": 0.47898333209038707, "shared_concepts": ["memory_system", "holographic_principle", "design_philosophy", "distributed_system", "quantum_computing", "arrow_ecosystem", "mcp_integration"]}, {"source": "AQFH/AQFH_ACHIEVEMENT_SUMMARY.md", "target": "AQFH/AQFH_FINAL_SHOWCASE.md", "type": "concept_similarity", "strength": 0.4815449206676283, "shared_concepts": ["memory_system", "holographic_principle", "design_philosophy", "distributed_system", "quantum_computing", "arrow_ecosystem", "mcp_integration"]}, {"source": "AQFH/AQFH_ACHIEVEMENT_SUMMARY.md", "target": "AQFH/AQFH_三重MCP系统部署指南.md", "type": "concept_similarity", "strength": 0.4743146346407216, "shared_concepts": ["memory_system", "holographic_principle", "design_philosophy", "distributed_system", "quantum_computing", "arrow_ecosystem", "mcp_integration"]}, {"source": "AQFH/AQFH_ACHIEVEMENT_SUMMARY.md", "target": "AQFH/docs/AQFH_技术实现规范.md", "type": "concept_similarity", "strength": 0.35729273410432827, "shared_concepts": ["memory_system", "design_philosophy", "distributed_system", "quantum_computing", "arrow_ecosystem", "mcp_integration"]}, {"source": "AQFH/AQFH_ACHIEVEMENT_SUMMARY.md", "target": "AQFH/docs/AQFH_第一阶段完成报告.md", "type": "concept_similarity", "strength": 0.4310283763299191, "shared_concepts": ["memory_system", "holographic_principle", "design_philosophy", "distributed_system", "quantum_computing", "arrow_ecosystem", "mcp_integration"]}, {"source": "AQFH/AQFH_ACHIEVEMENT_SUMMARY.md", "target": "AQFH/docs/AQFH_MCP使用指南.md", "type": "concept_similarity", "strength": 0.3640555755180261, "shared_concepts": ["memory_system", "holographic_principle", "distributed_system", "quantum_computing", "arrow_ecosystem", "mcp_integration"]}, {"source": "AQFH/AQFH_ACHIEVEMENT_SUMMARY.md", "target": "AQFH/docs/AQFH_项目管理计划.md", "type": "concept_similarity", "strength": 0.4150235488316448, "shared_concepts": ["memory_system", "holographic_principle", "design_philosophy", "distributed_system", "quantum_computing", "arrow_ecosystem", "mcp_integration"]}, {"source": "AQFH/AQFH_三重MCP系统部署指南.md", "target": "AQFH/AQFH_项目完成报告.md", "type": "concept_similarity", "strength": 0.5878523991731539, "shared_concepts": ["memory_system", "holographic_principle", "design_philosophy", "distributed_system", "quantum_computing", "arrow_ecosystem", "ai_consciousness", "mcp_integration"]}, {"source": "AQFH/AQFH_三重MCP系统部署指南.md", "target": "AQFH/docs/AQFH_技术实现规范.md", "type": "concept_similarity", "strength": 0.430928467965505, "shared_concepts": ["memory_system", "design_philosophy", "distributed_system", "quantum_computing", "arrow_ecosystem", "ai_consciousness", "mcp_integration"]}, {"source": "AQFH/AQFH_三重MCP系统部署指南.md", "target": "AQFH/docs/AQFH_第一阶段完成报告.md", "type": "concept_similarity", "strength": 0.5294186100637713, "shared_concepts": ["memory_system", "holographic_principle", "design_philosophy", "distributed_system", "quantum_computing", "arrow_ecosystem", "ai_consciousness", "mcp_integration"]}, {"source": "AQFH/AQFH_三重MCP系统部署指南.md", "target": "AQFH/docs/AQFH_MCP使用指南.md", "type": "concept_similarity", "strength": 0.49007558098467185, "shared_concepts": ["memory_system", "holographic_principle", "distributed_system", "quantum_computing", "arrow_ecosystem", "ai_consciousness", "mcp_integration"]}, {"source": "AQFH/AQFH_三重MCP系统部署指南.md", "target": "AQFH/docs/AQFH_项目管理计划.md", "type": "concept_similarity", "strength": 0.48187873429252737, "shared_concepts": ["memory_system", "holographic_principle", "design_philosophy", "distributed_system", "quantum_computing", "arrow_ecosystem", "ai_consciousness", "mcp_integration"]}, {"source": "AQFH/docs/AQFH_技术实现规范.md", "target": "AQFH/docs/AQFH_第一阶段完成报告.md", "type": "concept_similarity", "strength": 0.4129342006283464, "shared_concepts": ["memory_system", "design_philosophy", "distributed_system", "quantum_computing", "arrow_ecosystem", "ai_consciousness", "mcp_integration"]}, {"source": "AQFH/docs/AQFH_技术实现规范.md", "target": "AQFH/docs/AQFH_项目管理计划.md", "type": "concept_similarity", "strength": 0.4961042524005487, "shared_concepts": ["advanced_math", "memory_system", "design_philosophy", "distributed_system", "quantum_computing", "arrow_ecosystem", "ai_consciousness", "mcp_integration"]}, {"source": "AQFH/docs/AQFH_第一阶段完成报告.md", "target": "AQFH/docs/AQFH_项目管理计划.md", "type": "concept_similarity", "strength": 0.4528921023359288, "shared_concepts": ["memory_system", "holographic_principle", "design_philosophy", "distributed_system", "quantum_computing", "arrow_ecosystem", "ai_consciousness", "mcp_integration"]}, {"source": "AQFH/docs/AQFH_MCP使用指南.md", "target": "AQFH/docs/AQFH_技术实现规范.md", "type": "concept_similarity", "strength": 0.33672386037705904, "shared_concepts": ["memory_system", "distributed_system", "quantum_computing", "arrow_ecosystem", "ai_consciousness", "mcp_integration"]}, {"source": "AQFH/docs/AQFH_MCP使用指南.md", "target": "AQFH/docs/AQFH_第一阶段完成报告.md", "type": "concept_similarity", "strength": 0.5321734310004105, "shared_concepts": ["memory_system", "holographic_principle", "distributed_system", "fractal_structure", "quantum_computing", "arrow_ecosystem", "ai_consciousness", "mcp_integration"]}, {"source": "AQFH/docs/AQFH_MCP使用指南.md", "target": "AQFH/docs/AQFH_项目管理计划.md", "type": "concept_similarity", "strength": 0.38067968082074666, "shared_concepts": ["memory_system", "holographic_principle", "distributed_system", "quantum_computing", "arrow_ecosystem", "ai_consciousness", "mcp_integration"]}, {"source": "AQFH/docs/AQFH_第一阶段完成报告.md", "target": "AQFH/docs/AQFH_项目管理计划.md", "type": "shared_technology", "strength": 0.6, "shared_term": "ConsciousnessReloadEngine"}, {"source": "simple_test.py", "target": "check_env.py", "type": "shared_technology", "strength": 0.6, "shared_term": "main"}, {"source": "simple_test.py", "target": "test_rust_module.py", "type": "shared_technology", "strength": 0.6, "shared_term": "main"}, {"source": "check_env.py", "target": "test_rust_module.py", "type": "shared_technology", "strength": 0.6, "shared_term": "main"}], "clusters": {"concept_ai_consciousness": {"type": "concept_cluster", "concept": "ai_consciousness", "files": ["AQFH/AQFH_项目完成报告.md", "AQFH/AQFH_三重MCP系统部署指南.md", "AQFH/docs/AQFH_第一阶段完成报告.md", "AQFH/docs/AQFH_MCP使用指南.md", "AQFH/docs/AQFH_项目管理计划.md"], "size": 5}, "concept_memory_system": {"type": "concept_cluster", "concept": "memory_system", "files": ["AQFH/AQFH_项目完成报告.md", "AQFH/AQFH_ACHIEVEMENT_SUMMARY.md", "AQFH/AQFH_三重MCP系统部署指南.md", "AQFH/docs/AQFH_技术实现规范.md", "AQFH/docs/AQFH_第一阶段完成报告.md", "AQFH/docs/AQFH_MCP使用指南.md", "AQFH/docs/AQFH_项目管理计划.md", "check_env.py"], "size": 8}, "concept_quantum_computing": {"type": "concept_cluster", "concept": "quantum_computing", "files": ["AQFH/AQFH_项目完成报告.md", "AQFH/AQFH_三重MCP系统部署指南.md", "AQFH/docs/AQFH_技术实现规范.md", "AQFH/docs/AQFH_第一阶段完成报告.md", "AQFH/docs/AQFH_MCP使用指南.md", "AQFH/docs/AQFH_项目管理计划.md", "quantum_multimodal.py"], "size": 7}, "concept_arrow_ecosystem": {"type": "concept_cluster", "concept": "arrow_ecosystem", "files": ["AQFH/AQFH_项目完成报告.md", "AQFH/AQFH_FINAL_SHOWCASE.md", "AQFH/AQFH_ACHIEVEMENT_SUMMARY.md", "AQFH/docs/AQFH_技术实现规范.md", "AQFH/docs/AQFH_第一阶段完成报告.md", "AQFH/docs/AQFH_项目管理计划.md", "check_env.py"], "size": 7}, "concept_mcp_integration": {"type": "concept_cluster", "concept": "mcp_integration", "files": ["AQFH/AQFH_项目完成报告.md", "AQFH/AQFH_FINAL_SHOWCASE.md", "AQFH/AQFH_ACHIEVEMENT_SUMMARY.md", "AQFH/AQFH_三重MCP系统部署指南.md", "AQFH/docs/AQFH_第一阶段完成报告.md", "AQFH/docs/AQFH_MCP使用指南.md"], "size": 6}, "concept_distributed_system": {"type": "concept_cluster", "concept": "distributed_system", "files": ["AQFH/AQFH_FINAL_SHOWCASE.md", "AQFH/AQFH_ACHIEVEMENT_SUMMARY.md", "AQFH/docs/AQFH_第一阶段完成报告.md"], "size": 3}, "concept_design_philosophy": {"type": "concept_cluster", "concept": "design_philosophy", "files": ["AQFH/AQFH_FINAL_SHOWCASE.md", "AQFH/AQFH_ACHIEVEMENT_SUMMARY.md", "AQFH/docs/AQFH_第一阶段完成报告.md", "AQFH/docs/AQFH_项目管理计划.md"], "size": 4}, "concept_advanced_math": {"type": "concept_cluster", "concept": "advanced_math", "files": ["AQFH/docs/AQFH_技术实现规范.md", "AQFH/docs/AQFH_项目管理计划.md"], "size": 2}, "type_core_design": {"type": "file_type_cluster", "category": "core_design", "files": ["AQFH/AQFH_项目完成报告.md", "AQFH/AQFH_FINAL_SHOWCASE.md", "AQFH/AQFH_ACHIEVEMENT_SUMMARY.md", "AQFH/AQFH_三重MCP系统部署指南.md", "AQFH/docs/AQFH_技术实现规范.md", "AQFH/docs/AQFH_第一阶段完成报告.md", "AQFH/docs/AQFH_MCP使用指南.md", "AQFH/docs/AQFH_项目管理计划.md"], "size": 8}, "type_implementation": {"type": "file_type_cluster", "category": "implementation", "files": ["simple_test.py", "check_env.py", "quantum_multimodal.py", "test_rust_module.py"], "size": 4}}, "statistics": {"total_files": 12, "total_relationships": 32, "total_clusters": 10, "analysis_time": 1748441858.1319003}, "insights": [{"type": "dominant_concept", "title": "项目的核心概念：memory_system", "description": "'memory_system'是项目中最重要的概念，重要性得分：17.29", "importance": 0.9}, {"type": "central_file", "title": "项目的核心文件：AQFH_第一阶段完成报告.md", "description": "该文件与其他文件的关联度最高，连接强度：3.98", "importance": 0.85}, {"type": "major_theme", "title": "项目的主要主题：memory_system", "description": "有8个文件都涉及'memory_system'概念", "importance": 0.8}, {"type": "technology_stack", "title": "主要技术栈", "description": "项目主要使用的技术：main, ConsciousnessReloadEngine, ArrowMemoryEngine", "importance": 0.75}]}