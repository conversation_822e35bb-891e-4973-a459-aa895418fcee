#!/usr/bin/env python3
"""
测试PyO3 0.24+兼容版本的自解释性算子
"""

import time
import json
from pprint import pprint

# 导入算子
import self_explainability_v24

def test_basic_functionality():
    """测试基本功能"""
    print("测试基本功能...")

    # 创建自解释性算子
    operator = self_explainability_v24.SelfExplainabilityOperator(
        explanation_type="hybrid",
        explanation_format="mixed",
        explanation_complexity="adaptive",
        feature_importance_threshold=0.05,
        counterfactual_count=3,
        example_count=3,
        rule_count=5
    )

    # 测试输入数据
    input_data = {
        "features": {
            "text_length": 500,
            "sentiment_score": 0.75,
            "topic_relevance": 0.9,
            "user_engagement": 0.8,
            "readability": 0.65
        },
        "prediction": "positive",
        "confidence": 0.85,
        "model_type": "classification",
        "domain": "sentiment_analysis"
    }

    # 应用算子
    result = operator.apply(input_data)

    # 检查结果
    assert "id" in result
    assert "summary" in result
    assert "feature_importances" in result
    assert "counterfactuals" in result
    assert "examples" in result
    assert "rules" in result
    assert "confidence" in result
    assert "stability" in result
    assert "consistency" in result

    # 打印结果
    print("解释ID:", result["id"])
    print("解释摘要:", result["summary"])
    print("解释类型:", result["explanation_type"])
    print("解释格式:", result["explanation_format"])
    print("解释复杂度:", result["explanation_complexity"])
    print("解释置信度:", result["confidence"])
    print("解释稳定性:", result["stability"])
    print("解释一致性:", result["consistency"])

    print("\n特征重要性:")
    for feature in result["feature_importances"]:
        print(f"  - {feature['name']}: {feature['importance']:.4f}")

    print("\n反事实解释:")
    for i, counterfactual in enumerate(result["counterfactuals"]):
        print(f"  反事实 {i+1}:")
        print(f"    原始预测: {counterfactual['original_prediction']}")
        print(f"    反事实预测: {counterfactual['counterfactual_prediction']}")
        print(f"    改变的特征: {', '.join(counterfactual['changed_features'])}")
        print(f"    反事实距离: {counterfactual['distance']:.4f}")
        print(f"    反事实可行性: {counterfactual['feasibility']:.4f}")

    print("\n示例解释:")
    for i, example in enumerate(result["examples"]):
        print(f"  示例 {i+1}:")
        print(f"    ID: {example['id']}")
        print(f"    相似度: {example['similarity']:.4f}")
        print(f"    预测: {example['prediction']}")

    print("\n规则解释:")
    for i, rule in enumerate(result["rules"]):
        print(f"  规则 {i+1}:")
        print(f"    ID: {rule['id']}")
        print(f"    条件: {' 且 '.join(rule['conditions'])}")
        print(f"    结果: {rule['outcome']}")
        print(f"    置信度: {rule['confidence']:.4f}")
        print(f"    支持度: {rule['support']:.4f}")

    print("基本功能测试通过！")

def test_explanation_types():
    """测试不同的解释类型"""
    print("\n测试不同的解释类型...")

    # 解释类型
    explanation_types = [
        "feature_importance", "local", "global",
        "counterfactual", "example", "rule", "hybrid"
    ]

    # 测试输入数据
    input_data = {
        "features": {
            "text_length": 500,
            "sentiment_score": 0.75,
            "topic_relevance": 0.9,
            "user_engagement": 0.8,
            "readability": 0.65
        },
        "prediction": "positive",
        "confidence": 0.85
    }

    for explanation_type in explanation_types:
        print(f"\n解释类型: {explanation_type}")

        # 创建自解释性算子
        operator = self_explainability_v24.SelfExplainabilityOperator(
            explanation_type=explanation_type,
            explanation_format="mixed",
            explanation_complexity="adaptive"
        )

        # 应用算子
        result = operator.apply(input_data)

        # 打印结果
        print(f"解释摘要: {result['summary']}")

        # 检查特定解释类型的结果
        if explanation_type == "feature_importance":
            assert len(result["feature_importances"]) > 0
            print(f"特征重要性数量: {len(result['feature_importances'])}")
        elif explanation_type == "counterfactual":
            assert len(result["counterfactuals"]) > 0
            print(f"反事实解释数量: {len(result['counterfactuals'])}")
        elif explanation_type == "example":
            assert len(result["examples"]) > 0
            print(f"示例解释数量: {len(result['examples'])}")
        elif explanation_type == "rule":
            assert len(result["rules"]) > 0
            print(f"规则解释数量: {len(result['rules'])}")
        elif explanation_type == "hybrid":
            assert len(result["feature_importances"]) > 0
            assert len(result["counterfactuals"]) > 0
            assert len(result["examples"]) > 0
            assert len(result["rules"]) > 0
            print(f"混合解释包含多种解释类型")

    print("\n不同解释类型测试通过！")

def test_explanation_formats():
    """测试不同的解释格式"""
    print("\n测试不同的解释格式...")

    # 解释格式
    explanation_formats = ["text", "visual", "tabular", "mixed"]

    # 测试输入数据
    input_data = {
        "features": {
            "text_length": 500,
            "sentiment_score": 0.75,
            "topic_relevance": 0.9
        },
        "prediction": "positive"
    }

    for explanation_format in explanation_formats:
        print(f"\n解释格式: {explanation_format}")

        # 创建自解释性算子
        operator = self_explainability_v24.SelfExplainabilityOperator(
            explanation_type="hybrid",
            explanation_format=explanation_format,
            explanation_complexity="adaptive"
        )

        # 应用算子
        result = operator.apply(input_data)

        # 打印结果
        print(f"解释格式: {result['explanation_format']}")
        print(f"解释摘要: {result['summary']}")

    print("\n不同解释格式测试通过！")

def test_explanation_complexities():
    """测试不同的解释复杂度"""
    print("\n测试不同的解释复杂度...")

    # 解释复杂度
    explanation_complexities = ["simple", "medium", "complex", "adaptive"]

    # 测试输入数据
    input_data = {
        "features": {
            "text_length": 500,
            "sentiment_score": 0.75,
            "topic_relevance": 0.9
        },
        "prediction": "positive"
    }

    for explanation_complexity in explanation_complexities:
        print(f"\n解释复杂度: {explanation_complexity}")

        # 创建自解释性算子
        operator = self_explainability_v24.SelfExplainabilityOperator(
            explanation_type="hybrid",
            explanation_format="mixed",
            explanation_complexity=explanation_complexity
        )

        # 应用算子
        result = operator.apply(input_data)

        # 打印结果
        print(f"解释复杂度: {result['explanation_complexity']}")
        print(f"解释摘要: {result['summary']}")

    print("\n不同解释复杂度测试通过！")

def test_performance():
    """测试性能"""
    print("\n测试性能...")

    # 创建自解释性算子
    operator = self_explainability_v24.SelfExplainabilityOperator()

    # 测试输入数据
    input_data = {
        "features": {
            "text_length": 500,
            "sentiment_score": 0.75,
            "topic_relevance": 0.9,
            "user_engagement": 0.8,
            "readability": 0.65
        },
        "prediction": "positive",
        "confidence": 0.85
    }

    # 测试缓存
    print("测试缓存...")

    # 第一次调用
    start_time = time.time()
    operator.apply(input_data)
    first_call_time = time.time() - start_time
    print(f"第一次调用时间: {first_call_time:.6f}秒")

    # 第二次调用（应该使用缓存）
    start_time = time.time()
    operator.apply(input_data)
    second_call_time = time.time() - start_time
    print(f"第二次调用时间: {second_call_time:.6f}秒")

    # 获取缓存统计信息
    cache_stats = operator.get_cache_stats()
    print("缓存统计信息:")
    pprint(cache_stats)

    # 清理缓存
    operator.clear_cache()

    # 获取性能指标
    metrics = operator.get_performance_metrics()
    print("性能指标:")
    pprint(metrics)

    print("性能测试通过！")

def test_metadata():
    """测试元数据"""
    print("\n测试元数据...")

    # 创建自解释性算子
    operator = self_explainability_v24.SelfExplainabilityOperator()

    # 获取算子元数据
    metadata = operator.get_metadata()
    print("算子元数据:")
    pprint(metadata)

    # 获取算子参数
    parameters = operator.get_parameters()
    print("\n算子参数:")
    pprint(parameters)

    # 设置算子参数
    new_parameters = {
        "explanation_type": "FeatureImportance",  # 使用枚举值的字符串表示
        "explanation_format": "Text",  # 使用枚举值的字符串表示
        "explanation_complexity": "Simple",  # 使用枚举值的字符串表示
        "feature_importance_threshold": 0.1,
        "counterfactual_count": 2,
        "example_count": 2,
        "rule_count": 3
    }

    operator.set_parameters(new_parameters)

    # 获取更新后的算子参数
    updated_parameters = operator.get_parameters()
    print("\n更新后的算子参数:")
    pprint(updated_parameters)

    print("元数据测试通过！")

def main():
    """主函数"""
    print("开始测试PyO3 0.24+兼容版本的自解释性算子...\n")

    # 测试基本功能
    test_basic_functionality()

    # 测试不同的解释类型
    test_explanation_types()

    # 测试不同的解释格式
    test_explanation_formats()

    # 测试不同的解释复杂度
    test_explanation_complexities()

    # 测试性能
    test_performance()

    # 测试元数据
    test_metadata()

    print("\n所有测试通过！")

if __name__ == "__main__":
    main()
