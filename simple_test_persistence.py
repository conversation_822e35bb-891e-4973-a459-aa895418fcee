"""
简单的持续图生成器测试脚本

这个脚本测试重构后的持续图生成器的基本功能。
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Any, Dict


# 导入辅助函数
def preprocess_input(input_data: Any) -> Dict[int, np.ndarray]:
    """预处理输入数据，转换为持续图字典"""
    # 如果输入是持续同调计算结果
    if isinstance(input_data, dict) and 'persistence_diagrams' in input_data:
        return input_data['persistence_diagrams']
    
    # 如果输入是持续图
    if isinstance(input_data, np.ndarray) and len(input_data.shape) == 2 and input_data.shape[1] == 2:
        # 假设是0维持续图
        return {0: input_data}
    
    # 如果输入是持续条形码
    if isinstance(input_data, list) and all(isinstance(item, tuple) and len(item) == 2 for item in input_data):
        # 转换为numpy数组
        diagram = np.array(input_data)
        # 假设是0维持续图
        return {0: diagram}
    
    # 如果输入已经是持续图字典
    if isinstance(input_data, dict) and all(isinstance(key, int) for key in input_data.keys()):
        return input_data
    
    # 不支持的输入类型
    raise ValueError(f"Unsupported input type: {type(input_data)}")


def compute_statistics(diagram: np.ndarray) -> Dict[str, float]:
    """计算持续图的统计信息"""
    # 如果持续图为空，返回空统计信息
    if len(diagram) == 0:
        return {
            'count': 0,
            'avg_persistence': 0.0,
            'max_persistence': 0.0,
            'total_persistence': 0.0,
            'persistence_entropy': 0.0
        }
    
    # 计算持续度（死亡时间 - 出生时间）
    persistence = diagram[:, 1] - diagram[:, 0]
    
    # 处理无限持续度
    finite_persistence = persistence[np.isfinite(persistence)]
    
    # 计算统计信息
    count = len(diagram)
    avg_persistence = np.mean(finite_persistence) if len(finite_persistence) > 0 else 0.0
    max_persistence = np.max(finite_persistence) if len(finite_persistence) > 0 else 0.0
    total_persistence = np.sum(finite_persistence)
    
    # 计算持续熵
    if len(finite_persistence) > 0:
        # 归一化持续度
        normalized_persistence = finite_persistence / np.sum(finite_persistence)
        # 计算熵
        entropy = -np.sum(normalized_persistence * np.log(normalized_persistence + 1e-10))
    else:
        entropy = 0.0
    
    return {
        'count': count,
        'avg_persistence': avg_persistence,
        'max_persistence': max_persistence,
        'total_persistence': total_persistence,
        'persistence_entropy': entropy
    }


def plot_persistence_diagram(diagram: np.ndarray, dimension: int) -> plt.Figure:
    """绘制持续图"""
    # 创建图形
    fig, ax = plt.subplots(figsize=(8, 8))
    
    # 绘制对角线
    min_val = 0
    max_val = 0
    
    if len(diagram) > 0:
        # 找出有限值的最大值
        finite_values = diagram[np.isfinite(diagram)]
        if len(finite_values) > 0:
            max_val = np.max(finite_values) * 1.1
    
    # 如果没有有限值，使用默认范围
    if max_val <= min_val:
        min_val = 0
        max_val = 1
    
    # 绘制对角线
    ax.plot([min_val, max_val], [min_val, max_val], 'k--')
    
    # 绘制持续点
    if len(diagram) > 0:
        # 分离有限点和无限点
        finite_mask = np.isfinite(diagram[:, 1])
        finite_points = diagram[finite_mask]
        infinite_points = diagram[~finite_mask]
        
        # 绘制有限点
        if len(finite_points) > 0:
            ax.scatter(finite_points[:, 0], finite_points[:, 1], label=f'H{dimension}', alpha=0.7)
        
        # 绘制无限点（在图表顶部）
        if len(infinite_points) > 0:
            ax.scatter(infinite_points[:, 0], [max_val] * len(infinite_points), 
                      marker='^', label=f'H{dimension} (∞)', alpha=0.7)
    
    # 设置标签和标题
    ax.set_xlabel('Birth')
    ax.set_ylabel('Death')
    ax.set_title(f'Persistence Diagram (H{dimension})')
    
    # 设置坐标轴范围
    ax.set_xlim(min_val, max_val)
    ax.set_ylim(min_val, max_val)
    
    # 添加图例
    ax.legend()
    
    # 添加网格
    ax.grid(True, linestyle='--', alpha=0.7)
    
    return fig


# 持续图生成器类
class PersistenceDiagramGenerator:
    """
    持续图生成器类
    
    该类提供了生成和分析持续图的方法，支持多种距离度量和可视化功能。
    
    属性:
        visualization_enabled (bool): 是否启用可视化
        max_dimension (int): 最大维数
        name (str): 算子名称
    """
    
    def __init__(self, 
                 visualization_enabled: bool = True,
                 max_dimension: int = 2,
                 **kwargs):
        """
        初始化PersistenceDiagramGenerator算子
        
        参数:
            visualization_enabled (bool): 是否启用可视化
            max_dimension (int): 最大维数
            **kwargs: 其他参数
        """
        self.visualization_enabled = visualization_enabled
        self.max_dimension = max_dimension
        self.name = "PersistenceDiagramGenerator"
    
    def apply(self, input_data: Any, **kwargs) -> Any:
        """
        应用PersistenceDiagramGenerator算子到输入数据
        
        参数:
            input_data: 输入数据，可以是以下形式之一：
                - 持续同调计算结果：包含持续图、持续条形码和持续贝蒂数的字典
                - 持续图：形状为(n_pairs, 2)的numpy数组，表示(birth, death)对
                - 持续条形码：(birth, death)对的列表
            **kwargs: 其他参数，包括：
                - dimensions (list, optional): 要处理的维数列表，默认为[0, 1, ..., max_dimension]
                - plot (bool, optional): 是否生成图表，默认为True
                - compute_statistics (bool, optional): 是否计算统计信息，默认为True
        
        返回:
            持续图分析结果，包含图表、统计信息
        """
        # 提取参数
        dimensions = kwargs.get('dimensions', list(range(self.max_dimension + 1)))
        plot = kwargs.get('plot', True) and self.visualization_enabled
        compute_stats = kwargs.get('compute_statistics', True)
        
        # 预处理输入数据
        persistence_data = preprocess_input(input_data)
        
        # 初始化结果
        result = {
            'diagrams': {},
            'statistics': {},
            'plots': {}
        }
        
        # 处理每个维度的持续图
        for dim in dimensions:
            if dim in persistence_data:
                # 提取持续图
                diagram = persistence_data[dim]
                
                # 存储持续图
                result['diagrams'][dim] = diagram
                
                # 计算统计信息
                if compute_stats:
                    result['statistics'][dim] = compute_statistics(diagram)
                
                # 生成图表
                if plot:
                    result['plots'][dim] = plot_persistence_diagram(diagram, dim)
        
        return result
    
    def __str__(self) -> str:
        """返回算子的字符串表示"""
        return f"PersistenceDiagramGenerator(visualization_enabled={self.visualization_enabled}, max_dimension={self.max_dimension})"


def generate_circle_data(n_points=100, noise=0.1):
    """生成圆形点云数据"""
    theta = np.linspace(0, 2*np.pi, n_points)
    x = np.cos(theta) + np.random.normal(0, noise, n_points)
    y = np.sin(theta) + np.random.normal(0, noise, n_points)
    return np.column_stack((x, y))


def compute_persistence_diagrams(points):
    """计算持续图（简化版）"""
    # 这里我们使用简化的实现，实际应用中应该使用专门的库如Ripser或GUDHI
    
    # 0维持续图（连通分量）
    diagram_0 = np.array([
        [0.0, 0.1],
        [0.0, 0.2],
        [0.0, 0.3],
        [0.0, 0.4],
        [0.0, float('inf')]  # 一个无限持续的连通分量
    ])
    
    # 1维持续图（环）
    diagram_1 = np.array([
        [0.2, 0.8]  # 一个环
    ])
    
    return {
        'persistence_diagrams': {
            0: diagram_0,
            1: diagram_1
        }
    }


def test_persistence_diagram_generator():
    """测试PersistenceDiagramGenerator"""
    print("\n测试PersistenceDiagramGenerator...")
    
    # 生成圆形点云数据
    circle_data = generate_circle_data(n_points=50, noise=0.05)
    
    # 计算持续图
    persistence_result = compute_persistence_diagrams(circle_data)
    
    # 创建持续图生成器
    pd_generator = PersistenceDiagramGenerator(visualization_enabled=True)
    print(f"创建生成器: {pd_generator}")
    
    # 应用生成器
    result = pd_generator.apply(persistence_result, compute_statistics=True)
    
    # 打印结果
    print(f"持续图生成结果:")
    
    # 打印统计信息
    if 'statistics' in result:
        for dim, stats in result['statistics'].items():
            print(f"  {dim}维统计信息:")
            for key, value in stats.items():
                print(f"    {key}: {value}")
    
    # 显示图表
    if 'plots' in result:
        for dim, fig in result['plots'].items():
            if fig is not None:
                plt.figure(fig.number)
                plt.title(f"Dimension {dim} Persistence Diagram")
                plt.tight_layout()
                plt.show()
    
    print("PersistenceDiagramGenerator测试完成")
    
    return result


def main():
    """主函数"""
    print("开始测试持续图生成器...")
    
    # 测试PersistenceDiagramGenerator
    pd_result = test_persistence_diagram_generator()
    
    print("\n所有测试完成")


if __name__ == "__main__":
    main()
