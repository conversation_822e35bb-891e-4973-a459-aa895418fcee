/*
 * Parallel Operations
 *
 * This module provides parallel operations for NumPy arrays.
 */

use pyo3::prelude::*;
use pyo3::types::{<PERSON>yD<PERSON>, <PERSON>y<PERSON>ist, PyTuple};
use numpy::{PyArray, PyArray1, PyArray2, PyArray3, PyArray4, PyArrayDyn, IntoPyArray, PyReadonlyArray};
use ndarray::{Array, ArrayD, Axis, Dimension, IxDyn, ArrayView, ArrayViewD, ArrayViewMut, ArrayViewMutD};
use std::sync::{Arc, RwLock};
use rayon::prelude::*;

/// Parallel operations
pub struct ParallelOps {
    /// The name of the operations
    pub name: String,
    /// The properties of the operations
    pub properties: RwLock<std::collections::HashMap<String, PyObject>>,
}

impl ParallelOps {
    /// Create a new parallel operations
    pub fn new(name: &str) -> Self {
        Self {
            name: name.to_string(),
            properties: RwLock::new(std::collections::HashMap::new()),
        }
    }

    /// Get the number of available threads
    pub fn get_num_threads(&self) -> usize {
        rayon::current_num_threads()
    }

    /// Set the number of threads to use
    pub fn set_num_threads(&self, num_threads: usize) {
        rayon::ThreadPoolBuilder::new()
            .num_threads(num_threads)
            .build_global()
            .unwrap();
    }

    /// Apply a function to each element of an array in parallel
    pub fn par_map<'py>(&self, py: Python<'py>, array: &PyAny, func: &PyAny) -> PyResult<&'py PyAny> {
        // Convert array to numpy array
        let array = PyReadonlyArray::from_object(py, array)?;

        // Get array view
        let array_view = array.as_array();

        // Create output array
        let mut result = Array::zeros(array_view.dim());

        // Apply the function to each element in parallel
        array_view.indexed_iter().collect::<Vec<_>>().par_iter().for_each(|&(idx, &val)| {
            // Call the Python function
            let result_val = Python::with_gil(|py| {
                let args = PyTuple::new(py, [val]);
                func.call1(args).unwrap().extract::<f64>().unwrap()
            });

            // Store the result
            result[idx] = result_val;
        });

        // Convert result to Python
        Ok(result.into_pyarray(py))
    }

    /// Apply a function to each element of an array in parallel with index
    pub fn par_map_indexed<'py>(&self, py: Python<'py>, array: &PyAny, func: &PyAny) -> PyResult<&'py PyAny> {
        // Convert array to numpy array
        let array = PyReadonlyArray::from_object(py, array)?;

        // Get array view
        let array_view = array.as_array();

        // Create output array
        let mut result = Array::zeros(array_view.dim());

        // Apply the function to each element in parallel
        array_view.indexed_iter().collect::<Vec<_>>().par_iter().for_each(|&(idx, &val)| {
            // Convert index to Python tuple
            let idx_tuple = Python::with_gil(|py| {
                let idx_vec: Vec<usize> = idx.into_dimension().into_pattern().into_iter().collect();
                PyTuple::new(py, idx_vec)
            });

            // Call the Python function
            let result_val = Python::with_gil(|py| {
                let args = PyTuple::new(py, [idx_tuple, val]);
                func.call1(args).unwrap().extract::<f64>().unwrap()
            });

            // Store the result
            result[idx] = result_val;
        });

        // Convert result to Python
        Ok(result.into_pyarray(py))
    }

    /// Reduce an array along an axis in parallel
    pub fn par_reduce<'py>(&self, py: Python<'py>, array: &PyAny, axis: usize, func: &PyAny, initial: Option<f64>) -> PyResult<&'py PyAny> {
        // Convert array to numpy array
        let array = PyReadonlyArray::from_object(py, array)?;

        // Check if axis is valid
        if axis >= array.ndim() {
            return Err(pyo3::exceptions::PyValueError::new_err(
                format!("Axis {} is out of bounds for array of dimension {}", axis, array.ndim())
            ));
        }

        // Get array view
        let array_view = array.as_array();

        // Calculate the shape of the result
        let mut result_shape = array.shape().to_vec();
        result_shape.remove(axis);

        // Create output array
        let mut result = Array::zeros(IxDyn(&result_shape));

        // Get the size of the axis
        let axis_size = array.shape()[axis];

        // Reduce the array along the axis in parallel
        result.indexed_iter_mut().collect::<Vec<_>>().par_iter_mut().for_each(|(idx, val)| {
            // Convert the index to a full index for the original array
            let mut full_idx = idx.into_dimension().into_pattern().into_iter().collect::<Vec<_>>();
            full_idx.insert(axis, 0);

            // Initialize the accumulator
            let mut acc = if let Some(init) = initial {
                init
            } else {
                array_view[IxDyn(&full_idx)]
            };

            // Reduce along the axis
            for i in if initial.is_some() { 0 } else { 1 }..axis_size {
                full_idx[axis] = i;

                // Get the current value
                let curr = array_view[IxDyn(&full_idx)];

                // Call the Python function
                acc = Python::with_gil(|py| {
                    let args = PyTuple::new(py, [acc, curr]);
                    func.call1(args).unwrap().extract::<f64>().unwrap()
                });
            }

            // Store the result
            *val = acc;
        });

        // Convert result to Python
        Ok(result.into_pyarray(py))
    }

    /// Filter an array in parallel
    pub fn par_filter<'py>(&self, py: Python<'py>, array: &PyAny, func: &PyAny) -> PyResult<&'py PyAny> {
        // Convert array to numpy array
        let array = PyReadonlyArray::from_object(py, array)?;

        // Get array view
        let array_view = array.as_array();

        // Filter the array in parallel
        let filtered: Vec<f64> = array_view.iter().cloned().collect::<Vec<_>>().par_iter()
            .filter_map(|&val| {
                // Call the Python function
                let keep = Python::with_gil(|py| {
                    let args = PyTuple::new(py, [val]);
                    func.call1(args).unwrap().extract::<bool>().unwrap()
                });

                if keep {
                    Some(val)
                } else {
                    None
                }
            })
            .collect();

        // Create output array
        let result = Array::from_vec(filtered);

        // Convert result to Python
        Ok(result.into_pyarray(py))
    }

    /// Sort an array in parallel
    pub fn par_sort<'py>(&self, py: Python<'py>, array: &PyAny, axis: Option<usize>) -> PyResult<&'py PyAny> {
        // Convert array to numpy array
        let array = PyReadonlyArray::from_object(py, array)?;

        // Get array view
        let array_view = array.as_array();

        // Sort the array
        if let Some(axis) = axis {
            // Check if axis is valid
            if axis >= array.ndim() {
                return Err(pyo3::exceptions::PyValueError::new_err(
                    format!("Axis {} is out of bounds for array of dimension {}", axis, array.ndim())
                ));
            }

            // Create output array
            let mut result = array_view.to_owned();

            // Sort along the specified axis
            // This is a simplified implementation that doesn't use parallelism
            // A proper implementation would require more complex code
            result.map_axis_mut(Axis(axis), |mut view| {
                let mut data: Vec<f64> = view.iter().cloned().collect();
                data.sort_by(|a, b| a.partial_cmp(b).unwrap());
                for (i, &val) in data.iter().enumerate() {
                    view[i] = val;
                }
            });

            // Convert result to Python
            Ok(result.into_pyarray(py))
        } else {
            // Sort all elements
            let mut data: Vec<f64> = array_view.iter().cloned().collect();

            // Sort in parallel
            data.par_sort_by(|a, b| a.partial_cmp(b).unwrap());

            // Create output array
            let result = Array::from_vec(data).into_shape(array_view.dim()).unwrap();

            // Convert result to Python
            Ok(result.into_pyarray(py))
        }
    }
}

/// Python wrapper for ParallelOps
#[pyclass(name = "ParallelOps")]
pub struct PyParallelOps {
    /// The inner parallel operations
    pub ops: Arc<ParallelOps>,
}

#[pymethods]
impl PyParallelOps {
    /// Create a new parallel operations
    #[new]
    fn new(name: &str) -> Self {
        Self {
            ops: Arc::new(ParallelOps::new(name)),
        }
    }

    /// Get the number of available threads
    fn get_num_threads(&self) -> usize {
        self.ops.get_num_threads()
    }

    /// Set the number of threads to use
    fn set_num_threads(&self, num_threads: usize) {
        self.ops.set_num_threads(num_threads);
    }

    /// Apply a function to each element of an array in parallel
    fn par_map<'py>(&self, py: Python<'py>, array: &PyAny, func: &PyAny) -> PyResult<&'py PyAny> {
        self.ops.par_map(py, array, func)
    }

    /// Apply a function to each element of an array in parallel with index
    fn par_map_indexed<'py>(&self, py: Python<'py>, array: &PyAny, func: &PyAny) -> PyResult<&'py PyAny> {
        self.ops.par_map_indexed(py, array, func)
    }

    /// Reduce an array along an axis in parallel
    #[pyo3(signature = (array, axis, func, initial=None))]
    fn par_reduce<'py>(&self, py: Python<'py>, array: &PyAny, axis: usize, func: &PyAny, initial: Option<f64>) -> PyResult<&'py PyAny> {
        self.ops.par_reduce(py, array, axis, func, initial)
    }

    /// Filter an array in parallel
    fn par_filter<'py>(&self, py: Python<'py>, array: &PyAny, func: &PyAny) -> PyResult<&'py PyAny> {
        self.ops.par_filter(py, array, func)
    }

    /// Sort an array in parallel
    #[pyo3(signature = (array, axis=None))]
    fn par_sort<'py>(&self, py: Python<'py>, array: &PyAny, axis: Option<usize>) -> PyResult<&'py PyAny> {
        self.ops.par_sort(py, array, axis)
    }

    /// Get the name of the operations
    #[getter]
    fn name(&self) -> String {
        self.ops.name.clone()
    }
}
