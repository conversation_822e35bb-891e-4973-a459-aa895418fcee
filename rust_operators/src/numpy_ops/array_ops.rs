/*
 * Array Operations
 *
 * This module provides optimized operations for NumPy arrays.
 */

use pyo3::prelude::*;
use pyo3::types::{<PERSON>yD<PERSON>, PyList, PyTuple};
use numpy::{PyArray, PyArray1, PyArray2, PyArray3, PyArray4, PyArrayDyn, IntoPyArray, PyReadonlyArray};
use ndarray::{Array, ArrayD, Axis, Dimension, IxDyn, ArrayView, ArrayViewD, ArrayViewMut, ArrayViewMutD};
use std::sync::{Arc, RwLock};
use rayon::prelude::*;

/// Array operations
pub struct ArrayOps {
    /// The name of the operations
    pub name: String,
    /// The properties of the operations
    pub properties: RwLock<std::collections::HashMap<String, PyObject>>,
}

impl ArrayOps {
    /// Create a new array operations
    pub fn new(name: &str) -> Self {
        Self {
            name: name.to_string(),
            properties: RwLock::new(std::collections::HashMap::new()),
        }
    }
    
    /// Concatenate arrays along an axis
    pub fn concatenate<'py>(&self, py: Python<'py>, arrays: &[&PyAny], axis: usize) -> PyResult<&'py PyAny> {
        // Check if arrays is empty
        if arrays.is_empty() {
            return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>("No arrays to concatenate"));
        }
        
        // Convert all arrays to numpy arrays
        let mut numpy_arrays = Vec::with_capacity(arrays.len());
        for array in arrays {
            numpy_arrays.push(PyReadonlyArray::from_object(py, array)?);
        }
        
        // Check if all arrays have the same number of dimensions
        let ndim = numpy_arrays[0].ndim();
        for (i, array) in numpy_arrays.iter().enumerate().skip(1) {
            if array.ndim() != ndim {
                return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                    format!("All arrays must have the same number of dimensions, but arrays[0].ndim() = {} and arrays[{}].ndim() = {}", 
                            ndim, i, array.ndim())
                ));
            }
        }
        
        // Check if axis is valid
        if axis >= ndim {
            return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                format!("Axis {} is out of bounds for array of dimension {}", axis, ndim)
            ));
        }
        
        // Check if shapes match along all dimensions except the concatenation axis
        let shape = numpy_arrays[0].shape();
        for (i, array) in numpy_arrays.iter().enumerate().skip(1) {
            let array_shape = array.shape();
            for (j, (&s1, &s2)) in shape.iter().zip(array_shape.iter()).enumerate() {
                if j != axis && s1 != s2 {
                    return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                        format!("All arrays must have the same shape along all dimensions except the concatenation axis, but arrays[0].shape[{}] = {} and arrays[{}].shape[{}] = {}", 
                                j, s1, i, j, s2)
                    ));
                }
            }
        }
        
        // Calculate the shape of the result
        let mut result_shape = shape.to_vec();
        result_shape[axis] = numpy_arrays.iter().map(|a| a.shape()[axis]).sum();
        
        // Create the result array
        let mut result = ArrayD::zeros(IxDyn(&result_shape));
        
        // Fill the result array
        let mut start_idx = 0;
        for array in &numpy_arrays {
            let array_view = array.as_array();
            let array_shape = array.shape();
            
            // Calculate the slice for this array
            let mut indices = Vec::with_capacity(ndim);
            for i in 0..ndim {
                if i == axis {
                    indices.push(ndarray::s![start_idx..start_idx + array_shape[i]]);
                } else {
                    indices.push(ndarray::s![..]);
                }
            }
            
            // Create the slice
            let mut slice = result.slice_mut(ndarray::SliceInfo::new(&indices).unwrap());
            
            // Copy the data
            slice.assign(&array_view);
            
            // Update the start index
            start_idx += array_shape[axis];
        }
        
        // Convert result to Python
        Ok(result.into_pyarray(py))
    }
    
    /// Split an array into multiple sub-arrays
    pub fn split<'py>(&self, py: Python<'py>, array: &PyAny, indices_or_sections: &PyAny, axis: usize) -> PyResult<&'py PyAny> {
        // Convert array to numpy array
        let array = PyReadonlyArray::from_object(py, array)?;
        
        // Check if axis is valid
        if axis >= array.ndim() {
            return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                format!("Axis {} is out of bounds for array of dimension {}", axis, array.ndim())
            ));
        }
        
        // Get the array view
        let array_view = array.as_array();
        
        // Determine the indices to split at
        let indices = if indices_or_sections.is_instance_of::<pyo3::types::PyInt>()? {
            // Split into equal sections
            let sections = indices_or_sections.extract::<usize>()?;
            if sections == 0 {
                return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>("Number of sections must be at least 1"));
            }
            
            let axis_size = array.shape()[axis];
            if axis_size % sections != 0 {
                return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                    format!("Array of length {} cannot be split into {} equal sections along axis {}", 
                            axis_size, sections, axis)
                ));
            }
            
            let section_size = axis_size / sections;
            (1..sections).map(|i| i * section_size).collect::<Vec<_>>()
        } else {
            // Use the provided indices
            indices_or_sections.extract::<Vec<usize>>()?
        };
        
        // Check if indices are valid
        let axis_size = array.shape()[axis];
        for &idx in &indices {
            if idx >= axis_size {
                return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                    format!("Index {} is out of bounds for axis {} with size {}", idx, axis, axis_size)
                ));
            }
        }
        
        // Create the result list
        let result = PyList::empty(py);
        
        // Split the array
        let mut start_idx = 0;
        for &end_idx in &indices {
            // Calculate the slice for this sub-array
            let mut indices = Vec::with_capacity(array.ndim());
            for i in 0..array.ndim() {
                if i == axis {
                    indices.push(ndarray::s![start_idx..end_idx]);
                } else {
                    indices.push(ndarray::s![..]);
                }
            }
            
            // Create the slice
            let slice = array_view.slice(ndarray::SliceInfo::new(&indices).unwrap());
            
            // Add the sub-array to the result list
            result.append(slice.to_owned().into_pyarray(py))?;
            
            // Update the start index
            start_idx = end_idx;
        }
        
        // Add the last sub-array
        let mut indices = Vec::with_capacity(array.ndim());
        for i in 0..array.ndim() {
            if i == axis {
                indices.push(ndarray::s![start_idx..]);
            } else {
                indices.push(ndarray::s![..]);
            }
        }
        
        // Create the slice
        let slice = array_view.slice(ndarray::SliceInfo::new(&indices).unwrap());
        
        // Add the sub-array to the result list
        result.append(slice.to_owned().into_pyarray(py))?;
        
        Ok(result)
    }
    
    /// Stack arrays along a new axis
    pub fn stack<'py>(&self, py: Python<'py>, arrays: &[&PyAny], axis: usize) -> PyResult<&'py PyAny> {
        // Check if arrays is empty
        if arrays.is_empty() {
            return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>("No arrays to stack"));
        }
        
        // Convert all arrays to numpy arrays
        let mut numpy_arrays = Vec::with_capacity(arrays.len());
        for array in arrays {
            numpy_arrays.push(PyReadonlyArray::from_object(py, array)?);
        }
        
        // Check if all arrays have the same shape
        let shape = numpy_arrays[0].shape();
        for (i, array) in numpy_arrays.iter().enumerate().skip(1) {
            if array.shape() != shape {
                return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                    format!("All arrays must have the same shape, but arrays[0].shape = {:?} and arrays[{}].shape = {:?}", 
                            shape, i, array.shape())
                ));
            }
        }
        
        // Check if axis is valid
        if axis > numpy_arrays[0].ndim() {
            return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                format!("Axis {} is out of bounds for array of dimension {}", axis, numpy_arrays[0].ndim())
            ));
        }
        
        // Calculate the shape of the result
        let mut result_shape = shape.to_vec();
        result_shape.insert(axis, arrays.len());
        
        // Create the result array
        let mut result = ArrayD::zeros(IxDyn(&result_shape));
        
        // Fill the result array
        for (i, array) in numpy_arrays.iter().enumerate() {
            let array_view = array.as_array();
            
            // Calculate the slice for this array
            let mut indices = Vec::with_capacity(result_shape.len());
            for j in 0..result_shape.len() {
                if j == axis {
                    indices.push(ndarray::s![i]);
                } else {
                    indices.push(ndarray::s![..]);
                }
            }
            
            // Create the slice
            let mut slice = result.slice_mut(ndarray::SliceInfo::new(&indices).unwrap());
            
            // Copy the data
            slice.assign(&array_view);
        }
        
        // Convert result to Python
        Ok(result.into_pyarray(py))
    }
}

/// Python wrapper for ArrayOps
#[pyclass(name = "ArrayOps")]
pub struct PyArrayOps {
    /// The inner array operations
    pub ops: Arc<ArrayOps>,
}

#[pymethods]
impl PyArrayOps {
    /// Create a new array operations
    #[new]
    fn new(name: &str) -> Self {
        Self {
            ops: Arc::new(ArrayOps::new(name)),
        }
    }
    
    /// Concatenate arrays along an axis
    fn concatenate<'py>(&self, py: Python<'py>, arrays: Vec<&PyAny>, axis: usize) -> PyResult<&'py PyAny> {
        self.ops.concatenate(py, &arrays, axis)
    }
    
    /// Split an array into multiple sub-arrays
    fn split<'py>(&self, py: Python<'py>, array: &PyAny, indices_or_sections: &PyAny, axis: usize) -> PyResult<&'py PyAny> {
        self.ops.split(py, array, indices_or_sections, axis)
    }
    
    /// Stack arrays along a new axis
    fn stack<'py>(&self, py: Python<'py>, arrays: Vec<&PyAny>, axis: usize) -> PyResult<&'py PyAny> {
        self.ops.stack(py, &arrays, axis)
    }
    
    /// Get the name of the operations
    #[getter]
    fn name(&self) -> String {
        self.ops.name.clone()
    }
}
