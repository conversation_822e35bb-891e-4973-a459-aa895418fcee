/*
 * Math Operations
 *
 * This module provides optimized math operations for NumPy arrays.
 */

use pyo3::prelude::*;
use pyo3::types::{<PERSON>y<PERSON><PERSON>, <PERSON>yList, PyTuple};
use numpy::{PyArray, PyArray1, PyArray2, PyArray3, PyArray4, PyArrayDyn, IntoPyArray, PyReadonlyArray};
use ndarray::{Array, ArrayD, Axis, Dimension, IxDyn, ArrayView, ArrayViewD, ArrayViewMut, ArrayViewMutD};
use std::sync::{Arc, RwLock};
use rayon::prelude::*;

/// Math operations
pub struct MathOps {
    /// The name of the operations
    pub name: String,
    /// The properties of the operations
    pub properties: RwLock<std::collections::HashMap<String, PyObject>>,
}

impl MathOps {
    /// Create a new math operations
    pub fn new(name: &str) -> Self {
        Self {
            name: name.to_string(),
            properties: RwLock::new(std::collections::HashMap::new()),
        }
    }

    /// Calculate the sum of array elements
    pub fn sum<'py>(&self, py: Python<'py>, array: &PyAny, axis: Option<usize>) -> PyResult<&'py PyAny> {
        // Convert array to numpy array
        let array = PyReadonlyArray::from_object(py, array)?;

        // Get array view
        let array_view = array.as_array();

        // Calculate sum
        if let Some(axis) = axis {
            // Check if axis is valid
            if axis >= array.ndim() {
                return Err(pyo3::exceptions::PyValueError::new_err(
                    format!("Axis {} is out of bounds for array of dimension {}", axis, array.ndim())
                ));
            }

            // Sum along the specified axis
            let result = array_view.sum_axis(Axis(axis));

            // Convert result to Python
            Ok(result.into_pyarray(py))
        } else {
            // Sum all elements
            let sum = array_view.sum();

            // Convert result to Python
            Ok(sum.to_object(py))
        }
    }

    /// Calculate the mean of array elements
    pub fn mean<'py>(&self, py: Python<'py>, array: &PyAny, axis: Option<usize>) -> PyResult<&'py PyAny> {
        // Convert array to numpy array
        let array = PyReadonlyArray::from_object(py, array)?;

        // Get array view
        let array_view = array.as_array();

        // Calculate mean
        if let Some(axis) = axis {
            // Check if axis is valid
            if axis >= array.ndim() {
                return Err(pyo3::exceptions::PyValueError::new_err(
                    format!("Axis {} is out of bounds for array of dimension {}", axis, array.ndim())
                ));
            }

            // Calculate sum along the specified axis
            let sum = array_view.sum_axis(Axis(axis));

            // Calculate the number of elements along the axis
            let count = array.shape()[axis];

            // Calculate mean
            let mean = sum.mapv(|x| x / count as f64);

            // Convert result to Python
            Ok(mean.into_pyarray(py))
        } else {
            // Calculate sum of all elements
            let sum = array_view.sum();

            // Calculate the number of elements
            let count = array_view.len();

            // Calculate mean
            let mean = sum / count as f64;

            // Convert result to Python
            Ok(mean.to_object(py))
        }
    }

    /// Calculate the standard deviation of array elements
    pub fn std<'py>(&self, py: Python<'py>, array: &PyAny, axis: Option<usize>, ddof: usize) -> PyResult<&'py PyAny> {
        // Convert array to numpy array
        let array = PyReadonlyArray::from_object(py, array)?;

        // Get array view
        let array_view = array.as_array();

        // Calculate standard deviation
        if let Some(axis) = axis {
            // Check if axis is valid
            if axis >= array.ndim() {
                return Err(pyo3::exceptions::PyValueError::new_err(
                    format!("Axis {} is out of bounds for array of dimension {}", axis, array.ndim())
                ));
            }

            // Calculate mean along the specified axis
            let mean = array_view.mean_axis(Axis(axis)).unwrap();

            // Calculate squared differences
            let mut squared_diff = Array::zeros(mean.dim());

            // Iterate over the array
            for (idx, &val) in array_view.indexed_iter() {
                // Calculate the index for the mean array
                let mut mean_idx = idx.into_dimension();
                mean_idx.remove_axis(Axis(axis));

                // Calculate squared difference
                let diff = val - mean[mean_idx];
                squared_diff[mean_idx] += diff * diff;
            }

            // Calculate the number of elements along the axis
            let count = array.shape()[axis];

            // Calculate variance
            let variance = squared_diff.mapv(|x| x / (count - ddof) as f64);

            // Calculate standard deviation
            let std_dev = variance.mapv(|x| x.sqrt());

            // Convert result to Python
            Ok(std_dev.into_pyarray(py))
        } else {
            // Calculate mean of all elements
            let mean = array_view.mean().unwrap();

            // Calculate sum of squared differences
            let squared_diff_sum = array_view.fold(0.0, |acc, &x| acc + (x - mean).powi(2));

            // Calculate the number of elements
            let count = array_view.len();

            // Calculate variance
            let variance = squared_diff_sum / (count - ddof) as f64;

            // Calculate standard deviation
            let std_dev = variance.sqrt();

            // Convert result to Python
            Ok(std_dev.to_object(py))
        }
    }

    /// Calculate the minimum value of array elements
    pub fn min<'py>(&self, py: Python<'py>, array: &PyAny, axis: Option<usize>) -> PyResult<&'py PyAny> {
        // Convert array to numpy array
        let array = PyReadonlyArray::from_object(py, array)?;

        // Get array view
        let array_view = array.as_array();

        // Calculate minimum
        if let Some(axis) = axis {
            // Check if axis is valid
            if axis >= array.ndim() {
                return Err(pyo3::exceptions::PyValueError::new_err(
                    format!("Axis {} is out of bounds for array of dimension {}", axis, array.ndim())
                ));
            }

            // Find minimum along the specified axis
            let result = array_view.fold_axis(Axis(axis), f64::INFINITY, |&acc, &x| acc.min(x));

            // Convert result to Python
            Ok(result.into_pyarray(py))
        } else {
            // Find minimum of all elements
            let min = array_view.fold(f64::INFINITY, |acc, &x| acc.min(x));

            // Convert result to Python
            Ok(min.to_object(py))
        }
    }

    /// Calculate the maximum value of array elements
    pub fn max<'py>(&self, py: Python<'py>, array: &PyAny, axis: Option<usize>) -> PyResult<&'py PyAny> {
        // Convert array to numpy array
        let array = PyReadonlyArray::from_object(py, array)?;

        // Get array view
        let array_view = array.as_array();

        // Calculate maximum
        if let Some(axis) = axis {
            // Check if axis is valid
            if axis >= array.ndim() {
                return Err(pyo3::exceptions::PyValueError::new_err(
                    format!("Axis {} is out of bounds for array of dimension {}", axis, array.ndim())
                ));
            }

            // Find maximum along the specified axis
            let result = array_view.fold_axis(Axis(axis), f64::NEG_INFINITY, |&acc, &x| acc.max(x));

            // Convert result to Python
            Ok(result.into_pyarray(py))
        } else {
            // Find maximum of all elements
            let max = array_view.fold(f64::NEG_INFINITY, |acc, &x| acc.max(x));

            // Convert result to Python
            Ok(max.to_object(py))
        }
    }
}

/// Python wrapper for MathOps
#[pyclass(name = "MathOps")]
pub struct PyMathOps {
    /// The inner math operations
    pub ops: Arc<MathOps>,
}

#[pymethods]
impl PyMathOps {
    /// Create a new math operations
    #[new]
    fn new(name: &str) -> Self {
        Self {
            ops: Arc::new(MathOps::new(name)),
        }
    }

    /// Calculate the sum of array elements
    #[pyo3(signature = (array, axis=None))]
    fn sum<'py>(&self, py: Python<'py>, array: &PyAny, axis: Option<usize>) -> PyResult<&'py PyAny> {
        self.ops.sum(py, array, axis)
    }

    /// Calculate the mean of array elements
    #[pyo3(signature = (array, axis=None))]
    fn mean<'py>(&self, py: Python<'py>, array: &PyAny, axis: Option<usize>) -> PyResult<&'py PyAny> {
        self.ops.mean(py, array, axis)
    }

    /// Calculate the standard deviation of array elements
    #[pyo3(signature = (array, axis=None, ddof=0))]
    fn std<'py>(&self, py: Python<'py>, array: &PyAny, axis: Option<usize>, ddof: usize) -> PyResult<&'py PyAny> {
        self.ops.std(py, array, axis, ddof)
    }

    /// Calculate the minimum value of array elements
    #[pyo3(signature = (array, axis=None))]
    fn min<'py>(&self, py: Python<'py>, array: &PyAny, axis: Option<usize>) -> PyResult<&'py PyAny> {
        self.ops.min(py, array, axis)
    }

    /// Calculate the maximum value of array elements
    #[pyo3(signature = (array, axis=None))]
    fn max<'py>(&self, py: Python<'py>, array: &PyAny, axis: Option<usize>) -> PyResult<&'py PyAny> {
        self.ops.max(py, array, axis)
    }

    /// Get the name of the operations
    #[getter]
    fn name(&self) -> String {
        self.ops.name.clone()
    }
}
