/*
 * NumPy Operations Module
 *
 * This module provides optimized operations for NumPy arrays.
 * It leverages NumPy 2.x features, particularly GIL-free operations.
 */

use pyo3::prelude::*;
use pyo3::types::{<PERSON>yD<PERSON>, PyList, PyTuple};
use numpy::{PyArray, PyArray1, PyArray2, PyArray3, PyArray4, PyArrayDyn, IntoPyArray, PyReadonlyArray};
use ndarray::{Array, ArrayD, Axis, Dimension, IxDyn, ArrayView, ArrayViewD, ArrayViewMut, ArrayViewMutD};
use std::sync::{Arc, RwLock};
use rayon::prelude::*;

// Module declarations
mod array_ops;
mod math_ops;
mod simd_ops;
mod parallel_ops;
mod tensor_ops;

// Re-exports
pub use array_ops::{<PERSON><PERSON>yOps, PyArrayOps};
pub use math_ops::{<PERSON><PERSON><PERSON>, PyMathOps};
pub use simd_ops::{SimdOps, PySimdOps};
pub use parallel_ops::{ParallelOps, PyParallelOps};
pub use tensor_ops::{TensorOps, PyTensorOps};

/// Register the numpy_ops module with Python
pub fn register_module(_py: Python, m: &PyModule) -> PyResult<()> {
    // Register the ArrayOps class
    m.add_class::<PyArrayOps>()?;

    // Register the MathOps class
    m.add_class::<PyMathOps>()?;

    // Register the SimdOps class
    m.add_class::<PySimdOps>()?;

    // Register the ParallelOps class
    m.add_class::<PyParallelOps>()?;

    // Register the TensorOps class
    m.add_class::<PyTensorOps>()?;

    // Add module-level functions
    m.add_function(wrap_pyfunction!(array_add, m)?)?;
    m.add_function(wrap_pyfunction!(array_multiply, m)?)?;
    m.add_function(wrap_pyfunction!(array_matmul, m)?)?;
    m.add_function(wrap_pyfunction!(array_transpose, m)?)?;
    m.add_function(wrap_pyfunction!(array_reshape, m)?)?;

    Ok(())
}

/// Add two NumPy arrays
#[pyfunction]
fn array_add<'py>(py: Python<'py>, a: &PyAny, b: &PyAny) -> PyResult<&'py PyAny> {
    // Convert inputs to numpy arrays
    let a_array = PyReadonlyArray::from_object(py, a)?;
    let b_array = PyReadonlyArray::from_object(py, b)?;

    // Check shapes
    if a_array.shape() != b_array.shape() {
        return Err(pyo3::exceptions::PyValueError::new_err(
            format!("Arrays must have the same shape: {:?} vs {:?}", a_array.shape(), b_array.shape())
        ));
    }

    // Get array views
    let a_view = a_array.as_array();
    let b_view = b_array.as_array();

    // Create output array
    let mut result = Array::zeros(a_view.dim());

    // Perform addition
    for (i, (a_val, b_val)) in a_view.iter().zip(b_view.iter()).enumerate() {
        let idx = ndarray::IxDyn::from_slice(&ndarray::indices(result.shape()).into_iter().map(|v| v[i]).collect::<Vec<_>>());
        result[idx] = a_val + b_val;
    }

    // Convert result to Python
    Ok(result.into_pyarray(py))
}

/// Multiply two NumPy arrays element-wise
#[pyfunction]
fn array_multiply<'py>(py: Python<'py>, a: &PyAny, b: &PyAny) -> PyResult<&'py PyAny> {
    // Convert inputs to numpy arrays
    let a_array = PyReadonlyArray::from_object(py, a)?;
    let b_array = PyReadonlyArray::from_object(py, b)?;

    // Check shapes
    if a_array.shape() != b_array.shape() {
        return Err(pyo3::exceptions::PyValueError::new_err(
            format!("Arrays must have the same shape: {:?} vs {:?}", a_array.shape(), b_array.shape())
        ));
    }

    // Get array views
    let a_view = a_array.as_array();
    let b_view = b_array.as_array();

    // Create output array
    let mut result = Array::zeros(a_view.dim());

    // Perform multiplication
    for (i, (a_val, b_val)) in a_view.iter().zip(b_view.iter()).enumerate() {
        let idx = ndarray::IxDyn::from_slice(&ndarray::indices(result.shape()).into_iter().map(|v| v[i]).collect::<Vec<_>>());
        result[idx] = a_val * b_val;
    }

    // Convert result to Python
    Ok(result.into_pyarray(py))
}

/// Matrix multiplication of two NumPy arrays
#[pyfunction]
fn array_matmul<'py>(py: Python<'py>, a: &PyAny, b: &PyAny) -> PyResult<&'py PyAny> {
    // Convert inputs to numpy arrays
    let a_array = PyReadonlyArray::from_object(py, a)?;
    let b_array = PyReadonlyArray::from_object(py, b)?;

    // Check dimensions
    if a_array.ndim() != 2 || b_array.ndim() != 2 {
        return Err(pyo3::exceptions::PyValueError::new_err(
            format!("Both arrays must be 2-dimensional for matrix multiplication")
        ));
    }

    // Check shapes for matrix multiplication
    let a_shape = a_array.shape();
    let b_shape = b_array.shape();

    if a_shape[1] != b_shape[0] {
        return Err(pyo3::exceptions::PyValueError::new_err(
            format!("Shape mismatch for matrix multiplication: {:?} and {:?}", a_shape, b_shape)
        ));
    }

    // Get array views
    let a_view = a_array.as_array();
    let b_view = b_array.as_array();

    // Create output array
    let mut result = Array::zeros((a_shape[0], b_shape[1]));

    // Perform matrix multiplication
    for i in 0..a_shape[0] {
        for j in 0..b_shape[1] {
            let mut sum = 0.0;
            for k in 0..a_shape[1] {
                sum += a_view[[i, k]] * b_view[[k, j]];
            }
            result[[i, j]] = sum;
        }
    }

    // Convert result to Python
    Ok(result.into_pyarray(py))
}

/// Transpose a NumPy array
#[pyfunction]
fn array_transpose<'py>(py: Python<'py>, a: &PyAny) -> PyResult<&'py PyAny> {
    // Convert input to numpy array
    let a_array = PyReadonlyArray::from_object(py, a)?;

    // Get array view
    let a_view = a_array.as_array();

    // Create transposed array
    let result = a_view.t().to_owned();

    // Convert result to Python
    Ok(result.into_pyarray(py))
}

/// Reshape a NumPy array
#[pyfunction]
fn array_reshape<'py>(py: Python<'py>, a: &PyAny, shape: Vec<usize>) -> PyResult<&'py PyAny> {
    // Convert input to numpy array
    let a_array = PyReadonlyArray::from_object(py, a)?;

    // Get array view
    let a_view = a_array.as_array();

    // Check if the new shape is compatible
    let total_elements: usize = a_view.len();
    let new_total: usize = shape.iter().product();

    if total_elements != new_total {
        return Err(pyo3::exceptions::PyValueError::new_err(
            format!("Cannot reshape array of size {} into shape {:?}", total_elements, shape)
        ));
    }

    // Create reshaped array
    let result = a_view.into_shape(shape).map_err(|e| {
        pyo3::exceptions::PyValueError::new_err(format!("Reshape error: {}", e))
    })?;

    // Convert result to Python
    Ok(result.into_pyarray(py))
}
