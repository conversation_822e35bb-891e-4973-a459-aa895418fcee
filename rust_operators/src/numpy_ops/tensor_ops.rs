/*
 * Tensor Operations
 *
 * This module provides optimized tensor operations for NumPy arrays.
 */

use pyo3::prelude::*;
use pyo3::types::{<PERSON>yD<PERSON>, PyList, PyTuple};
use numpy::{PyArray, PyArray1, PyArray2, PyArray3, PyArray4, PyArrayDyn, IntoPyArray, PyReadonlyArray};
use ndarray::{Array, ArrayD, Axis, Dimension, IxDyn, ArrayView, ArrayViewD, ArrayViewMut, ArrayViewMutD};
use std::sync::{Arc, RwLock};
use rayon::prelude::*;

/// Tensor operations
pub struct TensorOps {
    /// The name of the operations
    pub name: String,
    /// The properties of the operations
    pub properties: RwLock<std::collections::HashMap<String, PyObject>>,
}

impl TensorOps {
    /// Create a new tensor operations
    pub fn new(name: &str) -> Self {
        Self {
            name: name.to_string(),
            properties: RwLock::new(std::collections::HashMap::new()),
        }
    }
    
    /// Perform tensor contraction
    pub fn tensordot<'py>(&self, py: Python<'py>, a: &PyAny, b: &PyAny, axes: &PyAny) -> PyResult<&'py PyAny> {
        // Convert arrays to numpy arrays
        let a_array = PyReadonlyArray::from_object(py, a)?;
        let b_array = PyReadonlyArray::from_object(py, b)?;
        
        // Get array views
        let a_view = a_array.as_array();
        let b_view = b_array.as_array();
        
        // Parse axes
        let axes = if axes.is_instance_of::<pyo3::types::PyInt>()? {
            // Single integer: contract last N axes of a with first N axes of b
            let n = axes.extract::<usize>()?;
            
            // Check if the number of axes is valid
            if n > a_array.ndim() || n > b_array.ndim() {
                return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                    format!("Cannot contract {} axes: a has {} dimensions, b has {} dimensions", 
                            n, a_array.ndim(), b_array.ndim())
                ));
            }
            
            // Create axes pairs
            let a_axes = (a_array.ndim() - n..a_array.ndim()).collect::<Vec<_>>();
            let b_axes = (0..n).collect::<Vec<_>>();
            
            (a_axes, b_axes)
        } else {
            // Tuple of two lists: contract specified axes
            let axes_tuple = axes.extract::<(Vec<usize>, Vec<usize>)>()?;
            
            // Check if the number of axes is valid
            if axes_tuple.0.len() != axes_tuple.1.len() {
                return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                    format!("Axes have different lengths: {} and {}", axes_tuple.0.len(), axes_tuple.1.len())
                ));
            }
            
            // Check if the axes are valid
            for &axis in &axes_tuple.0 {
                if axis >= a_array.ndim() {
                    return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                        format!("Axis {} is out of bounds for array of dimension {}", axis, a_array.ndim())
                    ));
                }
            }
            
            for &axis in &axes_tuple.1 {
                if axis >= b_array.ndim() {
                    return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                        format!("Axis {} is out of bounds for array of dimension {}", axis, b_array.ndim())
                    ));
                }
            }
            
            // Check if the contracted dimensions match
            for (&a_axis, &b_axis) in axes_tuple.0.iter().zip(axes_tuple.1.iter()) {
                if a_array.shape()[a_axis] != b_array.shape()[b_axis] {
                    return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                        format!("Dimension mismatch: a.shape[{}] = {}, b.shape[{}] = {}", 
                                a_axis, a_array.shape()[a_axis], b_axis, b_array.shape()[b_axis])
                    ));
                }
            }
            
            axes_tuple
        };
        
        // Calculate the shape of the result
        let mut a_free_axes = (0..a_array.ndim()).collect::<Vec<_>>();
        let mut b_free_axes = (0..b_array.ndim()).collect::<Vec<_>>();
        
        // Remove contracted axes
        for &axis in &axes.0 {
            a_free_axes.retain(|&x| x != axis);
        }
        
        for &axis in &axes.1 {
            b_free_axes.retain(|&x| x != axis);
        }
        
        // Calculate the shape of the result
        let mut result_shape = Vec::new();
        
        // Add free dimensions from a
        for &axis in &a_free_axes {
            result_shape.push(a_array.shape()[axis]);
        }
        
        // Add free dimensions from b
        for &axis in &b_free_axes {
            result_shape.push(b_array.shape()[axis]);
        }
        
        // Create the result array
        let mut result = Array::zeros(IxDyn(&result_shape));
        
        // Perform tensor contraction
        // This is a simplified implementation that doesn't use parallelism
        // A proper implementation would require more complex code
        
        // Calculate the size of the contracted dimensions
        let contracted_size: usize = axes.0.iter().map(|&axis| a_array.shape()[axis]).product();
        
        // Iterate over the free dimensions of a
        let a_free_shape: Vec<usize> = a_free_axes.iter().map(|&axis| a_array.shape()[axis]).collect();
        let a_free_strides: Vec<usize> = a_free_axes.iter().map(|&axis| a_array.strides()[axis]).collect();
        
        // Iterate over the free dimensions of b
        let b_free_shape: Vec<usize> = b_free_axes.iter().map(|&axis| b_array.shape()[axis]).collect();
        let b_free_strides: Vec<usize> = b_free_axes.iter().map(|&axis| b_array.strides()[axis]).collect();
        
        // Iterate over the contracted dimensions
        let a_contracted_strides: Vec<usize> = axes.0.iter().map(|&axis| a_array.strides()[axis]).collect();
        let b_contracted_strides: Vec<usize> = axes.1.iter().map(|&axis| b_array.strides()[axis]).collect();
        
        // Perform the contraction
        // This is a naive implementation that doesn't use optimized BLAS routines
        // A proper implementation would use BLAS for matrix multiplication
        
        // Iterate over the free dimensions of a
        for a_idx in ndarray::indices(a_free_shape.clone()) {
            // Iterate over the free dimensions of b
            for b_idx in ndarray::indices(b_free_shape.clone()) {
                // Calculate the result index
                let mut result_idx = Vec::new();
                result_idx.extend(a_idx.slice().iter());
                result_idx.extend(b_idx.slice().iter());
                
                // Initialize the sum
                let mut sum = 0.0;
                
                // Iterate over the contracted dimensions
                for contracted_idx in ndarray::indices(vec![contracted_size]) {
                    // Calculate the a index
                    let mut a_full_idx = vec![0; a_array.ndim()];
                    for (i, &axis) in a_free_axes.iter().enumerate() {
                        a_full_idx[axis] = a_idx[i];
                    }
                    for (i, &axis) in axes.0.iter().enumerate() {
                        a_full_idx[axis] = contracted_idx[0];
                    }
                    
                    // Calculate the b index
                    let mut b_full_idx = vec![0; b_array.ndim()];
                    for (i, &axis) in b_free_axes.iter().enumerate() {
                        b_full_idx[axis] = b_idx[i];
                    }
                    for (i, &axis) in axes.1.iter().enumerate() {
                        b_full_idx[axis] = contracted_idx[0];
                    }
                    
                    // Get the values
                    let a_val = a_view[IxDyn(&a_full_idx)];
                    let b_val = b_view[IxDyn(&b_full_idx)];
                    
                    // Add to the sum
                    sum += a_val * b_val;
                }
                
                // Set the result
                result[IxDyn(&result_idx)] = sum;
            }
        }
        
        // Convert result to Python
        Ok(result.into_pyarray(py))
    }
    
    /// Perform outer product
    pub fn outer<'py>(&self, py: Python<'py>, a: &PyAny, b: &PyAny) -> PyResult<&'py PyAny> {
        // Convert arrays to numpy arrays
        let a_array = PyReadonlyArray::from_object(py, a)?;
        let b_array = PyReadonlyArray::from_object(py, b)?;
        
        // Get array views
        let a_view = a_array.as_array();
        let b_view = b_array.as_array();
        
        // Calculate the shape of the result
        let mut result_shape = Vec::new();
        result_shape.extend(a_array.shape());
        result_shape.extend(b_array.shape());
        
        // Create the result array
        let mut result = Array::zeros(IxDyn(&result_shape));
        
        // Perform outer product
        for a_idx in ndarray::indices(a_array.shape().to_vec()) {
            for b_idx in ndarray::indices(b_array.shape().to_vec()) {
                // Calculate the result index
                let mut result_idx = Vec::new();
                result_idx.extend(a_idx.slice().iter());
                result_idx.extend(b_idx.slice().iter());
                
                // Get the values
                let a_val = a_view[a_idx];
                let b_val = b_view[b_idx];
                
                // Set the result
                result[IxDyn(&result_idx)] = a_val * b_val;
            }
        }
        
        // Convert result to Python
        Ok(result.into_pyarray(py))
    }
    
    /// Perform inner product
    pub fn inner<'py>(&self, py: Python<'py>, a: &PyAny, b: &PyAny) -> PyResult<&'py PyAny> {
        // Convert arrays to numpy arrays
        let a_array = PyReadonlyArray::from_object(py, a)?;
        let b_array = PyReadonlyArray::from_object(py, b)?;
        
        // Check if the last dimension of a matches the last dimension of b
        if a_array.shape().last() != b_array.shape().last() {
            return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                format!("Last dimension of a ({}) must match last dimension of b ({})", 
                        a_array.shape().last().unwrap(), b_array.shape().last().unwrap())
            ));
        }
        
        // Get array views
        let a_view = a_array.as_array();
        let b_view = b_array.as_array();
        
        // Calculate the shape of the result
        let mut result_shape = Vec::new();
        result_shape.extend(&a_array.shape()[..a_array.ndim() - 1]);
        result_shape.extend(&b_array.shape()[..b_array.ndim() - 1]);
        
        // Create the result array
        let mut result = Array::zeros(IxDyn(&result_shape));
        
        // Perform inner product
        let last_dim = *a_array.shape().last().unwrap();
        
        for a_idx in ndarray::indices(a_array.shape()[..a_array.ndim() - 1].to_vec()) {
            for b_idx in ndarray::indices(b_array.shape()[..b_array.ndim() - 1].to_vec()) {
                // Calculate the result index
                let mut result_idx = Vec::new();
                result_idx.extend(a_idx.slice().iter());
                result_idx.extend(b_idx.slice().iter());
                
                // Initialize the sum
                let mut sum = 0.0;
                
                // Iterate over the last dimension
                for i in 0..last_dim {
                    // Calculate the a index
                    let mut a_full_idx = Vec::new();
                    a_full_idx.extend(a_idx.slice().iter());
                    a_full_idx.push(i);
                    
                    // Calculate the b index
                    let mut b_full_idx = Vec::new();
                    b_full_idx.extend(b_idx.slice().iter());
                    b_full_idx.push(i);
                    
                    // Get the values
                    let a_val = a_view[IxDyn(&a_full_idx)];
                    let b_val = b_view[IxDyn(&b_full_idx)];
                    
                    // Add to the sum
                    sum += a_val * b_val;
                }
                
                // Set the result
                result[IxDyn(&result_idx)] = sum;
            }
        }
        
        // Convert result to Python
        Ok(result.into_pyarray(py))
    }
}

/// Python wrapper for TensorOps
#[pyclass(name = "TensorOps")]
pub struct PyTensorOps {
    /// The inner tensor operations
    pub ops: Arc<TensorOps>,
}

#[pymethods]
impl PyTensorOps {
    /// Create a new tensor operations
    #[new]
    fn new(name: &str) -> Self {
        Self {
            ops: Arc::new(TensorOps::new(name)),
        }
    }
    
    /// Perform tensor contraction
    fn tensordot<'py>(&self, py: Python<'py>, a: &PyAny, b: &PyAny, axes: &PyAny) -> PyResult<&'py PyAny> {
        self.ops.tensordot(py, a, b, axes)
    }
    
    /// Perform outer product
    fn outer<'py>(&self, py: Python<'py>, a: &PyAny, b: &PyAny) -> PyResult<&'py PyAny> {
        self.ops.outer(py, a, b)
    }
    
    /// Perform inner product
    fn inner<'py>(&self, py: Python<'py>, a: &PyAny, b: &PyAny) -> PyResult<&'py PyAny> {
        self.ops.inner(py, a, b)
    }
    
    /// Get the name of the operations
    #[getter]
    fn name(&self) -> String {
        self.ops.name.clone()
    }
}
