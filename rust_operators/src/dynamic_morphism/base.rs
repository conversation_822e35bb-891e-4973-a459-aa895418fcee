/*
 * Dynamic Morphism Base
 *
 * This module provides the base class for dynamic morphism.
 */

use pyo3::prelude::*;
use pyo3::types::{PyD<PERSON>, PyList, PyString, PyTuple};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};

/// A type of morphism
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum MorphismType {
    /// Identity morphism
    Identity,
    /// Isomorphism
    Isomorphism,
    /// Homomorphism
    Homomorphism,
    /// Endomorphism
    Endomorphism,
    /// Automorphism
    Automorphism,
    /// Monomorphism
    Monomorphism,
    /// Epimorphism
    Epimorphism,
}

impl std::fmt::Display for MorphismType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            MorphismType::Identity => write!(f, "Identity"),
            MorphismType::Isomorphism => write!(f, "Isomorphism"),
            MorphismType::Homomorphism => write!(f, "Homomorphism"),
            MorphismType::Endomorphism => write!(f, "Endomorphism"),
            MorphismType::Automorphism => write!(f, "Automorphism"),
            MorphismType::Monomorphism => write!(f, "Monomorphism"),
            MorphismType::Epimorphism => write!(f, "Epimorphism"),
        }
    }
}

/// A morphism function
pub type MorphismFunction = Arc<dyn Fn(&PyAny) -> PyResult<PyObject> + Send + Sync>;

/// A dynamic morphism base
pub struct DynamicMorphismBase {
    /// The name of the morphism
    pub name: String,
    /// The type of the morphism
    pub morphism_type: MorphismType,
    /// The domain of the morphism
    pub domain: String,
    /// The codomain of the morphism
    pub codomain: String,
    /// The morphism function
    pub morphism_fn: MorphismFunction,
    /// The inverse morphism function, if any
    pub inverse_fn: Option<MorphismFunction>,
    /// The properties of the morphism
    pub properties: RwLock<HashMap<String, PyObject>>,
}

impl DynamicMorphismBase {
    /// Create a new dynamic morphism
    pub fn new(
        name: &str,
        morphism_type: MorphismType,
        domain: &str,
        codomain: &str,
        morphism_fn: MorphismFunction,
        inverse_fn: Option<MorphismFunction>,
    ) -> Self {
        Self {
            name: name.to_string(),
            morphism_type,
            domain: domain.to_string(),
            codomain: codomain.to_string(),
            morphism_fn,
            inverse_fn,
            properties: RwLock::new(HashMap::new()),
        }
    }
    
    /// Apply the morphism to an object
    pub fn apply(&self, py: Python, obj: &PyAny) -> PyResult<PyObject> {
        (self.morphism_fn)(obj)
    }
    
    /// Apply the inverse morphism to an object
    pub fn apply_inverse(&self, py: Python, obj: &PyAny) -> PyResult<PyObject> {
        if let Some(inverse_fn) = &self.inverse_fn {
            inverse_fn(obj)
        } else {
            Err(PyErr::new::<pyo3::exceptions::PyValueError, _>("Inverse morphism not defined"))
        }
    }
    
    /// Check if the morphism is invertible
    pub fn is_invertible(&self) -> bool {
        self.inverse_fn.is_some()
    }
    
    /// Set a property of the morphism
    pub fn set_property(&self, py: Python, key: &str, value: &PyAny) -> PyResult<()> {
        let mut properties = self.properties.write().unwrap();
        properties.insert(key.to_string(), value.to_object(py));
        Ok(())
    }
    
    /// Get a property of the morphism
    pub fn get_property(&self, py: Python, key: &str) -> PyResult<Option<PyObject>> {
        let properties = self.properties.read().unwrap();
        Ok(properties.get(key).cloned())
    }
    
    /// Check if the morphism has a property
    pub fn has_property(&self, key: &str) -> bool {
        let properties = self.properties.read().unwrap();
        properties.contains_key(key)
    }
    
    /// Remove a property of the morphism
    pub fn remove_property(&self, key: &str) -> bool {
        let mut properties = self.properties.write().unwrap();
        properties.remove(key).is_some()
    }
    
    /// Get all properties of the morphism
    pub fn get_all_properties(&self, py: Python) -> PyResult<HashMap<String, PyObject>> {
        let properties = self.properties.read().unwrap();
        Ok(properties.clone())
    }
}

/// Python wrapper for MorphismType
#[pyclass(name = "MorphismType")]
#[derive(Clone)]
pub struct PyMorphismType {
    /// The inner morphism type
    pub morphism_type: MorphismType,
}

#[pymethods]
impl PyMorphismType {
    /// Create a new morphism type
    #[new]
    fn new(value: &str) -> PyResult<Self> {
        let morphism_type = match value.to_lowercase().as_str() {
            "identity" => MorphismType::Identity,
            "isomorphism" => MorphismType::Isomorphism,
            "homomorphism" => MorphismType::Homomorphism,
            "endomorphism" => MorphismType::Endomorphism,
            "automorphism" => MorphismType::Automorphism,
            "monomorphism" => MorphismType::Monomorphism,
            "epimorphism" => MorphismType::Epimorphism,
            _ => return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(format!("Invalid morphism type: {}", value))),
        };
        
        Ok(Self { morphism_type })
    }
    
    /// Get the name of the morphism type
    #[getter]
    fn name(&self) -> String {
        self.morphism_type.to_string()
    }
    
    /// String representation
    fn __str__(&self) -> String {
        self.morphism_type.to_string()
    }
    
    /// Representation
    fn __repr__(&self) -> String {
        format!("MorphismType.{}", self.morphism_type)
    }
    
    /// Static method to create IDENTITY morphism type
    #[staticmethod]
    fn identity() -> Self {
        Self { morphism_type: MorphismType::Identity }
    }
    
    /// Static method to create ISOMORPHISM morphism type
    #[staticmethod]
    fn isomorphism() -> Self {
        Self { morphism_type: MorphismType::Isomorphism }
    }
    
    /// Static method to create HOMOMORPHISM morphism type
    #[staticmethod]
    fn homomorphism() -> Self {
        Self { morphism_type: MorphismType::Homomorphism }
    }
    
    /// Static method to create ENDOMORPHISM morphism type
    #[staticmethod]
    fn endomorphism() -> Self {
        Self { morphism_type: MorphismType::Endomorphism }
    }
    
    /// Static method to create AUTOMORPHISM morphism type
    #[staticmethod]
    fn automorphism() -> Self {
        Self { morphism_type: MorphismType::Automorphism }
    }
    
    /// Static method to create MONOMORPHISM morphism type
    #[staticmethod]
    fn monomorphism() -> Self {
        Self { morphism_type: MorphismType::Monomorphism }
    }
    
    /// Static method to create EPIMORPHISM morphism type
    #[staticmethod]
    fn epimorphism() -> Self {
        Self { morphism_type: MorphismType::Epimorphism }
    }
}

/// Python wrapper for DynamicMorphismBase
#[pyclass(name = "DynamicMorphismBase")]
pub struct PyDynamicMorphismBase {
    /// The inner morphism
    pub morphism: Arc<DynamicMorphismBase>,
}

#[pymethods]
impl PyDynamicMorphismBase {
    /// Create a new dynamic morphism
    #[new]
    fn new(
        name: &str,
        morphism_type: &PyMorphismType,
        domain: &str,
        codomain: &str,
        morphism_fn: &PyAny,
        inverse_fn: Option<&PyAny>,
    ) -> PyResult<Self> {
        // Create a wrapper for the Python morphism function
        let morphism_fn_wrapper: MorphismFunction = Arc::new(move |obj: &PyAny| -> PyResult<PyObject> {
            Python::with_gil(|py| {
                let args = PyTuple::new(py, &[obj]);
                let result = morphism_fn.call1(args)?;
                Ok(result.to_object(py))
            })
        });
        
        // Create a wrapper for the Python inverse function, if any
        let inverse_fn_wrapper = if let Some(inverse_fn) = inverse_fn {
            Some(Arc::new(move |obj: &PyAny| -> PyResult<PyObject> {
                Python::with_gil(|py| {
                    let args = PyTuple::new(py, &[obj]);
                    let result = inverse_fn.call1(args)?;
                    Ok(result.to_object(py))
                })
            }) as MorphismFunction)
        } else {
            None
        };
        
        // Create the morphism
        let morphism = DynamicMorphismBase::new(
            name,
            morphism_type.morphism_type,
            domain,
            codomain,
            morphism_fn_wrapper,
            inverse_fn_wrapper,
        );
        
        Ok(Self {
            morphism: Arc::new(morphism),
        })
    }
    
    /// Apply the morphism to an object
    fn apply(&self, obj: &PyAny) -> PyResult<PyObject> {
        Python::with_gil(|py| {
            self.morphism.apply(py, obj)
        })
    }
    
    /// Apply the inverse morphism to an object
    fn apply_inverse(&self, obj: &PyAny) -> PyResult<PyObject> {
        Python::with_gil(|py| {
            self.morphism.apply_inverse(py, obj)
        })
    }
    
    /// Check if the morphism is invertible
    fn is_invertible(&self) -> bool {
        self.morphism.is_invertible()
    }
    
    /// Set a property of the morphism
    fn set_property(&self, key: &str, value: &PyAny) -> PyResult<()> {
        Python::with_gil(|py| {
            self.morphism.set_property(py, key, value)
        })
    }
    
    /// Get a property of the morphism
    fn get_property(&self, key: &str) -> PyResult<Option<PyObject>> {
        Python::with_gil(|py| {
            self.morphism.get_property(py, key)
        })
    }
    
    /// Check if the morphism has a property
    fn has_property(&self, key: &str) -> bool {
        self.morphism.has_property(key)
    }
    
    /// Remove a property of the morphism
    fn remove_property(&self, key: &str) -> bool {
        self.morphism.remove_property(key)
    }
    
    /// Get all properties of the morphism
    fn get_all_properties(&self) -> PyResult<HashMap<String, PyObject>> {
        Python::with_gil(|py| {
            self.morphism.get_all_properties(py)
        })
    }
    
    /// Get the name of the morphism
    #[getter]
    fn name(&self) -> String {
        self.morphism.name.clone()
    }
    
    /// Get the type of the morphism
    #[getter]
    fn morphism_type(&self) -> PyMorphismType {
        PyMorphismType {
            morphism_type: self.morphism.morphism_type,
        }
    }
    
    /// Get the domain of the morphism
    #[getter]
    fn domain(&self) -> String {
        self.morphism.domain.clone()
    }
    
    /// Get the codomain of the morphism
    #[getter]
    fn codomain(&self) -> String {
        self.morphism.codomain.clone()
    }
}
