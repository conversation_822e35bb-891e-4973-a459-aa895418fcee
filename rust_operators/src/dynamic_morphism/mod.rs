/*
 * Dynamic Morphism Module
 *
 * This module provides tools for dynamic morphism between different data structures.
 */

use pyo3::prelude::*;
use pyo3::types::{PyDict, PyList, PyString, PyTuple};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};

// Module declarations
mod base;
mod composition;
mod environment;
mod feedback;

// Re-exports
pub use base::{DynamicMorphismBase, PyDynamicMorphismBase};
pub use composition::{MorphismComposition, PyMorphismComposition};
pub use environment::{EnvironmentSensitive, PyEnvironmentSensitive};
pub use feedback::{FeedbackMechanism, PyFeedbackMechanism};

/// Register the dynamic_morphism module with Python
pub fn register_module(_py: Python, m: &PyModule) -> PyResult<()> {
    // Register the DynamicMorphismBase class
    m.add_class::<PyDynamicMorphismBase>()?;
    
    // Register the MorphismComposition class
    m.add_class::<PyMorphismComposition>()?;
    
    // Register the EnvironmentSensitive class
    m.add_class::<PyEnvironmentSensitive>()?;
    
    // Register the FeedbackMechanism class
    m.add_class::<PyFeedbackMechanism>()?;
    
    // Add module-level constants or functions here
    
    Ok(())
}
