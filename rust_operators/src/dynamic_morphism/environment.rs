/*
 * Environment Sensitive
 *
 * This module provides tools for environment-sensitive morphisms.
 */

use pyo3::prelude::*;
use pyo3::types::{<PERSON>yD<PERSON>, <PERSON>yList, PyString, PyTuple};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use super::base::{DynamicMorphismBase, MorphismType, PyDynamicMorphismBase, PyMorphismType};

/// An environment for morphisms
pub struct Environment {
    /// The name of the environment
    pub name: String,
    /// The variables in the environment
    pub variables: RwLock<HashMap<String, PyObject>>,
    /// The parent environment, if any
    pub parent: Option<Arc<Environment>>,
}

impl Environment {
    /// Create a new environment
    pub fn new(name: &str, parent: Option<Arc<Environment>>) -> Self {
        Self {
            name: name.to_string(),
            variables: RwLock::new(HashMap::new()),
            parent,
        }
    }
    
    /// Set a variable in the environment
    pub fn set_variable(&self, py: Python, key: &str, value: &PyAny) -> PyResult<()> {
        let mut variables = self.variables.write().unwrap();
        variables.insert(key.to_string(), value.to_object(py));
        Ok(())
    }
    
    /// Get a variable from the environment
    pub fn get_variable(&self, py: Python, key: &str) -> PyResult<Option<PyObject>> {
        // Check if the variable exists in this environment
        let variables = self.variables.read().unwrap();
        if let Some(value) = variables.get(key) {
            return Ok(Some(value.clone()));
        }
        
        // Check if the variable exists in the parent environment
        if let Some(parent) = &self.parent {
            return parent.get_variable(py, key);
        }
        
        Ok(None)
    }
    
    /// Check if the environment has a variable
    pub fn has_variable(&self, key: &str) -> bool {
        // Check if the variable exists in this environment
        let variables = self.variables.read().unwrap();
        if variables.contains_key(key) {
            return true;
        }
        
        // Check if the variable exists in the parent environment
        if let Some(parent) = &self.parent {
            return parent.has_variable(key);
        }
        
        false
    }
    
    /// Remove a variable from the environment
    pub fn remove_variable(&self, key: &str) -> bool {
        let mut variables = self.variables.write().unwrap();
        variables.remove(key).is_some()
    }
    
    /// Get all variables in the environment
    pub fn get_all_variables(&self, py: Python) -> PyResult<HashMap<String, PyObject>> {
        let mut result = HashMap::new();
        
        // Get variables from the parent environment
        if let Some(parent) = &self.parent {
            result = parent.get_all_variables(py)?;
        }
        
        // Add variables from this environment, overriding parent variables
        let variables = self.variables.read().unwrap();
        for (key, value) in variables.iter() {
            result.insert(key.clone(), value.clone());
        }
        
        Ok(result)
    }
}

/// An environment-sensitive morphism
pub struct EnvironmentSensitive {
    /// The name of the morphism
    pub name: String,
    /// The base morphism
    pub base_morphism: Arc<DynamicMorphismBase>,
    /// The environment
    pub environment: Arc<Environment>,
    /// The properties of the morphism
    pub properties: RwLock<HashMap<String, PyObject>>,
}

impl EnvironmentSensitive {
    /// Create a new environment-sensitive morphism
    pub fn new(
        name: &str,
        base_morphism: Arc<DynamicMorphismBase>,
        environment: Arc<Environment>,
    ) -> Self {
        Self {
            name: name.to_string(),
            base_morphism,
            environment,
            properties: RwLock::new(HashMap::new()),
        }
    }
    
    /// Apply the morphism to an object
    pub fn apply(&self, py: Python, obj: &PyAny) -> PyResult<PyObject> {
        // Apply the base morphism
        self.base_morphism.apply(py, obj)
    }
    
    /// Apply the inverse morphism to an object
    pub fn apply_inverse(&self, py: Python, obj: &PyAny) -> PyResult<PyObject> {
        // Apply the inverse of the base morphism
        self.base_morphism.apply_inverse(py, obj)
    }
    
    /// Check if the morphism is invertible
    pub fn is_invertible(&self) -> bool {
        self.base_morphism.is_invertible()
    }
    
    /// Set a property of the morphism
    pub fn set_property(&self, py: Python, key: &str, value: &PyAny) -> PyResult<()> {
        let mut properties = self.properties.write().unwrap();
        properties.insert(key.to_string(), value.to_object(py));
        Ok(())
    }
    
    /// Get a property of the morphism
    pub fn get_property(&self, py: Python, key: &str) -> PyResult<Option<PyObject>> {
        let properties = self.properties.read().unwrap();
        Ok(properties.get(key).cloned())
    }
    
    /// Check if the morphism has a property
    pub fn has_property(&self, key: &str) -> bool {
        let properties = self.properties.read().unwrap();
        properties.contains_key(key)
    }
    
    /// Remove a property of the morphism
    pub fn remove_property(&self, key: &str) -> bool {
        let mut properties = self.properties.write().unwrap();
        properties.remove(key).is_some()
    }
    
    /// Get all properties of the morphism
    pub fn get_all_properties(&self, py: Python) -> PyResult<HashMap<String, PyObject>> {
        let properties = self.properties.read().unwrap();
        Ok(properties.clone())
    }
    
    /// Get the domain of the morphism
    pub fn domain(&self) -> String {
        self.base_morphism.domain.clone()
    }
    
    /// Get the codomain of the morphism
    pub fn codomain(&self) -> String {
        self.base_morphism.codomain.clone()
    }
    
    /// Get the type of the morphism
    pub fn morphism_type(&self) -> MorphismType {
        self.base_morphism.morphism_type
    }
}

/// Python wrapper for Environment
#[pyclass(name = "Environment")]
pub struct PyEnvironment {
    /// The inner environment
    pub environment: Arc<Environment>,
}

#[pymethods]
impl PyEnvironment {
    /// Create a new environment
    #[new]
    fn new(name: &str, parent: Option<&PyEnvironment>) -> Self {
        let parent_env = parent.map(|p| p.environment.clone());
        
        Self {
            environment: Arc::new(Environment::new(name, parent_env)),
        }
    }
    
    /// Set a variable in the environment
    fn set_variable(&self, key: &str, value: &PyAny) -> PyResult<()> {
        Python::with_gil(|py| {
            self.environment.set_variable(py, key, value)
        })
    }
    
    /// Get a variable from the environment
    fn get_variable(&self, key: &str) -> PyResult<Option<PyObject>> {
        Python::with_gil(|py| {
            self.environment.get_variable(py, key)
        })
    }
    
    /// Check if the environment has a variable
    fn has_variable(&self, key: &str) -> bool {
        self.environment.has_variable(key)
    }
    
    /// Remove a variable from the environment
    fn remove_variable(&self, key: &str) -> bool {
        self.environment.remove_variable(key)
    }
    
    /// Get all variables in the environment
    fn get_all_variables(&self) -> PyResult<HashMap<String, PyObject>> {
        Python::with_gil(|py| {
            self.environment.get_all_variables(py)
        })
    }
    
    /// Get the name of the environment
    #[getter]
    fn name(&self) -> String {
        self.environment.name.clone()
    }
    
    /// Get the parent environment, if any
    #[getter]
    fn parent(&self) -> Option<PyEnvironment> {
        self.environment.parent.as_ref().map(|p| PyEnvironment {
            environment: p.clone(),
        })
    }
}

/// Python wrapper for EnvironmentSensitive
#[pyclass(name = "EnvironmentSensitive")]
pub struct PyEnvironmentSensitive {
    /// The inner morphism
    pub morphism: Arc<EnvironmentSensitive>,
}

#[pymethods]
impl PyEnvironmentSensitive {
    /// Create a new environment-sensitive morphism
    #[new]
    fn new(
        name: &str,
        base_morphism: &PyDynamicMorphismBase,
        environment: &PyEnvironment,
    ) -> Self {
        Self {
            morphism: Arc::new(EnvironmentSensitive::new(
                name,
                base_morphism.morphism.clone(),
                environment.environment.clone(),
            )),
        }
    }
    
    /// Apply the morphism to an object
    fn apply(&self, obj: &PyAny) -> PyResult<PyObject> {
        Python::with_gil(|py| {
            self.morphism.apply(py, obj)
        })
    }
    
    /// Apply the inverse morphism to an object
    fn apply_inverse(&self, obj: &PyAny) -> PyResult<PyObject> {
        Python::with_gil(|py| {
            self.morphism.apply_inverse(py, obj)
        })
    }
    
    /// Check if the morphism is invertible
    fn is_invertible(&self) -> bool {
        self.morphism.is_invertible()
    }
    
    /// Set a property of the morphism
    fn set_property(&self, key: &str, value: &PyAny) -> PyResult<()> {
        Python::with_gil(|py| {
            self.morphism.set_property(py, key, value)
        })
    }
    
    /// Get a property of the morphism
    fn get_property(&self, key: &str) -> PyResult<Option<PyObject>> {
        Python::with_gil(|py| {
            self.morphism.get_property(py, key)
        })
    }
    
    /// Check if the morphism has a property
    fn has_property(&self, key: &str) -> bool {
        self.morphism.has_property(key)
    }
    
    /// Remove a property of the morphism
    fn remove_property(&self, key: &str) -> bool {
        self.morphism.remove_property(key)
    }
    
    /// Get all properties of the morphism
    fn get_all_properties(&self) -> PyResult<HashMap<String, PyObject>> {
        Python::with_gil(|py| {
            self.morphism.get_all_properties(py)
        })
    }
    
    /// Get the name of the morphism
    #[getter]
    fn name(&self) -> String {
        self.morphism.name.clone()
    }
    
    /// Get the domain of the morphism
    #[getter]
    fn domain(&self) -> String {
        self.morphism.domain()
    }
    
    /// Get the codomain of the morphism
    #[getter]
    fn codomain(&self) -> String {
        self.morphism.codomain()
    }
    
    /// Get the type of the morphism
    #[getter]
    fn morphism_type(&self) -> PyMorphismType {
        PyMorphismType {
            morphism_type: self.morphism.morphism_type(),
        }
    }
    
    /// Get the base morphism
    #[getter]
    fn base_morphism(&self) -> PyDynamicMorphismBase {
        PyDynamicMorphismBase {
            morphism: self.morphism.base_morphism.clone(),
        }
    }
    
    /// Get the environment
    #[getter]
    fn environment(&self) -> PyEnvironment {
        PyEnvironment {
            environment: self.morphism.environment.clone(),
        }
    }
}
