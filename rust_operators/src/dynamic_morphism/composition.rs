/*
 * Morphism Composition
 *
 * This module provides tools for composing morphisms.
 */

use pyo3::prelude::*;
use pyo3::types::{<PERSON>yD<PERSON>, PyList, PyString, PyTuple};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use super::base::{DynamicMorphismBase, MorphismType, PyDynamicMorphismBase, PyMorphismType};

/// A composition of morphisms
pub struct MorphismComposition {
    /// The name of the composition
    pub name: String,
    /// The morphisms in the composition
    pub morphisms: RwLock<Vec<Arc<DynamicMorphismBase>>>,
    /// The properties of the composition
    pub properties: RwLock<HashMap<String, PyObject>>,
}

impl MorphismComposition {
    /// Create a new morphism composition
    pub fn new(name: &str) -> Self {
        Self {
            name: name.to_string(),
            morphisms: RwLock::new(Vec::new()),
            properties: RwLock::new(HashMap::new()),
        }
    }
    
    /// Add a morphism to the composition
    pub fn add_morphism(&self, morphism: Arc<DynamicMorphismBase>) -> PyResult<()> {
        let mut morphisms = self.morphisms.write().unwrap();
        
        // Check if the composition is empty
        if morphisms.is_empty() {
            morphisms.push(morphism);
            return Ok(());
        }
        
        // Check if the morphism can be composed with the last morphism
        let last_morphism = morphisms.last().unwrap();
        if last_morphism.codomain != morphism.domain {
            return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                format!("Cannot compose morphism {} with domain {} with morphism {} with codomain {}",
                    morphism.name, morphism.domain, last_morphism.name, last_morphism.codomain)
            ));
        }
        
        morphisms.push(morphism);
        Ok(())
    }
    
    /// Remove a morphism from the composition
    pub fn remove_morphism(&self, index: usize) -> PyResult<Arc<DynamicMorphismBase>> {
        let mut morphisms = self.morphisms.write().unwrap();
        
        if index >= morphisms.len() {
            return Err(PyErr::new::<pyo3::exceptions::PyIndexError, _>(
                format!("Index {} out of range for composition with {} morphisms", index, morphisms.len())
            ));
        }
        
        // Check if removing the morphism would break the composition
        if morphisms.len() > 1 && index < morphisms.len() - 1 {
            let prev_morphism = if index > 0 {
                morphisms.get(index - 1).unwrap()
            } else {
                morphisms.get(index).unwrap()
            };
            
            let next_morphism = morphisms.get(index + 1).unwrap();
            
            if prev_morphism.codomain != next_morphism.domain {
                return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                    format!("Cannot remove morphism at index {} because it would break the composition", index)
                ));
            }
        }
        
        Ok(morphisms.remove(index))
    }
    
    /// Get the number of morphisms in the composition
    pub fn len(&self) -> usize {
        let morphisms = self.morphisms.read().unwrap();
        morphisms.len()
    }
    
    /// Check if the composition is empty
    pub fn is_empty(&self) -> bool {
        let morphisms = self.morphisms.read().unwrap();
        morphisms.is_empty()
    }
    
    /// Get the domain of the composition
    pub fn domain(&self) -> PyResult<String> {
        let morphisms = self.morphisms.read().unwrap();
        
        if morphisms.is_empty() {
            return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>("Composition is empty"));
        }
        
        Ok(morphisms.first().unwrap().domain.clone())
    }
    
    /// Get the codomain of the composition
    pub fn codomain(&self) -> PyResult<String> {
        let morphisms = self.morphisms.read().unwrap();
        
        if morphisms.is_empty() {
            return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>("Composition is empty"));
        }
        
        Ok(morphisms.last().unwrap().codomain.clone())
    }
    
    /// Apply the composition to an object
    pub fn apply(&self, py: Python, obj: &PyAny) -> PyResult<PyObject> {
        let morphisms = self.morphisms.read().unwrap();
        
        if morphisms.is_empty() {
            return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>("Composition is empty"));
        }
        
        let mut result = obj.to_object(py);
        
        for morphism in morphisms.iter() {
            let obj = result.extract::<&PyAny>(py)?;
            result = morphism.apply(py, obj)?;
        }
        
        Ok(result)
    }
    
    /// Apply the inverse composition to an object
    pub fn apply_inverse(&self, py: Python, obj: &PyAny) -> PyResult<PyObject> {
        let morphisms = self.morphisms.read().unwrap();
        
        if morphisms.is_empty() {
            return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>("Composition is empty"));
        }
        
        // Check if all morphisms are invertible
        for morphism in morphisms.iter() {
            if !morphism.is_invertible() {
                return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                    format!("Morphism {} is not invertible", morphism.name)
                ));
            }
        }
        
        let mut result = obj.to_object(py);
        
        // Apply the inverse morphisms in reverse order
        for morphism in morphisms.iter().rev() {
            let obj = result.extract::<&PyAny>(py)?;
            result = morphism.apply_inverse(py, obj)?;
        }
        
        Ok(result)
    }
    
    /// Check if the composition is invertible
    pub fn is_invertible(&self) -> bool {
        let morphisms = self.morphisms.read().unwrap();
        
        if morphisms.is_empty() {
            return false;
        }
        
        // Check if all morphisms are invertible
        for morphism in morphisms.iter() {
            if !morphism.is_invertible() {
                return false;
            }
        }
        
        true
    }
    
    /// Set a property of the composition
    pub fn set_property(&self, py: Python, key: &str, value: &PyAny) -> PyResult<()> {
        let mut properties = self.properties.write().unwrap();
        properties.insert(key.to_string(), value.to_object(py));
        Ok(())
    }
    
    /// Get a property of the composition
    pub fn get_property(&self, py: Python, key: &str) -> PyResult<Option<PyObject>> {
        let properties = self.properties.read().unwrap();
        Ok(properties.get(key).cloned())
    }
    
    /// Check if the composition has a property
    pub fn has_property(&self, key: &str) -> bool {
        let properties = self.properties.read().unwrap();
        properties.contains_key(key)
    }
    
    /// Remove a property of the composition
    pub fn remove_property(&self, key: &str) -> bool {
        let mut properties = self.properties.write().unwrap();
        properties.remove(key).is_some()
    }
    
    /// Get all properties of the composition
    pub fn get_all_properties(&self, py: Python) -> PyResult<HashMap<String, PyObject>> {
        let properties = self.properties.read().unwrap();
        Ok(properties.clone())
    }
}

/// Python wrapper for MorphismComposition
#[pyclass(name = "MorphismComposition")]
pub struct PyMorphismComposition {
    /// The inner composition
    pub composition: Arc<MorphismComposition>,
}

#[pymethods]
impl PyMorphismComposition {
    /// Create a new morphism composition
    #[new]
    fn new(name: &str) -> Self {
        Self {
            composition: Arc::new(MorphismComposition::new(name)),
        }
    }
    
    /// Add a morphism to the composition
    fn add_morphism(&self, morphism: &PyDynamicMorphismBase) -> PyResult<()> {
        self.composition.add_morphism(morphism.morphism.clone())
    }
    
    /// Remove a morphism from the composition
    fn remove_morphism(&self, index: usize) -> PyResult<PyDynamicMorphismBase> {
        let morphism = self.composition.remove_morphism(index)?;
        
        Ok(PyDynamicMorphismBase {
            morphism,
        })
    }
    
    /// Get the number of morphisms in the composition
    fn __len__(&self) -> usize {
        self.composition.len()
    }
    
    /// Check if the composition is empty
    fn is_empty(&self) -> bool {
        self.composition.is_empty()
    }
    
    /// Get the domain of the composition
    fn domain(&self) -> PyResult<String> {
        self.composition.domain()
    }
    
    /// Get the codomain of the composition
    fn codomain(&self) -> PyResult<String> {
        self.composition.codomain()
    }
    
    /// Apply the composition to an object
    fn apply(&self, obj: &PyAny) -> PyResult<PyObject> {
        Python::with_gil(|py| {
            self.composition.apply(py, obj)
        })
    }
    
    /// Apply the inverse composition to an object
    fn apply_inverse(&self, obj: &PyAny) -> PyResult<PyObject> {
        Python::with_gil(|py| {
            self.composition.apply_inverse(py, obj)
        })
    }
    
    /// Check if the composition is invertible
    fn is_invertible(&self) -> bool {
        self.composition.is_invertible()
    }
    
    /// Set a property of the composition
    fn set_property(&self, key: &str, value: &PyAny) -> PyResult<()> {
        Python::with_gil(|py| {
            self.composition.set_property(py, key, value)
        })
    }
    
    /// Get a property of the composition
    fn get_property(&self, key: &str) -> PyResult<Option<PyObject>> {
        Python::with_gil(|py| {
            self.composition.get_property(py, key)
        })
    }
    
    /// Check if the composition has a property
    fn has_property(&self, key: &str) -> bool {
        self.composition.has_property(key)
    }
    
    /// Remove a property of the composition
    fn remove_property(&self, key: &str) -> bool {
        self.composition.remove_property(key)
    }
    
    /// Get all properties of the composition
    fn get_all_properties(&self) -> PyResult<HashMap<String, PyObject>> {
        Python::with_gil(|py| {
            self.composition.get_all_properties(py)
        })
    }
    
    /// Get the name of the composition
    #[getter]
    fn name(&self) -> String {
        self.composition.name.clone()
    }
}
