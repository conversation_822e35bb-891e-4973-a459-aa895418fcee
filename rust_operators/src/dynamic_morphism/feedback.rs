/*
 * Feedback Mechanism
 *
 * This module provides tools for feedback mechanisms in morphisms.
 */

use pyo3::prelude::*;
use pyo3::types::{PyD<PERSON>, PyList, PyString, PyTuple};
use std::collections::{HashMap, VecDeque};
use std::sync::{Arc, RwLock};
use super::base::{DynamicMorphismBase, MorphismType, PyDynamicMorphismBase, PyMorphismType};

/// A feedback entry
#[derive(Clone)]
pub struct FeedbackEntry {
    /// The input object
    pub input: PyObject,
    /// The output object
    pub output: PyObject,
    /// The error, if any
    pub error: Option<String>,
    /// The timestamp
    pub timestamp: f64,
    /// Additional metadata
    pub metadata: HashMap<String, PyObject>,
}

/// A feedback mechanism
pub struct FeedbackMechanism {
    /// The name of the mechanism
    pub name: String,
    /// The base morphism
    pub base_morphism: Arc<DynamicMorphismBase>,
    /// The feedback history
    pub history: RwLock<VecDeque<FeedbackEntry>>,
    /// The maximum history size
    pub max_history_size: usize,
    /// The feedback function
    pub feedback_fn: Option<Arc<dyn Fn(&PyAny, &PyAny, Option<&str>) -> PyResult<PyObject> + Send + Sync>>,
    /// The properties of the mechanism
    pub properties: RwLock<HashMap<String, PyObject>>,
}

impl FeedbackMechanism {
    /// Create a new feedback mechanism
    pub fn new(
        name: &str,
        base_morphism: Arc<DynamicMorphismBase>,
        max_history_size: usize,
        feedback_fn: Option<Arc<dyn Fn(&PyAny, &PyAny, Option<&str>) -> PyResult<PyObject> + Send + Sync>>,
    ) -> Self {
        Self {
            name: name.to_string(),
            base_morphism,
            history: RwLock::new(VecDeque::with_capacity(max_history_size)),
            max_history_size,
            feedback_fn,
            properties: RwLock::new(HashMap::new()),
        }
    }
    
    /// Apply the morphism to an object
    pub fn apply(&self, py: Python, obj: &PyAny) -> PyResult<PyObject> {
        // Apply the base morphism
        let result = match self.base_morphism.apply(py, obj) {
            Ok(result) => {
                // Add the result to the history
                let entry = FeedbackEntry {
                    input: obj.to_object(py),
                    output: result.clone(),
                    error: None,
                    timestamp: std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap_or_default()
                        .as_secs_f64(),
                    metadata: HashMap::new(),
                };
                
                self.add_to_history(entry);
                
                Ok(result)
            }
            Err(e) => {
                // Add the error to the history
                let entry = FeedbackEntry {
                    input: obj.to_object(py),
                    output: py.None(),
                    error: Some(e.to_string()),
                    timestamp: std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap_or_default()
                        .as_secs_f64(),
                    metadata: HashMap::new(),
                };
                
                self.add_to_history(entry);
                
                Err(e)
            }
        };
        
        // Apply the feedback function, if any
        if let Some(feedback_fn) = &self.feedback_fn {
            if let Ok(output) = &result {
                let error = if result.is_err() {
                    Some(result.as_ref().err().unwrap().to_string().as_str())
                } else {
                    None
                };
                
                let output_obj = output.extract::<&PyAny>(py)?;
                let _ = feedback_fn(obj, output_obj, error);
            }
        }
        
        result
    }
    
    /// Apply the inverse morphism to an object
    pub fn apply_inverse(&self, py: Python, obj: &PyAny) -> PyResult<PyObject> {
        // Apply the inverse of the base morphism
        let result = match self.base_morphism.apply_inverse(py, obj) {
            Ok(result) => {
                // Add the result to the history
                let entry = FeedbackEntry {
                    input: obj.to_object(py),
                    output: result.clone(),
                    error: None,
                    timestamp: std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap_or_default()
                        .as_secs_f64(),
                    metadata: HashMap::new(),
                };
                
                self.add_to_history(entry);
                
                Ok(result)
            }
            Err(e) => {
                // Add the error to the history
                let entry = FeedbackEntry {
                    input: obj.to_object(py),
                    output: py.None(),
                    error: Some(e.to_string()),
                    timestamp: std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap_or_default()
                        .as_secs_f64(),
                    metadata: HashMap::new(),
                };
                
                self.add_to_history(entry);
                
                Err(e)
            }
        };
        
        // Apply the feedback function, if any
        if let Some(feedback_fn) = &self.feedback_fn {
            if let Ok(output) = &result {
                let error = if result.is_err() {
                    Some(result.as_ref().err().unwrap().to_string().as_str())
                } else {
                    None
                };
                
                let output_obj = output.extract::<&PyAny>(py)?;
                let _ = feedback_fn(obj, output_obj, error);
            }
        }
        
        result
    }
    
    /// Add an entry to the history
    fn add_to_history(&self, entry: FeedbackEntry) {
        let mut history = self.history.write().unwrap();
        
        // If the history is full, remove the oldest entry
        if history.len() >= self.max_history_size {
            history.pop_front();
        }
        
        history.push_back(entry);
    }
    
    /// Get the history
    pub fn get_history(&self, py: Python) -> PyResult<Vec<HashMap<String, PyObject>>> {
        let history = self.history.read().unwrap();
        
        let mut result = Vec::new();
        
        for entry in history.iter() {
            let mut entry_dict = HashMap::new();
            
            entry_dict.insert("input".to_string(), entry.input.clone());
            entry_dict.insert("output".to_string(), entry.output.clone());
            
            if let Some(error) = &entry.error {
                entry_dict.insert("error".to_string(), error.to_object(py));
            } else {
                entry_dict.insert("error".to_string(), py.None());
            }
            
            entry_dict.insert("timestamp".to_string(), entry.timestamp.to_object(py));
            
            let metadata_dict = PyDict::new(py);
            for (key, value) in &entry.metadata {
                metadata_dict.set_item(key, value)?;
            }
            
            entry_dict.insert("metadata".to_string(), metadata_dict.to_object(py));
            
            result.push(entry_dict);
        }
        
        Ok(result)
    }
    
    /// Clear the history
    pub fn clear_history(&self) {
        let mut history = self.history.write().unwrap();
        history.clear();
    }
    
    /// Get the number of entries in the history
    pub fn history_size(&self) -> usize {
        let history = self.history.read().unwrap();
        history.len()
    }
    
    /// Check if the morphism is invertible
    pub fn is_invertible(&self) -> bool {
        self.base_morphism.is_invertible()
    }
    
    /// Set a property of the mechanism
    pub fn set_property(&self, py: Python, key: &str, value: &PyAny) -> PyResult<()> {
        let mut properties = self.properties.write().unwrap();
        properties.insert(key.to_string(), value.to_object(py));
        Ok(())
    }
    
    /// Get a property of the mechanism
    pub fn get_property(&self, py: Python, key: &str) -> PyResult<Option<PyObject>> {
        let properties = self.properties.read().unwrap();
        Ok(properties.get(key).cloned())
    }
    
    /// Check if the mechanism has a property
    pub fn has_property(&self, key: &str) -> bool {
        let properties = self.properties.read().unwrap();
        properties.contains_key(key)
    }
    
    /// Remove a property of the mechanism
    pub fn remove_property(&self, key: &str) -> bool {
        let mut properties = self.properties.write().unwrap();
        properties.remove(key).is_some()
    }
    
    /// Get all properties of the mechanism
    pub fn get_all_properties(&self, py: Python) -> PyResult<HashMap<String, PyObject>> {
        let properties = self.properties.read().unwrap();
        Ok(properties.clone())
    }
    
    /// Get the domain of the mechanism
    pub fn domain(&self) -> String {
        self.base_morphism.domain.clone()
    }
    
    /// Get the codomain of the mechanism
    pub fn codomain(&self) -> String {
        self.base_morphism.codomain.clone()
    }
    
    /// Get the type of the mechanism
    pub fn morphism_type(&self) -> MorphismType {
        self.base_morphism.morphism_type
    }
}

/// Python wrapper for FeedbackMechanism
#[pyclass(name = "FeedbackMechanism")]
pub struct PyFeedbackMechanism {
    /// The inner mechanism
    pub mechanism: Arc<FeedbackMechanism>,
}

#[pymethods]
impl PyFeedbackMechanism {
    /// Create a new feedback mechanism
    #[new]
    fn new(
        name: &str,
        base_morphism: &PyDynamicMorphismBase,
        max_history_size: usize,
        feedback_fn: Option<&PyAny>,
    ) -> PyResult<Self> {
        // Create a wrapper for the Python feedback function, if any
        let feedback_fn_wrapper = if let Some(feedback_fn) = feedback_fn {
            Some(Arc::new(move |input: &PyAny, output: &PyAny, error: Option<&str>| -> PyResult<PyObject> {
                Python::with_gil(|py| {
                    let args = if let Some(error) = error {
                        PyTuple::new(py, &[input, output, error])
                    } else {
                        PyTuple::new(py, &[input, output, py.None()])
                    };
                    
                    let result = feedback_fn.call1(args)?;
                    Ok(result.to_object(py))
                })
            }) as Arc<dyn Fn(&PyAny, &PyAny, Option<&str>) -> PyResult<PyObject> + Send + Sync>)
        } else {
            None
        };
        
        Ok(Self {
            mechanism: Arc::new(FeedbackMechanism::new(
                name,
                base_morphism.morphism.clone(),
                max_history_size,
                feedback_fn_wrapper,
            )),
        })
    }
    
    /// Apply the mechanism to an object
    fn apply(&self, obj: &PyAny) -> PyResult<PyObject> {
        Python::with_gil(|py| {
            self.mechanism.apply(py, obj)
        })
    }
    
    /// Apply the inverse mechanism to an object
    fn apply_inverse(&self, obj: &PyAny) -> PyResult<PyObject> {
        Python::with_gil(|py| {
            self.mechanism.apply_inverse(py, obj)
        })
    }
    
    /// Get the history
    fn get_history(&self) -> PyResult<Vec<HashMap<String, PyObject>>> {
        Python::with_gil(|py| {
            self.mechanism.get_history(py)
        })
    }
    
    /// Clear the history
    fn clear_history(&self) {
        self.mechanism.clear_history();
    }
    
    /// Get the number of entries in the history
    fn history_size(&self) -> usize {
        self.mechanism.history_size()
    }
    
    /// Check if the mechanism is invertible
    fn is_invertible(&self) -> bool {
        self.mechanism.is_invertible()
    }
    
    /// Set a property of the mechanism
    fn set_property(&self, key: &str, value: &PyAny) -> PyResult<()> {
        Python::with_gil(|py| {
            self.mechanism.set_property(py, key, value)
        })
    }
    
    /// Get a property of the mechanism
    fn get_property(&self, key: &str) -> PyResult<Option<PyObject>> {
        Python::with_gil(|py| {
            self.mechanism.get_property(py, key)
        })
    }
    
    /// Check if the mechanism has a property
    fn has_property(&self, key: &str) -> bool {
        self.mechanism.has_property(key)
    }
    
    /// Remove a property of the mechanism
    fn remove_property(&self, key: &str) -> bool {
        self.mechanism.remove_property(key)
    }
    
    /// Get all properties of the mechanism
    fn get_all_properties(&self) -> PyResult<HashMap<String, PyObject>> {
        Python::with_gil(|py| {
            self.mechanism.get_all_properties(py)
        })
    }
    
    /// Get the name of the mechanism
    #[getter]
    fn name(&self) -> String {
        self.mechanism.name.clone()
    }
    
    /// Get the domain of the mechanism
    #[getter]
    fn domain(&self) -> String {
        self.mechanism.domain()
    }
    
    /// Get the codomain of the mechanism
    #[getter]
    fn codomain(&self) -> String {
        self.mechanism.codomain()
    }
    
    /// Get the type of the mechanism
    #[getter]
    fn morphism_type(&self) -> PyMorphismType {
        PyMorphismType {
            morphism_type: self.mechanism.morphism_type(),
        }
    }
    
    /// Get the base morphism
    #[getter]
    fn base_morphism(&self) -> PyDynamicMorphismBase {
        PyDynamicMorphismBase {
            morphism: self.mechanism.base_morphism.clone(),
        }
    }
    
    /// Get the maximum history size
    #[getter]
    fn max_history_size(&self) -> usize {
        self.mechanism.max_history_size
    }
}
