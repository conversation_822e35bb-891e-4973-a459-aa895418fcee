/*
 * Error Codes
 *
 * This module defines error codes and categories for the error handling system.
 */

use pyo3::prelude::*;
use pyo3::types::{PyDict, PyString};
use std::collections::HashMap;
use std::fmt;
use std::sync::{Arc, RwLock};

/// Error severity levels
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum ErrorSeverity {
    /// Debug information
    Debug = 0,
    /// Informational message
    Info = 1,
    /// Warning
    Warning = 2,
    /// Error
    Error = 3,
    /// Critical error
    Critical = 4,
    /// Fatal error
    Fatal = 5,
}

impl fmt::Display for ErrorSeverity {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ErrorSeverity::Debug => write!(f, "DEBUG"),
            ErrorSeverity::Info => write!(f, "INFO"),
            ErrorSeverity::Warning => write!(f, "WARNING"),
            ErrorSeverity::Error => write!(f, "ERROR"),
            ErrorSeverity::Critical => write!(f, "CRITICAL"),
            ErrorSeverity::Fatal => write!(f, "FATAL"),
        }
    }
}

/// Python wrapper for ErrorSeverity
#[pyclass(name = "ErrorSeverity")]
#[derive(Clone)]
pub struct PyErrorSeverity {
    /// The inner severity
    pub severity: ErrorSeverity,
}

#[pymethods]
impl PyErrorSeverity {
    /// Create a new error severity
    #[new]
    fn new(value: u8) -> PyResult<Self> {
        let severity = match value {
            0 => ErrorSeverity::Debug,
            1 => ErrorSeverity::Info,
            2 => ErrorSeverity::Warning,
            3 => ErrorSeverity::Error,
            4 => ErrorSeverity::Critical,
            5 => ErrorSeverity::Fatal,
            _ => return Err(pyo3::exceptions::PyValueError::new_err(format!("Invalid severity value: {}", value))),
        };

        Ok(Self { severity })
    }

    /// Get the severity value
    #[getter]
    fn value(&self) -> u8 {
        self.severity as u8
    }

    /// Get the severity name
    #[getter]
    fn name(&self) -> String {
        self.severity.to_string()
    }

    /// String representation
    fn __str__(&self) -> String {
        self.severity.to_string()
    }

    /// Representation
    fn __repr__(&self) -> String {
        format!("ErrorSeverity.{}", self.severity)
    }

    /// Static method to create DEBUG severity
    #[staticmethod]
    fn debug() -> Self {
        Self { severity: ErrorSeverity::Debug }
    }

    /// Static method to create INFO severity
    #[staticmethod]
    fn info() -> Self {
        Self { severity: ErrorSeverity::Info }
    }

    /// Static method to create WARNING severity
    #[staticmethod]
    fn warning() -> Self {
        Self { severity: ErrorSeverity::Warning }
    }

    /// Static method to create ERROR severity
    #[staticmethod]
    fn error() -> Self {
        Self { severity: ErrorSeverity::Error }
    }

    /// Static method to create CRITICAL severity
    #[staticmethod]
    fn critical() -> Self {
        Self { severity: ErrorSeverity::Critical }
    }

    /// Static method to create FATAL severity
    #[staticmethod]
    fn fatal() -> Self {
        Self { severity: ErrorSeverity::Fatal }
    }
}

/// Error categories
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ErrorCategory {
    /// System error
    System = 100,
    /// Network error
    Network = 200,
    /// Database error
    Database = 300,
    /// Validation error
    Validation = 400,
    /// Permission error
    Permission = 500,
    /// Resource error
    Resource = 600,
    /// Algorithm error
    Algorithm = 700,
    /// Operator error
    Operator = 800,
    /// API error
    Api = 900,
    /// Unknown error
    Unknown = 999,
}

impl fmt::Display for ErrorCategory {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ErrorCategory::System => write!(f, "SYSTEM"),
            ErrorCategory::Network => write!(f, "NETWORK"),
            ErrorCategory::Database => write!(f, "DATABASE"),
            ErrorCategory::Validation => write!(f, "VALIDATION"),
            ErrorCategory::Permission => write!(f, "PERMISSION"),
            ErrorCategory::Resource => write!(f, "RESOURCE"),
            ErrorCategory::Algorithm => write!(f, "ALGORITHM"),
            ErrorCategory::Operator => write!(f, "OPERATOR"),
            ErrorCategory::Api => write!(f, "API"),
            ErrorCategory::Unknown => write!(f, "UNKNOWN"),
        }
    }
}

/// Python wrapper for ErrorCategory
#[pyclass(name = "ErrorCategory")]
#[derive(Clone)]
pub struct PyErrorCategory {
    /// The inner category
    pub category: ErrorCategory,
}

#[pymethods]
impl PyErrorCategory {
    /// Create a new error category
    #[new]
    fn new(value: u16) -> PyResult<Self> {
        let category = match value {
            100 => ErrorCategory::System,
            200 => ErrorCategory::Network,
            300 => ErrorCategory::Database,
            400 => ErrorCategory::Validation,
            500 => ErrorCategory::Permission,
            600 => ErrorCategory::Resource,
            700 => ErrorCategory::Algorithm,
            800 => ErrorCategory::Operator,
            900 => ErrorCategory::Api,
            999 => ErrorCategory::Unknown,
            _ => return Err(pyo3::exceptions::PyValueError::new_err(format!("Invalid category value: {}", value))),
        };

        Ok(Self { category })
    }

    /// Get the category value
    #[getter]
    fn value(&self) -> u16 {
        self.category as u16
    }

    /// Get the category name
    #[getter]
    fn name(&self) -> String {
        self.category.to_string()
    }

    /// String representation
    fn __str__(&self) -> String {
        self.category.to_string()
    }

    /// Representation
    fn __repr__(&self) -> String {
        format!("ErrorCategory.{}", self.category)
    }

    /// Static method to create SYSTEM category
    #[staticmethod]
    fn system() -> Self {
        Self { category: ErrorCategory::System }
    }

    /// Static method to create NETWORK category
    #[staticmethod]
    fn network() -> Self {
        Self { category: ErrorCategory::Network }
    }

    /// Static method to create DATABASE category
    #[staticmethod]
    fn database() -> Self {
        Self { category: ErrorCategory::Database }
    }

    /// Static method to create VALIDATION category
    #[staticmethod]
    fn validation() -> Self {
        Self { category: ErrorCategory::Validation }
    }

    /// Static method to create PERMISSION category
    #[staticmethod]
    fn permission() -> Self {
        Self { category: ErrorCategory::Permission }
    }

    /// Static method to create RESOURCE category
    #[staticmethod]
    fn resource() -> Self {
        Self { category: ErrorCategory::Resource }
    }

    /// Static method to create ALGORITHM category
    #[staticmethod]
    fn algorithm() -> Self {
        Self { category: ErrorCategory::Algorithm }
    }

    /// Static method to create OPERATOR category
    #[staticmethod]
    fn operator() -> Self {
        Self { category: ErrorCategory::Operator }
    }

    /// Static method to create API category
    #[staticmethod]
    fn api() -> Self {
        Self { category: ErrorCategory::Api }
    }

    /// Static method to create UNKNOWN category
    #[staticmethod]
    fn unknown() -> Self {
        Self { category: ErrorCategory::Unknown }
    }
}

/// An error code
#[derive(Debug, Clone)]
pub struct ErrorCode {
    /// The error code
    pub code: u32,
    /// The error category
    pub category: ErrorCategory,
    /// The error severity
    pub severity: ErrorSeverity,
    /// The error message template
    pub message: String,
    /// The error description
    pub description: String,
    /// The recovery hint
    pub recovery_hint: String,
}

impl ErrorCode {
    /// Create a new error code
    pub fn new(
        code: u32,
        category: ErrorCategory,
        severity: ErrorSeverity,
        message: &str,
        description: &str,
        recovery_hint: &str,
    ) -> Self {
        Self {
            code,
            category,
            severity,
            message: message.to_string(),
            description: description.to_string(),
            recovery_hint: recovery_hint.to_string(),
        }
    }

    /// Format the error message with parameters
    pub fn format_message(&self, params: &HashMap<String, String>) -> String {
        let mut message = self.message.clone();

        for (key, value) in params {
            message = message.replace(&format!("{{{}}}", key), value);
        }

        message
    }
}

impl fmt::Display for ErrorCode {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "[{}:{}] {}", self.category, self.code, self.message)
    }
}

/// Python wrapper for ErrorCode
#[pyclass(name = "ErrorCode")]
#[derive(Clone)]
pub struct PyErrorCode {
    /// The inner error code
    pub error_code: Arc<ErrorCode>,
}

#[pymethods]
impl PyErrorCode {
    /// Create a new error code
    #[new]
    fn new(
        code: u32,
        category: &PyErrorCategory,
        severity: &PyErrorSeverity,
        message: &str,
        description: &str,
        recovery_hint: &str,
    ) -> Self {
        let error_code = ErrorCode::new(
            code,
            category.category,
            severity.severity,
            message,
            description,
            recovery_hint,
        );

        Self {
            error_code: Arc::new(error_code),
        }
    }

    /// Format the error message with parameters
    fn format_message(&self, params: &PyDict) -> PyResult<String> {
        let mut param_map = HashMap::new();

        for (key, value) in params.iter() {
            let key_str = key.extract::<String>()?;
            let value_str = value.extract::<String>()?;
            param_map.insert(key_str, value_str);
        }

        Ok(self.error_code.format_message(&param_map))
    }

    /// Get the error code
    #[getter]
    fn code(&self) -> u32 {
        self.error_code.code
    }

    /// Get the error category
    #[getter]
    fn category(&self) -> PyErrorCategory {
        PyErrorCategory {
            category: self.error_code.category,
        }
    }

    /// Get the error severity
    #[getter]
    fn severity(&self) -> PyErrorSeverity {
        PyErrorSeverity {
            severity: self.error_code.severity,
        }
    }

    /// Get the error message
    #[getter]
    fn message(&self) -> String {
        self.error_code.message.clone()
    }

    /// Get the error description
    #[getter]
    fn description(&self) -> String {
        self.error_code.description.clone()
    }

    /// Get the recovery hint
    #[getter]
    fn recovery_hint(&self) -> String {
        self.error_code.recovery_hint.clone()
    }

    /// String representation
    fn __str__(&self) -> String {
        self.error_code.to_string()
    }

    /// Representation
    fn __repr__(&self) -> String {
        format!(
            "ErrorCode(code={}, category={}, severity={}, message='{}')",
            self.error_code.code,
            self.error_code.category,
            self.error_code.severity,
            self.error_code.message
        )
    }
}

/// A registry for error codes
#[derive(Debug)]
pub struct ErrorCodeRegistry {
    /// The error codes
    pub error_codes: RwLock<HashMap<u32, Arc<ErrorCode>>>,
}

impl ErrorCodeRegistry {
    /// Create a new error code registry
    pub fn new() -> Self {
        Self {
            error_codes: RwLock::new(HashMap::new()),
        }
    }

    /// Register an error code
    pub fn register(&self, error_code: Arc<ErrorCode>) {
        let mut error_codes = self.error_codes.write().unwrap();
        error_codes.insert(error_code.code, error_code);
    }

    /// Get an error code by code
    pub fn get(&self, code: u32) -> Option<Arc<ErrorCode>> {
        let error_codes = self.error_codes.read().unwrap();
        error_codes.get(&code).cloned()
    }

    /// Get all error codes
    pub fn get_all(&self) -> HashMap<u32, Arc<ErrorCode>> {
        let error_codes = self.error_codes.read().unwrap();
        error_codes.clone()
    }

    /// Get error codes by category
    pub fn get_by_category(&self, category: ErrorCategory) -> HashMap<u32, Arc<ErrorCode>> {
        let error_codes = self.error_codes.read().unwrap();
        error_codes
            .iter()
            .filter(|(_, ec)| ec.category == category)
            .map(|(code, ec)| (*code, ec.clone()))
            .collect()
    }

    /// Get error codes by severity
    pub fn get_by_severity(&self, severity: ErrorSeverity) -> HashMap<u32, Arc<ErrorCode>> {
        let error_codes = self.error_codes.read().unwrap();
        error_codes
            .iter()
            .filter(|(_, ec)| ec.severity == severity)
            .map(|(code, ec)| (*code, ec.clone()))
            .collect()
    }
}

/// Python wrapper for ErrorCodeRegistry
#[pyclass(name = "ErrorCodeRegistry")]
pub struct PyErrorCodeRegistry {
    /// The inner registry
    pub registry: Arc<ErrorCodeRegistry>,
}

#[pymethods]
impl PyErrorCodeRegistry {
    /// Create a new error code registry
    #[new]
    fn new() -> Self {
        Self {
            registry: Arc::new(ErrorCodeRegistry::new()),
        }
    }

    /// Register an error code
    fn register(&self, error_code: &PyErrorCode) {
        self.registry.register(error_code.error_code.clone());
    }

    /// Get an error code by code
    fn get(&self, code: u32) -> Option<PyErrorCode> {
        self.registry.get(code).map(|ec| PyErrorCode {
            error_code: ec,
        })
    }

    /// Get all error codes
    fn get_all(&self) -> HashMap<u32, PyErrorCode> {
        let error_codes = self.registry.get_all();
        error_codes
            .into_iter()
            .map(|(code, ec)| (code, PyErrorCode { error_code: ec }))
            .collect()
    }

    /// Get error codes by category
    fn get_by_category(&self, category: &PyErrorCategory) -> HashMap<u32, PyErrorCode> {
        let error_codes = self.registry.get_by_category(category.category);
        error_codes
            .into_iter()
            .map(|(code, ec)| (code, PyErrorCode { error_code: ec }))
            .collect()
    }

    /// Get error codes by severity
    fn get_by_severity(&self, severity: &PyErrorSeverity) -> HashMap<u32, PyErrorCode> {
        let error_codes = self.registry.get_by_severity(severity.severity);
        error_codes
            .into_iter()
            .map(|(code, ec)| (code, PyErrorCode { error_code: ec }))
            .collect()
    }
}
