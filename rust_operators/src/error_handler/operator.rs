/*
 * Error Handler Operator
 *
 * This module provides a Rust implementation of the error handling operator.
 */

use pyo3::prelude::*;
use pyo3::types::{PyDict, PyString};
use std::collections::HashMap;
use std::fmt;
use std::sync::{Arc, RwLock};
use std::time::{SystemTime, UNIX_EPOCH};
use super::error_codes::{<PERSON>rrorCode, ErrorSeverity, PyErrorCode};
use super::handler::{T<PERSON><PERSON><PERSON>, <PERSON><PERSON>r<PERSON><PERSON><PERSON>, <PERSON>yTT<PERSON>rror, PyErrorHandler};
use super::recovery::RecoveryStrategy;

/// Error handling operator
pub struct ErrorHandlingOperator {
    /// The error handler
    pub handler: Arc<ErrorHandler>,
    /// The operator name
    pub name: String,
    /// Performance metrics
    pub metrics: Arc<RwLock<HashMap<String, f64>>>,
}

impl ErrorHandlingOperator {
    /// Create a new error handling operator
    pub fn new(name: &str) -> Self {
        let handler = Arc::new(ErrorHandler::new(name));
        let metrics = Arc::new(RwLock::new(HashMap::new()));
        
        // Initialize metrics
        {
            let mut metrics_guard = metrics.write().unwrap();
            metrics_guard.insert("handled_errors".to_string(), 0.0);
            metrics_guard.insert("successful_recoveries".to_string(), 0.0);
            metrics_guard.insert("failed_recoveries".to_string(), 0.0);
            metrics_guard.insert("total_processing_time".to_string(), 0.0);
            metrics_guard.insert("avg_processing_time".to_string(), 0.0);
        }
        
        Self {
            handler,
            name: name.to_string(),
            metrics,
        }
    }
    
    /// Handle an error
    pub fn handle_error(&self, error: &TTError, context: Option<&PyDict>, strategy: Option<&str>) -> PyResult<PyObject> {
        // Record start time
        let start_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs_f64();
        
        // Handle the error
        self.handler.handle(error);
        
        // Try to recover
        let (success, recovery_data) = self.handler.recover(error);
        
        // Record end time
        let end_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs_f64();
        let processing_time = end_time - start_time;
        
        // Update metrics
        {
            let mut metrics_guard = self.metrics.write().unwrap();
            let handled_errors = metrics_guard.get("handled_errors").unwrap_or(&0.0) + 1.0;
            metrics_guard.insert("handled_errors".to_string(), handled_errors);
            
            let total_processing_time = metrics_guard.get("total_processing_time").unwrap_or(&0.0) + processing_time;
            metrics_guard.insert("total_processing_time".to_string(), total_processing_time);
            
            let avg_processing_time = total_processing_time / handled_errors;
            metrics_guard.insert("avg_processing_time".to_string(), avg_processing_time);
            
            if success {
                let successful_recoveries = metrics_guard.get("successful_recoveries").unwrap_or(&0.0) + 1.0;
                metrics_guard.insert("successful_recoveries".to_string(), successful_recoveries);
            } else {
                let failed_recoveries = metrics_guard.get("failed_recoveries").unwrap_or(&0.0) + 1.0;
                metrics_guard.insert("failed_recoveries".to_string(), failed_recoveries);
            }
        }
        
        // Create result
        Python::with_gil(|py| {
            let result = PyDict::new(py);
            result.set_item("success", success)?;
            result.set_item("error_type", error.error_code.name.clone())?;
            result.set_item("error_message", error.message.clone())?;
            result.set_item("error_code", error.error_code.code)?;
            result.set_item("severity", error.error_code.severity.to_string())?;
            result.set_item("processing_time", processing_time)?;
            
            if let Some(recovery) = recovery_data {
                result.set_item("recovery_data", recovery)?;
            }
            
            Ok(result.to_object(py))
        })
    }
    
    /// Get performance metrics
    pub fn get_metrics(&self) -> HashMap<String, f64> {
        let metrics_guard = self.metrics.read().unwrap();
        metrics_guard.clone()
    }
}

/// Python wrapper for ErrorHandlingOperator
#[pyclass]
pub struct PyErrorHandlingOperator {
    /// The error handling operator
    pub operator: ErrorHandlingOperator,
}

#[pymethods]
impl PyErrorHandlingOperator {
    /// Create a new error handling operator
    #[new]
    fn new(name: &str) -> Self {
        Self {
            operator: ErrorHandlingOperator::new(name),
        }
    }
    
    /// Handle an error
    fn handle_error(&self, error: &PyTTError, context: Option<&PyDict>, strategy: Option<&str>) -> PyResult<PyObject> {
        self.operator.handle_error(&error.error, context, strategy)
    }
    
    /// Get performance metrics
    fn get_metrics(&self) -> HashMap<String, f64> {
        self.operator.get_metrics()
    }
    
    /// Get the name of the operator
    #[getter]
    fn name(&self) -> String {
        self.operator.name.clone()
    }
    
    /// Get metadata
    fn get_metadata(&self) -> PyResult<PyObject> {
        Python::with_gil(|py| {
            let metadata = PyDict::new(py);
            metadata.set_item("name", "error_handling_operator")?;
            metadata.set_item("type", "error_handling")?;
            metadata.set_item("description", "统一的错误处理机制，包括错误捕获、转换、处理和报告")?;
            metadata.set_item("version", "1.0.0")?;
            metadata.set_item("author", "Transcendent Thinking Engine Team")?;
            
            let tags = vec!["error_handling", "recovery", "resilience"];
            metadata.set_item("tags", tags)?;
            
            let input_types = vec!["Exception", "Dict"];
            metadata.set_item("input_types", input_types)?;
            
            let output_types = vec!["ErrorHandlingResult"];
            metadata.set_item("output_types", output_types)?;
            
            let parameters = PyDict::new(py);
            parameters.set_item("max_retries", "最大重试次数")?;
            parameters.set_item("retry_delay", "重试延迟（秒）")?;
            parameters.set_item("log_errors", "是否记录错误")?;
            parameters.set_item("capture_traceback", "是否捕获堆栈跟踪")?;
            parameters.set_item("propagate_after_handling", "处理后是否传播错误")?;
            
            metadata.set_item("parameters", parameters)?;
            
            Ok(metadata.to_object(py))
        })
    }
    
    /// Check compatibility with another operator
    fn is_compatible_with(&self, _other_operator: &PyAny) -> bool {
        // Error handling operator is compatible with all operators
        true
    }
    
    /// Get complexity information
    fn get_complexity(&self) -> PyResult<PyObject> {
        Python::with_gil(|py| {
            let complexity = PyDict::new(py);
            complexity.set_item("time_complexity", "O(1)")?;
            complexity.set_item("space_complexity", "O(1)")?;
            complexity.set_item("description", "错误处理算子的时间和空间复杂度主要取决于处理策略和错误类型")?;
            
            Ok(complexity.to_object(py))
        })
    }
}
