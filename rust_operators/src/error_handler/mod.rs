/*
 * Error Handler Module
 *
 * This module provides tools for handling errors in a unified way.
 */

use pyo3::prelude::*;
use pyo3::types::{PyDict, PyList, PyString};
use std::collections::HashMap;
use std::sync::Arc;

// Module declarations
mod error_codes;
mod handler;
mod recovery;
mod reporter;

// Re-exports
pub use error_codes::{ErrorCode, ErrorCategory, ErrorSeverity, ErrorCodeRegistry};
pub use handler::{<PERSON>rror<PERSON>and<PERSON>, TTError};
pub use recovery::{RecoveryStrategy, RetryStrategy, FallbackStrategy, CircuitBreaker};
pub use reporter::ErrorReporter;

/// Register the error_handler module with Python
pub fn register_module(_py: Python, m: &PyModule) -> PyResult<()> {
    // Register the ErrorSeverity enum
    m.add_class::<error_codes::PyErrorSeverity>()?;
    
    // Register the ErrorCategory enum
    m.add_class::<error_codes::PyErrorCategory>()?;
    
    // Register the ErrorCode class
    m.add_class::<error_codes::PyErrorCode>()?;
    
    // Register the ErrorCodeRegistry class
    m.add_class::<error_codes::PyErrorCodeRegistry>()?;
    
    // Register the TTError class
    m.add_class::<handler::PyTTError>()?;
    
    // Register the ErrorHandler class
    m.add_class::<handler::PyErrorHandler>()?;
    
    // Register the recovery strategy classes
    m.add_class::<recovery::PyRetryStrategy>()?;
    m.add_class::<recovery::PyFallbackStrategy>()?;
    m.add_class::<recovery::PyCircuitBreaker>()?;
    
    // Register the ErrorReporter class
    m.add_class::<reporter::PyErrorReporter>()?;
    
    // Add module-level constants or functions here
    
    Ok(())
}
