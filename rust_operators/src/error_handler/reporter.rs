/*
 * Error Reporter
 *
 * This module provides tools for reporting errors.
 */

use pyo3::prelude::*;
use pyo3::types::{<PERSON>yD<PERSON>, PyList, PyString};
use std::collections::HashMap;
use std::fs::{self, File};
use std::io::Write;
use std::path::{Path, PathBuf};
use std::sync::{Arc, Mutex, RwLock};
use std::time::{SystemTime, UNIX_EPOCH};
use chrono::{DateTime, Utc};
use super::error_codes::{ErrorSeverity, PyErrorSeverity};
use super::handler::{TTError, PyTTError};

/// A reporter for errors
pub struct ErrorReporter {
    /// The name of the reporter
    pub name: String,
    /// The directory for error reports
    pub report_dir: PathBuf,
    /// The minimum severity for reporting
    pub min_severity: ErrorSeverity,
    /// The format for error reports
    pub report_format: String,
    /// The maximum number of reports to buffer
    pub max_reports: usize,
    /// The buffer for error reports
    pub error_buffer: Mutex<Vec<Arc<TTError>>>,
}

impl ErrorReporter {
    /// Create a new error reporter
    pub fn new(name: &str) -> Self {
        let report_dir = PathBuf::from("logs/error_reports");
        
        // Create the report directory if it doesn't exist
        fs::create_dir_all(&report_dir).unwrap_or_default();
        
        Self {
            name: name.to_string(),
            report_dir,
            min_severity: ErrorSeverity::Error,
            report_format: "json".to_string(),
            max_reports: 100,
            error_buffer: Mutex::new(Vec::new()),
        }
    }
    
    /// Configure the reporter
    pub fn configure(
        &self,
        report_dir: Option<&str>,
        min_severity: Option<ErrorSeverity>,
        report_format: Option<&str>,
        max_reports: Option<usize>,
    ) {
        // Update the report directory
        if let Some(dir) = report_dir {
            let path = PathBuf::from(dir);
            fs::create_dir_all(&path).unwrap_or_default();
            let mut report_dir = self.report_dir.clone();
            report_dir = path;
        }
        
        // Update the minimum severity
        if let Some(severity) = min_severity {
            let mut min_severity = self.min_severity;
            min_severity = severity;
        }
        
        // Update the report format
        if let Some(format) = report_format {
            if format == "json" || format == "text" || format == "html" {
                let mut report_format = self.report_format.clone();
                report_format = format.to_string();
            }
        }
        
        // Update the maximum number of reports
        if let Some(max) = max_reports {
            let mut max_reports = self.max_reports;
            max_reports = max;
        }
    }
    
    /// Report an error
    pub fn report(&self, error: &TTError) -> Option<PathBuf> {
        // Check if the error severity is high enough
        if error.error_code.severity < self.min_severity {
            return None;
        }
        
        // Add the error to the buffer
        let mut buffer = self.error_buffer.lock().unwrap();
        buffer.push(Arc::new(error.clone()));
        
        // Generate a report for critical errors
        if error.error_code.severity >= ErrorSeverity::Critical {
            return self.generate_report(error);
        }
        
        // Check if the buffer is full
        if buffer.len() >= self.max_reports {
            drop(buffer); // Release the lock before calling flush
            return self.flush().first().cloned();
        }
        
        None
    }
    
    /// Flush the error buffer and generate a report
    pub fn flush(&self) -> Vec<PathBuf> {
        let mut buffer = self.error_buffer.lock().unwrap();
        
        if buffer.is_empty() {
            return Vec::new();
        }
        
        // Generate a timestamp for the report
        let now = SystemTime::now();
        let datetime: DateTime<Utc> = now.into();
        let timestamp = datetime.format("%Y%m%d_%H%M%S").to_string();
        
        // Create the report path
        let report_path = self.report_dir.join(format!("error_report_{}.{}", timestamp, self.report_format));
        
        // Generate the report
        match self.report_format.as_str() {
            "json" => {
                // Create a JSON report
                let mut json = String::from("[\n");
                
                for (i, error) in buffer.iter().enumerate() {
                    let error_json = format!(
                        "  {{\n    \"code\": {},\n    \"category\": \"{}\",\n    \"severity\": \"{}\",\n    \"message\": \"{}\",\n    \"details\": {},\n    \"timestamp\": {},\n    \"traceback\": \"{}\"\n  }}",
                        error.error_code.code,
                        error.error_code.category,
                        error.error_code.severity,
                        error.message.replace("\"", "\\\""),
                        serde_json::to_string(&error.details).unwrap_or_else(|_| "{}".to_string()),
                        error.timestamp,
                        error.traceback.replace("\"", "\\\"").replace("\n", "\\n")
                    );
                    
                    json.push_str(&error_json);
                    
                    if i < buffer.len() - 1 {
                        json.push_str(",\n");
                    } else {
                        json.push_str("\n");
                    }
                }
                
                json.push_str("]\n");
                
                // Write the report to a file
                if let Ok(mut file) = File::create(&report_path) {
                    if file.write_all(json.as_bytes()).is_ok() {
                        // Clear the buffer
                        buffer.clear();
                        
                        return vec![report_path];
                    }
                }
            }
            "text" => {
                // Create a text report
                let mut text = String::new();
                
                for error in buffer.iter() {
                    text.push_str(&format!("[{}:{}] {}\n", error.error_code.category, error.error_code.code, error.message));
                    text.push_str(&format!("Severity: {}\n", error.error_code.severity));
                    text.push_str(&format!("Timestamp: {}\n", error.timestamp));
                    text.push_str(&format!("Details: {:?}\n", error.details));
                    text.push_str(&format!("Traceback: {}\n", error.traceback));
                    text.push_str("\n");
                }
                
                // Write the report to a file
                if let Ok(mut file) = File::create(&report_path) {
                    if file.write_all(text.as_bytes()).is_ok() {
                        // Clear the buffer
                        buffer.clear();
                        
                        return vec![report_path];
                    }
                }
            }
            "html" => {
                // Create an HTML report
                let mut html = String::from("<!DOCTYPE html>\n<html>\n<head>\n<title>Error Report</title>\n</head>\n<body>\n");
                
                html.push_str("<h1>Error Report</h1>\n");
                html.push_str(&format!("<p>Generated at: {}</p>\n", timestamp));
                html.push_str("<table border='1'>\n");
                html.push_str("<tr><th>Code</th><th>Category</th><th>Severity</th><th>Message</th><th>Details</th></tr>\n");
                
                for error in buffer.iter() {
                    html.push_str("<tr>\n");
                    html.push_str(&format!("<td>{}</td>\n", error.error_code.code));
                    html.push_str(&format!("<td>{}</td>\n", error.error_code.category));
                    html.push_str(&format!("<td>{}</td>\n", error.error_code.severity));
                    html.push_str(&format!("<td>{}</td>\n", error.message));
                    html.push_str(&format!("<td>{:?}</td>\n", error.details));
                    html.push_str("</tr>\n");
                }
                
                html.push_str("</table>\n");
                html.push_str("</body>\n</html>\n");
                
                // Write the report to a file
                if let Ok(mut file) = File::create(&report_path) {
                    if file.write_all(html.as_bytes()).is_ok() {
                        // Clear the buffer
                        buffer.clear();
                        
                        return vec![report_path];
                    }
                }
            }
            _ => {}
        }
        
        Vec::new()
    }
    
    /// Generate a report for a single error
    fn generate_report(&self, error: &TTError) -> Option<PathBuf> {
        // Generate a timestamp for the report
        let now = SystemTime::now();
        let datetime: DateTime<Utc> = now.into();
        let timestamp = datetime.format("%Y%m%d_%H%M%S").to_string();
        
        // Create the report path
        let report_path = self.report_dir.join(format!("critical_error_{}.{}", timestamp, self.report_format));
        
        // Generate the report
        match self.report_format.as_str() {
            "json" => {
                // Create a JSON report
                let error_json = format!(
                    "{{\n  \"code\": {},\n  \"category\": \"{}\",\n  \"severity\": \"{}\",\n  \"message\": \"{}\",\n  \"details\": {},\n  \"timestamp\": {},\n  \"traceback\": \"{}\"\n}}",
                    error.error_code.code,
                    error.error_code.category,
                    error.error_code.severity,
                    error.message.replace("\"", "\\\""),
                    serde_json::to_string(&error.details).unwrap_or_else(|_| "{}".to_string()),
                    error.timestamp,
                    error.traceback.replace("\"", "\\\"").replace("\n", "\\n")
                );
                
                // Write the report to a file
                if let Ok(mut file) = File::create(&report_path) {
                    if file.write_all(error_json.as_bytes()).is_ok() {
                        return Some(report_path);
                    }
                }
            }
            "text" => {
                // Create a text report
                let text = format!(
                    "[{}:{}] {}\nSeverity: {}\nTimestamp: {}\nDetails: {:?}\nTraceback: {}\n",
                    error.error_code.category,
                    error.error_code.code,
                    error.message,
                    error.error_code.severity,
                    error.timestamp,
                    error.details,
                    error.traceback
                );
                
                // Write the report to a file
                if let Ok(mut file) = File::create(&report_path) {
                    if file.write_all(text.as_bytes()).is_ok() {
                        return Some(report_path);
                    }
                }
            }
            "html" => {
                // Create an HTML report
                let html = format!(
                    "<!DOCTYPE html>\n<html>\n<head>\n<title>Critical Error Report</title>\n</head>\n<body>\n<h1>Critical Error Report</h1>\n<p>Generated at: {}</p>\n<table border='1'>\n<tr><th>Code</th><th>Category</th><th>Severity</th><th>Message</th><th>Details</th></tr>\n<tr>\n<td>{}</td>\n<td>{}</td>\n<td>{}</td>\n<td>{}</td>\n<td>{:?}</td>\n</tr>\n</table>\n<h2>Traceback</h2>\n<pre>{}</pre>\n</body>\n</html>\n",
                    timestamp,
                    error.error_code.code,
                    error.error_code.category,
                    error.error_code.severity,
                    error.message,
                    error.details,
                    error.traceback
                );
                
                // Write the report to a file
                if let Ok(mut file) = File::create(&report_path) {
                    if file.write_all(html.as_bytes()).is_ok() {
                        return Some(report_path);
                    }
                }
            }
            _ => {}
        }
        
        None
    }
    
    /// Clean up old reports
    pub fn cleanup_old_reports(&self, max_age_days: u64) -> usize {
        let max_age_secs = max_age_days * 24 * 60 * 60;
        let now = SystemTime::now();
        
        let mut count = 0;
        
        if let Ok(entries) = fs::read_dir(&self.report_dir) {
            for entry in entries.flatten() {
                let path = entry.path();
                
                // Check if the file is a report
                if path.is_file() && path.extension().map_or(false, |ext| {
                    ext == "json" || ext == "text" || ext == "html"
                }) {
                    // Check the file age
                    if let Ok(metadata) = fs::metadata(&path) {
                        if let Ok(modified) = metadata.modified() {
                            if let Ok(age) = now.duration_since(modified) {
                                if age.as_secs() > max_age_secs {
                                    // Remove the file
                                    if fs::remove_file(&path).is_ok() {
                                        count += 1;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        count
    }
}

/// Python wrapper for ErrorReporter
#[pyclass(name = "ErrorReporter")]
pub struct PyErrorReporter {
    /// The inner reporter
    pub reporter: Arc<ErrorReporter>,
}

#[pymethods]
impl PyErrorReporter {
    /// Create a new error reporter
    #[new]
    fn new(name: &str) -> Self {
        Self {
            reporter: Arc::new(ErrorReporter::new(name)),
        }
    }
    
    /// Configure the reporter
    fn configure(
        &self,
        report_dir: Option<&str>,
        min_severity: Option<&PyErrorSeverity>,
        report_format: Option<&str>,
        max_reports: Option<usize>,
    ) {
        self.reporter.configure(
            report_dir,
            min_severity.map(|s| s.severity),
            report_format,
            max_reports,
        );
    }
    
    /// Report an error
    fn report(&self, error: &PyTTError) -> Option<String> {
        self.reporter.report(&error.error).map(|p| p.to_string_lossy().to_string())
    }
    
    /// Flush the error buffer and generate a report
    fn flush(&self) -> Vec<String> {
        self.reporter.flush().iter().map(|p| p.to_string_lossy().to_string()).collect()
    }
    
    /// Clean up old reports
    fn cleanup_old_reports(&self, max_age_days: u64) -> usize {
        self.reporter.cleanup_old_reports(max_age_days)
    }
    
    /// Get the name of the reporter
    #[getter]
    fn name(&self) -> String {
        self.reporter.name.clone()
    }
    
    /// Get the report directory
    #[getter]
    fn report_dir(&self) -> String {
        self.reporter.report_dir.to_string_lossy().to_string()
    }
    
    /// Get the minimum severity
    #[getter]
    fn min_severity(&self) -> PyErrorSeverity {
        PyErrorSeverity {
            severity: self.reporter.min_severity,
        }
    }
    
    /// Get the report format
    #[getter]
    fn report_format(&self) -> String {
        self.reporter.report_format.clone()
    }
    
    /// Get the maximum number of reports
    #[getter]
    fn max_reports(&self) -> usize {
        self.reporter.max_reports
    }
}
