/*
 * Error <PERSON>
 *
 * This module provides tools for handling errors in a unified way.
 */

use pyo3::prelude::*;
use pyo3::types::{PyDict, PyString};
use std::collections::HashMap;
use std::fmt;
use std::sync::{Arc, RwLock};
use std::time::{SystemTime, UNIX_EPOCH};
use super::error_codes::{ErrorCode, ErrorSeverity, PyErrorCode};
use super::recovery::RecoveryStrategy;

/// A Transcendent Thinking Engine error
#[derive(Debug, Clone)]
pub struct TTError {
    /// The error code
    pub error_code: Arc<ErrorCode>,
    /// The error message
    pub message: String,
    /// The error details
    pub details: HashMap<String, String>,
    /// The error timestamp
    pub timestamp: f64,
    /// The error traceback
    pub traceback: String,
}

impl TTError {
    /// Create a new error
    pub fn new(
        error_code: Arc<ErrorCode>,
        message: Option<String>,
        details: Option<HashMap<String, String>>,
        traceback: Option<String>,
    ) -> Self {
        let message = message.unwrap_or_else(|| error_code.message.clone());
        let details = details.unwrap_or_else(HashMap::new);
        let traceback = traceback.unwrap_or_else(|| "".to_string());

        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs_f64();

        Self {
            error_code,
            message,
            details,
            timestamp,
            traceback,
        }
    }

    /// Convert the error to a dictionary
    pub fn to_dict(&self) -> HashMap<String, PyObject> {
        let mut dict = HashMap::new();

        Python::with_gil(|py| {
            dict.insert("code".to_string(), self.error_code.code.to_object(py));
            dict.insert("category".to_string(), self.error_code.category.to_string().to_object(py));
            dict.insert("severity".to_string(), self.error_code.severity.to_string().to_object(py));
            dict.insert("message".to_string(), self.message.to_object(py));

            let details_dict = PyDict::new(py);
            for (key, value) in &self.details {
                details_dict.set_item(key, value).unwrap();
            }
            dict.insert("details".to_string(), details_dict.to_object(py));

            dict.insert("timestamp".to_string(), self.timestamp.to_object(py));
            dict.insert("traceback".to_string(), self.traceback.to_object(py));
        });

        dict
    }
}

impl fmt::Display for TTError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "[{}:{}] {}", self.error_code.category, self.error_code.code, self.message)
    }
}

/// Python wrapper for TTError
#[pyclass(name = "TTError")]
#[derive(Clone)]
pub struct PyTTError {
    /// The inner error
    pub error: Arc<TTError>,
}

#[pymethods]
impl PyTTError {
    /// Create a new error
    #[new]
    fn new(
        error_code: &PyErrorCode,
        message: Option<String>,
        details: Option<&PyDict>,
        py: Python,
    ) -> PyResult<Self> {
        let mut details_map = HashMap::new();

        if let Some(details_dict) = details {
            for (key, value) in details_dict.iter() {
                let key_str = key.extract::<String>()?;
                let value_str = value.extract::<String>()?;
                details_map.insert(key_str, value_str);
            }
        }

        // Get the traceback
        let traceback = match py.import("traceback") {
            Ok(traceback_module) => {
                match traceback_module.getattr("format_exc") {
                    Ok(format_exc) => {
                        match format_exc.call0() {
                            Ok(result) => result.extract::<String>().unwrap_or_default(),
                            Err(_) => "".to_string(),
                        }
                    }
                    Err(_) => "".to_string(),
                }
            }
            Err(_) => "".to_string(),
        };

        let error = TTError::new(
            error_code.error_code.clone(),
            message,
            Some(details_map),
            Some(traceback),
        );

        Ok(Self {
            error: Arc::new(error),
        })
    }

    /// Convert the error to a dictionary
    fn to_dict(&self) -> HashMap<String, PyObject> {
        self.error.to_dict()
    }

    /// Get the error code
    #[getter]
    fn error_code(&self) -> PyErrorCode {
        PyErrorCode {
            error_code: self.error.error_code.clone(),
        }
    }

    /// Get the error message
    #[getter]
    fn message(&self) -> String {
        self.error.message.clone()
    }

    /// Get the error details
    #[getter]
    fn details(&self, py: Python) -> PyResult<PyObject> {
        let details_dict = PyDict::new(py);

        for (key, value) in &self.error.details {
            details_dict.set_item(key, value)?;
        }

        Ok(details_dict.to_object(py))
    }

    /// Get the error timestamp
    #[getter]
    fn timestamp(&self) -> f64 {
        self.error.timestamp
    }

    /// Get the error traceback
    #[getter]
    fn traceback(&self) -> String {
        self.error.traceback.clone()
    }

    /// String representation
    fn __str__(&self) -> String {
        self.error.to_string()
    }

    /// Representation
    fn __repr__(&self) -> String {
        format!(
            "TTError(code={}, category={}, severity={}, message='{}')",
            self.error.error_code.code,
            self.error.error_code.category,
            self.error.error_code.severity,
            self.error.message
        )
    }
}

/// A handler for errors
pub struct ErrorHandler {
    /// The name of the handler
    pub name: String,
    /// The handlers for different severity levels
    pub handlers: RwLock<HashMap<ErrorSeverity, Vec<Arc<dyn Fn(&TTError) + Send + Sync>>>>,
    /// The recovery strategies for different error codes
    pub recovery_strategies: RwLock<HashMap<u32, Arc<dyn RecoveryStrategy + Send + Sync>>>,
}

impl ErrorHandler {
    /// Create a new error handler
    pub fn new(name: &str) -> Self {
        let mut handlers = HashMap::new();

        // Initialize handlers for all severity levels
        handlers.insert(ErrorSeverity::Debug, Vec::new());
        handlers.insert(ErrorSeverity::Info, Vec::new());
        handlers.insert(ErrorSeverity::Warning, Vec::new());
        handlers.insert(ErrorSeverity::Error, Vec::new());
        handlers.insert(ErrorSeverity::Critical, Vec::new());
        handlers.insert(ErrorSeverity::Fatal, Vec::new());

        Self {
            name: name.to_string(),
            handlers: RwLock::new(handlers),
            recovery_strategies: RwLock::new(HashMap::new()),
        }
    }

    /// Register a handler for a severity level
    pub fn register_handler<F>(&self, severity: ErrorSeverity, handler: F)
    where
        F: Fn(&TTError) + Send + Sync + 'static,
    {
        let mut handlers = self.handlers.write().unwrap();

        if let Some(handlers_vec) = handlers.get_mut(&severity) {
            handlers_vec.push(Arc::new(handler));
        }
    }

    /// Register a recovery strategy for an error code
    pub fn register_recovery_strategy(&self, error_code: u32, strategy: Arc<dyn RecoveryStrategy + Send + Sync>) {
        let mut strategies = self.recovery_strategies.write().unwrap();
        strategies.insert(error_code, strategy);
    }

    /// Handle an error
    pub fn handle(&self, error: &TTError) {
        // Call handlers for the error's severity level
        let handlers = self.handlers.read().unwrap();

        if let Some(handlers_vec) = handlers.get(&error.error_code.severity) {
            for handler in handlers_vec {
                handler(error);
            }
        }
    }

    /// Recover from an error
    pub fn recover(&self, error: &TTError) -> (bool, Option<PyObject>) {
        // Get the recovery strategy for the error code
        let strategies = self.recovery_strategies.read().unwrap();

        if let Some(strategy) = strategies.get(&error.error_code.code) {
            // Apply the recovery strategy
            strategy.recover(error)
        } else {
            // No recovery strategy found
            (false, None)
        }
    }
}

/// Python wrapper for ErrorHandler
#[pyclass(name = "ErrorHandler")]
pub struct PyErrorHandler {
    /// The inner handler
    pub handler: Arc<ErrorHandler>,
}

#[pymethods]
impl PyErrorHandler {
    /// Create a new error handler
    #[new]
    fn new(name: &str) -> Self {
        Self {
            handler: Arc::new(ErrorHandler::new(name)),
        }
    }

    /// Register a handler for a severity level
    fn register_handler(&self, severity: &PyErrorSeverity, handler: &PyAny) -> PyResult<()> {
        // Create a wrapper for the Python function
        let handler_fn = Arc::new(move |error: &TTError| {
            Python::with_gil(|py| {
                // Create a PyTTError from the TTError
                let py_error = PyTTError {
                    error: Arc::new(error.clone()),
                };

                // Call the Python function
                let _ = handler.call1((py_error,));
            });
        });

        self.handler.register_handler(severity.severity, handler_fn);

        Ok(())
    }

    /// Register a recovery strategy for an error code
    fn register_recovery_strategy(&self, error_code: u32, strategy: &PyAny) -> PyResult<()> {
        // Create a wrapper for the Python function
        let strategy_fn = Arc::new(move |error: &TTError| -> (bool, Option<PyObject>) {
            Python::with_gil(|py| {
                // Create a PyTTError from the TTError
                let py_error = PyTTError {
                    error: Arc::new(error.clone()),
                };

                // Call the Python function
                match strategy.call1((py_error,)) {
                    Ok(result) => {
                        // Check if the result is a tuple of (bool, Any)
                        if let Ok((success, value)) = result.extract::<(bool, &PyAny)>() {
                            (success, Some(value.to_object(py)))
                        } else if let Ok(success) = result.extract::<bool>() {
                            (success, None)
                        } else {
                            (false, None)
                        }
                    }
                    Err(_) => (false, None),
                }
            })
        });

        self.handler.register_recovery_strategy(error_code, strategy_fn);

        Ok(())
    }

    /// Handle an error
    fn handle(&self, error: &PyTTError) {
        self.handler.handle(&error.error);
    }

    /// Recover from an error
    fn recover(&self, error: &PyTTError) -> (bool, Option<PyObject>) {
        self.handler.recover(&error.error)
    }

    /// Get the name of the handler
    #[getter]
    fn name(&self) -> String {
        self.handler.name.clone()
    }
}
