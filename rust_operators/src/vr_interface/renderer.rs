/*
 * Renderer
 *
 * This module provides tools for rendering VR scenes.
 */

use pyo3::prelude::*;
use pyo3::types::{PyDict, PyList, PyString, PyTuple};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use super::scene::{VRScene, PyVRScene, SceneObject, PySceneObject};
use super::transform::{Vector3, PyVector3, Transform, PyTransform};

/// A render configuration
#[derive(Clone)]
pub struct RenderConfig {
    /// The width of the render
    pub width: u32,
    /// The height of the render
    pub height: u32,
    /// The field of view
    pub fov: f64,
    /// The near clip plane
    pub near_clip: f64,
    /// The far clip plane
    pub far_clip: f64,
    /// The background color
    pub background_color: Vector3,
    /// The ambient light color
    pub ambient_light: Vector3,
    /// The properties of the render
    pub properties: RwLock<HashMap<String, PyObject>>,
}

impl RenderConfig {
    /// Create a new render configuration
    pub fn new(
        width: u32,
        height: u32,
        fov: f64,
        near_clip: f64,
        far_clip: f64,
        background_color: Vector3,
        ambient_light: Vector3,
    ) -> Self {
        Self {
            width,
            height,
            fov,
            near_clip,
            far_clip,
            background_color,
            ambient_light,
            properties: RwLock::new(HashMap::new()),
        }
    }
    
    /// Create a default render configuration
    pub fn default() -> Self {
        Self {
            width: 1920,
            height: 1080,
            fov: 60.0,
            near_clip: 0.1,
            far_clip: 1000.0,
            background_color: Vector3::new(0.0, 0.0, 0.0),
            ambient_light: Vector3::new(0.2, 0.2, 0.2),
            properties: RwLock::new(HashMap::new()),
        }
    }
    
    /// Set a property
    pub fn set_property(&self, py: Python, key: &str, value: &PyAny) -> PyResult<()> {
        let mut properties = self.properties.write().unwrap();
        properties.insert(key.to_string(), value.to_object(py));
        Ok(())
    }
    
    /// Get a property
    pub fn get_property(&self, py: Python, key: &str) -> PyResult<Option<PyObject>> {
        let properties = self.properties.read().unwrap();
        Ok(properties.get(key).cloned())
    }
    
    /// Check if the configuration has a property
    pub fn has_property(&self, key: &str) -> bool {
        let properties = self.properties.read().unwrap();
        properties.contains_key(key)
    }
    
    /// Remove a property
    pub fn remove_property(&self, key: &str) -> bool {
        let mut properties = self.properties.write().unwrap();
        properties.remove(key).is_some()
    }
    
    /// Get all properties
    pub fn get_all_properties(&self, py: Python) -> PyResult<HashMap<String, PyObject>> {
        let properties = self.properties.read().unwrap();
        Ok(properties.clone())
    }
    
    /// Get the configuration as a dictionary
    pub fn to_dict(&self, py: Python) -> PyResult<PyObject> {
        let dict = PyDict::new(py);
        
        dict.set_item("width", self.width)?;
        dict.set_item("height", self.height)?;
        dict.set_item("fov", self.fov)?;
        dict.set_item("near_clip", self.near_clip)?;
        dict.set_item("far_clip", self.far_clip)?;
        
        let background_color_dict = PyDict::new(py);
        background_color_dict.set_item("r", self.background_color.x)?;
        background_color_dict.set_item("g", self.background_color.y)?;
        background_color_dict.set_item("b", self.background_color.z)?;
        dict.set_item("background_color", background_color_dict)?;
        
        let ambient_light_dict = PyDict::new(py);
        ambient_light_dict.set_item("r", self.ambient_light.x)?;
        ambient_light_dict.set_item("g", self.ambient_light.y)?;
        ambient_light_dict.set_item("b", self.ambient_light.z)?;
        dict.set_item("ambient_light", ambient_light_dict)?;
        
        // Add properties
        let properties = self.properties.read().unwrap();
        let properties_dict = PyDict::new(py);
        
        for (key, value) in properties.iter() {
            properties_dict.set_item(key, value)?;
        }
        
        dict.set_item("properties", properties_dict)?;
        
        Ok(dict.to_object(py))
    }
}

/// Python wrapper for RenderConfig
#[pyclass(name = "RenderConfig")]
#[derive(Clone)]
pub struct PyRenderConfig {
    /// The inner render configuration
    pub config: RenderConfig,
}

#[pymethods]
impl PyRenderConfig {
    /// Create a new render configuration
    #[new]
    fn new(
        width: u32,
        height: u32,
        fov: f64,
        near_clip: f64,
        far_clip: f64,
        background_color: &PyVector3,
        ambient_light: &PyVector3,
    ) -> Self {
        Self {
            config: RenderConfig::new(
                width,
                height,
                fov,
                near_clip,
                far_clip,
                background_color.vector.clone(),
                ambient_light.vector.clone(),
            ),
        }
    }
    
    /// Create a default render configuration
    #[staticmethod]
    fn default() -> Self {
        Self {
            config: RenderConfig::default(),
        }
    }
    
    /// Set a property
    fn set_property(&self, key: &str, value: &PyAny) -> PyResult<()> {
        Python::with_gil(|py| {
            self.config.set_property(py, key, value)
        })
    }
    
    /// Get a property
    fn get_property(&self, key: &str) -> PyResult<Option<PyObject>> {
        Python::with_gil(|py| {
            self.config.get_property(py, key)
        })
    }
    
    /// Check if the configuration has a property
    fn has_property(&self, key: &str) -> bool {
        self.config.has_property(key)
    }
    
    /// Remove a property
    fn remove_property(&self, key: &str) -> bool {
        self.config.remove_property(key)
    }
    
    /// Get all properties
    fn get_all_properties(&self) -> PyResult<HashMap<String, PyObject>> {
        Python::with_gil(|py| {
            self.config.get_all_properties(py)
        })
    }
    
    /// Get the configuration as a dictionary
    fn to_dict(&self) -> PyResult<PyObject> {
        Python::with_gil(|py| {
            self.config.to_dict(py)
        })
    }
    
    /// Get the width of the render
    #[getter]
    fn width(&self) -> u32 {
        self.config.width
    }
    
    /// Set the width of the render
    #[setter]
    fn set_width(&mut self, width: u32) {
        self.config.width = width;
    }
    
    /// Get the height of the render
    #[getter]
    fn height(&self) -> u32 {
        self.config.height
    }
    
    /// Set the height of the render
    #[setter]
    fn set_height(&mut self, height: u32) {
        self.config.height = height;
    }
    
    /// Get the field of view
    #[getter]
    fn fov(&self) -> f64 {
        self.config.fov
    }
    
    /// Set the field of view
    #[setter]
    fn set_fov(&mut self, fov: f64) {
        self.config.fov = fov;
    }
    
    /// Get the near clip plane
    #[getter]
    fn near_clip(&self) -> f64 {
        self.config.near_clip
    }
    
    /// Set the near clip plane
    #[setter]
    fn set_near_clip(&mut self, near_clip: f64) {
        self.config.near_clip = near_clip;
    }
    
    /// Get the far clip plane
    #[getter]
    fn far_clip(&self) -> f64 {
        self.config.far_clip
    }
    
    /// Set the far clip plane
    #[setter]
    fn set_far_clip(&mut self, far_clip: f64) {
        self.config.far_clip = far_clip;
    }
    
    /// Get the background color
    #[getter]
    fn background_color(&self) -> PyVector3 {
        PyVector3 {
            vector: self.config.background_color.clone(),
        }
    }
    
    /// Set the background color
    #[setter]
    fn set_background_color(&mut self, background_color: &PyVector3) {
        self.config.background_color = background_color.vector.clone();
    }
    
    /// Get the ambient light color
    #[getter]
    fn ambient_light(&self) -> PyVector3 {
        PyVector3 {
            vector: self.config.ambient_light.clone(),
        }
    }
    
    /// Set the ambient light color
    #[setter]
    fn set_ambient_light(&mut self, ambient_light: &PyVector3) {
        self.config.ambient_light = ambient_light.vector.clone();
    }
}

/// A renderer
pub struct Renderer {
    /// The name of the renderer
    pub name: String,
    /// The render configuration
    pub config: RwLock<RenderConfig>,
    /// The properties of the renderer
    pub properties: RwLock<HashMap<String, PyObject>>,
}

impl Renderer {
    /// Create a new renderer
    pub fn new(name: &str, config: RenderConfig) -> Self {
        Self {
            name: name.to_string(),
            config: RwLock::new(config),
            properties: RwLock::new(HashMap::new()),
        }
    }
    
    /// Set the render configuration
    pub fn set_config(&self, config: RenderConfig) {
        let mut current_config = self.config.write().unwrap();
        *current_config = config;
    }
    
    /// Get the render configuration
    pub fn get_config(&self) -> RenderConfig {
        let config = self.config.read().unwrap();
        config.clone()
    }
    
    /// Set a property
    pub fn set_property(&self, py: Python, key: &str, value: &PyAny) -> PyResult<()> {
        let mut properties = self.properties.write().unwrap();
        properties.insert(key.to_string(), value.to_object(py));
        Ok(())
    }
    
    /// Get a property
    pub fn get_property(&self, py: Python, key: &str) -> PyResult<Option<PyObject>> {
        let properties = self.properties.read().unwrap();
        Ok(properties.get(key).cloned())
    }
    
    /// Check if the renderer has a property
    pub fn has_property(&self, key: &str) -> bool {
        let properties = self.properties.read().unwrap();
        properties.contains_key(key)
    }
    
    /// Remove a property
    pub fn remove_property(&self, key: &str) -> bool {
        let mut properties = self.properties.write().unwrap();
        properties.remove(key).is_some()
    }
    
    /// Get all properties
    pub fn get_all_properties(&self, py: Python) -> PyResult<HashMap<String, PyObject>> {
        let properties = self.properties.read().unwrap();
        Ok(properties.clone())
    }
    
    /// Render a scene
    pub fn render(&self, py: Python, scene: &VRScene) -> PyResult<PyObject> {
        // This is a placeholder implementation
        // In a real application, we would use a rendering engine
        
        let config = self.config.read().unwrap();
        
        // Create a dictionary to represent the rendered scene
        let dict = PyDict::new(py);
        
        dict.set_item("renderer", self.name.clone())?;
        dict.set_item("scene", scene.id.clone())?;
        dict.set_item("config", config.to_dict(py)?)?;
        
        // Add the root objects
        let root_objects = scene.root_objects.read().unwrap();
        let objects_dict = PyDict::new(py);
        
        for (id, object) in root_objects.iter() {
            objects_dict.set_item(id, object.to_dict(py)?)?;
        }
        
        dict.set_item("objects", objects_dict)?;
        
        Ok(dict.to_object(py))
    }
}

/// Python wrapper for Renderer
#[pyclass(name = "Renderer")]
pub struct PyRenderer {
    /// The inner renderer
    pub renderer: Arc<Renderer>,
}

#[pymethods]
impl PyRenderer {
    /// Create a new renderer
    #[new]
    fn new(name: &str, config: &PyRenderConfig) -> Self {
        Self {
            renderer: Arc::new(Renderer::new(name, config.config.clone())),
        }
    }
    
    /// Set the render configuration
    fn set_config(&self, config: &PyRenderConfig) {
        self.renderer.set_config(config.config.clone());
    }
    
    /// Get the render configuration
    fn get_config(&self) -> PyRenderConfig {
        PyRenderConfig {
            config: self.renderer.get_config(),
        }
    }
    
    /// Set a property
    fn set_property(&self, key: &str, value: &PyAny) -> PyResult<()> {
        Python::with_gil(|py| {
            self.renderer.set_property(py, key, value)
        })
    }
    
    /// Get a property
    fn get_property(&self, key: &str) -> PyResult<Option<PyObject>> {
        Python::with_gil(|py| {
            self.renderer.get_property(py, key)
        })
    }
    
    /// Check if the renderer has a property
    fn has_property(&self, key: &str) -> bool {
        self.renderer.has_property(key)
    }
    
    /// Remove a property
    fn remove_property(&self, key: &str) -> bool {
        self.renderer.remove_property(key)
    }
    
    /// Get all properties
    fn get_all_properties(&self) -> PyResult<HashMap<String, PyObject>> {
        Python::with_gil(|py| {
            self.renderer.get_all_properties(py)
        })
    }
    
    /// Render a scene
    fn render(&self, scene: &PyVRScene) -> PyResult<PyObject> {
        Python::with_gil(|py| {
            self.renderer.render(py, &scene.scene)
        })
    }
    
    /// Get the name of the renderer
    #[getter]
    fn name(&self) -> String {
        self.renderer.name.clone()
    }
}
