/*
 * VR Interface Module
 *
 * This module provides tools for interfacing with virtual reality environments.
 */

use pyo3::prelude::*;
use pyo3::types::{<PERSON>yD<PERSON>, PyList, PyString, PyTuple};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};

// Module declarations
mod scene;
mod interaction;
mod transform;
mod renderer;

// Re-exports
pub use scene::{VRScene, PyVRScene, SceneObject, PySceneObject};
pub use interaction::{InteractionHandler, PyInteractionHandler, InteractionEvent, PyInteractionEvent};
pub use transform::{TransformManager, PyTransformManager, Transform, PyTransform};
pub use renderer::{<PERSON><PERSON><PERSON>, PyRenderer, RenderConfig, PyRenderConfig};

/// Register the vr_interface module with Python
pub fn register_module(_py: Python, m: &PyModule) -> PyResult<()> {
    // Register the scene classes
    m.add_class::<PyVRScene>()?;
    m.add_class::<PySceneObject>()?;
    
    // Register the interaction classes
    m.add_class::<PyInteractionHandler>()?;
    m.add_class::<PyInteractionEvent>()?;
    
    // Register the transform classes
    m.add_class::<PyTransformManager>()?;
    m.add_class::<PyTransform>()?;
    
    // Register the renderer classes
    m.add_class::<PyRenderer>()?;
    m.add_class::<PyRenderConfig>()?;
    
    // Add module-level constants or functions here
    
    Ok(())
}
