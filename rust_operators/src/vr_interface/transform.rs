/*
 * Transform Manager
 *
 * This module provides tools for managing transforms in VR scenes.
 */

use pyo3::prelude::*;
use pyo3::types::{<PERSON>yD<PERSON>, PyList, PyString, PyTuple};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use ndarray::{Array1, Array2};

/// A 3D vector
#[derive(<PERSON><PERSON>, Debug)]
pub struct Vector3 {
    /// The x component
    pub x: f64,
    /// The y component
    pub y: f64,
    /// The z component
    pub z: f64,
}

impl Vector3 {
    /// Create a new vector
    pub fn new(x: f64, y: f64, z: f64) -> Self {
        Self { x, y, z }
    }
    
    /// Create a zero vector
    pub fn zero() -> Self {
        Self { x: 0.0, y: 0.0, z: 0.0 }
    }
    
    /// Create a one vector
    pub fn one() -> Self {
        Self { x: 1.0, y: 1.0, z: 1.0 }
    }
    
    /// Convert to a vector
    pub fn to_vec(&self) -> Vec<f64> {
        vec![self.x, self.y, self.z]
    }
    
    /// Convert to an array
    pub fn to_array(&self) -> Array1<f64> {
        Array1::from_vec(vec![self.x, self.y, self.z])
    }
    
    /// Add two vectors
    pub fn add(&self, other: &Vector3) -> Vector3 {
        Vector3::new(self.x + other.x, self.y + other.y, self.z + other.z)
    }
    
    /// Subtract two vectors
    pub fn sub(&self, other: &Vector3) -> Vector3 {
        Vector3::new(self.x - other.x, self.y - other.y, self.z - other.z)
    }
    
    /// Multiply by a scalar
    pub fn mul(&self, scalar: f64) -> Vector3 {
        Vector3::new(self.x * scalar, self.y * scalar, self.z * scalar)
    }
    
    /// Divide by a scalar
    pub fn div(&self, scalar: f64) -> Vector3 {
        Vector3::new(self.x / scalar, self.y / scalar, self.z / scalar)
    }
    
    /// Calculate the dot product
    pub fn dot(&self, other: &Vector3) -> f64 {
        self.x * other.x + self.y * other.y + self.z * other.z
    }
    
    /// Calculate the cross product
    pub fn cross(&self, other: &Vector3) -> Vector3 {
        Vector3::new(
            self.y * other.z - self.z * other.y,
            self.z * other.x - self.x * other.z,
            self.x * other.y - self.y * other.x,
        )
    }
    
    /// Calculate the magnitude
    pub fn magnitude(&self) -> f64 {
        (self.x * self.x + self.y * self.y + self.z * self.z).sqrt()
    }
    
    /// Normalize the vector
    pub fn normalize(&self) -> Vector3 {
        let mag = self.magnitude();
        if mag > 0.0 {
            self.div(mag)
        } else {
            self.clone()
        }
    }
}

/// A transform
#[derive(Clone, Debug)]
pub struct Transform {
    /// The position
    pub position: Vector3,
    /// The rotation
    pub rotation: Vector3,
    /// The scale
    pub scale: Vector3,
}

impl Transform {
    /// Create a new transform
    pub fn new(position: Vector3, rotation: Vector3, scale: Vector3) -> Self {
        Self { position, rotation, scale }
    }
    
    /// Create an identity transform
    pub fn identity() -> Self {
        Self {
            position: Vector3::zero(),
            rotation: Vector3::zero(),
            scale: Vector3::one(),
        }
    }
    
    /// Convert to a matrix
    pub fn to_matrix(&self) -> Array2<f64> {
        // This is a simplified implementation
        // In a real application, we would use quaternions for rotation
        
        // Create a 4x4 identity matrix
        let mut matrix = Array2::eye(4);
        
        // Set the translation components
        matrix[[0, 3]] = self.position.x;
        matrix[[1, 3]] = self.position.y;
        matrix[[2, 3]] = self.position.z;
        
        // Set the scale components
        matrix[[0, 0]] = self.scale.x;
        matrix[[1, 1]] = self.scale.y;
        matrix[[2, 2]] = self.scale.z;
        
        // In a real implementation, we would apply rotation here
        // using quaternions or Euler angles
        
        matrix
    }
    
    /// Apply the transform to a point
    pub fn apply_to_point(&self, point: &Vector3) -> Vector3 {
        // This is a simplified implementation
        // In a real application, we would use the full transformation matrix
        
        // Apply scale
        let scaled = Vector3::new(
            point.x * self.scale.x,
            point.y * self.scale.y,
            point.z * self.scale.z,
        );
        
        // Apply rotation (simplified)
        // In a real implementation, we would use quaternions or rotation matrices
        let rotated = scaled;
        
        // Apply translation
        rotated.add(&self.position)
    }
    
    /// Combine two transforms
    pub fn combine(&self, other: &Transform) -> Transform {
        // This is a simplified implementation
        // In a real application, we would use matrix multiplication
        
        Transform {
            position: self.position.add(&other.position),
            rotation: self.rotation.add(&other.rotation),
            scale: Vector3::new(
                self.scale.x * other.scale.x,
                self.scale.y * other.scale.y,
                self.scale.z * other.scale.z,
            ),
        }
    }
}

/// Python wrapper for Vector3
#[pyclass(name = "Vector3")]
#[derive(Clone)]
pub struct PyVector3 {
    /// The inner vector
    pub vector: Vector3,
}

#[pymethods]
impl PyVector3 {
    /// Create a new vector
    #[new]
    fn new(x: f64, y: f64, z: f64) -> Self {
        Self {
            vector: Vector3::new(x, y, z),
        }
    }
    
    /// Create a zero vector
    #[staticmethod]
    fn zero() -> Self {
        Self {
            vector: Vector3::zero(),
        }
    }
    
    /// Create a one vector
    #[staticmethod]
    fn one() -> Self {
        Self {
            vector: Vector3::one(),
        }
    }
    
    /// Convert to a list
    fn to_list(&self) -> Vec<f64> {
        self.vector.to_vec()
    }
    
    /// Add two vectors
    fn __add__(&self, other: &PyVector3) -> PyVector3 {
        PyVector3 {
            vector: self.vector.add(&other.vector),
        }
    }
    
    /// Subtract two vectors
    fn __sub__(&self, other: &PyVector3) -> PyVector3 {
        PyVector3 {
            vector: self.vector.sub(&other.vector),
        }
    }
    
    /// Multiply by a scalar
    fn __mul__(&self, scalar: f64) -> PyVector3 {
        PyVector3 {
            vector: self.vector.mul(scalar),
        }
    }
    
    /// Divide by a scalar
    fn __truediv__(&self, scalar: f64) -> PyVector3 {
        PyVector3 {
            vector: self.vector.div(scalar),
        }
    }
    
    /// Calculate the dot product
    fn dot(&self, other: &PyVector3) -> f64 {
        self.vector.dot(&other.vector)
    }
    
    /// Calculate the cross product
    fn cross(&self, other: &PyVector3) -> PyVector3 {
        PyVector3 {
            vector: self.vector.cross(&other.vector),
        }
    }
    
    /// Calculate the magnitude
    fn magnitude(&self) -> f64 {
        self.vector.magnitude()
    }
    
    /// Normalize the vector
    fn normalize(&self) -> PyVector3 {
        PyVector3 {
            vector: self.vector.normalize(),
        }
    }
    
    /// Get the x component
    #[getter]
    fn x(&self) -> f64 {
        self.vector.x
    }
    
    /// Set the x component
    #[setter]
    fn set_x(&mut self, x: f64) {
        self.vector.x = x;
    }
    
    /// Get the y component
    #[getter]
    fn y(&self) -> f64 {
        self.vector.y
    }
    
    /// Set the y component
    #[setter]
    fn set_y(&mut self, y: f64) {
        self.vector.y = y;
    }
    
    /// Get the z component
    #[getter]
    fn z(&self) -> f64 {
        self.vector.z
    }
    
    /// Set the z component
    #[setter]
    fn set_z(&mut self, z: f64) {
        self.vector.z = z;
    }
    
    /// String representation
    fn __str__(&self) -> String {
        format!("Vector3({}, {}, {})", self.vector.x, self.vector.y, self.vector.z)
    }
    
    /// Representation
    fn __repr__(&self) -> String {
        format!("Vector3({}, {}, {})", self.vector.x, self.vector.y, self.vector.z)
    }
}

/// Python wrapper for Transform
#[pyclass(name = "Transform")]
#[derive(Clone)]
pub struct PyTransform {
    /// The inner transform
    pub transform: Transform,
}

#[pymethods]
impl PyTransform {
    /// Create a new transform
    #[new]
    fn new(position: &PyVector3, rotation: &PyVector3, scale: &PyVector3) -> Self {
        Self {
            transform: Transform::new(position.vector.clone(), rotation.vector.clone(), scale.vector.clone()),
        }
    }
    
    /// Create an identity transform
    #[staticmethod]
    fn identity() -> Self {
        Self {
            transform: Transform::identity(),
        }
    }
    
    /// Convert to a matrix
    fn to_matrix(&self) -> Vec<Vec<f64>> {
        let matrix = self.transform.to_matrix();
        
        // Convert to a nested vector
        let mut result = Vec::new();
        for i in 0..4 {
            let mut row = Vec::new();
            for j in 0..4 {
                row.push(matrix[[i, j]]);
            }
            result.push(row);
        }
        
        result
    }
    
    /// Apply the transform to a point
    fn apply_to_point(&self, point: &PyVector3) -> PyVector3 {
        PyVector3 {
            vector: self.transform.apply_to_point(&point.vector),
        }
    }
    
    /// Combine two transforms
    fn combine(&self, other: &PyTransform) -> PyTransform {
        PyTransform {
            transform: self.transform.combine(&other.transform),
        }
    }
    
    /// Get the position
    #[getter]
    fn position(&self) -> PyVector3 {
        PyVector3 {
            vector: self.transform.position.clone(),
        }
    }
    
    /// Set the position
    #[setter]
    fn set_position(&mut self, position: &PyVector3) {
        self.transform.position = position.vector.clone();
    }
    
    /// Get the rotation
    #[getter]
    fn rotation(&self) -> PyVector3 {
        PyVector3 {
            vector: self.transform.rotation.clone(),
        }
    }
    
    /// Set the rotation
    #[setter]
    fn set_rotation(&mut self, rotation: &PyVector3) {
        self.transform.rotation = rotation.vector.clone();
    }
    
    /// Get the scale
    #[getter]
    fn scale(&self) -> PyVector3 {
        PyVector3 {
            vector: self.transform.scale.clone(),
        }
    }
    
    /// Set the scale
    #[setter]
    fn set_scale(&mut self, scale: &PyVector3) {
        self.transform.scale = scale.vector.clone();
    }
    
    /// String representation
    fn __str__(&self) -> String {
        format!(
            "Transform(position={}, rotation={}, scale={})",
            self.position().__str__(),
            self.rotation().__str__(),
            self.scale().__str__()
        )
    }
    
    /// Representation
    fn __repr__(&self) -> String {
        format!(
            "Transform(position={}, rotation={}, scale={})",
            self.position().__repr__(),
            self.rotation().__repr__(),
            self.scale().__repr__()
        )
    }
}

/// A transform manager
pub struct TransformManager {
    /// The name of the manager
    pub name: String,
    /// The transforms
    pub transforms: RwLock<HashMap<String, Transform>>,
}

impl TransformManager {
    /// Create a new transform manager
    pub fn new(name: &str) -> Self {
        Self {
            name: name.to_string(),
            transforms: RwLock::new(HashMap::new()),
        }
    }
    
    /// Set a transform
    pub fn set_transform(&self, id: &str, transform: Transform) {
        let mut transforms = self.transforms.write().unwrap();
        transforms.insert(id.to_string(), transform);
    }
    
    /// Get a transform
    pub fn get_transform(&self, id: &str) -> Option<Transform> {
        let transforms = self.transforms.read().unwrap();
        transforms.get(id).cloned()
    }
    
    /// Remove a transform
    pub fn remove_transform(&self, id: &str) -> Option<Transform> {
        let mut transforms = self.transforms.write().unwrap();
        transforms.remove(id)
    }
    
    /// Get all transforms
    pub fn get_all_transforms(&self) -> HashMap<String, Transform> {
        let transforms = self.transforms.read().unwrap();
        transforms.clone()
    }
}

/// Python wrapper for TransformManager
#[pyclass(name = "TransformManager")]
pub struct PyTransformManager {
    /// The inner transform manager
    pub manager: Arc<TransformManager>,
}

#[pymethods]
impl PyTransformManager {
    /// Create a new transform manager
    #[new]
    fn new(name: &str) -> Self {
        Self {
            manager: Arc::new(TransformManager::new(name)),
        }
    }
    
    /// Set a transform
    fn set_transform(&self, id: &str, transform: &PyTransform) {
        self.manager.set_transform(id, transform.transform.clone());
    }
    
    /// Get a transform
    fn get_transform(&self, id: &str) -> Option<PyTransform> {
        self.manager.get_transform(id).map(|t| PyTransform { transform: t })
    }
    
    /// Remove a transform
    fn remove_transform(&self, id: &str) -> Option<PyTransform> {
        self.manager.remove_transform(id).map(|t| PyTransform { transform: t })
    }
    
    /// Get all transforms
    fn get_all_transforms(&self) -> HashMap<String, PyTransform> {
        self.manager.get_all_transforms().into_iter().map(|(id, t)| (id, PyTransform { transform: t })).collect()
    }
    
    /// Get the name of the manager
    #[getter]
    fn name(&self) -> String {
        self.manager.name.clone()
    }
}
