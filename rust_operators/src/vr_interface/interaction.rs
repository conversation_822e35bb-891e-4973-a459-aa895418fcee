/*
 * Interaction Handler
 *
 * This module provides tools for handling interactions in VR scenes.
 */

use pyo3::prelude::*;
use pyo3::types::{PyDict, PyList, PyString, PyTuple};
use std::collections::{HashMap, VecDeque};
use std::sync::{Arc, RwLock};
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use super::transform::{Vector3, PyVector3, Transform, PyTransform};
use super::scene::{SceneObject, PySceneObject};

/// An interaction event type
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum InteractionEventType {
    /// A button press event
    ButtonPress,
    /// A button release event
    ButtonRelease,
    /// A button click event
    ButtonClick,
    /// A button double click event
    ButtonDoubleClick,
    /// A button hold event
    ButtonHold,
    /// A pointer enter event
    PointerEnter,
    /// A pointer exit event
    PointerExit,
    /// A pointer move event
    PointerMove,
    /// A pointer drag start event
    PointerDragStart,
    /// A pointer drag event
    PointerDrag,
    /// A pointer drag end event
    PointerDragEnd,
    /// A gesture start event
    GestureStart,
    /// A gesture event
    Gesture,
    /// A gesture end event
    GestureEnd,
    /// A custom event
    Custom,
}

impl std::fmt::Display for InteractionEventType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            InteractionEventType::ButtonPress => write!(f, "ButtonPress"),
            InteractionEventType::ButtonRelease => write!(f, "ButtonRelease"),
            InteractionEventType::ButtonClick => write!(f, "ButtonClick"),
            InteractionEventType::ButtonDoubleClick => write!(f, "ButtonDoubleClick"),
            InteractionEventType::ButtonHold => write!(f, "ButtonHold"),
            InteractionEventType::PointerEnter => write!(f, "PointerEnter"),
            InteractionEventType::PointerExit => write!(f, "PointerExit"),
            InteractionEventType::PointerMove => write!(f, "PointerMove"),
            InteractionEventType::PointerDragStart => write!(f, "PointerDragStart"),
            InteractionEventType::PointerDrag => write!(f, "PointerDrag"),
            InteractionEventType::PointerDragEnd => write!(f, "PointerDragEnd"),
            InteractionEventType::GestureStart => write!(f, "GestureStart"),
            InteractionEventType::Gesture => write!(f, "Gesture"),
            InteractionEventType::GestureEnd => write!(f, "GestureEnd"),
            InteractionEventType::Custom => write!(f, "Custom"),
        }
    }
}

/// An interaction event
#[derive(Clone)]
pub struct InteractionEvent {
    /// The ID of the event
    pub id: String,
    /// The type of the event
    pub event_type: InteractionEventType,
    /// The source of the event
    pub source: Option<Arc<SceneObject>>,
    /// The target of the event
    pub target: Option<Arc<SceneObject>>,
    /// The position of the event
    pub position: Vector3,
    /// The timestamp of the event
    pub timestamp: f64,
    /// The data of the event
    pub data: HashMap<String, PyObject>,
}

impl InteractionEvent {
    /// Create a new interaction event
    pub fn new(
        id: &str,
        event_type: InteractionEventType,
        source: Option<Arc<SceneObject>>,
        target: Option<Arc<SceneObject>>,
        position: Vector3,
        data: HashMap<String, PyObject>,
    ) -> Self {
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs_f64();
        
        Self {
            id: id.to_string(),
            event_type,
            source,
            target,
            position,
            timestamp,
            data,
        }
    }
    
    /// Get the event as a dictionary
    pub fn to_dict(&self, py: Python) -> PyResult<PyObject> {
        let dict = PyDict::new(py);
        
        dict.set_item("id", self.id.clone())?;
        dict.set_item("type", self.event_type.to_string())?;
        
        if let Some(source) = &self.source {
            dict.set_item("source", source.to_dict(py)?)?;
        } else {
            dict.set_item("source", py.None())?;
        }
        
        if let Some(target) = &self.target {
            dict.set_item("target", target.to_dict(py)?)?;
        } else {
            dict.set_item("target", py.None())?;
        }
        
        let position_dict = PyDict::new(py);
        position_dict.set_item("x", self.position.x)?;
        position_dict.set_item("y", self.position.y)?;
        position_dict.set_item("z", self.position.z)?;
        dict.set_item("position", position_dict)?;
        
        dict.set_item("timestamp", self.timestamp)?;
        
        let data_dict = PyDict::new(py);
        for (key, value) in &self.data {
            data_dict.set_item(key, value)?;
        }
        dict.set_item("data", data_dict)?;
        
        Ok(dict.to_object(py))
    }
}

/// Python wrapper for InteractionEventType
#[pyclass(name = "InteractionEventType")]
#[derive(Clone)]
pub struct PyInteractionEventType {
    /// The inner event type
    pub event_type: InteractionEventType,
}

#[pymethods]
impl PyInteractionEventType {
    /// Create a new interaction event type
    #[new]
    fn new(event_type: &str) -> PyResult<Self> {
        let event_type = match event_type {
            "ButtonPress" => InteractionEventType::ButtonPress,
            "ButtonRelease" => InteractionEventType::ButtonRelease,
            "ButtonClick" => InteractionEventType::ButtonClick,
            "ButtonDoubleClick" => InteractionEventType::ButtonDoubleClick,
            "ButtonHold" => InteractionEventType::ButtonHold,
            "PointerEnter" => InteractionEventType::PointerEnter,
            "PointerExit" => InteractionEventType::PointerExit,
            "PointerMove" => InteractionEventType::PointerMove,
            "PointerDragStart" => InteractionEventType::PointerDragStart,
            "PointerDrag" => InteractionEventType::PointerDrag,
            "PointerDragEnd" => InteractionEventType::PointerDragEnd,
            "GestureStart" => InteractionEventType::GestureStart,
            "Gesture" => InteractionEventType::Gesture,
            "GestureEnd" => InteractionEventType::GestureEnd,
            "Custom" => InteractionEventType::Custom,
            _ => return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(format!("Invalid event type: {}", event_type))),
        };
        
        Ok(Self { event_type })
    }
    
    /// String representation
    fn __str__(&self) -> String {
        self.event_type.to_string()
    }
    
    /// Representation
    fn __repr__(&self) -> String {
        format!("InteractionEventType.{}", self.event_type)
    }
    
    /// Static method to create BUTTON_PRESS event type
    #[staticmethod]
    fn button_press() -> Self {
        Self { event_type: InteractionEventType::ButtonPress }
    }
    
    /// Static method to create BUTTON_RELEASE event type
    #[staticmethod]
    fn button_release() -> Self {
        Self { event_type: InteractionEventType::ButtonRelease }
    }
    
    /// Static method to create BUTTON_CLICK event type
    #[staticmethod]
    fn button_click() -> Self {
        Self { event_type: InteractionEventType::ButtonClick }
    }
    
    /// Static method to create BUTTON_DOUBLE_CLICK event type
    #[staticmethod]
    fn button_double_click() -> Self {
        Self { event_type: InteractionEventType::ButtonDoubleClick }
    }
    
    /// Static method to create BUTTON_HOLD event type
    #[staticmethod]
    fn button_hold() -> Self {
        Self { event_type: InteractionEventType::ButtonHold }
    }
    
    /// Static method to create POINTER_ENTER event type
    #[staticmethod]
    fn pointer_enter() -> Self {
        Self { event_type: InteractionEventType::PointerEnter }
    }
    
    /// Static method to create POINTER_EXIT event type
    #[staticmethod]
    fn pointer_exit() -> Self {
        Self { event_type: InteractionEventType::PointerExit }
    }
    
    /// Static method to create POINTER_MOVE event type
    #[staticmethod]
    fn pointer_move() -> Self {
        Self { event_type: InteractionEventType::PointerMove }
    }
    
    /// Static method to create POINTER_DRAG_START event type
    #[staticmethod]
    fn pointer_drag_start() -> Self {
        Self { event_type: InteractionEventType::PointerDragStart }
    }
    
    /// Static method to create POINTER_DRAG event type
    #[staticmethod]
    fn pointer_drag() -> Self {
        Self { event_type: InteractionEventType::PointerDrag }
    }
    
    /// Static method to create POINTER_DRAG_END event type
    #[staticmethod]
    fn pointer_drag_end() -> Self {
        Self { event_type: InteractionEventType::PointerDragEnd }
    }
    
    /// Static method to create GESTURE_START event type
    #[staticmethod]
    fn gesture_start() -> Self {
        Self { event_type: InteractionEventType::GestureStart }
    }
    
    /// Static method to create GESTURE event type
    #[staticmethod]
    fn gesture() -> Self {
        Self { event_type: InteractionEventType::Gesture }
    }
    
    /// Static method to create GESTURE_END event type
    #[staticmethod]
    fn gesture_end() -> Self {
        Self { event_type: InteractionEventType::GestureEnd }
    }
    
    /// Static method to create CUSTOM event type
    #[staticmethod]
    fn custom() -> Self {
        Self { event_type: InteractionEventType::Custom }
    }
}

/// Python wrapper for InteractionEvent
#[pyclass(name = "InteractionEvent")]
#[derive(Clone)]
pub struct PyInteractionEvent {
    /// The inner event
    pub event: InteractionEvent,
}

#[pymethods]
impl PyInteractionEvent {
    /// Create a new interaction event
    #[new]
    fn new(
        id: &str,
        event_type: &PyInteractionEventType,
        source: Option<&PySceneObject>,
        target: Option<&PySceneObject>,
        position: &PyVector3,
        data: Option<&PyDict>,
        py: Python,
    ) -> PyResult<Self> {
        let mut data_map = HashMap::new();
        
        if let Some(data_dict) = data {
            for (key, value) in data_dict.iter() {
                let key_str = key.extract::<String>()?;
                data_map.insert(key_str, value.to_object(py));
            }
        }
        
        Ok(Self {
            event: InteractionEvent::new(
                id,
                event_type.event_type,
                source.map(|s| s.object.clone()),
                target.map(|t| t.object.clone()),
                position.vector.clone(),
                data_map,
            ),
        })
    }
    
    /// Get the event as a dictionary
    fn to_dict(&self) -> PyResult<PyObject> {
        Python::with_gil(|py| {
            self.event.to_dict(py)
        })
    }
    
    /// Get the ID of the event
    #[getter]
    fn id(&self) -> String {
        self.event.id.clone()
    }
    
    /// Get the type of the event
    #[getter]
    fn event_type(&self) -> PyInteractionEventType {
        PyInteractionEventType {
            event_type: self.event.event_type,
        }
    }
    
    /// Get the source of the event
    #[getter]
    fn source(&self) -> Option<PySceneObject> {
        self.event.source.clone().map(|s| PySceneObject { object: s })
    }
    
    /// Get the target of the event
    #[getter]
    fn target(&self) -> Option<PySceneObject> {
        self.event.target.clone().map(|t| PySceneObject { object: t })
    }
    
    /// Get the position of the event
    #[getter]
    fn position(&self) -> PyVector3 {
        PyVector3 {
            vector: self.event.position.clone(),
        }
    }
    
    /// Get the timestamp of the event
    #[getter]
    fn timestamp(&self) -> f64 {
        self.event.timestamp
    }
    
    /// Get the data of the event
    #[getter]
    fn data(&self, py: Python) -> PyResult<PyObject> {
        let data_dict = PyDict::new(py);
        
        for (key, value) in &self.event.data {
            data_dict.set_item(key, value)?;
        }
        
        Ok(data_dict.to_object(py))
    }
}

/// An event handler function
pub type EventHandlerFunction = Arc<dyn Fn(&InteractionEvent) -> PyResult<()> + Send + Sync>;

/// An interaction handler
pub struct InteractionHandler {
    /// The name of the handler
    pub name: String,
    /// The event handlers
    pub handlers: RwLock<HashMap<InteractionEventType, Vec<EventHandlerFunction>>>,
    /// The event history
    pub history: RwLock<VecDeque<InteractionEvent>>,
    /// The maximum history size
    pub max_history_size: usize,
}

impl InteractionHandler {
    /// Create a new interaction handler
    pub fn new(name: &str, max_history_size: usize) -> Self {
        let mut handlers = HashMap::new();
        
        // Initialize handlers for all event types
        handlers.insert(InteractionEventType::ButtonPress, Vec::new());
        handlers.insert(InteractionEventType::ButtonRelease, Vec::new());
        handlers.insert(InteractionEventType::ButtonClick, Vec::new());
        handlers.insert(InteractionEventType::ButtonDoubleClick, Vec::new());
        handlers.insert(InteractionEventType::ButtonHold, Vec::new());
        handlers.insert(InteractionEventType::PointerEnter, Vec::new());
        handlers.insert(InteractionEventType::PointerExit, Vec::new());
        handlers.insert(InteractionEventType::PointerMove, Vec::new());
        handlers.insert(InteractionEventType::PointerDragStart, Vec::new());
        handlers.insert(InteractionEventType::PointerDrag, Vec::new());
        handlers.insert(InteractionEventType::PointerDragEnd, Vec::new());
        handlers.insert(InteractionEventType::GestureStart, Vec::new());
        handlers.insert(InteractionEventType::Gesture, Vec::new());
        handlers.insert(InteractionEventType::GestureEnd, Vec::new());
        handlers.insert(InteractionEventType::Custom, Vec::new());
        
        Self {
            name: name.to_string(),
            handlers: RwLock::new(handlers),
            history: RwLock::new(VecDeque::with_capacity(max_history_size)),
            max_history_size,
        }
    }
    
    /// Register an event handler
    pub fn register_handler(&self, event_type: InteractionEventType, handler: EventHandlerFunction) {
        let mut handlers = self.handlers.write().unwrap();
        
        if let Some(handlers_vec) = handlers.get_mut(&event_type) {
            handlers_vec.push(handler);
        }
    }
    
    /// Handle an event
    pub fn handle_event(&self, event: &InteractionEvent) -> PyResult<()> {
        // Add the event to the history
        let mut history = self.history.write().unwrap();
        
        // If the history is full, remove the oldest event
        if history.len() >= self.max_history_size {
            history.pop_front();
        }
        
        history.push_back(event.clone());
        
        // Call the handlers for the event type
        let handlers = self.handlers.read().unwrap();
        
        if let Some(handlers_vec) = handlers.get(&event.event_type) {
            for handler in handlers_vec {
                handler(event)?;
            }
        }
        
        Ok(())
    }
    
    /// Get the event history
    pub fn get_history(&self) -> Vec<InteractionEvent> {
        let history = self.history.read().unwrap();
        history.iter().cloned().collect()
    }
    
    /// Clear the event history
    pub fn clear_history(&self) {
        let mut history = self.history.write().unwrap();
        history.clear();
    }
}

/// Python wrapper for InteractionHandler
#[pyclass(name = "InteractionHandler")]
pub struct PyInteractionHandler {
    /// The inner handler
    pub handler: Arc<InteractionHandler>,
}

#[pymethods]
impl PyInteractionHandler {
    /// Create a new interaction handler
    #[new]
    fn new(name: &str, max_history_size: usize) -> Self {
        Self {
            handler: Arc::new(InteractionHandler::new(name, max_history_size)),
        }
    }
    
    /// Register an event handler
    fn register_handler(&self, event_type: &PyInteractionEventType, handler: &PyAny) -> PyResult<()> {
        // Create a wrapper for the Python function
        let handler_fn = Arc::new(move |event: &InteractionEvent| -> PyResult<()> {
            Python::with_gil(|py| {
                // Create a PyInteractionEvent from the InteractionEvent
                let py_event = PyInteractionEvent {
                    event: event.clone(),
                };
                
                // Call the Python function
                handler.call1((py_event,))?;
                
                Ok(())
            })
        });
        
        self.handler.register_handler(event_type.event_type, handler_fn);
        
        Ok(())
    }
    
    /// Handle an event
    fn handle_event(&self, event: &PyInteractionEvent) -> PyResult<()> {
        self.handler.handle_event(&event.event)
    }
    
    /// Get the event history
    fn get_history(&self) -> Vec<PyInteractionEvent> {
        self.handler.get_history().into_iter().map(|e| PyInteractionEvent { event: e }).collect()
    }
    
    /// Clear the event history
    fn clear_history(&self) {
        self.handler.clear_history();
    }
    
    /// Get the name of the handler
    #[getter]
    fn name(&self) -> String {
        self.handler.name.clone()
    }
    
    /// Get the maximum history size
    #[getter]
    fn max_history_size(&self) -> usize {
        self.handler.max_history_size
    }
}
