/*
 * VR Scene
 *
 * This module provides tools for managing VR scenes.
 */

use pyo3::prelude::*;
use pyo3::types::{PyDict, PyList, PyString, PyTuple};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use super::transform::{Transform, PyTransform};

/// A VR scene object
pub struct SceneObject {
    /// The ID of the object
    pub id: String,
    /// The name of the object
    pub name: String,
    /// The type of the object
    pub object_type: String,
    /// The transform of the object
    pub transform: Transform,
    /// The properties of the object
    pub properties: RwLock<HashMap<String, PyObject>>,
    /// The children of the object
    pub children: RwLock<Vec<Arc<SceneObject>>>,
}

impl SceneObject {
    /// Create a new scene object
    pub fn new(id: &str, name: &str, object_type: &str, transform: Transform) -> Self {
        Self {
            id: id.to_string(),
            name: name.to_string(),
            object_type: object_type.to_string(),
            transform,
            properties: RwLock::new(HashMap::new()),
            children: RwLock::new(Vec::new()),
        }
    }
    
    /// Add a child object
    pub fn add_child(&self, child: Arc<SceneObject>) {
        let mut children = self.children.write().unwrap();
        children.push(child);
    }
    
    /// Remove a child object
    pub fn remove_child(&self, id: &str) -> Option<Arc<SceneObject>> {
        let mut children = self.children.write().unwrap();
        
        if let Some(index) = children.iter().position(|child| child.id == id) {
            Some(children.remove(index))
        } else {
            None
        }
    }
    
    /// Get a child object by ID
    pub fn get_child(&self, id: &str) -> Option<Arc<SceneObject>> {
        let children = self.children.read().unwrap();
        
        for child in children.iter() {
            if child.id == id {
                return Some(child.clone());
            }
        }
        
        None
    }
    
    /// Get all children
    pub fn get_children(&self) -> Vec<Arc<SceneObject>> {
        let children = self.children.read().unwrap();
        children.clone()
    }
    
    /// Set a property
    pub fn set_property(&self, py: Python, key: &str, value: &PyAny) -> PyResult<()> {
        let mut properties = self.properties.write().unwrap();
        properties.insert(key.to_string(), value.to_object(py));
        Ok(())
    }
    
    /// Get a property
    pub fn get_property(&self, py: Python, key: &str) -> PyResult<Option<PyObject>> {
        let properties = self.properties.read().unwrap();
        Ok(properties.get(key).cloned())
    }
    
    /// Check if the object has a property
    pub fn has_property(&self, key: &str) -> bool {
        let properties = self.properties.read().unwrap();
        properties.contains_key(key)
    }
    
    /// Remove a property
    pub fn remove_property(&self, key: &str) -> bool {
        let mut properties = self.properties.write().unwrap();
        properties.remove(key).is_some()
    }
    
    /// Get all properties
    pub fn get_all_properties(&self, py: Python) -> PyResult<HashMap<String, PyObject>> {
        let properties = self.properties.read().unwrap();
        Ok(properties.clone())
    }
    
    /// Get the object as a dictionary
    pub fn to_dict(&self, py: Python) -> PyResult<PyObject> {
        let dict = PyDict::new(py);
        
        dict.set_item("id", self.id.clone())?;
        dict.set_item("name", self.name.clone())?;
        dict.set_item("type", self.object_type.clone())?;
        
        // Add transform
        let transform_dict = PyDict::new(py);
        transform_dict.set_item("position", self.transform.position.to_vec())?;
        transform_dict.set_item("rotation", self.transform.rotation.to_vec())?;
        transform_dict.set_item("scale", self.transform.scale.to_vec())?;
        dict.set_item("transform", transform_dict)?;
        
        // Add properties
        let properties = self.properties.read().unwrap();
        let properties_dict = PyDict::new(py);
        
        for (key, value) in properties.iter() {
            properties_dict.set_item(key, value)?;
        }
        
        dict.set_item("properties", properties_dict)?;
        
        // Add children
        let children = self.children.read().unwrap();
        let children_list = PyList::empty(py);
        
        for child in children.iter() {
            let child_dict = child.to_dict(py)?;
            children_list.append(child_dict)?;
        }
        
        dict.set_item("children", children_list)?;
        
        Ok(dict.to_object(py))
    }
}

/// Python wrapper for SceneObject
#[pyclass(name = "SceneObject")]
pub struct PySceneObject {
    /// The inner scene object
    pub object: Arc<SceneObject>,
}

#[pymethods]
impl PySceneObject {
    /// Create a new scene object
    #[new]
    fn new(id: &str, name: &str, object_type: &str, transform: &PyTransform) -> Self {
        Self {
            object: Arc::new(SceneObject::new(id, name, object_type, transform.transform.clone())),
        }
    }
    
    /// Add a child object
    fn add_child(&self, child: &PySceneObject) {
        self.object.add_child(child.object.clone());
    }
    
    /// Remove a child object
    fn remove_child(&self, id: &str) -> Option<PySceneObject> {
        self.object.remove_child(id).map(|obj| PySceneObject { object: obj })
    }
    
    /// Get a child object by ID
    fn get_child(&self, id: &str) -> Option<PySceneObject> {
        self.object.get_child(id).map(|obj| PySceneObject { object: obj })
    }
    
    /// Get all children
    fn get_children(&self) -> Vec<PySceneObject> {
        self.object.get_children().into_iter().map(|obj| PySceneObject { object: obj }).collect()
    }
    
    /// Set a property
    fn set_property(&self, key: &str, value: &PyAny) -> PyResult<()> {
        Python::with_gil(|py| {
            self.object.set_property(py, key, value)
        })
    }
    
    /// Get a property
    fn get_property(&self, key: &str) -> PyResult<Option<PyObject>> {
        Python::with_gil(|py| {
            self.object.get_property(py, key)
        })
    }
    
    /// Check if the object has a property
    fn has_property(&self, key: &str) -> bool {
        self.object.has_property(key)
    }
    
    /// Remove a property
    fn remove_property(&self, key: &str) -> bool {
        self.object.remove_property(key)
    }
    
    /// Get all properties
    fn get_all_properties(&self) -> PyResult<HashMap<String, PyObject>> {
        Python::with_gil(|py| {
            self.object.get_all_properties(py)
        })
    }
    
    /// Get the object as a dictionary
    fn to_dict(&self) -> PyResult<PyObject> {
        Python::with_gil(|py| {
            self.object.to_dict(py)
        })
    }
    
    /// Get the ID of the object
    #[getter]
    fn id(&self) -> String {
        self.object.id.clone()
    }
    
    /// Get the name of the object
    #[getter]
    fn name(&self) -> String {
        self.object.name.clone()
    }
    
    /// Set the name of the object
    #[setter]
    fn set_name(&mut self, name: &str) {
        let mut object_name = self.object.name.clone();
        object_name = name.to_string();
    }
    
    /// Get the type of the object
    #[getter]
    fn object_type(&self) -> String {
        self.object.object_type.clone()
    }
    
    /// Get the transform of the object
    #[getter]
    fn transform(&self) -> PyTransform {
        PyTransform {
            transform: self.object.transform.clone(),
        }
    }
}

/// A VR scene
pub struct VRScene {
    /// The ID of the scene
    pub id: String,
    /// The name of the scene
    pub name: String,
    /// The root objects of the scene
    pub root_objects: RwLock<HashMap<String, Arc<SceneObject>>>,
    /// The properties of the scene
    pub properties: RwLock<HashMap<String, PyObject>>,
}

impl VRScene {
    /// Create a new VR scene
    pub fn new(id: &str, name: &str) -> Self {
        Self {
            id: id.to_string(),
            name: name.to_string(),
            root_objects: RwLock::new(HashMap::new()),
            properties: RwLock::new(HashMap::new()),
        }
    }
    
    /// Add a root object
    pub fn add_root_object(&self, object: Arc<SceneObject>) {
        let mut root_objects = self.root_objects.write().unwrap();
        root_objects.insert(object.id.clone(), object);
    }
    
    /// Remove a root object
    pub fn remove_root_object(&self, id: &str) -> Option<Arc<SceneObject>> {
        let mut root_objects = self.root_objects.write().unwrap();
        root_objects.remove(id)
    }
    
    /// Get a root object by ID
    pub fn get_root_object(&self, id: &str) -> Option<Arc<SceneObject>> {
        let root_objects = self.root_objects.read().unwrap();
        root_objects.get(id).cloned()
    }
    
    /// Get all root objects
    pub fn get_root_objects(&self) -> HashMap<String, Arc<SceneObject>> {
        let root_objects = self.root_objects.read().unwrap();
        root_objects.clone()
    }
    
    /// Set a property
    pub fn set_property(&self, py: Python, key: &str, value: &PyAny) -> PyResult<()> {
        let mut properties = self.properties.write().unwrap();
        properties.insert(key.to_string(), value.to_object(py));
        Ok(())
    }
    
    /// Get a property
    pub fn get_property(&self, py: Python, key: &str) -> PyResult<Option<PyObject>> {
        let properties = self.properties.read().unwrap();
        Ok(properties.get(key).cloned())
    }
    
    /// Check if the scene has a property
    pub fn has_property(&self, key: &str) -> bool {
        let properties = self.properties.read().unwrap();
        properties.contains_key(key)
    }
    
    /// Remove a property
    pub fn remove_property(&self, key: &str) -> bool {
        let mut properties = self.properties.write().unwrap();
        properties.remove(key).is_some()
    }
    
    /// Get all properties
    pub fn get_all_properties(&self, py: Python) -> PyResult<HashMap<String, PyObject>> {
        let properties = self.properties.read().unwrap();
        Ok(properties.clone())
    }
    
    /// Get the scene as a dictionary
    pub fn to_dict(&self, py: Python) -> PyResult<PyObject> {
        let dict = PyDict::new(py);
        
        dict.set_item("id", self.id.clone())?;
        dict.set_item("name", self.name.clone())?;
        
        // Add root objects
        let root_objects = self.root_objects.read().unwrap();
        let objects_dict = PyDict::new(py);
        
        for (id, object) in root_objects.iter() {
            objects_dict.set_item(id, object.to_dict(py)?)?;
        }
        
        dict.set_item("root_objects", objects_dict)?;
        
        // Add properties
        let properties = self.properties.read().unwrap();
        let properties_dict = PyDict::new(py);
        
        for (key, value) in properties.iter() {
            properties_dict.set_item(key, value)?;
        }
        
        dict.set_item("properties", properties_dict)?;
        
        Ok(dict.to_object(py))
    }
}

/// Python wrapper for VRScene
#[pyclass(name = "VRScene")]
pub struct PyVRScene {
    /// The inner VR scene
    pub scene: Arc<VRScene>,
}

#[pymethods]
impl PyVRScene {
    /// Create a new VR scene
    #[new]
    fn new(id: &str, name: &str) -> Self {
        Self {
            scene: Arc::new(VRScene::new(id, name)),
        }
    }
    
    /// Add a root object
    fn add_root_object(&self, object: &PySceneObject) {
        self.scene.add_root_object(object.object.clone());
    }
    
    /// Remove a root object
    fn remove_root_object(&self, id: &str) -> Option<PySceneObject> {
        self.scene.remove_root_object(id).map(|obj| PySceneObject { object: obj })
    }
    
    /// Get a root object by ID
    fn get_root_object(&self, id: &str) -> Option<PySceneObject> {
        self.scene.get_root_object(id).map(|obj| PySceneObject { object: obj })
    }
    
    /// Get all root objects
    fn get_root_objects(&self) -> HashMap<String, PySceneObject> {
        self.scene.get_root_objects().into_iter().map(|(id, obj)| (id, PySceneObject { object: obj })).collect()
    }
    
    /// Set a property
    fn set_property(&self, key: &str, value: &PyAny) -> PyResult<()> {
        Python::with_gil(|py| {
            self.scene.set_property(py, key, value)
        })
    }
    
    /// Get a property
    fn get_property(&self, key: &str) -> PyResult<Option<PyObject>> {
        Python::with_gil(|py| {
            self.scene.get_property(py, key)
        })
    }
    
    /// Check if the scene has a property
    fn has_property(&self, key: &str) -> bool {
        self.scene.has_property(key)
    }
    
    /// Remove a property
    fn remove_property(&self, key: &str) -> bool {
        self.scene.remove_property(key)
    }
    
    /// Get all properties
    fn get_all_properties(&self) -> PyResult<HashMap<String, PyObject>> {
        Python::with_gil(|py| {
            self.scene.get_all_properties(py)
        })
    }
    
    /// Get the scene as a dictionary
    fn to_dict(&self) -> PyResult<PyObject> {
        Python::with_gil(|py| {
            self.scene.to_dict(py)
        })
    }
    
    /// Get the ID of the scene
    #[getter]
    fn id(&self) -> String {
        self.scene.id.clone()
    }
    
    /// Get the name of the scene
    #[getter]
    fn name(&self) -> String {
        self.scene.name.clone()
    }
    
    /// Set the name of the scene
    #[setter]
    fn set_name(&mut self, name: &str) {
        let mut scene_name = self.scene.name.clone();
        scene_name = name.to_string();
    }
}
