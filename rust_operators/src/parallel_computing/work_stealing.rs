/*
 * Work Stealing
 *
 * This module provides a work stealing scheduler for parallel computing.
 */

use pyo3::prelude::*;
use pyo3::types::{<PERSON>y<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, PyTuple};
use std::sync::{<PERSON>, RwLock, Mutex};
use std::collections::{HashMap, BinaryHeap, VecDeque};
use std::cmp::{Ordering, Reverse};
use std::time::{Duration, Instant};
use std::thread;
use crossbeam::channel::{bounded, unbounded, Sender, Receiver};
use crossbeam::deque::{Worker, Stealer, Injector};
use rayon::prelude::*;

/// A work stealer
pub struct WorkStealer {
    /// The name of the stealer
    pub name: String,
    /// The number of workers
    pub num_workers: usize,
    /// The global queue
    pub global_queue: Arc<Injector<Task>>,
    /// The workers
    pub workers: Vec<Worker<Task>>,
    /// The stealers
    pub stealers: Vec<Stealer<Task>>,
    /// The worker threads
    pub worker_threads: RwLock<Vec<thread::Jo<PERSON><PERSON><PERSON><PERSON><()>>>,
    /// The stop flag
    pub stop: RwLock<bool>,
}

/// A task
pub struct Task {
    /// The ID of the task
    pub id: String,
    /// The function to execute
    pub func: PyObject,
    /// The arguments to pass to the function
    pub args: PyObject,
    /// The keyword arguments to pass to the function
    pub kwargs: PyObject,
}

impl Task {
    /// Create a new task
    pub fn new(id: &str, func: PyObject, args: PyObject, kwargs: PyObject) -> Self {
        Self {
            id: id.to_string(),
            func,
            args,
            kwargs,
        }
    }
    
    /// Execute the task
    pub fn execute(&self, py: Python) -> PyResult<PyObject> {
        self.func.call(py, &self.args, Some(&self.kwargs))
    }
}

impl WorkStealer {
    /// Create a new work stealer
    pub fn new(name: &str, num_workers: usize) -> Self {
        // Create the global queue
        let global_queue = Arc::new(Injector::new());
        
        // Create workers and stealers
        let mut workers = Vec::with_capacity(num_workers);
        let mut stealers = Vec::with_capacity(num_workers);
        
        for _ in 0..num_workers {
            let worker = Worker::new_fifo();
            stealers.push(worker.stealer());
            workers.push(worker);
        }
        
        Self {
            name: name.to_string(),
            num_workers,
            global_queue,
            workers,
            stealers,
            worker_threads: RwLock::new(Vec::new()),
            stop: RwLock::new(false),
        }
    }
    
    /// Start the work stealer
    pub fn start(&self) {
        // Create worker threads
        let mut worker_threads = self.worker_threads.write().unwrap();
        
        for id in 0..self.num_workers {
            let worker = self.workers[id].clone();
            let stealers = self.stealers.clone();
            let global_queue = self.global_queue.clone();
            let stop = self.stop.clone();
            
            let worker_thread = thread::spawn(move || {
                while !*stop.read().unwrap() {
                    // Try to pop a task from the local queue
                    if let Some(task) = worker.pop() {
                        // Execute the task
                        Python::with_gil(|py| {
                            let _ = task.execute(py);
                        });
                        continue;
                    }
                    
                    // Try to steal a task from the global queue
                    if let Some(task) = global_queue.steal_batch_and_pop(&worker) {
                        // Execute the task
                        Python::with_gil(|py| {
                            let _ = task.execute(py);
                        });
                        continue;
                    }
                    
                    // Try to steal a task from other workers
                    let mut found = false;
                    for stealer in &stealers {
                        if let Some(task) = stealer.steal_batch_and_pop(&worker) {
                            // Execute the task
                            Python::with_gil(|py| {
                                let _ = task.execute(py);
                            });
                            found = true;
                            break;
                        }
                    }
                    
                    if found {
                        continue;
                    }
                    
                    // No tasks found, sleep for a short time
                    thread::sleep(Duration::from_millis(1));
                }
            });
            
            worker_threads.push(worker_thread);
        }
    }
    
    /// Stop the work stealer
    pub fn stop(&self) {
        // Set the stop flag
        *self.stop.write().unwrap() = true;
        
        // Wait for worker threads to finish
        let mut worker_threads = self.worker_threads.write().unwrap();
        while let Some(worker_thread) = worker_threads.pop() {
            let _ = worker_thread.join();
        }
    }
    
    /// Submit a task
    pub fn submit(&self, task: Task) {
        // Push the task to the global queue
        self.global_queue.push(task);
    }
    
    /// Submit a task with a Python function
    pub fn submit_py(&self, id: &str, func: PyObject, args: PyObject, kwargs: PyObject) {
        // Create the task
        let task = Task::new(id, func, args, kwargs);
        
        // Submit the task
        self.submit(task);
    }
}

/// Python wrapper for WorkStealer
#[pyclass(name = "WorkStealer")]
pub struct PyWorkStealer {
    /// The inner stealer
    pub stealer: Arc<WorkStealer>,
}

#[pymethods]
impl PyWorkStealer {
    /// Create a new work stealer
    #[new]
    fn new(name: &str, num_workers: Option<usize>) -> Self {
        let num_workers = num_workers.unwrap_or_else(num_cpus::get);
        
        Self {
            stealer: Arc::new(WorkStealer::new(name, num_workers)),
        }
    }
    
    /// Start the work stealer
    fn start(&self) {
        self.stealer.start();
    }
    
    /// Stop the work stealer
    fn stop(&self) {
        self.stealer.stop();
    }
    
    /// Submit a task
    fn submit(&self, id: &str, func: PyObject, args: Option<&PyTuple>, kwargs: Option<&PyDict>, py: Python) -> PyResult<()> {
        let args = args.map_or_else(|| PyTuple::empty(py).to_object(py), |a| a.to_object(py));
        let kwargs = kwargs.map_or_else(|| PyDict::new(py).to_object(py), |k| k.to_object(py));
        
        self.stealer.submit_py(id, func, args, kwargs);
        
        Ok(())
    }
    
    /// Get the name of the stealer
    #[getter]
    fn name(&self) -> String {
        self.stealer.name.clone()
    }
    
    /// Get the number of workers
    #[getter]
    fn num_workers(&self) -> usize {
        self.stealer.num_workers
    }
    
    /// String representation
    fn __str__(&self) -> String {
        format!("WorkStealer({}, {} workers)", self.stealer.name, self.stealer.num_workers)
    }
    
    /// Representation
    fn __repr__(&self) -> String {
        format!("WorkStealer(name='{}', num_workers={})", self.stealer.name, self.stealer.num_workers)
    }
}
