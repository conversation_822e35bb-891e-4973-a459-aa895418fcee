/*
 * Thread Pool
 *
 * This module provides a thread pool for parallel computing.
 */

use pyo3::prelude::*;
use pyo3::types::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>y<PERSON><PERSON><PERSON>};
use std::sync::{<PERSON>, RwLock, <PERSON>tex};
use std::collections::{HashMap, BinaryHeap, VecDeque};
use std::cmp::{Ordering, Reverse};
use std::time::{Duration, Instant};
use std::thread;
use crossbeam::channel::{bounded, unbounded, Sender, Receiver};
use rayon::prelude::*;

/// A thread pool
pub struct ThreadPool {
    /// The name of the pool
    pub name: String,
    /// The number of threads
    pub num_threads: usize,
    /// The workers
    pub workers: Vec<Worker>,
    /// The task sender
    pub task_sender: Sender<Message>,
    /// The task receivers
    pub task_receivers: Vec<Receiver<Message>>,
    /// The stop flag
    pub stop: RwLock<bool>,
}

/// A worker
pub struct Worker {
    /// The ID of the worker
    pub id: usize,
    /// The thread handle
    pub thread: Option<thread::Jo<PERSON><PERSON><PERSON><PERSON><()>>,
}

/// A message
pub enum Message {
    /// A task to execute
    Task(Box<dyn FnOnce() + Send + 'static>),
    /// A signal to terminate
    Terminate,
}

impl ThreadPool {
    /// Create a new thread pool
    pub fn new(name: &str, num_threads: usize) -> Self {
        // Create channels
        let (task_sender, task_receiver) = unbounded();
        let mut task_receivers = Vec::with_capacity(num_threads);
        
        for _ in 0..num_threads {
            task_receivers.push(task_receiver.clone());
        }
        
        // Create workers
        let mut workers = Vec::with_capacity(num_threads);
        
        for id in 0..num_threads {
            workers.push(Worker {
                id,
                thread: None,
            });
        }
        
        Self {
            name: name.to_string(),
            num_threads,
            workers,
            task_sender,
            task_receivers,
            stop: RwLock::new(false),
        }
    }
    
    /// Start the thread pool
    pub fn start(&mut self) {
        for id in 0..self.num_threads {
            let task_receiver = self.task_receivers[id].clone();
            let stop = self.stop.clone();
            
            let thread = thread::spawn(move || {
                loop {
                    // Check if we should stop
                    if *stop.read().unwrap() {
                        break;
                    }
                    
                    // Wait for a task
                    match task_receiver.recv_timeout(Duration::from_millis(100)) {
                        Ok(Message::Task(task)) => {
                            // Execute the task
                            task();
                        }
                        Ok(Message::Terminate) => {
                            // Terminate the worker
                            break;
                        }
                        Err(_) => {
                            // Timeout, check if we should stop
                            if *stop.read().unwrap() {
                                break;
                            }
                        }
                    }
                }
            });
            
            self.workers[id].thread = Some(thread);
        }
    }
    
    /// Stop the thread pool
    pub fn stop(&mut self) {
        // Set the stop flag
        *self.stop.write().unwrap() = true;
        
        // Send terminate messages
        for _ in 0..self.num_threads {
            let _ = self.task_sender.send(Message::Terminate);
        }
        
        // Wait for workers to finish
        for worker in &mut self.workers {
            if let Some(thread) = worker.thread.take() {
                let _ = thread.join();
            }
        }
    }
    
    /// Execute a task
    pub fn execute<F>(&self, task: F) -> PyResult<()>
    where
        F: FnOnce() + Send + 'static,
    {
        // Send the task
        self.task_sender.send(Message::Task(Box::new(task))).map_err(|e| {
            PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Failed to send task: {}", e))
        })?;
        
        Ok(())
    }
    
    /// Execute a task with a Python function
    pub fn execute_py(&self, func: PyObject, args: PyObject, kwargs: PyObject) -> PyResult<()> {
        // Send the task
        self.task_sender.send(Message::Task(Box::new(move || {
            Python::with_gil(|py| {
                let _ = func.call(py, &args, Some(&kwargs));
            });
        }))).map_err(|e| {
            PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Failed to send task: {}", e))
        })?;
        
        Ok(())
    }
}

/// Python wrapper for ThreadPool
#[pyclass(name = "ThreadPool")]
pub struct PyThreadPool {
    /// The inner pool
    pub pool: Arc<RwLock<ThreadPool>>,
}

#[pymethods]
impl PyThreadPool {
    /// Create a new thread pool
    #[new]
    fn new(name: &str, num_threads: Option<usize>) -> Self {
        let num_threads = num_threads.unwrap_or_else(num_cpus::get);
        
        Self {
            pool: Arc::new(RwLock::new(ThreadPool::new(name, num_threads))),
        }
    }
    
    /// Start the thread pool
    fn start(&self) {
        let mut pool = self.pool.write().unwrap();
        pool.start();
    }
    
    /// Stop the thread pool
    fn stop(&self) {
        let mut pool = self.pool.write().unwrap();
        pool.stop();
    }
    
    /// Execute a task
    fn execute(&self, func: PyObject, args: Option<&PyTuple>, kwargs: Option<&PyDict>, py: Python) -> PyResult<()> {
        let args = args.map_or_else(|| PyTuple::empty(py).to_object(py), |a| a.to_object(py));
        let kwargs = kwargs.map_or_else(|| PyDict::new(py).to_object(py), |k| k.to_object(py));
        
        let pool = self.pool.read().unwrap();
        pool.execute_py(func, args, kwargs)
    }
    
    /// Get the name of the pool
    #[getter]
    fn name(&self) -> String {
        let pool = self.pool.read().unwrap();
        pool.name.clone()
    }
    
    /// Get the number of threads
    #[getter]
    fn num_threads(&self) -> usize {
        let pool = self.pool.read().unwrap();
        pool.num_threads
    }
    
    /// String representation
    fn __str__(&self) -> String {
        let pool = self.pool.read().unwrap();
        format!("ThreadPool({}, {} threads)", pool.name, pool.num_threads)
    }
    
    /// Representation
    fn __repr__(&self) -> String {
        let pool = self.pool.read().unwrap();
        format!("ThreadPool(name='{}', num_threads={})", pool.name, pool.num_threads)
    }
}
