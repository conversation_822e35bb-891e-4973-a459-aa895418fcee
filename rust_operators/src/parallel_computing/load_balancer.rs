/*
 * Load Balancer
 *
 * This module provides a load balancer for parallel computing.
 */

use pyo3::prelude::*;
use pyo3::types::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>y<PERSON><PERSON>le};
use std::sync::{<PERSON>, RwLock, Mutex};
use std::collections::{HashMap, BinaryHeap, VecDeque};
use std::cmp::{Ordering, Reverse};
use std::time::{Duration, Instant};
use std::thread;
use crossbeam::channel::{bounded, unbounded, Sender, Receiver};
use rayon::prelude::*;

use super::task_scheduler::{Task, TaskPriority, TaskStatus, PyTask};

/// Worker statistics
#[derive(Debug, <PERSON><PERSON>)]
pub struct WorkerStats {
    /// The ID of the worker
    pub id: usize,
    /// The name of the worker
    pub name: String,
    /// The number of tasks processed
    pub tasks_processed: usize,
    /// The number of tasks succeeded
    pub tasks_succeeded: usize,
    /// The number of tasks failed
    pub tasks_failed: usize,
    /// The total processing time
    pub total_processing_time: Duration,
    /// The average processing time
    pub average_processing_time: Duration,
    /// The current load
    pub current_load: f64,
    /// The last update time
    pub last_update: Instant,
}

impl WorkerStats {
    /// Create new worker statistics
    pub fn new(id: usize, name: &str) -> Self {
        Self {
            id,
            name: name.to_string(),
            tasks_processed: 0,
            tasks_succeeded: 0,
            tasks_failed: 0,
            total_processing_time: Duration::from_secs(0),
            average_processing_time: Duration::from_secs(0),
            current_load: 0.0,
            last_update: Instant::now(),
        }
    }
    
    /// Update the statistics with a new task
    pub fn update_with_task(&mut self, task: &Task, success: bool) {
        self.tasks_processed += 1;
        
        if success {
            self.tasks_succeeded += 1;
        } else {
            self.tasks_failed += 1;
        }
        
        if let Some(execution_time) = task.get_execution_time() {
            self.total_processing_time += execution_time;
            self.average_processing_time = self.total_processing_time / self.tasks_processed as u32;
        }
        
        self.last_update = Instant::now();
    }
    
    /// Update the current load
    pub fn update_load(&mut self, load: f64) {
        self.current_load = load;
        self.last_update = Instant::now();
    }
}

/// Load balancing strategy
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum LoadBalancingStrategy {
    /// Round robin
    RoundRobin,
    /// Least connections
    LeastConnections,
    /// Least response time
    LeastResponseTime,
    /// Weighted round robin
    WeightedRoundRobin,
    /// Weighted least connections
    WeightedLeastConnections,
    /// IP hash
    IpHash,
}

impl LoadBalancingStrategy {
    /// Create a load balancing strategy from a string
    pub fn from_str(s: &str) -> Result<Self, String> {
        match s.to_lowercase().as_str() {
            "round_robin" => Ok(LoadBalancingStrategy::RoundRobin),
            "least_connections" => Ok(LoadBalancingStrategy::LeastConnections),
            "least_response_time" => Ok(LoadBalancingStrategy::LeastResponseTime),
            "weighted_round_robin" => Ok(LoadBalancingStrategy::WeightedRoundRobin),
            "weighted_least_connections" => Ok(LoadBalancingStrategy::WeightedLeastConnections),
            "ip_hash" => Ok(LoadBalancingStrategy::IpHash),
            _ => Err(format!("Invalid load balancing strategy: {}", s)),
        }
    }
    
    /// Convert to a string
    pub fn to_str(&self) -> &'static str {
        match self {
            LoadBalancingStrategy::RoundRobin => "round_robin",
            LoadBalancingStrategy::LeastConnections => "least_connections",
            LoadBalancingStrategy::LeastResponseTime => "least_response_time",
            LoadBalancingStrategy::WeightedRoundRobin => "weighted_round_robin",
            LoadBalancingStrategy::WeightedLeastConnections => "weighted_least_connections",
            LoadBalancingStrategy::IpHash => "ip_hash",
        }
    }
}

/// A load balancer
pub struct LoadBalancer {
    /// The name of the load balancer
    pub name: String,
    /// The workers
    pub workers: RwLock<HashMap<usize, WorkerStats>>,
    /// The worker weights
    pub worker_weights: RwLock<HashMap<usize, f64>>,
    /// The current worker index for round robin
    pub current_worker_index: RwLock<usize>,
    /// The load balancing strategy
    pub strategy: RwLock<LoadBalancingStrategy>,
    /// The task distribution
    pub task_distribution: RwLock<HashMap<String, usize>>,
}

impl LoadBalancer {
    /// Create a new load balancer
    pub fn new(name: &str, strategy: LoadBalancingStrategy) -> Self {
        Self {
            name: name.to_string(),
            workers: RwLock::new(HashMap::new()),
            worker_weights: RwLock::new(HashMap::new()),
            current_worker_index: RwLock::new(0),
            strategy: RwLock::new(strategy),
            task_distribution: RwLock::new(HashMap::new()),
        }
    }
    
    /// Register a worker
    pub fn register_worker(&self, id: usize, name: &str, weight: Option<f64>) -> usize {
        let mut workers = self.workers.write().unwrap();
        let mut worker_weights = self.worker_weights.write().unwrap();
        
        workers.insert(id, WorkerStats::new(id, name));
        worker_weights.insert(id, weight.unwrap_or(1.0));
        
        id
    }
    
    /// Unregister a worker
    pub fn unregister_worker(&self, id: usize) -> bool {
        let mut workers = self.workers.write().unwrap();
        let mut worker_weights = self.worker_weights.write().unwrap();
        
        let removed_worker = workers.remove(&id).is_some();
        let removed_weight = worker_weights.remove(&id).is_some();
        
        removed_worker || removed_weight
    }
    
    /// Get a worker by ID
    pub fn get_worker(&self, id: usize) -> Option<WorkerStats> {
        self.workers.read().unwrap().get(&id).cloned()
    }
    
    /// Get all workers
    pub fn get_all_workers(&self) -> Vec<WorkerStats> {
        self.workers.read().unwrap().values().cloned().collect()
    }
    
    /// Update worker statistics
    pub fn update_worker_stats(&self, id: usize, task: &Task, success: bool) {
        let mut workers = self.workers.write().unwrap();
        
        if let Some(worker) = workers.get_mut(&id) {
            worker.update_with_task(task, success);
        }
    }
    
    /// Update worker load
    pub fn update_worker_load(&self, id: usize, load: f64) {
        let mut workers = self.workers.write().unwrap();
        
        if let Some(worker) = workers.get_mut(&id) {
            worker.update_load(load);
        }
    }
    
    /// Set worker weight
    pub fn set_worker_weight(&self, id: usize, weight: f64) {
        let mut worker_weights = self.worker_weights.write().unwrap();
        
        worker_weights.insert(id, weight);
    }
    
    /// Get worker weight
    pub fn get_worker_weight(&self, id: usize) -> Option<f64> {
        self.worker_weights.read().unwrap().get(&id).cloned()
    }
    
    /// Set the load balancing strategy
    pub fn set_strategy(&self, strategy: LoadBalancingStrategy) {
        *self.strategy.write().unwrap() = strategy;
    }
    
    /// Get the load balancing strategy
    pub fn get_strategy(&self) -> LoadBalancingStrategy {
        *self.strategy.read().unwrap()
    }
    
    /// Select a worker for a task
    pub fn select_worker(&self, task: &Task) -> Option<usize> {
        let workers = self.workers.read().unwrap();
        let worker_weights = self.worker_weights.read().unwrap();
        
        if workers.is_empty() {
            return None;
        }
        
        match *self.strategy.read().unwrap() {
            LoadBalancingStrategy::RoundRobin => {
                let mut current_index = self.current_worker_index.write().unwrap();
                let worker_ids: Vec<usize> = workers.keys().cloned().collect();
                
                if worker_ids.is_empty() {
                    return None;
                }
                
                let worker_id = worker_ids[*current_index % worker_ids.len()];
                *current_index = (*current_index + 1) % worker_ids.len();
                
                Some(worker_id)
            }
            LoadBalancingStrategy::LeastConnections => {
                workers.iter()
                    .min_by_key(|(_, stats)| stats.current_load as usize)
                    .map(|(id, _)| *id)
            }
            LoadBalancingStrategy::LeastResponseTime => {
                workers.iter()
                    .min_by_key(|(_, stats)| stats.average_processing_time)
                    .map(|(id, _)| *id)
            }
            LoadBalancingStrategy::WeightedRoundRobin => {
                // This is a simplified implementation
                // In a real application, we would use a more sophisticated algorithm
                
                let mut current_index = self.current_worker_index.write().unwrap();
                let worker_ids: Vec<usize> = workers.keys().cloned().collect();
                
                if worker_ids.is_empty() {
                    return None;
                }
                
                let worker_id = worker_ids[*current_index % worker_ids.len()];
                *current_index = (*current_index + 1) % worker_ids.len();
                
                Some(worker_id)
            }
            LoadBalancingStrategy::WeightedLeastConnections => {
                workers.iter()
                    .min_by_key(|(id, stats)| {
                        let weight = worker_weights.get(id).unwrap_or(&1.0);
                        (stats.current_load / weight) as usize
                    })
                    .map(|(id, _)| *id)
            }
            LoadBalancingStrategy::IpHash => {
                // This is a simplified implementation
                // In a real application, we would use the client IP
                
                let task_id = task.id.clone();
                let mut task_distribution = self.task_distribution.write().unwrap();
                
                if let Some(worker_id) = task_distribution.get(&task_id) {
                    return Some(*worker_id);
                }
                
                let worker_ids: Vec<usize> = workers.keys().cloned().collect();
                
                if worker_ids.is_empty() {
                    return None;
                }
                
                let hash = task_id.bytes().fold(0, |acc, b| acc.wrapping_add(b as usize));
                let worker_id = worker_ids[hash % worker_ids.len()];
                
                task_distribution.insert(task_id, worker_id);
                
                Some(worker_id)
            }
        }
    }
    
    /// Get the task distribution
    pub fn get_task_distribution(&self) -> HashMap<String, usize> {
        self.task_distribution.read().unwrap().clone()
    }
    
    /// Clear the task distribution
    pub fn clear_task_distribution(&self) {
        self.task_distribution.write().unwrap().clear();
    }
}

/// Python wrapper for WorkerStats
#[pyclass(name = "WorkerStats")]
#[derive(Clone)]
pub struct PyWorkerStats {
    /// The inner stats
    pub stats: WorkerStats,
}

#[pymethods]
impl PyWorkerStats {
    /// Get the ID of the worker
    #[getter]
    fn id(&self) -> usize {
        self.stats.id
    }
    
    /// Get the name of the worker
    #[getter]
    fn name(&self) -> String {
        self.stats.name.clone()
    }
    
    /// Get the number of tasks processed
    #[getter]
    fn tasks_processed(&self) -> usize {
        self.stats.tasks_processed
    }
    
    /// Get the number of tasks succeeded
    #[getter]
    fn tasks_succeeded(&self) -> usize {
        self.stats.tasks_succeeded
    }
    
    /// Get the number of tasks failed
    #[getter]
    fn tasks_failed(&self) -> usize {
        self.stats.tasks_failed
    }
    
    /// Get the total processing time
    #[getter]
    fn total_processing_time(&self) -> f64 {
        self.stats.total_processing_time.as_secs_f64()
    }
    
    /// Get the average processing time
    #[getter]
    fn average_processing_time(&self) -> f64 {
        self.stats.average_processing_time.as_secs_f64()
    }
    
    /// Get the current load
    #[getter]
    fn current_load(&self) -> f64 {
        self.stats.current_load
    }
    
    /// Get the last update time
    #[getter]
    fn last_update(&self) -> f64 {
        self.stats.last_update.elapsed().as_secs_f64()
    }
    
    /// String representation
    fn __str__(&self) -> String {
        format!("WorkerStats({}, {}, {} tasks)", self.stats.id, self.stats.name, self.stats.tasks_processed)
    }
    
    /// Representation
    fn __repr__(&self) -> String {
        format!("WorkerStats(id={}, name='{}', tasks_processed={})", self.stats.id, self.stats.name, self.stats.tasks_processed)
    }
}

/// Python wrapper for LoadBalancingStrategy
#[pyclass(name = "LoadBalancingStrategy")]
#[derive(Clone, Copy)]
pub struct PyLoadBalancingStrategy {
    /// The inner strategy
    pub strategy: LoadBalancingStrategy,
}

#[pymethods]
impl PyLoadBalancingStrategy {
    /// Create a new load balancing strategy
    #[new]
    fn new(strategy: &str) -> PyResult<Self> {
        LoadBalancingStrategy::from_str(strategy)
            .map(|s| Self { strategy: s })
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(e))
    }
    
    /// Convert to a string
    fn __str__(&self) -> &'static str {
        self.strategy.to_str()
    }
    
    /// Representation
    fn __repr__(&self) -> String {
        format!("LoadBalancingStrategy.{}", self.strategy.to_str())
    }
    
    /// Round robin strategy
    #[staticmethod]
    fn round_robin() -> Self {
        Self { strategy: LoadBalancingStrategy::RoundRobin }
    }
    
    /// Least connections strategy
    #[staticmethod]
    fn least_connections() -> Self {
        Self { strategy: LoadBalancingStrategy::LeastConnections }
    }
    
    /// Least response time strategy
    #[staticmethod]
    fn least_response_time() -> Self {
        Self { strategy: LoadBalancingStrategy::LeastResponseTime }
    }
    
    /// Weighted round robin strategy
    #[staticmethod]
    fn weighted_round_robin() -> Self {
        Self { strategy: LoadBalancingStrategy::WeightedRoundRobin }
    }
    
    /// Weighted least connections strategy
    #[staticmethod]
    fn weighted_least_connections() -> Self {
        Self { strategy: LoadBalancingStrategy::WeightedLeastConnections }
    }
    
    /// IP hash strategy
    #[staticmethod]
    fn ip_hash() -> Self {
        Self { strategy: LoadBalancingStrategy::IpHash }
    }
}

/// Python wrapper for LoadBalancer
#[pyclass(name = "LoadBalancer")]
pub struct PyLoadBalancer {
    /// The inner load balancer
    pub balancer: Arc<LoadBalancer>,
}

#[pymethods]
impl PyLoadBalancer {
    /// Create a new load balancer
    #[new]
    fn new(name: &str, strategy: Option<&PyLoadBalancingStrategy>) -> Self {
        let strategy = strategy.map_or(LoadBalancingStrategy::RoundRobin, |s| s.strategy);
        
        Self {
            balancer: Arc::new(LoadBalancer::new(name, strategy)),
        }
    }
    
    /// Register a worker
    fn register_worker(&self, id: usize, name: &str, weight: Option<f64>) -> usize {
        self.balancer.register_worker(id, name, weight)
    }
    
    /// Unregister a worker
    fn unregister_worker(&self, id: usize) -> bool {
        self.balancer.unregister_worker(id)
    }
    
    /// Get a worker by ID
    fn get_worker(&self, id: usize) -> Option<PyWorkerStats> {
        self.balancer.get_worker(id).map(|stats| PyWorkerStats { stats })
    }
    
    /// Get all workers
    fn get_all_workers(&self) -> Vec<PyWorkerStats> {
        self.balancer.get_all_workers().into_iter().map(|stats| PyWorkerStats { stats }).collect()
    }
    
    /// Update worker statistics
    fn update_worker_stats(&self, id: usize, task: &PyTask, success: bool) {
        self.balancer.update_worker_stats(id, &task.task, success);
    }
    
    /// Update worker load
    fn update_worker_load(&self, id: usize, load: f64) {
        self.balancer.update_worker_load(id, load);
    }
    
    /// Set worker weight
    fn set_worker_weight(&self, id: usize, weight: f64) {
        self.balancer.set_worker_weight(id, weight);
    }
    
    /// Get worker weight
    fn get_worker_weight(&self, id: usize) -> Option<f64> {
        self.balancer.get_worker_weight(id)
    }
    
    /// Set the load balancing strategy
    fn set_strategy(&self, strategy: &PyLoadBalancingStrategy) {
        self.balancer.set_strategy(strategy.strategy);
    }
    
    /// Get the load balancing strategy
    fn get_strategy(&self) -> PyLoadBalancingStrategy {
        PyLoadBalancingStrategy { strategy: self.balancer.get_strategy() }
    }
    
    /// Select a worker for a task
    fn select_worker(&self, task: &PyTask) -> Option<usize> {
        self.balancer.select_worker(&task.task)
    }
    
    /// Get the task distribution
    fn get_task_distribution(&self) -> HashMap<String, usize> {
        self.balancer.get_task_distribution()
    }
    
    /// Clear the task distribution
    fn clear_task_distribution(&self) {
        self.balancer.clear_task_distribution();
    }
    
    /// Get the name of the load balancer
    #[getter]
    fn name(&self) -> String {
        self.balancer.name.clone()
    }
    
    /// Get the number of workers
    #[getter]
    fn num_workers(&self) -> usize {
        self.balancer.workers.read().unwrap().len()
    }
    
    /// String representation
    fn __str__(&self) -> String {
        format!("LoadBalancer({}, {} workers)", self.balancer.name, self.balancer.workers.read().unwrap().len())
    }
    
    /// Representation
    fn __repr__(&self) -> String {
        format!("LoadBalancer(name='{}', strategy={})", self.balancer.name, self.balancer.get_strategy().to_str())
    }
}
