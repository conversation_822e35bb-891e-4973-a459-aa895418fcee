/*
 * Task Scheduler
 *
 * This module provides a task scheduler for parallel computing.
 */

use pyo3::prelude::*;
use pyo3::types::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>y<PERSON><PERSON>le};
use std::sync::{<PERSON>, <PERSON>wLock, Mutex};
use std::collections::{HashMap, BinaryHeap, VecDeque};
use std::cmp::{Ordering, Reverse};
use std::time::{Duration, Instant};
use std::thread;
use crossbeam::channel::{bounded, unbounded, Sender, Receiver};
use rayon::prelude::*;

/// Task priority
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum TaskPriority {
    /// Low priority
    Low = 0,
    /// Normal priority
    Normal = 1,
    /// High priority
    High = 2,
    /// Critical priority
    Critical = 3,
}

impl TaskPriority {
    /// Create a task priority from a string
    pub fn from_str(s: &str) -> Result<Self, String> {
        match s.to_lowercase().as_str() {
            "low" => Ok(TaskPriority::Low),
            "normal" => Ok(TaskPriority::Normal),
            "high" => Ok(TaskPriority::High),
            "critical" => Ok(TaskPriority::Critical),
            _ => Err(format!("Invalid task priority: {}", s)),
        }
    }
    
    /// Convert to a string
    pub fn to_str(&self) -> &'static str {
        match self {
            TaskPriority::Low => "low",
            TaskPriority::Normal => "normal",
            TaskPriority::High => "high",
            TaskPriority::Critical => "critical",
        }
    }
}

/// Task status
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TaskStatus {
    /// Task is pending
    Pending,
    /// Task is running
    Running,
    /// Task is completed
    Completed,
    /// Task failed
    Failed,
    /// Task is cancelled
    Cancelled,
}

impl TaskStatus {
    /// Create a task status from a string
    pub fn from_str(s: &str) -> Result<Self, String> {
        match s.to_lowercase().as_str() {
            "pending" => Ok(TaskStatus::Pending),
            "running" => Ok(TaskStatus::Running),
            "completed" => Ok(TaskStatus::Completed),
            "failed" => Ok(TaskStatus::Failed),
            "cancelled" => Ok(TaskStatus::Cancelled),
            _ => Err(format!("Invalid task status: {}", s)),
        }
    }
    
    /// Convert to a string
    pub fn to_str(&self) -> &'static str {
        match self {
            TaskStatus::Pending => "pending",
            TaskStatus::Running => "running",
            TaskStatus::Completed => "completed",
            TaskStatus::Failed => "failed",
            TaskStatus::Cancelled => "cancelled",
        }
    }
}

/// A task
#[derive(Clone)]
pub struct Task {
    /// The ID of the task
    pub id: String,
    /// The name of the task
    pub name: String,
    /// The function to execute
    pub func: PyObject,
    /// The arguments to pass to the function
    pub args: PyObject,
    /// The keyword arguments to pass to the function
    pub kwargs: PyObject,
    /// The priority of the task
    pub priority: TaskPriority,
    /// The status of the task
    pub status: RwLock<TaskStatus>,
    /// The result of the task
    pub result: RwLock<Option<PyObject>>,
    /// The error of the task
    pub error: RwLock<Option<PyObject>>,
    /// The creation time of the task
    pub created_at: Instant,
    /// The start time of the task
    pub started_at: RwLock<Option<Instant>>,
    /// The completion time of the task
    pub completed_at: RwLock<Option<Instant>>,
    /// The dependencies of the task
    pub dependencies: Vec<String>,
    /// The callback function to call when the task is completed
    pub callback: Option<PyObject>,
}

impl Task {
    /// Create a new task
    pub fn new(
        id: &str,
        name: &str,
        func: PyObject,
        args: PyObject,
        kwargs: PyObject,
        priority: TaskPriority,
        dependencies: Vec<String>,
        callback: Option<PyObject>,
    ) -> Self {
        Self {
            id: id.to_string(),
            name: name.to_string(),
            func,
            args,
            kwargs,
            priority,
            status: RwLock::new(TaskStatus::Pending),
            result: RwLock::new(None),
            error: RwLock::new(None),
            created_at: Instant::now(),
            started_at: RwLock::new(None),
            completed_at: RwLock::new(None),
            dependencies,
            callback,
        }
    }
    
    /// Execute the task
    pub fn execute(&self, py: Python) -> PyResult<PyObject> {
        // Update the status
        *self.status.write().unwrap() = TaskStatus::Running;
        *self.started_at.write().unwrap() = Some(Instant::now());
        
        // Execute the function
        let result = self.func.call(py, &self.args, Some(&self.kwargs));
        
        // Update the status
        match result {
            Ok(result) => {
                *self.status.write().unwrap() = TaskStatus::Completed;
                *self.result.write().unwrap() = Some(result.to_object(py));
                *self.completed_at.write().unwrap() = Some(Instant::now());
                
                // Call the callback if provided
                if let Some(callback) = &self.callback {
                    let _ = callback.call1(py, (result.to_object(py),));
                }
                
                Ok(result.to_object(py))
            }
            Err(err) => {
                *self.status.write().unwrap() = TaskStatus::Failed;
                *self.error.write().unwrap() = Some(err.to_object(py));
                *self.completed_at.write().unwrap() = Some(Instant::now());
                
                Err(err)
            }
        }
    }
    
    /// Get the status of the task
    pub fn get_status(&self) -> TaskStatus {
        *self.status.read().unwrap()
    }
    
    /// Get the result of the task
    pub fn get_result(&self) -> Option<PyObject> {
        self.result.read().unwrap().clone()
    }
    
    /// Get the error of the task
    pub fn get_error(&self) -> Option<PyObject> {
        self.error.read().unwrap().clone()
    }
    
    /// Get the execution time of the task
    pub fn get_execution_time(&self) -> Option<Duration> {
        let started_at = *self.started_at.read().unwrap();
        let completed_at = *self.completed_at.read().unwrap();
        
        match (started_at, completed_at) {
            (Some(start), Some(end)) => Some(end.duration_since(start)),
            _ => None,
        }
    }
    
    /// Cancel the task
    pub fn cancel(&self) {
        *self.status.write().unwrap() = TaskStatus::Cancelled;
    }
    
    /// Check if the task is completed
    pub fn is_completed(&self) -> bool {
        self.get_status() == TaskStatus::Completed
    }
    
    /// Check if the task is failed
    pub fn is_failed(&self) -> bool {
        self.get_status() == TaskStatus::Failed
    }
    
    /// Check if the task is cancelled
    pub fn is_cancelled(&self) -> bool {
        self.get_status() == TaskStatus::Cancelled
    }
    
    /// Check if the task is pending
    pub fn is_pending(&self) -> bool {
        self.get_status() == TaskStatus::Pending
    }
    
    /// Check if the task is running
    pub fn is_running(&self) -> bool {
        self.get_status() == TaskStatus::Running
    }
}

impl PartialEq for Task {
    fn eq(&self, other: &Self) -> bool {
        self.id == other.id
    }
}

impl Eq for Task {}

impl PartialOrd for Task {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some(self.cmp(other))
    }
}

impl Ord for Task {
    fn cmp(&self, other: &Self) -> Ordering {
        // First compare by priority
        let priority_cmp = self.priority.cmp(&other.priority);
        if priority_cmp != Ordering::Equal {
            return priority_cmp;
        }
        
        // Then compare by creation time (older tasks first)
        self.created_at.cmp(&other.created_at)
    }
}

/// A task scheduler
pub struct TaskScheduler {
    /// The name of the scheduler
    pub name: String,
    /// The tasks
    pub tasks: RwLock<HashMap<String, Arc<Task>>>,
    /// The task queue
    pub queue: Mutex<BinaryHeap<Arc<Task>>>,
    /// The number of worker threads
    pub num_workers: usize,
    /// The worker threads
    pub workers: RwLock<Vec<thread::JoinHandle<()>>>,
    /// The task sender
    pub task_sender: Sender<Arc<Task>>,
    /// The task receiver
    pub task_receiver: Receiver<Arc<Task>>,
    /// The stop flag
    pub stop: RwLock<bool>,
}

impl TaskScheduler {
    /// Create a new task scheduler
    pub fn new(name: &str, num_workers: usize) -> Self {
        let (task_sender, task_receiver) = unbounded();
        
        Self {
            name: name.to_string(),
            tasks: RwLock::new(HashMap::new()),
            queue: Mutex::new(BinaryHeap::new()),
            num_workers,
            workers: RwLock::new(Vec::new()),
            task_sender,
            task_receiver,
            stop: RwLock::new(false),
        }
    }
    
    /// Start the scheduler
    pub fn start(&self) {
        // Create worker threads
        let mut workers = self.workers.write().unwrap();
        
        for _ in 0..self.num_workers {
            let task_receiver = self.task_receiver.clone();
            let stop = self.stop.clone();
            
            let worker = thread::spawn(move || {
                while !*stop.read().unwrap() {
                    // Wait for a task
                    match task_receiver.recv_timeout(Duration::from_millis(100)) {
                        Ok(task) => {
                            // Execute the task
                            Python::with_gil(|py| {
                                let _ = task.execute(py);
                            });
                        }
                        Err(_) => {
                            // Timeout, check if we should stop
                            if *stop.read().unwrap() {
                                break;
                            }
                        }
                    }
                }
            });
            
            workers.push(worker);
        }
    }
    
    /// Stop the scheduler
    pub fn stop(&self) {
        // Set the stop flag
        *self.stop.write().unwrap() = true;
        
        // Wait for worker threads to finish
        let mut workers = self.workers.write().unwrap();
        while let Some(worker) = workers.pop() {
            let _ = worker.join();
        }
    }
    
    /// Submit a task
    pub fn submit(&self, task: Arc<Task>) -> PyResult<()> {
        // Add the task to the tasks map
        self.tasks.write().unwrap().insert(task.id.clone(), task.clone());
        
        // Add the task to the queue
        self.queue.lock().unwrap().push(task.clone());
        
        // Send the task to a worker
        self.task_sender.send(task).map_err(|e| {
            PyErr::new::<pyo3::exceptions::PyRuntimeError, _>(format!("Failed to send task: {}", e))
        })?;
        
        Ok(())
    }
    
    /// Get a task by ID
    pub fn get_task(&self, id: &str) -> Option<Arc<Task>> {
        self.tasks.read().unwrap().get(id).cloned()
    }
    
    /// Get all tasks
    pub fn get_all_tasks(&self) -> Vec<Arc<Task>> {
        self.tasks.read().unwrap().values().cloned().collect()
    }
    
    /// Get pending tasks
    pub fn get_pending_tasks(&self) -> Vec<Arc<Task>> {
        self.tasks.read().unwrap().values()
            .filter(|task| task.is_pending())
            .cloned()
            .collect()
    }
    
    /// Get running tasks
    pub fn get_running_tasks(&self) -> Vec<Arc<Task>> {
        self.tasks.read().unwrap().values()
            .filter(|task| task.is_running())
            .cloned()
            .collect()
    }
    
    /// Get completed tasks
    pub fn get_completed_tasks(&self) -> Vec<Arc<Task>> {
        self.tasks.read().unwrap().values()
            .filter(|task| task.is_completed())
            .cloned()
            .collect()
    }
    
    /// Get failed tasks
    pub fn get_failed_tasks(&self) -> Vec<Arc<Task>> {
        self.tasks.read().unwrap().values()
            .filter(|task| task.is_failed())
            .cloned()
            .collect()
    }
    
    /// Get cancelled tasks
    pub fn get_cancelled_tasks(&self) -> Vec<Arc<Task>> {
        self.tasks.read().unwrap().values()
            .filter(|task| task.is_cancelled())
            .cloned()
            .collect()
    }
    
    /// Cancel a task
    pub fn cancel_task(&self, id: &str) -> bool {
        if let Some(task) = self.get_task(id) {
            task.cancel();
            true
        } else {
            false
        }
    }
    
    /// Cancel all tasks
    pub fn cancel_all_tasks(&self) {
        for task in self.get_all_tasks() {
            task.cancel();
        }
    }
    
    /// Wait for a task to complete
    pub fn wait_for_task(&self, id: &str, timeout: Option<Duration>) -> PyResult<Arc<Task>> {
        let start_time = Instant::now();
        
        loop {
            if let Some(task) = self.get_task(id) {
                if task.is_completed() || task.is_failed() || task.is_cancelled() {
                    return Ok(task);
                }
            } else {
                return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(format!("Task not found: {}", id)));
            }
            
            // Check timeout
            if let Some(timeout) = timeout {
                if start_time.elapsed() >= timeout {
                    return Err(PyErr::new::<pyo3::exceptions::PyTimeoutError, _>(format!("Timeout waiting for task: {}", id)));
                }
            }
            
            // Sleep for a short time
            thread::sleep(Duration::from_millis(10));
        }
    }
    
    /// Wait for all tasks to complete
    pub fn wait_for_all_tasks(&self, timeout: Option<Duration>) -> PyResult<()> {
        let start_time = Instant::now();
        
        loop {
            let pending_count = self.get_pending_tasks().len();
            let running_count = self.get_running_tasks().len();
            
            if pending_count == 0 && running_count == 0 {
                return Ok(());
            }
            
            // Check timeout
            if let Some(timeout) = timeout {
                if start_time.elapsed() >= timeout {
                    return Err(PyErr::new::<pyo3::exceptions::PyTimeoutError, _>(
                        format!("Timeout waiting for tasks: {} pending, {} running", pending_count, running_count)
                    ));
                }
            }
            
            // Sleep for a short time
            thread::sleep(Duration::from_millis(10));
        }
    }
}

/// Python wrapper for TaskPriority
#[pyclass(name = "TaskPriority")]
#[derive(Clone, Copy)]
pub struct PyTaskPriority {
    /// The inner priority
    pub priority: TaskPriority,
}

#[pymethods]
impl PyTaskPriority {
    /// Create a new task priority
    #[new]
    fn new(priority: &str) -> PyResult<Self> {
        TaskPriority::from_str(priority)
            .map(|p| Self { priority: p })
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(e))
    }
    
    /// Convert to a string
    fn __str__(&self) -> &'static str {
        self.priority.to_str()
    }
    
    /// Representation
    fn __repr__(&self) -> String {
        format!("TaskPriority.{}", self.priority.to_str())
    }
    
    /// Low priority
    #[staticmethod]
    fn low() -> Self {
        Self { priority: TaskPriority::Low }
    }
    
    /// Normal priority
    #[staticmethod]
    fn normal() -> Self {
        Self { priority: TaskPriority::Normal }
    }
    
    /// High priority
    #[staticmethod]
    fn high() -> Self {
        Self { priority: TaskPriority::High }
    }
    
    /// Critical priority
    #[staticmethod]
    fn critical() -> Self {
        Self { priority: TaskPriority::Critical }
    }
}

/// Python wrapper for TaskStatus
#[pyclass(name = "TaskStatus")]
#[derive(Clone, Copy)]
pub struct PyTaskStatus {
    /// The inner status
    pub status: TaskStatus,
}

#[pymethods]
impl PyTaskStatus {
    /// Create a new task status
    #[new]
    fn new(status: &str) -> PyResult<Self> {
        TaskStatus::from_str(status)
            .map(|s| Self { status: s })
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(e))
    }
    
    /// Convert to a string
    fn __str__(&self) -> &'static str {
        self.status.to_str()
    }
    
    /// Representation
    fn __repr__(&self) -> String {
        format!("TaskStatus.{}", self.status.to_str())
    }
    
    /// Pending status
    #[staticmethod]
    fn pending() -> Self {
        Self { status: TaskStatus::Pending }
    }
    
    /// Running status
    #[staticmethod]
    fn running() -> Self {
        Self { status: TaskStatus::Running }
    }
    
    /// Completed status
    #[staticmethod]
    fn completed() -> Self {
        Self { status: TaskStatus::Completed }
    }
    
    /// Failed status
    #[staticmethod]
    fn failed() -> Self {
        Self { status: TaskStatus::Failed }
    }
    
    /// Cancelled status
    #[staticmethod]
    fn cancelled() -> Self {
        Self { status: TaskStatus::Cancelled }
    }
}

/// Python wrapper for Task
#[pyclass(name = "Task")]
#[derive(Clone)]
pub struct PyTask {
    /// The inner task
    pub task: Arc<Task>,
}

#[pymethods]
impl PyTask {
    /// Create a new task
    #[new]
    fn new(
        id: &str,
        name: &str,
        func: PyObject,
        args: Option<&PyTuple>,
        kwargs: Option<&PyDict>,
        priority: Option<&PyTaskPriority>,
        dependencies: Option<Vec<String>>,
        callback: Option<PyObject>,
        py: Python,
    ) -> Self {
        let args = args.map_or_else(|| PyTuple::empty(py).to_object(py), |a| a.to_object(py));
        let kwargs = kwargs.map_or_else(|| PyDict::new(py).to_object(py), |k| k.to_object(py));
        let priority = priority.map_or(TaskPriority::Normal, |p| p.priority);
        let dependencies = dependencies.unwrap_or_default();
        
        Self {
            task: Arc::new(Task::new(
                id,
                name,
                func,
                args,
                kwargs,
                priority,
                dependencies,
                callback,
            )),
        }
    }
    
    /// Get the ID of the task
    #[getter]
    fn id(&self) -> String {
        self.task.id.clone()
    }
    
    /// Get the name of the task
    #[getter]
    fn name(&self) -> String {
        self.task.name.clone()
    }
    
    /// Get the priority of the task
    #[getter]
    fn priority(&self) -> PyTaskPriority {
        PyTaskPriority { priority: self.task.priority }
    }
    
    /// Get the status of the task
    #[getter]
    fn status(&self) -> PyTaskStatus {
        PyTaskStatus { status: self.task.get_status() }
    }
    
    /// Get the result of the task
    #[getter]
    fn result(&self) -> Option<PyObject> {
        self.task.get_result()
    }
    
    /// Get the error of the task
    #[getter]
    fn error(&self) -> Option<PyObject> {
        self.task.get_error()
    }
    
    /// Get the execution time of the task
    #[getter]
    fn execution_time(&self) -> Option<f64> {
        self.task.get_execution_time().map(|d| d.as_secs_f64())
    }
    
    /// Get the dependencies of the task
    #[getter]
    fn dependencies(&self) -> Vec<String> {
        self.task.dependencies.clone()
    }
    
    /// Cancel the task
    fn cancel(&self) {
        self.task.cancel();
    }
    
    /// Check if the task is completed
    fn is_completed(&self) -> bool {
        self.task.is_completed()
    }
    
    /// Check if the task is failed
    fn is_failed(&self) -> bool {
        self.task.is_failed()
    }
    
    /// Check if the task is cancelled
    fn is_cancelled(&self) -> bool {
        self.task.is_cancelled()
    }
    
    /// Check if the task is pending
    fn is_pending(&self) -> bool {
        self.task.is_pending()
    }
    
    /// Check if the task is running
    fn is_running(&self) -> bool {
        self.task.is_running()
    }
    
    /// String representation
    fn __str__(&self) -> String {
        format!("Task({}, {}, {})", self.task.id, self.task.name, self.task.get_status().to_str())
    }
    
    /// Representation
    fn __repr__(&self) -> String {
        format!("Task(id='{}', name='{}', status={})", self.task.id, self.task.name, self.task.get_status().to_str())
    }
}

/// Python wrapper for TaskScheduler
#[pyclass(name = "TaskScheduler")]
pub struct PyTaskScheduler {
    /// The inner scheduler
    pub scheduler: Arc<TaskScheduler>,
}

#[pymethods]
impl PyTaskScheduler {
    /// Create a new task scheduler
    #[new]
    fn new(name: &str, num_workers: Option<usize>) -> Self {
        let num_workers = num_workers.unwrap_or_else(num_cpus::get);
        
        Self {
            scheduler: Arc::new(TaskScheduler::new(name, num_workers)),
        }
    }
    
    /// Start the scheduler
    fn start(&self) {
        self.scheduler.start();
    }
    
    /// Stop the scheduler
    fn stop(&self) {
        self.scheduler.stop();
    }
    
    /// Submit a task
    fn submit(&self, task: &PyTask) -> PyResult<()> {
        self.scheduler.submit(task.task.clone())
    }
    
    /// Create and submit a task
    fn submit_task(
        &self,
        id: &str,
        name: &str,
        func: PyObject,
        args: Option<&PyTuple>,
        kwargs: Option<&PyDict>,
        priority: Option<&PyTaskPriority>,
        dependencies: Option<Vec<String>>,
        callback: Option<PyObject>,
        py: Python,
    ) -> PyResult<PyTask> {
        let task = PyTask::new(id, name, func, args, kwargs, priority, dependencies, callback, py);
        self.submit(&task)?;
        Ok(task)
    }
    
    /// Get a task by ID
    fn get_task(&self, id: &str) -> Option<PyTask> {
        self.scheduler.get_task(id).map(|task| PyTask { task })
    }
    
    /// Get all tasks
    fn get_all_tasks(&self) -> Vec<PyTask> {
        self.scheduler.get_all_tasks().into_iter().map(|task| PyTask { task }).collect()
    }
    
    /// Get pending tasks
    fn get_pending_tasks(&self) -> Vec<PyTask> {
        self.scheduler.get_pending_tasks().into_iter().map(|task| PyTask { task }).collect()
    }
    
    /// Get running tasks
    fn get_running_tasks(&self) -> Vec<PyTask> {
        self.scheduler.get_running_tasks().into_iter().map(|task| PyTask { task }).collect()
    }
    
    /// Get completed tasks
    fn get_completed_tasks(&self) -> Vec<PyTask> {
        self.scheduler.get_completed_tasks().into_iter().map(|task| PyTask { task }).collect()
    }
    
    /// Get failed tasks
    fn get_failed_tasks(&self) -> Vec<PyTask> {
        self.scheduler.get_failed_tasks().into_iter().map(|task| PyTask { task }).collect()
    }
    
    /// Get cancelled tasks
    fn get_cancelled_tasks(&self) -> Vec<PyTask> {
        self.scheduler.get_cancelled_tasks().into_iter().map(|task| PyTask { task }).collect()
    }
    
    /// Cancel a task
    fn cancel_task(&self, id: &str) -> bool {
        self.scheduler.cancel_task(id)
    }
    
    /// Cancel all tasks
    fn cancel_all_tasks(&self) {
        self.scheduler.cancel_all_tasks();
    }
    
    /// Wait for a task to complete
    fn wait_for_task(&self, id: &str, timeout: Option<f64>) -> PyResult<PyTask> {
        let timeout = timeout.map(|t| Duration::from_secs_f64(t));
        self.scheduler.wait_for_task(id, timeout).map(|task| PyTask { task })
    }
    
    /// Wait for all tasks to complete
    fn wait_for_all_tasks(&self, timeout: Option<f64>) -> PyResult<()> {
        let timeout = timeout.map(|t| Duration::from_secs_f64(t));
        self.scheduler.wait_for_all_tasks(timeout)
    }
    
    /// Get the name of the scheduler
    #[getter]
    fn name(&self) -> String {
        self.scheduler.name.clone()
    }
    
    /// Get the number of worker threads
    #[getter]
    fn num_workers(&self) -> usize {
        self.scheduler.num_workers
    }
    
    /// Get the number of tasks
    #[getter]
    fn num_tasks(&self) -> usize {
        self.scheduler.tasks.read().unwrap().len()
    }
    
    /// Get the number of pending tasks
    #[getter]
    fn num_pending_tasks(&self) -> usize {
        self.scheduler.get_pending_tasks().len()
    }
    
    /// Get the number of running tasks
    #[getter]
    fn num_running_tasks(&self) -> usize {
        self.scheduler.get_running_tasks().len()
    }
    
    /// Get the number of completed tasks
    #[getter]
    fn num_completed_tasks(&self) -> usize {
        self.scheduler.get_completed_tasks().len()
    }
    
    /// Get the number of failed tasks
    #[getter]
    fn num_failed_tasks(&self) -> usize {
        self.scheduler.get_failed_tasks().len()
    }
    
    /// Get the number of cancelled tasks
    #[getter]
    fn num_cancelled_tasks(&self) -> usize {
        self.scheduler.get_cancelled_tasks().len()
    }
    
    /// String representation
    fn __str__(&self) -> String {
        format!("TaskScheduler({}, {} workers)", self.scheduler.name, self.scheduler.num_workers)
    }
    
    /// Representation
    fn __repr__(&self) -> String {
        format!("TaskScheduler(name='{}', num_workers={})", self.scheduler.name, self.scheduler.num_workers)
    }
}
