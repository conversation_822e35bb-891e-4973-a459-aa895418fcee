/*
 * Version Adapter
 *
 * This module provides tools for adapting between different API versions.
 */

use pyo3::prelude::*;
use pyo3::types::{PyD<PERSON>, PyList, PyString, PyTuple};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use super::detector::{VersionDetector, PyVersionDetector};

/// Information about a version
#[derive(Clone)]
pub struct VersionInfo {
    /// The version identifier (e.g., "1.0")
    pub version: String,
    /// Additional information about the version
    pub info: HashMap<String, String>,
}

/// A function that adapts from one version to another
pub type AdapterFunction = Arc<dyn Fn(&PyAny) -> PyResult<PyObject> + Send + Sync>;

/// A version adapter
pub struct VersionAdapter {
    /// The name of the adapter
    pub name: String,
    /// The current version
    pub current_version: String,
    /// The target version
    pub target_version: String,
    /// The version detector
    pub detector: Arc<VersionDetector>,
    /// The adapters for different version pairs
    pub adapters: RwLock<HashMap<(String, String), AdapterFunction>>,
    /// Information about different versions
    pub version_info: RwLock<HashMap<String, VersionInfo>>,
}

impl VersionAdapter {
    /// Create a new version adapter
    pub fn new(current_version: &str, target_version: &str, name: &str) -> Self {
        Self {
            name: name.to_string(),
            current_version: current_version.to_string(),
            target_version: target_version.to_string(),
            detector: Arc::new(VersionDetector::new(&format!("{}_detector", name))),
            adapters: RwLock::new(HashMap::new()),
            version_info: RwLock::new(HashMap::new()),
        }
    }

    /// Register a version pattern
    pub fn register_pattern(&self, version: &str, pattern: &str) -> Result<(), regex::Error> {
        self.detector.register_pattern(version, pattern)
    }

    /// Add information about a version
    pub fn add_version(&self, version: &str, info: HashMap<String, String>) {
        let version_info = VersionInfo {
            version: version.to_string(),
            info,
        };

        let mut versions = self.version_info.write().unwrap();
        versions.insert(version.to_string(), version_info);
    }

    /// Register an adapter function
    pub fn register_adapter(&self, source_version: &str, target_version: &str, adapter: AdapterFunction) {
        let mut adapters = self.adapters.write().unwrap();
        adapters.insert((source_version.to_string(), target_version.to_string()), adapter);
    }

    /// Check if an object can be adapted
    pub fn can_adapt(&self, obj: &PyAny) -> PyResult<bool> {
        let version = self.detector.detect_version(obj)?;

        if let Some(version) = version {
            // Check if we have a direct adapter
            let adapters = self.adapters.read().unwrap();
            if adapters.contains_key(&(version.clone(), self.target_version.clone())) {
                return Ok(true);
            }

            // Check if we can find an adaptation path
            let path = self.get_adaptation_path_internal(&version);
            Ok(!path.is_empty())
        } else {
            // If we can't detect the version, assume it's the current version
            let adapters = self.adapters.read().unwrap();
            Ok(adapters.contains_key(&(self.current_version.clone(), self.target_version.clone())))
        }
    }

    /// Get the adaptation path for an object
    pub fn get_adaptation_path(&self, obj: &PyAny) -> PyResult<Vec<String>> {
        let version = self.detector.detect_version(obj)?;

        if let Some(version) = version {
            Ok(self.get_adaptation_path_internal(&version))
        } else {
            // If we can't detect the version, assume it's the current version
            Ok(self.get_adaptation_path_internal(&self.current_version))
        }
    }

    /// Get the adaptation path between two versions
    fn get_adaptation_path_internal(&self, source_version: &str) -> Vec<String> {
        // If source and target are the same, return just the source
        if source_version == self.target_version {
            return vec![source_version.to_string()];
        }

        // Check if we have a direct adapter
        let adapters = self.adapters.read().unwrap();
        if adapters.contains_key(&(source_version.to_string(), self.target_version.clone())) {
            return vec![source_version.to_string(), self.target_version.clone()];
        }

        // TODO: Implement more complex path finding if needed
        // For now, just return an empty path if no direct adapter is found
        vec![]
    }

    /// Adapt an object from one version to another
    pub fn adapt(&self, py: Python, obj: &PyAny) -> PyResult<(PyObject, bool, Vec<String>)> {
        let version = self.detector.detect_version(obj)?;

        let source_version = version.unwrap_or_else(|| self.current_version.clone());

        // If source and target are the same, return the object as is
        if source_version == self.target_version {
            return Ok((obj.to_object(py), true, vec![source_version]));
        }

        // Get the adaptation path
        let path = self.get_adaptation_path_internal(&source_version);
        if path.is_empty() {
            return Ok((obj.to_object(py), false, vec![source_version]));
        }

        // Apply the adapters along the path
        let mut current_obj = obj.to_object(py);
        let mut success = true;

        let adapters = self.adapters.read().unwrap();

        for i in 0..path.len() - 1 {
            let source = &path[i];
            let target = &path[i + 1];

            if let Some(adapter) = adapters.get(&(source.clone(), target.clone())) {
                match current_obj.extract::<&PyAny>(py) {
                    Ok(obj) => {
                        match adapter(obj) {
                            Ok(adapted) => {
                                current_obj = adapted;
                            }
                            Err(e) => {
                                success = false;
                                return Err(e);
                            }
                        }
                    }
                    Err(e) => {
                        success = false;
                        return Err(e);
                    }
                }
            } else {
                success = false;
                break;
            }
        }

        Ok((current_obj, success, path))
    }
}

/// Python wrapper for VersionAdapter
#[pyclass(name = "VersionAdapter")]
pub struct PyVersionAdapter {
    /// The inner adapter
    adapter: Arc<VersionAdapter>,
}

#[pymethods]
impl PyVersionAdapter {
    /// Create a new version adapter
    #[new]
    fn new(current_version: &str, target_version: &str, name: &str) -> Self {
        Self {
            adapter: Arc::new(VersionAdapter::new(current_version, target_version, name)),
        }
    }

    /// Register a version pattern
    fn register_version_pattern(&self, version: &str, pattern: &str) -> PyResult<()> {
        self.adapter.register_pattern(version, pattern)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(format!("Invalid pattern: {}", e)))
    }

    /// Add information about a version
    fn add_version(&self, version: &str, info: &PyDict) -> PyResult<()> {
        let mut info_map = HashMap::new();

        for (key, value) in info.iter() {
            let key_str = key.extract::<String>()?;
            let value_str = value.extract::<String>()?;
            info_map.insert(key_str, value_str);
        }

        self.adapter.add_version(version, info_map);
        Ok(())
    }

    /// Register an adapter function
    fn register_adapter(&self, source_version: &str, target_version: &str, adapter_fn: &PyAny) -> PyResult<()> {
        // Create a wrapper for the Python function
        let adapter: AdapterFunction = Arc::new(move |obj: &PyAny| -> PyResult<PyObject> {
            Python::with_gil(|py| {
                let args = PyTuple::new(py, &[obj]);
                let result = adapter_fn.call1(args)?;
                Ok(result.to_object(py))
            })
        });

        self.adapter.register_adapter(source_version, target_version, adapter);
        Ok(())
    }

    /// Check if an object can be adapted
    fn can_adapt(&self, obj: &PyAny) -> PyResult<bool> {
        self.adapter.can_adapt(obj)
    }

    /// Get the adaptation path for an object
    fn get_adaptation_path(&self, obj: &PyAny) -> PyResult<Vec<String>> {
        self.adapter.get_adaptation_path(obj)
    }

    /// Adapt an object from one version to another
    fn adapt(&self, obj: &PyAny) -> PyResult<(PyObject, bool, Vec<String>)> {
        Python::with_gil(|py| {
            self.adapter.adapt(py, obj)
        })
    }

    /// Get the name of the adapter
    #[getter]
    fn name(&self) -> String {
        self.adapter.name.clone()
    }

    /// Get the current version
    #[getter]
    fn current_version(&self) -> String {
        self.adapter.current_version.clone()
    }

    /// Get the target version
    #[getter]
    fn target_version(&self) -> String {
        self.adapter.target_version.clone()
    }
}
