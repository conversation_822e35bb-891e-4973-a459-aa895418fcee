/*
 * Version Detector
 *
 * This module provides tools for detecting API versions from objects.
 */

use pyo3::prelude::*;
use pyo3::types::{PyDict, PyString};
use regex::Regex;
use std::collections::HashMap;
use std::sync::{Arc, RwLock};

/// A pattern for matching version strings
#[derive(Clone)]
pub struct VersionPattern {
    /// The version identifier (e.g., "1.0")
    pub version: String,
    /// The regex pattern for matching this version
    pub pattern: Regex,
}

impl VersionPattern {
    /// Create a new version pattern
    pub fn new(version: &str, pattern: &str) -> Result<Self, regex::Error> {
        Ok(Self {
            version: version.to_string(),
            pattern: Regex::new(pattern)?,
        })
    }

    /// Check if a string matches this version pattern
    pub fn matches(&self, version_str: &str) -> bool {
        self.pattern.is_match(version_str)
    }
}

/// A detector for API versions
pub struct VersionDetector {
    /// The name of the detector
    pub name: String,
    /// The patterns for matching versions
    pub patterns: RwLock<Vec<VersionPattern>>,
}

impl VersionDetector {
    /// Create a new version detector
    pub fn new(name: &str) -> Self {
        Self {
            name: name.to_string(),
            patterns: RwLock::new(Vec::new()),
        }
    }

    /// Register a version pattern
    pub fn register_pattern(&self, version: &str, pattern: &str) -> Result<(), regex::Error> {
        let pattern = VersionPattern::new(version, pattern)?;
        let mut patterns = self.patterns.write().unwrap();
        patterns.push(pattern);
        Ok(())
    }

    /// Detect the version of an object
    pub fn detect_version(&self, obj: &PyAny) -> PyResult<Option<String>> {
        // Try to get the version attribute
        if let Ok(version) = obj.getattr("version") {
            if let Ok(version_str) = version.extract::<String>() {
                return Ok(self.match_version(&version_str));
            }
        }

        // Try to get the __version__ attribute
        if let Ok(version) = obj.getattr("__version__") {
            if let Ok(version_str) = version.extract::<String>() {
                return Ok(self.match_version(&version_str));
            }
        }

        // Try to get the version from a dictionary
        if let Ok(true) = obj.is_instance_of::<PyDict>() {
            let dict = obj.downcast::<PyDict>()?;
            
            // Try "version" key
            if let Some(version) = dict.get_item("version") {
                if let Ok(version_str) = version.extract::<String>() {
                    return Ok(self.match_version(&version_str));
                }
            }
            
            // Try "__version__" key
            if let Some(version) = dict.get_item("__version__") {
                if let Ok(version_str) = version.extract::<String>() {
                    return Ok(self.match_version(&version_str));
                }
            }
        }

        // No version found
        Ok(None)
    }

    /// Match a version string against the registered patterns
    pub fn match_version(&self, version_str: &str) -> Option<String> {
        let patterns = self.patterns.read().unwrap();
        
        // Try exact match first
        for pattern in patterns.iter() {
            if pattern.matches(version_str) {
                return Some(pattern.version.clone());
            }
        }
        
        // If no exact match, try to find the closest version
        self.find_closest_version(version_str)
    }

    /// Find the closest version to a version string
    fn find_closest_version(&self, version_str: &str) -> Option<String> {
        // Parse the version string into components
        let components: Vec<&str> = version_str.split('.').collect();
        if components.is_empty() {
            return None;
        }
        
        // Try to parse the major version
        let major = match components[0].parse::<f32>() {
            Ok(m) => m,
            Err(_) => return None,
        };
        
        // Find the closest version based on major version
        let patterns = self.patterns.read().unwrap();
        let mut closest_version = None;
        let mut closest_diff = f32::MAX;
        
        for pattern in patterns.iter() {
            let pattern_components: Vec<&str> = pattern.version.split('.').collect();
            if pattern_components.is_empty() {
                continue;
            }
            
            if let Ok(pattern_major) = pattern_components[0].parse::<f32>() {
                let diff = (major - pattern_major).abs();
                if diff < closest_diff {
                    closest_diff = diff;
                    closest_version = Some(pattern.version.clone());
                }
            }
        }
        
        closest_version
    }
}

/// Python wrapper for VersionDetector
#[pyclass(name = "VersionDetector")]
pub struct PyVersionDetector {
    /// The inner detector
    detector: Arc<VersionDetector>,
}

#[pymethods]
impl PyVersionDetector {
    /// Create a new version detector
    #[new]
    fn new(name: &str) -> Self {
        Self {
            detector: Arc::new(VersionDetector::new(name)),
        }
    }

    /// Register a version pattern
    fn register_version_pattern(&self, version: &str, pattern: &str) -> PyResult<()> {
        self.detector.register_pattern(version, pattern)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(format!("Invalid pattern: {}", e)))
    }

    /// Detect the version of an object
    fn detect_version(&self, obj: &PyAny) -> PyResult<Option<String>> {
        self.detector.detect_version(obj)
    }

    /// Match a version string against the registered patterns
    fn _match_version(&self, version_str: &str) -> Option<String> {
        self.detector.match_version(version_str)
    }

    /// Find the closest version to a version string
    fn _find_closest_version(&self, version_str: &str) -> Option<String> {
        self.detector.find_closest_version(version_str)
    }

    /// Get the name of the detector
    #[getter]
    fn name(&self) -> String {
        self.detector.name.clone()
    }
}
