/*
 * Compatibility Tester
 *
 * This module provides tools for testing compatibility between different API versions.
 */

use pyo3::prelude::*;
use pyo3::types::{<PERSON>yD<PERSON>, PyList, PyString};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use super::adapter::{VersionAdapter, PyVersionAdapter};

/// A test case for compatibility testing
#[derive(Clone)]
pub struct TestCase {
    /// The ID of the test case
    pub id: String,
    /// The source version
    pub source_version: String,
    /// The target version
    pub target_version: String,
    /// The input object
    pub input: PyObject,
    /// The expected output
    pub expected_output: PyObject,
    /// A description of the test case
    pub description: Option<String>,
}

/// The result of a compatibility test
#[derive(Clone)]
pub struct TestResult {
    /// The ID of the test case
    pub test_id: String,
    /// Whether the test succeeded
    pub success: bool,
    /// The error message, if any
    pub error: Option<String>,
    /// The actual output, if any
    pub output: Option<PyObject>,
    /// The adaptation path
    pub path: Vec<String>,
}

/// A tester for compatibility between different API versions
pub struct CompatibilityTester {
    /// The name of the tester
    pub name: String,
    /// The registered adapters
    pub adapters: RwLock<HashMap<String, Arc<VersionAdapter>>>,
    /// The registered test cases
    pub test_cases: RwLock<HashMap<String, TestCase>>,
    /// The results of the tests
    pub results: RwLock<HashMap<String, TestResult>>,
}

impl CompatibilityTester {
    /// Create a new compatibility tester
    pub fn new(name: &str) -> Self {
        Self {
            name: name.to_string(),
            adapters: RwLock::new(HashMap::new()),
            test_cases: RwLock::new(HashMap::new()),
            results: RwLock::new(HashMap::new()),
        }
    }

    /// Register an adapter
    pub fn register_adapter(&self, version: &str, adapter: Arc<VersionAdapter>) {
        let mut adapters = self.adapters.write().unwrap();
        adapters.insert(version.to_string(), adapter);
    }

    /// Register a test case
    pub fn register_test_case(
        &self,
        py: Python,
        test_id: &str,
        source_version: &str,
        target_version: &str,
        input: &PyAny,
        expected_output: &PyAny,
        description: Option<&str>,
    ) -> PyResult<()> {
        let test_case = TestCase {
            id: test_id.to_string(),
            source_version: source_version.to_string(),
            target_version: target_version.to_string(),
            input: input.to_object(py),
            expected_output: expected_output.to_object(py),
            description: description.map(|s| s.to_string()),
        };
        
        let mut test_cases = self.test_cases.write().unwrap();
        test_cases.insert(test_id.to_string(), test_case);
        
        Ok(())
    }

    /// Run a specific test
    pub fn run_test(&self, py: Python, test_id: &str) -> PyResult<TestResult> {
        // Get the test case
        let test_cases = self.test_cases.read().unwrap();
        let test_case = match test_cases.get(test_id) {
            Some(tc) => tc,
            None => {
                return Ok(TestResult {
                    test_id: test_id.to_string(),
                    success: false,
                    error: Some(format!("Test case not found: {}", test_id)),
                    output: None,
                    path: vec![],
                });
            }
        };
        
        // Get the adapter for the target version
        let adapters = self.adapters.read().unwrap();
        let adapter = match adapters.get(&test_case.target_version) {
            Some(a) => a,
            None => {
                return Ok(TestResult {
                    test_id: test_id.to_string(),
                    success: false,
                    error: Some(format!("Adapter not found for version: {}", test_case.target_version)),
                    output: None,
                    path: vec![],
                });
            }
        };
        
        // Run the test
        let input = test_case.input.extract::<&PyAny>(py)?;
        match adapter.adapt(py, input) {
            Ok((output, success, path)) => {
                if !success {
                    return Ok(TestResult {
                        test_id: test_id.to_string(),
                        success: false,
                        error: Some("Adaptation failed".to_string()),
                        output: Some(output),
                        path,
                    });
                }
                
                // Compare the output with the expected output
                let expected_output = test_case.expected_output.extract::<&PyAny>(py)?;
                let output_obj = output.extract::<&PyAny>(py)?;
                
                match py.import("builtins")?.getattr("type")?.call1((output_obj,))? {
                    t if t.is_instance_of::<PyDict>()? => {
                        // For dictionaries, compare the keys and values
                        let output_dict = output_obj.downcast::<PyDict>()?;
                        let expected_dict = expected_output.downcast::<PyDict>()?;
                        
                        let mut success = true;
                        let mut error = None;
                        
                        // Check if all keys in expected_dict are in output_dict with the same values
                        for (key, value) in expected_dict.iter() {
                            if let Some(output_value) = output_dict.get_item(key) {
                                if !output_value.eq(value)? {
                                    success = false;
                                    error = Some(format!("Value mismatch for key {}", key));
                                    break;
                                }
                            } else {
                                success = false;
                                error = Some(format!("Key {} not found in output", key));
                                break;
                            }
                        }
                        
                        Ok(TestResult {
                            test_id: test_id.to_string(),
                            success,
                            error,
                            output: Some(output),
                            path,
                        })
                    },
                    _ => {
                        // For other types, use the equality operator
                        let eq = output_obj.eq(expected_output)?;
                        
                        Ok(TestResult {
                            test_id: test_id.to_string(),
                            success: eq,
                            error: if eq { None } else { Some("Output does not match expected output".to_string()) },
                            output: Some(output),
                            path,
                        })
                    }
                }
            },
            Err(e) => {
                Ok(TestResult {
                    test_id: test_id.to_string(),
                    success: false,
                    error: Some(format!("Adaptation error: {}", e)),
                    output: None,
                    path: vec![],
                })
            }
        }
    }

    /// Run all tests
    pub fn run_all_tests(&self, py: Python) -> PyResult<HashMap<String, TestResult>> {
        let mut results = HashMap::new();
        
        let test_cases = self.test_cases.read().unwrap();
        for test_id in test_cases.keys() {
            let result = self.run_test(py, test_id)?;
            results.insert(test_id.clone(), result);
        }
        
        // Store the results
        let mut results_lock = self.results.write().unwrap();
        *results_lock = results.clone();
        
        Ok(results)
    }

    /// Get a summary of the test results
    pub fn get_test_summary(&self) -> HashMap<String, PyObject> {
        let results = self.results.read().unwrap();
        
        let total = results.len();
        let success = results.values().filter(|r| r.success).count();
        let failure = total - success;
        let success_rate = if total > 0 { success as f64 / total as f64 } else { 0.0 };
        
        let mut summary = HashMap::new();
        Python::with_gil(|py| {
            summary.insert("total".to_string(), total.to_object(py));
            summary.insert("success".to_string(), success.to_object(py));
            summary.insert("failure".to_string(), failure.to_object(py));
            summary.insert("success_rate".to_string(), success_rate.to_object(py));
        });
        
        summary
    }
}

/// Python wrapper for CompatibilityTester
#[pyclass(name = "CompatibilityTester")]
pub struct PyCompatibilityTester {
    /// The inner tester
    tester: Arc<CompatibilityTester>,
}

#[pymethods]
impl PyCompatibilityTester {
    /// Create a new compatibility tester
    #[new]
    fn new(name: &str) -> Self {
        Self {
            tester: Arc::new(CompatibilityTester::new(name)),
        }
    }

    /// Register an adapter
    fn register_adapter(&self, version: &str, adapter: &PyVersionAdapter) -> PyResult<()> {
        // Extract the inner adapter
        let adapter_arc = adapter.adapter.clone();
        
        self.tester.register_adapter(version, adapter_arc);
        Ok(())
    }

    /// Register a test case
    fn register_test_case(
        &self,
        test_id: &str,
        source_version: &str,
        target_version: &str,
        input: &PyAny,
        expected_output: &PyAny,
        description: Option<&str>,
    ) -> PyResult<()> {
        Python::with_gil(|py| {
            self.tester.register_test_case(py, test_id, source_version, target_version, input, expected_output, description)
        })
    }

    /// Run a specific test
    fn run_test(&self, test_id: &str) -> PyResult<HashMap<String, PyObject>> {
        Python::with_gil(|py| {
            let result = self.tester.run_test(py, test_id)?;
            
            let mut result_dict = HashMap::new();
            result_dict.insert("test_id".to_string(), result.test_id.to_object(py));
            result_dict.insert("success".to_string(), result.success.to_object(py));
            
            if let Some(error) = result.error {
                result_dict.insert("error".to_string(), error.to_object(py));
            } else {
                result_dict.insert("error".to_string(), py.None());
            }
            
            if let Some(output) = result.output {
                result_dict.insert("output".to_string(), output);
            } else {
                result_dict.insert("output".to_string(), py.None());
            }
            
            result_dict.insert("path".to_string(), result.path.to_object(py));
            
            Ok(result_dict)
        })
    }

    /// Run all tests
    fn run_all_tests(&self) -> PyResult<HashMap<String, HashMap<String, PyObject>>> {
        Python::with_gil(|py| {
            let results = self.tester.run_all_tests(py)?;
            
            let mut results_dict = HashMap::new();
            for (test_id, result) in results {
                let mut result_dict = HashMap::new();
                result_dict.insert("test_id".to_string(), result.test_id.to_object(py));
                result_dict.insert("success".to_string(), result.success.to_object(py));
                
                if let Some(error) = result.error {
                    result_dict.insert("error".to_string(), error.to_object(py));
                } else {
                    result_dict.insert("error".to_string(), py.None());
                }
                
                if let Some(output) = result.output {
                    result_dict.insert("output".to_string(), output);
                } else {
                    result_dict.insert("output".to_string(), py.None());
                }
                
                result_dict.insert("path".to_string(), result.path.to_object(py));
                
                results_dict.insert(test_id, result_dict);
            }
            
            Ok(results_dict)
        })
    }

    /// Get a summary of the test results
    fn get_test_summary(&self) -> HashMap<String, PyObject> {
        self.tester.get_test_summary()
    }

    /// Get the name of the tester
    #[getter]
    fn name(&self) -> String {
        self.tester.name.clone()
    }
}
