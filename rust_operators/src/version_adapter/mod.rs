/*
 * API Version Adapter Module
 *
 * This module provides tools for detecting API versions and adapting between
 * different API versions.
 */

use pyo3::prelude::*;
use pyo3::types::{PyDict, PyList, PyString};
use std::collections::HashMap;
use std::sync::Arc;

// Module declarations
mod detector;
mod adapter;
mod compatibility;

// Re-exports
pub use detector::{VersionDetector, VersionPattern};
pub use adapter::{VersionAdapter, AdapterFunction, VersionInfo};
pub use compatibility::{CompatibilityTester, TestCase, TestResult};

/// Register the version_adapter module with Python
pub fn register_module(_py: Python, m: &PyModule) -> PyResult<()> {
    // Register the VersionDetector class
    m.add_class::<detector::PyVersionDetector>()?;
    
    // Register the VersionAdapter class
    m.add_class::<adapter::PyVersionAdapter>()?;
    
    // Register the CompatibilityTester class
    m.add_class::<compatibility::PyCompatibilityTester>()?;
    
    // Add module-level constants or functions here
    
    Ok(())
}
