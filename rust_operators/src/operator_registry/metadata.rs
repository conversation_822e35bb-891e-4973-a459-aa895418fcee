/*
 * Operator Metadata
 *
 * This module provides metadata for operators.
 */

use pyo3::prelude::*;
use pyo3::types::{<PERSON>y<PERSON><PERSON>, <PERSON>y<PERSON><PERSON>, PyTuple};
use std::sync::{Arc, RwLock};
use std::collections::{HashMap, HashSet};
use std::fmt;
use serde::{Serialize, Deserialize};
use semver::{Version, VersionReq};
use chrono::{DateTime, Utc};

/// Operator metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OperatorMetadata {
    /// The name of the operator
    pub name: String,
    /// The version of the operator
    pub version: String,
    /// The description of the operator
    pub description: String,
    /// The tags of the operator
    pub tags: Vec<String>,
    /// The dependencies of the operator
    pub dependencies: Vec<(String, String)>,
    /// The creation time of the operator
    pub created_at: DateTime<Utc>,
    /// The last update time of the operator
    pub updated_at: DateTime<Utc>,
}

impl OperatorMetadata {
    /// Create a new operator metadata
    pub fn new(
        name: &str,
        version: &str,
        description: &str,
        tags: Vec<String>,
        dependencies: Vec<(String, String)>,
    ) -> Self {
        let now = Utc::now();

        Self {
            name: name.to_string(),
            version: version.to_string(),
            description: description.to_string(),
            tags,
            dependencies,
            created_at: now,
            updated_at: now,
        }
    }

    /// Update the metadata
    pub fn update(
        &mut self,
        version: Option<&str>,
        description: Option<&str>,
        tags: Option<Vec<String>>,
        dependencies: Option<Vec<(String, String)>>,
    ) {
        if let Some(version) = version {
            self.version = version.to_string();
        }

        if let Some(description) = description {
            self.description = description.to_string();
        }

        if let Some(tags) = tags {
            self.tags = tags;
        }

        if let Some(dependencies) = dependencies {
            self.dependencies = dependencies;
        }

        self.updated_at = Utc::now();
    }

    /// Check if the metadata has a tag
    pub fn has_tag(&self, tag: &str) -> bool {
        self.tags.contains(&tag.to_string())
    }

    /// Check if the metadata has a dependency
    pub fn has_dependency(&self, dependency: &str) -> bool {
        self.dependencies.iter().any(|(name, _)| name == dependency)
    }

    /// Check if the metadata has a dependency with a specific version
    pub fn has_dependency_with_version(&self, dependency: &str, version_req: &str) -> PyResult<bool> {
        // Parse version requirement
        let req = VersionReq::parse(version_req)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(format!("Invalid version requirement: {}", e)))?;

        for (name, version) in &self.dependencies {
            if name == dependency {
                // Parse dependency version
                let version = Version::parse(version)
                    .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(format!("Invalid dependency version: {}", e)))?;

                // Check compatibility
                return Ok(req.matches(&version));
            }
        }

        Ok(false)
    }

    /// Add a tag
    pub fn add_tag(&mut self, tag: &str) {
        if !self.tags.contains(&tag.to_string()) {
            self.tags.push(tag.to_string());
            self.updated_at = Utc::now();
        }
    }

    /// Remove a tag
    pub fn remove_tag(&mut self, tag: &str) -> bool {
        let len = self.tags.len();
        self.tags.retain(|t| t != tag);

        if self.tags.len() != len {
            self.updated_at = Utc::now();
            true
        } else {
            false
        }
    }

    /// Add a dependency
    pub fn add_dependency(&mut self, dependency: &str, version: &str) {
        // Check if dependency already exists
        for (name, ver) in &mut self.dependencies {
            if name == dependency {
                // Update version
                *ver = version.to_string();
                self.updated_at = Utc::now();
                return;
            }
        }

        // Add new dependency
        self.dependencies.push((dependency.to_string(), version.to_string()));
        self.updated_at = Utc::now();
    }

    /// Remove a dependency
    pub fn remove_dependency(&mut self, dependency: &str) -> bool {
        let len = self.dependencies.len();
        self.dependencies.retain(|(name, _)| name != dependency);

        if self.dependencies.len() != len {
            self.updated_at = Utc::now();
            true
        } else {
            false
        }
    }

    /// Convert to dictionary
    pub fn to_dict(&self) -> HashMap<String, PyObject> {
        let mut dict = HashMap::new();

        Python::with_gil(|py| {
            dict.insert("name".to_string(), self.name.clone().into_py(py));
            dict.insert("version".to_string(), self.version.clone().into_py(py));
            dict.insert("description".to_string(), self.description.clone().into_py(py));
            dict.insert("tags".to_string(), self.tags.clone().into_py(py));

            // Convert dependencies to list of tuples
            let deps = PyList::new(py, &[]);
            for (name, version) in &self.dependencies {
                let tuple = PyTuple::new(py, [name.clone(), version.clone()]);
                deps.append(tuple).unwrap();
            }

            dict.insert("dependencies".to_string(), deps.into_py(py));
            dict.insert("created_at".to_string(), self.created_at.to_string().into_py(py));
            dict.insert("updated_at".to_string(), self.updated_at.to_string().into_py(py));
        });

        dict
    }
}

/// Python wrapper for OperatorMetadata
#[pyclass(name = "OperatorMetadata")]
pub struct PyOperatorMetadata {
    /// The inner metadata
    pub metadata: OperatorMetadata,
}

#[pymethods]
impl PyOperatorMetadata {
    /// Create a new operator metadata
    #[new]
    fn new(
        name: &str,
        version: &str,
        description: Option<&str>,
        tags: Option<Vec<String>>,
        dependencies: Option<Vec<(String, String)>>,
    ) -> Self {
        Self {
            metadata: OperatorMetadata::new(
                name,
                version,
                description.unwrap_or(""),
                tags.unwrap_or_default(),
                dependencies.unwrap_or_default(),
            ),
        }
    }

    /// Update the metadata
    fn update(
        &mut self,
        version: Option<&str>,
        description: Option<&str>,
        tags: Option<Vec<String>>,
        dependencies: Option<Vec<(String, String)>>,
    ) {
        self.metadata.update(version, description, tags, dependencies);
    }

    /// Check if the metadata has a tag
    fn has_tag(&self, tag: &str) -> bool {
        self.metadata.has_tag(tag)
    }

    /// Check if the metadata has a dependency
    fn has_dependency(&self, dependency: &str) -> bool {
        self.metadata.has_dependency(dependency)
    }

    /// Check if the metadata has a dependency with a specific version
    fn has_dependency_with_version(&self, dependency: &str, version_req: &str) -> PyResult<bool> {
        self.metadata.has_dependency_with_version(dependency, version_req)
    }

    /// Add a tag
    fn add_tag(&mut self, tag: &str) {
        self.metadata.add_tag(tag);
    }

    /// Remove a tag
    fn remove_tag(&mut self, tag: &str) -> bool {
        self.metadata.remove_tag(tag)
    }

    /// Add a dependency
    fn add_dependency(&mut self, dependency: &str, version: &str) {
        self.metadata.add_dependency(dependency, version);
    }

    /// Remove a dependency
    fn remove_dependency(&mut self, dependency: &str) -> bool {
        self.metadata.remove_dependency(dependency)
    }

    /// Get the name of the operator
    #[getter]
    fn name(&self) -> String {
        self.metadata.name.clone()
    }

    /// Get the version of the operator
    #[getter]
    fn version(&self) -> String {
        self.metadata.version.clone()
    }

    /// Get the description of the operator
    #[getter]
    fn description(&self) -> String {
        self.metadata.description.clone()
    }

    /// Get the tags of the operator
    #[getter]
    fn tags(&self) -> Vec<String> {
        self.metadata.tags.clone()
    }

    /// Get the dependencies of the operator
    #[getter]
    fn dependencies(&self) -> Vec<(String, String)> {
        self.metadata.dependencies.clone()
    }

    /// Get the creation time of the operator
    #[getter]
    fn created_at(&self) -> String {
        self.metadata.created_at.to_string()
    }

    /// Get the last update time of the operator
    #[getter]
    fn updated_at(&self) -> String {
        self.metadata.updated_at.to_string()
    }

    /// Convert to dictionary
    fn to_dict<'py>(&self, py: Python<'py>) -> PyResult<&'py PyAny> {
        let dict = PyDict::new(py);

        dict.set_item("name", self.metadata.name.clone())?;
        dict.set_item("version", self.metadata.version.clone())?;
        dict.set_item("description", self.metadata.description.clone())?;
        dict.set_item("tags", self.metadata.tags.clone())?;

        // Convert dependencies to list of tuples
        let deps = PyList::new(py, &[]);
        for (name, version) in &self.metadata.dependencies {
            let tuple = PyTuple::new(py, [name, version]);
            deps.append(tuple)?;
        }

        dict.set_item("dependencies", deps)?;
        dict.set_item("created_at", self.metadata.created_at.to_string())?;
        dict.set_item("updated_at", self.metadata.updated_at.to_string())?;

        Ok(dict.into_any())
    }

    /// String representation
    fn __str__(&self) -> String {
        format!("OperatorMetadata({}, {})", self.metadata.name, self.metadata.version)
    }

    /// Representation
    fn __repr__(&self) -> String {
        format!("OperatorMetadata(name='{}', version='{}', tags={:?}, dependencies={:?})",
                self.metadata.name, self.metadata.version, self.metadata.tags, self.metadata.dependencies)
    }
}
