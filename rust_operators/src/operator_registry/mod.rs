/*
 * Operator Registry
 *
 * This module provides a unified registry for operators.
 */

use pyo3::prelude::*;
use pyo3::types::{PyD<PERSON>, PyList, PyTuple};
use std::sync::{Arc, RwLock};
use std::collections::{HashMap, HashSet};
use std::fmt;
use serde::{Serialize, Deserialize};
use semver::{Version, VersionReq};

// Module declarations
pub mod registry;
pub mod metadata;
pub mod dependency;
pub mod compatibility;
pub mod factory;

// Re-exports
pub use registry::{OperatorRegistry, PyOperatorRegistry};
pub use metadata::{OperatorMetadata, PyOperatorMetadata};
pub use dependency::{DependencyManager, PyDependencyManager};
pub use compatibility::{CompatibilityChecker, PyCompatibilityChecker};
pub use factory::{OperatorFactory, PyOperatorFactory};

/// Register the operator_registry module with Python
pub fn register_module(_py: Python<'_>, m: &PyModule) -> PyResult<()> {
    // Register the OperatorRegistry class
    m.add_class::<PyOperatorRegistry>()?;

    // Register the OperatorMetadata class
    m.add_class::<PyOperatorMetadata>()?;

    // Register the DependencyManager class
    m.add_class::<PyDependencyManager>()?;

    // Register the CompatibilityChecker class
    m.add_class::<PyCompatibilityChecker>()?;

    // Register the OperatorFactory class
    m.add_class::<PyOperatorFactory>()?;

    // Add module-level functions
    m.add_function(wrap_pyfunction!(get_global_registry, m)?)?;
    m.add_function(wrap_pyfunction!(register_operator, m)?)?;
    m.add_function(wrap_pyfunction!(get_operator, m)?)?;
    m.add_function(wrap_pyfunction!(list_operators, m)?)?;
    m.add_function(wrap_pyfunction!(get_operator_metadata, m)?)?;
    m.add_function(wrap_pyfunction!(check_compatibility, m)?)?;

    Ok(())
}

/// Operator category
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum OperatorCategory {
    /// Differential geometry operators
    DifferentialGeometry,
    /// Interference operators
    Interference,
    /// Fractal operators
    Fractal,
    /// Game theory operators
    GameTheory,
    /// Topology operators
    Topology,
    /// Transform operators
    Transform,
    /// Evolution operators
    Evolution,
    /// Resonance network operators
    ResonanceNetwork,
    /// Explanation operators
    Explanation,
    /// Verification operators
    Verification,
    /// Engineering support operators
    EngineeringSupport,
    /// Multilevel operators
    Multilevel,
    /// Entropy operators
    Entropy,
    /// Utility operators
    Utility,
    /// NumPy operators
    NumPy,
    /// Parallel computing operators
    ParallelComputing,
    /// Custom category
    Custom(String),
}

impl OperatorCategory {
    /// Convert to string
    pub fn to_str(&self) -> String {
        match self {
            OperatorCategory::DifferentialGeometry => "differential_geometry".to_string(),
            OperatorCategory::Interference => "interference".to_string(),
            OperatorCategory::Fractal => "fractal".to_string(),
            OperatorCategory::GameTheory => "game_theory".to_string(),
            OperatorCategory::Topology => "topology".to_string(),
            OperatorCategory::Transform => "transform".to_string(),
            OperatorCategory::Evolution => "evolution".to_string(),
            OperatorCategory::ResonanceNetwork => "resonance_network".to_string(),
            OperatorCategory::Explanation => "explanation".to_string(),
            OperatorCategory::Verification => "verification".to_string(),
            OperatorCategory::EngineeringSupport => "engineering_support".to_string(),
            OperatorCategory::Multilevel => "multilevel".to_string(),
            OperatorCategory::Entropy => "entropy".to_string(),
            OperatorCategory::Utility => "utility".to_string(),
            OperatorCategory::NumPy => "numpy".to_string(),
            OperatorCategory::ParallelComputing => "parallel_computing".to_string(),
            OperatorCategory::Custom(name) => name.clone(),
        }
    }

    /// Create from string
    pub fn from_str(s: &str) -> Self {
        match s {
            "differential_geometry" => OperatorCategory::DifferentialGeometry,
            "interference" => OperatorCategory::Interference,
            "fractal" => OperatorCategory::Fractal,
            "game_theory" => OperatorCategory::GameTheory,
            "topology" => OperatorCategory::Topology,
            "transform" => OperatorCategory::Transform,
            "evolution" => OperatorCategory::Evolution,
            "resonance_network" => OperatorCategory::ResonanceNetwork,
            "explanation" => OperatorCategory::Explanation,
            "verification" => OperatorCategory::Verification,
            "engineering_support" => OperatorCategory::EngineeringSupport,
            "multilevel" => OperatorCategory::Multilevel,
            "entropy" => OperatorCategory::Entropy,
            "utility" => OperatorCategory::Utility,
            "numpy" => OperatorCategory::NumPy,
            "parallel_computing" => OperatorCategory::ParallelComputing,
            _ => OperatorCategory::Custom(s.to_string()),
        }
    }
}

impl fmt::Display for OperatorCategory {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(f, "{}", self.to_str())
    }
}

/// Python wrapper for OperatorCategory
#[pyclass(name = "OperatorCategory")]
#[derive(Clone)]
pub struct PyOperatorCategory {
    /// The inner category
    pub category: OperatorCategory,
}

#[pymethods]
impl PyOperatorCategory {
    /// Create a new operator category
    #[new]
    fn new(category: &str) -> Self {
        Self {
            category: OperatorCategory::from_str(category),
        }
    }

    /// Convert to string
    fn __str__(&self) -> String {
        self.category.to_str()
    }

    /// Representation
    fn __repr__(&self) -> String {
        format!("OperatorCategory('{}')", self.category.to_str())
    }

    /// Differential geometry category
    #[staticmethod]
    fn differential_geometry() -> Self {
        Self {
            category: OperatorCategory::DifferentialGeometry,
        }
    }

    /// Interference category
    #[staticmethod]
    fn interference() -> Self {
        Self {
            category: OperatorCategory::Interference,
        }
    }

    /// Fractal category
    #[staticmethod]
    fn fractal() -> Self {
        Self {
            category: OperatorCategory::Fractal,
        }
    }

    /// Game theory category
    #[staticmethod]
    fn game_theory() -> Self {
        Self {
            category: OperatorCategory::GameTheory,
        }
    }

    /// Topology category
    #[staticmethod]
    fn topology() -> Self {
        Self {
            category: OperatorCategory::Topology,
        }
    }

    /// Transform category
    #[staticmethod]
    fn transform() -> Self {
        Self {
            category: OperatorCategory::Transform,
        }
    }

    /// Evolution category
    #[staticmethod]
    fn evolution() -> Self {
        Self {
            category: OperatorCategory::Evolution,
        }
    }

    /// Resonance network category
    #[staticmethod]
    fn resonance_network() -> Self {
        Self {
            category: OperatorCategory::ResonanceNetwork,
        }
    }

    /// Explanation category
    #[staticmethod]
    fn explanation() -> Self {
        Self {
            category: OperatorCategory::Explanation,
        }
    }

    /// Verification category
    #[staticmethod]
    fn verification() -> Self {
        Self {
            category: OperatorCategory::Verification,
        }
    }

    /// Engineering support category
    #[staticmethod]
    fn engineering_support() -> Self {
        Self {
            category: OperatorCategory::EngineeringSupport,
        }
    }

    /// Multilevel category
    #[staticmethod]
    fn multilevel() -> Self {
        Self {
            category: OperatorCategory::Multilevel,
        }
    }

    /// Entropy category
    #[staticmethod]
    fn entropy() -> Self {
        Self {
            category: OperatorCategory::Entropy,
        }
    }

    /// Utility category
    #[staticmethod]
    fn utility() -> Self {
        Self {
            category: OperatorCategory::Utility,
        }
    }

    /// NumPy category
    #[staticmethod]
    fn numpy() -> Self {
        Self {
            category: OperatorCategory::NumPy,
        }
    }

    /// Parallel computing category
    #[staticmethod]
    fn parallel_computing() -> Self {
        Self {
            category: OperatorCategory::ParallelComputing,
        }
    }

    /// Custom category
    #[staticmethod]
    fn custom(name: &str) -> Self {
        Self {
            category: OperatorCategory::Custom(name.to_string()),
        }
    }
}

/// Global registry
lazy_static! {
    static ref GLOBAL_REGISTRY: Arc<RwLock<OperatorRegistry>> = Arc::new(RwLock::new(OperatorRegistry::new()));
}

/// Get the global registry
#[pyfunction]
fn get_global_registry() -> PyOperatorRegistry {
    PyOperatorRegistry {
        registry: GLOBAL_REGISTRY.clone(),
    }
}

/// Register an operator
#[pyfunction]
fn register_operator<'py>(
    category: &str,
    name: &str,
    operator: PyObject,
    version: &str,
    description: Option<&str>,
    tags: Option<Vec<String>>,
    dependencies: Option<Vec<(String, String)>>,
    py: Python<'py>,
) -> PyResult<bool> {
    let mut registry = GLOBAL_REGISTRY.write().unwrap();

    // Create metadata
    let metadata = OperatorMetadata::new(
        name,
        version,
        description.unwrap_or(""),
        tags.unwrap_or_default(),
        dependencies.unwrap_or_default(),
    );

    // Register operator
    registry.register(
        OperatorCategory::from_str(category),
        name,
        operator,
        metadata,
    )
}

/// Get an operator
#[pyfunction]
fn get_operator<'py>(category: &str, name: &str, py: Python<'py>) -> PyResult<Option<PyObject>> {
    let registry = GLOBAL_REGISTRY.read().unwrap();

    // Get operator
    registry.get(OperatorCategory::from_str(category), name)
}

/// List operators
#[pyfunction]
fn list_operators<'py>(category: Option<&str>, py: Python<'py>) -> PyResult<Vec<(String, String)>> {
    let registry = GLOBAL_REGISTRY.read().unwrap();

    // List operators
    if let Some(category) = category {
        registry.list_by_category(OperatorCategory::from_str(category))
    } else {
        registry.list_all()
    }
}

/// Get operator metadata
#[pyfunction]
fn get_operator_metadata<'py>(category: &str, name: &str, py: Python<'py>) -> PyResult<Option<PyOperatorMetadata>> {
    let registry = GLOBAL_REGISTRY.read().unwrap();

    // Get metadata
    if let Some(metadata) = registry.get_metadata(OperatorCategory::from_str(category), name) {
        Ok(Some(PyOperatorMetadata { metadata: metadata.clone() }))
    } else {
        Ok(None)
    }
}

/// Check compatibility
#[pyfunction]
fn check_compatibility<'py>(
    category: &str,
    name: &str,
    version_req: &str,
    py: Python<'py>,
) -> PyResult<bool> {
    let registry = GLOBAL_REGISTRY.read().unwrap();

    // Get metadata
    if let Some(metadata) = registry.get_metadata(OperatorCategory::from_str(category), name) {
        // Parse version requirement
        let req = VersionReq::parse(version_req)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(format!("Invalid version requirement: {}", e)))?;

        // Parse operator version
        let version = Version::parse(&metadata.version)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(format!("Invalid operator version: {}", e)))?;

        // Check compatibility
        Ok(req.matches(&version))
    } else {
        Ok(false)
    }
}
