/*
 * Operator Factory
 *
 * This module provides a factory for creating operators.
 */

use pyo3::prelude::*;
use pyo3::types::{<PERSON>yD<PERSON>, PyList, PyTuple};
use std::sync::{Arc, RwLock};
use std::collections::{HashMap, HashSet};
use std::fmt;
use serde::{Serialize, Deserialize};
use semver::{Version, VersionReq};
use log::{info, warn, error, debug};

use super::{OperatorCategory, OperatorMetadata, OperatorRegistry, DependencyManager};

/// Operator factory
pub struct OperatorFactory {
    /// The registry
    pub registry: Arc<RwLock<OperatorRegistry>>,
    /// The dependency manager
    pub dependency_manager: DependencyManager,
    /// The operator constructors
    pub constructors: RwLock<HashMap<(String, String), PyObject>>,
}

impl OperatorFactory {
    /// Create a new operator factory
    pub fn new(registry: Arc<RwLock<OperatorRegistry>>) -> Self {
        Self {
            registry: registry.clone(),
            dependency_manager: DependencyManager::new(registry),
            constructors: RwLock::new(HashMap::new()),
        }
    }
    
    /// Register a constructor
    pub fn register_constructor(
        &self,
        category: OperatorCategory,
        name: &str,
        constructor: PyObject,
    ) -> PyResult<()> {
        let mut constructors = self.constructors.write().unwrap();
        constructors.insert((category.to_str(), name.to_string()), constructor);
        
        Ok(())
    }
    
    /// Remove a constructor
    pub fn remove_constructor(
        &self,
        category: OperatorCategory,
        name: &str,
    ) -> bool {
        let mut constructors = self.constructors.write().unwrap();
        constructors.remove(&(category.to_str(), name.to_string())).is_some()
    }
    
    /// Create an operator
    pub fn create_operator(
        &self,
        category: OperatorCategory,
        name: &str,
        args: &PyTuple,
        kwargs: Option<&PyDict>,
        py: Python,
    ) -> PyResult<PyObject> {
        // Check if constructor exists
        let constructors = self.constructors.read().unwrap();
        
        if let Some(constructor) = constructors.get(&(category.to_str(), name.to_string())) {
            // Call constructor
            constructor.call(py, args, kwargs)
        } else {
            // Check if operator exists in registry
            let registry = self.registry.read().unwrap();
            
            if let Some(operator) = registry.get(category.clone(), name)? {
                // Call operator
                operator.call(py, args, kwargs)
            } else {
                Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                    format!("Operator {}.{} not found", category, name)
                ))
            }
        }
    }
    
    /// Create an operator with dependencies
    pub fn create_operator_with_dependencies(
        &self,
        category: OperatorCategory,
        name: &str,
        args: &PyTuple,
        kwargs: Option<&PyDict>,
        py: Python,
    ) -> PyResult<PyObject> {
        // Resolve dependencies
        let dependencies = self.dependency_manager.resolve_dependencies(category.clone(), name)?;
        
        // Create operators in dependency order
        let mut created_operators = HashMap::new();
        
        for (dep_category, dep_name) in dependencies {
            // Skip if already created
            if created_operators.contains_key(&(dep_category.clone(), dep_name.clone())) {
                continue;
            }
            
            // Create empty args and kwargs
            let dep_args = PyTuple::empty(py);
            let dep_kwargs = PyDict::new(py);
            
            // Create operator
            let operator = self.create_operator(
                OperatorCategory::from_str(&dep_category),
                &dep_name,
                dep_args,
                Some(dep_kwargs),
                py,
            )?;
            
            // Store operator
            created_operators.insert((dep_category, dep_name), operator);
        }
        
        // Create the requested operator
        self.create_operator(category, name, args, kwargs, py)
    }
    
    /// Get all constructors
    pub fn get_constructors(&self) -> Vec<(String, String)> {
        let constructors = self.constructors.read().unwrap();
        
        constructors.keys()
            .map(|(category, name)| (category.clone(), name.clone()))
            .collect()
    }
    
    /// Clear all constructors
    pub fn clear_constructors(&self) {
        let mut constructors = self.constructors.write().unwrap();
        constructors.clear();
    }
}

/// Python wrapper for OperatorFactory
#[pyclass(name = "OperatorFactory")]
pub struct PyOperatorFactory {
    /// The inner factory
    pub factory: OperatorFactory,
}

#[pymethods]
impl PyOperatorFactory {
    /// Create a new operator factory
    #[new]
    fn new(registry: &PyOperatorRegistry) -> Self {
        Self {
            factory: OperatorFactory::new(registry.registry.clone()),
        }
    }
    
    /// Register a constructor
    fn register_constructor(
        &self,
        category: &str,
        name: &str,
        constructor: PyObject,
    ) -> PyResult<()> {
        self.factory.register_constructor(
            OperatorCategory::from_str(category),
            name,
            constructor,
        )
    }
    
    /// Remove a constructor
    fn remove_constructor(
        &self,
        category: &str,
        name: &str,
    ) -> bool {
        self.factory.remove_constructor(
            OperatorCategory::from_str(category),
            name,
        )
    }
    
    /// Create an operator
    fn create_operator(
        &self,
        category: &str,
        name: &str,
        args: Option<&PyTuple>,
        kwargs: Option<&PyDict>,
        py: Python,
    ) -> PyResult<PyObject> {
        let args = args.unwrap_or_else(|| PyTuple::empty(py));
        
        self.factory.create_operator(
            OperatorCategory::from_str(category),
            name,
            args,
            kwargs,
            py,
        )
    }
    
    /// Create an operator with dependencies
    fn create_operator_with_dependencies(
        &self,
        category: &str,
        name: &str,
        args: Option<&PyTuple>,
        kwargs: Option<&PyDict>,
        py: Python,
    ) -> PyResult<PyObject> {
        let args = args.unwrap_or_else(|| PyTuple::empty(py));
        
        self.factory.create_operator_with_dependencies(
            OperatorCategory::from_str(category),
            name,
            args,
            kwargs,
            py,
        )
    }
    
    /// Get all constructors
    fn get_constructors(&self) -> Vec<(String, String)> {
        self.factory.get_constructors()
    }
    
    /// Clear all constructors
    fn clear_constructors(&self) {
        self.factory.clear_constructors();
    }
    
    /// String representation
    fn __str__(&self) -> String {
        let constructors = self.factory.get_constructors();
        format!("OperatorFactory({} constructors)", constructors.len())
    }
    
    /// Representation
    fn __repr__(&self) -> String {
        let constructors = self.factory.get_constructors();
        format!("OperatorFactory({} constructors)", constructors.len())
    }
}

// Import PyOperatorRegistry
use super::registry::PyOperatorRegistry;
