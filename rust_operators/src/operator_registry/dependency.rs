/*
 * Dependency Manager
 *
 * This module provides dependency management for operators.
 */

use pyo3::prelude::*;
use pyo3::types::{PyD<PERSON>, PyList, PyTuple};
use std::sync::{Arc, RwLock};
use std::collections::{HashMap, HashSet, VecDeque};
use std::fmt;
use serde::{Serialize, Deserialize};
use semver::{Version, VersionReq};
use log::{info, warn, error, debug};

use super::{OperatorCategory, OperatorMetadata, OperatorRegistry};

/// Dependency manager
pub struct DependencyManager {
    /// The registry
    pub registry: Arc<RwLock<OperatorRegistry>>,
}

impl DependencyManager {
    /// Create a new dependency manager
    pub fn new(registry: Arc<RwLock<OperatorRegistry>>) -> Self {
        Self {
            registry,
        }
    }

    /// Check if an operator's dependencies are satisfied
    pub fn check_dependencies(&self, category: OperatorCategory, name: &str) -> PyResult<(bool, Vec<String>)> {
        let registry = self.registry.read().unwrap();

        // Get operator metadata
        if let Some(metadata) = registry.get_metadata(category, name) {
            let mut missing = Vec::new();

            // Check each dependency
            for (dep_name, dep_version) in &metadata.dependencies {
                // Parse dependency parts
                let parts: Vec<&str> = dep_name.split('.').collect();
                if parts.len() != 2 {
                    return Err(pyo3::exceptions::PyValueError::new_err(
                        format!("Invalid dependency format: {}", dep_name)
                    ));
                }

                let dep_category = OperatorCategory::from_str(parts[0]);
                let dep_op_name = parts[1];

                // Check if dependency exists
                if let Some(dep_metadata) = registry.get_metadata(dep_category.clone(), dep_op_name) {
                    // Parse version requirement
                    let req = VersionReq::parse(dep_version)
                        .map_err(|e| pyo3::exceptions::PyValueError::new_err(
                            format!("Invalid version requirement: {}", e)
                        ))?;

                    // Parse operator version
                    let version = Version::parse(&dep_metadata.version)
                        .map_err(|e| pyo3::exceptions::PyValueError::new_err(
                            format!("Invalid operator version: {}", e)
                        ))?;

                    // Check compatibility
                    if !req.matches(&version) {
                        missing.push(format!("{}.{} (requires {}, found {})",
                                            dep_category, dep_op_name, dep_version, dep_metadata.version));
                    }
                } else {
                    missing.push(format!("{}.{} (not found)", dep_category, dep_op_name));
                }
            }

            Ok((missing.is_empty(), missing))
        } else {
            Err(pyo3::exceptions::PyValueError::new_err(
                format!("Operator {}.{} not found", category, name)
            ))
        }
    }

    /// Get all operators that depend on an operator
    pub fn get_dependents(&self, category: OperatorCategory, name: &str) -> Vec<(String, String)> {
        let registry = self.registry.read().unwrap();
        let mut dependents = Vec::new();

        // Check all operators
        for (cat, metadata_map) in &registry.metadata {
            for (op_name, metadata) in metadata_map {
                // Check dependencies
                for (dep_name, _) in &metadata.dependencies {
                    // Parse dependency parts
                    let parts: Vec<&str> = dep_name.split('.').collect();
                    if parts.len() != 2 {
                        continue;
                    }

                    let dep_category = OperatorCategory::from_str(parts[0]);
                    let dep_op_name = parts[1];

                    // Check if this is the operator we're looking for
                    if dep_category == category && dep_op_name == name {
                        dependents.push((cat.to_str(), op_name.clone()));
                        break;
                    }
                }
            }
        }

        dependents
    }

    /// Build a dependency graph
    pub fn build_dependency_graph(&self) -> HashMap<(String, String), Vec<(String, String)>> {
        let registry = self.registry.read().unwrap();
        let mut graph = HashMap::new();

        // Build graph
        for (category, metadata_map) in &registry.metadata {
            for (name, metadata) in metadata_map {
                let key = (category.to_str(), name.clone());
                let mut deps = Vec::new();

                // Add dependencies
                for (dep_name, _) in &metadata.dependencies {
                    // Parse dependency parts
                    let parts: Vec<&str> = dep_name.split('.').collect();
                    if parts.len() != 2 {
                        continue;
                    }

                    deps.push((parts[0].to_string(), parts[1].to_string()));
                }

                graph.insert(key, deps);
            }
        }

        graph
    }

    /// Check for circular dependencies
    pub fn check_circular_dependencies(&self) -> Vec<Vec<(String, String)>> {
        let graph = self.build_dependency_graph();
        let mut cycles = Vec::new();

        // Check each node
        for (node, _) in &graph {
            let mut visited = HashSet::new();
            let mut path = Vec::new();

            self.dfs_check_cycle(node, &graph, &mut visited, &mut path, &mut cycles);
        }

        cycles
    }

    /// DFS to check for cycles
    fn dfs_check_cycle(
        &self,
        node: &(String, String),
        graph: &HashMap<(String, String), Vec<(String, String)>>,
        visited: &mut HashSet<(String, String)>,
        path: &mut Vec<(String, String)>,
        cycles: &mut Vec<Vec<(String, String)>>,
    ) {
        // Skip if already visited
        if visited.contains(node) {
            return;
        }

        // Check if node is in current path (cycle)
        if let Some(pos) = path.iter().position(|n| n == node) {
            // Extract cycle
            let mut cycle = path[pos..].to_vec();
            cycle.push(node.clone());
            cycles.push(cycle);
            return;
        }

        // Add to path
        path.push(node.clone());
        visited.insert(node.clone());

        // Visit neighbors
        if let Some(neighbors) = graph.get(node) {
            for neighbor in neighbors {
                self.dfs_check_cycle(neighbor, graph, visited, path, cycles);
            }
        }

        // Remove from path
        path.pop();
    }

    /// Resolve dependencies in topological order
    pub fn resolve_dependencies(&self, category: OperatorCategory, name: &str) -> PyResult<Vec<(String, String)>> {
        let registry = self.registry.read().unwrap();

        // Check if operator exists
        if !registry.exists(category.clone(), name) {
            return Err(pyo3::exceptions::PyValueError::new_err(
                format!("Operator {}.{} not found", category, name)
            ));
        }

        // Build dependency graph
        let graph = self.build_dependency_graph();

        // Perform topological sort
        let mut result = Vec::new();
        let mut visited = HashSet::new();

        self.topological_sort(&(category.to_str(), name.to_string()), &graph, &mut visited, &mut result)?;

        Ok(result)
    }

    /// Topological sort
    fn topological_sort(
        &self,
        node: &(String, String),
        graph: &HashMap<(String, String), Vec<(String, String)>>,
        visited: &mut HashSet<(String, String)>,
        result: &mut Vec<(String, String)>,
    ) -> PyResult<()> {
        // Skip if already visited
        if visited.contains(node) {
            return Ok(());
        }

        // Add to visited
        visited.insert(node.clone());

        // Visit dependencies
        if let Some(deps) = graph.get(node) {
            for dep in deps {
                self.topological_sort(dep, graph, visited, result)?;
            }
        }

        // Add to result
        result.push(node.clone());

        Ok(())
    }
}

/// Python wrapper for DependencyManager
#[pyclass(name = "DependencyManager")]
pub struct PyDependencyManager {
    /// The inner manager
    pub manager: DependencyManager,
}

#[pymethods]
impl PyDependencyManager {
    /// Create a new dependency manager
    #[new]
    fn new(registry: &PyOperatorRegistry) -> Self {
        Self {
            manager: DependencyManager::new(registry.registry.clone()),
        }
    }

    /// Check if an operator's dependencies are satisfied
    fn check_dependencies(&self, category: &str, name: &str) -> PyResult<(bool, Vec<String>)> {
        self.manager.check_dependencies(OperatorCategory::from_str(category), name)
    }

    /// Get all operators that depend on an operator
    fn get_dependents(&self, category: &str, name: &str) -> Vec<(String, String)> {
        self.manager.get_dependents(OperatorCategory::from_str(category), name)
    }

    /// Build a dependency graph
    fn build_dependency_graph(&self) -> HashMap<(String, String), Vec<(String, String)>> {
        self.manager.build_dependency_graph()
    }

    /// Check for circular dependencies
    fn check_circular_dependencies(&self) -> Vec<Vec<(String, String)>> {
        self.manager.check_circular_dependencies()
    }

    /// Resolve dependencies in topological order
    fn resolve_dependencies(&self, category: &str, name: &str) -> PyResult<Vec<(String, String)>> {
        self.manager.resolve_dependencies(OperatorCategory::from_str(category), name)
    }

    /// String representation
    fn __str__(&self) -> String {
        "DependencyManager".to_string()
    }

    /// Representation
    fn __repr__(&self) -> String {
        "DependencyManager()".to_string()
    }
}

// Import PyOperatorRegistry
use super::registry::PyOperatorRegistry;
