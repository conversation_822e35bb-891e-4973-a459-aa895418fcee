/*
 * Operator Registry
 *
 * This module provides the core registry functionality.
 */

use pyo3::prelude::*;
use pyo3::types::{<PERSON>yD<PERSON>, <PERSON>yList, PyTuple};
use std::sync::{Arc, RwLock};
use std::collections::{HashMap, HashSet};
use std::fmt;
use serde::{Serialize, Deserialize};
use semver::{Version, VersionReq};
use log::{info, warn, error, debug};

use super::{OperatorCategory, OperatorMetadata};

/// Operator registry
pub struct OperatorRegistry {
    /// The operators
    pub operators: HashMap<OperatorCategory, HashMap<String, PyObject>>,
    /// The operator metadata
    pub metadata: HashMap<OperatorCategory, HashMap<String, OperatorMetadata>>,
}

impl OperatorRegistry {
    /// Create a new operator registry
    pub fn new() -> Self {
        Self {
            operators: HashMap::new(),
            metadata: HashMap::new(),
        }
    }

    /// Register an operator
    pub fn register(
        &mut self,
        category: OperatorCategory,
        name: &str,
        operator: PyObject,
        metadata: OperatorMetadata,
    ) -> PyResult<bool> {
        // Ensure category exists
        if !self.operators.contains_key(&category) {
            self.operators.insert(category.clone(), HashMap::new());
            self.metadata.insert(category.clone(), HashMap::new());
        }

        // Check if operator already exists
        let operators = self.operators.get_mut(&category).unwrap();
        let metadata_map = self.metadata.get_mut(&category).unwrap();

        if operators.contains_key(name) {
            warn!("Operator {}.{} already exists, will be overwritten", category, name);
        }

        // Register operator
        operators.insert(name.to_string(), operator);
        metadata_map.insert(name.to_string(), metadata);

        info!("Registered operator {}.{}", category, name);

        Ok(true)
    }

    /// Get an operator
    pub fn get(&self, category: OperatorCategory, name: &str) -> PyResult<Option<PyObject>> {
        // Check if category exists
        if let Some(operators) = self.operators.get(&category) {
            // Check if operator exists
            if let Some(operator) = operators.get(name) {
                return Ok(Some(operator.clone()));
            }
        }

        Ok(None)
    }

    /// Get operator metadata
    pub fn get_metadata(&self, category: OperatorCategory, name: &str) -> Option<&OperatorMetadata> {
        // Check if category exists
        if let Some(metadata_map) = self.metadata.get(&category) {
            // Check if operator exists
            return metadata_map.get(name);
        }

        None
    }

    /// List all operators
    pub fn list_all(&self) -> Vec<(String, String)> {
        let mut result = Vec::new();

        for (category, operators) in &self.operators {
            for name in operators.keys() {
                result.push((category.to_str(), name.clone()));
            }
        }

        result
    }

    /// List operators by category
    pub fn list_by_category(&self, category: OperatorCategory) -> Vec<(String, String)> {
        let mut result = Vec::new();

        if let Some(operators) = self.operators.get(&category) {
            for name in operators.keys() {
                result.push((category.to_str(), name.clone()));
            }
        }

        result
    }

    /// Check if an operator exists
    pub fn exists(&self, category: OperatorCategory, name: &str) -> bool {
        if let Some(operators) = self.operators.get(&category) {
            return operators.contains_key(name);
        }

        false
    }

    /// Remove an operator
    pub fn remove(&mut self, category: OperatorCategory, name: &str) -> bool {
        let mut removed = false;

        if let Some(operators) = self.operators.get_mut(&category) {
            if operators.remove(name).is_some() {
                removed = true;
            }
        }

        if let Some(metadata_map) = self.metadata.get_mut(&category) {
            if metadata_map.remove(name).is_some() {
                removed = true;
            }
        }

        if removed {
            info!("Removed operator {}.{}", category, name);
        }

        removed
    }

    /// Clear all operators
    pub fn clear(&mut self) {
        self.operators.clear();
        self.metadata.clear();

        info!("Cleared all operators");
    }

    /// Get all categories
    pub fn get_categories(&self) -> Vec<OperatorCategory> {
        self.operators.keys().cloned().collect()
    }

    /// Get all operators in a category
    pub fn get_operators_in_category(&self, category: OperatorCategory) -> Vec<String> {
        if let Some(operators) = self.operators.get(&category) {
            return operators.keys().cloned().collect();
        }

        Vec::new()
    }

    /// Get all operators with a tag
    pub fn get_operators_with_tag(&self, tag: &str) -> Vec<(String, String)> {
        let mut result = Vec::new();

        for (category, metadata_map) in &self.metadata {
            for (name, metadata) in metadata_map {
                if metadata.tags.contains(&tag.to_string()) {
                    result.push((category.to_str(), name.clone()));
                }
            }
        }

        result
    }

    /// Get all operators with a version
    pub fn get_operators_with_version(&self, version_req: &str) -> PyResult<Vec<(String, String)>> {
        let mut result = Vec::new();

        // Parse version requirement
        let req = VersionReq::parse(version_req)
            .map_err(|e| pyo3::exceptions::PyValueError::new_err(format!("Invalid version requirement: {}", e)))?;

        for (category, metadata_map) in &self.metadata {
            for (name, metadata) in metadata_map {
                // Parse operator version
                let version = Version::parse(&metadata.version)
                    .map_err(|e| pyo3::exceptions::PyValueError::new_err(format!("Invalid operator version: {}", e)))?;

                // Check compatibility
                if req.matches(&version) {
                    result.push((category.to_str(), name.clone()));
                }
            }
        }

        Ok(result)
    }

    /// Get all operators with a dependency
    pub fn get_operators_with_dependency(&self, dependency: &str, version_req: Option<&str>) -> PyResult<Vec<(String, String)>> {
        let mut result = Vec::new();

        // Parse version requirement if provided
        let req = if let Some(version_req) = version_req {
            Some(VersionReq::parse(version_req)
                .map_err(|e| pyo3::exceptions::PyValueError::new_err(format!("Invalid version requirement: {}", e)))?)
        } else {
            None
        };

        for (category, metadata_map) in &self.metadata {
            for (name, metadata) in metadata_map {
                // Check dependencies
                for (dep_name, dep_version) in &metadata.dependencies {
                    if dep_name == dependency {
                        // Check version if required
                        if let Some(req) = &req {
                            // Parse dependency version
                            let version = Version::parse(dep_version)
                                .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(format!("Invalid dependency version: {}", e)))?;

                            // Check compatibility
                            if req.matches(&version) {
                                result.push((category.to_str(), name.clone()));
                            }
                        } else {
                            result.push((category.to_str(), name.clone()));
                        }

                        break;
                    }
                }
            }
        }

        Ok(result)
    }
}

/// Python wrapper for OperatorRegistry
#[pyclass(name = "OperatorRegistry")]
pub struct PyOperatorRegistry {
    /// The inner registry
    pub registry: Arc<RwLock<OperatorRegistry>>,
}

#[pymethods]
impl PyOperatorRegistry {
    /// Create a new operator registry
    #[new]
    fn new() -> Self {
        Self {
            registry: Arc::new(RwLock::new(OperatorRegistry::new())),
        }
    }

    /// Register an operator
    #[pyo3(signature = (category, name, operator, version, description=None, tags=None, dependencies=None))]
    fn register(
        &self,
        category: &str,
        name: &str,
        operator: PyObject,
        version: &str,
        description: Option<&str>,
        tags: Option<Vec<String>>,
        dependencies: Option<Vec<(String, String)>>,
        py: Python<'_>,
    ) -> PyResult<bool> {
        let mut registry = self.registry.write().unwrap();

        // Create metadata
        let metadata = OperatorMetadata::new(
            name,
            version,
            description.unwrap_or(""),
            tags.unwrap_or_default(),
            dependencies.unwrap_or_default(),
        );

        // Register operator
        registry.register(
            OperatorCategory::from_str(category),
            name,
            operator,
            metadata,
        )
    }

    /// Get an operator
    fn get<'py>(&self, category: &str, name: &str, py: Python<'py>) -> PyResult<Option<PyObject>> {
        let registry = self.registry.read().unwrap();

        // Get operator
        registry.get(OperatorCategory::from_str(category), name)
    }

    /// List all operators
    fn list_all(&self) -> Vec<(String, String)> {
        let registry = self.registry.read().unwrap();

        // List operators
        registry.list_all()
    }

    /// List operators by category
    fn list_by_category(&self, category: &str) -> Vec<(String, String)> {
        let registry = self.registry.read().unwrap();

        // List operators
        registry.list_by_category(OperatorCategory::from_str(category))
    }

    /// Check if an operator exists
    fn exists(&self, category: &str, name: &str) -> bool {
        let registry = self.registry.read().unwrap();

        // Check if operator exists
        registry.exists(OperatorCategory::from_str(category), name)
    }

    /// Remove an operator
    fn remove(&self, category: &str, name: &str) -> bool {
        let mut registry = self.registry.write().unwrap();

        // Remove operator
        registry.remove(OperatorCategory::from_str(category), name)
    }

    /// Clear all operators
    fn clear(&self) {
        let mut registry = self.registry.write().unwrap();

        // Clear operators
        registry.clear();
    }

    /// Get all categories
    fn get_categories(&self) -> Vec<String> {
        let registry = self.registry.read().unwrap();

        // Get categories
        registry.get_categories().iter().map(|c| c.to_str()).collect()
    }

    /// Get all operators in a category
    fn get_operators_in_category(&self, category: &str) -> Vec<String> {
        let registry = self.registry.read().unwrap();

        // Get operators
        registry.get_operators_in_category(OperatorCategory::from_str(category))
    }

    /// Get all operators with a tag
    fn get_operators_with_tag(&self, tag: &str) -> Vec<(String, String)> {
        let registry = self.registry.read().unwrap();

        // Get operators
        registry.get_operators_with_tag(tag)
    }

    /// Get all operators with a version
    fn get_operators_with_version(&self, version_req: &str) -> PyResult<Vec<(String, String)>> {
        let registry = self.registry.read().unwrap();

        // Get operators
        registry.get_operators_with_version(version_req)
    }

    /// Get all operators with a dependency
    fn get_operators_with_dependency(&self, dependency: &str, version_req: Option<&str>) -> PyResult<Vec<(String, String)>> {
        let registry = self.registry.read().unwrap();

        // Get operators
        registry.get_operators_with_dependency(dependency, version_req)
    }

    /// String representation
    fn __str__(&self) -> String {
        let registry = self.registry.read().unwrap();

        // Get operator count
        let count = registry.list_all().len();

        format!("OperatorRegistry({} operators)", count)
    }

    /// Representation
    fn __repr__(&self) -> String {
        let registry = self.registry.read().unwrap();

        // Get operator count
        let count = registry.list_all().len();

        format!("OperatorRegistry({} operators)", count)
    }
}
