/*
 * Compatibility Checker
 *
 * This module provides compatibility checking for operators.
 */

use pyo3::prelude::*;
use pyo3::types::{<PERSON>yD<PERSON>, PyList, PyTuple};
use std::sync::{Arc, RwLock};
use std::collections::{HashMap, HashSet};
use std::fmt;
use serde::{Serialize, Deserialize};
use semver::{Version, VersionReq};
use log::{info, warn, error, debug};

use super::{OperatorCategory, OperatorMetadata, OperatorRegistry};

/// Compatibility level
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum CompatibilityLevel {
    /// Fully compatible
    Full,
    /// Partially compatible
    Partial,
    /// Not compatible
    None,
}

impl CompatibilityLevel {
    /// Convert to string
    pub fn to_str(&self) -> &'static str {
        match self {
            CompatibilityLevel::Full => "full",
            CompatibilityLevel::Partial => "partial",
            CompatibilityLevel::None => "none",
        }
    }
    
    /// Create from string
    pub fn from_str(s: &str) -> Self {
        match s.to_lowercase().as_str() {
            "full" => CompatibilityLevel::Full,
            "partial" => CompatibilityLevel::Partial,
            "none" => CompatibilityLevel::None,
            _ => CompatibilityLevel::None,
        }
    }
}

/// Compatibility checker
pub struct CompatibilityChecker {
    /// The registry
    pub registry: Arc<RwLock<OperatorRegistry>>,
    /// The compatibility rules
    pub rules: RwLock<HashMap<(String, String, String), CompatibilityLevel>>,
}

impl CompatibilityChecker {
    /// Create a new compatibility checker
    pub fn new(registry: Arc<RwLock<OperatorRegistry>>) -> Self {
        Self {
            registry,
            rules: RwLock::new(HashMap::new()),
        }
    }
    
    /// Add a compatibility rule
    pub fn add_rule(
        &self,
        category: OperatorCategory,
        name: &str,
        version_req: &str,
        level: CompatibilityLevel,
    ) -> PyResult<()> {
        // Parse version requirement
        let _ = VersionReq::parse(version_req)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(
                format!("Invalid version requirement: {}", e)
            ))?;
        
        // Add rule
        let mut rules = self.rules.write().unwrap();
        rules.insert((category.to_str(), name.to_string(), version_req.to_string()), level);
        
        Ok(())
    }
    
    /// Remove a compatibility rule
    pub fn remove_rule(
        &self,
        category: OperatorCategory,
        name: &str,
        version_req: &str,
    ) -> bool {
        let mut rules = self.rules.write().unwrap();
        rules.remove(&(category.to_str(), name.to_string(), version_req.to_string())).is_some()
    }
    
    /// Check compatibility
    pub fn check_compatibility(
        &self,
        category: OperatorCategory,
        name: &str,
        version_req: &str,
    ) -> PyResult<CompatibilityLevel> {
        let registry = self.registry.read().unwrap();
        
        // Get operator metadata
        if let Some(metadata) = registry.get_metadata(category.clone(), name) {
            // Parse version requirement
            let req = VersionReq::parse(version_req)
                .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(
                    format!("Invalid version requirement: {}", e)
                ))?;
            
            // Parse operator version
            let version = Version::parse(&metadata.version)
                .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(
                    format!("Invalid operator version: {}", e)
                ))?;
            
            // Check compatibility
            if req.matches(&version) {
                return Ok(CompatibilityLevel::Full);
            }
            
            // Check rules
            let rules = self.rules.read().unwrap();
            
            for ((rule_category, rule_name, rule_version_req), level) in &*rules {
                if rule_category == &category.to_str() && rule_name == name {
                    // Parse rule version requirement
                    let rule_req = VersionReq::parse(rule_version_req)
                        .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(
                            format!("Invalid rule version requirement: {}", e)
                        ))?;
                    
                    // Check if rule matches
                    if rule_req.matches(&version) {
                        return Ok(*level);
                    }
                }
            }
            
            // Default to not compatible
            Ok(CompatibilityLevel::None)
        } else {
            Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                format!("Operator {}.{} not found", category, name)
            ))
        }
    }
    
    /// Get all compatibility rules
    pub fn get_rules(&self) -> Vec<(String, String, String, String)> {
        let rules = self.rules.read().unwrap();
        
        rules.iter()
            .map(|((category, name, version_req), level)| {
                (category.clone(), name.clone(), version_req.clone(), level.to_str().to_string())
            })
            .collect()
    }
    
    /// Get compatibility rules for an operator
    pub fn get_rules_for_operator(
        &self,
        category: OperatorCategory,
        name: &str,
    ) -> Vec<(String, String, String)> {
        let rules = self.rules.read().unwrap();
        
        rules.iter()
            .filter(|((rule_category, rule_name, _), _)| {
                rule_category == &category.to_str() && rule_name == name
            })
            .map(|((_, _, version_req), level)| {
                (version_req.clone(), level.to_str().to_string(), "".to_string())
            })
            .collect()
    }
    
    /// Clear all compatibility rules
    pub fn clear_rules(&self) {
        let mut rules = self.rules.write().unwrap();
        rules.clear();
    }
}

/// Python wrapper for CompatibilityLevel
#[pyclass(name = "CompatibilityLevel")]
#[derive(Clone, Copy)]
pub struct PyCompatibilityLevel {
    /// The inner level
    pub level: CompatibilityLevel,
}

#[pymethods]
impl PyCompatibilityLevel {
    /// Create a new compatibility level
    #[new]
    fn new(level: &str) -> Self {
        Self {
            level: CompatibilityLevel::from_str(level),
        }
    }
    
    /// Convert to string
    fn __str__(&self) -> &'static str {
        self.level.to_str()
    }
    
    /// Representation
    fn __repr__(&self) -> String {
        format!("CompatibilityLevel.{}", self.level.to_str())
    }
    
    /// Full compatibility
    #[staticmethod]
    fn full() -> Self {
        Self {
            level: CompatibilityLevel::Full,
        }
    }
    
    /// Partial compatibility
    #[staticmethod]
    fn partial() -> Self {
        Self {
            level: CompatibilityLevel::Partial,
        }
    }
    
    /// No compatibility
    #[staticmethod]
    fn none() -> Self {
        Self {
            level: CompatibilityLevel::None,
        }
    }
}

/// Python wrapper for CompatibilityChecker
#[pyclass(name = "CompatibilityChecker")]
pub struct PyCompatibilityChecker {
    /// The inner checker
    pub checker: CompatibilityChecker,
}

#[pymethods]
impl PyCompatibilityChecker {
    /// Create a new compatibility checker
    #[new]
    fn new(registry: &PyOperatorRegistry) -> Self {
        Self {
            checker: CompatibilityChecker::new(registry.registry.clone()),
        }
    }
    
    /// Add a compatibility rule
    fn add_rule(
        &self,
        category: &str,
        name: &str,
        version_req: &str,
        level: &PyCompatibilityLevel,
    ) -> PyResult<()> {
        self.checker.add_rule(
            OperatorCategory::from_str(category),
            name,
            version_req,
            level.level,
        )
    }
    
    /// Remove a compatibility rule
    fn remove_rule(
        &self,
        category: &str,
        name: &str,
        version_req: &str,
    ) -> bool {
        self.checker.remove_rule(
            OperatorCategory::from_str(category),
            name,
            version_req,
        )
    }
    
    /// Check compatibility
    fn check_compatibility(
        &self,
        category: &str,
        name: &str,
        version_req: &str,
    ) -> PyResult<PyCompatibilityLevel> {
        let level = self.checker.check_compatibility(
            OperatorCategory::from_str(category),
            name,
            version_req,
        )?;
        
        Ok(PyCompatibilityLevel { level })
    }
    
    /// Get all compatibility rules
    fn get_rules(&self) -> Vec<(String, String, String, String)> {
        self.checker.get_rules()
    }
    
    /// Get compatibility rules for an operator
    fn get_rules_for_operator(
        &self,
        category: &str,
        name: &str,
    ) -> Vec<(String, String, String)> {
        self.checker.get_rules_for_operator(
            OperatorCategory::from_str(category),
            name,
        )
    }
    
    /// Clear all compatibility rules
    fn clear_rules(&self) {
        self.checker.clear_rules();
    }
    
    /// String representation
    fn __str__(&self) -> String {
        let rules = self.checker.get_rules();
        format!("CompatibilityChecker({} rules)", rules.len())
    }
    
    /// Representation
    fn __repr__(&self) -> String {
        let rules = self.checker.get_rules();
        format!("CompatibilityChecker({} rules)", rules.len())
    }
}

// Import PyOperatorRegistry
use super::registry::PyOperatorRegistry;
