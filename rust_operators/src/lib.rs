/*
 * Rust Operators for Transcendent Thinking Engine
 *
 * This library provides Rust implementations of various operators for the
 * Transcendent Thinking Engine, with Python bindings via PyO3.
 */

#[macro_use]
extern crate lazy_static;

use pyo3::prelude::*;

// Module declarations
pub mod version_adapter;
pub mod error_handler;
pub mod dynamic_morphism;
pub mod vr_interface;
pub mod numpy_ops;
pub mod parallel_computing;
pub mod operator_registry;

/// A simple function that adds two numbers
#[pyfunction]
fn add(a: i64, b: i64) -> PyResult<i64> {
    Ok(a + b)
}

/// A simple function that multiplies two numbers
#[pyfunction]
fn multiply(a: i64, b: i64) -> PyResult<i64> {
    Ok(a * b)
}

/// A simple class that represents a point
#[pyclass]
struct Point {
    x: f64,
    y: f64,
}

#[pymethods]
impl Point {
    /// Create a new point
    #[new]
    fn new(x: f64, y: f64) -> Self {
        Self { x, y }
    }

    /// Get the x coordinate
    #[getter]
    fn x(&self) -> f64 {
        self.x
    }

    /// Get the y coordinate
    #[getter]
    fn y(&self) -> f64 {
        self.y
    }

    /// Calculate the distance from the origin
    fn distance_from_origin(&self) -> f64 {
        (self.x * self.x + self.y * self.y).sqrt()
    }

    /// String representation
    fn __str__(&self) -> String {
        format!("Point({}, {})", self.x, self.y)
    }
}

/// Python module for Rust operators
#[pymodule]
fn rust_operators(py: Python<'_>, m: &PyModule) -> PyResult<()> {
    // Add basic functions and classes
    m.add_function(wrap_pyfunction!(add, m)?)?;
    m.add_function(wrap_pyfunction!(multiply, m)?)?;
    m.add_class::<Point>()?;

    // Add numpy_ops classes to the main module
    m.add_class::<numpy_ops::PyMathOps>()?;
    m.add_class::<numpy_ops::PyParallelOps>()?;
    m.add_class::<parallel_computing::PyParallelAlgorithms>()?;

    // Add module info
    m.setattr("__version__", "0.1.0")?;
    m.setattr("__author__", "Transcendent Thinking Engine Team")?;

    // Add submodules
    let nonlinear_interference = PyModule::new(py, "nonlinear_interference")?;
    let fractal_routing = PyModule::new(py, "fractal_routing")?;
    let game_scheduler = PyModule::new(py, "game_scheduler")?;

    // Register our new submodules
    let version_adapter_module = PyModule::new(py, "version_adapter")?;
    version_adapter::register_module(py, &version_adapter_module)?;

    let error_handler_module = PyModule::new(py, "error_handler")?;
    error_handler::register_module(py, &error_handler_module)?;

    let dynamic_morphism_module = PyModule::new(py, "dynamic_morphism")?;
    dynamic_morphism::register_module(py, &dynamic_morphism_module)?;

    let vr_interface_module = PyModule::new(py, "vr_interface")?;
    vr_interface::register_module(py, &vr_interface_module)?;

    let numpy_ops_module = PyModule::new(py, "numpy_ops")?;
    numpy_ops::register_module(py, &numpy_ops_module)?;

    let parallel_computing_module = PyModule::new(py, "parallel_computing")?;
    parallel_computing::register_module(py, &parallel_computing_module)?;

    let operator_registry_module = PyModule::new(py, "operator_registry")?;
    operator_registry::register_module(py, &operator_registry_module)?;

    // Add all the submodules to the main module
    m.add_submodule(&nonlinear_interference)?;
    m.add_submodule(&fractal_routing)?;
    m.add_submodule(&game_scheduler)?;
    m.add_submodule(&version_adapter_module)?;
    m.add_submodule(&error_handler_module)?;
    m.add_submodule(&dynamic_morphism_module)?;
    m.add_submodule(&vr_interface_module)?;
    m.add_submodule(&numpy_ops_module)?;
    m.add_submodule(&parallel_computing_module)?;
    m.add_submodule(&operator_registry_module)?;

    Ok(())
}
