/*
 * Arrow扩展类型库 - Rust实现
 *
 * 提供Arrow扩展类型的Rust实现，包括复数类型、量子态类型、张量类型和Parquet存储支持。
 */

// 导出模块
pub mod complex;
pub mod quantum_state;
pub mod tensor;
pub mod utils;
pub mod parquet;

// 重新导出主要类型
pub use complex::{Complex64Type, Complex128Type, ComplexArray};
pub use quantum_state::{QuantumStateType, QuantumStateArray};
pub use tensor::{TensorType, TensorArray};
pub use parquet::{ParquetStorageOptions, write_arrow_to_parquet, read_parquet_to_arrow};

// 版本信息
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

// Python模块定义
#[cfg(feature = "python")]
use pyo3::prelude::*;

#[cfg(feature = "python")]
#[pymodule]
fn tte_arrow_extensions(_py: Python, m: &PyModule) -> PyResult<()> {
    // 添加版本信息
    m.add("__version__", VERSION)?;
    
    // 注册复数类型
    complex::register_python_module(_py, m)?;
    
    // 注册量子态类型
    quantum_state::register_python_module(_py, m)?;
    
    // 注册张量类型
    tensor::register_python_module(_py, m)?;
    
    // 注册Parquet存储支持
    parquet::register_python_module(_py, m)?;
    
    // 注册工具函数
    utils::register_python_module(_py, m)?;
    
    Ok(())
}
