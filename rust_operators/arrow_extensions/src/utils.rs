/*
 * Arrow扩展工具函数 - Rust实现
 *
 * 提供Arrow扩展类型的通用工具函数，包括类型转换和验证。
 */

use std::sync::Arc;
use arrow::array::{Array, ArrayRef};
use arrow::datatypes::DataType;
use arrow_schema::ArrowError;
use thiserror::Error;

#[cfg(feature = "python")]
use pyo3::prelude::*;
#[cfg(feature = "python")]
use numpy::{PyArray, PyArrayDyn};

/// 错误类型
#[derive(Error, Debug)]
pub enum UtilsError {
    #[error("Arrow error: {0}")]
    Arrow(#[from] ArrowError),

    #[error("Invalid data type: expected {expected}, got {actual}")]
    InvalidDataType {
        expected: String,
        actual: String,
    },

    #[error("Validation error: {0}")]
    Validation(String),

    #[error("Python error: {0}")]
    Python(String),
}

/// 验证Arrow数组的数据类型
pub fn validate_array_type(array: &dyn Array, expected_type: &DataType) -> Result<(), UtilsError> {
    let actual_type = array.data_type();
    if actual_type != expected_type {
        return Err(UtilsError::InvalidDataType {
            expected: format!("{:?}", expected_type),
            actual: format!("{:?}", actual_type),
        });
    }

    Ok(())
}

/// 验证Arrow数组的长度
pub fn validate_array_length(array: &dyn Array, expected_length: usize) -> Result<(), UtilsError> {
    let actual_length = array.len();
    if actual_length != expected_length {
        return Err(UtilsError::Validation(
            format!("Array length mismatch: expected {}, got {}", expected_length, actual_length)
        ));
    }

    Ok(())
}

/// 检查Arrow数组是否包含空值
pub fn check_null_values(array: &dyn Array) -> bool {
    array.null_count() > 0
}

/// 创建具有指定类型和长度的空Arrow数组
pub fn create_empty_array(data_type: &DataType, length: usize) -> Result<ArrayRef, UtilsError> {
    match data_type {
        DataType::Int32 => {
            let array = arrow::array::Int32Array::from(vec![0i32; length]);
            Ok(Arc::new(array) as ArrayRef)
        },
        DataType::Int64 => {
            let array = arrow::array::Int64Array::from(vec![0i64; length]);
            Ok(Arc::new(array) as ArrayRef)
        },
        DataType::Float32 => {
            let array = arrow::array::Float32Array::from(vec![0.0f32; length]);
            Ok(Arc::new(array) as ArrayRef)
        },
        DataType::Float64 => {
            let array = arrow::array::Float64Array::from(vec![0.0f64; length]);
            Ok(Arc::new(array) as ArrayRef)
        },
        DataType::Struct(fields) => {
            // 为结构体的每个字段创建空数组
            let mut field_arrays = Vec::with_capacity(fields.len());
            for field in fields {
                let empty_array = create_empty_array(field.data_type(), length)?;
                field_arrays.push((field.clone(), empty_array));
            }

            let array = arrow::array::StructArray::from(field_arrays);
            Ok(Arc::new(array) as ArrayRef)
        },
        DataType::List(field) => {
            // 创建空的值数组
            let values = create_empty_array(field.data_type(), 0)?;

            // 创建空的偏移量数组
            // 创建全零偏移量数组
            let offsets_vec = vec![0i32; length + 1];
            let offsets = arrow::buffer::OffsetBuffer::<i32>::new(offsets_vec.into());

            // 创建ListArray
            let array = arrow::array::ListArray::try_new(
                field.clone(),
                offsets,
                values,
                None,
            )?;

            Ok(Arc::new(array) as ArrayRef)
        },
        _ => Err(UtilsError::Validation(
            format!("Unsupported data type for empty array: {:?}", data_type)
        )),
    }
}

/// 合并多个Arrow数组
pub fn concatenate_arrays(arrays: &[ArrayRef]) -> Result<ArrayRef, UtilsError> {
    if arrays.is_empty() {
        return Err(UtilsError::Validation("Cannot concatenate empty array list".to_string()));
    }

    // 检查所有数组的类型是否一致
    let first_type = arrays[0].data_type();
    for (i, array) in arrays.iter().enumerate().skip(1) {
        if array.data_type() != first_type {
            return Err(UtilsError::InvalidDataType {
                expected: format!("{:?}", first_type),
                actual: format!("{:?} at index {}", array.data_type(), i),
            });
        }
    }

    // 使用Arrow的concat函数合并数组
    let arrays_refs: Vec<&dyn arrow::array::Array> = arrays.iter().map(|a| a.as_ref() as &dyn arrow::array::Array).collect();
    let result = arrow::compute::concat(&arrays_refs)?;
    Ok(Arc::new(result))
}

/// 将Arrow数组切片为指定范围
pub fn slice_array(array: &ArrayRef, offset: usize, length: usize) -> Result<ArrayRef, UtilsError> {
    if offset + length > array.len() {
        return Err(UtilsError::Validation(
            format!("Slice range ({}, {}) exceeds array length {}",
                    offset, offset + length, array.len())
        ));
    }

    Ok(array.slice(offset, length))
}

/// Python模块注册
#[cfg(feature = "python")]
pub fn register_python_module(_py: Python, m: &PyModule) -> PyResult<()> {
    // 注册工具函数
    m.add_function(wrap_pyfunction!(check_array_null, m)?)?;
    m.add_function(wrap_pyfunction!(concatenate_arrow_arrays, m)?)?;

    Ok(())
}

/// 检查Arrow数组是否包含空值
#[cfg(feature = "python")]
#[pyfunction]
fn check_array_null(py: Python, array: PyObject) -> PyResult<bool> {
    // 注意：这里需要从PyArrow对象中提取Array，但由于我们没有直接的PyArrow绑定，
    // 这里只是示意性代码，实际实现需要使用PyArrow的API

    // 假设我们已经获取了Array
    let array = arrow::array::Int32Array::from(vec![1, 2, 3]);

    Ok(check_null_values(&array))
}

/// 合并多个Arrow数组
#[cfg(feature = "python")]
#[pyfunction]
fn concatenate_arrow_arrays(py: Python, arrays: Vec<PyObject>) -> PyResult<PyObject> {
    // 注意：这里需要从PyArrow对象中提取Array，但由于我们没有直接的PyArrow绑定，
    // 这里只是示意性代码，实际实现需要使用PyArrow的API

    // 假设我们已经获取了Array列表
    let array1 = Arc::new(arrow::array::Int32Array::from(vec![1, 2, 3])) as ArrayRef;
    let array2 = Arc::new(arrow::array::Int32Array::from(vec![4, 5, 6])) as ArrayRef;

    let arrays = vec![array1, array2];

    match concatenate_arrays(&arrays) {
        Ok(_) => Ok(py.None()),
        Err(e) => Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
            format!("Failed to concatenate arrays: {}", e)
        )),
    }
}
