/*
 * Arrow扩展类型的Parquet存储支持
 *
 * 提供将Arrow扩展类型存储到Parquet文件的功能，包括复数类型、量子态类型和张量类型。
 */

use std::sync::Arc;
use std::fs::File;
use std::path::Path;

use arrow_array::{Array, ArrayRef, RecordBatch, RecordBatchReader};
use arrow_schema::{Field, Schema, ArrowError};
use parquet::arrow::ArrowWriter;
use parquet::file::properties::WriterProperties;
use parquet::errors::ParquetError;
use thiserror::Error;

use crate::complex::ComplexArrayError;
use crate::quantum_state::QuantumStateError;
use crate::tensor::TensorError;

#[cfg(feature = "python")]
use pyo3::prelude::*;

/// 错误类型
#[derive(Error, Debug)]
pub enum ParquetStorageError {
    #[error("Arrow error: {0}")]
    Arrow(#[from] ArrowError),

    #[error("Parquet error: {0}")]
    Parquet(#[from] ParquetError),

    #[error("Complex array error: {0}")]
    ComplexArray(#[from] ComplexArrayError),

    #[error("Quantum state error: {0}")]
    QuantumState(#[from] QuantumStateError),

    #[error("Tensor error: {0}")]
    Tensor(#[from] TensorError),

    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),

    #[error("Invalid data: {0}")]
    InvalidData(String),

    #[error("Python error: {0}")]
    Python(String),
}

/// Parquet存储选项
#[derive(Debug, Clone)]
pub struct ParquetStorageOptions {
    /// 压缩类型
    pub compression: String,

    /// 写入批次大小
    pub batch_size: usize,

    /// 是否启用统计信息
    pub enable_statistics: bool,

    /// 是否启用字典编码
    pub enable_dictionary: bool,

    /// 最大行组大小（字节）
    pub max_row_group_size: usize,
}

impl Default for ParquetStorageOptions {
    fn default() -> Self {
        Self {
            compression: "snappy".to_string(),
            batch_size: 1024,
            enable_statistics: true,
            enable_dictionary: true,
            max_row_group_size: 1024 * 1024, // 1MB
        }
    }
}

/// 创建Parquet写入器属性
fn create_writer_properties(options: &ParquetStorageOptions) -> WriterProperties {
    let compression = match options.compression.as_str() {
        "snappy" => parquet::basic::Compression::SNAPPY,
        "gzip" => parquet::basic::Compression::GZIP(Default::default()),
        "lz4" => parquet::basic::Compression::LZ4,
        "zstd" => parquet::basic::Compression::ZSTD(Default::default()),
        _ => parquet::basic::Compression::UNCOMPRESSED,
    };

    let mut builder = WriterProperties::builder()
        .set_compression(compression)
        .set_max_row_group_size(options.max_row_group_size);

    if !options.enable_statistics {
        builder = builder.set_statistics_enabled(parquet::file::properties::EnabledStatistics::None);
    }

    if !options.enable_dictionary {
        builder = builder.set_dictionary_enabled(false);
    }

    builder.build()
}

/// 将Arrow数组写入Parquet文件
pub fn write_arrow_to_parquet<P: AsRef<Path>>(
    arrays: Vec<ArrayRef>,
    column_names: Vec<String>,
    file_path: P,
    options: Option<ParquetStorageOptions>,
) -> Result<(), ParquetStorageError> {
    // 验证输入
    if arrays.is_empty() {
        return Err(ParquetStorageError::InvalidData("Empty arrays".to_string()));
    }

    if arrays.len() != column_names.len() {
        return Err(ParquetStorageError::InvalidData(
            format!("Number of arrays ({}) does not match number of column names ({})",
                    arrays.len(), column_names.len())
        ));
    }

    // 创建Schema
    let fields: Vec<Field> = arrays.iter().zip(column_names.iter())
        .map(|(array, name)| Field::new(name, array.data_type().clone(), true))
        .collect();

    let schema = Schema::new(fields);
    let schema_ref = Arc::new(schema);

    // 创建RecordBatch
    let record_batch = RecordBatch::try_new(schema_ref.clone(), arrays)?;

    // 创建写入器属性
    let options = options.unwrap_or_default();
    let props = create_writer_properties(&options);

    // 创建文件
    let file = File::create(file_path)?;

    // 创建写入器
    let mut writer = ArrowWriter::try_new(file, schema_ref, Some(props))?;

    // 写入数据
    writer.write(&record_batch)?;

    // 完成写入
    writer.close()?;

    Ok(())
}

/// 从Parquet文件读取Arrow数组
pub fn read_parquet_to_arrow<P: AsRef<Path>>(
    file_path: P,
) -> Result<Vec<(String, ArrayRef)>, ParquetStorageError> {
    // 打开文件
    let file = File::open(file_path)?;

    // 创建读取器
    let reader = parquet::arrow::arrow_reader::ParquetRecordBatchReader::try_new(file, 1024)?;

    // 获取Schema
    let schema = reader.schema();

    let mut result = Vec::new();

    // 处理每个记录批次
    // 读取所有记录批次
    for batch_result in reader {
        let batch = batch_result?;

        // 提取每个列
        for i in 0..batch.num_columns() {
            let column_name = schema.field(i).name().clone();
            let column_array = batch.column(i).clone();

            result.push((column_name, column_array));
        }

        // 只处理第一个批次
        break;
    }

    Ok(result)
}

/// Python模块注册
#[cfg(feature = "python")]
pub fn register_python_module(_py: Python, m: &PyModule) -> PyResult<()> {
    // 注册ParquetStorageOptions类型
    m.add_class::<PyParquetStorageOptions>()?;

    // 注册函数
    m.add_function(wrap_pyfunction!(py_write_arrow_to_parquet, m)?)?;
    m.add_function(wrap_pyfunction!(py_read_parquet_to_arrow, m)?)?;

    Ok(())
}

/// Python ParquetStorageOptions类型
#[cfg(feature = "python")]
#[pyclass(name = "ParquetStorageOptions")]
pub struct PyParquetStorageOptions {
    inner: ParquetStorageOptions,
}

#[cfg(feature = "python")]
#[pymethods]
impl PyParquetStorageOptions {
    #[new]
    #[args(
        compression = "\"snappy\"",
        batch_size = "1024",
        enable_statistics = "true",
        enable_dictionary = "true",
        max_row_group_size = "1048576"
    )]
    fn new(
        compression: &str,
        batch_size: usize,
        enable_statistics: bool,
        enable_dictionary: bool,
        max_row_group_size: usize,
    ) -> Self {
        Self {
            inner: ParquetStorageOptions {
                compression: compression.to_string(),
                batch_size,
                enable_statistics,
                enable_dictionary,
                max_row_group_size,
            },
        }
    }

    #[getter]
    fn compression(&self) -> String {
        self.inner.compression.clone()
    }

    #[setter]
    fn set_compression(&mut self, value: &str) {
        self.inner.compression = value.to_string();
    }

    #[getter]
    fn batch_size(&self) -> usize {
        self.inner.batch_size
    }

    #[setter]
    fn set_batch_size(&mut self, value: usize) {
        self.inner.batch_size = value;
    }

    #[getter]
    fn enable_statistics(&self) -> bool {
        self.inner.enable_statistics
    }

    #[setter]
    fn set_enable_statistics(&mut self, value: bool) {
        self.inner.enable_statistics = value;
    }

    #[getter]
    fn enable_dictionary(&self) -> bool {
        self.inner.enable_dictionary
    }

    #[setter]
    fn set_enable_dictionary(&mut self, value: bool) {
        self.inner.enable_dictionary = value;
    }

    #[getter]
    fn max_row_group_size(&self) -> usize {
        self.inner.max_row_group_size
    }

    #[setter]
    fn set_max_row_group_size(&mut self, value: usize) {
        self.inner.max_row_group_size = value;
    }
}

/// Python函数：将Arrow数组写入Parquet文件
#[cfg(feature = "python")]
#[pyfunction]
fn py_write_arrow_to_parquet(
    _py: Python,
    arrays: Vec<PyObject>,
    column_names: Vec<String>,
    file_path: String,
    options: Option<&PyParquetStorageOptions>,
) -> PyResult<()> {
    // 注意：这里需要实际实现从PyArrow对象中提取Array，但由于我们没有直接的PyArrow绑定，
    // 这里只是示意性代码，实际实现需要使用PyArrow的API

    // 转换选项
    let rust_options = options.map(|o| o.inner.clone());

    // 调用Rust函数
    match write_arrow_to_parquet(
        Vec::new(), // 这里应该是从PyArrow对象中提取的Array
        column_names,
        file_path,
        rust_options,
    ) {
        Ok(_) => Ok(()),
        Err(e) => Err(PyErr::new::<pyo3::exceptions::PyIOError, _>(
            format!("Failed to write to Parquet: {}", e)
        )),
    }
}

/// Python函数：从Parquet文件读取Arrow数组
#[cfg(feature = "python")]
#[pyfunction]
fn py_read_parquet_to_arrow(
    py: Python,
    file_path: String,
) -> PyResult<PyObject> {
    // 调用Rust函数
    match read_parquet_to_arrow(file_path) {
        Ok(_) => {
            // 注意：这里需要实际实现将Array转换为PyArrow对象，但由于我们没有直接的PyArrow绑定，
            // 这里只是示意性代码，实际实现需要使用PyArrow的API
            Ok(py.None())
        },
        Err(e) => Err(PyErr::new::<pyo3::exceptions::PyIOError, _>(
            format!("Failed to read from Parquet: {}", e)
        )),
    }
}
