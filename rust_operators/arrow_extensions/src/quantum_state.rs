/*
 * Arrow量子态类型扩展 - Rust实现
 *
 * 提供量子态类型的Arrow表示，支持量子态的存储和操作。
 * 基于StructArray实现，将量子态表示为包含复数振幅的数组。
 */

use std::sync::Arc;
use arrow::array::{Array, StructArray, ListArray};
use arrow::datatypes::{DataType, Field};
use arrow_schema::ArrowError;
use num_complex::Complex64;
use thiserror::Error;

use crate::complex::{Complex128Type, ComplexArrayError};

#[cfg(feature = "python")]
use pyo3::prelude::*;
#[cfg(feature = "python")]
use numpy::{PyArray, PyArrayDyn, IntoPyArray, PyReadonlyArrayDyn};

/// 错误类型
#[derive(Error, Debug)]
pub enum QuantumStateError {
    #[error("Arrow error: {0}")]
    Arrow(#[from] ArrowError),

    #[error("Complex array error: {0}")]
    ComplexArray(#[from] ComplexArrayError),

    #[error("Invalid data type: expected {expected}, got {actual}")]
    InvalidDataType {
        expected: String,
        actual: String,
    },

    #[error("Invalid quantum state: {0}")]
    InvalidState(String),

    #[error("Python error: {0}")]
    Python(String),
}

/// 量子态类型
#[derive(Debug, Clone)]
pub struct QuantumStateType;

impl QuantumStateType {
    /// 创建新的QuantumStateType
    pub fn new() -> Self {
        Self {}
    }

    /// 获取存储类型
    pub fn storage_type() -> DataType {
        // 量子态是复数振幅的列表
        DataType::List(Arc::new(Field::new(
            "amplitudes",
            Complex128Type::storage_type(),
            false
        )))
    }

    /// 获取类型名称
    pub fn type_name() -> &'static str {
        "quantum_state"
    }

    /// 从复数振幅数组创建量子态
    pub fn from_complex_amplitudes(
        amplitudes: &[Complex64],
        check_normalization: bool
    ) -> Result<ListArray, QuantumStateError> {
        // 检查归一化
        if check_normalization {
            let norm_squared: f64 = amplitudes.iter().map(|c| c.norm_sqr()).sum();
            if (norm_squared - 1.0).abs() > 1e-10 {
                return Err(QuantumStateError::InvalidState(
                    format!("Quantum state is not normalized: norm = {}", norm_squared.sqrt())
                ));
            }
        }

        // 创建复数数组
        let complex_array = Complex128Type::from_complex64_slice(amplitudes)
            .map_err(QuantumStateError::ComplexArray)?;

        // 创建列表数组
        let field = Arc::new(Field::new("item", complex_array.data_type().clone(), true));
        let offsets = arrow::buffer::OffsetBuffer::<i32>::from_lengths([complex_array.len()]);
        let list_array = arrow::array::ListArray::new(
            field,
            offsets,
            Arc::new(complex_array.clone()),
            None
        );

        Ok(list_array)
    }

    /// 将ListArray转换为复数振幅数组
    pub fn to_complex_amplitudes(array: &ListArray) -> Result<Vec<Complex64>, QuantumStateError> {
        // 验证列表元素类型
        let value_type = array.value_type();
        if !matches!(value_type, DataType::Struct(_)) {
            return Err(QuantumStateError::InvalidDataType {
                expected: "Struct".to_string(),
                actual: format!("{:?}", value_type),
            });
        }

        // 获取值数组
        let values = array.values();
        let struct_array = values
            .as_any()
            .downcast_ref::<StructArray>()
            .ok_or_else(|| QuantumStateError::InvalidDataType {
                expected: "StructArray".to_string(),
                actual: format!("{:?}", values.data_type()),
            })?;

        // 转换为复数数组
        let complex_values = Complex128Type::to_complex64_vec(struct_array)
            .map_err(QuantumStateError::ComplexArray)?;

        Ok(complex_values)
    }

    /// 计算量子态的归一化系数
    pub fn calculate_normalization(amplitudes: &[Complex64]) -> f64 {
        let norm_squared: f64 = amplitudes.iter().map(|c| c.norm_sqr()).sum();
        norm_squared.sqrt()
    }

    /// 归一化量子态
    pub fn normalize_state(amplitudes: &mut [Complex64]) {
        let norm = Self::calculate_normalization(amplitudes);
        if norm > 0.0 {
            for amplitude in amplitudes.iter_mut() {
                *amplitude /= Complex64::new(norm, 0.0);
            }
        }
    }
}

/// 量子态数组接口
pub trait QuantumStateArray {
    /// 获取数组长度
    fn len(&self) -> usize;

    /// 检查数组是否为空
    fn is_empty(&self) -> bool {
        self.len() == 0
    }

    /// 获取量子态的维度（希尔伯特空间的维度）
    fn dimension(&self, index: usize) -> Result<usize, QuantumStateError>;

    /// 获取量子态的振幅
    fn amplitudes(&self, index: usize) -> Result<Vec<Complex64>, QuantumStateError>;

    /// 检查量子态是否归一化
    fn is_normalized(&self, index: usize, tolerance: f64) -> Result<bool, QuantumStateError> {
        let amplitudes = self.amplitudes(index)?;
        let norm_squared: f64 = amplitudes.iter().map(|c| c.norm_sqr()).sum();
        Ok((norm_squared - 1.0).abs() <= tolerance)
    }

    /// 获取列表数组
    fn list_array(&self) -> &ListArray;
}

/// Python模块注册
#[cfg(feature = "python")]
pub fn register_python_module(_py: Python, m: &PyModule) -> PyResult<()> {
    // 注册QuantumStateType类型
    m.add_class::<PyQuantumStateType>()?;

    // 注册转换函数
    m.add_function(wrap_pyfunction!(numpy_to_arrow_quantum_state, m)?)?;
    m.add_function(wrap_pyfunction!(arrow_to_numpy_quantum_state, m)?)?;

    Ok(())
}

/// Python QuantumStateType类型
#[cfg(feature = "python")]
#[pyclass(name = "QuantumStateType")]
pub struct PyQuantumStateType;

#[cfg(feature = "python")]
#[pymethods]
impl PyQuantumStateType {
    #[new]
    fn new() -> Self {
        Self
    }

    #[staticmethod]
    fn from_numpy(py: Python, array: &PyArrayDyn<f64>, check_normalization: bool) -> PyResult<PyObject> {
        // 检查数组形状
        let shape = array.shape();
        if shape.len() != 2 || shape[1] != 2 {
            return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                "Array must be of shape (n, 2) for complex amplitudes"
            ));
        }

        // 获取数组数据
        let array_readonly = array.readonly();
        let data = array_readonly.as_array();

        // 创建Complex64数组
        let mut complex_data = Vec::with_capacity(shape[0]);
        for i in 0..shape[0] {
            complex_data.push(Complex64::new(data[[i, 0]], data[[i, 1]]));
        }

        // 检查归一化
        if check_normalization {
            let norm_squared: f64 = complex_data.iter().map(|c| c.norm_sqr()).sum();
            if (norm_squared - 1.0).abs() > 1e-10 {
                return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                    format!("Quantum state is not normalized: norm = {}", norm_squared.sqrt())
                ));
            }
        }

        // 创建ListArray
        let list_array = match QuantumStateType::from_complex_amplitudes(&complex_data, check_normalization) {
            Ok(arr) => arr,
            Err(e) => return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                format!("Failed to create ListArray: {}", e)
            )),
        };

        // 返回Python对象
        // 注意：这里需要实际创建PyArrow对象，但由于我们没有直接的PyArrow绑定，
        // 这里只是示意性代码，实际实现需要使用PyArrow的API
        Ok(py.None())
    }

    #[staticmethod]
    fn to_numpy(py: Python, array: PyObject) -> PyResult<PyObject> {
        // 注意：这里需要从PyArrow对象中提取ListArray，但由于我们没有直接的PyArrow绑定，
        // 这里只是示意性代码，实际实现需要使用PyArrow的API

        // 假设我们已经获取了ListArray
        // 这里只是创建一个示例ListArray
        let complex_array = Complex128Type::from_complex64_slice(&[Complex64::new(0.0, 0.0)])
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(
                format!("Failed to create complex array: {}", e)
            ))?;

        let list_array = arrow::array::ListArray::from_iter_primitive(
            vec![(0..1)],
            |_| Ok(complex_array.column(0).clone()),
            |_| Ok(complex_array.column(1).clone()),
        );

        // 转换为复数数组
        let complex_data = match QuantumStateType::to_complex_amplitudes(&list_array) {
            Ok(data) => data,
            Err(e) => return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                format!("Failed to convert to complex amplitudes: {}", e)
            )),
        };

        // 创建NumPy数组
        let n = complex_data.len();
        let mut array_data = Vec::with_capacity(n * 2);
        for c in complex_data {
            array_data.push(c.re);
            array_data.push(c.im);
        }

        // 创建形状为(n, 2)的NumPy数组
        let array = unsafe {
            PyArray::from_data(
                py,
                &[n, 2],
                array_data.as_ptr(),
                array_data,
            )
        };

        Ok(array.to_object(py))
    }

    #[staticmethod]
    fn normalize(py: Python, array: &PyArrayDyn<f64>) -> PyResult<PyObject> {
        // 检查数组形状
        let shape = array.shape();
        if shape.len() != 2 || shape[1] != 2 {
            return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                "Array must be of shape (n, 2) for complex amplitudes"
            ));
        }

        // 获取数组数据
        let array_readonly = array.readonly();
        let data = array_readonly.as_array();

        // 创建Complex64数组
        let mut complex_data = Vec::with_capacity(shape[0]);
        for i in 0..shape[0] {
            complex_data.push(Complex64::new(data[[i, 0]], data[[i, 1]]));
        }

        // 归一化
        let mut normalized_data = complex_data.clone();
        QuantumStateType::normalize_state(&mut normalized_data);

        // 创建NumPy数组
        let n = normalized_data.len();
        let mut array_data = Vec::with_capacity(n * 2);
        for c in normalized_data {
            array_data.push(c.re);
            array_data.push(c.im);
        }

        // 创建形状为(n, 2)的NumPy数组
        let array = unsafe {
            PyArray::from_data(
                py,
                &[n, 2],
                array_data.as_ptr(),
                array_data,
            )
        };

        Ok(array.to_object(py))
    }
}

/// 将NumPy数组转换为Arrow QuantumState数组
#[cfg(feature = "python")]
#[pyfunction]
fn numpy_to_arrow_quantum_state(py: Python, array: &PyAny, check_normalization: bool) -> PyResult<PyObject> {
    // 这里需要实际实现NumPy到Arrow的转换
    // 由于缺少PyArrow绑定，这里只是示意性代码
    PyQuantumStateType::from_numpy(py, array.downcast()?, check_normalization)
}

/// 将Arrow QuantumState数组转换为NumPy数组
#[cfg(feature = "python")]
#[pyfunction]
fn arrow_to_numpy_quantum_state(py: Python, array: PyObject) -> PyResult<PyObject> {
    // 这里需要实际实现Arrow到NumPy的转换
    // 由于缺少PyArrow绑定，这里只是示意性代码
    PyQuantumStateType::to_numpy(py, array)
}
