#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
非线性干涉算法简单示例
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# 添加模块路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入非线性干涉模块
try:
    from nonlinear_interference import NonlinearInterferenceFFT, NonlinearFieldFunctionFFT
except ImportError:
    print("无法导入nonlinear_interference模块，请确保已编译")
    sys.exit(1)

def plot_state(state, title, filename=None):
    """绘制量子态的概率分布"""
    probabilities = np.abs(state)**2
    
    plt.figure(figsize=(10, 6))
    plt.bar(range(len(probabilities)), probabilities)
    plt.xlabel('状态索引')
    plt.ylabel('概率')
    plt.title(title)
    plt.grid(True, alpha=0.3)
    
    if filename:
        plt.savefig(filename)
        print(f"图像已保存为 {filename}")
    
    plt.show()

def main():
    """主函数"""
    print("非线性干涉算法示例")
    print("-" * 50)
    
    # 创建非线性干涉算子
    interference = NonlinearInterferenceFFT(
        lambda_param=0.5,
        fft_threshold=64
    )
    
    # 创建非线性场函数
    field_function = NonlinearFieldFunctionFFT(
        nonlinearity_type="quadratic",
        fft_threshold=64
    )
    
    # 创建测试状态
    size = 8
    
    # 状态1：|0⟩ + |1⟩
    state1 = np.zeros(size, dtype=complex)
    state1[0] = 1.0
    state1[1] = 1.0
    state1 /= np.sqrt(np.sum(np.abs(state1)**2))
    
    # 状态2：|1⟩ + |2⟩
    state2 = np.zeros(size, dtype=complex)
    state2[1] = 1.0
    state2[2] = 1.0
    state2 /= np.sqrt(np.sum(np.abs(state2)**2))
    
    print(f"状态1: {state1}")
    print(f"状态2: {state2}")
    
    # 使用不同的lambda参数融合状态
    lambda_values = [0.0, 0.25, 0.5, 0.75, 1.0]
    
    print("\n使用不同的lambda参数融合状态:")
    for lambda_param in lambda_values:
        fused_state = interference.fuse(state1, state2, lambda_param=lambda_param)
        probabilities = np.abs(fused_state)**2
        
        print(f"lambda = {lambda_param}:")
        print(f"  融合状态: {fused_state}")
        print(f"  概率分布: {probabilities}")
        
        # 计算熵损失
        entropy_loss = interference.evaluate_entropy_loss(state1, state2, fused_state)
        print(f"  熵损失: {entropy_loss}")
        print()
    
    # 自适应融合
    print("\n自适应融合:")
    fused_state, optimal_lambda = interference.adaptive_fuse(
        state1, state2, max_iterations=20, tolerance=1e-6
    )
    
    print(f"最优lambda参数: {optimal_lambda}")
    print(f"自适应融合状态: {fused_state}")
    print(f"概率分布: {np.abs(fused_state)**2}")
    
    # 应用非线性场函数
    print("\n应用非线性场函数:")
    
    # 尝试不同的非线性类型
    nonlinearity_types = ["quadratic", "cubic", "exponential"]
    
    for nonlinearity_type in nonlinearity_types:
        field_function.nonlinearity_type = nonlinearity_type
        transformed_state = field_function.apply(state1)
        
        print(f"{nonlinearity_type}非线性:")
        print(f"  变换后状态: {transformed_state}")
        print(f"  概率分布: {np.abs(transformed_state)**2}")
        print()
    
    # 可视化
    try:
        # 绘制原始状态
        plot_state(state1, "状态1的概率分布", "state1.png")
        plot_state(state2, "状态2的概率分布", "state2.png")
        
        # 绘制融合状态
        plot_state(fused_state, f"融合状态的概率分布 (λ = {optimal_lambda:.2f})", "fused_state.png")
        
        # 绘制非线性场函数变换后的状态
        field_function.nonlinearity_type = "quadratic"
        transformed_state = field_function.apply(state1)
        plot_state(transformed_state, "二次非线性变换后的概率分布", "transformed_state.png")
    except Exception as e:
        print(f"可视化失败: {e}")

if __name__ == "__main__":
    main()
