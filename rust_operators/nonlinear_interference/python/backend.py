"""非线性干涉算法的Python后备实现"""
import numpy as np

class NonlinearInterference:
    def __init__(self, lambda_param=0.5, fft_threshold=64):
        self.lambda_param = lambda_param
        self.fft_threshold = fft_threshold
            
    def fuse(self, state1, state2, lambda_param=None):
        if lambda_param is None:
            lambda_param = self.lambda_param
            
        if len(state1) >= self.fft_threshold and self._is_power_of_two(len(state1)):
            return self._fuse_fft(state1, state2, lambda_param)
        else:
            return self._fuse_direct(state1, state2, lambda_param)
    
    def _fuse_direct(self, state1, state2, lambda_param):
        # 计算内积
        inner_product = np.vdot(state1, state2)
        # 非线性干涉
        result = state1 + state2 + lambda_param * inner_product * state1 * state2
        # 归一化
        result = result / np.linalg.norm(result)
        return result
    
    def _fuse_fft(self, state1, state2, lambda_param):
        # FFT变换
        fft1 = np.fft.fft(state1)
        fft2 = np.fft.fft(state2)
        
        # 计算时域内积
        inner_product = np.vdot(state1, state2)
        
        # 频域合并
        fft_result = fft1 + fft2 + lambda_param * inner_product * fft1 * fft2
        
        # 逆FFT
        result = np.fft.ifft(fft_result)
        
        # 归一化
        result = result / np.linalg.norm(result)
        return result
    
    def _is_power_of_two(self, n):
        return (n != 0) and (n & (n - 1) == 0)

class NonlinearFieldFunction:
    def __init__(self, nonlinearity_type="quadratic", fft_threshold=64):
        self.nonlinearity_type = nonlinearity_type
        self.fft_threshold = fft_threshold
        
    def apply(self, state):
        if self.nonlinearity_type == "quadratic":
            return self._apply_quadratic(state)
        elif self.nonlinearity_type == "cubic":
            return self._apply_cubic(state)
        elif self.nonlinearity_type == "exponential":
            return self._apply_exponential(state)
        else:
            raise ValueError(f"不支持的非线性类型: {self.nonlinearity_type}")
    
    def _apply_quadratic(self, state):
        abs_squared = np.abs(state) ** 2
        result = abs_squared * state
        return result / np.linalg.norm(result)
    
    def _apply_cubic(self, state):
        abs_squared = np.abs(state) ** 2
        result = (abs_squared ** 2) * state
        return result / np.linalg.norm(result)
    
    def _apply_exponential(self, state):
        abs_squared = np.abs(state) ** 2
        result = np.exp(abs_squared) * state
        return result / np.linalg.norm(result)

__all__ = ['NonlinearInterference', 'NonlinearFieldFunction']