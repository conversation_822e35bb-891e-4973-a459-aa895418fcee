"""非线性干涉算法模块"""
import sys
import os
from pathlib import Path

# 将编译后的动态库目录添加到路径中
lib_path = Path(__file__).parent.parent.parent / "target" / "release"
if lib_path.exists():
    if sys.platform == "win32":
        os.add_dll_directory(str(lib_path))
    sys.path.append(str(lib_path))

try:
    from nonlinear_interference import NonlinearInterferenceFFT as NonlinearInterference
    from nonlinear_interference import NonlinearFieldFunctionFFT as NonlinearFieldFunction
except ImportError as e:
    print(f"警告：无法导入非线性干涉算法模块: {e}")
    print("使用Python实现作为后备方案")
    
    class NonlinearInterference:
        def __init__(self, lambda_param=0.5, fft_threshold=64):
            self.lambda_param = lambda_param
            self.fft_threshold = fft_threshold
            
        def fuse(self, state1, state2, lambda_param=None):
            import numpy as np
            if lambda_param is None:
                lambda_param = self.lambda_param
            # 简单的线性叠加作为后备实现
            result = state1 + state2 + lambda_param * state1 * state2
            # 归一化
            result = result / np.linalg.norm(result)
            return result

__all__ = ['NonlinearInterference', 'NonlinearFieldFunction']