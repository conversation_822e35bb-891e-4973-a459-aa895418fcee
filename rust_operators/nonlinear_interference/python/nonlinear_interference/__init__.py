"""
非线性干涉算法模块

该模块实现了基于非线性干涉的量子态融合算法，用于超越态思维引擎中的信息处理和状态演化。
算法结合了量子力学、非线性动力学和快速傅里叶变换（FFT）技术，实现高效、自适应的状态融合和演化功能。
"""

import os
import sys

# 尝试导入Rust模块
try:
    # 获取当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 查找.so文件
    so_files = [f for f in os.listdir(current_dir) if f.endswith('.so')]
    
    if so_files:
        # 如果找到.so文件，导入第一个
        so_file = so_files[0]
        so_path = os.path.join(current_dir, so_file)
        
        # 将.so文件所在目录添加到sys.path
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # 导入模块
        from .nonlinear_interference import NonlinearInterferenceFFT, NonlinearFieldFunctionFFT
        
        # 导出类
        __all__ = ["NonlinearInterferenceFFT", "NonlinearFieldFunctionFFT"]
        
    else:
        # 如果没有找到.so文件，打印错误信息
        import sys
        print(f"无法找到nonlinear_interference模块的.so文件，请确保已编译。搜索目录: {current_dir}", file=sys.stderr)
        __all__ = []
        
except ImportError as e:
    # 如果导入失败，打印错误信息
    import sys
    print(f"无法导入nonlinear_interference模块: {e}，请确保已编译", file=sys.stderr)
    __all__ = []
