# 非线性干涉算法 (Nonlinear Interference Algorithm)

该模块实现了基于非线性干涉的量子态融合算法，用于超越态思维引擎中的信息处理和状态演化。算法结合了量子力学、非线性动力学和快速傅里叶变换（FFT）技术，实现高效、自适应的状态融合和演化功能。

## 特性

- 基于非线性干涉的量子态融合
- 支持多种非线性场函数：二次、三次、指数
- 使用FFT加速大规模计算
- 自适应参数优化
- 熵损失评估
- 支持Rust实现，提供Python绑定

## 安装

### 依赖

- Python 3.13+
- Rust 1.75+
- PyO3 0.24+
- NumPy 1.24+
- Matplotlib (可选，用于可视化)

### 编译

```bash
cd /path/to/nonlinear_interference
cargo build --release
```

## 使用

### 基本用法

```python
import numpy as np
from nonlinear_interference import NonlinearInterferenceFFT, NonlinearFieldFunctionFFT

# 创建非线性干涉算子
interference = NonlinearInterferenceFFT(
    lambda_param=0.5,
    fft_threshold=64
)

# 创建非线性场函数
field_function = NonlinearFieldFunctionFFT(
    nonlinearity_type="quadratic",
    fft_threshold=64
)

# 创建测试状态
state1 = np.array([complex(1.0, 0.0), complex(0.0, 0.0)]) / np.sqrt(1.0)
state2 = np.array([complex(0.0, 0.0), complex(1.0, 0.0)]) / np.sqrt(1.0)

# 融合状态
fused_state = interference.fuse(state1, state2)
print(f"Fused state: {fused_state}")

# 计算熵损失
entropy_loss = interference.evaluate_entropy_loss(state1, state2, fused_state)
print(f"Entropy loss: {entropy_loss}")

# 自适应融合
fused_state, optimal_lambda = interference.adaptive_fuse(
    state1, state2, max_iterations=20, tolerance=1e-6
)
print(f"Optimal lambda: {optimal_lambda}")

# 应用非线性场函数
transformed_state = field_function.apply(state1)
print(f"Transformed state: {transformed_state}")
```

## 示例

查看 `examples` 目录中的示例脚本：

- `simple_interference.py`: 简单的非线性干涉示例，包括可视化

## 测试

运行测试：

```bash
cd /path/to/nonlinear_interference
python tests/test_nonlinear_interference.py
```

## 算法原理

非线性干涉算法基于以下原理：

1. **量子态融合**: 通过非线性干涉将两个量子态融合为一个新的量子态。
2. **非线性场函数**: 使用非线性场函数对量子态进行变换，实现非线性演化。
3. **FFT加速**: 使用快速傅里叶变换加速大规模计算，提高算法效率。
4. **自适应参数优化**: 通过优化参数λ，最小化熵损失，实现最优融合。

### 融合方程

基本融合方程：

```
ψ_fused = ψ₁ + ψ₂ + λ⟨ψ₁|ψ₂⟩ψ₁ψ₂
```

其中：
- ψ₁, ψ₂ 是输入量子态
- λ 是干涉强度参数
- ⟨ψ₁|ψ₂⟩ 是内积

### 非线性场函数

支持多种非线性场函数：

1. **二次非线性**: F(ψ) = |ψ|²ψ
2. **三次非线性**: F(ψ) = |ψ|⁴ψ
3. **指数非线性**: F(ψ) = exp(|ψ|²)ψ

### 统一演化方程

统一演化方程：

```
ψ_t+1 = E(ψ_t) = ψ_t + λ(t)·F(ψ_t)
```

其中：
- E 是统一演化算子
- F 是非线性场函数
- λ(t) 是自适应参数

## 性能指标

- **熵损失**: 衡量融合过程中的信息损失。
- **计算复杂度**: 直接计算 O(n)，FFT计算 O(n log n)。
- **融合质量**: 通过自适应参数优化，最大化融合质量。

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。
