#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
非线性干涉算法测试
"""

import sys
import os
import unittest
import numpy as np

# 添加模块路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入非线性干涉模块
try:
    from nonlinear_interference import NonlinearInterferenceFFT, NonlinearFieldFunctionFFT
except ImportError:
    print("无法导入nonlinear_interference模块，请确保已编译")
    sys.exit(1)

class TestNonlinearInterference(unittest.TestCase):
    """测试非线性干涉算法"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建非线性干涉算子
        self.interference = NonlinearInterferenceFFT(
            lambda_param=0.5,
            fft_threshold=64
        )
        
        # 创建非线性场函数
        self.field_function = NonlinearFieldFunctionFFT(
            nonlinearity_type="quadratic",
            fft_threshold=64
        )
        
        # 创建测试状态
        self.state1 = np.array([complex(1.0, 0.0), complex(0.0, 0.0)]) / np.sqrt(1.0)
        self.state2 = np.array([complex(0.0, 0.0), complex(1.0, 0.0)]) / np.sqrt(1.0)
        
        # 创建大型测试状态（用于FFT测试）
        size = 128
        self.large_state1 = np.zeros(size, dtype=complex)
        self.large_state2 = np.zeros(size, dtype=complex)
        
        # 设置一些非零值
        self.large_state1[0] = 1.0
        self.large_state1[1] = 0.5
        self.large_state2[1] = 0.5
        self.large_state2[2] = 1.0
        
        # 归一化
        self.large_state1 /= np.sqrt(np.sum(np.abs(self.large_state1)**2))
        self.large_state2 /= np.sqrt(np.sum(np.abs(self.large_state2)**2))
    
    def test_fuse(self):
        """测试fuse方法"""
        # 融合状态
        fused_state = self.interference.fuse(self.state1, self.state2)
        
        # 验证结果
        self.assertIsInstance(fused_state, np.ndarray)
        self.assertEqual(fused_state.shape, self.state1.shape)
        
        # 验证归一化
        norm = np.sum(np.abs(fused_state)**2)
        self.assertAlmostEqual(norm, 1.0, places=6)
        
        # 打印结果
        print(f"Fused state: {fused_state}")
    
    def test_fuse_fft(self):
        """测试使用FFT的融合"""
        # 融合大型状态
        fused_state = self.interference.fuse(self.large_state1, self.large_state2)
        
        # 验证结果
        self.assertIsInstance(fused_state, np.ndarray)
        self.assertEqual(fused_state.shape, self.large_state1.shape)
        
        # 验证归一化
        norm = np.sum(np.abs(fused_state)**2)
        self.assertAlmostEqual(norm, 1.0, places=6)
        
        # 打印结果
        print(f"Fused state (FFT) shape: {fused_state.shape}")
    
    def test_evaluate_entropy_loss(self):
        """测试熵损失评估"""
        # 融合状态
        fused_state = self.interference.fuse(self.state1, self.state2)
        
        # 计算熵损失
        entropy_loss = self.interference.evaluate_entropy_loss(
            self.state1, self.state2, fused_state
        )
        
        # 验证结果
        self.assertIsInstance(entropy_loss, float)
        
        # 打印结果
        print(f"Entropy loss: {entropy_loss}")
    
    def test_adaptive_fuse(self):
        """测试自适应融合"""
        # 自适应融合
        fused_state, optimal_lambda = self.interference.adaptive_fuse(
            self.state1, self.state2, max_iterations=10, tolerance=1e-6
        )
        
        # 验证结果
        self.assertIsInstance(fused_state, np.ndarray)
        self.assertEqual(fused_state.shape, self.state1.shape)
        self.assertIsInstance(optimal_lambda, float)
        self.assertTrue(0.0 <= optimal_lambda <= 1.0)
        
        # 验证归一化
        norm = np.sum(np.abs(fused_state)**2)
        self.assertAlmostEqual(norm, 1.0, places=6)
        
        # 打印结果
        print(f"Adaptive fused state: {fused_state}")
        print(f"Optimal lambda: {optimal_lambda}")
    
    def test_nonlinear_field_function(self):
        """测试非线性场函数"""
        # 应用非线性场函数
        transformed_state = self.field_function.apply(self.state1)
        
        # 验证结果
        self.assertIsInstance(transformed_state, np.ndarray)
        self.assertEqual(transformed_state.shape, self.state1.shape)
        
        # 验证归一化
        norm = np.sum(np.abs(transformed_state)**2)
        self.assertAlmostEqual(norm, 1.0, places=6)
        
        # 打印结果
        print(f"Transformed state: {transformed_state}")
    
    def test_nonlinear_field_function_fft(self):
        """测试使用FFT的非线性场函数"""
        # 应用非线性场函数到大型状态
        transformed_state = self.field_function.apply(self.large_state1)
        
        # 验证结果
        self.assertIsInstance(transformed_state, np.ndarray)
        self.assertEqual(transformed_state.shape, self.large_state1.shape)
        
        # 验证归一化
        norm = np.sum(np.abs(transformed_state)**2)
        self.assertAlmostEqual(norm, 1.0, places=6)
        
        # 打印结果
        print(f"Transformed state (FFT) shape: {transformed_state.shape}")

if __name__ == "__main__":
    unittest.main()
