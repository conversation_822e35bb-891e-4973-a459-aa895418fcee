//! Nonlinear Interference Algorithm with FFT Acceleration
//!
//! This module implements the Nonlinear Interference Algorithm in Rust with FFT acceleration.
//! It extends nonlinear interference as an intrinsic evolution mechanism for transcendent states,
//! rather than an external connection tool.
//! Unified evolution equation: ψ_t+1 = E(ψ_t) = ψ_t + λ(t)·F(ψ_t)
//! where E is the unified evolution operator, F is the nonlinear field function,
//! and λ(t) is the adaptive parameter.

use num_complex::Complex64;
use pyo3::prelude::*;
use rustfft::{FftPlanner, num_complex::Complex as RustComplex};
use numpy::{PyArray1, IntoPyArray, PyReadonlyArray1};

mod utils;

/// Nonlinear Interference Operator with FFT Acceleration
///
/// This operator implements the nonlinear interference algorithm that combines
/// transcendental states through nonlinear interference, with FFT acceleration for large-scale computations.
#[pyclass]
pub struct NonlinearInterferenceFFT {
    /// Interference strength parameter
    #[pyo3(get, set)]
    lambda_param: f64,

    /// Threshold for switching between direct computation and FFT
    #[pyo3(get, set)]
    fft_threshold: usize,
}

#[pymethods]
impl NonlinearInterferenceFFT {
    /// Create a new NonlinearInterferenceFFT
    #[new]
    #[pyo3(signature = (lambda_param=0.5, fft_threshold=64))]
    fn new(lambda_param: f64, fft_threshold: usize) -> Self {
        Self {
            lambda_param,
            fft_threshold,
        }
    }

    /// Fuse two states using nonlinear interference
    ///
    /// Args:
    ///     state1: First state as complex array
    ///     state2: Second state as complex array
    ///
    /// Returns:
    ///     Fused state as complex array
    #[pyo3(signature = (state1, state2, lambda_param=None))]
    fn fuse<'py>(
        &self,
        py: Python<'py>,
        state1: PyReadonlyArray1<Complex64>,
        state2: PyReadonlyArray1<Complex64>,
        lambda_param: Option<f64>,
    ) -> PyResult<Py<PyArray1<Complex64>>> {
        // Get lambda parameter (use provided or default)
        let lambda = lambda_param.unwrap_or(self.lambda_param);

        // Convert to Rust arrays
        let state1_array = state1.as_array();
        let state2_array = state2.as_array();

        // Check dimensions
        if state1_array.len() != state2_array.len() {
            return Err(pyo3::exceptions::PyValueError::new_err(
                format!("States must have the same dimension, got {} and {}",
                        state1_array.len(), state2_array.len())
            ));
        }

        // Convert numpy arrays to Vec for our methods
        let state1_vec: Vec<Complex64> = state1_array.iter().copied().collect();
        let state2_vec: Vec<Complex64> = state2_array.iter().copied().collect();

        // Choose the appropriate method based on size
        let result = if state1_array.len() <= self.fft_threshold || !state1_array.len().is_power_of_two() {
            // For small states or non-power-of-2 sizes, use direct computation
            self.fuse_direct(&state1_vec, &state2_vec, lambda)
        } else {
            // For larger states that are a power of 2, use FFT-based computation
            self.fuse_fft(&state1_vec, &state2_vec, lambda)
        };

        // Convert result to numpy array
        Ok(result.into_pyarray(py).into())
    }

    /// Evaluate entropy loss for the fusion
    ///
    /// Args:
    ///     state1: First state as complex array
    ///     state2: Second state as complex array
    ///     fused_state: Fused state as complex array
    ///
    /// Returns:
    ///     Entropy loss as float
    fn evaluate_entropy_loss<'py>(
        &self,
        _py: Python<'py>,
        state1: PyReadonlyArray1<Complex64>,
        state2: PyReadonlyArray1<Complex64>,
        fused_state: PyReadonlyArray1<Complex64>,
    ) -> PyResult<f64> {
        // Convert to Rust arrays
        let state1_array = state1.as_array();
        let state2_array = state2.as_array();
        let fused_array = fused_state.as_array();

        // Check dimensions
        if state1_array.len() != state2_array.len() || state1_array.len() != fused_array.len() {
            return Err(pyo3::exceptions::PyValueError::new_err(
                "All states must have the same dimension"
            ));
        }

        // Convert to Vec for our utility functions
        let state1_vec: Vec<Complex64> = state1_array.iter().copied().collect();
        let state2_vec: Vec<Complex64> = state2_array.iter().copied().collect();
        let fused_vec: Vec<Complex64> = fused_array.iter().copied().collect();

        // Calculate entropy of each state
        let entropy1 = utils::calculate_entropy(&state1_vec);
        let entropy2 = utils::calculate_entropy(&state2_vec);
        let entropy_fused = utils::calculate_entropy(&fused_vec);

        // Calculate entropy loss
        let entropy_loss = entropy1 + entropy2 - entropy_fused;

        Ok(entropy_loss)
    }

    /// Adaptively fuse two states by finding optimal lambda parameter
    ///
    /// Args:
    ///     state1: First state as complex array
    ///     state2: Second state as complex array
    ///     max_iterations: Maximum number of iterations for optimization
    ///     tolerance: Convergence tolerance
    ///
    /// Returns:
    ///     Tuple of (fused_state, optimal_lambda)
    #[pyo3(signature = (state1, state2, max_iterations=20, tolerance=1e-6))]
    fn adaptive_fuse<'py>(
        &self,
        py: Python<'py>,
        state1: PyReadonlyArray1<Complex64>,
        state2: PyReadonlyArray1<Complex64>,
        max_iterations: usize,
        tolerance: f64,
    ) -> PyResult<(Py<PyArray1<Complex64>>, f64)> {
        // Convert to Rust arrays
        let state1_array = state1.as_array();
        let state2_array = state2.as_array();

        // Check dimensions
        if state1_array.len() != state2_array.len() {
            return Err(pyo3::exceptions::PyValueError::new_err(
                "States must have the same dimension"
            ));
        }

        // Convert to Vec for our methods
        let state1_vec: Vec<Complex64> = state1_array.iter().copied().collect();
        let state2_vec: Vec<Complex64> = state2_array.iter().copied().collect();

        // Initial lambda values to try
        let lambda_values = [0.0, 0.25, 0.5, 0.75, 1.0];

        // Try different lambda values and find the one with minimum entropy loss
        let mut best_lambda = 0.5;
        let mut best_entropy_loss = f64::MAX;
        let mut best_fused_state = Vec::new();

        for &lambda in &lambda_values {
            let fused_state = if state1_array.len() <= self.fft_threshold || !state1_array.len().is_power_of_two() {
                self.fuse_direct(&state1_vec, &state2_vec, lambda)
            } else {
                self.fuse_fft(&state1_vec, &state2_vec, lambda)
            };

            let entropy1 = utils::calculate_entropy(&state1_vec);
            let entropy2 = utils::calculate_entropy(&state2_vec);
            let entropy_fused = utils::calculate_entropy(&fused_state);

            let entropy_loss = entropy1 + entropy2 - entropy_fused;

            if entropy_loss < best_entropy_loss {
                best_entropy_loss = entropy_loss;
                best_lambda = lambda;
                best_fused_state = fused_state;
            }
        }

        // Refine the lambda parameter using gradient descent
        let mut current_lambda = best_lambda;
        let mut current_entropy_loss = best_entropy_loss;
        let mut current_fused_state = best_fused_state.clone();

        for _ in 0..max_iterations {
            // Try small perturbations in lambda
            let delta = 0.05;
            let lambda_plus = (current_lambda + delta).min(1.0);
            let lambda_minus = (current_lambda - delta).max(0.0);

            // Compute fused states and entropy losses
            let fused_plus = if state1_array.len() <= self.fft_threshold || !state1_array.len().is_power_of_two() {
                self.fuse_direct(&state1_vec, &state2_vec, lambda_plus)
            } else {
                self.fuse_fft(&state1_vec, &state2_vec, lambda_plus)
            };

            let fused_minus = if state1_array.len() <= self.fft_threshold || !state1_array.len().is_power_of_two() {
                self.fuse_direct(&state1_vec, &state2_vec, lambda_minus)
            } else {
                self.fuse_fft(&state1_vec, &state2_vec, lambda_minus)
            };

            let entropy1 = utils::calculate_entropy(&state1_vec);
            let entropy2 = utils::calculate_entropy(&state2_vec);
            let entropy_plus = utils::calculate_entropy(&fused_plus);
            let entropy_minus = utils::calculate_entropy(&fused_minus);

            let loss_plus = entropy1 + entropy2 - entropy_plus;
            let loss_minus = entropy1 + entropy2 - entropy_minus;

            // Update lambda based on gradient
            if loss_plus < current_entropy_loss && loss_plus <= loss_minus {
                current_lambda = lambda_plus;
                current_entropy_loss = loss_plus;
                current_fused_state = fused_plus;
            } else if loss_minus < current_entropy_loss {
                current_lambda = lambda_minus;
                current_entropy_loss = loss_minus;
                current_fused_state = fused_minus;
            } else {
                // No improvement, reduce step size
                if delta < tolerance {
                    break;
                }
            }
        }

        Ok((current_fused_state.into_pyarray(py).into(), current_lambda))
    }
}

impl NonlinearInterferenceFFT {
    // Direct computation of nonlinear interference
    fn fuse_direct(&self, state1: &[Complex64], state2: &[Complex64], lambda: f64) -> Vec<Complex64> {
        let size = state1.len();
        let mut result = Vec::with_capacity(size);

        // Calculate inner product
        let inner_product = utils::inner_product(state1, state2);

        // Fuse states
        for i in 0..size {
            let fused = state1[i] + state2[i] + lambda * inner_product * state1[i] * state2[i];
            result.push(fused);
        }

        // Normalize
        utils::normalize(&mut result);

        result
    }

    // FFT-based computation of nonlinear interference
    fn fuse_fft(&self, state1: &[Complex64], state2: &[Complex64], lambda: f64) -> Vec<Complex64> {
        let size = state1.len();

        // Convert to rustfft Complex type
        let mut state1_fft: Vec<RustComplex<f64>> = state1.iter()
            .map(|c| RustComplex { re: c.re, im: c.im })
            .collect();

        let mut state2_fft: Vec<RustComplex<f64>> = state2.iter()
            .map(|c| RustComplex { re: c.re, im: c.im })
            .collect();

        // Create FFT planner
        let mut planner = FftPlanner::new();
        let fft = planner.plan_fft_forward(size);
        let ifft = planner.plan_fft_inverse(size);

        // Perform forward FFT
        fft.process(&mut state1_fft);
        fft.process(&mut state2_fft);

        // Calculate inner product in time domain
        let inner_product = utils::inner_product(state1, state2);

        // Combine in frequency domain
        let mut combined_fft = Vec::with_capacity(size);
        for i in 0..size {
            let s1 = state1_fft[i];
            let s2 = state2_fft[i];
            let combined = s1 + s2 + lambda * inner_product * s1 * s2;
            combined_fft.push(combined);
        }

        // Perform inverse FFT
        ifft.process(&mut combined_fft);

        // Convert back to Complex64 and normalize
        let norm_factor = 1.0 / (size as f64);
        let mut result: Vec<Complex64> = combined_fft.iter()
            .map(|c| Complex64::new(c.re * norm_factor, c.im * norm_factor))
            .collect();

        // Normalize
        utils::normalize(&mut result);

        result
    }
}

/// Nonlinear Field Function with FFT Acceleration
///
/// This class implements the nonlinear field function F(ψ) for the nonlinear interference algorithm,
/// with FFT acceleration for large-scale computations.
#[pyclass]
pub struct NonlinearFieldFunctionFFT {
    /// Nonlinearity type
    #[pyo3(get, set)]
    nonlinearity_type: String,

    /// Threshold for switching between direct computation and FFT
    #[pyo3(get, set)]
    fft_threshold: usize,
}

#[pymethods]
impl NonlinearFieldFunctionFFT {
    /// Create a new NonlinearFieldFunctionFFT
    #[new]
    #[pyo3(signature = (nonlinearity_type="quadratic", fft_threshold=64))]
    fn new(nonlinearity_type: &str, fft_threshold: usize) -> Self {
        Self {
            nonlinearity_type: nonlinearity_type.to_string(),
            fft_threshold,
        }
    }

    /// Apply the nonlinear field function to a state
    ///
    /// Args:
    ///     state: Input state as complex array
    ///
    /// Returns:
    ///     Transformed state as complex array
    fn apply<'py>(
        &self,
        py: Python<'py>,
        state: PyReadonlyArray1<Complex64>,
    ) -> PyResult<Py<PyArray1<Complex64>>> {
        // Convert to Rust array
        let state_array = state.as_array();
        let size = state_array.len();

        // Convert to Vec for our methods
        let state_vec: Vec<Complex64> = state_array.iter().copied().collect();

        // Choose the appropriate method based on size and nonlinearity type
        let result = if size <= self.fft_threshold || !size.is_power_of_two() {
            // For small states or non-power-of-2 sizes, use direct computation
            match self.nonlinearity_type.as_str() {
                "quadratic" => self.quadratic_nonlinearity_direct(&state_vec),
                "cubic" => self.cubic_nonlinearity_direct(&state_vec),
                "exponential" => self.exponential_nonlinearity_direct(&state_vec),
                _ => return Err(pyo3::exceptions::PyValueError::new_err(
                    format!("Unsupported nonlinearity type: {}", self.nonlinearity_type)
                )),
            }
        } else {
            // For larger states that are a power of 2, use FFT-based computation
            match self.nonlinearity_type.as_str() {
                "quadratic" => self.quadratic_nonlinearity_fft(&state_vec),
                "cubic" => self.cubic_nonlinearity_fft(&state_vec),
                "exponential" => self.exponential_nonlinearity_fft(&state_vec),
                _ => return Err(pyo3::exceptions::PyValueError::new_err(
                    format!("Unsupported nonlinearity type: {}", self.nonlinearity_type)
                )),
            }
        };

        // Convert result to numpy array
        Ok(result.into_pyarray(py).into())
    }
}

impl NonlinearFieldFunctionFFT {
    // Direct computation of quadratic nonlinearity
    fn quadratic_nonlinearity_direct(&self, state: &[Complex64]) -> Vec<Complex64> {
        let mut result = Vec::with_capacity(state.len());

        for &s in state {
            // Apply quadratic nonlinearity: |ψ|²ψ
            let abs_squared = s.norm_sqr();
            result.push(abs_squared * s);
        }

        // Normalize
        utils::normalize(&mut result);

        result
    }

    // Direct computation of cubic nonlinearity
    fn cubic_nonlinearity_direct(&self, state: &[Complex64]) -> Vec<Complex64> {
        let mut result = Vec::with_capacity(state.len());

        for &s in state {
            // Apply cubic nonlinearity: |ψ|⁴ψ
            let abs_squared = s.norm_sqr();
            result.push(abs_squared * abs_squared * s);
        }

        // Normalize
        utils::normalize(&mut result);

        result
    }

    // Direct computation of exponential nonlinearity
    fn exponential_nonlinearity_direct(&self, state: &[Complex64]) -> Vec<Complex64> {
        let mut result = Vec::with_capacity(state.len());

        for &s in state {
            // Apply exponential nonlinearity: exp(|ψ|²)ψ
            let abs_squared = s.norm_sqr();
            result.push(Complex64::new(abs_squared.exp(), 0.0) * s);
        }

        // Normalize
        utils::normalize(&mut result);

        result
    }

    // FFT-based computation of quadratic nonlinearity
    fn quadratic_nonlinearity_fft(&self, state: &[Complex64]) -> Vec<Complex64> {
        let size = state.len();

        // Convert to rustfft Complex type
        let mut state_fft: Vec<RustComplex<f64>> = state.iter()
            .map(|c| RustComplex { re: c.re, im: c.im })
            .collect();

        // Create FFT planner
        let mut planner = FftPlanner::new();
        let fft = planner.plan_fft_forward(size);
        let ifft = planner.plan_fft_inverse(size);

        // Perform forward FFT
        fft.process(&mut state_fft);

        // Compute convolution in frequency domain
        let mut convolution = vec![RustComplex { re: 0.0, im: 0.0 }; size];
        for i in 0..size {
            for j in 0..size {
                let k = (i + j) % size;
                convolution[k] = convolution[k] + state_fft[i] * state_fft[j];
            }
        }

        // Perform inverse FFT
        ifft.process(&mut convolution);

        // Normalize and apply nonlinearity
        let norm_factor = 1.0 / (size as f64);
        let mut result: Vec<Complex64> = state.iter().zip(convolution.iter())
            .map(|(&s, &c)| {
                let conv = Complex64::new(c.re * norm_factor, c.im * norm_factor);
                // Apply quadratic nonlinearity: |ψ|²ψ
                conv * s
            })
            .collect();

        // Normalize
        utils::normalize(&mut result);

        result
    }

    // FFT-based computation of cubic nonlinearity
    fn cubic_nonlinearity_fft(&self, state: &[Complex64]) -> Vec<Complex64> {
        // Similar to quadratic but with double convolution
        // For simplicity, we'll use the direct method for now
        self.cubic_nonlinearity_direct(state)
    }

    // FFT-based computation of exponential nonlinearity
    fn exponential_nonlinearity_fft(&self, state: &[Complex64]) -> Vec<Complex64> {
        // Exponential is harder to compute with FFT
        // For simplicity, we'll use the direct method for now
        self.exponential_nonlinearity_direct(state)
    }
}

/// Python module for nonlinear interference
#[pymodule]
fn nonlinear_interference(_py: Python, m: &Bound<PyModule>) -> PyResult<()> {
    m.add_class::<NonlinearInterferenceFFT>()?;
    m.add_class::<NonlinearFieldFunctionFFT>()?;
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use num_complex::Complex64;

    #[test]
    fn test_nonlinear_interference() {
        // Create random states
        let state1 = vec![
            Complex64::new(0.5, 0.0),
            Complex64::new(0.5, 0.0),
            Complex64::new(0.5, 0.0),
            Complex64::new(0.5, 0.0),
        ];
        let state2 = vec![
            Complex64::new(0.0, 0.5),
            Complex64::new(0.0, 0.5),
            Complex64::new(0.0, 0.5),
            Complex64::new(0.0, 0.5),
        ];

        // Normalize states
        let mut state1_normalized = state1.clone();
        let mut state2_normalized = state2.clone();
        utils::normalize(&mut state1_normalized);
        utils::normalize(&mut state2_normalized);

        // Create interference operator
        let interference = NonlinearInterferenceFFT::new(0.5, 64);

        // Fuse states
        let fused_state = interference.fuse_direct(&state1_normalized, &state2_normalized, 0.5);

        // Check normalization
        let norm: f64 = fused_state.iter().map(|c| c.norm_sqr()).sum();
        assert!((norm - 1.0).abs() < 1e-10, "Fused state not normalized");
    }

    #[test]
    fn test_nonlinear_field_function() {
        // Create random state
        let state = vec![
            Complex64::new(0.5, 0.0),
            Complex64::new(0.5, 0.0),
            Complex64::new(0.5, 0.0),
            Complex64::new(0.5, 0.0),
        ];

        // Normalize state
        let mut state_normalized = state.clone();
        utils::normalize(&mut state_normalized);

        // Create field function
        let field_function = NonlinearFieldFunctionFFT::new("quadratic", 64);

        // Apply field function
        let result = field_function.quadratic_nonlinearity_direct(&state_normalized);

        // Check normalization
        let norm: f64 = result.iter().map(|c| c.norm_sqr()).sum();
        assert!((norm - 1.0).abs() < 1e-10, "Result not normalized");
    }
}
