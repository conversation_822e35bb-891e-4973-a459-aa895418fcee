//! Utility functions for nonlinear interference algorithm

use num_complex::Complex64;

/// Calculate inner product of two complex vectors
///
/// Args:
///     a: First vector
///     b: Second vector
///
/// Returns:
///     Inner product <a|b>
pub fn inner_product(a: &[Complex64], b: &[Complex64]) -> Complex64 {
    if a.len() != b.len() {
        panic!("Vectors must have the same length");
    }
    
    a.iter().zip(b.iter())
        .map(|(&a_i, &b_i)| a_i.conj() * b_i)
        .sum()
}

/// Normalize a complex vector
///
/// Args:
///     v: Vector to normalize
pub fn normalize(v: &mut [Complex64]) {
    let norm_squared: f64 = v.iter().map(|c| c.norm_sqr()).sum();
    let norm = norm_squared.sqrt();
    
    if norm > 0.0 {
        for c in v.iter_mut() {
            *c /= norm;
        }
    }
}

/// Calculate entropy of a quantum state
///
/// Args:
///     state: Quantum state as complex vector
///
/// Returns:
///     Von <PERSON> entropy
pub fn calculate_entropy(state: &[Complex64]) -> f64 {
    // Calculate density matrix eigenvalues (for pure state, it's just |state|^2)
    let probabilities: Vec<f64> = state.iter()
        .map(|c| c.norm_sqr())
        .collect();
    
    // Calculate entropy
    -probabilities.iter()
        .filter(|&&p| p > 1e-10) // Avoid log(0)
        .map(|&p| p * p.ln())
        .sum::<f64>()
}

/// Calculate fidelity between two quantum states
///
/// Args:
///     state1: First quantum state
///     state2: Second quantum state
///
/// Returns:
///     Fidelity between states
pub fn calculate_fidelity(state1: &[Complex64], state2: &[Complex64]) -> f64 {
    let inner = inner_product(state1, state2);
    inner.norm_sqr()
}

/// Convert complex vector to real vector of probabilities
///
/// Args:
///     state: Complex vector
///
/// Returns:
///     Vector of probabilities
pub fn to_probabilities(state: &[Complex64]) -> Vec<f64> {
    state.iter()
        .map(|c| c.norm_sqr())
        .collect()
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_inner_product() {
        let a = vec![
            Complex64::new(1.0, 0.0),
            Complex64::new(0.0, 1.0),
        ];
        let b = vec![
            Complex64::new(0.0, 1.0),
            Complex64::new(1.0, 0.0),
        ];
        
        let result = inner_product(&a, &b);
        assert!((result.re - 0.0).abs() < 1e-10);
        assert!((result.im - 1.0).abs() < 1e-10);
    }
    
    #[test]
    fn test_normalize() {
        let mut v = vec![
            Complex64::new(1.0, 0.0),
            Complex64::new(1.0, 0.0),
        ];
        
        normalize(&mut v);
        
        let norm: f64 = v.iter().map(|c| c.norm_sqr()).sum();
        assert!((norm - 1.0).abs() < 1e-10);
    }
    
    #[test]
    fn test_calculate_entropy() {
        // Equal superposition state
        let state = vec![
            Complex64::new(1.0 / 2.0_f64.sqrt(), 0.0),
            Complex64::new(1.0 / 2.0_f64.sqrt(), 0.0),
        ];
        
        let entropy = calculate_entropy(&state);
        assert!((entropy - 0.693).abs() < 1e-3); // ln(2) ≈ 0.693
    }
    
    #[test]
    fn test_calculate_fidelity() {
        // Orthogonal states
        let state1 = vec![
            Complex64::new(1.0, 0.0),
            Complex64::new(0.0, 0.0),
        ];
        let state2 = vec![
            Complex64::new(0.0, 0.0),
            Complex64::new(1.0, 0.0),
        ];
        
        let fidelity = calculate_fidelity(&state1, &state2);
        assert!((fidelity - 0.0).abs() < 1e-10);
        
        // Same state
        let fidelity = calculate_fidelity(&state1, &state1);
        assert!((fidelity - 1.0).abs() < 1e-10);
    }
}
