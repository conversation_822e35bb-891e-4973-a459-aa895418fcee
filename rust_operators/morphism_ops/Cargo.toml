[package]
name = "morphism_ops"
version = "0.1.0"
edition = "2021"
authors = ["TTE Team"]
description = "Morphism operations for the TTE system"
license = "Apache-2.0"

[dependencies]
# Core dependencies
anyhow = "=1.0.75"
thiserror = "=1.0.50"
serde = { version = "=1.0.190", features = ["derive"] }
serde_json = "=1.0.107"

# Arrow and Parquet dependencies
arrow = "=47.0.0"  # 使用固定版本
arrow-schema = "=47.0.0"
arrow-array = "=47.0.0"
arrow-buffer = "=47.0.0"
arrow-data = "=47.0.0"
parquet = "=47.0.0"

# PyO3 for Python bindings
pyo3 = { version = "=0.24.0", features = ["extension-module"] }
numpy = "=0.24.0"

# Other dependencies
petgraph = "=0.6.3"  # For graph operations
rayon = "=1.8.0"     # For parallel processing
tokio = { version = "=1.36.0", features = ["full"] }  # For async operations
num-complex = "=0.4.4"  # For complex number support
chrono = "=0.4.31"    # For timestamp handling
uuid = { version = "=1.4.1", features = ["v4", "serde"] }  # For unique IDs

# Local dependencies
# 使用项目中已有的Arrow扩展
tte-arrow-extensions = { path = "../arrow_extensions" }

[lib]
name = "morphism_ops"
crate-type = ["cdylib", "rlib"]

[features]
default = ["python"]
python = ["pyo3/extension-module"]
