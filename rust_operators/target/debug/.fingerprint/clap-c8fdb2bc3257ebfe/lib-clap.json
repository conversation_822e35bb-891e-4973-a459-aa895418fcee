{"rustc": 13226066032359371072, "features": "[\"std\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 11439587820120798860, "path": 5121657870517866281, "deps": [[14883207739598929556, "clap_builder", false, 18422857339704134496]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-c8fdb2bc3257ebfe/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}