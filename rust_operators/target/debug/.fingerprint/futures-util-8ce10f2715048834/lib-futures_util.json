{"rustc": 13226066032359371072, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 3511054296942749999, "deps": [[5103565458935487, "futures_io", false, 10300281526247872792], [1615478164327904835, "pin_utils", false, 18005482553868299082], [1811549171721445101, "futures_channel", false, 11437389577576968099], [1906322745568073236, "pin_project_lite", false, 10219473703885776191], [3129130049864710036, "memchr", false, 8524624104848506511], [6955678925937229351, "slab", false, 6744941536728174380], [7013762810557009322, "futures_sink", false, 2490746997234238810], [7620660491849607393, "futures_core", false, 15188467420776905840], [10565019901765856648, "futures_macro", false, 10801016842313784771], [16240732885093539806, "futures_task", false, 2707135702929036295]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-8ce10f2715048834/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}