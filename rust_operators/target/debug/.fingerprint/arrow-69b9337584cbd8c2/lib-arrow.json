{"rustc": 13226066032359371072, "features": "[\"arrow-csv\", \"arrow-ipc\", \"arrow-json\", \"csv\", \"default\", \"ipc\", \"json\"]", "declared_features": "[\"arrow-csv\", \"arrow-ipc\", \"arrow-json\", \"chrono-tz\", \"csv\", \"default\", \"ffi\", \"force_validate\", \"ipc\", \"ipc_compression\", \"json\", \"prettyprint\", \"pyarrow\", \"pyo3\", \"rand\", \"simd\", \"test_utils\"]", "target": 6254409934500757753, "profile": 2241668132362809309, "path": 8381539124347793649, "deps": [[966925859616469517, "ahash", false, 49049309969073002], [2140843760726074692, "arrow_data", false, 11513626693437653607], [3689832133781688085, "arrow_ipc", false, 9135342830533018838], [5823538416534401049, "arrow_array", false, 697313811594728727], [6514458020490376390, "arrow_row", false, 15428973543425554313], [8381098251122895350, "arrow_arith", false, 4527646891673499482], [9186549525796839419, "arrow_schema", false, 11712575913533371336], [11187074603613875605, "arrow_string", false, 17273400723095213805], [13732969735454813052, "arrow_select", false, 17443866710244371561], [15682310156530082340, "arrow_cast", false, 1475177930722984045], [16013744090300057463, "arrow_csv", false, 6293858671617807550], [16712208477695536887, "arrow_json", false, 11829044535965108996], [17634991317354174067, "arrow_buffer", false, 11158242986437087196], [18099201144075660655, "arrow_ord", false, 10254531907151089934]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/arrow-69b9337584cbd8c2/dep-lib-arrow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}