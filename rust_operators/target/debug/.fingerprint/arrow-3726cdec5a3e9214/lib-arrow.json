{"rustc": 13226066032359371072, "features": "[\"arrow-csv\", \"arrow-ipc\", \"arrow-json\", \"csv\", \"default\", \"ipc\", \"json\"]", "declared_features": "[\"arrow-csv\", \"arrow-ipc\", \"arrow-json\", \"chrono-tz\", \"csv\", \"default\", \"ffi\", \"force_validate\", \"ipc\", \"ipc_compression\", \"json\", \"prettyprint\", \"pyarrow\", \"pyo3\", \"rand\", \"simd\", \"test_utils\"]", "target": 6254409934500757753, "profile": 15657897354478470176, "path": 8381539124347793649, "deps": [[966925859616469517, "ahash", false, 8546145077362023350], [2140843760726074692, "arrow_data", false, 317147444098083511], [3689832133781688085, "arrow_ipc", false, 5838741226785654852], [5823538416534401049, "arrow_array", false, 15968640214660871060], [6514458020490376390, "arrow_row", false, 18390931718185351723], [8381098251122895350, "arrow_arith", false, 12688562298346382767], [9186549525796839419, "arrow_schema", false, 17669956525666561263], [11187074603613875605, "arrow_string", false, 13940214214124249800], [13732969735454813052, "arrow_select", false, 15164546417013230536], [15682310156530082340, "arrow_cast", false, 11575023257470749651], [16013744090300057463, "arrow_csv", false, 4547224958938924902], [16712208477695536887, "arrow_json", false, 6135689110501397050], [17634991317354174067, "arrow_buffer", false, 17467384677230318365], [18099201144075660655, "arrow_ord", false, 9501337912645729184]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/arrow-3726cdec5a3e9214/dep-lib-arrow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}