[package]
name = "resonance_network_v24"
version = "0.1.0"
edition = "2021"
authors = ["超越态思维引擎团队"]
description = "共振网络算子的Rust实现"
license = "MIT"

[lib]
name = "resonance_network"
crate-type = ["cdylib"]

[dependencies]
pyo3 = { version = "0.24.0", features = ["extension-module"] }
numpy = "0.24.0"
ndarray = "0.15.6"
num-complex = "0.4.3"
rayon = "1.7.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
thiserror = "1.0"
petgraph = "0.6.3"
