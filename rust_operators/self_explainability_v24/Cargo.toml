[package]
name = "self_explainability_v24"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true

[lib]
name = "self_explainability_v24"
crate-type = ["cdylib"]

[dependencies]
pyo3.workspace = true
ndarray.workspace = true
numpy.workspace = true
serde.workspace = true
serde_json.workspace = true
num-complex.workspace = true
rayon.workspace = true
thiserror.workspace = true
rand.workspace = true
lazy_static.workspace = true
chrono = "0.4"
uuid.workspace = true
