// 超越态思维引擎自解释性算子的数据模型 (PyO3 0.24+ 兼容版本)

use serde::{Serialize, Deserialize};
use std::collections::HashMap;

/// 解释类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ExplanationType {
    /// 特征重要性解释
    FeatureImportance,
    /// 局部解释
    Local,
    /// 全局解释
    Global,
    /// 反事实解释
    Counterfactual,
    /// 示例解释
    Example,
    /// 规则解释
    Rule,
    /// 混合解释
    Hybrid,
}

impl Default for ExplanationType {
    fn default() -> Self {
        Self::Hybrid
    }
}

/// 解释格式
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ExplanationFormat {
    /// 文本格式
    Text,
    /// 可视化格式
    Visual,
    /// 表格格式
    Tabular,
    /// 混合格式
    Mixed,
}

impl Default for ExplanationFormat {
    fn default() -> Self {
        Self::Mixed
    }
}

/// 解释复杂度
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ExplanationComplexity {
    /// 简单解释
    Simple,
    /// 中等复杂度解释
    Medium,
    /// 复杂解释
    Complex,
    /// 自适应复杂度解释
    Adaptive,
}

impl Default for ExplanationComplexity {
    fn default() -> Self {
        Self::Adaptive
    }
}

/// 特征重要性
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FeatureImportance {
    /// 特征名称
    pub name: String,
    /// 特征重要性分数
    pub importance: f64,
    /// 特征值
    pub value: serde_json::Value,
    /// 特征类型
    pub feature_type: String,
    /// 特征描述
    pub description: String,
    /// 特征单位
    pub unit: Option<String>,
    /// 特征范围
    pub range: Option<(f64, f64)>,
}

/// 反事实解释
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Counterfactual {
    /// 原始特征
    pub original_features: HashMap<String, serde_json::Value>,
    /// 反事实特征
    pub counterfactual_features: HashMap<String, serde_json::Value>,
    /// 原始预测
    pub original_prediction: serde_json::Value,
    /// 反事实预测
    pub counterfactual_prediction: serde_json::Value,
    /// 改变的特征
    pub changed_features: Vec<String>,
    /// 改变的特征重要性
    pub changed_feature_importance: HashMap<String, f64>,
    /// 反事实距离
    pub distance: f64,
    /// 反事实可行性
    pub feasibility: f64,
}

/// 示例解释
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Example {
    /// 示例特征
    pub features: HashMap<String, serde_json::Value>,
    /// 示例预测
    pub prediction: serde_json::Value,
    /// 示例相似度
    pub similarity: f64,
    /// 示例权重
    pub weight: f64,
    /// 示例ID
    pub id: String,
    /// 示例描述
    pub description: Option<String>,
}

/// 规则解释
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Rule {
    /// 规则ID
    pub id: String,
    /// 规则条件
    pub conditions: Vec<String>,
    /// 规则结果
    pub outcome: String,
    /// 规则支持度
    pub support: f64,
    /// 规则置信度
    pub confidence: f64,
    /// 规则复杂度
    pub complexity: usize,
    /// 规则描述
    pub description: Option<String>,
}

/// 自解释性报告
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SelfExplainabilityReport {
    /// 报告ID
    pub id: String,
    /// 报告时间戳
    pub timestamp: String,
    /// 解释类型
    pub explanation_type: ExplanationType,
    /// 解释格式
    pub explanation_format: ExplanationFormat,
    /// 解释复杂度
    pub explanation_complexity: ExplanationComplexity,
    /// 解释摘要
    pub summary: String,
    /// 特征重要性列表
    pub feature_importances: Vec<FeatureImportance>,
    /// 反事实解释列表
    pub counterfactuals: Vec<Counterfactual>,
    /// 示例解释列表
    pub examples: Vec<Example>,
    /// 规则解释列表
    pub rules: Vec<Rule>,
    /// 解释置信度
    pub confidence: f64,
    /// 解释稳定性
    pub stability: f64,
    /// 解释一致性
    pub consistency: f64,
    /// 解释元数据
    pub metadata: HashMap<String, String>,
}

impl Default for SelfExplainabilityReport {
    fn default() -> Self {
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            timestamp: chrono::Utc::now().to_rfc3339(),
            explanation_type: ExplanationType::default(),
            explanation_format: ExplanationFormat::default(),
            explanation_complexity: ExplanationComplexity::default(),
            summary: String::new(),
            feature_importances: Vec::new(),
            counterfactuals: Vec::new(),
            examples: Vec::new(),
            rules: Vec::new(),
            confidence: 0.0,
            stability: 0.0,
            consistency: 0.0,
            metadata: HashMap::new(),
        }
    }
}
