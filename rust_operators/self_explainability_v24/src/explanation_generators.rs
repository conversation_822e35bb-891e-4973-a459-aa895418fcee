// 超越态思维引擎自解释性算子的解释生成方法 (PyO3 0.24+ 兼容版本)

use std::collections::HashMap;

use crate::models::{
    FeatureImportance, Counterfactual, Example, Rule,
    SelfExplainabilityReport,
};
use crate::self_explainability_operator::SelfExplainabilityOperator;

impl SelfExplainabilityOperator {
    /// 生成特征重要性解释
    pub fn generate_feature_importance_explanation(
        &self,
        input_data: &HashMap<String, serde_json::Value>,
        report: &mut SelfExplainabilityReport,
    ) -> Result<(), String> {
        // 检查输入数据
        let features = if let Some(features) = input_data.get("features") {
            if let Some(features_obj) = features.as_object() {
                features_obj
            } else {
                return Err("features必须是对象".to_string());
            }
        } else {
            return Err("输入数据缺少features字段".to_string());
        };
        
        // 生成特征重要性
        let mut feature_importances = Vec::new();
        
        for (name, value) in features {
            // 模拟特征重要性计算
            let importance = rand::random::<f64>();
            
            // 只保留重要性超过阈值的特征
            if importance >= self.feature_importance_threshold {
                let feature_type = match value {
                    serde_json::Value::Null => "null",
                    serde_json::Value::Bool(_) => "boolean",
                    serde_json::Value::Number(_) => "number",
                    serde_json::Value::String(_) => "string",
                    serde_json::Value::Array(_) => "array",
                    serde_json::Value::Object(_) => "object",
                };
                
                let feature_importance = FeatureImportance {
                    name: name.clone(),
                    importance,
                    value: value.clone(),
                    feature_type: feature_type.to_string(),
                    description: format!("特征 {} 的重要性", name),
                    unit: None,
                    range: None,
                };
                
                feature_importances.push(feature_importance);
            }
        }
        
        // 按重要性排序
        feature_importances.sort_by(|a, b| b.importance.partial_cmp(&a.importance).unwrap());
        
        // 更新报告
        report.feature_importances = feature_importances;
        
        Ok(())
    }
    
    /// 生成局部解释
    pub fn generate_local_explanation(
        &self,
        input_data: &HashMap<String, serde_json::Value>,
        report: &mut SelfExplainabilityReport,
    ) -> Result<(), String> {
        // 生成特征重要性解释
        self.generate_feature_importance_explanation(input_data, report)?;
        
        // 生成反事实解释
        self.generate_counterfactual_explanation(input_data, report)?;
        
        Ok(())
    }
    
    /// 生成全局解释
    pub fn generate_global_explanation(
        &self,
        input_data: &HashMap<String, serde_json::Value>,
        report: &mut SelfExplainabilityReport,
    ) -> Result<(), String> {
        // 生成特征重要性解释
        self.generate_feature_importance_explanation(input_data, report)?;
        
        // 生成规则解释
        self.generate_rule_explanation(input_data, report)?;
        
        Ok(())
    }
    
    /// 生成反事实解释
    pub fn generate_counterfactual_explanation(
        &self,
        input_data: &HashMap<String, serde_json::Value>,
        report: &mut SelfExplainabilityReport,
    ) -> Result<(), String> {
        // 检查输入数据
        let features = if let Some(features) = input_data.get("features") {
            if let Some(features_obj) = features.as_object() {
                features_obj
            } else {
                return Err("features必须是对象".to_string());
            }
        } else {
            return Err("输入数据缺少features字段".to_string());
        };
        
        let prediction = if let Some(prediction) = input_data.get("prediction") {
            prediction
        } else {
            return Err("输入数据缺少prediction字段".to_string());
        };
        
        // 生成反事实解释
        let mut counterfactuals = Vec::new();
        
        for _ in 0..self.counterfactual_count {
            // 创建原始特征的副本
            let mut original_features = HashMap::new();
            let mut counterfactual_features = HashMap::new();
            let mut changed_features = Vec::new();
            let mut changed_feature_importance = HashMap::new();
            
            for (name, value) in features {
                original_features.insert(name.clone(), value.clone());
                
                // 随机决定是否改变这个特征
                if rand::random::<f64>() < 0.3 {
                    // 根据特征类型生成新值
                    let new_value = match value {
                        serde_json::Value::Null => serde_json::Value::Null,
                        serde_json::Value::Bool(b) => serde_json::Value::Bool(!b),
                        serde_json::Value::Number(n) => {
                            if let Some(f) = n.as_f64() {
                                serde_json::Value::Number(serde_json::Number::from_f64(f * (0.5 + rand::random::<f64>())).unwrap())
                            } else {
                                value.clone()
                            }
                        },
                        serde_json::Value::String(s) => {
                            if s == "positive" {
                                serde_json::Value::String("negative".to_string())
                            } else if s == "negative" {
                                serde_json::Value::String("positive".to_string())
                            } else {
                                serde_json::Value::String(format!("{}_cf", s))
                            }
                        },
                        _ => value.clone(),
                    };
                    
                    counterfactual_features.insert(name.clone(), new_value);
                    changed_features.push(name.clone());
                    changed_feature_importance.insert(name.clone(), rand::random::<f64>());
                } else {
                    counterfactual_features.insert(name.clone(), value.clone());
                }
            }
            
            // 生成反事实预测
            let counterfactual_prediction = match prediction {
                serde_json::Value::String(s) => {
                    if s == "positive" {
                        serde_json::Value::String("negative".to_string())
                    } else if s == "negative" {
                        serde_json::Value::String("positive".to_string())
                    } else {
                        serde_json::Value::String(format!("{}_cf", s))
                    }
                },
                serde_json::Value::Number(n) => {
                    if let Some(f) = n.as_f64() {
                        serde_json::Value::Number(serde_json::Number::from_f64(1.0 - f).unwrap())
                    } else {
                        prediction.clone()
                    }
                },
                _ => prediction.clone(),
            };
            
            // 创建反事实解释
            let counterfactual = Counterfactual {
                original_features,
                counterfactual_features,
                original_prediction: prediction.clone(),
                counterfactual_prediction,
                changed_features,
                changed_feature_importance,
                distance: rand::random::<f64>(),
                feasibility: rand::random::<f64>(),
            };
            
            counterfactuals.push(counterfactual);
        }
        
        // 更新报告
        report.counterfactuals = counterfactuals;
        
        Ok(())
    }
    
    /// 生成示例解释
    pub fn generate_example_explanation(
        &self,
        input_data: &HashMap<String, serde_json::Value>,
        report: &mut SelfExplainabilityReport,
    ) -> Result<(), String> {
        // 检查输入数据
        let features = if let Some(features) = input_data.get("features") {
            if let Some(features_obj) = features.as_object() {
                features_obj
            } else {
                return Err("features必须是对象".to_string());
            }
        } else {
            return Err("输入数据缺少features字段".to_string());
        };
        
        let prediction = if let Some(prediction) = input_data.get("prediction") {
            prediction
        } else {
            return Err("输入数据缺少prediction字段".to_string());
        };
        
        // 生成示例解释
        let mut examples = Vec::new();
        
        for i in 0..self.example_count {
            // 创建示例特征
            let mut example_features = HashMap::new();
            
            for (name, value) in features {
                // 随机决定是否改变这个特征
                if rand::random::<f64>() < 0.2 {
                    // 根据特征类型生成新值
                    let new_value = match value {
                        serde_json::Value::Null => serde_json::Value::Null,
                        serde_json::Value::Bool(b) => serde_json::Value::Bool(!b),
                        serde_json::Value::Number(n) => {
                            if let Some(f) = n.as_f64() {
                                serde_json::Value::Number(serde_json::Number::from_f64(f * (0.8 + rand::random::<f64>() * 0.4)).unwrap())
                            } else {
                                value.clone()
                            }
                        },
                        serde_json::Value::String(s) => {
                            serde_json::Value::String(format!("{}_ex", s))
                        },
                        _ => value.clone(),
                    };
                    
                    example_features.insert(name.clone(), new_value);
                } else {
                    example_features.insert(name.clone(), value.clone());
                }
            }
            
            // 创建示例解释
            let example = Example {
                features: example_features,
                prediction: prediction.clone(),
                similarity: 0.7 + rand::random::<f64>() * 0.3,
                weight: rand::random::<f64>(),
                id: format!("example_{}", i),
                description: Some(format!("与输入数据相似的示例 {}", i)),
            };
            
            examples.push(example);
        }
        
        // 按相似度排序
        examples.sort_by(|a, b| b.similarity.partial_cmp(&a.similarity).unwrap());
        
        // 更新报告
        report.examples = examples;
        
        Ok(())
    }
    
    /// 生成规则解释
    pub fn generate_rule_explanation(
        &self,
        input_data: &HashMap<String, serde_json::Value>,
        report: &mut SelfExplainabilityReport,
    ) -> Result<(), String> {
        // 检查输入数据
        let features = if let Some(features) = input_data.get("features") {
            if let Some(features_obj) = features.as_object() {
                features_obj
            } else {
                return Err("features必须是对象".to_string());
            }
        } else {
            return Err("输入数据缺少features字段".to_string());
        };
        
        let prediction = if let Some(prediction) = input_data.get("prediction") {
            prediction
        } else {
            return Err("输入数据缺少prediction字段".to_string());
        };
        
        // 生成规则解释
        let mut rules = Vec::new();
        
        for i in 0..self.rule_count {
            // 创建规则条件
            let mut conditions = Vec::new();
            let feature_keys: Vec<&String> = features.keys().collect();
            let condition_count = 1 + (rand::random::<f64>() * 3.0) as usize;
            
            for _ in 0..condition_count {
                if feature_keys.is_empty() {
                    break;
                }
                
                let feature_idx = (rand::random::<f64>() * feature_keys.len() as f64) as usize;
                let feature_name = feature_keys[feature_idx];
                let feature_value = &features[feature_name];
                
                let condition = match feature_value {
                    serde_json::Value::Null => format!("{} IS NULL", feature_name),
                    serde_json::Value::Bool(b) => format!("{} = {}", feature_name, b),
                    serde_json::Value::Number(n) => {
                        if let Some(f) = n.as_f64() {
                            let threshold = f * (0.9 + rand::random::<f64>() * 0.2);
                            if rand::random::<bool>() {
                                format!("{} > {:.2}", feature_name, threshold)
                            } else {
                                format!("{} < {:.2}", feature_name, threshold)
                            }
                        } else {
                            format!("{} = {}", feature_name, n)
                        }
                    },
                    serde_json::Value::String(s) => format!("{} = '{}'", feature_name, s),
                    _ => format!("{} IS NOT NULL", feature_name),
                };
                
                conditions.push(condition);
            }
            
            // 创建规则结果
            let outcome = match prediction {
                serde_json::Value::String(s) => format!("prediction = '{}'", s),
                serde_json::Value::Number(n) => format!("prediction = {}", n),
                _ => format!("prediction = {:?}", prediction),
            };
            
            // 创建规则解释
            let rule = Rule {
                id: format!("rule_{}", i),
                conditions,
                outcome,
                support: 0.6 + rand::random::<f64>() * 0.4,
                confidence: 0.7 + rand::random::<f64>() * 0.3,
                complexity: condition_count,
                description: Some(format!("规则 {}", i)),
            };
            
            rules.push(rule);
        }
        
        // 按置信度排序
        rules.sort_by(|a, b| b.confidence.partial_cmp(&a.confidence).unwrap());
        
        // 更新报告
        report.rules = rules;
        
        Ok(())
    }
    
    /// 生成混合解释
    pub fn generate_hybrid_explanation(
        &self,
        input_data: &HashMap<String, serde_json::Value>,
        report: &mut SelfExplainabilityReport,
    ) -> Result<(), String> {
        // 生成特征重要性解释
        self.generate_feature_importance_explanation(input_data, report)?;
        
        // 生成反事实解释
        self.generate_counterfactual_explanation(input_data, report)?;
        
        // 生成示例解释
        self.generate_example_explanation(input_data, report)?;
        
        // 生成规则解释
        self.generate_rule_explanation(input_data, report)?;
        
        Ok(())
    }
    
    /// 生成解释摘要
    pub fn generate_explanation_summary(
        &self,
        report: &mut SelfExplainabilityReport,
    ) -> Result<(), String> {
        // 根据解释类型生成摘要
        let summary = match report.explanation_type {
            crate::models::ExplanationType::FeatureImportance => {
                if report.feature_importances.is_empty() {
                    "没有找到重要的特征。".to_string()
                } else {
                    let top_features: Vec<String> = report.feature_importances.iter()
                        .take(3)
                        .map(|f| format!("{}（重要性：{:.2}）", f.name, f.importance))
                        .collect();
                    
                    format!("最重要的特征是：{}。", top_features.join("、"))
                }
            },
            crate::models::ExplanationType::Local => {
                let mut summary_parts = Vec::new();
                
                if !report.feature_importances.is_empty() {
                    let top_feature = &report.feature_importances[0];
                    summary_parts.push(format!("最重要的特征是{}（重要性：{:.2}）", top_feature.name, top_feature.importance));
                }
                
                if !report.counterfactuals.is_empty() {
                    let counterfactual = &report.counterfactuals[0];
                    let changed_features = counterfactual.changed_features.join("、");
                    summary_parts.push(format!("如果改变{}，预测结果将变为{:?}", changed_features, counterfactual.counterfactual_prediction));
                }
                
                if summary_parts.is_empty() {
                    "无法生成局部解释。".to_string()
                } else {
                    summary_parts.join("。")
                }
            },
            crate::models::ExplanationType::Global => {
                let mut summary_parts = Vec::new();
                
                if !report.feature_importances.is_empty() {
                    let top_features: Vec<String> = report.feature_importances.iter()
                        .take(3)
                        .map(|f| f.name.clone())
                        .collect();
                    
                    summary_parts.push(format!("全局最重要的特征是：{}", top_features.join("、")));
                }
                
                if !report.rules.is_empty() {
                    let top_rule = &report.rules[0];
                    summary_parts.push(format!("最可靠的规则是：如果{}，则{}", top_rule.conditions.join(" 且 "), top_rule.outcome));
                }
                
                if summary_parts.is_empty() {
                    "无法生成全局解释。".to_string()
                } else {
                    summary_parts.join("。")
                }
            },
            crate::models::ExplanationType::Counterfactual => {
                if report.counterfactuals.is_empty() {
                    "无法生成反事实解释。".to_string()
                } else {
                    let counterfactual = &report.counterfactuals[0];
                    let changed_features = counterfactual.changed_features.join("、");
                    format!("如果改变{}，预测结果将从{:?}变为{:?}", changed_features, counterfactual.original_prediction, counterfactual.counterfactual_prediction)
                }
            },
            crate::models::ExplanationType::Example => {
                if report.examples.is_empty() {
                    "无法找到相似的示例。".to_string()
                } else {
                    let example = &report.examples[0];
                    format!("找到了一个相似度为{:.2}的示例，其预测结果为{:?}", example.similarity, example.prediction)
                }
            },
            crate::models::ExplanationType::Rule => {
                if report.rules.is_empty() {
                    "无法生成规则解释。".to_string()
                } else {
                    let top_rule = &report.rules[0];
                    format!("最可靠的规则（置信度：{:.2}）是：如果{}，则{}", top_rule.confidence, top_rule.conditions.join(" 且 "), top_rule.outcome)
                }
            },
            crate::models::ExplanationType::Hybrid => {
                let mut summary_parts = Vec::new();
                
                if !report.feature_importances.is_empty() {
                    let top_feature = &report.feature_importances[0];
                    summary_parts.push(format!("最重要的特征是{}（重要性：{:.2}）", top_feature.name, top_feature.importance));
                }
                
                if !report.counterfactuals.is_empty() {
                    let counterfactual = &report.counterfactuals[0];
                    let changed_features = counterfactual.changed_features.join("、");
                    summary_parts.push(format!("如果改变{}，预测结果将变为{:?}", changed_features, counterfactual.counterfactual_prediction));
                }
                
                if !report.examples.is_empty() {
                    let example = &report.examples[0];
                    summary_parts.push(format!("找到了一个相似度为{:.2}的示例", example.similarity));
                }
                
                if !report.rules.is_empty() {
                    let top_rule = &report.rules[0];
                    summary_parts.push(format!("最可靠的规则是：如果{}，则{}", top_rule.conditions.join(" 且 "), top_rule.outcome));
                }
                
                if summary_parts.is_empty() {
                    "无法生成混合解释。".to_string()
                } else {
                    summary_parts.join("。")
                }
            },
        };
        
        // 更新报告
        report.summary = summary;
        
        Ok(())
    }
    
    /// 计算解释质量指标
    pub fn calculate_explanation_quality_metrics(
        &self,
        report: &mut SelfExplainabilityReport,
    ) -> Result<(), String> {
        // 计算解释置信度
        let mut confidence_components = Vec::new();
        
        if !report.feature_importances.is_empty() {
            let avg_importance = report.feature_importances.iter()
                .map(|f| f.importance)
                .sum::<f64>() / report.feature_importances.len() as f64;
            
            confidence_components.push(avg_importance);
        }
        
        if !report.counterfactuals.is_empty() {
            let avg_feasibility = report.counterfactuals.iter()
                .map(|c| c.feasibility)
                .sum::<f64>() / report.counterfactuals.len() as f64;
            
            confidence_components.push(avg_feasibility);
        }
        
        if !report.examples.is_empty() {
            let avg_similarity = report.examples.iter()
                .map(|e| e.similarity)
                .sum::<f64>() / report.examples.len() as f64;
            
            confidence_components.push(avg_similarity);
        }
        
        if !report.rules.is_empty() {
            let avg_confidence = report.rules.iter()
                .map(|r| r.confidence)
                .sum::<f64>() / report.rules.len() as f64;
            
            confidence_components.push(avg_confidence);
        }
        
        let confidence = if confidence_components.is_empty() {
            0.0
        } else {
            confidence_components.iter().sum::<f64>() / confidence_components.len() as f64
        };
        
        // 计算解释稳定性（模拟）
        let stability = 0.7 + rand::random::<f64>() * 0.3;
        
        // 计算解释一致性（模拟）
        let consistency = 0.8 + rand::random::<f64>() * 0.2;
        
        // 更新报告
        report.confidence = confidence;
        report.stability = stability;
        report.consistency = consistency;
        
        Ok(())
    }
}
