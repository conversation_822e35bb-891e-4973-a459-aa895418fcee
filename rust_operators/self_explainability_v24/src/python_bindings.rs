// Python绑定 (PyO3 0.24+ 兼容版本)

use pyo3::prelude::*;
use pyo3::types::{PyDict, PyList};
use pyo3::exceptions::{PyValueError, PyTypeError};

use std::collections::HashMap;

use crate::models::{
    ExplanationType, ExplanationFormat, ExplanationComplexity,
    FeatureImportance, Counterfactual, Example, Rule,
    SelfExplainabilityReport,
};
use crate::self_explainability_operator::{
    SelfExplainabilityOperator, ThreadSafeSelfExplainabilityOperator
};

/// Python绑定的自解释性算子
#[pyclass(name = "SelfExplainabilityOperator")]
#[derive(Clone)]
pub struct PySelfExplainabilityOperator {
    /// 内部自解释性算子
    pub operator: ThreadSafeSelfExplainabilityOperator,
}

// 确保PySelfExplainabilityOperator是线程安全的
unsafe impl Send for PySelfExplainabilityOperator {}
unsafe impl Sync for PySelfExplainabilityOperator {}

#[pymethods]
impl PySelfExplainabilityOperator {
    /// 创建新的自解释性算子
    #[new]
    #[pyo3(signature = (explanation_type="hybrid", explanation_format="mixed", explanation_complexity="adaptive", feature_importance_threshold=0.05, counterfactual_count=3, example_count=3, rule_count=5, **_kwargs))]
    fn new(
        explanation_type: &str,
        explanation_format: &str,
        explanation_complexity: &str,
        feature_importance_threshold: f64,
        counterfactual_count: usize,
        example_count: usize,
        rule_count: usize,
        _kwargs: Option<&Bound<'_, PyDict>>,
    ) -> PyResult<Self> {
        // 解析解释类型
        let explanation_type_enum = match explanation_type {
            "feature_importance" => ExplanationType::FeatureImportance,
            "local" => ExplanationType::Local,
            "global" => ExplanationType::Global,
            "counterfactual" => ExplanationType::Counterfactual,
            "example" => ExplanationType::Example,
            "rule" => ExplanationType::Rule,
            "hybrid" => ExplanationType::Hybrid,
            _ => return Err(PyValueError::new_err(format!("不支持的解释类型: {}", explanation_type))),
        };

        // 解析解释格式
        let explanation_format_enum = match explanation_format {
            "text" => ExplanationFormat::Text,
            "visual" => ExplanationFormat::Visual,
            "tabular" => ExplanationFormat::Tabular,
            "mixed" => ExplanationFormat::Mixed,
            _ => return Err(PyValueError::new_err(format!("不支持的解释格式: {}", explanation_format))),
        };

        // 解析解释复杂度
        let explanation_complexity_enum = match explanation_complexity {
            "simple" => ExplanationComplexity::Simple,
            "medium" => ExplanationComplexity::Medium,
            "complex" => ExplanationComplexity::Complex,
            "adaptive" => ExplanationComplexity::Adaptive,
            _ => return Err(PyValueError::new_err(format!("不支持的解释复杂度: {}", explanation_complexity))),
        };

        // 创建算子
        let operator = SelfExplainabilityOperator::new_thread_safe(
            Some(explanation_type_enum),
            Some(explanation_format_enum),
            Some(explanation_complexity_enum),
            Some(feature_importance_threshold),
            Some(counterfactual_count),
            Some(example_count),
            Some(rule_count),
        );

        Ok(Self { operator })
    }

    /// 应用自解释性算子到输入数据
    #[pyo3(signature = (input_data, **_kwargs))]
    fn apply<'py>(
        &self,
        py: Python<'py>,
        input_data: &Bound<'py, PyAny>,
        _kwargs: Option<&Bound<'py, PyDict>>,
    ) -> PyResult<Bound<'py, PyDict>> {
        // 将Python输入数据转换为Rust数据结构
        let input_map = if let Ok(input_dict) = input_data.downcast::<PyDict>() {
            let mut input_map = HashMap::new();

            for (key, value) in input_dict.iter() {
                let key_str = key.extract::<String>()?;
                let value_json = python_value_to_json(&value)?;
                input_map.insert(key_str, value_json);
            }

            input_map
        } else {
            return Err(PyTypeError::new_err("输入数据必须是字典"));
        };

        // 获取写锁并应用算子
        let mut operator = self.operator.write()
            .map_err(|e| PyValueError::new_err(format!("Failed to acquire write lock: {}", e)))?;

        let result = operator.apply(&input_map)
            .map_err(|e| PyValueError::new_err(e))?;

        // 将结果转换为Python字典
        self_explainability_report_to_py_dict(py, &result)
    }

    /// 获取算子元数据
    fn get_metadata<'py>(&self, py: Python<'py>) -> PyResult<Bound<'py, PyDict>> {
        let operator = self.operator.read()
            .map_err(|e| PyValueError::new_err(format!("Failed to acquire read lock: {}", e)))?;

        let metadata = operator.get_metadata();
        let result_dict = PyDict::new(py);

        for (key, value) in metadata {
            result_dict.set_item(key, value)?;
        }

        Ok(result_dict.into())
    }

    /// 检查与其他算子的兼容性
    fn is_compatible_with(&self, _other_operator: &Bound<'_, PyAny>) -> PyResult<bool> {
        // 简化实现，实际应该检查其他算子的输出类型是否与本算子的输入类型兼容
        Ok(true)
    }

    /// 获取性能指标
    fn get_performance_metrics<'py>(&self, py: Python<'py>) -> PyResult<Bound<'py, PyDict>> {
        let operator = self.operator.read()
            .map_err(|e| PyValueError::new_err(format!("Failed to acquire read lock: {}", e)))?;

        let metrics = operator.get_performance_metrics();
        let result_dict = PyDict::new(py);

        for (key, value) in metrics {
            result_dict.set_item(key, value)?;
        }

        Ok(result_dict.into())
    }

    /// 与其他算子组合
    fn compose<'py>(&self, _other_operator: &Bound<'py, PyAny>) -> PyResult<Bound<'py, PyAny>> {
        // 简化实现，实际应该创建组合算子
        Err(PyValueError::new_err("组合算子功能尚未实现"))
    }

    /// 获取算子参数
    fn get_parameters<'py>(&self, py: Python<'py>) -> PyResult<Bound<'py, PyDict>> {
        let operator = self.operator.read()
            .map_err(|e| PyValueError::new_err(format!("Failed to acquire read lock: {}", e)))?;

        let parameters = operator.get_parameters();
        let result_dict = PyDict::new(py);

        for (key, value) in parameters {
            let py_value = json_to_python_value(py, &value)?;
            result_dict.set_item(key, py_value)?;
        }

        Ok(result_dict.into())
    }

    /// 设置算子参数
    fn set_parameters(&self, parameters: &Bound<'_, PyDict>) -> PyResult<()> {
        let mut operator = self.operator.write()
            .map_err(|e| PyValueError::new_err(format!("Failed to acquire write lock: {}", e)))?;

        let mut param_map = HashMap::new();

        for (key, value) in parameters.iter() {
            let key_str = key.extract::<String>()?;
            let value_json = python_value_to_json(&value)?;
            param_map.insert(key_str, value_json);
        }

        operator.set_parameters(&param_map)
            .map_err(|e| PyValueError::new_err(e))?;

        Ok(())
    }

    /// 检查是否有Rust实现
    fn to_rust(&self) -> PyResult<bool> {
        // 这个方法在Rust实现中总是返回true
        Ok(true)
    }

    /// 清理缓存
    fn clear_cache(&self) -> PyResult<()> {
        let mut operator = self.operator.write()
            .map_err(|e| PyValueError::new_err(format!("Failed to acquire write lock: {}", e)))?;

        operator.cache.clear();
        operator.cache_hits = 0;
        operator.cache_misses = 0;

        Ok(())
    }

    /// 获取缓存统计信息
    fn get_cache_stats<'py>(&self, py: Python<'py>) -> PyResult<Bound<'py, PyDict>> {
        let operator = self.operator.read()
            .map_err(|e| PyValueError::new_err(format!("Failed to acquire read lock: {}", e)))?;

        let result_dict = PyDict::new(py);
        result_dict.set_item("cache_size", operator.cache.len())?;
        result_dict.set_item("cache_hits", operator.cache_hits)?;
        result_dict.set_item("cache_misses", operator.cache_misses)?;

        let hit_ratio = if operator.cache_hits + operator.cache_misses > 0 {
            operator.cache_hits as f64 / (operator.cache_hits + operator.cache_misses) as f64
        } else {
            0.0
        };

        result_dict.set_item("cache_hit_ratio", hit_ratio)?;

        Ok(result_dict.into())
    }
}

/// 将Python值转换为JSON值
fn python_value_to_json(value: &Bound<'_, PyAny>) -> PyResult<serde_json::Value> {
    if value.is_none() {
        Ok(serde_json::Value::Null)
    } else if let Ok(bool_val) = value.extract::<bool>() {
        Ok(serde_json::Value::Bool(bool_val))
    } else if let Ok(int_val) = value.extract::<i64>() {
        Ok(serde_json::Value::Number(int_val.into()))
    } else if let Ok(float_val) = value.extract::<f64>() {
        if let Some(num) = serde_json::Number::from_f64(float_val) {
            Ok(serde_json::Value::Number(num))
        } else {
            Err(PyValueError::new_err(format!("无法将浮点数 {} 转换为JSON", float_val)))
        }
    } else if let Ok(str_val) = value.extract::<String>() {
        Ok(serde_json::Value::String(str_val))
    } else if let Ok(list_val) = value.downcast::<PyList>() {
        let mut json_array = Vec::new();
        for item in list_val.iter() {
            json_array.push(python_value_to_json(&item)?);
        }
        Ok(serde_json::Value::Array(json_array))
    } else if let Ok(dict_val) = value.downcast::<PyDict>() {
        let mut json_obj = serde_json::Map::new();
        for (key, val) in dict_val.iter() {
            let key_str = key.extract::<String>()?;
            json_obj.insert(key_str, python_value_to_json(&val)?);
        }
        Ok(serde_json::Value::Object(json_obj))
    } else {
        Err(PyTypeError::new_err(format!("不支持的Python类型: {}", value)))
    }
}

/// 将JSON值转换为Python值
fn json_to_python_value<'py>(py: Python<'py>, value: &serde_json::Value) -> PyResult<Bound<'py, PyAny>> {
    match value {
        serde_json::Value::Null => {
            let none = py.None();
            Ok(none.into_bound(py))
        },
        serde_json::Value::Bool(b) => {
            // 直接使用布尔值创建Python布尔对象
            Ok(b.to_object(py).into_bound(py))
        },
        serde_json::Value::Number(n) => {
            if let Some(i) = n.as_i64() {
                Ok(i.to_object(py).into_bound(py))
            } else if let Some(f) = n.as_f64() {
                Ok(f.to_object(py).into_bound(py))
            } else {
                Err(PyValueError::new_err(format!("无法将JSON数字 {:?} 转换为Python", n)))
            }
        },
        serde_json::Value::String(s) => {
            Ok(s.to_object(py).into_bound(py))
        },
        serde_json::Value::Array(arr) => {
            // 直接使用Python的list函数创建列表
            let py_list = PyList::empty(py);
            for item in arr {
                let py_item = json_to_python_value(py, item)?;
                py_list.append(py_item)?;
            }
            // 转换为PyAny
            Ok(py_list.to_object(py).into_bound(py))
        },
        serde_json::Value::Object(obj) => {
            // 直接使用Python的dict函数创建字典
            let py_dict = PyDict::new(py);
            for (key, val) in obj {
                let py_val = json_to_python_value(py, val)?;
                py_dict.set_item(key, py_val)?;
            }
            // 转换为PyAny
            Ok(py_dict.to_object(py).into_bound(py))
        },
    }
}

/// 将ExplanationType转换为字符串
fn explanation_type_to_string(explanation_type: &ExplanationType) -> String {
    match explanation_type {
        ExplanationType::FeatureImportance => "feature_importance".to_string(),
        ExplanationType::Local => "local".to_string(),
        ExplanationType::Global => "global".to_string(),
        ExplanationType::Counterfactual => "counterfactual".to_string(),
        ExplanationType::Example => "example".to_string(),
        ExplanationType::Rule => "rule".to_string(),
        ExplanationType::Hybrid => "hybrid".to_string(),
    }
}

/// 将ExplanationFormat转换为字符串
fn explanation_format_to_string(explanation_format: &ExplanationFormat) -> String {
    match explanation_format {
        ExplanationFormat::Text => "text".to_string(),
        ExplanationFormat::Visual => "visual".to_string(),
        ExplanationFormat::Tabular => "tabular".to_string(),
        ExplanationFormat::Mixed => "mixed".to_string(),
    }
}

/// 将ExplanationComplexity转换为字符串
fn explanation_complexity_to_string(explanation_complexity: &ExplanationComplexity) -> String {
    match explanation_complexity {
        ExplanationComplexity::Simple => "simple".to_string(),
        ExplanationComplexity::Medium => "medium".to_string(),
        ExplanationComplexity::Complex => "complex".to_string(),
        ExplanationComplexity::Adaptive => "adaptive".to_string(),
    }
}

/// 将FeatureImportance转换为Python字典
fn feature_importance_to_py_dict<'py>(py: Python<'py>, feature_importance: &FeatureImportance) -> PyResult<Bound<'py, PyDict>> {
    let result_dict = PyDict::new(py);

    // 添加特征名称
    result_dict.set_item("name", &feature_importance.name)?;

    // 添加特征重要性分数
    result_dict.set_item("importance", feature_importance.importance)?;

    // 添加特征值
    result_dict.set_item("value", json_to_python_value(py, &feature_importance.value)?)?;

    // 添加特征类型
    result_dict.set_item("feature_type", &feature_importance.feature_type)?;

    // 添加特征描述
    result_dict.set_item("description", &feature_importance.description)?;

    // 添加特征单位
    if let Some(unit) = &feature_importance.unit {
        result_dict.set_item("unit", unit)?;
    } else {
        result_dict.set_item("unit", py.None())?;
    }

    // 添加特征范围
    if let Some((min, max)) = feature_importance.range {
        let range_dict = PyDict::new(py);
        range_dict.set_item("min", min)?;
        range_dict.set_item("max", max)?;
        result_dict.set_item("range", range_dict)?;
    } else {
        result_dict.set_item("range", py.None())?;
    }

    Ok(result_dict.into())
}

/// 将Counterfactual转换为Python字典
fn counterfactual_to_py_dict<'py>(py: Python<'py>, counterfactual: &Counterfactual) -> PyResult<Bound<'py, PyDict>> {
    let result_dict = PyDict::new(py);

    // 添加原始特征
    let original_features_dict = PyDict::new(py);
    for (key, value) in &counterfactual.original_features {
        original_features_dict.set_item(key, json_to_python_value(py, value)?)?;
    }
    result_dict.set_item("original_features", original_features_dict)?;

    // 添加反事实特征
    let counterfactual_features_dict = PyDict::new(py);
    for (key, value) in &counterfactual.counterfactual_features {
        counterfactual_features_dict.set_item(key, json_to_python_value(py, value)?)?;
    }
    result_dict.set_item("counterfactual_features", counterfactual_features_dict)?;

    // 添加原始预测
    result_dict.set_item("original_prediction", json_to_python_value(py, &counterfactual.original_prediction)?)?;

    // 添加反事实预测
    result_dict.set_item("counterfactual_prediction", json_to_python_value(py, &counterfactual.counterfactual_prediction)?)?;

    // 添加改变的特征
    let changed_features_list = PyList::empty(py);
    for feature in &counterfactual.changed_features {
        changed_features_list.append(feature)?;
    }
    result_dict.set_item("changed_features", changed_features_list)?;

    // 添加改变的特征重要性
    let changed_feature_importance_dict = PyDict::new(py);
    for (key, value) in &counterfactual.changed_feature_importance {
        changed_feature_importance_dict.set_item(key, value)?;
    }
    result_dict.set_item("changed_feature_importance", changed_feature_importance_dict)?;

    // 添加反事实距离
    result_dict.set_item("distance", counterfactual.distance)?;

    // 添加反事实可行性
    result_dict.set_item("feasibility", counterfactual.feasibility)?;

    Ok(result_dict.into())
}

/// 将Example转换为Python字典
fn example_to_py_dict<'py>(py: Python<'py>, example: &Example) -> PyResult<Bound<'py, PyDict>> {
    let result_dict = PyDict::new(py);

    // 添加示例特征
    let features_dict = PyDict::new(py);
    for (key, value) in &example.features {
        features_dict.set_item(key, json_to_python_value(py, value)?)?;
    }
    result_dict.set_item("features", features_dict)?;

    // 添加示例预测
    result_dict.set_item("prediction", json_to_python_value(py, &example.prediction)?)?;

    // 添加示例相似度
    result_dict.set_item("similarity", example.similarity)?;

    // 添加示例权重
    result_dict.set_item("weight", example.weight)?;

    // 添加示例ID
    result_dict.set_item("id", &example.id)?;

    // 添加示例描述
    if let Some(description) = &example.description {
        result_dict.set_item("description", description)?;
    } else {
        result_dict.set_item("description", py.None())?;
    }

    Ok(result_dict.into())
}

/// 将Rule转换为Python字典
fn rule_to_py_dict<'py>(py: Python<'py>, rule: &Rule) -> PyResult<Bound<'py, PyDict>> {
    let result_dict = PyDict::new(py);

    // 添加规则ID
    result_dict.set_item("id", &rule.id)?;

    // 添加规则条件
    let conditions_list = PyList::empty(py);
    for condition in &rule.conditions {
        conditions_list.append(condition)?;
    }
    result_dict.set_item("conditions", conditions_list)?;

    // 添加规则结果
    result_dict.set_item("outcome", &rule.outcome)?;

    // 添加规则支持度
    result_dict.set_item("support", rule.support)?;

    // 添加规则置信度
    result_dict.set_item("confidence", rule.confidence)?;

    // 添加规则复杂度
    result_dict.set_item("complexity", rule.complexity)?;

    // 添加规则描述
    if let Some(description) = &rule.description {
        result_dict.set_item("description", description)?;
    } else {
        result_dict.set_item("description", py.None())?;
    }

    Ok(result_dict.into())
}

/// 将SelfExplainabilityReport转换为Python字典
fn self_explainability_report_to_py_dict<'py>(py: Python<'py>, report: &SelfExplainabilityReport) -> PyResult<Bound<'py, PyDict>> {
    let result_dict = PyDict::new(py);

    // 添加报告ID
    result_dict.set_item("id", &report.id)?;

    // 添加报告时间戳
    result_dict.set_item("timestamp", &report.timestamp)?;

    // 添加解释类型
    result_dict.set_item("explanation_type", explanation_type_to_string(&report.explanation_type))?;

    // 添加解释格式
    result_dict.set_item("explanation_format", explanation_format_to_string(&report.explanation_format))?;

    // 添加解释复杂度
    result_dict.set_item("explanation_complexity", explanation_complexity_to_string(&report.explanation_complexity))?;

    // 添加解释摘要
    result_dict.set_item("summary", &report.summary)?;

    // 添加特征重要性列表
    let feature_importances_list = PyList::empty(py);
    for feature_importance in &report.feature_importances {
        feature_importances_list.append(feature_importance_to_py_dict(py, feature_importance)?)?;
    }
    result_dict.set_item("feature_importances", feature_importances_list)?;

    // 添加反事实解释列表
    let counterfactuals_list = PyList::empty(py);
    for counterfactual in &report.counterfactuals {
        counterfactuals_list.append(counterfactual_to_py_dict(py, counterfactual)?)?;
    }
    result_dict.set_item("counterfactuals", counterfactuals_list)?;

    // 添加示例解释列表
    let examples_list = PyList::empty(py);
    for example in &report.examples {
        examples_list.append(example_to_py_dict(py, example)?)?;
    }
    result_dict.set_item("examples", examples_list)?;

    // 添加规则解释列表
    let rules_list = PyList::empty(py);
    for rule in &report.rules {
        rules_list.append(rule_to_py_dict(py, rule)?)?;
    }
    result_dict.set_item("rules", rules_list)?;

    // 添加解释置信度
    result_dict.set_item("confidence", report.confidence)?;

    // 添加解释稳定性
    result_dict.set_item("stability", report.stability)?;

    // 添加解释一致性
    result_dict.set_item("consistency", report.consistency)?;

    // 添加解释元数据
    let metadata_dict = PyDict::new(py);
    for (key, value) in &report.metadata {
        metadata_dict.set_item(key, value)?;
    }
    result_dict.set_item("metadata", metadata_dict)?;

    Ok(result_dict.into())
}
