// 超越态思维引擎自解释性算子 (PyO3 0.24+ 兼容版本)

use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use std::time::Instant;

use crate::models::{
    ExplanationType, ExplanationFormat, ExplanationComplexity,
    SelfExplainabilityReport,
};

/// 自解释性算子
#[derive(Debug)]
pub struct SelfExplainabilityOperator {
    /// 解释类型
    pub explanation_type: ExplanationType,
    /// 解释格式
    pub explanation_format: ExplanationFormat,
    /// 解释复杂度
    pub explanation_complexity: ExplanationComplexity,
    /// 特征重要性阈值
    pub feature_importance_threshold: f64,
    /// 反事实数量
    pub counterfactual_count: usize,
    /// 示例数量
    pub example_count: usize,
    /// 规则数量
    pub rule_count: usize,
    /// 缓存
    pub cache: HashMap<String, SelfExplainabilityReport>,
    /// 缓存命中次数
    pub cache_hits: usize,
    /// 缓存未命中次数
    pub cache_misses: usize,
    /// 上次执行时间（毫秒）
    pub last_execution_time: f64,
}

/// 线程安全的自解释性算子
pub type ThreadSafeSelfExplainabilityOperator = Arc<RwLock<SelfExplainabilityOperator>>;

impl SelfExplainabilityOperator {
    /// 创建新的自解释性算子
    pub fn new(
        explanation_type: Option<ExplanationType>,
        explanation_format: Option<ExplanationFormat>,
        explanation_complexity: Option<ExplanationComplexity>,
        feature_importance_threshold: Option<f64>,
        counterfactual_count: Option<usize>,
        example_count: Option<usize>,
        rule_count: Option<usize>,
    ) -> Self {
        Self {
            explanation_type: explanation_type.unwrap_or_default(),
            explanation_format: explanation_format.unwrap_or_default(),
            explanation_complexity: explanation_complexity.unwrap_or_default(),
            feature_importance_threshold: feature_importance_threshold.unwrap_or(0.05),
            counterfactual_count: counterfactual_count.unwrap_or(3),
            example_count: example_count.unwrap_or(3),
            rule_count: rule_count.unwrap_or(5),
            cache: HashMap::new(),
            cache_hits: 0,
            cache_misses: 0,
            last_execution_time: 0.0,
        }
    }

    /// 创建线程安全的自解释性算子
    pub fn new_thread_safe(
        explanation_type: Option<ExplanationType>,
        explanation_format: Option<ExplanationFormat>,
        explanation_complexity: Option<ExplanationComplexity>,
        feature_importance_threshold: Option<f64>,
        counterfactual_count: Option<usize>,
        example_count: Option<usize>,
        rule_count: Option<usize>,
    ) -> ThreadSafeSelfExplainabilityOperator {
        Arc::new(RwLock::new(Self::new(
            explanation_type,
            explanation_format,
            explanation_complexity,
            feature_importance_threshold,
            counterfactual_count,
            example_count,
            rule_count,
        )))
    }

    /// 应用自解释性算子到输入数据
    pub fn apply(
        &mut self,
        input_data: &HashMap<String, serde_json::Value>,
    ) -> Result<SelfExplainabilityReport, String> {
        let start = Instant::now();

        // 计算缓存键
        let cache_key = self.compute_cache_key(input_data);

        // 检查缓存
        if let Some(cached_result) = self.cache.get(&cache_key) {
            self.cache_hits += 1;
            return Ok(cached_result.clone());
        }

        self.cache_misses += 1;

        // 创建自解释性报告
        let mut report = SelfExplainabilityReport {
            explanation_type: self.explanation_type.clone(),
            explanation_format: self.explanation_format.clone(),
            explanation_complexity: self.explanation_complexity.clone(),
            ..Default::default()
        };

        // 根据解释类型生成解释
        match self.explanation_type {
            ExplanationType::FeatureImportance => {
                self.generate_feature_importance_explanation(input_data, &mut report)?;
            },
            ExplanationType::Local => {
                self.generate_local_explanation(input_data, &mut report)?;
            },
            ExplanationType::Global => {
                self.generate_global_explanation(input_data, &mut report)?;
            },
            ExplanationType::Counterfactual => {
                self.generate_counterfactual_explanation(input_data, &mut report)?;
            },
            ExplanationType::Example => {
                self.generate_example_explanation(input_data, &mut report)?;
            },
            ExplanationType::Rule => {
                self.generate_rule_explanation(input_data, &mut report)?;
            },
            ExplanationType::Hybrid => {
                self.generate_hybrid_explanation(input_data, &mut report)?;
            },
        }

        // 生成解释摘要
        self.generate_explanation_summary(&mut report)?;

        // 计算解释质量指标
        self.calculate_explanation_quality_metrics(&mut report)?;

        // 添加元数据
        report.metadata.insert("operator".to_string(), "SelfExplainabilityOperator".to_string());
        report.metadata.insert("version".to_string(), "0.1.0".to_string());
        report.metadata.insert("explanation_type".to_string(), format!("{:?}", self.explanation_type));
        report.metadata.insert("explanation_format".to_string(), format!("{:?}", self.explanation_format));
        report.metadata.insert("explanation_complexity".to_string(), format!("{:?}", self.explanation_complexity));

        // 记录性能指标
        self.last_execution_time = start.elapsed().as_secs_f64() * 1000.0;
        report.metadata.insert("execution_time_ms".to_string(), self.last_execution_time.to_string());

        // 缓存结果
        self.cache.insert(cache_key, report.clone());

        // 如果缓存太大，清理一些条目
        if self.cache.len() > 100 {
            self.prune_cache();
        }

        Ok(report)
    }

    /// 获取算子元数据
    pub fn get_metadata(&self) -> HashMap<String, String> {
        let mut metadata = HashMap::new();
        metadata.insert("name".to_string(), "SelfExplainabilityOperator".to_string());
        metadata.insert("version".to_string(), "0.1.0".to_string());
        metadata.insert("explanation_type".to_string(), format!("{:?}", self.explanation_type));
        metadata.insert("explanation_format".to_string(), format!("{:?}", self.explanation_format));
        metadata.insert("explanation_complexity".to_string(), format!("{:?}", self.explanation_complexity));
        metadata.insert("feature_importance_threshold".to_string(), self.feature_importance_threshold.to_string());
        metadata.insert("counterfactual_count".to_string(), self.counterfactual_count.to_string());
        metadata.insert("example_count".to_string(), self.example_count.to_string());
        metadata.insert("rule_count".to_string(), self.rule_count.to_string());
        metadata.insert("cache_size".to_string(), self.cache.len().to_string());
        metadata.insert("cache_hits".to_string(), self.cache_hits.to_string());
        metadata.insert("cache_misses".to_string(), self.cache_misses.to_string());
        metadata
    }

    /// 获取性能指标
    pub fn get_performance_metrics(&self) -> HashMap<String, f64> {
        let mut metrics = HashMap::new();
        metrics.insert("last_execution_time_ms".to_string(), self.last_execution_time);
        metrics.insert("cache_hit_ratio".to_string(),
            if self.cache_hits + self.cache_misses > 0 {
                self.cache_hits as f64 / (self.cache_hits + self.cache_misses) as f64
            } else {
                0.0
            }
        );
        metrics.insert("cache_size".to_string(), self.cache.len() as f64);
        metrics
    }

    /// 获取算子参数
    pub fn get_parameters(&self) -> HashMap<String, serde_json::Value> {
        let mut parameters = HashMap::new();
        parameters.insert("explanation_type".to_string(), serde_json::json!(format!("{:?}", self.explanation_type)));
        parameters.insert("explanation_format".to_string(), serde_json::json!(format!("{:?}", self.explanation_format)));
        parameters.insert("explanation_complexity".to_string(), serde_json::json!(format!("{:?}", self.explanation_complexity)));
        parameters.insert("feature_importance_threshold".to_string(), serde_json::json!(self.feature_importance_threshold));
        parameters.insert("counterfactual_count".to_string(), serde_json::json!(self.counterfactual_count));
        parameters.insert("example_count".to_string(), serde_json::json!(self.example_count));
        parameters.insert("rule_count".to_string(), serde_json::json!(self.rule_count));
        parameters.insert("cache_enabled".to_string(), serde_json::json!(true));
        parameters
    }

    /// 设置算子参数
    pub fn set_parameters(&mut self, parameters: &HashMap<String, serde_json::Value>) -> Result<(), String> {
        // 设置解释类型
        if let Some(explanation_type) = parameters.get("explanation_type") {
            if let Some(explanation_type_str) = explanation_type.as_str() {
                self.explanation_type = match explanation_type_str {
                    "FeatureImportance" => ExplanationType::FeatureImportance,
                    "Local" => ExplanationType::Local,
                    "Global" => ExplanationType::Global,
                    "Counterfactual" => ExplanationType::Counterfactual,
                    "Example" => ExplanationType::Example,
                    "Rule" => ExplanationType::Rule,
                    "Hybrid" => ExplanationType::Hybrid,
                    _ => return Err(format!("不支持的解释类型: {}", explanation_type_str)),
                };
            }
        }

        // 设置解释格式
        if let Some(explanation_format) = parameters.get("explanation_format") {
            if let Some(explanation_format_str) = explanation_format.as_str() {
                self.explanation_format = match explanation_format_str {
                    "Text" => ExplanationFormat::Text,
                    "Visual" => ExplanationFormat::Visual,
                    "Tabular" => ExplanationFormat::Tabular,
                    "Mixed" => ExplanationFormat::Mixed,
                    _ => return Err(format!("不支持的解释格式: {}", explanation_format_str)),
                };
            }
        }

        // 设置解释复杂度
        if let Some(explanation_complexity) = parameters.get("explanation_complexity") {
            if let Some(explanation_complexity_str) = explanation_complexity.as_str() {
                self.explanation_complexity = match explanation_complexity_str {
                    "Simple" => ExplanationComplexity::Simple,
                    "Medium" => ExplanationComplexity::Medium,
                    "Complex" => ExplanationComplexity::Complex,
                    "Adaptive" => ExplanationComplexity::Adaptive,
                    _ => return Err(format!("不支持的解释复杂度: {}", explanation_complexity_str)),
                };
            }
        }

        // 设置特征重要性阈值
        if let Some(feature_importance_threshold) = parameters.get("feature_importance_threshold") {
            if let Some(value) = feature_importance_threshold.as_f64() {
                if value < 0.0 || value > 1.0 {
                    return Err("特征重要性阈值必须在0到1之间".to_string());
                }
                self.feature_importance_threshold = value;
            }
        }

        // 设置反事实数量
        if let Some(counterfactual_count) = parameters.get("counterfactual_count") {
            if let Some(value) = counterfactual_count.as_u64() {
                self.counterfactual_count = value as usize;
            }
        }

        // 设置示例数量
        if let Some(example_count) = parameters.get("example_count") {
            if let Some(value) = example_count.as_u64() {
                self.example_count = value as usize;
            }
        }

        // 设置规则数量
        if let Some(rule_count) = parameters.get("rule_count") {
            if let Some(value) = rule_count.as_u64() {
                self.rule_count = value as usize;
            }
        }

        // 如果参数变化，清空缓存
        self.cache.clear();
        self.cache_hits = 0;
        self.cache_misses = 0;

        Ok(())
    }

    /// 清理缓存
    fn prune_cache(&mut self) {
        // 简单策略：保留最近的50个条目
        if self.cache.len() > 50 {
            let keys: Vec<String> = self.cache.keys().cloned().collect();
            let keys_to_remove = &keys[0..keys.len() - 50];

            for key in keys_to_remove {
                self.cache.remove(key);
            }
        }
    }

    /// 计算缓存键
    fn compute_cache_key(&self, input_data: &HashMap<String, serde_json::Value>) -> String {
        // 序列化输入数据
        let input_json = serde_json::to_string(input_data).unwrap_or_default();

        // 组合参数
        format!(
            "{}:{}:{}:{}:{}:{}:{}:{}",
            input_json,
            format!("{:?}", self.explanation_type),
            format!("{:?}", self.explanation_format),
            format!("{:?}", self.explanation_complexity),
            self.feature_importance_threshold,
            self.counterfactual_count,
            self.example_count,
            self.rule_count
        )
    }
}

impl Clone for SelfExplainabilityOperator {
    fn clone(&self) -> Self {
        Self {
            explanation_type: self.explanation_type.clone(),
            explanation_format: self.explanation_format.clone(),
            explanation_complexity: self.explanation_complexity.clone(),
            feature_importance_threshold: self.feature_importance_threshold,
            counterfactual_count: self.counterfactual_count,
            example_count: self.example_count,
            rule_count: self.rule_count,
            cache: HashMap::new(), // 不复制缓存
            cache_hits: 0,
            cache_misses: 0,
            last_execution_time: self.last_execution_time,
        }
    }
}
