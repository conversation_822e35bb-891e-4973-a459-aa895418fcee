#!/usr/bin/env python3
"""
测试PyO3 0.24+兼容版本的优化多层次解释生成算子
"""

import time
import json
from pprint import pprint

# 导入算子
import explanation_v24

def test_basic_functionality():
    """测试基本功能"""
    print("测试基本功能...")
    
    # 创建优化的多层次解释生成算子
    operator = explanation_v24.OptimizedMultilevelExplanationOperator(
        tech_level=0.7,
        concept_level=0.5,
        analogy_level=0.3,
        parallel_threshold=3
    )
    
    # 测试输入数据
    input_data = {
        "model_output": {
            "prediction": "positive",
            "confidence": 0.85,
            "features": {
                "text_length": 500,
                "sentiment_score": 0.75,
                "topic_relevance": 0.9
            }
        },
        "explanation_level": "all",
        "user_expertise": "intermediate"
    }
    
    # 应用算子
    result = operator.apply(input_data)
    
    # 检查结果
    assert "technical" in result
    assert "conceptual" in result
    assert "analogy" in result
    assert "fused" in result
    assert "weights" in result
    
    # 打印结果
    print("技术层面解释:")
    pprint(result["technical"])
    print("\n概念层面解释:")
    pprint(result["conceptual"])
    print("\n类比层面解释:")
    pprint(result["analogy"])
    print("\n融合解释:")
    pprint(result["fused"])
    print("\n权重:")
    pprint(result["weights"])
    
    print("基本功能测试通过！")

def test_user_model():
    """测试用户模型"""
    print("\n测试用户模型...")
    
    # 创建优化的多层次解释生成算子
    operator = explanation_v24.OptimizedMultilevelExplanationOperator()
    
    # 测试输入数据
    input_data = {
        "model_output": {
            "prediction": "positive",
            "confidence": 0.85,
            "features": {
                "text_length": 500,
                "sentiment_score": 0.75,
                "topic_relevance": 0.9
            }
        }
    }
    
    # 不同用户模型
    user_models = [
        {"technical_expertise": 0.9, "domain_knowledge": 0.2, "learning_style": "technical"},
        {"technical_expertise": 0.2, "domain_knowledge": 0.9, "learning_style": "conceptual"},
        {"technical_expertise": 0.5, "domain_knowledge": 0.5, "learning_style": "analogy"}
    ]
    
    for i, user_model in enumerate(user_models):
        print(f"\n用户模型 {i+1}:")
        pprint(user_model)
        
        # 应用算子
        result = operator.apply(input_data, user_model=user_model)
        
        # 打印权重
        print("权重:")
        pprint(result["weights"])
    
    print("用户模型测试通过！")

def test_performance():
    """测试性能"""
    print("\n测试性能...")
    
    # 创建优化的多层次解释生成算子
    operator = explanation_v24.OptimizedMultilevelExplanationOperator()
    
    # 测试输入数据
    input_data = {
        "model_output": {
            "prediction": "positive",
            "confidence": 0.85,
            "features": {
                "text_length": 500,
                "sentiment_score": 0.75,
                "topic_relevance": 0.9
            }
        }
    }
    
    # 测试缓存
    print("测试缓存...")
    
    # 第一次调用
    start_time = time.time()
    operator.apply(input_data)
    first_call_time = time.time() - start_time
    print(f"第一次调用时间: {first_call_time:.6f}秒")
    
    # 第二次调用（应该使用缓存）
    start_time = time.time()
    operator.apply(input_data)
    second_call_time = time.time() - start_time
    print(f"第二次调用时间: {second_call_time:.6f}秒")
    
    # 获取缓存统计信息
    cache_stats = operator.get_cache_stats()
    print("缓存统计信息:")
    pprint(cache_stats)
    
    # 清理缓存
    operator.clear_cache()
    
    # 获取性能指标
    metrics = operator.get_performance_metrics()
    print("性能指标:")
    pprint(metrics)
    
    print("性能测试通过！")

def test_batch_processing():
    """测试批量处理"""
    print("\n测试批量处理...")
    
    # 创建优化的多层次解释生成算子
    operator = explanation_v24.OptimizedMultilevelExplanationOperator(
        parallel_threshold=2
    )
    
    # 测试输入数据
    input_batch = [
        {
            "model_output": {
                "prediction": "positive",
                "confidence": 0.85,
                "features": {
                    "text_length": 500,
                    "sentiment_score": 0.75,
                    "topic_relevance": 0.9
                }
            }
        },
        {
            "model_output": {
                "prediction": "negative",
                "confidence": 0.65,
                "features": {
                    "text_length": 300,
                    "sentiment_score": 0.25,
                    "topic_relevance": 0.7
                }
            }
        },
        {
            "model_output": {
                "prediction": "neutral",
                "confidence": 0.55,
                "features": {
                    "text_length": 400,
                    "sentiment_score": 0.5,
                    "topic_relevance": 0.8
                }
            }
        }
    ]
    
    # 应用算子
    try:
        results = operator.apply_batch(input_batch)
        
        # 检查结果
        assert len(results) == len(input_batch)
        
        # 打印结果
        for i, result in enumerate(results):
            print(f"\n批量处理结果 {i+1}:")
            print(f"预测: {input_batch[i]['model_output']['prediction']}")
            print(f"融合解释: {result['fused']['justification']}")
        
        print("批量处理测试通过！")
    except Exception as e:
        print(f"批量处理测试失败: {e}")

def test_metadata():
    """测试元数据"""
    print("\n测试元数据...")
    
    # 创建优化的多层次解释生成算子
    operator = explanation_v24.OptimizedMultilevelExplanationOperator()
    
    # 获取算子元数据
    metadata = operator.get_metadata()
    print("算子元数据:")
    pprint(metadata)
    
    # 获取算子参数
    parameters = operator.get_parameters()
    print("\n算子参数:")
    pprint(parameters)
    
    # 设置算子参数
    new_parameters = {
        "tech_level": 0.8,
        "concept_level": 0.6,
        "analogy_level": 0.4,
        "user_model": {
            "technical_expertise": 0.7,
            "domain_knowledge": 0.6,
            "learning_style": "balanced"
        }
    }
    
    operator.set_parameters(new_parameters)
    
    # 获取更新后的算子参数
    updated_parameters = operator.get_parameters()
    print("\n更新后的算子参数:")
    pprint(updated_parameters)
    
    print("元数据测试通过！")

def main():
    """主函数"""
    print("开始测试PyO3 0.24+兼容版本的优化多层次解释生成算子...\n")
    
    # 测试基本功能
    test_basic_functionality()
    
    # 测试用户模型
    test_user_model()
    
    # 测试性能
    test_performance()
    
    # 测试批量处理
    test_batch_processing()
    
    # 测试元数据
    test_metadata()
    
    print("\n所有测试通过！")

if __name__ == "__main__":
    main()
