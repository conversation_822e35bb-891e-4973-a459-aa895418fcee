"""
FFT融合方法直接测试

这是一个直接测试脚本，不依赖于项目的其他部分。
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Any, Dict, List, Optional, Tuple, Union, Callable
from abc import ABC, abstractmethod
import time


# 定义算法接口
class AlgorithmInterface(ABC):
    """超越态算法接口基类"""
    
    @abstractmethod
    def __init__(self, **kwargs):
        """初始化算法"""
        pass
    
    @abstractmethod
    def compute(self, input_data: Any, **kwargs) -> Any:
        """执行算法计算"""
        pass
    
    @abstractmethod
    def get_metadata(self) -> Dict[str, Any]:
        """获取算法元数据"""
        pass
    
    @abstractmethod
    def is_compatible_with(self, other_algorithm: 'AlgorithmInterface') -> bool:
        """检查与其他算法的兼容性"""
        pass
    
    @abstractmethod
    def get_performance_metrics(self) -> Dict[str, float]:
        """获取性能指标"""
        pass


# 定义FFT变换器
class FFTTransformer:
    """FFT变换器"""
    
    def __init__(self, window_type: str = 'hann'):
        """初始化FFT变换器"""
        self.window_type = window_type
    
    def transform(self, signal: np.ndarray) -> np.ndarray:
        """执行快速傅里叶变换"""
        # 应用窗口函数
        windowed_signal = self._apply_window(signal)
        
        # 执行FFT
        frequency_domain = np.fft.fft(windowed_signal)
        
        return frequency_domain
    
    def inverse_transform(self, frequency_domain: np.ndarray) -> np.ndarray:
        """执行逆快速傅里叶变换"""
        # 执行IFFT
        signal = np.fft.ifft(frequency_domain)
        
        # 取实部
        signal = np.real(signal)
        
        return signal
    
    def _apply_window(self, signal: np.ndarray) -> np.ndarray:
        """应用窗口函数"""
        n = len(signal)
        
        # 创建窗口函数
        if self.window_type == 'hann':
            window = np.hanning(n)
        elif self.window_type == 'hamming':
            window = np.hamming(n)
        elif self.window_type == 'blackman':
            window = np.blackman(n)
        elif self.window_type == 'rectangular':
            window = np.ones(n)
        else:
            raise ValueError(f"不支持的窗口类型: {self.window_type}")
        
        # 应用窗口函数
        windowed_signal = signal * window
        
        return windowed_signal


# 定义频域滤波器
class FrequencyDomainFilter:
    """频域滤波器"""
    
    def __init__(self):
        """初始化频域滤波器"""
        pass
    
    def apply_filter(self, frequency_domain: np.ndarray, filter_params: Dict[str, Any]) -> np.ndarray:
        """应用频域滤波器"""
        # 获取滤波器类型
        filter_type = filter_params.get('type', 'lowpass')
        
        # 根据滤波器类型应用滤波器
        if filter_type == 'lowpass':
            return self._apply_lowpass_filter(frequency_domain, filter_params)
        elif filter_type == 'highpass':
            return self._apply_highpass_filter(frequency_domain, filter_params)
        else:
            raise ValueError(f"不支持的滤波器类型: {filter_type}")
    
    def _apply_lowpass_filter(self, frequency_domain: np.ndarray, filter_params: Dict[str, Any]) -> np.ndarray:
        """应用低通滤波器"""
        # 获取截止频率
        cutoff = filter_params.get('cutoff', 0.5)
        
        # 创建频率网格
        n = len(frequency_domain)
        freq = np.fft.fftfreq(n)
        
        # 创建低通滤波器
        filter_mask = np.abs(freq) < cutoff
        
        # 应用滤波器
        filtered_domain = frequency_domain.copy()
        filtered_domain[~filter_mask] = 0
        
        return filtered_domain
    
    def _apply_highpass_filter(self, frequency_domain: np.ndarray, filter_params: Dict[str, Any]) -> np.ndarray:
        """应用高通滤波器"""
        # 获取截止频率
        cutoff = filter_params.get('cutoff', 0.5)
        
        # 创建频率网格
        n = len(frequency_domain)
        freq = np.fft.fftfreq(n)
        
        # 创建高通滤波器
        filter_mask = np.abs(freq) >= cutoff
        
        # 应用滤波器
        filtered_domain = frequency_domain.copy()
        filtered_domain[~filter_mask] = 0
        
        return filtered_domain


# 定义融合分析器
class FusionAnalyzer:
    """融合分析器"""
    
    def __init__(self):
        """初始化融合分析器"""
        pass
    
    def evaluate_fusion_quality(self, source_signals: List[np.ndarray], 
                               fused_signal: np.ndarray) -> float:
        """评估融合质量"""
        # 计算相关系数
        corr_values = []
        
        for source in source_signals:
            # 确保信号长度一致
            min_length = min(len(source), len(fused_signal))
            source = source[:min_length]
            fused = fused_signal[:min_length]
            
            # 计算相关系数
            corr = np.corrcoef(source, fused)[0, 1]
            
            # 归一化到[0, 1]范围
            corr = (corr + 1) / 2.0
            
            corr_values.append(corr)
        
        # 取平均值
        avg_corr = np.mean(corr_values)
        
        return avg_corr


# 定义FFT融合方法
class FFTFusion(AlgorithmInterface):
    """FFT融合方法"""
    
    def __init__(self, fusion_method: str = 'weighted', window_type: str = 'hann', **kwargs):
        """初始化FFT融合方法"""
        self.fusion_method = fusion_method
        self.window_type = window_type
        
        # 性能指标
        self._performance_metrics = {
            'total_time': 0.0,
            'transform_time': 0.0,
            'fusion_time': 0.0,
            'inverse_transform_time': 0.0,
            'fusion_quality': 0.0
        }
        
        # 创建辅助组件
        self.transformer = FFTTransformer(window_type=window_type)
        self.filter = FrequencyDomainFilter()
        self.analyzer = FusionAnalyzer()
        
        # 自定义融合函数
        self._custom_fusion_function = kwargs.get('custom_fusion_function', None)
    
    def compute(self, input_data: List[np.ndarray], **kwargs) -> Dict[str, Any]:
        """执行FFT融合"""
        # 记录开始时间
        start_time = time.time()
        
        # 解析输入数据
        signals = input_data
        
        # 更新参数（如果在kwargs中提供）
        fusion_method = kwargs.get('fusion_method', self.fusion_method)
        weights = kwargs.get('weights', None)
        custom_fusion_function = kwargs.get('custom_fusion_function', self._custom_fusion_function)
        filter_params = kwargs.get('filter_params', None)
        
        # 执行FFT变换
        transform_start_time = time.time()
        frequency_domains = []
        
        for signal in signals:
            frequency_domain = self.transformer.transform(signal)
            frequency_domains.append(frequency_domain)
        
        transform_end_time = time.time()
        
        # 更新性能指标
        self._performance_metrics['transform_time'] = transform_end_time - transform_start_time
        
        # 执行频域融合
        fusion_start_time = time.time()
        
        # 应用频域滤波（如果提供了滤波参数）
        if filter_params:
            for i in range(len(frequency_domains)):
                frequency_domains[i] = self.filter.apply_filter(frequency_domains[i], filter_params)
        
        # 执行融合
        fused_frequency_domain = self._fuse_frequency_domains(
            frequency_domains, fusion_method, weights, custom_fusion_function
        )
        
        fusion_end_time = time.time()
        
        # 更新性能指标
        self._performance_metrics['fusion_time'] = fusion_end_time - fusion_start_time
        
        # 执行逆FFT变换
        inverse_start_time = time.time()
        fused_signal = self.transformer.inverse_transform(fused_frequency_domain)
        inverse_end_time = time.time()
        
        # 更新性能指标
        self._performance_metrics['inverse_transform_time'] = inverse_end_time - inverse_start_time
        
        # 评估融合质量
        fusion_quality = self.analyzer.evaluate_fusion_quality(signals, fused_signal)
        
        # 更新性能指标
        self._performance_metrics['fusion_quality'] = fusion_quality
        
        # 记录结束时间
        end_time = time.time()
        
        # 更新性能指标
        self._performance_metrics['total_time'] = end_time - start_time
        
        # 构建结果
        result = {
            'fused_signal': fused_signal,
            'frequency_domain': fused_frequency_domain,
            'fusion_quality': fusion_quality,
            'performance': self.get_performance_metrics()
        }
        
        return result
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取算法元数据"""
        return {
            'name': 'FFTFusion',
            'version': '1.0.0',
            'description': 'FFT融合方法，用于在频域中融合多个信号或图像',
            'parameters': {
                'fusion_method': self.fusion_method,
                'window_type': self.window_type
            },
            'performance_metrics': self.get_performance_metrics()
        }
    
    def is_compatible_with(self, other_algorithm: AlgorithmInterface) -> bool:
        """检查与其他算法的兼容性"""
        # 检查其他算法是否是FFTFusion的实例
        if not isinstance(other_algorithm, FFTFusion):
            return False
        
        # 检查其他算法的参数是否与当前算法兼容
        other_metadata = other_algorithm.get_metadata()
        
        # 检查窗口类型是否兼容
        if other_metadata['parameters']['window_type'] != self.window_type:
            return False
        
        return True
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """获取性能指标"""
        return self._performance_metrics
    
    def _fuse_frequency_domains(self, frequency_domains: List[np.ndarray], 
                               fusion_method: str, weights: Optional[List[float]] = None,
                               custom_fusion_function: Optional[Callable] = None) -> np.ndarray:
        """融合频域表示"""
        if not frequency_domains:
            raise ValueError("频域表示列表不能为空")
        
        # 根据融合方法执行融合
        if fusion_method == 'average':
            # 平均融合
            fused = np.mean(frequency_domains, axis=0)
        elif fusion_method == 'weighted':
            # 加权融合
            if weights is None:
                # 如果没有提供权重，则使用均匀权重
                weights = [1.0 / len(frequency_domains)] * len(frequency_domains)
            elif len(weights) != len(frequency_domains):
                raise ValueError(f"权重列表长度 {len(weights)} 与频域表示列表长度 {len(frequency_domains)} 不一致")
            
            # 归一化权重
            weights = np.array(weights) / np.sum(weights)
            
            # 加权融合
            fused = np.zeros_like(frequency_domains[0], dtype=complex)
            for i, fd in enumerate(frequency_domains):
                fused += weights[i] * fd
        elif fusion_method == 'max':
            # 最大值融合
            # 对于复数，我们使用幅度作为比较标准
            amplitudes = [np.abs(fd) for fd in frequency_domains]
            max_indices = np.argmax(amplitudes, axis=0)
            
            # 使用索引从原始复数数据中选择
            fused = np.zeros_like(frequency_domains[0], dtype=complex)
            for i, fd in enumerate(frequency_domains):
                mask = (max_indices == i)
                fused[mask] = fd[mask]
        elif fusion_method == 'custom':
            # 自定义融合
            if custom_fusion_function is None:
                raise ValueError("使用'custom'融合方法时必须提供自定义融合函数")
            
            fused = custom_fusion_function(frequency_domains)
        else:
            raise ValueError(f"不支持的融合方法: {fusion_method}")
        
        return fused


def create_test_signals():
    """创建测试信号"""
    # 创建时间轴
    t = np.linspace(0, 1, 1000)
    
    # 创建信号1：正弦波
    signal1 = np.sin(2 * np.pi * 5 * t)
    
    # 创建信号2：余弦波
    signal2 = np.cos(2 * np.pi * 10 * t)
    
    # 创建信号3：方波
    signal3 = np.sign(np.sin(2 * np.pi * 2 * t))
    
    return t, [signal1, signal2, signal3]


def plot_signals(t, signals, title="信号"):
    """绘制信号"""
    plt.figure(figsize=(10, 6))
    
    for i, signal in enumerate(signals):
        plt.plot(t, signal, label=f"信号 {i+1}")
    
    plt.xlabel('时间')
    plt.ylabel('幅度')
    plt.title(title)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()


def main():
    """主函数"""
    # 设置随机种子，确保结果可重现
    np.random.seed(42)
    
    # 创建测试信号
    t, signals = create_test_signals()
    
    # 绘制测试信号
    plot_signals(t, signals, title="测试信号")
    
    # 创建融合器
    fusion = FFTFusion(
        fusion_method='weighted',
        window_type='hann'
    )
    
    # 执行融合
    print("执行FFT融合...")
    start_time = time.time()
    result = fusion.compute(signals)
    end_time = time.time()
    
    print(f"计算完成，耗时: {end_time - start_time:.2f}秒")
    
    # 提取结果
    fused_signal = result['fused_signal']
    frequency_domain = result['frequency_domain']
    fusion_quality = result['fusion_quality']
    performance = result['performance']
    
    # 打印性能指标
    print(f"性能指标:")
    print(f"  总时间: {performance['total_time']:.2f}秒")
    print(f"  变换时间: {performance['transform_time']:.2f}秒")
    print(f"  融合时间: {performance['fusion_time']:.2f}秒")
    print(f"  逆变换时间: {performance['inverse_transform_time']:.2f}秒")
    print(f"  融合质量: {fusion_quality:.4f}")
    
    # 绘制融合信号
    plot_signals(t, [fused_signal], title=f"融合信号 (质量: {fusion_quality:.4f})")
    
    # 绘制频域表示
    plt.figure(figsize=(10, 6))
    plt.plot(np.abs(frequency_domain))
    plt.xlabel('频率')
    plt.ylabel('幅度')
    plt.title("融合信号的频域表示")
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()
    
    print("测试成功完成！")


if __name__ == "__main__":
    main()
