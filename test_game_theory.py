"""
博弈论算子测试脚本

这个脚本测试博弈论算子的基本功能。
"""

import numpy as np
from src.operators.game_theory.optimizer import GameTheoreticOptimizer
from src.operators.game_theory.nash_finder import NashEquilibriumFinder
from src.operators.game_theory.utility import UtilityFunctionGenerator
from src.operators.game_theory.analysis import <PERSON>quilibriumA<PERSON><PERSON><PERSON>


def test_game_theoretic_optimizer():
    """测试GameTheoreticOptimizer算子"""
    print("\n测试GameTheoreticOptimizer算子...")
    
    # 创建算子
    optimizer = GameTheoreticOptimizer(num_players=2, max_iterations=100)
    print(f"创建算子: {optimizer}")
    
    # 创建效用生成器
    generator = UtilityFunctionGenerator(random_seed=42)
    
    # 生成囚徒困境博弈
    row_matrix, col_matrix = generator.generate_prisoners_dilemma()
    print(f"行玩家效用矩阵:\n{row_matrix}")
    print(f"列玩家效用矩阵:\n{col_matrix}")
    
    # 使用最佳响应动态优化
    result = optimizer.apply((row_matrix, col_matrix), method='best_response')
    
    # 打印结果
    print(f"最佳响应动态优化结果:")
    print(f"  策略: {result['strategies']}")
    print(f"  效用: {result['utilities']}")
    print(f"  收敛: {result['converged']}")
    print(f"  迭代次数: {result['iterations']}")
    
    # 使用虚拟对弈优化
    result = optimizer.apply((row_matrix, col_matrix), method='fictitious_play')
    
    # 打印结果
    print(f"虚拟对弈优化结果:")
    print(f"  策略: {result['strategies']}")
    print(f"  效用: {result['utilities']}")
    print(f"  收敛: {result['converged']}")
    print(f"  迭代次数: {result['iterations']}")
    
    print("GameTheoreticOptimizer测试完成")


def test_nash_equilibrium_finder():
    """测试NashEquilibriumFinder算子"""
    print("\n测试NashEquilibriumFinder算子...")
    
    # 创建算子
    finder = NashEquilibriumFinder(method='lemke_howson')
    print(f"创建算子: {finder}")
    
    # 创建效用生成器
    generator = UtilityFunctionGenerator(random_seed=42)
    
    # 生成匹配硬币博弈（零和博弈）
    matching_pennies = generator.generate_matching_pennies()
    print(f"匹配硬币效用矩阵:\n{matching_pennies}")
    
    # 使用Lemke-Howson算法求解
    result = finder.apply(matching_pennies, method='lemke_howson')
    
    # 打印结果
    print(f"Lemke-Howson算法求解结果:")
    print(f"  策略: {result['strategies']}")
    print(f"  效用: {result['utilities']}")
    print(f"  收敛: {result['converged']}")
    print(f"  迭代次数: {result['iterations']}")
    
    # 生成囚徒困境博弈（一般和博弈）
    row_matrix, col_matrix = generator.generate_prisoners_dilemma()
    
    # 使用支撑枚举法求解
    result = finder.apply((row_matrix, col_matrix), method='support_enumeration')
    
    # 打印结果
    print(f"支撑枚举法求解结果:")
    print(f"  策略: {result['strategies']}")
    print(f"  效用: {result['utilities']}")
    print(f"  收敛: {result['converged']}")
    print(f"  迭代次数: {result['iterations']}")
    
    print("NashEquilibriumFinder测试完成")


def test_utility_function_generator():
    """测试UtilityFunctionGenerator"""
    print("\n测试UtilityFunctionGenerator...")
    
    # 创建生成器
    generator = UtilityFunctionGenerator(random_seed=42)
    print(f"创建生成器: {generator}")
    
    # 生成零和博弈
    zero_sum = generator.generate_zero_sum_game(3, 3)
    print(f"零和博弈效用矩阵:\n{zero_sum}")
    
    # 生成一般和博弈
    row_matrix, col_matrix = generator.generate_general_sum_game(3, 3, correlation=0.5)
    print(f"一般和博弈行玩家效用矩阵:\n{row_matrix}")
    print(f"一般和博弈列玩家效用矩阵:\n{col_matrix}")
    
    # 生成经典博弈
    pd_row, pd_col = generator.generate_prisoners_dilemma()
    print(f"囚徒困境博弈效用矩阵:\n{pd_row}\n{pd_col}")
    
    bos_row, bos_col = generator.generate_battle_of_sexes()
    print(f"性别之战博弈效用矩阵:\n{bos_row}\n{bos_col}")
    
    chicken_row, chicken_col = generator.generate_chicken_game()
    print(f"胆小鬼博弈效用矩阵:\n{chicken_row}\n{chicken_col}")
    
    # 生成完整博弈模型
    game_model = generator.generate_game_model('prisoners_dilemma')
    print(f"囚徒困境博弈模型:\n  玩家: {game_model['players']}")
    print(f"  策略空间: {game_model['strategy_spaces']}")
    print(f"  效用矩阵: {game_model['utility_matrices']}")
    print(f"  类型: {game_model['type']}")
    
    print("UtilityFunctionGenerator测试完成")


def test_equilibrium_analyzer():
    """测试EquilibriumAnalyzer"""
    print("\n测试EquilibriumAnalyzer...")
    
    # 创建分析器
    analyzer = EquilibriumAnalyzer(visualization_enabled=False)
    print(f"创建分析器: {analyzer}")
    
    # 创建效用生成器
    generator = UtilityFunctionGenerator(random_seed=42)
    
    # 生成囚徒困境博弈
    game_model = generator.generate_game_model('prisoners_dilemma')
    
    # 创建纳什均衡求解器
    finder = NashEquilibriumFinder(method='support_enumeration')
    
    # 求解均衡
    equilibrium = finder.apply(game_model)
    
    # 验证均衡
    verification = analyzer.verify_nash_equilibrium(game_model, equilibrium['strategies'])
    print(f"均衡验证结果:")
    print(f"  是否为均衡: {verification['is_equilibrium']}")
    print(f"  偏离程度: {verification['deviations']}")
    print(f"  期望效用: {verification['expected_utilities']}")
    
    # 分析均衡性质
    properties = analyzer.analyze_equilibrium_properties(game_model, equilibrium)
    print(f"均衡性质分析结果:")
    print(f"  是否为纯策略均衡: {properties['pure_strategy']}")
    print(f"  是否为混合策略均衡: {properties['mixed_strategy']}")
    print(f"  社会福利: {properties['social_welfare']}")
    
    print("EquilibriumAnalyzer测试完成")


def main():
    """主函数"""
    print("开始测试博弈论算子...")
    
    test_game_theoretic_optimizer()
    test_nash_equilibrium_finder()
    test_utility_function_generator()
    test_equilibrium_analyzer()
    
    print("\n所有测试完成")


if __name__ == "__main__":
    main()
