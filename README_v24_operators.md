# 超越态思维引擎算子库 (PyO3 0.24+ 兼容版本)

本项目包含了超越态思维引擎的核心算子库，这些算子使用Rust实现，并通过PyO3 0.24+绑定到Python。这些算子完全兼容Python 3.13和PyO3 0.24+。

## 已实现的算子

1. **优化多层次解释生成算子** (`explanation_v24`)
   - 生成技术、概念和类比三个层次的解释
   - 支持融合解释，将多个层次的解释融合为一个统一的解释
   - 支持反事实解释，解释模型在输入变化时的行为
   - 支持多语言解释生成

2. **可验证性算子** (`verifiability_v24`)
   - 支持形式化验证、统计验证、经验验证和对抗验证
   - 支持混合验证方法，综合多种验证方法的结果
   - 支持自定义验证属性，如一致性、单调性等
   - 支持验证报告生成，包括验证分数、验证状态和详细信息

3. **自解释性算子** (`self_explainability_v24`)
   - 支持特征重要性解释、局部解释、全局解释、反事实解释、示例解释和规则解释
   - 支持混合解释，综合多种解释方法的结果
   - 支持多种解释格式，如文本、可视化和表格
   - 支持自适应解释复杂度，根据用户需求调整解释的复杂度

## 使用示例

### 安装

```bash
# 从源码构建
cd /path/to/TTE
python build_explanation_operators_v24.py
python build_verifiability_operators_v24.py
python build_self_explainability_operators_v24.py
```

### 基本用法

```python
# 导入算子
import explanation_v24
import verifiability_v24
import self_explainability_v24

# 创建多层次解释生成算子
explanation_operator = explanation_v24.OptimizedMultilevelExplanationOperator(
    levels=["technical", "conceptual", "analogical"],
    max_tokens_per_level=200,
    include_examples=True,
    language="zh-CN"
)

# 创建可验证性算子
verifiability_operator = verifiability_v24.VerifiabilityOperator(
    method="hybrid",
    threshold=0.7,
    timeout_ms=30000
)

# 创建自解释性算子
self_explainability_operator = self_explainability_v24.SelfExplainabilityOperator(
    explanation_type="hybrid",
    explanation_format="mixed",
    explanation_complexity="adaptive"
)

# 应用算子
explanation_result = explanation_operator.apply(input_data)
verifiability_result = verifiability_operator.apply(input_data, properties=properties)
self_explainability_result = self_explainability_operator.apply(input_data)
```

### 组合使用

请参考 `example_combined_operators_v24.py` 文件，了解如何组合使用这些算子。

## 性能优化

这些算子经过了性能优化，包括：

1. 使用Rust实现核心算法，提高计算效率
2. 使用缓存机制，避免重复计算
3. 支持并行处理，提高处理大量数据的效率
4. 优化内存使用，减少内存占用

## 兼容性

这些算子完全兼容Python 3.13和PyO3 0.24+，可以在无GIL环境下运行。

## 测试

每个算子都有对应的测试脚本：

- `test_explanation_v24_full.py`
- `test_verifiability_v24_full.py`
- `test_self_explainability_v24_full.py`

运行这些测试脚本，确保算子正常工作。

## 注意事项

1. 这些算子是超越态思维引擎的核心组件，应该通过统一注册表管理，支持按需组合，灵活搭配。
2. 不要使用Python导入方式进行测试，应该使用注册表机制。
3. 如果遇到兼容性问题，不要使用版本降级的方式解决，应该修复代码以适应最新版本。
4. 单个代码文件超过400行时，建议拆分为多个模块，以便于维护和升级。

## 未来工作

1. 实现更多算子，如动态形态算子、环境敏感算子等
2. 优化算子性能，提高计算效率
3. 增强算子的可解释性和可验证性
4. 支持更多的应用场景和领域
