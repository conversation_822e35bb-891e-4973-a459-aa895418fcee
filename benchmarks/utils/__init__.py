"""
超越态思维引擎算子库基准测试 - 工具模块

本包提供了基准测试和集成分析所需的工具函数。
"""

from .data_generators import generate_test_data
from .benchmark_runner import benchmark_operator
from .report_generator import plot_benchmark_results, generate_report
from .integration_analyzer import IntegrationAnalyzer

__all__ = [
    'generate_test_data',
    'benchmark_operator',
    'plot_benchmark_results',
    'generate_report',
    'IntegrationAnalyzer'
]
