"""
超越态思维引擎算子库基准测试 - 基准测试运行器

本模块提供了运行基准测试的函数。
"""

import logging
import time
import traceback
import tracemalloc
import numpy as np
from typing import Any, Dict, List

# 配置日志
logger = logging.getLogger(__name__)

def benchmark_operator(
    category: str,
    operator_name: str,
    operator_class: Any,
    test_data: List[Dict[str, Any]],
    num_runs: int = 5,
    warmup_runs: int = 2
) -> Dict[str, Any]:
    """
    对算子进行基准测试
    
    参数:
        category: 算子类别
        operator_name: 算子名称
        operator_class: 算子类
        test_data: 测试数据
        num_runs: 运行次数
        warmup_runs: 预热运行次数
        
    返回:
        基准测试结果
    """
    logger.info(f"对 {category}.{operator_name} 进行基准测试...")
    
    # 创建算子实例
    try:
        operator = operator_class()
    except Exception as e:
        logger.error(f"创建 {category}.{operator_name} 实例失败: {e}")
        return {
            "category": category,
            "name": operator_name,
            "error": str(e),
            "traceback": traceback.format_exc()
        }
    
    # 检查测试数据
    if not test_data:
        logger.warning(f"{category}.{operator_name} 没有测试数据，跳过基准测试")
        return {
            "category": category,
            "name": operator_name,
            "error": "没有测试数据",
            "execution_times": [],
            "memory_usages": [],
            "avg_execution_time": 0,
            "avg_memory_usage": 0
        }
    
    # 预热运行
    logger.info(f"{category}.{operator_name} 预热运行...")
    for i in range(warmup_runs):
        try:
            for data in test_data:
                operator.apply(data)
        except Exception as e:
            logger.error(f"{category}.{operator_name} 预热运行失败: {e}")
            return {
                "category": category,
                "name": operator_name,
                "error": str(e),
                "traceback": traceback.format_exc()
            }
    
    # 性能测试
    execution_times = []
    
    for run in range(num_runs):
        logger.info(f"{category}.{operator_name} - 运行 {run + 1}/{num_runs}...")
        
        start_time = time.time()
        
        try:
            for data in test_data:
                operator.apply(data)
        except Exception as e:
            logger.error(f"{category}.{operator_name} 运行失败: {e}")
            return {
                "category": category,
                "name": operator_name,
                "error": str(e),
                "traceback": traceback.format_exc()
            }
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        execution_times.append(elapsed_time)
        
        logger.info(f"{category}.{operator_name} - 运行 {run + 1}/{num_runs} 完成，耗时 {elapsed_time:.4f} 秒")
    
    # 内存使用测试
    logger.info(f"{category}.{operator_name} - 内存使用测试...")
    
    def memory_test_func():
        operator = operator_class()
        for data in test_data:
            operator.apply(data)
    
    tracemalloc.start()
    memory_test_func()
    current, peak = tracemalloc.get_traced_memory()
    tracemalloc.stop()
    
    memory_usage_mb = peak / (1024 * 1024)  # 转换为MB
    
    logger.info(f"{category}.{operator_name} - 内存使用: {memory_usage_mb:.2f} MB")
    
    # 计算平均值和标准差
    avg_execution_time = np.mean(execution_times)
    std_execution_time = np.std(execution_times)
    
    # 返回结果
    return {
        "category": category,
        "name": operator_name,
        "execution_times": execution_times,
        "memory_usage": memory_usage_mb,
        "avg_execution_time": avg_execution_time,
        "std_execution_time": std_execution_time
    }
