"""
超越态思维引擎算子库基准测试 - 报告生成器

本模块提供了生成基准测试报告的函数。
"""

import os
import json
import time
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Any, Dict, List

# 配置日志
logger = logging.getLogger(__name__)

def plot_benchmark_results(results: List[Dict[str, Any]], output_path: str) -> None:
    """
    绘制基准测试结果
    
    参数:
        results: 基准测试结果
        output_path: 输出路径
    """
    # 提取数据
    categories = []
    names = []
    avg_times = []
    std_times = []
    memory_usages = []
    
    for result in results:
        if "error" in result:
            continue
        
        categories.append(result["category"])
        names.append(result["name"])
        avg_times.append(result["avg_execution_time"])
        std_times.append(result.get("std_execution_time", 0))
        memory_usages.append(result["memory_usage"])
    
    # 创建标签
    labels = [f"{cat}.{name}" for cat, name in zip(categories, names)]
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # 执行时间图表
    bars = ax1.bar(labels, avg_times, yerr=std_times, capsize=5)
    ax1.set_ylabel('执行时间 (秒)')
    ax1.set_title('算子执行时间')
    ax1.set_xticklabels(labels, rotation=45, ha='right')
    
    # 为每个柱子添加标签
    for bar, time in zip(bars, avg_times):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                f'{time:.4f}s',
                ha='center', va='bottom', rotation=0)
    
    # 内存使用图表
    bars = ax2.bar(labels, memory_usages)
    ax2.set_ylabel('内存使用 (MB)')
    ax2.set_title('算子内存使用')
    ax2.set_xticklabels(labels, rotation=45, ha='right')
    
    # 为每个柱子添加标签
    for bar, memory in zip(bars, memory_usages):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                f'{memory:.2f}MB',
                ha='center', va='bottom', rotation=0)
    
    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()

def generate_report(results: List[Dict[str, Any]], output_dir: str) -> None:
    """
    生成基准测试报告
    
    参数:
        results: 基准测试结果
        output_dir: 输出目录
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存原始结果
    with open(os.path.join(output_dir, 'benchmark_results.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    # 创建CSV报告
    report_data = []
    
    for result in results:
        if "error" in result:
            report_data.append({
                "Category": result["category"],
                "Operator": result["name"],
                "Status": "Failed",
                "Error": result["error"],
                "Execution Time (s)": "N/A",
                "Memory Usage (MB)": "N/A"
            })
        else:
            report_data.append({
                "Category": result["category"],
                "Operator": result["name"],
                "Status": "Success",
                "Error": "",
                "Execution Time (s)": f"{result['avg_execution_time']:.4f} ± {result.get('std_execution_time', 0):.4f}",
                "Memory Usage (MB)": f"{result['memory_usage']:.2f}"
            })
    
    df = pd.DataFrame(report_data)
    df.to_csv(os.path.join(output_dir, 'benchmark_report.csv'), index=False)
    
    # 创建HTML报告
    html_report = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>超越态思维引擎算子库基准测试报告</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
                line-height: 1.6;
            }
            h1, h2, h3 {
                color: #333;
            }
            table {
                border-collapse: collapse;
                width: 100%;
                margin-bottom: 20px;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }
            th {
                background-color: #f2f2f2;
            }
            tr:nth-child(even) {
                background-color: #f9f9f9;
            }
            .success {
                color: green;
            }
            .failed {
                color: red;
            }
            .chart {
                margin: 20px 0;
                max-width: 100%;
            }
        </style>
    </head>
    <body>
        <h1>超越态思维引擎算子库基准测试报告</h1>
        <p>生成时间: """ + time.strftime("%Y-%m-%d %H:%M:%S") + """</p>
        
        <h2>测试结果摘要</h2>
        <table>
            <tr>
                <th>类别</th>
                <th>算子</th>
                <th>状态</th>
                <th>执行时间 (秒)</th>
                <th>内存使用 (MB)</th>
                <th>错误信息</th>
            </tr>
    """
    
    for row in report_data:
        status_class = "success" if row["Status"] == "Success" else "failed"
        html_report += f"""
            <tr>
                <td>{row["Category"]}</td>
                <td>{row["Operator"]}</td>
                <td class="{status_class}">{row["Status"]}</td>
                <td>{row["Execution Time (s)"]}</td>
                <td>{row["Memory Usage (MB)"]}</td>
                <td>{row["Error"]}</td>
            </tr>
        """
    
    html_report += """
        </table>
        
        <h2>性能图表</h2>
        <img class="chart" src="benchmark_results.png" alt="性能图表">
        
        <h2>详细结果</h2>
        <p>详细结果请查看 <a href="benchmark_results.json">benchmark_results.json</a> 和 <a href="benchmark_report.csv">benchmark_report.csv</a>。</p>
    </body>
    </html>
    """
    
    with open(os.path.join(output_dir, 'benchmark_report.html'), 'w') as f:
        f.write(html_report)
    
    # 绘制图表
    plot_benchmark_results(results, os.path.join(output_dir, 'benchmark_results.png'))
    
    logger.info(f"基准测试报告已生成到 {output_dir}")
