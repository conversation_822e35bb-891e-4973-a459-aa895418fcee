"""
超越态思维引擎算子库 - 集成分析器

本模块提供了分析算子之间集成情况的工具。
"""

import logging
import time
import numpy as np
from typing import Any, Dict, List, Set, Tuple, Optional

# 配置日志
logger = logging.getLogger(__name__)

class IntegrationAnalyzer:
    """
    算子集成分析器
    
    分析算子之间的集成情况，检测潜在的问题，并提供优化建议。
    """
    
    def __init__(self, operator_registry):
        """
        初始化算子集成分析器
        
        参数:
            operator_registry: 算子注册表
        """
        self.registry = operator_registry
        self.categories = self.registry.get_categories()
        self.operators = {}
        self.dependencies = {}
        self.compatibility = {}
        
        # 加载所有算子
        for category in self.categories:
            category_operators = self.registry.get_operators_in_category(category)
            for name, operator_class in category_operators.items():
                self.operators[f"{category}.{name}"] = operator_class
    
    def analyze_dependencies(self) -> Dict[str, List[str]]:
        """
        分析算子之间的依赖关系
        
        返回:
            算子依赖关系字典
        """
        logger.info("分析算子依赖关系...")
        
        dependencies = {}
        
        for operator_id, operator_class in self.operators.items():
            try:
                # 创建算子实例
                operator = operator_class()
                
                # 获取元数据
                metadata = operator.get_metadata() if hasattr(operator, "get_metadata") else {}
                
                # 获取依赖
                deps = metadata.get("dependencies", [])
                
                dependencies[operator_id] = deps
                
                logger.debug(f"算子 {operator_id} 依赖: {deps}")
            except Exception as e:
                logger.warning(f"获取算子 {operator_id} 依赖失败: {e}")
                dependencies[operator_id] = []
        
        self.dependencies = dependencies
        return dependencies
    
    def analyze_compatibility(self) -> Dict[str, Dict[str, bool]]:
        """
        分析算子之间的兼容性
        
        返回:
            算子兼容性字典
        """
        logger.info("分析算子兼容性...")
        
        compatibility = {}
        
        for operator_id, operator_class in self.operators.items():
            compatibility[operator_id] = {}
            
            for other_id, other_class in self.operators.items():
                if operator_id == other_id:
                    compatibility[operator_id][other_id] = True
                    continue
                
                try:
                    # 创建算子实例
                    operator = operator_class()
                    other = other_class()
                    
                    # 获取元数据
                    metadata = operator.get_metadata() if hasattr(operator, "get_metadata") else {}
                    other_metadata = other.get_metadata() if hasattr(other, "get_metadata") else {}
                    
                    # 获取兼容性信息
                    compatible_with = metadata.get("compatible_with", [])
                    incompatible_with = metadata.get("incompatible_with", [])
                    
                    # 检查兼容性
                    if other_id in compatible_with:
                        compatibility[operator_id][other_id] = True
                    elif other_id in incompatible_with:
                        compatibility[operator_id][other_id] = False
                    else:
                        # 默认兼容
                        compatibility[operator_id][other_id] = True
                    
                    logger.debug(f"算子 {operator_id} 与 {other_id} 兼容性: {compatibility[operator_id][other_id]}")
                except Exception as e:
                    logger.warning(f"获取算子 {operator_id} 与 {other_id} 兼容性失败: {e}")
                    compatibility[operator_id][other_id] = False
        
        self.compatibility = compatibility
        return compatibility
    
    def detect_circular_dependencies(self) -> List[List[str]]:
        """
        检测循环依赖
        
        返回:
            循环依赖列表
        """
        logger.info("检测循环依赖...")
        
        if not self.dependencies:
            self.analyze_dependencies()
        
        circular_deps = []
        visited = set()
        path = []
        
        def dfs(node):
            if node in path:
                # 找到循环依赖
                cycle = path[path.index(node):]
                cycle.append(node)
                circular_deps.append(cycle)
                return
            
            if node in visited:
                return
            
            visited.add(node)
            path.append(node)
            
            for dep in self.dependencies.get(node, []):
                if dep in self.operators:
                    dfs(dep)
            
            path.pop()
        
        for operator_id in self.operators:
            dfs(operator_id)
        
        return circular_deps
    
    def detect_missing_dependencies(self) -> Dict[str, List[str]]:
        """
        检测缺失的依赖
        
        返回:
            缺失依赖字典
        """
        logger.info("检测缺失依赖...")
        
        if not self.dependencies:
            self.analyze_dependencies()
        
        missing_deps = {}
        
        for operator_id, deps in self.dependencies.items():
            missing = [dep for dep in deps if dep not in self.operators]
            
            if missing:
                missing_deps[operator_id] = missing
                logger.warning(f"算子 {operator_id} 缺失依赖: {missing}")
        
        return missing_deps
    
    def detect_compatibility_issues(self) -> Dict[str, List[str]]:
        """
        检测兼容性问题
        
        返回:
            兼容性问题字典
        """
        logger.info("检测兼容性问题...")
        
        if not self.compatibility:
            self.analyze_compatibility()
        
        compatibility_issues = {}
        
        for operator_id, compat in self.compatibility.items():
            incompatible = [other_id for other_id, compatible in compat.items() if not compatible]
            
            if incompatible:
                compatibility_issues[operator_id] = incompatible
                logger.warning(f"算子 {operator_id} 与以下算子不兼容: {incompatible}")
        
        return compatibility_issues
    
    def analyze_integration(self) -> Dict[str, Any]:
        """
        分析算子集成情况
        
        返回:
            集成分析结果
        """
        logger.info("分析算子集成情况...")
        
        # 分析依赖关系
        dependencies = self.analyze_dependencies()
        
        # 分析兼容性
        compatibility = self.analyze_compatibility()
        
        # 检测循环依赖
        circular_deps = self.detect_circular_dependencies()
        
        # 检测缺失依赖
        missing_deps = self.detect_missing_dependencies()
        
        # 检测兼容性问题
        compatibility_issues = self.detect_compatibility_issues()
        
        # 生成报告
        report = {
            "total_operators": len(self.operators),
            "categories": len(self.categories),
            "circular_dependencies": circular_deps,
            "missing_dependencies": missing_deps,
            "compatibility_issues": compatibility_issues,
            "operators_with_dependencies": sum(1 for deps in dependencies.values() if deps),
            "operators_with_compatibility_issues": len(compatibility_issues),
            "operators_with_circular_dependencies": len(set(op for cycle in circular_deps for op in cycle)),
            "operators_with_missing_dependencies": len(missing_deps)
        }
        
        return report
    
    def generate_optimization_suggestions(self, report: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        生成优化建议
        
        参数:
            report: 集成分析报告
            
        返回:
            优化建议列表
        """
        logger.info("生成优化建议...")
        
        suggestions = []
        
        # 处理循环依赖
        if report["circular_dependencies"]:
            for cycle in report["circular_dependencies"]:
                suggestions.append({
                    "type": "circular_dependency",
                    "severity": "high",
                    "operators": cycle,
                    "description": f"检测到循环依赖: {' -> '.join(cycle)}",
                    "suggestion": "重构这些算子，移除循环依赖。可以考虑引入中间层或使用依赖注入。"
                })
        
        # 处理缺失依赖
        for operator_id, missing in report["missing_dependencies"].items():
            suggestions.append({
                "type": "missing_dependency",
                "severity": "medium",
                "operator": operator_id,
                "missing": missing,
                "description": f"算子 {operator_id} 缺失依赖: {', '.join(missing)}",
                "suggestion": "实现缺失的依赖算子，或修改算子以移除对缺失依赖的需求。"
            })
        
        # 处理兼容性问题
        for operator_id, incompatible in report["compatibility_issues"].items():
            suggestions.append({
                "type": "compatibility_issue",
                "severity": "medium",
                "operator": operator_id,
                "incompatible": incompatible,
                "description": f"算子 {operator_id} 与以下算子不兼容: {', '.join(incompatible)}",
                "suggestion": "修改算子以提高兼容性，或确保这些不兼容的算子不会同时使用。"
            })
        
        return suggestions
    
    def analyze_and_suggest(self) -> Dict[str, Any]:
        """
        分析算子集成情况并生成优化建议
        
        返回:
            分析结果和优化建议
        """
        logger.info("分析算子集成情况并生成优化建议...")
        
        # 分析集成情况
        report = self.analyze_integration()
        
        # 生成优化建议
        suggestions = self.generate_optimization_suggestions(report)
        
        # 合并结果
        result = {
            "report": report,
            "suggestions": suggestions
        }
        
        return result
