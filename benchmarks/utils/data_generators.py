"""
超越态思维引擎算子库基准测试 - 测试数据生成器

本模块提供了为各种算子生成测试数据的函数。
"""

import logging
import numpy as np
from typing import Any, Dict, List

# 配置日志
logger = logging.getLogger(__name__)

def generate_test_data(category: str, operator_name: str, num_samples: int = 10) -> List[Dict[str, Any]]:
    """
    为指定算子生成测试数据
    
    参数:
        category: 算子类别
        operator_name: 算子名称
        num_samples: 样本数量
        
    返回:
        测试数据列表
    """
    logger.info(f"为 {category}.{operator_name} 生成测试数据...")
    
    # 根据算子类别和名称生成适当的测试数据
    if category == "explanation":
        return generate_explanation_test_data(operator_name, num_samples)
    elif category == "verification":
        return generate_verification_test_data(operator_name, num_samples)
    elif category == "engineering_support":
        return generate_engineering_support_test_data(operator_name, num_samples)
    
    # 默认返回空列表
    logger.warning(f"没有为 {category}.{operator_name} 定义测试数据生成器，返回空列表")
    return []

def generate_explanation_test_data(operator_name: str, num_samples: int) -> List[Dict[str, Any]]:
    """生成解释算子的测试数据"""
    if operator_name == "multilevel_explanation":
        return [
            {
                "model_output": {
                    "prediction": "positive" if np.random.random() > 0.5 else "negative",
                    "confidence": np.random.random(),
                    "features": {
                        "text_length": int(np.random.random() * 1000),
                        "sentiment_score": np.random.random(),
                        "topic_relevance": np.random.random()
                    }
                },
                "explanation_level": "all",
                "user_expertise": np.random.choice(["beginner", "intermediate", "expert"])
            }
            for _ in range(num_samples)
        ]
    elif operator_name == "explanation_quality":
        return [
            {
                "explanation": {
                    "technical": {
                        "trace": [{"step": i, "action": f"action_{i}"} for i in range(5)],
                        "justification": f"Technical justification {i}",
                        "counterfactual": [{"condition": f"condition_{j}", "result": f"result_{j}"} for j in range(3)]
                    },
                    "conceptual": {
                        "trace": [{"concept": f"concept_{i}", "relation": f"relation_{i}"} for i in range(4)],
                        "justification": f"Conceptual justification {i}",
                        "counterfactual": [{"condition": f"condition_{j}", "result": f"result_{j}"} for j in range(2)]
                    },
                    "analogy": {
                        "trace": [{"analogy": f"analogy_{i}", "mapping": f"mapping_{i}"} for i in range(3)],
                        "justification": f"Analogy justification {i}",
                        "counterfactual": [{"condition": f"condition_{j}", "result": f"result_{j}"} for j in range(2)]
                    }
                },
                "metrics": ["completeness", "consistency", "comprehensibility"]
            }
            for i in range(num_samples)
        ]
    elif operator_name == "visualization":
        return [
            {
                "explanation": {
                    "technical": {
                        "trace": [{"step": i, "action": f"action_{i}"} for i in range(5)],
                        "justification": f"Technical justification {i}",
                        "counterfactual": [{"condition": f"condition_{j}", "result": f"result_{j}"} for j in range(3)]
                    },
                    "conceptual": {
                        "trace": [{"concept": f"concept_{i}", "relation": f"relation_{i}"} for i in range(4)],
                        "justification": f"Conceptual justification {i}",
                        "counterfactual": [{"condition": f"condition_{j}", "result": f"result_{j}"} for j in range(2)]
                    },
                    "analogy": {
                        "trace": [{"analogy": f"analogy_{i}", "mapping": f"mapping_{i}"} for i in range(3)],
                        "justification": f"Analogy justification {i}",
                        "counterfactual": [{"condition": f"condition_{j}", "result": f"result_{j}"} for j in range(2)]
                    }
                },
                "visualization_type": np.random.choice(["decision_tree", "feature_importance", "attention_heatmap"]),
                "format": np.random.choice(["png", "svg", "html"]),
                "interactive": np.random.choice([True, False])
            }
            for i in range(num_samples)
        ]
    return []

def generate_verification_test_data(operator_name: str, num_samples: int) -> List[Dict[str, Any]]:
    """生成验证算子的测试数据"""
    if operator_name == "multi_method_verification":
        return [
            {
                "state": {
                    "variables": {
                        "x": np.random.random() * 100,
                        "y": np.random.random() * 100,
                        "z": np.random.random() * 100
                    }
                },
                "properties": [
                    {
                        "id": f"safety{i}",
                        "type": "safety",
                        "expression": f"G(x > {np.random.random() * 10})"
                    },
                    {
                        "id": f"liveness{i}",
                        "type": "liveness",
                        "expression": f"F(y = {int(np.random.random() * 10)})"
                    },
                    {
                        "id": f"runtime{i}",
                        "type": "runtime",
                        "expression": f"x + y < {np.random.random() * 200}"
                    }
                ]
            }
            for i in range(num_samples)
        ]
    elif operator_name == "consistency_verification":
        return [
            {
                "properties": [
                    {
                        "id": f"prop{i}_{j}",
                        "expression": f"property_{i}_{j}",
                        "dependencies": [f"prop{i}_{k}" for k in range(j) if k < j]
                    }
                    for j in range(5)
                ],
                "verification_type": np.random.choice(["logical", "temporal", "causal"])
            }
            for i in range(num_samples)
        ]
    elif operator_name == "realtime_verification":
        return [
            {
                "state": {
                    "variables": {
                        "x": np.random.random() * 100,
                        "y": np.random.random() * 100,
                        "z": np.random.random() * 100
                    }
                },
                "properties": [
                    {
                        "id": f"runtime{i}_{j}",
                        "type": "runtime",
                        "expression": f"x + y < {np.random.random() * 200}"
                    }
                    for j in range(3)
                ],
                "monitoring_interval": np.random.random() * 0.1
            }
            for i in range(num_samples)
        ]
    return []

def generate_engineering_support_test_data(operator_name: str, num_samples: int) -> List[Dict[str, Any]]:
    """生成工程支持算子的测试数据"""
    if operator_name == "plugin_manager":
        return [
            {
                "operation": np.random.choice(["discover", "list", "get_info"]),
                "plugin_id": f"plugin_{i}" if np.random.random() > 0.5 else None,
                "plugin_path": f"/path/to/plugin_{i}" if np.random.random() > 0.5 else None
            }
            for i in range(num_samples)
        ]
    elif operator_name == "smart_logger":
        return [
            {
                "operation": np.random.choice(["log", "query", "get_stats"]),
                "message": f"Log message {i}",
                "level": np.random.choice(["debug", "info", "warning", "error", "critical"]),
                "extra": {f"key_{j}": f"value_{j}" for j in range(int(np.random.random() * 5))}
            }
            for i in range(num_samples)
        ]
    elif operator_name == "evolution_tracker":
        return [
            {
                "operation": np.random.choice(["track_event", "record_metric", "set_stage", "get_stats"]),
                "event_name": f"Event {i}",
                "event_type": f"Type {i}",
                "source": f"Source {i}",
                "severity": np.random.choice(["low", "medium", "high", "critical"]),
                "data": {f"key_{j}": f"value_{j}" for j in range(int(np.random.random() * 5))}
            }
            for i in range(num_samples)
        ]
    return []
