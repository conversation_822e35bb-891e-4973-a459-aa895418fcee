#!/usr/bin/env python3
"""
算子性能基准测试脚本

比较Python和Rust实现的性能。
"""

import os
import sys
import time
import json
import logging
import argparse
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Any, List, Tuple, Optional, Callable

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('..'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 尝试导入算子
try:
    # 解释算子
    from src.operators.explanation.multilevel_explanation import MultilevelExplanationOperator
    from src.operators.explanation.explanation_quality import ExplanationQualityOperator
    from src.operators.explanation.visualization import VisualizationExplanationOperator
    
    # 验证算子
    from src.operators.verification.multi_method_verification import MultiMethodVerificationOperator
    from src.operators.verification.consistency_verification import ConsistencyVerificationOperator
    from src.operators.verification.realtime_verification import RealtimeVerificationOperator
    
    # Rust实现
    try:
        from src.operators.explanation.rust_wrapper import (
            RustMultilevelExplanationOperator,
            RustExplanationQualityOperator,
            RustVisualizationExplanationOperator,
            EXPLANATION_RUST_AVAILABLE
        )
    except ImportError:
        logger.warning("Rust实现的解释算子不可用")
        EXPLANATION_RUST_AVAILABLE = False
    
    try:
        from src.operators.verification.rust_wrapper import (
            RustMultiMethodVerificationOperator,
            RustConsistencyVerificationOperator,
            RustRealtimeVerificationOperator,
            RUST_AVAILABLE as VERIFICATION_RUST_AVAILABLE
        )
    except ImportError:
        logger.warning("Rust实现的验证算子不可用")
        VERIFICATION_RUST_AVAILABLE = False
    
    OPERATORS_AVAILABLE = True
except ImportError as e:
    logger.error(f"导入算子失败: {e}")
    OPERATORS_AVAILABLE = False
    EXPLANATION_RUST_AVAILABLE = False
    VERIFICATION_RUST_AVAILABLE = False


def generate_explanation_test_data(num_samples: int) -> List[Dict[str, Any]]:
    """
    生成解释算子的测试数据
    
    参数:
        num_samples: 样本数量
        
    返回:
        测试数据列表
    """
    test_data = []
    
    for i in range(num_samples):
        # 生成随机特征
        features = {
            "feature1": np.random.random(),
            "feature2": np.random.random(),
            "feature3": np.random.random(),
            "feature4": np.random.random(),
            "feature5": np.random.random(),
        }
        
        # 生成随机预测
        prediction = "positive" if np.random.random() > 0.5 else "negative"
        
        # 生成随机置信度
        confidence = np.random.random()
        
        # 生成随机用户专业水平
        user_expertise = np.random.choice(["beginner", "intermediate", "expert"])
        
        # 生成随机解释级别
        explanation_level = np.random.choice(["technical", "conceptual", "analogical", "all"])
        
        # 构建测试数据
        data = {
            "model_output": {
                "prediction": prediction,
                "confidence": confidence,
                "features": features
            },
            "explanation_level": explanation_level,
            "user_expertise": user_expertise
        }
        
        test_data.append(data)
    
    return test_data


def generate_verification_test_data(num_samples: int) -> List[Dict[str, Any]]:
    """
    生成验证算子的测试数据
    
    参数:
        num_samples: 样本数量
        
    返回:
        测试数据列表
    """
    test_data = []
    
    for i in range(num_samples):
        # 生成随机变量
        variables = {
            "x": np.random.random() * 100,
            "y": np.random.random() * 100,
            "z": np.random.random() * 100,
        }
        
        # 生成随机属性
        properties = [
            {
                "id": f"safety{i}",
                "type": "safety",
                "expression": f"G(x > {np.random.random() * 10})"
            },
            {
                "id": f"liveness{i}",
                "type": "liveness",
                "expression": f"F(y = {np.random.random() * 10})"
            },
            {
                "id": f"runtime{i}",
                "type": "runtime",
                "expression": f"x + y < {np.random.random() * 200}"
            }
        ]
        
        # 构建测试数据
        data = {
            "state": {
                "variables": variables
            },
            "properties": properties
        }
        
        test_data.append(data)
    
    return test_data


def benchmark_operator(
    operator_name: str,
    python_operator: Any,
    rust_operator: Optional[Any],
    test_data: List[Dict[str, Any]],
    num_runs: int = 5
) -> Dict[str, Any]:
    """
    对算子进行基准测试
    
    参数:
        operator_name: 算子名称
        python_operator: Python实现的算子
        rust_operator: Rust实现的算子，如果不可用则为None
        test_data: 测试数据
        num_runs: 运行次数
        
    返回:
        基准测试结果
    """
    logger.info(f"对 {operator_name} 进行基准测试...")
    
    # Python实现的性能
    python_times = []
    
    for run in range(num_runs):
        logger.info(f"Python实现 - 运行 {run + 1}/{num_runs}...")
        
        start_time = time.time()
        
        for data in test_data:
            python_operator.apply(data)
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        python_times.append(elapsed_time)
        
        logger.info(f"Python实现 - 运行 {run + 1}/{num_runs} 完成，耗时 {elapsed_time:.4f} 秒")
    
    # 计算Python实现的平均时间和标准差
    python_mean_time = np.mean(python_times)
    python_std_time = np.std(python_times)
    
    # Rust实现的性能
    rust_times = []
    rust_mean_time = None
    rust_std_time = None
    speedup = None
    
    if rust_operator is not None:
        for run in range(num_runs):
            logger.info(f"Rust实现 - 运行 {run + 1}/{num_runs}...")
            
            start_time = time.time()
            
            for data in test_data:
                rust_operator.apply(data)
            
            end_time = time.time()
            elapsed_time = end_time - start_time
            
            rust_times.append(elapsed_time)
            
            logger.info(f"Rust实现 - 运行 {run + 1}/{num_runs} 完成，耗时 {elapsed_time:.4f} 秒")
        
        # 计算Rust实现的平均时间和标准差
        rust_mean_time = np.mean(rust_times)
        rust_std_time = np.std(rust_times)
        
        # 计算加速比
        speedup = python_mean_time / rust_mean_time
    
    # 构建结果
    result = {
        "operator_name": operator_name,
        "num_samples": len(test_data),
        "num_runs": num_runs,
        "python_times": python_times,
        "python_mean_time": python_mean_time,
        "python_std_time": python_std_time,
        "rust_times": rust_times,
        "rust_mean_time": rust_mean_time,
        "rust_std_time": rust_std_time,
        "speedup": speedup
    }
    
    return result


def plot_benchmark_results(results: List[Dict[str, Any]], output_file: str) -> None:
    """
    绘制基准测试结果
    
    参数:
        results: 基准测试结果
        output_file: 输出文件路径
    """
    # 提取数据
    operator_names = [result["operator_name"] for result in results]
    python_mean_times = [result["python_mean_time"] for result in results]
    python_std_times = [result["python_std_time"] for result in results]
    
    rust_mean_times = []
    rust_std_times = []
    speedups = []
    
    for result in results:
        if result["rust_mean_time"] is not None:
            rust_mean_times.append(result["rust_mean_time"])
            rust_std_times.append(result["rust_std_time"])
            speedups.append(result["speedup"])
        else:
            rust_mean_times.append(0)
            rust_std_times.append(0)
            speedups.append(0)
    
    # 设置图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 设置柱状图的位置
    x = np.arange(len(operator_names))
    width = 0.35
    
    # 绘制执行时间柱状图
    rects1 = ax1.bar(x - width/2, python_mean_times, width, label='Python', yerr=python_std_times, capsize=5)
    rects2 = ax1.bar(x + width/2, rust_mean_times, width, label='Rust', yerr=rust_std_times, capsize=5)
    
    ax1.set_xlabel('算子')
    ax1.set_ylabel('执行时间 (秒)')
    ax1.set_title('Python vs Rust 执行时间')
    ax1.set_xticks(x)
    ax1.set_xticklabels(operator_names, rotation=45, ha='right')
    ax1.legend()
    
    # 添加数据标签
    def add_labels(rects):
        for rect in rects:
            height = rect.get_height()
            if height > 0:
                ax1.annotate(f'{height:.2f}',
                            xy=(rect.get_x() + rect.get_width() / 2, height),
                            xytext=(0, 3),  # 3 points vertical offset
                            textcoords="offset points",
                            ha='center', va='bottom')
    
    add_labels(rects1)
    add_labels(rects2)
    
    # 绘制加速比柱状图
    ax2.bar(x, speedups, width, label='加速比')
    
    ax2.set_xlabel('算子')
    ax2.set_ylabel('加速比')
    ax2.set_title('Rust vs Python 加速比')
    ax2.set_xticks(x)
    ax2.set_xticklabels(operator_names, rotation=45, ha='right')
    
    # 添加数据标签
    for i, v in enumerate(speedups):
        if v > 0:
            ax2.text(i, v + 0.1, f'{v:.2f}x', ha='center')
    
    plt.tight_layout()
    plt.savefig(output_file)
    plt.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='算子性能基准测试')
    parser.add_argument('--num-samples', type=int, default=100, help='每个算子的测试样本数量')
    parser.add_argument('--num-runs', type=int, default=5, help='每个算子的运行次数')
    parser.add_argument('--output-dir', type=str, default='results', help='输出目录')
    args = parser.parse_args()
    
    # 检查算子是否可用
    if not OPERATORS_AVAILABLE:
        logger.error("算子不可用，无法进行基准测试")
        return
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 生成测试数据
    explanation_test_data = generate_explanation_test_data(args.num_samples)
    verification_test_data = generate_verification_test_data(args.num_samples)
    
    # 基准测试结果
    results = []
    
    # 测试多层次解释生成算子
    python_operator = MultilevelExplanationOperator()
    rust_operator = RustMultilevelExplanationOperator() if EXPLANATION_RUST_AVAILABLE else None
    
    result = benchmark_operator(
        "MultilevelExplanationOperator",
        python_operator,
        rust_operator,
        explanation_test_data,
        args.num_runs
    )
    
    results.append(result)
    
    # 测试解释质量评估算子
    python_operator = ExplanationQualityOperator()
    rust_operator = RustExplanationQualityOperator() if EXPLANATION_RUST_AVAILABLE else None
    
    result = benchmark_operator(
        "ExplanationQualityOperator",
        python_operator,
        rust_operator,
        explanation_test_data,
        args.num_runs
    )
    
    results.append(result)
    
    # 测试可视化解释算子
    python_operator = VisualizationExplanationOperator()
    rust_operator = RustVisualizationExplanationOperator() if EXPLANATION_RUST_AVAILABLE else None
    
    result = benchmark_operator(
        "VisualizationExplanationOperator",
        python_operator,
        rust_operator,
        explanation_test_data,
        args.num_runs
    )
    
    results.append(result)
    
    # 测试多方法验证算子
    python_operator = MultiMethodVerificationOperator()
    rust_operator = RustMultiMethodVerificationOperator() if VERIFICATION_RUST_AVAILABLE else None
    
    result = benchmark_operator(
        "MultiMethodVerificationOperator",
        python_operator,
        rust_operator,
        verification_test_data,
        args.num_runs
    )
    
    results.append(result)
    
    # 测试一致性验证算子
    python_operator = ConsistencyVerificationOperator()
    rust_operator = RustConsistencyVerificationOperator() if VERIFICATION_RUST_AVAILABLE else None
    
    result = benchmark_operator(
        "ConsistencyVerificationOperator",
        python_operator,
        rust_operator,
        verification_test_data,
        args.num_runs
    )
    
    results.append(result)
    
    # 测试实时验证算子
    python_operator = RealtimeVerificationOperator()
    rust_operator = RustRealtimeVerificationOperator() if VERIFICATION_RUST_AVAILABLE else None
    
    result = benchmark_operator(
        "RealtimeVerificationOperator",
        python_operator,
        rust_operator,
        verification_test_data,
        args.num_runs
    )
    
    results.append(result)
    
    # 保存结果
    with open(os.path.join(args.output_dir, 'benchmark_results.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    # 绘制结果
    plot_benchmark_results(results, os.path.join(args.output_dir, 'benchmark_results.png'))
    
    # 打印结果摘要
    print("\n基准测试结果摘要:")
    print(f"{'算子名称':<30} {'Python (秒)':<15} {'Rust (秒)':<15} {'加速比':<10}")
    print("-" * 70)
    
    for result in results:
        operator_name = result["operator_name"]
        python_mean_time = result["python_mean_time"]
        rust_mean_time = result["rust_mean_time"] if result["rust_mean_time"] is not None else "N/A"
        speedup = result["speedup"] if result["speedup"] is not None else "N/A"
        
        if isinstance(rust_mean_time, float):
            rust_mean_time_str = f"{rust_mean_time:.4f}"
        else:
            rust_mean_time_str = rust_mean_time
        
        if isinstance(speedup, float):
            speedup_str = f"{speedup:.2f}x"
        else:
            speedup_str = speedup
        
        print(f"{operator_name:<30} {python_mean_time:.4f}         {rust_mean_time_str:<15} {speedup_str:<10}")


if __name__ == "__main__":
    main()
