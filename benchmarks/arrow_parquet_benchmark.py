#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
超越态计算框架 - Arrow与Parquet存储性能基准测试

本脚本用于测试Arrow扩展类型和Parquet存储的性能，包括：
1. 复数数组转换性能
2. 量子态操作性能
3. 张量操作性能
4. Parquet读写性能
5. 多级缓存模拟测试
"""

import os
import time
import numpy as np
import pyarrow as pa
import tempfile
import matplotlib.pyplot as plt
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass

# 导入Arrow扩展模块
from src.core.data.arrow_types.complex_array import (
    numpy_to_arrow_complex64, arrow_to_numpy_complex64,
    numpy_to_arrow_complex128, arrow_to_numpy_complex128
)
from src.core.data.arrow_types.quantum_state_array import (
    numpy_to_arrow_quantum_state, arrow_to_numpy_quantum_state
)
from src.core.data.arrow_types.tensor_array import (
    numpy_to_arrow_tensor, arrow_to_numpy_tensor
)
from src.core.data.arrow_types.parquet_storage import (
    ParquetStorageOptions,
    write_arrow_to_parquet, read_parquet_to_arrow,
    write_numpy_to_parquet, read_parquet_to_numpy
)


@dataclass
class BenchmarkResult:
    """基准测试结果类"""
    name: str
    operation: str
    sizes: List[int]
    times: List[float]
    memory_usage: Optional[List[float]] = None
    
    def average_time(self) -> float:
        """计算平均时间"""
        return sum(self.times) / len(self.times) if self.times else 0
    
    def throughput(self, data_size: List[float]) -> List[float]:
        """计算吞吐量 (MB/s)"""
        return [size / (time * 1e6) for size, time in zip(data_size, self.times)]


class ArrowParquetBenchmark:
    """Arrow与Parquet存储性能基准测试类"""
    
    def __init__(self, output_dir: str = "./benchmark_results"):
        """初始化基准测试类
        
        Args:
            output_dir: 输出目录，用于保存测试结果和图表
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True, parents=True)
        self.results: Dict[str, List[BenchmarkResult]] = {}
        
        # 临时目录，用于存储测试文件
        self.temp_dir = Path(tempfile.mkdtemp(prefix="tte_benchmark_"))
        print(f"临时目录: {self.temp_dir}")
    
    def _time_function(self, func, *args, **kwargs) -> float:
        """测量函数执行时间
        
        Args:
            func: 要测量的函数
            *args, **kwargs: 函数参数
            
        Returns:
            执行时间（秒）
        """
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        return end_time - start_time, result
    
    def benchmark_complex_conversion(self, 
                                    sizes: List[int] = [1000, 10000, 100000, 1000000],
                                    dtype: np.dtype = np.complex128,
                                    repeats: int = 5) -> BenchmarkResult:
        """基准测试复数数组转换性能
        
        Args:
            sizes: 测试的数组大小列表
            dtype: 复数数据类型
            repeats: 每个大小重复测试的次数
            
        Returns:
            基准测试结果
        """
        print(f"\n测试复数数组转换性能 (dtype={dtype})...")
        to_arrow_times = []
        from_arrow_times = []
        
        for size in sizes:
            print(f"  测试大小: {size}")
            to_arrow_time_sum = 0
            from_arrow_time_sum = 0
            
            for _ in range(repeats):
                # 创建随机复数数组
                data = np.random.random(size) + 1j * np.random.random(size)
                data = data.astype(dtype)
                
                # 测试NumPy到Arrow的转换
                if dtype == np.complex64:
                    to_arrow_time, arrow_array = self._time_function(
                        numpy_to_arrow_complex64, data
                    )
                else:
                    to_arrow_time, arrow_array = self._time_function(
                        numpy_to_arrow_complex128, data
                    )
                to_arrow_time_sum += to_arrow_time
                
                # 测试Arrow到NumPy的转换
                if dtype == np.complex64:
                    from_arrow_time, _ = self._time_function(
                        arrow_to_numpy_complex64, arrow_array
                    )
                else:
                    from_arrow_time, _ = self._time_function(
                        arrow_to_numpy_complex128, arrow_array
                    )
                from_arrow_time_sum += from_arrow_time
            
            to_arrow_times.append(to_arrow_time_sum / repeats)
            from_arrow_times.append(from_arrow_time_sum / repeats)
        
        # 创建并返回结果
        to_arrow_result = BenchmarkResult(
            name=f"Complex{64 if dtype == np.complex64 else 128}",
            operation="NumPy到Arrow转换",
            sizes=sizes,
            times=to_arrow_times
        )
        
        from_arrow_result = BenchmarkResult(
            name=f"Complex{64 if dtype == np.complex64 else 128}",
            operation="Arrow到NumPy转换",
            sizes=sizes,
            times=from_arrow_times
        )
        
        # 存储结果
        if "complex_conversion" not in self.results:
            self.results["complex_conversion"] = []
        
        self.results["complex_conversion"].extend([to_arrow_result, from_arrow_result])
        
        return to_arrow_result, from_arrow_result
    
    def benchmark_quantum_state_operations(self,
                                         sizes: List[int] = [8, 16, 32, 64, 128, 256],
                                         repeats: int = 5) -> BenchmarkResult:
        """基准测试量子态操作性能
        
        Args:
            sizes: 测试的量子态大小列表（2^n个振幅）
            repeats: 每个大小重复测试的次数
            
        Returns:
            基准测试结果
        """
        print("\n测试量子态操作性能...")
        to_arrow_times = []
        from_arrow_times = []
        
        for size in sizes:
            print(f"  测试量子态大小: {size}")
            to_arrow_time_sum = 0
            from_arrow_time_sum = 0
            
            for _ in range(repeats):
                # 创建随机量子态（复振幅）
                # 注意：这里创建的不是物理上有效的量子态，仅用于性能测试
                amplitudes = np.random.random(size) + 1j * np.random.random(size)
                # 归一化
                amplitudes = amplitudes / np.sqrt(np.sum(np.abs(amplitudes)**2))
                
                # 创建量子态表示（实部和虚部分开存储）
                quantum_state = np.column_stack((amplitudes.real, amplitudes.imag))
                
                # 测试NumPy到Arrow的转换
                to_arrow_time, arrow_array = self._time_function(
                    numpy_to_arrow_quantum_state, quantum_state, True
                )
                to_arrow_time_sum += to_arrow_time
                
                # 测试Arrow到NumPy的转换
                from_arrow_time, _ = self._time_function(
                    arrow_to_numpy_quantum_state, arrow_array
                )
                from_arrow_time_sum += from_arrow_time
            
            to_arrow_times.append(to_arrow_time_sum / repeats)
            from_arrow_times.append(from_arrow_time_sum / repeats)
        
        # 创建并返回结果
        to_arrow_result = BenchmarkResult(
            name="QuantumState",
            operation="NumPy到Arrow转换",
            sizes=sizes,
            times=to_arrow_times
        )
        
        from_arrow_result = BenchmarkResult(
            name="QuantumState",
            operation="Arrow到NumPy转换",
            sizes=sizes,
            times=from_arrow_times
        )
        
        # 存储结果
        if "quantum_state_operations" not in self.results:
            self.results["quantum_state_operations"] = []
        
        self.results["quantum_state_operations"].extend([to_arrow_result, from_arrow_result])
        
        return to_arrow_result, from_arrow_result
    
    def benchmark_tensor_operations(self,
                                  shapes: List[Tuple[int, ...]] = [(10, 10), (100, 100), (10, 10, 10), (100, 10, 10)],
                                  repeats: int = 5) -> BenchmarkResult:
        """基准测试张量操作性能
        
        Args:
            shapes: 测试的张量形状列表
            repeats: 每个形状重复测试的次数
            
        Returns:
            基准测试结果
        """
        print("\n测试张量操作性能...")
        to_arrow_times = []
        from_arrow_times = []
        sizes = [np.prod(shape) for shape in shapes]
        
        for i, shape in enumerate(shapes):
            print(f"  测试张量形状: {shape}")
            to_arrow_time_sum = 0
            from_arrow_time_sum = 0
            
            for _ in range(repeats):
                # 创建随机张量
                tensor = np.random.random(shape)
                
                # 测试NumPy到Arrow的转换
                to_arrow_time, arrow_array = self._time_function(
                    numpy_to_arrow_tensor, tensor
                )
                to_arrow_time_sum += to_arrow_time
                
                # 测试Arrow到NumPy的转换
                from_arrow_time, _ = self._time_function(
                    arrow_to_numpy_tensor, arrow_array
                )
                from_arrow_time_sum += from_arrow_time
            
            to_arrow_times.append(to_arrow_time_sum / repeats)
            from_arrow_times.append(from_arrow_time_sum / repeats)
        
        # 创建并返回结果
        to_arrow_result = BenchmarkResult(
            name="Tensor",
            operation="NumPy到Arrow转换",
            sizes=sizes,
            times=to_arrow_times
        )
        
        from_arrow_result = BenchmarkResult(
            name="Tensor",
            operation="Arrow到NumPy转换",
            sizes=sizes,
            times=from_arrow_times
        )
        
        # 存储结果
        if "tensor_operations" not in self.results:
            self.results["tensor_operations"] = []
        
        self.results["tensor_operations"].extend([to_arrow_result, from_arrow_result])
        
        return to_arrow_result, from_arrow_result
    
    def benchmark_parquet_io(self,
                           sizes: List[int] = [1000, 10000, 100000],
                           compression: str = "snappy",
                           repeats: int = 3) -> BenchmarkResult:
        """基准测试Parquet读写性能
        
        Args:
            sizes: 测试的数组大小列表
            compression: 压缩算法
            repeats: 每个大小重复测试的次数
            
        Returns:
            基准测试结果
        """
        print(f"\n测试Parquet读写性能 (compression={compression})...")
        write_times = []
        read_times = []
        
        options = ParquetStorageOptions(compression=compression)
        
        for size in sizes:
            print(f"  测试大小: {size}")
            write_time_sum = 0
            read_time_sum = 0
            
            for r in range(repeats):
                # 创建测试数据
                complex_data = np.random.random(size) + 1j * np.random.random(size)
                complex_data = complex_data.astype(np.complex128)
                tensor_data = np.random.random((size // 10, 10))
                
                # 转换为Arrow数组
                arrow_complex = numpy_to_arrow_complex128(complex_data)
                arrow_tensor = numpy_to_arrow_tensor(tensor_data)
                
                # 创建文件路径
                file_path = self.temp_dir / f"benchmark_data_{size}_{r}.parquet"
                
                # 测试写入性能
                write_time, _ = self._time_function(
                    write_arrow_to_parquet,
                    [arrow_complex, arrow_tensor],
                    ["complex_data", "tensor_data"],
                    str(file_path),
                    options
                )
                write_time_sum += write_time
                
                # 测试读取性能
                read_time, _ = self._time_function(
                    read_parquet_to_arrow,
                    str(file_path),
                    None,
                    options
                )
                read_time_sum += read_time
                
                # 清理文件
                if file_path.exists():
                    file_path.unlink()
            
            write_times.append(write_time_sum / repeats)
            read_times.append(read_time_sum / repeats)
        
        # 创建并返回结果
        write_result = BenchmarkResult(
            name=f"Parquet_{compression}",
            operation="写入",
            sizes=sizes,
            times=write_times
        )
        
        read_result = BenchmarkResult(
            name=f"Parquet_{compression}",
            operation="读取",
            sizes=sizes,
            times=read_times
        )
        
        # 存储结果
        if "parquet_io" not in self.results:
            self.results["parquet_io"] = []
        
        self.results["parquet_io"].extend([write_result, read_result])
        
        return write_result, read_result
    
    def plot_results(self):
        """绘制基准测试结果图表"""
        print("\n绘制基准测试结果图表...")
        
        for test_name, results in self.results.items():
            plt.figure(figsize=(12, 8))
            
            # 按操作类型分组
            operations = {}
            for result in results:
                if result.operation not in operations:
                    operations[result.operation] = []
                operations[result.operation].append(result)
            
            # 为每种操作创建子图
            fig, axes = plt.subplots(len(operations), 1, figsize=(12, 6 * len(operations)))
            if len(operations) == 1:
                axes = [axes]
            
            for i, (operation, op_results) in enumerate(operations.items()):
                ax = axes[i]
                
                for result in op_results:
                    ax.plot(result.sizes, result.times, 'o-', label=result.name)
                
                ax.set_title(f"{test_name} - {operation}")
                ax.set_xlabel("数据大小")
                ax.set_ylabel("时间 (秒)")
                ax.set_xscale('log')
                ax.set_yscale('log')
                ax.grid(True, which="both", ls="--")
                ax.legend()
            
            plt.tight_layout()
            plt.savefig(self.output_dir / f"{test_name}_benchmark.png")
            plt.close()
    
    def run_all_benchmarks(self):
        """运行所有基准测试"""
        print("开始运行所有基准测试...")
        
        # 复数数组转换测试
        self.benchmark_complex_conversion(dtype=np.complex64)
        self.benchmark_complex_conversion(dtype=np.complex128)
        
        # 量子态操作测试
        self.benchmark_quantum_state_operations()
        
        # 张量操作测试
        self.benchmark_tensor_operations()
        
        # Parquet读写测试
        for compression in ["snappy", "zstd", "gzip", None]:
            self.benchmark_parquet_io(compression=compression)
        
        # 绘制结果
        self.plot_results()
        
        print(f"\n基准测试完成，结果保存在 {self.output_dir}")
    
    def cleanup(self):
        """清理临时文件"""
        import shutil
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
            print(f"已清理临时目录: {self.temp_dir}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Arrow与Parquet存储性能基准测试")
    parser.add_argument("--output-dir", type=str, default="./benchmark_results",
                        help="输出目录，用于保存测试结果和图表")
    args = parser.parse_args()
    
    benchmark = ArrowParquetBenchmark(output_dir=args.output_dir)
    
    try:
        benchmark.run_all_benchmarks()
    finally:
        benchmark.cleanup()


if __name__ == "__main__":
    main()
