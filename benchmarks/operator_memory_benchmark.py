#!/usr/bin/env python3
"""
算子内存使用基准测试脚本

比较Python和Rust实现的内存使用情况。
"""

import os
import sys
import json
import logging
import argparse
import numpy as np
import matplotlib.pyplot as plt
import psutil
import gc
from typing import Dict, Any, List, Tuple, Optional, Callable
from memory_profiler import memory_usage

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('..'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 尝试导入算子
try:
    # 解释算子
    from src.operators.explanation.multilevel_explanation import MultilevelExplanationOperator
    from src.operators.explanation.explanation_quality import ExplanationQualityOperator
    from src.operators.explanation.visualization import VisualizationExplanationOperator
    
    # 验证算子
    from src.operators.verification.multi_method_verification import MultiMethodVerificationOperator
    from src.operators.verification.consistency_verification import ConsistencyVerificationOperator
    from src.operators.verification.realtime_verification import RealtimeVerificationOperator
    
    # Rust实现
    try:
        from src.operators.explanation.rust_wrapper import (
            RustMultilevelExplanationOperator,
            RustExplanationQualityOperator,
            RustVisualizationExplanationOperator,
            EXPLANATION_RUST_AVAILABLE
        )
    except ImportError:
        logger.warning("Rust实现的解释算子不可用")
        EXPLANATION_RUST_AVAILABLE = False
    
    try:
        from src.operators.verification.rust_wrapper import (
            RustMultiMethodVerificationOperator,
            RustConsistencyVerificationOperator,
            RustRealtimeVerificationOperator,
            RUST_AVAILABLE as VERIFICATION_RUST_AVAILABLE
        )
    except ImportError:
        logger.warning("Rust实现的验证算子不可用")
        VERIFICATION_RUST_AVAILABLE = False
    
    OPERATORS_AVAILABLE = True
except ImportError as e:
    logger.error(f"导入算子失败: {e}")
    OPERATORS_AVAILABLE = False
    EXPLANATION_RUST_AVAILABLE = False
    VERIFICATION_RUST_AVAILABLE = False


def generate_explanation_test_data(num_samples: int) -> List[Dict[str, Any]]:
    """
    生成解释算子的测试数据
    
    参数:
        num_samples: 样本数量
        
    返回:
        测试数据列表
    """
    test_data = []
    
    for i in range(num_samples):
        # 生成随机特征
        features = {
            "feature1": np.random.random(),
            "feature2": np.random.random(),
            "feature3": np.random.random(),
            "feature4": np.random.random(),
            "feature5": np.random.random(),
        }
        
        # 生成随机预测
        prediction = "positive" if np.random.random() > 0.5 else "negative"
        
        # 生成随机置信度
        confidence = np.random.random()
        
        # 生成随机用户专业水平
        user_expertise = np.random.choice(["beginner", "intermediate", "expert"])
        
        # 生成随机解释级别
        explanation_level = np.random.choice(["technical", "conceptual", "analogical", "all"])
        
        # 构建测试数据
        data = {
            "model_output": {
                "prediction": prediction,
                "confidence": confidence,
                "features": features
            },
            "explanation_level": explanation_level,
            "user_expertise": user_expertise
        }
        
        test_data.append(data)
    
    return test_data


def generate_verification_test_data(num_samples: int) -> List[Dict[str, Any]]:
    """
    生成验证算子的测试数据
    
    参数:
        num_samples: 样本数量
        
    返回:
        测试数据列表
    """
    test_data = []
    
    for i in range(num_samples):
        # 生成随机变量
        variables = {
            "x": np.random.random() * 100,
            "y": np.random.random() * 100,
            "z": np.random.random() * 100,
        }
        
        # 生成随机属性
        properties = [
            {
                "id": f"safety{i}",
                "type": "safety",
                "expression": f"G(x > {np.random.random() * 10})"
            },
            {
                "id": f"liveness{i}",
                "type": "liveness",
                "expression": f"F(y = {np.random.random() * 10})"
            },
            {
                "id": f"runtime{i}",
                "type": "runtime",
                "expression": f"x + y < {np.random.random() * 200}"
            }
        ]
        
        # 构建测试数据
        data = {
            "state": {
                "variables": variables
            },
            "properties": properties
        }
        
        test_data.append(data)
    
    return test_data


def measure_memory_usage(func: Callable, *args, **kwargs) -> Tuple[float, float]:
    """
    测量函数的内存使用情况
    
    参数:
        func: 要测量的函数
        *args: 函数的位置参数
        **kwargs: 函数的关键字参数
        
    返回:
        (内存使用增量, 最大内存使用量)
    """
    # 强制垃圾回收
    gc.collect()
    
    # 测量内存使用
    process = psutil.Process(os.getpid())
    mem_before = process.memory_info().rss / 1024 / 1024  # MB
    
    # 使用memory_profiler测量最大内存使用量
    mem_usage = memory_usage((func, args, kwargs), interval=0.1, timeout=None, max_iterations=1)
    max_mem = max(mem_usage)
    
    # 测量函数执行后的内存使用
    mem_after = process.memory_info().rss / 1024 / 1024  # MB
    
    # 计算内存使用增量
    mem_diff = mem_after - mem_before
    
    return mem_diff, max_mem


def benchmark_operator_memory(
    operator_name: str,
    python_operator: Any,
    rust_operator: Optional[Any],
    test_data: List[Dict[str, Any]],
    num_runs: int = 3
) -> Dict[str, Any]:
    """
    对算子进行内存使用基准测试
    
    参数:
        operator_name: 算子名称
        python_operator: Python实现的算子
        rust_operator: Rust实现的算子，如果不可用则为None
        test_data: 测试数据
        num_runs: 运行次数
        
    返回:
        基准测试结果
    """
    logger.info(f"对 {operator_name} 进行内存使用基准测试...")
    
    # Python实现的内存使用
    python_mem_diffs = []
    python_max_mems = []
    
    for run in range(num_runs):
        logger.info(f"Python实现 - 运行 {run + 1}/{num_runs}...")
        
        # 定义测试函数
        def test_func():
            for data in test_data:
                python_operator.apply(data)
        
        # 测量内存使用
        mem_diff, max_mem = measure_memory_usage(test_func)
        
        python_mem_diffs.append(mem_diff)
        python_max_mems.append(max_mem)
        
        logger.info(f"Python实现 - 运行 {run + 1}/{num_runs} 完成，内存使用增量 {mem_diff:.2f} MB，最大内存使用量 {max_mem:.2f} MB")
    
    # 计算Python实现的平均内存使用和标准差
    python_mean_mem_diff = np.mean(python_mem_diffs)
    python_std_mem_diff = np.std(python_mem_diffs)
    python_mean_max_mem = np.mean(python_max_mems)
    python_std_max_mem = np.std(python_max_mems)
    
    # Rust实现的内存使用
    rust_mem_diffs = []
    rust_max_mems = []
    rust_mean_mem_diff = None
    rust_std_mem_diff = None
    rust_mean_max_mem = None
    rust_std_max_mem = None
    mem_reduction = None
    
    if rust_operator is not None:
        for run in range(num_runs):
            logger.info(f"Rust实现 - 运行 {run + 1}/{num_runs}...")
            
            # 定义测试函数
            def test_func():
                for data in test_data:
                    rust_operator.apply(data)
            
            # 测量内存使用
            mem_diff, max_mem = measure_memory_usage(test_func)
            
            rust_mem_diffs.append(mem_diff)
            rust_max_mems.append(max_mem)
            
            logger.info(f"Rust实现 - 运行 {run + 1}/{num_runs} 完成，内存使用增量 {mem_diff:.2f} MB，最大内存使用量 {max_mem:.2f} MB")
        
        # 计算Rust实现的平均内存使用和标准差
        rust_mean_mem_diff = np.mean(rust_mem_diffs)
        rust_std_mem_diff = np.std(rust_mem_diffs)
        rust_mean_max_mem = np.mean(rust_max_mems)
        rust_std_max_mem = np.std(rust_max_mems)
        
        # 计算内存减少比例
        if python_mean_max_mem > 0:
            mem_reduction = 1.0 - (rust_mean_max_mem / python_mean_max_mem)
    
    # 构建结果
    result = {
        "operator_name": operator_name,
        "num_samples": len(test_data),
        "num_runs": num_runs,
        "python_mem_diffs": python_mem_diffs,
        "python_mean_mem_diff": python_mean_mem_diff,
        "python_std_mem_diff": python_std_mem_diff,
        "python_max_mems": python_max_mems,
        "python_mean_max_mem": python_mean_max_mem,
        "python_std_max_mem": python_std_max_mem,
        "rust_mem_diffs": rust_mem_diffs,
        "rust_mean_mem_diff": rust_mean_mem_diff,
        "rust_std_mem_diff": rust_std_mem_diff,
        "rust_max_mems": rust_max_mems,
        "rust_mean_max_mem": rust_mean_max_mem,
        "rust_std_max_mem": rust_std_max_mem,
        "mem_reduction": mem_reduction
    }
    
    return result


def plot_memory_benchmark_results(results: List[Dict[str, Any]], output_file: str) -> None:
    """
    绘制内存使用基准测试结果
    
    参数:
        results: 基准测试结果
        output_file: 输出文件路径
    """
    # 提取数据
    operator_names = [result["operator_name"] for result in results]
    python_mean_max_mems = [result["python_mean_max_mem"] for result in results]
    python_std_max_mems = [result["python_std_max_mem"] for result in results]
    
    rust_mean_max_mems = []
    rust_std_max_mems = []
    mem_reductions = []
    
    for result in results:
        if result["rust_mean_max_mem"] is not None:
            rust_mean_max_mems.append(result["rust_mean_max_mem"])
            rust_std_max_mems.append(result["rust_std_max_mem"])
            mem_reductions.append(result["mem_reduction"] * 100)  # 转换为百分比
        else:
            rust_mean_max_mems.append(0)
            rust_std_max_mems.append(0)
            mem_reductions.append(0)
    
    # 设置图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 设置柱状图的位置
    x = np.arange(len(operator_names))
    width = 0.35
    
    # 绘制内存使用柱状图
    rects1 = ax1.bar(x - width/2, python_mean_max_mems, width, label='Python', yerr=python_std_max_mems, capsize=5)
    rects2 = ax1.bar(x + width/2, rust_mean_max_mems, width, label='Rust', yerr=rust_std_max_mems, capsize=5)
    
    ax1.set_xlabel('算子')
    ax1.set_ylabel('最大内存使用量 (MB)')
    ax1.set_title('Python vs Rust 内存使用')
    ax1.set_xticks(x)
    ax1.set_xticklabels(operator_names, rotation=45, ha='right')
    ax1.legend()
    
    # 添加数据标签
    def add_labels(rects):
        for rect in rects:
            height = rect.get_height()
            if height > 0:
                ax1.annotate(f'{height:.1f}',
                            xy=(rect.get_x() + rect.get_width() / 2, height),
                            xytext=(0, 3),  # 3 points vertical offset
                            textcoords="offset points",
                            ha='center', va='bottom')
    
    add_labels(rects1)
    add_labels(rects2)
    
    # 绘制内存减少比例柱状图
    ax2.bar(x, mem_reductions, width, label='内存减少比例')
    
    ax2.set_xlabel('算子')
    ax2.set_ylabel('内存减少比例 (%)')
    ax2.set_title('Rust vs Python 内存减少比例')
    ax2.set_xticks(x)
    ax2.set_xticklabels(operator_names, rotation=45, ha='right')
    
    # 添加数据标签
    for i, v in enumerate(mem_reductions):
        if v > 0:
            ax2.text(i, v + 1, f'{v:.1f}%', ha='center')
    
    plt.tight_layout()
    plt.savefig(output_file)
    plt.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='算子内存使用基准测试')
    parser.add_argument('--num-samples', type=int, default=100, help='每个算子的测试样本数量')
    parser.add_argument('--num-runs', type=int, default=3, help='每个算子的运行次数')
    parser.add_argument('--output-dir', type=str, default='results', help='输出目录')
    args = parser.parse_args()
    
    # 检查算子是否可用
    if not OPERATORS_AVAILABLE:
        logger.error("算子不可用，无法进行基准测试")
        return
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 生成测试数据
    explanation_test_data = generate_explanation_test_data(args.num_samples)
    verification_test_data = generate_verification_test_data(args.num_samples)
    
    # 基准测试结果
    results = []
    
    # 测试多层次解释生成算子
    python_operator = MultilevelExplanationOperator()
    rust_operator = RustMultilevelExplanationOperator() if EXPLANATION_RUST_AVAILABLE else None
    
    result = benchmark_operator_memory(
        "MultilevelExplanationOperator",
        python_operator,
        rust_operator,
        explanation_test_data,
        args.num_runs
    )
    
    results.append(result)
    
    # 测试解释质量评估算子
    python_operator = ExplanationQualityOperator()
    rust_operator = RustExplanationQualityOperator() if EXPLANATION_RUST_AVAILABLE else None
    
    result = benchmark_operator_memory(
        "ExplanationQualityOperator",
        python_operator,
        rust_operator,
        explanation_test_data,
        args.num_runs
    )
    
    results.append(result)
    
    # 测试可视化解释算子
    python_operator = VisualizationExplanationOperator()
    rust_operator = RustVisualizationExplanationOperator() if EXPLANATION_RUST_AVAILABLE else None
    
    result = benchmark_operator_memory(
        "VisualizationExplanationOperator",
        python_operator,
        rust_operator,
        explanation_test_data,
        args.num_runs
    )
    
    results.append(result)
    
    # 测试多方法验证算子
    python_operator = MultiMethodVerificationOperator()
    rust_operator = RustMultiMethodVerificationOperator() if VERIFICATION_RUST_AVAILABLE else None
    
    result = benchmark_operator_memory(
        "MultiMethodVerificationOperator",
        python_operator,
        rust_operator,
        verification_test_data,
        args.num_runs
    )
    
    results.append(result)
    
    # 测试一致性验证算子
    python_operator = ConsistencyVerificationOperator()
    rust_operator = RustConsistencyVerificationOperator() if VERIFICATION_RUST_AVAILABLE else None
    
    result = benchmark_operator_memory(
        "ConsistencyVerificationOperator",
        python_operator,
        rust_operator,
        verification_test_data,
        args.num_runs
    )
    
    results.append(result)
    
    # 测试实时验证算子
    python_operator = RealtimeVerificationOperator()
    rust_operator = RustRealtimeVerificationOperator() if VERIFICATION_RUST_AVAILABLE else None
    
    result = benchmark_operator_memory(
        "RealtimeVerificationOperator",
        python_operator,
        rust_operator,
        verification_test_data,
        args.num_runs
    )
    
    results.append(result)
    
    # 保存结果
    with open(os.path.join(args.output_dir, 'memory_benchmark_results.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    # 绘制结果
    plot_memory_benchmark_results(results, os.path.join(args.output_dir, 'memory_benchmark_results.png'))
    
    # 打印结果摘要
    print("\n内存使用基准测试结果摘要:")
    print(f"{'算子名称':<30} {'Python (MB)':<15} {'Rust (MB)':<15} {'减少比例':<10}")
    print("-" * 70)
    
    for result in results:
        operator_name = result["operator_name"]
        python_mean_max_mem = result["python_mean_max_mem"]
        rust_mean_max_mem = result["rust_mean_max_mem"] if result["rust_mean_max_mem"] is not None else "N/A"
        mem_reduction = result["mem_reduction"] if result["mem_reduction"] is not None else "N/A"
        
        if isinstance(rust_mean_max_mem, float):
            rust_mean_max_mem_str = f"{rust_mean_max_mem:.2f}"
        else:
            rust_mean_max_mem_str = rust_mean_max_mem
        
        if isinstance(mem_reduction, float):
            mem_reduction_str = f"{mem_reduction * 100:.1f}%"
        else:
            mem_reduction_str = mem_reduction
        
        print(f"{operator_name:<30} {python_mean_max_mem:.2f}         {rust_mean_max_mem_str:<15} {mem_reduction_str:<10}")


if __name__ == "__main__":
    main()
