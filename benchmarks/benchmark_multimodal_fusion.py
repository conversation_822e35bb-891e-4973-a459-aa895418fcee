"""多模态融合编码系统基准测试"""

import numpy as np
import time
import logging
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt

from src.transcendental_tensor.multimodal_fusion.multimodal_fusion_operator import (
    MultimodalFusionOperator,
    ModalityType,
    ModalityConfig
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FusionBenchmark:
    """融合基准测试类"""
    
    def __init__(self):
        self.fusion_op = MultimodalFusionOperator()
        self.results = {}
        
    def setup_modalities(self):
        """设置测试模态"""
        modalities = {
            "quantum_small": ModalityConfig(
                type=ModalityType.QUANTUM,
                dimension=8,
                encoding_params={"encoding_type": "standard"}
            ),
            "quantum_large": ModalityConfig(
                type=ModalityType.QUANTUM,
                dimension=32,
                encoding_params={"encoding_type": "standard"}
            ),
            "holographic_small": ModalityConfig(
                type=ModalityType.HOLOGRAPHIC,
                dimension=16,
                encoding_params={"field_type": "scalar"}
            ),
            "holographic_large": ModalityConfig(
                type=ModalityType.HOLOGRAPHIC,
                dimension=64,
                encoding_params={"field_type": "scalar"}
            ),
            "fractal_small": ModalityConfig(
                type=ModalityType.FRACTAL,
                dimension=12,
                encoding_params={"max_scales": 3}
            ),
            "fractal_large": ModalityConfig(
                type=ModalityType.FRACTAL,
                dimension=48,
                encoding_params={"max_scales": 5}
            )
        }
        
        for name, config in modalities.items():
            self.fusion_op.register_modality(name, config)
            
    def generate_test_data(self, size: str = "small") -> Dict[str, np.ndarray]:
        """生成测试数据"""
        suffix = "_" + size
        data = {}
        
        # 量子数据
        n_qubits = 3 if size == "small" else 5
        dim = 2 ** n_qubits
        quantum_state = np.random.rand(dim) + 1j * np.random.rand(dim)
        quantum_state = quantum_state / np.linalg.norm(quantum_state)
        data["quantum" + suffix] = quantum_state
        
        # 全息场数据
        field_size = 4 if size == "small" else 8
        holographic_data = np.random.rand(field_size, field_size)
        data["holographic" + suffix] = holographic_data
        
        # 分形数据
        fractal_size = 8 if size == "small" else 16
        fractal_data = np.random.rand(fractal_size, fractal_size)
        data["fractal" + suffix] = fractal_data
        
        return data
        
    def benchmark_encoding(self, 
                         data: Dict[str, np.ndarray],
                         n_runs: int = 10) -> Dict[str, Dict[str, float]]:
        """测试编码性能"""
        results = {}
        
        for modality_name, modality_data in data.items():
            times = []
            memory_usage = []
            success_rate = 0
            
            for _ in range(n_runs):
                start_time = time.time()
                try:
                    result = self.fusion_op.encode_modality(modality_data, modality_name)
                    if result["success"]:
                        success_rate += 1
                    end_time = time.time()
                    times.append(end_time - start_time)
                    # TODO: 添加内存使用监控
                    memory_usage.append(0)  # 占位
                except Exception as e:
                    logger.error(f"编码失败 {modality_name}: {str(e)}")
                    
            results[modality_name] = {
                "avg_time": np.mean(times),
                "std_time": np.std(times),
                "avg_memory": np.mean(memory_usage),
                "success_rate": success_rate / n_runs
            }
            
        return results
        
    def benchmark_fusion(self,
                        size: str = "small",
                        n_runs: int = 10) -> Dict[str, float]:
        """测试融合性能"""
        data = self.generate_test_data(size)
        encoded_data = []
        
        # 编码数据
        for modality_name, modality_data in data.items():
            result = self.fusion_op.encode_modality(modality_data, modality_name)
            if result["success"]:
                encoded_data.append(result)
                
        times = []
        qualities = []
        success_rate = 0
        
        # 执行融合测试
        for _ in range(n_runs):
            start_time = time.time()
            try:
                result = self.fusion_op.fuse_modalities(encoded_data)
                if result["success"]:
                    success_rate += 1
                    end_time = time.time()
                    times.append(end_time - start_time)
                    qualities.append(result["fusion_quality"])
            except Exception as e:
                logger.error(f"融合失败: {str(e)}")
                
        return {
            "avg_time": np.mean(times),
            "std_time": np.std(times),
            "avg_quality": np.mean(qualities),
            "std_quality": np.std(qualities),
            "success_rate": success_rate / n_runs
        }
        
    def run_full_benchmark(self) -> None:
        """运行完整基准测试"""
        logger.info("开始基准测试...")
        
        # 设置模态
        self.setup_modalities()
        
        # 测试小规模数据
        logger.info("测试小规模数据...")
        small_data = self.generate_test_data("small")
        small_encoding_results = self.benchmark_encoding(small_data)
        small_fusion_results = self.benchmark_fusion("small")
        
        # 测试大规模数据
        logger.info("测试大规模数据...")
        large_data = self.generate_test_data("large")
        large_encoding_results = self.benchmark_encoding(large_data)
        large_fusion_results = self.benchmark_fusion("large")
        
        # 存储结果
        self.results = {
            "small": {
                "encoding": small_encoding_results,
                "fusion": small_fusion_results
            },
            "large": {
                "encoding": large_encoding_results,
                "fusion": large_fusion_results
            }
        }
        
        # 可视化结果
        self.plot_results()
        
    def plot_results(self) -> None:
        """可视化基准测试结果"""
        plt.figure(figsize=(15, 10))
        
        # 绘制编码时间对比
        plt.subplot(2, 2, 1)
        self._plot_encoding_times()
        
        # 绘制融合性能
        plt.subplot(2, 2, 2)
        self._plot_fusion_performance()
        
        # 绘制成功率对比
        plt.subplot(2, 2, 3)
        self._plot_success_rates()
        
        # 绘制规模可扩展性
        plt.subplot(2, 2, 4)
        self._plot_scalability()
        
        plt.tight_layout()
        plt.savefig("benchmark_results/multimodal_fusion_benchmark.png")
        plt.close()
        
    def _plot_encoding_times(self):
        """绘制编码时间对比"""
        modalities = list(self.results["small"]["encoding"].keys())
        small_times = [self.results["small"]["encoding"][m]["avg_time"] for m in modalities]
        large_times = [self.results["large"]["encoding"][m]["avg_time"] for m in modalities]
        
        x = np.arange(len(modalities))
        width = 0.35
        
        plt.bar(x - width/2, small_times, width, label='Small Scale')
        plt.bar(x + width/2, large_times, width, label='Large Scale')
        
        plt.xlabel('Modality')
        plt.ylabel('Average Encoding Time (s)')
        plt.title('Encoding Performance by Modality')
        plt.xticks(x, [m.split('_')[0] for m in modalities])
        plt.legend()
        
    def _plot_fusion_performance(self):
        """绘制融合性能"""
        scales = ['small', 'large']
        times = [self.results[s]["fusion"]["avg_time"] for s in scales]
        qualities = [self.results[s]["fusion"]["avg_quality"] for s in scales]
        
        ax1 = plt.gca()
        ax2 = ax1.twinx()
        
        ax1.plot(scales, times, 'b-', label='Fusion Time')
        ax2.plot(scales, qualities, 'r-', label='Fusion Quality')
        
        ax1.set_xlabel('Data Scale')
        ax1.set_ylabel('Fusion Time (s)', color='b')
        ax2.set_ylabel('Fusion Quality', color='r')
        
        plt.title('Fusion Performance by Scale')
        
    def _plot_success_rates(self):
        """绘制成功率对比"""
        modalities = list(self.results["small"]["encoding"].keys())
        small_rates = [self.results["small"]["encoding"][m]["success_rate"] for m in modalities]
        large_rates = [self.results["large"]["encoding"][m]["success_rate"] for m in modalities]
        
        x = np.arange(len(modalities))
        width = 0.35
        
        plt.bar(x - width/2, small_rates, width, label='Small Scale')
        plt.bar(x + width/2, large_rates, width, label='Large Scale')
        
        plt.xlabel('Modality')
        plt.ylabel('Success Rate')
        plt.title('Success Rate by Modality')
        plt.xticks(x, [m.split('_')[0] for m in modalities])
        plt.legend()
        
    def _plot_scalability(self):
        """绘制规模可扩展性"""
        modalities = list(self.results["small"]["encoding"].keys())
        small_times = [self.results["small"]["encoding"][m]["avg_time"] for m in modalities]
        large_times = [self.results["large"]["encoding"][m]["avg_time"] for m in modalities]
        
        speedup = [l/s for l, s in zip(large_times, small_times)]
        
        plt.plot(range(len(modalities)), speedup, 'g-')
        plt.axhline(y=4, color='r', linestyle='--', label='Linear Speedup')
        
        plt.xlabel('Modality')
        plt.ylabel('Large/Small Time Ratio')
        plt.title('Scalability Analysis')
        plt.xticks(range(len(modalities)), [m.split('_')[0] for m in modalities])
        plt.legend()

def main():
    """主函数"""
    benchmark = FusionBenchmark()
    benchmark.run_full_benchmark()
    
    logger.info("基准测试完成，结果已保存到 benchmark_results/multimodal_fusion_benchmark.png")

if __name__ == "__main__":
    main()