"""
多模态融合系统性能基准测试

本模块实现了一系列性能测试，用于评估系统在不同场景下的性能表现：
1. 编码性能
2. 融合性能
3. 内存使用
4. 并行处理效率
"""

import numpy as np
import time
import psutil
import threading
import multiprocessing as mp
from typing import Dict, List, Any, Tuple
import logging
from pathlib import Path
import json
import matplotlib.pyplot as plt
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor

from src.transcendental_tensor.multimodal_fusion.multimodal_fusion_operator import (
    MultimodalFusionOperator,
    ModalityType,
    ModalityConfig
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FusionBenchmark:
    """融合性能基准测试"""
    
    def __init__(self, output_dir: str = "./benchmark_results"):
        self.fusion_op = MultimodalFusionOperator()
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化模态
        self._setup_modalities()
        
        # 性能记录
        self.metrics = {
            "encoding_times": [],
            "fusion_times": [],
            "memory_usage": [],
            "thread_scaling": [],
            "process_scaling": []
        }
        
    def _setup_modalities(self):
        """设置测试模态"""
        # 量子态
        self.fusion_op.register_modality(
            "quantum",
            ModalityConfig(
                type=ModalityType.QUANTUM,
                dimension=8,
                encoding_params={"encoding_type": "amplitude"}
            )
        )
        
        # 全息场
        self.fusion_op.register_modality(
            "holographic",
            ModalityConfig(
                type=ModalityType.HOLOGRAPHIC,
                dimension=16,
                encoding_params={"field_type": "scalar"}
            )
        )
        
        # 分形
        self.fusion_op.register_modality(
            "fractal",
            ModalityConfig(
                type=ModalityType.FRACTAL,
                dimension=12,
                encoding_params={"max_scales": 3}
            )
        )
        
    def benchmark_encoding(self,
                         sizes: List[int],
                         repeats: int = 5) -> Dict[str, List[float]]:
        """测试编码性能"""
        logger.info("开始编码性能测试...")
        results = {
            "quantum": [],
            "holographic": [],
            "fractal": []
        }
        
        for size in sizes:
            # 生成测试数据
            test_data = {
                "quantum": np.random.rand(size, 4) + 1j * np.random.rand(size, 4),
                "holographic": np.random.rand(size, 4, 4),
                "fractal": np.random.rand(size, 8, 8)
            }
            
            for modality in test_data:
                times = []
                for _ in range(repeats):
                    start_time = time.time()
                    self.fusion_op.encode_modality(
                        test_data[modality],
                        modality
                    )
                    times.append(time.time() - start_time)
                    
                results[modality].append(np.mean(times))
                
            # 记录内存使用
            self.metrics["memory_usage"].append(
                psutil.Process().memory_info().rss / 1024 / 1024  # MB
            )
            
        return results
        
    def benchmark_fusion(self,
                        num_modalities: List[int],
                        data_size: int = 1000,
                        repeats: int = 5) -> List[float]:
        """测试融合性能"""
        logger.info("开始融合性能测试...")
        results = []
        
        for num_mods in num_modalities:
            # 准备测试数据
            encoded_data = []
            for i in range(num_mods):
                data = np.random.rand(data_size)
                result = {
                    "success": True,
                    "modality": f"mod_{i}",
                    "encoded_data": data,
                    "metadata": {
                        "type": "TEST",
                        "dimension": data_size
                    }
                }
                encoded_data.append(result)
                
            # 执行融合测试
            times = []
            for _ in range(repeats):
                start_time = time.time()
                self.fusion_op.fuse_modalities(encoded_data)
                times.append(time.time() - start_time)
                
            results.append(np.mean(times))
            
        return results
        
    def benchmark_parallel_encoding(self,
                                  num_threads: List[int],
                                  data_size: int = 1000) -> Dict[str, List[float]]:
        """测试并行编码性能"""
        logger.info("开始并行处理测试...")
        thread_results = []
        process_results = []
        
        # 准备测试数据
        test_data = np.random.rand(data_size, 4)
        
        def encode_task():
            self.fusion_op.encode_modality(test_data, "quantum")
            
        # 线程测试
        for n_threads in num_threads:
            start_time = time.time()
            with ThreadPoolExecutor(max_workers=n_threads) as executor:
                list(executor.map(lambda _: encode_task(), range(n_threads)))
            thread_time = time.time() - start_time
            thread_results.append(thread_time)
            
        # 进程测试
        for n_processes in num_threads:
            start_time = time.time()
            with ProcessPoolExecutor(max_workers=n_processes) as executor:
                list(executor.map(lambda _: encode_task(), range(n_processes)))
            process_time = time.time() - start_time
            process_results.append(process_time)
            
        return {
            "thread": thread_results,
            "process": process_results
        }
        
    def run_all_benchmarks(self):
        """运行所有基准测试"""
        # 编码测试
        sizes = [100, 500, 1000, 5000, 10000]
        encoding_results = self.benchmark_encoding(sizes)
        
        # 融合测试
        num_modalities = [2, 3, 4, 5, 6]
        fusion_results = self.benchmark_fusion(num_modalities)
        
        # 并行测试
        num_threads = [1, 2, 4, 8, 16]
        parallel_results = self.benchmark_parallel_encoding(num_threads)
        
        # 保存结果
        self._save_results({
            "encoding": encoding_results,
            "fusion": fusion_results,
            "parallel": parallel_results,
            "memory": self.metrics["memory_usage"]
        })
        
        # 生成可视化
        self._generate_plots({
            "sizes": sizes,
            "num_modalities": num_modalities,
            "num_threads": num_threads
        })
        
    def _save_results(self, results: Dict[str, Any]):
        """保存测试结果"""
        output_file = self.output_dir / "benchmark_results.json"
        with open(output_file, "w") as f:
            json.dump(results, f, indent=2)
            
    def _generate_plots(self, params: Dict[str, List[int]]):
        """生成性能图表"""
        # 1. 编码性能图
        plt.figure(figsize=(10, 6))
        for modality in ["quantum", "holographic", "fractal"]:
            plt.plot(
                params["sizes"],
                self.metrics["encoding_times"],
                label=modality
            )
        plt.xlabel("数据大小")
        plt.ylabel("编码时间 (秒)")
        plt.title("编码性能随数据规模变化")
        plt.legend()
        plt.savefig(self.output_dir / "encoding_performance.png")
        plt.close()
        
        # 2. 融合性能图
        plt.figure(figsize=(10, 6))
        plt.plot(params["num_modalities"], self.metrics["fusion_times"])
        plt.xlabel("模态数量")
        plt.ylabel("融合时间 (秒)")
        plt.title("融合性能随模态数量变化")
        plt.savefig(self.output_dir / "fusion_performance.png")
        plt.close()
        
        # 3. 并行扩展性图
        plt.figure(figsize=(10, 6))
        plt.plot(
            params["num_threads"],
            self.metrics["thread_scaling"],
            label="Thread"
        )
        plt.plot(
            params["num_threads"],
            self.metrics["process_scaling"],
            label="Process"
        )
        plt.xlabel("并行单元数量")
        plt.ylabel("加速比")
        plt.title("并行处理扩展性")
        plt.legend()
        plt.savefig(self.output_dir / "parallel_scaling.png")
        plt.close()
        
        # 4. 内存使用图
        plt.figure(figsize=(10, 6))
        plt.plot(params["sizes"], self.metrics["memory_usage"])
        plt.xlabel("数据大小")
        plt.ylabel("内存使用 (MB)")
        plt.title("内存使用随数据规模变化")
        plt.savefig(self.output_dir / "memory_usage.png")
        plt.close()
        
def main():
    """主函数"""
    benchmark = FusionBenchmark()
    
    try:
        # 运行基准测试
        benchmark.run_all_benchmarks()
        logger.info("基准测试完成，结果已保存到 benchmark_results 目录")
        
    except Exception as e:
        logger.error(f"基准测试失败: {str(e)}")
        raise
        
if __name__ == "__main__":
    main()