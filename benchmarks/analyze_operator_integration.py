#!/usr/bin/env python3
"""
超越态思维引擎算子库集成分析

本脚本分析算子之间的集成情况，检测潜在的问题，并提供优化建议。
"""

import os
import sys
import json
import argparse
import logging
from typing import Dict, Any

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入算子注册表
from src.operators.registry import operator_registry

# 导入集成分析器
from benchmarks.utils.integration_analyzer import IntegrationAnalyzer

def generate_html_report(result: Dict[str, Any], output_path: str) -> None:
    """
    生成HTML报告
    
    参数:
        result: 分析结果
        output_path: 输出路径
    """
    report = result["report"]
    suggestions = result["suggestions"]
    
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>超越态思维引擎算子库集成分析报告</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
                line-height: 1.6;
            }
            h1, h2, h3 {
                color: #333;
            }
            .summary {
                background-color: #f5f5f5;
                padding: 15px;
                border-radius: 5px;
                margin-bottom: 20px;
            }
            .issue {
                margin-bottom: 15px;
                padding: 10px;
                border-left: 4px solid #ccc;
            }
            .high {
                border-left-color: #d9534f;
                background-color: #f9f2f2;
            }
            .medium {
                border-left-color: #f0ad4e;
                background-color: #faf6f2;
            }
            .low {
                border-left-color: #5bc0de;
                background-color: #f2f9fa;
            }
            table {
                border-collapse: collapse;
                width: 100%;
                margin-bottom: 20px;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }
            th {
                background-color: #f2f2f2;
            }
            tr:nth-child(even) {
                background-color: #f9f9f9;
            }
        </style>
    </head>
    <body>
        <h1>超越态思维引擎算子库集成分析报告</h1>
        
        <div class="summary">
            <h2>摘要</h2>
            <p>总算子数: """ + str(report["total_operators"]) + """</p>
            <p>算子类别数: """ + str(report["categories"]) + """</p>
            <p>有依赖的算子数: """ + str(report["operators_with_dependencies"]) + """</p>
            <p>有兼容性问题的算子数: """ + str(report["operators_with_compatibility_issues"]) + """</p>
            <p>有循环依赖的算子数: """ + str(report["operators_with_circular_dependencies"]) + """</p>
            <p>有缺失依赖的算子数: """ + str(report["operators_with_missing_dependencies"]) + """</p>
        </div>
        
        <h2>优化建议</h2>
    """
    
    # 添加优化建议
    if not suggestions:
        html += "<p>没有发现需要优化的问题。</p>"
    else:
        for suggestion in suggestions:
            severity_class = suggestion["severity"]
            
            html += f"""
            <div class="issue {severity_class}">
                <h3>{suggestion["description"]}</h3>
                <p><strong>严重程度:</strong> {suggestion["severity"]}</p>
                <p><strong>建议:</strong> {suggestion["suggestion"]}</p>
            """
            
            if suggestion["type"] == "circular_dependency":
                html += f"""
                <p><strong>循环依赖:</strong> {' -> '.join(suggestion["operators"])}</p>
                """
            elif suggestion["type"] == "missing_dependency":
                html += f"""
                <p><strong>算子:</strong> {suggestion["operator"]}</p>
                <p><strong>缺失依赖:</strong> {', '.join(suggestion["missing"])}</p>
                """
            elif suggestion["type"] == "compatibility_issue":
                html += f"""
                <p><strong>算子:</strong> {suggestion["operator"]}</p>
                <p><strong>不兼容算子:</strong> {', '.join(suggestion["incompatible"])}</p>
                """
            
            html += "</div>"
    
    # 添加详细信息
    html += """
        <h2>详细信息</h2>
        
        <h3>循环依赖</h3>
    """
    
    if not report["circular_dependencies"]:
        html += "<p>没有检测到循环依赖。</p>"
    else:
        html += "<ul>"
        for cycle in report["circular_dependencies"]:
            html += f"<li>{' -> '.join(cycle)}</li>"
        html += "</ul>"
    
    html += "<h3>缺失依赖</h3>"
    
    if not report["missing_dependencies"]:
        html += "<p>没有检测到缺失依赖。</p>"
    else:
        html += "<table>"
        html += "<tr><th>算子</th><th>缺失依赖</th></tr>"
        for operator_id, missing in report["missing_dependencies"].items():
            html += f"<tr><td>{operator_id}</td><td>{', '.join(missing)}</td></tr>"
        html += "</table>"
    
    html += "<h3>兼容性问题</h3>"
    
    if not report["compatibility_issues"]:
        html += "<p>没有检测到兼容性问题。</p>"
    else:
        html += "<table>"
        html += "<tr><th>算子</th><th>不兼容算子</th></tr>"
        for operator_id, incompatible in report["compatibility_issues"].items():
            html += f"<tr><td>{operator_id}</td><td>{', '.join(incompatible)}</td></tr>"
        html += "</table>"
    
    html += """
    </body>
    </html>
    """
    
    with open(output_path, "w") as f:
        f.write(html)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='超越态思维引擎算子库集成分析')
    parser.add_argument('--output-dir', type=str, default='integration_analysis', help='输出目录')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 创建集成分析器
    analyzer = IntegrationAnalyzer(operator_registry)
    
    # 分析算子集成情况并生成优化建议
    result = analyzer.analyze_and_suggest()
    
    # 保存结果
    with open(os.path.join(args.output_dir, 'integration_analysis.json'), 'w') as f:
        json.dump(result, f, indent=2)
    
    # 生成HTML报告
    generate_html_report(result, os.path.join(args.output_dir, 'integration_analysis.html'))
    
    logger.info(f"集成分析报告已生成到 {args.output_dir}")

if __name__ == "__main__":
    main()
