#!/usr/bin/env python3
"""
超越态思维引擎算子库综合基准测试

本脚本对算子库中的所有算子进行全面的性能和内存使用测试，并生成详细的报告。
"""

import os
import sys
import argparse
import logging
import traceback
from typing import Any, Dict, List, Tuple
from concurrent.futures import ProcessPoolExecutor, as_completed

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入算子注册表
from src.operators.registry import operator_registry

# 导入工具函数
from benchmarks.utils import generate_test_data, benchmark_operator, generate_report

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='超越态思维引擎算子库综合基准测试')
    parser.add_argument('--output-dir', type=str, default='benchmark_results', help='输出目录')
    parser.add_argument('--num-samples', type=int, default=10, help='每个算子的测试样本数量')
    parser.add_argument('--num-runs', type=int, default=5, help='每个算子的运行次数')
    parser.add_argument('--warmup-runs', type=int, default=2, help='每个算子的预热运行次数')
    parser.add_argument('--categories', type=str, nargs='+', help='要测试的算子类别，不指定则测试所有类别')
    parser.add_argument('--operators', type=str, nargs='+', help='要测试的算子名称，不指定则测试所有算子')
    parser.add_argument('--parallel', action='store_true', help='是否并行运行测试')
    parser.add_argument('--max-workers', type=int, default=4, help='并行运行时的最大工作进程数')

    args = parser.parse_args()

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 获取所有算子类别
    categories = args.categories or operator_registry.get_categories()

    # 准备测试任务
    tasks = []

    for category in categories:
        # 获取该类别下的所有算子
        operators = operator_registry.get_operators_in_category(category)

        for operator_name, operator_class in operators.items():
            # 如果指定了算子名称，则只测试指定的算子
            if args.operators and operator_name not in args.operators:
                continue

            # 生成测试数据
            test_data = generate_test_data(category, operator_name, args.num_samples)

            # 添加到任务列表
            tasks.append((category, operator_name, operator_class, test_data))

    # 运行测试
    results = []

    if args.parallel:
        # 并行运行
        logger.info(f"并行运行测试，最大工作进程数: {args.max_workers}")

        with ProcessPoolExecutor(max_workers=args.max_workers) as executor:
            future_to_task = {
                executor.submit(
                    benchmark_operator,
                    category,
                    operator_name,
                    operator_class,
                    test_data,
                    args.num_runs,
                    args.warmup_runs
                ): (category, operator_name)
                for category, operator_name, operator_class, test_data in tasks
            }

            for future in as_completed(future_to_task):
                category, operator_name = future_to_task[future]

                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    logger.error(f"{category}.{operator_name} 测试失败: {e}")
                    results.append({
                        "category": category,
                        "name": operator_name,
                        "error": str(e),
                        "traceback": traceback.format_exc()
                    })
    else:
        # 串行运行
        logger.info("串行运行测试")

        for category, operator_name, operator_class, test_data in tasks:
            result = benchmark_operator(
                category,
                operator_name,
                operator_class,
                test_data,
                args.num_runs,
                args.warmup_runs
            )

            results.append(result)

    # 生成报告
    generate_report(results, args.output_dir)

if __name__ == "__main__":
    main()
