"""
实际算子测试脚本

本脚本测试实际的TransformOperator和EvolutionOperator。
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Any, Dict, List, Optional, Tuple, Union, Callable
from src.interfaces.validator import OperatorValidator, OperatorCompatibilityChecker
from src.interfaces.composer import CompositeOperator, OperatorPipeline
from src.operators.transform import TransformOperator
from src.operators.evolution import EvolutionOperator


def test_transform_operator():
    """测试TransformOperator"""
    print("\n测试TransformOperator...")
    
    # 创建随机数据
    np.random.seed(42)
    data = np.random.randn(10, 2)
    
    # 创建线性变换算子
    transform_op = TransformOperator(
        transform_type='linear',
        dimension=2,
        parameters={
            'matrix': np.array([[np.cos(np.pi/4), -np.sin(np.pi/4)], 
                               [np.sin(np.pi/4), np.cos(np.pi/4)]]),
            'offset': np.array([0, 0])
        }
    )
    
    # 验证算子
    validator = OperatorValidator()
    valid, errors = validator.validate(transform_op)
    print(f"验证TransformOperator:")
    print(f"  有效: {valid}")
    if not valid:
        for error in errors:
            print(f"  - {error}")
    
    # 应用变换
    transformed_data = transform_op.apply(data)
    
    # 打印结果
    print(f"变换类型: {transform_op.transform_type}")
    print(f"变换参数: {transform_op.parameters}")
    print(f"原始数据形状: {data.shape}")
    print(f"变换后数据形状: {transformed_data.shape}")
    
    # 可视化原始数据和变换后的数据
    plt.figure(figsize=(10, 5))
    
    plt.subplot(1, 2, 1)
    plt.scatter(data[:, 0], data[:, 1], alpha=0.7)
    plt.title('Original Data')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.scatter(transformed_data[:, 0], transformed_data[:, 1], alpha=0.7)
    plt.title('Transformed Data')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('transform_operator_test.png')
    plt.close()
    
    print("TransformOperator测试完成")
    
    return transform_op


def test_evolution_operator():
    """测试EvolutionOperator"""
    print("\n测试EvolutionOperator...")
    
    # 创建随机数据
    np.random.seed(42)
    data = np.random.randn(10, 2)
    
    # 创建微分方程
    def harmonic_oscillator(t, x):
        # 简谐振荡器: d^2x/dt^2 = -x
        # 转换为一阶系统: dx/dt = v, dv/dt = -x
        if len(x.shape) == 1:
            # 单个点
            dx = np.zeros(2)
            dx[0] = x[1]
            dx[1] = -x[0]
            return dx
        else:
            # 多个点
            dx = np.zeros_like(x)
            dx[:, 0] = x[:, 1]
            dx[:, 1] = -x[:, 0]
            return dx
    
    # 创建演化算子
    evolution_op = EvolutionOperator(
        evolution_type='differential_equation',
        dimension=2,
        parameters={
            'equation': harmonic_oscillator,
            'method': 'rk4',
            'time_step': 0.1,
            'num_steps': 100
        }
    )
    
    # 验证算子
    validator = OperatorValidator()
    valid, errors = validator.validate(evolution_op)
    print(f"验证EvolutionOperator:")
    print(f"  有效: {valid}")
    if not valid:
        for error in errors:
            print(f"  - {error}")
    
    # 应用演化
    trajectory = evolution_op.apply(data, return_trajectory=True)
    
    # 打印结果
    print(f"演化类型: {evolution_op.evolution_type}")
    print(f"演化参数: {evolution_op.parameters}")
    print(f"原始数据形状: {data.shape}")
    print(f"轨迹长度: {len(trajectory)}")
    print(f"最终状态形状: {trajectory[-1].shape}")
    
    # 可视化轨迹
    plt.figure(figsize=(10, 8))
    
    # 绘制第一个点的轨迹
    trajectory_point = np.array([state[0] for state in trajectory])
    plt.plot(trajectory_point[:, 0], trajectory_point[:, 1], 'b-', alpha=0.7)
    plt.scatter(trajectory_point[0, 0], trajectory_point[0, 1], c='g', s=100, label='Start')
    plt.scatter(trajectory_point[-1, 0], trajectory_point[-1, 1], c='r', s=100, label='End')
    
    plt.title('Harmonic Oscillator Trajectory')
    plt.xlabel('Position')
    plt.ylabel('Velocity')
    plt.grid(True)
    plt.legend()
    
    plt.savefig('evolution_operator_test.png')
    plt.close()
    
    print("EvolutionOperator测试完成")
    
    return evolution_op


def test_operator_compatibility(transform_op, evolution_op):
    """测试算子兼容性"""
    print("\n测试算子兼容性...")
    
    # 检查兼容性
    checker = OperatorCompatibilityChecker()
    compatible, message = checker.check_compatibility(transform_op, evolution_op)
    
    print(f"检查 {transform_op.get_metadata()['name']} 与 {evolution_op.get_metadata()['name']} 的兼容性:")
    print(f"  兼容: {compatible}")
    print(f"  消息: {message}")
    
    print("算子兼容性测试完成")


def test_operator_composition(transform_op, evolution_op):
    """测试算子组合"""
    print("\n测试算子组合...")
    
    # 创建复合算子
    composite_op = CompositeOperator([transform_op, evolution_op])
    
    # 验证复合算子
    validator = OperatorValidator()
    valid, errors = validator.validate(composite_op)
    print(f"验证CompositeOperator:")
    print(f"  有效: {valid}")
    if not valid:
        for error in errors:
            print(f"  - {error}")
    
    # 打印复合算子信息
    print(f"复合算子: {composite_op.get_metadata()['name']}")
    print(f"复合算子参数: {composite_op.get_parameters()}")
    
    # 创建随机数据
    np.random.seed(42)
    data = np.random.randn(10, 2)
    
    # 应用复合算子
    result = composite_op.apply(data)
    
    print(f"输入数据形状: {data.shape}")
    print(f"输出数据形状: {result.shape}")
    
    print("算子组合测试完成")
    
    return composite_op


def test_operator_pipeline(transform_op, evolution_op):
    """测试算子流水线"""
    print("\n测试算子流水线...")
    
    # 创建算子流水线
    pipeline = OperatorPipeline("TestPipeline")
    
    # 添加算子
    pipeline.add(transform_op)
    pipeline.add(evolution_op)
    
    # 打印流水线信息
    print(f"流水线: {pipeline}")
    print(f"流水线长度: {len(pipeline)}")
    
    # 创建随机数据
    np.random.seed(42)
    data = np.random.randn(10, 2)
    
    # 应用流水线
    result = pipeline.apply(data)
    
    print(f"输入数据形状: {data.shape}")
    print(f"输出数据形状: {result.shape}")
    
    # 构建复合算子
    composite_op = pipeline.build()
    
    # 验证复合算子
    validator = OperatorValidator()
    valid, errors = validator.validate(composite_op)
    print(f"验证流水线构建的复合算子:")
    print(f"  有效: {valid}")
    if not valid:
        for error in errors:
            print(f"  - {error}")
    
    print("算子流水线测试完成")


def main():
    """主函数"""
    print("开始测试实际算子...")
    
    try:
        # 测试TransformOperator
        transform_op = test_transform_operator()
        
        # 测试EvolutionOperator
        evolution_op = test_evolution_operator()
        
        # 测试算子兼容性
        test_operator_compatibility(transform_op, evolution_op)
        
        # 测试算子组合
        composite_op = test_operator_composition(transform_op, evolution_op)
        
        # 测试算子流水线
        test_operator_pipeline(transform_op, evolution_op)
        
        print("\n所有测试完成")
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")


if __name__ == "__main__":
    main()
