#!/usr/bin/env python3
"""
代码库智能索引工具
系统性扫描、分析和索引整个代码库，建立完整的知识图谱
"""

import os
import sys
import json
import time
from pathlib import Path
from typing import Dict, List, Set, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import hashlib

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

@dataclass
class FileInfo:
    """文件信息"""
    path: str
    name: str
    extension: str
    size: int
    modified_time: str
    file_type: str
    language: str
    importance_score: float
    summary: str
    key_components: List[str]
    dependencies: List[str]
    hash: str

@dataclass
class ComponentInfo:
    """组件信息"""
    name: str
    type: str  # struct, function, class, module, etc.
    file_path: str
    description: str
    interfaces: List[str]
    dependencies: List[str]
    performance_notes: str
    integration_potential: str

@dataclass
class CodebaseIndex:
    """代码库索引"""
    name: str
    path: str
    total_files: int
    indexed_files: int
    languages: Dict[str, int]
    file_types: Dict[str, int]
    files: List[FileInfo]
    components: List[ComponentInfo]
    relationships: Dict[str, List[str]]
    created_at: str
    updated_at: str

class CodebaseIndexer:
    """代码库索引器"""
    
    def __init__(self):
        self.file_extensions = {
            # Rust
            '.rs': ('rust', 'code', 0.9),
            '.toml': ('toml', 'config', 0.7),
            
            # Python
            '.py': ('python', 'code', 0.9),
            '.pyx': ('cython', 'code', 0.8),
            
            # Documentation
            '.md': ('markdown', 'doc', 0.8),
            '.rst': ('rst', 'doc', 0.7),
            '.txt': ('text', 'doc', 0.5),
            
            # Config
            '.json': ('json', 'config', 0.6),
            '.yaml': ('yaml', 'config', 0.6),
            '.yml': ('yaml', 'config', 0.6),
            '.ini': ('ini', 'config', 0.5),
            
            # Build
            'Cargo.toml': ('toml', 'build', 0.8),
            'Cargo.lock': ('toml', 'build', 0.4),
            'pyproject.toml': ('toml', 'build', 0.8),
            'requirements.txt': ('text', 'build', 0.7),
            'setup.py': ('python', 'build', 0.7),
        }
        
        self.ignore_patterns = {
            'target', '__pycache__', '.git', '.vscode', 'node_modules',
            '.pytest_cache', '.mypy_cache', 'dist', 'build', '.tox'
        }
        
        self.high_value_keywords = {
            'performance', 'optimization', 'memory', 'cache', 'parallel',
            'distributed', 'async', 'quantum', 'neural', 'arrow', 'tensor',
            'algorithm', 'structure', 'engine', 'core', 'framework'
        }
    
    def should_ignore(self, path: Path) -> bool:
        """检查是否应该忽略此路径"""
        for part in path.parts:
            if part in self.ignore_patterns:
                return True
        return False
    
    def get_file_info(self, file_path: Path) -> Optional[FileInfo]:
        """获取文件信息"""
        try:
            stat = file_path.stat()
            
            # 确定文件类型和语言
            ext = file_path.suffix.lower()
            name = file_path.name
            
            if name in self.file_extensions:
                language, file_type, importance = self.file_extensions[name]
            elif ext in self.file_extensions:
                language, file_type, importance = self.file_extensions[ext]
            else:
                language, file_type, importance = 'unknown', 'other', 0.1
            
            # 计算文件哈希
            try:
                with open(file_path, 'rb') as f:
                    content = f.read()
                    file_hash = hashlib.md5(content).hexdigest()
            except:
                file_hash = "unknown"
            
            # 分析文件内容（简化版）
            summary, components, dependencies = self.analyze_file_content(file_path, language)
            
            # 调整重要性分数
            importance = self.calculate_importance_score(file_path, summary, importance)
            
            return FileInfo(
                path=str(file_path),
                name=name,
                extension=ext,
                size=stat.st_size,
                modified_time=datetime.fromtimestamp(stat.st_mtime).isoformat(),
                file_type=file_type,
                language=language,
                importance_score=importance,
                summary=summary,
                key_components=components,
                dependencies=dependencies,
                hash=file_hash
            )
        except Exception as e:
            print(f"   ⚠️ 分析文件失败 {file_path}: {e}")
            return None
    
    def analyze_file_content(self, file_path: Path, language: str) -> tuple:
        """分析文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 简化的内容分析
            lines = content.split('\n')
            summary_lines = []
            components = []
            dependencies = []
            
            for line in lines[:50]:  # 只分析前50行
                line = line.strip()
                if not line or line.startswith('#') or line.startswith('//'):
                    if line and any(keyword in line.lower() for keyword in self.high_value_keywords):
                        summary_lines.append(line)
                    continue
                
                # Rust分析
                if language == 'rust':
                    if line.startswith('pub struct ') or line.startswith('struct '):
                        components.append(line.split()[2].rstrip('{'))
                    elif line.startswith('pub fn ') or line.startswith('fn '):
                        components.append(line.split()[2].split('(')[0])
                    elif line.startswith('use '):
                        dependencies.append(line.replace('use ', '').rstrip(';'))
                
                # Python分析
                elif language == 'python':
                    if line.startswith('class '):
                        components.append(line.split()[1].rstrip(':'))
                    elif line.startswith('def '):
                        components.append(line.split()[1].split('(')[0])
                    elif line.startswith('import ') or line.startswith('from '):
                        dependencies.append(line)
            
            # 生成摘要
            if summary_lines:
                summary = ' | '.join(summary_lines[:3])
            else:
                summary = f"{language.title()} file with {len(components)} components"
            
            return summary[:200], components[:10], dependencies[:10]
            
        except Exception as e:
            return f"Analysis failed: {e}", [], []
    
    def calculate_importance_score(self, file_path: Path, summary: str, base_score: float) -> float:
        """计算重要性分数"""
        score = base_score
        
        # 路径加权
        path_str = str(file_path).lower()
        if any(keyword in path_str for keyword in ['core', 'engine', 'main', 'lib']):
            score += 0.2
        if any(keyword in path_str for keyword in ['test', 'example', 'demo']):
            score -= 0.1
        
        # 内容加权
        summary_lower = summary.lower()
        keyword_count = sum(1 for keyword in self.high_value_keywords if keyword in summary_lower)
        score += keyword_count * 0.1
        
        # 文件大小加权
        if file_path.stat().st_size > 10000:  # 大于10KB
            score += 0.1
        
        return min(1.0, max(0.0, score))
    
    def index_codebase(self, codebase_path: str, codebase_name: str) -> CodebaseIndex:
        """索引代码库"""
        print(f"🔍 开始索引代码库: {codebase_name}")
        print(f"   路径: {codebase_path}")
        
        start_time = time.time()
        codebase_path = Path(codebase_path)
        
        files = []
        languages = {}
        file_types = {}
        total_files = 0
        indexed_files = 0
        
        # 遍历所有文件
        for file_path in codebase_path.rglob('*'):
            if file_path.is_file() and not self.should_ignore(file_path):
                total_files += 1
                
                file_info = self.get_file_info(file_path)
                if file_info and file_info.importance_score > 0.3:  # 只索引重要文件
                    files.append(file_info)
                    indexed_files += 1
                    
                    # 统计
                    languages[file_info.language] = languages.get(file_info.language, 0) + 1
                    file_types[file_info.file_type] = file_types.get(file_info.file_type, 0) + 1
                    
                    if indexed_files % 50 == 0:
                        print(f"   已索引: {indexed_files} 文件...")
        
        # 按重要性排序
        files.sort(key=lambda x: x.importance_score, reverse=True)
        
        # 创建索引
        index = CodebaseIndex(
            name=codebase_name,
            path=str(codebase_path),
            total_files=total_files,
            indexed_files=indexed_files,
            languages=languages,
            file_types=file_types,
            files=files,
            components=[],  # 将在后续步骤中填充
            relationships={},  # 将在后续步骤中填充
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )
        
        elapsed = time.time() - start_time
        print(f"✅ 索引完成: {indexed_files}/{total_files} 文件 ({elapsed:.2f}s)")
        print(f"   语言分布: {languages}")
        print(f"   文件类型: {file_types}")
        
        return index
    
    def save_index(self, index: CodebaseIndex, output_file: str):
        """保存索引到文件"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(index), f, indent=2, ensure_ascii=False)
            print(f"💾 索引已保存: {output_file}")
        except Exception as e:
            print(f"❌ 保存索引失败: {e}")
    
    def load_index(self, index_file: str) -> Optional[CodebaseIndex]:
        """从文件加载索引"""
        try:
            with open(index_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return CodebaseIndex(**data)
        except Exception as e:
            print(f"❌ 加载索引失败: {e}")
            return None

def main():
    """主函数"""
    print("🗂️ 代码库智能索引工具")
    print("系统性扫描、分析和索引整个代码库")
    print("=" * 60)
    
    indexer = CodebaseIndexer()
    
    # 定义要索引的代码库
    codebases = [
        ("AQFH", "/home/<USER>/CascadeProjects/AQFH"),
        ("TCT", "/home/<USER>/CascadeProjects/TCT"),
        ("TFF", "/home/<USER>/CascadeProjects/TFF"),
        ("TTE", "/home/<USER>/CascadeProjects/TTE"),
    ]
    
    all_indices = []
    
    for name, path in codebases:
        if Path(path).exists():
            print(f"\n📁 索引代码库: {name}")
            index = indexer.index_codebase(path, name)
            all_indices.append(index)
            
            # 保存单个索引
            output_file = f"index_{name.lower()}.json"
            indexer.save_index(index, output_file)
            
            # 显示重要文件
            print(f"\n🌟 {name} 重要文件 (Top 10):")
            for i, file_info in enumerate(index.files[:10]):
                print(f"   {i+1}. {file_info.name} ({file_info.importance_score:.2f}) - {file_info.summary[:80]}...")
        else:
            print(f"⚠️ 代码库不存在: {path}")
    
    print(f"\n✅ 索引完成！共索引 {len(all_indices)} 个代码库")
    print("💡 下一步：将这些索引信息存储到记忆系统中")

if __name__ == "__main__":
    main()
