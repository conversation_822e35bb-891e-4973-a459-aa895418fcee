#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数值算子测试脚本
"""

import os
import sys
import unittest
import numpy as np
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 添加库目录到 LD_LIBRARY_PATH
lib_dir = project_root / "target" / "release"
os.environ["LD_LIBRARY_PATH"] = f"{os.environ.get('LD_LIBRARY_PATH', '')}:{lib_dir}"

try:
    # 导入数值算子模块
    from rust_operators import PyMathOps as MathOps, PyParallelOps as ParallelOps
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)

class TestMathOps(unittest.TestCase):
    """测试数值算子"""

    def setUp(self):
        """测试前准备"""
        # 创建数值算子
        self.math_ops = MathOps("test_math_ops")

        # 创建测试数据
        self.array_1d = np.array([1.0, 2.0, 3.0, 4.0, 5.0])
        self.array_2d = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0], [7.0, 8.0, 9.0]])

    def test_sum(self):
        """测试求和"""
        # 测试一维数组求和
        result = self.math_ops.sum(self.array_1d)
        self.assertEqual(result, 15.0)

        # 测试二维数组求和
        result = self.math_ops.sum(self.array_2d)
        self.assertEqual(result, 45.0)

        # 测试指定轴求和
        result = self.math_ops.sum(self.array_2d, axis=0)
        np.testing.assert_array_equal(result, np.array([12.0, 15.0, 18.0]))

        result = self.math_ops.sum(self.array_2d, axis=1)
        np.testing.assert_array_equal(result, np.array([6.0, 15.0, 24.0]))

    def test_mean(self):
        """测试求均值"""
        # 测试一维数组求均值
        result = self.math_ops.mean(self.array_1d)
        self.assertEqual(result, 3.0)

        # 测试二维数组求均值
        result = self.math_ops.mean(self.array_2d)
        self.assertEqual(result, 5.0)

        # 测试指定轴求均值
        result = self.math_ops.mean(self.array_2d, axis=0)
        np.testing.assert_array_equal(result, np.array([4.0, 5.0, 6.0]))

        result = self.math_ops.mean(self.array_2d, axis=1)
        np.testing.assert_array_equal(result, np.array([2.0, 5.0, 8.0]))

    def test_std(self):
        """测试求标准差"""
        # 测试一维数组求标准差
        result = self.math_ops.std(self.array_1d, ddof=0)
        self.assertAlmostEqual(result, np.std(self.array_1d, ddof=0))

        # 测试二维数组求标准差
        result = self.math_ops.std(self.array_2d, ddof=0)
        self.assertAlmostEqual(result, np.std(self.array_2d, ddof=0))

        # 测试指定轴求标准差
        result = self.math_ops.std(self.array_2d, axis=0, ddof=0)
        np.testing.assert_array_almost_equal(result, np.std(self.array_2d, axis=0, ddof=0))

        result = self.math_ops.std(self.array_2d, axis=1, ddof=0)
        np.testing.assert_array_almost_equal(result, np.std(self.array_2d, axis=1, ddof=0))

    def test_min(self):
        """测试求最小值"""
        # 测试一维数组求最小值
        result = self.math_ops.min(self.array_1d)
        self.assertEqual(result, 1.0)

        # 测试二维数组求最小值
        result = self.math_ops.min(self.array_2d)
        self.assertEqual(result, 1.0)

        # 测试指定轴求最小值
        result = self.math_ops.min(self.array_2d, axis=0)
        np.testing.assert_array_equal(result, np.array([1.0, 2.0, 3.0]))

        result = self.math_ops.min(self.array_2d, axis=1)
        np.testing.assert_array_equal(result, np.array([1.0, 4.0, 7.0]))

    def test_max(self):
        """测试求最大值"""
        # 测试一维数组求最大值
        result = self.math_ops.max(self.array_1d)
        self.assertEqual(result, 5.0)

        # 测试二维数组求最大值
        result = self.math_ops.max(self.array_2d)
        self.assertEqual(result, 9.0)

        # 测试指定轴求最大值
        result = self.math_ops.max(self.array_2d, axis=0)
        np.testing.assert_array_equal(result, np.array([7.0, 8.0, 9.0]))

        result = self.math_ops.max(self.array_2d, axis=1)
        np.testing.assert_array_equal(result, np.array([3.0, 6.0, 9.0]))

class TestParallelOps(unittest.TestCase):
    """测试并行算子"""

    def setUp(self):
        """测试前准备"""
        # 创建并行算子
        self.parallel_ops = ParallelOps("test_parallel_ops")

        # 创建测试数据
        self.array_1d = np.array([1.0, 2.0, 3.0, 4.0, 5.0])
        self.array_2d = np.array([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0], [7.0, 8.0, 9.0]])

    def test_get_num_threads(self):
        """测试获取线程数"""
        num_threads = self.parallel_ops.get_num_threads()
        self.assertGreater(num_threads, 0)

    def test_set_num_threads(self):
        """测试设置线程数"""
        # 获取当前线程数
        old_num_threads = self.parallel_ops.get_num_threads()

        # 设置新的线程数
        new_num_threads = max(1, old_num_threads - 1)
        self.parallel_ops.set_num_threads(new_num_threads)

        # 验证线程数已更改
        self.assertEqual(self.parallel_ops.get_num_threads(), new_num_threads)

        # 恢复原来的线程数
        self.parallel_ops.set_num_threads(old_num_threads)

    def test_par_map(self):
        """测试并行映射"""
        # 定义映射函数
        def square(x):
            return x * x

        # 测试一维数组并行映射
        result = self.parallel_ops.par_map(self.array_1d, square)
        np.testing.assert_array_equal(result, np.array([1.0, 4.0, 9.0, 16.0, 25.0]))

        # 测试二维数组并行映射
        result = self.parallel_ops.par_map(self.array_2d, square)
        np.testing.assert_array_equal(result, self.array_2d * self.array_2d)

    def test_par_map_indexed(self):
        """测试带索引的并行映射"""
        # 定义映射函数
        def indexed_square(idx, x):
            return idx[0] + x * x

        # 测试二维数组带索引的并行映射
        result = self.parallel_ops.par_map_indexed(self.array_2d, indexed_square)
        expected = np.zeros_like(self.array_2d)
        for i in range(self.array_2d.shape[0]):
            for j in range(self.array_2d.shape[1]):
                expected[i, j] = i + self.array_2d[i, j] * self.array_2d[i, j]

        np.testing.assert_array_equal(result, expected)

    def test_par_reduce(self):
        """测试并行归约"""
        # 定义归约函数
        def add(x, y):
            return x + y

        # 测试指定轴并行归约
        result = self.parallel_ops.par_reduce(self.array_2d, 0, add)
        np.testing.assert_array_equal(result, np.array([12.0, 15.0, 18.0]))

        result = self.parallel_ops.par_reduce(self.array_2d, 1, add)
        np.testing.assert_array_equal(result, np.array([6.0, 15.0, 24.0]))

        # 测试带初始值的并行归约
        result = self.parallel_ops.par_reduce(self.array_2d, 0, add, 10.0)
        np.testing.assert_array_equal(result, np.array([22.0, 25.0, 28.0]))

    def test_par_filter(self):
        """测试并行过滤"""
        # 定义过滤函数
        def is_even(x):
            return x % 2 == 0

        # 测试一维数组并行过滤
        result = self.parallel_ops.par_filter(self.array_1d, is_even)
        np.testing.assert_array_equal(result, np.array([2.0, 4.0]))

        # 测试二维数组并行过滤
        result = self.parallel_ops.par_filter(self.array_2d.flatten(), is_even)
        np.testing.assert_array_equal(result, np.array([2.0, 4.0, 6.0, 8.0]))

    def test_par_sort(self):
        """测试并行排序"""
        # 创建乱序数组
        unsorted = np.array([5.0, 2.0, 8.0, 1.0, 9.0, 3.0, 7.0, 4.0, 6.0])

        # 测试并行排序
        result = self.parallel_ops.par_sort(unsorted)
        np.testing.assert_array_equal(result, np.sort(unsorted))

        # 测试二维数组指定轴并行排序
        unsorted_2d = np.array([[5.0, 2.0, 8.0], [1.0, 9.0, 3.0], [7.0, 4.0, 6.0]])

        result = self.parallel_ops.par_sort(unsorted_2d, axis=0)
        np.testing.assert_array_equal(result, np.sort(unsorted_2d, axis=0))

        result = self.parallel_ops.par_sort(unsorted_2d, axis=1)
        np.testing.assert_array_equal(result, np.sort(unsorted_2d, axis=1))

if __name__ == "__main__":
    unittest.main()
