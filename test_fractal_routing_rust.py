"""
测试分形路由算子的Rust实现
"""

import numpy as np
import sys
import os
import importlib.util
import importlib.machinery
import networkx as nx

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 尝试导入Rust实现
RUST_AVAILABLE = False
try:
    # 查找fractal_routing模块
    module_paths = [
        os.path.join(os.path.dirname(__file__), 'src/operators/fractal_routing/fractal_routing.so'),
        os.path.join(os.path.dirname(__file__), 'target/debug/libfractal_routing.so'),
        os.path.join(os.path.dirname(__file__), 'target/release/libfractal_routing.so')
    ]

    # 查找存在的模块路径
    module_path = None
    for path in module_paths:
        if os.path.exists(path):
            module_path = path
            break

    if module_path:
        # 加载模块
        spec = importlib.util.spec_from_file_location("fractal_routing", module_path)
        fractal_routing = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(fractal_routing)

        # 从模块中导入类和函数
        FractalRoutingOperator = fractal_routing.FractalRoutingOperator
        register_fractal_routing = fractal_routing.register_fractal_routing
        RUST_AVAILABLE = True
        print(f"成功导入Rust实现，模块路径: {module_path}")
    else:
        print(f"Rust模块文件不存在")
        raise ImportError("Rust模块文件不存在")
except (ImportError, AttributeError) as e:
    print(f"无法导入fractal_routing模块，可能是Rust实现未正确构建: {e}")

    # 使用Python实现
    from src.operators.fractal.routing import FractalRoutingOperator

    def register_fractal_routing(*args, **kwargs):
        return "dummy_id"

def create_test_graph():
    """创建测试图"""
    G = nx.Graph()

    # 添加节点
    for i in range(10):
        G.add_node(str(i), x=float(i), y=float(i), importance=0.5 + 0.05 * i)

    # 添加边
    for i in range(9):
        G.add_edge(str(i), str(i+1), weight=1.0)

    # 添加一些额外的边
    G.add_edge("0", "5", weight=5.0)
    G.add_edge("1", "6", weight=4.0)
    G.add_edge("2", "7", weight=3.0)
    G.add_edge("3", "8", weight=2.0)
    G.add_edge("4", "9", weight=1.0)

    # 添加节点和边的属性字典
    nx.set_node_attributes(G, {node: attrs for node, attrs in G.nodes(data=True)})
    nx.set_edge_attributes(G, {(u, v): attrs for u, v, attrs in G.edges(data=True)})

    return G

def main():
    """主函数"""
    # 检查Rust实现是否可用
    print(f"Rust实现可用: {RUST_AVAILABLE}")

    if not RUST_AVAILABLE:
        print("Rust实现不可用，使用Python实现")

    # 创建FractalRoutingOperator算子
    operator = FractalRoutingOperator(
        dimension=2.5,
        max_hops=20,
        optimization_enabled=True,
        adaptive_mode=True,
        routing_strategy="adaptive"
    )

    # 打印算子信息
    print(f"算子: {operator}")

    # 创建测试图
    G = create_test_graph()
    print(f"测试图: 节点数={G.number_of_nodes()}, 边数={G.number_of_edges()}")

    # 测试单个路由请求
    source = "0"
    target = "9"

    # 应用路由算子
    result = operator.apply((G, source, target), strategy="shortest")
    print(f"从节点 {source} 到节点 {target} 的路由: {result}")

    # 测试多个路由请求
    requests = [("0", "5"), ("1", "8"), ("2", "9")]

    # 应用路由算子
    result_dict = operator.apply((G, requests), strategy="balanced")
    print(f"多个路由请求的结果: {result_dict}")

    # 测试不同的路由策略
    strategies = ["shortest", "balanced", "adaptive", "hierarchical"]
    for strategy in strategies:
        result = operator.apply((G, source, target), strategy=strategy)
        print(f"使用 {strategy} 策略的路由: {result}")

    # 测试路由约束
    constraints = {"max_length": 5, "max_weight": 10.0}
    result = operator.apply((G, source, target), strategy="shortest", constraints=constraints)
    print(f"带约束的路由: {result}")

    # 获取算子元数据
    metadata = operator.get_metadata()
    print(f"算子元数据: {metadata}")

    # 获取性能指标
    if hasattr(operator, 'get_performance_metrics'):
        performance_metrics = operator.get_performance_metrics()
        print(f"性能指标: {performance_metrics}")
    else:
        print("Python实现不支持get_performance_metrics方法")

    # 获取算子复杂度信息
    if hasattr(operator, 'get_complexity'):
        complexity = operator.get_complexity()
        print(f"算子复杂度信息: {complexity}")
    else:
        print("Python实现不支持get_complexity方法")

    # 清除缓存
    if hasattr(operator, 'clear_cache'):
        operator.clear_cache()
        print("已清除路由缓存")
    else:
        print("Python实现不支持clear_cache方法")

    print("测试完成")

if __name__ == "__main__":
    main()
