"""
测试干涉算子的Rust实现
"""

import numpy as np
import sys
import os
import importlib.util
import importlib.machinery

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 尝试导入Rust实现
RUST_AVAILABLE = False
try:
    # 查找interference_module模块
    module_paths = [
        os.path.join(os.path.dirname(__file__), 'src/operators/interference/interference_module.so'),
        os.path.join(os.path.dirname(__file__), 'target/debug/libinterference_module.so'),
        os.path.join(os.path.dirname(__file__), 'target/release/libinterference_module.so')
    ]

    # 查找存在的模块路径
    module_path = None
    for path in module_paths:
        if os.path.exists(path):
            module_path = path
            break

    if module_path:
        # 加载模块
        spec = importlib.util.spec_from_file_location("interference_module", module_path)
        interference_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(interference_module)

        # 从模块中导入类和函数
        InterferenceOperator = interference_module.InterferenceOperator
        register_interference = interference_module.register_interference
        RUST_AVAILABLE = True
        print(f"成功导入Rust实现，模块路径: {module_path}")
    else:
        print(f"Rust模块文件不存在")
        raise ImportError("Rust模块文件不存在")
except (ImportError, AttributeError) as e:
    print(f"无法导入interference_module模块，可能是Rust实现未正确构建: {e}")

    # 使用Python实现
    from src.operators.interference.interference_operator import InterferenceOperator

    def register_interference(*args, **kwargs):
        return "dummy_id"

def main():
    """主函数"""
    # 检查Rust实现是否可用
    print(f"Rust实现可用: {RUST_AVAILABLE}")

    if not RUST_AVAILABLE:
        print("Rust实现不可用，使用Python实现")

    # 创建InterferenceOperator算子
    operator = InterferenceOperator(
        dimension=3,
        pattern_type="transcendental",
        optimization_enabled=True,
        adaptive_mode=True
    )

    # 打印算子信息
    print(f"算子: {operator}")

    # 创建测试向量
    vector = np.array([1.0, 0.0, 0.0])

    # 应用干涉
    result_vector = operator.apply(vector, strength=1.0, phase_shift=0.0)
    print(f"向量干涉结果: {result_vector}")

    # 创建测试矩阵
    matrix = np.array([
        [1.0, 0.0, 0.0],
        [0.0, 1.0, 0.0],
        [0.0, 0.0, 1.0]
    ])

    # 应用干涉
    result_matrix = operator.apply(matrix, strength=1.0, phase_shift=0.0)
    print(f"矩阵干涉结果形状: {result_matrix.shape}")

    # 创建测试张量
    tensor = np.zeros((2, 2, 3))
    tensor[0, 0] = [1.0, 0.0, 0.0]
    tensor[0, 1] = [0.0, 1.0, 0.0]
    tensor[1, 0] = [0.0, 0.0, 1.0]
    tensor[1, 1] = [1.0, 1.0, 1.0]

    # 应用干涉
    result_tensor = operator.apply(tensor, strength=1.0, phase_shift=0.0)
    print(f"张量干涉结果形状: {result_tensor.shape}")

    # 测试数据融合
    data1 = np.array([1.0, 0.0, 0.0])
    data2 = np.array([0.0, 1.0, 0.0])

    # 融合数据
    if hasattr(operator, 'fuse_data'):
        fused_data = operator.fuse_data(data1, data2, mode="add", weight1=0.5, weight2=0.5)
        print(f"融合数据结果: {fused_data}")
    else:
        print("Python实现不支持fuse_data方法")

    # 获取算子元数据
    metadata = operator.get_metadata()
    print(f"算子元数据: {metadata}")

    # 获取性能指标
    if hasattr(operator, 'get_performance_metrics'):
        performance_metrics = operator.get_performance_metrics()
        print(f"性能指标: {performance_metrics}")
    else:
        print("Python实现不支持get_performance_metrics方法")

    # 获取算子复杂度信息
    if hasattr(operator, 'get_complexity'):
        complexity = operator.get_complexity()
        print(f"算子复杂度信息: {complexity}")
    else:
        print("Python实现不支持get_complexity方法")

    print("测试完成")

if __name__ == "__main__":
    main()
