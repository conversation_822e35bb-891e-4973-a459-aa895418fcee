{"timestamp": "2025-05-28T20:27:53.577578", "python_version": "3.13.3 experimental free-threading build (main, May 14 2025, 21:30:28) [GCC 11.4.0]", "test_results": {"memory_operations": {"save_performance": {"total_attempts": 25, "successful_saves": 25, "success_rate": 1.0, "avg_save_time": 0.0007544612884521485, "median_save_time": 0.0006706714630126953, "min_save_time": 0.0006120204925537109, "max_save_time": 0.002671957015991211, "total_save_time": 0.01886153221130371, "batch_time": 0.018892526626586914, "save_ops_per_sec": 1323.274567458765}, "search_performance": {"total_queries": 5, "successful_searches": 5, "avg_search_time": 0.04572901725769043, "median_search_time": 0.03632688522338867, "min_search_time": 0.03547978401184082, "max_search_time": 0.08371734619140625, "total_search_time": 0.22864508628845215, "search_ops_per_sec": 21.86795299721483, "avg_results_per_query": 10}, "status_performance": {"total_queries": 5, "successful_queries": 5, "avg_status_time": 5.984306335449219e-05, "median_status_time": 3.8623809814453125e-05, "status_ops_per_sec": 16710.37450199203}}, "system_resources": {"process_memory_mb": 161.25, "process_memory_percent": 0.027828125247111125, "process_cpu_percent": 0.0, "system_memory_total_gb": 565.8688888549805, "system_memory_available_gb": 548.7072792053223, "system_memory_percent": 3.0, "system_cpu_percent": 2.7}}}