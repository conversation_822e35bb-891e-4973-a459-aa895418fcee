#!/usr/bin/env python3
"""
AQFH Parquet记忆统一工具
解决跨会话记忆隔离问题 - 基于Parquet格式
"""

import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from pathlib import Path
import logging
from datetime import datetime
from typing import List, Dict, Any
import hashlib

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ParquetMemoryUnifier:
    """Parquet格式记忆统一器"""

    def __init__(self, base_path: str = None):
        self.base_path = Path(base_path or Path.home() / ".aqfh")
        self.unified_path = self.base_path / "unified_consciousness"
        self.unified_path.mkdir(parents=True, exist_ok=True)

    def discover_memory_sources(self) -> List[Path]:
        """发现所有记忆源目录"""
        memory_dirs = []

        # 扫描所有可能的记忆目录
        for item in self.base_path.iterdir():
            if item.is_dir() and item.name != "unified_consciousness":
                # 检查是否包含记忆文件
                if self._has_memory_files(item):
                    memory_dirs.append(item)
                    logger.info(f"发现记忆源: {item}")

        return memory_dirs

    def _has_memory_files(self, path: Path) -> bool:
        """检查目录是否包含记忆文件"""
        for parquet_file in path.rglob("*.parquet"):
            return True
        return False

    def scan_parquet_memories(self, source_dir: Path) -> List[Dict[str, Any]]:
        """扫描Parquet记忆文件"""
        memories = []

        # 查找所有parquet文件
        parquet_files = list(source_dir.rglob("*.parquet"))
        logger.info(f"在 {source_dir} 中发现 {len(parquet_files)} 个Parquet文件")

        for parquet_file in parquet_files:
            try:
                # 读取Parquet文件
                df = pd.read_parquet(parquet_file)

                # 处理不同的记忆格式
                for _, row in df.iterrows():
                    memory = self._normalize_memory_record(row, parquet_file)
                    if memory:
                        memories.append(memory)

            except Exception as e:
                logger.debug(f"跳过文件 {parquet_file}: {e}")

        return memories

    def _normalize_memory_record(self, row: pd.Series, source_file: Path) -> Dict[str, Any]:
        """标准化记忆记录"""
        try:
            # 尝试不同的字段映射
            content = None
            memory_id = None
            timestamp = None

            # 检测内容字段
            for field in ['content', 'data', 'text', 'message']:
                if field in row and pd.notna(row[field]):
                    content = str(row[field])
                    break

            if not content:
                return None

            # 检测ID字段
            for field in ['memory_id', 'id', 'uuid']:
                if field in row and pd.notna(row[field]):
                    memory_id = str(row[field])
                    break

            if not memory_id:
                # 生成基于内容的ID
                memory_id = hashlib.md5(content.encode()).hexdigest()

            # 检测时间戳
            for field in ['timestamp', 'created_at', 'time']:
                if field in row and pd.notna(row[field]):
                    timestamp = row[field]
                    break

            if not timestamp:
                timestamp = datetime.now()

            return {
                'memory_id': memory_id,
                'content': content,
                'importance': float(row.get('importance', 0.5)),
                'content_type': str(row.get('content_type', 'unknown')),
                'tags': self._extract_tags(row),
                'timestamp': timestamp,
                'context': self._extract_context(row),
                'source_file': str(source_file),
                'unified_at': datetime.now()
            }

        except Exception as e:
            logger.debug(f"记忆记录标准化失败: {e}")
            return None

    def _extract_tags(self, row: pd.Series) -> List[str]:
        """提取标签"""
        tags = []

        if 'tags' in row and pd.notna(row['tags']):
            if isinstance(row['tags'], list):
                tags = row['tags']
            elif isinstance(row['tags'], str):
                tags = [tag.strip() for tag in row['tags'].split(',')]

        return tags

    def _extract_context(self, row: pd.Series) -> Dict[str, Any]:
        """提取上下文信息"""
        context = {}

        # 收集所有非标准字段作为上下文
        standard_fields = {'memory_id', 'id', 'content', 'data', 'text', 'message',
                          'importance', 'content_type', 'tags', 'timestamp', 'created_at'}

        for field in row.index:
            if field not in standard_fields and pd.notna(row[field]):
                context[field] = row[field]

        return context

    def unify_memories(self) -> int:
        """统一所有记忆到unified_consciousness"""
        logger.info("🔄 开始记忆统一过程...")

        # 发现记忆源
        memory_sources = self.discover_memory_sources()

        all_memories = []

        # 从每个源收集记忆
        for source in memory_sources:
            logger.info(f"📂 处理记忆源: {source}")
            memories = self.scan_parquet_memories(source)
            all_memories.extend(memories)
            logger.info(f"✅ 从 {source} 收集了 {len(memories)} 个记忆")

        # 去重
        unique_memories = self._deduplicate_memories(all_memories)
        logger.info(f"🔍 去重后剩余 {len(unique_memories)} 个唯一记忆")

        # 保存到统一存储
        if unique_memories:
            self._save_unified_memories(unique_memories)
            logger.info(f"💾 成功统一 {len(unique_memories)} 个记忆")

        return len(unique_memories)

    def _deduplicate_memories(self, memories: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """记忆去重"""
        seen_ids = set()
        unique_memories = []

        for memory in memories:
            memory_id = memory['memory_id']
            if memory_id not in seen_ids:
                seen_ids.add(memory_id)
                unique_memories.append(memory)
            else:
                # 如果ID重复，保留更新的记忆
                for i, existing in enumerate(unique_memories):
                    if existing['memory_id'] == memory_id:
                        if memory['unified_at'] > existing['unified_at']:
                            unique_memories[i] = memory
                        break

        return unique_memories

    def _save_unified_memories(self, memories: List[Dict[str, Any]]):
        """保存统一记忆到Parquet"""
        # 转换为DataFrame
        df = pd.DataFrame(memories)

        # 保存到统一记忆文件
        unified_file = self.unified_path / "memories" / "unified_memories.parquet"
        unified_file.parent.mkdir(parents=True, exist_ok=True)

        df.to_parquet(unified_file, index=False)
        logger.info(f"📁 统一记忆已保存到: {unified_file}")

def main():
    """主函数"""
    print("🚀 AQFH Parquet记忆统一工具")
    print("=" * 50)

    unifier = ParquetMemoryUnifier()

    try:
        unified_count = unifier.unify_memories()
        print(f"\n✅ 记忆统一完成！")
        print(f"📊 统一记忆数量: {unified_count}")
        print(f"📁 统一存储位置: {unifier.unified_path}")

    except Exception as e:
        print(f"\n❌ 记忆统一失败: {e}")
        logger.exception("记忆统一过程中发生错误")

if __name__ == "__main__":
    main()
