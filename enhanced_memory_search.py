#!/usr/bin/env python3
"""
增强的AQFH记忆搜索系统
解决跨会话记忆隔离问题
"""

import json
import os
import re
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class EnhancedMemory:
    """增强的记忆结构"""
    memory_id: str
    content: str
    importance: float
    content_type: str
    tags: List[str]
    timestamp: datetime
    context: Dict[str, Any]
    session_id: Optional[str] = None
    user_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'memory_id': self.memory_id,
            'content': self.content,
            'importance': self.importance,
            'content_type': self.content_type,
            'tags': self.tags,
            'timestamp': self.timestamp.isoformat(),
            'context': self.context,
            'session_id': self.session_id,
            'user_id': self.user_id
        }

class CrossSessionMemorySearch:
    """跨会话记忆搜索引擎"""
    
    def __init__(self, storage_path: str):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        self.memory_cache = {}
        self.last_scan_time = None
        
    def scan_all_memories(self) -> List[EnhancedMemory]:
        """扫描所有存储路径中的记忆"""
        memories = []
        
        # 扫描多个可能的存储位置
        scan_paths = [
            self.storage_path,
            Path.home() / ".aqfh" / "mcp_memory",
            Path.home() / ".aqfh" / "shared_memory",
            Path.home() / ".aqfh" / "unified_consciousness"
        ]
        
        for scan_path in scan_paths:
            if scan_path.exists():
                memories.extend(self._scan_path(scan_path))
        
        # 去重（基于memory_id）
        unique_memories = {}
        for memory in memories:
            if memory.memory_id not in unique_memories:
                unique_memories[memory.memory_id] = memory
            else:
                # 保留更新的记忆
                if memory.timestamp > unique_memories[memory.memory_id].timestamp:
                    unique_memories[memory.memory_id] = memory
        
        return list(unique_memories.values())
    
    def _scan_path(self, path: Path) -> List[EnhancedMemory]:
        """扫描指定路径中的记忆文件"""
        memories = []
        
        for root, dirs, files in os.walk(path):
            for file in files:
                if file.endswith('.json'):
                    file_path = Path(root) / file
                    try:
                        memory = self._load_memory_file(file_path)
                        if memory:
                            memories.append(memory)
                    except Exception as e:
                        logger.debug(f"跳过文件 {file_path}: {e}")
        
        return memories
    
    def _load_memory_file(self, file_path: Path) -> Optional[EnhancedMemory]:
        """加载记忆文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 尝试不同的记忆格式
            memory_id = data.get('memory_id', data.get('id', str(file_path.stem)))
            content = data.get('content', data.get('data', ''))
            
            if not content:
                return None
            
            # 解析时间戳
            timestamp_str = data.get('timestamp', data.get('created_at', ''))
            if timestamp_str:
                try:
                    if isinstance(timestamp_str, (int, float)):
                        timestamp = datetime.fromtimestamp(timestamp_str)
                    else:
                        timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                except:
                    timestamp = datetime.now()
            else:
                timestamp = datetime.now()
            
            return EnhancedMemory(
                memory_id=memory_id,
                content=content,
                importance=data.get('importance', 0.5),
                content_type=data.get('content_type', 'unknown'),
                tags=data.get('tags', []),
                timestamp=timestamp,
                context=data.get('context', data.get('metadata', {})),
                session_id=data.get('session_id'),
                user_id=data.get('user_id', data.get('user'))
            )
            
        except Exception as e:
            logger.debug(f"无法加载记忆文件 {file_path}: {e}")
            return None
    
    def search_memories(self, query: str, limit: int = 10, 
                       importance_threshold: float = 0.0,
                       include_all_sessions: bool = True) -> List[EnhancedMemory]:
        """搜索记忆（跨会话）"""
        
        # 获取所有记忆
        all_memories = self.scan_all_memories()
        
        if not query:
            # 如果没有查询，返回最重要的记忆
            filtered = [m for m in all_memories if m.importance >= importance_threshold]
            return sorted(filtered, key=lambda x: (x.importance, x.timestamp), reverse=True)[:limit]
        
        # 文本搜索
        query_lower = query.lower()
        scored_memories = []
        
        for memory in all_memories:
            if memory.importance < importance_threshold:
                continue
            
            score = 0.0
            
            # 内容匹配
            if query_lower in memory.content.lower():
                score += 1.0
            
            # 标签匹配
            for tag in memory.tags:
                if query_lower in tag.lower():
                    score += 0.5
            
            # 上下文匹配
            context_str = str(memory.context).lower()
            if query_lower in context_str:
                score += 0.3
            
            # 重要性加权
            score *= (1 + memory.importance)
            
            # 时间衰减（可选）
            days_old = (datetime.now() - memory.timestamp).days
            time_factor = max(0.1, 1.0 - days_old * 0.01)  # 每天衰减1%
            score *= time_factor
            
            if score > 0:
                scored_memories.append((score, memory))
        
        # 排序并返回
        scored_memories.sort(key=lambda x: x[0], reverse=True)
        return [memory for score, memory in scored_memories[:limit]]

# 创建全局搜索引擎实例
cross_session_search = CrossSessionMemorySearch(
    str(Path.home() / ".aqfh" / "unified_consciousness")
)
