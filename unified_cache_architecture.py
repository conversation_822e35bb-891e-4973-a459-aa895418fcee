#!/usr/bin/env python3
"""
AQFH统一缓存架构设计
解决多实例缓存目标不统一和传导机制问题
"""

import threading
import time
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum
import json
import hashlib

class CacheLevel(Enum):
    """缓存级别定义"""
    L0_RAM = 0      # RAM缓存 - 各实例独立
    L1_NVME = 1     # NVMe缓存 - 各实例独立  
    L2_SAS = 2      # SAS缓存 - 各实例独立
    L3_UNIFIED = 3  # 统一基础存储 - 全局共享

@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    level: CacheLevel
    timestamp: float
    access_count: int
    importance: float
    size_bytes: int

class UnifiedCacheCoordinator:
    """统一缓存协调器 - 解决多实例缓存统一问题"""
    
    def __init__(self, unified_base_path: str):
        """
        初始化统一缓存协调器
        
        Args:
            unified_base_path: 统一的基础存储路径
        """
        self.unified_base_path = Path(unified_base_path)
        self.unified_base_path.mkdir(parents=True, exist_ok=True)
        
        # 统一存储目标
        self.unified_storage = {
            'base_memories': self.unified_base_path / "base" / "memories.parquet",
            'index_file': self.unified_base_path / "index" / "unified_index.json",
            'metadata': self.unified_base_path / "metadata" / "cache_metadata.json"
        }
        
        # 确保目录存在
        for storage_path in self.unified_storage.values():
            storage_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 实例注册表
        self.registered_instances = {}
        self.instance_lock = threading.RLock()
        
        # 缓存传导规则
        self.promotion_rules = {
            'access_threshold': 3,      # 访问3次后提升
            'importance_threshold': 0.7, # 重要性>0.7立即提升
            'time_threshold': 3600      # 1小时内访问频繁则提升
        }
        
        self.demotion_rules = {
            'idle_time': 7200,          # 2小时未访问则降级
            'low_importance': 0.3,      # 重要性<0.3优先降级
            'cache_pressure': 0.8       # 缓存使用率>80%时降级
        }
        
        print(f"🎯 统一缓存协调器初始化完成")
        print(f"📁 统一存储路径: {self.unified_base_path}")
    
    def register_instance(self, instance_id: str, instance_config: Dict[str, Any]) -> bool:
        """注册缓存实例"""
        with self.instance_lock:
            # 强制所有实例使用统一的base存储
            unified_config = instance_config.copy()
            unified_config['base_dir'] = str(self.unified_base_path)
            unified_config['unified_mode'] = True
            unified_config['instance_id'] = instance_id
            
            self.registered_instances[instance_id] = {
                'config': unified_config,
                'registered_at': time.time(),
                'last_seen': time.time(),
                'cache_stats': {
                    'L0_size': 0,
                    'L1_size': 0, 
                    'L2_size': 0,
                    'total_operations': 0
                }
            }
            
            print(f"✅ 实例注册成功: {instance_id}")
            print(f"🔗 强制使用统一存储: {self.unified_base_path}")
            return True
    
    def coordinate_cache_access(self, instance_id: str, operation: str, 
                              key: str, value: Any = None, 
                              target_level: CacheLevel = None) -> Any:
        """协调缓存访问 - 核心统一机制"""
        
        if instance_id not in self.registered_instances:
            raise ValueError(f"实例未注册: {instance_id}")
        
        # 更新实例活跃时间
        self.registered_instances[instance_id]['last_seen'] = time.time()
        
        if operation == 'get':
            return self._coordinate_get(instance_id, key)
        elif operation == 'put':
            return self._coordinate_put(instance_id, key, value, target_level)
        elif operation == 'promote':
            return self._coordinate_promote(instance_id, key)
        elif operation == 'demote':
            return self._coordinate_demote(instance_id, key)
        else:
            raise ValueError(f"不支持的操作: {operation}")
    
    def _coordinate_get(self, instance_id: str, key: str) -> Optional[Any]:
        """协调GET操作 - 统一检索逻辑"""
        
        # 1. 先检查实例本地缓存 (L0->L1->L2)
        local_value = self._check_local_cache(instance_id, key)
        if local_value is not None:
            # 更新访问统计
            self._update_access_stats(key, 'local_hit')
            return local_value
        
        # 2. 检查统一基础存储 (L3)
        unified_value = self._check_unified_storage(key)
        if unified_value is not None:
            # 根据访问模式决定是否提升到本地缓存
            if self._should_promote_to_local(key):
                self._promote_to_local_cache(instance_id, key, unified_value)
            
            self._update_access_stats(key, 'unified_hit')
            return unified_value
        
        # 3. 检查其他实例的缓存 (跨实例检索)
        cross_instance_value = self._check_cross_instance_cache(instance_id, key)
        if cross_instance_value is not None:
            # 复制到本地缓存和统一存储
            self._replicate_across_caches(instance_id, key, cross_instance_value)
            self._update_access_stats(key, 'cross_instance_hit')
            return cross_instance_value
        
        # 4. 缓存未命中
        self._update_access_stats(key, 'miss')
        return None
    
    def _coordinate_put(self, instance_id: str, key: str, value: Any, 
                       target_level: CacheLevel = None) -> bool:
        """协调PUT操作 - 统一存储逻辑"""
        
        # 计算value的重要性和大小
        importance = self._calculate_importance(value)
        size_bytes = self._estimate_size(value)
        
        # 创建缓存条目
        cache_entry = CacheEntry(
            key=key,
            value=value,
            level=target_level or self._determine_initial_level(importance),
            timestamp=time.time(),
            access_count=1,
            importance=importance,
            size_bytes=size_bytes
        )
        
        # 根据重要性和大小决定存储策略
        if importance >= 0.8:
            # 高重要性：立即存储到统一基础存储 + 本地缓存
            self._store_to_unified_storage(cache_entry)
            self._store_to_local_cache(instance_id, cache_entry, CacheLevel.L0_RAM)
            
        elif importance >= 0.5:
            # 中等重要性：存储到本地缓存，异步同步到统一存储
            self._store_to_local_cache(instance_id, cache_entry, CacheLevel.L1_NVME)
            self._schedule_async_sync(cache_entry)
            
        else:
            # 低重要性：仅存储到本地缓存
            self._store_to_local_cache(instance_id, cache_entry, CacheLevel.L2_SAS)
        
        # 更新统计信息
        self._update_cache_stats(instance_id, cache_entry)
        
        return True
    
    def _coordinate_promote(self, instance_id: str, key: str) -> bool:
        """协调缓存提升 - 统一提升逻辑"""
        
        # 检查提升条件
        if not self._should_promote(key):
            return False
        
        # 从低级缓存获取数据
        value = self._get_from_lower_cache(instance_id, key)
        if value is None:
            return False
        
        # 提升到更高级缓存
        target_level = self._determine_promotion_target(key)
        cache_entry = CacheEntry(
            key=key,
            value=value,
            level=target_level,
            timestamp=time.time(),
            access_count=self._get_access_count(key) + 1,
            importance=self._calculate_importance(value),
            size_bytes=self._estimate_size(value)
        )
        
        # 执行提升
        if target_level == CacheLevel.L3_UNIFIED:
            self._store_to_unified_storage(cache_entry)
        else:
            self._store_to_local_cache(instance_id, cache_entry, target_level)
        
        print(f"⬆️ 缓存提升: {key} -> {target_level.name}")
        return True
    
    def _coordinate_demote(self, instance_id: str, key: str) -> bool:
        """协调缓存降级 - 统一降级逻辑"""
        
        # 检查降级条件
        if not self._should_demote(key):
            return False
        
        # 执行降级逻辑
        current_level = self._get_current_level(instance_id, key)
        target_level = self._determine_demotion_target(current_level)
        
        # 移动数据
        value = self._remove_from_current_cache(instance_id, key, current_level)
        if value is not None:
            cache_entry = CacheEntry(
                key=key,
                value=value,
                level=target_level,
                timestamp=time.time(),
                access_count=self._get_access_count(key),
                importance=self._calculate_importance(value),
                size_bytes=self._estimate_size(value)
            )
            
            if target_level == CacheLevel.L3_UNIFIED:
                self._store_to_unified_storage(cache_entry)
            else:
                self._store_to_local_cache(instance_id, cache_entry, target_level)
        
        print(f"⬇️ 缓存降级: {key} -> {target_level.name}")
        return True
    
    # 辅助方法（简化实现）
    def _check_local_cache(self, instance_id: str, key: str) -> Optional[Any]:
        """检查本地缓存"""
        # 实际实现需要调用具体的缓存实例
        return None
    
    def _check_unified_storage(self, key: str) -> Optional[Any]:
        """检查统一存储"""
        # 实际实现需要读取Parquet文件
        return None
    
    def _calculate_importance(self, value: Any) -> float:
        """计算重要性"""
        # 简化实现
        return 0.5
    
    def _estimate_size(self, value: Any) -> int:
        """估算大小"""
        # 简化实现
        return 1024
    
    def get_unified_statistics(self) -> Dict[str, Any]:
        """获取统一缓存统计信息"""
        with self.instance_lock:
            total_instances = len(self.registered_instances)
            active_instances = sum(1 for inst in self.registered_instances.values() 
                                 if time.time() - inst['last_seen'] < 300)  # 5分钟内活跃
            
            return {
                'total_instances': total_instances,
                'active_instances': active_instances,
                'unified_storage_path': str(self.unified_base_path),
                'cache_coordination_enabled': True,
                'registered_instances': list(self.registered_instances.keys())
            }

# 使用示例
if __name__ == "__main__":
    # 创建统一缓存协调器
    coordinator = UnifiedCacheCoordinator("/home/<USER>/.aqfh/unified_consciousness")
    
    # 注册实例
    coordinator.register_instance("mcp_instance_1", {"capacity": "100MB"})
    coordinator.register_instance("mcp_instance_2", {"capacity": "100MB"})
    
    # 获取统计信息
    stats = coordinator.get_unified_statistics()
    print(f"📊 统一缓存统计: {stats}")
