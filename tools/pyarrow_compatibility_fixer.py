#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PyArrow 14.0.0+ 兼容性修复工具

自动修复 PyArrow 14.0.0+ 兼容性问题，特别是 Arrow Flight API 和扩展类型 API 的变更。
"""

import os
import re
import sys
import json
import argparse
import logging
import tempfile
import shutil
from pathlib import Path
from typing import List, Dict, Set, Tuple, Optional, Any, Union

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("pyarrow_compatibility_fixer")

# PyArrow 14.0.0+ API 变更
PYARROW_API_CHANGES = {
    # 扩展类型 API 变更
    "extension_type_api": {
        # 旧 API -> 新 API
        "pa.ExtensionType": "pa.ExtensionType",  # 类名未变，但实现可能有变化
        "pa.ExtensionArray": "pa.ExtensionArray",  # 类名未变，但实现可能有变化
        "__arrow_ext_serialize__": "__arrow_ext_serialize__",  # 方法名未变，但签名可能有变化
        "__arrow_ext_deserialize__": "__arrow_ext_deserialize__"  # 方法名未变，但签名可能有变化
    },
    
    # Arrow Flight API 变更
    "flight_api_changes": {
        "FlightServerBase": "FlightServerBase",  # 类名未变，但实现可能有变化
        "FlightClient": "FlightClient",  # 类名未变，但实现可能有变化
        "FlightDescriptor": "FlightDescriptor",  # 类名未变，但实现可能有变化
        "FlightInfo": "FlightInfo",  # 类名未变，但实现可能有变化
        "FlightEndpoint": "FlightEndpoint"  # 类名未变，但实现可能有变化
    },
    
    # 数据类型 API 变更
    "data_type_api": {
        "pa.DataType": "pa.DataType",  # 类名未变，但实现可能有变化
        "pa.Schema": "pa.Schema",  # 类名未变，但实现可能有变化
        "pa.field": "pa.field",  # 函数名未变，但签名可能有变化
        "pa.struct": "pa.struct",  # 函数名未变，但签名可能有变化
        "pa.list_": "pa.list_",  # 函数名未变，但签名可能有变化
        "pa.map_": "pa.map_"  # 函数名未变，但签名可能有变化
    }
}

# 需要添加的导入语句
IMPORT_STATEMENTS = {
    "flight_api_changes": "import pyarrow.flight as flight",
    "extension_type_api": "import pyarrow as pa",
    "data_type_api": "import pyarrow as pa"
}

# 需要添加的兼容性处理代码
COMPATIBILITY_CODE = {
    "extension_type_api": """
# PyArrow 14.0.0+ 兼容性处理
try:
    # 检查 PyArrow 版本
    import pyarrow as pa
    _pa_version = tuple(int(x) for x in pa.__version__.split('.'))
    _is_pa_14_plus = _pa_version >= (14, 0, 0)
except (ImportError, AttributeError):
    _is_pa_14_plus = False

# 扩展类型 API 兼容性处理
def _create_extension_array(extension_type, storage):
    \"\"\"创建扩展数组的兼容性函数
    
    Args:
        extension_type: 扩展类型
        storage: 存储数组
        
    Returns:
        扩展数组
    \"\"\"
    try:
        # PyArrow 14.0.0+ API
        if _is_pa_14_plus:
            return pa.ExtensionArray.from_storage(extension_type, storage)
        # 旧版 API
        else:
            return pa.ExtensionArray.from_storage(extension_type, storage)
    except Exception as e:
        logger.error(f"创建扩展数组失败: {e}")
        # 尝试直接使用存储数组
        return storage
""",
    
    "flight_api_changes": """
# PyArrow Flight 14.0.0+ 兼容性处理
try:
    # 检查 PyArrow Flight 版本
    import pyarrow.flight as flight
    import pyarrow as pa
    _pa_version = tuple(int(x) for x in pa.__version__.split('.'))
    _is_pa_14_plus = _pa_version >= (14, 0, 0)
except (ImportError, AttributeError):
    _is_pa_14_plus = False

# Flight API 兼容性处理
def _create_flight_descriptor(path):
    \"\"\"创建 FlightDescriptor 的兼容性函数
    
    Args:
        path: 路径
        
    Returns:
        FlightDescriptor
    \"\"\"
    try:
        # 确保路径是字节串
        if isinstance(path, str):
            path = path.encode('utf-8')
        
        # PyArrow Flight 14.0.0+ API
        if _is_pa_14_plus:
            return flight.FlightDescriptor.for_path(path)
        # 旧版 API
        else:
            return flight.FlightDescriptor.for_path(path)
    except Exception as e:
        logger.error(f"创建 FlightDescriptor 失败: {e}")
        # 尝试直接使用路径
        return path
"""
}


class PyArrowCompatibilityFixer:
    """PyArrow 兼容性修复器"""
    
    def __init__(self, project_path: str, report_file: Optional[str] = None):
        """初始化修复器
        
        Args:
            project_path: 项目路径
            report_file: 兼容性问题报告文件路径，如果为 None 则自动扫描
        """
        self.project_path = Path(project_path)
        self.report_file = report_file
        self.issues = []
        self.fixed_issues = []
        self.failed_fixes = []
        
        # 如果提供了报告文件，则从中加载问题
        if report_file:
            self._load_issues_from_report(report_file)
    
    def _load_issues_from_report(self, report_file: str) -> None:
        """从报告文件加载兼容性问题
        
        Args:
            report_file: 报告文件路径
        """
        try:
            with open(report_file, "r", encoding="utf-8") as f:
                report_content = f.read()
            
            # 解析报告中的问题
            issues = []
            
            # 首先检查是否有 extension_type_api 部分
            extension_type_section = re.search(r'## extension_type_api.*?(?=##|\Z)', report_content, re.DOTALL)
            if extension_type_section:
                section_content = extension_type_section.group(0)
                # 提取该部分中的所有问题
                pattern = r"- \*\*(.*?):(\d+)\*\*\s+```python\s+(.*?)\s+```"
                matches = re.finditer(pattern, section_content, re.DOTALL)
                
                for match in matches:
                    file_path = match.group(1)
                    line_no = int(match.group(2))
                    content = match.group(3).strip()
                    
                    issues.append({
                        "file": file_path,
                        "line": line_no,
                        "type": "extension_type_api",
                        "content": content
                    })
            
            # 检查是否有 flight_api_changes 相关问题
            flight_pattern = r"- \*\*(.*?):(\d+)\*\*\s+```python\s+(.*?Flight.*?)\s+```"
            flight_matches = re.finditer(flight_pattern, report_content, re.DOTALL)
            
            for match in flight_matches:
                file_path = match.group(1)
                line_no = int(match.group(2))
                content = match.group(3).strip()
                
                if "Flight" in content:
                    issues.append({
                        "file": file_path,
                        "line": line_no,
                        "type": "flight_api_changes",
                        "content": content
                    })
            
            # 检查是否有 data_type_api 相关问题
            data_type_pattern = r"- \*\*(.*?):(\d+)\*\*\s+```python\s+(.*?pa\.(DataType|Schema|field|struct|list_|map_).*?)\s+```"
            data_type_matches = re.finditer(data_type_pattern, report_content, re.DOTALL)
            
            for match in data_type_matches:
                file_path = match.group(1)
                line_no = int(match.group(2))
                content = match.group(3).strip()
                
                issues.append({
                    "file": file_path,
                    "line": line_no,
                    "type": "data_type_api",
                    "content": content
                })
            
            self.issues = issues
            logger.info(f"从报告文件加载了 {len(issues)} 个 PyArrow 兼容性问题。")
        
        except Exception as e:
            logger.error(f"从报告文件加载问题时出错: {e}")
            self.issues = []
    
    def fix_issues(self, backup: bool = True) -> None:
        """修复兼容性问题
        
        Args:
            backup: 是否在修复前备份文件
        """
        if not self.issues:
            logger.info("没有需要修复的 PyArrow 兼容性问题。")
            return
        
        logger.info(f"开始修复 {len(self.issues)} 个 PyArrow 兼容性问题...")
        
        # 按文件分组问题
        issues_by_file = {}
        for issue in self.issues:
            file_path = issue["file"]
            if file_path not in issues_by_file:
                issues_by_file[file_path] = []
            issues_by_file[file_path].append(issue)
        
        # 处理每个文件
        for file_path, file_issues in issues_by_file.items():
            self._fix_file_issues(file_path, file_issues, backup)
        
        logger.info(f"修复完成。成功修复 {len(self.fixed_issues)} 个问题，失败 {len(self.failed_fixes)} 个问题。")
    
    def _fix_file_issues(self, file_path: str, issues: List[Dict[str, Any]], backup: bool) -> None:
        """修复单个文件中的兼容性问题
        
        Args:
            file_path: 文件路径
            issues: 文件中的问题列表
            backup: 是否在修复前备份文件
        """
        abs_path = self.project_path / file_path
        
        try:
            # 读取文件内容
            with open(abs_path, "r", encoding="utf-8") as f:
                content = f.read()
                lines = content.splitlines()
            
            # 创建备份
            if backup:
                backup_path = abs_path.with_suffix(abs_path.suffix + ".pyarrow.bak")
                shutil.copy2(abs_path, backup_path)
                logger.info(f"已创建备份: {backup_path}")
            
            # 修复问题
            modified_content = content
            
            # 确定需要添加的导入语句和兼容性代码
            issue_types = set(issue["type"] for issue in issues)
            imports_to_add = []
            compat_code_to_add = []
            
            for issue_type in issue_types:
                if issue_type in IMPORT_STATEMENTS and IMPORT_STATEMENTS[issue_type] not in modified_content:
                    imports_to_add.append(IMPORT_STATEMENTS[issue_type])
                
                if issue_type in COMPATIBILITY_CODE and COMPATIBILITY_CODE[issue_type] not in modified_content:
                    compat_code_to_add.append(COMPATIBILITY_CODE[issue_type])
            
            # 添加导入语句
            if imports_to_add:
                # 找到最后一个导入语句
                import_pattern = r"^(?:from\s+[\w.]+\s+import|\s*import)\s+.*?$"
                matches = list(re.finditer(import_pattern, modified_content, re.MULTILINE))
                
                if matches:
                    last_import = matches[-1]
                    insert_pos = last_import.end()
                    import_text = "\n" + "\n".join(imports_to_add) + "\n"
                    modified_content = modified_content[:insert_pos] + import_text + modified_content[insert_pos:]
                else:
                    # 如果没有找到导入语句，在文件开头添加
                    import_text = "\n".join(imports_to_add) + "\n\n"
                    modified_content = import_text + modified_content
            
            # 添加兼容性代码
            if compat_code_to_add:
                # 找到最后一个导入语句后的位置
                import_pattern = r"^(?:from\s+[\w.]+\s+import|\s*import)\s+.*?$"
                matches = list(re.finditer(import_pattern, modified_content, re.MULTILINE))
                
                if matches:
                    last_import = matches[-1]
                    insert_pos = last_import.end()
                    
                    # 找到下一个非空行
                    lines = modified_content.splitlines()
                    line_pos = modified_content[:insert_pos].count('\n')
                    
                    while line_pos < len(lines) and not lines[line_pos].strip():
                        line_pos += 1
                        insert_pos += len(lines[line_pos - 1]) + 1
                    
                    compat_text = "\n\n" + "\n".join(compat_code_to_add) + "\n"
                    modified_content = modified_content[:insert_pos] + compat_text + modified_content[insert_pos:]
                else:
                    # 如果没有找到导入语句，在文件开头添加
                    compat_text = "\n".join(compat_code_to_add) + "\n\n"
                    modified_content = compat_text + modified_content
            
            # 修复具体问题
            # 按行号倒序处理问题，避免修改后行号变化的问题
            sorted_issues = sorted(issues, key=lambda x: x["line"], reverse=True)
            
            for issue in sorted_issues:
                line_no = issue["line"] - 1  # 转为0索引
                if line_no >= len(lines):
                    self.failed_fixes.append(issue)
                    logger.warning(f"行号 {issue['line']} 超出文件行数范围，无法修复")
                    continue
                
                line = lines[line_no]
                
                # 根据问题类型修复
                if issue["type"] == "extension_type_api":
                    fixed_line = self._fix_extension_type_api(line)
                elif issue["type"] == "flight_api_changes":
                    fixed_line = self._fix_flight_api(line)
                elif issue["type"] == "data_type_api":
                    fixed_line = self._fix_data_type_api(line)
                else:
                    logger.warning(f"不支持修复问题类型: {issue['type']}")
                    self.failed_fixes.append(issue)
                    continue
                
                if fixed_line != line:
                    # 替换行
                    lines[line_no] = fixed_line
                    self.fixed_issues.append(issue)
                    logger.info(f"修复了 {file_path}:{issue['line']} 的问题")
                else:
                    self.failed_fixes.append(issue)
                    logger.warning(f"无法修复 {file_path}:{issue['line']} 的问题")
            
            # 更新内容
            modified_content = "\n".join(lines)
            
            # 写回文件
            with open(abs_path, "w", encoding="utf-8") as f:
                f.write(modified_content)
        
        except Exception as e:
            logger.error(f"修复文件 {file_path} 时出错: {e}")
            for issue in issues:
                self.failed_fixes.append(issue)
    
    def _fix_extension_type_api(self, line: str) -> str:
        """修复扩展类型 API 问题
        
        Args:
            line: 代码行
            
        Returns:
            修复后的代码行
        """
        # 修复 pa.ExtensionArray.from_storage 调用
        pattern = r"(pa\.ExtensionArray\.from_storage\()(.*?)(,\s*)(.*?)(\))"
        if re.search(pattern, line):
            return re.sub(pattern, r"_create_extension_array(\2\3\4)", line)
        
        return line
    
    def _fix_flight_api(self, line: str) -> str:
        """修复 Flight API 问题
        
        Args:
            line: 代码行
            
        Returns:
            修复后的代码行
        """
        # 修复 FlightDescriptor.for_path 调用
        pattern = r"(flight\.FlightDescriptor\.for_path\()(.*?)(\))"
        if re.search(pattern, line):
            return re.sub(pattern, r"_create_flight_descriptor(\2)", line)
        
        return line
    
    def _fix_data_type_api(self, line: str) -> str:
        """修复数据类型 API 问题
        
        Args:
            line: 代码行
            
        Returns:
            修复后的代码行
        """
        # 目前没有特定的修复，因为 API 名称没有变化
        return line
    
    def generate_report(self, output_file: Optional[str] = None) -> None:
        """生成修复报告
        
        Args:
            output_file: 输出文件路径，如果为 None 则输出到控制台
        """
        # 生成报告
        report = []
        report.append("# PyArrow 14.0.0+ 兼容性修复报告\n")
        report.append(f"修复时间: {logging.Formatter().formatTime(logging.LogRecord('', 0, '', 0, None, None, None, None))}\n")
        report.append(f"项目路径: {self.project_path}\n")
        report.append(f"总问题数: {len(self.issues)}\n")
        report.append(f"成功修复: {len(self.fixed_issues)}\n")
        report.append(f"修复失败: {len(self.failed_fixes)}\n\n")
        
        # 成功修复的问题
        if self.fixed_issues:
            report.append("## 成功修复的问题\n\n")
            
            for issue in self.fixed_issues:
                report.append(f"- **{issue['file']}:{issue['line']}** ({issue['type']})\n")
                report.append(f"  ```python\n  {issue['content']}\n  ```\n")
            
            report.append("\n")
        
        # 修复失败的问题
        if self.failed_fixes:
            report.append("## 修复失败的问题\n\n")
            
            for issue in self.failed_fixes:
                report.append(f"- **{issue['file']}:{issue['line']}** ({issue['type']})\n")
                report.append(f"  ```python\n  {issue['content']}\n  ```\n")
            
            report.append("\n")
        
        # 添加手动修复建议
        report.append("## 手动修复建议\n\n")
        
        # 扩展类型 API 修复建议
        report.append("### 扩展类型 API 修复\n\n")
        report.append("添加兼容性函数：\n\n")
        report.append("```python\n")
        report.append("# PyArrow 14.0.0+ 兼容性处理\n")
        report.append("try:\n")
        report.append("    # 检查 PyArrow 版本\n")
        report.append("    import pyarrow as pa\n")
        report.append("    _pa_version = tuple(int(x) for x in pa.__version__.split('.'))\n")
        report.append("    _is_pa_14_plus = _pa_version >= (14, 0, 0)\n")
        report.append("except (ImportError, AttributeError):\n")
        report.append("    _is_pa_14_plus = False\n\n")
        report.append("# 扩展类型 API 兼容性处理\n")
        report.append("def _create_extension_array(extension_type, storage):\n")
        report.append("    \"\"\"创建扩展数组的兼容性函数\n")
        report.append("    \n")
        report.append("    Args:\n")
        report.append("        extension_type: 扩展类型\n")
        report.append("        storage: 存储数组\n")
        report.append("        \n")
        report.append("    Returns:\n")
        report.append("        扩展数组\n")
        report.append("    \"\"\"\n")
        report.append("    try:\n")
        report.append("        # PyArrow 14.0.0+ API\n")
        report.append("        if _is_pa_14_plus:\n")
        report.append("            return pa.ExtensionArray.from_storage(extension_type, storage)\n")
        report.append("        # 旧版 API\n")
        report.append("        else:\n")
        report.append("            return pa.ExtensionArray.from_storage(extension_type, storage)\n")
        report.append("    except Exception as e:\n")
        report.append("        logger.error(f\"创建扩展数组失败: {e}\")\n")
        report.append("        # 尝试直接使用存储数组\n")
        report.append("        return storage\n")
        report.append("```\n\n")
        report.append("然后替换所有 `pa.ExtensionArray.from_storage` 调用:\n\n")
        report.append("```python\n")
        report.append("# 修复前\n")
        report.append("result = pa.ExtensionArray.from_storage(extension_type, storage)\n\n")
        report.append("# 修复后\n")
        report.append("result = _create_extension_array(extension_type, storage)\n")
        report.append("```\n\n")
        
        # Flight API 修复建议
        report.append("### Flight API 修复\n\n")
        report.append("添加兼容性函数：\n\n")
        report.append("```python\n")
        report.append("# PyArrow Flight 14.0.0+ 兼容性处理\n")
        report.append("try:\n")
        report.append("    # 检查 PyArrow Flight 版本\n")
        report.append("    import pyarrow.flight as flight\n")
        report.append("    import pyarrow as pa\n")
        report.append("    _pa_version = tuple(int(x) for x in pa.__version__.split('.'))\n")
        report.append("    _is_pa_14_plus = _pa_version >= (14, 0, 0)\n")
        report.append("except (ImportError, AttributeError):\n")
        report.append("    _is_pa_14_plus = False\n\n")
        report.append("# Flight API 兼容性处理\n")
        report.append("def _create_flight_descriptor(path):\n")
        report.append("    \"\"\"创建 FlightDescriptor 的兼容性函数\n")
        report.append("    \n")
        report.append("    Args:\n")
        report.append("        path: 路径\n")
        report.append("        \n")
        report.append("    Returns:\n")
        report.append("        FlightDescriptor\n")
        report.append("    \"\"\"\n")
        report.append("    try:\n")
        report.append("        # 确保路径是字节串\n")
        report.append("        if isinstance(path, str):\n")
        report.append("            path = path.encode('utf-8')\n")
        report.append("        \n")
        report.append("        # PyArrow Flight 14.0.0+ API\n")
        report.append("        if _is_pa_14_plus:\n")
        report.append("            return flight.FlightDescriptor.for_path(path)\n")
        report.append("        # 旧版 API\n")
        report.append("        else:\n")
        report.append("            return flight.FlightDescriptor.for_path(path)\n")
        report.append("    except Exception as e:\n")
        report.append("        logger.error(f\"创建 FlightDescriptor 失败: {e}\")\n")
        report.append("        # 尝试直接使用路径\n")
        report.append("        return path\n")
        report.append("```\n\n")
        report.append("然后替换所有 `flight.FlightDescriptor.for_path` 调用:\n\n")
        report.append("```python\n")
        report.append("# 修复前\n")
        report.append("descriptor = flight.FlightDescriptor.for_path(path.encode('utf-8'))\n\n")
        report.append("# 修复后\n")
        report.append("descriptor = _create_flight_descriptor(path)\n")
        report.append("```\n\n")
        
        # 输出报告
        report_text = "".join(report)
        
        if output_file:
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(report_text)
            logger.info(f"修复报告已保存到: {output_file}")
        else:
            print(report_text)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="PyArrow 14.0.0+ 兼容性修复工具")
    parser.add_argument("project_path", help="要修复的项目路径")
    parser.add_argument("--report", "-r", help="兼容性问题报告文件路径")
    parser.add_argument("--output", "-o", help="输出修复报告文件路径")
    parser.add_argument("--no-backup", action="store_true", help="不创建备份文件")
    
    args = parser.parse_args()
    
    fixer = PyArrowCompatibilityFixer(args.project_path, args.report)
    fixer.fix_issues(backup=not args.no_backup)
    fixer.generate_report(output_file=args.output)


if __name__ == "__main__":
    main()
