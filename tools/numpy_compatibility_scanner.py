#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
NumPy 2.x 兼容性扫描工具

扫描项目中可能受 NumPy 2.x API 变更影响的代码，生成详细的兼容性问题报告。
"""

import os
import re
import ast
import sys
import argparse
import logging
from pathlib import Path
from typing import List, Dict, Set, Tuple, Optional, Any
import pyarrow as pa
import pyarrow.flight as flight
import pyarrow as pa


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("numpy_compatibility_scanner")

# 定义需要特别关注的 NumPy 2.x 兼容性问题
NUMPY_COMPATIBILITY_ISSUES = {
    # 函数返回类型变更：从列表变为元组
    "return_type_changes": [
        "np.meshgrid", 
        "numpy.meshgrid",
        "np.ogrid", 
        "numpy.ogrid",
        "np.indices", 
        "numpy.indices",
        "np.unravel_index", 
        "numpy.unravel_index",
        "np.where", 
        "numpy.where"
    ],
    
    # GIL 状态管理
    "gil_state_usage": [
        "np.gil_state", 
        "numpy.gil_state"
    ],
    
    # 类型提升行为变更
    "type_promotion": [
        # 涉及不同数据类型的数组操作
        r"np\.array\([^)]+dtype=[^)]+\)\s*[\+\-\*\/]\s*np\.array",
        r"numpy\.array\([^)]+dtype=[^)]+\)\s*[\+\-\*\/]\s*numpy\.array"
    ],
    
    # copy=False 行为变更
    "copy_behavior": [
        r"np\.array\([^)]+copy\s*=\s*False", 
        r"numpy\.array\([^)]+copy\s*=\s*False"
    ],
    
    # any 和 all 返回值变化
    "any_all_return": [
        r"np\.any\(.*object\(\).*\)", 
        r"numpy\.any\(.*object\(\).*\)",
        r"np\.all\(.*object\(\).*\)", 
        r"numpy\.all\(.*object\(\).*\)"
    ],
    
    # 标量表示变更
    "scalar_representation": [
        r"str\(np\.[a-z]+\([^)]+\)\)", 
        r"str\(numpy\.[a-z]+\([^)]+\)\)"
    ]
}

# PyArrow 兼容性问题
PYARROW_COMPATIBILITY_ISSUES = {
    # Arrow Flight API 变更
    "flight_api_changes": [
        "FlightServerBase", 
        "FlightClient",
        "FlightDescriptor",
        "FlightInfo",
        "FlightEndpoint"
    ],
    
    # PyArrow 数据类型 API 变更
    "data_type_api": [
        "pa.DataType", 
        "pa.Schema",
        "pa.field",
        "pa.struct",
        "pa.list_",
        "pa.map_"
    ],
    
    # PyArrow 扩展类型 API 变更
    "extension_type_api": [
        "pa.ExtensionType", 
        "pa.ExtensionArray",
        "__arrow_ext_serialize__",
        "__arrow_ext_deserialize__"
    ]
}


class CompatibilityScanner:
    """NumPy 和 PyArrow 兼容性扫描器"""
    
    def __init__(self, project_path: str):
        """初始化扫描器
        
        Args:
            project_path: 项目路径
        """
        self.project_path = Path(project_path)
        self.issues = []
        self.files_scanned = 0
        self.files_with_issues = 0
        
    def scan_project(self, exclude_dirs: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """扫描整个项目
        
        Args:
            exclude_dirs: 要排除的目录列表
            
        Returns:
            兼容性问题列表
        """
        if exclude_dirs is None:
            exclude_dirs = [".git", "__pycache__", "venv", "env", ".venv", ".env", "build", "dist"]
        
        logger.info(f"开始扫描项目: {self.project_path}")
        
        for root, dirs, files in os.walk(self.project_path):
            # 排除指定目录
            dirs[:] = [d for d in dirs if d not in exclude_dirs]
            
            for file in files:
                if file.endswith(".py"):
                    file_path = Path(root) / file
                    self._scan_file(file_path)
        
        logger.info(f"扫描完成。扫描了 {self.files_scanned} 个文件，发现 {self.files_with_issues} 个文件存在兼容性问题。")
        logger.info(f"共发现 {len(self.issues)} 个兼容性问题。")
        
        return self.issues
    
    def _scan_file(self, file_path: Path) -> None:
        """扫描单个文件
        
        Args:
            file_path: 文件路径
        """
        self.files_scanned += 1
        rel_path = file_path.relative_to(self.project_path)
        
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            # 检查 NumPy 兼容性问题
            numpy_issues = self._check_numpy_compatibility(content, file_path)
            
            # 检查 PyArrow 兼容性问题
            pyarrow_issues = self._check_pyarrow_compatibility(content, file_path)
            
            # 合并问题
            file_issues = numpy_issues + pyarrow_issues
            
            if file_issues:
                self.files_with_issues += 1
                self.issues.extend(file_issues)
                logger.info(f"文件 {rel_path} 中发现 {len(file_issues)} 个兼容性问题。")
        
        except Exception as e:
            logger.error(f"扫描文件 {rel_path} 时出错: {e}")
    
    def _check_numpy_compatibility(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """检查 NumPy 兼容性问题
        
        Args:
            content: 文件内容
            file_path: 文件路径
            
        Returns:
            兼容性问题列表
        """
        issues = []
        rel_path = file_path.relative_to(self.project_path)
        
        # 检查是否导入了 NumPy
        if "import numpy" not in content and "from numpy" not in content:
            return issues
        
        # 检查各类兼容性问题
        for issue_type, patterns in NUMPY_COMPATIBILITY_ISSUES.items():
            for pattern in patterns:
                # 对于正则表达式模式
                if pattern.startswith("r\"") or pattern.startswith("r'"):
                    pattern = pattern[2:-1]  # 去掉 r"..." 的引号
                    matches = re.finditer(pattern, content)
                    for match in matches:
                        line_no = content[:match.start()].count('\n') + 1
                        line_content = content.splitlines()[line_no - 1]
                        issues.append({
                            "file": str(rel_path),
                            "line": line_no,
                            "type": issue_type,
                            "content": line_content.strip(),
                            "message": self._get_issue_message(issue_type)
                        })
                # 对于简单字符串模式
                else:
                    for line_no, line in enumerate(content.splitlines(), 1):
                        if pattern in line:
                            issues.append({
                                "file": str(rel_path),
                                "line": line_no,
                                "type": issue_type,
                                "content": line.strip(),
                                "message": self._get_issue_message(issue_type)
                            })
        
        return issues
    
    def _check_pyarrow_compatibility(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """检查 PyArrow 兼容性问题
        
        Args:
            content: 文件内容
            file_path: 文件路径
            
        Returns:
            兼容性问题列表
        """
        issues = []
        rel_path = file_path.relative_to(self.project_path)
        
        # 检查是否导入了 PyArrow
        if "import pyarrow" not in content and "from pyarrow" not in content:
            return issues
        
        # 检查各类兼容性问题
        for issue_type, patterns in PYARROW_COMPATIBILITY_ISSUES.items():
            for pattern in patterns:
                for line_no, line in enumerate(content.splitlines(), 1):
                    if pattern in line:
                        issues.append({
                            "file": str(rel_path),
                            "line": line_no,
                            "type": issue_type,
                            "content": line.strip(),
                            "message": self._get_pyarrow_issue_message(issue_type)
                        })
        
        return issues
    
    def _get_issue_message(self, issue_type: str) -> str:
        """获取 NumPy 兼容性问题的描述信息
        
        Args:
            issue_type: 问题类型
            
        Returns:
            问题描述
        """
        messages = {
            "return_type_changes": "NumPy 2.x 中此函数返回元组而非列表，需要使用 list() 转换或修改解包逻辑",
            "gil_state_usage": "使用了 NumPy 2.x 特有的 gil_state 功能，需要添加兼容性处理",
            "type_promotion": "NumPy 2.x 中类型提升行为发生变化，可能导致计算结果不同",
            "copy_behavior": "NumPy 2.x 中 copy=False 行为变化，如果需要复制会引发 ValueError",
            "any_all_return": "NumPy 2.x 中 any/all 对对象数组返回布尔值而非最后一个对象",
            "scalar_representation": "NumPy 2.x 中标量表示方式变更，现在打印为 np.float64(3.0) 而非仅 3.0"
        }
        
        return messages.get(issue_type, "未知的 NumPy 兼容性问题")
    
    def _get_pyarrow_issue_message(self, issue_type: str) -> str:
        """获取 PyArrow 兼容性问题的描述信息
        
        Args:
            issue_type: 问题类型
            
        Returns:
            问题描述
        """
        messages = {
            "flight_api_changes": "PyArrow Flight API 在 14.0.0+ 版本中可能有变更，请检查接口兼容性",
            "data_type_api": "PyArrow 数据类型 API 在 14.0.0+ 版本中可能有变更，请检查接口兼容性",
            "extension_type_api": "PyArrow 扩展类型 API 在 14.0.0+ 版本中可能有变更，请检查接口兼容性"
        }
        
        return messages.get(issue_type, "未知的 PyArrow 兼容性问题")
    
    def generate_report(self, output_file: Optional[str] = None) -> None:
        """生成兼容性问题报告
        
        Args:
            output_file: 输出文件路径，如果为 None 则输出到控制台
        """
        if not self.issues:
            logger.info("未发现兼容性问题。")
            return
        
        # 按文件和行号排序
        sorted_issues = sorted(self.issues, key=lambda x: (x["file"], x["line"]))
        
        # 按问题类型分组
        issues_by_type = {}
        for issue in sorted_issues:
            issue_type = issue["type"]
            if issue_type not in issues_by_type:
                issues_by_type[issue_type] = []
            issues_by_type[issue_type].append(issue)
        
        # 生成报告
        report = []
        report.append("# NumPy 2.x 和 PyArrow 14.0.0+ 兼容性问题报告\n")
        report.append(f"扫描时间: {logging.Formatter().formatTime(logging.LogRecord('', 0, '', 0, None, None, None, None))}\n")
        report.append(f"扫描项目: {self.project_path}\n")
        report.append(f"扫描文件数: {self.files_scanned}\n")
        report.append(f"存在问题的文件数: {self.files_with_issues}\n")
        report.append(f"问题总数: {len(self.issues)}\n\n")
        
        # 按问题类型生成详细报告
        for issue_type, issues in issues_by_type.items():
            report.append(f"## {issue_type} ({len(issues)}个问题)\n")
            
            # 添加问题描述
            if issue_type in NUMPY_COMPATIBILITY_ISSUES:
                report.append(f"{self._get_issue_message(issue_type)}\n\n")
            else:
                report.append(f"{self._get_pyarrow_issue_message(issue_type)}\n\n")
            
            # 添加问题详情
            for issue in issues:
                report.append(f"- **{issue['file']}:{issue['line']}**\n")
                report.append(f"  ```python\n  {issue['content']}\n  ```\n")
            
            report.append("\n")
        
        # 添加修复建议
        report.append("## 修复建议\n\n")
        
        # 返回类型变更修复建议
        if "return_type_changes" in issues_by_type:
            report.append("### 返回类型变更修复\n\n")
            report.append("```python\n")
            report.append("# 修复前\n")
            report.append("phi, theta = list(np.meshgrid(phi, theta))\n\n")
            report.append("# 修复后\n")
            report.append("phi, theta = list(np.meshgrid(phi, theta))\n")
            report.append("```\n\n")
        
        # GIL 状态管理修复建议
        if "gil_state_usage" in issues_by_type:
            report.append("### GIL 状态管理修复\n\n")
            report.append("创建兼容性工具 `gil_utils.py`:\n\n")
            report.append("```python\n")
            report.append("import contextlib\n")
            report.append("import numpy as np\n\n")
            report.append("@contextlib.contextmanager\n")
            report.append("def gil_state(acquire=True):\n")
            report.append("    \"\"\"\n")
            report.append("    GIL 状态管理上下文管理器\n")
            report.append("    \n")
            report.append("    在支持的 Python 版本中管理 GIL 状态，在不支持的版本中为空操作\n")
            report.append("    \n")
            report.append("    Args:\n")
            report.append("        acquire: 是否获取 GIL，True 表示获取，False 表示释放\n")
            report.append("        \n")
            report.append("    Yields:\n")
            report.append("        None\n")
            report.append("    \"\"\"\n")
            report.append("    try:\n")
            report.append("        # 尝试使用 NumPy 2.2.5+ 的 gil_state\n")
            report.append("        with gil_state(acquire):\n")
            report.append("            yield\n")
            report.append("    except (AttributeError, ImportError):\n")
            report.append("        # 在不支持的版本中为空操作\n")
            report.append("        yield\n")
            report.append("```\n\n")
            report.append("然后替换所有 `np.gil_state` 调用:\n\n")
            report.append("```python\n")
            report.append("# 修复前\n")
            report.append("with gil_state(False):\n")
            report.append("    # 计算密集型操作\n\n")
            report.append("# 修复后\n")
            report.append("from .gil_utils import gil_state\n")
            report.append("with gil_state(False):\n")
            report.append("    # 计算密集型操作\n")
            report.append("```\n\n")
        
        # 类型提升行为修复建议
        if "type_promotion" in issues_by_type:
            report.append("### 类型提升行为修复\n\n")
            report.append("```python\n")
            report.append("# 修复前\n")
            report.append("result = np.array([1], dtype=np.int8) + np.array([256], dtype=np.uint16)\n\n")
            report.append("# 修复后 - 显式指定结果类型\n")
            report.append("result = (np.array([1], dtype=np.int8) + np.array([256], dtype=np.uint16)).astype(np.int32)\n")
            report.append("```\n\n")
        
        # 输出报告
        report_text = "".join(report)
        
        if output_file:
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(report_text)
            logger.info(f"兼容性问题报告已保存到: {output_file}")
        else:
            print(report_text)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="NumPy 2.x 和 PyArrow 14.0.0+ 兼容性扫描工具")
    parser.add_argument("project_path", help="要扫描的项目路径")
    parser.add_argument("--output", "-o", help="输出报告文件路径")
    parser.add_argument("--exclude", "-e", nargs="+", help="要排除的目录列表")
    
    args = parser.parse_args()
    
    scanner = CompatibilityScanner(args.project_path)
    scanner.scan_project(exclude_dirs=args.exclude)
    scanner.generate_report(output_file=args.output)


if __name__ == "__main__":
    main()