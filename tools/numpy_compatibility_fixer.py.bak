#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
NumPy 2.x 兼容性修复工具

自动修复 NumPy 2.x 和 PyArrow 14.0.0+ 兼容性问题。
"""

import os
import re
import sys
import json
import argparse
import logging
import tempfile
import shutil
from pathlib import Path
from typing import List, Dict, Set, Tuple, Optional, Any, Union

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("numpy_compatibility_fixer")


class CompatibilityFixer:
    """NumPy 和 PyArrow 兼容性修复器"""
    
    def __init__(self, project_path: str, report_file: Optional[str] = None):
        """初始化修复器
        
        Args:
            project_path: 项目路径
            report_file: 兼容性问题报告文件路径，如果为 None 则自动扫描
        """
        self.project_path = Path(project_path)
        self.report_file = report_file
        self.issues = []
        self.fixed_issues = []
        self.failed_fixes = []
        
        # 如果提供了报告文件，则从中加载问题
        if report_file:
            self._load_issues_from_report(report_file)
    
    def _load_issues_from_report(self, report_file: str) -> None:
        """从报告文件加载兼容性问题
        
        Args:
            report_file: 报告文件路径
        """
        try:
            with open(report_file, "r", encoding="utf-8") as f:
                report_content = f.read()
            
            # 解析报告中的问题
            # 这里假设报告是由 numpy_compatibility_scanner.py 生成的 Markdown 格式
            issues = []
            
            # 使用正则表达式提取问题
            pattern = r"- \*\*(.*?):(\d+)\*\*\n\s+```python\n\s+(.*?)\n\s+```"
            matches = re.finditer(pattern, report_content, re.DOTALL)
            
            for match in matches:
                file_path = match.group(1)
                line_no = int(match.group(2))
                content = match.group(3).strip()
                
                # 尝试确定问题类型
                issue_type = "unknown"
                if "np.meshgrid" in content or "numpy.meshgrid" in content or "np.ogrid" in content:
                    issue_type = "return_type_changes"
                elif "np.gil_state" in content or "numpy.gil_state" in content:
                    issue_type = "gil_state_usage"
                elif "dtype=" in content and ("+" in content or "-" in content or "*" in content or "/" in content):
                    issue_type = "type_promotion"
                elif "copy=False" in content:
                    issue_type = "copy_behavior"
                
                issues.append({
                    "file": file_path,
                    "line": line_no,
                    "type": issue_type,
                    "content": content
                })
            
            self.issues = issues
            logger.info(f"从报告文件加载了 {len(issues)} 个兼容性问题。")
        
        except Exception as e:
            logger.error(f"从报告文件加载问题时出错: {e}")
            self.issues = []
    
    def scan_project(self) -> None:
        """扫描项目中的兼容性问题"""
        if self.issues:
            logger.info("已从报告文件加载问题，跳过扫描。")
            return
        
        try:
            # 导入扫描器
            sys.path.insert(0, str(self.project_path / "tools"))
            from numpy_compatibility_scanner import CompatibilityScanner
            
            # 扫描项目
            scanner = CompatibilityScanner(self.project_path)
            self.issues = scanner.scan_project()
            
            logger.info(f"扫描发现 {len(self.issues)} 个兼容性问题。")
        
        except ImportError:
            logger.error("无法导入兼容性扫描器，请确保 numpy_compatibility_scanner.py 在 tools 目录中。")
            sys.exit(1)
    
    def fix_issues(self, backup: bool = True) -> None:
        """修复兼容性问题
        
        Args:
            backup: 是否在修复前备份文件
        """
        if not self.issues:
            logger.info("没有需要修复的兼容性问题。")
            return
        
        logger.info(f"开始修复 {len(self.issues)} 个兼容性问题...")
        
        # 按文件分组问题
        issues_by_file = {}
        for issue in self.issues:
            file_path = issue["file"]
            if file_path not in issues_by_file:
                issues_by_file[file_path] = []
            issues_by_file[file_path].append(issue)
        
        # 处理每个文件
        for file_path, file_issues in issues_by_file.items():
            self._fix_file_issues(file_path, file_issues, backup)
        
        logger.info(f"修复完成。成功修复 {len(self.fixed_issues)} 个问题，失败 {len(self.failed_fixes)} 个问题。")
    
    def _fix_file_issues(self, file_path: str, issues: List[Dict[str, Any]], backup: bool) -> None:
        """修复单个文件中的兼容性问题
        
        Args:
            file_path: 文件路径
            issues: 文件中的问题列表
            backup: 是否在修复前备份文件
        """
        abs_path = self.project_path / file_path
        
        try:
            # 读取文件内容
            with open(abs_path, "r", encoding="utf-8") as f:
                content = f.read()
                lines = content.splitlines()
            
            # 创建备份
            if backup:
                backup_path = abs_path.with_suffix(abs_path.suffix + ".bak")
                shutil.copy2(abs_path, backup_path)
                logger.info(f"已创建备份: {backup_path}")
            
            # 修复问题
            modified_content = content
            
            # 首先检查是否需要添加 gil_utils 导入
            needs_gil_utils = any(issue["type"] == "gil_state_usage" for issue in issues)
            
            if needs_gil_utils:
                # 创建 gil_utils.py 文件（如果不存在）
                self._create_gil_utils()
                
                # 添加导入语句
                import_path = self._get_relative_import_path(file_path, "gil_utils")
                import_statement = f"from {import_path} import gil_state\n"
                
                # 检查是否已经导入
                if import_statement not in modified_content:
                    # 找到最后一个导入语句
                    import_pattern = r"^(?:from\s+[\w.]+\s+import|\s*import)\s+.*?$"
                    matches = list(re.finditer(import_pattern, modified_content, re.MULTILINE))
                    
                    if matches:
                        last_import = matches[-1]
                        insert_pos = last_import.end()
                        modified_content = modified_content[:insert_pos] + "\n" + import_statement + modified_content[insert_pos:]
                    else:
                        # 如果没有找到导入语句，在文件开头添加
                        modified_content = import_statement + modified_content
            
            # 按行号倒序处理问题，避免修改后行号变化的问题
            sorted_issues = sorted(issues, key=lambda x: x["line"], reverse=True)
            
            for issue in sorted_issues:
                line_no = issue["line"] - 1  # 转为0索引
                line = lines[line_no] if line_no < len(lines) else ""
                
                # 根据问题类型修复
                if issue["type"] == "return_type_changes":
                    fixed_line = self._fix_return_type(line)
                elif issue["type"] == "gil_state_usage":
                    fixed_line = self._fix_gil_state(line)
                elif issue["type"] == "type_promotion":
                    fixed_line = self._fix_type_promotion(line)
                elif issue["type"] == "copy_behavior":
                    fixed_line = self._fix_copy_behavior(line)
                else:
                    logger.warning(f"不支持修复问题类型: {issue['type']}")
                    self.failed_fixes.append(issue)
                    continue
                
                if fixed_line != line:
                    # 替换行
                    lines[line_no] = fixed_line
                    modified_content = "\n".join(lines)
                    self.fixed_issues.append(issue)
                    logger.info(f"修复了 {file_path}:{issue['line']} 的问题")
                else:
                    self.failed_fixes.append(issue)
                    logger.warning(f"无法修复 {file_path}:{issue['line']} 的问题")
            
            # 写回文件
            with open(abs_path, "w", encoding="utf-8") as f:
                f.write(modified_content)
        
        except Exception as e:
            logger.error(f"修复文件 {file_path} 时出错: {e}")
            for issue in issues:
                self.failed_fixes.append(issue)
    
    def _fix_return_type(self, line: str) -> str:
        """修复函数返回类型问题
        
        Args:
            line: 代码行
            
        Returns:
            修复后的代码行
        """
        # 匹配 np.meshgrid, np.ogrid 等函数调用的解包赋值
        patterns = [
            (r"(\w+(?:\s*,\s*\w+)+)\s*=\s*(np\.meshgrid\(.*?\))", r"\1 = list(\2)"),
            (r"(\w+(?:\s*,\s*\w+)+)\s*=\s*(numpy\.meshgrid\(.*?\))", r"\1 = list(\2)"),
            (r"(\w+(?:\s*,\s*\w+)+)\s*=\s*(np\.ogrid\(.*?\))", r"\1 = list(\2)"),
            (r"(\w+(?:\s*,\s*\w+)+)\s*=\s*(numpy\.ogrid\(.*?\))", r"\1 = list(\2)"),
            (r"(\w+(?:\s*,\s*\w+)+)\s*=\s*(np\.indices\(.*?\))", r"\1 = list(\2)"),
            (r"(\w+(?:\s*,\s*\w+)+)\s*=\s*(numpy\.indices\(.*?\))", r"\1 = list(\2)"),
            (r"(\w+(?:\s*,\s*\w+)+)\s*=\s*(np\.unravel_index\(.*?\))", r"\1 = list(\2)"),
            (r"(\w+(?:\s*,\s*\w+)+)\s*=\s*(numpy\.unravel_index\(.*?\))", r"\1 = list(\2)"),
            (r"(\w+(?:\s*,\s*\w+)+)\s*=\s*(np\.where\(.*?\))", r"\1 = list(\2)"),
            (r"(\w+(?:\s*,\s*\w+)+)\s*=\s*(numpy\.where\(.*?\))", r"\1 = list(\2)")
        ]
        
        for pattern, replacement in patterns:
            if re.search(pattern, line):
                return re.sub(pattern, replacement, line)
        
        return line
    
    def _fix_gil_state(self, line: str) -> str:
        """修复 GIL 状态管理问题
        
        Args:
            line: 代码行
            
        Returns:
            修复后的代码行
        """
        # 替换 np.gil_state 为 gil_state
        patterns = [
            (r"with\s+np\.gil_state\((.*?)\)", r"with gil_state(\1)"),
            (r"with\s+numpy\.gil_state\((.*?)\)", r"with gil_state(\1)")
        ]
        
        for pattern, replacement in patterns:
            if re.search(pattern, line):
                return re.sub(pattern, replacement, line)
        
        return line
    
    def _fix_type_promotion(self, line: str) -> str:
        """修复类型提升行为问题
        
        Args:
            line: 代码行
            
        Returns:
            修复后的代码行
        """
        # 这是一个复杂的问题，需要更深入的分析
        # 这里只提供一个简单的修复示例
        
        # 匹配不同类型数组的操作
        pattern = r"(np\.array\([^)]+dtype=[^)]+\))\s*([\+\-\*\/])\s*(np\.array\([^)]+\))"
        
        if re.search(pattern, line):
            # 添加显式类型转换
            match = re.search(pattern, line)
            if match:
                expr = match.group(0)
                return line.replace(expr, f"({expr}).astype(np.float64)")
        
        return line
    
    def _fix_copy_behavior(self, line: str) -> str:
        """修复 copy=False 行为问题
        
        Args:
            line: 代码行
            
        Returns:
            修复后的代码行
        """
        # 替换 copy=False 为 copy=True
        patterns = [
            (r"np\.array\(([^,]+),\s*copy\s*=\s*False", r"np.array(\1, copy=True"),
            (r"numpy\.array\(([^,]+),\s*copy\s*=\s*False", r"numpy.array(\1, copy=True")
        ]
        
        for pattern, replacement in patterns:
            if re.search(pattern, line):
                return re.sub(pattern, replacement, line)
        
        return line
    
    def _create_gil_utils(self) -> None:
        """创建 gil_utils.py 文件"""
        gil_utils_dir = self.project_path / "src" / "core" / "data" / "arrow_types"
        gil_utils_path = gil_utils_dir / "gil_utils.py"
        
        # 检查文件是否已存在
        if gil_utils_path.exists():
            logger.info(f"gil_utils.py 文件已存在: {gil_utils_path}")
            return
        
        # 创建目录（如果不存在）
        gil_utils_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建文件
        gil_utils_content = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-

\"\"\"
GIL 状态管理工具

提供在不同 Python 版本中一致的 GIL 管理接口。
\"\"\"

import contextlib
import numpy as np


@contextlib.contextmanager
def gil_state(acquire=True):
    \"\"\"
    GIL 状态管理上下文管理器
    
    在支持的 Python 版本中管理 GIL 状态，在不支持的版本中为空操作
    
    Args:
        acquire: 是否获取 GIL，True 表示获取，False 表示释放
        
    Yields:
        None
    \"\"\"
    try:
        # 尝试使用 NumPy 2.2.5+ 的 gil_state
        with np.gil_state(acquire):
            yield
    except (AttributeError, ImportError):
        # 在不支持的版本中为空操作
        yield
"""
        
        with open(gil_utils_path, "w", encoding="utf-8") as f:
            f.write(gil_utils_content)
        
        logger.info(f"创建了 gil_utils.py 文件: {gil_utils_path}")
    
    def _get_relative_import_path(self, file_path: str, module: str) -> str:
        """获取相对导入路径
        
        Args:
            file_path: 文件路径
            module: 模块名
            
        Returns:
            相对导入路径
        """
        # 将文件路径转换为 Python 导入路径
        parts = Path(file_path).parts
        
        # 查找 src 或项目根目录
        if "src" in parts:
            src_index = parts.index("src")
            import_parts = parts[src_index:]
        else:
            import_parts = parts
        
        # 构建导入路径
        import_path = ".".join(import_parts[:-1])  # 排除文件名
        
        # 检查是否是同一目录
        if Path(file_path).parent.name == "arrow_types":
            return f".{module}"
        else:
            return f"{import_path}.{module}" if import_path else module
    
    def generate_report(self, output_file: Optional[str] = None) -> None:
        """生成修复报告
        
        Args:
            output_file: 输出文件路径，如果为 None 则输出到控制台
        """
        # 生成报告
        report = []
        report.append("# NumPy 2.x 和 PyArrow 14.0.0+ 兼容性修复报告\n")
        report.append(f"修复时间: {logging.Formatter().formatTime(logging.LogRecord('', 0, '', 0, None, None, None, None))}\n")
        report.append(f"项目路径: {self.project_path}\n")
        report.append(f"总问题数: {len(self.issues)}\n")
        report.append(f"成功修复: {len(self.fixed_issues)}\n")
        report.append(f"修复失败: {len(self.failed_fixes)}\n\n")
        
        # 成功修复的问题
        if self.fixed_issues:
            report.append("## 成功修复的问题\n\n")
            
            for issue in self.fixed_issues:
                report.append(f"- **{issue['file']}:{issue['line']}** ({issue['type']})\n")
                report.append(f"  ```python\n  {issue['content']}\n  ```\n")
            
            report.append("\n")
        
        # 修复失败的问题
        if self.failed_fixes:
            report.append("## 修复失败的问题\n\n")
            
            for issue in self.failed_fixes:
                report.append(f"- **{issue['file']}:{issue['line']}** ({issue['type']})\n")
                report.append(f"  ```python\n  {issue['content']}\n  ```\n")
            
            report.append("\n")
        
        # 添加手动修复建议
        if self.failed_fixes:
            report.append("## 手动修复建议\n\n")
            
            # 按问题类型分组
            issues_by_type = {}
            for issue in self.failed_fixes:
                issue_type = issue["type"]
                if issue_type not in issues_by_type:
                    issues_by_type[issue_type] = []
                issues_by_type[issue_type].append(issue)
            
            # 返回类型变更修复建议
            if "return_type_changes" in issues_by_type:
                report.append("### 返回类型变更修复\n\n")
                report.append("```python\n")
                report.append("# 修复前\n")
                report.append("phi, theta = np.meshgrid(phi, theta)\n\n")
                report.append("# 修复后\n")
                report.append("phi, theta = list(np.meshgrid(phi, theta))\n")
                report.append("```\n\n")
            
            # 类型提升行为修复建议
            if "type_promotion" in issues_by_type:
                report.append("### 类型提升行为修复\n\n")
                report.append("```python\n")
                report.append("# 修复前\n")
                report.append("result = np.array([1], dtype=np.int8) + np.array([256], dtype=np.uint16)\n\n")
                report.append("# 修复后 - 显式指定结果类型\n")
                report.append("result = (np.array([1], dtype=np.int8) + np.array([256], dtype=np.uint16)).astype(np.int32)\n")
                report.append("```\n\n")
        
        # 输出报告
        report_text = "".join(report)
        
        if output_file:
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(report_text)
            logger.info(f"修复报告已保存到: {output_file}")
        else:
            print(report_text)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="NumPy 2.x 和 PyArrow 14.0.0+ 兼容性修复工具")
    parser.add_argument("project_path", help="要修复的项目路径")
    parser.add_argument("--report", "-r", help="兼容性问题报告文件路径")
    parser.add_argument("--output", "-o", help="输出修复报告文件路径")
    parser.add_argument("--no-backup", action="store_true", help="不创建备份文件")
    
    args = parser.parse_args()
    
    fixer = CompatibilityFixer(args.project_path, args.report)
    
    if not args.report:
        fixer.scan_project()
    
    fixer.fix_issues(backup=not args.no_backup)
    fixer.generate_report(output_file=args.output)


if __name__ == "__main__":
    main()
