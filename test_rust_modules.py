"""测试 Rust 模块的功能"""
import unittest
import numpy as np
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

class TestRustModules(unittest.TestCase):
    def setUp(self):
        """导入所需模块"""
        try:
            # 从编译后的动态库目录导入模块
            lib_path = project_root / "rust_operators" / "target" / "release"
            if lib_path.exists():
                sys.path.append(str(lib_path))
            
            # 导入Python封装模块
            from rust_operators.nonlinear_interference.python import NonlinearInterference
            from rust_operators.fractal_routing.python import FractalRouter
            from rust_operators.game_scheduler.python import GameScheduler, Resource, Task
            
            self.NonlinearInterference = NonlinearInterference
            self.FractalRouter = FractalRouter
            self.GameScheduler = GameScheduler
            self.Resource = Resource
            self.Task = Task
        except ImportError as e:
            print(f"导入错误: {e}")
            print("正在使用Python后备实现...")
            # 使用Python后备实现
            from rust_operators.nonlinear_interference.python.backend import NonlinearInterference
            from rust_operators.fractal_routing.python.backend import FractalRouter
            from rust_operators.game_scheduler.python.backend import GameScheduler, Resource, Task
            
            self.NonlinearInterference = NonlinearInterference
            self.FractalRouter = FractalRouter
            self.GameScheduler = GameScheduler
            self.Resource = Resource
            self.Task = Task
            
    def test_nonlinear_interference(self):
        """测试非线性干涉算法"""
        # 创建测试数据
        test_data = np.random.rand(64) + 1j * np.random.rand(64)
        test_data = test_data / np.linalg.norm(test_data)
        test_data2 = np.random.rand(64) + 1j * np.random.rand(64)
        test_data2 = test_data2 / np.linalg.norm(test_data2)
        
        try:
            # 创建非线性干涉算子
            operator = self.NonlinearInterference(lambda_param=0.5, fft_threshold=64)
            # 进行干涉融合
            result = operator.fuse(test_data, test_data2)
            self.assertIsNotNone(result)
            self.assertEqual(len(result), len(test_data))
            # 验证结果是否为单位向量
            norm = np.linalg.norm(result)
            self.assertAlmostEqual(norm, 1.0, places=6)
        except Exception as e:
            self.fail(f"非线性干涉算法测试失败: {e}")
            
    def test_fractal_routing(self):
        """测试分形动力学路由算法"""
        try:
            router = self.FractalRouter(
                dimension=2.5,
                max_hops=20,
                optimization_enabled=True,
                adaptive_mode=True,
                routing_strategy="adaptive"
            )
            # 使用apply方法进行路由
            routes = router.apply("A", "C")
            self.assertIsNotNone(routes)
            self.assertTrue(isinstance(routes, list))
            self.assertTrue(len(routes) > 0)
            # 验证每个路由路径
            for route in routes:
                self.assertTrue(isinstance(route, list))
                self.assertTrue(len(route) > 0)
                self.assertEqual(route[0], "A")  # 起点
                self.assertEqual(route[-1], "C")  # 终点
        except Exception as e:
            self.fail(f"分形动力学路由算法测试失败: {e}")
            
    def test_game_scheduler(self):
        """测试博弈优化资源调度算法"""
        try:
            # 创建调度器
            scheduler = self.GameScheduler(
                policy="nash_equilibrium",
                num_agents=4,
                max_iterations=100,
                convergence_threshold=1e-6,
                seed=42
            )
            
            # 创建资源
            cpu_resource = self.Resource(
                id="cpu",
                capacity=100.0,
                available_capacity=100.0,
                performance=1.0,
                cost=1.0
            )
            mem_resource = self.Resource(
                id="memory",
                capacity=1000.0,
                available_capacity=1000.0,
                performance=1.0,
                cost=1.0
            )
            
            # 注册资源
            scheduler.register_resource(cpu_resource)
            scheduler.register_resource(mem_resource)
            
            # 创建任务
            task1 = self.Task(
                id="task1",
                resource_demand=10.0,
                priority=1.0,
                deadline=100.0,
                status="pending"
            )
            task2 = self.Task(
                id="task2",
                resource_demand=20.0,
                priority=2.0,
                deadline=200.0,
                status="pending"
            )
            
            # 注册任务
            scheduler.register_task(task1)
            scheduler.register_task(task2)
            
            # 执行调度
            schedule = scheduler.schedule()
            
            self.assertIsNotNone(schedule)
            self.assertTrue(isinstance(schedule, dict))
            # 验证调度结果包含所有任务
            self.assertEqual(len(schedule), 2)
            self.assertIn("task1", schedule)
            self.assertIn("task2", schedule)
            
        except Exception as e:
            self.fail(f"博弈优化资源调度算法测试失败: {e}")

if __name__ == '__main__':
    unittest.main()