#!/usr/bin/env python3
"""
简化的AQFH记忆统一工具
专注于解决多实例记忆数据统一问题
"""

import os
import shutil
from pathlib import Path
from datetime import datetime

def discover_memory_directories():
    """发现所有记忆目录"""
    base_path = Path.home() / ".aqfh"
    if not base_path.exists():
        print(f"❌ 基础路径不存在: {base_path}")
        return []
    
    memory_dirs = []
    unified_path = base_path / "unified_consciousness"
    
    print(f"🔍 扫描基础路径: {base_path}")
    
    for item in base_path.iterdir():
        if item.is_dir() and item.name != "unified_consciousness":
            # 检查是否包含parquet文件
            parquet_files = list(item.rglob("*.parquet"))
            if parquet_files:
                memory_dirs.append({
                    'path': item,
                    'name': item.name,
                    'parquet_count': len(parquet_files)
                })
                print(f"  📁 {item.name}: {len(parquet_files)} 个parquet文件")
    
    return memory_dirs

def copy_parquet_files(source_dirs, target_dir):
    """复制parquet文件到统一目录"""
    target_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建分类目录
    categories = {
        'mcp': target_dir / "mcp_memories",
        'test': target_dir / "test_memories", 
        'palace': target_dir / "palace_memories",
        'hybrid': target_dir / "hybrid_memories",
        'other': target_dir / "other_memories"
    }
    
    for cat_dir in categories.values():
        cat_dir.mkdir(parents=True, exist_ok=True)
    
    total_copied = 0
    
    for source_info in source_dirs:
        source_path = source_info['path']
        source_name = source_info['name']
        
        print(f"\n📂 处理目录: {source_name}")
        
        # 确定分类
        if 'mcp' in source_name.lower():
            category = 'mcp'
        elif 'test' in source_name.lower():
            category = 'test'
        elif 'palace' in source_name.lower():
            category = 'palace'
        elif 'hybrid' in source_name.lower():
            category = 'hybrid'
        else:
            category = 'other'
        
        target_category_dir = categories[category] / source_name
        target_category_dir.mkdir(parents=True, exist_ok=True)
        
        # 复制所有parquet文件
        parquet_files = list(source_path.rglob("*.parquet"))
        copied_count = 0
        
        for parquet_file in parquet_files:
            try:
                # 保持相对路径结构
                relative_path = parquet_file.relative_to(source_path)
                target_file = target_category_dir / relative_path
                target_file.parent.mkdir(parents=True, exist_ok=True)
                
                # 复制文件
                shutil.copy2(parquet_file, target_file)
                copied_count += 1
                
            except Exception as e:
                print(f"    ❌ 复制失败 {parquet_file}: {e}")
        
        print(f"    ✅ 复制了 {copied_count} 个文件到 {category} 分类")
        total_copied += copied_count
    
    return total_copied

def create_unified_index(target_dir):
    """创建统一索引文件"""
    index_file = target_dir / "unified_index.txt"
    
    with open(index_file, 'w', encoding='utf-8') as f:
        f.write(f"AQFH统一记忆索引\n")
        f.write(f"创建时间: {datetime.now()}\n")
        f.write(f"=" * 50 + "\n\n")
        
        # 统计各分类的文件数量
        for category_dir in target_dir.iterdir():
            if category_dir.is_dir():
                parquet_count = len(list(category_dir.rglob("*.parquet")))
                f.write(f"{category_dir.name}: {parquet_count} 个parquet文件\n")
        
        f.write(f"\n总计: {len(list(target_dir.rglob('*.parquet')))} 个parquet文件\n")
    
    print(f"📋 创建索引文件: {index_file}")

def main():
    """主函数"""
    print("🚀 AQFH简化记忆统一工具")
    print("=" * 50)
    
    # 发现记忆目录
    memory_dirs = discover_memory_directories()
    
    if not memory_dirs:
        print("❌ 未发现任何记忆目录")
        return
    
    print(f"\n📊 发现 {len(memory_dirs)} 个记忆目录")
    total_files = sum(d['parquet_count'] for d in memory_dirs)
    print(f"📊 总计 {total_files} 个parquet文件")
    
    # 确认操作
    response = input(f"\n是否继续统一这些记忆？(y/N): ")
    if response.lower() != 'y':
        print("❌ 操作已取消")
        return
    
    # 执行统一
    target_dir = Path.home() / ".aqfh" / "unified_consciousness" / "consolidated_memories"
    
    print(f"\n🔄 开始统一记忆到: {target_dir}")
    copied_count = copy_parquet_files(memory_dirs, target_dir)
    
    # 创建索引
    create_unified_index(target_dir)
    
    print(f"\n✅ 记忆统一完成！")
    print(f"📊 成功统一 {copied_count} 个parquet文件")
    print(f"📁 统一位置: {target_dir}")

if __name__ == "__main__":
    main()
