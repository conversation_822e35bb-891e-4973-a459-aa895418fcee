#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试结果分析工具

本模块用于分析测试结果并生成报告。
"""

import os
import sys
import json
import argparse
import time
import logging
import matplotlib.pyplot as plt
import numpy as np
from typing import Dict, List, Any, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def load_test_results(file_path: str) -> Dict[str, Any]:
    """加载测试结果
    
    Args:
        file_path: 测试结果文件路径
    
    Returns:
        测试结果数据
    """
    logger.info(f"加载测试结果: {file_path}")
    
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    return data


def analyze_test_results(data: Dict[str, Any]) -> Dict[str, Any]:
    """分析测试结果
    
    Args:
        data: 测试结果数据
    
    Returns:
        分析结果
    """
    logger.info("分析测试结果")
    
    # 获取测试结果
    results = data["results"]
    summary = data["summary"]
    
    # 计算各类测试的成功率
    success_rates = {result["test_type"]: result["success_rate"] for result in results}
    
    # 计算各类测试的持续时间
    durations = {result["test_type"]: result["duration"] for result in results}
    
    # 计算各类测试的错误率
    error_rates = {result["test_type"]: (result["errors"] + result["failures"]) / result["total"] if result["total"] > 0 else 0 for result in results}
    
    # 计算总体成功率
    total_success_rate = summary["total_success"] / summary["total_tests"] if summary["total_tests"] > 0 else 0
    
    # 计算总体错误率
    total_error_rate = (summary["total_failures"] + summary["total_errors"]) / summary["total_tests"] if summary["total_tests"] > 0 else 0
    
    # 返回分析结果
    return {
        "success_rates": success_rates,
        "durations": durations,
        "error_rates": error_rates,
        "total_success_rate": total_success_rate,
        "total_error_rate": total_error_rate
    }


def generate_report(data: Dict[str, Any], analysis: Dict[str, Any], output_dir: str):
    """生成测试报告
    
    Args:
        data: 测试结果数据
        analysis: 分析结果
        output_dir: 输出目录
    """
    logger.info(f"生成测试报告: {output_dir}")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成成功率图表
    generate_success_rate_chart(analysis["success_rates"], os.path.join(output_dir, "success_rates.png"))
    
    # 生成持续时间图表
    generate_duration_chart(analysis["durations"], os.path.join(output_dir, "durations.png"))
    
    # 生成错误率图表
    generate_error_rate_chart(analysis["error_rates"], os.path.join(output_dir, "error_rates.png"))
    
    # 生成总体成功率图表
    generate_total_success_rate_chart(analysis["total_success_rate"], os.path.join(output_dir, "total_success_rate.png"))
    
    # 生成HTML报告
    generate_html_report(data, analysis, os.path.join(output_dir, "report.html"))
    
    logger.info(f"测试报告已生成: {output_dir}")


def generate_success_rate_chart(success_rates: Dict[str, float], output_path: str):
    """生成成功率图表
    
    Args:
        success_rates: 成功率数据
        output_path: 输出路径
    """
    plt.figure(figsize=(10, 6))
    
    # 绘制条形图
    test_types = list(success_rates.keys())
    rates = [success_rates[test_type] * 100 for test_type in test_types]
    
    plt.bar(test_types, rates, color='green')
    
    # 添加标签和标题
    plt.xlabel('测试类型')
    plt.ylabel('成功率 (%)')
    plt.title('各类测试成功率')
    
    # 添加数值标签
    for i, rate in enumerate(rates):
        plt.text(i, rate + 1, f"{rate:.1f}%", ha='center')
    
    # 设置Y轴范围
    plt.ylim(0, 105)
    
    # 旋转X轴标签
    plt.xticks(rotation=45, ha='right')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig(output_path)
    plt.close()


def generate_duration_chart(durations: Dict[str, float], output_path: str):
    """生成持续时间图表
    
    Args:
        durations: 持续时间数据
        output_path: 输出路径
    """
    plt.figure(figsize=(10, 6))
    
    # 绘制条形图
    test_types = list(durations.keys())
    times = [durations[test_type] for test_type in test_types]
    
    plt.bar(test_types, times, color='blue')
    
    # 添加标签和标题
    plt.xlabel('测试类型')
    plt.ylabel('持续时间 (秒)')
    plt.title('各类测试持续时间')
    
    # 添加数值标签
    for i, time in enumerate(times):
        plt.text(i, time + 1, f"{time:.1f}s", ha='center')
    
    # 旋转X轴标签
    plt.xticks(rotation=45, ha='right')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig(output_path)
    plt.close()


def generate_error_rate_chart(error_rates: Dict[str, float], output_path: str):
    """生成错误率图表
    
    Args:
        error_rates: 错误率数据
        output_path: 输出路径
    """
    plt.figure(figsize=(10, 6))
    
    # 绘制条形图
    test_types = list(error_rates.keys())
    rates = [error_rates[test_type] * 100 for test_type in test_types]
    
    plt.bar(test_types, rates, color='red')
    
    # 添加标签和标题
    plt.xlabel('测试类型')
    plt.ylabel('错误率 (%)')
    plt.title('各类测试错误率')
    
    # 添加数值标签
    for i, rate in enumerate(rates):
        plt.text(i, rate + 0.5, f"{rate:.1f}%", ha='center')
    
    # 设置Y轴范围
    plt.ylim(0, max(rates) * 1.2 if rates else 10)
    
    # 旋转X轴标签
    plt.xticks(rotation=45, ha='right')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig(output_path)
    plt.close()


def generate_total_success_rate_chart(total_success_rate: float, output_path: str):
    """生成总体成功率图表
    
    Args:
        total_success_rate: 总体成功率
        output_path: 输出路径
    """
    plt.figure(figsize=(8, 8))
    
    # 绘制饼图
    labels = ['成功', '失败']
    sizes = [total_success_rate * 100, (1 - total_success_rate) * 100]
    colors = ['green', 'red']
    explode = (0.1, 0)  # 突出显示成功部分
    
    plt.pie(sizes, explode=explode, labels=labels, colors=colors, autopct='%1.1f%%', shadow=True, startangle=90)
    
    # 添加标题
    plt.title('总体测试成功率')
    
    # 保存图表
    plt.savefig(output_path)
    plt.close()


def generate_html_report(data: Dict[str, Any], analysis: Dict[str, Any], output_path: str):
    """生成HTML报告
    
    Args:
        data: 测试结果数据
        analysis: 分析结果
        output_path: 输出路径
    """
    # 获取测试结果
    results = data["results"]
    summary = data["summary"]
    
    # 创建HTML报告
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>超融态思维引擎测试报告</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                margin: 20px;
                background-color: #f5f5f5;
            }}
            h1, h2, h3 {{
                color: #333;
            }}
            .container {{
                max-width: 1200px;
                margin: 0 auto;
                background-color: white;
                padding: 20px;
                border-radius: 5px;
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            }}
            .summary {{
                margin-bottom: 20px;
                padding: 15px;
                background-color: #e9f7ef;
                border-radius: 5px;
            }}
            .charts {{
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                margin-bottom: 20px;
            }}
            .chart {{
                width: 48%;
                margin-bottom: 20px;
                background-color: white;
                padding: 10px;
                border-radius: 5px;
                box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
            }}
            table {{
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }}
            th, td {{
                padding: 10px;
                text-align: left;
                border-bottom: 1px solid #ddd;
            }}
            th {{
                background-color: #f2f2f2;
            }}
            tr:hover {{
                background-color: #f5f5f5;
            }}
            .success {{
                color: green;
            }}
            .failure {{
                color: red;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>超融态思维引擎测试报告</h1>
            <p>生成时间: {data["date"]}</p>
            
            <div class="summary">
                <h2>测试摘要</h2>
                <p>总测试数: {summary["total_tests"]}</p>
                <p>成功: <span class="success">{summary["total_success"]}</span></p>
                <p>失败: <span class="failure">{summary["total_failures"]}</span></p>
                <p>错误: <span class="failure">{summary["total_errors"]}</span></p>
                <p>跳过: {summary["total_skipped"]}</p>
                <p>持续时间: {summary["total_duration"]:.2f} 秒</p>
                <p>成功率: <span class="{
                    "success" if analysis["total_success_rate"] >= 0.9 else "failure"
                }">{analysis["total_success_rate"]:.2%}</span></p>
            </div>
            
            <div class="charts">
                <div class="chart">
                    <h3>各类测试成功率</h3>
                    <img src="success_rates.png" alt="成功率图表" style="width: 100%;">
                </div>
                <div class="chart">
                    <h3>各类测试持续时间</h3>
                    <img src="durations.png" alt="持续时间图表" style="width: 100%;">
                </div>
                <div class="chart">
                    <h3>各类测试错误率</h3>
                    <img src="error_rates.png" alt="错误率图表" style="width: 100%;">
                </div>
                <div class="chart">
                    <h3>总体测试成功率</h3>
                    <img src="total_success_rate.png" alt="总体成功率图表" style="width: 100%;">
                </div>
            </div>
            
            <h2>详细测试结果</h2>
            <table>
                <tr>
                    <th>测试类型</th>
                    <th>总测试数</th>
                    <th>成功</th>
                    <th>失败</th>
                    <th>错误</th>
                    <th>跳过</th>
                    <th>持续时间 (秒)</th>
                    <th>成功率</th>
                </tr>
    """
    
    # 添加测试结果
    for result in results:
        html += f"""
                <tr>
                    <td>{result["test_type"]}</td>
                    <td>{result["total"]}</td>
                    <td class="success">{result["success"]}</td>
                    <td class="failure">{result["failures"]}</td>
                    <td class="failure">{result["errors"]}</td>
                    <td>{result["skipped"]}</td>
                    <td>{result["duration"]:.2f}</td>
                    <td class="{
                        "success" if result["success_rate"] >= 0.9 else "failure"
                    }">{result["success_rate"]:.2%}</td>
                </tr>
    """
    
    # 添加总计行
    html += f"""
                <tr>
                    <th>总计</th>
                    <th>{summary["total_tests"]}</th>
                    <th class="success">{summary["total_success"]}</th>
                    <th class="failure">{summary["total_failures"]}</th>
                    <th class="failure">{summary["total_errors"]}</th>
                    <th>{summary["total_skipped"]}</th>
                    <th>{summary["total_duration"]:.2f}</th>
                    <th class="{
                        "success" if analysis["total_success_rate"] >= 0.9 else "failure"
                    }">{analysis["total_success_rate"]:.2%}</th>
                </tr>
            </table>
            
            <h2>分析结论</h2>
            <p>根据测试结果，系统的总体成功率为 {analysis["total_success_rate"]:.2%}，{
                "表现良好。" if analysis["total_success_rate"] >= 0.9 else "需要进一步改进。"
            }</p>
            <p>各类测试中，{
                max(analysis["success_rates"].items(), key=lambda x: x[1])[0]
            } 测试的成功率最高，为 {max(analysis["success_rates"].values()):.2%}；{
                min(analysis["success_rates"].items(), key=lambda x: x[1])[0]
            } 测试的成功率最低，为 {min(analysis["success_rates"].values()):.2%}。</p>
            <p>各类测试中，{
                max(analysis["durations"].items(), key=lambda x: x[1])[0]
            } 测试的持续时间最长，为 {max(analysis["durations"].values()):.2f} 秒；{
                min(analysis["durations"].items(), key=lambda x: x[1])[0]
            } 测试的持续时间最短，为 {min(analysis["durations"].values()):.2f} 秒。</p>
            <p>各类测试中，{
                max(analysis["error_rates"].items(), key=lambda x: x[1])[0]
            } 测试的错误率最高，为 {max(analysis["error_rates"].values()):.2%}；{
                min(analysis["error_rates"].items(), key=lambda x: x[1])[0]
            } 测试的错误率最低，为 {min(analysis["error_rates"].values()):.2%}。</p>
        </div>
    </body>
    </html>
    """
    
    # 保存HTML报告
    with open(output_path, 'w') as f:
        f.write(html)


def main():
    """主函数"""
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='分析测试结果并生成报告')
    parser.add_argument('input', type=str, help='测试结果文件')
    parser.add_argument('--output-dir', type=str, default='test_report', help='输出目录')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 加载测试结果
    data = load_test_results(args.input)
    
    # 分析测试结果
    analysis = analyze_test_results(data)
    
    # 生成报告
    generate_report(data, analysis, args.output_dir)


if __name__ == "__main__":
    main()
