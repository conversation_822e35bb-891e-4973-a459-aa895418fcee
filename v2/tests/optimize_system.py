#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
系统优化工具

本模块用于根据测试结果优化系统。
"""

import os
import sys
import json
import argparse
import logging
import time
import psutil
import numpy as np
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 导入系统模块
from src.core.morphism import MorphismSystemFactory
from src.core.metacognition import MetacognitionFactory
from src.core.distributed import DistributedSystemFactory

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def load_test_results(file_path: str) -> Dict[str, Any]:
    """加载测试结果
    
    Args:
        file_path: 测试结果文件路径
    
    Returns:
        测试结果数据
    """
    logger.info(f"加载测试结果: {file_path}")
    
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    return data


def analyze_performance_bottlenecks(data: Dict[str, Any]) -> Dict[str, Any]:
    """分析性能瓶颈
    
    Args:
        data: 测试结果数据
    
    Returns:
        性能瓶颈分析结果
    """
    logger.info("分析性能瓶颈")
    
    # 获取测试结果
    results = data["results"]
    
    # 查找性能测试结果
    performance_results = [result for result in results if result["test_type"] == "性能"]
    
    # 如果没有性能测试结果，返回空结果
    if not performance_results:
        logger.warning("没有找到性能测试结果")
        return {}
    
    # 获取性能测试结果
    performance_result = performance_results[0]
    
    # 分析性能瓶颈
    bottlenecks = {}
    
    # 如果性能测试成功率低于90%，认为存在性能瓶颈
    if performance_result["success_rate"] < 0.9:
        bottlenecks["overall"] = {
            "severity": "high",
            "description": "整体性能测试成功率低于90%",
            "success_rate": performance_result["success_rate"]
        }
    
    # 如果性能测试持续时间过长，认为存在性能瓶颈
    if performance_result["duration"] > 300:  # 5分钟
        bottlenecks["duration"] = {
            "severity": "medium",
            "description": "性能测试持续时间过长",
            "duration": performance_result["duration"]
        }
    
    # 如果性能测试错误率高于10%，认为存在性能瓶颈
    error_rate = (performance_result["failures"] + performance_result["errors"]) / performance_result["total"] if performance_result["total"] > 0 else 0
    if error_rate > 0.1:
        bottlenecks["errors"] = {
            "severity": "high",
            "description": "性能测试错误率高于10%",
            "error_rate": error_rate
        }
    
    return bottlenecks


def analyze_stability_issues(data: Dict[str, Any]) -> Dict[str, Any]:
    """分析稳定性问题
    
    Args:
        data: 测试结果数据
    
    Returns:
        稳定性问题分析结果
    """
    logger.info("分析稳定性问题")
    
    # 获取测试结果
    results = data["results"]
    
    # 查找稳定性测试结果
    stability_results = [result for result in results if result["test_type"] == "稳定性"]
    
    # 如果没有稳定性测试结果，返回空结果
    if not stability_results:
        logger.warning("没有找到稳定性测试结果")
        return {}
    
    # 获取稳定性测试结果
    stability_result = stability_results[0]
    
    # 分析稳定性问题
    issues = {}
    
    # 如果稳定性测试成功率低于90%，认为存在稳定性问题
    if stability_result["success_rate"] < 0.9:
        issues["overall"] = {
            "severity": "high",
            "description": "整体稳定性测试成功率低于90%",
            "success_rate": stability_result["success_rate"]
        }
    
    # 如果稳定性测试错误率高于10%，认为存在稳定性问题
    error_rate = (stability_result["failures"] + stability_result["errors"]) / stability_result["total"] if stability_result["total"] > 0 else 0
    if error_rate > 0.1:
        issues["errors"] = {
            "severity": "high",
            "description": "稳定性测试错误率高于10%",
            "error_rate": error_rate
        }
    
    return issues


def optimize_morphism_system():
    """优化态射系统"""
    logger.info("优化态射系统")
    
    # 创建态射系统工厂
    morphism_factory = MorphismSystemFactory()
    
    # 获取态射系统优化器
    optimizer = morphism_factory.get_optimizer()
    
    # 执行优化
    optimization_result = optimizer.optimize()
    
    # 输出优化结果
    logger.info(f"态射系统优化结果: {optimization_result}")
    
    return optimization_result


def optimize_metacognition_system():
    """优化元认知系统"""
    logger.info("优化元认知系统")
    
    # 创建元认知系统工厂
    metacognition_factory = MetacognitionFactory()
    
    # 获取元认知系统优化器
    optimizer = metacognition_factory.get_optimizer()
    
    # 执行优化
    optimization_result = optimizer.optimize()
    
    # 输出优化结果
    logger.info(f"元认知系统优化结果: {optimization_result}")
    
    return optimization_result


def optimize_distributed_system():
    """优化分布式系统"""
    logger.info("优化分布式系统")
    
    # 创建分布式系统工厂
    distributed_factory = DistributedSystemFactory()
    
    # 获取分布式系统优化器
    optimizer = distributed_factory.get_optimizer()
    
    # 执行优化
    optimization_result = optimizer.optimize()
    
    # 输出优化结果
    logger.info(f"分布式系统优化结果: {optimization_result}")
    
    return optimization_result


def optimize_system_configuration():
    """优化系统配置"""
    logger.info("优化系统配置")
    
    # 获取系统资源信息
    cpu_count = psutil.cpu_count()
    memory_info = psutil.virtual_memory()
    
    # 计算最佳线程数
    optimal_thread_count = max(1, cpu_count - 1)  # 保留一个CPU核心给系统
    
    # 计算最佳内存使用量
    available_memory = memory_info.available
    optimal_memory_usage = int(available_memory * 0.8)  # 使用80%的可用内存
    
    # 创建优化配置
    config = {
        "thread_pool_size": optimal_thread_count,
        "max_memory_usage": optimal_memory_usage,
        "gc_threshold": 1000,  # 垃圾回收阈值
        "cache_size": min(1024 * 1024 * 100, int(available_memory * 0.1))  # 缓存大小，最大100MB
    }
    
    # 保存优化配置
    config_path = os.path.join(os.path.dirname(__file__), '../../config/optimized_config.json')
    os.makedirs(os.path.dirname(config_path), exist_ok=True)
    
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    logger.info(f"系统配置已优化并保存到: {config_path}")
    
    return config


def generate_optimization_report(bottlenecks: Dict[str, Any], issues: Dict[str, Any], optimization_results: Dict[str, Any], output_path: str):
    """生成优化报告
    
    Args:
        bottlenecks: 性能瓶颈分析结果
        issues: 稳定性问题分析结果
        optimization_results: 优化结果
        output_path: 输出路径
    """
    logger.info(f"生成优化报告: {output_path}")
    
    # 创建HTML报告
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>超融态思维引擎优化报告</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                margin: 20px;
                background-color: #f5f5f5;
            }}
            h1, h2, h3 {{
                color: #333;
            }}
            .container {{
                max-width: 1200px;
                margin: 0 auto;
                background-color: white;
                padding: 20px;
                border-radius: 5px;
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            }}
            .section {{
                margin-bottom: 20px;
                padding: 15px;
                background-color: #f9f9f9;
                border-radius: 5px;
            }}
            table {{
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }}
            th, td {{
                padding: 10px;
                text-align: left;
                border-bottom: 1px solid #ddd;
            }}
            th {{
                background-color: #f2f2f2;
            }}
            tr:hover {{
                background-color: #f5f5f5;
            }}
            .high {{
                color: red;
            }}
            .medium {{
                color: orange;
            }}
            .low {{
                color: green;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>超融态思维引擎优化报告</h1>
            <p>生成时间: {time.strftime("%Y-%m-%d %H:%M:%S")}</p>
            
            <div class="section">
                <h2>性能瓶颈分析</h2>
    """
    
    if bottlenecks:
        html += """
                <table>
                    <tr>
                        <th>瓶颈</th>
                        <th>严重程度</th>
                        <th>描述</th>
                        <th>详情</th>
                    </tr>
        """
        
        for bottleneck_id, bottleneck in bottlenecks.items():
            html += f"""
                    <tr>
                        <td>{bottleneck_id}</td>
                        <td class="{bottleneck['severity']}">{bottleneck['severity']}</td>
                        <td>{bottleneck['description']}</td>
                        <td>{
                            f"成功率: {bottleneck['success_rate']:.2%}" if 'success_rate' in bottleneck else
                            f"持续时间: {bottleneck['duration']:.2f} 秒" if 'duration' in bottleneck else
                            f"错误率: {bottleneck['error_rate']:.2%}" if 'error_rate' in bottleneck else
                            ""
                        }</td>
                    </tr>
            """
        
        html += """
                </table>
        """
    else:
        html += """
                <p>未发现性能瓶颈。</p>
        """
    
    html += """
            </div>
            
            <div class="section">
                <h2>稳定性问题分析</h2>
    """
    
    if issues:
        html += """
                <table>
                    <tr>
                        <th>问题</th>
                        <th>严重程度</th>
                        <th>描述</th>
                        <th>详情</th>
                    </tr>
        """
        
        for issue_id, issue in issues.items():
            html += f"""
                    <tr>
                        <td>{issue_id}</td>
                        <td class="{issue['severity']}">{issue['severity']}</td>
                        <td>{issue['description']}</td>
                        <td>{
                            f"成功率: {issue['success_rate']:.2%}" if 'success_rate' in issue else
                            f"错误率: {issue['error_rate']:.2%}" if 'error_rate' in issue else
                            ""
                        }</td>
                    </tr>
            """
        
        html += """
                </table>
        """
    else:
        html += """
                <p>未发现稳定性问题。</p>
        """
    
    html += """
            </div>
            
            <div class="section">
                <h2>优化结果</h2>
    """
    
    if optimization_results:
        html += """
                <table>
                    <tr>
                        <th>系统</th>
                        <th>优化结果</th>
                    </tr>
        """
        
        for system, result in optimization_results.items():
            html += f"""
                    <tr>
                        <td>{system}</td>
                        <td>{result}</td>
                    </tr>
            """
        
        html += """
                </table>
        """
    else:
        html += """
                <p>未执行优化。</p>
        """
    
    html += """
            </div>
            
            <div class="section">
                <h2>优化建议</h2>
                <ul>
    """
    
    if bottlenecks:
        for bottleneck_id, bottleneck in bottlenecks.items():
            if bottleneck['severity'] == 'high':
                html += f"""
                    <li class="high">优先解决 {bottleneck_id} 瓶颈: {bottleneck['description']}</li>
                """
            elif bottleneck['severity'] == 'medium':
                html += f"""
                    <li class="medium">考虑解决 {bottleneck_id} 瓶颈: {bottleneck['description']}</li>
                """
    
    if issues:
        for issue_id, issue in issues.items():
            if issue['severity'] == 'high':
                html += f"""
                    <li class="high">优先解决 {issue_id} 问题: {issue['description']}</li>
                """
            elif issue['severity'] == 'medium':
                html += f"""
                    <li class="medium">考虑解决 {issue_id} 问题: {issue['description']}</li>
                """
    
    if not bottlenecks and not issues:
        html += """
                    <li class="low">系统性能和稳定性良好，无需进一步优化。</li>
        """
    
    html += """
                </ul>
            </div>
        </div>
    </body>
    </html>
    """
    
    # 保存HTML报告
    with open(output_path, 'w') as f:
        f.write(html)


def main():
    """主函数"""
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='根据测试结果优化系统')
    parser.add_argument('--test-results', type=str, help='测试结果文件')
    parser.add_argument('--output', type=str, default='optimization_report.html', help='优化报告输出路径')
    parser.add_argument('--optimize-morphism', action='store_true', help='优化态射系统')
    parser.add_argument('--optimize-metacognition', action='store_true', help='优化元认知系统')
    parser.add_argument('--optimize-distributed', action='store_true', help='优化分布式系统')
    parser.add_argument('--optimize-config', action='store_true', help='优化系统配置')
    parser.add_argument('--all', action='store_true', help='执行所有优化')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 如果没有指定优化类型，默认执行所有优化
    if not (args.optimize_morphism or args.optimize_metacognition or args.optimize_distributed or args.optimize_config):
        args.all = True
    
    # 如果指定了执行所有优化，设置所有优化类型为True
    if args.all:
        args.optimize_morphism = True
        args.optimize_metacognition = True
        args.optimize_distributed = True
        args.optimize_config = True
    
    # 初始化性能瓶颈和稳定性问题
    bottlenecks = {}
    issues = {}
    
    # 如果指定了测试结果文件，加载测试结果并分析
    if args.test_results:
        # 加载测试结果
        data = load_test_results(args.test_results)
        
        # 分析性能瓶颈
        bottlenecks = analyze_performance_bottlenecks(data)
        
        # 分析稳定性问题
        issues = analyze_stability_issues(data)
    
    # 执行优化
    optimization_results = {}
    
    # 优化态射系统
    if args.optimize_morphism:
        try:
            optimization_results["态射系统"] = optimize_morphism_system()
        except Exception as e:
            logger.error(f"优化态射系统时发生错误: {e}")
            optimization_results["态射系统"] = f"优化失败: {e}"
    
    # 优化元认知系统
    if args.optimize_metacognition:
        try:
            optimization_results["元认知系统"] = optimize_metacognition_system()
        except Exception as e:
            logger.error(f"优化元认知系统时发生错误: {e}")
            optimization_results["元认知系统"] = f"优化失败: {e}"
    
    # 优化分布式系统
    if args.optimize_distributed:
        try:
            optimization_results["分布式系统"] = optimize_distributed_system()
        except Exception as e:
            logger.error(f"优化分布式系统时发生错误: {e}")
            optimization_results["分布式系统"] = f"优化失败: {e}"
    
    # 优化系统配置
    if args.optimize_config:
        try:
            optimization_results["系统配置"] = optimize_system_configuration()
        except Exception as e:
            logger.error(f"优化系统配置时发生错误: {e}")
            optimization_results["系统配置"] = f"优化失败: {e}"
    
    # 生成优化报告
    generate_optimization_report(bottlenecks, issues, optimization_results, args.output)
    
    logger.info(f"优化报告已生成: {args.output}")


if __name__ == "__main__":
    main()
