#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
持续负载稳定性测试

本模块测试系统在持续负载下的稳定性。
"""

import os
import sys
import unittest
import numpy as np
import logging
import time
import threading
import queue
import random
from typing import Any, Dict, List, Optional, Tuple, Union

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

# 导入测试基类
from v2.tests.integration.stability.test_base import BaseStabilityTest

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ContinuousLoadTest(BaseStabilityTest):
    """持续负载稳定性测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        super().setUp()
        
        # 创建任务队列
        self.task_queue = queue.Queue()
        
        # 创建结果队列
        self.result_queue = queue.Queue()
        
        # 创建错误队列
        self.error_queue = queue.Queue()
        
        # 创建工作线程
        self.workers = []
        self.stop_workers = False
        
        # 创建统计数据
        self.stats = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "task_types": {},
            "execution_times": [],
            "start_time": time.time()
        }
    
    def tearDown(self):
        """测试后的清理工作"""
        # 停止工作线程
        self.stop_workers = True
        
        # 等待工作线程结束
        for worker in self.workers:
            if worker.is_alive():
                worker.join(timeout=1.0)
        
        # 计算统计数据
        self.stats["end_time"] = time.time()
        self.stats["duration"] = self.stats["end_time"] - self.stats["start_time"]
        
        if self.stats["execution_times"]:
            self.stats["avg_execution_time"] = sum(self.stats["execution_times"]) / len(self.stats["execution_times"])
            self.stats["min_execution_time"] = min(self.stats["execution_times"])
            self.stats["max_execution_time"] = max(self.stats["execution_times"])
        else:
            self.stats["avg_execution_time"] = 0
            self.stats["min_execution_time"] = 0
            self.stats["max_execution_time"] = 0
        
        # 输出统计数据
        logger.info(f"持续负载测试统计:")
        logger.info(f"  总任务数: {self.stats['total_tasks']}")
        logger.info(f"  完成任务数: {self.stats['completed_tasks']}")
        logger.info(f"  失败任务数: {self.stats['failed_tasks']}")
        logger.info(f"  任务类型分布: {self.stats['task_types']}")
        logger.info(f"  平均执行时间: {self.stats['avg_execution_time']:.6f} 秒")
        logger.info(f"  最小执行时间: {self.stats['min_execution_time']:.6f} 秒")
        logger.info(f"  最大执行时间: {self.stats['max_execution_time']:.6f} 秒")
        logger.info(f"  测试持续时间: {self.stats['duration']:.2f} 秒")
        
        # 调用父类的tearDown方法
        super().tearDown()
    
    def worker_function(self, worker_id: int):
        """工作线程函数"""
        logger.info(f"工作线程 {worker_id} 启动")
        
        while not self.stop_workers:
            try:
                # 从任务队列获取任务，如果队列为空，等待1秒
                try:
                    task = self.task_queue.get(timeout=1.0)
                except queue.Empty:
                    continue
                
                # 执行任务
                logger.debug(f"工作线程 {worker_id} 执行任务: {task['task_id']}")
                
                start_time = time.time()
                
                try:
                    # 根据任务类型执行不同的操作
                    if task["task_type"] == "morphism_execution":
                        result = self.execute_morphism_task(task)
                    elif task["task_type"] == "metacognition_task":
                        result = self.execute_metacognition_task(task)
                    elif task["task_type"] == "distributed_task":
                        result = self.execute_distributed_task(task)
                    else:
                        raise ValueError(f"未知任务类型: {task['task_type']}")
                    
                    # 计算执行时间
                    execution_time = time.time() - start_time
                    
                    # 将结果放入结果队列
                    self.result_queue.put({
                        "task_id": task["task_id"],
                        "task_type": task["task_type"],
                        "result": result,
                        "execution_time": execution_time,
                        "worker_id": worker_id
                    })
                    
                    # 更新统计数据
                    self.stats["completed_tasks"] += 1
                    self.stats["execution_times"].append(execution_time)
                    
                except Exception as e:
                    # 将错误放入错误队列
                    self.error_queue.put({
                        "task_id": task["task_id"],
                        "task_type": task["task_type"],
                        "error": str(e),
                        "error_type": type(e).__name__,
                        "worker_id": worker_id
                    })
                    
                    # 更新统计数据
                    self.stats["failed_tasks"] += 1
                    
                    logger.error(f"工作线程 {worker_id} 执行任务 {task['task_id']} 出错: {e}")
                
                finally:
                    # 标记任务完成
                    self.task_queue.task_done()
            
            except Exception as e:
                logger.error(f"工作线程 {worker_id} 发生异常: {e}")
        
        logger.info(f"工作线程 {worker_id} 结束")
    
    def execute_morphism_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """执行态射任务"""
        # 获取任务数据
        morphism_id = task["data"]["morphism_id"]
        input_data = np.array(task["data"]["input_data"])
        
        # 获取态射
        if morphism_id == "linear_morphism":
            morphism = self.linear_morphism
        elif morphism_id == "nonlinear_morphism":
            morphism = self.nonlinear_morphism
        else:
            raise ValueError(f"未知态射ID: {morphism_id}")
        
        # 执行态射
        output_data = morphism.apply(input_data)
        
        # 返回结果
        return {
            "output_data": output_data.tolist(),
            "status": "success"
        }
    
    def execute_metacognition_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """执行元认知任务"""
        # 获取任务数据
        metacognitive_process = task["data"]["metacognitive_process"]
        data = task["data"].get("data", {})
        
        # 创建认知状态
        cognitive_state = self.metacognition_manager.create_cognitive_state(
            cognitive_level=CognitiveLevel.PERCEPTION,
            data=data,
            metadata={"description": f"任务 {task['task_id']} 的认知状态"}
        )
        
        # 创建元认知状态
        metacognitive_state = self.metacognition_manager.create_metacognitive_state(
            metacognitive_process=MetacognitiveProcess[metacognitive_process],
            cognitive_states={cognitive_state.state_id: cognitive_state},
            data=data,
            metadata={"description": f"任务 {task['task_id']} 的元认知状态"}
        )
        
        # 返回结果
        return {
            "cognitive_state_id": cognitive_state.state_id,
            "metacognitive_state_id": metacognitive_state.state_id,
            "status": "success"
        }
    
    def execute_distributed_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """执行分布式任务"""
        # 获取任务数据
        distributed_task_type = task["data"]["distributed_task_type"]
        
        if distributed_task_type == "message_passing":
            # 创建消息
            message = self.distributed_factory.create_message(
                message_id=f"message_{task['task_id']}",
                source_node_id="master_node",
                target_node_id=task["data"]["target_node_id"],
                message_type=MessageType.TASK_ASSIGNMENT,
                content={"task_id": task["task_id"]},
                metadata={"description": f"任务 {task['task_id']} 的消息"}
            )
            
            # 发送消息
            self.distributed_manager.send_message(message)
            
            # 返回结果
            return {
                "message_id": message.message_id,
                "status": "success"
            }
        
        elif distributed_task_type == "task_scheduling":
            # 创建分布式任务
            distributed_task = self.distributed_factory.create_task(
                task_id=f"distributed_task_{task['task_id']}",
                task_type="test_task",
                source_node_id="master_node",
                target_node_id=None,  # 由调度器分配
                priority=task["data"].get("priority", 5),
                data={"original_task_id": task["task_id"]},
                metadata={"description": f"任务 {task['task_id']} 的分布式任务"}
            )
            
            # 提交任务
            self.task_scheduler.submit_task(distributed_task)
            
            # 分配任务
            target_node_id = random.choice(list(self.nodes.keys()))
            self.task_scheduler.assign_task(distributed_task.task_id, target_node_id)
            
            # 完成任务
            self.task_scheduler.complete_task(
                task_id=distributed_task.task_id,
                result={"status": "success"}
            )
            
            # 返回结果
            return {
                "distributed_task_id": distributed_task.task_id,
                "target_node_id": target_node_id,
                "status": "success"
            }
        
        else:
            raise ValueError(f"未知分布式任务类型: {distributed_task_type}")
    
    def generate_random_task(self) -> Dict[str, Any]:
        """生成随机任务"""
        # 生成任务ID
        task_id = f"task_{time.time()}_{random.randint(1000, 9999)}"
        
        # 随机选择任务类型
        task_types = ["morphism_execution", "metacognition_task", "distributed_task"]
        task_type = random.choice(task_types)
        
        # 更新统计数据
        self.stats["total_tasks"] += 1
        self.stats["task_types"][task_type] = self.stats["task_types"].get(task_type, 0) + 1
        
        # 根据任务类型生成任务数据
        if task_type == "morphism_execution":
            # 随机选择态射
            morphism_ids = ["linear_morphism", "nonlinear_morphism"]
            morphism_id = random.choice(morphism_ids)
            
            # 生成随机输入数据
            input_data = np.random.rand(2).tolist()
            
            # 创建任务数据
            task_data = {
                "morphism_id": morphism_id,
                "input_data": input_data
            }
        
        elif task_type == "metacognition_task":
            # 随机选择元认知过程
            metacognitive_processes = ["MONITORING", "CONTROL", "EVALUATION", "LEARNING", "PREDICTION"]
            metacognitive_process = random.choice(metacognitive_processes)
            
            # 生成随机数据
            data = {
                "value": random.random(),
                "timestamp": time.time()
            }
            
            # 创建任务数据
            task_data = {
                "metacognitive_process": metacognitive_process,
                "data": data
            }
        
        elif task_type == "distributed_task":
            # 随机选择分布式任务类型
            distributed_task_types = ["message_passing", "task_scheduling"]
            distributed_task_type = random.choice(distributed_task_types)
            
            # 随机选择目标节点
            target_node_id = random.choice(list(self.nodes.keys()))
            
            # 创建任务数据
            task_data = {
                "distributed_task_type": distributed_task_type,
                "target_node_id": target_node_id,
                "priority": random.randint(1, 10)
            }
        
        else:
            raise ValueError(f"未知任务类型: {task_type}")
        
        # 创建任务
        task = {
            "task_id": task_id,
            "task_type": task_type,
            "data": task_data,
            "timestamp": time.time()
        }
        
        return task
    
    def test_continuous_load(self):
        """测试持续负载"""
        logger.info("开始持续负载测试")
        
        # 测试参数
        test_duration = 60  # 测试持续时间（秒）
        worker_count = 4    # 工作线程数量
        task_rate = 10      # 每秒生成的任务数
        
        # 创建工作线程
        for i in range(worker_count):
            worker = threading.Thread(target=self.worker_function, args=(i,))
            worker.daemon = True
            worker.start()
            self.workers.append(worker)
        
        # 记录开始时间
        start_time = time.time()
        
        # 持续生成任务
        task_count = 0
        next_task_time = start_time
        
        try:
            while time.time() - start_time < test_duration:
                # 检查是否应该生成新任务
                current_time = time.time()
                if current_time >= next_task_time:
                    # 生成随机任务
                    task = self.generate_random_task()
                    
                    # 将任务放入队列
                    self.task_queue.put(task)
                    
                    # 更新任务计数
                    task_count += 1
                    
                    # 计算下一个任务的时间
                    next_task_time = start_time + task_count / task_rate
                
                # 检查错误队列
                while not self.error_queue.empty():
                    error = self.error_queue.get_nowait()
                    logger.error(f"任务 {error['task_id']} 执行出错: {error['error']}")
                
                # 短暂休眠，避免CPU占用过高
                time.sleep(0.01)
        
        except KeyboardInterrupt:
            logger.info("测试被用户中断")
        
        # 等待所有任务完成
        logger.info("等待所有任务完成...")
        self.task_queue.join()
        
        # 计算测试结果
        end_time = time.time()
        test_duration_actual = end_time - start_time
        
        # 获取结果
        results = []
        while not self.result_queue.empty():
            results.append(self.result_queue.get_nowait())
        
        # 获取错误
        errors = []
        while not self.error_queue.empty():
            errors.append(self.error_queue.get_nowait())
        
        # 输出测试结果
        logger.info(f"持续负载测试完成")
        logger.info(f"  实际测试持续时间: {test_duration_actual:.2f} 秒")
        logger.info(f"  生成任务数: {task_count}")
        logger.info(f"  完成任务数: {len(results)}")
        logger.info(f"  错误任务数: {len(errors)}")
        
        # 验证测试结果
        self.assertGreaterEqual(len(results), task_count * 0.9, "至少90%的任务应该成功完成")
        self.assertLessEqual(len(errors), task_count * 0.1, "错误任务数不应超过10%")
    
    def test_burst_load(self):
        """测试突发负载"""
        logger.info("开始突发负载测试")
        
        # 测试参数
        worker_count = 4     # 工作线程数量
        burst_size = 100     # 突发任务数量
        burst_count = 5      # 突发次数
        burst_interval = 5   # 突发间隔（秒）
        
        # 创建工作线程
        for i in range(worker_count):
            worker = threading.Thread(target=self.worker_function, args=(i,))
            worker.daemon = True
            worker.start()
            self.workers.append(worker)
        
        # 记录开始时间
        start_time = time.time()
        
        # 执行突发负载测试
        for burst in range(burst_count):
            logger.info(f"执行突发负载 {burst+1}/{burst_count}")
            
            # 生成突发任务
            for _ in range(burst_size):
                task = self.generate_random_task()
                self.task_queue.put(task)
            
            # 等待一段时间
            time.sleep(burst_interval)
            
            # 检查错误队列
            while not self.error_queue.empty():
                error = self.error_queue.get_nowait()
                logger.error(f"任务 {error['task_id']} 执行出错: {error['error']}")
        
        # 等待所有任务完成
        logger.info("等待所有任务完成...")
        self.task_queue.join()
        
        # 计算测试结果
        end_time = time.time()
        test_duration = end_time - start_time
        
        # 获取结果
        results = []
        while not self.result_queue.empty():
            results.append(self.result_queue.get_nowait())
        
        # 获取错误
        errors = []
        while not self.error_queue.empty():
            errors.append(self.error_queue.get_nowait())
        
        # 输出测试结果
        logger.info(f"突发负载测试完成")
        logger.info(f"  测试持续时间: {test_duration:.2f} 秒")
        logger.info(f"  总任务数: {burst_size * burst_count}")
        logger.info(f"  完成任务数: {len(results)}")
        logger.info(f"  错误任务数: {len(errors)}")
        
        # 验证测试结果
        self.assertGreaterEqual(len(results), burst_size * burst_count * 0.9, "至少90%的任务应该成功完成")
        self.assertLessEqual(len(errors), burst_size * burst_count * 0.1, "错误任务数不应超过10%")
    
    def test_mixed_load(self):
        """测试混合负载"""
        logger.info("开始混合负载测试")
        
        # 测试参数
        test_duration = 60   # 测试持续时间（秒）
        worker_count = 4     # 工作线程数量
        base_task_rate = 5   # 基础每秒任务数
        burst_size = 50      # 突发任务数量
        burst_interval = 10  # 突发间隔（秒）
        
        # 创建工作线程
        for i in range(worker_count):
            worker = threading.Thread(target=self.worker_function, args=(i,))
            worker.daemon = True
            worker.start()
            self.workers.append(worker)
        
        # 记录开始时间
        start_time = time.time()
        
        # 持续生成任务
        task_count = 0
        next_task_time = start_time
        next_burst_time = start_time + burst_interval
        
        try:
            while time.time() - start_time < test_duration:
                current_time = time.time()
                
                # 检查是否应该生成突发任务
                if current_time >= next_burst_time:
                    logger.info(f"生成突发任务: {burst_size} 个")
                    
                    # 生成突发任务
                    for _ in range(burst_size):
                        task = self.generate_random_task()
                        self.task_queue.put(task)
                        task_count += 1
                    
                    # 计算下一次突发时间
                    next_burst_time = current_time + burst_interval
                
                # 检查是否应该生成基础任务
                if current_time >= next_task_time:
                    # 生成随机任务
                    task = self.generate_random_task()
                    
                    # 将任务放入队列
                    self.task_queue.put(task)
                    
                    # 更新任务计数
                    task_count += 1
                    
                    # 计算下一个任务的时间
                    next_task_time = start_time + task_count / base_task_rate
                
                # 检查错误队列
                while not self.error_queue.empty():
                    error = self.error_queue.get_nowait()
                    logger.error(f"任务 {error['task_id']} 执行出错: {error['error']}")
                
                # 短暂休眠，避免CPU占用过高
                time.sleep(0.01)
        
        except KeyboardInterrupt:
            logger.info("测试被用户中断")
        
        # 等待所有任务完成
        logger.info("等待所有任务完成...")
        self.task_queue.join()
        
        # 计算测试结果
        end_time = time.time()
        test_duration_actual = end_time - start_time
        
        # 获取结果
        results = []
        while not self.result_queue.empty():
            results.append(self.result_queue.get_nowait())
        
        # 获取错误
        errors = []
        while not self.error_queue.empty():
            errors.append(self.error_queue.get_nowait())
        
        # 输出测试结果
        logger.info(f"混合负载测试完成")
        logger.info(f"  实际测试持续时间: {test_duration_actual:.2f} 秒")
        logger.info(f"  生成任务数: {task_count}")
        logger.info(f"  完成任务数: {len(results)}")
        logger.info(f"  错误任务数: {len(errors)}")
        
        # 验证测试结果
        self.assertGreaterEqual(len(results), task_count * 0.9, "至少90%的任务应该成功完成")
        self.assertLessEqual(len(errors), task_count * 0.1, "错误任务数不应超过10%")


if __name__ == "__main__":
    unittest.main()
