#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
长时间运行稳定性测试运行器

本模块用于运行长时间运行稳定性的所有测试。
"""

import os
import sys
import unittest
import logging
import argparse

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

# 导入测试模块
from v2.tests.integration.stability.test_continuous_load import ContinuousLoadTest
from v2.tests.integration.stability.test_long_running import LongRunningTest

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_tests(test_type=None, duration_factor=1.0):
    """运行测试
    
    Args:
        test_type: 要运行的测试类型，可以是 'continuous_load', 'long_running' 或 None（运行所有测试）
        duration_factor: 测试持续时间的倍数，用于调整测试时间
    """
    logger.info("运行长时间运行稳定性测试")
    logger.info(f"测试类型: {test_type if test_type else '所有'}")
    logger.info(f"持续时间因子: {duration_factor}")
    
    # 调整测试持续时间
    if duration_factor != 1.0:
        # 修改 ContinuousLoadTest 中的测试持续时间
        if hasattr(ContinuousLoadTest, 'test_continuous_load'):
            original_method = ContinuousLoadTest.test_continuous_load
            def modified_method(self):
                # 保存原始测试持续时间
                original_duration = 60  # 默认值
                # 查找并修改测试持续时间
                for line in original_method.__code__.co_consts:
                    if isinstance(line, str) and "test_duration" in line:
                        try:
                            # 提取测试持续时间
                            import re
                            match = re.search(r'test_duration\s*=\s*(\d+)', line)
                            if match:
                                original_duration = int(match.group(1))
                                break
                        except:
                            pass
                
                # 修改测试持续时间
                test_duration = int(original_duration * duration_factor)
                logger.info(f"调整 test_continuous_load 测试持续时间: {original_duration} -> {test_duration}")
                
                # 替换测试方法中的测试持续时间
                import inspect
                source = inspect.getsource(original_method)
                modified_source = source.replace(f"test_duration = {original_duration}", f"test_duration = {test_duration}")
                
                # 执行修改后的方法
                exec(modified_source, globals(), {'self': self})
            
            # 替换测试方法
            ContinuousLoadTest.test_continuous_load = modified_method
        
        # 修改 LongRunningTest 中的测试持续时间
        if hasattr(LongRunningTest, 'test_long_running_stability'):
            original_method = LongRunningTest.test_long_running_stability
            def modified_method(self):
                # 保存原始测试持续时间
                original_duration = 300  # 默认值
                # 查找并修改测试持续时间
                for line in original_method.__code__.co_consts:
                    if isinstance(line, str) and "test_duration" in line:
                        try:
                            # 提取测试持续时间
                            import re
                            match = re.search(r'test_duration\s*=\s*(\d+)', line)
                            if match:
                                original_duration = int(match.group(1))
                                break
                        except:
                            pass
                
                # 修改测试持续时间
                test_duration = int(original_duration * duration_factor)
                logger.info(f"调整 test_long_running_stability 测试持续时间: {original_duration} -> {test_duration}")
                
                # 替换测试方法中的测试持续时间
                import inspect
                source = inspect.getsource(original_method)
                modified_source = source.replace(f"test_duration = {original_duration}", f"test_duration = {test_duration}")
                
                # 执行修改后的方法
                exec(modified_source, globals(), {'self': self})
            
            # 替换测试方法
            LongRunningTest.test_long_running_stability = modified_method
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 根据测试类型添加测试用例
    if test_type is None or test_type == 'continuous_load':
        test_suite.addTest(unittest.makeSuite(ContinuousLoadTest))
    
    if test_type is None or test_type == 'long_running':
        test_suite.addTest(unittest.makeSuite(LongRunningTest))
    
    # 创建测试运行器
    test_runner = unittest.TextTestRunner(verbosity=2)
    
    # 运行测试
    test_result = test_runner.run(test_suite)
    
    # 输出测试结果
    logger.info(f"测试结果: 运行 {test_result.testsRun} 个测试")
    logger.info(f"成功: {test_result.testsRun - len(test_result.errors) - len(test_result.failures)}")
    logger.info(f"失败: {len(test_result.failures)}")
    logger.info(f"错误: {len(test_result.errors)}")
    
    # 返回测试结果
    return test_result.wasSuccessful()


if __name__ == "__main__":
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='运行长时间运行稳定性测试')
    parser.add_argument('--type', choices=['continuous_load', 'long_running'], help='要运行的测试类型')
    parser.add_argument('--duration-factor', type=float, default=1.0, help='测试持续时间的倍数')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 运行测试
    success = run_tests(test_type=args.type, duration_factor=args.duration_factor)
    
    # 设置退出码
    sys.exit(0 if success else 1)
