#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
长时间运行稳定性测试基类

本模块提供了长时间运行稳定性测试的基础类，包含共享的设置和工具方法。
"""

import os
import sys
import unittest
import numpy as np
import logging
import time
import psutil
import threading
import multiprocessing
import gc
import signal
from typing import Any, Dict, List, Optional, Tuple, Union, Callable

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入态射系统模块
from src.core.morphism import (
    MorphismSystemFactory, Category, Object, Morphism, MorphismType,
    FeedbackFactory, FeedbackManager, 
    EvolutionFactory, EvolutionManager
)

# 导入元认知系统模块
from src.core.metacognition import (
    MetacognitionFactory, MetacognitionManager,
    CognitiveLevel, MetacognitiveProcess,
    CognitiveState, MetacognitiveState
)

# 导入分布式系统模块
from src.core.distributed import (
    DistributedSystemFactory, DistributedManager,
    Node, NodeType, Message, MessageType,
    NetworkTopology, CommunicationProtocol,
    TaskScheduler, Task, TaskStatus
)


class BaseStabilityTest(unittest.TestCase):
    """长时间运行稳定性测试基类"""
    
    def setUp(self):
        """测试前的准备工作"""
        logger.info("设置长时间运行稳定性测试环境")
        
        # 记录开始时间
        self.start_time = time.time()
        
        # 记录初始资源使用情况
        self.initial_resources = self.get_system_resources()
        logger.info(f"初始系统资源使用情况: {self.initial_resources}")
        
        # 创建态射系统工厂
        self.morphism_factory = MorphismSystemFactory()
        
        # 创建反馈工厂和管理器
        self.feedback_factory = FeedbackFactory()
        self.feedback_manager = FeedbackManager(manager_id="feedback_manager")
        
        # 创建演化工厂和管理器
        self.evolution_factory = EvolutionFactory()
        self.evolution_manager = EvolutionManager(manager_id="evolution_manager")
        
        # 创建元认知系统工厂
        self.metacognition_factory = MetacognitionFactory()
        
        # 创建元认知系统管理器
        self.metacognition_manager = MetacognitionManager(manager_id="metacognition_manager")
        
        # 创建分布式系统工厂
        self.distributed_factory = DistributedSystemFactory()
        
        # 创建分布式系统管理器
        self.distributed_manager = DistributedManager(manager_id="distributed_manager")
        
        # 创建任务调度器
        self.task_scheduler = TaskScheduler(scheduler_id="task_scheduler")
        
        # 创建网络拓扑
        self.network_topology = self.distributed_factory.create_network_topology(
            topology_id="test_topology",
            topology_type=NetworkTopology.MESH,
            metadata={"description": "测试网络拓扑"}
        )
        
        # 创建通信协议
        self.communication_protocol = self.distributed_factory.create_communication_protocol(
            protocol_id="test_protocol",
            protocol_type=CommunicationProtocol.JSON_RPC,
            metadata={"description": "测试通信协议"}
        )
        
        # 创建节点
        self.nodes = {}
        for i in range(5):
            node_id = f"node_{i}"
            node = self.distributed_factory.create_node(
                node_id=node_id,
                node_type=NodeType.WORKER,
                network_topology_id="test_topology",
                communication_protocol_id="test_protocol",
                metadata={
                    "description": f"测试节点 {i}",
                    "capabilities": {
                        "cpu_cores": 4,
                        "memory": 8192,  # MB
                        "gpu": i % 2 == 0  # 偶数节点有GPU
                    },
                    "status": "active",
                    "load": 0.2 + i * 0.1,
                    "memory_usage": 0.3 + i * 0.1
                }
            )
            self.nodes[node_id] = node
            self.distributed_manager.register_node(node)
        
        # 创建主节点
        self.master_node = self.distributed_factory.create_node(
            node_id="master_node",
            node_type=NodeType.MASTER,
            network_topology_id="test_topology",
            communication_protocol_id="test_protocol",
            metadata={"description": "主节点"}
        )
        self.distributed_manager.register_node(self.master_node)
        
        # 创建向量空间范畴
        self.vector_category = self.morphism_factory.create_category(
            category_id="vector_space",
            category_type="VECTOR_SPACE",
            metadata={"description": "向量空间范畴"}
        )
        
        # 创建对象（向量空间）
        self.r2_space = self.morphism_factory.create_object(
            object_id="R2",
            category_id="vector_space",
            data={"dimension": 2},
            metadata={"description": "二维实数向量空间"}
        )
        
        self.r3_space = self.morphism_factory.create_object(
            object_id="R3",
            category_id="vector_space",
            data={"dimension": 3},
            metadata={"description": "三维实数向量空间"}
        )
        
        # 创建线性态射
        def linear_transform(x: np.ndarray) -> np.ndarray:
            """线性变换：R2 -> R3"""
            matrix = np.array([
                [1, 2],
                [3, 4],
                [5, 6]
            ])
            return matrix @ x
        
        self.linear_morphism = self.morphism_factory.create_linear_morphism(
            domain_id="R2",
            codomain_id="R3",
            morphism_id="linear_morphism",
            function=linear_transform,
            metadata={"description": "从R2到R3的线性变换"}
        )
        
        # 创建非线性态射
        def nonlinear_transform(x: np.ndarray) -> np.ndarray:
            """非线性变换：R2 -> R2"""
            return np.array([
                x[0]**2,
                np.sin(x[1])
            ])
        
        self.nonlinear_morphism = self.morphism_factory.create_nonlinear_morphism(
            domain_id="R2",
            codomain_id="R2",
            morphism_id="nonlinear_morphism",
            function=nonlinear_transform,
            metadata={"description": "R2上的非线性变换"}
        )
        
        # 创建资源监控线程
        self.stop_monitoring = False
        self.resource_history = []
        self.monitoring_thread = threading.Thread(target=self.monitor_resources)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()
    
    def tearDown(self):
        """测试后的清理工作"""
        logger.info("清理长时间运行稳定性测试环境")
        
        # 停止资源监控
        self.stop_monitoring = True
        if self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=1.0)
        
        # 记录结束时间
        self.end_time = time.time()
        self.test_duration = self.end_time - self.start_time
        
        # 记录最终资源使用情况
        self.final_resources = self.get_system_resources()
        logger.info(f"最终系统资源使用情况: {self.final_resources}")
        
        # 计算资源使用变化
        cpu_change = self.final_resources["cpu_percent"] - self.initial_resources["cpu_percent"]
        memory_change = self.final_resources["memory_percent"] - self.initial_resources["memory_percent"]
        
        logger.info(f"测试持续时间: {self.test_duration:.2f} 秒")
        logger.info(f"CPU使用率变化: {cpu_change:.2f}%")
        logger.info(f"内存使用率变化: {memory_change:.2f}%")
        
        # 分析资源历史
        if self.resource_history:
            cpu_history = [r["cpu_percent"] for r in self.resource_history]
            memory_history = [r["memory_percent"] for r in self.resource_history]
            
            avg_cpu = sum(cpu_history) / len(cpu_history)
            max_cpu = max(cpu_history)
            min_cpu = min(cpu_history)
            
            avg_memory = sum(memory_history) / len(memory_history)
            max_memory = max(memory_history)
            min_memory = min(memory_history)
            
            logger.info(f"平均CPU使用率: {avg_cpu:.2f}% (最小: {min_cpu:.2f}%, 最大: {max_cpu:.2f}%)")
            logger.info(f"平均内存使用率: {avg_memory:.2f}% (最小: {min_memory:.2f}%, 最大: {max_memory:.2f}%)")
        
        # 清理资源
        self.morphism_factory = None
        self.feedback_factory = None
        self.feedback_manager = None
        self.evolution_factory = None
        self.evolution_manager = None
        self.metacognition_factory = None
        self.metacognition_manager = None
        self.distributed_factory = None
        self.distributed_manager = None
        self.task_scheduler = None
        self.network_topology = None
        self.communication_protocol = None
        self.nodes = None
        self.master_node = None
        self.vector_category = None
        self.r2_space = None
        self.r3_space = None
        self.linear_morphism = None
        self.nonlinear_morphism = None
        
        # 强制垃圾回收
        gc.collect()
    
    def monitor_resources(self, interval: float = 1.0):
        """监控系统资源使用情况"""
        while not self.stop_monitoring:
            # 获取当前资源使用情况
            resources = self.get_system_resources()
            
            # 添加到历史记录
            self.resource_history.append(resources)
            
            # 等待指定时间
            time.sleep(interval)
    
    def get_system_resources(self) -> Dict[str, Any]:
        """获取系统资源使用情况"""
        return {
            "timestamp": time.time(),
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "available_memory_mb": psutil.virtual_memory().available / (1024 * 1024),
            "disk_percent": psutil.disk_usage('/').percent,
            "process_count": len(psutil.pids()),
            "process_info": {
                "cpu_percent": psutil.Process().cpu_percent(),
                "memory_percent": psutil.Process().memory_percent(),
                "memory_info": dict(psutil.Process().memory_info()._asdict())
            }
        }
    
    def run_with_timeout(self, func: Callable, args: Tuple = (), kwargs: Dict = None, timeout_sec: float = 60.0) -> Optional[Any]:
        """使用超时运行函数"""
        if kwargs is None:
            kwargs = {}
        
        result = [None]
        exception = [None]
        
        def target():
            try:
                result[0] = func(*args, **kwargs)
            except Exception as e:
                exception[0] = e
        
        thread = threading.Thread(target=target)
        thread.daemon = True
        thread.start()
        thread.join(timeout_sec)
        
        if thread.is_alive():
            logger.warning(f"函数执行超时: {func.__name__}, 超时时间: {timeout_sec} 秒")
            return None
        
        if exception[0] is not None:
            logger.error(f"函数执行异常: {func.__name__}, 异常: {exception[0]}")
            raise exception[0]
        
        return result[0]
    
    def run_with_memory_limit(self, func: Callable, args: Tuple = (), kwargs: Dict = None, memory_limit_mb: float = 1024.0) -> Optional[Any]:
        """使用内存限制运行函数"""
        if kwargs is None:
            kwargs = {}
        
        # 获取当前可用内存
        available_memory_mb = psutil.virtual_memory().available / (1024 * 1024)
        
        # 如果可用内存小于限制，返回None
        if available_memory_mb < memory_limit_mb:
            logger.warning(f"可用内存不足: {available_memory_mb:.2f} MB < {memory_limit_mb:.2f} MB")
            return None
        
        # 创建子进程执行函数
        def target(queue):
            try:
                result = func(*args, **kwargs)
                queue.put(("result", result))
            except Exception as e:
                queue.put(("exception", e))
        
        # 创建队列
        queue = multiprocessing.Queue()
        
        # 创建进程
        process = multiprocessing.Process(target=target, args=(queue,))
        
        # 启动进程
        process.start()
        
        # 等待进程完成
        process.join()
        
        # 检查进程是否正常退出
        if process.exitcode != 0:
            logger.error(f"进程异常退出: exitcode={process.exitcode}")
            return None
        
        # 获取结果
        if queue.empty():
            logger.error("进程未返回结果")
            return None
        
        result_type, result_value = queue.get()
        
        if result_type == "exception":
            logger.error(f"函数执行异常: {func.__name__}, 异常: {result_value}")
            raise result_value
        
        return result_value
    
    def create_timeout_handler(self, timeout_sec: float):
        """创建超时处理器"""
        def timeout_handler(signum, frame):
            raise TimeoutError(f"操作超时，超时时间: {timeout_sec} 秒")
        
        # 设置信号处理器
        signal.signal(signal.SIGALRM, timeout_handler)
        
        # 设置闹钟
        signal.alarm(int(timeout_sec))
        
        return lambda: signal.alarm(0)  # 返回取消闹钟的函数
    
    def measure_execution_time(self, func: Callable, args: Tuple = (), kwargs: Dict = None, repeat: int = 1) -> Tuple[float, float, float]:
        """测量函数执行时间"""
        if kwargs is None:
            kwargs = {}
        
        execution_times = []
        
        for _ in range(repeat):
            start_time = time.time()
            func(*args, **kwargs)
            end_time = time.time()
            execution_times.append(end_time - start_time)
        
        avg_time = sum(execution_times) / len(execution_times)
        min_time = min(execution_times)
        max_time = max(execution_times)
        
        return avg_time, min_time, max_time
    
    def measure_memory_usage(self, func: Callable, args: Tuple = (), kwargs: Dict = None) -> Tuple[float, float]:
        """测量函数内存使用"""
        if kwargs is None:
            kwargs = {}
        
        # 强制垃圾回收
        gc.collect()
        
        # 获取初始内存使用
        process = psutil.Process()
        initial_memory = process.memory_info().rss / (1024 * 1024)  # MB
        
        # 执行函数
        func(*args, **kwargs)
        
        # 获取执行后内存使用
        final_memory = process.memory_info().rss / (1024 * 1024)  # MB
        
        # 计算内存使用变化
        memory_change = final_memory - initial_memory
        
        return final_memory, memory_change
