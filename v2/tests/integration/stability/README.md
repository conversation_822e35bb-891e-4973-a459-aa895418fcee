# 长时间运行稳定性测试

本目录包含长时间运行稳定性的测试，用于测试系统在长时间运行和持续负载下的稳定性。

## 测试内容

测试套件包含以下测试：

1. **持续负载测试**：测试系统在持续负载下的稳定性
   - 测试持续负载
   - 测试突发负载
   - 测试混合负载

2. **长时间运行测试**：测试系统在长时间运行下的稳定性
   - 测试长时间运行稳定性
   - 测试系统恢复能力

## 运行测试

### 运行所有测试

要运行所有测试，请执行以下命令：

```bash
cd /path/to/project
python -m v2.tests.integration.stability.run_tests
```

或者直接运行测试脚本：

```bash
cd /path/to/project
python v2/tests/integration/stability/run_tests.py
```

### 运行特定类型的测试

要运行特定类型的测试，请使用 `--type` 参数：

```bash
# 运行持续负载测试
python v2/tests/integration/stability/run_tests.py --type continuous_load

# 运行长时间运行测试
python v2/tests/integration/stability/run_tests.py --type long_running
```

### 调整测试持续时间

要调整测试持续时间，请使用 `--duration-factor` 参数：

```bash
# 运行持续时间为默认值一半的测试
python v2/tests/integration/stability/run_tests.py --duration-factor 0.5

# 运行持续时间为默认值两倍的测试
python v2/tests/integration/stability/run_tests.py --duration-factor 2.0
```

### 运行单个测试文件

要运行单个测试文件，请执行以下命令：

```bash
cd /path/to/project
python -m unittest v2.tests.integration.stability.test_continuous_load
```

或者直接运行测试文件：

```bash
cd /path/to/project
python v2/tests/integration/stability/test_continuous_load.py
```

### 运行单个测试用例

要运行单个测试用例，请执行以下命令：

```bash
cd /path/to/project
python -m unittest v2.tests.integration.stability.test_continuous_load.ContinuousLoadTest.test_continuous_load
```

## 测试依赖

测试套件依赖以下模块：

- `unittest`: Python标准库中的单元测试框架
- `numpy`: 用于数值计算
- `logging`: 用于日志记录
- `psutil`: 用于获取系统资源使用情况
- `threading`: 用于多线程操作
- `queue`: 用于线程间通信
- `gc`: 用于垃圾回收控制

## 注意事项

- 运行测试前，请确保已安装所有依赖
- 长时间运行测试可能需要较长时间运行，请耐心等待
- 测试会消耗大量系统资源，请确保系统有足够的资源
- 测试会模拟各种负载和故障情况，但不会对实际系统造成损害
- 如果需要中断测试，可以按 Ctrl+C
