#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
长时间运行稳定性测试

本模块测试系统在长时间运行下的稳定性。
"""

import os
import sys
import unittest
import numpy as np
import logging
import time
import threading
import queue
import random
import gc
from typing import Any, Dict, List, Optional, Tuple, Union

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

# 导入测试基类
from v2.tests.integration.stability.test_base import BaseStabilityTest

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class LongRunningTest(BaseStabilityTest):
    """长时间运行稳定性测试类"""
    
    def setUp(self):
        """测试前的准备工作"""
        super().setUp()
        
        # 创建测试数据
        self.test_data = {
            "morphism_executions": 0,
            "metacognition_operations": 0,
            "distributed_operations": 0,
            "memory_checks": 0,
            "gc_collections": 0,
            "errors": 0,
            "start_time": time.time()
        }
        
        # 创建停止标志
        self.stop_test = False
        
        # 创建测试线程
        self.test_threads = []
    
    def tearDown(self):
        """测试后的清理工作"""
        # 停止测试线程
        self.stop_test = True
        
        # 等待测试线程结束
        for thread in self.test_threads:
            if thread.is_alive():
                thread.join(timeout=1.0)
        
        # 计算测试数据
        self.test_data["end_time"] = time.time()
        self.test_data["duration"] = self.test_data["end_time"] - self.test_data["start_time"]
        
        # 计算每秒操作数
        operations_per_second = {
            "morphism_executions": self.test_data["morphism_executions"] / self.test_data["duration"],
            "metacognition_operations": self.test_data["metacognition_operations"] / self.test_data["duration"],
            "distributed_operations": self.test_data["distributed_operations"] / self.test_data["duration"],
            "memory_checks": self.test_data["memory_checks"] / self.test_data["duration"],
            "gc_collections": self.test_data["gc_collections"] / self.test_data["duration"]
        }
        
        # 输出测试数据
        logger.info(f"长时间运行测试数据:")
        logger.info(f"  测试持续时间: {self.test_data['duration']:.2f} 秒")
        logger.info(f"  态射执行次数: {self.test_data['morphism_executions']} ({operations_per_second['morphism_executions']:.2f}/秒)")
        logger.info(f"  元认知操作次数: {self.test_data['metacognition_operations']} ({operations_per_second['metacognition_operations']:.2f}/秒)")
        logger.info(f"  分布式操作次数: {self.test_data['distributed_operations']} ({operations_per_second['distributed_operations']:.2f}/秒)")
        logger.info(f"  内存检查次数: {self.test_data['memory_checks']} ({operations_per_second['memory_checks']:.2f}/秒)")
        logger.info(f"  垃圾回收次数: {self.test_data['gc_collections']} ({operations_per_second['gc_collections']:.2f}/秒)")
        logger.info(f"  错误次数: {self.test_data['errors']}")
        
        # 调用父类的tearDown方法
        super().tearDown()
    
    def morphism_thread(self):
        """态射执行线程"""
        logger.info("态射执行线程启动")
        
        # 创建输入数据
        input_vectors = [
            np.array([1.0, 2.0]),
            np.array([2.0, 3.0]),
            np.array([3.0, 4.0]),
            np.array([4.0, 5.0]),
            np.array([5.0, 6.0])
        ]
        
        # 获取态射
        morphisms = [self.linear_morphism, self.nonlinear_morphism]
        
        while not self.stop_test:
            try:
                # 随机选择输入向量
                input_vector = random.choice(input_vectors)
                
                # 随机选择态射
                morphism = random.choice(morphisms)
                
                # 执行态射
                output_vector = morphism.apply(input_vector)
                
                # 更新测试数据
                self.test_data["morphism_executions"] += 1
                
                # 短暂休眠
                time.sleep(0.01)
            
            except Exception as e:
                logger.error(f"态射执行线程发生异常: {e}")
                self.test_data["errors"] += 1
        
        logger.info("态射执行线程结束")
    
    def metacognition_thread(self):
        """元认知操作线程"""
        logger.info("元认知操作线程启动")
        
        # 创建认知状态和元认知状态列表
        cognitive_states = []
        metacognitive_states = []
        
        # 最大状态数量
        max_states = 100
        
        while not self.stop_test:
            try:
                # 随机选择操作类型
                operation_type = random.choice(["create", "update", "delete"])
                
                if operation_type == "create":
                    # 创建认知状态
                    cognitive_state = self.metacognition_manager.create_cognitive_state(
                        cognitive_level=CognitiveLevel.PERCEPTION,
                        data={"value": random.random(), "timestamp": time.time()},
                        metadata={"description": "测试认知状态"}
                    )
                    
                    # 添加到列表
                    cognitive_states.append(cognitive_state)
                    
                    # 创建元认知状态
                    metacognitive_state = self.metacognition_manager.create_metacognitive_state(
                        metacognitive_process=MetacognitiveProcess.MONITORING,
                        cognitive_states={cognitive_state.state_id: cognitive_state},
                        data={"value": random.random(), "timestamp": time.time()},
                        metadata={"description": "测试元认知状态"}
                    )
                    
                    # 添加到列表
                    metacognitive_states.append(metacognitive_state)
                
                elif operation_type == "update" and metacognitive_states:
                    # 随机选择一个元认知状态
                    state = random.choice(metacognitive_states)
                    
                    # 更新元认知状态
                    updated_state = self.metacognition_manager.update_metacognitive_state(
                        state_id=state.state_id,
                        data={"value": random.random(), "timestamp": time.time()}
                    )
                    
                    # 更新列表
                    index = metacognitive_states.index(state)
                    metacognitive_states[index] = updated_state
                
                elif operation_type == "delete" and metacognitive_states:
                    # 随机选择一个元认知状态
                    state = random.choice(metacognitive_states)
                    
                    # 从列表中移除
                    metacognitive_states.remove(state)
                    
                    # 如果有关联的认知状态，也从列表中移除
                    for cognitive_state in list(cognitive_states):
                        if cognitive_state.state_id in state.cognitive_states:
                            cognitive_states.remove(cognitive_state)
                
                # 限制状态数量
                if len(cognitive_states) > max_states:
                    # 移除多余的状态
                    excess = len(cognitive_states) - max_states
                    cognitive_states = cognitive_states[excess:]
                
                if len(metacognitive_states) > max_states:
                    # 移除多余的状态
                    excess = len(metacognitive_states) - max_states
                    metacognitive_states = metacognitive_states[excess:]
                
                # 更新测试数据
                self.test_data["metacognition_operations"] += 1
                
                # 短暂休眠
                time.sleep(0.01)
            
            except Exception as e:
                logger.error(f"元认知操作线程发生异常: {e}")
                self.test_data["errors"] += 1
        
        logger.info("元认知操作线程结束")
    
    def distributed_thread(self):
        """分布式操作线程"""
        logger.info("分布式操作线程启动")
        
        # 创建任务列表
        tasks = []
        
        # 最大任务数量
        max_tasks = 100
        
        while not self.stop_test:
            try:
                # 随机选择操作类型
                operation_type = random.choice(["create_task", "send_message", "node_status"])
                
                if operation_type == "create_task":
                    # 创建任务
                    task = self.distributed_factory.create_task(
                        task_id=f"task_{time.time()}_{random.randint(1000, 9999)}",
                        task_type="test_task",
                        source_node_id="master_node",
                        target_node_id=None,  # 由调度器分配
                        priority=random.randint(1, 10),
                        data={"value": random.random()},
                        metadata={"description": "测试任务"}
                    )
                    
                    # 提交任务
                    self.task_scheduler.submit_task(task)
                    
                    # 分配任务
                    target_node_id = random.choice(list(self.nodes.keys()))
                    self.task_scheduler.assign_task(task.task_id, target_node_id)
                    
                    # 添加到列表
                    tasks.append(task)
                    
                    # 随机完成一些任务
                    if random.random() < 0.8 and tasks:
                        # 随机选择一个任务
                        task_to_complete = random.choice(tasks)
                        
                        # 完成任务
                        self.task_scheduler.complete_task(
                            task_id=task_to_complete.task_id,
                            result={"status": "success"}
                        )
                        
                        # 从列表中移除
                        tasks.remove(task_to_complete)
                
                elif operation_type == "send_message":
                    # 随机选择源节点和目标节点
                    source_node_id = random.choice(list(self.nodes.keys()))
                    target_node_id = random.choice(list(self.nodes.keys()))
                    
                    # 创建消息
                    message = self.distributed_factory.create_message(
                        message_id=f"message_{time.time()}_{random.randint(1000, 9999)}",
                        source_node_id=source_node_id,
                        target_node_id=target_node_id,
                        message_type=MessageType.TASK_ASSIGNMENT,
                        content={"value": random.random()},
                        metadata={"description": "测试消息"}
                    )
                    
                    # 发送消息
                    self.distributed_manager.send_message(message)
                
                elif operation_type == "node_status":
                    # 随机选择一个节点
                    node_id = random.choice(list(self.nodes.keys()))
                    node = self.nodes[node_id]
                    
                    # 更新节点状态
                    node.metadata["load"] = random.random()
                    node.metadata["memory_usage"] = random.random()
                    node.metadata["last_heartbeat"] = time.time()
                
                # 限制任务数量
                if len(tasks) > max_tasks:
                    # 移除多余的任务
                    excess = len(tasks) - max_tasks
                    tasks_to_remove = tasks[:excess]
                    tasks = tasks[excess:]
                    
                    # 取消这些任务
                    for task in tasks_to_remove:
                        self.task_scheduler.cancel_task(task.task_id)
                
                # 更新测试数据
                self.test_data["distributed_operations"] += 1
                
                # 短暂休眠
                time.sleep(0.01)
            
            except Exception as e:
                logger.error(f"分布式操作线程发生异常: {e}")
                self.test_data["errors"] += 1
        
        logger.info("分布式操作线程结束")
    
    def memory_thread(self):
        """内存监控线程"""
        logger.info("内存监控线程启动")
        
        # 内存检查间隔（秒）
        check_interval = 1.0
        
        # 垃圾回收间隔（秒）
        gc_interval = 5.0
        
        # 记录上次垃圾回收时间
        last_gc_time = time.time()
        
        while not self.stop_test:
            try:
                # 获取当前内存使用情况
                memory_info = self.get_system_resources()
                
                # 检查内存使用率
                memory_percent = memory_info["memory_percent"]
                
                # 如果内存使用率过高，强制垃圾回收
                if memory_percent > 90:
                    logger.warning(f"内存使用率过高: {memory_percent}%，强制垃圾回收")
                    gc.collect()
                    self.test_data["gc_collections"] += 1
                
                # 定期垃圾回收
                current_time = time.time()
                if current_time - last_gc_time >= gc_interval:
                    gc.collect()
                    last_gc_time = current_time
                    self.test_data["gc_collections"] += 1
                
                # 更新测试数据
                self.test_data["memory_checks"] += 1
                
                # 等待下一次检查
                time.sleep(check_interval)
            
            except Exception as e:
                logger.error(f"内存监控线程发生异常: {e}")
                self.test_data["errors"] += 1
        
        logger.info("内存监控线程结束")
    
    def test_long_running_stability(self):
        """测试长时间运行稳定性"""
        logger.info("开始长时间运行稳定性测试")
        
        # 测试参数
        test_duration = 300  # 测试持续时间（秒）
        
        # 创建测试线程
        threads = [
            threading.Thread(target=self.morphism_thread),
            threading.Thread(target=self.metacognition_thread),
            threading.Thread(target=self.distributed_thread),
            threading.Thread(target=self.memory_thread)
        ]
        
        # 启动线程
        for thread in threads:
            thread.daemon = True
            thread.start()
            self.test_threads.append(thread)
        
        # 记录开始时间
        start_time = time.time()
        
        # 等待测试持续时间
        try:
            while time.time() - start_time < test_duration:
                # 每10秒输出一次状态
                if int(time.time() - start_time) % 10 == 0:
                    elapsed = time.time() - start_time
                    logger.info(f"测试运行中... 已经过 {elapsed:.0f} 秒，还剩 {test_duration - elapsed:.0f} 秒")
                    
                    # 输出当前测试数据
                    logger.info(f"  态射执行次数: {self.test_data['morphism_executions']}")
                    logger.info(f"  元认知操作次数: {self.test_data['metacognition_operations']}")
                    logger.info(f"  分布式操作次数: {self.test_data['distributed_operations']}")
                    logger.info(f"  内存检查次数: {self.test_data['memory_checks']}")
                    logger.info(f"  垃圾回收次数: {self.test_data['gc_collections']}")
                    logger.info(f"  错误次数: {self.test_data['errors']}")
                
                # 短暂休眠
                time.sleep(1.0)
        
        except KeyboardInterrupt:
            logger.info("测试被用户中断")
        
        # 停止测试线程
        self.stop_test = True
        
        # 等待线程结束
        for thread in self.test_threads:
            thread.join(timeout=1.0)
        
        # 验证测试结果
        self.assertGreater(self.test_data["morphism_executions"], 0, "应该有态射执行")
        self.assertGreater(self.test_data["metacognition_operations"], 0, "应该有元认知操作")
        self.assertGreater(self.test_data["distributed_operations"], 0, "应该有分布式操作")
        self.assertGreater(self.test_data["memory_checks"], 0, "应该有内存检查")
        self.assertLessEqual(self.test_data["errors"], self.test_data["morphism_executions"] * 0.01, "错误率不应超过1%")
    
    def test_system_recovery(self):
        """测试系统恢复能力"""
        logger.info("开始系统恢复能力测试")
        
        # 测试参数
        test_duration = 180  # 测试持续时间（秒）
        failure_interval = 30  # 故障间隔（秒）
        
        # 创建测试线程
        threads = [
            threading.Thread(target=self.morphism_thread),
            threading.Thread(target=self.metacognition_thread),
            threading.Thread(target=self.distributed_thread),
            threading.Thread(target=self.memory_thread)
        ]
        
        # 启动线程
        for thread in threads:
            thread.daemon = True
            thread.start()
            self.test_threads.append(thread)
        
        # 记录开始时间
        start_time = time.time()
        next_failure_time = start_time + failure_interval
        
        # 等待测试持续时间
        try:
            while time.time() - start_time < test_duration:
                current_time = time.time()
                
                # 检查是否应该模拟故障
                if current_time >= next_failure_time:
                    # 随机选择故障类型
                    failure_type = random.choice(["node_failure", "network_failure", "memory_pressure"])
                    
                    if failure_type == "node_failure":
                        # 随机选择一个节点
                        node_id = random.choice(list(self.nodes.keys()))
                        
                        logger.info(f"模拟节点故障: {node_id}")
                        self.simulate_node_failure(node_id=node_id, duration_sec=5.0)
                    
                    elif failure_type == "network_failure":
                        # 随机选择一个节点
                        node_id = random.choice(list(self.nodes.keys()))
                        
                        logger.info(f"模拟网络故障: {node_id}")
                        self.simulate_network_failure(node_id=node_id, duration_sec=5.0)
                    
                    elif failure_type == "memory_pressure":
                        logger.info("模拟内存压力")
                        self.simulate_high_memory_usage(memory_mb=100, duration_sec=5.0)
                    
                    # 计算下一次故障时间
                    next_failure_time = current_time + failure_interval
                
                # 每10秒输出一次状态
                if int(current_time - start_time) % 10 == 0:
                    elapsed = current_time - start_time
                    logger.info(f"测试运行中... 已经过 {elapsed:.0f} 秒，还剩 {test_duration - elapsed:.0f} 秒")
                    
                    # 输出当前测试数据
                    logger.info(f"  态射执行次数: {self.test_data['morphism_executions']}")
                    logger.info(f"  元认知操作次数: {self.test_data['metacognition_operations']}")
                    logger.info(f"  分布式操作次数: {self.test_data['distributed_operations']}")
                    logger.info(f"  错误次数: {self.test_data['errors']}")
                
                # 短暂休眠
                time.sleep(1.0)
        
        except KeyboardInterrupt:
            logger.info("测试被用户中断")
        
        # 停止测试线程
        self.stop_test = True
        
        # 等待线程结束
        for thread in self.test_threads:
            thread.join(timeout=1.0)
        
        # 验证测试结果
        self.assertGreater(self.test_data["morphism_executions"], 0, "应该有态射执行")
        self.assertGreater(self.test_data["metacognition_operations"], 0, "应该有元认知操作")
        self.assertGreater(self.test_data["distributed_operations"], 0, "应该有分布式操作")
        
        # 计算错误率
        total_operations = (
            self.test_data["morphism_executions"] +
            self.test_data["metacognition_operations"] +
            self.test_data["distributed_operations"]
        )
        error_rate = self.test_data["errors"] / total_operations if total_operations > 0 else 0
        
        logger.info(f"错误率: {error_rate:.4f} ({self.test_data['errors']}/{total_operations})")
        
        # 验证错误率不超过5%（考虑到我们故意引入了故障）
        self.assertLessEqual(error_rate, 0.05, "错误率不应超过5%")


if __name__ == "__main__":
    unittest.main()
