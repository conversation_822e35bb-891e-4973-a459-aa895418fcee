#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
边界条件和异常情况集成测试基类

本模块提供了边界条件和异常情况集成测试的基础类，包含共享的设置和工具方法。
"""

import os
import sys
import unittest
import numpy as np
import logging
import time
import psutil
import threading
import multiprocessing
from typing import Any, Dict, List, Optional, Tuple, Union, Callable

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入态射系统模块
from src.core.morphism import (
    MorphismSystemFactory, Category, Object, Morphism, MorphismType,
    FeedbackFactory, FeedbackManager, 
    EvolutionFactory, EvolutionManager
)

# 导入元认知系统模块
from src.core.metacognition import (
    MetacognitionFactory, MetacognitionManager,
    CognitiveLevel, MetacognitiveProcess,
    CognitiveState, MetacognitiveState
)

# 导入分布式系统模块
from src.core.distributed import (
    DistributedSystemFactory, DistributedManager,
    Node, NodeType, Message, MessageType,
    NetworkTopology, CommunicationProtocol,
    TaskScheduler, Task, TaskStatus
)


class BaseBoundaryConditionTest(unittest.TestCase):
    """边界条件和异常情况集成测试基类"""
    
    def setUp(self):
        """测试前的准备工作"""
        logger.info("设置边界条件和异常情况集成测试环境")
        
        # 创建态射系统工厂
        self.morphism_factory = MorphismSystemFactory()
        
        # 创建反馈工厂和管理器
        self.feedback_factory = FeedbackFactory()
        self.feedback_manager = FeedbackManager(manager_id="feedback_manager")
        
        # 创建演化工厂和管理器
        self.evolution_factory = EvolutionFactory()
        self.evolution_manager = EvolutionManager(manager_id="evolution_manager")
        
        # 创建元认知系统工厂
        self.metacognition_factory = MetacognitionFactory()
        
        # 创建元认知系统管理器
        self.metacognition_manager = MetacognitionManager(manager_id="metacognition_manager")
        
        # 创建分布式系统工厂
        self.distributed_factory = DistributedSystemFactory()
        
        # 创建分布式系统管理器
        self.distributed_manager = DistributedManager(manager_id="distributed_manager")
        
        # 创建任务调度器
        self.task_scheduler = TaskScheduler(scheduler_id="task_scheduler")
        
        # 创建网络拓扑
        self.network_topology = self.distributed_factory.create_network_topology(
            topology_id="test_topology",
            topology_type=NetworkTopology.MESH,
            metadata={"description": "测试网络拓扑"}
        )
        
        # 创建通信协议
        self.communication_protocol = self.distributed_factory.create_communication_protocol(
            protocol_id="test_protocol",
            protocol_type=CommunicationProtocol.JSON_RPC,
            metadata={"description": "测试通信协议"}
        )
        
        # 创建节点
        self.nodes = {}
        for i in range(5):
            node_id = f"node_{i}"
            node = self.distributed_factory.create_node(
                node_id=node_id,
                node_type=NodeType.WORKER,
                network_topology_id="test_topology",
                communication_protocol_id="test_protocol",
                metadata={
                    "description": f"测试节点 {i}",
                    "capabilities": {
                        "cpu_cores": 4,
                        "memory": 8192,  # MB
                        "gpu": i % 2 == 0  # 偶数节点有GPU
                    },
                    "status": "active",
                    "load": 0.2 + i * 0.1,
                    "memory_usage": 0.3 + i * 0.1
                }
            )
            self.nodes[node_id] = node
            self.distributed_manager.register_node(node)
        
        # 创建主节点
        self.master_node = self.distributed_factory.create_node(
            node_id="master_node",
            node_type=NodeType.MASTER,
            network_topology_id="test_topology",
            communication_protocol_id="test_protocol",
            metadata={"description": "主节点"}
        )
        self.distributed_manager.register_node(self.master_node)
        
        # 创建向量空间范畴
        self.vector_category = self.morphism_factory.create_category(
            category_id="vector_space",
            category_type="VECTOR_SPACE",
            metadata={"description": "向量空间范畴"}
        )
        
        # 创建对象（向量空间）
        self.r2_space = self.morphism_factory.create_object(
            object_id="R2",
            category_id="vector_space",
            data={"dimension": 2},
            metadata={"description": "二维实数向量空间"}
        )
        
        self.r3_space = self.morphism_factory.create_object(
            object_id="R3",
            category_id="vector_space",
            data={"dimension": 3},
            metadata={"description": "三维实数向量空间"}
        )
        
        # 创建线性态射
        def linear_transform(x: np.ndarray) -> np.ndarray:
            """线性变换：R2 -> R3"""
            matrix = np.array([
                [1, 2],
                [3, 4],
                [5, 6]
            ])
            return matrix @ x
        
        self.linear_morphism = self.morphism_factory.create_linear_morphism(
            domain_id="R2",
            codomain_id="R3",
            morphism_id="linear_morphism",
            function=linear_transform,
            metadata={"description": "从R2到R3的线性变换"}
        )
        
        # 创建非线性态射
        def nonlinear_transform(x: np.ndarray) -> np.ndarray:
            """非线性变换：R2 -> R2"""
            return np.array([
                x[0]**2,
                np.sin(x[1])
            ])
        
        self.nonlinear_morphism = self.morphism_factory.create_nonlinear_morphism(
            domain_id="R2",
            codomain_id="R2",
            morphism_id="nonlinear_morphism",
            function=nonlinear_transform,
            metadata={"description": "R2上的非线性变换"}
        )
    
    def tearDown(self):
        """测试后的清理工作"""
        logger.info("清理边界条件和异常情况集成测试环境")
        
        # 清理资源
        self.morphism_factory = None
        self.feedback_factory = None
        self.feedback_manager = None
        self.evolution_factory = None
        self.evolution_manager = None
        self.metacognition_factory = None
        self.metacognition_manager = None
        self.distributed_factory = None
        self.distributed_manager = None
        self.task_scheduler = None
        self.network_topology = None
        self.communication_protocol = None
        self.nodes = None
        self.master_node = None
        self.vector_category = None
        self.r2_space = None
        self.r3_space = None
        self.linear_morphism = None
        self.nonlinear_morphism = None
    
    def simulate_high_memory_usage(self, memory_mb: int, duration_sec: float = 1.0) -> None:
        """模拟高内存使用"""
        logger.info(f"模拟高内存使用: {memory_mb} MB, 持续 {duration_sec} 秒")
        
        # 分配指定大小的内存
        data = bytearray(memory_mb * 1024 * 1024)
        
        # 等待指定时间
        time.sleep(duration_sec)
        
        # 释放内存
        del data
    
    def simulate_high_cpu_usage(self, cpu_percent: float, duration_sec: float = 1.0) -> None:
        """模拟高CPU使用"""
        logger.info(f"模拟高CPU使用: {cpu_percent}%, 持续 {duration_sec} 秒")
        
        # 记录开始时间
        start_time = time.time()
        
        # 计算结束时间
        end_time = start_time + duration_sec
        
        # 循环直到达到指定时间
        while time.time() < end_time:
            # 计算需要工作的时间比例
            work_time = cpu_percent / 100.0
            sleep_time = 1.0 - work_time
            
            # 工作时间
            work_end = time.time() + work_time * 0.01
            while time.time() < work_end:
                # 执行一些计算密集型操作
                _ = [i**2 for i in range(1000)]
            
            # 休息时间
            if sleep_time > 0:
                time.sleep(sleep_time * 0.01)
    
    def simulate_network_delay(self, delay_sec: float) -> None:
        """模拟网络延迟"""
        logger.info(f"模拟网络延迟: {delay_sec} 秒")
        time.sleep(delay_sec)
    
    def simulate_network_failure(self, node_id: str, duration_sec: float = 10.0) -> None:
        """模拟节点网络故障"""
        logger.info(f"模拟节点 {node_id} 网络故障, 持续 {duration_sec} 秒")
        
        # 设置节点状态为断开连接
        if node_id in self.nodes:
            self.nodes[node_id].metadata["status"] = "disconnected"
            self.nodes[node_id].metadata["last_heartbeat"] = time.time() - 60  # 60秒前的心跳
        elif node_id == "master_node":
            self.master_node.metadata["status"] = "disconnected"
            self.master_node.metadata["last_heartbeat"] = time.time() - 60
        
        # 等待指定时间
        time.sleep(duration_sec)
        
        # 恢复节点状态
        if node_id in self.nodes:
            self.nodes[node_id].metadata["status"] = "active"
            self.nodes[node_id].metadata["last_heartbeat"] = time.time()
        elif node_id == "master_node":
            self.master_node.metadata["status"] = "active"
            self.master_node.metadata["last_heartbeat"] = time.time()
    
    def simulate_node_failure(self, node_id: str, duration_sec: float = 10.0) -> None:
        """模拟节点故障"""
        logger.info(f"模拟节点 {node_id} 故障, 持续 {duration_sec} 秒")
        
        # 设置节点状态为故障
        if node_id in self.nodes:
            self.nodes[node_id].metadata["status"] = "failed"
            self.nodes[node_id].metadata["last_heartbeat"] = time.time() - 120  # 120秒前的心跳
        elif node_id == "master_node":
            self.master_node.metadata["status"] = "failed"
            self.master_node.metadata["last_heartbeat"] = time.time() - 120
        
        # 等待指定时间
        time.sleep(duration_sec)
        
        # 恢复节点状态
        if node_id in self.nodes:
            self.nodes[node_id].metadata["status"] = "active"
            self.nodes[node_id].metadata["last_heartbeat"] = time.time()
        elif node_id == "master_node":
            self.master_node.metadata["status"] = "active"
            self.master_node.metadata["last_heartbeat"] = time.time()
    
    def simulate_data_corruption(self, data: Any) -> Any:
        """模拟数据损坏"""
        logger.info(f"模拟数据损坏: {type(data)}")
        
        if isinstance(data, dict):
            # 随机删除一个键
            if data:
                key = list(data.keys())[0]
                corrupted_data = data.copy()
                del corrupted_data[key]
                return corrupted_data
            return data
        
        elif isinstance(data, list):
            # 随机删除一个元素
            if data:
                corrupted_data = data.copy()
                corrupted_data.pop(0)
                return corrupted_data
            return data
        
        elif isinstance(data, np.ndarray):
            # 将一个元素设置为NaN
            if data.size > 0:
                corrupted_data = data.copy()
                corrupted_data.flat[0] = np.nan
                return corrupted_data
            return data
        
        elif isinstance(data, str):
            # 截断字符串
            if data:
                return data[:len(data)//2]
            return data
        
        else:
            # 对于其他类型，返回None
            return None
    
    def run_with_timeout(self, func: Callable, args: Tuple = (), kwargs: Dict = None, timeout_sec: float = 5.0) -> Optional[Any]:
        """使用超时运行函数"""
        if kwargs is None:
            kwargs = {}
        
        result = [None]
        exception = [None]
        
        def target():
            try:
                result[0] = func(*args, **kwargs)
            except Exception as e:
                exception[0] = e
        
        thread = threading.Thread(target=target)
        thread.daemon = True
        thread.start()
        thread.join(timeout_sec)
        
        if thread.is_alive():
            logger.warning(f"函数执行超时: {func.__name__}, 超时时间: {timeout_sec} 秒")
            return None
        
        if exception[0] is not None:
            logger.error(f"函数执行异常: {func.__name__}, 异常: {exception[0]}")
            raise exception[0]
        
        return result[0]
    
    def get_system_resources(self) -> Dict[str, Any]:
        """获取系统资源使用情况"""
        return {
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "available_memory_mb": psutil.virtual_memory().available / (1024 * 1024),
            "disk_percent": psutil.disk_usage('/').percent,
            "process_count": len(psutil.pids())
        }
