#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
边界条件和异常情况集成测试运行器

本模块用于运行边界条件和异常情况的所有集成测试。
"""

import os
import sys
import unittest
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

# 导入测试模块
from v2.tests.integration.boundary_conditions.test_resource_limits import ResourceLimitTest
from v2.tests.integration.boundary_conditions.test_error_handling import ErrorHandlingTest
from v2.tests.integration.boundary_conditions.test_recovery_mechanisms import RecoveryMechanismsTest

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_tests():
    """运行所有测试"""
    logger.info("运行边界条件和异常情况集成测试")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_suite.addTest(unittest.makeSuite(ResourceLimitTest))
    test_suite.addTest(unittest.makeSuite(ErrorHandlingTest))
    test_suite.addTest(unittest.makeSuite(RecoveryMechanismsTest))
    
    # 创建测试运行器
    test_runner = unittest.TextTestRunner(verbosity=2)
    
    # 运行测试
    test_result = test_runner.run(test_suite)
    
    # 输出测试结果
    logger.info(f"测试结果: 运行 {test_result.testsRun} 个测试")
    logger.info(f"成功: {test_result.testsRun - len(test_result.errors) - len(test_result.failures)}")
    logger.info(f"失败: {len(test_result.failures)}")
    logger.info(f"错误: {len(test_result.errors)}")
    
    # 返回测试结果
    return test_result.wasSuccessful()


if __name__ == "__main__":
    # 运行测试
    success = run_tests()
    
    # 设置退出码
    sys.exit(0 if success else 1)
