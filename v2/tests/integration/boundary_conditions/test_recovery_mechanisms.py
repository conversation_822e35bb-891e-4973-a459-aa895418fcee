#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
恢复机制集成测试

本模块测试系统从各种故障中恢复的能力。
"""

import os
import sys
import unittest
import numpy as np
import logging
import time
import json
import threading
from typing import Any, Dict, List, Optional, Tuple, Union

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

# 导入测试基类
from v2.tests.integration.boundary_conditions.test_base import BaseBoundaryConditionTest

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class RecoveryMechanismsTest(BaseBoundaryConditionTest):
    """恢复机制集成测试类"""
    
    def test_node_recovery(self):
        """测试节点恢复机制"""
        logger.info("测试节点恢复机制")
        
        # 选择一个节点进行测试
        test_node_id = "node_0"
        test_node = self.nodes[test_node_id]
        
        # 记录初始状态
        initial_status = test_node.metadata.get("status", "unknown")
        logger.info(f"节点 {test_node_id} 初始状态: {initial_status}")
        
        # 模拟节点故障
        logger.info(f"模拟节点 {test_node_id} 故障")
        self.simulate_node_failure(node_id=test_node_id, duration_sec=0)  # 立即设置为故障状态
        
        # 验证节点状态
        self.assertEqual(test_node.metadata["status"], "failed")
        
        # 创建节点恢复任务
        recovery_task = self.distributed_factory.create_task(
            task_id=f"node_recovery_task_{time.time()}",
            task_type="node_recovery",
            source_node_id="master_node",
            target_node_id="master_node",
            priority=10,  # 高优先级
            data={
                "node_id": test_node_id,
                "recovery_type": "restart",
                "timeout": 30.0  # 30秒超时
            },
            metadata={"description": "节点恢复任务"}
        )
        
        # 提交任务
        self.task_scheduler.submit_task(recovery_task)
        
        # 分配任务
        self.task_scheduler.assign_task(recovery_task.task_id, "master_node")
        
        # 模拟执行恢复任务
        logger.info(f"执行节点 {test_node_id} 恢复任务")
        
        # 模拟节点重启过程
        def restart_node():
            # 设置节点状态为重启中
            test_node.metadata["status"] = "restarting"
            logger.info(f"节点 {test_node_id} 状态: restarting")
            
            # 等待一段时间
            time.sleep(2.0)
            
            # 设置节点状态为活跃
            test_node.metadata["status"] = "active"
            test_node.metadata["last_heartbeat"] = time.time()
            logger.info(f"节点 {test_node_id} 状态: active")
        
        # 在单独的线程中执行重启
        restart_thread = threading.Thread(target=restart_node)
        restart_thread.start()
        
        # 等待重启完成
        restart_thread.join()
        
        # 验证节点状态
        self.assertEqual(test_node.metadata["status"], "active")
        
        # 创建恢复结果
        recovery_result = {
            "node_id": test_node_id,
            "initial_status": "failed",
            "final_status": test_node.metadata["status"],
            "recovery_time": 2.0,
            "status": "success"
        }
        
        # 完成恢复任务
        self.task_scheduler.complete_task(recovery_task.task_id, recovery_result)
        
        # 获取完成的恢复任务
        completed_recovery_task = self.task_scheduler.get_task(recovery_task.task_id)
        
        # 验证恢复任务结果
        self.assertEqual(completed_recovery_task.status, TaskStatus.COMPLETED)
        recovery_result = completed_recovery_task.data["result"]
        self.assertEqual(recovery_result["status"], "success")
        self.assertEqual(recovery_result["final_status"], "active")
        
        logger.info(f"节点 {test_node_id} 恢复成功")
        
        # 测试恢复后的节点功能
        logger.info(f"测试恢复后的节点 {test_node_id} 功能")
        
        # 创建测试任务
        test_task = self.distributed_factory.create_task(
            task_id=f"post_recovery_task_{time.time()}",
            task_type="morphism_execution",
            source_node_id="master_node",
            target_node_id=test_node_id,
            priority=5,
            data={
                "morphism_id": self.linear_morphism.morphism_id,
                "input_data": [1.0, 2.0]
            },
            metadata={"description": "恢复后测试任务"}
        )
        
        # 提交任务
        self.task_scheduler.submit_task(test_task)
        
        # 分配任务
        self.task_scheduler.assign_task(test_task.task_id, test_node_id)
        
        # 模拟节点执行任务
        input_data = np.array([1.0, 2.0])
        output_data = self.linear_morphism.apply(input_data)
        
        # 创建任务结果
        task_result = {
            "output_data": output_data.tolist(),
            "execution_time": 0.001,
            "status": "success"
        }
        
        # 完成任务
        self.task_scheduler.complete_task(test_task.task_id, task_result)
        
        # 获取完成的任务
        completed_test_task = self.task_scheduler.get_task(test_task.task_id)
        
        # 验证任务结果
        self.assertEqual(completed_test_task.status, TaskStatus.COMPLETED)
        test_result = completed_test_task.data["result"]
        self.assertEqual(test_result["status"], "success")
        
        logger.info(f"恢复后的节点 {test_node_id} 功能正常")
    
    def test_data_recovery(self):
        """测试数据恢复机制"""
        logger.info("测试数据恢复机制")
        
        # 创建原始数据
        original_data = {
            "vector": np.array([1.0, 2.0, 3.0]),
            "matrix": np.array([[1.0, 2.0], [3.0, 4.0]]),
            "scalar": 5.0,
            "string": "test_string",
            "dict": {"key1": "value1", "key2": 2},
            "list": [1, 2, 3, 4, 5]
        }
        
        # 创建数据备份
        logger.info("创建数据备份")
        
        # 序列化数据
        serialized_data = {
            "vector": original_data["vector"].tolist(),
            "matrix": original_data["matrix"].tolist(),
            "scalar": original_data["scalar"],
            "string": original_data["string"],
            "dict": original_data["dict"],
            "list": original_data["list"]
        }
        
        # 创建备份任务
        backup_task = self.distributed_factory.create_task(
            task_id=f"data_backup_task_{time.time()}",
            task_type="data_backup",
            source_node_id="master_node",
            target_node_id="node_1",  # 使用不同的节点
            priority=5,
            data={
                "data_id": "test_data",
                "data": serialized_data,
                "timestamp": time.time()
            },
            metadata={"description": "数据备份任务"}
        )
        
        # 提交任务
        self.task_scheduler.submit_task(backup_task)
        
        # 分配任务
        self.task_scheduler.assign_task(backup_task.task_id, "node_1")
        
        # 模拟执行备份任务
        backup_result = {
            "data_id": "test_data",
            "backup_id": f"backup_{time.time()}",
            "status": "success"
        }
        
        # 完成备份任务
        self.task_scheduler.complete_task(backup_task.task_id, backup_result)
        
        # 获取完成的备份任务
        completed_backup_task = self.task_scheduler.get_task(backup_task.task_id)
        
        # 验证备份任务结果
        self.assertEqual(completed_backup_task.status, TaskStatus.COMPLETED)
        backup_result = completed_backup_task.data["result"]
        self.assertEqual(backup_result["status"], "success")
        
        # 获取备份ID
        backup_id = backup_result["backup_id"]
        logger.info(f"数据备份成功，备份ID: {backup_id}")
        
        # 模拟数据损坏
        logger.info("模拟数据损坏")
        corrupted_data = self.simulate_data_corruption(original_data)
        
        # 验证数据已损坏
        self.assertNotEqual(corrupted_data, original_data)
        
        # 创建数据恢复任务
        recovery_task = self.distributed_factory.create_task(
            task_id=f"data_recovery_task_{time.time()}",
            task_type="data_recovery",
            source_node_id="master_node",
            target_node_id="node_1",
            priority=10,  # 高优先级
            data={
                "data_id": "test_data",
                "backup_id": backup_id,
                "reason": "数据损坏"
            },
            metadata={"description": "数据恢复任务"}
        )
        
        # 提交任务
        self.task_scheduler.submit_task(recovery_task)
        
        # 分配任务
        self.task_scheduler.assign_task(recovery_task.task_id, "node_1")
        
        # 模拟执行恢复任务
        logger.info("执行数据恢复")
        
        # 从备份中恢复数据
        recovered_data = serialized_data
        
        # 创建恢复结果
        recovery_result = {
            "data_id": "test_data",
            "backup_id": backup_id,
            "recovered_data": recovered_data,
            "status": "success"
        }
        
        # 完成恢复任务
        self.task_scheduler.complete_task(recovery_task.task_id, recovery_result)
        
        # 获取完成的恢复任务
        completed_recovery_task = self.task_scheduler.get_task(recovery_task.task_id)
        
        # 验证恢复任务结果
        self.assertEqual(completed_recovery_task.status, TaskStatus.COMPLETED)
        recovery_result = completed_recovery_task.data["result"]
        self.assertEqual(recovery_result["status"], "success")
        
        # 验证恢复的数据
        recovered_data = recovery_result["recovered_data"]
        
        # 将恢复的数据转换回原始格式
        restored_data = {
            "vector": np.array(recovered_data["vector"]),
            "matrix": np.array(recovered_data["matrix"]),
            "scalar": recovered_data["scalar"],
            "string": recovered_data["string"],
            "dict": recovered_data["dict"],
            "list": recovered_data["list"]
        }
        
        # 验证恢复的数据与原始数据一致
        self.assertTrue(np.array_equal(restored_data["vector"], original_data["vector"]))
        self.assertTrue(np.array_equal(restored_data["matrix"], original_data["matrix"]))
        self.assertEqual(restored_data["scalar"], original_data["scalar"])
        self.assertEqual(restored_data["string"], original_data["string"])
        self.assertEqual(restored_data["dict"], original_data["dict"])
        self.assertEqual(restored_data["list"], original_data["list"])
        
        logger.info("数据恢复成功，恢复的数据与原始数据一致")
    
    def test_task_recovery(self):
        """测试任务恢复机制"""
        logger.info("测试任务恢复机制")
        
        # 创建长时间运行的任务
        long_running_task = self.distributed_factory.create_task(
            task_id=f"long_running_task_{time.time()}",
            task_type="long_computation",
            source_node_id="master_node",
            target_node_id="node_0",
            priority=5,
            data={
                "computation_time": 5.0,  # 5秒计算时间
                "checkpoint_interval": 1.0  # 每1秒创建一个检查点
            },
            metadata={"description": "长时间运行任务"}
        )
        
        # 提交任务
        self.task_scheduler.submit_task(long_running_task)
        
        # 分配任务
        self.task_scheduler.assign_task(long_running_task.task_id, "node_0")
        
        # 模拟执行长时间运行的任务
        logger.info("开始执行长时间运行任务")
        
        # 创建检查点
        checkpoints = []
        
        # 模拟任务执行和检查点创建
        start_time = time.time()
        computation_time = long_running_task.data["computation_time"]
        checkpoint_interval = long_running_task.data["checkpoint_interval"]
        
        # 计算检查点数量
        checkpoint_count = int(computation_time / checkpoint_interval)
        
        # 创建检查点
        for i in range(checkpoint_count):
            # 计算当前进度
            progress = (i + 1) / checkpoint_count
            
            # 创建检查点
            checkpoint = {
                "checkpoint_id": f"checkpoint_{i+1}",
                "task_id": long_running_task.task_id,
                "timestamp": time.time(),
                "progress": progress,
                "intermediate_result": progress * 100
            }
            
            checkpoints.append(checkpoint)
            
            logger.info(f"创建检查点 {i+1}/{checkpoint_count}, 进度: {progress:.2f}")
            
            # 等待检查点间隔
            time.sleep(checkpoint_interval)
        
        # 模拟任务中断
        logger.info("模拟任务中断")
        
        # 模拟节点故障
        self.simulate_node_failure(node_id="node_0", duration_sec=2.0)
        
        # 创建任务恢复任务
        task_recovery_task = self.distributed_factory.create_task(
            task_id=f"task_recovery_task_{time.time()}",
            task_type="task_recovery",
            source_node_id="master_node",
            target_node_id="master_node",
            priority=10,  # 高优先级
            data={
                "original_task_id": long_running_task.task_id,
                "checkpoints": checkpoints,
                "reason": "节点故障"
            },
            metadata={"description": "任务恢复任务"}
        )
        
        # 提交任务
        self.task_scheduler.submit_task(task_recovery_task)
        
        # 分配任务
        self.task_scheduler.assign_task(task_recovery_task.task_id, "master_node")
        
        # 模拟执行任务恢复
        logger.info("执行任务恢复")
        
        # 获取最新的检查点
        latest_checkpoint = checkpoints[-1] if checkpoints else None
        
        if latest_checkpoint:
            # 创建恢复的任务
            recovered_task = self.distributed_factory.create_task(
                task_id=f"recovered_task_{time.time()}",
                task_type="long_computation",
                source_node_id="master_node",
                target_node_id="node_2",  # 使用不同的节点
                priority=5,
                data={
                    "computation_time": computation_time * (1 - latest_checkpoint["progress"]),
                    "checkpoint_interval": checkpoint_interval,
                    "original_task_id": long_running_task.task_id,
                    "start_from_checkpoint": latest_checkpoint,
                    "recovered": True
                },
                metadata={"description": "恢复的长时间运行任务"}
            )
            
            # 提交恢复的任务
            self.task_scheduler.submit_task(recovered_task)
            
            # 分配恢复的任务
            self.task_scheduler.assign_task(recovered_task.task_id, "node_2")
            
            # 创建恢复结果
            recovery_result = {
                "original_task_id": long_running_task.task_id,
                "recovered_task_id": recovered_task.task_id,
                "checkpoint_id": latest_checkpoint["checkpoint_id"],
                "progress_at_recovery": latest_checkpoint["progress"],
                "status": "success"
            }
            
            # 完成恢复任务
            self.task_scheduler.complete_task(task_recovery_task.task_id, recovery_result)
            
            # 获取完成的恢复任务
            completed_recovery_task = self.task_scheduler.get_task(task_recovery_task.task_id)
            
            # 验证恢复任务结果
            self.assertEqual(completed_recovery_task.status, TaskStatus.COMPLETED)
            recovery_result = completed_recovery_task.data["result"]
            self.assertEqual(recovery_result["status"], "success")
            
            logger.info(f"任务恢复成功，从检查点 {latest_checkpoint['checkpoint_id']} 恢复，进度: {latest_checkpoint['progress']:.2f}")
            
            # 模拟执行恢复的任务
            logger.info("执行恢复的任务")
            
            # 计算剩余时间
            remaining_time = computation_time * (1 - latest_checkpoint["progress"])
            
            # 等待剩余时间
            time.sleep(remaining_time)
            
            # 创建任务结果
            task_result = {
                "result": 100,  # 最终结果
                "execution_time": remaining_time,
                "original_task_id": long_running_task.task_id,
                "recovered": True,
                "status": "success"
            }
            
            # 完成恢复的任务
            self.task_scheduler.complete_task(recovered_task.task_id, task_result)
            
            # 获取完成的恢复任务
            completed_recovered_task = self.task_scheduler.get_task(recovered_task.task_id)
            
            # 验证恢复任务结果
            self.assertEqual(completed_recovered_task.status, TaskStatus.COMPLETED)
            task_result = completed_recovered_task.data["result"]
            self.assertEqual(task_result["status"], "success")
            self.assertTrue(task_result["recovered"])
            
            logger.info("恢复的任务成功完成")
        else:
            logger.warning("没有可用的检查点，无法恢复任务")
            
            # 创建恢复结果
            recovery_result = {
                "original_task_id": long_running_task.task_id,
                "status": "failed",
                "reason": "没有可用的检查点"
            }
            
            # 完成恢复任务
            self.task_scheduler.complete_task(task_recovery_task.task_id, recovery_result)


if __name__ == "__main__":
    unittest.main()
