#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
资源限制集成测试

本模块测试系统在资源限制条件下的行为。
"""

import os
import sys
import unittest
import numpy as np
import logging
import time
import psutil
import gc
from typing import Any, Dict, List, Optional, Tuple, Union

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

# 导入测试基类
from v2.tests.integration.boundary_conditions.test_base import BaseBoundaryConditionTest

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ResourceLimitTest(BaseBoundaryConditionTest):
    """资源限制集成测试类"""
    
    def test_memory_limit(self):
        """测试内存限制情况下的系统行为"""
        logger.info("测试内存限制情况下的系统行为")
        
        # 获取当前系统资源使用情况
        initial_resources = self.get_system_resources()
        logger.info(f"初始系统资源使用情况: {initial_resources}")
        
        # 计算可以分配的内存量（保留20%的可用内存）
        available_memory_mb = initial_resources["available_memory_mb"]
        memory_to_use_mb = int(available_memory_mb * 0.7)  # 使用70%的可用内存
        
        logger.info(f"可用内存: {available_memory_mb:.2f} MB, 将使用: {memory_to_use_mb} MB")
        
        # 创建大量认知状态，消耗内存
        try:
            # 记录开始时间
            start_time = time.time()
            
            # 创建大量认知状态
            states = []
            large_data_size = 1024 * 1024  # 1MB
            large_data = bytearray(large_data_size)
            
            # 计算需要创建的状态数量
            state_count = memory_to_use_mb
            
            logger.info(f"开始创建 {state_count} 个认知状态")
            
            for i in range(state_count):
                # 每创建10个状态检查一次内存使用情况
                if i % 10 == 0:
                    current_resources = self.get_system_resources()
                    memory_percent = current_resources["memory_percent"]
                    
                    # 如果内存使用率超过90%，停止创建
                    if memory_percent > 90:
                        logger.warning(f"内存使用率达到 {memory_percent}%, 停止创建状态")
                        break
                
                # 创建认知状态
                state_data = {
                    "index": i,
                    "timestamp": time.time(),
                    "large_data_reference": id(large_data)  # 只存储引用，不复制数据
                }
                
                state = self.metacognition_manager.create_cognitive_state(
                    cognitive_level=CognitiveLevel.PERCEPTION,
                    data=state_data,
                    metadata={"description": f"大内存状态 {i}"}
                )
                
                states.append(state)
            
            # 记录结束时间和创建的状态数量
            end_time = time.time()
            creation_time = end_time - start_time
            created_states = len(states)
            
            logger.info(f"创建了 {created_states} 个认知状态, 耗时: {creation_time:.2f} 秒")
            
            # 获取当前系统资源使用情况
            current_resources = self.get_system_resources()
            logger.info(f"当前系统资源使用情况: {current_resources}")
            
            # 验证内存使用增加
            self.assertGreater(current_resources["memory_percent"], initial_resources["memory_percent"])
            
            # 测试在高内存负载下创建元认知状态
            logger.info("在高内存负载下创建元认知状态")
            
            # 选择一部分认知状态
            selected_states = states[:min(100, len(states))]
            selected_states_dict = {state.state_id: state for state in selected_states}
            
            # 创建元认知状态
            meta_state_data = {
                "timestamp": time.time(),
                "state_count": len(selected_states),
                "memory_usage": current_resources["memory_percent"]
            }
            
            meta_state = self.metacognition_manager.create_metacognitive_state(
                metacognitive_process=MetacognitiveProcess.MONITORING,
                cognitive_states=selected_states_dict,
                data=meta_state_data,
                metadata={"description": "高内存负载监控"}
            )
            
            # 验证元认知状态
            self.assertIsNotNone(meta_state)
            self.assertEqual(len(meta_state.cognitive_states), len(selected_states))
            
            # 清理部分状态，释放内存
            logger.info("清理部分状态，释放内存")
            states_to_clear = states[len(states)//2:]
            states = states[:len(states)//2]
            
            # 强制垃圾回收
            states_to_clear.clear()
            gc.collect()
            
            # 获取清理后的系统资源使用情况
            after_clear_resources = self.get_system_resources()
            logger.info(f"清理后系统资源使用情况: {after_clear_resources}")
            
        except MemoryError as e:
            logger.error(f"内存错误: {e}")
            # 即使发生内存错误，测试也应该继续
            pass
        
        finally:
            # 清理所有状态
            states.clear()
            gc.collect()
            
            # 获取最终系统资源使用情况
            final_resources = self.get_system_resources()
            logger.info(f"最终系统资源使用情况: {final_resources}")
    
    def test_cpu_limit(self):
        """测试CPU限制情况下的系统行为"""
        logger.info("测试CPU限制情况下的系统行为")
        
        # 获取当前系统资源使用情况
        initial_resources = self.get_system_resources()
        logger.info(f"初始系统资源使用情况: {initial_resources}")
        
        # 创建计算密集型态射
        def compute_intensive_transform(x: np.ndarray) -> np.ndarray:
            """计算密集型变换：R2 -> R2"""
            # 执行一些计算密集型操作
            result = x.copy()
            for _ in range(1000000):
                result = np.sin(result) + np.cos(result)
            return result
        
        compute_morphism = self.morphism_factory.create_nonlinear_morphism(
            domain_id="R2",
            codomain_id="R2",
            morphism_id="compute_intensive_morphism",
            function=compute_intensive_transform,
            metadata={"description": "计算密集型变换"}
        )
        
        # 创建输入数据
        input_vector = np.array([1.0, 2.0])
        
        # 模拟高CPU负载
        logger.info("模拟高CPU负载")
        self.simulate_high_cpu_usage(cpu_percent=90, duration_sec=2.0)
        
        # 获取高负载下的系统资源使用情况
        high_load_resources = self.get_system_resources()
        logger.info(f"高负载下系统资源使用情况: {high_load_resources}")
        
        # 在高CPU负载下执行态射
        logger.info("在高CPU负载下执行态射")
        
        # 创建任务
        task = self.distributed_factory.create_task(
            task_id=f"cpu_intensive_task_{time.time()}",
            task_type="morphism_execution",
            source_node_id="master_node",
            target_node_id="node_0",
            priority=5,
            data={
                "morphism_id": compute_morphism.morphism_id,
                "input_data": input_vector.tolist()
            },
            metadata={"description": "CPU密集型任务"}
        )
        
        # 提交任务
        self.task_scheduler.submit_task(task)
        
        # 分配任务
        self.task_scheduler.assign_task(task.task_id, "node_0")
        
        # 模拟节点执行任务
        start_time = time.time()
        
        # 使用超时运行函数
        output_data = self.run_with_timeout(
            func=compute_morphism.apply,
            args=(input_vector,),
            timeout_sec=10.0
        )
        
        execution_time = time.time() - start_time
        
        # 检查是否超时
        if output_data is None:
            logger.warning("态射执行超时")
            task_result = {
                "status": "timeout",
                "execution_time": execution_time
            }
        else:
            logger.info(f"态射执行完成，耗时: {execution_time:.2f} 秒")
            task_result = {
                "output_data": output_data.tolist(),
                "execution_time": execution_time,
                "status": "success"
            }
        
        # 完成任务
        self.task_scheduler.complete_task(task.task_id, task_result)
        
        # 获取完成的任务
        completed_task = self.task_scheduler.get_task(task.task_id)
        
        # 验证任务结果
        self.assertEqual(completed_task.status, TaskStatus.COMPLETED)
        
        # 获取最终系统资源使用情况
        final_resources = self.get_system_resources()
        logger.info(f"最终系统资源使用情况: {final_resources}")
    
    def test_network_bandwidth_limit(self):
        """测试网络带宽限制情况下的系统行为"""
        logger.info("测试网络带宽限制情况下的系统行为")
        
        # 创建大型数据
        data_size_mb = 10  # 10MB
        large_data = np.random.rand(data_size_mb * 1024 * 1024 // 8)  # 每个float64占8字节
        
        # 创建大型数据传输任务
        task = self.distributed_factory.create_task(
            task_id=f"large_data_task_{time.time()}",
            task_type="data_transfer",
            source_node_id="master_node",
            target_node_id="node_0",
            priority=5,
            data={
                "data_size_mb": data_size_mb,
                "data_sample": large_data[:10].tolist()  # 只发送一小部分样本
            },
            metadata={"description": "大型数据传输任务"}
        )
        
        # 提交任务
        self.task_scheduler.submit_task(task)
        
        # 分配任务
        self.task_scheduler.assign_task(task.task_id, "node_0")
        
        # 模拟网络延迟
        logger.info("模拟网络延迟")
        self.simulate_network_delay(delay_sec=1.0)
        
        # 模拟带宽限制下的数据传输
        logger.info("模拟带宽限制下的数据传输")
        
        # 计算理论传输时间（假设带宽为10MB/s）
        bandwidth_mb_per_sec = 10
        theoretical_transfer_time = data_size_mb / bandwidth_mb_per_sec
        
        logger.info(f"数据大小: {data_size_mb} MB, 带宽: {bandwidth_mb_per_sec} MB/s")
        logger.info(f"理论传输时间: {theoretical_transfer_time:.2f} 秒")
        
        # 模拟数据传输
        start_time = time.time()
        
        # 分块传输数据
        chunk_size_mb = 1  # 每次传输1MB
        chunks = data_size_mb // chunk_size_mb
        
        for i in range(chunks):
            # 模拟每块数据的传输时间
            chunk_transfer_time = chunk_size_mb / bandwidth_mb_per_sec
            
            # 模拟网络延迟
            self.simulate_network_delay(delay_sec=chunk_transfer_time)
            
            logger.info(f"传输数据块 {i+1}/{chunks}, 大小: {chunk_size_mb} MB")
        
        transfer_time = time.time() - start_time
        
        logger.info(f"数据传输完成，实际耗时: {transfer_time:.2f} 秒")
        
        # 创建任务结果
        task_result = {
            "transfer_time": transfer_time,
            "theoretical_transfer_time": theoretical_transfer_time,
            "bandwidth_mb_per_sec": bandwidth_mb_per_sec,
            "status": "success"
        }
        
        # 完成任务
        self.task_scheduler.complete_task(task.task_id, task_result)
        
        # 获取完成的任务
        completed_task = self.task_scheduler.get_task(task.task_id)
        
        # 验证任务结果
        self.assertEqual(completed_task.status, TaskStatus.COMPLETED)
        result = completed_task.data["result"]
        self.assertEqual(result["status"], "success")
        
        # 验证传输时间接近理论时间
        self.assertGreaterEqual(result["transfer_time"], result["theoretical_transfer_time"])
        
        # 测试在带宽限制下的分布式态射执行
        logger.info("测试在带宽限制下的分布式态射执行")
        
        # 创建参数化态射
        def parameterized_transform(x: np.ndarray, params: Dict[str, Any]) -> np.ndarray:
            """参数化变换：R2 -> R3"""
            # 获取权重矩阵和偏置向量
            weight_matrix = params.get("weight_matrix", np.zeros((3, 2)))
            bias_vector = params.get("bias_vector", np.zeros(3))
            # 应用变换
            return weight_matrix @ x + bias_vector
        
        # 创建大型参数
        large_params = {
            "weight_matrix": np.random.rand(3, 2),
            "bias_vector": np.random.rand(3),
            "large_data": np.random.rand(1000, 1000)  # 添加大型数据
        }
        
        parameterized_morphism = self.morphism_factory.create_parameterized_morphism(
            domain_id="R2",
            codomain_id="R3",
            morphism_id="parameterized_morphism",
            function=parameterized_transform,
            parameters=large_params,
            metadata={"description": "带大型参数的参数化变换"}
        )
        
        # 创建输入数据
        input_vector = np.array([1.0, 2.0])
        
        # 创建态射执行任务
        morphism_task = self.distributed_factory.create_task(
            task_id=f"morphism_task_{time.time()}",
            task_type="morphism_execution",
            source_node_id="master_node",
            target_node_id="node_1",
            priority=5,
            data={
                "morphism_id": parameterized_morphism.morphism_id,
                "input_data": input_vector.tolist(),
                "parameters": {
                    "weight_matrix": large_params["weight_matrix"].tolist(),
                    "bias_vector": large_params["bias_vector"].tolist()
                    # 不传输大型数据，模拟参数压缩
                }
            },
            metadata={"description": "带宽限制下的态射执行任务"}
        )
        
        # 提交任务
        self.task_scheduler.submit_task(morphism_task)
        
        # 分配任务
        self.task_scheduler.assign_task(morphism_task.task_id, "node_1")
        
        # 模拟节点执行任务
        updated_task = self.task_scheduler.get_task(morphism_task.task_id)
        
        # 从任务数据中提取参数
        task_params = {
            "weight_matrix": np.array(updated_task.data["parameters"]["weight_matrix"]),
            "bias_vector": np.array(updated_task.data["parameters"]["bias_vector"])
        }
        
        # 从任务数据中提取输入数据
        input_data = np.array(updated_task.data["input_data"])
        
        # 执行态射
        output_data = parameterized_transform(input_data, task_params)
        
        # 创建任务结果
        morphism_result = {
            "output_data": output_data.tolist(),
            "execution_time": 0.001,
            "status": "success"
        }
        
        # 完成任务
        self.task_scheduler.complete_task(morphism_task.task_id, morphism_result)
        
        # 获取完成的任务
        completed_morphism_task = self.task_scheduler.get_task(morphism_task.task_id)
        
        # 验证任务结果
        self.assertEqual(completed_morphism_task.status, TaskStatus.COMPLETED)
        morphism_result = completed_morphism_task.data["result"]
        self.assertEqual(morphism_result["status"], "success")


if __name__ == "__main__":
    unittest.main()
