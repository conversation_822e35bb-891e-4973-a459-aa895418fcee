#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
错误处理集成测试

本模块测试系统在各种错误情况下的处理能力。
"""

import os
import sys
import unittest
import numpy as np
import logging
import time
import json
from typing import Any, Dict, List, Optional, Tuple, Union

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

# 导入测试基类
from v2.tests.integration.boundary_conditions.test_base import BaseBoundaryConditionTest

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ErrorHandlingTest(BaseBoundaryConditionTest):
    """错误处理集成测试类"""
    
    def test_node_failure_handling(self):
        """测试节点故障处理"""
        logger.info("测试节点故障处理")
        
        # 创建输入数据
        input_vector = np.array([1.0, 2.0])
        
        # 创建态射执行任务
        task = self.distributed_factory.create_task(
            task_id=f"node_failure_task_{time.time()}",
            task_type="morphism_execution",
            source_node_id="master_node",
            target_node_id="node_0",
            priority=5,
            data={
                "morphism_id": self.linear_morphism.morphism_id,
                "input_data": input_vector.tolist()
            },
            metadata={"description": "节点故障测试任务"}
        )
        
        # 提交任务
        self.task_scheduler.submit_task(task)
        
        # 分配任务
        self.task_scheduler.assign_task(task.task_id, "node_0")
        
        # 模拟节点故障
        logger.info("模拟节点故障")
        self.simulate_node_failure(node_id="node_0", duration_sec=5.0)
        
        # 获取任务状态
        updated_task = self.task_scheduler.get_task(task.task_id)
        
        # 验证任务状态
        self.assertEqual(updated_task.status, TaskStatus.ASSIGNED)
        
        # 模拟故障检测
        logger.info("模拟故障检测")
        
        # 创建故障检测任务
        detection_task = self.distributed_factory.create_task(
            task_id=f"failure_detection_task_{time.time()}",
            task_type="node_health_check",
            source_node_id="master_node",
            target_node_id="master_node",
            priority=10,  # 高优先级
            data={},
            metadata={"description": "节点健康检查任务"}
        )
        
        # 提交任务
        self.task_scheduler.submit_task(detection_task)
        
        # 分配任务
        self.task_scheduler.assign_task(detection_task.task_id, "master_node")
        
        # 模拟执行故障检测
        node_statuses = {}
        for node_id, node in self.nodes.items():
            # 检查节点状态
            status = node.metadata.get("status", "unknown")
            last_heartbeat = node.metadata.get("last_heartbeat", 0)
            current_time = time.time()
            
            # 如果节点状态为failed或者心跳超时，标记为故障
            if status == "failed" or (current_time - last_heartbeat) > 30:
                node_statuses[node_id] = "failed"
            else:
                node_statuses[node_id] = "active"
        
        # 创建检测结果
        detection_result = {
            "node_statuses": node_statuses,
            "failed_nodes": [node_id for node_id, status in node_statuses.items() if status == "failed"],
            "active_nodes": [node_id for node_id, status in node_statuses.items() if status == "active"],
            "status": "success"
        }
        
        # 完成检测任务
        self.task_scheduler.complete_task(detection_task.task_id, detection_result)
        
        # 获取完成的检测任务
        completed_detection_task = self.task_scheduler.get_task(detection_task.task_id)
        
        # 验证检测结果
        self.assertEqual(completed_detection_task.status, TaskStatus.COMPLETED)
        detection_result = completed_detection_task.data["result"]
        self.assertIn("node_0", detection_result["failed_nodes"])
        
        # 模拟任务重新分配
        logger.info("模拟任务重新分配")
        
        # 获取可用节点
        available_nodes = detection_result["active_nodes"]
        
        # 如果有可用节点，重新分配任务
        if available_nodes:
            new_target_node = available_nodes[0]
            
            # 重新分配任务
            self.task_scheduler.reassign_task(task.task_id, new_target_node)
            
            # 获取更新后的任务
            reassigned_task = self.task_scheduler.get_task(task.task_id)
            
            # 验证任务已重新分配
            self.assertEqual(reassigned_task.target_node_id, new_target_node)
            self.assertEqual(reassigned_task.status, TaskStatus.ASSIGNED)
            
            logger.info(f"任务已重新分配到节点: {new_target_node}")
            
            # 模拟新节点执行任务
            input_data = np.array(reassigned_task.data["input_data"])
            output_data = self.linear_morphism.apply(input_data)
            
            # 创建任务结果
            task_result = {
                "output_data": output_data.tolist(),
                "execution_time": 0.001,
                "status": "success",
                "reassigned": True,
                "original_node": "node_0",
                "execution_node": new_target_node
            }
            
            # 完成任务
            self.task_scheduler.complete_task(task.task_id, task_result)
            
            # 获取完成的任务
            completed_task = self.task_scheduler.get_task(task.task_id)
            
            # 验证任务结果
            self.assertEqual(completed_task.status, TaskStatus.COMPLETED)
            result = completed_task.data["result"]
            self.assertEqual(result["status"], "success")
            self.assertTrue(result["reassigned"])
            
            logger.info(f"任务已成功完成，结果: {result}")
        else:
            logger.warning("没有可用节点进行任务重新分配")
    
    def test_data_corruption_handling(self):
        """测试数据损坏处理"""
        logger.info("测试数据损坏处理")
        
        # 创建输入数据
        input_vector = np.array([1.0, 2.0])
        
        # 创建态射执行任务
        task = self.distributed_factory.create_task(
            task_id=f"data_corruption_task_{time.time()}",
            task_type="morphism_execution",
            source_node_id="master_node",
            target_node_id="node_0",
            priority=5,
            data={
                "morphism_id": self.linear_morphism.morphism_id,
                "input_data": input_vector.tolist()
            },
            metadata={"description": "数据损坏测试任务"}
        )
        
        # 提交任务
        self.task_scheduler.submit_task(task)
        
        # 分配任务
        self.task_scheduler.assign_task(task.task_id, "node_0")
        
        # 获取更新后的任务
        updated_task = self.task_scheduler.get_task(task.task_id)
        
        # 模拟数据损坏
        logger.info("模拟数据损坏")
        corrupted_input_data = self.simulate_data_corruption(updated_task.data["input_data"])
        
        # 模拟节点尝试执行任务
        try:
            # 尝试将损坏的数据转换为numpy数组
            input_data = np.array(corrupted_input_data)
            
            # 尝试执行态射
            output_data = self.linear_morphism.apply(input_data)
            
            # 创建任务结果
            task_result = {
                "output_data": output_data.tolist(),
                "execution_time": 0.001,
                "status": "success"
            }
            
            # 完成任务
            self.task_scheduler.complete_task(task.task_id, task_result)
            
        except Exception as e:
            logger.error(f"执行任务时发生错误: {e}")
            
            # 创建错误结果
            error_result = {
                "error": str(e),
                "error_type": type(e).__name__,
                "status": "error",
                "data_corruption": True
            }
            
            # 完成任务，但标记为错误
            self.task_scheduler.complete_task(task.task_id, error_result)
        
        # 获取完成的任务
        completed_task = self.task_scheduler.get_task(task.task_id)
        
        # 验证任务状态
        self.assertEqual(completed_task.status, TaskStatus.COMPLETED)
        
        # 获取任务结果
        result = completed_task.data["result"]
        
        # 检查是否检测到数据损坏
        if result["status"] == "error":
            logger.info(f"检测到数据损坏: {result['error']}")
            
            # 模拟数据恢复
            logger.info("模拟数据恢复")
            
            # 创建数据恢复任务
            recovery_task = self.distributed_factory.create_task(
                task_id=f"data_recovery_task_{time.time()}",
                task_type="data_recovery",
                source_node_id="master_node",
                target_node_id="node_0",
                priority=10,  # 高优先级
                data={
                    "original_task_id": task.task_id,
                    "original_data": {
                        "morphism_id": self.linear_morphism.morphism_id,
                        "input_data": input_vector.tolist()
                    }
                },
                metadata={"description": "数据恢复任务"}
            )
            
            # 提交恢复任务
            self.task_scheduler.submit_task(recovery_task)
            
            # 分配恢复任务
            self.task_scheduler.assign_task(recovery_task.task_id, "node_0")
            
            # 模拟执行恢复任务
            recovery_result = {
                "recovered": True,
                "original_task_id": task.task_id,
                "status": "success"
            }
            
            # 完成恢复任务
            self.task_scheduler.complete_task(recovery_task.task_id, recovery_result)
            
            # 获取完成的恢复任务
            completed_recovery_task = self.task_scheduler.get_task(recovery_task.task_id)
            
            # 验证恢复任务结果
            self.assertEqual(completed_recovery_task.status, TaskStatus.COMPLETED)
            recovery_result = completed_recovery_task.data["result"]
            self.assertEqual(recovery_result["status"], "success")
            self.assertTrue(recovery_result["recovered"])
            
            # 重新创建原始任务
            new_task = self.distributed_factory.create_task(
                task_id=f"recovered_task_{time.time()}",
                task_type="morphism_execution",
                source_node_id="master_node",
                target_node_id="node_0",
                priority=5,
                data={
                    "morphism_id": self.linear_morphism.morphism_id,
                    "input_data": input_vector.tolist(),
                    "recovered": True,
                    "original_task_id": task.task_id
                },
                metadata={"description": "恢复后的任务"}
            )
            
            # 提交新任务
            self.task_scheduler.submit_task(new_task)
            
            # 分配新任务
            self.task_scheduler.assign_task(new_task.task_id, "node_0")
            
            # 模拟执行新任务
            input_data = np.array(input_vector)
            output_data = self.linear_morphism.apply(input_data)
            
            # 创建任务结果
            new_task_result = {
                "output_data": output_data.tolist(),
                "execution_time": 0.001,
                "status": "success",
                "recovered": True,
                "original_task_id": task.task_id
            }
            
            # 完成新任务
            self.task_scheduler.complete_task(new_task.task_id, new_task_result)
            
            # 获取完成的新任务
            completed_new_task = self.task_scheduler.get_task(new_task.task_id)
            
            # 验证新任务结果
            self.assertEqual(completed_new_task.status, TaskStatus.COMPLETED)
            new_result = completed_new_task.data["result"]
            self.assertEqual(new_result["status"], "success")
            self.assertTrue(new_result["recovered"])
            
            logger.info(f"恢复后的任务已成功完成，结果: {new_result}")
        else:
            logger.info("任务成功执行，未检测到数据损坏")
    
    def test_communication_error_handling(self):
        """测试通信错误处理"""
        logger.info("测试通信错误处理")
        
        # 创建消息
        message = self.distributed_factory.create_message(
            message_id=f"test_message_{time.time()}",
            source_node_id="master_node",
            target_node_id="node_0",
            message_type=MessageType.TASK_ASSIGNMENT,
            content={"task_id": f"test_task_{time.time()}"},
            metadata={"description": "测试消息"}
        )
        
        # 模拟网络故障
        logger.info("模拟网络故障")
        self.simulate_network_failure(node_id="node_0", duration_sec=5.0)
        
        # 尝试发送消息
        logger.info("尝试发送消息")
        send_result = self.distributed_manager.send_message(message)
        
        # 验证发送结果
        self.assertTrue(send_result, "消息应该被接受发送，即使目标节点不可用")
        
        # 获取发送的消息
        sent_messages = self.distributed_manager.get_sent_messages()
        self.assertIn(message.message_id, [msg.message_id for msg in sent_messages])
        
        # 模拟消息重试机制
        logger.info("模拟消息重试机制")
        
        # 创建重试任务
        retry_task = self.distributed_factory.create_task(
            task_id=f"message_retry_task_{time.time()}",
            task_type="message_retry",
            source_node_id="master_node",
            target_node_id="master_node",
            priority=10,  # 高优先级
            data={
                "message_id": message.message_id,
                "retry_count": 0,
                "max_retries": 3,
                "retry_interval": 1.0  # 1秒重试间隔
            },
            metadata={"description": "消息重试任务"}
        )
        
        # 提交重试任务
        self.task_scheduler.submit_task(retry_task)
        
        # 分配重试任务
        self.task_scheduler.assign_task(retry_task.task_id, "master_node")
        
        # 模拟执行重试任务
        max_retries = retry_task.data["max_retries"]
        retry_interval = retry_task.data["retry_interval"]
        
        # 模拟重试过程
        for retry_count in range(max_retries):
            logger.info(f"尝试重试 {retry_count + 1}/{max_retries}")
            
            # 检查目标节点是否可用
            node_status = self.nodes["node_0"].metadata.get("status", "unknown")
            
            if node_status == "active":
                logger.info("目标节点已恢复，重新发送消息")
                
                # 重新发送消息
                resend_result = self.distributed_manager.send_message(message)
                
                # 验证重新发送结果
                self.assertTrue(resend_result)
                
                # 创建重试结果
                retry_result = {
                    "message_id": message.message_id,
                    "retry_count": retry_count + 1,
                    "success": True,
                    "status": "success"
                }
                
                # 完成重试任务
                self.task_scheduler.complete_task(retry_task.task_id, retry_result)
                break
            else:
                logger.info(f"目标节点仍不可用，等待 {retry_interval} 秒后重试")
                
                # 等待重试间隔
                time.sleep(retry_interval)
        else:
            # 达到最大重试次数
            logger.warning(f"达到最大重试次数 {max_retries}，放弃发送消息")
            
            # 创建重试结果
            retry_result = {
                "message_id": message.message_id,
                "retry_count": max_retries,
                "success": False,
                "status": "failed"
            }
            
            # 完成重试任务
            self.task_scheduler.complete_task(retry_task.task_id, retry_result)
        
        # 获取完成的重试任务
        completed_retry_task = self.task_scheduler.get_task(retry_task.task_id)
        
        # 验证重试任务结果
        self.assertEqual(completed_retry_task.status, TaskStatus.COMPLETED)
        retry_result = completed_retry_task.data["result"]
        
        if retry_result["success"]:
            logger.info(f"消息重试成功，重试次数: {retry_result['retry_count']}")
        else:
            logger.warning(f"消息重试失败，重试次数: {retry_result['retry_count']}")
    
    def test_exception_handling(self):
        """测试异常处理"""
        logger.info("测试异常处理")
        
        # 创建会抛出异常的态射
        def exception_morphism(x: np.ndarray) -> np.ndarray:
            """抛出异常的态射"""
            if np.any(x < 0):
                raise ValueError("输入包含负数")
            elif np.any(x > 10):
                raise ValueError("输入超过上限")
            elif np.any(np.isnan(x)):
                raise ValueError("输入包含NaN")
            return x * 2
        
        # 创建异常态射
        exception_morphism_obj = self.morphism_factory.create_nonlinear_morphism(
            domain_id="R2",
            codomain_id="R2",
            morphism_id="exception_morphism",
            function=exception_morphism,
            metadata={"description": "抛出异常的态射"}
        )
        
        # 测试不同的异常情况
        test_cases = [
            {"name": "正常输入", "input": np.array([1.0, 2.0]), "expect_exception": False},
            {"name": "负数输入", "input": np.array([-1.0, 2.0]), "expect_exception": True},
            {"name": "超过上限", "input": np.array([5.0, 15.0]), "expect_exception": True},
            {"name": "包含NaN", "input": np.array([1.0, np.nan]), "expect_exception": True}
        ]
        
        for test_case in test_cases:
            logger.info(f"测试: {test_case['name']}")
            
            # 创建任务
            task = self.distributed_factory.create_task(
                task_id=f"exception_task_{time.time()}",
                task_type="morphism_execution",
                source_node_id="master_node",
                target_node_id="node_0",
                priority=5,
                data={
                    "morphism_id": exception_morphism_obj.morphism_id,
                    "input_data": test_case["input"].tolist()
                },
                metadata={"description": f"异常测试任务: {test_case['name']}"}
            )
            
            # 提交任务
            self.task_scheduler.submit_task(task)
            
            # 分配任务
            self.task_scheduler.assign_task(task.task_id, "node_0")
            
            # 获取更新后的任务
            updated_task = self.task_scheduler.get_task(task.task_id)
            
            # 模拟节点执行任务
            try:
                # 获取输入数据
                input_data = np.array(updated_task.data["input_data"])
                
                # 执行态射
                output_data = exception_morphism_obj.apply(input_data)
                
                # 创建任务结果
                task_result = {
                    "output_data": output_data.tolist(),
                    "execution_time": 0.001,
                    "status": "success"
                }
                
                # 完成任务
                self.task_scheduler.complete_task(task.task_id, task_result)
                
                # 如果期望异常但没有抛出，标记测试失败
                if test_case["expect_exception"]:
                    self.fail(f"期望异常但没有抛出: {test_case['name']}")
                
            except Exception as e:
                logger.error(f"执行任务时发生错误: {e}")
                
                # 创建错误结果
                error_result = {
                    "error": str(e),
                    "error_type": type(e).__name__,
                    "status": "error"
                }
                
                # 完成任务，但标记为错误
                self.task_scheduler.complete_task(task.task_id, error_result)
                
                # 如果不期望异常但抛出了，标记测试失败
                if not test_case["expect_exception"]:
                    self.fail(f"不期望异常但抛出了: {test_case['name']}, 异常: {e}")
            
            # 获取完成的任务
            completed_task = self.task_scheduler.get_task(task.task_id)
            
            # 验证任务状态
            self.assertEqual(completed_task.status, TaskStatus.COMPLETED)
            
            # 获取任务结果
            result = completed_task.data["result"]
            
            # 验证结果状态
            if test_case["expect_exception"]:
                self.assertEqual(result["status"], "error")
                logger.info(f"预期异常已捕获: {result['error']}")
            else:
                self.assertEqual(result["status"], "success")
                logger.info("任务成功执行，无异常")


if __name__ == "__main__":
    unittest.main()
