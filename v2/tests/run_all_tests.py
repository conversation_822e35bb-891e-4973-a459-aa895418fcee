#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
超融态思维引擎测试运行器

本模块用于运行所有测试，包括单元测试、集成测试、性能测试和稳定性测试。
"""

import os
import sys
import unittest
import logging
import argparse
import time
import json
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def discover_tests(test_dir: str) -> unittest.TestSuite:
    """发现指定目录下的所有测试
    
    Args:
        test_dir: 测试目录
    
    Returns:
        包含所有测试的测试套件
    """
    logger.info(f"发现测试: {test_dir}")
    return unittest.defaultTestLoader.discover(test_dir, pattern="test_*.py")


def run_tests(test_suite: unittest.TestSuite, test_type: str) -> Dict[str, Any]:
    """运行测试套件
    
    Args:
        test_suite: 测试套件
        test_type: 测试类型
    
    Returns:
        测试结果统计
    """
    logger.info(f"运行 {test_type} 测试")
    
    # 创建测试运行器
    test_runner = unittest.TextTestRunner(verbosity=2)
    
    # 记录开始时间
    start_time = time.time()
    
    # 运行测试
    test_result = test_runner.run(test_suite)
    
    # 记录结束时间
    end_time = time.time()
    test_duration = end_time - start_time
    
    # 统计测试结果
    stats = {
        "test_type": test_type,
        "total": test_result.testsRun,
        "success": test_result.testsRun - len(test_result.errors) - len(test_result.failures),
        "failures": len(test_result.failures),
        "errors": len(test_result.errors),
        "skipped": len(test_result.skipped) if hasattr(test_result, "skipped") else 0,
        "duration": test_duration,
        "success_rate": (test_result.testsRun - len(test_result.errors) - len(test_result.failures)) / test_result.testsRun if test_result.testsRun > 0 else 0
    }
    
    # 输出测试结果
    logger.info(f"{test_type} 测试结果:")
    logger.info(f"  总测试数: {stats['total']}")
    logger.info(f"  成功: {stats['success']}")
    logger.info(f"  失败: {stats['failures']}")
    logger.info(f"  错误: {stats['errors']}")
    logger.info(f"  跳过: {stats['skipped']}")
    logger.info(f"  持续时间: {stats['duration']:.2f} 秒")
    logger.info(f"  成功率: {stats['success_rate']:.2%}")
    
    return stats


def main():
    """主函数"""
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='运行超融态思维引擎测试')
    parser.add_argument('--unit', action='store_true', help='运行单元测试')
    parser.add_argument('--integration', action='store_true', help='运行集成测试')
    parser.add_argument('--performance', action='store_true', help='运行性能测试')
    parser.add_argument('--stability', action='store_true', help='运行稳定性测试')
    parser.add_argument('--all', action='store_true', help='运行所有测试')
    parser.add_argument('--output', type=str, help='测试结果输出文件')
    parser.add_argument('--stability-duration-factor', type=float, default=0.1, help='稳定性测试持续时间因子')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 如果没有指定测试类型，默认运行所有测试
    if not (args.unit or args.integration or args.performance or args.stability):
        args.all = True
    
    # 如果指定了运行所有测试，设置所有测试类型为True
    if args.all:
        args.unit = True
        args.integration = True
        args.performance = True
        args.stability = True
    
    # 记录测试结果
    test_results = []
    
    # 运行单元测试
    if args.unit:
        unit_test_suite = discover_tests("v2/tests/unit")
        unit_test_stats = run_tests(unit_test_suite, "单元")
        test_results.append(unit_test_stats)
    
    # 运行集成测试
    if args.integration:
        # 运行态射系统与元认知系统集成测试
        morphism_metacognition_test_suite = discover_tests("v2/tests/integration/morphism_metacognition")
        morphism_metacognition_stats = run_tests(morphism_metacognition_test_suite, "态射系统与元认知系统集成")
        test_results.append(morphism_metacognition_stats)
        
        # 运行元认知系统与分布式系统集成测试
        metacognition_distributed_test_suite = discover_tests("v2/tests/integration/metacognition_distributed")
        metacognition_distributed_stats = run_tests(metacognition_distributed_test_suite, "元认知系统与分布式系统集成")
        test_results.append(metacognition_distributed_stats)
        
        # 运行态射系统与分布式系统集成测试
        morphism_distributed_test_suite = discover_tests("v2/tests/integration/morphism_distributed")
        morphism_distributed_stats = run_tests(morphism_distributed_test_suite, "态射系统与分布式系统集成")
        test_results.append(morphism_distributed_stats)
        
        # 运行边界条件和异常情况测试
        boundary_conditions_test_suite = discover_tests("v2/tests/integration/boundary_conditions")
        boundary_conditions_stats = run_tests(boundary_conditions_test_suite, "边界条件和异常情况")
        test_results.append(boundary_conditions_stats)
    
    # 运行性能测试
    if args.performance:
        performance_test_suite = discover_tests("v2/tests/performance")
        performance_stats = run_tests(performance_test_suite, "性能")
        test_results.append(performance_stats)
    
    # 运行稳定性测试
    if args.stability:
        # 导入稳定性测试运行器
        sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), 'integration/stability')))
        from run_tests import run_tests as run_stability_tests
        
        # 运行持续负载测试
        logger.info("运行持续负载测试")
        continuous_load_success = run_stability_tests(
            test_type='continuous_load',
            duration_factor=args.stability_duration_factor
        )
        
        # 运行长时间运行测试
        logger.info("运行长时间运行测试")
        long_running_success = run_stability_tests(
            test_type='long_running',
            duration_factor=args.stability_duration_factor
        )
        
        # 统计稳定性测试结果
        stability_stats = {
            "test_type": "稳定性",
            "total": 2,
            "success": (1 if continuous_load_success else 0) + (1 if long_running_success else 0),
            "failures": (0 if continuous_load_success else 1) + (0 if long_running_success else 1),
            "errors": 0,
            "skipped": 0,
            "duration": 0,  # 无法准确获取持续时间
            "success_rate": ((1 if continuous_load_success else 0) + (1 if long_running_success else 0)) / 2
        }
        
        test_results.append(stability_stats)
    
    # 计算总体测试结果
    total_tests = sum(result["total"] for result in test_results)
    total_success = sum(result["success"] for result in test_results)
    total_failures = sum(result["failures"] for result in test_results)
    total_errors = sum(result["errors"] for result in test_results)
    total_skipped = sum(result["skipped"] for result in test_results)
    total_duration = sum(result["duration"] for result in test_results)
    total_success_rate = total_success / total_tests if total_tests > 0 else 0
    
    # 输出总体测试结果
    logger.info("总体测试结果:")
    logger.info(f"  总测试数: {total_tests}")
    logger.info(f"  成功: {total_success}")
    logger.info(f"  失败: {total_failures}")
    logger.info(f"  错误: {total_errors}")
    logger.info(f"  跳过: {total_skipped}")
    logger.info(f"  持续时间: {total_duration:.2f} 秒")
    logger.info(f"  成功率: {total_success_rate:.2%}")
    
    # 如果指定了输出文件，将测试结果保存到文件
    if args.output:
        # 创建测试结果报告
        report = {
            "timestamp": time.time(),
            "date": time.strftime("%Y-%m-%d %H:%M:%S"),
            "results": test_results,
            "summary": {
                "total_tests": total_tests,
                "total_success": total_success,
                "total_failures": total_failures,
                "total_errors": total_errors,
                "total_skipped": total_skipped,
                "total_duration": total_duration,
                "total_success_rate": total_success_rate
            }
        }
        
        # 保存测试结果报告
        with open(args.output, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"测试结果已保存到: {args.output}")
    
    # 设置退出码
    if total_failures > 0 or total_errors > 0:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()
