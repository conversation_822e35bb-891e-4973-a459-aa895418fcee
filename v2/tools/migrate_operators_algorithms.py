#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
迁移算子和算法

本脚本用于将TCT目录中的算子和算法迁移到TTE目录，并确保它们已正确注册。
"""

import os
import sys
import shutil
import importlib
import inspect
import logging
import argparse
from typing import Dict, List, Any, Optional, Set, Tuple, Union, Callable

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 目录路径
TCT_DIR = "/home/<USER>/CascadeProjects/TCT"
TTE_DIR = "/home/<USER>/CascadeProjects/TTE"


def find_operator_directories(base_dir: str) -> List[str]:
    """查找算子目录

    Args:
        base_dir: 基础目录

    Returns:
        算子目录列表
    """
    operator_dirs = []
    
    for root, dirs, files in os.walk(base_dir):
        for dir_name in dirs:
            if dir_name == "operators":
                operator_dirs.append(os.path.join(root, dir_name))
    
    return operator_dirs


def find_algorithm_directories(base_dir: str) -> List[str]:
    """查找算法目录

    Args:
        base_dir: 基础目录

    Returns:
        算法目录列表
    """
    algorithm_dirs = []
    
    for root, dirs, files in os.walk(base_dir):
        for dir_name in dirs:
            if dir_name == "algorithms":
                algorithm_dirs.append(os.path.join(root, dir_name))
    
    return algorithm_dirs


def migrate_directory(source_dir: str, target_dir: str, overwrite: bool = False) -> Tuple[int, int, List[str]]:
    """迁移目录

    Args:
        source_dir: 源目录
        target_dir: 目标目录
        overwrite: 是否覆盖已存在的文件

    Returns:
        迁移的文件数、跳过的文件数和错误列表
    """
    logger.info(f"迁移目录: {source_dir} -> {target_dir}")
    
    # 创建目标目录
    os.makedirs(target_dir, exist_ok=True)
    
    # 统计
    migrated_count = 0
    skipped_count = 0
    errors = []
    
    # 遍历源目录
    for root, dirs, files in os.walk(source_dir):
        # 计算相对路径
        rel_path = os.path.relpath(root, source_dir)
        
        # 创建目标目录
        target_path = os.path.join(target_dir, rel_path)
        os.makedirs(target_path, exist_ok=True)
        
        # 复制文件
        for file in files:
            source_file = os.path.join(root, file)
            target_file = os.path.join(target_path, file)
            
            try:
                # 检查目标文件是否存在
                if os.path.exists(target_file) and not overwrite:
                    logger.warning(f"跳过已存在的文件: {target_file}")
                    skipped_count += 1
                    continue
                
                # 复制文件
                shutil.copy2(source_file, target_file)
                logger.info(f"已复制: {source_file} -> {target_file}")
                migrated_count += 1
            
            except Exception as e:
                error_msg = f"复制文件 {source_file} 失败: {e}"
                logger.error(error_msg)
                errors.append(error_msg)
    
    return migrated_count, skipped_count, errors


def main():
    """主函数"""
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='迁移算子和算法')
    parser.add_argument('--overwrite', action='store_true', help='覆盖已存在的文件')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 查找TCT目录中的算子目录
    tct_operator_dirs = find_operator_directories(TCT_DIR)
    logger.info(f"在TCT目录中找到 {len(tct_operator_dirs)} 个算子目录")
    
    # 查找TCT目录中的算法目录
    tct_algorithm_dirs = find_algorithm_directories(TCT_DIR)
    logger.info(f"在TCT目录中找到 {len(tct_algorithm_dirs)} 个算法目录")
    
    # 查找TTE目录中的算子目录
    tte_operator_dirs = find_operator_directories(TTE_DIR)
    logger.info(f"在TTE目录中找到 {len(tte_operator_dirs)} 个算子目录")
    
    # 查找TTE目录中的算法目录
    tte_algorithm_dirs = find_algorithm_directories(TTE_DIR)
    logger.info(f"在TTE目录中找到 {len(tte_algorithm_dirs)} 个算法目录")
    
    # 统计
    total_migrated_files = 0
    total_skipped_files = 0
    total_errors = []
    
    # 迁移算子
    for source_dir in tct_operator_dirs:
        # 计算目标目录
        rel_path = os.path.relpath(source_dir, TCT_DIR)
        target_dir = os.path.join(TTE_DIR, rel_path)
        
        # 检查目标目录是否已存在
        if os.path.exists(target_dir) and not args.overwrite:
            logger.warning(f"跳过已存在的目标目录: {target_dir}")
            continue
        
        # 迁移目录
        migrated_count, skipped_count, errors = migrate_directory(source_dir, target_dir, args.overwrite)
        
        # 更新统计
        total_migrated_files += migrated_count
        total_skipped_files += skipped_count
        total_errors.extend(errors)
    
    # 迁移算法
    for source_dir in tct_algorithm_dirs:
        # 计算目标目录
        rel_path = os.path.relpath(source_dir, TCT_DIR)
        target_dir = os.path.join(TTE_DIR, rel_path)
        
        # 检查目标目录是否已存在
        if os.path.exists(target_dir) and not args.overwrite:
            logger.warning(f"跳过已存在的目标目录: {target_dir}")
            continue
        
        # 迁移目录
        migrated_count, skipped_count, errors = migrate_directory(source_dir, target_dir, args.overwrite)
        
        # 更新统计
        total_migrated_files += migrated_count
        total_skipped_files += skipped_count
        total_errors.extend(errors)
    
    # 输出结果
    logger.info(f"\n迁移完成！")
    logger.info(f"迁移的文件数: {total_migrated_files}")
    logger.info(f"跳过的文件数: {total_skipped_files}")
    logger.info(f"错误数: {len(total_errors)}")
    
    # 输出错误
    if total_errors:
        logger.info("\n错误列表:")
        for error in total_errors:
            logger.info(f"  - {error}")
    
    # 提示用户运行检查注册状态的脚本
    logger.info("\n请运行以下命令检查算子和算法的注册状态:")
    logger.info("  python v2/tools/check_registrations.py --dir /home/<USER>/CascadeProjects/TTE")
    logger.info("如果需要注册未注册的算子和算法，请添加 --register 参数:")
    logger.info("  python v2/tools/check_registrations.py --dir /home/<USER>/CascadeProjects/TTE --register")


if __name__ == "__main__":
    main()
