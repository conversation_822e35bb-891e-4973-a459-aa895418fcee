#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
系统监控工具

本模块提供了实时监控系统运行状态的功能，包括资源使用情况、任务执行情况和节点状态。
"""

import os
import sys
import argparse
import logging
import time
import json
import threading
import queue
import psutil
import matplotlib.pyplot as plt
import numpy as np
import tkinter as tk
from tkinter import ttk
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from typing import Dict, List, Any, Optional, Tuple

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 导入系统模块
try:
    from src.core.distributed import DistributedSystemFactory, DistributedManager
except ImportError:
    # 如果无法导入，创建模拟类
    class DistributedSystemFactory:
        @staticmethod
        def get_instance():
            return DistributedSystemFactory()
        
        def get_nodes(self):
            return {}
    
    class DistributedManager:
        @staticmethod
        def get_instance():
            return DistributedManager()
        
        def get_tasks(self):
            return []
        
        def get_messages(self):
            return []

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SystemMonitor:
    """系统监控类"""
    
    def __init__(self, update_interval: float = 1.0):
        """初始化系统监控
        
        Args:
            update_interval: 更新间隔（秒）
        """
        self.update_interval = update_interval
        self.stop_monitoring = False
        self.data_queue = queue.Queue()
        
        # 创建分布式系统工厂
        try:
            self.distributed_factory = DistributedSystemFactory.get_instance()
            self.distributed_manager = DistributedManager.get_instance()
        except Exception as e:
            logger.warning(f"无法创建分布式系统工厂: {e}")
            self.distributed_factory = None
            self.distributed_manager = None
        
        # 初始化数据存储
        self.system_data = {
            "timestamp": [],
            "cpu_percent": [],
            "memory_percent": [],
            "disk_percent": [],
            "network_sent": [],
            "network_recv": [],
            "task_count": [],
            "active_nodes": [],
            "message_count": []
        }
        
        # 初始化网络计数器
        self.last_net_io = psutil.net_io_counters()
        self.last_net_time = time.time()
    
    def start_monitoring(self):
        """开始监控"""
        logger.info("开始系统监控")
        
        # 创建监控线程
        self.monitor_thread = threading.Thread(target=self._monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        logger.info("停止系统监控")
        self.stop_monitoring = True
        
        if self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=1.0)
    
    def _monitor_loop(self):
        """监控循环"""
        while not self.stop_monitoring:
            try:
                # 收集系统数据
                system_data = self._collect_system_data()
                
                # 将数据放入队列
                self.data_queue.put(system_data)
                
                # 更新数据存储
                self._update_data_storage(system_data)
                
                # 等待下一次更新
                time.sleep(self.update_interval)
            
            except Exception as e:
                logger.error(f"监控循环发生错误: {e}")
    
    def _collect_system_data(self) -> Dict[str, Any]:
        """收集系统数据
        
        Returns:
            系统数据
        """
        # 获取当前时间
        current_time = time.time()
        
        # 获取CPU使用率
        cpu_percent = psutil.cpu_percent()
        
        # 获取内存使用率
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        
        # 获取磁盘使用率
        disk = psutil.disk_usage('/')
        disk_percent = disk.percent
        
        # 获取网络IO
        net_io = psutil.net_io_counters()
        net_time_diff = current_time - self.last_net_time
        
        if net_time_diff > 0:
            # 计算网络发送和接收速率（字节/秒）
            network_sent = (net_io.bytes_sent - self.last_net_io.bytes_sent) / net_time_diff
            network_recv = (net_io.bytes_recv - self.last_net_io.bytes_recv) / net_time_diff
        else:
            network_sent = 0
            network_recv = 0
        
        # 更新网络计数器
        self.last_net_io = net_io
        self.last_net_time = current_time
        
        # 获取任务数量
        task_count = 0
        if self.distributed_manager:
            try:
                tasks = self.distributed_manager.get_tasks()
                task_count = len(tasks)
            except Exception as e:
                logger.warning(f"获取任务数量时发生错误: {e}")
        
        # 获取活跃节点数量
        active_nodes = 0
        if self.distributed_factory:
            try:
                nodes = self.distributed_factory.get_nodes()
                active_nodes = sum(1 for node in nodes.values() if node.metadata.get("status") == "active")
            except Exception as e:
                logger.warning(f"获取活跃节点数量时发生错误: {e}")
        
        # 获取消息数量
        message_count = 0
        if self.distributed_manager:
            try:
                messages = self.distributed_manager.get_messages()
                message_count = len(messages)
            except Exception as e:
                logger.warning(f"获取消息数量时发生错误: {e}")
        
        # 创建系统数据
        system_data = {
            "timestamp": current_time,
            "cpu_percent": cpu_percent,
            "memory_percent": memory_percent,
            "disk_percent": disk_percent,
            "network_sent": network_sent,
            "network_recv": network_recv,
            "task_count": task_count,
            "active_nodes": active_nodes,
            "message_count": message_count
        }
        
        return system_data
    
    def _update_data_storage(self, system_data: Dict[str, Any]):
        """更新数据存储
        
        Args:
            system_data: 系统数据
        """
        # 更新数据存储
        for key, value in system_data.items():
            if key in self.system_data:
                self.system_data[key].append(value)
        
        # 限制数据存储大小
        max_data_points = 100
        for key in self.system_data:
            if len(self.system_data[key]) > max_data_points:
                self.system_data[key] = self.system_data[key][-max_data_points:]
    
    def get_latest_data(self) -> Dict[str, Any]:
        """获取最新数据
        
        Returns:
            最新数据
        """
        try:
            # 从队列获取最新数据
            return self.data_queue.get_nowait()
        except queue.Empty:
            # 如果队列为空，返回空数据
            return {}
    
    def get_data_history(self) -> Dict[str, List[Any]]:
        """获取数据历史
        
        Returns:
            数据历史
        """
        return self.system_data


class SystemMonitorGUI:
    """系统监控GUI"""
    
    def __init__(self, monitor: SystemMonitor, update_interval: float = 1.0):
        """初始化系统监控GUI
        
        Args:
            monitor: 系统监控对象
            update_interval: 更新间隔（秒）
        """
        self.monitor = monitor
        self.update_interval = update_interval
        
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("超融态思维引擎系统监控")
        self.root.geometry("1200x800")
        
        # 创建标签页
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建系统资源标签页
        self.system_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.system_frame, text="系统资源")
        
        # 创建任务标签页
        self.task_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.task_frame, text="任务")
        
        # 创建节点标签页
        self.node_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.node_frame, text="节点")
        
        # 创建系统资源图表
        self._create_system_charts()
        
        # 创建任务图表
        self._create_task_charts()
        
        # 创建节点图表
        self._create_node_charts()
        
        # 创建状态栏
        self.status_bar = tk.Label(self.root, text="系统监控已启动", bd=1, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 启动更新循环
        self.root.after(int(self.update_interval * 1000), self._update_gui)
    
    def _create_system_charts(self):
        """创建系统资源图表"""
        # 创建图表
        self.system_fig, self.system_axes = plt.subplots(2, 2, figsize=(10, 8))
        self.system_fig.tight_layout(pad=3.0)
        
        # CPU使用率图表
        self.cpu_line, = self.system_axes[0, 0].plot([], [], 'b-', label='CPU使用率')
        self.system_axes[0, 0].set_title('CPU使用率')
        self.system_axes[0, 0].set_xlabel('时间')
        self.system_axes[0, 0].set_ylabel('使用率 (%)')
        self.system_axes[0, 0].set_ylim(0, 100)
        self.system_axes[0, 0].grid(True)
        self.system_axes[0, 0].legend()
        
        # 内存使用率图表
        self.memory_line, = self.system_axes[0, 1].plot([], [], 'g-', label='内存使用率')
        self.system_axes[0, 1].set_title('内存使用率')
        self.system_axes[0, 1].set_xlabel('时间')
        self.system_axes[0, 1].set_ylabel('使用率 (%)')
        self.system_axes[0, 1].set_ylim(0, 100)
        self.system_axes[0, 1].grid(True)
        self.system_axes[0, 1].legend()
        
        # 磁盘使用率图表
        self.disk_line, = self.system_axes[1, 0].plot([], [], 'r-', label='磁盘使用率')
        self.system_axes[1, 0].set_title('磁盘使用率')
        self.system_axes[1, 0].set_xlabel('时间')
        self.system_axes[1, 0].set_ylabel('使用率 (%)')
        self.system_axes[1, 0].set_ylim(0, 100)
        self.system_axes[1, 0].grid(True)
        self.system_axes[1, 0].legend()
        
        # 网络使用率图表
        self.network_sent_line, = self.system_axes[1, 1].plot([], [], 'c-', label='发送')
        self.network_recv_line, = self.system_axes[1, 1].plot([], [], 'm-', label='接收')
        self.system_axes[1, 1].set_title('网络使用率')
        self.system_axes[1, 1].set_xlabel('时间')
        self.system_axes[1, 1].set_ylabel('速率 (字节/秒)')
        self.system_axes[1, 1].grid(True)
        self.system_axes[1, 1].legend()
        
        # 创建画布
        self.system_canvas = FigureCanvasTkAgg(self.system_fig, master=self.system_frame)
        self.system_canvas.draw()
        self.system_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def _create_task_charts(self):
        """创建任务图表"""
        # 创建图表
        self.task_fig, self.task_axes = plt.subplots(2, 1, figsize=(10, 8))
        self.task_fig.tight_layout(pad=3.0)
        
        # 任务数量图表
        self.task_count_line, = self.task_axes[0].plot([], [], 'b-', label='任务数量')
        self.task_axes[0].set_title('任务数量')
        self.task_axes[0].set_xlabel('时间')
        self.task_axes[0].set_ylabel('数量')
        self.task_axes[0].grid(True)
        self.task_axes[0].legend()
        
        # 消息数量图表
        self.message_count_line, = self.task_axes[1].plot([], [], 'g-', label='消息数量')
        self.task_axes[1].set_title('消息数量')
        self.task_axes[1].set_xlabel('时间')
        self.task_axes[1].set_ylabel('数量')
        self.task_axes[1].grid(True)
        self.task_axes[1].legend()
        
        # 创建画布
        self.task_canvas = FigureCanvasTkAgg(self.task_fig, master=self.task_frame)
        self.task_canvas.draw()
        self.task_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def _create_node_charts(self):
        """创建节点图表"""
        # 创建图表
        self.node_fig, self.node_axes = plt.subplots(1, 1, figsize=(10, 8))
        self.node_fig.tight_layout(pad=3.0)
        
        # 活跃节点数量图表
        self.active_nodes_line, = self.node_axes.plot([], [], 'b-', label='活跃节点数量')
        self.node_axes.set_title('活跃节点数量')
        self.node_axes.set_xlabel('时间')
        self.node_axes.set_ylabel('数量')
        self.node_axes.grid(True)
        self.node_axes.legend()
        
        # 创建画布
        self.node_canvas = FigureCanvasTkAgg(self.node_fig, master=self.node_frame)
        self.node_canvas.draw()
        self.node_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def _update_gui(self):
        """更新GUI"""
        try:
            # 获取数据历史
            data = self.monitor.get_data_history()
            
            # 如果没有数据，等待下一次更新
            if not data or not data["timestamp"]:
                self.root.after(int(self.update_interval * 1000), self._update_gui)
                return
            
            # 创建时间轴（相对时间，以秒为单位）
            if len(data["timestamp"]) > 1:
                time_axis = [t - data["timestamp"][0] for t in data["timestamp"]]
            else:
                time_axis = [0]
            
            # 更新CPU使用率图表
            self.cpu_line.set_data(time_axis, data["cpu_percent"])
            self.system_axes[0, 0].relim()
            self.system_axes[0, 0].autoscale_view()
            
            # 更新内存使用率图表
            self.memory_line.set_data(time_axis, data["memory_percent"])
            self.system_axes[0, 1].relim()
            self.system_axes[0, 1].autoscale_view()
            
            # 更新磁盘使用率图表
            self.disk_line.set_data(time_axis, data["disk_percent"])
            self.system_axes[1, 0].relim()
            self.system_axes[1, 0].autoscale_view()
            
            # 更新网络使用率图表
            self.network_sent_line.set_data(time_axis, data["network_sent"])
            self.network_recv_line.set_data(time_axis, data["network_recv"])
            self.system_axes[1, 1].relim()
            self.system_axes[1, 1].autoscale_view()
            
            # 更新任务数量图表
            self.task_count_line.set_data(time_axis, data["task_count"])
            self.task_axes[0].relim()
            self.task_axes[0].autoscale_view()
            
            # 更新消息数量图表
            self.message_count_line.set_data(time_axis, data["message_count"])
            self.task_axes[1].relim()
            self.task_axes[1].autoscale_view()
            
            # 更新活跃节点数量图表
            self.active_nodes_line.set_data(time_axis, data["active_nodes"])
            self.node_axes.relim()
            self.node_axes.autoscale_view()
            
            # 重绘图表
            self.system_canvas.draw()
            self.task_canvas.draw()
            self.node_canvas.draw()
            
            # 更新状态栏
            latest_data = data["timestamp"][-1] if data["timestamp"] else 0
            self.status_bar.config(text=f"最后更新: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(latest_data))}")
            
            # 安排下一次更新
            self.root.after(int(self.update_interval * 1000), self._update_gui)
        
        except Exception as e:
            logger.error(f"更新GUI时发生错误: {e}")
            self.status_bar.config(text=f"更新错误: {e}")
            self.root.after(int(self.update_interval * 1000), self._update_gui)
    
    def run(self):
        """运行GUI"""
        # 启动监控
        self.monitor.start_monitoring()
        
        # 运行主循环
        self.root.mainloop()
        
        # 停止监控
        self.monitor.stop_monitoring()


def save_monitoring_data(monitor: SystemMonitor, output_file: str, interval: float = 60.0):
    """保存监控数据
    
    Args:
        monitor: 系统监控对象
        output_file: 输出文件
        interval: 保存间隔（秒）
    """
    logger.info(f"开始保存监控数据: {output_file}")
    
    try:
        while True:
            # 获取数据历史
            data = monitor.get_data_history()
            
            # 保存数据
            with open(output_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            logger.info(f"监控数据已保存: {output_file}")
            
            # 等待下一次保存
            time.sleep(interval)
    
    except KeyboardInterrupt:
        logger.info("保存监控数据已停止")


def main():
    """主函数"""
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='监控系统运行状态')
    parser.add_argument('--gui', action='store_true', help='启动GUI')
    parser.add_argument('--interval', type=float, default=1.0, help='更新间隔（秒）')
    parser.add_argument('--output', type=str, help='输出文件')
    parser.add_argument('--save-interval', type=float, default=60.0, help='保存间隔（秒）')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 创建系统监控
    monitor = SystemMonitor(update_interval=args.interval)
    
    # 如果指定了输出文件，启动保存线程
    if args.output:
        save_thread = threading.Thread(
            target=save_monitoring_data,
            args=(monitor, args.output, args.save_interval)
        )
        save_thread.daemon = True
        save_thread.start()
    
    # 如果启动GUI，创建GUI
    if args.gui:
        gui = SystemMonitorGUI(monitor, update_interval=args.interval)
        gui.run()
    else:
        # 否则，直接启动监控
        monitor.start_monitoring()
        
        try:
            while True:
                # 获取最新数据
                data = monitor.get_latest_data()
                
                # 如果有数据，输出
                if data:
                    print(f"CPU: {data['cpu_percent']:.1f}% | "
                          f"内存: {data['memory_percent']:.1f}% | "
                          f"磁盘: {data['disk_percent']:.1f}% | "
                          f"任务: {data['task_count']} | "
                          f"节点: {data['active_nodes']}")
                
                # 等待下一次更新
                time.sleep(args.interval)
        
        except KeyboardInterrupt:
            logger.info("监控已停止")
            monitor.stop_monitoring()


if __name__ == "__main__":
    main()
