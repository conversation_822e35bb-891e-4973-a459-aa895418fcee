# 超融态思维引擎工具

本目录包含超融态思维引擎的各种工具，用于测试、优化和发布系统。

## 工具列表

### 测试工具

- `run_all_tests.py`: 运行所有测试，包括单元测试、集成测试、性能测试和稳定性测试
- `analyze_test_results.py`: 分析测试结果并生成报告
- `optimize_system.py`: 根据测试结果优化系统

### 监控与分析工具

- `monitor_system.py`: 实时监控系统运行状态，包括资源使用情况、任务执行情况和节点状态
- `profile_system.py`: 分析系统性能瓶颈，包括CPU分析、内存分析和IO分析

### 自动化测试工具

- `automation/`: 自动化测试工具包，用于自动化生成测试用例、执行测试和生成测试报告
  - `automate_tests.py`: 自动化测试工具主模块
  - `test_base.py`: 自动化测试基础类
  - `test_generator.py`: 测试用例生成器
  - `test_executor.py`: 测试执行器

### 发布工具

- `prepare_release.py`: 准备系统发布，包括生成文档、打包代码和创建发布说明

## 使用说明

### 运行所有测试

```bash
cd /path/to/project
python v2/tests/run_all_tests.py --output test_results.json
```

可选参数:
- `--unit`: 运行单元测试
- `--integration`: 运行集成测试
- `--performance`: 运行性能测试
- `--stability`: 运行稳定性测试
- `--all`: 运行所有测试（默认）
- `--output`: 测试结果输出文件
- `--stability-duration-factor`: 稳定性测试持续时间因子（默认为0.1，即10%的正常持续时间）

### 分析测试结果

```bash
cd /path/to/project
python v2/tests/analyze_test_results.py test_results.json --output-dir test_report
```

可选参数:
- `--output-dir`: 输出目录（默认为test_report）

### 优化系统

```bash
cd /path/to/project
python v2/tests/optimize_system.py --test-results test_results.json --output optimization_report.html
```

可选参数:
- `--test-results`: 测试结果文件
- `--output`: 优化报告输出路径（默认为optimization_report.html）
- `--optimize-morphism`: 优化态射系统
- `--optimize-metacognition`: 优化元认知系统
- `--optimize-distributed`: 优化分布式系统
- `--optimize-config`: 优化系统配置
- `--all`: 执行所有优化（默认）

### 准备系统发布

```bash
cd /path/to/project
python v2/tools/prepare_release.py --output-dir release
```

可选参数:
- `--output-dir`: 输出目录（默认为release）
- `--docs`: 生成文档
- `--package`: 打包代码
- `--release-notes`: 创建发布说明
- `--deployment-guide`: 创建部署指南
- `--all`: 执行所有操作（默认）

### 监控系统

```bash
cd /path/to/project
python v2/tools/monitor_system.py --gui
```

可选参数:
- `--gui`: 启动GUI界面
- `--interval`: 更新间隔（秒）
- `--output`: 输出文件
- `--save-interval`: 保存间隔（秒）

### 分析系统性能

```bash
cd /path/to/project
python v2/tools/profile_system.py --module src.core.morphism --function apply_morphism --output-dir profile_report
```

可选参数:
- `--module`: 要分析的模块
- `--function`: 要分析的函数
- `--args`: 函数参数 (JSON格式)
- `--kwargs`: 函数关键字参数 (JSON格式)
- `--output-dir`: 输出目录
- `--top-n`: 显示的函数数量

### 自动化测试

```bash
cd /path/to/project
python v2/tools/automation/automate_tests.py generate --template basic_test --suite example_tests --config example_config.json
```

可用命令:
- `generate`: 生成测试套件
- `api`: 生成API测试套件
- `run`: 运行测试
- `continuous`: 启动持续测试

## 工作流程

1. 运行所有测试，生成测试结果
2. 分析测试结果，找出系统的瓶颈和问题
3. 使用监控工具实时监控系统运行状态
4. 使用性能分析工具分析系统性能瓶颈
5. 根据分析结果优化系统
6. 使用自动化测试工具生成和执行测试
7. 再次运行测试，验证优化效果
8. 准备系统发布

## 注意事项

- 运行测试前，请确保已安装所有依赖
- 长时间运行测试可能需要较长时间运行，请耐心等待
- 测试会消耗大量系统资源，请确保系统有足够的资源
- 优化系统可能会修改系统配置，请在优化前备份重要数据
- 发布准备会创建新的文件和目录，请确保有足够的磁盘空间
