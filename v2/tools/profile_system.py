#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
系统性能分析工具

本模块提供了分析系统性能瓶颈的功能，包括CPU分析、内存分析和IO分析。
"""

import os
import sys
import argparse
import logging
import time
import json
import cProfile
import pstats
import io
import tracemalloc
import gc
import psutil
import matplotlib.pyplot as plt
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Callable

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CPUProfiler:
    """CPU性能分析器"""
    
    def __init__(self):
        """初始化CPU性能分析器"""
        self.profiler = cProfile.Profile()
    
    def start(self):
        """开始分析"""
        logger.info("开始CPU性能分析")
        self.profiler.enable()
    
    def stop(self) -> pstats.Stats:
        """停止分析
        
        Returns:
            性能统计
        """
        logger.info("停止CPU性能分析")
        self.profiler.disable()
        
        # 创建统计对象
        string_io = io.StringIO()
        stats = pstats.Stats(self.profiler, stream=string_io)
        stats.sort_stats('cumulative')
        
        return stats
    
    def profile_function(self, func: Callable, *args, **kwargs) -> Tuple[Any, pstats.Stats]:
        """分析函数
        
        Args:
            func: 要分析的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
        
        Returns:
            函数返回值和性能统计
        """
        logger.info(f"分析函数: {func.__name__}")
        
        # 开始分析
        self.start()
        
        # 执行函数
        try:
            result = func(*args, **kwargs)
        finally:
            # 停止分析
            stats = self.stop()
        
        return result, stats
    
    def save_stats(self, stats: pstats.Stats, output_file: str):
        """保存统计结果
        
        Args:
            stats: 性能统计
            output_file: 输出文件
        """
        logger.info(f"保存CPU性能统计: {output_file}")
        
        # 保存统计结果
        stats.dump_stats(output_file)
    
    def print_stats(self, stats: pstats.Stats, top_n: int = 20):
        """打印统计结果
        
        Args:
            stats: 性能统计
            top_n: 显示的函数数量
        """
        logger.info("CPU性能统计:")
        
        # 打印统计结果
        stats.strip_dirs()
        stats.sort_stats('cumulative')
        stats.print_stats(top_n)
    
    def generate_report(self, stats: pstats.Stats, output_file: str, top_n: int = 20):
        """生成报告
        
        Args:
            stats: 性能统计
            output_file: 输出文件
            top_n: 显示的函数数量
        """
        logger.info(f"生成CPU性能报告: {output_file}")
        
        # 创建报告
        string_io = io.StringIO()
        stats.stream = string_io
        stats.strip_dirs()
        stats.sort_stats('cumulative')
        stats.print_stats(top_n)
        
        # 获取报告内容
        report = string_io.getvalue()
        
        # 保存报告
        with open(output_file, 'w') as f:
            f.write(report)


class MemoryProfiler:
    """内存性能分析器"""
    
    def __init__(self):
        """初始化内存性能分析器"""
        self.snapshot_before = None
        self.snapshot_after = None
    
    def start(self):
        """开始分析"""
        logger.info("开始内存性能分析")
        
        # 强制垃圾回收
        gc.collect()
        
        # 启动跟踪
        tracemalloc.start()
        
        # 获取初始快照
        self.snapshot_before = tracemalloc.take_snapshot()
    
    def stop(self) -> Tuple[tracemalloc.Snapshot, tracemalloc.Snapshot]:
        """停止分析
        
        Returns:
            分析前后的快照
        """
        logger.info("停止内存性能分析")
        
        # 获取结束快照
        self.snapshot_after = tracemalloc.take_snapshot()
        
        # 停止跟踪
        tracemalloc.stop()
        
        return self.snapshot_before, self.snapshot_after
    
    def profile_function(self, func: Callable, *args, **kwargs) -> Tuple[Any, Tuple[tracemalloc.Snapshot, tracemalloc.Snapshot]]:
        """分析函数
        
        Args:
            func: 要分析的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
        
        Returns:
            函数返回值和分析前后的快照
        """
        logger.info(f"分析函数: {func.__name__}")
        
        # 开始分析
        self.start()
        
        # 执行函数
        try:
            result = func(*args, **kwargs)
        finally:
            # 停止分析
            snapshots = self.stop()
        
        return result, snapshots
    
    def print_stats(self, top_n: int = 20):
        """打印统计结果
        
        Args:
            top_n: 显示的分配数量
        """
        logger.info("内存性能统计:")
        
        if self.snapshot_before is None or self.snapshot_after is None:
            logger.warning("没有可用的内存快照")
            return
        
        # 计算差异
        stats = self.snapshot_after.compare_to(self.snapshot_before, 'lineno')
        
        # 打印统计结果
        for stat in stats[:top_n]:
            print(f"{stat.size_diff / 1024:.1f} KB: {stat.traceback.format()[0]}")
    
    def generate_report(self, output_file: str, top_n: int = 20):
        """生成报告
        
        Args:
            output_file: 输出文件
            top_n: 显示的分配数量
        """
        logger.info(f"生成内存性能报告: {output_file}")
        
        if self.snapshot_before is None or self.snapshot_after is None:
            logger.warning("没有可用的内存快照")
            return
        
        # 计算差异
        stats = self.snapshot_after.compare_to(self.snapshot_before, 'lineno')
        
        # 创建报告
        report = "内存性能报告\n"
        report += "=" * 80 + "\n\n"
        
        # 添加统计结果
        report += "内存分配差异 (按大小排序):\n"
        report += "-" * 80 + "\n"
        
        for stat in stats[:top_n]:
            report += f"{stat.size_diff / 1024:.1f} KB: {stat.traceback.format()[0]}\n"
        
        # 保存报告
        with open(output_file, 'w') as f:
            f.write(report)


class IOProfiler:
    """IO性能分析器"""
    
    def __init__(self):
        """初始化IO性能分析器"""
        self.io_before = None
        self.io_after = None
        self.start_time = None
        self.end_time = None
    
    def start(self):
        """开始分析"""
        logger.info("开始IO性能分析")
        
        # 获取初始IO计数
        self.io_before = psutil.disk_io_counters()
        self.start_time = time.time()
    
    def stop(self) -> Tuple[psutil._common.sdiskio, psutil._common.sdiskio, float, float]:
        """停止分析
        
        Returns:
            分析前后的IO计数和时间
        """
        logger.info("停止IO性能分析")
        
        # 获取结束IO计数
        self.io_after = psutil.disk_io_counters()
        self.end_time = time.time()
        
        return self.io_before, self.io_after, self.start_time, self.end_time
    
    def profile_function(self, func: Callable, *args, **kwargs) -> Tuple[Any, Tuple[psutil._common.sdiskio, psutil._common.sdiskio, float, float]]:
        """分析函数
        
        Args:
            func: 要分析的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
        
        Returns:
            函数返回值和分析结果
        """
        logger.info(f"分析函数: {func.__name__}")
        
        # 开始分析
        self.start()
        
        # 执行函数
        try:
            result = func(*args, **kwargs)
        finally:
            # 停止分析
            io_stats = self.stop()
        
        return result, io_stats
    
    def print_stats(self):
        """打印统计结果"""
        logger.info("IO性能统计:")
        
        if self.io_before is None or self.io_after is None:
            logger.warning("没有可用的IO统计")
            return
        
        # 计算时间差
        time_diff = self.end_time - self.start_time
        
        # 计算IO差异
        read_bytes = self.io_after.read_bytes - self.io_before.read_bytes
        write_bytes = self.io_after.write_bytes - self.io_before.write_bytes
        read_count = self.io_after.read_count - self.io_before.read_count
        write_count = self.io_after.write_count - self.io_before.write_count
        
        # 计算IO速率
        read_rate = read_bytes / time_diff if time_diff > 0 else 0
        write_rate = write_bytes / time_diff if time_diff > 0 else 0
        
        # 打印统计结果
        print(f"读取: {read_bytes / 1024:.1f} KB ({read_count} 次), {read_rate / 1024:.1f} KB/s")
        print(f"写入: {write_bytes / 1024:.1f} KB ({write_count} 次), {write_rate / 1024:.1f} KB/s")
    
    def generate_report(self, output_file: str):
        """生成报告
        
        Args:
            output_file: 输出文件
        """
        logger.info(f"生成IO性能报告: {output_file}")
        
        if self.io_before is None or self.io_after is None:
            logger.warning("没有可用的IO统计")
            return
        
        # 计算时间差
        time_diff = self.end_time - self.start_time
        
        # 计算IO差异
        read_bytes = self.io_after.read_bytes - self.io_before.read_bytes
        write_bytes = self.io_after.write_bytes - self.io_before.write_bytes
        read_count = self.io_after.read_count - self.io_before.read_count
        write_count = self.io_after.write_count - self.io_before.write_count
        
        # 计算IO速率
        read_rate = read_bytes / time_diff if time_diff > 0 else 0
        write_rate = write_bytes / time_diff if time_diff > 0 else 0
        
        # 创建报告
        report = "IO性能报告\n"
        report += "=" * 80 + "\n\n"
        
        # 添加统计结果
        report += f"总时间: {time_diff:.2f} 秒\n\n"
        report += "磁盘IO:\n"
        report += "-" * 80 + "\n"
        report += f"读取: {read_bytes / 1024:.1f} KB ({read_count} 次), {read_rate / 1024:.1f} KB/s\n"
        report += f"写入: {write_bytes / 1024:.1f} KB ({write_count} 次), {write_rate / 1024:.1f} KB/s\n"
        
        # 保存报告
        with open(output_file, 'w') as f:
            f.write(report)


class SystemProfiler:
    """系统性能分析器"""
    
    def __init__(self):
        """初始化系统性能分析器"""
        self.cpu_profiler = CPUProfiler()
        self.memory_profiler = MemoryProfiler()
        self.io_profiler = IOProfiler()
    
    def profile_function(self, func: Callable, *args, **kwargs) -> Tuple[Any, Dict[str, Any]]:
        """分析函数
        
        Args:
            func: 要分析的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
        
        Returns:
            函数返回值和分析结果
        """
        logger.info(f"分析函数: {func.__name__}")
        
        # 分析CPU
        cpu_result, cpu_stats = self.cpu_profiler.profile_function(func, *args, **kwargs)
        
        # 分析内存
        memory_result, memory_snapshots = self.memory_profiler.profile_function(func, *args, **kwargs)
        
        # 分析IO
        io_result, io_stats = self.io_profiler.profile_function(func, *args, **kwargs)
        
        # 创建分析结果
        profile_results = {
            "cpu": {
                "stats": cpu_stats
            },
            "memory": {
                "snapshots": memory_snapshots
            },
            "io": {
                "stats": io_stats
            }
        }
        
        return cpu_result, profile_results
    
    def print_stats(self, profile_results: Dict[str, Any], top_n: int = 20):
        """打印统计结果
        
        Args:
            profile_results: 分析结果
            top_n: 显示的函数数量
        """
        logger.info("系统性能统计:")
        
        # 打印CPU统计
        print("\nCPU性能统计:")
        print("=" * 80)
        self.cpu_profiler.print_stats(profile_results["cpu"]["stats"], top_n)
        
        # 打印内存统计
        print("\n内存性能统计:")
        print("=" * 80)
        self.memory_profiler.snapshot_before, self.memory_profiler.snapshot_after = profile_results["memory"]["snapshots"]
        self.memory_profiler.print_stats(top_n)
        
        # 打印IO统计
        print("\nIO性能统计:")
        print("=" * 80)
        self.io_profiler.io_before, self.io_profiler.io_after, self.io_profiler.start_time, self.io_profiler.end_time = profile_results["io"]["stats"]
        self.io_profiler.print_stats()
    
    def generate_report(self, profile_results: Dict[str, Any], output_dir: str, top_n: int = 20):
        """生成报告
        
        Args:
            profile_results: 分析结果
            output_dir: 输出目录
            top_n: 显示的函数数量
        """
        logger.info(f"生成系统性能报告: {output_dir}")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成CPU报告
        cpu_report_file = os.path.join(output_dir, "cpu_report.txt")
        self.cpu_profiler.generate_report(profile_results["cpu"]["stats"], cpu_report_file, top_n)
        
        # 生成内存报告
        memory_report_file = os.path.join(output_dir, "memory_report.txt")
        self.memory_profiler.snapshot_before, self.memory_profiler.snapshot_after = profile_results["memory"]["snapshots"]
        self.memory_profiler.generate_report(memory_report_file, top_n)
        
        # 生成IO报告
        io_report_file = os.path.join(output_dir, "io_report.txt")
        self.io_profiler.io_before, self.io_profiler.io_after, self.io_profiler.start_time, self.io_profiler.end_time = profile_results["io"]["stats"]
        self.io_profiler.generate_report(io_report_file)
        
        # 生成HTML报告
        html_report_file = os.path.join(output_dir, "profile_report.html")
        self._generate_html_report(profile_results, html_report_file, top_n)
    
    def _generate_html_report(self, profile_results: Dict[str, Any], output_file: str, top_n: int = 20):
        """生成HTML报告
        
        Args:
            profile_results: 分析结果
            output_file: 输出文件
            top_n: 显示的函数数量
        """
        logger.info(f"生成HTML性能报告: {output_file}")
        
        # 获取CPU统计
        cpu_stats = profile_results["cpu"]["stats"]
        
        # 获取内存统计
        self.memory_profiler.snapshot_before, self.memory_profiler.snapshot_after = profile_results["memory"]["snapshots"]
        memory_stats = self.memory_profiler.snapshot_after.compare_to(self.memory_profiler.snapshot_before, 'lineno')
        
        # 获取IO统计
        self.io_profiler.io_before, self.io_profiler.io_after, self.io_profiler.start_time, self.io_profiler.end_time = profile_results["io"]["stats"]
        time_diff = self.io_profiler.end_time - self.io_profiler.start_time
        read_bytes = self.io_profiler.io_after.read_bytes - self.io_profiler.io_before.read_bytes
        write_bytes = self.io_profiler.io_after.write_bytes - self.io_profiler.io_before.write_bytes
        read_count = self.io_profiler.io_after.read_count - self.io_profiler.io_before.read_count
        write_count = self.io_profiler.io_after.write_count - self.io_profiler.io_before.write_count
        read_rate = read_bytes / time_diff if time_diff > 0 else 0
        write_rate = write_bytes / time_diff if time_diff > 0 else 0
        
        # 创建HTML报告
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>系统性能分析报告</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    margin: 20px;
                    background-color: #f5f5f5;
                }}
                h1, h2, h3 {{
                    color: #333;
                }}
                .container {{
                    max-width: 1200px;
                    margin: 0 auto;
                    background-color: white;
                    padding: 20px;
                    border-radius: 5px;
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                }}
                .section {{
                    margin-bottom: 20px;
                    padding: 15px;
                    background-color: #f9f9f9;
                    border-radius: 5px;
                }}
                table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }}
                th, td {{
                    padding: 10px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }}
                th {{
                    background-color: #f2f2f2;
                }}
                tr:hover {{
                    background-color: #f5f5f5;
                }}
                .chart {{
                    width: 100%;
                    height: 300px;
                    margin-bottom: 20px;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>系统性能分析报告</h1>
                <p>生成时间: {time.strftime("%Y-%m-%d %H:%M:%S")}</p>
                
                <div class="section">
                    <h2>CPU性能分析</h2>
                    <table>
                        <tr>
                            <th>函数</th>
                            <th>调用次数</th>
                            <th>总时间 (秒)</th>
                            <th>每次调用时间 (秒)</th>
                            <th>累计时间 (秒)</th>
                        </tr>
        """
        
        # 获取CPU统计
        string_io = io.StringIO()
        cpu_stats.stream = string_io
        cpu_stats.strip_dirs()
        cpu_stats.sort_stats('cumulative')
        cpu_stats.print_stats(top_n)
        
        # 解析CPU统计
        stats_text = string_io.getvalue()
        stats_lines = stats_text.split('\n')
        
        # 跳过头部
        for i, line in enumerate(stats_lines):
            if line.strip().startswith('ncalls'):
                break
        
        # 添加CPU统计
        for line in stats_lines[i+1:i+1+top_n]:
            if not line.strip():
                continue
            
            parts = line.strip().split()
            if len(parts) < 5:
                continue
            
            ncalls = parts[0]
            tottime = parts[1]
            percall_tot = parts[2]
            cumtime = parts[3]
            percall_cum = parts[4]
            function = ' '.join(parts[5:])
            
            html += f"""
                        <tr>
                            <td>{function}</td>
                            <td>{ncalls}</td>
                            <td>{tottime}</td>
                            <td>{percall_tot}</td>
                            <td>{cumtime}</td>
                        </tr>
            """
        
        html += f"""
                    </table>
                </div>
                
                <div class="section">
                    <h2>内存性能分析</h2>
                    <table>
                        <tr>
                            <th>大小 (KB)</th>
                            <th>位置</th>
                        </tr>
        """
        
        # 添加内存统计
        for stat in memory_stats[:top_n]:
            html += f"""
                        <tr>
                            <td>{stat.size_diff / 1024:.1f}</td>
                            <td>{stat.traceback.format()[0]}</td>
                        </tr>
            """
        
        html += f"""
                    </table>
                </div>
                
                <div class="section">
                    <h2>IO性能分析</h2>
                    <table>
                        <tr>
                            <th>操作</th>
                            <th>大小 (KB)</th>
                            <th>次数</th>
                            <th>速率 (KB/s)</th>
                        </tr>
                        <tr>
                            <td>读取</td>
                            <td>{read_bytes / 1024:.1f}</td>
                            <td>{read_count}</td>
                            <td>{read_rate / 1024:.1f}</td>
                        </tr>
                        <tr>
                            <td>写入</td>
                            <td>{write_bytes / 1024:.1f}</td>
                            <td>{write_count}</td>
                            <td>{write_rate / 1024:.1f}</td>
                        </tr>
                    </table>
                </div>
                
                <div class="section">
                    <h2>总结</h2>
                    <p>总执行时间: {time_diff:.2f} 秒</p>
                    <p>CPU密集型函数: {stats_lines[i+1].strip().split()[-1] if len(stats_lines) > i+1 else "无"}</p>
                    <p>内存密集型位置: {memory_stats[0].traceback.format()[0] if memory_stats else "无"}</p>
                    <p>IO操作: 读取 {read_count} 次, 写入 {write_count} 次</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # 保存HTML报告
        with open(output_file, 'w') as f:
            f.write(html)


def import_module(module_name: str) -> Any:
    """导入模块
    
    Args:
        module_name: 模块名称
    
    Returns:
        导入的模块
    """
    logger.info(f"导入模块: {module_name}")
    
    try:
        module = __import__(module_name, fromlist=[''])
        return module
    except ImportError as e:
        logger.error(f"导入模块 {module_name} 失败: {e}")
        return None


def get_function(module: Any, function_name: str) -> Optional[Callable]:
    """获取函数
    
    Args:
        module: 模块
        function_name: 函数名称
    
    Returns:
        函数对象
    """
    logger.info(f"获取函数: {function_name}")
    
    if module is None:
        return None
    
    try:
        function = getattr(module, function_name)
        return function
    except AttributeError as e:
        logger.error(f"获取函数 {function_name} 失败: {e}")
        return None


def main():
    """主函数"""
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='分析系统性能')
    parser.add_argument('--module', type=str, required=True, help='要分析的模块')
    parser.add_argument('--function', type=str, required=True, help='要分析的函数')
    parser.add_argument('--args', type=str, help='函数参数 (JSON格式)')
    parser.add_argument('--kwargs', type=str, help='函数关键字参数 (JSON格式)')
    parser.add_argument('--output-dir', type=str, default='profile_report', help='输出目录')
    parser.add_argument('--top-n', type=int, default=20, help='显示的函数数量')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 导入模块
    module = import_module(args.module)
    if module is None:
        sys.exit(1)
    
    # 获取函数
    function = get_function(module, args.function)
    if function is None:
        sys.exit(1)
    
    # 解析函数参数
    function_args = []
    if args.args:
        try:
            function_args = json.loads(args.args)
        except json.JSONDecodeError as e:
            logger.error(f"解析函数参数失败: {e}")
            sys.exit(1)
    
    # 解析函数关键字参数
    function_kwargs = {}
    if args.kwargs:
        try:
            function_kwargs = json.loads(args.kwargs)
        except json.JSONDecodeError as e:
            logger.error(f"解析函数关键字参数失败: {e}")
            sys.exit(1)
    
    # 创建系统性能分析器
    profiler = SystemProfiler()
    
    # 分析函数
    result, profile_results = profiler.profile_function(function, *function_args, **function_kwargs)
    
    # 打印统计结果
    profiler.print_stats(profile_results, args.top_n)
    
    # 生成报告
    profiler.generate_report(profile_results, args.output_dir, args.top_n)
    
    logger.info(f"性能分析报告已生成: {args.output_dir}")


if __name__ == "__main__":
    main()
