#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复导入路径

本脚本用于修复代码中的导入路径，将TCT替换为TTE。
"""

import os
import sys
import re
from typing import List, Tuple

# 要处理的目录
DIRS_TO_PROCESS = [
    "/home/<USER>/CascadeProjects/TTE/v2/tools",
    "/home/<USER>/CascadeProjects/TTE/v2/tests"
]

# 要处理的文件扩展名
FILE_EXTENSIONS = [".py", ".md"]

# 要替换的模式
PATTERNS = [
    (r"from\s+TCT\.", "from TTE."),
    (r"import\s+TCT\.", "import TTE."),
    (r"sys\.path\.append\(.*?TCT", "sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))")
]


def process_file(file_path: str) -> <PERSON><PERSON>[int, List[str]]:
    """处理文件

    Args:
        file_path: 文件路径

    Returns:
        替换次数和替换行
    """
    print(f"处理文件: {file_path}")
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换内容
    new_content = content
    replacements = []
    
    for pattern, replacement in PATTERNS:
        # 查找所有匹配
        matches = re.finditer(pattern, content, re.MULTILINE)
        
        for match in matches:
            line = content[match.start():content.find('\n', match.start())]
            new_line = re.sub(pattern, replacement, line)
            replacements.append((line, new_line))
        
        # 替换内容
        new_content = re.sub(pattern, replacement, new_content)
    
    # 如果内容有变化，写回文件
    if new_content != content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
    
    return len(replacements), replacements


def process_directory(dir_path: str) -> Tuple[int, int, List[Tuple[str, List[str]]]]:
    """处理目录

    Args:
        dir_path: 目录路径

    Returns:
        处理的文件数、替换次数和替换详情
    """
    print(f"处理目录: {dir_path}")
    
    file_count = 0
    replacement_count = 0
    replacement_details = []
    
    # 遍历目录
    for root, dirs, files in os.walk(dir_path):
        for file in files:
            # 检查文件扩展名
            if any(file.endswith(ext) for ext in FILE_EXTENSIONS):
                file_path = os.path.join(root, file)
                
                # 处理文件
                count, replacements = process_file(file_path)
                
                # 更新计数
                file_count += 1
                replacement_count += count
                
                # 添加替换详情
                if replacements:
                    replacement_details.append((file_path, replacements))
    
    return file_count, replacement_count, replacement_details


def main():
    """主函数"""
    total_file_count = 0
    total_replacement_count = 0
    all_replacement_details = []
    
    # 处理所有目录
    for dir_path in DIRS_TO_PROCESS:
        file_count, replacement_count, replacement_details = process_directory(dir_path)
        
        # 更新计数
        total_file_count += file_count
        total_replacement_count += replacement_count
        all_replacement_details.extend(replacement_details)
    
    # 输出结果
    print(f"\n处理完成！")
    print(f"处理文件数: {total_file_count}")
    print(f"替换次数: {total_replacement_count}")
    
    # 输出替换详情
    if all_replacement_details:
        print("\n替换详情:")
        for file_path, replacements in all_replacement_details:
            print(f"\n文件: {file_path}")
            for old_line, new_line in replacements:
                print(f"  - 旧: {old_line}")
                print(f"  - 新: {new_line}")


if __name__ == "__main__":
    main()
