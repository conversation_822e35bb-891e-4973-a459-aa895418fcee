#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查算子和算法注册状态

本脚本用于检查算子和算法的注册状态，并将未注册的算子和算法注册到注册表中。
"""

import os
import sys
import importlib
import inspect
import logging
import argparse
from typing import Dict, List, Any, Optional, Set, Tuple, Union, Callable

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入注册表
try:
    from src.operators.registry import OperatorRegistry
    from src.core.distributed.algorithm.algorithm_registry import AlgorithmRegistry
    from src.core.distributed.algorithm.algorithm_types import Algorithm, AlgorithmType
except ImportError as e:
    logger.error(f"导入注册表失败: {e}")
    logger.error("请确保您在TTE目录中运行此脚本")
    sys.exit(1)


def find_python_files(directory: str) -> List[str]:
    """查找Python文件

    Args:
        directory: 目录路径

    Returns:
        Python文件列表
    """
    python_files = []
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py') and not file.startswith('__'):
                python_files.append(os.path.join(root, file))
    
    return python_files


def get_module_name(file_path: str, base_dir: str) -> str:
    """获取模块名称

    Args:
        file_path: 文件路径
        base_dir: 基础目录

    Returns:
        模块名称
    """
    # 计算相对路径
    rel_path = os.path.relpath(file_path, base_dir)
    
    # 移除扩展名
    module_path = os.path.splitext(rel_path)[0]
    
    # 替换路径分隔符
    module_name = module_path.replace(os.path.sep, '.')
    
    return module_name


def import_module_from_file(file_path: str, base_dir: str) -> Optional[Any]:
    """从文件导入模块

    Args:
        file_path: 文件路径
        base_dir: 基础目录

    Returns:
        模块对象
    """
    try:
        # 获取模块名称
        module_name = get_module_name(file_path, base_dir)
        
        # 导入模块
        spec = importlib.util.spec_from_file_location(module_name, file_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        return module
    
    except Exception as e:
        logger.error(f"导入模块 {file_path} 失败: {e}")
        return None


def find_operators_in_module(module: Any) -> List[Tuple[str, Any]]:
    """在模块中查找算子

    Args:
        module: 模块对象

    Returns:
        算子列表
    """
    operators = []
    
    # 遍历模块中的所有对象
    for name, obj in inspect.getmembers(module):
        # 跳过私有对象
        if name.startswith('_'):
            continue
        
        # 检查是否是算子
        if hasattr(obj, 'is_operator') and obj.is_operator:
            operators.append((name, obj))
        elif hasattr(obj, 'operator_type') and obj.operator_type:
            operators.append((name, obj))
        elif name.endswith('Operator') and inspect.isclass(obj):
            operators.append((name, obj))
    
    return operators


def find_algorithms_in_module(module: Any) -> List[Tuple[str, Any]]:
    """在模块中查找算法

    Args:
        module: 模块对象

    Returns:
        算法列表
    """
    algorithms = []
    
    # 遍历模块中的所有对象
    for name, obj in inspect.getmembers(module):
        # 跳过私有对象
        if name.startswith('_'):
            continue
        
        # 检查是否是算法
        if hasattr(obj, 'is_algorithm') and obj.is_algorithm:
            algorithms.append((name, obj))
        elif hasattr(obj, 'algorithm_type') and obj.algorithm_type:
            algorithms.append((name, obj))
        elif name.endswith('Algorithm') and inspect.isclass(obj):
            algorithms.append((name, obj))
    
    return algorithms


def check_operator_registration(operator_registry: OperatorRegistry, operators: List[Tuple[str, Any]]) -> Tuple[List[Tuple[str, Any]], List[Tuple[str, Any]]]:
    """检查算子注册状态

    Args:
        operator_registry: 算子注册表
        operators: 算子列表

    Returns:
        已注册算子列表和未注册算子列表
    """
    registered_operators = []
    unregistered_operators = []
    
    for name, operator in operators:
        # 获取算子ID
        operator_id = getattr(operator, 'operator_id', name)
        
        # 检查是否已注册
        if operator_registry.lookup_operator(operator_id):
            registered_operators.append((name, operator))
        else:
            unregistered_operators.append((name, operator))
    
    return registered_operators, unregistered_operators


def check_algorithm_registration(algorithm_registry: AlgorithmRegistry, algorithms: List[Tuple[str, Any]]) -> Tuple[List[Tuple[str, Any]], List[Tuple[str, Any]]]:
    """检查算法注册状态

    Args:
        algorithm_registry: 算法注册表
        algorithms: 算法列表

    Returns:
        已注册算法列表和未注册算法列表
    """
    registered_algorithms = []
    unregistered_algorithms = []
    
    for name, algorithm in algorithms:
        # 获取算法ID
        algorithm_id = getattr(algorithm, 'algorithm_id', name)
        
        # 检查是否已注册
        if algorithm_registry.lookup_algorithm(algorithm_id):
            registered_algorithms.append((name, algorithm))
        else:
            unregistered_algorithms.append((name, algorithm))
    
    return registered_algorithms, unregistered_algorithms


def register_operators(operator_registry: OperatorRegistry, operators: List[Tuple[str, Any]]) -> Tuple[List[Tuple[str, Any]], List[Tuple[str, Any]]]:
    """注册算子

    Args:
        operator_registry: 算子注册表
        operators: 算子列表

    Returns:
        成功注册的算子列表和注册失败的算子列表
    """
    success_operators = []
    failed_operators = []
    
    for name, operator in operators:
        try:
            # 注册算子
            if operator_registry.register_operator(operator):
                success_operators.append((name, operator))
            else:
                failed_operators.append((name, operator))
        
        except Exception as e:
            logger.error(f"注册算子 {name} 失败: {e}")
            failed_operators.append((name, operator))
    
    return success_operators, failed_operators


def register_algorithms(algorithm_registry: AlgorithmRegistry, algorithms: List[Tuple[str, Any]]) -> Tuple[List[Tuple[str, Any]], List[Tuple[str, Any]]]:
    """注册算法

    Args:
        algorithm_registry: 算法注册表
        algorithms: 算法列表

    Returns:
        成功注册的算法列表和注册失败的算法列表
    """
    success_algorithms = []
    failed_algorithms = []
    
    for name, algorithm in algorithms:
        try:
            # 注册算法
            if algorithm_registry.register_algorithm(algorithm):
                success_algorithms.append((name, algorithm))
            else:
                failed_algorithms.append((name, algorithm))
        
        except Exception as e:
            logger.error(f"注册算法 {name} 失败: {e}")
            failed_algorithms.append((name, algorithm))
    
    return success_algorithms, failed_algorithms


def main():
    """主函数"""
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='检查算子和算法注册状态')
    parser.add_argument('--dir', type=str, default='/home/<USER>/CascadeProjects/TTE', help='要检查的目录')
    parser.add_argument('--register', action='store_true', help='注册未注册的算子和算法')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 创建算子注册表
    operator_registry = OperatorRegistry("main_registry")
    
    # 创建算法注册表
    algorithm_registry = AlgorithmRegistry("main_registry")
    
    # 查找Python文件
    python_files = find_python_files(args.dir)
    logger.info(f"找到 {len(python_files)} 个Python文件")
    
    # 统计
    total_operators = 0
    registered_operators = 0
    unregistered_operators = 0
    
    total_algorithms = 0
    registered_algorithms = 0
    unregistered_algorithms = 0
    
    # 处理每个Python文件
    for file_path in python_files:
        # 导入模块
        module = import_module_from_file(file_path, args.dir)
        if not module:
            continue
        
        # 查找算子
        operators = find_operators_in_module(module)
        total_operators += len(operators)
        
        # 检查算子注册状态
        registered, unregistered = check_operator_registration(operator_registry, operators)
        registered_operators += len(registered)
        unregistered_operators += len(unregistered)
        
        # 注册未注册的算子
        if args.register and unregistered:
            success, failed = register_operators(operator_registry, unregistered)
            logger.info(f"注册算子: 成功 {len(success)}, 失败 {len(failed)}")
        
        # 查找算法
        algorithms = find_algorithms_in_module(module)
        total_algorithms += len(algorithms)
        
        # 检查算法注册状态
        registered, unregistered = check_algorithm_registration(algorithm_registry, algorithms)
        registered_algorithms += len(registered)
        unregistered_algorithms += len(unregistered)
        
        # 注册未注册的算法
        if args.register and unregistered:
            success, failed = register_algorithms(algorithm_registry, unregistered)
            logger.info(f"注册算法: 成功 {len(success)}, 失败 {len(failed)}")
    
    # 输出结果
    logger.info(f"\n检查完成！")
    logger.info(f"算子: 总计 {total_operators}, 已注册 {registered_operators}, 未注册 {unregistered_operators}")
    logger.info(f"算法: 总计 {total_algorithms}, 已注册 {registered_algorithms}, 未注册 {unregistered_algorithms}")
    
    # 输出注册表统计信息
    logger.info(f"\n算子注册表统计:")
    logger.info(f"  算子数量: {operator_registry.get_operator_count()}")
    logger.info(f"  算子类型数量: {operator_registry.get_operator_type_count()}")
    
    logger.info(f"\n算法注册表统计:")
    logger.info(f"  算法数量: {algorithm_registry.get_algorithm_count()}")
    logger.info(f"  算法类型数量: {algorithm_registry.get_algorithm_type_count()}")


if __name__ == "__main__":
    main()
