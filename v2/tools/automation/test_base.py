#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
自动化测试基础类

本模块提供了自动化测试的基础类，包括测试用例、测试套件和测试运行器。
"""

import os
import sys
import logging
import time
import json
import unittest
import traceback
from typing import Dict, List, Any, Optional, Tuple, Callable, Union

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class AutomatedTestCase(unittest.TestCase):
    """自动化测试用例基类"""
    
    def __init__(self, methodName: str = 'runTest'):
        """初始化测试用例
        
        Args:
            methodName: 测试方法名称
        """
        super().__init__(methodName)
        self.start_time = None
        self.end_time = None
        self.duration = None
        self.result = None
    
    def setUp(self):
        """测试前的准备工作"""
        logger.info(f"开始测试: {self._testMethodName}")
        self.start_time = time.time()
    
    def tearDown(self):
        """测试后的清理工作"""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        logger.info(f"结束测试: {self._testMethodName}, 耗时: {self.duration:.2f} 秒")
    
    def run(self, result=None):
        """运行测试
        
        Args:
            result: 测试结果
        """
        self.result = result
        super().run(result)
    
    def get_test_info(self) -> Dict[str, Any]:
        """获取测试信息
        
        Returns:
            测试信息
        """
        return {
            "name": self._testMethodName,
            "description": self._testMethodDoc,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "duration": self.duration
        }


class AutomatedTestSuite(unittest.TestSuite):
    """自动化测试套件"""
    
    def __init__(self, tests=(), name=None, description=None):
        """初始化测试套件
        
        Args:
            tests: 测试用例列表
            name: 测试套件名称
            description: 测试套件描述
        """
        super().__init__(tests)
        self.name = name
        self.description = description
        self.start_time = None
        self.end_time = None
        self.duration = None
    
    def run(self, result, debug=False):
        """运行测试套件
        
        Args:
            result: 测试结果
            debug: 是否调试模式
        """
        logger.info(f"开始测试套件: {self.name}")
        self.start_time = time.time()
        
        super().run(result, debug)
        
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        logger.info(f"结束测试套件: {self.name}, 耗时: {self.duration:.2f} 秒")
    
    def get_suite_info(self) -> Dict[str, Any]:
        """获取测试套件信息
        
        Returns:
            测试套件信息
        """
        return {
            "name": self.name,
            "description": self.description,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "duration": self.duration,
            "test_count": self.countTestCases()
        }


class AutomatedTestResult(unittest.TextTestResult):
    """自动化测试结果"""
    
    def __init__(self, stream, descriptions, verbosity):
        """初始化测试结果
        
        Args:
            stream: 输出流
            descriptions: 是否显示描述
            verbosity: 详细程度
        """
        super().__init__(stream, descriptions, verbosity)
        self.successes = []
        self.start_time = time.time()
        self.end_time = None
        self.duration = None
    
    def addSuccess(self, test):
        """添加成功的测试
        
        Args:
            test: 测试用例
        """
        super().addSuccess(test)
        self.successes.append(test)
    
    def stopTestRun(self):
        """停止测试运行"""
        super().stopTestRun()
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
    
    def get_result_info(self) -> Dict[str, Any]:
        """获取测试结果信息
        
        Returns:
            测试结果信息
        """
        return {
            "start_time": self.start_time,
            "end_time": self.end_time,
            "duration": self.duration,
            "test_count": self.testsRun,
            "success_count": len(self.successes),
            "failure_count": len(self.failures),
            "error_count": len(self.errors),
            "skipped_count": len(self.skipped),
            "success_rate": len(self.successes) / self.testsRun if self.testsRun > 0 else 0
        }


class AutomatedTestRunner(unittest.TextTestRunner):
    """自动化测试运行器"""
    
    def __init__(self, stream=None, descriptions=True, verbosity=1,
                 failfast=False, buffer=False, resultclass=None, warnings=None,
                 *, tb_locals=False):
        """初始化测试运行器
        
        Args:
            stream: 输出流
            descriptions: 是否显示描述
            verbosity: 详细程度
            failfast: 是否快速失败
            buffer: 是否缓冲输出
            resultclass: 结果类
            warnings: 警告过滤器
            tb_locals: 是否显示本地变量
        """
        resultclass = resultclass or AutomatedTestResult
        super().__init__(stream, descriptions, verbosity,
                         failfast, buffer, resultclass, warnings,
                         tb_locals=tb_locals)
    
    def run(self, test):
        """运行测试
        
        Args:
            test: 测试用例或测试套件
        
        Returns:
            测试结果
        """
        logger.info("开始运行测试")
        
        result = super().run(test)
        
        logger.info("结束运行测试")
        
        return result
