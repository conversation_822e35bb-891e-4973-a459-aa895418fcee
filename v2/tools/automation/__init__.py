"""
自动化测试工具包

本包提供了自动化测试的功能，包括生成测试用例、执行测试和生成测试报告。
"""

from v2.tools.automation.test_base import AutomatedTestCase, AutomatedTestSuite, AutomatedTestResult, AutomatedTestRunner
from v2.tools.automation.test_generator import TestCaseGenerator, APITestGenerator, register_default_templates
from v2.tools.automation.test_executor import TestExecutionManager
from v2.tools.automation.automate_tests import TestReportGenerator, AutomatedTestTool

__all__ = [
    'AutomatedTestCase',
    'AutomatedTestSuite',
    'AutomatedTestResult',
    'AutomatedTestRunner',
    'TestCaseGenerator',
    'APITestGenerator',
    'register_default_templates',
    'TestExecutionManager',
    'TestReportGenerator',
    'AutomatedTestTool'
]
