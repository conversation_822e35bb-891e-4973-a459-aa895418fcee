#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
自动化测试执行器

本模块提供了自动化执行测试的功能，包括并行执行测试、定时执行测试和持续执行测试。
"""

import os
import sys
import logging
import time
import json
import threading
import multiprocessing
import queue
import schedule
import importlib
import importlib.util
import unittest
from typing import Dict, List, Any, Optional, Tuple, Callable, Union

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入测试基础类
from v2.tools.automation.test_base import AutomatedTestCase, AutomatedTestSuite, AutomatedTestResult, AutomatedTestRunner

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ParallelTestExecutor:
    """并行测试执行器"""
    
    def __init__(self, max_workers: int = None):
        """初始化并行测试执行器
        
        Args:
            max_workers: 最大工作线程数，默认为CPU核心数
        """
        self.max_workers = max_workers or multiprocessing.cpu_count()
        self.result_queue = multiprocessing.Queue()
    
    def _run_test_in_process(self, test_module: str, test_class: str, test_method: str = None):
        """在进程中运行测试
        
        Args:
            test_module: 测试模块
            test_class: 测试类
            test_method: 测试方法，如果为None，则运行整个测试类
        """
        try:
            # 导入测试模块
            module = importlib.import_module(test_module)
            
            # 获取测试类
            test_class_obj = getattr(module, test_class)
            
            # 创建测试套件
            if test_method:
                # 运行单个测试方法
                suite = unittest.TestSuite()
                suite.addTest(test_class_obj(test_method))
            else:
                # 运行整个测试类
                suite = unittest.makeSuite(test_class_obj)
            
            # 创建测试运行器
            runner = AutomatedTestRunner(verbosity=0)
            
            # 运行测试
            result = runner.run(suite)
            
            # 将结果放入队列
            self.result_queue.put({
                "module": test_module,
                "class": test_class,
                "method": test_method,
                "success": result.wasSuccessful(),
                "test_count": result.testsRun,
                "success_count": len(result.successes),
                "failure_count": len(result.failures),
                "error_count": len(result.errors)
            })
        
        except Exception as e:
            # 将异常放入队列
            self.result_queue.put({
                "module": test_module,
                "class": test_class,
                "method": test_method,
                "success": False,
                "error": str(e)
            })
    
    def run_tests_in_parallel(self, tests: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """并行运行测试
        
        Args:
            tests: 测试列表，每个测试是一个字典，包含模块、类和方法
        
        Returns:
            测试结果列表
        """
        logger.info(f"并行运行 {len(tests)} 个测试，最大工作进程数: {self.max_workers}")
        
        # 创建进程池
        pool = multiprocessing.Pool(processes=self.max_workers)
        
        # 提交测试任务
        for test in tests:
            module = test["module"]
            class_name = test["class"]
            method = test.get("method")
            
            pool.apply_async(self._run_test_in_process, args=(module, class_name, method))
        
        # 关闭进程池
        pool.close()
        
        # 等待所有测试完成
        pool.join()
        
        # 收集测试结果
        results = []
        while not self.result_queue.empty():
            results.append(self.result_queue.get())
        
        # 统计测试结果
        total_tests = len(tests)
        success_count = sum(1 for result in results if result.get("success", False))
        
        logger.info(f"测试完成: 总计 {total_tests} 个测试，成功 {success_count} 个，失败 {total_tests - success_count} 个")
        
        return results


class ScheduledTestExecutor:
    """定时测试执行器"""
    
    def __init__(self):
        """初始化定时测试执行器"""
        self.scheduler = schedule.Scheduler()
        self.running = False
        self.thread = None
    
    def add_test(self, test_func: Callable, interval: str, *args, **kwargs):
        """添加定时测试
        
        Args:
            test_func: 测试函数
            interval: 时间间隔，格式为"every X seconds/minutes/hours/days at HH:MM"
            *args: 测试函数参数
            **kwargs: 测试函数关键字参数
        """
        # 解析时间间隔
        parts = interval.split()
        if len(parts) < 3:
            raise ValueError(f"无效的时间间隔格式: {interval}")
        
        # 获取时间单位和数量
        every_index = parts.index("every")
        unit_index = every_index + 2
        
        if unit_index >= len(parts):
            raise ValueError(f"无效的时间间隔格式: {interval}")
        
        quantity = parts[every_index + 1]
        unit = parts[unit_index]
        
        # 创建任务
        job = None
        
        if unit == "seconds":
            job = self.scheduler.every(int(quantity)).seconds
        elif unit == "minutes":
            job = self.scheduler.every(int(quantity)).minutes
        elif unit == "hours":
            job = self.scheduler.every(int(quantity)).hours
        elif unit == "days":
            job = self.scheduler.every(int(quantity)).days
        else:
            raise ValueError(f"无效的时间单位: {unit}")
        
        # 设置时间
        if "at" in parts:
            at_index = parts.index("at")
            if at_index + 1 < len(parts):
                time_str = parts[at_index + 1]
                job = job.at(time_str)
        
        # 设置任务
        job.do(test_func, *args, **kwargs)
        
        logger.info(f"已添加定时测试: {interval}")
    
    def start(self):
        """启动定时测试执行器"""
        if self.running:
            logger.warning("定时测试执行器已经在运行")
            return
        
        self.running = True
        
        # 创建线程
        self.thread = threading.Thread(target=self._run_scheduler)
        self.thread.daemon = True
        self.thread.start()
        
        logger.info("定时测试执行器已启动")
    
    def stop(self):
        """停止定时测试执行器"""
        if not self.running:
            logger.warning("定时测试执行器未在运行")
            return
        
        self.running = False
        
        # 等待线程结束
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=1.0)
        
        logger.info("定时测试执行器已停止")
    
    def _run_scheduler(self):
        """运行调度器"""
        while self.running:
            self.scheduler.run_pending()
            time.sleep(1)


class ContinuousTestExecutor:
    """持续测试执行器"""
    
    def __init__(self, test_dir: str, interval: float = 60.0):
        """初始化持续测试执行器
        
        Args:
            test_dir: 测试目录
            interval: 测试间隔（秒）
        """
        self.test_dir = test_dir
        self.interval = interval
        self.running = False
        self.thread = None
        self.results = []
    
    def start(self):
        """启动持续测试执行器"""
        if self.running:
            logger.warning("持续测试执行器已经在运行")
            return
        
        self.running = True
        
        # 创建线程
        self.thread = threading.Thread(target=self._run_tests)
        self.thread.daemon = True
        self.thread.start()
        
        logger.info(f"持续测试执行器已启动，测试目录: {self.test_dir}，间隔: {self.interval} 秒")
    
    def stop(self):
        """停止持续测试执行器"""
        if not self.running:
            logger.warning("持续测试执行器未在运行")
            return
        
        self.running = False
        
        # 等待线程结束
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=1.0)
        
        logger.info("持续测试执行器已停止")
    
    def get_results(self) -> List[Dict[str, Any]]:
        """获取测试结果
        
        Returns:
            测试结果列表
        """
        return self.results
    
    def _run_tests(self):
        """运行测试"""
        while self.running:
            try:
                # 发现测试
                test_suite = unittest.defaultTestLoader.discover(self.test_dir, pattern="test_*.py")
                
                # 创建测试运行器
                runner = AutomatedTestRunner(verbosity=0)
                
                # 运行测试
                start_time = time.time()
                result = runner.run(test_suite)
                end_time = time.time()
                
                # 记录测试结果
                test_result = {
                    "timestamp": time.time(),
                    "test_count": result.testsRun,
                    "success_count": len(result.successes),
                    "failure_count": len(result.failures),
                    "error_count": len(result.errors),
                    "duration": end_time - start_time,
                    "success_rate": len(result.successes) / result.testsRun if result.testsRun > 0 else 0
                }
                
                self.results.append(test_result)
                
                # 限制结果数量
                max_results = 100
                if len(self.results) > max_results:
                    self.results = self.results[-max_results:]
                
                # 输出测试结果
                logger.info(f"测试完成: 总计 {test_result['test_count']} 个测试，"
                           f"成功 {test_result['success_count']} 个，"
                           f"失败 {test_result['failure_count']} 个，"
                           f"错误 {test_result['error_count']} 个，"
                           f"耗时 {test_result['duration']:.2f} 秒，"
                           f"成功率 {test_result['success_rate']:.2%}")
            
            except Exception as e:
                logger.error(f"运行测试时发生错误: {e}")
            
            # 等待下一次测试
            time.sleep(self.interval)


class TestExecutionManager:
    """测试执行管理器"""
    
    def __init__(self):
        """初始化测试执行管理器"""
        self.parallel_executor = ParallelTestExecutor()
        self.scheduled_executor = ScheduledTestExecutor()
        self.continuous_executors = {}
    
    def run_tests_in_parallel(self, tests: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """并行运行测试
        
        Args:
            tests: 测试列表，每个测试是一个字典，包含模块、类和方法
        
        Returns:
            测试结果列表
        """
        return self.parallel_executor.run_tests_in_parallel(tests)
    
    def schedule_test(self, test_func: Callable, interval: str, *args, **kwargs):
        """添加定时测试
        
        Args:
            test_func: 测试函数
            interval: 时间间隔，格式为"every X seconds/minutes/hours/days at HH:MM"
            *args: 测试函数参数
            **kwargs: 测试函数关键字参数
        """
        self.scheduled_executor.add_test(test_func, interval, *args, **kwargs)
    
    def start_scheduled_tests(self):
        """启动定时测试"""
        self.scheduled_executor.start()
    
    def stop_scheduled_tests(self):
        """停止定时测试"""
        self.scheduled_executor.stop()
    
    def start_continuous_tests(self, test_dir: str, interval: float = 60.0) -> str:
        """启动持续测试
        
        Args:
            test_dir: 测试目录
            interval: 测试间隔（秒）
        
        Returns:
            测试ID
        """
        # 创建测试ID
        test_id = f"continuous_{time.time()}"
        
        # 创建持续测试执行器
        executor = ContinuousTestExecutor(test_dir, interval)
        
        # 启动执行器
        executor.start()
        
        # 保存执行器
        self.continuous_executors[test_id] = executor
        
        return test_id
    
    def stop_continuous_tests(self, test_id: str):
        """停止持续测试
        
        Args:
            test_id: 测试ID
        """
        if test_id not in self.continuous_executors:
            logger.warning(f"未知的测试ID: {test_id}")
            return
        
        # 停止执行器
        executor = self.continuous_executors[test_id]
        executor.stop()
        
        # 移除执行器
        del self.continuous_executors[test_id]
    
    def get_continuous_test_results(self, test_id: str) -> List[Dict[str, Any]]:
        """获取持续测试结果
        
        Args:
            test_id: 测试ID
        
        Returns:
            测试结果列表
        """
        if test_id not in self.continuous_executors:
            logger.warning(f"未知的测试ID: {test_id}")
            return []
        
        # 获取测试结果
        executor = self.continuous_executors[test_id]
        return executor.get_results()
    
    def stop_all_tests(self):
        """停止所有测试"""
        # 停止定时测试
        self.stop_scheduled_tests()
        
        # 停止所有持续测试
        for test_id in list(self.continuous_executors.keys()):
            self.stop_continuous_tests(test_id)
        
        logger.info("已停止所有测试")
