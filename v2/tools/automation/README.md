# 自动化测试工具

本目录包含自动化测试工具，用于自动化生成测试用例、执行测试和生成测试报告。

## 工具组件

### 测试基础类

- `AutomatedTestCase`: 自动化测试用例基类
- `AutomatedTestSuite`: 自动化测试套件
- `AutomatedTestResult`: 自动化测试结果
- `AutomatedTestRunner`: 自动化测试运行器

### 测试用例生成器

- `TestCaseGenerator`: 测试用例生成器
- `APITestGenerator`: API测试生成器

### 测试执行器

- `ParallelTestExecutor`: 并行测试执行器
- `ScheduledTestExecutor`: 定时测试执行器
- `ContinuousTestExecutor`: 持续测试执行器
- `TestExecutionManager`: 测试执行管理器

### 测试报告生成器

- `TestReportGenerator`: 测试报告生成器

### 自动化测试工具

- `AutomatedTestTool`: 自动化测试工具主类

## 使用说明

### 生成测试套件

```bash
python -m v2.tools.automation.automate_tests generate --template basic_test --suite example_tests --config example_config.json --output-dir v2/tools/automation
```

配置文件示例：

```json
{
  "classes": [
    {
      "name": "ExampleTest",
      "methods": [
        {
          "name": "test_example",
          "parameters": {
            "description": "示例测试",
            "test_code": "result = 1 + 1",
            "assertions": "self.assertEqual(result, 2)"
          }
        }
      ]
    }
  ]
}
```

### 生成API测试套件

```bash
python -m v2.tools.automation.automate_tests api --module example_module --path path/to/example_module.py --output-dir v2/tools/automation
```

### 运行测试

```bash
python -m v2.tools.automation.automate_tests run --config tests_config.json --report test_report.html
```

配置文件示例：

```json
{
  "tests": [
    {
      "module": "v2.tools.automation.example_tests.test_exampletest",
      "class": "ExampleTest",
      "method": "test_example"
    }
  ]
}
```

### 启动持续测试

```bash
python -m v2.tools.automation.automate_tests continuous --dir v2/tools/automation/example_tests --interval 60
```

## 编程接口

### 创建测试用例

```python
from v2.tools.automation import AutomatedTestCase

class MyTest(AutomatedTestCase):
    def test_example(self):
        result = 1 + 1
        self.assertEqual(result, 2)
```

### 生成测试套件

```python
from v2.tools.automation import TestCaseGenerator

generator = TestCaseGenerator()
generator.register_template(my_template)

test_classes = generator.generate_test_suite("my_template", "my_suite", classes)
generator.save_test_suite(test_classes, "output_dir", "my_suite")
```

### 执行测试

```python
from v2.tools.automation import TestExecutionManager

executor = TestExecutionManager()
results = executor.run_tests_in_parallel(tests)
```

### 生成测试报告

```python
from v2.tools.automation import TestReportGenerator

generator = TestReportGenerator()
generator.generate_report(results, "report.html")
```

### 使用自动化测试工具

```python
from v2.tools.automation import AutomatedTestTool

tool = AutomatedTestTool()
tool.generate_test_suite("my_template", "my_suite", classes, "output_dir")
results = tool.run_tests_in_parallel(tests)
tool.generate_report(results, "report.html")
```

## 注意事项

- 测试用例应该继承自`AutomatedTestCase`
- 测试套件应该使用`AutomatedTestSuite`
- 测试运行器应该使用`AutomatedTestRunner`
- 测试报告会以HTML格式生成
- 并行执行测试时，每个测试会在单独的进程中运行
- 定时测试和持续测试会在后台线程中运行
