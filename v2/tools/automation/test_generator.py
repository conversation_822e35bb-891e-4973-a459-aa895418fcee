#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
自动化测试用例生成器

本模块提供了自动化生成测试用例的功能，包括从模板生成测试用例和从API生成测试用例。
"""

import os
import sys
import logging
import time
import json
import inspect
import importlib
import random
import string
import re
from typing import Dict, List, Any, Optional, Tuple, Callable, Union

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入测试基础类
from v2.tools.automation.test_base import AutomatedTestCase, AutomatedTestSuite

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TestCaseTemplate:
    """测试用例模板"""
    
    def __init__(self, name: str, description: str, code_template: str, parameters: Dict[str, Any] = None):
        """初始化测试用例模板
        
        Args:
            name: 模板名称
            description: 模板描述
            code_template: 代码模板
            parameters: 模板参数
        """
        self.name = name
        self.description = description
        self.code_template = code_template
        self.parameters = parameters or {}
    
    def generate_test_case(self, class_name: str, method_name: str, parameters: Dict[str, Any] = None) -> str:
        """生成测试用例代码
        
        Args:
            class_name: 测试类名称
            method_name: 测试方法名称
            parameters: 测试参数
        
        Returns:
            测试用例代码
        """
        # 合并参数
        params = self.parameters.copy()
        if parameters:
            params.update(parameters)
        
        # 替换模板中的参数
        code = self.code_template
        for key, value in params.items():
            placeholder = f"{{{{ {key} }}}}"
            if isinstance(value, str):
                code = code.replace(placeholder, value)
            else:
                code = code.replace(placeholder, str(value))
        
        # 替换类名和方法名
        code = code.replace("{{ class_name }}", class_name)
        code = code.replace("{{ method_name }}", method_name)
        
        return code


class TestCaseGenerator:
    """测试用例生成器"""
    
    def __init__(self):
        """初始化测试用例生成器"""
        self.templates = {}
    
    def register_template(self, template: TestCaseTemplate):
        """注册测试用例模板
        
        Args:
            template: 测试用例模板
        """
        self.templates[template.name] = template
    
    def generate_test_case(self, template_name: str, class_name: str, method_name: str, parameters: Dict[str, Any] = None) -> str:
        """生成测试用例代码
        
        Args:
            template_name: 模板名称
            class_name: 测试类名称
            method_name: 测试方法名称
            parameters: 测试参数
        
        Returns:
            测试用例代码
        """
        if template_name not in self.templates:
            raise ValueError(f"未知的模板名称: {template_name}")
        
        template = self.templates[template_name]
        return template.generate_test_case(class_name, method_name, parameters)
    
    def generate_test_class(self, template_name: str, class_name: str, methods: List[Dict[str, Any]]) -> str:
        """生成测试类代码
        
        Args:
            template_name: 模板名称
            class_name: 测试类名称
            methods: 测试方法列表，每个方法是一个字典，包含方法名称和参数
        
        Returns:
            测试类代码
        """
        if template_name not in self.templates:
            raise ValueError(f"未知的模板名称: {template_name}")
        
        # 生成导入语句
        imports = f"""
import unittest
import logging
import time
import json
from typing import Dict, List, Any, Optional, Tuple, Callable, Union

# 添加项目根目录到Python路径
import os
import sys
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入测试基础类
from v2.tools.automation.test_base import AutomatedTestCase

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
"""
        
        # 生成类定义
        class_def = f"""
class {class_name}(AutomatedTestCase):
    \"\"\"自动生成的测试类\"\"\"
    
    def setUp(self):
        \"\"\"测试前的准备工作\"\"\"
        super().setUp()
        # 在这里添加测试前的准备工作
    
    def tearDown(self):
        \"\"\"测试后的清理工作\"\"\"
        # 在这里添加测试后的清理工作
        super().tearDown()
"""
        
        # 生成测试方法
        methods_code = ""
        for method in methods:
            method_name = method["name"]
            parameters = method.get("parameters", {})
            
            method_code = self.generate_test_case(template_name, class_name, method_name, parameters)
            methods_code += f"\n{method_code}\n"
        
        # 生成主函数
        main_func = """
if __name__ == "__main__":
    unittest.main()
"""
        
        # 组合代码
        code = f"{imports}\n{class_def}\n{methods_code}\n{main_func}"
        
        return code
    
    def save_test_class(self, code: str, file_path: str):
        """保存测试类代码到文件
        
        Args:
            code: 测试类代码
            file_path: 文件路径
        """
        # 创建目录
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 保存代码
        with open(file_path, 'w') as f:
            f.write(code)
        
        logger.info(f"测试类代码已保存到: {file_path}")
    
    def generate_test_suite(self, template_name: str, suite_name: str, classes: List[Dict[str, Any]]) -> Dict[str, str]:
        """生成测试套件
        
        Args:
            template_name: 模板名称
            suite_name: 测试套件名称
            classes: 测试类列表，每个类是一个字典，包含类名称和方法列表
        
        Returns:
            测试类代码字典，键为类名称，值为类代码
        """
        test_classes = {}
        
        for class_info in classes:
            class_name = class_info["name"]
            methods = class_info["methods"]
            
            code = self.generate_test_class(template_name, class_name, methods)
            test_classes[class_name] = code
        
        return test_classes
    
    def save_test_suite(self, test_classes: Dict[str, str], output_dir: str, suite_name: str):
        """保存测试套件到目录
        
        Args:
            test_classes: 测试类代码字典，键为类名称，值为类代码
            output_dir: 输出目录
            suite_name: 测试套件名称
        """
        # 创建套件目录
        suite_dir = os.path.join(output_dir, suite_name)
        os.makedirs(suite_dir, exist_ok=True)
        
        # 创建__init__.py文件
        init_file = os.path.join(suite_dir, "__init__.py")
        with open(init_file, 'w') as f:
            f.write(f'"""\n{suite_name} 测试套件\n"""\n')
        
        # 保存测试类
        for class_name, code in test_classes.items():
            file_name = f"test_{class_name.lower()}.py"
            file_path = os.path.join(suite_dir, file_name)
            self.save_test_class(code, file_path)
        
        # 创建套件运行器
        runner_code = f"""#!/usr/bin/env python3
# -*- coding: utf-8 -*-

\"\"\"
{suite_name} 测试套件运行器
\"\"\"

import unittest
import logging
import time
import json
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

# 导入测试基础类
from v2.tools.automation.test_base import AutomatedTestSuite, AutomatedTestRunner

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入测试类
"""
        
        for class_name in test_classes.keys():
            file_name = f"test_{class_name.lower()}"
            runner_code += f"from v2.tools.automation.{suite_name}.{file_name} import {class_name}\n"
        
        runner_code += f"""
def run_tests():
    \"\"\"运行测试套件\"\"\"
    # 创建测试套件
    suite = AutomatedTestSuite(name="{suite_name}", description="{suite_name} 测试套件")
    
    # 添加测试类
"""
        
        for class_name in test_classes.keys():
            runner_code += f"    suite.addTest(unittest.makeSuite({class_name}))\n"
        
        runner_code += """
    # 创建测试运行器
    runner = AutomatedTestRunner(verbosity=2)
    
    # 运行测试
    result = runner.run(suite)
    
    # 输出测试结果
    logger.info(f"测试结果: 运行 {result.testsRun} 个测试")
    logger.info(f"成功: {len(result.successes)}")
    logger.info(f"失败: {len(result.failures)}")
    logger.info(f"错误: {len(result.errors)}")
    
    # 返回测试结果
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
"""
        
        # 保存套件运行器
        runner_file = os.path.join(suite_dir, "run_tests.py")
        with open(runner_file, 'w') as f:
            f.write(runner_code)
        
        # 设置运行器可执行权限
        os.chmod(runner_file, 0o755)
        
        logger.info(f"测试套件已保存到: {suite_dir}")


class APITestGenerator:
    """API测试生成器"""
    
    def __init__(self):
        """初始化API测试生成器"""
        self.test_generator = TestCaseGenerator()
        self.api_modules = {}
    
    def register_api_module(self, module_name: str, module_path: str):
        """注册API模块
        
        Args:
            module_name: 模块名称
            module_path: 模块路径
        """
        try:
            # 导入模块
            spec = importlib.util.spec_from_file_location(module_name, module_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 注册模块
            self.api_modules[module_name] = module
            
            logger.info(f"已注册API模块: {module_name}")
        
        except Exception as e:
            logger.error(f"注册API模块 {module_name} 失败: {e}")
    
    def generate_api_test_case(self, module_name: str, class_name: str, method_name: str) -> Dict[str, Any]:
        """生成API测试用例
        
        Args:
            module_name: 模块名称
            class_name: 类名称
            method_name: 方法名称
        
        Returns:
            测试用例信息
        """
        if module_name not in self.api_modules:
            raise ValueError(f"未知的模块名称: {module_name}")
        
        module = self.api_modules[module_name]
        
        # 获取类
        if not hasattr(module, class_name):
            raise ValueError(f"模块 {module_name} 中没有类 {class_name}")
        
        cls = getattr(module, class_name)
        
        # 获取方法
        if not hasattr(cls, method_name):
            raise ValueError(f"类 {class_name} 中没有方法 {method_name}")
        
        method = getattr(cls, method_name)
        
        # 获取方法签名
        sig = inspect.signature(method)
        
        # 生成测试参数
        parameters = {}
        for param_name, param in sig.parameters.items():
            if param_name == 'self':
                continue
            
            # 根据参数类型生成测试值
            if param.annotation == int:
                parameters[param_name] = random.randint(1, 100)
            elif param.annotation == float:
                parameters[param_name] = random.uniform(1.0, 100.0)
            elif param.annotation == str:
                parameters[param_name] = ''.join(random.choices(string.ascii_letters, k=10))
            elif param.annotation == bool:
                parameters[param_name] = random.choice([True, False])
            elif param.annotation == list:
                parameters[param_name] = [random.randint(1, 100) for _ in range(5)]
            elif param.annotation == dict:
                parameters[param_name] = {f'key{i}': random.randint(1, 100) for i in range(5)}
            else:
                # 默认值
                parameters[param_name] = None
        
        # 生成测试用例信息
        test_case = {
            "module_name": module_name,
            "class_name": class_name,
            "method_name": method_name,
            "parameters": parameters
        }
        
        return test_case
    
    def generate_api_test_suite(self, module_name: str, output_dir: str):
        """生成API测试套件
        
        Args:
            module_name: 模块名称
            output_dir: 输出目录
        """
        if module_name not in self.api_modules:
            raise ValueError(f"未知的模块名称: {module_name}")
        
        module = self.api_modules[module_name]
        
        # 获取模块中的所有类
        classes = []
        for class_name, cls in inspect.getmembers(module, inspect.isclass):
            # 跳过导入的类
            if cls.__module__ != module.__name__:
                continue
            
            # 获取类中的所有方法
            methods = []
            for method_name, method in inspect.getmembers(cls, inspect.isfunction):
                # 跳过私有方法
                if method_name.startswith('_'):
                    continue
                
                # 生成测试用例
                test_case = self.generate_api_test_case(module_name, class_name, method_name)
                
                # 添加到方法列表
                methods.append({
                    "name": f"test_{method_name}",
                    "parameters": {
                        "method_name": method_name,
                        "parameters": json.dumps(test_case["parameters"], indent=2)
                    }
                })
            
            # 添加到类列表
            if methods:
                classes.append({
                    "name": f"{class_name}Test",
                    "methods": methods
                })
        
        # 生成测试套件
        suite_name = f"{module_name}_tests"
        test_classes = self.test_generator.generate_test_suite("api_test", suite_name, classes)
        
        # 保存测试套件
        self.test_generator.save_test_suite(test_classes, output_dir, suite_name)
        
        logger.info(f"API测试套件已生成: {os.path.join(output_dir, suite_name)}")


# 注册测试用例模板
def register_default_templates(generator: TestCaseGenerator):
    """注册默认测试用例模板
    
    Args:
        generator: 测试用例生成器
    """
    # 基本测试模板
    basic_template = TestCaseTemplate(
        name="basic_test",
        description="基本测试模板",
        code_template="""
    def {{ method_name }}(self):
        \"\"\"{{ description }}\"\"\"
        logger.info("运行测试: {{ method_name }}")
        
        # 测试代码
        {{ test_code }}
        
        # 断言
        {{ assertions }}
"""
    )
    generator.register_template(basic_template)
    
    # API测试模板
    api_test_template = TestCaseTemplate(
        name="api_test",
        description="API测试模板",
        code_template="""
    def {{ method_name }}(self):
        \"\"\"测试 {{ method_name }}\"\"\"
        logger.info("测试 {{ method_name }}")
        
        try:
            # 创建测试对象
            obj = self.create_test_object()
            
            # 准备参数
            parameters = {{ parameters }}
            
            # 调用方法
            result = getattr(obj, "{{ method_name }}")(**parameters)
            
            # 验证结果
            self.assertIsNotNone(result)
            
            logger.info(f"测试 {{ method_name }} 成功")
        
        except Exception as e:
            logger.error(f"测试 {{ method_name }} 失败: {e}")
            raise
    
    def create_test_object(self):
        \"\"\"创建测试对象\"\"\"
        # 在这里创建测试对象
        return object()
"""
    )
    generator.register_template(api_test_template)
    
    # 性能测试模板
    performance_test_template = TestCaseTemplate(
        name="performance_test",
        description="性能测试模板",
        code_template="""
    def {{ method_name }}(self):
        \"\"\"{{ description }}\"\"\"
        logger.info("运行性能测试: {{ method_name }}")
        
        # 准备测试数据
        {{ test_data }}
        
        # 记录开始时间
        start_time = time.time()
        
        # 执行测试
        {{ test_code }}
        
        # 记录结束时间
        end_time = time.time()
        duration = end_time - start_time
        
        # 验证性能
        logger.info(f"测试 {{ method_name }} 耗时: {duration:.6f} 秒")
        self.assertLess(duration, {{ max_duration }})
"""
    )
    generator.register_template(performance_test_template)
    
    # 并发测试模板
    concurrency_test_template = TestCaseTemplate(
        name="concurrency_test",
        description="并发测试模板",
        code_template="""
    def {{ method_name }}(self):
        \"\"\"{{ description }}\"\"\"
        logger.info("运行并发测试: {{ method_name }}")
        
        import threading
        import queue
        
        # 准备测试数据
        {{ test_data }}
        
        # 创建结果队列
        result_queue = queue.Queue()
        
        # 创建线程函数
        def thread_func(thread_id):
            try:
                # 执行测试
                {{ test_code }}
                
                # 将结果放入队列
                result_queue.put((thread_id, True, None))
            
            except Exception as e:
                # 将异常放入队列
                result_queue.put((thread_id, False, str(e)))
        
        # 创建线程
        threads = []
        for i in range({{ thread_count }}):
            thread = threading.Thread(target=thread_func, args=(i,))
            threads.append(thread)
        
        # 启动线程
        for thread in threads:
            thread.start()
        
        # 等待线程结束
        for thread in threads:
            thread.join()
        
        # 检查结果
        success_count = 0
        failure_count = 0
        
        while not result_queue.empty():
            thread_id, success, error = result_queue.get()
            if success:
                success_count += 1
            else:
                failure_count += 1
                logger.error(f"线程 {thread_id} 失败: {error}")
        
        # 验证结果
        logger.info(f"并发测试结果: 成功 {success_count}, 失败 {failure_count}")
        self.assertEqual(failure_count, 0)
"""
    )
    generator.register_template(concurrency_test_template)
    
    logger.info("已注册默认测试用例模板")
