#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
自动化测试工具

本模块提供了自动化测试的主要功能，包括生成测试用例、执行测试和生成测试报告。
"""

import os
import sys
import argparse
import logging
import time
import json
import importlib
from typing import Dict, List, Any, Optional, Tuple, Callable, Union

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 导入测试模块
from v2.tools.automation.test_base import AutomatedTestCase, AutomatedTestSuite, AutomatedTestResult, AutomatedTestRunner
from v2.tools.automation.test_generator import TestCaseGenerator, APITestGenerator, register_default_templates
from v2.tools.automation.test_executor import TestExecutionManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TestReportGenerator:
    """测试报告生成器"""
    
    def __init__(self):
        """初始化测试报告生成器"""
        pass
    
    def generate_report(self, results: List[Dict[str, Any]], output_file: str):
        """生成测试报告
        
        Args:
            results: 测试结果列表
            output_file: 输出文件
        """
        logger.info(f"生成测试报告: {output_file}")
        
        # 计算统计数据
        total_tests = len(results)
        success_count = sum(1 for result in results if result.get("success", False))
        failure_count = total_tests - success_count
        
        # 创建HTML报告
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>自动化测试报告</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    margin: 20px;
                    background-color: #f5f5f5;
                }}
                h1, h2, h3 {{
                    color: #333;
                }}
                .container {{
                    max-width: 1200px;
                    margin: 0 auto;
                    background-color: white;
                    padding: 20px;
                    border-radius: 5px;
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                }}
                .summary {{
                    margin-bottom: 20px;
                    padding: 15px;
                    background-color: #e9f7ef;
                    border-radius: 5px;
                }}
                table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }}
                th, td {{
                    padding: 10px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }}
                th {{
                    background-color: #f2f2f2;
                }}
                tr:hover {{
                    background-color: #f5f5f5;
                }}
                .success {{
                    color: green;
                }}
                .failure {{
                    color: red;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>自动化测试报告</h1>
                <p>生成时间: {time.strftime("%Y-%m-%d %H:%M:%S")}</p>
                
                <div class="summary">
                    <h2>测试摘要</h2>
                    <p>总测试数: {total_tests}</p>
                    <p>成功: <span class="success">{success_count}</span></p>
                    <p>失败: <span class="failure">{failure_count}</span></p>
                    <p>成功率: <span class="{
                        "success" if success_count / total_tests >= 0.9 else "failure"
                    }">{success_count / total_tests:.2%}</span></p>
                </div>
                
                <h2>测试结果</h2>
                <table>
                    <tr>
                        <th>模块</th>
                        <th>类</th>
                        <th>方法</th>
                        <th>结果</th>
                        <th>详情</th>
                    </tr>
        """
        
        # 添加测试结果
        for result in results:
            module = result.get("module", "")
            class_name = result.get("class", "")
            method = result.get("method", "")
            success = result.get("success", False)
            
            # 获取详情
            details = ""
            if "error" in result:
                details = result["error"]
            elif "test_count" in result:
                details = f"测试: {result['test_count']}, 成功: {result['success_count']}, 失败: {result['failure_count']}, 错误: {result['error_count']}"
            
            html += f"""
                    <tr>
                        <td>{module}</td>
                        <td>{class_name}</td>
                        <td>{method or "所有方法"}</td>
                        <td class="{'success' if success else 'failure'}">{
                            "成功" if success else "失败"
                        }</td>
                        <td>{details}</td>
                    </tr>
            """
        
        html += """
                </table>
            </div>
        </body>
        </html>
        """
        
        # 保存HTML报告
        with open(output_file, 'w') as f:
            f.write(html)
        
        logger.info(f"测试报告已生成: {output_file}")


class AutomatedTestTool:
    """自动化测试工具"""
    
    def __init__(self):
        """初始化自动化测试工具"""
        self.test_generator = TestCaseGenerator()
        self.api_test_generator = APITestGenerator()
        self.test_executor = TestExecutionManager()
        self.report_generator = TestReportGenerator()
        
        # 注册默认模板
        register_default_templates(self.test_generator)
    
    def generate_test_suite(self, template_name: str, suite_name: str, classes: List[Dict[str, Any]], output_dir: str):
        """生成测试套件
        
        Args:
            template_name: 模板名称
            suite_name: 测试套件名称
            classes: 测试类列表，每个类是一个字典，包含类名称和方法列表
            output_dir: 输出目录
        """
        # 生成测试套件
        test_classes = self.test_generator.generate_test_suite(template_name, suite_name, classes)
        
        # 保存测试套件
        self.test_generator.save_test_suite(test_classes, output_dir, suite_name)
    
    def generate_api_test_suite(self, module_name: str, module_path: str, output_dir: str):
        """生成API测试套件
        
        Args:
            module_name: 模块名称
            module_path: 模块路径
            output_dir: 输出目录
        """
        # 注册API模块
        self.api_test_generator.register_api_module(module_name, module_path)
        
        # 生成API测试套件
        self.api_test_generator.generate_api_test_suite(module_name, output_dir)
    
    def run_tests_in_parallel(self, tests: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """并行运行测试
        
        Args:
            tests: 测试列表，每个测试是一个字典，包含模块、类和方法
        
        Returns:
            测试结果列表
        """
        return self.test_executor.run_tests_in_parallel(tests)
    
    def schedule_test(self, test_func: Callable, interval: str, *args, **kwargs):
        """添加定时测试
        
        Args:
            test_func: 测试函数
            interval: 时间间隔，格式为"every X seconds/minutes/hours/days at HH:MM"
            *args: 测试函数参数
            **kwargs: 测试函数关键字参数
        """
        self.test_executor.schedule_test(test_func, interval, *args, **kwargs)
    
    def start_scheduled_tests(self):
        """启动定时测试"""
        self.test_executor.start_scheduled_tests()
    
    def stop_scheduled_tests(self):
        """停止定时测试"""
        self.test_executor.stop_scheduled_tests()
    
    def start_continuous_tests(self, test_dir: str, interval: float = 60.0) -> str:
        """启动持续测试
        
        Args:
            test_dir: 测试目录
            interval: 测试间隔（秒）
        
        Returns:
            测试ID
        """
        return self.test_executor.start_continuous_tests(test_dir, interval)
    
    def stop_continuous_tests(self, test_id: str):
        """停止持续测试
        
        Args:
            test_id: 测试ID
        """
        self.test_executor.stop_continuous_tests(test_id)
    
    def get_continuous_test_results(self, test_id: str) -> List[Dict[str, Any]]:
        """获取持续测试结果
        
        Args:
            test_id: 测试ID
        
        Returns:
            测试结果列表
        """
        return self.test_executor.get_continuous_test_results(test_id)
    
    def stop_all_tests(self):
        """停止所有测试"""
        self.test_executor.stop_all_tests()
    
    def generate_report(self, results: List[Dict[str, Any]], output_file: str):
        """生成测试报告
        
        Args:
            results: 测试结果列表
            output_file: 输出文件
        """
        self.report_generator.generate_report(results, output_file)


def main():
    """主函数"""
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='自动化测试工具')
    subparsers = parser.add_subparsers(dest='command', help='命令')
    
    # 生成测试套件命令
    generate_parser = subparsers.add_parser('generate', help='生成测试套件')
    generate_parser.add_argument('--template', type=str, required=True, help='模板名称')
    generate_parser.add_argument('--suite', type=str, required=True, help='测试套件名称')
    generate_parser.add_argument('--config', type=str, required=True, help='配置文件')
    generate_parser.add_argument('--output-dir', type=str, default='v2/tools/automation', help='输出目录')
    
    # 生成API测试套件命令
    api_parser = subparsers.add_parser('api', help='生成API测试套件')
    api_parser.add_argument('--module', type=str, required=True, help='模块名称')
    api_parser.add_argument('--path', type=str, required=True, help='模块路径')
    api_parser.add_argument('--output-dir', type=str, default='v2/tools/automation', help='输出目录')
    
    # 运行测试命令
    run_parser = subparsers.add_parser('run', help='运行测试')
    run_parser.add_argument('--config', type=str, required=True, help='配置文件')
    run_parser.add_argument('--report', type=str, help='报告文件')
    
    # 启动持续测试命令
    continuous_parser = subparsers.add_parser('continuous', help='启动持续测试')
    continuous_parser.add_argument('--dir', type=str, required=True, help='测试目录')
    continuous_parser.add_argument('--interval', type=float, default=60.0, help='测试间隔（秒）')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 创建自动化测试工具
    tool = AutomatedTestTool()
    
    # 执行命令
    if args.command == 'generate':
        # 加载配置文件
        with open(args.config, 'r') as f:
            config = json.load(f)
        
        # 生成测试套件
        tool.generate_test_suite(args.template, args.suite, config["classes"], args.output_dir)
    
    elif args.command == 'api':
        # 生成API测试套件
        tool.generate_api_test_suite(args.module, args.path, args.output_dir)
    
    elif args.command == 'run':
        # 加载配置文件
        with open(args.config, 'r') as f:
            config = json.load(f)
        
        # 运行测试
        results = tool.run_tests_in_parallel(config["tests"])
        
        # 生成报告
        if args.report:
            tool.generate_report(results, args.report)
    
    elif args.command == 'continuous':
        # 启动持续测试
        test_id = tool.start_continuous_tests(args.dir, args.interval)
        
        try:
            # 等待用户中断
            print(f"持续测试已启动，测试ID: {test_id}")
            print("按Ctrl+C停止测试")
            
            while True:
                time.sleep(1)
        
        except KeyboardInterrupt:
            # 停止测试
            tool.stop_continuous_tests(test_id)
            print("持续测试已停止")
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
