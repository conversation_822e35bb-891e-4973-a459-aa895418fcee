[package]
name = "aqfh_rust_final"
version = "0.1.0"
edition = "2021"

[lib]
name = "aqfh_rust_final"
crate-type = ["cdylib"]

[dependencies]
# PyO3 for Python 3.13.3 with latest API (Context7 verified)
pyo3 = { version = "0.24.0", features = ["extension-module", "abi3-py313"] }
uuid = { version = "1.10.0", features = ["v4"] }

# NumPy 2.2.5 compatibility (Context7 verified - API version 19)
numpy = "0.22.0"

# Basic serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

[build-dependencies]
pyo3-build-config = { version = "0.24.0", features = ["resolve-config"] }

[profile.release]
opt-level = 3
lto = true
