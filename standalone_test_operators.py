"""
独立的算子测试脚本

本脚本测试TransformOperator和EvolutionOperator的组合。
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Any, Dict, List, Optional, Tuple, Union, Callable
from abc import ABC, abstractmethod


# 定义OperatorInterface接口
class OperatorInterface(ABC):
    """超越态算子接口基类"""
    
    @abstractmethod
    def __init__(self, **kwargs):
        """初始化算子"""
        pass
    
    @abstractmethod
    def apply(self, input_data: Any, **kwargs) -> Any:
        """应用算子到输入数据"""
        pass
    
    @abstractmethod
    def get_metadata(self) -> Dict[str, Any]:
        """获取算子元数据"""
        pass
    
    @abstractmethod
    def is_compatible_with(self, other_operator: 'OperatorInterface') -> bool:
        """检查与其他算子的兼容性"""
        pass
    
    @abstractmethod
    def get_performance_metrics(self) -> Dict[str, float]:
        """获取性能指标"""
        pass
    
    @abstractmethod
    def compose(self, other_operator: 'OperatorInterface') -> 'OperatorInterface':
        """与其他算子组合"""
        pass
    
    @abstractmethod
    def get_parameters(self) -> Dict[str, Any]:
        """获取算子参数"""
        pass
    
    @abstractmethod
    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """设置算子参数"""
        pass
    
    @abstractmethod
    def to_rust(self) -> bool:
        """检查是否有Rust实现"""
        pass
    
    @abstractmethod
    def get_complexity(self) -> Dict[str, Any]:
        """获取算子复杂度信息"""
        pass


# 实现TransformOperator
class TransformOperator(OperatorInterface):
    """简化的变换算子类"""
    
    def __init__(self, transform_type: str = 'linear', dimension: int = 3, parameters: Optional[Dict[str, Any]] = None, **kwargs):
        """初始化变换算子"""
        self.transform_type = transform_type
        self.dimension = dimension
        self.parameters = parameters or {}
        self.name = "TransformOperator"
        
        # 初始化变换矩阵（如果是线性变换）
        if transform_type == 'linear' and 'matrix' not in self.parameters:
            self.parameters['matrix'] = np.eye(dimension)
    
    def apply(self, input_data: Any, **kwargs) -> Any:
        """应用变换到输入数据"""
        # 提取参数
        inplace = kwargs.get('inplace', False)
        inverse = kwargs.get('inverse', False)
        
        # 预处理输入数据
        data = input_data
        
        # 如果不是原地修改，创建数据副本
        if not inplace:
            data = data.copy()
        
        # 应用变换
        if self.transform_type == 'linear':
            if inverse:
                # 线性变换的逆变换
                matrix = self.parameters.get('matrix', np.eye(self.dimension))
                offset = self.parameters.get('offset', np.zeros(self.dimension))
                
                # 计算矩阵的逆
                try:
                    inv_matrix = np.linalg.inv(matrix)
                except np.linalg.LinAlgError:
                    raise ValueError("Matrix is not invertible")
                
                # 应用逆变换: x = A^(-1)(y - b)
                result = np.dot(data - offset, inv_matrix.T)
            else:
                # 线性变换
                matrix = self.parameters.get('matrix', np.eye(self.dimension))
                offset = self.parameters.get('offset', np.zeros(self.dimension))
                
                # 应用线性变换: y = Ax + b
                result = np.dot(data, matrix.T) + offset
        else:
            # 简化实现，返回输入数据
            result = data
        
        return result
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取算子元数据"""
        return {
            "name": self.name,
            "type": "transform",
            "transform_type": self.transform_type,
            "dimension": self.dimension,
            "description": "Transform operator for applying various transformations to data"
        }
    
    def is_compatible_with(self, other_operator: 'OperatorInterface') -> bool:
        """检查与其他算子的兼容性"""
        # 简化实现，始终返回True
        return True
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """获取性能指标"""
        return {
            "time_complexity": 1.0,
            "space_complexity": 1.0,
            "numerical_stability": 0.9,
            "parallelizability": 0.8
        }
    
    def compose(self, other_operator: 'OperatorInterface') -> 'OperatorInterface':
        """与其他算子组合"""
        # 创建复合算子
        return CompositeOperator([self, other_operator])
    
    def get_parameters(self) -> Dict[str, Any]:
        """获取算子参数"""
        return {
            "transform_type": self.transform_type,
            "dimension": self.dimension,
            "parameters": self.parameters
        }
    
    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """设置算子参数"""
        if "transform_type" in parameters:
            self.transform_type = parameters["transform_type"]
        if "dimension" in parameters:
            self.dimension = parameters["dimension"]
        if "parameters" in parameters:
            self.parameters = parameters["parameters"]
    
    def to_rust(self) -> bool:
        """检查是否有Rust实现"""
        return False
    
    def get_complexity(self) -> Dict[str, Any]:
        """获取算子复杂度信息"""
        return {
            "time_complexity": "O(n)",
            "space_complexity": "O(n)",
            "computational_complexity": "Low",
            "numerical_stability": "High",
            "parallelizable": True
        }


# 实现EvolutionOperator
class EvolutionOperator(OperatorInterface):
    """简化的演化算子类"""
    
    def __init__(self, evolution_type: str = 'differential_equation', dimension: int = 3, parameters: Optional[Dict[str, Any]] = None, **kwargs):
        """初始化演化算子"""
        self.evolution_type = evolution_type
        self.dimension = dimension
        self.parameters = parameters or {}
        self.name = "EvolutionOperator"
        
        # 初始化默认参数
        self._init_default_parameters()
    
    def _init_default_parameters(self):
        """初始化默认参数"""
        if self.evolution_type == 'differential_equation' and 'equation' not in self.parameters:
            # 默认为简单的线性系统
            self.parameters['equation'] = lambda t, x: np.zeros_like(x)
            self.parameters['time_step'] = 0.01
            self.parameters['num_steps'] = 100
    
    def apply(self, input_data: Any, **kwargs) -> Any:
        """应用演化到输入数据"""
        # 提取参数
        return_trajectory = kwargs.get('return_trajectory', False)
        
        # 预处理输入数据
        data = input_data
        
        # 应用演化
        if self.evolution_type == 'differential_equation':
            # 从参数中提取微分方程和时间步长
            equation = self.parameters.get('equation', lambda t, x: np.zeros_like(x))
            time_step = self.parameters.get('time_step', 0.01)
            num_steps = self.parameters.get('num_steps', 100)
            
            # 初始化轨迹
            trajectory = [data.copy()]
            
            # 使用数值方法求解微分方程
            method = self.parameters.get('method', 'euler')
            
            if method == 'euler':
                # 欧拉方法
                for i in range(num_steps):
                    t = i * time_step
                    dt = time_step
                    x = trajectory[-1]
                    dx = equation(t, x)
                    trajectory.append(x + dx * dt)
            
            elif method == 'rk4':
                # 四阶龙格-库塔方法
                for i in range(num_steps):
                    t = i * time_step
                    dt = time_step
                    x = trajectory[-1]
                    
                    k1 = equation(t, x)
                    k2 = equation(t + dt/2, x + k1 * dt/2)
                    k3 = equation(t + dt/2, x + k2 * dt/2)
                    k4 = equation(t + dt, x + k3 * dt)
                    
                    trajectory.append(x + (k1 + 2*k2 + 2*k3 + k4) * dt/6)
            
            # 返回结果
            if return_trajectory:
                return trajectory
            else:
                return trajectory[-1]
        else:
            # 简化实现，返回输入数据
            if return_trajectory:
                return [data.copy(), data.copy()]
            else:
                return data.copy()
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取算子元数据"""
        return {
            "name": self.name,
            "type": "evolution",
            "evolution_type": self.evolution_type,
            "dimension": self.dimension,
            "description": "Evolution operator for applying various evolutionary processes to data"
        }
    
    def is_compatible_with(self, other_operator: 'OperatorInterface') -> bool:
        """检查与其他算子的兼容性"""
        # 简化实现，始终返回True
        return True
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """获取性能指标"""
        return {
            "time_complexity": 1.0,
            "space_complexity": 1.0,
            "numerical_stability": 0.9,
            "parallelizability": 0.8
        }
    
    def compose(self, other_operator: 'OperatorInterface') -> 'OperatorInterface':
        """与其他算子组合"""
        # 创建复合算子
        return CompositeOperator([self, other_operator])
    
    def get_parameters(self) -> Dict[str, Any]:
        """获取算子参数"""
        return {
            "evolution_type": self.evolution_type,
            "dimension": self.dimension,
            "parameters": self.parameters
        }
    
    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """设置算子参数"""
        if "evolution_type" in parameters:
            self.evolution_type = parameters["evolution_type"]
        if "dimension" in parameters:
            self.dimension = parameters["dimension"]
        if "parameters" in parameters:
            self.parameters = parameters["parameters"]
        
        # 初始化默认参数
        self._init_default_parameters()
    
    def to_rust(self) -> bool:
        """检查是否有Rust实现"""
        return False
    
    def get_complexity(self) -> Dict[str, Any]:
        """获取算子复杂度信息"""
        return {
            "time_complexity": "O(n*t)",
            "space_complexity": "O(n*t)",
            "computational_complexity": "Medium",
            "numerical_stability": "Medium",
            "parallelizable": True
        }


# 实现CompositeOperator
class CompositeOperator(OperatorInterface):
    """复合算子类"""
    
    def __init__(self, operators: List[OperatorInterface], name: Optional[str] = None, **kwargs):
        """初始化复合算子"""
        if not operators:
            raise ValueError("算子列表不能为空")
        
        self.operators = operators
        self.name = name or f"CompositeOperator({','.join(op.get_metadata()['name'] for op in operators)})"
    
    def apply(self, input_data: Any, **kwargs) -> Any:
        """应用复合算子到输入数据"""
        result = input_data
        
        for operator in self.operators:
            result = operator.apply(result, **kwargs)
        
        return result
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取算子元数据"""
        return {
            "name": self.name,
            "type": "composite",
            "operators": [op.get_metadata() for op in self.operators],
            "description": "Composite operator composed of multiple operators"
        }
    
    def is_compatible_with(self, other_operator: 'OperatorInterface') -> bool:
        """检查与其他算子的兼容性"""
        # 复合算子与其他算子的兼容性取决于最后一个算子
        return self.operators[-1].is_compatible_with(other_operator)
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """获取性能指标"""
        # 合并所有算子的性能指标
        metrics = {}
        
        for operator in self.operators:
            op_metrics = operator.get_performance_metrics()
            for key, value in op_metrics.items():
                if key in metrics:
                    # 对于数值指标，取平均值
                    if isinstance(value, (int, float)) and isinstance(metrics[key], (int, float)):
                        metrics[key] = (metrics[key] + value) / 2
                    # 对于字符串指标，保留最后一个
                    else:
                        metrics[key] = value
                else:
                    metrics[key] = value
        
        return metrics
    
    def compose(self, other_operator: 'OperatorInterface') -> 'OperatorInterface':
        """与其他算子组合"""
        # 创建新的复合算子
        new_operators = self.operators.copy()
        
        if isinstance(other_operator, CompositeOperator):
            # 如果是复合算子，添加其所有算子
            new_operators.extend(other_operator.operators)
        else:
            # 否则添加单个算子
            new_operators.append(other_operator)
        
        return CompositeOperator(new_operators)
    
    def get_parameters(self) -> Dict[str, Any]:
        """获取算子参数"""
        # 返回所有算子的参数
        return {
            f"operator_{i}": op.get_parameters() for i, op in enumerate(self.operators)
        }
    
    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """设置算子参数"""
        # 设置各个算子的参数
        for key, value in parameters.items():
            if key.startswith("operator_"):
                try:
                    index = int(key.split("_")[1])
                    if 0 <= index < len(self.operators):
                        self.operators[index].set_parameters(value)
                except (ValueError, IndexError):
                    pass
    
    def to_rust(self) -> bool:
        """检查是否有Rust实现"""
        # 如果所有算子都有Rust实现，则复合算子也有Rust实现
        return all(op.to_rust() for op in self.operators)
    
    def get_complexity(self) -> Dict[str, Any]:
        """获取算子复杂度信息"""
        # 合并所有算子的复杂度信息
        complexity = {}
        
        for operator in self.operators:
            op_complexity = operator.get_complexity()
            for key, value in op_complexity.items():
                if key in complexity:
                    # 对于数值指标，取最大值
                    if isinstance(value, (int, float)) and isinstance(complexity[key], (int, float)):
                        complexity[key] = max(complexity[key], value)
                    # 对于字符串指标，保留最后一个
                    else:
                        complexity[key] = value
                else:
                    complexity[key] = value
        
        return complexity


# 实现OperatorValidator
class OperatorValidator:
    """算子验证器类"""
    
    @staticmethod
    def validate(operator: Any) -> Tuple[bool, List[str]]:
        """验证算子是否符合接口规范"""
        errors = []
        
        # 检查是否继承自OperatorInterface
        if not isinstance(operator, OperatorInterface):
            errors.append(f"算子 {operator.__class__.__name__} 未继承自OperatorInterface")
            return False, errors
        
        # 检查必需的方法
        required_methods = [
            'apply',
            'get_metadata',
            'is_compatible_with',
            'get_performance_metrics',
            'compose',
            'get_parameters',
            'set_parameters',
            'to_rust',
            'get_complexity'
        ]
        
        for method_name in required_methods:
            if not hasattr(operator, method_name) or not callable(getattr(operator, method_name)):
                errors.append(f"算子缺少必需的方法: {method_name}")
        
        # 检查元数据
        try:
            metadata = operator.get_metadata()
            if not isinstance(metadata, dict):
                errors.append(f"get_metadata 方法应返回字典，但返回了 {type(metadata)}")
            else:
                # 检查必需的元数据字段
                required_fields = ['name', 'type', 'description']
                for field in required_fields:
                    if field not in metadata:
                        errors.append(f"元数据缺少必需的字段: {field}")
        except Exception as e:
            errors.append(f"调用 get_metadata 方法时出错: {str(e)}")
        
        return len(errors) == 0, errors


def test_transform_operator():
    """测试TransformOperator"""
    print("\n测试TransformOperator...")
    
    # 创建随机数据
    np.random.seed(42)
    data = np.random.randn(10, 2)
    
    # 创建线性变换算子
    transform_op = TransformOperator(
        transform_type='linear',
        dimension=2,
        parameters={
            'matrix': np.array([[np.cos(np.pi/4), -np.sin(np.pi/4)], 
                               [np.sin(np.pi/4), np.cos(np.pi/4)]]),
            'offset': np.array([0, 0])
        }
    )
    
    # 验证算子
    validator = OperatorValidator()
    valid, errors = validator.validate(transform_op)
    print(f"验证TransformOperator:")
    print(f"  有效: {valid}")
    if not valid:
        for error in errors:
            print(f"  - {error}")
    
    # 应用变换
    transformed_data = transform_op.apply(data)
    
    # 打印结果
    print(f"变换类型: {transform_op.transform_type}")
    print(f"变换参数: {transform_op.parameters}")
    print(f"原始数据形状: {data.shape}")
    print(f"变换后数据形状: {transformed_data.shape}")
    
    # 可视化原始数据和变换后的数据
    plt.figure(figsize=(10, 5))
    
    plt.subplot(1, 2, 1)
    plt.scatter(data[:, 0], data[:, 1], alpha=0.7)
    plt.title('Original Data')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.scatter(transformed_data[:, 0], transformed_data[:, 1], alpha=0.7)
    plt.title('Transformed Data')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('transform_operator_test.png')
    plt.close()
    
    print("TransformOperator测试完成")
    
    return transform_op


def test_evolution_operator():
    """测试EvolutionOperator"""
    print("\n测试EvolutionOperator...")
    
    # 创建随机数据
    np.random.seed(42)
    data = np.random.randn(10, 2)
    
    # 创建微分方程
    def harmonic_oscillator(t, x):
        # 简谐振荡器: d^2x/dt^2 = -x
        # 转换为一阶系统: dx/dt = v, dv/dt = -x
        if len(x.shape) == 1:
            # 单个点
            dx = np.zeros(2)
            dx[0] = x[1]
            dx[1] = -x[0]
            return dx
        else:
            # 多个点
            dx = np.zeros_like(x)
            dx[:, 0] = x[:, 1]
            dx[:, 1] = -x[:, 0]
            return dx
    
    # 创建演化算子
    evolution_op = EvolutionOperator(
        evolution_type='differential_equation',
        dimension=2,
        parameters={
            'equation': harmonic_oscillator,
            'method': 'rk4',
            'time_step': 0.1,
            'num_steps': 100
        }
    )
    
    # 验证算子
    validator = OperatorValidator()
    valid, errors = validator.validate(evolution_op)
    print(f"验证EvolutionOperator:")
    print(f"  有效: {valid}")
    if not valid:
        for error in errors:
            print(f"  - {error}")
    
    # 应用演化
    trajectory = evolution_op.apply(data, return_trajectory=True)
    
    # 打印结果
    print(f"演化类型: {evolution_op.evolution_type}")
    print(f"演化参数: {evolution_op.parameters}")
    print(f"原始数据形状: {data.shape}")
    print(f"轨迹长度: {len(trajectory)}")
    print(f"最终状态形状: {trajectory[-1].shape}")
    
    # 可视化轨迹
    plt.figure(figsize=(10, 8))
    
    # 绘制第一个点的轨迹
    trajectory_point = np.array([state[0] for state in trajectory])
    plt.plot(trajectory_point[:, 0], trajectory_point[:, 1], 'b-', alpha=0.7)
    plt.scatter(trajectory_point[0, 0], trajectory_point[0, 1], c='g', s=100, label='Start')
    plt.scatter(trajectory_point[-1, 0], trajectory_point[-1, 1], c='r', s=100, label='End')
    
    plt.title('Harmonic Oscillator Trajectory')
    plt.xlabel('Position')
    plt.ylabel('Velocity')
    plt.grid(True)
    plt.legend()
    
    plt.savefig('evolution_operator_test.png')
    plt.close()
    
    print("EvolutionOperator测试完成")
    
    return evolution_op


def test_composite_operator(transform_op, evolution_op):
    """测试复合算子"""
    print("\n测试复合算子...")
    
    # 创建复合算子
    composite_op = CompositeOperator([transform_op, evolution_op])
    
    # 验证复合算子
    validator = OperatorValidator()
    valid, errors = validator.validate(composite_op)
    print(f"验证CompositeOperator:")
    print(f"  有效: {valid}")
    if not valid:
        for error in errors:
            print(f"  - {error}")
    
    # 打印复合算子信息
    print(f"复合算子: {composite_op.name}")
    print(f"复合算子元数据: {composite_op.get_metadata()}")
    print(f"复合算子参数: {composite_op.get_parameters()}")
    
    # 创建随机数据
    np.random.seed(42)
    data = np.random.randn(10, 2)
    
    # 应用复合算子
    result = composite_op.apply(data)
    
    print(f"输入数据形状: {data.shape}")
    print(f"输出数据形状: {result.shape}")
    
    print("复合算子测试完成")
    
    return composite_op


def main():
    """主函数"""
    print("开始测试算子...")
    
    # 测试TransformOperator
    transform_op = test_transform_operator()
    
    # 测试EvolutionOperator
    evolution_op = test_evolution_operator()
    
    # 测试复合算子
    composite_op = test_composite_operator(transform_op, evolution_op)
    
    print("\n所有测试完成")


if __name__ == "__main__":
    main()
