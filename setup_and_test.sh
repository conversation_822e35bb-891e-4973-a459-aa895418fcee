#!/bin/bash
# 安装依赖
apt-get update && apt-get install -y python3-pip
pip install torch numpy

# 创建测试脚本
cat > gpu_benchmark.py << 'EOL'
import torch
import time
import json

# 基本信息
gpu_info = {
    'name': torch.cuda.get_device_name(),
    'compute_capability': torch.cuda.get_device_capability(),
    'cuda_version': torch.version.cuda
}

# 检测是否支持双精度
try:
    test_tensor = torch.ones(10, 10, dtype=torch.float64, device='cuda')
    test_result = test_tensor * test_tensor
    supports_fp64 = True
except Exception as e:
    supports_fp64 = False
    gpu_info['fp64_support_error'] = str(e)

gpu_info['supports_fp64'] = supports_fp64

# 测试结果
results = {'gpu_info': gpu_info}

# 如果支持双精度，测试双精度性能
if supports_fp64:
    # 双精度矩阵乘法测试
    for size in [1000, 3000, 5000]:
        try:
            a = torch.randn(size, size, dtype=torch.float64, device='cuda')
            b = torch.randn(size, size, dtype=torch.float64, device='cuda')
            
            # 预热
            for _ in range(3):
                c = torch.matmul(a, b)
                torch.cuda.synchronize()
            
            # 计时
            torch.cuda.synchronize()
            start = time.time()
            for _ in range(5):
                c = torch.matmul(a, b)
                torch.cuda.synchronize()
            end = time.time()
            
            results[f'fp64_matmul_{size}'] = (end - start) / 5
        except Exception as e:
            results[f'fp64_matmul_{size}_error'] = str(e)

# 单精度测试
for size in [1000, 3000, 5000]:
    try:
        a = torch.randn(size, size, dtype=torch.float32, device='cuda')
        b = torch.randn(size, size, dtype=torch.float32, device='cuda')
        
        # 预热
        for _ in range(3):
            c = torch.matmul(a, b)
            torch.cuda.synchronize()
        
        # 计时
        torch.cuda.synchronize()
        start = time.time()
        for _ in range(5):
            c = torch.matmul(a, b)
            torch.cuda.synchronize()
        end = time.time()
        
        results[f'fp32_matmul_{size}'] = (end - start) / 5
    except Exception as e:
        results[f'fp32_matmul_{size}_error'] = str(e)

# 计算FP64/FP32性能比率
if supports_fp64:
    fp64_fp32_ratios = {}
    
    for size in [1000, 3000, 5000]:
        fp64_key = f'fp64_matmul_{size}'
        fp32_key = f'fp32_matmul_{size}'
        
        if fp64_key in results and fp32_key in results:
            ratio = results[fp32_key] / results[fp64_key]
            fp64_fp32_ratios[f'matmul_{size}_ratio'] = ratio
    
    results['fp64_fp32_ratios'] = fp64_fp32_ratios
    
    if fp64_fp32_ratios:
        avg_ratio = sum(fp64_fp32_ratios.values()) / len(fp64_fp32_ratios)
        results['fp64_fp32_avg_ratio'] = avg_ratio

# 保存结果
with open('/root/benchmark_results.json', 'w') as f:
    json.dump(results, f, indent=2)

print(json.dumps(results, indent=2))
EOL

# 运行测试
python3 gpu_benchmark.py > /root/benchmark_results.txt
cat /root/benchmark_results.txt
