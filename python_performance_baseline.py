#!/usr/bin/env python3
"""
Python性能基线测试
为Rust对比建立准确的基准数据
"""

import time
import statistics
import sys
import json
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

class PythonPerformanceBaseline:
    """Python性能基线测试器"""
    
    def __init__(self):
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "python_version": sys.version,
            "test_results": {}
        }
        
    def test_memory_operations(self, test_size=50):
        """测试记忆操作性能"""
        print(f"📊 测试记忆操作性能 (规模: {test_size})")
        
        try:
            from aqfh_unified_consciousness_mcp import unified_system
            
            # 测试数据
            test_memories = [
                f"🧪 Python基线测试记忆 {i} - 用于建立准确的性能基准"
                for i in range(test_size)
            ]
            
            # 保存性能测试
            print("   💾 测试保存性能...")
            save_times = []
            successful_saves = 0
            
            start_batch = time.time()
            for i, memory in enumerate(test_memories):
                start_time = time.time()
                try:
                    result = unified_system.save_memory(
                        content=memory,
                        content_type="baseline_test",
                        importance=0.5,
                        tags=["python_baseline"],
                        context={"test_id": i}
                    )
                    end_time = time.time()
                    if result:
                        save_times.append(end_time - start_time)
                        successful_saves += 1
                except Exception as e:
                    print(f"      保存错误 {i}: {e}")
                    break
            end_batch = time.time()
            
            # 搜索性能测试
            print("   🔍 测试搜索性能...")
            search_queries = [
                "Python基线测试",
                "性能基准",
                "baseline_test",
                "测试记忆",
                "准确"
            ]
            search_times = []
            search_results_count = []
            
            for query in search_queries:
                start_time = time.time()
                try:
                    results = unified_system.search_memories(query, limit=10)
                    end_time = time.time()
                    search_times.append(end_time - start_time)
                    search_results_count.append(len(results))
                except Exception as e:
                    print(f"      搜索错误 '{query}': {e}")
            
            # 状态查询性能测试
            print("   📊 测试状态查询性能...")
            status_times = []
            for i in range(5):
                start_time = time.time()
                try:
                    status = unified_system.get_consciousness_status()
                    end_time = time.time()
                    status_times.append(end_time - start_time)
                except Exception as e:
                    print(f"      状态查询错误 {i}: {e}")
            
            # 计算统计数据
            results = {
                "save_performance": {
                    "total_attempts": len(test_memories),
                    "successful_saves": successful_saves,
                    "success_rate": successful_saves / len(test_memories) if test_memories else 0,
                    "avg_save_time": statistics.mean(save_times) if save_times else 0,
                    "median_save_time": statistics.median(save_times) if save_times else 0,
                    "min_save_time": min(save_times) if save_times else 0,
                    "max_save_time": max(save_times) if save_times else 0,
                    "total_save_time": sum(save_times) if save_times else 0,
                    "batch_time": end_batch - start_batch,
                    "save_ops_per_sec": successful_saves / (end_batch - start_batch) if (end_batch - start_batch) > 0 else 0
                },
                "search_performance": {
                    "total_queries": len(search_queries),
                    "successful_searches": len(search_times),
                    "avg_search_time": statistics.mean(search_times) if search_times else 0,
                    "median_search_time": statistics.median(search_times) if search_times else 0,
                    "min_search_time": min(search_times) if search_times else 0,
                    "max_search_time": max(search_times) if search_times else 0,
                    "total_search_time": sum(search_times) if search_times else 0,
                    "search_ops_per_sec": len(search_times) / sum(search_times) if search_times and sum(search_times) > 0 else 0,
                    "avg_results_per_query": statistics.mean(search_results_count) if search_results_count else 0
                },
                "status_performance": {
                    "total_queries": 5,
                    "successful_queries": len(status_times),
                    "avg_status_time": statistics.mean(status_times) if status_times else 0,
                    "median_status_time": statistics.median(status_times) if status_times else 0,
                    "status_ops_per_sec": len(status_times) / sum(status_times) if status_times and sum(status_times) > 0 else 0
                }
            }
            
            # 打印结果
            save_perf = results["save_performance"]
            search_perf = results["search_performance"]
            status_perf = results["status_performance"]
            
            print(f"\n   📊 保存性能结果:")
            print(f"      成功率: {save_perf['success_rate']:.2%}")
            print(f"      平均时间: {save_perf['avg_save_time']:.6f}s")
            print(f"      吞吐量: {save_perf['save_ops_per_sec']:.2f} ops/s")
            
            print(f"\n   🔍 搜索性能结果:")
            print(f"      平均时间: {search_perf['avg_search_time']:.6f}s")
            print(f"      吞吐量: {search_perf['search_ops_per_sec']:.2f} ops/s")
            print(f"      平均结果数: {search_perf['avg_results_per_query']:.1f}")
            
            print(f"\n   📊 状态查询结果:")
            print(f"      平均时间: {status_perf['avg_status_time']:.6f}s")
            print(f"      吞吐量: {status_perf['status_ops_per_sec']:.2f} ops/s")
            
            return results
            
        except ImportError as e:
            print(f"   ❌ 无法导入Python系统: {e}")
            return None
        except Exception as e:
            print(f"   ❌ 记忆操作测试失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def test_system_resources(self):
        """测试系统资源使用"""
        print("🖥️ 测试系统资源使用")
        
        try:
            import psutil
            import os
            
            # 获取当前进程信息
            process = psutil.Process(os.getpid())
            
            # 内存使用
            memory_info = process.memory_info()
            memory_percent = process.memory_percent()
            
            # CPU使用
            cpu_percent = process.cpu_percent(interval=1)
            
            # 系统信息
            system_memory = psutil.virtual_memory()
            system_cpu = psutil.cpu_percent(interval=1)
            
            results = {
                "process_memory_mb": memory_info.rss / 1024 / 1024,
                "process_memory_percent": memory_percent,
                "process_cpu_percent": cpu_percent,
                "system_memory_total_gb": system_memory.total / 1024 / 1024 / 1024,
                "system_memory_available_gb": system_memory.available / 1024 / 1024 / 1024,
                "system_memory_percent": system_memory.percent,
                "system_cpu_percent": system_cpu
            }
            
            print(f"   进程内存: {results['process_memory_mb']:.1f} MB ({results['process_memory_percent']:.1f}%)")
            print(f"   进程CPU: {results['process_cpu_percent']:.1f}%")
            print(f"   系统内存: {results['system_memory_percent']:.1f}% 使用")
            print(f"   系统CPU: {results['system_cpu_percent']:.1f}% 使用")
            
            return results
            
        except ImportError:
            print("   ⚠️ psutil未安装，跳过资源监控")
            return None
        except Exception as e:
            print(f"   ❌ 资源测试失败: {e}")
            return None
    
    def run_full_baseline(self, test_size=30):
        """运行完整的基线测试"""
        print("🧪 Python性能基线测试")
        print("建立准确的基准数据，为Rust对比做准备")
        print("=" * 60)
        
        # 记忆操作测试
        memory_results = self.test_memory_operations(test_size)
        if memory_results:
            self.results["test_results"]["memory_operations"] = memory_results
        
        # 系统资源测试
        resource_results = self.test_system_resources()
        if resource_results:
            self.results["test_results"]["system_resources"] = resource_results
        
        # 保存结果
        self.save_results()
        
        return self.results
    
    def save_results(self):
        """保存测试结果"""
        try:
            filename = f"python_baseline_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, ensure_ascii=False)
            print(f"\n💾 基线测试结果已保存: {filename}")
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

def main():
    """主函数"""
    baseline = PythonPerformanceBaseline()
    results = baseline.run_full_baseline(test_size=25)
    
    print("\n" + "=" * 60)
    print("📋 Python基线测试完成")
    
    if "memory_operations" in results["test_results"]:
        memory_ops = results["test_results"]["memory_operations"]
        print(f"\n🎯 关键性能指标:")
        print(f"   保存吞吐量: {memory_ops['save_performance']['save_ops_per_sec']:.2f} ops/s")
        print(f"   搜索吞吐量: {memory_ops['search_performance']['search_ops_per_sec']:.2f} ops/s")
        print(f"   平均保存延迟: {memory_ops['save_performance']['avg_save_time']:.6f}s")
        print(f"   平均搜索延迟: {memory_ops['search_performance']['avg_search_time']:.6f}s")
    
    print(f"\n💡 这些数据将作为Rust性能对比的基准")
    print(f"   Rust编译完成后，我们将看到具体的性能提升倍数！")

if __name__ == "__main__":
    main()
