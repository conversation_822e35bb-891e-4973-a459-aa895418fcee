Metadata-Version: 2.4
Name: tte
Version: 0.1.0
Summary: Transcendental Thinking Engine
Author: TTE Team
License: MIT
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Rust
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Development Status :: 3 - Alpha
Requires-Python: >=3.7
Description-Content-Type: text/markdown
Requires-Dist: numpy>=1.24.0
Requires-Dist: scipy>=1.10.0
Requires-Dist: networkx>=3.0
Requires-Dist: pydantic>=2.0.0
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-cov>=4.0.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: isort>=5.12.0; extra == "dev"
Requires-Dist: mypy>=1.0.0; extra == "dev"
Provides-Extra: distributed
Requires-Dist: ray>=2.6.0; extra == "distributed"
Provides-Extra: visualization
Requires-Dist: matplotlib>=3.7.0; extra == "visualization"
Requires-Dist: plotly>=5.14.0; extra == "visualization"

# 超越态思维引擎 (Transcendental Thinking Engine)

超越态思维引擎是一个基于超越态计算理论的分布式框架，旨在实现高并发、高容错的超越态计算。该引擎融合了量子计算的高效并行性和全息计算的高容错性，形成了独特的超越态计算范式。

## 核心特性

- **超越态编码与解码系统**：实现经典信息与超越态表示之间的高效转换
- **分形神经网络系统**：基于分形理论构建的自适应神经网络架构
- **记忆系统**：高效的超越态记忆存储与检索机制
- **语义理解系统**：深度语义分析与理解能力
- **推理与思维系统**：基于超越态的高级推理能力
- **学习与适应系统**：自适应学习与优化机制
- **分布式网络系统**：支持大规模分布式部署的网络架构
- **任务批处理系统**：高效的任务批处理机制，提高系统吞吐量

## 技术栈

- **Python 3.13+**：高级接口和算法实现，支持无GIL模式
- **Rust**：性能关键部分和底层算法实现
- **PyO3 0.24+**：Python与Rust的无缝集成
- **NumPy 1.24+**：科学计算支持，支持无GIL模式
- **Ray**：分布式计算框架
- **统一注册表**：算法和算子的统一管理

## 安装

```bash
pip install tte
```

## 快速开始

### 基本使用

```python
from tte import TranscendentalEngine

# 初始化引擎
engine = TranscendentalEngine()

# 加载数据
engine.load_data("path/to/data")

# 执行计算
result = engine.compute()

# 输出结果
print(result)
```

### 任务批处理

```python
from tte.core.distributed.task.task import Task
from tte.core.distributed.task.task_types import TaskType, TaskPriority
from tte.core.distributed.scheduler.batch_scheduler import BatchScheduler, BatchingPolicy
from tte.core.distributed.executor.batch_executor import BatchExecutor, ExecutionMode

# 创建调度器
scheduler = BatchScheduler(
    batching_policy=BatchingPolicy.HYBRID,
    default_batch_size=5
)

# 创建执行器
executor = BatchExecutor(
    batch_scheduler=scheduler,
    execution_mode=ExecutionMode.ADAPTIVE,
    max_workers=4
)

# 设置任务执行函数
def task_executor(task):
    # 执行任务逻辑
    return {"task_id": task.id, "result": "success"}

executor.set_task_executor(task_executor)

# 启动调度器和执行器
scheduler.start()
executor.start()

# 创建任务
task = Task(
    task_id="task1",
    task_type=TaskType.COMPUTE,
    content={"operation": "add", "a": 1, "b": 2}
)

# 提交任务
scheduler.submit_task(task)

# 等待任务完成
import time
time.sleep(1.0)

# 获取结果
batch = scheduler.get_task_batch(task.id)
result = executor.get_batch_result(batch.id)
print(result)

# 停止调度器和执行器
scheduler.stop()
executor.stop()
```

## 最近开发的工具

我们最近开发了以下高级工具，进一步增强了超越态思维引擎的功能：

1. **系统监控工具** (`v2/tools/monitor_system.py`)
   - 实时监控系统运行状态
   - 提供GUI界面和命令行界面
   - 支持监控CPU、内存、磁盘、网络等资源使用情况
   - 支持监控任务执行情况和节点状态

2. **性能分析工具** (`v2/tools/profile_system.py`)
   - 分析系统性能瓶颈
   - 支持CPU分析、内存分析和IO分析
   - 生成详细的性能报告

3. **自动化测试工具** (`v2/tools/automation/`)
   - 自动化生成测试用例
   - 支持从模板生成测试用例
   - 支持从API生成测试用例
   - 支持并行执行测试
   - 支持定时执行测试
   - 支持持续执行测试
   - 生成详细的测试报告

### 使用说明

#### 监控系统

```bash
cd /path/to/project
python v2/tools/monitor_system.py --gui
```

可选参数:
- `--gui`: 启动GUI界面
- `--interval`: 更新间隔（秒）
- `--output`: 输出文件
- `--save-interval`: 保存间隔（秒）

#### 分析系统性能

```bash
cd /path/to/project
python v2/tools/profile_system.py --module src.core.morphism --function apply_morphism --output-dir profile_report
```

可选参数:
- `--module`: 要分析的模块
- `--function`: 要分析的函数
- `--args`: 函数参数 (JSON格式)
- `--kwargs`: 函数关键字参数 (JSON格式)
- `--output-dir`: 输出目录
- `--top-n`: 显示的函数数量

#### 自动化测试

```bash
cd /path/to/project
python v2/tools/automation/automate_tests.py generate --template basic_test --suite example_tests --config example_config.json
```

可用命令:
- `generate`: 生成测试套件
- `api`: 生成API测试套件
- `run`: 运行测试
- `continuous`: 启动持续测试

## 文档

详细文档请参阅 [docs/](docs/) 目录。

## 运行测试和示例

### 运行测试

```bash
# 运行所有测试
python -m unittest discover tests

# 运行特定测试
python -m unittest tests/test_batch_processing.py
```

### 运行示例

```bash
# 运行批处理示例
python examples/batch_processing_example.py
```

## 开发注意事项

- 所有开发工作应该在TTE目录中进行，而不是TCT目录
- 使用相对导入路径，而不是绝对导入路径，以确保代码的可移植性
- 遵循项目的代码风格和命名约定
- 编写详细的文档和注释
- 编写完整的测试用例
- Python版本要求3.13+，支持无GIL模式
- 使用最新版本的组件，如NumPy 1.24+和PyO3 0.24+
- 算法和算子采用统一注册表管理，支持按需组合

## 许可证

MIT License
