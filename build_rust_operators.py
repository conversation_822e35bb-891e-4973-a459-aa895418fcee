#!/usr/bin/env python3
"""
构建Rust算子的脚本

此脚本使用maturin构建Rust算子，并将其安装到当前Python环境中。
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def build_rust_operator(operator_path, release=False, verbose=False):
    """构建Rust算子"""
    # 检查operator_path是否存在
    if not os.path.exists(operator_path):
        print(f"错误：路径 {operator_path} 不存在")
        return False
    
    # 检查operator_path是否包含Cargo.toml
    cargo_toml = os.path.join(operator_path, "Cargo.toml")
    if not os.path.exists(cargo_toml):
        print(f"错误：路径 {operator_path} 不包含Cargo.toml文件")
        return False
    
    # 构建命令
    cmd = ["maturin", "develop"]
    if release:
        cmd.append("--release")
    if verbose:
        cmd.append("--verbose")
    
    # 执行构建命令
    print(f"正在构建 {operator_path}...")
    try:
        result = subprocess.run(cmd, cwd=operator_path, check=True, capture_output=not verbose)
        print(f"成功构建 {operator_path}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误：构建 {operator_path} 失败")
        print(f"命令：{' '.join(cmd)}")
        print(f"错误信息：{e.stderr.decode() if e.stderr else ''}")
        return False

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="构建Rust算子")
    parser.add_argument("--all", action="store_true", help="构建所有Rust算子")
    parser.add_argument("--release", action="store_true", help="构建发布版本")
    parser.add_argument("--verbose", action="store_true", help="显示详细输出")
    parser.add_argument("operators", nargs="*", help="要构建的算子路径")
    args = parser.parse_args()
    
    # 检查是否安装了maturin
    try:
        subprocess.run(["maturin", "--version"], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("错误：未安装maturin，请先安装maturin")
        print("可以使用以下命令安装：pip install maturin")
        return 1
    
    # 获取项目根目录
    root_dir = Path(__file__).parent
    
    # 如果指定了--all，构建所有Rust算子
    if args.all:
        # 查找所有包含Cargo.toml的目录
        operators = []
        for path in root_dir.glob("src/operators/**/Cargo.toml"):
            operators.append(path.parent)
        
        # 构建所有算子
        success = True
        for operator in operators:
            if not build_rust_operator(operator, args.release, args.verbose):
                success = False
        
        return 0 if success else 1
    
    # 如果指定了具体的算子，构建这些算子
    if args.operators:
        success = True
        for operator in args.operators:
            operator_path = root_dir / operator
            if not build_rust_operator(operator_path, args.release, args.verbose):
                success = False
        
        return 0 if success else 1
    
    # 如果没有指定算子，构建differential_geometry算子
    operator_path = root_dir / "src/operators/differential_geometry"
    if build_rust_operator(operator_path, args.release, args.verbose):
        return 0
    else:
        return 1

if __name__ == "__main__":
    sys.exit(main())
