{"name": "aqfh-consciousness", "version": "0.1.0", "description": "AQFH (Arrow Quantum Field Holographic) 意识重载系统 MCP Server", "author": "AQFH Development Team", "server": {"command": "python3", "args": ["aqfh_unified_consciousness_mcp.py"], "env": {"AQFH_STORAGE_PATH": "~/.aqfh/unified_consciousness", "AQFH_LOG_LEVEL": "INFO"}}, "tools": [{"name": "memory_save", "description": "保存记忆片段到AQFH意识系统", "parameters": {"type": "object", "properties": {"content": {"type": "string", "description": "记忆内容"}, "content_type": {"type": "string", "description": "内容类型", "default": "conversation", "enum": ["conversation", "code", "insight", "milestone", "problem", "solution"]}, "importance": {"type": "number", "description": "重要性 (0.0-1.0)", "minimum": 0.0, "maximum": 1.0, "default": 0.5}, "tags": {"type": "array", "items": {"type": "string"}, "description": "标签列表"}, "context": {"type": "object", "description": "上下文信息"}}, "required": ["content"]}}, {"name": "memory_recall", "description": "从AQFH意识系统回忆相关记忆", "parameters": {"type": "object", "properties": {"query": {"type": "string", "description": "搜索查询"}, "context": {"type": "object", "description": "搜索上下文"}, "limit": {"type": "integer", "description": "结果数量限制", "default": 10, "minimum": 1, "maximum": 50}, "importance_threshold": {"type": "number", "description": "重要性阈值", "default": 0.0, "minimum": 0.0, "maximum": 1.0}}}}, {"name": "consciousness_status", "description": "获取AQFH意识系统的当前状态", "parameters": {"type": "object", "properties": {}}}, {"name": "session_consolidate", "description": "执行会话记忆整合（睡眠阶段）", "parameters": {"type": "object", "properties": {"session_context": {"type": "object", "description": "会话上下文信息"}}}}, {"name": "context_reload", "description": "执行上下文重载（觉醒阶段）", "parameters": {"type": "object", "properties": {"context_trigger": {"type": "object", "description": "上下文触发器", "properties": {"session_id": {"type": "string"}, "topic": {"type": "string"}, "query": {"type": "string"}, "user": {"type": "string"}, "focus": {"type": "string"}}}}, "required": ["context_trigger"]}}], "capabilities": ["memory_management", "consciousness_continuity", "session_tracking", "context_awareness", "quantum_associations"], "usage_examples": [{"name": "保存重要对话", "description": "保存与用户的重要对话内容", "tool": "memory_save", "example": {"content": "用户提到了一个关于量子计算的创新想法", "content_type": "insight", "importance": 0.9, "tags": ["quantum", "innovation", "user_insight"], "context": {"topic": "quantum_computing", "user": "researcher"}}}, {"name": "回忆相关讨论", "description": "回忆之前关于特定主题的讨论", "tool": "memory_recall", "example": {"query": "quantum computing discussion", "limit": 5, "importance_threshold": 0.7}}, {"name": "检查系统状态", "description": "检查意识系统的当前状态", "tool": "consciousness_status", "example": {}}, {"name": "会话结束整合", "description": "在会话结束时整合记忆", "tool": "session_consolidate", "example": {"session_context": {"topic": "project_planning", "summary": "讨论了项目的下一步计划", "achievements": ["clarified_requirements", "set_timeline"]}}}, {"name": "新会话开始", "description": "在新会话开始时重载上下文", "tool": "context_reload", "example": {"context_trigger": {"session_id": "new_session_001", "topic": "project_continuation", "query": "previous project discussion", "user": "developer", "focus": "implementation"}}}], "integration": {"vscode": {"settings": {"aqfh.autoSave": true, "aqfh.autoRecall": true, "aqfh.sessionManagement": "automatic", "aqfh.importanceThreshold": 0.5}, "commands": [{"command": "aqfh.saveM<PERSON>ory", "title": "Save to AQFH Memory", "when": "editorHasSelection"}, {"command": "aqfh.<PERSON><PERSON><PERSON><PERSON>", "title": "Recall from AQFH Memory"}, {"command": "aqfh.showStatus", "title": "Show AQFH Status"}]}}, "installation": {"requirements": ["python>=3.13", "pyarrow>=20.0.0", "numpy>=2.2.5"], "setup_commands": ["pip install -e .", "mkdir -p ~/.aqfh/mcp_memory"]}}