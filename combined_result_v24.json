{"input_data": {"features": {"text_length": 500, "sentiment_score": 0.75, "topic_relevance": 0.9, "user_engagement": 0.8, "readability": 0.65}, "prediction": "positive", "confidence": 0.85, "model_type": "classification", "domain": "sentiment_analysis", "context": {"user_id": "user_123", "session_id": "session_456", "timestamp": "2023-06-15T10:30:00Z", "device": "mobile", "language": "zh-CN"}}, "explanation": {"multi_level": {"technical": {"trace": [{"feature_text_length": "text_length: 500", "feature_user_engagement": "user_engagement: 0.8", "feature_sentiment_score": "sentiment_score: 0.75", "feature_readability": "readability: 0.65", "feature_topic_relevance": "topic_relevance: 0.9", "step": "输入特征分析"}, {"prediction": "预测: \"positive\"", "confidence": "置信度: 0.85", "step": "预测结果"}], "justification": "从技术角度来看，模型基于输入特征进行了预测。预测结果为 positive，这是基于特征的加权计算得出的。", "counterfactual": [{"counterfactual": "如果readability增加10%，预测结果可能会改变", "feature": "readability", "original": "0.65"}, {"counterfactual": "如果sentiment_score增加10%，预测结果可能会改变", "feature": "sentiment_score", "original": "0.75"}, {"counterfactual": "如果text_length增加10%，预测结果可能会改变", "feature": "text_length", "original": "500"}, {"original": "0.9", "counterfactual": "如果topic_relevance增加10%，预测结果可能会改变", "feature": "topic_relevance"}, {"feature": "user_engagement", "counterfactual": "如果user_engagement增加10%，预测结果可能会改变", "original": "0.8"}]}, "conceptual": {"trace": [{"step": "概念理解", "concept": "模型通过识别输入数据中的模式来做出决策"}, {"explanation": "预测结果 \"positive\" 表示模型认为输入数据符合特定的模式", "step": "预测结果解释"}], "justification": "从概念上讲，模型识别了输入数据中的模式，并将其与已知的模式进行比较。预测结果 positive 表示模型认为输入数据最符合该类别的模式。", "counterfactual": [{"explanation": "如果输入数据的模式发生变化，模型会识别不同的模式并给出不同的预测结果", "concept": "模式变化"}]}, "analogy": {"trace": [{"step": "类比理解", "analogy": "模型就像一个图书管理员，通过查看书的特征（如封面、标题、作者）来判断这本书属于哪个类别"}, {"step": "预测结果类比", "explanation": "预测结果 \"positive\" 就像图书管理员将一本书归类到特定的书架上"}], "justification": "想象模型是一个图书管理员，它通过查看书的特征来决定将书放在哪个书架上。在这个例子中，模型看到了输入数据的特征，并决定将其归类为 positive，就像将一本书放在特定的书架上。", "counterfactual": [{"analogy": "书的特征变化", "explanation": "如果这本书的封面或内容发生变化，图书管理员可能会将其归类到不同的书架上"}]}, "fused": {"trace": [{"feature_text_length": "text_length: 500", "feature_user_engagement": "user_engagement: 0.8", "feature_sentiment_score": "sentiment_score: 0.75", "feature_readability": "readability: 0.65", "feature_topic_relevance": "topic_relevance: 0.9", "step": "输入特征分析"}, {"prediction": "预测: \"positive\"", "confidence": "置信度: 0.85", "step": "预测结果"}, {"step": "概念理解", "concept": "模型通过识别输入数据中的模式来做出决策"}, {"explanation": "预测结果 \"positive\" 表示模型认为输入数据符合特定的模式", "step": "预测结果解释"}], "justification": "【技术解释】从技术角度来看，模型基于输入特征进行了预测。预测结果为 positive，这是基于特征的加权计算得出的。 【概念解释】从概念上讲，模型识别了输入数据中的模式，并将其与已知的模式进行比较。预测结果 positive 表示模型认为输入数据最符合该类别的模式。 ", "counterfactual": [{"counterfactual": "如果readability增加10%，预测结果可能会改变", "feature": "readability", "original": "0.65"}, {"counterfactual": "如果sentiment_score增加10%，预测结果可能会改变", "feature": "sentiment_score", "original": "0.75"}, {"counterfactual": "如果text_length增加10%，预测结果可能会改变", "feature": "text_length", "original": "500"}, {"original": "0.9", "counterfactual": "如果topic_relevance增加10%，预测结果可能会改变", "feature": "topic_relevance"}, {"feature": "user_engagement", "counterfactual": "如果user_engagement增加10%，预测结果可能会改变", "original": "0.8"}, {"explanation": "如果输入数据的模式发生变化，模型会识别不同的模式并给出不同的预测结果", "concept": "模式变化"}]}, "weights": {"technical": 0.3731343283582089, "conceptual": 0.3731343283582089, "analogy": 0.2537313432835821}}, "summary": "最重要的特征是user_engagement（重要性：0.80）。如果改变，预测结果将变为String(\"negative\")。找到了一个相似度为0.93的示例。最可靠的规则是：如果user_engagement > 0.77 且 readability > 0.69，则prediction = 'positive'", "feature_importances": [{"name": "user_engagement", "importance": 0.7955269922761878, "value": 0.8, "feature_type": "number", "description": "特征 user_engagement 的重要性", "unit": null, "range": null}, {"name": "topic_relevance", "importance": 0.6233171805356981, "value": 0.9, "feature_type": "number", "description": "特征 topic_relevance 的重要性", "unit": null, "range": null}, {"name": "sentiment_score", "importance": 0.48352842141763086, "value": 0.75, "feature_type": "number", "description": "特征 sentiment_score 的重要性", "unit": null, "range": null}, {"name": "readability", "importance": 0.14608703528351008, "value": 0.65, "feature_type": "number", "description": "特征 readability 的重要性", "unit": null, "range": null}, {"name": "text_length", "importance": 0.13294808786072598, "value": 500, "feature_type": "number", "description": "特征 text_length 的重要性", "unit": null, "range": null}], "counterfactuals": [{"original_features": {"sentiment_score": 0.75, "user_engagement": 0.8, "text_length": 500, "topic_relevance": 0.9, "readability": 0.65}, "counterfactual_features": {"sentiment_score": 0.75, "readability": 0.65, "topic_relevance": 0.9, "user_engagement": 0.8, "text_length": 500}, "original_prediction": "positive", "counterfactual_prediction": "negative", "changed_features": [], "changed_feature_importance": {}, "distance": 0.6843818702312385, "feasibility": 0.5493052439439158}, {"original_features": {"sentiment_score": 0.75, "text_length": 500, "topic_relevance": 0.9, "user_engagement": 0.8, "readability": 0.65}, "counterfactual_features": {"readability": 0.65, "sentiment_score": 0.75, "text_length": 500, "topic_relevance": 0.7089494008017363, "user_engagement": 0.7035029199584386}, "original_prediction": "positive", "counterfactual_prediction": "negative", "changed_features": ["topic_relevance", "user_engagement"], "changed_feature_importance": {"topic_relevance": 0.8369393080141303, "user_engagement": 0.9466743792581164}, "distance": 0.24595970966108494, "feasibility": 0.0546362155425244}, {"original_features": {"text_length": 500, "sentiment_score": 0.75, "readability": 0.65, "topic_relevance": 0.9, "user_engagement": 0.8}, "counterfactual_features": {"readability": 0.65, "topic_relevance": 0.6120314670920173, "text_length": 320.9809218232763, "user_engagement": 0.45842099893013144, "sentiment_score": 0.75}, "original_prediction": "positive", "counterfactual_prediction": "negative", "changed_features": ["text_length", "topic_relevance", "user_engagement"], "changed_feature_importance": {"topic_relevance": 0.5455292628850269, "user_engagement": 0.8814343090074948, "text_length": 0.7498795557714114}, "distance": 0.6608180490253399, "feasibility": 0.9553428614065693}], "rules": [{"id": "rule_0", "conditions": ["user_engagement > 0.77", "readability > 0.69"], "outcome": "prediction = 'positive'", "support": 0.7404584943889617, "confidence": 0.9543199284582297, "complexity": 2, "description": "规则 0"}, {"id": "rule_1", "conditions": ["sentiment_score < 0.69"], "outcome": "prediction = 'positive'", "support": 0.9011372618208978, "confidence": 0.8826305739750914, "complexity": 1, "description": "规则 1"}, {"id": "rule_4", "conditions": ["readability > 0.62", "text_length > 477.17", "text_length > 506.35"], "outcome": "prediction = 'positive'", "support": 0.7946850992308157, "confidence": 0.8571796716537275, "complexity": 3, "description": "规则 4"}, {"id": "rule_2", "conditions": ["readability < 0.62", "topic_relevance < 0.84", "text_length < 487.53"], "outcome": "prediction = 'positive'", "support": 0.6800018914663813, "confidence": 0.8509885597344211, "complexity": 3, "description": "规则 2"}, {"id": "rule_3", "conditions": ["user_engagement > 0.81"], "outcome": "prediction = 'positive'", "support": 0.6418462729963195, "confidence": 0.7633192422179296, "complexity": 1, "description": "规则 3"}]}, "verifiability": {"overall_score": 0.5652748614796199, "properties": [{"name": "一致性", "description": "模型在相似输入上产生相似输出", "property_type": "robustness", "expression": "forall x, y. |x - y| < epsilon -> |f(x) - f(y)| < delta", "importance": 0.8, "result": {"status": "failed", "score": 0.4766884235213006, "method": "hybrid", "details": {"formal_score": "0.3914612464833803", "adversarial_score": "0.6535247483386774", "score": "0.4766884235213006", "threshold": "0.7", "expression": "forall x, y. |x - y| < epsilon -> |f(x) - f(y)| < delta", "statistical_score": "0.46355199642432954", "verification_method": "hybrid", "property_name": "一致性", "status": "Failed", "empirical_score": "0.4473975049062606", "property_type": "robustness"}, "timestamp": "2025-05-01T14:23:57.480986473+00:00", "duration_ms": 7.421055, "counterexamples": [{"model_type": "classification", "features": {"readability": 0.65, "sentiment_score": 0.75, "text_length": 500, "topic_relevance": 0.9, "user_engagement": 0.8}, "confidence": 0.85, "prediction": "positive", "domain": "sentiment_analysis", "context": {"device": "mobile", "language": "zh-CN", "session_id": "session_456", "timestamp": "2023-06-15T10:30:00Z", "user_id": "user_123"}}, {"prediction": "positive", "features": {"readability": 0.65, "sentiment_score": 0.75, "text_length": 500, "topic_relevance": 0.9, "user_engagement": 0.8}, "context": {"device": "mobile", "language": "zh-CN", "session_id": "session_456", "timestamp": "2023-06-15T10:30:00Z", "user_id": "user_123"}, "confidence": 0.85, "domain": "sentiment_analysis", "model_type": "classification"}, {"features": {"readability": 0.65, "sentiment_score": 0.75, "text_length": 500, "topic_relevance": 0.9, "user_engagement": 0.8}, "model_type": "classification", "confidence": 0.85, "prediction": "positive", "context": {"device": "mobile", "language": "zh-CN", "session_id": "session_456", "timestamp": "2023-06-15T10:30:00Z", "user_id": "user_123"}, "domain": "sentiment_analysis"}]}}, {"name": "单调性", "description": "情感分数增加时，积极预测概率不减少", "property_type": "monotonicity", "expression": "forall x, y. x.sentiment_score <= y.sentiment_score -> f(x) <= f(y)", "importance": 0.6, "result": {"status": "uncertain", "score": 0.6833901120907124, "method": "hybrid", "details": {"expression": "forall x, y. x.sentiment_score <= y.sentiment_score -> f(x) <= f(y)", "formal_score": "0.9169169402275986", "adversarial_score": "0.5524994355319979", "threshold": "0.7", "property_name": "单调性", "score": "0.6833901120907124", "statistical_score": "0.5907110505395496", "property_type": "monotonicity", "verification_method": "hybrid", "empirical_score": "0.6030091387708421", "status": "Uncertain"}, "timestamp": "2025-05-01T14:23:57.481247109+00:00", "duration_ms": 0.256826, "counterexamples": []}}]}, "metadata": {"explanation_time": 0.00013303756713867188, "verifiability_time": 0.008655071258544922, "self_explainability_time": 0.00048160552978515625, "total_time": 0.00926971435546875}}