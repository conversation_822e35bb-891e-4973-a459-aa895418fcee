#!/usr/bin/env python3
"""
示例：组合使用PyO3 0.24+兼容版本的算子
"""

import time
from pprint import pprint

# 导入算子
import explanation_v24
import verifiability_v24
import self_explainability_v24

def main():
    """主函数"""
    print("超越态思维引擎算子示例 (PyO3 0.24+ 兼容版本)\n")

    # 创建输入数据
    input_data = {
        "features": {
            "text_length": 500,
            "sentiment_score": 0.75,
            "topic_relevance": 0.9,
            "user_engagement": 0.8,
            "readability": 0.65
        },
        "prediction": "positive",
        "confidence": 0.85,
        "model_type": "classification",
        "domain": "sentiment_analysis",
        "context": {
            "user_id": "user_123",
            "session_id": "session_456",
            "timestamp": "2023-06-15T10:30:00Z",
            "device": "mobile",
            "language": "zh-CN"
        }
    }

    # 创建多层次解释生成算子
    explanation_operator = explanation_v24.OptimizedMultilevelExplanationOperator(
        levels=["technical", "conceptual", "analogical"],
        max_tokens_per_level=200,
        include_examples=True,
        language="zh-CN"
    )

    # 创建可验证性算子
    verifiability_operator = verifiability_v24.VerifiabilityOperator(
        method="hybrid",
        threshold=0.7,
        timeout_ms=30000
    )

    # 创建自解释性算子
    self_explainability_operator = self_explainability_v24.SelfExplainabilityOperator(
        explanation_type="hybrid",
        explanation_format="mixed",
        explanation_complexity="adaptive"
    )

    # 应用多层次解释生成算子
    print("应用多层次解释生成算子...")
    start_time = time.time()
    explanation_input = {
        "model_output": {
            "decision": "模型预测情感为积极，置信度为0.85",
            "features": input_data["features"],
            "prediction": input_data["prediction"],
            "confidence": input_data["confidence"]
        },
        "context": {
            "reasoning": "文本长度适中，情感分数较高，主题相关性高，用户参与度好，可读性中等",
            "user_context": input_data["context"]
        }
    }
    explanation_result = explanation_operator.apply(explanation_input)
    explanation_time = time.time() - start_time

    # 打印多层次解释结果
    print(f"\n多层次解释生成完成 (耗时: {explanation_time:.4f}秒)")
    print("\n多层次解释结果:")
    print(explanation_result)

    # 检查结果结构
    if "explanations" in explanation_result:
        for level, explanation in explanation_result["explanations"].items():
            print(f"\n{level}层面解释:")
            print(explanation)

    # 应用可验证性算子
    print("\n应用可验证性算子...")

    # 创建验证属性
    properties = [
        {
            "name": "一致性",
            "description": "模型在相似输入上产生相似输出",
            "property_type": "robustness",
            "expression": "forall x, y. |x - y| < epsilon -> |f(x) - f(y)| < delta",
            "importance": 0.8
        },
        {
            "name": "单调性",
            "description": "情感分数增加时，积极预测概率不减少",
            "property_type": "monotonicity",
            "expression": "forall x, y. x.sentiment_score <= y.sentiment_score -> f(x) <= f(y)",
            "importance": 0.6
        }
    ]

    start_time = time.time()
    verifiability_result = verifiability_operator.apply(input_data, properties=properties)
    verifiability_time = time.time() - start_time

    # 打印可验证性结果
    print(f"\n可验证性评估完成 (耗时: {verifiability_time:.4f}秒)")
    print(f"总体验证分数: {verifiability_result['overall_score']:.4f}")
    print(f"验证方法: {verifiability_result['method']}")

    print("\n验证属性:")
    for prop in verifiability_result["properties"]:
        print(f"  - {prop['name']} ({prop['property_type']})")
        print(f"    验证状态: {prop['result']['status']}")
        print(f"    验证分数: {prop['result']['score']:.4f}")

    # 应用自解释性算子
    print("\n应用自解释性算子...")
    start_time = time.time()
    self_explainability_result = self_explainability_operator.apply(input_data)
    self_explainability_time = time.time() - start_time

    # 打印自解释性结果
    print(f"\n自解释性生成完成 (耗时: {self_explainability_time:.4f}秒)")
    print(f"解释摘要: {self_explainability_result['summary']}")
    print(f"解释置信度: {self_explainability_result['confidence']:.4f}")

    print("\n特征重要性 (前3个):")
    for feature in self_explainability_result["feature_importances"][:3]:
        print(f"  - {feature['name']}: {feature['importance']:.4f}")

    print("\n反事实解释 (第1个):")
    if self_explainability_result["counterfactuals"]:
        counterfactual = self_explainability_result["counterfactuals"][0]
        print(f"  原始预测: {counterfactual['original_prediction']}")
        print(f"  反事实预测: {counterfactual['counterfactual_prediction']}")
        print(f"  改变的特征: {', '.join(counterfactual['changed_features'])}")

    # 组合结果
    print("\n组合算子结果...")
    combined_result = {
        "input_data": input_data,
        "explanation": {
            "multi_level": explanation_result,
            "summary": self_explainability_result["summary"],
            "feature_importances": self_explainability_result["feature_importances"],
            "counterfactuals": self_explainability_result["counterfactuals"],
            "rules": self_explainability_result["rules"]
        },
        "verifiability": {
            "overall_score": verifiability_result["overall_score"],
            "properties": verifiability_result["properties"]
        },
        "metadata": {
            "explanation_time": explanation_time,
            "verifiability_time": verifiability_time,
            "self_explainability_time": self_explainability_time,
            "total_time": explanation_time + verifiability_time + self_explainability_time
        }
    }

    print(f"\n组合结果生成完成 (总耗时: {combined_result['metadata']['total_time']:.4f}秒)")
    print("组合结果包含多层次解释、可验证性评估和自解释性分析")

    # 保存组合结果
    print("\n保存组合结果...")
    import json
    with open("combined_result_v24.json", "w", encoding="utf-8") as f:
        json.dump(combined_result, f, ensure_ascii=False, indent=2)

    print("组合结果已保存到 combined_result_v24.json")

if __name__ == "__main__":
    main()
